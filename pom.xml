<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>su.reddot</groupId>
    <artifactId>oskelly</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>Oskelly</name>
    <description></description>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.1.6.RELEASE</version>
        <relativePath/>
    </parent>

    <repositories>
        <repository>
            <id>gitlab-maven</id>
            <url>https://gitlab.oskelly.ru/api/v4/groups/14/-/packages/maven</url>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>gitlab-maven</id>
            <url>https://gitlab.oskelly.ru/api/v4/projects/4/packages/maven</url>
        </repository>
        <snapshotRepository>
            <id>gitlab-maven</id>
            <url>https://gitlab.oskelly.ru/api/v4/projects/4/packages/maven</url>
        </snapshotRepository>
    </distributionManagement>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-bom</artifactId>
                <version>1.11.1000</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongodb-driver</artifactId>
                <version>3.10.2</version>
            </dependency>
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongo-java-driver</artifactId>
                <version>3.10.2</version>
            </dependency>
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongodb-driver-core</artifactId>
                <version>3.10.2</version>
            </dependency>
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongodb-driver-async</artifactId>
                <version>3.10.2</version>
            </dependency>
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>bson</artifactId>
                <version>3.10.2</version>
            </dependency>
            <dependency>
                <groupId>com.google.cloud</groupId>
                <artifactId>libraries-bom</artifactId>
                <version>26.56.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>Greenwich.SR5</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <java.version>1.8</java.version>
        <thymeleaf.version>3.0.11.RELEASE</thymeleaf.version>
        <thymeleaf-layout-dialect.version>2.4.1</thymeleaf-layout-dialect.version>
        <querydsl.version>4.3.1</querydsl.version>
        <hibernate.version>5.6.4.Final</hibernate.version>
        <hypersistence-utils.version>3.8.3</hypersistence-utils.version>
        <flyway.version>5.2.4</flyway.version>
        <junit-jupiter.version>5.9.0</junit-jupiter.version>
        <aspectj.version>1.9.5</aspectj.version>
        <allure.version>2.19.0</allure.version>
        <logistic-api.version>1.0.23</logistic-api.version>
        <payments-api.version>0.0.69</payments-api.version>
        <kratos-api.version>release-1.0.1.jdk8</kratos-api.version>
        <okhttp.version>4.10.0</okhttp.version>
        <gsonfire.version>1.8.5</gsonfire.version>
        <kotlin-stdlib.version>1.3.70</kotlin-stdlib.version>
        <swift.people.rest.client.version>1.0.1</swift.people.rest.client.version>
    </properties>

    <dependencies>
        <!-- seo client -->
        <dependency>
            <groupId>io.gsonfire</groupId>
            <artifactId>gson-fire</artifactId>
            <version>${gsonfire.version}</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>${okhttp.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
            <version>${kotlin-stdlib.version}</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>logging-interceptor</artifactId>
            <version>${okhttp.version}</version>
        </dependency>
        <!-- /seo client -->
        <dependency>
            <groupId>io.qameta.allure</groupId>
            <artifactId>allure-junit5</artifactId>
            <version>${allure.version}</version>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>${junit-jupiter.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>${junit-jupiter.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter</artifactId>
            <version>2.1.6.RELEASE</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security.oauth</groupId>
            <artifactId>spring-security-oauth2</artifactId>
            <version>2.3.3.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.security.oauth.boot</groupId>
            <artifactId>spring-security-oauth2-autoconfigure</artifactId>
            <version>2.1.6.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
            <version>2.1.6.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
            <version>1.5.2.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.session</groupId>
            <artifactId>spring-session-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.session</groupId>
            <artifactId>spring-session-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <!-- Это последняя версия кафки, совместимой с данной версией спринга. Версия, которую спринг качает автоматом, не стартует -->
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
            <version>2.2.15.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>

        <!--Позволяет использовать в шаблонах выражения типа sec:authorize="isAuthenticated()"-->
        <dependency>
            <groupId>org.thymeleaf.extras</groupId>
            <artifactId>thymeleaf-extras-springsecurity5</artifactId>
            <version>3.0.4.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.thymeleaf.extras</groupId>
            <artifactId>thymeleaf-extras-java8time</artifactId>
            <version>3.0.4.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>nz.net.ultraq.thymeleaf</groupId>
            <artifactId>thymeleaf-layout-dialect</artifactId>
            <version>${thymeleaf-layout-dialect.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>5.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.0.0</version>
            <exclusions>
                <exclusion>
                    <groupId>xml-apis</groupId>
                    <artifactId>xml-apis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>xalan</groupId>
                    <artifactId>xalan</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--database dependencies-->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
            <version>3.4.5</version>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
            <version>${flyway.version}</version>
        </dependency>

        <dependency>
            <groupId>com.querydsl</groupId>
            <artifactId>querydsl-apt</artifactId>
            <version>${querydsl.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.querydsl</groupId>
            <artifactId>querydsl-jpa</artifactId>
            <version>${querydsl.version}</version>
        </dependency>

        <dependency>
            <groupId>com.querydsl</groupId>
            <artifactId>querydsl-sql</artifactId>
            <version>${querydsl.version}</version>
        </dependency>

        <!-- Версия должна совпадать с hibernate.core -->
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-envers</artifactId>
            <version>${hibernate.version}</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-java8</artifactId>
            <version>${hibernate.version}</version>
        </dependency>
        <dependency>
            <groupId>io.hypersistence</groupId>
            <artifactId>hypersistence-utils-hibernate-55</artifactId>
            <version>${hypersistence-utils.version}</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
            <version>1.18.12</version>
        </dependency>

        <dependency>
            <groupId>com.checkout</groupId>
            <artifactId>checkout-sdk-java</artifactId>
            <version>5.9.4</version>
        </dependency>

        <!-- (де) сериализация данных типа Java 8 Date And Time Api (JSR-310) -->
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>2.8.8</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
            <version>2.9.4</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-csv</artifactId>
        </dependency>

        <!--Работа с бинарной БД с ip адресами и странами -->
        <dependency>
            <groupId>com.maxmind.db</groupId>
            <artifactId>maxmind-db</artifactId>
            <version>2.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.maven</groupId>
            <artifactId>maven-artifact</artifactId>
            <version>3.8.3</version>
        </dependency>

        <!--test dependencies-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>junit</groupId>
                    <artifactId>junit</artifactId>
                </exclusion>
            </exclusions>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <version>1.1-groovy-2.4</version>
            <exclusions>
                <exclusion>
                    <groupId>junit</groupId>
                    <artifactId>junit</artifactId>
                </exclusion>
            </exclusions>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-spring</artifactId>
            <version>1.1-groovy-2.4</version>
            <scope>test</scope>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.assertj/assertj-core -->
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <version>3.26.3</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.ws</groupId>
            <artifactId>spring-ws-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.notnoop.apns</groupId>
            <artifactId>apns</artifactId>
            <version>1.0.0.Beta6</version>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.11.2</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.10</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>1.9</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.apache.cxf/cxf-rt-rs-client -->
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-rt-rs-client</artifactId>
            <version>3.2.2</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.cxf/cxf-rt-transports-http-hc -->
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-rt-transports-http-hc</artifactId>
            <version>3.2.2</version>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>4.1.0</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>4.6.1</version>
            <scope>test</scope>
        </dependency>

        <!-- Unofficial OneSignal Java SDK -->
        <dependency>
            <groupId>com.currencyfair</groupId>
            <artifactId>onesignal</artifactId>
            <version>1.0.15</version>
        </dependency>

        <!--Ftp Client-->
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.6</version>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>33.4.0-jre</version>
        </dependency>
        <dependency>
            <groupId>org.mock-server</groupId>
            <artifactId>mockserver-netty</artifactId>
            <version>5.10.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mock-server</groupId>
            <artifactId>mockserver-client-java</artifactId>
            <version>5.10.0</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>5.3</version>
        </dependency>


        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.20</version>
        </dependency>

        <dependency>
            <groupId>com.github.dhorions</groupId>
            <artifactId>boxable</artifactId>
            <version>1.5</version>
        </dependency>

        <!-- JSON Web Token support for the JVM and Android -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>0.9.1</version>
        </dependency>

        <!-- The Bouncy Castle Crypto package is a Java implementation of cryptographic algorithms.
        This jar contains JCE provider and lightweight API for the Bouncy Castle Cryptography APIs for JDK 1.5 and up. -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>1.66</version>
        </dependency>

        <!-- Metrics -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>

        <!-- Swagger -->
<!--\-->

        <!-- Translated -->
        <dependency>
            <groupId>com.google.cloud</groupId>
            <artifactId>google-cloud-translate</artifactId>
        </dependency>
        <!-- Recaptcha -->
        <dependency>
            <groupId>com.google.cloud</groupId>
            <artifactId>google-cloud-recaptchaenterprise</artifactId>
        </dependency>

        <!-- DTO mapping tool -->
        <dependency>
            <groupId>org.modelmapper</groupId>
            <artifactId>modelmapper</artifactId>
            <version>2.4.4</version>
        </dependency>

        <!-- Used to beautify 1C XML reports (search for IndentingXMLStreamWriter) -->
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>txw2</artifactId>
            <version>2.3.1</version>
        </dependency>

        <!--        OP-->
        <dependency>
            <groupId>su.reddot</groupId>
            <artifactId>oskelly-order-processing-internal-api</artifactId>
            <version>1.0.208</version>
        </dependency>

        <dependency>
            <groupId>ru.oskelly</groupId>
            <artifactId>swift-people-rest-client</artifactId>
            <version>${swift.people.rest.client.version}</version>
        </dependency>

        <dependency>
            <groupId>su.reddot</groupId>
            <artifactId>logistic-api</artifactId>
            <version>${logistic-api.version}</version>
        </dependency>

        <dependency>
            <groupId>ru.oskelly</groupId>
            <artifactId>payments-api</artifactId>
            <version>${payments-api.version}</version>
        </dependency>
        <dependency>
            <groupId>ru.oskelly</groupId>
            <artifactId>kratos-api</artifactId>
            <version>${kratos-api.version}</version>
        </dependency>

        <dependency>
            <groupId>ru.oskelly</groupId>
            <artifactId>monolith.api</artifactId>
            <version>1.0.12</version>
        </dependency>

        <dependency>
            <groupId>com.github.dfabulich</groupId>
            <artifactId>sitemapgen4j</artifactId>
            <version>1.1.2</version>
        </dependency>

        <dependency>
            <artifactId>reflections</artifactId>
            <groupId>org.reflections</groupId>
            <version>0.9.9</version>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>7.2</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>

        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
        </dependency>

        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
            <version>0.4.17</version>
        </dependency>

        <dependency>
            <groupId>org.sejda.imageio</groupId>
            <artifactId>webp-imageio</artifactId>
            <version>0.1.6</version>
        </dependency>

        <dependency>
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility</artifactId>
            <version>4.2.0</version>
        </dependency>

        <dependency>
            <groupId>io.qameta.allure</groupId>
            <artifactId>allure-java-commons</artifactId>
            <version>2.20.1</version>
            <scope>test</scope>
        </dependency>

        <!-- XSS Sanitizer -->
        <dependency>
            <groupId>com.googlecode.owasp-java-html-sanitizer</groupId>
            <artifactId>owasp-java-html-sanitizer</artifactId>
            <version>20211018.2</version>
        </dependency>

        <dependency>
            <groupId>com.googlecode.libphonenumber</groupId>
            <artifactId>libphonenumber</artifactId>
            <version>8.13.10</version>
        </dependency>

        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
            <version>1.8.0</version>
            <exclusions>
                <exclusion>
                    <groupId>io.swagger.core.v3</groupId>
                    <artifactId>swagger-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.swagger.core.v3</groupId>
                    <artifactId>swagger-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.swagger.core.v3</groupId>
                    <artifactId>swagger-models</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-core</artifactId>
            <version>2.2.15</version>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>2.2.15</version>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-models</artifactId>
            <version>2.2.15</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.fasterxml.jackson.dataformat</groupId>-->
<!--            <artifactId>jackson-dataformat-yaml</artifactId>-->
<!--            <version>2.14.2</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.apache.lucene</groupId>
            <artifactId>lucene-snowball</artifactId>
            <version>3.0.3</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.17.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-context</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>prod</id>
            <properties>
                <cse>http://web.cse.ru/1c/ws/Web1C.1cws?wsdl</cse>
                <major>major-prod.wsdl</major>
            </properties>
        </profile>

        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>

            <properties>
                <cse>http://lk-test.cse.ru/1c/ws/web1c.1cws?wsdl</cse>
                <major>major-test.wsdl</major>
            </properties>
        </profile>

        <profile>
            <id>debug</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>

            <!--<properties>
                <cse>http://lk-test.cse.ru/1c/ws/web1c.1cws?wsdl</cse>
                <major>major-test.wsdl</major>
            </properties>-->
            <properties>
                <cse>http://web.cse.ru/1c/ws/Web1C.1cws?wsdl</cse>
                <major>major-prod.wsdl</major>
            </properties>

        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <testFailureIgnore>false</testFailureIgnore>
                    <argLine>
                        -javaagent:"${settings.localRepository}/org/aspectj/aspectjweaver/${aspectj.version}/aspectjweaver-${aspectj.version}.jar"
                        -Dfile.encoding=UTF-8
                    </argLine>
                    <systemProperties>
                        <property>
                            <name>junit.jupiter.extensions.autodetection.enabled</name>
                            <value>true</value>
                        </property>
                        <property>
                            <name>allure.results.directory</name>
                            <value>${project.build.directory}/allure-results</value>
                        </property>
                    </systemProperties>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.junit.platform</groupId>
                        <artifactId>junit-platform-surefire-provider</artifactId>
                        <version>1.2.0</version>
                    </dependency>
                    <dependency>
                        <groupId>org.aspectj</groupId>
                        <artifactId>aspectjweaver</artifactId>
                        <version>${aspectj.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>io.qameta.allure</groupId>
                <artifactId>allure-maven</artifactId>
                <version>2.11.2</version>
                <configuration>
                    <reportVersion>2.19.0</reportVersion>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <executable>true</executable>
                </configuration>
            </plugin>

            <!--Кодогенерация вспомогательных классов для каждой сущности @Entity -->
            <plugin>
                <groupId>com.mysema.maven</groupId>
                <artifactId>apt-maven-plugin</artifactId>
                <version>1.1.3</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>process</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>target/generated-sources/java</outputDirectory>
                            <processor>com.querydsl.apt.jpa.JPAAnnotationProcessor</processor>
                            <options>
                                <querydsl.excludedClasses>su.reddot.domain.model.bonuses.OrderBonusesTransaction
                                </querydsl.excludedClasses>
                            </options>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Кодогенерация из схемы логистов -->
            <plugin>
                <groupId>org.jvnet.jaxb2.maven2</groupId>
                <artifactId>maven-jaxb2-plugin</artifactId>
                <version>0.13.1</version>

                <executions>

                    <execution>
                        <id>cse</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <generateDirectory>cse</generateDirectory>
                            <schemaLanguage>WSDL</schemaLanguage>
                            <generatePackage>logistic.wsdl</generatePackage>
                            <generateDirectory>${project.build.directory}/generated-sources/xjc-cse</generateDirectory>
                            <schemas>
                                <schema>
                                    <url>${cse}</url>
                                </schema>
                            </schemas>
                        </configuration>
                    </execution>

                    <execution>
                        <id>best2pay</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <generateDirectory>best2pay</generateDirectory>
                            <schemaLanguage>AUTODETECT</schemaLanguage>
                            <generatePackage>best2pay.xsd</generatePackage>
                            <generateDirectory>${project.build.directory}/generated-sources/xjc-best2pay
                            </generateDirectory>
                            <schemaDirectory>src/main/resources/schemas</schemaDirectory>
                            <schemaIncludes>
                                <include>best2pay.xsd</include>
                            </schemaIncludes>
                        </configuration>
                    </execution>

                    <execution>
                        <id>major</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <args>
                                <arg>-XautoNameResolution</arg>
                            </args>
                            <generateDirectory>major</generateDirectory>
                            <schemaLanguage>WSDL</schemaLanguage>
                            <generatePackage>major.wsdl</generatePackage>
                            <generateDirectory>${project.build.directory}/generated-sources/xjc-major
                            </generateDirectory>
                            <schemaDirectory>${project.basedir}/src/main/resources/schemas</schemaDirectory>
                            <schemaIncludes>
                                <include>${major}</include>
                            </schemaIncludes>
                            <!--<schemaFiles>major-test.wsdl</schemaFiles>-->
                        </configuration>
                    </execution>

                    <execution>
                        <id>aramex-shipping</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <generateDirectory>aramex/shipping</generateDirectory>
                            <schemaLanguage>WSDL</schemaLanguage>
                            <generatePackage>aramex.shipping.wsdl</generatePackage>
                            <generateDirectory>${project.build.directory}/generated-sources/xjc-aramex-shipping
                            </generateDirectory>
                            <schemaDirectory>${project.basedir}/src/main/resources/schemas</schemaDirectory>
                            <schemaIncludes>
                                <include>aramex-shipping.wsdl</include>
                            </schemaIncludes>
                        </configuration>
                    </execution>

                    <execution>
                        <id>aramex-tracking</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <generateDirectory>aramex/tracking</generateDirectory>
                            <schemaLanguage>WSDL</schemaLanguage>
                            <generatePackage>aramex.tracking.wsdl</generatePackage>
                            <generateDirectory>${project.build.directory}/generated-sources/xjc-aramex-tracking
                            </generateDirectory>
                            <schemaDirectory>${project.basedir}/src/main/resources/schemas</schemaDirectory>
                            <schemaIncludes>
                                <include>aramex-tracking.wsdl</include>
                            </schemaIncludes>
                        </configuration>
                    </execution>

                    <execution>
                        <id>aramex-location</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <generateDirectory>aramex/location</generateDirectory>
                            <schemaLanguage>WSDL</schemaLanguage>
                            <generatePackage>aramex.location.wsdl</generatePackage>
                            <generateDirectory>${project.build.directory}/generated-sources/xjc-aramex-location
                            </generateDirectory>
                            <schemaDirectory>${project.basedir}/src/main/resources/schemas</schemaDirectory>
                            <schemaIncludes>
                                <include>aramex-location.wsdl</include>
                            </schemaIncludes>
                        </configuration>
                    </execution>
                </executions>

                <configuration>
                    <catalog>src/main/resources/schemas/catalog.cat</catalog>
                    <!--Using catalogs to resolve schema URIs in strict mode is known to be problematic and may fail.-->
                    <strict>false</strict>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                    <useIncrementalCompilation>true</useIncrementalCompilation>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.6.1</version>
                <executions>
                    <!-- Вытаскиваем OpenAPI определение сервиса Логистики -->
                    <execution>
                        <id>extract-logistic-api-definition</id>
                        <phase>initialize</phase>
                        <goals>
                            <goal>unpack</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>su.reddot</groupId>
                                    <artifactId>logistic-api</artifactId>
                                    <version>${logistic-api.version}</version>
                                    <type>jar</type>
                                    <overWrite>true</overWrite>
                                    <outputDirectory>${project.build.directory}/extracted/logistic-api</outputDirectory>
                                    <includes>/api.yml</includes>
                                </artifactItem>
                            </artifactItems>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <version>6.4.0</version>
                <executions>
                    <execution>
                        <id>generate-product-request-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/productrequest/openapi.json
                            </inputSpec>
                            <generatorName>spring</generatorName>
                            <modelPackage>su.reddot.domain.service.dto.productrequest</modelPackage>
                            <generateApis>false</generateApis>
                            <generateSupportingFiles>false</generateSupportingFiles>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <generateModelDocumentation>false</generateModelDocumentation>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-verification-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/verification/openapi.json
                            </inputSpec>
                            <generatorName>spring</generatorName>
                            <modelPackage>su.reddot.domain.service.dto.verification</modelPackage>
                            <generateApis>false</generateApis>
                            <generateSupportingFiles>false</generateSupportingFiles>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <generateModelDocumentation>false</generateModelDocumentation>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-product-sale-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/salerequest/openapi.yaml
                            </inputSpec>
                            <generatorName>java</generatorName>
                            <library>resttemplate</library>
                            <modelPackage>su.reddot.domain.service.salerequest.model</modelPackage>
                            <apiPackage>su.reddot.domain.service.salerequest.api</apiPackage>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateModelTests>false</generateModelTests>
                            <generateApiTests>false</generateApiTests>
                            <configOptions>
                                <openApiNullable>false</openApiNullable>
                            </configOptions>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-catalog-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/external-catalog/openapi.yaml
                            </inputSpec>
                            <generatorName>java</generatorName>
                            <library>resttemplate</library>
                            <modelPackage>su.reddot.domain.service.external.catalog.model</modelPackage>
                            <apiPackage>su.reddot.domain.service.external.catalog.api</apiPackage>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateModelTests>false</generateModelTests>
                            <generateApiTests>false</generateApiTests>
                            <configOptions>
                                <oas3>true</oas3>
                                <withInterfaces>true</withInterfaces>
                                <openApiNullable>false</openApiNullable>
                            </configOptions>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-filter-subscriptions-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/filter-subscriptions/openapi.yaml
                            </inputSpec>
                            <generatorName>java</generatorName>
                            <library>resttemplate</library>
                            <modelPackage>su.reddot.domain.service.filtersubscriptions.model</modelPackage>
                            <apiPackage>su.reddot.domain.service.filtersubscriptions.api</apiPackage>
                            <importMappings>
                                <importMapping>FilterSubscriptionCatalogRequest=su.reddot.domain.service.external.catalog.model.FilterSubscriptionCatalogRequest</importMapping>
                                <importMapping>FilterSubscriptionOriginalFeRequest=su.reddot.presentation.api.v2.filter.FilterSubscriptionOriginalFeRequest</importMapping>
                            </importMappings>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateModelTests>false</generateModelTests>
                            <generateApiTests>false</generateApiTests>
                            <configOptions>
                                <oas3>true</oas3>
                                <withInterfaces>true</withInterfaces>
                                <openApiNullable>false</openApiNullable>
                            </configOptions>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-topn-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/topn/openapi.yaml
                            </inputSpec>
                            <generatorName>java</generatorName>
                            <library>resttemplate</library>
                            <modelPackage>su.reddot.domain.service.topn.model</modelPackage>
                            <apiPackage>su.reddot.domain.service.topn.api</apiPackage>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateModelTests>false</generateModelTests>
                            <generateApiTests>false</generateApiTests>
                            <configOptions>
                                <oas3>true</oas3>
                                <withInterfaces>true</withInterfaces>
                                <openApiNullable>false</openApiNullable>
                            </configOptions>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-personalized-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/personalized/openapi.yaml
                            </inputSpec>
                            <generatorName>java</generatorName>
                            <library>resttemplate</library>
                            <modelPackage>su.reddot.domain.service.personalized.model</modelPackage>
                            <apiPackage>su.reddot.domain.service.personalized.api</apiPackage>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateModelTests>false</generateModelTests>
                            <generateApiTests>false</generateApiTests>
                            <configOptions>
                                <oas3>true</oas3>
                                <withInterfaces>true</withInterfaces>
                                <openApiNullable>false</openApiNullable>
                            </configOptions>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-spellcheck-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/external-spellchecker/openapi.yaml
                            </inputSpec>
                            <generatorName>java</generatorName>
                            <library>resttemplate</library>
                            <modelPackage>su.reddot.domain.service.external.spellcheck.model</modelPackage>
                            <apiPackage>su.reddot.domain.service.external.spellcheck.api</apiPackage>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateModelTests>false</generateModelTests>
                            <generateApiTests>false</generateApiTests>
                            <configOptions>
                                <oas3>true</oas3>
                                <withInterfaces>true</withInterfaces>
                                <openApiNullable>false</openApiNullable>
                            </configOptions>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-suggesting-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/external-suggesting/openapi.yaml
                            </inputSpec>
                            <generatorName>java</generatorName>
                            <library>resttemplate</library>
                            <modelPackage>su.reddot.domain.service.external.suggesting.model</modelPackage>
                            <apiPackage>su.reddot.domain.service.external.suggesting.api</apiPackage>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateModelTests>false</generateModelTests>
                            <generateApiTests>false</generateApiTests>
                            <configOptions>
                                <oas3>true</oas3>
                                <withInterfaces>true</withInterfaces>
                                <openApiNullable>false</openApiNullable>
                            </configOptions>
                        </configuration>
                    </execution>

                    <execution>
                        <id>generate-logistic-service-client</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.build.directory}/extracted/logistic-api/api.yml</inputSpec>
                            <generatorName>java</generatorName>
                            <library>resttemplate</library>
                            <modelPackage>su.reddot.domain.service.integration.logistic.model</modelPackage>
                            <apiPackage>su.reddot.domain.service.integration.logistic.api</apiPackage>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateModelTests>false</generateModelTests>
                            <generateApiTests>false</generateApiTests>
                        </configuration>
                    </execution>

                    <!-- seo-service -->
                    <execution>
                        <id>generate-seo-service-client</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/seo-service/seo-doc.json
                            </inputSpec>
                            <generatorName>java</generatorName>
                            <modelPackage>ru.oskelly.seo.model</modelPackage>
                            <apiPackage>ru.oskelly.seo.api</apiPackage>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateModelTests>false</generateModelTests>
                            <generateApiTests>false</generateApiTests>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-community-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/community/openapi.json
                            </inputSpec>
                            <generatorName>java</generatorName>
                            <library>resttemplate</library>
                            <modelPackage>su.reddot.domain.service.community.model</modelPackage>
                            <apiPackage>su.reddot.domain.service.community.api</apiPackage>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateModelTests>false</generateModelTests>
                            <generateApiTests>false</generateApiTests>
                            <configOptions>
                                <openApiNullable>false</openApiNullable>
                            </configOptions>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-bonuses-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/bonuses/openapi.json
                            </inputSpec>
                            <generatorName>java</generatorName>
                            <library>resttemplate</library>
                            <modelPackage>su.reddot.domain.service.bonuses.model</modelPackage>
                            <apiPackage>su.reddot.domain.service.bonuses.api</apiPackage>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateModelTests>false</generateModelTests>
                            <generateApiTests>false</generateApiTests>
                            <configOptions>
                                <openApiNullable>false</openApiNullable>
                            </configOptions>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-loyalty-cards-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/bonuses/openapi_loyaltycards.json
                            </inputSpec>
                            <generatorName>java</generatorName>
                            <library>resttemplate</library>
                            <modelPackage>su.reddot.domain.service.bonuses.loyaltycards.model</modelPackage>
                            <apiPackage>su.reddot.domain.service.bonuses.loyaltycards.api</apiPackage>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateModelTests>false</generateModelTests>
                            <generateApiTests>false</generateApiTests>
                            <configOptions>
                                <openApiNullable>false</openApiNullable>
                            </configOptions>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-social-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/social/openapi.yaml
                            </inputSpec>
                            <generatorName>java</generatorName>
                            <library>resttemplate</library>
                            <modelPackage>su.reddot.domain.service.social.model</modelPackage>
                            <apiPackage>su.reddot.domain.service.social.api</apiPackage>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateModelTests>false</generateModelTests>
                            <generateApiTests>false</generateApiTests>
                            <configOptions>
                                <openApiNullable>false</openApiNullable>
                            </configOptions>
                            <additionalProperties>
                                <additionalProperty>removeEnumValuePrefix=false</additionalProperty>
                            </additionalProperties>
                        </configuration>
                    </execution>

                    <!-- Oscelly concierge -->
                    <execution>
                        <id>generate-oscelly-concierge-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/concierge/openapi.yaml
                            </inputSpec>
                            <generatorName>java</generatorName>
                            <library>resttemplate</library>
                            <modelPackage>su.reddot.domain.service.concierge.model</modelPackage>
                            <apiPackage>su.reddot.domain.service.concierge.api</apiPackage>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateModelTests>false</generateModelTests>
                            <generateApiTests>false</generateApiTests>
                            <configOptions>
                                <oas3>true</oas3>
                                <withInterfaces>true</withInterfaces>
                                <openApiNullable>false</openApiNullable>
                            </configOptions>
                        </configuration>
                    </execution>

                    <!-- sales-app-api -->
                    <execution>
                        <id>generate-sales-app-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/salesapp/openapi.yaml</inputSpec>
                            <generatorName>java</generatorName>
                            <library>resttemplate</library>
                            <modelPackage>su.reddot.domain.service.salesapp.model</modelPackage>
                            <apiPackage>su.reddot.domain.service.salesapp.api</apiPackage>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateModelTests>false</generateModelTests>
                            <generateApiTests>false</generateApiTests>
                            <configOptions>
                                <oas3>true</oas3>
                                <withInterfaces>true</withInterfaces>
                                <openApiNullable>false</openApiNullable>
                            </configOptions>
                        </configuration>
                    </execution>

                    <execution>
                        <id>generate-search-history-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/search-history/openapi.yaml
                            </inputSpec>
                            <generatorName>java</generatorName>
                            <library>resttemplate</library>
                            <modelPackage>su.reddot.domain.service.search.history.model</modelPackage>
                            <apiPackage>su.reddot.domain.service.search.history.api</apiPackage>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateModelTests>false</generateModelTests>
                            <generateApiTests>false</generateApiTests>
                            <configOptions>
                                <openApiNullable>false</openApiNullable>
                            </configOptions>
                            <additionalProperties>
                                <additionalProperty>removeEnumValuePrefix=false</additionalProperty>
                            </additionalProperties>
                        </configuration>
                    </execution>

                    <execution>
                        <id>generate-loyalty-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/loyalty/openapi.yaml
                            </inputSpec>
                            <generatorName>java</generatorName>
                            <library>resttemplate</library>
                            <modelPackage>su.reddot.domain.service.loyalty.model</modelPackage>
                            <apiPackage>su.reddot.domain.service.loyalty.api</apiPackage>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateModelTests>false</generateModelTests>
                            <generateApiTests>false</generateApiTests>
                            <configOptions>
                                <openApiNullable>false</openApiNullable>
                            </configOptions>
                            <additionalProperties>
                                <additionalProperty>removeEnumValuePrefix=false</additionalProperty>
                            </additionalProperties>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
