package su.reddot.component;

import lombok.Builder;
import lombok.Getter;

import java.math.BigDecimal;

@Getter
@Builder(toBuilder = true)
public class HoldRequest {
    private final String holdEndPoint;

    private final String promoCode;
    private final String currencyCode;
    private final String paymentSystem;
    private final Long paymentBuyerCounterpartyId;
    private final String type;
    private final BigDecimal totalBonusesAmount;
}
