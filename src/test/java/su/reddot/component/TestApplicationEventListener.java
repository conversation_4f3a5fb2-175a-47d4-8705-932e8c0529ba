package su.reddot.component;

import io.micrometer.core.lang.NonNull;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import su.reddot.domain.event.ChangeEnvironmentEvent;
import su.reddot.domain.event.UserAuthEvent;
import su.reddot.domain.event.UserSubscriptionEvent;
import su.reddot.domain.event.UserRegisterEvent;
import su.reddot.domain.event.UserEmailSubscriptionEvent;
import su.reddot.domain.service.user.ChangeUserEvent;

@Component @Getter @Setter
public class TestApplicationEventListener {
    private UserAuthEvent lastUserAuthEvent;
    private UserRegisterEvent lastUserRegisterEvent;
    private UserSubscriptionEvent lastUserMobilePushEvent;
    private UserEmailSubscriptionEvent userEmailSubscriptionEvent;
    private ChangeUserEvent lastChangeUserEvent;
    private ChangeEnvironmentEvent lastChangeEnvironmentEvent;

    @EventListener
    public void onUserAuthEvent(@NonNull UserAuthEvent event){
        //Пропускаем локальные события
        if(event.getInstanceId() == null) return;
        lastUserAuthEvent = event;
    }

    @EventListener
    public void onUserRegisterEvent(@NonNull UserRegisterEvent event){
        //Пропускаем локальные события
        if(event.getInstanceId() == null) return;
        lastUserRegisterEvent = event;
    }

    @EventListener
    public void onUserEmailSubscriptionEvent(@NonNull UserEmailSubscriptionEvent event){
        //Пропускаем локальные события
        if(event.getInstanceId() == null) return;
        userEmailSubscriptionEvent = event;
    }

    @EventListener
    public void onUserPushSubscriptionEvent(@NonNull UserSubscriptionEvent event){
        //Пропускаем локальные события
        if(event.getInstanceId() == null) return;
        lastUserMobilePushEvent = event;
    }

    @EventListener
    public void onChangeUserEvent(@NonNull ChangeUserEvent event){
        //Пропускаем локальные события
        if(event.getInstanceId() == null) return;
        lastChangeUserEvent = event;
    }

    @EventListener
    public void onChangeEnvironmentEvent(@NonNull ChangeEnvironmentEvent event){
        if(event.getInstanceId() == null) return;
        lastChangeEnvironmentEvent = event;
    }
}
