package su.reddot.component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.service.catalog.menu.CatalogMenu;
import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.domain.service.dto.ProductImageDTO;
import su.reddot.domain.service.dto.UserDTO;
import su.reddot.domain.service.staticresource.StaticResourceBalancer;
import su.reddot.presentation.api.v2.Api2Response;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Некоторые тесты обращаются к каталогу для проверки функционала, н.п. торги, лайки, подписка, бан и т.п.
 * Чтобы не плодить одинаковый код по получению товаров из каталога создается этот класс.
 */
@Component
@RequiredArgsConstructor
public class CatalogTestSupport {
    @Autowired
    private TestApiConfiguration testApiConfiguration;

    @Autowired
    private StaticResourceBalancer staticResourceBalancer;

    @Autowired
    private ObjectMapper objectMapper;

    @Setter
    private ApiV2Client apiV2Client;

    private String getServiceUrl() {
        return testApiConfiguration.getServerUrl() + "/api/v2/catalog";
    }
    private String getProductsUrl(){ return getServiceUrl() + "/products"; }
    private String getSingleProductUrl(Long productId){ return getProductsUrl() + "/" + productId; }
    private String getProductIdsPageUrl(){ return getServiceUrl() + "/product-ids-page"; }

    private String getCatalogMenuContentUrl(){ return getServiceUrl() + "/menuContent"; }

    private String getCatalogMenuUrl(){ return getServiceUrl() + "/menu"; }

    @SneakyThrows
    public ProductDTO getProductSuccessful(Long productId, boolean withAuthorizeParams){
        ResponseEntity<String> response = apiV2Client.request(getSingleProductUrl(productId), null, HttpMethod.GET, null, String.class, withAuthorizeParams);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        Api2Response<ProductDTO> body = objectMapper.readValue(response.getBody(), new TypeReference<Api2Response<ProductDTO>>() {
        });
        ProductDTO product = body.getData();
        assertNotNull(product);
        return product;
    }

    @SneakyThrows
    public String getCatalogMenuStringSuccessful(String version,
                                                 boolean isExperimentalVersion, boolean productRequestEnabled) {
        ResponseEntity<String> response = apiV2Client.request(getCatalogMenuUrl(),
                ImmutableMap.of(
                        "version", version,
                        "isExperimentalVersion", String.valueOf(isExperimentalVersion),
                        "productRequestEnabled", String.valueOf(productRequestEnabled)
                ),
                HttpMethod.GET, null, String.class, false);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        return response.getBody();
    }

    @SneakyThrows
    public CatalogMenu getCatalogMenuSuccessful(String version,
                                                boolean isExperimentalVersion, boolean productRequestEnabled) {
        ResponseEntity<String> response = apiV2Client.request(getCatalogMenuContentUrl(),
                ImmutableMap.of(
                        "version", version,
                        "isExperimentalVersion", String.valueOf(isExperimentalVersion),
                        "productRequestEnabled", String.valueOf(productRequestEnabled)
                        ),
                HttpMethod.GET, null, String.class, false);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        Api2Response<CatalogMenu> body =
                objectMapper.readValue(response.getBody(), new TypeReference<Api2Response<CatalogMenu>>() {});
        CatalogMenu catalogMenu = body.getData();
        assertNotNull(catalogMenu);
        return catalogMenu;
    }

    @SneakyThrows
    public void getCatalogMenuUnsuccessful(String version, boolean isExperimentalVersion) {
        ResponseEntity<String> response = apiV2Client.request(getCatalogMenuContentUrl(),
                ImmutableMap.of("version", version, "isExperimentalVersion", String.valueOf(isExperimentalVersion)),
                HttpMethod.GET, null, String.class, false);
        assertTrue(response.getStatusCode().is4xxClientError());
        assertNotNull(response.getBody());
    }

    @SneakyThrows
    public Page<ProductDTO> getProductsPageSuccessful(Map<String, String> getParams, boolean withAuthorizeParams){
        ResponseEntity<String> response = apiV2Client.request(getProductsUrl(), getParams, HttpMethod.GET, null, String.class, withAuthorizeParams);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        Api2Response<Page<ProductDTO>> body = objectMapper.readValue(response.getBody(), new TypeReference<Api2Response<Page<ProductDTO>>>() {
        });
        Page<ProductDTO> page = body.getData();
        assertNotNull(page);
        assertTrue(page.getTotalAmount() > 0);
        assertTrue(page.getTotalPages() > 0);
        assertNotNull(page.getItems());
        assertFalse(page.getItems().isEmpty());
        for(ProductDTO productDTO : page.getItems()){
            assertValidProductDTO(productDTO);
        }
        return page;
    }

    public Page<Long> getProductIdsPageSuccessful(Map<String, String> getParams, boolean withAuthorizeParams){
        ResponseEntity<Api2Response<Page<Long>>> response = apiV2Client.request(getProductIdsPageUrl(), getParams, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<Page<Long>>>() {}, withAuthorizeParams);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        Page<Long> page = response.getBody().getData();
        assertNotNull(page);
        assertTrue(page.getTotalAmount() > 0);
        assertTrue(page.getTotalPages() > 0);
        assertNotNull(page.getItems());
        assertFalse(page.getItems().isEmpty());
        return page;
    }

    public void assertValidProductDTO(ProductDTO productDTO){
        assertNotNull(productDTO.getProductId());
        assertNotNull(productDTO.getProductState());
        assertNotNull(productDTO.getSeller());
        assertTrue(productDTO.getImages() == null || productDTO.getImages().isEmpty() || assertValidProductImageDTO(productDTO.getImages().get(0)));
        assertNotNull(productDTO.getIsAtOffice());
        assertValidUserDTO(productDTO.getSeller());
        assertNotNull(productDTO.getSalesChannel());
    }

    public boolean assertImageStartsWithImg(String img){
        if(img == null) return true;
        img = img.replace(testApiConfiguration.getServerUrl(), "");
        boolean result = img == null || img.startsWith(staticResourceBalancer.getImagesPrefix());
        assertTrue(result);
        return result;
    }

    public boolean assertValidUserDTO(UserDTO userDTO){
        assertNotNull(userDTO);
        assertNotNull(userDTO.getId());
        assertNotNull(userDTO.getNickname());
        assertNotNull(userDTO.getFirstChar());
        assertTrue(userDTO.getFirstChar().length() == 1);
        assertTrue(userDTO.getFirstChar().equals(userDTO.getFirstChar().toLowerCase()));
        assertNotNull(userDTO.getIsPro());
        assertTrue(userDTO.getAvatarPath() == null || assertImageStartsWithImg(userDTO.getAvatarPath()));
        return true;
    }

    public boolean assertValidProductImageDTO(ProductImageDTO productImageDTO){
        assertNotNull(productImageDTO);
        assertNotNull(productImageDTO.getId());
        assertNotNull(productImageDTO.getOrder());
        assertTrue(productImageDTO.getOrder() > 0);
        assertTrue(assertImageStartsWithImg(productImageDTO.getPath()));
        return true;
    }

    public Map<String, String> getFilterParams(ProductState state){
        Map<String, String> params = new HashMap<>();
        if(state != null) params.put("state", state.name());
        return params;
    }


}
