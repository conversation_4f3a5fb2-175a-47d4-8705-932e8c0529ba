package su.reddot.component;

import lombok.RequiredArgsConstructor;
import su.reddot.domain.dao.address.CityRepository;
import su.reddot.domain.dao.address.CountryRepository;
import su.reddot.domain.dao.delivery.DeliveryCompanyCityRepository;
import su.reddot.domain.dao.delivery.DeliveryCompanyCountryRepository;
import su.reddot.domain.dao.deliverycompany.DeliveryCompanyRepository;
import su.reddot.domain.model.address.City;
import su.reddot.domain.model.address.Country;
import su.reddot.domain.model.logistic.DeliveryCompany;
import su.reddot.domain.model.logistic.DeliveryCompanyCity;
import su.reddot.domain.model.logistic.DeliveryCompanyCountry;

import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class AddressTestSupport {

    private final DeliveryCompanyRepository deliveryCompanyRepository;

    private final CountryRepository countryRepository;

    private final CityRepository cityRepository;

    private final DeliveryCompanyCountryRepository deliveryCompanyCountryRepository;

    private final DeliveryCompanyCityRepository deliveryCompanyCityRepository;

    public void addDeliveryCompanySupport(long countryId, long cityId) {
        DeliveryCompany deliveryCompany = deliveryCompanyRepository.getOne(1L);
        Country country = countryRepository.getOne(countryId);
        if (deliveryCompanyCountryRepository.countByCountry(country) == 0) {
            DeliveryCompanyCountry deliveryCompanyCountry = new DeliveryCompanyCountry();
            deliveryCompanyCountry.setDeliveryCompany(deliveryCompany);
            deliveryCompanyCountry.setCountry(country);
            deliveryCompanyCountryRepository.save(deliveryCompanyCountry);
        }
        City city = cityRepository.getOne(cityId);
        if (deliveryCompanyCityRepository.countByCity(city) == 0) {
            DeliveryCompanyCity deliveryCompanyCity = new DeliveryCompanyCity();
            deliveryCompanyCity.setDeliveryCompany(deliveryCompany);
            deliveryCompanyCity.setCity(city);
            deliveryCompanyCityRepository.save(deliveryCompanyCity);
        }
    }
}
