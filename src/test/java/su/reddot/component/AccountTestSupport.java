package su.reddot.component;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import su.reddot.domain.service.dto.AccountDTO;
import su.reddot.domain.service.dto.BubblesDTO;
import su.reddot.domain.service.dto.CountryDTO;
import su.reddot.domain.service.subscriptionContact.SubscriptionContactService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.security.view.SocialLoginRequest;
import su.reddot.infrastructure.security.view.SocialLoginResponse;
import su.reddot.presentation.api.v2.AccountControllerApiV2;
import su.reddot.presentation.api.v2.Api2Response;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Некоторые тесты обращаются к акканту для проверки функционала, н.п. торги, публикация, подписка, бан и т.п.
 * Чтобы не плодить одинаковый код по получению аккаунта создается этот класс.
 */
@Component
@RequiredArgsConstructor
public class AccountTestSupport {
    @Autowired
    private TestApiConfiguration testApiConfiguration;

    public String getServiceUrl(){
        return testApiConfiguration.getServerUrl() + "/api/v2/account";
    }
    public String getCounterpartyUrl(){
        return getServiceUrl() + "/counterparty";
    }
    public String getAddressEndpointUrl(){
        return getServiceUrl() + "/addressendpoint";
    }
    public String getBubblesUrl(){
        return getServiceUrl() + "/bubbles";
    }
    public String getSexUrl(){
        return getServiceUrl() + "/sex";
    }
    public String getPushGroupsUrl(){
        return getServiceUrl() + "/pushGroups";
    }
    public String getEmailGroupsUrl() {
        return getServiceUrl() + "/emailGroups";
    }
    public String getWhatsappGroupsUrl() {
        return getServiceUrl() + "/whatsappGroups";
    }
    public String getFollowingIds() { return getServiceUrl() + "/followingIds";}
    public String getRegisterUrl() { return getServiceUrl() + "/register";}
    public String getSocialRegisterUrl() { return getServiceUrl() + "/social-register";}
    public String getSocialLoginUrl() { return getServiceUrl() + "/social-login";}
    public String getResetPasswordUrl() { return getServiceUrl() + "/reset";}
    public String getSetPasswordUrl() { return getServiceUrl() + "/password";}
    public String getUpdatePasswordUrl() { return getServiceUrl() + "/password";}
    public String getRawAuthUrl() { return getServiceUrl() + "/rawauth";}
    public String getAuthUrl() { return getServiceUrl() + "/auth";}
    public String getUploadAvatarUrl() { return getServiceUrl() + "/image";}
    public String getUserfilesUrl() { return getServiceUrl() + "/userfiles";}
    public String getSubscriptionUrl() {
        return getServiceUrl() + "/email-subscription";
    }
    public String getCredsUrl() {
        return getServiceUrl() + "/creds";
    }
    public String setCountryUrl() {
        return getServiceUrl() + "/addresses/country";
    }

    public AccountDTO getAccountSuccessful(ApiV2Client apiV2Client, boolean withAuthorizeParams){
        ResponseEntity<Api2Response<AccountDTO>> response = apiV2Client.request(getServiceUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<AccountDTO>>() {}, withAuthorizeParams);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        return response.getBody().getData();
    }

    public AccountDTO updateAccountSuccessful(AccountDTO accountDTO, ApiV2Client apiV2Client, boolean withAuthorizeParams){
        ResponseEntity<Api2Response<AccountDTO>> response = apiV2Client.request(getServiceUrl(), null, HttpMethod.PUT, accountDTO, new ParameterizedTypeReference<Api2Response<AccountDTO>>() {}, true);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        return response.getBody().getData();
    }

    public long registerUser(ApiV2Client apiV2Client, UserService.EmailRegistrationRequest request){
        //Регистрируем пользователя
        ResponseEntity<Api2Response<Long>> response = apiV2Client.request(getRegisterUrl(), null, HttpMethod.POST, TestUtils.getMultivalueMapWithObjectFields(request), new ParameterizedTypeReference<Api2Response<Long>>() {}, false);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        Long newUserId = response.getBody().getData();
        assertNotNull(newUserId);
        return newUserId;
    }

    public long registerRandomUser(ApiV2Client apiV2Client){
        String password = RandomStringUtils.randomAlphabetic(8);
        UserService.EmailRegistrationRequest request = new UserService.EmailRegistrationRequest()
                .setRegisterEmail(RandomStringUtils.randomAlphabetic(8) + "@mail.ru")
                .setRegisterNickname(RandomStringUtils.randomAlphabetic(8).toLowerCase())
                .setRegisterPassword(password)
                .setRegisterConfirmPassword(password)
                .setRegisterPhone("+7" + RandomStringUtils.randomNumeric(10));

        return registerUser(apiV2Client, request);
    }

    public void assertSocialLoginReturnsFailed(ApiV2Client apiV2Client, SocialLoginRequest request, String expectedMessage){
        ResponseEntity<String> response = apiV2Client.request(getSocialLoginUrl(), null, HttpMethod.POST, request, String.class, false);
        assertTrue(response.getStatusCode().is4xxClientError() || response.getStatusCode().is5xxServerError());
        String responseBody = response.getBody();
        assertNotNull(responseBody);
        assertTrue(responseBody.replace(" : ", ":").contains("\"message\":\"" + expectedMessage));
    }

    public SocialLoginResponse socialLoginSuccessful(ApiV2Client apiV2Client, SocialLoginRequest request){
        ResponseEntity<Api2Response<SocialLoginResponse>> response = apiV2Client.request(getSocialLoginUrl(), null, HttpMethod.POST, MediaType.APPLICATION_JSON, request, new ParameterizedTypeReference<Api2Response<SocialLoginResponse>>() {}, false);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        return response.getBody().getData();
    }

    public String subscribeSuccessful(ApiV2Client apiV2Client, SubscriptionContactService.SubscriptionContactRequest request){
        ResponseEntity<Api2Response<String>> response = apiV2Client.request(getSubscriptionUrl(), null, HttpMethod.POST, TestUtils.getMultivalueMapWithObjectFields(request), new ParameterizedTypeReference<Api2Response<String>>() {}, false);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        return response.getBody().getData();
    }

    public BubblesDTO getBubblesSuccessful(ApiV2Client apiV2Client, boolean withAuthorizeParams){
        ResponseEntity<Api2Response<BubblesDTO>> response = apiV2Client.request(getBubblesUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<BubblesDTO>>() {}, withAuthorizeParams);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        return response.getBody().getData();
    }

    //Авторизация по сырому (незашифрованному) паролю
    public long rawAuthSuccessful(ApiV2Client apiV2Client, String email, String password){
        MultiValueMap<String, String> request = TestUtils.getOneParamAsMultiValueMap("rawEmail", email);
        request.set("rawPassword", password);
        ResponseEntity<Api2Response<AccountControllerApiV2.IdDTO>> response = apiV2Client.request(getRawAuthUrl(), null, HttpMethod.POST, request, new ParameterizedTypeReference<Api2Response<AccountControllerApiV2.IdDTO>>() {}, false);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        Api2Response<AccountControllerApiV2.IdDTO> resultObj = response.getBody();
        assertNotNull(resultObj);
        assertEquals("Авторизация", resultObj.getMessage());
        assertNotNull(resultObj.getData());
        return resultObj.getData().getId();
    }

    //Авторизация по email/password
    //либо appleId/password
    //либо facebookId/password
    //либо vkId/password
    public long authSuccessful(@NonNull ApiV2Client apiV2Client, MultiValueMap<String, String> request){
        ResponseEntity<Api2Response<AccountControllerApiV2.IdDTO>> response = apiV2Client.request(getAuthUrl(), null, HttpMethod.POST, request, new ParameterizedTypeReference<Api2Response<AccountControllerApiV2.IdDTO>>() {}, false);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        Api2Response<AccountControllerApiV2.IdDTO> resultObj = response.getBody();
        assertNotNull(resultObj);
        assertEquals("Авторизация", resultObj.getMessage());
        assertNotNull(resultObj.getData());
        return resultObj.getData().getId();
    }

    // Смена страны
    public long changeCountrySuccessful(ApiV2Client apiV2Client, Long countryId){
        MultiValueMap<String, String> request = TestUtils.getOneParamAsMultiValueMap("countryId", countryId);
        ResponseEntity<Api2Response<CountryDTO>> response = apiV2Client.request(setCountryUrl(), null, HttpMethod.POST, request, new ParameterizedTypeReference<Api2Response<CountryDTO>>() {}, false);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        Api2Response<CountryDTO> resultObj = response.getBody();
        assertNotNull(resultObj);
        assertEquals(resultObj.getMessage(), null);
        assertNotNull(resultObj.getData());
        return resultObj.getData().getId();
    }
}
