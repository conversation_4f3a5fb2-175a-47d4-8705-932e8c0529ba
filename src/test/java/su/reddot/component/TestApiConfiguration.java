package su.reddot.component;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import su.reddot.infrastructure.test.TestApiPortHandler;

@Component
@NoArgsConstructor @Getter @Setter
@ConfigurationProperties(prefix = "test.api")
public class TestApiConfiguration {
    private String host;
    private int port;

    @Autowired
    private TestApiPortHandler testApiPortHandler;

    public String getServerUrl() {
        return testApiPortHandler.replacePortInUrl("http://" + host + ":" + port);
    }
}
