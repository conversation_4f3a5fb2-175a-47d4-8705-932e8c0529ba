package su.reddot.domain.dao.delivery;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.model.delivery.DeliveryZone;

import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class DeliveryZoneRepositoryTest extends AbstractSpringTest {
    private static final int KOSTROMSKAYA_DISTRICT_ID = 37;
    private static final String KOSTROMSKAYA_DISTRICT_NAME = "Костромская область";
    private static final UUID KOSTROMSKAYA_DISTRICT_REGION_FIAS_ID =
        UUID.fromString("15784a67-8cea-425b-834a-6afe0e3ed61c");
    private static final int KOSTROMSKAYA_DISTRICT_DELIVERY_PERIOD_DAYS = 2;
    private static final int KOSTROMSKAYA_DISTRICT_SHIPPING_PERIOD_DAYS = 1;
    private static final int TOTAL_NUMBER_OF_ZONES = 90;
    private static final int MAX_DELIVERY_PERIOD_DAYS = 4;

    @Autowired
    private DeliveryZoneRepository deliveryZoneRepository;

    @Test
    public void find_all_zones() {
        List<DeliveryZone> deliveryZones = deliveryZoneRepository.findAll();
        assertEquals(TOTAL_NUMBER_OF_ZONES, deliveryZones.size());

        DeliveryZone zone = deliveryZones.stream()
            .filter(dz -> dz.getId() == KOSTROMSKAYA_DISTRICT_ID).findFirst().get();

        assertEquals(KOSTROMSKAYA_DISTRICT_ID, zone.getId());
        assertEquals(KOSTROMSKAYA_DISTRICT_NAME, zone.getName());
        assertEquals(KOSTROMSKAYA_DISTRICT_REGION_FIAS_ID, zone.getRegionFiasId());
        assertEquals(KOSTROMSKAYA_DISTRICT_DELIVERY_PERIOD_DAYS, zone.getAverageDeliveryPeriodDays());
        assertEquals(KOSTROMSKAYA_DISTRICT_SHIPPING_PERIOD_DAYS, zone.getAverageShippingPeriodDays());
    }

    @Test
    public void find_by_fias_id() {
        DeliveryZone zone = deliveryZoneRepository.findDeliveryZoneByRegionFiasId(KOSTROMSKAYA_DISTRICT_REGION_FIAS_ID);

        assertEquals(KOSTROMSKAYA_DISTRICT_ID, zone.getId());
        assertEquals(KOSTROMSKAYA_DISTRICT_NAME, zone.getName());
        assertEquals(KOSTROMSKAYA_DISTRICT_REGION_FIAS_ID, zone.getRegionFiasId());
        assertEquals(KOSTROMSKAYA_DISTRICT_DELIVERY_PERIOD_DAYS, zone.getAverageDeliveryPeriodDays());
        assertEquals(KOSTROMSKAYA_DISTRICT_SHIPPING_PERIOD_DAYS, zone.getAverageShippingPeriodDays());
    }

    @Test
    public void get_max_delivery_time() {
        int maxDeliveryPeriod = deliveryZoneRepository.getMaxDeliveryPeriod();

        assertEquals(MAX_DELIVERY_PERIOD_DAYS, maxDeliveryPeriod);
    }
}