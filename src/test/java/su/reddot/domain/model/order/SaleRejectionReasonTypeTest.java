package su.reddot.domain.model.order;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class SaleRejectionReasonTypeTest {

    @Test
    public void generateMessageKeys() {
        Assertions.assertThat(SaleRejectionReasonType.Type.CHANGE_DECISION.appMessageKey())
            .isEqualTo("entity.enum.SaleRejectionReasonType.CHANGE_DECISION.appDisplayName");
        Assertions.assertThat(SaleRejectionReasonType.Type.SOLD_ON_OTHER_PLATFORM.appMessageKey())
            .isEqualTo("entity.enum.SaleRejectionReasonType.SOLD_ON_OTHER_PLATFORM.appDisplayName");
        Assertions.assertThat(SaleRejectionReasonType.Type.GOING_TO_CHANGE_PRICE.appMessageKey())
            .isEqualTo("entity.enum.SaleRejectionReasonType.GOING_TO_CHANGE_PRICE.appDisplayName");
        Assertions.assertThat(SaleRejectionReasonType.Type.PRODUCT_CONDITION_CHANGED.appMessageKey())
            .isEqualTo("entity.enum.SaleRejectionReasonType.PRODUCT_CONDITION_CHANGED.appDisplayName");
        Assertions.assertThat(SaleRejectionReasonType.Type.OTHER.appMessageKey())
            .isEqualTo("entity.enum.SaleRejectionReasonType.OTHER.appDisplayName");

        Assertions.assertThat(SaleRejectionReasonType.Type.CHANGE_DECISION.adminMessageKey())
            .isEqualTo("entity.enum.SaleRejectionReasonType.CHANGE_DECISION.adminDisplayName");
        Assertions.assertThat(SaleRejectionReasonType.Type.SOLD_ON_OTHER_PLATFORM.adminMessageKey())
            .isEqualTo("entity.enum.SaleRejectionReasonType.SOLD_ON_OTHER_PLATFORM.adminDisplayName");
        Assertions.assertThat(SaleRejectionReasonType.Type.GOING_TO_CHANGE_PRICE.adminMessageKey())
            .isEqualTo("entity.enum.SaleRejectionReasonType.GOING_TO_CHANGE_PRICE.adminDisplayName");
        Assertions.assertThat(SaleRejectionReasonType.Type.PRODUCT_CONDITION_CHANGED.adminMessageKey())
            .isEqualTo("entity.enum.SaleRejectionReasonType.PRODUCT_CONDITION_CHANGED.adminDisplayName");
        Assertions.assertThat(SaleRejectionReasonType.Type.OTHER.adminMessageKey())
            .isEqualTo("entity.enum.SaleRejectionReasonType.OTHER.adminDisplayName");
        Assertions.assertThat(SaleRejectionReasonType.Type.CAN_NOT_SEND.adminMessageKey())
            .isEqualTo("entity.enum.SaleRejectionReasonType.CAN_NOT_SEND.adminDisplayName");
        Assertions.assertThat(SaleRejectionReasonType.Type.DONT_ANSWER.adminMessageKey())
            .isEqualTo("entity.enum.SaleRejectionReasonType.DONT_ANSWER.adminDisplayName");
    }
}