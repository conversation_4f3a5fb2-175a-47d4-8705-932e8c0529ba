package su.reddot.domain.service.product;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.data.domain.Sort;
import su.reddot.infrastructure.configparam.dto.SlotPatternDTO;

import java.util.Arrays;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;

class OffsetServiceTest {

    private static Stream<Arguments> provideDataForCorrectOffset() {
        return Stream.of(
                // 1. Base case
                new CorrectOffsetInvocationParams()
                        .setPage(1)
                        .setPageSize(10)
                        .setSlotPatternDTO(6, 1, 2, 3)
                        .setOverallSamplePrimaryCount(10)
                        .setOverallSampleSecondaryCount(10)
                        .setExpectedResult(0, 4, 0, 0, 6, 0),

                // 2. Different pattern size
                new CorrectOffsetInvocationParams()
                        .setPage(2)
                        .setPageSize(5)
                        .setSlotPatternDTO(4, 2, 3, 4)
                        .setOverallSamplePrimaryCount(20)
                        .setOverallSampleSecondaryCount(20)
                        .setExpectedResult(2, 3, 1, 3, 2, 0),

                // 3. Page size less than pattern size
                new CorrectOffsetInvocationParams()
                        .setPage(2)
                        .setPageSize(3)
                        .setSlotPatternDTO(5, 2, 3, 4, 5, 1)
                        .setOverallSamplePrimaryCount(20)
                        .setOverallSampleSecondaryCount(20)
                        .setExpectedResult(1, 1, 0, 2, 2, 1),

                // 4. Page size greater than pattern size
                new CorrectOffsetInvocationParams()
                        .setPage(3)
                        .setPageSize(15)
                        .setSlotPatternDTO(3, 1, 2, 3)
                        .setOverallSamplePrimaryCount(30)
                        .setOverallSampleSecondaryCount(30)
                        .setExpectedResult(0, 15, 0, 30, 0, 0),

                // 5. Larger overall counts
                new CorrectOffsetInvocationParams()
                        .setPage(1)
                        .setPageSize(10)
                        .setSlotPatternDTO(3, 1, 2, 3)
                        .setOverallSamplePrimaryCount(100)
                        .setOverallSampleSecondaryCount(100)
                        .setExpectedResult(0, 4, 0, 0, 6, 0),

                // 6. Primary count more than secondary count
                new CorrectOffsetInvocationParams()
                        .setPage(3)
                        .setPageSize(10)
                        .setSlotPatternDTO(3, 1, 2, 3)
                        .setOverallSamplePrimaryCount(50)
                        .setOverallSampleSecondaryCount(5)
                        .setExpectedResult(1, 10, 0, 5, 0, 0),

                // 7. Only primary pattern
                new CorrectOffsetInvocationParams()
                        .setPage(2)
                        .setPageSize(10)
                        .setSlotPatternDTO(1, 1)
                        .setOverallSamplePrimaryCount(30)
                        .setOverallSampleSecondaryCount(0)
                        .setExpectedResult(0, 10, 0, 0, 0, 0),

                // 8. Only secondary pattern
                new CorrectOffsetInvocationParams()
                        .setPage(2)
                        .setPageSize(10)
                        .setSlotPatternDTO(1, 2)
                        .setOverallSamplePrimaryCount(0)
                        .setOverallSampleSecondaryCount(30)
                        .setExpectedResult(0, 0, 0, 10, 10, 0),

                // 9. Larger pattern size with varying patterns
                new CorrectOffsetInvocationParams()
                        .setPage(4)
                        .setPageSize(20)
                        .setSlotPatternDTO(6, 1, 2)
                        .setOverallSamplePrimaryCount(60)
                        .setOverallSampleSecondaryCount(60)
                        .setExpectedResult(0, 20, 0, 60, 0, 0),

                // 10. Edge case with page size of 1
                new CorrectOffsetInvocationParams()
                        .setPage(5)
                        .setPageSize(1)
                        .setSlotPatternDTO(2, 1, 2)
                        .setOverallSamplePrimaryCount(10)
                        .setOverallSampleSecondaryCount(10)
                        .setExpectedResult(0, 1, 0, 4, 0, 0),

                // 11. Smallest values for everything
                new CorrectOffsetInvocationParams()
                        .setPage(1)
                        .setPageSize(1)
                        .setSlotPatternDTO(1, 1)
                        .setOverallSamplePrimaryCount(1)
                        .setOverallSampleSecondaryCount(1)
                        .setExpectedResult(0, 1, 0, 0, 0, 0),

                // 12. Mixed pattern with uneven distribution
                new CorrectOffsetInvocationParams()
                        .setPage(3)
                        .setPageSize(7)
                        .setSlotPatternDTO(4, 2, 4)
                        .setOverallSamplePrimaryCount(28)
                        .setOverallSampleSecondaryCount(28)
                        .setExpectedResult(0, 7, 0, 14, 0, 0),

                // 13. High page number
                new CorrectOffsetInvocationParams()
                        .setPage(10)
                        .setPageSize(5)
                        .setSlotPatternDTO(3, 1, 2, 3)
                        .setOverallSamplePrimaryCount(50)
                        .setOverallSampleSecondaryCount(50)
                        .setExpectedResult(0, 2, 0, 45, 3, 0),

                // 14. High overall sample counts
                new CorrectOffsetInvocationParams()
                        .setPage(4)
                        .setPageSize(40)
                        .setSlotPatternDTO(5, 1, 2, 3, 4, 5)
                        .setOverallSamplePrimaryCount(200)
                        .setOverallSampleSecondaryCount(200)
                        .setExpectedResult(0, 8, 0, 120, 32, 0),

                // 15. Complex pattern with larger size
                new CorrectOffsetInvocationParams()
                        .setPage(6)
                        .setPageSize(30)
                        .setSlotPatternDTO(15, 1, 2, 3, 7, 8, 9, 14, 15)
                        .setOverallSamplePrimaryCount(240)
                        .setOverallSampleSecondaryCount(240)
                        .setExpectedResult(3, 14, 0, 147, 16, 0)
        ).map(Arguments::of);
    }

    @MethodSource("provideDataForCorrectOffset")
    @ParameterizedTest
    void shouldCombineCorrect(CorrectOffsetInvocationParams params) {
        //WHEN
        Pair<OffsetLimitPageable, OffsetLimitPageable> actualResult = OffsetService.setOffsetLimit(
                params.page,
                params.pageSize,
                params.slotPatternDTO,
                params.overallSamplePrimaryCount,
                params.overallSampleSecondaryCount,
                params.sort);
        //THEN
        assertEquals(params.getExpectedResult(), actualResult);
    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    private static class CorrectOffsetInvocationParams {
        long page;
        long pageSize;
        SlotPatternDTO slotPatternDTO;
        long overallSamplePrimaryCount;
        long overallSampleSecondaryCount;
        Sort sort = Sort.unsorted();
        MutablePair<OffsetLimitPageable, OffsetLimitPageable> expectedResult;

        public CorrectOffsetInvocationParams setSlotPatternDTO(int patternLength, Integer... pattern) {
            this.slotPatternDTO = new SlotPatternDTO(Arrays.asList(pattern), patternLength);
            return this;
        }

        public CorrectOffsetInvocationParams setExpectedResult(int primaryGivenElements, int primarySampleSize, int primaryPageNumber,
                                                               int secondaryGivenElements, int secondarySampleSize, int secondaryPageNumber) {
            this.expectedResult = MutablePair.of(
                    new OffsetLimitPageable(primaryGivenElements, primarySampleSize, Sort.unsorted()),
                    new OffsetLimitPageable(secondaryGivenElements, secondarySampleSize, Sort.unsorted())
            );
            return this;
        }
    }
}