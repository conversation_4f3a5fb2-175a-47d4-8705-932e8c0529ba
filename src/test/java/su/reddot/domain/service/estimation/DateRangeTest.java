package su.reddot.domain.service.estimation;

import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertEquals;

class DateRangeTest {

    @Test
    public void toString_sameDayRu() {
        Locale locale = new Locale("ru", "RU");
        Locale.setDefault(locale);

        DateRange dateRange = new DateRange(
                LocalDate.of(2023, 4, 14),
                LocalDate.of(2023, 4, 14)
        );

        assertEquals("14 апреля", dateRange.toString());
    }

    @Test
    public void toString_sameMonthsRu() {
        Locale locale = new Locale("ru", "RU");
        Locale.setDefault(locale);

        DateRange dateRange = new DateRange(
                LocalDate.of(2023, 4, 14),
                LocalDate.of(2023, 4, 15)
        );

        assertEquals("14 — 15 апреля", dateRange.toString());
    }

    @Test
    public void toString_differentMonthsRu() {
        Locale locale = new Locale("ru", "RU");
        Locale.setDefault(locale);

        DateRange dateRange = new DateRange(
                LocalDate.of(2023, 4, 14),
                LocalDate.of(2023, 5, 15)
        );

        assertEquals("14 апреля — 15 мая", dateRange.toString());
    }

    @Test
    public void toString_sameDayEn() {
        Locale locale = new Locale("en", "US");
        Locale.setDefault(locale);

        DateRange dateRange = new DateRange(
                LocalDate.of(2023, 4, 14),
                LocalDate.of(2023, 4, 14)
        );

        assertEquals("April 14th", dateRange.toString());
    }

    @Test
    public void toString_sameMonthsEn() {
        Locale locale = new Locale("en", "US");
        Locale.setDefault(locale);

        DateRange dateRange = new DateRange(
                LocalDate.of(2023, 4, 14),
                LocalDate.of(2023, 4, 15)
        );

        assertEquals("April 14th — 15th", dateRange.toString());
    }

    @Test
    public void toString_differentMonthsEn() {
        Locale locale = new Locale("en", "US");
        Locale.setDefault(locale);

        DateRange dateRange = new DateRange(
                LocalDate.of(2023, 4, 14),
                LocalDate.of(2023, 5, 15)
        );

        assertEquals("April 14th — May 15th", dateRange.toString());
    }
}