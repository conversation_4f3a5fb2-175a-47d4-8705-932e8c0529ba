package su.reddot.domain.service.util;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import lombok.Data;
import lombok.experimental.Accessors;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.stream.Stream;

class GraphUtilsTest {

    private static Stream<Arguments> provideTestDataStream() {
        return Stream.of(
                        new ConnectedVertexTestData()
                                .setTestDataName("Null start vertex")
                                .setStartVertex(null)
                                .setGraphDescription(ImmutableMap.of(
                                        0, ImmutableSet.of(1)
                                ))
                                .setExpectedResult(Collections.emptySet()),
                        new ConnectedVertexTestData()
                                .setTestDataName("Alone vertex")
                                .setGraphDescription(ImmutableMap.of(
                                        0, Collections.emptySet()
                                ))
                                .setExpectedResult(ImmutableSet.of(0)),
                        new ConnectedVertexTestData()
                                .setTestDataName("Self connected vertex")
                                .setGraphDescription(ImmutableMap.of(
                                        0, ImmutableSet.of(0)
                                ))
                                .setExpectedResult(ImmutableSet.of(0)),
                        new ConnectedVertexTestData()
                                .setTestDataName("Graph with nonexistent top vertex")
                                .setGraphDescription(ImmutableMap.of(
                                        1, ImmutableSet.of(2, 3)
                                ))
                                .setExpectedResult(Collections.emptySet()),
                        new ConnectedVertexTestData()
                                .setTestDataName("Graph with nonexistent vertices")
                                .setGraphDescription(ImmutableMap.of(
                                        0, ImmutableSet.of(0, 1, 2)
                                ))
                                .setExpectedResult(ImmutableSet.of(0)),
                        new ConnectedVertexTestData()
                                .setTestDataName("Line 0 -> 1 -> 2")
                                .setGraphDescription(ImmutableMap.of(
                                        0, ImmutableSet.of(1),
                                        1, ImmutableSet.of(2)
                                ))
                                .setExpectedResult(ImmutableSet.of(0, 1, 2)),
                        new ConnectedVertexTestData()
                                .setTestDataName("Cycle 0 -> 1 -> 2 -> 0")
                                .setGraphDescription(ImmutableMap.of(
                                        0, ImmutableSet.of(1),
                                        1, ImmutableSet.of(2),
                                        2, ImmutableSet.of(0)
                                ))
                                .setExpectedResult(ImmutableSet.of(0, 1, 2)),
                        new ConnectedVertexTestData()
                                .setTestDataName("Tree: 0 -> 1, 0 -> 2, 1 -> 3, 1 -> 4, 2 -> 5")
                                .setGraphDescription(ImmutableMap.of(
                                        0, ImmutableSet.of(1, 2),
                                        1, ImmutableSet.of(3, 4),
                                        2, ImmutableSet.of(5)
                                ))
                                .setExpectedResult(ImmutableSet.of(0, 1, 2, 3, 4, 5)),
                        new ConnectedVertexTestData()
                                .setTestDataName("Line with cycle: 0 -> 1 -> 2 -> 3 -> 1")
                                .setGraphDescription(ImmutableMap.of(
                                        0, ImmutableSet.of(1),
                                        1, ImmutableSet.of(2),
                                        2, ImmutableSet.of(3),
                                        3, ImmutableSet.of(1)
                                ))
                                .setExpectedResult(ImmutableSet.of(0, 1, 2, 3)),
                        new ConnectedVertexTestData()
                                .setTestDataName("Input graph with extra vertices")
                                .setGraphDescription(ImmutableMap.of(
                                        0, ImmutableSet.of(1),
                                        1, ImmutableSet.of(2),
                                        3, ImmutableSet.of(2),
                                        4, ImmutableSet.of(3, 5),
                                        5, ImmutableSet.of(1)
                                ))
                                .setExpectedResult(ImmutableSet.of(0, 1, 2))
                        )
                .map(Arguments::of);
    }

    @MethodSource("provideTestDataStream")
    @ParameterizedTest
    void findAllConnectedVertex(ConnectedVertexTestData inputData) {
        //WHEN
        Set<Integer> actualResult = callFindAllConnectedVertex(inputData);
        //THEN
        Assertions.assertIterableEquals(inputData.expectedResult, actualResult,
                String.format("Test for method: GraphUtils.findAllConnectedVertex test data name: %s", inputData.testDataName));
    }

    private Set<Integer> callFindAllConnectedVertex(ConnectedVertexTestData connectedVertexTestData) {
        return GraphUtils.findAllConnectedVertex(
                connectedVertexTestData.startVertex,
                connectedVertexTestData.graphDescription);
    }

    @Data
    @Accessors(chain = true)
    private static class ConnectedVertexTestData {

        private String testDataName;
        private Integer startVertex = 0;
        private Map<Integer, Set<Integer>> graphDescription;
        private Set<Integer> expectedResult;
    }
}