package su.reddot.domain.service.util;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import su.reddot.infrastructure.configparam.dto.SlotPatternDTO;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

class MergeBySlotsUtilsTest {

    private static Stream<Arguments> provideDataForCorrectCombine() {
        return Stream.of(
                //pattern: [2], length: 2
                //Only target collection
                new CorrectCombineInvocationParams()
                        .setSlotPatternDTO(2, 2)
                        .setTargetCollection("10", "11", "12", "13", "14")
                        .setPageNumber(0)
                        .setPageLength(10)
                        .setExpectedResult("10", "11", "12", "13", "14"),
                //Only merge collection
                new CorrectCombineInvocationParams()
                        .setSlotPatternDTO(2, 2)
                        .setMergeCollection("20", "21", "22", "23", "24")
                        .setPageNumber(0)
                        .setPageLength(10)
                        .setExpectedResult("20", "21", "22", "23", "24"),
                //Full target collection and full merge collection
                new CorrectCombineInvocationParams()
                        .setSlotPatternDTO(2, 2)
                        .setTargetCollection("10", "11", "12", "13", "14")
                        .setMergeCollection("20", "21", "22", "23", "24")
                        .setPageNumber(0)
                        .setPageLength(10)
                        .setExpectedResult("10", "20", "11", "21", "12", "22", "13", "23", "14", "24"),
                //Full target collection and not full merge collection
                new CorrectCombineInvocationParams()
                        .setSlotPatternDTO(2, 2)
                        .setTargetCollection("10", "11", "12", "13", "14")
                        .setMergeCollection("20")
                        .setPageNumber(0)
                        .setPageLength(10)
                        .setExpectedResult("10", "20", "11", "12", "13", "14"),
                //Not full target collection and full merge collection
                new CorrectCombineInvocationParams()
                        .setSlotPatternDTO(2, 2)
                        .setTargetCollection("10", "11")
                        .setMergeCollection("20", "21", "22", "23", "24")
                        .setPageNumber(0)
                        .setPageLength(10)
                        .setExpectedResult("10", "20", "11", "21", "22", "23", "24"),
                new CorrectCombineInvocationParams()
                        .setSlotPatternDTO(2, 2)
                        .setTargetCollection("10", "11", null, "13", "14")
                        .setMergeCollection("20", "21", "22", "23", "24")
                        .setPageNumber(0)
                        .setPageLength(10)
                        .setExpectedResult("10", "20", "11", "21", null, "22", "13", "23", "14", "24"),
                new CorrectCombineInvocationParams()
                        .setSlotPatternDTO(2, 2)
                        .setTargetCollection("10", "11", "12", "13", "14")
                        .setMergeCollection("20", "21", null, "23", "24")
                        .setPageNumber(0)
                        .setPageLength(10)
                        .setExpectedResult("10", "20", "11", "21", "12", null, "13", "23", "14", "24"),
                //pattern: [3], length: 3
                new CorrectCombineInvocationParams()
                        .setSlotPatternDTO(3, 3)
                        .setTargetCollection("10", "11", "12", "13")
                        .setMergeCollection("20")
                        .setPageNumber(0)
                        .setPageLength(5)
                        .setExpectedResult("10", "11", "20", "12", "13"),
                new CorrectCombineInvocationParams()
                        .setSlotPatternDTO(3, 3)
                        .setTargetCollection("10", "11", "12")
                        .setMergeCollection("20", "21")
                        .setPageNumber(1)
                        .setPageLength(5)
                        .setExpectedResult("20", "10", "11", "21", "12"),
                new CorrectCombineInvocationParams()
                        .setSlotPatternDTO(3, 3)
                        .setTargetCollection("10", "11")
                        .setMergeCollection("20", "21", "22")
                        .setPageNumber(1)
                        .setPageLength(5)
                        .setExpectedResult("20", "10", "11", "21", "22"),
                new CorrectCombineInvocationParams()
                        .setSlotPatternDTO(3, 3)
                        .setTargetCollection("10")
                        .setMergeCollection("20", "21", "22", "23")
                        .setPageNumber(1)
                        .setPageLength(5)
                        .setExpectedResult("20", "10", "21", "22", "23"),
                //pattern: [1, 3], length: 5
                new CorrectCombineInvocationParams()
                        .setSlotPatternDTO(5, 1, 3)
                        .setTargetCollection("10", "11", "12", "13")
                        .setMergeCollection("20", "21", "22", "23")
                        .setPageNumber(0)
                        .setPageLength(8)
                        .setExpectedResult("20", "10", "21", "11", "12", "22", "13", "23"),
                new CorrectCombineInvocationParams()
                        .setSlotPatternDTO(5, 1, 3)
                        .setTargetCollection("10", "11", "12", "13", "14")
                        .setMergeCollection("20", "21", "22")
                        .setPageNumber(1)
                        .setPageLength(8)
                        .setExpectedResult("10", "11", "20", "12", "21", "13", "14", "22"),
                //pattern: [1, 3, 7, 10], length: 10
                new CorrectCombineInvocationParams()
                        .setSlotPatternDTO(10, 1, 3, 7, 10)
                        .setTargetCollection("10", "11", "12", "13", "14", "15", "16", "17", "18")
                        .setMergeCollection("20", "21", "22", "23", "24", "25")
                        .setPageNumber(0)
                        .setPageLength(15)
                        .setExpectedResult("20", "10", "21", "11", "12", "13", "22", "14", "15", "23", "24", "16", "25", "17", "18"),
                new CorrectCombineInvocationParams()
                        .setSlotPatternDTO(10, 1, 3, 7, 10)
                        .setTargetCollection("19", "110", "111", "112", "113", "114", "115", "116", "117")
                        .setMergeCollection("26", "27", "28", "29", "210", "211")
                        .setPageNumber(1)
                        .setPageLength(15)
                        .setExpectedResult("19", "26", "110", "111", "27", "28", "112", "29", "113", "114", "115", "210", "116", "117", "211"),
                //для данного паттерна 3ая страница должна быть смешанна так же как и первая страница
                new CorrectCombineInvocationParams()
                        .setSlotPatternDTO(10, 1, 3, 7, 10)
                        .setTargetCollection("10", "11", "12", "13", "14", "15", "16", "17", "18")
                        .setMergeCollection("20", "21", "22", "23", "24", "25")
                        .setPageNumber(2)
                        .setPageLength(15)
                        .setExpectedResult("20", "10", "21", "11", "12", "13", "22", "14", "15", "23", "24", "16", "25", "17", "18"),
                //паттерн должен корректно укладываться в страницу вдвое большую чем размер паттерна, для любого номера страницы
                new CorrectCombineInvocationParams()
                        .setSlotPatternDTO(10, 1, 3, 7, 10)
                        .setTargetCollection("10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "110", "111")
                        .setMergeCollection("20", "21", "22", "23", "24", "25", "26", "27")
                        .setPageNumber(0)
                        .setPageLength(20)
                        .setExpectedResult("20", "10", "21", "11", "12", "13", "22", "14", "15", "23", "24", "16", "25", "17", "18", "19", "26", "110", "111", "27"),
                new CorrectCombineInvocationParams()
                        .setSlotPatternDTO(10, 1, 3, 7, 10)
                        .setTargetCollection("10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "110", "111")
                        .setMergeCollection("20", "21", "22", "23", "24", "25", "26", "27")
                        .setPageNumber(1)
                        .setPageLength(20)
                        .setExpectedResult("20", "10", "21", "11", "12", "13", "22", "14", "15", "23", "24", "16", "25", "17", "18", "19", "26", "110", "111", "27"),
                new CorrectCombineInvocationParams()
                        .setSlotPatternDTO(10, 1, 3, 7, 10)
                        .setTargetCollection("10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "110", "111")
                        .setMergeCollection("20", "21", "22", "23", "24", "25", "26", "27")
                        .setPageNumber(1000)
                        .setPageLength(20)
                        .setExpectedResult("20", "10", "21", "11", "12", "13", "22", "14", "15", "23", "24", "16", "25", "17", "18", "19", "26", "110", "111", "27"),
                new CorrectCombineInvocationParams()
                        .setSlotPatternDTO(4, 3, 4)
                        .setTargetCollection("10", "11", "12", "13", "14", "15")
                        .setMergeCollection("20", "21", "22", "23", "24", "25")
                        .setPageNumber(0)
                        .setPageLength(12)
                        .setExpectedResult("10", "11", "20", "21", "12", "13", "22", "23", "14", "15", "24", "25"),
                new CorrectCombineInvocationParams()
                        .setSlotPatternDTO(4, 3, 4)
                        .setTargetCollection("10", "11", "12", "13", "14")
                        .setMergeCollection("20", "21", "22", "23", "24")
                        .setPageNumber(0)
                        .setPageLength(10)
                        .setExpectedResult("10", "11", "20", "21", "12", "13", "22", "23", "14", "24")
        ).map(Arguments::of);
    }

    @MethodSource("provideDataForCorrectCombine")
    @ParameterizedTest
    public void shouldCombineCorrect(CorrectCombineInvocationParams params) {
        //WHEN
        List<String> actualResult = MergeBySlotsUtils.mergeBySlotPattern(
                params.slotPatternDTO,
                params.targetCollection,
                params.mergeCollection,
                params.pageNumber,
                params.pageLength);
        //THEN
        assertArrayEquals(params.getExpectedResult().toArray(new String[]{}), actualResult.toArray(new String[]{}));
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    private static class CorrectCombineInvocationParams {
        private SlotPatternDTO slotPatternDTO;
        private List<String> targetCollection;
        private List<String> mergeCollection;
        private long pageNumber;
        private int pageLength;
        private List<String> expectedResult;

        public CorrectCombineInvocationParams setSlotPatternDTO(int patternLength, Integer... pattern) {
            this.slotPatternDTO = new SlotPatternDTO(Arrays.asList(pattern), patternLength);
            return this;
        }

        public CorrectCombineInvocationParams setTargetCollection(String... targetCollection) {
            this.targetCollection = Arrays.asList(targetCollection);
            return this;
        }

        public CorrectCombineInvocationParams setMergeCollection(String... mergeCollection) {
            this.mergeCollection = Arrays.asList(mergeCollection);
            return this;
        }

        public CorrectCombineInvocationParams setExpectedResult(String... expectedResult) {
            this.expectedResult = Arrays.asList(expectedResult);
            return this;
        }
    }

}