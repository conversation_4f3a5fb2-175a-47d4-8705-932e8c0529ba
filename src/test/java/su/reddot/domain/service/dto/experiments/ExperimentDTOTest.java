package su.reddot.domain.service.dto.experiments;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.HashMap;

import org.junit.jupiter.api.Test;

class ExperimentDTOTest {
    /**
     * Тестирование метода isOn() с разными значениями входящих параметров
     */
    @Test
    void testIsOn() {
        // Arrange, Act and Assert
        assertFalse((new ExperimentDTO()).isOn());
        assertFalse((new ExperimentDTO(1L, "Key", "ABC123", 1L, "42", new HashMap<>())).isOn());
        assertFalse((new ExperimentDTO(1L, "RESALE_AND_BRAND_NEW_FILTER", "ABC123", 1L, "42", new HashMap<>())).isOn());
        assertTrue((new ExperimentDTO(1L, "CATALOG_SLOT_PATTERN", "ABC123", 1L, "42", new HashMap<>())).isOn());
        assertTrue((new ExperimentDTO(1L, "RESALE_AND_BRAND_NEW_FILTER", "ABC123", 1L, "On", new HashMap<>())).isOn());
    }
}
