package su.reddot.domain.service.order.track.positiondetails;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

public class OskellyAppHtmlWrapperTest {

    @Test
    public void shouldWrapWithTags() {
        StringBuilder stringBuilder = new StringBuilder();
        OskellyAppHtmlWrapper.wrapWithTags(
            stringBuilder, "a some text",
            OskellyAppHtmlTags.ITALIC, OskellyAppHtmlTags.PARAGRAPH
        );
        String html = stringBuilder.toString();
        Assertions.assertThat(html).isEqualTo("<p><italic>a some text</italic></p>\n");
    }

    @Test
    public void shouldWrapWithTags_colored() {
        StringBuilder stringBuilder = new StringBuilder();
        OskellyAppHtmlWrapper.wrapWithTags(
            stringBuilder, "a some other text",
            OskellyAppHtmlTags.ITALIC, OskellyAppHtmlTags.PARAGRAPH
        );
        String html = stringBuilder.toString();
        Assertions.assertThat(html).isEqualTo("<p><italic>a some other text</italic></p>\n");
    }

    @Test
    public void shouldWrapSeveralTexts_colored() {
        StringBuilder stringBuilder = new StringBuilder();
        OskellyAppHtmlWrapper.wrapWithTags(
            stringBuilder, "a some other text",
            OskellyAppHtmlTags.ITALIC, OskellyAppHtmlTags.PARAGRAPH
        );
        OskellyAppHtmlWrapper.wrapWithTags(
            stringBuilder, "the next paragraph text",
            OskellyAppHtmlTags.PARAGRAPH
        );
        String html = stringBuilder.toString();
        Assertions.assertThat(html).isEqualTo(
            "<p><italic>a some other text</italic></p>\n" +
                "<p>the next paragraph text</p>\n"
        );
    }
}