package su.reddot.domain.service.order.track.utils;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

class MessageCodeUtilsTest {

    @Test
    public void shouldBuildCodeOfSeveralParts() {
        String code = MessageCodeUtils.buildCode();
        Assertions.assertThat(code).isEqualTo("");

        code = MessageCodeUtils.buildCode("part1");
        Assertions.assertThat(code).isEqualTo("part1");

        code = MessageCodeUtils.buildCode("part1", "part2");
        Assertions.assertThat(code).isEqualTo("part1.part2");

        code = MessageCodeUtils.buildCode("part1", "part2", "part3");
        Assertions.assertThat(code).isEqualTo("part1.part2.part3");

        code = MessageCodeUtils.buildCode(null);
        Assertions.assertThat(code).isEqualTo("");

        code = MessageCodeUtils.buildCode("");
        Assertions.assertThat(code).isEqualTo("");

        code = MessageCodeUtils.buildCode("part1", null);
        Assertions.assertThat(code).isEqualTo("part1");

        code = MessageCodeUtils.buildCode("part1", "");
        Assertions.assertThat(code).isEqualTo("part1");

        code = MessageCodeUtils.buildCode("part1", "part2", null);
        Assertions.assertThat(code).isEqualTo("part1.part2");

        code = MessageCodeUtils.buildCode("part1", "part2", "");
        Assertions.assertThat(code).isEqualTo("part1.part2");

        code = MessageCodeUtils.buildCode("", "part2", "part3");
        Assertions.assertThat(code).isEqualTo("part2.part3");

        code = MessageCodeUtils.buildCode(null, "part2", "part3");
        Assertions.assertThat(code).isEqualTo("part2.part3");

        code = MessageCodeUtils.buildCode("part1", "", "part3", "part4");
        Assertions.assertThat(code).isEqualTo("part1.part3.part4");

        code = MessageCodeUtils.buildCode("part1", null, "part3", "part4");
        Assertions.assertThat(code).isEqualTo("part1.part3.part4");
    }
}