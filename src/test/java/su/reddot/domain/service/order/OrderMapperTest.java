package su.reddot.domain.service.order;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import su.reddot.domain.model.logistic.LogisticsInfo;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.service.order.impl.OrderMapper;
import su.reddot.infrastructure.logistic.CommonLogisticService;

import java.time.LocalDate;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class OrderMapperTest {

    @Mock
    private CommonLogisticService commonLogisticService;

    @InjectMocks
    private OrderMapper orderMapper;

    @Test
    public void getPickupFromSellerDateAndTime_ruVersion() {
        Mockito.when(commonLogisticService.getPickupTimeIntervalUserDescription(Mockito.any())).thenReturn("с 9:00 до 18:00");

        Locale locale = new Locale("ru", "RU");
        Locale.setDefault(locale);

        LogisticsInfo logisticsInfo = new LogisticsInfo();
        logisticsInfo.setPickupDate(LocalDate.of(2023,4,10));

        Order order = new Order();
        order.setSellerLogisticsInfo(logisticsInfo);

        String result = orderMapper.getPickupFromSellerDateAndTime(order);

        assertEquals("10 апреля с 9:00 до 18:00", result);
    }

    @Test
    public void getPickupFromSellerDateAndTime_enVersion() {
        Mockito.when(commonLogisticService.getPickupTimeIntervalUserDescription(Mockito.any())).thenReturn("from 9:00 to 18:00");

        Locale locale = new Locale("en", "US");
        Locale.setDefault(locale);

        LogisticsInfo logisticsInfo = new LogisticsInfo();
        logisticsInfo.setPickupDate(LocalDate.of(2023,4,10));

        Order order = new Order();
        order.setSellerLogisticsInfo(logisticsInfo);

        String result = orderMapper.getPickupFromSellerDateAndTime(order);

        assertEquals("April 10th from 9:00 to 18:00", result);
    }

}