package ru.oskelly.tests;

import org.springframework.test.context.transaction.TestTransaction;

public abstract class SpringTestWithDb extends SpringTestWithCtxCounterIT {

    public void commitAndStartNewTransaction() {
        TestTransaction.flagForCommit();
        TestTransaction.end();
        TestTransaction.start();
    }

    public void rollbackAndStartNewTransaction() {
        TestTransaction.flagForRollback();
        TestTransaction.end();
        TestTransaction.start();
    }

    protected void commitTransaction() {
        TestTransaction.flagForCommit();
        TestTransaction.end();
    }

    protected void rollbackTransaction() {
        TestTransaction.flagForRollback();
        TestTransaction.end();
    }

    protected void startNewTransaction() {
        TestTransaction.start();
    }

}