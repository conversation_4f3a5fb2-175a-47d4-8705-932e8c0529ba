package ru.oskelly.tests.pr.common.bonuses;

import su.reddot.domain.exception.bonuses.BonusesBadRequestException;
import su.reddot.domain.service.bonuses.BonusesControllerAPI;
import su.reddot.domain.service.bonuses.model.Amount;
import su.reddot.domain.service.bonuses.model.BalanceDTO;
import su.reddot.domain.service.bonuses.model.NotificationStage;
import su.reddot.domain.service.bonuses.model.ResponseBodyBalanceDTO;
import su.reddot.domain.service.bonuses.model.ResponseBodyListTransactionWithBalanceDTO;
import su.reddot.domain.service.bonuses.model.ResponseBodyTransactionWithBalanceDTO;
import su.reddot.domain.service.bonuses.model.TransactionDTO;
import su.reddot.domain.service.bonuses.model.TransactionWithBalanceDTO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

public class BonusesControllerApiStub extends BonusesControllerAPI {

    private static final String BONUSES_TEST_EXCEPTION = "test";
    private static final Long BONUSES_TEST_EXCEPTION_ORDER_ID = 2L;

    private final Random random = new Random();

    private final Map<Long, ResponseBodyTransactionWithBalanceDTO> data = new HashMap<>();

    private final BalanceDTO balance;

    private boolean exceptionThrowing = true;

    public BonusesControllerApiStub() {
        super(null);
        balance = new BalanceDTO();
        Amount amount = new Amount();
        amount.setBonuses(new BigDecimal(1000));
        amount.setMoney(new BigDecimal(500));
        amount.setTotalAmount(new BigDecimal(1500));
        balance.setAmount(amount);
    }

    public void setBalance(BigDecimal bonuses, BigDecimal money) {
        Amount amount = new Amount();
        amount.setBonuses(bonuses);
        amount.setMoney(money);
        amount.setTotalAmount(bonuses.add(money));
        balance.setAmount(amount);
    }

    public void setIsExceptionThrowing(boolean enableThrowing) {
        exceptionThrowing = enableThrowing;
    }

    @Override
    public ResponseBodyBalanceDTO getAccountCurrentBonusesBalance(String accountId) {
        ResponseBodyBalanceDTO result = new ResponseBodyBalanceDTO();
        result.setData(balance);
        return result;
    }

    @Override
    public ResponseBodyTransactionWithBalanceDTO returnBonuses(String accountId, BonusesOrderAmount orderAmount, String idempotencyKey) {
        ResponseBodyTransactionWithBalanceDTO result = createTransaction(accountId, orderAmount, null, null, TransactionDTO.TrnTypeEnum.RETURN, TransactionDTO.StateEnum.COMMITTED, idempotencyKey);
        assert result.getData() != null;
        data.put(result.getData().getTransaction().getId(), result);
        return result;
    }

    @Override
    public ResponseBodyTransactionWithBalanceDTO transferBonuses(
            String accountId, BonusesOrderAmount orderAmount, Long authorId, LocalDate dateEnd, String description, String reason, List<NotificationStage> stages, String idempotencyKey, Boolean silentMode
    ) {
        ResponseBodyTransactionWithBalanceDTO result = createTransaction(accountId, orderAmount, description, reason, TransactionDTO.TrnTypeEnum.TRANSFER, TransactionDTO.StateEnum.COMMITTED, idempotencyKey);
        assert result.getData() != null;
        Amount amount = balance.getAmount();
        assert amount.getBonuses() != null;
        amount.setBonuses(amount.getBonuses().add(orderAmount.getBonuses()));
        assert amount.getMoney() != null;
        amount.setMoney(amount.getMoney().add(orderAmount.getMoney()));
        assert amount.getTotalAmount() != null;
        amount.setTotalAmount(amount.getTotalAmount().add(orderAmount.getTotal()));
        data.put(result.getData().getTransaction().getId(), result);
        return result;
    }

    @Override
    public ResponseBodyTransactionWithBalanceDTO withdrawBonusesManual(
            String accountId, BonusesOrderAmount orderAmount, Long authorId, String description, String reason, String idempotencyKey
    ) {
        ResponseBodyTransactionWithBalanceDTO result = createTransaction(accountId, orderAmount, description, reason, TransactionDTO.TrnTypeEnum.WITHDRAW, TransactionDTO.StateEnum.COMMITTED, idempotencyKey);
        assert result.getData() != null;
        Amount balanceAmount = balance.getAmount();
        assert balanceAmount.getBonuses() != null;
        balanceAmount.setBonuses(balanceAmount.getBonuses().subtract(orderAmount.getBonuses()));
        assert balanceAmount.getMoney() != null;
        balanceAmount.setMoney(balanceAmount.getMoney().subtract(orderAmount.getMoney()));
        assert balanceAmount.getTotalAmount() != null;
        balanceAmount.setTotalAmount(balanceAmount.getBonuses().add(balanceAmount.getMoney()));
        data.put(result.getData().getTransaction().getId(), result);
        return result;
    }

    @Override
    public ResponseBodyTransactionWithBalanceDTO withdrawBonusesCancel(String accountId, Long transactionId) {
        return changeTransactionStateFromHoldState(transactionId, TransactionDTO.StateEnum.CANCELED);
    }

    @Override
    public ResponseBodyTransactionWithBalanceDTO withdrawBonusesCommit(String accountId, Long transactionId, Long orderId) {
        return changeTransactionStateFromHoldState(transactionId, TransactionDTO.StateEnum.COMMITTED);
    }

    @Override
    public ResponseBodyTransactionWithBalanceDTO withdrawBonusesHold(
            String accountId, BonusesOrderAmount orderAmount, Long authorId, String description, String reason, String idempotencyKey
    ) {
        ResponseBodyTransactionWithBalanceDTO result = createTransaction(accountId, orderAmount, description, reason, TransactionDTO.TrnTypeEnum.WITHDRAW, TransactionDTO.StateEnum.HOLD, idempotencyKey);
        assert result.getData() != null;
        data.put(result.getData().getTransaction().getId(), result);
        return result;
    }

    @Override
    public ResponseBodyListTransactionWithBalanceDTO withdrawBonusesSplit(String accountId, Long transactionId, List<BonusesOrderAmountSplit> orderAmounts) {
        List<ResponseBodyTransactionWithBalanceDTO> trns = new ArrayList<>();

        ResponseBodyTransactionWithBalanceDTO sourceTrn = data.get(transactionId);
        assert sourceTrn.getData() != null;
        sourceTrn.getData().getTransaction().setState(TransactionDTO.StateEnum.CANCELED);

        for (BonusesOrderAmountSplit orderAmount: orderAmounts) {
            BonusesOrderAmount newOrderAmount = new BonusesOrderAmount(
                    orderAmount.getAmount().getOrderId(),
                    orderAmount.getAmount().getBonuses() == null ? BigDecimal.ZERO : orderAmount.getAmount().getBonuses(),
                    orderAmount.getAmount().getMoney() == null ? BigDecimal.ZERO : orderAmount.getAmount().getMoney(),
                    null
            );
            ResponseBodyTransactionWithBalanceDTO trn = createTransaction(
                    accountId, newOrderAmount, "", "", TransactionDTO.TrnTypeEnum.WITHDRAW, TransactionDTO.StateEnum.COMMITTED, orderAmount.getIdempotencyKey()
            );
            trns.add(trn);
            assert trn.getData() != null;
            data.put(trn.getData().getTransaction().getId(), trn);
        }

        ResponseBodyListTransactionWithBalanceDTO result = new ResponseBodyListTransactionWithBalanceDTO();
        result.setData(
                trns.stream().map(ResponseBodyTransactionWithBalanceDTO::getData).collect(Collectors.toList())
        );
        return result;
    }

    private ResponseBodyTransactionWithBalanceDTO createTransaction(
            String accountId, BonusesOrderAmount orderAmount, String description, String reason, TransactionDTO.TrnTypeEnum type, TransactionDTO.StateEnum state, String idempotencyKey
    ) {
        if (BONUSES_TEST_EXCEPTION_ORDER_ID.equals(orderAmount.getOrderId()) && exceptionThrowing) {
            throw new RuntimeException(BONUSES_TEST_EXCEPTION);
        }

        if (data.values().stream().anyMatch(p -> {
            assert p.getData() != null;
            return p.getData().getTransaction().getIdempotencyKey().equals(idempotencyKey);
        })) {
            throw new BonusesBadRequestException("", "");
        }

        ResponseBodyTransactionWithBalanceDTO result = new ResponseBodyTransactionWithBalanceDTO();
        TransactionWithBalanceDTO twb = new TransactionWithBalanceDTO();
        TransactionDTO t = new TransactionDTO();
        t.setBonusesAmount(orderAmount.getBonuses());
        t.setMoneyAmount(orderAmount.getMoney());
        long nextLong = random.nextLong();
        t.setId(nextLong < 0 ? -nextLong : nextLong);
        t.setOrderId(orderAmount.getOrderId());
        t.setDescription(description);
        t.setReason(reason);
        t.setAccountId(accountId);
        t.setTrnDate(OffsetDateTime.now());
        t.setTrnType(type);
        t.setState(state);
        t.setIdempotencyKey(idempotencyKey);
        twb.setTransaction(t);
        result.setData(twb);
        return result;
    }

    private ResponseBodyTransactionWithBalanceDTO changeTransactionStateFromHoldState(
            Long bonusesTransactionId, TransactionDTO.StateEnum state
    ) {
        ResponseBodyTransactionWithBalanceDTO result = data.get(bonusesTransactionId);

        assert result.getData() != null;

        if (!TransactionDTO.StateEnum.HOLD.equals(result.getData().getTransaction().getState())) {
            throw new RuntimeException(BONUSES_TEST_EXCEPTION);
        }
        result.getData().getTransaction().setState(state);

        return result;
    }
}