package ru.oskelly.tests.pr.common.bonuses;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import su.reddot.domain.service.bonuses.BonusesControllerAPI;

@TestConfiguration
public class BonusesServiceTestConfiguration {
    @Bean
    @Primary
    public BonusesControllerAPI testBonusesControllerAPI() {
        return new BonusesControllerApiStub();
    }
}
