package ru.oskelly.tests.pr.suite1_2.domain.service.device;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.device.DeviceRepository;
import su.reddot.domain.model.device.Device;
import su.reddot.domain.model.device.DeviceDtype;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.domain.service.device.DeviceService;
import su.reddot.infrastructure.configuration.OskellyApplication;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(SpringExtension.class)
@ActiveProfiles(AbstractSpringTest.testProfiles)
@SpringBootTest(classes = OskellyApplication.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class DeviceServiceTest {

    public static final String TEST_TOKEN = "test_token";
    @Autowired
    DeviceService deviceService;
    @Autowired
    UserRepository userRepository;
    @Autowired
    DeviceRepository deviceRepository;
    @Autowired
    CommissionGridService commissionGridService;

    @Test
    public void deleteExpiredTokensDeletesTokens() {

        User user = new User()
                .setNickname("test_nickname")
                .setUserType(User.UserType.SIMPLE_USER)
                .setChangeTime(LocalDateTime.now())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        user = userRepository.save(user);

        Device device = new Device()
                .setUserId(user.getId())
                .setDtype(DeviceDtype.AppleDevice)
                .setCreateTime(ZonedDateTime.now())
                .setToken(TEST_TOKEN);

        device = deviceRepository.save(device);

        deviceService.deleteExpiredTokens(Collections.singletonList(TEST_TOKEN));

        Optional<Device> deviceOptional = deviceRepository.findById(device.getId());
        assertThat(deviceOptional).isPresent();
        device = deviceOptional.get();
        assertThat(device.getToken()).isNull();

        deviceRepository.delete(device);
        userRepository.delete(user);
    }
}
