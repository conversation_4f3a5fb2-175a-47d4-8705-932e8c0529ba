package ru.oskelly.tests.pr.suite1_2.domain.service.export;

import javax.xml.namespace.NamespaceContext;
import javax.xml.stream.XMLStreamWriter;

/**
 * Dummy implementation of {@link XMLStreamWriter} which does nothing (for unit tests).
 */
public class DummyXMLStreamWriter implements XMLStreamWriter {

    @Override
    public void writeStartElement(String localName) {
    }

    @Override
    public void writeStartElement(String namespaceURI, String localName) {
    }

    @Override
    public void writeStartElement(String prefix, String localName, String namespaceURI) {
    }

    @Override
    public void writeEmptyElement(String namespaceURI, String localName) {
    }

    @Override
    public void writeEmptyElement(String prefix, String localName, String namespaceURI) {
    }

    @Override
    public void writeEmptyElement(String localName) {
    }

    @Override
    public void writeEndElement() {
    }

    @Override
    public void writeEndDocument() {
    }

    @Override
    public void close() {
    }

    @Override
    public void flush() {
    }

    @Override
    public void writeAttribute(String localName, String value) {
    }

    @Override
    public void writeAttribute(String prefix, String namespaceURI, String localName, String value) {
    }

    @Override
    public void writeAttribute(String namespaceURI, String localName, String value) {
    }

    @Override
    public void writeNamespace(String prefix, String namespaceURI) {
    }

    @Override
    public void writeDefaultNamespace(String namespaceURI) {
    }

    @Override
    public void writeComment(String data) {
    }

    @Override
    public void writeProcessingInstruction(String target) {
    }

    @Override
    public void writeProcessingInstruction(String target, String data) {
    }

    @Override
    public void writeCData(String data) {
    }

    @Override
    public void writeDTD(String dtd) {
    }

    @Override
    public void writeEntityRef(String name) {
    }

    @Override
    public void writeStartDocument() {
    }

    @Override
    public void writeStartDocument(String version) {
    }

    @Override
    public void writeStartDocument(String encoding, String version) {
    }

    @Override
    public void writeCharacters(String text) {
    }

    @Override
    public void writeCharacters(char[] text, int start, int len) {
    }

    @Override
    public String getPrefix(String uri) {
        return null;
    }

    @Override
    public void setPrefix(String prefix, String uri) {
    }

    @Override
    public void setDefaultNamespace(String uri) {
    }

    @Override
    public void setNamespaceContext(NamespaceContext context) {
    }

    @Override
    public NamespaceContext getNamespaceContext() {
        return null;
    }

    @Override
    public Object getProperty(String name) throws IllegalArgumentException {
        return null;
    }
}
