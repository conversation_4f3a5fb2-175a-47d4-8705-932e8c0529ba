package ru.oskelly.tests.pr.suite1_2.domain.service.like;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.MockPublisherConfiguration;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.BrandRepository;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.like.BrandLikeRepository;
import su.reddot.domain.dao.like.ProductLikeRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.event.OskellyFanoutEvent;
import su.reddot.domain.model.Brand;
import su.reddot.domain.model.like.BrandLike;
import su.reddot.domain.model.like.ProductLike;
import su.reddot.domain.model.notification.like.NewProductLikeNotification;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.domain.service.like.ChangeWishlistEvent;
import su.reddot.domain.service.like.LikeService;
import su.reddot.domain.service.notification.NewLikeEvent;
import su.reddot.domain.service.product.PriceChangedEvent;
import su.reddot.infrastructure.configuration.OskellyApplication;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {OskellyApplication.class, MockPublisherConfiguration.class})
@ActiveProfiles(profiles = AbstractSpringTest.testProfiles)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class DefaultLikeServiceTest {

    @Autowired
    LikeService defaultLikeService;
    @Autowired
    UserRepository userRepository;

    @Autowired
    BrandRepository brandRepository;
    @Autowired
    ProductRepository productRepository;

    @Autowired
    ApplicationEventPublisher publisher;
    @Autowired
    ProductLikeRepository productLikeRepository;
    @Autowired
    BrandLikeRepository brandLikeRepository;
    @Autowired
    CommissionGridService commissionGridService;

    private User currentUser;
    private Product likeableProduct;

    @BeforeEach
    public void init() {
        User user = new User()
                .setNickname(RandomStringUtils.randomAlphabetic(5))
                .setUserType(User.UserType.SIMPLE_USER)
                .setChangeTime(LocalDateTime.now())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        currentUser = userRepository.save(user);


        Product product = new Product();
        product.setBrand(brandRepository.getOne(1L));
        product.setCategoryId(2L);
        product.setSeller(currentUser);
        product.setStartPrice(BigDecimal.valueOf(10000));
        product.setCurrentPrice(BigDecimal.valueOf(9000));
        product.setProductState(ProductState.PUBLISHED);
        product.setPublishTime(LocalDateTime.now().minusMonths(3).minusDays(1));

        likeableProduct = productRepository.save(product);
    }

    @AfterEach
    public void cleanup() {
        productLikeRepository.deleteAll(productLikeRepository.findAllByUser(currentUser));
        brandLikeRepository.deleteAll(brandLikeRepository.findAllByUser(currentUser));
        productRepository.delete(likeableProduct);
        userRepository.delete(currentUser);
        reset(publisher);
    }


    @Test
    public void onLikePublishChangeWishlistEvent() {
        defaultLikeService.like(likeableProduct, currentUser);

        ChangeWishlistEvent expectedEvent = new ChangeWishlistEvent(Collections.singletonList(
                new ChangeWishlistEvent.ProductDto()
                        .setProductId(likeableProduct.getId())
                        .setPrice(likeableProduct.getCurrentPrice().doubleValue())
        ));

        verify(publisher).publishEvent(any(NewLikeEvent.class));
        verify(publisher, times(2)).publishEvent(any(OskellyFanoutEvent.class));

        verify(publisher).publishEvent(eq(expectedEvent));
    }


    @Test
    public void onDislikePublishChangeWishlistEvent() {
        defaultLikeService.like(likeableProduct, currentUser);
        defaultLikeService.dislike(likeableProduct, currentUser);

        ChangeWishlistEvent expectedEvent = new ChangeWishlistEvent(Collections.singletonList(
                new ChangeWishlistEvent.ProductDto()
                        .setProductId(likeableProduct.getId())
                        .setPrice(likeableProduct.getCurrentPrice().doubleValue())
        ));


        verify(publisher, times(4)).publishEvent(any(OskellyFanoutEvent.class));
        verify(publisher).publishEvent(eq(expectedEvent));
    }

    @Test
    public void onPriceChangeSendsChangeWishlistEvent() {
        ProductLike productLike = new ProductLike();
        productLike.setProduct(likeableProduct);
        productLike.setUser(currentUser);
        productLike.setCreateTime(ZonedDateTime.now());

        productLikeRepository.save(productLike);


        doNothing().when(publisher).publishEvent(any());

        // Тут важно именно то что цену итоговую берем именно из продукта из ивента, хотя в реальности такого расхождения цены в ивенте и продукте быть не должно
        defaultLikeService.onPriceChanged(new PriceChangedEvent(likeableProduct, likeableProduct.getCurrentPrice(), BigDecimal.valueOf(4000)));

        ChangeWishlistEvent expectedEvent = new ChangeWishlistEvent(Collections.singletonList(
                new ChangeWishlistEvent.ProductDto()
                        .setProductId(likeableProduct.getId())
                        .setPrice(BigDecimal.valueOf(9000).doubleValue())
        ));

        await().atMost(5, TimeUnit.SECONDS)
                        .untilAsserted(() -> verify(publisher, times(1)).publishEvent(eq(expectedEvent)));

    }

    @Test
    public void concurrentProductLikeSavesOnlyOneLikeAndSendsOnlyOneEvent() {
        ExecutorService concurrentLikeExecutor = Executors.newFixedThreadPool(3);
        List<CompletableFuture<Void>> taskPool = Arrays.asList(
                CompletableFuture.runAsync(() -> defaultLikeService.like(likeableProduct, currentUser), concurrentLikeExecutor),
                CompletableFuture.runAsync(() -> defaultLikeService.like(likeableProduct, currentUser), concurrentLikeExecutor),
                CompletableFuture.runAsync(() -> defaultLikeService.like(likeableProduct, currentUser), concurrentLikeExecutor)
        );

        taskPool.forEach(CompletableFuture::join);

        List<ProductLike> allByUser = productLikeRepository.findAllByUser(currentUser);
        assertThat(allByUser).hasSize(1);

        verify(publisher, times(1)).publishEvent(any(NewLikeEvent.class));
    }

    @Test
    public void concurrentBrandLikeSavesOnlyOneLike() {
        Brand likeableBrand = brandRepository.findFirstByName("GUCCI");
        ExecutorService concurrentLikeExecutor = Executors.newFixedThreadPool(3);
        List<CompletableFuture<Void>> taskPool = Arrays.asList(
                CompletableFuture.runAsync(() -> defaultLikeService.like(likeableBrand, currentUser), concurrentLikeExecutor),
                CompletableFuture.runAsync(() -> defaultLikeService.like(likeableBrand, currentUser), concurrentLikeExecutor),
                CompletableFuture.runAsync(() -> defaultLikeService.like(likeableBrand, currentUser), concurrentLikeExecutor)
        );

        taskPool.forEach(CompletableFuture::join);

        List<BrandLike> allByUser = brandLikeRepository.findAllByUser(currentUser);
        assertThat(allByUser).hasSize(1);
    }

}
