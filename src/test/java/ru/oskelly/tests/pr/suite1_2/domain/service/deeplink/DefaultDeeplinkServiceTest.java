package ru.oskelly.tests.pr.suite1_2.domain.service.deeplink;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.MessageSourceAccessor;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.model.Brand;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.account.AccountService;
import su.reddot.domain.service.adminpanel.primary.AdminPrimaryDataService;
import su.reddot.domain.service.adminpanel.v2.BannerSettingService;
import su.reddot.domain.service.adminpanel.v2.ProductCollectionDeeplinkService;
import su.reddot.domain.service.adminpanel.v2.ProductCollectionService;
import su.reddot.domain.service.attribute.AttributeService;
import su.reddot.domain.service.brand.BrandService;
import su.reddot.domain.service.catalog.CatalogCategory;
import su.reddot.domain.service.catalog.CategoryService;
import su.reddot.domain.service.deeplink.AppLinkConfiguration;
import su.reddot.domain.service.deeplink.DeeplinkResolveResult;
import su.reddot.domain.service.deeplink.DefaultDeeplinkService;
import su.reddot.domain.service.deeplink.UserProducts;
import su.reddot.domain.service.dto.BrandDTO;
import su.reddot.domain.service.dto.CategoryDTO;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.domain.service.dto.WebViewDTO;
import su.reddot.domain.service.dto.bargain.BargainDeeplinkDTO;
import su.reddot.domain.service.like.LikeService;
import su.reddot.domain.service.order.impl.DefaultOrderService;
import su.reddot.domain.service.product.ProductModelService;
import su.reddot.domain.service.product.ProductService;
import su.reddot.domain.service.productpublication.ProductPublicationService;
import su.reddot.domain.service.productrequest.ProductRequestService;
import su.reddot.domain.service.setting.FeatureFlagsSettingService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.configparam.ConfigParamService;
import su.reddot.infrastructure.security.SecurityService;
import su.reddot.presentation.adminpanel.v2.dto.PrimaryPageCondition;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class DefaultDeeplinkServiceTest {

    @Mock
    private CategoryService categoryService;

    @Mock
    private UserService userService;

    @Mock
    private ProductService productService;

    @Mock
    private BrandService brandService;

    @Mock
    private AccountService accountService;

    @Mock
    private SecurityService securityService;

    @Mock
    private LikeService likeService;

    @Mock
    private AdminPrimaryDataService primaryDataService;

    @Mock
    private AppLinkConfiguration appLinkConfiguration;

    @Mock
    private HttpServletRequest request;
    @Mock
    private MessageSourceAccessor messageSourceAccessor;

    private DefaultDeeplinkService deeplinkService;

    @Mock
    private ProductPublicationService productPublicationService;

    @Mock
    private BannerSettingService bannerSettingService;

    @Mock
    private ProductCollectionService productCollectionService;
    @Mock
    private ProductCollectionDeeplinkService productCollectionDeeplinkService;
    @Mock
    private ProductRequestService productRequestService;

    @Mock
    private FeatureFlagsSettingService featureFlagsSettingService;

    @Mock
    private DefaultOrderService defaultOrderService;

    @Mock
    private AttributeService attributeService;

    @Mock
    private ProductModelService productModelService;

    @Mock
    private ConfigParamService configParamService;

    @BeforeEach
    public void init() {
        deeplinkService = new DefaultDeeplinkService(
                categoryService,
                userService,
                productService,
                brandService,
                accountService,
                securityService,
                likeService,
                primaryDataService,
                appLinkConfiguration,
                request,
                messageSourceAccessor,
                bannerSettingService,
                productCollectionService,
                productCollectionDeeplinkService,
                defaultOrderService,
                productRequestService,
                featureFlagsSettingService,
                attributeService,
                Collections.emptyList(),
                configParamService,
                productModelService
        );
        when(configParamService.getValueAsListCached(anyString())).thenReturn(Collections.emptyList());
    }

    @Test
    public void resolveLinkResolvesOldProductLink() {
        when(productService.getProductDTO(anyLong(), eq(ProductService.UserType.HUMAN))).thenReturn(getProductDto());
        DeeplinkResolveResult<?> deeplinkResolveResult =
                deeplinkService.resolveLink("http://localhost:8080/products/18730", PrimaryPageCondition.ALL);
        assertThat(deeplinkResolveResult.getType()).isEqualTo("Product");
        assertThat(deeplinkResolveResult.getObject()).isInstanceOf(ProductDTO.class);
        ProductDTO productDTO = (ProductDTO) deeplinkResolveResult.getObject();
        assertThat(productDTO.getProductId()).isEqualTo(18730);
    }

    @Test
    public void resolveLinkResolvesHumanReadLink() {
        when(productService.getProductDTO(anyLong(), eq(ProductService.UserType.HUMAN))).thenReturn(getProductDto());
        DeeplinkResolveResult<?> deeplinkResolveResult =
                deeplinkService.resolveLink("http://localhost:8080/products/pryamye-dzhinsy-gilancioni-new-18730", PrimaryPageCondition.ALL);
        assertThat(deeplinkResolveResult.getType()).isEqualTo("Product");
        assertThat(deeplinkResolveResult.getObject()).isInstanceOf(ProductDTO.class);
        ProductDTO productDTO = (ProductDTO) deeplinkResolveResult.getObject();
        assertThat(productDTO.getProductId()).isEqualTo(18730);
    }

    @Test
    public void resolveLinkResolvesBlogLink() {
        DeeplinkResolveResult<?> deeplinkResolveResult =
                deeplinkService.resolveLink("https://oskelly.ru/blog/category/trendy/", PrimaryPageCondition.ALL);
        assertThat(deeplinkResolveResult.getType()).isEqualTo("WebView");
        assertThat(deeplinkResolveResult.getObject()).isInstanceOf(WebViewDTO.class);
        WebViewDTO webViewDTO = (WebViewDTO) deeplinkResolveResult.getObject();
        assertThat(webViewDTO.getUrl()).isEqualTo("https://oskelly.ru/blog/category/trendy/");
    }

    @Test
    public void resolveLinkResolvesAnyLink() {
        DeeplinkResolveResult<?> deeplinkResolveResult =
                deeplinkService.resolveLink("https://www.youtube.com/watch?v=dQw4w9WgXcQ", PrimaryPageCondition.ALL);
        assertThat(deeplinkResolveResult.getType()).isEqualTo("WebView");
        assertThat(deeplinkResolveResult.getObject()).isInstanceOf(WebViewDTO.class);
        WebViewDTO webViewDTO = (WebViewDTO) deeplinkResolveResult.getObject();
        assertThat(webViewDTO.getUrl()).isEqualTo("https://www.youtube.com/watch?v=dQw4w9WgXcQ");
    }

    @Test
    public void resolveLinkResolvesLinkForAllowedHost() {
        when(configParamService.getValueAsListCached(ConfigParamService.CONFIG_PARAM_OSOCIAL_POSTS_LINK_ALLOWED_HOSTS))
                .thenReturn(Collections.singletonList("youtube.com"));

        DeeplinkResolveResult<?> deeplinkResolveResult =
                deeplinkService.resolveLink("https://youtube.com", PrimaryPageCondition.ALL);
        assertThat(deeplinkResolveResult.getType()).isEqualTo("WebView");
        assertThat(deeplinkResolveResult.getObject()).isInstanceOf(WebViewDTO.class);
        WebViewDTO webViewDTO = (WebViewDTO) deeplinkResolveResult.getObject();
        assertThat(webViewDTO.getUrl()).isEqualTo("https://youtube.com");
    }

    @Test
    public void resolveLinkResolvesAccountProductsLink() {
        User user = mock(User.class);
        given(user.getId()).willReturn(1L);
        given(securityService.getCurrentAuthorizedUser()).willReturn(user);
        DeeplinkResolveResult<?> deeplinkResolveResult =
                deeplinkService.resolveLink("http://localhost:8080/account/products?state=PUBLISHED", PrimaryPageCondition.ALL);
        assertThat(deeplinkResolveResult.getType()).isEqualTo("UserProducts");
        assertThat(deeplinkResolveResult.getObject()).isInstanceOf(UserProducts.class);
        UserProducts userProducts = (UserProducts) deeplinkResolveResult.getObject();
        assertThat(userProducts.getProductState()).isEqualByComparingTo(ProductState.PUBLISHED);
        assertThat(userProducts.getAccountId()).isEqualTo(1L);
    }

    @Test
    public void resolveLinkResolvesAccountBargainLink() {
        DeeplinkResolveResult<?> deeplinkResolveResult =
                deeplinkService.resolveLink("http://localhost:8080/account/bargains/6", PrimaryPageCondition.ALL);
        assertThat(deeplinkResolveResult.getType()).isEqualTo("UserBargains");
        assertThat(deeplinkResolveResult.getObject()).isInstanceOf(BargainDeeplinkDTO.class);
        BargainDeeplinkDTO userProducts = (BargainDeeplinkDTO) deeplinkResolveResult.getObject();
        assertThat(userProducts.getBargainID()).isEqualByComparingTo(6L);
    }

    @Test
    public void resolveCatalogCategoryLink() {
        when(categoryService.findByUrlAndAlternativeUrl("zhenskoe/odezhda/korsety"))
                .thenReturn(Optional.of(getCatalogCategory()));
        when(categoryService.getCategoryDTO(any(CatalogCategory.class))).thenReturn(getCategoryDTO());

        DeeplinkResolveResult<?> deeplinkResolveResult =
                deeplinkService.resolveLink("/catalog/zhenskoe/odezhda/korsety", PrimaryPageCondition.ALL);

        assertThat(deeplinkResolveResult.getType()).isEqualTo("Catalog");

        List<CategoryDTO> categoryDTOs = (List<CategoryDTO>) deeplinkResolveResult.getObject();
        assertThat(categoryDTOs.get(0).getDisplayName()).isEqualTo("Корсеты");
        assertThat(deeplinkResolveResult.getTitle()).isEqualTo("Корсеты");
    }

    @Test
    public void resolveBrandWithoutCategoryLink() {
        Brand brand = new Brand();
        brand.setId(1L);
        brand.setTitle("Matthew Williamson");
        when(brandService.getByUrl("matthew-williamson")).thenReturn(Optional.of(brand));
        BrandDTO dto = new BrandDTO(1L).setTitle(brand.getTitle());
        when(brandService.getBrandDTO(any(Brand.class))).thenReturn(dto);
        DeeplinkResolveResult<?> deeplinkResolveResult =
                deeplinkService.resolveLink("/brands/matthew-williamson", PrimaryPageCondition.ALL);
        assertThat(deeplinkResolveResult.getType()).isEqualTo("Brand");
        BrandDTO brandDTO = (BrandDTO) deeplinkResolveResult.getObject();
        assertThat(brandDTO.getId()).isEqualTo(brand.getId());
        assertThat(deeplinkResolveResult.getTitle()).isEqualTo("Matthew Williamson");
    }

    public ProductDTO getProductDto() {
        ProductDTO productDTO = new ProductDTO();
        productDTO.setProductId(18730L);
        return productDTO;
    }

    public CatalogCategory getCatalogCategory() {
        return new CatalogCategory(1L, "Корсеты", false);
    }

    public CategoryDTO getCategoryDTO() {
        return new CategoryDTO().setId(1L).setDisplayName("Корсеты");
    }
}
