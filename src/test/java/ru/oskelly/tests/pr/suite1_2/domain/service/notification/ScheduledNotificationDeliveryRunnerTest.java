package ru.oskelly.tests.pr.suite1_2.domain.service.notification;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.ActiveProfiles;
import ru.oskelly.tests.AbstractSpringTest;
import su.reddot.domain.model.notification.UserSubscriptionType;
import su.reddot.domain.service.task.ScheduledNotificationDeliveryRunner;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.notificationDelivery.LogNotificationTransport;

import static org.mockito.Mockito.when;

@SpringBootTest(classes = {OskellyApplication.class})
@ActiveProfiles(AbstractSpringTest.testProfiles)
@Disabled("Тест отправки уведомлений, для тестирования вручную")
public class ScheduledNotificationDeliveryRunnerTest {

    @Autowired
    private ScheduledNotificationDeliveryRunner scheduledNotificationDeliveryRunner;

    @SpyBean
    private LogNotificationTransport logNotificationTransport;

    @Test
    public void test() {

        when(logNotificationTransport.getSupportedSubscriptionType())
                .thenReturn(UserSubscriptionType.MOBILE_PUSH);

        scheduledNotificationDeliveryRunner.deliverNotifications("log", UserSubscriptionType.MOBILE_PUSH, 10, 24L);
    }
}