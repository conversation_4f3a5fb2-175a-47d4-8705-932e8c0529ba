package ru.oskelly.tests.pr.suite1_2.domain.service.notification;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.BrandRepository;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.comment.CommentImageRepository;
import su.reddot.domain.dao.comment.CommentRepository;
import su.reddot.domain.dao.notification.NotificationGroupRepository;
import su.reddot.domain.dao.notification.NotificationGroupUserBindingRepository;
import su.reddot.domain.dao.notification.NotificationRepository;
import su.reddot.domain.dao.notificationDelivery.NotificationDeliveryRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.Comment;
import su.reddot.domain.model.CommentImage;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.notification.NotificationGroup;
import su.reddot.domain.model.notification.NotificationGroupUserBinding;
import su.reddot.domain.model.notification.UserSubscriptionType;
import su.reddot.domain.model.notification.catalog.YouWillLikeItNotification;
import su.reddot.domain.model.notification.comment.NewCommentNotification;
import su.reddot.domain.model.notification.product.SetLowerPriceNotification;
import su.reddot.domain.model.notificationDelivery.NotificationDelivery;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.domain.service.dto.NotificationDeliveryResult;
import su.reddot.domain.service.notification.DefaultNotificationService;
import su.reddot.domain.service.notification.NotificationService;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.notificationDelivery.NotificationTransport;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.*;

@ExtendWith(SpringExtension.class)
@SpringBootTest(
        properties = {
                "app.notificationDelivery.threadPool=10",
                "app.notificationDelivery.maxRetry=5"
        },
        classes = {OskellyApplication.class, DefaultNotificationServiceTest.TestConfig.class})
@ActiveProfiles(profiles = AbstractSpringTest.testProfiles)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class DefaultNotificationServiceTest {

    @TestConfiguration
    public static class TestConfig {
        @Bean
        @Primary
        public NotificationTransport testTransport() {
            NotificationTransport notificationTransport = mock(NotificationTransport.class);
            NotificationDeliveryResult notificationDeliveryResult = new NotificationDeliveryResult();
            notificationDeliveryResult.setType(NotificationDeliveryResult.ResultType.SUCCESSFUL);
            given(notificationTransport.deliverNotification(anyLong())).willReturn(notificationDeliveryResult);
            given(notificationTransport.getChannel()).willReturn("testChannel");
            return notificationTransport;
        }
    }

    @Autowired
    UserRepository userRepository;
    @Autowired
    NotificationGroupRepository notificationGroupRepository;
    @Autowired
    NotificationGroupUserBindingRepository notificationGroupUserBindingRepository;
    @Autowired
    NotificationRepository<Notification> notificationRepository;
    @MockBean
    List<NotificationService.NotificationProcessor> notificationProcessors;
    @Autowired
    DefaultNotificationService defaultNotificationService;
    @Autowired
    NotificationTransport testTransport;
    @Autowired
    NotificationDeliveryRepository notificationDeliveryRepository;
    @Autowired
    BrandRepository brandRepository;
    @Autowired
    ProductRepository productRepository;
    @Autowired
    CommissionGridService commissionGridService;
    @Autowired
    CommentRepository commentRepository;
    @Autowired
    CommentImageRepository commentImageRepository;

    @BeforeEach
    public void reset() {
        Mockito.reset(testTransport);
    }

    @Test
    public void deliverNotificationsDeliverNotificationUsingTransport() {
        List<Notification> notifications = new ArrayList<>();
        for (int i = 0; i < 500; i++) {
            Notification notification = new YouWillLikeItNotification()
                    .setCreateTime(ZonedDateTime.now());
            notifications.add(notification);
        }
        notifications = notificationRepository.saveAll(notifications);
        notificationRepository.flush();

        List<Long> notificationIds = notifications.stream().map(Notification::getId).collect(Collectors.toList());
        List<NotificationDeliveryResult> deliveryResults = defaultNotificationService.deliverNotifications("testChannel", notificationIds);
        assertThat(deliveryResults).hasSize(500);

        then(testTransport)
                .should(times(500))
                .deliverNotification(anyLong());

        notificationRepository.deleteAll(notifications);
    }

    @Test
    public void markNotificationAsDeliveredSavesSkippedNotifications() {
        Notification notification = new YouWillLikeItNotification()
                .setCreateTime(ZonedDateTime.now());
        notification = notificationRepository.save(notification);

        String reason = "Error reason";
        NotificationDeliveryResult notificationDeliveryResult = new NotificationDeliveryResult(
                notification.getId(),
                "onesignal",
                reason,
                NotificationDeliveryResult.ResultType.SKIPPED
        );

        defaultNotificationService.markNotificationDelivery(notificationDeliveryResult);


        List<NotificationDelivery> deliveries = notificationDeliveryRepository.findDeliveries("onesignal", notification.getId());
        assertThat(deliveries).hasSize(1);

        NotificationDelivery delivery = deliveries.get(0);
        assertThat(delivery.getSkipReason()).isEqualTo(reason);
        assertThat(delivery.getMetadata()).isNullOrEmpty();
        assertThat(delivery.getSuccessfulSentTime()).isNull();
        assertThat(delivery.getErrorSentTime()).isNull();

        notificationDeliveryRepository.deleteAll(deliveries);
        notificationRepository.delete(notification);
    }


    @Test
    public void markNotificationAsDeliveredSavesFailedNotifications() {

        Notification notification = new YouWillLikeItNotification()
                .setCreateTime(ZonedDateTime.now());
        notification = notificationRepository.save(notification);

        String reason = "Reason for skipping";
        NotificationDeliveryResult notificationDeliveryResult = new NotificationDeliveryResult(
                notification.getId(),
                "onesignal",
                reason,
                NotificationDeliveryResult.ResultType.FAILED
        );

        defaultNotificationService.markNotificationDelivery(notificationDeliveryResult);

        List<NotificationDelivery> deliveries = notificationDeliveryRepository.findDeliveries("onesignal", notification.getId());
        assertThat(deliveries).hasSize(1);

        NotificationDelivery delivery = deliveries.get(0);
        assertThat(delivery.getErrorReason()).isEqualTo(reason);
        assertThat(delivery.getMetadata()).isNullOrEmpty();
        assertThat(delivery.getSuccessfulSentTime()).isNull();
        assertThat(delivery.getErrorSentTime()).isNotNull();

        notificationDeliveryRepository.deleteAll(deliveries);
        notificationRepository.delete(notification);
    }

    @Test
    public void markNotificationAsDeliveredSavesTerminalFailedNotifications() {

        Notification notification = new YouWillLikeItNotification()
                .setCreateTime(ZonedDateTime.now());
        notification = notificationRepository.save(notification);

        String reason = "Reason for skipping";
        NotificationDeliveryResult notificationDeliveryResult = new NotificationDeliveryResult(
                notification.getId(),
                "onesignal",
                reason,
                NotificationDeliveryResult.ResultType.TERMINAL_FAILED
        );

        defaultNotificationService.markNotificationDelivery(notificationDeliveryResult);

        List<NotificationDelivery> deliveries = notificationDeliveryRepository.findDeliveries("onesignal", notification.getId());
        assertThat(deliveries).hasSize(1);

        NotificationDelivery delivery = deliveries.get(0);
        assertThat(delivery.getErrorReason()).isEqualTo(reason);
        assertThat(delivery.getMetadata()).isNullOrEmpty();
        assertThat(delivery.getSuccessfulSentTime()).isNull();
        assertThat(delivery.getErrorSentTime()).isNotNull();
        assertThat(delivery.getCountErrorSent()).isEqualTo(5);

        notificationDeliveryRepository.deleteAll(deliveries);
        notificationRepository.delete(notification);
    }


    @Test
    public void deliverNotificationsReturnSkippedResultTypeWhenShouldNotSendPush() {
        User user = new User()
                .setChangeTime(LocalDateTime.now())
                .setUserType(User.UserType.SIMPLE_USER)
                .setNickname(UUID.randomUUID().toString())
                .setEmail(UUID.randomUUID().toString())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        user = userRepository.saveAndFlush(user);

        Notification notification = new YouWillLikeItNotification()
                .setUser(user)
                .setCreateTime(ZonedDateTime.now());

        notification = notificationRepository.saveAndFlush(notification);

        List<Long> notificationIds = Collections.singletonList(notification.getId());
        List<NotificationDeliveryResult> deliveryResults = defaultNotificationService.deliverNotifications("testChannel", notificationIds);
        assertThat(deliveryResults).hasSize(1);

        then(testTransport)
                .should(never())
                .deliverNotification(anyLong());

        assertThat(deliveryResults.get(0).getType()).isEqualByComparingTo(NotificationDeliveryResult.ResultType.SKIPPED);
        assertThat(deliveryResults.get(0).getMessage()).isEqualTo("Уведомление выключено");

        userRepository.delete(user);
        notificationRepository.delete(notification);
    }


    @Test
    public void deliverNotificationsReturnSkippedResultTypeWhenDuplicate() {
        Notification notificationOriginal = new YouWillLikeItNotification()
                .setGuestToken("guest_token")
                .setCreateTime(ZonedDateTime.now());
        notificationOriginal = notificationRepository.saveAndFlush(notificationOriginal);

        Notification notificationDuplicate = new YouWillLikeItNotification()
                .setGuestToken("guest_token")
                .setCreateTime(ZonedDateTime.now());

        notificationDuplicate = notificationRepository.saveAndFlush(notificationDuplicate);

        List<Long> notificationIds = Collections.singletonList(notificationDuplicate.getId());
        List<NotificationDeliveryResult> deliveryResults = defaultNotificationService.deliverNotifications("testChannel", notificationIds);
        assertThat(deliveryResults).hasSize(1);

        then(testTransport)
                .should(never())
                .deliverNotification(anyLong());

        assertThat(deliveryResults.get(0).getType()).isEqualByComparingTo(NotificationDeliveryResult.ResultType.SKIPPED);
        assertThat(deliveryResults.get(0).getMessage()).isEqualTo("Дубликат");

        notificationRepository.delete(notificationDuplicate);
        notificationRepository.delete(notificationOriginal);
    }

    @Test
    public void getNotHiddenNotificationGroupsForSubscriptionTypeReturnsGroups() {
        List<NotificationGroup> notHiddenPushNotificationGroups = defaultNotificationService.getNotHiddenNotificationGroupsForSubscriptionType(UserSubscriptionType.MOBILE_PUSH);
        assertThat(notHiddenPushNotificationGroups).hasSize(4);
        assertThat(notHiddenPushNotificationGroups.stream().map(NotificationGroup::getId).collect(Collectors.toList())).containsExactlyInAnyOrder(1L, 2L, 3L, 5L);

        List<NotificationGroup> notHiddenEmailNotificationGroups = defaultNotificationService.getNotHiddenNotificationGroupsForSubscriptionType(UserSubscriptionType.EMAIL);
        assertThat(notHiddenEmailNotificationGroups).hasSize(4);
        assertThat(notHiddenEmailNotificationGroups.stream().map(NotificationGroup::getId).collect(Collectors.toList())).containsExactlyInAnyOrder(6L, 7L, 8L, 9L);
    }

    @Test
    public void shouldSendNotificationReturnFalseWhenGeneralGroupIsDisabled() {
        User user = new User()
                .setChangeTime(LocalDateTime.now())
                .setUserType(User.UserType.SIMPLE_USER)
                .setNickname(UUID.randomUUID().toString())
                .setNotificationGroupsChangeTime(ZonedDateTime.now()) // помечаем что пользователь меня группы, но так как для него групп нет, считается что он все выключил
                .setEmail(UUID.randomUUID().toString())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        user = userRepository.saveAndFlush(user);

        boolean shouldSend = defaultNotificationService.shouldSendNotification(user, "SaleCompletedNotification", UserSubscriptionType.EMAIL);
        assertThat(shouldSend).isFalse();

        shouldSend = defaultNotificationService.shouldSendNotification(user, "SaleCompletedNotification", UserSubscriptionType.MOBILE_PUSH);
        assertThat(shouldSend).isFalse();

        shouldSend = defaultNotificationService.shouldSendNotification(user, "SaleCompletedNotification", UserSubscriptionType.WHATSAPP);
        assertThat(shouldSend).isFalse();
    }


    @Test
    public void shouldSendNotificationReturnTrueForEnabledGroup() {
        User user = new User()
                .setChangeTime(LocalDateTime.now())
                .setUserType(User.UserType.SIMPLE_USER)
                .setNickname(UUID.randomUUID().toString())
                .setNotificationGroupsChangeTime(ZonedDateTime.now()) // помечаем что пользователь меня группы, но так как для него групп нет, считается что он все выключил
                .setEmail(UUID.randomUUID().toString())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        user = userRepository.saveAndFlush(user);

        NotificationGroup notificationGroup = notificationGroupRepository.getOne(8L);

        NotificationGroupUserBinding notificationGroupUserBinding = new NotificationGroupUserBinding();
        notificationGroupUserBinding.setNotificationGroup(notificationGroup);
        notificationGroupUserBinding.setUser(user);
        notificationGroupUserBinding = notificationGroupUserBindingRepository.saveAndFlush(notificationGroupUserBinding);

        NotificationGroupUserBinding globalEmailGroupUserBinding = new NotificationGroupUserBinding();
        globalEmailGroupUserBinding.setNotificationGroup(notificationGroupRepository.getOne(6L));
        globalEmailGroupUserBinding.setUser(user);
        globalEmailGroupUserBinding = notificationGroupUserBindingRepository.saveAndFlush(globalEmailGroupUserBinding);

        NotificationGroupUserBinding globalWhatsappGroupUserBinding = new NotificationGroupUserBinding();
        globalWhatsappGroupUserBinding.setNotificationGroup(notificationGroupRepository.getOne(10L));
        globalWhatsappGroupUserBinding.setUser(user);
        globalWhatsappGroupUserBinding = notificationGroupUserBindingRepository.saveAndFlush(globalWhatsappGroupUserBinding);

        boolean shouldSend = defaultNotificationService.shouldSendNotification(user, "SaleCompletedNotification", UserSubscriptionType.EMAIL);
        assertThat(shouldSend).isTrue();

        shouldSend = defaultNotificationService.shouldSendNotification(user, "SaleCompletedNotification", UserSubscriptionType.MOBILE_PUSH);
        assertThat(shouldSend).isFalse();

        shouldSend = defaultNotificationService.shouldSendNotification(user, NewCommentNotification.class.getSimpleName(), UserSubscriptionType.WHATSAPP);
        assertThat(shouldSend).isFalse();

        notificationGroupUserBindingRepository.delete(notificationGroupUserBinding);
        notificationGroupUserBindingRepository.delete(globalEmailGroupUserBinding);
        notificationGroupUserBindingRepository.delete(globalWhatsappGroupUserBinding);
    }

    @Test
    public void createSetLowerPriceNotificationSavesSetLowerPriceNotifications() {
        List<User> users = new ArrayList<>();
        List<Product> products = new ArrayList<>();
        MultiValueMap<Long, Long> usersToProducts = new LinkedMultiValueMap<>();
        for (int i = 0; i < 5; i++) {
            User seller = new User()
                    .setNickname(RandomStringUtils.randomAlphabetic(5))
                    .setUserType(User.UserType.SIMPLE_USER)
                    .setChangeTime(LocalDateTime.now())
                    .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
            seller = userRepository.saveAndFlush(seller);
            users.add(seller);

            Product product = new Product();
            product.setBrand(brandRepository.getOne(1L));
            product.setCategoryId(2L);
            product.setSeller(seller);
            product.setCurrentPrice(BigDecimal.valueOf(6000));
            product.setProductState(ProductState.PUBLISHED);

            product = productRepository.save(product);
            products.add(product);

            usersToProducts.add(seller.getId(), product.getId());
        }

        defaultNotificationService.createSetLowerPriceNotifications(usersToProducts);


        for (User user : users) {
            List<Notification> setLowerPriceNotifications = notificationRepository.findAllByUserAndDtype(user.getId(), "SetLowerPriceNotification");
            assertThat(setLowerPriceNotifications).isNotNull().hasSize(1);

            notificationRepository.deleteAll(setLowerPriceNotifications);
        }

        productRepository.deleteAll(products);
        userRepository.deleteAll(users);
    }


    @Test
    public void createSetLowerPriceNotificationSavesSetLowerPriceForSeveralProductsNotifications() {
        List<User> users = new ArrayList<>();
        List<Product> products = new ArrayList<>();
        MultiValueMap<Long, Long> usersToProducts = new LinkedMultiValueMap<>();
        for (int i = 0; i < 5; i++) {
            User seller = new User()
                    .setNickname(RandomStringUtils.randomAlphabetic(5))
                    .setUserType(User.UserType.SIMPLE_USER)
                    .setChangeTime(LocalDateTime.now())
                    .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
            seller = userRepository.saveAndFlush(seller);
            users.add(seller);

            for (int j = 0; j < 3; j++) {
                Product product = new Product();
                product.setBrand(brandRepository.getOne(1L));
                product.setCategoryId(2L);
                product.setSeller(seller);
                product.setCurrentPrice(BigDecimal.valueOf(6000));
                product.setProductState(ProductState.PUBLISHED);

                product = productRepository.save(product);
                products.add(product);

                usersToProducts.add(seller.getId(), product.getId());
            }
        }

        defaultNotificationService.createSetLowerPriceNotifications(usersToProducts);


        for (User user : users) {
            List<Notification> setLowerPriceNotifications = notificationRepository.findAllByUserAndDtype(user.getId(), "SetLowerPriceForSeveralProductsNotification");
            assertThat(setLowerPriceNotifications).isNotNull().hasSize(1);

            notificationRepository.deleteAll(setLowerPriceNotifications);
        }

        productRepository.deleteAll(products);
        userRepository.deleteAll(users);
    }


    @Test
    @Transactional
    public void checkActionCompletedNotSetActionCompleteTimeForSetLowerPriceNotificationWhenPriceNotLoweredEnough() {
        User seller = new User()
                .setChangeTime(LocalDateTime.now())
                .setUserType(User.UserType.SIMPLE_USER)
                .setNickname(UUID.randomUUID().toString())
                .setEmail(UUID.randomUUID().toString())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        seller = userRepository.saveAndFlush(seller);

        MultiValueMap<Long, Long> usersToProducts = new LinkedMultiValueMap<>();
        Product product = new Product();
        product.setBrand(brandRepository.getOne(1L));
        product.setCategoryId(2L);
        product.setSeller(seller);
        product.setStartPrice(BigDecimal.valueOf(1000));
        product.setCurrentPrice(BigDecimal.valueOf(1000));
        product.setProductState(ProductState.PUBLISHED);

        product = productRepository.save(product);

        usersToProducts.add(seller.getId(), product.getId());

        defaultNotificationService.createSetLowerPriceNotifications(usersToProducts);

        product.setCurrentPrice(BigDecimal.valueOf(950));
        product = productRepository.save(product);

        boolean actionCompleted = defaultNotificationService.checkActionCompleted(seller);

        assertThat(actionCompleted).isFalse();

        List<Notification> setLowerPriceNotifications = notificationRepository.findAllByUserAndDtype(seller.getId(), "SetLowerNotification");
        assertThat(setLowerPriceNotifications).noneMatch(Notification::isActionCompleted);

        notificationRepository.deleteAll(setLowerPriceNotifications);
        productRepository.delete(product);
        userRepository.delete(seller);
    }

    @Test
    @Transactional
    public void checkActionCompletedSetActionCompleteTimeForSetLowerPriceForSeveralProductsNotification() {
        User seller = new User()
                .setChangeTime(LocalDateTime.now())
                .setUserType(User.UserType.SIMPLE_USER)
                .setNickname(UUID.randomUUID().toString())
                .setEmail(UUID.randomUUID().toString())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        seller = userRepository.saveAndFlush(seller);

        MultiValueMap<Long, Long> usersToProducts = new LinkedMultiValueMap<>();
        List<Product> products = new ArrayList<>();

        // Создаем продукты, где цена уже была снижена
        for (int i = 0; i < 2; i++) {
            Product product = new Product();
            product.setBrand(brandRepository.getOne(1L));
            product.setCategoryId(2L);
            product.setSeller(seller);
            product.setStartPrice(BigDecimal.valueOf(6000));
            product.setCurrentPrice(BigDecimal.valueOf(5000));
            product.setProductState(ProductState.PUBLISHED);
            product.setPublishTime(LocalDateTime.now().minusDays(1));

            product = productRepository.saveAndFlush(product);
            usersToProducts.add(seller.getId(), product.getId());

            products.add(product);
        }

        // Новый продукт на который будет снижена цена
        Product product = new Product();
        product.setBrand(brandRepository.getOne(1L));
        product.setCategoryId(2L);
        product.setSeller(seller);
        product.setStartPrice(BigDecimal.valueOf(6000));
        product.setCurrentPrice(BigDecimal.valueOf(6000));
        product.setProductState(ProductState.PUBLISHED);
        product.setPublishTime(LocalDateTime.now());

        product = productRepository.save(product);

        usersToProducts.add(seller.getId(), product.getId());

        products.add(product);

        defaultNotificationService.createSetLowerPriceNotifications(usersToProducts);

        product.setStartPrice(BigDecimal.valueOf(6000));
        product.setCurrentPrice(BigDecimal.valueOf(5000));
        product.setPublishTime(LocalDateTime.now());
        product = productRepository.save(product);


        boolean actionCompleted = defaultNotificationService.checkActionCompleted(seller);

        assertThat(actionCompleted).isFalse();

        List<Notification> setLowerPriceNotifications = notificationRepository.findAllByUserAndDtype(seller.getId(), "SetLowerPriceForSeveralProductsNotification");
        assertThat(setLowerPriceNotifications).hasSize(1);
//        assertThat(setLowerPriceNotifications.get(0).isActionCompleted()).isTrue();
//        assertThat(setLowerPriceNotifications.get(0).getActionCompletedTime()).isEqualTo(product.getPublishTime().atZone(ZoneId.systemDefault()));
        assertThat(setLowerPriceNotifications.get(0).isActionCompleted()).isFalse();
        assertThat(setLowerPriceNotifications.get(0).getActionCompletedTime()).isNull();

        notificationRepository.deleteAll(setLowerPriceNotifications);
        productRepository.deleteAll(products);
        userRepository.delete(seller);
    }


    @Test
    @Transactional
    public void checkActionCompletedSetActionCompleteTimeForSetLowerPriceNotification() {
        User seller = new User()
                .setChangeTime(LocalDateTime.now())
                .setUserType(User.UserType.SIMPLE_USER)
                .setNickname(UUID.randomUUID().toString())
                .setEmail(UUID.randomUUID().toString())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        seller = userRepository.saveAndFlush(seller);

        MultiValueMap<Long, Long> usersToProducts = new LinkedMultiValueMap<>();
        Product product = new Product();
        product.setBrand(brandRepository.getOne(1L));
        product.setCategoryId(2L);
        product.setSeller(seller);
        product.setStartPrice(BigDecimal.valueOf(6000));
        product.setCurrentPrice(BigDecimal.valueOf(6000));
        product.setProductState(ProductState.PUBLISHED);
        product.setPublishTime(LocalDateTime.now());

        product = productRepository.saveAndFlush(product);

        usersToProducts.add(seller.getId(), product.getId());

        defaultNotificationService.createSetLowerPriceNotifications(usersToProducts);


        product.setStartPrice(BigDecimal.valueOf(6000));
        product.setCurrentPrice(BigDecimal.valueOf(5000));

        product = productRepository.saveAndFlush(product);

        boolean actionCompleted = defaultNotificationService.checkActionCompleted(seller);

        assertThat(actionCompleted).isFalse();

        List<Notification> setLowerPriceNotifications = notificationRepository.findAllByUserAndDtype(seller.getId(), "SetLowerPriceNotification");
        assertThat(setLowerPriceNotifications).hasSize(1);
//        assertThat(setLowerPriceNotifications.get(0).isActionCompleted()).isTrue();
//        assertThat(setLowerPriceNotifications.get(0).getActionCompletedTime()).isEqualTo(product.getPublishTime().atZone(ZoneId.systemDefault()));
        assertThat(setLowerPriceNotifications.get(0).isActionCompleted()).isFalse();
        assertThat(setLowerPriceNotifications.get(0).getActionCompletedTime()).isNull();

        notificationRepository.deleteAll(setLowerPriceNotifications);
        productRepository.delete(product);
        userRepository.delete(seller);
    }

    @Test
    public void markNotificationsAsNotNeedActionSetFalseToNeedAction() {
        User user = new User()
                .setChangeTime(LocalDateTime.now())
                .setUserType(User.UserType.SIMPLE_USER)
                .setNickname(UUID.randomUUID().toString())
                .setEmail(UUID.randomUUID().toString())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        user = userRepository.saveAndFlush(user);
        Product product = new Product();
        product.setBrand(brandRepository.getOne(1L));
        product.setCategoryId(2L);
        product.setSeller(user);
        product.setStartPrice(BigDecimal.valueOf(1000));
        product.setCurrentPrice(BigDecimal.valueOf(1000));
        product.setProductState(ProductState.PUBLISHED);
        product.setPublishTime(LocalDateTime.now());
        product = productRepository.saveAndFlush(product);

        Notification notification = new SetLowerPriceNotification()
                .setProduct(product)
                .setUser(user)
                .setCreateTime(ZonedDateTime.now());
        notification = notificationRepository.saveAndFlush(notification);

        defaultNotificationService.markNotificationsAsNotNeedAction(Collections.singletonList(notification.getId()));

        Optional<Notification> notificationOptional = notificationRepository.findById(notification.getId());
        assertThat(notificationOptional).isPresent();
        notification = notificationOptional.get();
        assertThat(notification.isNeedAction()).isFalse();

        productRepository.delete(product);
        notificationRepository.delete(notification);
        userRepository.delete(user);
    }

    @Test
    public void completeActionCompletesAction() {
        User seller = new User()
                .setChangeTime(LocalDateTime.now())
                .setUserType(User.UserType.SIMPLE_USER)
                .setNickname(UUID.randomUUID().toString())
                .setEmail(UUID.randomUUID().toString())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        seller = userRepository.saveAndFlush(seller);

        MultiValueMap<Long, Long> usersToProducts = new LinkedMultiValueMap<>();
        Product product = new Product();
        product.setBrand(brandRepository.getOne(1L));
        product.setCategoryId(2L);
        product.setSeller(seller);
        product.setStartPrice(BigDecimal.valueOf(6000));
        product.setCurrentPrice(BigDecimal.valueOf(6000));
        product.setProductState(ProductState.PUBLISHED);
        product.setPublishTime(LocalDateTime.now());
        product.setProductRejectReasons(Collections.emptyList());

        product = productRepository.saveAndFlush(product);

        usersToProducts.add(seller.getId(), product.getId());
        defaultNotificationService.createSetLowerPriceNotifications(usersToProducts);

        product.setStartPrice(BigDecimal.valueOf(6000));
        product.setCurrentPrice(BigDecimal.valueOf(5000));

        product = productRepository.saveAndFlush(product);

        defaultNotificationService.completeActions(SetLowerPriceNotification.class.getSimpleName(), Period.ofDays(1), 100);

        List<Notification> setLowerPriceNotifications = notificationRepository.findAllByUserAndDtype(seller.getId(), "SetLowerPriceNotification");
        assertThat(setLowerPriceNotifications).hasSize(1);
        assertThat(setLowerPriceNotifications.get(0).isActionCompleted()).isTrue();
        assertThat(setLowerPriceNotifications.get(0).getActionCompletedTime()).isEqualToIgnoringSeconds(product.getPublishTime().atZone(ZoneId.systemDefault()));

        notificationRepository.deleteAll(setLowerPriceNotifications);
        productRepository.delete(product);
        userRepository.delete(seller);
    }

    @Test
    public void shouldSendPushReturnFalseForNewCommentNotificationWhenThereAreImages() {

        User seller = new User()
                .setChangeTime(LocalDateTime.now())
                .setUserType(User.UserType.SIMPLE_USER)
                .setNickname(UUID.randomUUID().toString())
                .setEmail(UUID.randomUUID().toString())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        seller = userRepository.saveAndFlush(seller);

        Product product = new Product();
        product.setBrand(brandRepository.getOne(1L));
        product.setCategoryId(2L);
        product.setSeller(seller);
        product.setStartPrice(BigDecimal.valueOf(1000));
        product.setCurrentPrice(BigDecimal.valueOf(1000));
        product.setProductState(ProductState.PUBLISHED);

        product = productRepository.save(product);



        Comment comment = new Comment();
        comment.setImages(Collections.emptyList());
        comment.setProduct(product);
        comment.setPublishTime(ZonedDateTime.now());
        comment.setText("Test");
        comment.setPublisher(seller);
        comment = commentRepository.save(comment);

        CommentImage commentImage = new CommentImage();
        commentImage.setComment(comment);
        commentImage.setImagePath("/test/path");
        commentImage = commentImageRepository.save(commentImage);

        NewCommentNotification newCommentNotification = new NewCommentNotification();
        newCommentNotification.setComment(comment);
        newCommentNotification.setUser(seller);
        newCommentNotification.setCreateTime(ZonedDateTime.now());
        newCommentNotification = notificationRepository.save(newCommentNotification);

        Optional<Notification> notification = notificationRepository.findById(newCommentNotification.getId());
        boolean shouldSendNotification = defaultNotificationService.shouldSendNotification(notification.get(), UserSubscriptionType.WHATSAPP);
        assertThat(shouldSendNotification).isFalse();

        commentImageRepository.delete(commentImage);
        commentRepository.delete(comment);
        productRepository.delete(product);
        notificationRepository.delete(newCommentNotification);
        userRepository.delete(seller);
    }

}
