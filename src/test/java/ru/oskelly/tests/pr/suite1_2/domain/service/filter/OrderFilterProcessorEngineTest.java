package ru.oskelly.tests.pr.suite1_2.domain.service.filter;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.BooleanNode;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.exception.FilterFormatException;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderSource;
import su.reddot.domain.model.order.OrderSourceInfo;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.filter.OrderFilterProcessorEngine;
import su.reddot.domain.service.filter.OrderFilterTransformationService;
import su.reddot.domain.service.filter.model.ItemsCount;
import su.reddot.domain.service.filter.model.ProductFiltrationContext;
import su.reddot.domain.service.filter.model.filter.OrderFilters;
import su.reddot.domain.service.filter.model.filter.ProductFilter;
import su.reddot.domain.service.filter.model.filter.impl.MultiListFilter;
import su.reddot.domain.service.filter.model.filter.impl.list.ListSection;
import su.reddot.domain.service.filter.processor.filter.FilterProcessor;
import su.reddot.domain.service.filter.processor.filter.OrderFilterProcessorProvider;
import su.reddot.domain.service.filter.processor.filter.impl.BrandFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.OfflineOnlyFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.OrderSourceFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.OrderSourceInfoFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.OrderStateFilterProcessor;
import su.reddot.domain.service.filter.value.FilterValue;
import su.reddot.domain.service.filter.value.impl.BooleanValue;
import su.reddot.domain.service.filter.value.impl.IdListValue;
import su.reddot.domain.service.order.AvailableOrderFilters;
import su.reddot.domain.service.order.OrderFilterSpecification;
import su.reddot.domain.service.ordersourceinfo.OrderSourceInfoService;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.presentation.api.v2.filter.ProductFilterInfoRequest;
import su.reddot.presentation.api.v2.filter.ProductFilterRequest;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Fail.failBecauseExceptionWasNotThrown;
import static su.reddot.domain.model.order.OrderSourceInfo.ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME;
import static su.reddot.domain.model.order.OrderSourceInfo.ORDER_SOURCE_INFO_OSKELLY_STOCK_NAME;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest(classes = {OskellyApplication.class})
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class OrderFilterProcessorEngineTest extends AbstractSpringTest {

    private static final long SELLER_USER_ID = 19554L;

    @Autowired
    private OrderFilterTransformationService filterTransformationService;

    @Autowired
    private OrderFilterProcessorEngine orderFilterProcessorEngine;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private OrderSourceInfoService orderSourceInfoService;

    @Autowired
    private OrderFilterProcessorProvider orderFilterProcessorProvider;

    @Test
    public void testFindAllFilterProcessors() {

        List<FilterProcessor<? extends FilterValue>> processors = orderFilterProcessorProvider.findAllOrdered();

        assertEquals(4, processors.size());

        assertTrue(processors.stream()
                .anyMatch(it -> it instanceof OfflineOnlyFilterProcessor));
        assertTrue(processors.stream()
                .anyMatch(it -> it instanceof OrderSourceInfoFilterProcessor));
        assertTrue(processors.stream()
                .anyMatch(it -> it instanceof OrderSourceFilterProcessor));
        assertTrue(processors.stream()
                .anyMatch(it -> it instanceof OrderStateFilterProcessor));
    }

    @Test
    public void testFillFiltrationContextAndSpecification() {

        User seller = userRepository.findById(SELLER_USER_ID).get();

        OrderSourceInfo stolOrderSourceInfo = orderSourceInfoService.findOrderSourceInfoByNameCached(
                ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME).get();

        ProductFilterInfoRequest request = new ProductFilterInfoRequest();
        request.setCurrencyCode("RUB");

        // пройдемся по всем фильтрам и проверим, что они учитываются в FilterSpecification

        request.setFilters(ImmutableMap.of(
                OfflineOnlyFilterProcessor.FILTER_CODE, booleanNode(true),
                OrderSourceInfoFilterProcessor.FILTER_CODE, idListNode(stolOrderSourceInfo.getId())));

        ProductFiltrationContext filtrationContext = new ProductFiltrationContext();
        filterTransformationService.fillFiltrationContext(filtrationContext, request);

        Map<String, FilterValue> filterValues = filtrationContext.getFilterValues();

        assertThat(filterValues.get(OfflineOnlyFilterProcessor.FILTER_CODE)).isInstanceOf(BooleanValue.class);
        assertThat(filterValues.get(OrderSourceInfoFilterProcessor.FILTER_CODE)).isInstanceOf(IdListValue.class);

        OrderFilterSpecification spec = filterTransformationService.createFilterSpecification(filtrationContext, seller);

        assertThat(spec.offlineOnly()).isEqualTo(true);
        assertThat(spec.orderSourceInfoIds()).containsOnly(stolOrderSourceInfo.getId());
        assertThat(spec.sellerUserIds()).containsOnly(seller.getId());
    }

    @Test
    public void testFillFiltrationContextWithWrongFilterCodes() {
        ProductFilterInfoRequest request = new ProductFilterInfoRequest();
        request.setCurrencyCode("RUB");

        Map<String, JsonNode> filterJson = new HashMap<>();
        filterJson.put(BrandFilterProcessor.FILTER_CODE, idListNode(504L, 675L, 1860L));
        request.setFilters(filterJson);

        ProductFiltrationContext filtrationContext = new ProductFiltrationContext();
        try {
            filterTransformationService.fillFiltrationContext(filtrationContext, request);
            failBecauseExceptionWasNotThrown(FilterFormatException.class);
        } catch (FilterFormatException ex) {
            assertThat(ex.getMessage())
                    .isEqualTo("Не удалось найти подходящий обработчик для фильтра с кодом " + BrandFilterProcessor.FILTER_CODE);
        }

        filterJson.clear();
        filterJson.put("abracadabra", idListNode(504L, 675L, 1860L));
        try {
            filterTransformationService.fillFiltrationContext(filtrationContext, request);
            failBecauseExceptionWasNotThrown(FilterFormatException.class);
        } catch (FilterFormatException ex) {
            assertThat(ex.getMessage())
                    .isEqualTo("Не удалось найти подходящий обработчик для фильтра с кодом abracadabra");
        }
    }

    @Test
    @Transactional
    public void testTransformAvailableFilters() {
        AvailableOrderFilters availableFilters = new AvailableOrderFilters();

        availableFilters.setSourceInfoIds(ImmutableList.of(1L, 2L));

        ProductFilterInfoRequest request = new ProductFilterInfoRequest();
        request.setCurrencyCode("RUB");
        request.setFilters(ImmutableMap.of(
                OfflineOnlyFilterProcessor.FILTER_CODE, booleanNode(true),
                OrderSourceInfoFilterProcessor.FILTER_CODE, idListNode(1L)));

        ProductFiltrationContext filtrationContext = new ProductFiltrationContext();
        filterTransformationService.fillFiltrationContext(filtrationContext, request);

        filtrationContext.setWithValues(true);

        List<ProductFilter> filters =
                filterTransformationService.transformFilters(availableFilters, filtrationContext);

        assertThat(filters).hasSize(1);
        // заодно проверим и порядок фильтров
        assertSimpleList(filters.get(0), true, "sourceInfo", "Где находится товар",
                ImmutableList.of(1L),
                ImmutableList.of(1L, 2L),
                Collections.emptyList());

        // проверим, что не приходят значения, когда их не запрашивают
        filtrationContext.setWithValues(false);
        filters =
                filterTransformationService.transformFilters(availableFilters, filtrationContext);
        assertSimpleList(filters.get(0), false, "sourceInfo", "Где находится товар",
                ImmutableList.of(1L),
                ImmutableList.of(1L, 2L),
                Collections.emptyList());
    }

    @Test
    @Transactional
    public void testGetFiltersWithPresets() {

        OrderSourceInfo stolOrderSourceInfo = orderSourceInfoService.findOrderSourceInfoByNameCached(
                ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME).get();
        OrderSourceInfo stockOrderSourceInfo = orderSourceInfoService.findOrderSourceInfoByNameCached(
                ORDER_SOURCE_INFO_OSKELLY_STOCK_NAME).get();

        ProductFilterRequest request = new ProductFilterRequest();
        request.setCurrencyCode("RUB");

        Map<String, JsonNode> presetJson = new HashMap<>();
        request.setPresets(presetJson);

        Map<String, JsonNode> filterJson = new HashMap<>();
        request.setFilters(filterJson);

        // перемещаем часть заказов в оффлайн
        User seller = userRepository.findById(SELLER_USER_ID).get();

        List<Order> orders = orderRepository.findAllSalesBySeller(seller);
        moveOrderToOfflineSourceInfo(orders.get(0), stolOrderSourceInfo, false);
        moveOrderToOfflineSourceInfo(orders.get(1), stockOrderSourceInfo, false);

        commitAndStartNewTransaction();

        // сначала запросим фильтры без преднастроек
        OrderFilters availableFilters = orderFilterProcessorEngine.getAvailableFilters(request, seller);
        Long allItemsCount = availableFilters.getItemsCount();

        ProductFilter filterInfo = orderFilterProcessorEngine.getFilterInfo(request, OrderSourceInfoFilterProcessor.FILTER_CODE, seller);
        // проверяем, что без преднастроек вернулись все теги с категорией местоположения
        assertThat(((MultiListFilter) filterInfo).getValues().size()).isEqualTo(1);
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries().size()).isEqualTo(2);

        // проверяем преднастройки фильтрации заказов "только оффлайн"
        presetJson.clear();
        // оставляем только заказы товаров оффлайн
        presetJson.put(OfflineOnlyFilterProcessor.FILTER_CODE, booleanNode(true));

        availableFilters = orderFilterProcessorEngine.getAvailableFilters(request, seller);
        // проверим, что преднастройки ограничили количество заказов
        assertThat(availableFilters.getItemsCount()).isLessThan(allItemsCount);

        ItemsCount itemsCount = orderFilterProcessorEngine.getItemsCount(request, seller);
        assertThat(itemsCount.getItemsCount()).isLessThan(allItemsCount);

        filterInfo = orderFilterProcessorEngine.getFilterInfo(request, OrderSourceInfoFilterProcessor.FILTER_CODE, seller);
        // в доступных значениях должны остаться бутик на Столешникова, бутик Охотный
        assertThat(((MultiListFilter) filterInfo).getValues().size()).isEqualTo(1);
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries().size()).isEqualTo(2);


        // проверяем преднастройки фильтрации заказов по конкретному бутику
        presetJson.clear();
        // оставляем только заказы товаров в бутике на столешникова
        presetJson.put(OrderSourceInfoFilterProcessor.FILTER_CODE, idListNode(stolOrderSourceInfo.getId()));

        availableFilters = orderFilterProcessorEngine.getAvailableFilters(request, seller);
        // проверим, что преднастройки ограничили количество заказов
        assertThat(availableFilters.getItemsCount()).isLessThan(allItemsCount);

        itemsCount = orderFilterProcessorEngine.getItemsCount(request, seller);
        assertThat(itemsCount.getItemsCount()).isLessThan(allItemsCount);

        filterInfo = orderFilterProcessorEngine.getFilterInfo(request, OrderSourceInfoFilterProcessor.FILTER_CODE, seller);
        // в доступных значениях должен остаться только бутик на Столешникова
        assertThat(((MultiListFilter) filterInfo).getValues().size()).isEqualTo(1);
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries().size()).isEqualTo(1);
    }

    @Test
    @Transactional
    public void testGetItemsByFilters() {

        OrderSourceInfo stolOrderSourceInfo = orderSourceInfoService.findOrderSourceInfoByNameCached(
                ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME).get();
        OrderSourceInfo stockOrderSourceInfo = orderSourceInfoService.findOrderSourceInfoByNameCached(
                ORDER_SOURCE_INFO_OSKELLY_STOCK_NAME).get();

        ProductFilterRequest request = new ProductFilterRequest();
        request.setCurrencyCode("RUB");
        request.setPage(1);
        request.setPageLength(10000);

        Map<String, JsonNode> filterJson = new HashMap<>();
        request.setFilters(filterJson);

        User seller = userRepository.findById(SELLER_USER_ID).get();

        // гарантируем, что часть заказов онлайн, часть - оффлайн в нескольких бутиках

        List<Order> orders = orderRepository.findAllSalesBySeller(seller);

        Order onlineOrder = orders.get(0);
        Order stolOrder = orders.get(1);
        Order stockOrder = orders.get(2);
        Order stolSoldOrder = orders.get(3);

        moveOrderToOnline(onlineOrder);
        moveOrderToOfflineSourceInfo(stolOrder, stolOrderSourceInfo, false);
        moveOrderToOfflineSourceInfo(stockOrder, stockOrderSourceInfo, false);
        moveOrderToOfflineSourceInfo(stolSoldOrder, stolOrderSourceInfo, true);

        commitAndStartNewTransaction();

        // проверяем, что фильтром offlineOnly отбрасываются онлайн заказы и проданные оффлайн заказы

        filterJson.clear();
        filterJson.put(OfflineOnlyFilterProcessor.FILTER_CODE, booleanNode(true));

        List<OrderDTO> items = orderFilterProcessorEngine.getItems(request, seller).getItems();
        assertThat(items.stream()
                .map(OrderDTO::getId)
                .collect(Collectors.toList())).contains(stolOrder.getId(), stockOrder.getId());
        assertThat(items.stream()
                .map(OrderDTO::getId)
                .collect(Collectors.toList())).doesNotContain(onlineOrder.getId(), stolSoldOrder.getId());

        // проверяем, что фильтром sourceInfo отбрасываются неподходящие заказы по бутику

        filterJson.clear();
        filterJson.put(OrderSourceInfoFilterProcessor.FILTER_CODE, idListNode(stolOrderSourceInfo.getId()));

        items = orderFilterProcessorEngine.getItems(request, seller).getItems();
        assertThat(items.stream()
                .map(OrderDTO::getId)
                .collect(Collectors.toList())).contains(stolOrder.getId(), stolSoldOrder.getId());
        assertThat(items.stream()
                .map(OrderDTO::getId)
                .collect(Collectors.toList())).doesNotContain(onlineOrder.getId(), stockOrder.getId());
    }

    private Order moveOrderToOnline(Order order) {
        order.setOrderSource(OrderSource.IOS);
        order.setOrderSourceInfo(null);
        return orderRepository.save(order);
    }

    private Order moveOrderToOfflineSourceInfo(Order order, OrderSourceInfo orderSourceInfo, boolean sold) {
        order.setOrderSource(OrderSource.BOUTIQUE);
        order.setOrderSourceInfo(orderSourceInfo);
        order.setState(OrderState.MONEY_TRANSFERRED);
        order.setSoldTime(sold ? LocalDateTime.now() : null);
        order.setActionOrderSourceInfo(orderSourceInfo);
        return orderRepository.save(order);
    }

    private void assertSimpleList(ProductFilter productFilter, boolean withValues,
                                  String filterCode, String filterName,
                                  List<Long> selectedIds, List<Long> availableIds,
                                  List<String> searchableSections) {
        assertThat(productFilter.getType()).isEqualTo(MultiListFilter.TYPE);
        assertThat(productFilter.getCode()).isEqualTo(filterCode);
        assertThat(productFilter.getName()).isEqualTo(filterName);
        assertThat(productFilter).isInstanceOf(MultiListFilter.class);

        MultiListFilter filter = (MultiListFilter) productFilter;
        assertThat(filter.getSelectedValues()).hasSize(selectedIds.size());
        for (int i = 0; i < selectedIds.size(); i++) {
            assertThat(filter.getSelectedValues().get(i).getId()).isIn(selectedIds.toArray());
        }

        assertThat(filter.getSearchableSections()).containsOnly(searchableSections.toArray(new String[0]));
        assertThat(filter.getHasMoreValues()).isTrue();

        if (withValues) {
            assertThat(filter.getValues()).hasSize(1);
            ListSection section = filter.getValues().get(0);
            assertThat(section.getEntries()).hasSize(availableIds.size());
            for (int i = 0; i < availableIds.size(); i++) {
                assertThat(section.getEntries().get(i).getId()).isIn(availableIds.toArray());
                if (selectedIds.contains(section.getEntries().get(i).getId())) {
                    assertThat(section.getEntries().get(i).getIsSelected()).isTrue();
                } else {
                    assertThat(section.getEntries().get(i).getIsSelected()).isFalse();
                }
            }
        } else {
            assertThat(filter.getValues()).isNull();
        }
    }

    private JsonNode idListNode(Long... ids) {
        ObjectMapper objectMapper = new ObjectMapper();
        ArrayNode arrayNode = objectMapper.createArrayNode();
        Arrays.stream(ids).forEachOrdered(arrayNode::add);
        return arrayNode;
    }

    private JsonNode booleanNode(boolean value) {
        return BooleanNode.valueOf(value);
    }

}

