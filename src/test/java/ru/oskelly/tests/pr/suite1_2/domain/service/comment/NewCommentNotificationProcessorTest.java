package ru.oskelly.tests.pr.suite1_2.domain.service.comment;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.pr.suite1_2.domain.service.notification.DefaultNotificationServiceTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.BrandRepository;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.comment.CommentRepository;
import su.reddot.domain.dao.notification.NotificationRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.Comment;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.notification.comment.NewCommentNotification;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.comment.product.NewCommentNotificationProcessor;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.infrastructure.configuration.OskellyApplication;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(SpringExtension.class)
@ActiveProfiles(AbstractSpringTest.testProfiles)
@SpringBootTest(classes = {OskellyApplication.class, DefaultNotificationServiceTest.TestConfig.class})
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class NewCommentNotificationProcessorTest {

    public static final ZonedDateTime ORIGINAL_COMMENT_PUBLISH_TIME = ZonedDateTime.of(2022, 9, 12, 13, 0, 0, 0, ZoneId.systemDefault());
    public static final ZonedDateTime RESPONSE_TO_COMMENT_TIME = ZonedDateTime.of(2022, 9, 12, 14, 0, 0, 0, ZoneId.systemDefault());
    public static final ZonedDateTime SELLER_RESPONSE_TIME = ZonedDateTime.of(2022, 9, 12, 16, 25, 0, 0, ZoneId.systemDefault());
    @Autowired
    CommentRepository commentRepository;
    @Autowired
    UserRepository userRepository;
    @Autowired
    ProductRepository productRepository;
    @Autowired
    CommissionGridService commissionGridService;
    @Autowired
    BrandRepository brandRepository;
    @Autowired
    NotificationRepository<Notification> notificationRepository;

    @Autowired
    NewCommentNotificationProcessor newCommentNotificationProcessor;

    private User seller;
    private User buyer;
    private Product product;
    private Comment comment;
    private Comment buyerSelfResponse;
    private Comment sellerResponse;
    private Notification newCommentNotification;

    @BeforeEach
    public void init() {
        User seller = new User()
                .setNickname(RandomStringUtils.randomAlphabetic(5))
                .setUserType(User.UserType.SIMPLE_USER)
                .setChangeTime(LocalDateTime.now())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid())
                .setCounterparties(Collections.emptyList());
        this.seller = userRepository.saveAndFlush(seller);


        User buyer = new User()
                .setNickname(RandomStringUtils.randomAlphabetic(5))
                .setUserType(User.UserType.SIMPLE_USER)
                .setChangeTime(LocalDateTime.now())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid())
                .setCounterparties(Collections.emptyList());
        this.buyer = userRepository.saveAndFlush(buyer);

        Product product = new Product();
        product.setBrand(brandRepository.getOne(1L));
        product.setCategoryId(2L);
        product.setSeller(seller);
        product.setCurrentPrice(BigDecimal.valueOf(1000));
        product.setProductState(ProductState.PUBLISHED);
        this.product = productRepository.saveAndFlush(product);


        Comment comment = new Comment()
                .setText("TestComment")
                .setPublisher(buyer)
                .setProduct(product)
                .setPublishTime(ORIGINAL_COMMENT_PUBLISH_TIME);
        this.comment = commentRepository.saveAndFlush(comment);

        Comment buyerSelfResponse = new Comment()
                .setText("TestComment")
                .setPublisher(buyer)
                .setParentComment(comment)
                .setProduct(product)
                .setPublishTime(RESPONSE_TO_COMMENT_TIME);
        this.buyerSelfResponse = commentRepository.saveAndFlush(buyerSelfResponse);


        Comment sellerResponse = new Comment()
                .setText("TestComment")
                .setPublisher(seller)
                .setProduct(product)
                .setParentComment(comment)
                .setPublishTime(SELLER_RESPONSE_TIME);
        this.sellerResponse = commentRepository.saveAndFlush(sellerResponse);

        Notification newCommentNotification = new NewCommentNotification()
                .setComment(comment)
                .setUser(seller)
                .setCreateTime(ORIGINAL_COMMENT_PUBLISH_TIME);

        this.newCommentNotification = notificationRepository.saveAndFlush(newCommentNotification);
    }

    @AfterEach
    public void cleanup() {
        notificationRepository.delete(newCommentNotification);
        commentRepository.delete(comment);
        commentRepository.delete(buyerSelfResponse);
        commentRepository.delete(sellerResponse);
        productRepository.delete(product);
        userRepository.delete(seller);
        userRepository.delete(buyer);
    }

    @Test
    public void getActionCompleteTimeReturnsTimeOfSellerResponse() {
        ZonedDateTime actionCompletedTime = newCommentNotificationProcessor.getActionCompletedTime(newCommentNotification);
        assertThat(actionCompletedTime).isEqualTo(SELLER_RESPONSE_TIME);
    }
}