package ru.oskelly.tests.pr.suite1_2.domain.service.filter;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.service.filter.processor.filter.FilterProcessor;
import su.reddot.domain.service.filter.processor.filter.ProductRequestFilterProcessorProvider;
import su.reddot.domain.service.filter.processor.filter.impl.AttributeFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.AttributeValuesFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.BrandFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.CategoryFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.ConditionFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.LikedBrandsFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.ModelFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.PriceFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.ProductFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.ProductRequestFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.ProductRequestUserFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.ProductResponseUserFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.SizeFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.StateFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.WithTagFilterProcessor;
import su.reddot.domain.service.filter.value.FilterValue;
import su.reddot.infrastructure.configuration.OskellyApplication;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest(classes = {OskellyApplication.class})
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class ProductRequestFilterProcessorEngineTest extends AbstractSpringTest {

    @Autowired
    private ProductRequestFilterProcessorProvider productRequestFilterProcessorProvider;

    @Test
    public void testFindAllFilterProcessors() {

        List<FilterProcessor<? extends FilterValue>> processors = productRequestFilterProcessorProvider.findAllOrdered();

        assertEquals(15, processors.size());

        assertTrue(processors.stream()
                .anyMatch(it -> it instanceof AttributeFilterProcessor));
        assertTrue(processors.stream()
                .anyMatch(it -> it instanceof AttributeValuesFilterProcessor));
        assertTrue(processors.stream()
                .anyMatch(it -> it instanceof BrandFilterProcessor));
        assertTrue(processors.stream()
                .anyMatch(it -> it instanceof CategoryFilterProcessor));
        assertTrue(processors.stream()
                .anyMatch(it -> it instanceof ConditionFilterProcessor));
        assertTrue(processors.stream()
                .anyMatch(it -> it instanceof LikedBrandsFilterProcessor));
        assertTrue(processors.stream()
                .anyMatch(it -> it instanceof ModelFilterProcessor));
        assertTrue(processors.stream()
                .anyMatch(it -> it instanceof PriceFilterProcessor));
        assertTrue(processors.stream()
                .anyMatch(it -> it instanceof ProductFilterProcessor));
        assertTrue(processors.stream()
                .anyMatch(it -> it instanceof ProductRequestFilterProcessor));
        assertTrue(processors.stream()
                .anyMatch(it -> it instanceof ProductRequestUserFilterProcessor));
        assertTrue(processors.stream()
                .anyMatch(it -> it instanceof ProductResponseUserFilterProcessor));
        assertTrue(processors.stream()
                .anyMatch(it -> it instanceof SizeFilterProcessor));
        assertTrue(processors.stream()
                .anyMatch(it -> it instanceof StateFilterProcessor));
        assertTrue(processors.stream()
                .anyMatch(it -> it instanceof WithTagFilterProcessor));
    }
}

