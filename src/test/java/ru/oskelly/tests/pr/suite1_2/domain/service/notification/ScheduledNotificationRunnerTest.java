package ru.oskelly.tests.pr.suite1_2.domain.service.notification;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.util.MultiValueMap;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.MockPublisherConfiguration;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.BrandRepository;
import su.reddot.domain.dao.SizeRepository;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.bargain.BargainRepository;
import su.reddot.domain.dao.category.CategoryRepository;
import su.reddot.domain.dao.comment.CommentRepository;
import su.reddot.domain.dao.notification.NotificationRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.notification.product.SetLowerPriceNotification;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.domain.service.master.MasterServiceRequest;
import su.reddot.domain.service.task.ScheduledNotificationRunner;
import su.reddot.infrastructure.configuration.OskellyApplication;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.then;
import static org.mockito.BDDMockito.willDoNothing;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;

@SpringBootTest(classes = {OskellyApplication.class, MockPublisherConfiguration.class}, properties = {""})
@ActiveProfiles(AbstractSpringTest.testProfiles)
@ExtendWith(SpringExtension.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class ScheduledNotificationRunnerTest {

    @Autowired
    ScheduledNotificationRunner scheduledNotificationRunner;
    @Autowired
    UserRepository userRepository;
    @Autowired
    BrandRepository brandRepository;
    @Autowired
    ProductRepository productRepository;
    @Autowired
    CommissionGridService commissionGridService;
    @Autowired
    SizeRepository sizeRepository;
    @Autowired
    BargainRepository bargainRepository;
    @Autowired
    CategoryRepository categoryRepository;
    @Autowired
    CommentRepository commentRepository;

    @Autowired
    ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    NotificationRepository<Notification> notificationRepository;

    private User currentUser;
    private Product currentProduct;

    @BeforeEach
    public void init() {
        User user = new User()
                .setNickname(RandomStringUtils.randomAlphabetic(6))
                .setUserType(User.UserType.SIMPLE_USER)
                .setChangeTime(LocalDateTime.now())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid())
                .setPhone("88005553535")
                .setAddressEndpoints(Collections.emptyList())
                .setCounterparties(Collections.emptyList())
                .setChangeTime(LocalDateTime.now());
        currentUser = userRepository.save(user);

        currentProduct = new Product();
        currentProduct.setBrand(brandRepository.getOne(1L));
        currentProduct.setCategoryId(2L);
        currentProduct.setSeller(currentUser);
        currentProduct.setStartPrice(BigDecimal.valueOf(10000));
        currentProduct.setCurrentPrice(BigDecimal.valueOf(9000));
        currentProduct.setProductState(ProductState.PUBLISHED);
        currentProduct.setPublishTime(LocalDateTime.now().minusMonths(3).minusDays(1));
        currentProduct.setProductRejectReasons(Collections.emptyList());
        currentProduct = productRepository.saveAndFlush(currentProduct);
    }

    @AfterEach
    public void cleanup() {
        reset(applicationEventPublisher);
        notificationRepository.deleteAll(notificationRepository.findAllByUser(currentUser.getId()));
        productRepository.deleteAll(productRepository.getAllBySeller(currentUser));
        userRepository.delete(currentUser);
    }

    @Test
    public void createSetLowerPriceNotificationSendsListOfUsersToMaster() {
        ArgumentCaptor<Object> captor = ArgumentCaptor.forClass(Object.class);

        scheduledNotificationRunner.createSetLowerPriceNotification();

        then(applicationEventPublisher)
                .should(times(2))
                .publishEvent(captor.capture());

        Object event = captor.getValue();
        assertThat(event).isInstanceOf(MasterServiceRequest.class);
        MasterServiceRequest masterServiceRequest = (MasterServiceRequest) event;
        assertThat(masterServiceRequest.getRequestEntityObject()).isInstanceOf(MultiValueMap.class);

        MultiValueMap<Long, Long> usersToProductMap = (MultiValueMap<Long, Long>) masterServiceRequest.getRequestEntityObject();
        assertThat(usersToProductMap).containsKey(currentUser.getId());
        assertThat(usersToProductMap.get(currentUser.getId())).containsExactly(currentProduct.getId());
    }


    @Test
    public void createSetLowerPriceNotificationNotSendsRequestForNotificatedUserBeforeDate() {

        Product product = new Product();
        product.setBrand(brandRepository.getOne(1L));
        product.setCategoryId(2L);
        product.setSeller(currentUser);
        product.setStartPrice(BigDecimal.valueOf(10000));
        product.setCurrentPrice(BigDecimal.valueOf(9000));
        product.setProductState(ProductState.PUBLISHED);
        product.setPublishTime(LocalDateTime.now().minusMonths(1).minusDays(1));
        productRepository.saveAndFlush(product);


        Notification setLowerPriceNotification = new SetLowerPriceNotification()
                .setProduct(product)
                .setUser(currentUser)
                .setCreateTime(ZonedDateTime.now().minusDays(1));
        setLowerPriceNotification = notificationRepository.saveAndFlush(setLowerPriceNotification);

        ArgumentCaptor<Object> captor = ArgumentCaptor.forClass(Object.class);

        willDoNothing().given(applicationEventPublisher).publishEvent(captor.capture());

        scheduledNotificationRunner.createSetLowerPriceNotification();


        then(applicationEventPublisher)
                .should(times(1))
                .publishEvent(any(MasterServiceRequest.class));

        Object event = captor.getValue();
        assertThat(event).isInstanceOf(MasterServiceRequest.class);
        MasterServiceRequest masterServiceRequest = (MasterServiceRequest) event;
        assertThat(masterServiceRequest.getRequestEntityObject()).isInstanceOf(MultiValueMap.class);

        MultiValueMap<Long, Long> usersToProductMap = (MultiValueMap<Long, Long>) masterServiceRequest.getRequestEntityObject();
        assertThat(usersToProductMap).doesNotContainKeys(currentUser.getId());

        notificationRepository.delete(setLowerPriceNotification);
        productRepository.delete(product);
    }


    @Test
    public void createSetLowerPriceNotificationDoNothingWhenCannotLowerPriceBy10Percent() {

        currentProduct.setStartPrice(BigDecimal.valueOf(5000));
        currentProduct.setCurrentPrice(BigDecimal.valueOf(5000));

        ArgumentCaptor<Object> captor = ArgumentCaptor.forClass(Object.class);

        willDoNothing().given(applicationEventPublisher).publishEvent(captor.capture());

        currentProduct = productRepository.save(currentProduct);

        scheduledNotificationRunner.createSetLowerPriceNotification();

        then(applicationEventPublisher)
                .should(times(1))
                .publishEvent(any(MasterServiceRequest.class));

        Object event = captor.getValue();
        assertThat(event).isInstanceOf(MasterServiceRequest.class);
        MasterServiceRequest masterServiceRequest = (MasterServiceRequest) event;
        assertThat(masterServiceRequest.getRequestEntityObject()).isInstanceOf(MultiValueMap.class);

        MultiValueMap<Long, Long> usersToProductMap = (MultiValueMap<Long, Long>) masterServiceRequest.getRequestEntityObject();
        assertThat(usersToProductMap).doesNotContainKeys(currentUser.getId());
    }

    @Test
    public void markNotificationAsExpiredCallsMaster() {
        Product product = new Product();
        product.setBrand(brandRepository.getOne(1L));
        product.setCategoryId(2L);
        product.setSeller(currentUser);
        product.setStartPrice(BigDecimal.valueOf(10000));
        product.setCurrentPrice(BigDecimal.valueOf(9000));
        product.setProductState(ProductState.PUBLISHED);
        product.setPublishTime(LocalDateTime.now().minusMonths(3).minusDays(1));

        product = productRepository.saveAndFlush(product);

        Notification notification = new SetLowerPriceNotification()
                .setProduct(product)
                .setUser(currentUser)
                .setNeedAction(true)
                .setCreateTime(ZonedDateTime.now().minusDays(3));

        notification = notificationRepository.saveAndFlush(notification);

        scheduledNotificationRunner.markNotificationsAsExpired();
        ArgumentCaptor<Object> captor = ArgumentCaptor.forClass(Object.class);
        then(applicationEventPublisher).should(atLeastOnce())
                .publishEvent(captor.capture());

        Optional<MasterServiceRequest> request = captor.getAllValues().stream().filter(o -> o instanceof MasterServiceRequest).map(o -> (MasterServiceRequest)o).findFirst();
        assertThat(request).isPresent();
        assertThat(request.get().getRequestEntityObject()).isInstanceOf(List.class);
        List<Long> requestEntityObject = (List<Long>) request.get().getRequestEntityObject();
        assertThat(requestEntityObject).containsExactly(notification.getId());
        assertThat(request.get().getUrl()).isEqualTo("/api/v2/master/notification/markAsNotNeedAction");
        productRepository.delete(product);

    }
}
