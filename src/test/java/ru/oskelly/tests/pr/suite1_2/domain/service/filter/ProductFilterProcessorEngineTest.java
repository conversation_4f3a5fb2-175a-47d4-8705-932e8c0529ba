package ru.oskelly.tests.pr.suite1_2.domain.service.filter;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.BooleanNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.data.util.Pair;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.SizeRepository;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.product.ProductItemLocationRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.exception.FilterFormatException;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.ProductItemLocation;
import su.reddot.domain.model.size.SizeType;
import su.reddot.domain.model.user.SellerType;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.catalog.CatalogSlotFeaturesService;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.domain.service.dto.ProductModelDTO;
import su.reddot.domain.service.filter.FilterTransformationService;
import su.reddot.domain.service.filter.FiltrationServiceFeatures;
import su.reddot.domain.service.filter.ProductFilterProcessorEngine;
import su.reddot.domain.service.filter.ProductFilterTransformationService;
import su.reddot.domain.service.filter.model.ItemsCount;
import su.reddot.domain.service.filter.model.ProductFiltrationContext;
import su.reddot.domain.service.filter.model.filter.ProductFilter;
import su.reddot.domain.service.filter.model.filter.ProductFilters;
import su.reddot.domain.service.filter.model.filter.impl.BooleanFilter;
import su.reddot.domain.service.filter.model.filter.impl.CategoryTreeFilter;
import su.reddot.domain.service.filter.model.filter.impl.MultiListFilter;
import su.reddot.domain.service.filter.model.filter.impl.PriceFilter;
import su.reddot.domain.service.filter.model.filter.impl.SizeFilter;
import su.reddot.domain.service.filter.model.filter.impl.category.CategoryTreeValue;
import su.reddot.domain.service.filter.model.filter.impl.list.ListSection;
import su.reddot.domain.service.filter.model.filter.impl.size.CategorySize;
import su.reddot.domain.service.filter.processor.filter.FilterProcessor;
import su.reddot.domain.service.filter.processor.filter.ProductFilterProcessorProvider;
import su.reddot.domain.service.filter.processor.filter.impl.AttributeFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.AttributeValuesFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.BannerSettingIdFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.BoutiqueLocationTagFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.BrandFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.BrandNewFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.CategoryFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.ConditionFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.DeliveryDaysFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.ExclusiveLotFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.ExclusiveSelectionFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.InBoutiqueFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.InStockFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.LikedBrandsFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.LocationTagFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.ModelFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.NewCollectionFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.NewResaleFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.OfflineOnlyFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.OskellyChoiceFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.PriceFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.ProductFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.ResaleFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.SaleFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.SellerFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.SellerTypeFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.SizeFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.SizeTypeFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.StateFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.StreetwearFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.UserSexFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.VintageFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.WithTagFilterProcessor;
import su.reddot.domain.service.filter.processor.sorting.impl.NewSortingProcessor;
import su.reddot.domain.service.filter.processor.sorting.impl.PriceAscSortingProcessor;
import su.reddot.domain.service.filter.processor.sorting.impl.PriceDescSortingProcessor;
import su.reddot.domain.service.filter.value.FilterValue;
import su.reddot.domain.service.filter.value.impl.BooleanValue;
import su.reddot.domain.service.filter.value.impl.IdListValue;
import su.reddot.domain.service.filter.value.impl.PriceValue;
import su.reddot.domain.service.loyalty.LoyaltyService;
import su.reddot.domain.service.loyalty.LoyaltyServiceProperties;
import su.reddot.domain.service.product.ProductService;
import su.reddot.domain.service.product.item.ProductItemService;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.security.SecurityService;
import su.reddot.infrastructure.util.Utils;
import su.reddot.presentation.api.v2.filter.ProductFilterInfoRequest;
import su.reddot.presentation.api.v2.filter.ProductFilterItemsRequest;
import su.reddot.presentation.api.v2.filter.ProductFilterRequest;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;
import static su.reddot.domain.model.product.ProductItemLocation.LOCATION_CODE_BOUTIQUE_KUZNETSKY_BRIDGE;
import static su.reddot.domain.model.product.ProductItemLocation.LOCATION_CODE_BOUTIQUE_STOLESHNIKOV;
import static su.reddot.domain.model.product.ProductItemLocation.LOCATION_CODE_WAREHOUSE;
import static su.reddot.domain.model.user.User.UserType.SIMPLE_USER;
import static su.reddot.presentation.api.v2.filter.ClientProductFiltrationContext.ACCOUNT_PUBLISHED_OFFLINE_PRODUCTS;

@SpringBootTest(classes = {OskellyApplication.class})
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class ProductFilterProcessorEngineTest extends AbstractSpringTest {

    @Autowired
    private ProductFilterTransformationService productFilterTransformationService;

    @Autowired
    private ProductFilterProcessorEngine productFilterProcessorEngine;

    @Autowired
    private MessageSourceAccessor messageSourceAccessor;

    @Autowired
    private ProductRepository productRepository;

    @Autowired
    private ProductItemService productItemService;

    @Autowired
    private SizeRepository sizeRepository;

    @Autowired
    private ProductItemLocationRepository productItemLocationRepository;

    @Autowired
    private ProductFilterProcessorProvider productFilterProcessorProvider;

    @MockBean
    private LoyaltyService loyaltyServiceMock;

    @Autowired
    private CommissionGridService commissionGridService;

    @Autowired
    private UserRepository userRepository;

    @MockBean
    private SecurityService securityService;

    @MockBean
    private LoyaltyServiceProperties loyaltyServicePropertiesMock;

    @Test
    public void testFindAllFilterProcessors() {

        List<FilterProcessor<? extends FilterValue>> processors = productFilterProcessorProvider.findAllOrdered();

        assertEquals(33, processors.size());

        assertTrue(processors.stream()
                .anyMatch(AttributeFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(AttributeValuesFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(BrandFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(CategoryFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(ConditionFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(InStockFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(LikedBrandsFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(LocationTagFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(BoutiqueLocationTagFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(ModelFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(NewCollectionFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(ExclusiveSelectionFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(OfflineOnlyFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(OskellyChoiceFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(PriceFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(ProductFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(SaleFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(SellerFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(SellerTypeFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(SizeFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(SizeTypeFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(StateFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(StreetwearFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(UserSexFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(VintageFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(WithTagFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(BrandNewFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(ResaleFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(NewResaleFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(BannerSettingIdFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(ExclusiveLotFilterProcessor.class::isInstance));
        assertTrue(processors.stream()
                .anyMatch(DeliveryDaysFilterProcessor.class::isInstance));

    }

    @Test
    public void verifyFiltersAndSpecification() {

        ProductFilterInfoRequest request = new ProductFilterInfoRequest();
        request.setBaseCategory(2L);
        request.setCurrencyCode("RUR");

        // пройдемся по всем фильтрам и проверим, что они учитываются в FilterSpecification
        Map<String, JsonNode> filterJson = allFiltersJson();
        request.setFilters(filterJson);
        filterJson.put(LocationTagFilterProcessor.FILTER_CODE, null);

        ProductFiltrationContext filtrationContext = new ProductFiltrationContext();
        productFilterTransformationService.fillFiltrationContext(filtrationContext, request);

        ProductService.FilterSpecification spec =
                productFilterTransformationService.createFilterSpecification(filtrationContext);

        Map<String, FilterValue> filterValues = filtrationContext.getFilterValues();

        assertThat(filterValues.get(BrandFilterProcessor.FILTER_CODE)).isInstanceOf(IdListValue.class);
        assertThat(filterValues.get(CategoryFilterProcessor.FILTER_CODE)).isInstanceOf(IdListValue.class);
        assertThat(filterValues.get("attribute_10")).isInstanceOf(IdListValue.class);
        assertThat(filterValues.get("attribute_13")).isInstanceOf(IdListValue.class);
        assertThat(filterValues.get(ConditionFilterProcessor.FILTER_CODE)).isInstanceOf(IdListValue.class);
        assertThat(filterValues.get(ModelFilterProcessor.FILTER_CODE)).isInstanceOf(IdListValue.class);
        assertThat(filterValues.get(SizeFilterProcessor.FILTER_CODE)).isInstanceOf(IdListValue.class);
        assertThat(filterValues.get(SellerTypeFilterProcessor.FILTER_CODE)).isInstanceOf(IdListValue.class);
        assertThat(filterValues.get(WithTagFilterProcessor.FILTER_CODE)).isInstanceOf(BooleanValue.class);
        assertThat(filterValues.get(InStockFilterProcessor.FILTER_CODE)).isInstanceOf(BooleanValue.class);
        assertThat(filterValues.get(ExclusiveSelectionFilterProcessor.FILTER_CODE)).isInstanceOf(BooleanValue.class);
        assertThat(filterValues.get(NewCollectionFilterProcessor.FILTER_CODE)).isInstanceOf(BooleanValue.class);
        assertThat(filterValues.get(OskellyChoiceFilterProcessor.FILTER_CODE)).isInstanceOf(BooleanValue.class);
        assertThat(filterValues.get(SaleFilterProcessor.FILTER_CODE)).isInstanceOf(BooleanValue.class);
        assertThat(filterValues.get(StreetwearFilterProcessor.FILTER_CODE)).isInstanceOf(BooleanValue.class);
        assertThat(filterValues.get(VintageFilterProcessor.FILTER_CODE)).isInstanceOf(BooleanValue.class);
        assertThat(filterValues.get(PriceFilterProcessor.FILTER_CODE)).isInstanceOf(PriceValue.class);
        assertThat(filterValues.get(BoutiqueLocationTagFilterProcessor.FILTER_CODE)).isInstanceOf(IdListValue.class);
        assertThat(filterValues.get(LocationTagFilterProcessor.FILTER_CODE)).isNull();
        assertThat(filterValues.get(InBoutiqueFilterProcessor.FILTER_CODE)).isNull();
        assertThat(filterValues.get(ExclusiveLotFilterProcessor.FILTER_CODE)).isInstanceOf(BooleanValue.class);

        assertThat(spec.interestingBrands()).containsOnly(504L, 675L, 1860L);
        assertThat(spec.categoriesIds()).containsOnly(58L, 59L);
        assertThat(spec.interestingAttributeValues()).containsOnly(131L, 136L, 164L, 186L);
        assertThat(spec.interestingConditions()).containsOnly(1L);
        assertThat(spec.interestingConditions()).doesNotContain(2L, 3L);
        assertThat(spec.interestingProductModels()).containsOnly(1L, 644L, 3L);
        assertThat(spec.interestingSizes()).containsOnly(397L, 398L);
        assertThat(spec.isInStock()).isTrue();
        assertThat(spec.isNewCollection()).isTrue();
        assertThat(spec.hasOurChoice()).isTrue();
        assertThat(spec.isOnSale()).isTrue();
        assertThat(spec.interestingSellerTypes()).containsOnly(SellerType.INDIVIDUAL);
        assertThat(spec.isBeegz()).isTrue();
        assertThat(spec.isVintage()).isTrue();
        assertThat(spec.startPrice()).isEqualTo(BigDecimal.valueOf(5000));
        assertThat(spec.endPrice()).isEqualTo(BigDecimal.valueOf(20000));
        assertThat(spec.boutiqueLocationTags()).containsOnly(2L);
        assertThat(spec.locationTags()).isNullOrEmpty();
        assertThat(spec.isInBoutique()).isNull();
        assertThat(spec.exclusiveLotFilter()).isNotNull();
        assertThat(spec.exclusiveLotFilter().exclusiveLot()).isTrue();

        // проверим заполнение filtrationContext и spec с указанием контекста
        request.setContexts(ImmutableList.of(ACCOUNT_PUBLISHED_OFFLINE_PRODUCTS));
        filterJson = allFiltersJson();
        request.setFilters(filterJson);
        filterJson.put(BoutiqueLocationTagFilterProcessor.FILTER_CODE, null);

        filtrationContext = new ProductFiltrationContext();
        productFilterTransformationService.fillFiltrationContext(filtrationContext, request);

        spec = productFilterTransformationService.createFilterSpecification(filtrationContext);

        filterValues = filtrationContext.getFilterValues();

        assertThat(filterValues.get(BoutiqueLocationTagFilterProcessor.FILTER_CODE)).isNull();
        assertThat(filterValues.get(LocationTagFilterProcessor.FILTER_CODE)).isInstanceOf(IdListValue.class);
        assertThat(filterValues.get(InBoutiqueFilterProcessor.FILTER_CODE)).isNull();

        assertThat(spec.boutiqueLocationTags()).isNullOrEmpty();
        assertThat(spec.locationTags()).containsOnly(1L);
        assertThat(spec.isInBoutique()).isNull();

        // отдельно пройдем по корнер кейсам связанным с PRO пользователями и состоянием товара
        filterJson.clear();
        filterJson.put(ConditionFilterProcessor.FILTER_CODE, idListNode(1L, 2L, 3L));
        filterJson.put(WithTagFilterProcessor.FILTER_CODE, booleanNode(false));
        filterJson.put(SellerTypeFilterProcessor.FILTER_CODE, idListNode(2L));
        filterJson.put(PriceFilterProcessor.FILTER_CODE, priceNode(BigDecimal.valueOf(5000), null));

        filtrationContext = new ProductFiltrationContext();
        productFilterTransformationService.fillFiltrationContext(filtrationContext, request);

        spec = productFilterTransformationService.createFilterSpecification(filtrationContext);

        assertThat(spec.interestingConditions()).containsOnly(1L, 2L, 3L);
        assertThat(spec.interestingSellerTypes()).containsOnly(SellerType.CONSIGNMENT_SHOP);
        assertThat(spec.startPrice()).isEqualTo(BigDecimal.valueOf(5000));
        assertThat(spec.endPrice()).isNull();

        //

        filterJson.clear();
        filterJson.put(WithTagFilterProcessor.FILTER_CODE, booleanNode(true));
        filterJson.put(SellerTypeFilterProcessor.FILTER_CODE, idListNode(1L, 2L, 3L, 4L));
        filterJson.put(PriceFilterProcessor.FILTER_CODE, priceNode(null, BigDecimal.valueOf(20000)));

        filtrationContext = new ProductFiltrationContext();
        productFilterTransformationService.fillFiltrationContext(filtrationContext, request);

        spec = productFilterTransformationService.createFilterSpecification(filtrationContext);

        assertThat(spec.interestingConditions()).containsOnly(1L);
        assertThat(spec.interestingSellerTypes()).hasSize(4);
        assertThat(spec.startPrice()).isNull();
        assertThat(spec.endPrice()).isEqualTo(BigDecimal.valueOf(20000));

    }

    @Test
    public void usingOfInvalidFilterException() {
        ProductFilterInfoRequest request = new ProductFilterInfoRequest();
        request.setBaseCategory(2L);
        request.setCurrencyCode("RUR");
        request.setContexts(ImmutableList.of(ACCOUNT_PUBLISHED_OFFLINE_PRODUCTS));

        Map<String, JsonNode> filterJson = new HashMap<>();
        filterJson.put(BrandFilterProcessor.FILTER_CODE, idListNode(504L, 675L, 1860L));
        filterJson.put("abracadabra", idListNode(504L, 675L, 1860L));

        request.setFilters(filterJson);

        ProductFiltrationContext filtrationContext = new ProductFiltrationContext();
        try {
            productFilterTransformationService.fillFiltrationContext(filtrationContext, request);
            productFilterTransformationService.createFilterSpecification(filtrationContext);
            failBecauseExceptionWasNotThrown(FilterFormatException.class);
        } catch (FilterFormatException ex) {
            assertThat(ex.getMessage())
                    .isEqualTo("Не удалось найти подходящий обработчик для фильтра с кодом abracadabra");
        }
    }

    @Test
    @Transactional
    public void verifyFiltersOverAvailableFilters() {
        FilterTransformationService.AvailableFilters availableFilters = new FilterTransformationService.AvailableFilters();

        // сначала проверим, что норм транслируются все фильтры
        availableFilters.setFilter(ImmutableList.of(131L, 136L, 164L, 156L, 186L));
        availableFilters.setCategory(ImmutableList.of(2L, 36L, 56L, 58L, 59L));
        availableFilters.setBrand(ImmutableList.of(504L, 675L, 1860L, 1570L));
        availableFilters.setProductCondition(ImmutableList.of(1L, 2L));
        availableFilters.setProductModel(ImmutableList.of(
                new ProductModelDTO().setId(1L).setName("2.55").setUrl("255"),
                new ProductModelDTO().setId(644L).setName("Classic Flap").setUrl("classic-flap"),
                new ProductModelDTO().setId(3L).setName("Boy").setUrl("boy"),
                new ProductModelDTO().setId(4L).setName("Model 4").setUrl("model4")
        ));
        availableFilters.setSize(ImmutableList.of(397L, 398L, 400L));
        availableFilters.setIsInStock(true);
        availableFilters.setNewCollection(true);
        availableFilters.setExclusiveSelection(true);
        availableFilters.setHasOurChoice(true);
        availableFilters.setOnSale(true);
        availableFilters.setStreetwear(true);
        availableFilters.setVintage(true);
        availableFilters.setStartPrice(BigDecimal.valueOf(5000));
        availableFilters.setEndPrice(BigDecimal.valueOf(20000));
        availableFilters.setLocationTags(ImmutableList.of(1L, 2L));
        availableFilters.setSellerTypes(ImmutableList.of(3L, 4L));

        ProductFilterInfoRequest request = new ProductFilterInfoRequest();
        request.setBaseCategory(2L);
        request.setCurrencyCode("RUR");
        request.setContexts(ImmutableList.of(ACCOUNT_PUBLISHED_OFFLINE_PRODUCTS));
        Map<String, JsonNode> filterJson = allFiltersJson();
        request.setFilters(filterJson);
        filterJson.put(BoutiqueLocationTagFilterProcessor.FILTER_CODE, null);

        ProductFiltrationContext filtrationContext = new ProductFiltrationContext();
        productFilterTransformationService.fillFiltrationContext(filtrationContext, request);
        productFilterTransformationService.createFilterSpecification(filtrationContext);

        filtrationContext.setWithValues(true);

        List<ProductFilter> productFilters =
                productFilterTransformationService.transformFilters(availableFilters, filtrationContext);

        assertThat(productFilters).hasSize(19);
        // заодно проверим и порядок фильтров
        assertCategoryFilter(productFilters.get(0), true);
        assertBrandFilter(productFilters.get(1), true);
        assertSizeFilter(productFilters.get(2), true);

        // названия фильтров атрибутов берутся из данных, поэтому перевод не делается
        assertSimpleList(productFilters.get(3), true, "attribute_10", "Цвет",
                ImmutableList.of(131L, 136L, 164L),
                ImmutableList.of(131L, 136L, 156L, 164L),
                Collections.emptyList());

        assertSimpleList(productFilters.get(4), true, "attribute_13", "Посадка",
                ImmutableList.of(186L),
                ImmutableList.of(186L),
                Collections.emptyList());

        assertHotList(productFilters.get(5), ConditionFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(ConditionFilterProcessor.FILTER_NAME),
                ImmutableList.of(1L),
                ImmutableList.of(1L, 2L));

        assertPrice(productFilters.get(6), BigDecimal.valueOf(5000), BigDecimal.valueOf(20000));

        assertSimpleList(productFilters.get(7), true, ModelFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(ModelFilterProcessor.FILTER_NAME),
                ImmutableList.of(1L, 644L, 3L),
                ImmutableList.of(1L, 644L, 3L, 4L),
                Collections.singletonList(messageSourceAccessor.getMessage(ModelFilterProcessor.FILTER_NAME)));

        assertHotList(productFilters.get(8), SellerTypeFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(SellerTypeFilterProcessor.FILTER_NAME),
                ImmutableList.of(1L),
                ImmutableList.of(1L, 3L, 4L));

        assertSimpleList(productFilters.get(9), true, LocationTagFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(LocationTagFilterProcessor.FILTER_NAME),
                ImmutableList.of(1L),
                ImmutableList.of(1L, 2L),
                Collections.emptyList());

        assertBoolean(productFilters.get(10), SaleFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(SaleFilterProcessor.FILTER_NAME),
                true, true);
        assertBoolean(productFilters.get(11), OskellyChoiceFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(OskellyChoiceFilterProcessor.FILTER_NAME),
                true, true);
        assertBoolean(productFilters.get(12), StreetwearFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(StreetwearFilterProcessor.FILTER_NAME),
                true, true);
        assertBoolean(productFilters.get(13), NewCollectionFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(NewCollectionFilterProcessor.FILTER_NAME),
                true, true);
        assertBoolean(productFilters.get(14), InStockFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(InStockFilterProcessor.FILTER_NAME),
                true, true);
        assertBoolean(productFilters.get(15), VintageFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(VintageFilterProcessor.FILTER_NAME),
                true, true);
        assertBoolean(productFilters.get(16), BrandNewFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(BrandNewFilterProcessor.FILTER_NAME),
                false, true);
        assertBoolean(productFilters.get(17), ResaleFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(ResaleFilterProcessor.FILTER_NAME),
                false, true);
        assertBoolean(productFilters.get(18), ExclusiveSelectionFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(ExclusiveSelectionFilterProcessor.FILTER_NAME),
                true, true);

        // проверим, что не приходят значения, когда их не запрашивают
        filtrationContext.setWithValues(false);
        productFilters =
                productFilterTransformationService.transformFilters(availableFilters, filtrationContext);
        assertCategoryFilter(productFilters.get(0), false);
        assertBrandFilter(productFilters.get(1), false);
        assertSizeFilter(productFilters.get(2), false);
        assertSimpleList(productFilters.get(3), false, "attribute_10", "Цвет",
                ImmutableList.of(131L, 136L, 164L),
                ImmutableList.of(131L, 136L, 156L, 164L),
                Collections.emptyList());
        assertSimpleList(productFilters.get(4), false, "attribute_13", "Посадка",
                ImmutableList.of(186L),
                ImmutableList.of(186L),
                Collections.emptyList());
        assertSimpleList(productFilters.get(7), false, ModelFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(ModelFilterProcessor.FILTER_NAME),
                ImmutableList.of(1L, 644L, 3L),
                ImmutableList.of(1L, 644L, 3L, 4L),
                Collections.singletonList(messageSourceAccessor.getMessage(ModelFilterProcessor.FILTER_NAME)));
        assertSimpleList(productFilters.get(9), false, LocationTagFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(LocationTagFilterProcessor.FILTER_NAME),
                ImmutableList.of(1L),
                ImmutableList.of(1L, 644L),
                Collections.emptyList());

        // теперь проверим корнер кейсы
        availableFilters.setFilter(Collections.emptyList());
        availableFilters.setCategory(Collections.emptyList());
        availableFilters.setBrand(Collections.emptyList());
        availableFilters.setProductCondition(ImmutableList.of(1L, 2L));
        availableFilters.setProductModel(Collections.emptyList());
        availableFilters.setSize(Collections.emptyList());
        availableFilters.setSellerTypes(ImmutableList.of(1L, 2L));
        availableFilters.setIsInStock(false);
        availableFilters.setNewCollection(false);
        availableFilters.setHasOurChoice(false);
        availableFilters.setOnSale(false);
        availableFilters.setStreetwear(false);
        availableFilters.setVintage(false);
        availableFilters.setStartPrice(null);
        availableFilters.setEndPrice(BigDecimal.valueOf(20000));

        // насильно поменяем в контексте, что галка Новое не выбрана
        filtrationContext.getFilterValues().put(WithTagFilterProcessor.FILTER_CODE, new BooleanValue(false));
        filtrationContext.getFilterValues().remove(CategoryFilterProcessor.FILTER_CODE);
        filtrationContext.getFilterValues().remove(BrandFilterProcessor.FILTER_CODE);
        filtrationContext.getFilterValues().remove("attribute_10");
        filtrationContext.getFilterValues().remove("attribute_13");
        filtrationContext.getFilterValues().remove(BrandFilterProcessor.FILTER_CODE);
        filtrationContext.setWithValues(true);
        productFilters =
                productFilterTransformationService.transformFilters(availableFilters, filtrationContext);

        assertThat(productFilters).hasSize(15);

        assertHotList(productFilters.get(1), ConditionFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(ConditionFilterProcessor.FILTER_NAME),
                ImmutableList.of(1L, 2L),
                ImmutableList.of(1L, 2L));

        assertPrice(productFilters.get(2), null, BigDecimal.valueOf(20000));

        assertHotList(productFilters.get(4), SellerTypeFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(SellerTypeFilterProcessor.FILTER_NAME),
                ImmutableList.of(1L),
                ImmutableList.of(1L, 2L));

        assertBoolean(productFilters.get(6), SaleFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(SaleFilterProcessor.FILTER_NAME),
                true, false);
        assertBoolean(productFilters.get(7), OskellyChoiceFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(OskellyChoiceFilterProcessor.FILTER_NAME),
                true, false);
        assertBoolean(productFilters.get(8), StreetwearFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(StreetwearFilterProcessor.FILTER_NAME),
                true, false);
        assertBoolean(productFilters.get(9), NewCollectionFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(NewCollectionFilterProcessor.FILTER_NAME),
                true, false);
        assertBoolean(productFilters.get(10), InStockFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(InStockFilterProcessor.FILTER_NAME),
                true, false);
        assertBoolean(productFilters.get(11), VintageFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(VintageFilterProcessor.FILTER_NAME),
                true, false);
        assertBoolean(productFilters.get(12), BrandNewFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(BrandNewFilterProcessor.FILTER_NAME),
                false, true);
        assertBoolean(productFilters.get(13), ResaleFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(ResaleFilterProcessor.FILTER_NAME),
                false, true);
        assertBoolean(productFilters.get(14), ExclusiveSelectionFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(ExclusiveSelectionFilterProcessor.FILTER_NAME),
                true, true);

        availableFilters.setSellerTypes(ImmutableList.of(3L, 4L));
        availableFilters.setStartPrice(BigDecimal.valueOf(5000));
        availableFilters.setEndPrice(null);
        productFilters =
                productFilterTransformationService.transformFilters(availableFilters, filtrationContext);
        assertThat(productFilters).hasSize(15);

        assertPrice(productFilters.get(2), BigDecimal.valueOf(5000), null);

        assertHotList(productFilters.get(4), SellerTypeFilterProcessor.FILTER_CODE,
                messageSourceAccessor.getMessage(SellerTypeFilterProcessor.FILTER_NAME),
                ImmutableList.of(1L),
                ImmutableList.of(1L, 3L, 4L));
    }

    @Test
    @Transactional
    public void getFiltersAndData() {
        FiltrationServiceFeatures filtrationServiceFeatures =
                new FiltrationServiceFeatures().setExclusiveSelectionFilterCanBeEnabled(true);
        CatalogSlotFeaturesService.Features slotFeatures = new CatalogSlotFeaturesService.Features();

        ProductFilterRequest request = new ProductFilterRequest();
        request.setBaseCategory(2L);
        request.setCurrencyCode("RUB");

        Map<String, JsonNode> filterJson = new HashMap<>();
        filterJson.put(BrandFilterProcessor.FILTER_CODE, idListNode(504L, 675L));
        filterJson.put(CategoryFilterProcessor.FILTER_CODE, idListNode(5L, 14L));
        filterJson.put("attribute_10", idListNode(164L));
        filterJson.put(WithTagFilterProcessor.FILTER_CODE, booleanNode(true));
        filterJson.put(PriceFilterProcessor.FILTER_CODE, priceNode(null, BigDecimal.valueOf(100000)));

        request.setFilters(filterJson);

        ProductFilters productFilters =
                productFilterProcessorEngine.getAvailableFilters(request, filtrationServiceFeatures, slotFeatures);

        assertThat(productFilters.getItems()).isNull();
        assertThat(productFilters.getSorting()).hasSizeGreaterThan(1);
        assertThat(productFilters.getSorting().get(0).getCode()).isEqualTo(NewSortingProcessor.CODE);
        assertThat(productFilters.getSorting().get(0).getIsSelected()).isTrue();
        assertThat(productFilters.getSorting().get(1).getIsSelected()).isFalse();

        assertThat(productFilters.getItemsCount()).isEqualTo(8);
        assertThat(productFilters.getHotFilters()).containsOnly(
                CategoryFilterProcessor.FILTER_CODE,
                BrandFilterProcessor.FILTER_CODE,
                ConditionFilterProcessor.FILTER_CODE,
                SizeFilterProcessor.FILTER_CODE);

        assertThat(productFilters.getFilters()).hasSize(14);

        assertThat(productFilters.getFilters().get(0)).isInstanceOf(CategoryTreeFilter.class);

        CategoryTreeFilter categoryFilter = (CategoryTreeFilter) productFilters.getFilters().get(0);
        assertThat(categoryFilter.getCode()).isEqualTo(CategoryFilterProcessor.FILTER_CODE);
        assertThat(categoryFilter.getValues()).isNull();
        assertThat(categoryFilter.getHasMoreValues()).isTrue();

        // проверка, что вернулся только один атрибут (Цвет),
        // так как фильтровалось по разнородным (Обувь и сумки) категориям
        assertThat(productFilters.getFilters().stream()
                                   .filter(filter -> filter.getCode().startsWith("attribute_"))
                                   .count()).isEqualTo(1);

        request.setWithItems(true);
        request.setPageLength(3);
        request.setSorting(PriceAscSortingProcessor.CODE);
        productFilters =
                productFilterProcessorEngine.getAvailableFilters(request, filtrationServiceFeatures, slotFeatures);

        assertThat(productFilters.getItems()).isNotNull();

        List<ProductDTO> items = productFilters.getItems().getItems();
        assertThat(items).hasSize(3);
        // проверка того, что сортировка работает
        assertThat(items.get(0).getPrice()).isLessThanOrEqualTo(items.get(1).getPrice());
        assertThat(items.get(1).getPrice()).isLessThanOrEqualTo(items.get(2).getPrice());

        assertThat(productFilters.getSorting()).hasSizeGreaterThan(1);
        assertThat(productFilters.getSorting().get(0).getCode()).isEqualTo(NewSortingProcessor.CODE);
        assertThat(productFilters.getSorting().get(0).getIsSelected()).isFalse();
        assertThat(productFilters.getSorting().get(1).getCode()).isEqualTo(PriceAscSortingProcessor.CODE);
        assertThat(productFilters.getSorting().get(1).getIsSelected()).isTrue();

        assertThat(productFilters.getFilters()).hasSize(14);

        assertThat(productFilters.getFilters().get(0)).isInstanceOf(CategoryTreeFilter.class);

        categoryFilter = (CategoryTreeFilter) productFilters.getFilters().get(0);
        assertThat(categoryFilter.getCode()).isEqualTo(CategoryFilterProcessor.FILTER_CODE);
        assertThat(categoryFilter.getValues()).isNull();
        assertThat(categoryFilter.getHasMoreValues()).isTrue();

        // проверка метода возврата списка товаров
        Page<ProductDTO> productItems = productFilterProcessorEngine.getProductItems(request);
        assertThat(productItems.getItemsCount()).isEqualTo(3);
        items = productItems.getItems();
        assertThat(items).hasSize(3);
        // проверка того, что сортировка работает
        assertThat(items.get(0).getPrice()).isLessThanOrEqualTo(items.get(1).getPrice());
        assertThat(items.get(1).getPrice()).isLessThanOrEqualTo(items.get(2).getPrice());

        // проверка метода возврата количества товаров
        ItemsCount itemsCount = productFilterProcessorEngine.getItemsCount(request);
        assertThat(itemsCount.getItemsCount()).isEqualTo(8);

        filterJson = new HashMap<>();
        filterJson.put(BrandFilterProcessor.FILTER_CODE, idListNode(504L, 675L));
        filterJson.put(CategoryFilterProcessor.FILTER_CODE, idListNode(5L, 6L));
        filterJson.put("attribute_10", idListNode(164L));
        filterJson.put(WithTagFilterProcessor.FILTER_CODE, booleanNode(true));
        filterJson.put(PriceFilterProcessor.FILTER_CODE, priceNode(null, BigDecimal.valueOf(100000)));

        request.setFilters(filterJson);

        productFilters =
                productFilterProcessorEngine.getAvailableFilters(request, filtrationServiceFeatures, slotFeatures);

        // проверка, что вернулись три атрибута (Цвет, Материал и Длина ручек),
        // так как фильтровалось по однородным (Обувь) категориям
        assertThat(productFilters.getFilters().stream()
                                   .filter(filter -> filter.getCode().startsWith("attribute_"))
                                   .count()).isEqualTo(3);

        // проверка того, что при фильтрации учитывается baseCategory, если нет фильтров по категории

        // сначала проверим, сколько товаров без baseCategory
        filterJson = new HashMap<>();
        filterJson.put(BrandFilterProcessor.FILTER_CODE, idListNode(504L, 675L));
        filterJson.put("attribute_10", idListNode(164L));
        filterJson.put(WithTagFilterProcessor.FILTER_CODE, booleanNode(true));
        filterJson.put(PriceFilterProcessor.FILTER_CODE, priceNode(null, BigDecimal.valueOf(30000)));

        request.setFilters(filterJson);
        request.setBaseCategory(null);

        productFilters =
                productFilterProcessorEngine.getAvailableFilters(request, filtrationServiceFeatures, slotFeatures);

        assertThat(productFilters.getItemsCount()).isEqualTo(230);
        assertThat(productFilters.getItems().getTotalAmount()).isEqualTo(230);

        // а теперь передадим категорию ботинок в baseCategory
        request.setBaseCategory(12L);

        productFilters =
                productFilterProcessorEngine.getAvailableFilters(request, filtrationServiceFeatures, slotFeatures);

        assertThat(productFilters.getItemsCount()).isEqualTo(2);
        assertThat(productFilters.getItems().getItemsCount()).isEqualTo(2);
    }

    @Test
    @Transactional
    public void testFilterPresets() {

        ProductFilterRequest request = new ProductFilterRequest();
        request.setBaseCategory(1L);
        request.setCurrencyCode("RUB");

        Map<String, JsonNode> presetJson = new HashMap<>();
        request.setPresets(presetJson);

        Map<String, JsonNode> filterJson = new HashMap<>();
        request.setFilters(filterJson);

        // перемещаем часть товаров в оффлайн для проверки фильтрации по тегам местоположения
        ProductItemLocation stockLocation = productItemLocationRepository
                .getProductItemLocationByCode(LOCATION_CODE_WAREHOUSE).get();
        ProductItemLocation stolLocation = productItemLocationRepository
                .getProductItemLocationByCode(LOCATION_CODE_BOUTIQUE_STOLESHNIKOV).get();
        ProductItemLocation kuzLocation = productItemLocationRepository
                .getProductItemLocationByCode(LOCATION_CODE_BOUTIQUE_KUZNETSKY_BRIDGE).get();
        List<ProductDTO> products = productFilterProcessorEngine.getProductItems(request).getItems();
        moveProductItemsToLocation(products.get(0).getProductId(), stockLocation);
        moveProductItemsToLocation(products.get(1).getProductId(), stolLocation);
        moveProductItemsToLocation(products.get(2).getProductId(), kuzLocation);

        commitAndStartNewTransaction();

        // сначала запросим фильтры без преднастроек
        ProductFilters availableFilters = productFilterProcessorEngine.getAvailableFilters(request);
        Long allItemsCount = availableFilters.getItemsCount();

        ProductFilter filterInfo = productFilterProcessorEngine.getFilterInfo(request, CategoryFilterProcessor.FILTER_CODE);
        // проверяем, что без преднастроек вернулся корень
        assertThat(((CategoryTreeFilter) filterInfo).getValues()).hasSize(3);
        assertThat(((CategoryTreeFilter) filterInfo).getValues().get(2).getName()).isEqualTo("Мужское");

        filterInfo = productFilterProcessorEngine.getFilterInfo(request, BrandFilterProcessor.FILTER_CODE);
        // проверяем, что без преднастроек вернулось много брендов
        assertThat(((MultiListFilter) filterInfo).getValues()).hasSize(2);
        assertThat(((MultiListFilter) filterInfo).getValues().get(1).getEntries()).hasSizeGreaterThan(10);

        filterInfo = productFilterProcessorEngine.getFilterInfo(request, SizeFilterProcessor.FILTER_CODE);
        // проверяем, что без преднастроек вернулось много размеров
        assertThat(((SizeFilter) filterInfo).getValues()).hasSizeGreaterThan(2);

        filterInfo = productFilterProcessorEngine.getFilterInfo(request, PriceFilterProcessor.FILTER_CODE);
        // проверяем, что без преднастроек вернулся большой диапазон цен
        assertThat(((PriceFilter) filterInfo).getLower()).isLessThan(BigDecimal.valueOf(10000L));
        assertThat(((PriceFilter) filterInfo).getUpper()).isGreaterThan(BigDecimal.valueOf(100000L));

        filterInfo = productFilterProcessorEngine.getFilterInfo(request, ConditionFilterProcessor.FILTER_CODE);
        // проверяем, что без преднастроек вернулись все состояния товара
        assertThat(((MultiListFilter) filterInfo).getValues()).hasSize(1);
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries()).hasSize(3);

        filterInfo = productFilterProcessorEngine.getFilterInfo(request, SellerTypeFilterProcessor.FILTER_CODE);
        // проверяем, что без преднастроек вернулись все типы продавца
        assertThat(((MultiListFilter) filterInfo).getValues()).hasSize(1);
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries()).hasSize(1);

        filterInfo = productFilterProcessorEngine.getFilterInfo(request, OskellyChoiceFilterProcessor.FILTER_CODE);
        // проверяем, что без преднастроек тумблер в выключенном состоянии, но доступный для переключения
        assertThat(((BooleanFilter) filterInfo).getValue()).isFalse();
        assertThat(((BooleanFilter) filterInfo).getIsEnabled()).isTrue();

        filterInfo = productFilterProcessorEngine.getFilterInfo(request, LocationTagFilterProcessor.FILTER_CODE);
        // проверяем, что без преднастроек вернулись все теги с категорией местоположения
        assertThat(((MultiListFilter) filterInfo).getValues()).hasSize(1);
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries()).hasSize(3);

        filterInfo = productFilterProcessorEngine.getFilterInfo(request, BoutiqueLocationTagFilterProcessor.FILTER_CODE);
        // проверяем, что без преднастроек вернулись все теги с категорией местоположения
        assertThat(((MultiListFilter) filterInfo).getValues()).hasSize(1);
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries()).hasSize(2);

        // ограничиваем категории преднастройками, чтобы отображались только Джинсы и Блузы
        presetJson.put(CategoryFilterProcessor.FILTER_CODE, idListNode(56L, 542L));

        availableFilters = productFilterProcessorEngine.getAvailableFilters(request);
        // проверим, что преднастройки ограничили количество подходящих товаров
        assertThat(availableFilters.getItemsCount()).isLessThan(allItemsCount);

        filterInfo = productFilterProcessorEngine.getFilterInfo(request, CategoryFilterProcessor.FILTER_CODE);
        // в доступных значениях должны быть только блузки и джинсы
        assertThat(((CategoryTreeFilter) filterInfo).getValues()).hasSize(2);
        assertThat(((CategoryTreeFilter) filterInfo).getValues().get(0).getName()).isEqualTo("Джинсы");
        assertThat(((CategoryTreeFilter) filterInfo).getValues().get(1).getName()).isEqualTo("Рубашки и блузки");

        presetJson.clear();
        // оставляем только gucci и hermes
        presetJson.put(BrandFilterProcessor.FILTER_CODE, idListNode(675L, 2037L));

        availableFilters = productFilterProcessorEngine.getAvailableFilters(request);
        // проверим, что преднастройки ограничили количество подходящих товаров
        assertThat(availableFilters.getItemsCount()).isLessThan(allItemsCount);

        filterInfo = productFilterProcessorEngine.getFilterInfo(request, BrandFilterProcessor.FILTER_CODE);
        // в доступных значениях должны быть только блузки и джинсы
        assertThat(((MultiListFilter) filterInfo).getValues()).hasSize(2);
        assertThat(((MultiListFilter) filterInfo).getValues().get(1).getEntries()).hasSize(2);
        assertThat(((MultiListFilter) filterInfo).getValues().get(1).getEntries().get(0).getValue())
                .isEqualTo("GUCCI");
        assertThat(((MultiListFilter) filterInfo).getValues().get(1).getEntries().get(1).getValue())
                .isEqualTo("HERMES");

        presetJson.clear();
        // оставляем только 36 и 37 размеры обуви
        presetJson.put(SizeFilterProcessor.FILTER_CODE, idListNode(12L, 14L));

        availableFilters = productFilterProcessorEngine.getAvailableFilters(request);
        // проверим, что преднастройки ограничили количество подходящих товаров
        assertThat(availableFilters.getItemsCount()).isLessThan(allItemsCount);

        filterInfo = productFilterProcessorEngine.getFilterInfo(request, SizeFilterProcessor.FILTER_CODE);
        // в доступных значениях должны быть только размеры по обуви
        assertThat(((SizeFilter) filterInfo).getValues()).hasSize(1);
        assertThat(((SizeFilter) filterInfo).getValues().get(0).getName()).isEqualTo("Обувь");
        assertThat(((SizeFilter) filterInfo).getValues().get(0).getSizesValue().getValues()).hasSize(2);
        assertThat(((SizeFilter) filterInfo).getValues().get(0).getSizesValue().getValues().get(0).getId())
                .isEqualTo(12);
        assertThat(((SizeFilter) filterInfo).getValues().get(0).getSizesValue().getValues().get(1).getId())
                .isEqualTo(14);

        presetJson.clear();
        // делаем ограничение по цене от 20к до 80к
        presetJson.put(PriceFilterProcessor.FILTER_CODE,
                priceNode(BigDecimal.valueOf(20000), BigDecimal.valueOf(80000)));

        availableFilters = productFilterProcessorEngine.getAvailableFilters(request);
        // проверим, что преднастройки ограничили количество подходящих товаров
        assertThat(availableFilters.getItemsCount()).isLessThan(allItemsCount);

        filterInfo = productFilterProcessorEngine.getFilterInfo(request, PriceFilterProcessor.FILTER_CODE);
        // в доступных значениях должны быть цены-преднастройки
        assertThat(((PriceFilter) filterInfo).getLower()).isEqualTo(BigDecimal.valueOf(20000));
        assertThat(((PriceFilter) filterInfo).getUpper()).isEqualTo(BigDecimal.valueOf(80000));

        // а теперь проверим, что если в преднастройках не указать какую-то из цен,
        // то цена возьмется из доступных значений
        presetJson.clear();
        presetJson.put(PriceFilterProcessor.FILTER_CODE, priceNode(null, BigDecimal.valueOf(80000)));
        availableFilters = productFilterProcessorEngine.getAvailableFilters(request);
        // проверим, что преднастройки ограничили количество подходящих товаров
        assertThat(availableFilters.getItemsCount()).isLessThan(allItemsCount);

        filterInfo = productFilterProcessorEngine.getFilterInfo(request, PriceFilterProcessor.FILTER_CODE);
        assertThat(((PriceFilter) filterInfo).getLower()).isLessThan(BigDecimal.valueOf(10000));
        assertThat(((PriceFilter) filterInfo).getUpper()).isEqualTo(BigDecimal.valueOf(80000));

        presetJson.clear();
        presetJson.put(PriceFilterProcessor.FILTER_CODE, priceNode(BigDecimal.valueOf(20000), null));
        availableFilters = productFilterProcessorEngine.getAvailableFilters(request);
        // проверим, что преднастройки ограничили количество подходящих товаров
        assertThat(availableFilters.getItemsCount()).isLessThan(allItemsCount);

        filterInfo = productFilterProcessorEngine.getFilterInfo(request, PriceFilterProcessor.FILTER_CODE);
        assertThat(((PriceFilter) filterInfo).getLower()).isEqualTo(BigDecimal.valueOf(20000));
        assertThat(((PriceFilter) filterInfo).getUpper()).isGreaterThan(BigDecimal.valueOf(100000));

        presetJson.clear();
        // проверим, что в фактической фильтрации будет браться пересечение пользовательской фильтрации диапазона цены
        // и преднастроек цен
        filterJson.put(PriceFilterProcessor.FILTER_CODE, priceNode(
                BigDecimal.valueOf(50000), BigDecimal.valueOf(60000)));
        presetJson.put(PriceFilterProcessor.FILTER_CODE,
                priceNode(BigDecimal.valueOf(20000), BigDecimal.valueOf(80000)));

        request.setSorting(PriceAscSortingProcessor.CODE);
        Page<ProductDTO> productItems = productFilterProcessorEngine.getProductItems(request);
        assertThat(productItems.getTotalAmount()).isPositive();
        assertThat(productItems.getItems().get(0).getPrice()).isGreaterThanOrEqualTo(BigDecimal.valueOf(50000));

        request.setSorting(PriceDescSortingProcessor.CODE);
        productItems = productFilterProcessorEngine.getProductItems(request);
        assertThat(productItems.getTotalAmount()).isPositive();
        assertThat(productItems.getItems().get(0).getPrice()).isLessThanOrEqualTo(BigDecimal.valueOf(60000));

        presetJson.clear();
        filterJson.clear();

        filterJson.put(PriceFilterProcessor.FILTER_CODE, priceNode(
                BigDecimal.valueOf(50000), BigDecimal.valueOf(100000)));
        presetJson.put(PriceFilterProcessor.FILTER_CODE,
                priceNode(BigDecimal.valueOf(20000), BigDecimal.valueOf(80000)));

        request.setSorting(PriceAscSortingProcessor.CODE);
        productItems = productFilterProcessorEngine.getProductItems(request);
        assertThat(productItems.getTotalAmount()).isPositive();
        assertThat(productItems.getItems().get(0).getPrice()).isGreaterThanOrEqualTo(BigDecimal.valueOf(50000));

        request.setSorting(PriceDescSortingProcessor.CODE);
        productItems = productFilterProcessorEngine.getProductItems(request);
        assertThat(productItems.getTotalAmount()).isPositive();
        assertThat(productItems.getItems().get(0).getPrice()).isLessThanOrEqualTo(BigDecimal.valueOf(80000));

        filterJson.put(PriceFilterProcessor.FILTER_CODE, priceNode(
                BigDecimal.valueOf(90000), BigDecimal.valueOf(100000)));
        presetJson.put(PriceFilterProcessor.FILTER_CODE,
                priceNode(BigDecimal.valueOf(20000), BigDecimal.valueOf(80000)));

        // в случае, если нет пересечения пользовательского диапазона цен и преднастроек, бросаем
        // IllegalStateException("Start price can't be higher then end price");
        assertThatExceptionOfType(IllegalStateException.class).isThrownBy(() -> {
            productFilterProcessorEngine.getAvailableFilters(request);
        }).withMessage("Start price can't be higher then end price");

        presetJson.clear();
        filterJson.clear();
        // оставляем только Новое и Отличное состояния
        presetJson.put(ConditionFilterProcessor.FILTER_CODE, idListNode(1L, 2L));

        availableFilters = productFilterProcessorEngine.getAvailableFilters(request);
        // проверим, что преднастройки ограничили количество подходящих товаров
        assertThat(availableFilters.getItemsCount()).isLessThan(allItemsCount);

        filterInfo = productFilterProcessorEngine.getFilterInfo(request, ConditionFilterProcessor.FILTER_CODE);
        // в доступных значениях должны быть только Новые и в Отличном состоянии
        assertThat(((MultiListFilter) filterInfo).getValues()).hasSize(1);
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries()).hasSize(2);
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries().get(0).getId()).isEqualTo(1L);
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries().get(1).getId()).isEqualTo(2L);

        presetJson.clear();

        // оставляем только частный продавец
        presetJson.put(SellerTypeFilterProcessor.FILTER_CODE, idListNode(1L));

        availableFilters = productFilterProcessorEngine.getAvailableFilters(request);
        // проверим, что количество подходящих товаров не уменьшилось, т.к. в исходных данных только один тип продавца
        assertThat(availableFilters.getItemsCount()).isEqualTo(allItemsCount);

        filterInfo = productFilterProcessorEngine.getFilterInfo(request, SellerTypeFilterProcessor.FILTER_CODE);
        // в доступных значениях должны быть только частные продавцы
        assertThat(((MultiListFilter) filterInfo).getValues()).hasSize(1);
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries()).hasSize(1);
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries().get(0).getId()).isEqualTo(1L);

        presetJson.clear();

        // оставляем только товары с выбором оскелли
        presetJson.put(OskellyChoiceFilterProcessor.FILTER_CODE, booleanNode(true));

        availableFilters = productFilterProcessorEngine.getAvailableFilters(request);
        // проверим, что преднастройки ограничили количество подходящих товаров
        assertThat(availableFilters.getItemsCount()).isLessThan(allItemsCount);

        filterInfo = productFilterProcessorEngine.getFilterInfo(request, OskellyChoiceFilterProcessor.FILTER_CODE);
        // фильтр должен быть выставлен в True и должен задизейблиться тумблер
        assertThat(((BooleanFilter) filterInfo).getValue()).isTrue();
        assertThat(((BooleanFilter) filterInfo).getIsEnabled()).isFalse();

        presetJson.clear();
        // оставляем только товары в бутике на столешникова
        presetJson.put(LocationTagFilterProcessor.FILTER_CODE, idListNode(stolLocation.getId()));

        availableFilters = productFilterProcessorEngine.getAvailableFilters(request);
        // проверим, что преднастройки ограничили количество подходящих товаров
        assertThat(availableFilters.getItemsCount()).isLessThan(allItemsCount);

        ItemsCount itemsCount = productFilterProcessorEngine.getItemsCount(request);
        assertThat(itemsCount.getItemsCount()).isLessThan(allItemsCount);

        filterInfo = productFilterProcessorEngine.getFilterInfo(request, LocationTagFilterProcessor.FILTER_CODE);
        // в доступных значениях должен остаться только бутик на Столешникова
        assertThat(((MultiListFilter) filterInfo).getValues()).hasSize(1);
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries()).hasSize(1);

        presetJson.clear();
        // оставляем только товары в бутике на столешникова
        presetJson.put(BoutiqueLocationTagFilterProcessor.FILTER_CODE, idListNode(stolLocation.getId()));

        availableFilters = productFilterProcessorEngine.getAvailableFilters(request);
        // проверим, что преднастройки ограничили количество подходящих товаров
        assertThat(availableFilters.getItemsCount()).isLessThan(allItemsCount);

        itemsCount = productFilterProcessorEngine.getItemsCount(request);
        assertThat(itemsCount.getItemsCount()).isLessThan(allItemsCount);

        filterInfo = productFilterProcessorEngine.getFilterInfo(request, BoutiqueLocationTagFilterProcessor.FILTER_CODE);
        // в доступных значениях должен остаться только бутик на Столешникова
        assertThat(((MultiListFilter) filterInfo).getValues()).hasSize(1);
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries()).hasSize(1);

        // ----------- Раздел проверки атрибутов

        // работаем с Женской одеждой, чтобы можно было норм атрибуты потестить
        request.setBaseCategory(36L);
        presetJson.clear();

        availableFilters = productFilterProcessorEngine.getAvailableFilters(request);
        allItemsCount = availableFilters.getItemsCount();

        // проверяем, что кроме цвета нет доступных атрибутов
        List<ProductFilter> attributeFilters =
                availableFilters.getFilters().stream()
                                .filter(filter -> filter.getCode().startsWith("attribute_"))
                                .collect(Collectors.toList());
        assertThat(attributeFilters).hasSize(1);
        assertThat(attributeFilters.get(0).getCode()).isEqualTo("attribute_10");

        filterInfo = productFilterProcessorEngine.getFilterInfo(request, "attribute_10");
        // проверяем, что без преднастроек вернулось много цветов
        assertThat(((MultiListFilter) filterInfo).getValues()).hasSize(1);
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries()).hasSizeGreaterThan(5);

        // ограничим выбор красным и бордовым
        presetJson.put("attribute_10", idListNode(134L, 143L));

        availableFilters = productFilterProcessorEngine.getAvailableFilters(request);
        // проверим, что преднастройки ограничили количество подходящих товаров
        assertThat(availableFilters.getItemsCount()).isLessThan(allItemsCount);

        filterInfo = productFilterProcessorEngine.getFilterInfo(request, "attribute_10");
        // проверяем, что среди доступных значений вернулись только цвета из преднастроек
        assertThat(((MultiListFilter) filterInfo).getValues()).hasSize(1);
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries()).hasSize(2);
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries().get(0).getValue())
                .isEqualTo("Бордовый");
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries().get(1).getValue())
                .isEqualTo("Красный");

        // ограничим выбор красным и бордовым через фильтр сырых значений атрибутов
        presetJson.clear();
        presetJson.put(AttributeValuesFilterProcessor.FILTER_CODE, idListNode(134L, 143L));

        availableFilters = productFilterProcessorEngine.getAvailableFilters(request);
        // проверим, что преднастройки ограничили количество подходящих товаров
        assertThat(availableFilters.getItemsCount()).isLessThan(allItemsCount);

        filterInfo = productFilterProcessorEngine.getFilterInfo(request, "attribute_10");
        // проверяем, что среди доступных значений вернулись только цвета из преднастроек
        assertThat(((MultiListFilter) filterInfo).getValues()).hasSize(1);
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries()).hasSize(2);
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries().get(0).getValue())
                .isEqualTo("Бордовый");
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries().get(1).getValue())
                .isEqualTo("Красный");

        // а теперь попробуем выбрать что-то и проверим, что преднастройки атрибутов не сбились
        // Выберем белый цвет и еще для проверки Хлопок у материала джинсов
        filterJson.clear();
        filterJson.put(AttributeValuesFilterProcessor.FILTER_CODE, idListNode(131L, 284L));

        // в фильтрах должно вернуться теперь два фильтра по атрибутам
        availableFilters = productFilterProcessorEngine.getAvailableFilters(request);
        attributeFilters =
                availableFilters.getFilters().stream()
                                .filter(filter -> filter.getCode().startsWith("attribute_"))
                                .collect(Collectors.toList());
        assertThat(attributeFilters).hasSize(2);
        assertThat(attributeFilters.get(0).getCode()).isEqualTo("attribute_10");
        assertThat(attributeFilters.get(1).getCode()).isEqualTo("attribute_22");

        // проверяем, что среди доступных значений вернулись цвета из преднастроек + выбранный цвет
        filterInfo = productFilterProcessorEngine.getFilterInfo(request, "attribute_10");
        assertThat(((MultiListFilter) filterInfo).getValues()).hasSize(1);
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries()).hasSize(3);
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries().get(0).getValue())
                .isEqualTo("Белый");
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries().get(0).getIsSelected()).isTrue();
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries().get(1).getValue())
                .isEqualTo("Бордовый");
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries().get(2).getValue())
                .isEqualTo("Красный");

        // проверим, что есть значения в атрибуте Материал джинсов
        filterInfo = productFilterProcessorEngine.getFilterInfo(request, "attribute_22");
        assertThat(((MultiListFilter) filterInfo).getValues()).hasSize(1);
        assertThat(((MultiListFilter) filterInfo).getValues().get(0).getEntries()).hasSizeGreaterThan(0);
    }

    private void moveProductItemsToLocation(long productId, ProductItemLocation location) {
        Product product = productRepository.findById(productId).get();
        if (product.getAvailableProductItems() != null && !product.getAvailableProductItems().isEmpty()) {
            product.getAvailableProductItems()
                    .forEach(it -> productItemService.addOrUpdateStock(it, location, 1, ZonedDateTime.now()));
        } else {
            ProductItem productItem = new ProductItem();
            productItem.setProduct(product);
            productItem.setSize(sizeRepository.getOne(1L));
            productItem.setCount(1);
            productItem = productItemService.save(productItem);
            productItemService.addOrUpdateStock(productItem, location, 1, ZonedDateTime.now());
        }
    }

    private void assertBoolean(ProductFilter productFilter, String filterCode, String filterName,
                               Boolean value, Boolean isEnabled) {
        assertThat(productFilter.getType()).isEqualTo(BooleanValue.TYPE);
        assertThat(productFilter.getCode()).isEqualTo(filterCode);
        assertThat(productFilter.getName()).isEqualTo(filterName);
        assertThat(productFilter).isInstanceOf(BooleanFilter.class);

        BooleanFilter filter = (BooleanFilter) productFilter;
        assertThat(filter.getValue()).isEqualTo(value);
        assertThat(filter.getIsEnabled()).isEqualTo(isEnabled);
    }

    private void assertPrice(ProductFilter productFilter, BigDecimal lower, BigDecimal upper) {
        assertThat(productFilter.getType()).isEqualTo(PriceValue.TYPE);
        assertThat(productFilter.getCode()).isEqualTo(PriceFilterProcessor.FILTER_CODE);
        assertThat(productFilter.getName()).isEqualTo(
                messageSourceAccessor.getMessage(PriceFilterProcessor.FILTER_NAME));
        assertThat(productFilter).isInstanceOf(PriceFilter.class);

        PriceFilter filter = (PriceFilter) productFilter;
        assertThat(filter.getLower()).isEqualTo(lower);
        assertThat(filter.getUpper()).isEqualTo(upper);
    }

    private void assertHotList(ProductFilter productFilter,
                               String filterCode, String filterName,
                               List<Long> selectedIds, List<Long> availableIds) {
        assertThat(productFilter.getType()).isEqualTo(MultiListFilter.TYPE);
        assertThat(productFilter.getCode()).isEqualTo(filterCode);
        assertThat(productFilter.getName()).isEqualTo(filterName);
        assertThat(productFilter).isInstanceOf(MultiListFilter.class);

        MultiListFilter filter = (MultiListFilter) productFilter;
        assertThat(filter.getSelectedValues()).hasSize(selectedIds.size());
        for (int i = 0; i < selectedIds.size(); i++) {
            assertThat(filter.getSelectedValues().get(i).getId()).isIn(selectedIds.toArray());
        }

        assertThat(filter.getSearchableSections()).isEmpty();
        assertThat(filter.getHasMoreValues()).isFalse();

        assertThat(filter.getHotValues()).hasSize(availableIds.size());
        for (int i = 0; i < availableIds.size(); i++) {
            assertThat(filter.getHotValues().get(i).getId()).isIn(availableIds.toArray());
            if (selectedIds.contains(filter.getHotValues().get(i).getId())) {
                assertThat(filter.getHotValues().get(i).getIsSelected()).isTrue();
            } else {
                assertThat(filter.getHotValues().get(i).getIsSelected()).isFalse();
            }
        }
    }

    private void assertSimpleList(ProductFilter productFilter, boolean withValues,
                                  String filterCode, String filterName,
                                  List<Long> selectedIds, List<Long> availableIds,
                                  List<String> searchableSections) {
        assertThat(productFilter.getType()).isEqualTo(MultiListFilter.TYPE);
        assertThat(productFilter.getCode()).isEqualTo(filterCode);
        assertThat(productFilter.getName()).isEqualTo(filterName);
        assertThat(productFilter).isInstanceOf(MultiListFilter.class);

        MultiListFilter filter = (MultiListFilter) productFilter;
        assertThat(filter.getSelectedValues()).hasSize(selectedIds.size());
        for (int i = 0; i < selectedIds.size(); i++) {
            assertThat(filter.getSelectedValues().get(i).getId()).isIn(selectedIds.toArray());
        }

        assertThat(filter.getSearchableSections()).containsOnly(searchableSections.toArray(new String[0]));
        assertThat(filter.getHasMoreValues()).isTrue();

        if (withValues) {
            assertThat(filter.getValues()).hasSize(1);
            ListSection section = filter.getValues().get(0);
            assertThat(section.getEntries()).hasSize(availableIds.size());
            for (int i = 0; i < availableIds.size(); i++) {
                assertThat(section.getEntries().get(i).getId()).isIn(availableIds.toArray());
                if (selectedIds.contains(section.getEntries().get(i).getId())) {
                    assertThat(section.getEntries().get(i).getIsSelected()).isTrue();
                } else {
                    assertThat(section.getEntries().get(i).getIsSelected()).isFalse();
                }
            }
        } else {
            assertThat(filter.getValues()).isNull();
        }
    }

    private void assertSizeFilter(ProductFilter productFilter, boolean withValues) {
        assertThat(productFilter.getType()).isEqualTo(SizeFilter.TYPE);
        assertThat(productFilter.getCode()).isEqualTo(SizeFilterProcessor.FILTER_CODE);
        assertThat(productFilter.getName()).isEqualTo(
                messageSourceAccessor.getMessage(SizeFilterProcessor.FILTER_NAME));
        assertThat(productFilter).isInstanceOf(SizeFilter.class);

        SizeFilter filter = (SizeFilter) productFilter;
        assertThat(filter.getSelectedValues()).hasSize(2);
        assertThat(filter.getSelectedValues().get(0).getId()).isIn(397L, 398L);
        assertThat(filter.getSelectedValues().get(1).getId()).isIn(397L, 398L);

        assertThat(filter.getHasMoreValues()).isTrue();

        if (withValues) {
            assertThat(filter.getValues()).hasSize(1);
            CategorySize jeansSize = filter.getValues().get(0);
            assertThat(jeansSize.getSelectedValues()).hasSize(2);
            assertThat(jeansSize.getSelectedValues().get(0).getId()).isIn(397L, 398L);
            assertThat(jeansSize.getSelectedValues().get(1).getId()).isIn(397L, 398L);
            assertThat(jeansSize.getSizesValue()).isNotNull();
            assertThat(jeansSize.getSizesValue().getCode()).isEqualTo(
                    messageSourceAccessor.getMessage(SizeType.EU.getAbbreviation()));
            assertThat(jeansSize.getSizesValue().getValues()).hasSize(3);
            assertThat(jeansSize.getSizesValue().getValues().get(0).getCharts()).hasSizeGreaterThan(0);
        } else {
            assertThat(filter.getValues()).isNull();
        }
    }

    private void assertBrandFilter(ProductFilter productFilter, boolean withValues) {
        assertThat(productFilter.getType()).isEqualTo(MultiListFilter.TYPE);
        assertThat(productFilter.getCode()).isEqualTo(BrandFilterProcessor.FILTER_CODE);
        assertThat(productFilter.getName()).isEqualTo(
                messageSourceAccessor.getMessage(BrandFilterProcessor.FILTER_NAME));
        assertThat(productFilter).isInstanceOf(MultiListFilter.class);
        MultiListFilter filter = (MultiListFilter) productFilter;
        assertThat(filter.getSelectedValues()).hasSize(3);
        assertThat(filter.getSelectedValues().get(0).getId()).isIn(504L, 675L, 1860L);
        assertThat(filter.getSelectedValues().get(1).getId()).isIn(504L, 675L, 1860L);
        assertThat(filter.getSelectedValues().get(2).getId()).isIn(504L, 675L, 1860L);

        assertThat(filter.getHotValues()).hasSize(3);
        assertThat(filter.getHotValues().get(0).getId()).isIn(504L);
        assertThat(filter.getHotValues().get(0).getValue()).isEqualTo("DOLCE&GABBANA");
        assertThat(filter.getHotValues().get(0).getIsSelected()).isTrue();

        assertThat(filter.getSearchableSections()).containsOnly("Все бренды");
        assertThat(filter.getHasMoreValues()).isTrue();

        if (withValues) {
            assertThat(filter.getValues()).hasSize(2);
            ListSection topSection = filter.getValues().get(0);
            assertThat(topSection.getName()).isEqualTo("Популярные");
            assertThat(topSection.getEntries()).hasSize(3);

            ListSection allSection = filter.getValues().get(1);
            assertThat(allSection.getName()).isEqualTo("Все бренды");
            assertThat(allSection.getEntries()).hasSize(4);

            assertThat(allSection.getEntries().get(0).getId()).isIn(504L);
            assertThat(allSection.getEntries().get(0).getValue()).isEqualTo("DOLCE&GABBANA");
            assertThat(allSection.getEntries().get(0).getIsSelected()).isTrue();
            assertThat(allSection.getEntries().get(0).getImage()).isNull();
        } else {
            assertThat(filter.getValues()).isNull();
        }
    }

    private void assertCategoryFilter(ProductFilter productFilter, boolean withValues) {
        assertThat(productFilter.getType()).isEqualTo(CategoryTreeFilter.TYPE);
        assertThat(productFilter.getCode()).isEqualTo(CategoryFilterProcessor.FILTER_CODE);
        assertThat(productFilter.getName()).isEqualTo(
                messageSourceAccessor.getMessage(CategoryFilterProcessor.FILTER_NAME));
        assertThat(productFilter).isInstanceOf(CategoryTreeFilter.class);
        CategoryTreeFilter filter = (CategoryTreeFilter) productFilter;
        assertThat(filter.getSelectedValues()).hasSize(2);
        assertThat(filter.getSelectedValues().get(0).getId()).isIn(58L, 59L);
        assertThat(filter.getSelectedValues().get(1).getId()).isIn(58L, 59L);

        assertThat(filter.getHasMoreValues()).isTrue();

        if (withValues) {
            // вернется дерево начиная с категории Одежда, дальше Джинсы, потом уже типы джинс
            assertThat(filter.getValues()).hasSize(1);
            CategoryTreeValue clothesCategory = filter.getValues().get(0);
            assertThat(clothesCategory.getId()).isEqualTo(36L);
            assertThat(clothesCategory.getSelectedValues()).hasSize(2);
            assertThat(clothesCategory.getSelectedValues().get(0).getId()).isIn(58L, 59L);
            assertThat(clothesCategory.getSelectedValues().get(1).getId()).isIn(58L, 59L);
            assertThat(clothesCategory.getIsSelected()).isFalse();
            assertThat(clothesCategory.getChildren()).hasSize(1);

            CategoryTreeValue jeansCategory = clothesCategory.getChildren().get(0);
            assertThat(jeansCategory.getId()).isEqualTo(56L);
            assertThat(jeansCategory.getSelectedValues()).hasSize(2);
            assertThat(jeansCategory.getSelectedValues().get(0).getId()).isIn(58L, 59L);
            assertThat(jeansCategory.getSelectedValues().get(1).getId()).isIn(58L, 59L);
            assertThat(jeansCategory.getIsSelected()).isFalse();
            assertThat(jeansCategory.getChildren()).hasSize(2);

            assertThat(jeansCategory.getChildren().get(0).getId()).isIn(58L, 59L);
            assertThat(jeansCategory.getChildren().get(1).getId()).isIn(58L, 59L);
            assertThat(jeansCategory.getChildren().get(0).getIsSelected()).isTrue();
            assertThat(jeansCategory.getChildren().get(1).getIsSelected()).isTrue();

        } else {
            assertThat(filter.getValues()).isNull();
        }

    }

    private Map<String, JsonNode> allFiltersJson() {
        Map<String, JsonNode> filterJson = new HashMap<>();
        filterJson.put(BrandFilterProcessor.FILTER_CODE, idListNode(504L, 675L, 1860L));
        filterJson.put(CategoryFilterProcessor.FILTER_CODE, idListNode(58L, 59L));
        filterJson.put("attribute_10", idListNode(131L, 136L, 164L));
        filterJson.put("attribute_13", idListNode(186L));
        filterJson.put(ConditionFilterProcessor.FILTER_CODE, idListNode(1L, 2L, 3L));
        filterJson.put(ModelFilterProcessor.FILTER_CODE, idListNode(1L, 644L, 3L));
        filterJson.put(SizeFilterProcessor.FILTER_CODE, idListNode(397L, 398L));
        filterJson.put(SellerTypeFilterProcessor.FILTER_CODE, idListNode(1L));
        filterJson.put(WithTagFilterProcessor.FILTER_CODE, booleanNode(true));
        filterJson.put(InStockFilterProcessor.FILTER_CODE, booleanNode(true));
        filterJson.put(ExclusiveSelectionFilterProcessor.FILTER_CODE, booleanNode(true));
        filterJson.put(NewCollectionFilterProcessor.FILTER_CODE, booleanNode(true));
        filterJson.put(OskellyChoiceFilterProcessor.FILTER_CODE, booleanNode(true));
        filterJson.put(SaleFilterProcessor.FILTER_CODE, booleanNode(true));
        filterJson.put(StreetwearFilterProcessor.FILTER_CODE, booleanNode(true));
        filterJson.put(VintageFilterProcessor.FILTER_CODE, booleanNode(true));
        filterJson.put(PriceFilterProcessor.FILTER_CODE,
                priceNode(BigDecimal.valueOf(5000), BigDecimal.valueOf(20000)));
        filterJson.put(BoutiqueLocationTagFilterProcessor.FILTER_CODE, idListNode(2L));
        filterJson.put(LocationTagFilterProcessor.FILTER_CODE, idListNode(1L));
        filterJson.put(ExclusiveLotFilterProcessor.FILTER_CODE, booleanNode(true));
        return filterJson;
    }

    private JsonNode idListNode(Long... ids) {
        ObjectMapper objectMapper = new ObjectMapper();
        ArrayNode arrayNode = objectMapper.createArrayNode();
        Arrays.stream(ids).forEachOrdered(arrayNode::add);
        return arrayNode;
    }

    private JsonNode booleanNode(boolean value) {
        return BooleanNode.valueOf(value);
    }

    private JsonNode priceNode(BigDecimal lower, BigDecimal upper) {
        ObjectMapper objectMapper = new ObjectMapper();
        ObjectNode priceNode = objectMapper.createObjectNode();
        if (lower != null) {
            priceNode.put(PriceFilterProcessor.LOWER_PROPERTY, lower);
        }
        if (upper != null) {
            priceNode.put(PriceFilterProcessor.UPPER_PROPERTY, upper);
        }
        return priceNode;
    }

    @Test
    public void testClientContextProcessing() {

        ProductFilterInfoRequest request = new ProductFilterInfoRequest();
        request.setBaseCategory(2L);
        request.setCurrencyCode("RUR");
        request.setFilters(allFiltersJson());

        ProductFiltrationContext filtrationContext = new ProductFiltrationContext();
        productFilterTransformationService.fillFiltrationContext(filtrationContext, request);

        assertThat(filtrationContext.getIgnoredFilterTypes()).containsOnly(LocationTagFilterProcessor.TYPE,
                NewResaleFilterProcessor.TYPE);
        assertThat(filtrationContext.getIgnoredFilterTypes()).doesNotContain(BoutiqueLocationTagFilterProcessor.TYPE);

        request.setContexts(ImmutableList.of(ACCOUNT_PUBLISHED_OFFLINE_PRODUCTS));

        filtrationContext = new ProductFiltrationContext();
        productFilterTransformationService.fillFiltrationContext(filtrationContext, request);

        assertThat(filtrationContext.getIgnoredFilterTypes()).doesNotContain(LocationTagFilterProcessor.TYPE);
        assertThat(filtrationContext.getIgnoredFilterTypes()).containsOnly(BoutiqueLocationTagFilterProcessor.TYPE,
                NewResaleFilterProcessor.TYPE);
    }

    @Test
    @Transactional
    public void testGetExclusiveLotItems() {

        when(loyaltyServicePropertiesMock.isExclusiveLotsPrivilegeEnabled()).thenReturn(true);

        ProductFilterItemsRequest request = new ProductFilterItemsRequest();
        request.setBaseCategory(2L);
        request.setCurrencyCode("RUB");

        List<ProductDTO> products = productFilterProcessorEngine.getProductItems(request).getItems();

        // убедимся, что продуктов изначально достаточно для дальнейшей работы
        assertTrue(products.size() >= 3);

        ProductDTO exclusiveLot1 = products.get(0);
        long exclusiveLot1Id = exclusiveLot1.getProductId();
        User exclusiveLot1Seller = userRepository.findById(exclusiveLot1.getSeller().getId()).get();
        setExclusiveLot(exclusiveLot1Id);

        long exclusiveLot2Id = products.get(1).getProductId();
        setExclusiveLot(exclusiveLot2Id);

        long notExclusiveLotId = products.get(2).getProductId();
        unsetExclusiveLot(notExclusiveLotId);

        User user = userRepository.save(prepareUser(UUID.randomUUID() + "@email.com"));

        when(securityService.getCurrentAuthorizedUser()).thenReturn(exclusiveLot1Seller);
        when(securityService.getTokenToUserId()).thenReturn(ImmutableList.of(Pair.of("token", exclusiveLot1Seller.getId())));

        // пользователь без статуса black, но автор одного из продуктов, без фильтра по ЭЛ

        when(loyaltyServiceMock.hasUserBlackLoyaltyStatus(anyLong())).thenReturn(false);

        request.setFilters(Collections.emptyMap());
        Set<Long> productIds = productFilterProcessorEngine.getProductItems(request).getItems().stream()
                .map(ProductDTO::getProductId)
                .collect(Collectors.toSet());
        assertTrue(productIds.contains(exclusiveLot1Id));
        assertFalse(productIds.contains(exclusiveLot2Id));
        assertTrue(productIds.contains(notExclusiveLotId));

        // пользователь без статуса black, но автор одного из продуктов, с фильтром по ЭЛ

        request.setFilters(
                ImmutableMap.of(ExclusiveLotFilterProcessor.FILTER_CODE, booleanNode(true)));
        productIds = productFilterProcessorEngine.getProductItems(request).getItems().stream()
                .map(ProductDTO::getProductId)
                .collect(Collectors.toSet());
        assertTrue(productIds.contains(exclusiveLot1Id));
        assertFalse(productIds.contains(exclusiveLot2Id));
        assertTrue(productIds.contains(notExclusiveLotId));

        // пользователь без статуса black, без фильтра по ЭЛ

        when(securityService.getCurrentAuthorizedUser()).thenReturn(user);
        when(securityService.getTokenToUserId()).thenReturn(ImmutableList.of(Pair.of("token", user.getId())));

        when(loyaltyServiceMock.hasUserBlackLoyaltyStatus(anyLong())).thenReturn(false);

        request.setFilters(Collections.emptyMap());
        productIds = productFilterProcessorEngine.getProductItems(request).getItems().stream()
                .map(ProductDTO::getProductId)
                .collect(Collectors.toSet());
        assertFalse(productIds.contains(exclusiveLot1Id));
        assertFalse(productIds.contains(exclusiveLot2Id));
        assertTrue(productIds.contains(notExclusiveLotId));

        // пользователь без статуса black, с фильтром по ЭЛ

        request.setFilters(
                ImmutableMap.of(ExclusiveLotFilterProcessor.FILTER_CODE, booleanNode(true)));
        productIds = productFilterProcessorEngine.getProductItems(request).getItems().stream()
                .map(ProductDTO::getProductId)
                .collect(Collectors.toSet());
        assertFalse(productIds.contains(exclusiveLot1Id));
        assertFalse(productIds.contains(exclusiveLot2Id));
        assertTrue(productIds.contains(notExclusiveLotId));

        // пользователь со статусом black, без фильтра по ЭЛ

        when(loyaltyServiceMock.hasUserBlackLoyaltyStatus(anyLong())).thenReturn(true);

        request.setFilters(Collections.emptyMap());
        productIds = productFilterProcessorEngine.getProductItems(request).getItems().stream()
                .map(ProductDTO::getProductId)
                .collect(Collectors.toSet());
        assertTrue(productIds.contains(exclusiveLot1Id));
        assertTrue(productIds.contains(exclusiveLot2Id));
        assertTrue(productIds.contains(notExclusiveLotId));

        // пользователь со статусом black, с фильтром по ЭЛ

        request.setFilters(
                ImmutableMap.of(ExclusiveLotFilterProcessor.FILTER_CODE, booleanNode(true)));
        productIds = productFilterProcessorEngine.getProductItems(request).getItems().stream()
                .map(ProductDTO::getProductId)
                .collect(Collectors.toSet());
        assertTrue(productIds.contains(exclusiveLot1Id));
        assertTrue(productIds.contains(exclusiveLot2Id));
        assertFalse(productIds.contains(notExclusiveLotId));
    }

    private User prepareUser(String email) {
        return new User()
                .setChangeTime(Utils.nowAtUTC())
                .setEmail(email)
                .setNickname(RandomStringUtils.randomAlphabetic(5))
                .setRegistrationTime(ZonedDateTime.now())
                .setSellerType(SellerType.INDIVIDUAL)
                .setUserType(SIMPLE_USER)
                .setChatToken(UUID.randomUUID().toString())
                .setPhone(RandomStringUtils.randomNumeric(10))
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
    }

    private void setExclusiveLot(long productId) {
        Product product = productRepository.findById(productId).get();
        productRepository.save(product.setExclusiveLotTime(ZonedDateTime.now()));
    }

    private void unsetExclusiveLot(long productId) {
        Product product = productRepository.findById(productId).get();
        productRepository.save(product.setExclusiveLotTime(null));
    }
}

