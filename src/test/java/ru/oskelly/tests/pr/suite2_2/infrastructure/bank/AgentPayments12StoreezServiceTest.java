package ru.oskelly.tests.pr.suite2_2.infrastructure.bank;


import com.google.common.collect.Lists;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.MockPublisherConfiguration;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.BrandRepository;
import su.reddot.domain.dao.SizeRepository;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.agentreport.AgentReportRepository;
import su.reddot.domain.dao.agentreportpayments.AgentReportPaymentsRepository;
import su.reddot.domain.dao.counterparty.CounterpartyRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.product.ProductItemRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.agentreport.AgentReport;
import su.reddot.domain.model.agentreport.AgentReportPayments;
import su.reddot.domain.model.agentreport.AgentReportPaymentsId;
import su.reddot.domain.model.agentreport.AgentReportState;
import su.reddot.domain.model.banktransaction.TransactionState;
import su.reddot.domain.model.counterparty.CardCounterparty;
import su.reddot.domain.model.counterparty.Counterparty;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.order.OrderPositionState;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.infrastructure.bank.payout.AgentPayments12StoreezTransferService;
import su.reddot.infrastructure.configparam.ConfigParamService;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.mindbox.twelveStoreez.Mindbox12StoreezService;
import su.reddot.infrastructure.mindbox.twelveStoreez.exception.AddBonus12StoreezFailedException;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {OskellyApplication.class, MockPublisherConfiguration.class}, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@ActiveProfiles(profiles = AbstractSpringTest.testProfiles)
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class AgentPayments12StoreezServiceTest {

    @Autowired
    UserRepository userRepository;
    @Autowired
    CommissionGridService commissionGridService;
    @Autowired
    OrderRepository orderRepository;
    @Autowired
    AgentReportRepository agentReportRepository;
    @Autowired
    BrandRepository brandRepository;
    @Autowired
    ProductRepository productRepository;
    @Autowired
    CounterpartyRepository<Counterparty> counterpartyRepository;
    @Autowired
    ProductItemRepository productItemRepository;
    @Autowired
    SizeRepository sizeRepository;
    @Autowired
    AgentReportPaymentsRepository agentReportPaymentsRepository;
    @Autowired
    AgentPayments12StoreezTransferService agentPayments12StoreezService;

    @MockBean
    ConfigParamService configParamService;

    private User currentSeller;
    private User currentBuyer;
    private Order currentOrder;
    private Product currentProduct;
    private Counterparty currentSellerCounterparty;
    private ProductItem currentProductItem;
    private AgentReportPayments currentPayment;

    @MockBean
    Mindbox12StoreezService mindbox12StoreezService;

    @BeforeEach
    @Transactional
    public void init() {
        User user = new User()
                .setNickname(RandomStringUtils.randomAlphabetic(5))
                .setUserType(User.UserType.SIMPLE_USER)
                .setChangeTime(LocalDateTime.now())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        currentSeller = userRepository.saveAndFlush(user);

        User buyer = new User()
                .setNickname(RandomStringUtils.randomAlphabetic(5))
                .setUserType(User.UserType.SIMPLE_USER)
                .setChangeTime(LocalDateTime.now())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        currentBuyer = userRepository.saveAndFlush(buyer);


        Product product = new Product();
        product.setBrand(brandRepository.getOne(1L));
        product.setCategoryId(2L);
        product.setSeller(currentSeller);
        product.setStartPrice(BigDecimal.valueOf(10000));
        product.setCurrentPrice(BigDecimal.valueOf(9000));
        product.setProductState(ProductState.PUBLISHED);
        product.setPublishTime(LocalDateTime.now().minusMonths(3).minusDays(1));
        currentProduct = productRepository.saveAndFlush(product);

        Counterparty counterparty = new CardCounterparty()
                .setUser(currentSeller)
                .setPaymentAccount(RandomStringUtils.randomNumeric(20))
                .setCreateTime(ZonedDateTime.now())
                .setChangeTime(ZonedDateTime.now());
        currentSellerCounterparty = counterpartyRepository.saveAndFlush(counterparty);

        Order order = new Order();
        order.setUuid(UUID.randomUUID());
        order.setState(OrderState.CREATED);
        order.setBuyer(buyer);
        order.setSellerCounterparty(counterparty);
        order.setAmount(BigDecimal.ONE);
        order.setEffectiveAmount(BigDecimal.valueOf(100));
        order.setPaymentVersion("tcb");

        ProductItem productItem = new ProductItem();
        productItem.setProduct(product);
        productItem.setSize(sizeRepository.getOne(1L));
        currentProductItem = productItemRepository.saveAndFlush(productItem);

        OrderPosition orderPosition = new OrderPosition();
        orderPosition.setState(OrderPositionState.VERIFICATION_OK);
        orderPosition.setIsEffective(true);
        orderPosition.setParticipatesInPayment(true);
        orderPosition.setStateTime(LocalDateTime.now());
        orderPosition.setProductItem(productItem);
        orderPosition.setAmount(BigDecimal.valueOf(9000));
        orderPosition.setItemSaleAmount(orderPosition.getAmount());
        BigDecimal amount = product.getCurrentPrice();
        orderPosition.setAmount(amount);
        orderPosition.setItemSaleAmount(orderPosition.getAmount());
        orderPosition.setCommission(BigDecimal.valueOf(15.0));

        order.setOrderPosition(orderPosition);

        currentOrder = orderRepository.saveAndFlush(order);

        AgentReport agentReport = new AgentReport();
        agentReport.setState(AgentReportState.PAYMENT_START);
        agentReport.setOrder(currentOrder);
        agentReport.setPaymentAmount(BigDecimal.valueOf(9000));
        agentReport.setIsConfirmed(false);
        agentReport.setUserType(User.UserType.SIMPLE_USER);
        agentReport.setCleaningAmount(BigDecimal.valueOf(500));
        agentReport.setDefectsDiscountAmount(BigDecimal.ZERO);
        agentReport.setCreateTime(ZonedDateTime.now());
        agentReport.setDateContract(ZonedDateTime.now());
        AgentReport currentAgentReport = agentReportRepository.saveAndFlush(agentReport);

        order.setAgentReport(currentAgentReport);
        orderRepository.saveAndFlush(order);

        AgentReportPayments agentReportPayments = new AgentReportPayments()
                .setAgentReportPaymentsId(new AgentReportPaymentsId(agentReport.getId(), "12Storeez"))
                .setPhone("88005553535")
                .setAgentReport(agentReport)
                .setAmount(BigDecimal.valueOf(1000))
                .setState(TransactionState.PREPARED);
        currentPayment = agentReportPaymentsRepository.saveAndFlush(agentReportPayments);

        agentReport.setAgentReportPayments(Lists.newArrayList(agentReportPayments));
        agentReportRepository.saveAndFlush(agentReport);

        given(configParamService.getValueAsDouble(eq(ConfigParamService.CONFIG_PARAM_12_STOREEZ_ADDITIONAL_BONUS_MODIFIER))).willReturn(1.1);
    }

    @AfterEach
    public void cleanup() {
        agentReportPaymentsRepository.deleteAllInBatch();
        List<AgentReport> agentReports = agentReportRepository.findConfirmedBySeller(currentSeller.getId());
        agentReports.forEach(agentReport -> agentReport.setAgentReportPayments(Collections.emptyList()));
        orderRepository.delete(currentOrder);
        counterpartyRepository.delete(currentSellerCounterparty);
        productItemRepository.delete(currentProductItem);
        productRepository.delete(currentProduct);
        userRepository.delete(currentSeller);
        userRepository.delete(currentBuyer);
    }

    @Test
    @Transactional
    public void transferMoneyToSellerSendsBonusesWithAdditionalAmount() {
        given(mindbox12StoreezService.addBonuses(any(), any(), any(), any())).willReturn("Test");

        agentPayments12StoreezService.transferMoneyToSeller(currentPayment.getAgentReport().getOrder().getId(), currentPayment.getAgentReportPaymentsId().getBank());

        Optional<AgentReportPayments> payments = agentReportPaymentsRepository.findById(currentPayment.getAgentReportPaymentsId());
        assertThat(payments).isPresent();
        assertThat(payments.get().getState()).isEqualByComparingTo(TransactionState.DONE);
    }

    @Test
    @Transactional
    public void transferMoneyToSellerSavesExternalIdOnError() {
        String externalId = "b7ebc883-2b2a-41df-be55-9a4880a2a955";
        given(mindbox12StoreezService.addBonuses(any(), any(), any(), any())).willThrow(new AddBonus12StoreezFailedException("test message", externalId));

        agentPayments12StoreezService.transferMoneyToSeller(currentPayment.getAgentReport().getOrder().getId(), currentPayment.getAgentReportPaymentsId().getBank());

        Optional<AgentReportPayments> payments = agentReportPaymentsRepository.findById(currentPayment.getAgentReportPaymentsId());
        assertThat(payments).isPresent();
        assertThat(payments.get().getState()).isEqualByComparingTo(TransactionState.PREPARED);
        assertThat(payments.get().getExternalId()).isEqualTo(externalId);
    }

    @Test
    @Transactional
    public void transferMoneyToSellerRoundsPayment() {
        String externalId = "b7ebc883-2b2a-41df-be55-9a4880a2a955";
        currentPayment.setAmount(BigDecimal.valueOf(5554));

        ArgumentCaptor<BigDecimal> captor = ArgumentCaptor.forClass(BigDecimal.class);

        given(mindbox12StoreezService.addBonuses(any(), any(), any(), any())).willReturn(externalId);

        agentPayments12StoreezService.transferMoneyToSeller(currentPayment.getAgentReport().getOrder().getId(), currentPayment.getAgentReportPaymentsId().getBank());

        verify(mindbox12StoreezService, times(1)).addBonuses(any(), any(), captor.capture(), any());

        assertThat(captor.getValue()).isEqualTo(BigDecimal.valueOf(6110));
    }
}
