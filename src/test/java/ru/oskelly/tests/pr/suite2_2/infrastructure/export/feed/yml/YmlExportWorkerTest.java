package ru.oskelly.tests.pr.suite2_2.infrastructure.export.feed.yml;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.test.util.ReflectionTestUtils;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.category.CategoryRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.Brand;
import su.reddot.domain.model.category.Category;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.size.Size;
import su.reddot.domain.model.size.SizeType;
import su.reddot.domain.service.staticresource.StaticResourceBalancer;
import su.reddot.infrastructure.export.feed.yml.YmlExportWorker;
import su.reddot.infrastructure.util.XmlUtils;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamWriter;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.mock;

@ExtendWith(MockitoExtension.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class YmlExportWorkerTest {
    public static final long PRODUCT_ID = 1L;
    public static final long CATEGORY_ID = 12L;
    private static final long PRODUCT_ITEM_ID = 3L;
    @TempDir
    File temporaryFolder;

    @Mock
    ProductRepository productRepository;
    @Mock
    CategoryRepository categoryRepository;

    StaticResourceBalancer staticResourceBalancer = new StaticResourceBalancer();

    YmlExportWorker ymlExportWorker;
    private File ymlFile;
    private DocumentBuilder builder;
    private XPathFactory xPathFactory;
    private XMLStreamWriter xmlWriterForFile;

    @BeforeEach
    public void init() throws IOException, XMLStreamException, NoSuchFieldException, ParserConfigurationException {
        builder = DocumentBuilderFactory.newInstance().newDocumentBuilder();
        xPathFactory = XPathFactory.newInstance();

        ReflectionTestUtils.setField(staticResourceBalancer, "imagesUrlPath", "/img/");
        ReflectionTestUtils.setField(staticResourceBalancer, "imagesPrefix", "https://test");
        ymlFile = new File(temporaryFolder, "yml.xml");
        xmlWriterForFile = XmlUtils.createXmlWriterForFile(ymlFile, true);
        ymlExportWorker = new YmlExportWorker(xmlWriterForFile, categoryRepository, productRepository, staticResourceBalancer, 500);
    }


    @Test
    public void exportCreateCorrectYmlFile() throws IOException, SAXException, XMLStreamException, XPathExpressionException {
        given(productRepository.getAvailableProductIds())
                .willReturn(Collections.singletonList(PRODUCT_ID));

        Product product = getProduct();
        given(productRepository.getAvailableProductsForYml(any()))
                .willReturn(Collections.singletonList(product));
        Category category = getCategory();
        given(categoryRepository.findAll()).willReturn(Collections.singletonList(category));


        try (YmlExportWorker ymlExportWorker = new YmlExportWorker(xmlWriterForFile, categoryRepository, productRepository, staticResourceBalancer, 500)) {
            ymlExportWorker.export();
        }

        Document document = builder.parse(ymlFile);
        XPath xPath = xPathFactory.newXPath();

        assertThat(xPath.evaluate("yml_catalog", document)).isNotNull();
        assertThat(xPath.evaluate("yml_catalog/@date", document)).isNotNull();

        assertThat(xPath.evaluate("yml_catalog/shop/name", document)).isEqualTo("OSKELLY.RU LUXURY RESALE STORE");
        assertThat(xPath.evaluate("yml_catalog/shop/url", document)).isEqualTo("https://oskelly.ru/");
        assertThat(xPath.evaluate("yml_catalog/shop/company", document)).isEqualTo("Oskelly Group, LLC");

        assertThat(xPath.evaluate("yml_catalog/shop/currencies/currency[1]/@id", document)).isEqualTo("RUB");
        assertThat(xPath.evaluate("yml_catalog/shop/currencies/currency[1]/@rate", document)).isEqualTo("1");
        assertThat(xPath.evaluate("yml_catalog/shop/currencies/currency[2]/@id", document)).isEqualTo("USD");
        assertThat(xPath.evaluate("yml_catalog/shop/currencies/currency[2]/@rate", document)).isEqualTo("CBRF");
        assertThat(xPath.evaluate("yml_catalog/shop/currencies/currency[3]/@id", document)).isEqualTo("EUR");
        assertThat(xPath.evaluate("yml_catalog/shop/currencies/currency[3]/@rate", document)).isEqualTo("CBRF");

        assertThat(xPath.evaluate("yml_catalog/shop/categories/category[1]/@id", document)).isEqualTo(String.valueOf(CATEGORY_ID));
        assertThat(xPath.evaluate("yml_catalog/shop/categories/category[1]", document)).isEqualTo("Test Category");

        assertThat(xPath.evaluate("yml_catalog/shop/offers/offer[1]/@id", document)).isEqualTo(String.valueOf(PRODUCT_ITEM_ID));
        assertThat(xPath.evaluate("yml_catalog/shop/offers/offer[1]/name", document)).isEqualTo("Test Thing Test Brand Test model");
        assertThat(xPath.evaluate("yml_catalog/shop/offers/offer[1]/vendor", document)).isEqualTo("Test Brand");
        assertThat(xPath.evaluate("yml_catalog/shop/offers/offer[1]/url", document)).isEqualTo("https://oskelly.ru/products/test-test-1");
        assertThat(xPath.evaluate("yml_catalog/shop/offers/offer[1]/price", document)).isEqualTo("100");
        assertThat(xPath.evaluate("yml_catalog/shop/offers/offer[1]/currencyId", document)).isEqualTo("RUR");
        assertThat(xPath.evaluate("yml_catalog/shop/offers/offer[1]/delivery", document)).isEqualTo("true");
        assertThat(xPath.evaluate("yml_catalog/shop/offers/offer[1]/categoryId", document)).isEqualTo(String.valueOf(CATEGORY_ID));
        assertThat(xPath.evaluate("yml_catalog/shop/offers/offer[1]/param/@name", document)).isEqualTo("Размер");
        assertThat(xPath.evaluate("yml_catalog/shop/offers/offer[1]/param/@unit", document)).isEqualTo("RU");
        assertThat(xPath.evaluate("yml_catalog/shop/offers/offer[1]/param", document)).isEqualTo("44");
        assertThat(xPath.evaluate("yml_catalog/shop/offers/offer[1]/picture", document)).isEqualTo("https://test/img/img-preview");
        assertThat(xPath.evaluate("yml_catalog/shop/offers/offer[1]/description", document)).isEqualTo("Test description");
    }

    private Product getProduct() {
        Product product = mock(Product.class);
        given(product.getUrl()).willReturn("/products/test-test-1");
        given(product.getCategoryId()).willReturn(CATEGORY_ID);
        Brand brand = mock(Brand.class);
        given(brand.getName()).willReturn("Test Brand");
        given(product.getNameWithBrand()).willReturn("Test Thing Test Brand");
        given(product.getBrand()).willReturn(brand);
        given(product.getModel()).willReturn("Test model");
        given(product.getSizeType()).willReturn(SizeType.RU);
        given(product.getCurrentPrice()).willReturn(BigDecimal.valueOf(100));
        ProductItem productItem = new ProductItem();
        productItem.setId(PRODUCT_ITEM_ID);
        Size size = new Size();
        size.setRussian("44");
        productItem.setSize(size);
        given(product.getProductItems()).willReturn(Collections.singletonList(productItem));
        given(product.getImagePreview()).willReturn("img-preview");
        given(product.getDescription()).willReturn("Test description");
        return product;
    }

    private Category getCategory() {
        Category category = new Category();
        category.setDisplayName("Test Category");
        category.setId(CATEGORY_ID);
        return category;
    }

}
