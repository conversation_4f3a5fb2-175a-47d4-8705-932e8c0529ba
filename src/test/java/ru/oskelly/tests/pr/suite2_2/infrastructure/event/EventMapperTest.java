package ru.oskelly.tests.pr.suite2_2.infrastructure.event;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.model.activity.order.AddToCartActivity;
import su.reddot.domain.model.device.Device;
import su.reddot.domain.model.device.DeviceDtype;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.service.activity.ActivityService;
import su.reddot.domain.service.cart.ChangeCartEvent;
import su.reddot.domain.service.device.DeviceService;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.event.EventMapper;

import java.math.BigDecimal;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.BDDMockito.given;

@ActiveProfiles(AbstractSpringTest.testProfiles)
@SpringBootTest(classes = OskellyApplication.class)
@ExtendWith(SpringExtension.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class EventMapperTest {

    @Autowired
    EventMapper eventMapper;

    @MockBean
    DeviceService deviceService;
    @MockBean
    ActivityService activityService;


    @Test
    public void createChangeCartEventForAnonymousUserWithoutCurrentDeviceMindboxUUIDCreatesChangeCartEvent() {

        given(deviceService.getCurrentDeviceInfo())
                .willReturn(new DeviceService.DeviceInfo());


        AddToCartActivity activity = new AddToCartActivity();
        activity.setDevice(new Device().setDtype(DeviceDtype.AppleDevice)
                .setMindboxClientUuid("mindox-uuid")
                .setRemoteAddr("localhost")
                .setUserAgent("test-user-agent"));

        given(activityService.getLastActivity(isNull(), eq("guest-token"), eq(AddToCartActivity.class)))
                .willReturn(activity);

        Order order = new Order();

        OrderPosition orderPosition = new OrderPosition();
        orderPosition.setId(0L);
        ProductItem productItem = new ProductItem();
        productItem.setId(1L);
        Product product = new Product();
        product.setCurrentPrice(BigDecimal.valueOf(100));
        product.setId(2L);
        productItem.setProduct(product);
        orderPosition.setProductItem(productItem);

        order.setOrderPosition(orderPosition);
        order.setGuestToken("guest-token");

        Optional<ChangeCartEvent> changeCartEventOptional = eventMapper.getChangeCartEvent(order);
        assertThat(changeCartEventOptional).isPresent();
        ChangeCartEvent changeCartEvent = changeCartEventOptional.get();
        assertThat(changeCartEvent.getOrderPositionDtos()).hasSize(1);
        ChangeCartEvent.OrderPositionDto orderPositionOfEvent = changeCartEvent.getOrderPositionDtos().get(0);
        assertThat(orderPositionOfEvent.getProductItemId()).isEqualTo(1L);
        assertThat(orderPositionOfEvent.getCount()).isEqualTo(1);
        assertThat(orderPositionOfEvent.getItemPrice()).isEqualByComparingTo(100.0);
    }

    @Test
    public void createChangeWishlistEventCreateWishlistEventWithRoundedPrice() {

        given(deviceService.getCurrentDeviceInfo())
                .willReturn(new DeviceService.DeviceInfo());


        AddToCartActivity activity = new AddToCartActivity();
        activity.setDevice(new Device().setDtype(DeviceDtype.AppleDevice)
                .setMindboxClientUuid("mindox-uuid")
                .setRemoteAddr("localhost")
                .setUserAgent("test-user-agent"));

        given(activityService.getLastActivity(isNull(), eq("guest-token"), eq(AddToCartActivity.class)))
                .willReturn(activity);

        Order order = new Order();

        OrderPosition orderPosition = new OrderPosition();
        orderPosition.setId(0L);
        ProductItem productItem = new ProductItem();
        productItem.setId(1L);
        Product product = new Product();
        product.setCurrentPrice(BigDecimal.valueOf(100.125456));
        product.setId(2L);
        productItem.setProduct(product);
        orderPosition.setProductItem(productItem);

        order.setOrderPosition(orderPosition);
        order.setGuestToken("guest-token");

        Optional<ChangeCartEvent> changeCartEventOptional = eventMapper.getChangeCartEvent(order);
        assertThat(changeCartEventOptional).isPresent();
        ChangeCartEvent changeCartEvent = changeCartEventOptional.get();
        assertThat(changeCartEvent.getOrderPositionDtos()).hasSize(1);
        ChangeCartEvent.OrderPositionDto orderPositionOfEvent = changeCartEvent.getOrderPositionDtos().get(0);
        assertThat(orderPositionOfEvent.getItemPrice()).isEqualByComparingTo(100.13);
    }
}
