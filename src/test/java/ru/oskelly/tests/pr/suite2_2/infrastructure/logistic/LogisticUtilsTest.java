package ru.oskelly.tests.pr.suite2_2.infrastructure.logistic;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.time.LocalDateTime;

import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.model.logistic.Waybill;
import su.reddot.infrastructure.logistic.LogisticUtils;

import org.junit.jupiter.api.Test;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class LogisticUtilsTest {

    private final LogisticUtils logisticUtils = new LogisticUtils();

    @Test
    public void testIsMoscow() {
        assertTrue(logisticUtils.isMoscow("", "Москва"));
        assertTrue(logisticUtils.isMoscow("", "МОСКВА "));
        assertTrue(logisticUtils.isMoscow("", "г. Москва"));
        assertTrue(logisticUtils.isMoscow("Московская область", ""));
        assertTrue(logisticUtils.isMoscow(" МОСКОВСКАЯ область", ""));
        assertFalse(logisticUtils.isMoscow(null, null));
        assertFalse(logisticUtils.isMoscow("", ""));
        assertFalse(logisticUtils.isMoscow("Ленинградская область", ""));
    }

    @Test
    public void testIsSaintPetersburg() {
        assertTrue(logisticUtils.isSaintPetersburg("", "Санкт-Петербург"));
        assertTrue(logisticUtils.isSaintPetersburg("", "САНКТ-ПЕТЕРБУРГ "));
        assertTrue(logisticUtils.isSaintPetersburg("", " город Санкт-Петербург"));
        assertTrue(logisticUtils.isSaintPetersburg("Ленинградская область", ""));
        assertTrue(logisticUtils.isSaintPetersburg(" ЛЕНИНГРАДСКАЯ область", ""));
        assertFalse(logisticUtils.isSaintPetersburg(null, null));
        assertFalse(logisticUtils.isSaintPetersburg("", ""));
        assertFalse(logisticUtils.isSaintPetersburg("Московская область", ""));
    }

    /**
     * Test {@link LogisticUtils#isNewTrackingHistoryRecord} method.
     */
    @Test
    public void testIsNewTrackingHistoryRecord() {
        final Waybill waybill = mock(Waybill.class);
        when(waybill.getTrackingTime()).thenReturn(createTime(20));

        assertFalse(LogisticUtils.isNewTrackingHistoryRecord(null, waybill));
        assertFalse(LogisticUtils.isNewTrackingHistoryRecord(createTime(10), waybill));
        assertFalse(LogisticUtils.isNewTrackingHistoryRecord(createTime(20), waybill));
        assertTrue(LogisticUtils.isNewTrackingHistoryRecord(createTime(30), waybill));
    }

    private static LocalDateTime createTime(int minute) {
        return LocalDateTime.of(2022, 10, 29, 10, minute, 0);
    }
}
