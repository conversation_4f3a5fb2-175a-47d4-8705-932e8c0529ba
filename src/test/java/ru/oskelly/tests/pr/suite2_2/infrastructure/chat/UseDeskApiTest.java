package ru.oskelly.tests.pr.suite2_2.infrastructure.chat;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;

import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.infrastructure.chat.usedesk.api.UseDeskApi;

import org.junit.jupiter.api.Test;
import org.springframework.web.client.RestTemplate;

/**
 * Unit tests for {@link UseDeskApi}.
 */
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class UseDeskApiTest {

    /**
     * Test {@link UseDeskApi#getPhoneNumberInUsedeskFormat(String)} method.
     */
    @Test
    public void testGetPhoneNumberInUseDeskFormat() {
        UseDeskApi useDeskApi = new UseDeskApi("", "", mock(RestTemplate.class));
        String phoneNumberInUseDeskFormat = useDeskApi.getPhoneNumberInUsedeskFormat("+7 (999) 000-00-00");
        assertEquals("7(999)0000000", phoneNumberInUseDeskFormat);
    }
}
