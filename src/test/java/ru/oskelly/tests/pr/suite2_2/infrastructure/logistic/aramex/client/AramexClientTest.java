package ru.oskelly.tests.pr.suite2_2.infrastructure.logistic.aramex.client;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import ru.oskelly.tests.build.presentation.pdf.PdfTestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.infrastructure.logistic.aramex.AramexConfiguration;
import su.reddot.infrastructure.logistic.aramex.AramexPickupTimeCalculator;
import su.reddot.infrastructure.logistic.aramex.AramexPickupTimeFrame;
import su.reddot.infrastructure.logistic.aramex.client.AramexAddress;
import su.reddot.infrastructure.logistic.aramex.client.AramexAttachment;
import su.reddot.infrastructure.logistic.aramex.client.AramexClientException;
import su.reddot.infrastructure.logistic.aramex.client.AramexCountry;
import su.reddot.infrastructure.logistic.aramex.client.AramexLocationClient;
import su.reddot.infrastructure.logistic.aramex.client.AramexMoney;
import su.reddot.infrastructure.logistic.aramex.client.AramexPickupRequest;
import su.reddot.infrastructure.logistic.aramex.client.AramexPickupResponse;
import su.reddot.infrastructure.logistic.aramex.client.AramexPickupTrackingResponse;
import su.reddot.infrastructure.logistic.aramex.client.AramexShipmentRequest;
import su.reddot.infrastructure.logistic.aramex.client.AramexShipmentRequest.Item;
import su.reddot.infrastructure.logistic.aramex.client.AramexShipmentsTrackingResponse;
import su.reddot.infrastructure.logistic.aramex.client.AramexShippingClient;
import su.reddot.infrastructure.logistic.aramex.client.AramexState;
import su.reddot.infrastructure.logistic.aramex.client.AramexTrackingClient;
import su.reddot.infrastructure.logistic.aramex.client.AramexWaybill;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

@Slf4j
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
@Disabled("DEVALAN-1650")
public abstract class AramexClientTest {

    private static final long ORDER_ID = 11111L;
    private static final String LONG_DESCRIPTION =
            "Very loooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooong description";

    private static final AramexAddress SELLER_ADDRESS = AramexAddress.builder()
            .line1("JJGC4966, Ghulail District")
            .city("Jeddah")
            .countryCode("SA")
            .personName("Alice")
            .companyName("Seller person")
            .phoneNumber("+7 (999) 000-00-01")
            .pickupLocation("Some location")
            .build();

    private static final AramexAddress OFFICE_ADDRESS = AramexAddress.builder()
            .line1("DUBAI COMMERCITY LLC of PO Box 491, BCB2-215")
            .city("Dubai")
            .countryCode("AE")
            .personName("OSKELLY representative")
            .companyName("OSKELLY")
            .phoneNumber("+7 (999) 000-00-02")
            .pickupLocation("Some location")
            .build();

    private static final AramexAddress BUYER_ADDRESS = AramexAddress.builder()
            .line1("Office # 1010, 10th Floor, Block A, Building # 64, Al Razi Building, Oud Metha Road, Dubai Healthcare City")
            .city("Dubai")
            .countryCode("AE")
            .personName("Bob")
            .companyName("Buyer person")
            .phoneNumber("+7 (999) 000-00-03")
            .pickupLocation("")
            .build();

    private AramexShippingClient shippingClient;
    private AramexTrackingClient trackingClient;
    private AramexLocationClient locationClient;
    private AramexPickupTimeCalculator pickupTimeCalculator;

    @BeforeEach
    public void setUp() {
        final AramexConfiguration configuration = new AramexConfiguration();
        configuration.setShippingEndpoint("https://ws.aramex.net/shippingapi.v2/shipping/service_1_0.svc");
        configuration.setTrackingEndpoint("https://ws.aramex.net/shippingapi.v2/tracking/service_1_0.svc");
        configuration.setLocationEndpoint("https://ws.aramex.net/shippingapi.v2/location/service_1_0.svc");
        configuration.setUsername("<EMAIL>");
        configuration.setPassword("Ar@m3x$h1pp1ng");
        configuration.setAccountEntity("DXB");
        configuration.setAccountNumber("45796");
        configuration.setAccountPin("116216");
        configuration.setAccountCountryCode("AE");
        configuration.setVersion("v1.0");
        configuration.setZone("Asia/Dubai");
        shippingClient = createShippingClient(configuration);
        trackingClient = createTrackingClient(configuration);
        locationClient = createLocationClient(configuration);
        pickupTimeCalculator = new AramexPickupTimeCalculator(configuration);
    }

    @NonNull
    protected abstract AramexShippingClient createShippingClient(@NonNull AramexConfiguration configuration);

    @NonNull
    protected abstract AramexTrackingClient createTrackingClient(@NonNull AramexConfiguration configuration);

    @NonNull
    protected abstract AramexLocationClient createLocationClient(@NonNull AramexConfiguration configuration);

    /**
     * Test delivery from seller to office flow.
     */
    @Test
    public void testDeliveryFromSellerFlow() throws AramexClientException {
        testCreatePickupAndShipment(true);
    }

    /**
     * Test delivery from office to buyer flow.
     */
    @Test
    public void testDeliveryToBuyerFlow() throws AramexClientException {
        testCreatePickupAndShipment(false);
    }

    /**
     * Test creating pickup and shipment for either seller-to-office or office-to-buyer flow.
     * 1. Create an empty pickup.
     * 2. Track pickup to make sure it has been created successfully.
     * 3. Create a new shipment and link it to the created pickup.
     * 4. Track shipment.
     * 5. Get label PDF document.
     * 6. Cancel pickup (for cleanup purposes).
     */
    private void testCreatePickupAndShipment(boolean isFromSellerToOffice) throws AramexClientException {
        // Create an empty pickup
        final AramexPickupTimeFrame pickupTimeFrame = pickupTimeCalculator.getPickupTimeFrame(isFromSellerToOffice);
        final AramexPickupResponse response = shippingClient.createPickup(AramexPickupRequest.builder()
                .address(isFromSellerToOffice ? SELLER_ADDRESS : OFFICE_ADDRESS)
                .fromSellerToOffice(isFromSellerToOffice)
                .date(pickupTimeFrame.getDate())
                .fromTime(pickupTimeFrame.getFromTime())
                .toTime(pickupTimeFrame.getToTime())
                .closingTime(pickupTimeFrame.getToTime().plusHours(2))
                .build());

        final String pickupId = response.getId();
        final String pickupGuid = response.getGuid();

        log.info("Created test pickup: ID = {}, GUID = {}", pickupId, pickupGuid);

        // Track pickup
        trackPickup(pickupId);

        // Create a shipment and attach it to the pickup
        final String shipmentNumber = shippingClient.createShipment(AramexShipmentRequest.builder()
                .orderId(ORDER_ID)
                .pickupAddress(isFromSellerToOffice ? SELLER_ADDRESS : OFFICE_ADDRESS)
                .deliveryAddress(isFromSellerToOffice ? OFFICE_ADDRESS : BUYER_ADDRESS)
                .fromSellerToOffice(isFromSellerToOffice)
                .items(Arrays.asList(
                        AramexShipmentRequest.Item.builder()
                                .description("Shoes")
                                .weightKg(2)
                                .build(),
                        AramexShipmentRequest.Item.builder()
                                .description("Bag")
                                .weightKg(1)
                                .build(),
                        AramexShipmentRequest.Item.builder()
                                .description(LONG_DESCRIPTION)
                                .weightKg(1)
                                .build()))
                .pickupGuid(pickupGuid)
                .comment("User's comment (should appear in Remarks)")
                .customsValueAmount(AramexMoney.builder()
                        .value(10)
                        .currencyCode("AED")
                        .build())
                .attachment(AramexAttachment.builder()
                        .fileName("Doc")
                        .fileExtension("pdf")
                        .fileContents(new byte[] {0x25, 0x50, 0x44, 0x46, 0x25, 0x25, 0x45, 0x4F, 0x46})
                        .build())
                .build());

        log.info("Created test shipment: {}", shipmentNumber);

        // Track shipment
        trackShipment(shipmentNumber);

        // Get label PDF
        final byte[] labelPdfContents = shippingClient.getLabelPdf(shipmentNumber);
        log.info("Received label PDF of {} bytes size", labelPdfContents.length);
        PdfTestUtils.validatePdf(labelPdfContents);

        // Cancel pickup (cleanup)
        shippingClient.cancelPickup(pickupGuid);
        log.info("Test pickup {} has been canceled", pickupGuid);
    }

    /**
     * Test creating a shipment with missing weight (exception is expected).
     */
    @Test
    public void testCreateShipmentErrorMissingWeight() {
        AramexClientException exception = assertThrows(AramexClientException.class, () ->
            shippingClient.createShipment(AramexShipmentRequest.builder()
                    .orderId(ORDER_ID)
                    .pickupAddress(OFFICE_ADDRESS)
                    .deliveryAddress(BUYER_ADDRESS)
                    .fromSellerToOffice(false)
                    .items(Collections.singletonList(Item.builder()
                            .description("Shoes")
                            .build()))
                    .pickupGuid("G172592")
                    .build()));
        assertInstanceOf(AramexClientException.class, exception.getCause());
        assertEquals("ERR23: Details.ActualWeight.Value - Value is less than zero", exception.getMessage());
    }

    @Test
    @Disabled("Use this test to track some specific pickup")
    public void testPickupTracking() throws AramexClientException {
        trackPickup("G172592");
    }

    @Test
    @Disabled("Use this test to track some specific shipment")
    public void testShipmentTracking() throws AramexClientException {
        trackShipment("48415311420");
    }

    private void trackPickup(@NonNull final String pickupId) throws AramexClientException {
        final AramexPickupTrackingResponse trackingResponse = trackingClient.trackPickup(pickupId);
        log.info("Pickup status: {}", trackingResponse.getLastStatus());
    }

    private void trackShipment(@NonNull final String shipmentNumber) throws AramexClientException {
        final AramexShipmentsTrackingResponse trackingResponse = trackingClient.trackShipments(
                Collections.singletonList(shipmentNumber));
        assertTrue(trackingResponse.getNonExistingWaybills().isEmpty());
        assertEquals(1, trackingResponse.getExistingWaybills().size());
        final AramexWaybill waybill = trackingResponse.getExistingWaybills().get(0);
        log.info("Tracked Aramex waybill: " + waybill);
        assertEquals(shipmentNumber, waybill.getWaybillNumber());
    }

    @Test
    public void testFetchCountries() throws AramexClientException {
        final List<AramexCountry> countries = locationClient.fetchCountries();
        assertFalse(countries.isEmpty());
    }

    @Test
    public void testFetchStates() throws AramexClientException {
        final List<AramexState> states = locationClient.fetchStates("AE");
        assertFalse(states.isEmpty());
    }

    @Test
    public void testFetchCities() throws AramexClientException {
        final List<String> cities = locationClient.fetchCities("AE", null, null);
        assertFalse(cities.isEmpty());
    }

    @Test
    public void testFetchCitiesForState() throws AramexClientException {
        final List<String> cities = locationClient.fetchCities("AE", "Sharjah", "A");
        assertFalse(cities.isEmpty());
    }
}
