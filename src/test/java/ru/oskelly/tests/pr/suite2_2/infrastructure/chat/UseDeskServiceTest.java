package ru.oskelly.tests.pr.suite2_2.infrastructure.chat;

import io.micrometer.core.instrument.Counter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserAttributeValueRepository;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.bargain.BargainRepository;
import su.reddot.domain.service.adminpanel.tag.UserCommonTagService;
import su.reddot.domain.service.bargain.BargainConfiguration;
import su.reddot.domain.service.notification.NotificationService;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.product.ProductService;
import su.reddot.domain.service.setting.FeatureFlagsSettingService;
import su.reddot.domain.service.user.UserAttributeService;
import su.reddot.infrastructure.chat.UseDeskChannel;
import su.reddot.infrastructure.chat.UseDeskProperties;
import su.reddot.infrastructure.chat.usedesk.UseDeskService;
import su.reddot.infrastructure.chat.usedesk.api.UseDeskApi;
import su.reddot.infrastructure.chat.usedesk.api.request.CreateTicketRequest;
import su.reddot.infrastructure.chat.usedesk.api.response.CreateTicketResponse;
import su.reddot.infrastructure.configparam.ConfigParamService;
import su.reddot.infrastructure.util.CallInTransaction;

import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class UseDeskServiceTest {
    public static final String TEST_MESSAGE = "Test message";
    public static final String TEST_SUBJECT = "Test Subject";
    public static final String TEST_PHONE = "7987918046355";
    public static final String TEST_TAG = "test_tag";
    @Mock
    private UseDeskProperties useDeskProperties;
    @Mock
    private UserRepository userRepository;
    @Mock
    private UserAttributeValueRepository userAttributeValueRepository;
    @Mock
    private ApplicationEventPublisher publisher;
    @Mock
    private ProductService productService; // To fill user xtra info
    @Mock
    private OrderService orderService; // To fill user xtra info
    @Mock
    private BargainRepository bargainRepository; // To fill user xtra info
    @Mock
    private BargainConfiguration bargainConfiguration; // To fill user xtra info
    @Mock
    private RestTemplate restTemplate;
    @Mock
    private UseDeskApi useDeskApi;

    @Mock
    private UserAttributeService userAttributeService;

    private UseDeskService useDeskService;

    private static final String API_TOKEN = "test_token";
    private static final int WHATSAPP_CHANNEL_ID = 12345;
    private static final int USER_ID = 345;

    @BeforeEach
    public void init() throws NoSuchFieldException {
        Map<UseDeskChannel, Integer> useDeskChannels = new HashMap<>();
        useDeskChannels.put(UseDeskChannel.WHATSAPP, WHATSAPP_CHANNEL_ID);
        UseDeskProperties.NotificationsConfig notificationsConfig = new UseDeskProperties.NotificationsConfig();
        notificationsConfig.setUserId(USER_ID);
        notificationsConfig.setApiToken(API_TOKEN);
        notificationsConfig.setTags(TEST_TAG);

        when(useDeskProperties.getChannelIds()).thenReturn(useDeskChannels);
        when(useDeskProperties.getNotificationsConfig()).thenReturn(notificationsConfig);

        useDeskService = new UseDeskService(
                useDeskProperties,
                mock(ConfigParamService.class),
                mock(FeatureFlagsSettingService.class),
                userRepository,
                userAttributeValueRepository,
                publisher,
                productService,
                orderService,
                bargainRepository,
                bargainConfiguration,
                mock(NotificationService.class),
                mock(UserCommonTagService.class),
                restTemplate,
                mock(Counter.class),
                mock(Counter.class),
                userAttributeService,
                mock(MessageSourceAccessor.class),
                mock(CallInTransaction.class)
        );

        doReturn(getCreateTicketResponse()).when(useDeskApi).createTicket(any());
        ReflectionTestUtils.setField(useDeskService,"useDeskApi", useDeskApi);
    }

    @Test
    public void sendNotificationCallCreateTicket() {
        when(useDeskApi.getPhoneNumberInUsedeskFormat(any())).thenCallRealMethod();
        useDeskService.sendNotification(1L, UseDeskChannel.WHATSAPP, TEST_PHONE, TEST_SUBJECT, TEST_MESSAGE, TEST_TAG);
        ArgumentCaptor<CreateTicketRequest> requestArgumentCaptor = ArgumentCaptor.forClass(CreateTicketRequest.class);
        verify(useDeskApi, times(1)).createTicket(requestArgumentCaptor.capture());
        CreateTicketRequest expectedRequest = new CreateTicketRequest()
                .setApiToken(API_TOKEN)
                .setMessage(TEST_MESSAGE)
                .setSubject(TEST_SUBJECT)
                .setChannelId(WHATSAPP_CHANNEL_ID)
                .setFrom("user")
                .setTag(TEST_TAG)
                .setClientPhone(TEST_PHONE)
                .setUserId(USER_ID)
                .setClientId(1L);
        CreateTicketRequest request = requestArgumentCaptor.getValue();

        assertThat(request).isEqualTo(expectedRequest);
    }

    private CreateTicketResponse getCreateTicketResponse() {
        CreateTicketResponse createTicketResponse = new CreateTicketResponse();
        createTicketResponse.setStatus("success");
        createTicketResponse.setMessageStatus("delivered");
        createTicketResponse.setTicketId(12345678);
        return createTicketResponse;
    }
}
