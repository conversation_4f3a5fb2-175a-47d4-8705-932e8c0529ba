package ru.oskelly.tests.pr.suite2_2.infrastructure.logistic;

import java.util.List;

import com.google.common.collect.ImmutableList;

import lombok.NonNull;
import su.reddot.domain.service.integration.logistic.model.TimeIntervalDTO;
import su.reddot.infrastructure.logistic.TimeIntervalUtils;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * Tests for {@link TimeIntervalUtils}.
 */
public class TimeIntervalUtilsTest {

    @Test
    public void testGetClosestTimeIntervalId() {
        final List<TimeIntervalDTO> intervalList = ImmutableList.of(
                createTimeInterval(1, 8, 20),
                createTimeInterval(2, 7, 20),
                createTimeInterval(3, 9, 15),
                createTimeInterval(4, 9, 14),
                createTimeInterval(5, 13, 18));
        Assertions.assertEquals(4, TimeIntervalUtils.getClosestTimeIntervalId(1, intervalList));
        Assertions.assertEquals(5, TimeIntervalUtils.getClosestTimeIntervalId(2, intervalList));
        Assertions.assertEquals(1, TimeIntervalUtils.getClosestTimeIntervalId(3, intervalList));
    }

    @NonNull
    private static TimeIntervalDTO createTimeInterval(
            final long id,
            final int fromHour,
            final int toHour) {
        final TimeIntervalDTO timeInterval = new TimeIntervalDTO();
        timeInterval.setId(id);
        timeInterval.setFromHour(fromHour);
        timeInterval.setToHour(toHour);
        return timeInterval;
    }
}
