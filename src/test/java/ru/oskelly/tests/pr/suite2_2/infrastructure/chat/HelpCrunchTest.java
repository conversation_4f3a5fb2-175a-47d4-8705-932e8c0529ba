package ru.oskelly.tests.pr.suite2_2.infrastructure.chat;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.util.ReflectionTestUtils;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.bargain.BargainRepository;
import su.reddot.domain.model.bargain.BargainState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.bargain.BargainConfiguration;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.product.ProductService;
import su.reddot.domain.service.task.ScheduledRunner;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.chat.helpcrunch.HelpCrunchChatService;
import su.reddot.infrastructure.chat.helpcrunch.response.CustomerInfoResponse;
import su.reddot.infrastructure.util.Utils;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;
import static su.reddot.domain.model.user.User.UserType.SIMPLE_USER;

@Disabled
@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class HelpCrunchTest extends AbstractSpringTest {
    private User user;
    private Integer counteBySeller;
    private long countSalesBySeller;
    private long countBuyerOrders;
    //Выключаем старые офферы
    //private List<Offer> actualOutgoingOffers;
    private short countSyncUsers = 2;

    @Mock
    private ProductService productService;
    //Выключаем старые офферы
    /*@Mock
    private OfferService offerService;*/
    @Mock
    private OrderService orderService;

    @Mock
    private BargainConfiguration bargainConfiguration;

    @Mock
    private BargainRepository bargainRepository;

    @Autowired
    private ObjectMapper mapper;

    @Value("${helpcrunch.apiUrl}")
    private String apiUrl;

    @Value("${helpcrunch.apiToken}")
    private String apiToken;

    @InjectMocks
    private HelpCrunchChatService chatService;

    @Autowired
    private ScheduledRunner scheduledRunner;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserService userService;

    @BeforeEach
    public void init() {
        MockitoAnnotations.initMocks(this);

        user = new User();
        user.setChatToken("TEST_USER_ID");
        user.setNickname("TestUser_for_CRUD_test");
        user.setEmail("<EMAIL>");
        user.setPhone("8(800)111-11-11");
        user.setRegistrationTime(ZonedDateTime.now());

        counteBySeller = new Integer(1);
        countSalesBySeller = 2L;
        countBuyerOrders = 3L;
        //Выключаем старые офферы
        //actualOutgoingOffers = Arrays.asList(mock(Offer.class));

        ReflectionTestUtils.setField(chatService, "apiUrl", apiUrl);
        ReflectionTestUtils.setField(chatService, "apiToken", apiToken);
        ReflectionTestUtils.setField(chatService, "mapper", mapper);

        when(productService.countBySeller(user)).thenReturn(new Integer(counteBySeller));
        when(orderService.countSalesBySeller(user)).thenReturn(countSalesBySeller);
        when(orderService.countBuyerOrders(user)).thenReturn(countBuyerOrders);
        //Выключаем старые офферы
        //when(offerService.getActualOutgoingOffers(user)).thenReturn(actualOutgoingOffers);
        when(bargainConfiguration.isEnabled()).thenReturn(true);
        when(bargainConfiguration.isEnabled()).thenReturn(true);
        when(bargainRepository.countAllByBuyerIdAndStateIn(user.getId(), Arrays.asList(BargainState.OFFER))).thenReturn(1);
    }

    /**
     * Регистрацию нового пользователя и проверку того, что его данные появились в базе данных чата
     */
    @Test
    public void _01_registrationUser() {
        chatService.transferUsersInfo(Arrays.asList(user));
        checkSyncUserWithChatService(user);
    }

    /**
     * Изменение данных пользователя и проверка того, что измененные данные появились в базе чата.
     */
    @Test
    public void _02_changeUserData() {
        String otherEmail = "<EMAIL>";
        String otherPhone = "8(800)222-22-22";
        user.setEmail(otherEmail);
        user.setPhone(otherPhone);
        chatService.updateUsersInfo(Arrays.asList(user));
        checkSyncUserWithChatService(user);
    }

    /**
     * Удаляем созданного тестового пользователя, чтобы не мешал реальным.
     */
    @Test
    public void _03_deleteUser() {
        chatService.deleteCustomersData(Arrays.asList(user));
    }

    /**
     * Проверка полной цепочки синхронизации пользователей от task server до сохранения на master server
     */
    @Test
    public void _04_scheduledSynUsers() {
        // Необходимо взять с базы данных двух пользователей
        // и попытаться выполнить полную цепочку синхронизации с HelpCrunch
        List<User> syncUsers = new ArrayList<>(countSyncUsers);
        syncUsers.add(getRandomUser());
        syncUsers.add(getRandomUser());

        syncUsers = userRepository.saveAll(syncUsers);

        List<User> syncedUsersWithChat = scheduledRunner.syncUsersWithChatService(countSyncUsers);
        // На выполнение таска требуется время
	    TestUtils.sleep(1);
        assertNotNull(syncedUsersWithChat);
        assertTrue(syncedUsersWithChat.stream().map(syncedUserWithChat -> syncedUserWithChat.getId()).collect(Collectors.toList())
                .containsAll(syncUsers.stream().map(syncUser -> syncUser.getId()).collect(Collectors.toList())));
        syncUsers.forEach(u -> {
            checkSyncUserFromUserDB(u);
            checkSyncUserWithChatService(u);
        });

        // Пробуем изменить у пользователей любые данны и обновить информацию в HelpCrunch
        syncUsers = getDBUsers(syncUsers);
        for (User syncUser : syncUsers) {
            syncUser.setPhone(RandomStringUtils.randomNumeric(10));
            syncUser.setRegistrationTime(ZonedDateTime.now());
            syncUser.setChatSyncTime(null);
        }
        userRepository.saveAll(syncUsers);
        syncedUsersWithChat = scheduledRunner.syncUsersWithChatService(countSyncUsers);
        // На выполнение таска требуется время
	    TestUtils.sleep(1);
        assertNotNull(syncedUsersWithChat);
        assertTrue(syncedUsersWithChat.stream().map(syncedUserWithChat -> syncedUserWithChat.getId()).collect(Collectors.toList())
                .containsAll(syncUsers.stream().map(syncUser -> syncUser.getId()).collect(Collectors.toList())));
        syncUsers.forEach(u -> {
            checkSyncUserFromUserDB(u);
            checkSyncUserWithChatService(u);
        });

    }

    private void checkSyncUserWithChatService(User syncUser) {
        CustomerInfoResponse customerInfo = chatService.searchCustomerDataByChatToken(syncUser.getChatToken());
        assertNotNull(customerInfo);
        assertEquals(syncUser.getChatToken(), customerInfo.getUserId());
        assertEquals(syncUser.getNickname(), customerInfo.getName());
        assertEquals(syncUser.getEmail(), customerInfo.getEmail());
        assertEquals(syncUser.getPhone(), customerInfo.getPhone());
    }

    private void checkSyncUserFromUserDB(User syncUser) {
        User dbSyncedUser = userRepository.findById(syncUser.getId()).orElse(null);
        assertNotNull(dbSyncedUser);
        assertNotNull(dbSyncedUser.getChatToken());
        assertNotNull(dbSyncedUser.getChatSyncTime());
        assertEquals(syncUser.getId(), dbSyncedUser.getId());
        assertEquals(syncUser.getChatToken(), dbSyncedUser.getChatToken());
    }

    private User getRandomUser() {
        User randomUser = new User();
        randomUser.setChangeTime(Utils.nowAtUTC());
        String randomUserPassword = RandomStringUtils.randomAlphabetic(8);
        String randomUserEmail = RandomStringUtils.randomAlphabetic(3).toLowerCase() + "@test.mail";
        String randomUserPhone = RandomStringUtils.randomNumeric(10);
        randomUser.setEmail(randomUserEmail)
                .setNickname(RandomStringUtils.randomAlphabetic(5))
                .setHashedPassword(userService.getHashedPassword(randomUserPassword))
                .setRegistrationTime(ZonedDateTime.now())
                .setApiHashedPassword(userService.getApiHashedPassword(randomUserPassword, randomUserEmail))
                .setUserType(SIMPLE_USER)
                .setChatToken(UUID.randomUUID().toString())
                .setPhone(randomUserPhone);

        return randomUser;
    }

    private List<User> getDBUsers(List<User> syncUsers) {
        return userRepository.findAllById(syncUsers.stream().map(u -> u.getId()).collect(Collectors.toList()));
    }
}
