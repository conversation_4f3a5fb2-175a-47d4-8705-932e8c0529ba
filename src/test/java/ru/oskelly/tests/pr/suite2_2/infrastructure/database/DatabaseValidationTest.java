package ru.oskelly.tests.pr.suite2_2.infrastructure.database;

import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.jdbc.core.ColumnMapRowMapper;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;

import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class DatabaseValidationTest extends AbstractSpringTest {

    @Test
    @SneakyThrows
    public void validateNoConstraintsInAuditTables() {
        final URL queryUrl = getClass().getClassLoader().getResource("databaseValidationTest/validateNoConstraintsInAuditTables.sql");
        final String sqlQuery = IOUtils.toString(queryUrl, StandardCharsets.UTF_8);
        List<Map<String, Object>> resultData = jdbcTemplate.query(sqlQuery, new ColumnMapRowMapper());
        Assertions.assertThat(resultData).isEmpty();
    }

}