package ru.oskelly.tests.pr.suite2_2.infrastructure.configuration.amqp;

import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.AccountTestSupport;
import su.reddot.component.TestApplicationEventListener;
import su.reddot.domain.event.ChangeEnvironmentEvent;
import su.reddot.domain.event.UserAuthEvent;
import su.reddot.domain.event.UserSubscriptionEvent;
import su.reddot.domain.event.UserRegisterEvent;
import su.reddot.domain.event.UserEmailSubscriptionEvent;
import su.reddot.domain.model.notification.UserSubscriptionType;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.AccountDTO;
import su.reddot.domain.service.subscriptionContact.SubscriptionContactService;
import su.reddot.domain.service.system.SystemService;
import su.reddot.domain.service.user.ChangeUserEvent;
import su.reddot.infrastructure.util.ProductionEnvironment;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Проверка работоспособности пересылки сообщений через очереди
 */
@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class AMQPTest extends AbstractSpringTest {
    private static LocalDateTime time = LocalDateTime.now();

    @Autowired
    private ApplicationEventPublisher publisher;

    @Autowired
    private AccountTestSupport accountTestSupport;

    @Autowired
    private SystemService systemService;

    @Autowired
    private TestApplicationEventListener testApplicationEventListener;

    @Value("${test.api.user-id}")
    private Long userId;
    @Value("${test.api.user-email}")
    private String userEmail;
    @Value("${test.api.user-password}")
    private String userPassword;

    static ApiV2Client apiV2Client;

    @PostConstruct
    public void init(){
        apiV2Client = new ApiV2Client(userEmail, userPassword);
    }

    @Test
    public void _00_01_checkUserAuthEventDelivery(){
        //Чистим последнее сохраненное событие
        testApplicationEventListener.setLastUserAuthEvent(null);
        UserAuthEvent event = new UserAuthEvent().setUserId(userId).setTime(time);
        publisher.publishEvent(event);
        TestUtils.sleep(2);
        //Проверяем событие
        UserAuthEvent lastEvent = testApplicationEventListener.getLastUserAuthEvent();
        //Событие дошло
        assertNotNull(lastEvent);
        assertEquals(userId, lastEvent.getUserId());
        assertEquals(time, lastEvent.getTime());
        assertEquals(systemService.getInstanceId(), lastEvent.getInstanceId());
    }

    @Test
    public void _00_02_checkUserAuthEventDeliveryOnAuthorized(){
        //Чистим последнее сохраненное событие
        testApplicationEventListener.setLastUserAuthEvent(null);
        //Авторизуемся
        accountTestSupport.rawAuthSuccessful(apiV2Client, userEmail, userPassword);
        //Ждем события об авторизации через очередь
        TestUtils.sleep(2);
        //Проверяем событие
        UserAuthEvent lastEvent = testApplicationEventListener.getLastUserAuthEvent();
        //Событие дошло
        assertNotNull(lastEvent);
        assertEquals(userId, lastEvent.getUserId());
        assertEquals(systemService.getInstanceId(), lastEvent.getInstanceId());
    }

    @Test
    public void _01_01_checkUserSubscriptionEventEmailDelivery(){
        String contact = "<EMAIL>";
        //Чистим последнее сохраненное событие
        testApplicationEventListener.setUserEmailSubscriptionEvent(null);
        UserEmailSubscriptionEvent event = new UserEmailSubscriptionEvent().setUserId(userId).setTime(time)
                .setType(UserSubscriptionType.EMAIL)
                .setContact(contact)
                .setSubscribed(true);
        publisher.publishEvent(event);
        TestUtils.sleep(2);
        //Проверяем событие
        UserEmailSubscriptionEvent lastEvent = testApplicationEventListener.getUserEmailSubscriptionEvent();
        //Событие дошло
        assertNotNull(lastEvent);
        assertEquals(userId, lastEvent.getUserId());
        assertEquals(time, lastEvent.getTime());
        assertEquals(systemService.getInstanceId(), lastEvent.getInstanceId());
        assertTrue(lastEvent.isSubscribed());
        assertSame(UserSubscriptionType.EMAIL, lastEvent.getType());
        assertEquals(contact, lastEvent.getContact());
    }

    @Test
    public void _01_02_checkUserSubscriptionEventMobilePushDelivery(){
        String contact = "<EMAIL>";
        //Чистим последнее сохраненное событие
        testApplicationEventListener.setLastUserMobilePushEvent(null);
        UserSubscriptionEvent event = new UserSubscriptionEvent().setUserId(userId).setTime(time)
                .setType(UserSubscriptionType.MOBILE_PUSH)
                .setContact(contact);
        publisher.publishEvent(event);
        TestUtils.sleep(2);
        //Проверяем событие
        UserSubscriptionEvent lastEvent = testApplicationEventListener.getLastUserMobilePushEvent();
        //Событие дошло
        assertNotNull(lastEvent);
        assertEquals(userId, lastEvent.getUserId());
        assertEquals(time, lastEvent.getTime());
        assertEquals(systemService.getInstanceId(), lastEvent.getInstanceId());
        assertSame(UserSubscriptionType.MOBILE_PUSH, lastEvent.getType());
        assertEquals(contact, lastEvent.getContact());
    }

    @Test
    public void _01_03_checkUserSubscriptionEventDeliveryOnSubscribe(){
        String contact = "<EMAIL>";
        String name = "Надежда";
        User.Sex sex = User.Sex.FEMALE;
        //Чистим последнее сохраненное событие
        testApplicationEventListener.setUserEmailSubscriptionEvent(null);

        //Авторизуемся
        accountTestSupport.rawAuthSuccessful(apiV2Client, userEmail, userPassword);

        //Подписываемся
        SubscriptionContactService.SubscriptionContactRequest request = new SubscriptionContactService.SubscriptionContactRequest()
                .setEmailAddress(contact)
                .setName(name)
                .setSex(User.Sex.FEMALE);
        String response = accountTestSupport.subscribeSuccessful(apiV2Client, request);
        assertEquals("Подписка оформлена", response);
        TestUtils.sleep(2);
        //Проверяем событие
        UserEmailSubscriptionEvent lastEvent = testApplicationEventListener.getUserEmailSubscriptionEvent();
        //Событие дошло
        assertNotNull(lastEvent);
        assertEquals(userId, lastEvent.getUserId());
        assertNotNull(lastEvent.getTime());
        assertEquals(systemService.getInstanceId(), lastEvent.getInstanceId());
        assertTrue(lastEvent.isSubscribed());
        assertSame(UserSubscriptionType.EMAIL, lastEvent.getType());
        assertEquals(contact, lastEvent.getContact());
        assertEquals(name, lastEvent.getName());
        assertEquals(sex, lastEvent.getSex());
    }

    @Test
    public void _02_01_checkChangeUserEventDelivery(){
        String email = "<EMAIL>";
        String fullName = "Реальная Надежда Нанас";
        String phone = "+79203335556";
        String nickname = "test_nickname";
        //Чистим последнее сохраненное событие
        testApplicationEventListener.setLastChangeUserEvent(null);
        ChangeUserEvent event = new ChangeUserEvent().setId(userId).setIsPro(true)
                .setIsTrusted(true)
                .setEmail(email)
                .setFullName(fullName)
                .setNickName(nickname)
                .setPhone(phone);
        publisher.publishEvent(event);
        TestUtils.sleep(2);
        //Проверяем событие
        ChangeUserEvent lastEvent = testApplicationEventListener.getLastChangeUserEvent();
        //Событие дошло
        assertNotNull(lastEvent);
        assertEquals(userId, lastEvent.getId());
        assertEquals(email, lastEvent.getEmail());
        assertEquals(fullName, lastEvent.getFullName());
        assertEquals(phone, lastEvent.getPhone());
        assertTrue(lastEvent.getIsPro());
        assertTrue(lastEvent.getIsTrusted());
        assertEquals(systemService.getInstanceId(), lastEvent.getInstanceId());
        assertEquals(nickname, lastEvent.getNickName());
    }

    @Test
    public void _02_02_checkUserSubscriptionEventDeliveryOnSubscribe(){
        //Чистим последнее сохраненное событие
        testApplicationEventListener.setLastChangeUserEvent(null);

        //Авторизуемся
        accountTestSupport.rawAuthSuccessful(apiV2Client, userEmail, userPassword);

        //Получаем аккаунт
        AccountDTO accountDTO = accountTestSupport.getAccountSuccessful(apiV2Client, false);
        assertNotNull(accountDTO);

        //Обновляем телефон
        String newProneNumber = ("+7965" + ZonedDateTime.now().toEpochSecond()).substring(0, 12);
        accountDTO.setPhone(newProneNumber);

        //Устанавливаем isPro
        accountDTO.setIsPro(true);

        //Обновляем аккаунт
        AccountDTO updatedAccountDTO = accountTestSupport.updateAccountSuccessful(accountDTO, apiV2Client, false);

        assertNotNull(accountDTO);
        assertEquals(newProneNumber, updatedAccountDTO.getPhone());

        TestUtils.sleep(2);
        //Проверяем событие
        ChangeUserEvent lastEvent = testApplicationEventListener.getLastChangeUserEvent();
        //Событие дошло
        assertNotNull(lastEvent);
        assertEquals(userId, lastEvent.getId());
        assertTrue(lastEvent.getIsPro());
        assertEquals(systemService.getInstanceId(), lastEvent.getInstanceId());
    }

    @Test
    public void _03_01_checkUserRegisterEventDelivery(){
        String fullName = "Белых Иван Сергеевич";
        String phone = "+***********";
        String nickname = "test_nickname";
        //Чистим последнее сохраненное событие
        testApplicationEventListener.setLastUserRegisterEvent(null);
        UserRegisterEvent event = new UserRegisterEvent()
                .setUserId(userId)
                .setEmail(userEmail)
                .setFullName(fullName)
                .setPhone(phone)
                .setTime(time)
                .setNickName(nickname);
        publisher.publishEvent(event);
        TestUtils.sleep(2);
        //Проверяем событие
        UserRegisterEvent lastEvent = testApplicationEventListener.getLastUserRegisterEvent();
        //Событие дошло
        assertNotNull(lastEvent);
        assertEquals(userId, lastEvent.getUserId());
        assertEquals(userEmail, event.getEmail());
        assertEquals(fullName, event.getFullName());
        assertEquals(phone, event.getPhone());
        assertEquals(time, lastEvent.getTime());
        assertEquals(systemService.getInstanceId(), lastEvent.getInstanceId());
        assertEquals(nickname, lastEvent.getNickName());
    }

    @Test
    public void _03_02_checkUserRegisterEventDeliveryOnRegistered(){
        //Чистим последнее сохраненное событие
        testApplicationEventListener.setLastUserRegisterEvent(null);
        //Регистрируем пользователя
        Long userId = accountTestSupport.registerRandomUser(apiV2Client);

        //Ждем события об авторизации через очередь
        TestUtils.sleep(2);
        //Проверяем событие
        UserAuthEvent lastEvent = testApplicationEventListener.getLastUserAuthEvent();
        //Событие дошло
        assertNotNull(lastEvent);
        assertEquals(userId, lastEvent.getUserId());
        assertEquals(systemService.getInstanceId(), lastEvent.getInstanceId());
    }

    @Test
    public void _04_01_checkChangeEnvironmentEventDelivery(){
        //Чистим последнее сохраненное событие
        testApplicationEventListener.setLastChangeEnvironmentEvent(null);
        ChangeEnvironmentEvent event = new ChangeEnvironmentEvent().setEnvironment(ProductionEnvironment.RU);
        publisher.publishEvent(event);
        TestUtils.sleep(2);
        //Проверяем событие
        ChangeEnvironmentEvent lastEvent = testApplicationEventListener.getLastChangeEnvironmentEvent();
        //Событие дошло
        assertNotNull(lastEvent);
        assertEquals(ProductionEnvironment.RU, lastEvent.getEnvironment());
        assertEquals(systemService.getInstanceId(), lastEvent.getInstanceId());
    }

    @Test
    public void _04_02_checkChangeEnvironmentEventOnChangeCountry(){
        //Чистим последнее сохраненное событие
        testApplicationEventListener.setLastChangeEnvironmentEvent(null);
        //Меняем страну
        long countryId = accountTestSupport.changeCountrySuccessful(apiV2Client, 191L);
        assertEquals(191L, countryId);
        //Ждем события об авторизации через очередь
        TestUtils.sleep(2);
        //Проверяем событие
        ChangeEnvironmentEvent lastEvent = testApplicationEventListener.getLastChangeEnvironmentEvent();
        //Событие дошло
        assertNotNull(lastEvent);
        assertEquals(ProductionEnvironment.RU, lastEvent.getEnvironment());
        assertEquals(systemService.getInstanceId(), lastEvent.getInstanceId());
    }
}
