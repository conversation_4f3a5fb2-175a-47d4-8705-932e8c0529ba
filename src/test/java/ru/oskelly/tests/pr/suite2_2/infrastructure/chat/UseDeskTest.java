package ru.oskelly.tests.pr.suite2_2.infrastructure.chat;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserAttributeValueRepository;
import su.reddot.domain.model.address.Address;
import su.reddot.domain.model.addressendpoint.AddressEndpoint;
import su.reddot.domain.model.user.User;
import su.reddot.domain.model.user.UserAttributeType;
import su.reddot.domain.model.user.UserAttributeValue;
import su.reddot.domain.service.addressendpoint.AddressEndpointService;
import su.reddot.domain.service.dto.UserAttributeValueDTO;
import su.reddot.domain.service.user.UserService;
import su.reddot.domain.service.userattribute.UserAttributeValuesCollectRunner;
import su.reddot.infrastructure.chat.usedesk.UseDeskService;
import su.reddot.infrastructure.chat.usedesk.api.request.GetClientsRequest;
import su.reddot.infrastructure.chat.usedesk.api.request.UpdateClientRequest;
import su.reddot.infrastructure.chat.usedesk.api.response.CreateOrUpdateClientResponse;
import su.reddot.infrastructure.chat.usedesk.api.type.ClientAddress;
import su.reddot.infrastructure.chat.usedesk.api.type.ClientInfoDetails;
import su.reddot.infrastructure.chat.usedesk.api.type.ClientInfoFull;
import su.reddot.infrastructure.chat.usedesk.api.type.ClientInfoShort;
import su.reddot.infrastructure.chat.usedesk.api.request.CreateClientRequest;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.hamcrest.CoreMatchers.allOf;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Disabled
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class UseDeskTest extends AbstractSpringTest {

    @Autowired
    UseDeskService useDeskService;

    @Autowired
    UserAttributeValueRepository userAttributeValueRepository;

    @Autowired
    UserAttributeValuesCollectRunner userAttributeValuesCollectRunner;

    @Autowired
    UserService userService;

    @Autowired
    AddressEndpointService addressEndpointService;

    @Value("${test.usedesk.api-test-user.username}")
    private String testUserUsername;

    @Value("${test.usedesk.api-test-user.mailaddr}")
    private String testUserMailAddr;

    @Value("${test.usedesk.api-test-user.phone-id}")
    private String testUserPhoneNum;

    @Value("${test.usedesk.export-limits}")
    private int exportLimits;

    @Value("#{'${test.usedesk.export-emails}'.split(',')}")
    private List<String> exportEmails;

    @Value("${test.usedesk.phoneDupeUser.usermail}")
    private String phoneDupesUserMail;

    @Value("${test.usedesk.phoneDupeUser.phoneId1}")
    private String phoneDupesPhoneId1;

    @Value("${test.usedesk.phoneDupeUser.phoneId2}")
    private String phoneDupesPhoneId2;

    @Value("${test.usedesk.phoneDupeUser.phoneId3}")
    private String phoneDupesPhoneId3;

    private static String apCountry1 = "Россия";
    private static String apCountry3 = null;
    private static String region = "Тамбовская обл.";
    private static String apCity1 = "г.Тамбов";
    private static String apCity2 = "г.Черноголовка";
    private static String apCity3 = "г. Silent Hill";
    private static String zipCode = "435623";
    private static String address = "ул.Пушкина, д.Колотушкина, кв.67";

    private static String apFirstName1 = "Имя 1";
    private static String apExportLastName1 = "Фамилия 1";
    private static String apPatronymicName1 = "Отчество 1";

    private static String apFirstName2 = "Имя 2";
    private static String apExportLastName2 = "Фамилия 2";
    private static String apPatronymicName2 = "Отчество 2";

    private static String apExportPhone1 = "+79202341740";
    private static String apExportPhone2 = "+79204771677";

    @Test
    public void _01_getClients() {
        GetClientsRequest getClientsRequest = new GetClientsRequest();
        List<ClientInfoShort> clientList = useDeskService.getClients(getClientsRequest);
        assertTrue(clientList.size() > 0);
        assertTrue(clientList.size() <= 100);
    }

    @Test
    public void _02_getOneClient() {
        GetClientsRequest getClientsRequest = new GetClientsRequest();
        List<ClientInfoShort> clientList = useDeskService.getClients(getClientsRequest);
        assertTrue(clientList.size() > 0);
        assertTrue(clientList.size() <= 100);
        List<ClientInfoFull> clientInfo = useDeskService.getClient(clientList.get(0).getId());
        assertEquals(clientInfo.get(0).getClient().getName(), clientList.get(0).getName());
    }

    @Test
    public void _03_updateClient() {
        // Find by test mail
        GetClientsRequest getClientsRequest = new GetClientsRequest();
        getClientsRequest.setQuery(testUserMailAddr);
        List<ClientInfoShort> clientListFind = useDeskService.getClients(getClientsRequest);
        assertTrue(clientListFind.size() > 0);
        // Replace note with note + 1
        List<ClientInfoFull> clientListInfo = useDeskService.getClient(clientListFind.get(0).getId());
        assertEquals(1, clientListInfo.size());
        ClientInfoDetails clientInfo = clientListInfo.get(0).getClient();
        Pattern pattern = Pattern.compile("Note update (\\d*)");
        Matcher matcher = pattern.matcher(clientInfo.getNote());
        long nextValue = matcher.matches() ? Long.parseLong(matcher.group(1)) + 1 : 0;
        String noteText = "Note update " + nextValue;
        // Update client
        UpdateClientRequest updateClientRequest = new UpdateClientRequest();
        updateClientRequest.setClientId(clientInfo.getId());
        updateClientRequest.setNote(noteText);
        updateClientRequest.setIsNewNote(true);
        updateClientRequest.setPhone(testUserPhoneNum);
        CreateOrUpdateClientResponse updateClientResponse = useDeskService.updateClient(updateClientRequest);
        assertEquals(clientListFind.get(0).getId(), updateClientResponse.getClientId());
        // Verify updated
        List<ClientInfoFull> clientListInfo2 = useDeskService.getClient(clientListFind.get(0).getId());
        assertEquals(1, clientListInfo2.size());
        assertEquals(clientListInfo2.get(0).getClient().getNote(), noteText);
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _04_syncUsersWithFullCycle() {
        _04_prepareUsers2Sync(); // adding *@oskelly.ru users & dropping UseDesk attributes
        commitAndStartNewTransaction();
        //
        LocalDateTime syncTime1st = LocalDateTime.now(ZoneOffset.UTC);
        Map<Long, List<UserAttributeValueDTO>> sync1stUserIdAttributes = useDeskService.syncUsersWithUseDesk(exportLimits);
        assertThat(sync1stUserIdAttributes.size(), allOf(greaterThan(0), lessThanOrEqualTo(exportLimits)));
        commitAndStartNewTransaction();
        _04_validateUserAttributesAfterSync(syncTime1st, sync1stUserIdAttributes); // We have clientId / syncTime 4 each user, syncTime > NOW()
        //
        LocalDateTime syncTime2nd = LocalDateTime.now(ZoneOffset.UTC);
        Map<Long, List<UserAttributeValueDTO>> sync2ndUserIdAttributes = useDeskService.syncUsersWithUseDesk(exportLimits);
        assertThat(sync2ndUserIdAttributes.size(), allOf(greaterThan(0), lessThanOrEqualTo(exportLimits)));
        commitAndStartNewTransaction();
        _04_validateUserAttributesAfterSync(syncTime2nd, sync2ndUserIdAttributes); // We have clientId / syncTime 4 each user, syncTime > NOW()
        assertNotEquals(sync1stUserIdAttributes.keySet(), sync2ndUserIdAttributes.keySet()); // Should not match: next user pack to sync
        //
        _04_updateUsers2Sync(); // refreshing users from 1st sync attempt
        commitAndStartNewTransaction();
        //
        LocalDateTime syncTime3rd = LocalDateTime.now(ZoneOffset.UTC);
        Map<Long, List<UserAttributeValueDTO>> sync3rdUserIdAttributes = useDeskService.syncUsersWithUseDesk(exportLimits);
        assertThat(sync3rdUserIdAttributes.size(), allOf(greaterThan(0), lessThanOrEqualTo(exportLimits)));
        commitAndStartNewTransaction();
        _04_validateUserAttributesAfterSync(syncTime3rd, sync3rdUserIdAttributes); // We have clientId / syncTime 4 each user, syncTime > NOW()
        assertEquals(sync1stUserIdAttributes.keySet(), sync3rdUserIdAttributes.keySet()); // Should match cause we updated same users
    }

    private void _04_validateUserAttributesAfterSync(LocalDateTime syncStartTimeAt, Map<Long, List<UserAttributeValueDTO>> syncUserIdAttributes) {
        for (Long userId : syncUserIdAttributes.keySet()) {
            List<UserAttributeValue> userAttributeValues = userAttributeValueRepository.findAllByUserId(userId);
            userAttributeValues.stream().filter(uav -> uav.getUserAttributeId().equals(UserAttributeType.USEDESK_CLIENTID.id)).findFirst()
                    .orElseThrow(() -> new AssertionError("Unable to find UseDesk clientId user attribute"));
            UserAttributeValue uaSyncTime = userAttributeValues.stream().filter(uav -> uav.getUserAttributeId().equals(UserAttributeType.USEDESK_SYNCTIME.id)).findFirst()
                    .orElseThrow(() -> new AssertionError("Unable to find UseDesk syncTime user attribute"));
            assertTrue(uaSyncTime.getDatetimeValue().isAfter(syncStartTimeAt), "Wrong UseDesk syncTime in userAttribute");
            User syncUser = userService.getOne(userId);
            Pattern pattern = Pattern.compile(".*@oskelly.ru");
            Matcher matcher = pattern.matcher(syncUser.getEmail());
            assertTrue(matcher.matches(),"SyncUser mailMask verify failed");
        }
    }

    @Test
    @Transactional
    public void _05_verifyPhoneDupesOnSync() {
        User testUser = _XX_createOrUpdateUser(phoneDupesUserMail, StringUtils.substringBefore(phoneDupesUserMail, "@"), ZonedDateTime.now());
        //
        testUser.setPhone(phoneDupesPhoneId1);
        userService.save(testUser);
        _05_syncUserTwiceValidateThatPhoneOnlyOne(testUser, phoneDupesPhoneId1);
        //
        testUser.setPhone(phoneDupesPhoneId2);
        userService.save(testUser);
        _05_syncUserTwiceValidateThatPhoneOnlyOne(testUser, phoneDupesPhoneId2);
        //
        testUser.setPhone(phoneDupesPhoneId3);
        userService.save(testUser);
        _05_syncUserTwiceValidateThatPhoneOnlyOne(testUser, phoneDupesPhoneId3);
    }

    private void _05_syncUserTwiceValidateThatPhoneOnlyOne(User testUser, String phoneId) {
        String findPhoneNumber = useDeskService.getPhoneNumberInUsedeskFormat(phoneId);
        //
        List<UserAttributeValueDTO> sync1DTOs = useDeskService.syncOneUserWithUseDesk(testUser.getId()); // Sync 1st time, phone exists in client only once
        UserAttributeValueDTO sync1id = sync1DTOs.stream().filter(ua -> ua.getUserAttributeId().equals(UserAttributeType.USEDESK_CLIENTID.id)).findFirst()
                .orElseThrow(() -> new AssertionError("Unable to find UseDesk clientId user attribute"));
        List<ClientInfoFull> sync1info = useDeskService.getClient(sync1id.getLongValue());
        long phones1count = sync1info.get(0).getClient().getPhones().stream().filter(pi -> pi.getPhone().equals(findPhoneNumber)).count();
        assertEquals(findPhoneNumber, Integer.toString(1));
        assertEquals(findPhoneNumber, Long.toString(phones1count));
        //
        List<UserAttributeValueDTO> sync2DTOs = useDeskService.syncOneUserWithUseDesk(testUser.getId()); // Sync 2nd time, phone exists in client only once
        UserAttributeValueDTO sync2id = sync2DTOs.stream().filter(ua -> ua.getUserAttributeId().equals(UserAttributeType.USEDESK_CLIENTID.id)).findFirst()
                .orElseThrow(() -> new AssertionError("Unable to find UseDesk clientId user attribute"));
        List<ClientInfoFull> sync2info = useDeskService.getClient(sync2id.getLongValue());
        long phones2count = sync2info.get(0).getClient().getPhones().stream().filter(pi -> pi.getPhone().equals(findPhoneNumber)).count();

        assertEquals(findPhoneNumber, Integer.toString(1));
        assertEquals(findPhoneNumber, Long.toString(phones2count));
    }

    private Address _06_getAddress(String country, String city) {
        return new Address().setZipCode(zipCode).setCountry(country).setRegion(region).setCity(city).setAddress(address);
    }

    private AddressEndpoint _06_getAddressEndpoint(String country, String city, String lastName, String firstName, String patronymicName, String phone) {
        return new AddressEndpoint().setAddress(_06_getAddress(country, city)).setPhone(phone).setFirstName(firstName).setPatronymicName(patronymicName).setLastName(lastName);
    }

    private List<ClientAddress> getClientAddresses(ClientInfoDetails clientInfoDetails, String country, String city, String address) {
        Predicate<ClientAddress> countryPredicate = addr -> StringUtils.equals(addr.getCountry(), country) || StringUtils.isAllBlank(addr.getCountry(), country);
        Predicate<ClientAddress> cityPredicate = addr -> StringUtils.equals(addr.getCity(), city)  || StringUtils.isAllBlank(addr.getCity(), city);
        Predicate<ClientAddress> addressPredicate = addr -> StringUtils.equals(addr.getAddress(), address) || StringUtils.isAllBlank(addr.getAddress(), address);
        //
        return clientInfoDetails.getAddresses().stream().filter(countryPredicate.and(cityPredicate).and(addressPredicate)).collect(Collectors.toList());
    }

    @Test
    @Transactional
    public void _06_saveFullNamesAndPhonesFromAdressEndpointsNoDupes() {
        User testUser = _XX_createOrUpdateUser(phoneDupesUserMail, StringUtils.substringBefore(phoneDupesUserMail, "@"), ZonedDateTime.now());
        //
        AddressEndpoint addressEndpoint1 = _06_getAddressEndpoint(apCountry1, apCity1, apExportLastName1, apFirstName1, apPatronymicName1, apExportPhone1);
        addressEndpointService.save(testUser, addressEndpoint1);
        //
        AddressEndpoint addressEndpoint2 = _06_getAddressEndpoint(apCountry1, apCity1, apExportLastName2, apFirstName2, apPatronymicName2, apExportPhone2);
        addressEndpointService.save(testUser, addressEndpoint2);
        //
        AddressEndpoint addressEndpoint3 = _06_getAddressEndpoint(apCountry1, apCity2, apExportLastName2, apFirstName2, apPatronymicName2, apExportPhone2);
        addressEndpointService.save(testUser, addressEndpoint3);
        //
        AddressEndpoint addressEndpoint4 = _06_getAddressEndpoint(apCountry3, apCity3, apExportLastName2, apFirstName2, apPatronymicName2, apExportPhone2);
        addressEndpointService.save(testUser, addressEndpoint4);
        //
        List<UserAttributeValueDTO> sync1DTOs = useDeskService.syncOneUserWithUseDesk(testUser.getId()); // 1st sync, we have both messenger`s with full name, one for each
        List<ClientInfoFull> sync1info = getClientInfoFromSyncDTOs(sync1DTOs);
        ClientInfoDetails client1Details = sync1info.get(0).getClient();
        assertEquals(1, client1Details.getMessengers().stream().filter(msgr -> msgr.getIdentity().equals(addressEndpoint1.getFullName())).count());
        assertEquals(1, client1Details.getMessengers().stream().filter(msgr -> msgr.getIdentity().equals(addressEndpoint2.getFullName())).count());
        assertEquals(1, client1Details.getPhones().stream().filter(up -> up.getPhone().equals(useDeskService.getPhoneNumberInUsedeskFormat(apExportPhone1))).count());
        assertEquals(1, client1Details.getPhones().stream().filter(up -> up.getPhone().equals(useDeskService.getPhoneNumberInUsedeskFormat(apExportPhone2))).count());
        assertEquals(1, getClientAddresses(client1Details, apCountry1, apCity1, address).size());
        assertEquals(1, getClientAddresses(client1Details, apCountry1, apCity2, address).size());
        assertEquals(1, getClientAddresses(client1Details, apCountry3, apCity3, address).size());
        //
        List<UserAttributeValueDTO> sync2DTOs = useDeskService.syncOneUserWithUseDesk(testUser.getId()); // 2nd, sync user, we have both messenger`s with full name, one for each
        List<ClientInfoFull> sync2info = getClientInfoFromSyncDTOs(sync2DTOs);
        ClientInfoDetails client2Details = sync2info.get(0).getClient();
        assertEquals(1, client2Details.getMessengers().stream().filter(msgr -> msgr.getIdentity().equals(addressEndpoint1.getFullName())).count());
        assertEquals(1, client2Details.getMessengers().stream().filter(msgr -> msgr.getIdentity().equals(addressEndpoint2.getFullName())).count());
        assertEquals(1, client2Details.getPhones().stream().filter(up -> up.getPhone().equals(useDeskService.getPhoneNumberInUsedeskFormat(apExportPhone1))).count());
        assertEquals(1, client2Details.getPhones().stream().filter(up -> up.getPhone().equals(useDeskService.getPhoneNumberInUsedeskFormat(apExportPhone2))).count());
        assertEquals(1, getClientAddresses(client2Details, apCountry1, apCity1, address).size());
        assertEquals(1, getClientAddresses(client2Details, apCountry1, apCity2, address).size());
        assertEquals(1, getClientAddresses(client2Details, apCountry3, apCity3, address).size());
    }

    @Test
    @Transactional
    public void _07_buildUsersListToSync() {
        List<Long> userListToSync = useDeskService.buildUsersListToSync(30);
        Assertions.assertFalse(userListToSync.isEmpty());
    }

    @Test
    @Transactional
    public void _08_collectUsersAttributesValues() {
        userAttributeValuesCollectRunner.collectUsersAttributesValues();
    }

    private List<ClientInfoFull> getClientInfoFromSyncDTOs(List<UserAttributeValueDTO> sync1DTOs) {
        UserAttributeValueDTO sync1id = sync1DTOs.stream().filter(ua -> ua.getUserAttributeId().equals(UserAttributeType.USEDESK_CLIENTID.id)).findFirst()
                .orElseThrow(() -> new AssertionError("Unable to find UseDesk clientId user attribute"));
        return useDeskService.getClient(sync1id.getLongValue());
    }

    //@Test
    public void _XX_createClient() {
        CreateClientRequest createClientRequest = new CreateClientRequest();
        createClientRequest.setName(testUserUsername);
        createClientRequest.setPhone(testUserPhoneNum);
        createClientRequest.setEmails(Collections.singletonList(testUserMailAddr));
        CreateOrUpdateClientResponse createClientResponse = useDeskService.createClient(createClientRequest);
        //
        List<ClientInfoFull> clientInfo = useDeskService.getClient(createClientResponse.getClientId());
        assertEquals(testUserUsername, clientInfo.get(0).getClient().getName());
    }

    private void _04_prepareUsers2Sync() {
        ZonedDateTime registrationFrom = ZonedDateTime.now();
        for (int i = 0; i < exportEmails.size(); i++) {
            _04_prepareTestDbUser(exportEmails.get(i), StringUtils.substringBefore(exportEmails.get(i), "@"), registrationFrom.minusMinutes(i));
        }
    }

    private void _04_updateUsers2Sync() {
        exportEmails.forEach(this::_04_updateExistingUser);
    }

    private User _04_prepareTestDbUser(String email, String nickname, ZonedDateTime registrationTime) {
        User testUser = _XX_createOrUpdateUser(email, nickname, registrationTime);
        userAttributeValueRepository.deleteByUserIdAndUserAttributeId(testUser.getId(), UserAttributeType.USEDESK_CLIENTID.id);
        userAttributeValueRepository.deleteByUserIdAndUserAttributeId(testUser.getId(), UserAttributeType.USEDESK_SYNCTIME.id);
        return testUser;
    }

    private User _XX_createOrUpdateUser(String email, String nickname, ZonedDateTime registrationTime) {
        User updateUser = userService.getUserByEmail(email);
        if (Objects.isNull(updateUser)) {
            updateUser = new User();
        }
        updateUser.setUserType(User.UserType.SIMPLE_USER);
        updateUser.setEmail(email);
        updateUser.setNickname(nickname);
        updateUser.setLastAccessTime(LocalDateTime.now());
        updateUser.setRegistrationTime(registrationTime);
        return userService.save(updateUser);
    }

    private User _04_updateExistingUser(String email) {
        User updateUser = userService.getUserByEmail(email);
        updateUser.setLastAccessTime(LocalDateTime.now());
        return userService.save(updateUser);
    }


}