package ru.oskelly.tests.pr.suite2_2.infrastructure.logistic.aramex.client;

import lombok.NonNull;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.infrastructure.logistic.aramex.AramexConfiguration;
import su.reddot.infrastructure.logistic.aramex.client.AramexLocationClient;
import su.reddot.infrastructure.logistic.aramex.client.AramexShippingClient;
import su.reddot.infrastructure.logistic.aramex.client.AramexTrackingClient;
import su.reddot.infrastructure.logistic.aramex.client.rest.AramexLocationRestClient;
import su.reddot.infrastructure.logistic.aramex.client.rest.AramexShippingRestClient;
import su.reddot.infrastructure.logistic.aramex.client.rest.AramexTrackingRestClient;

import org.junit.jupiter.api.Disabled;

@Disabled("DEVALAN-1650")
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class AramexRestClientTest extends AramexClientTest {

    @Override
    @NonNull
    protected AramexShippingClient createShippingClient(@NonNull final AramexConfiguration configuration) {
        return new AramexShippingRestClient(configuration);
    }

    @Override
    @NonNull
    protected AramexTrackingClient createTrackingClient(@NonNull final AramexConfiguration configuration) {
        return new AramexTrackingRestClient(configuration);
    }

    @Override
    @NonNull
    protected AramexLocationClient createLocationClient(@NonNull final AramexConfiguration configuration) {
        return new AramexLocationRestClient(configuration);
    }
}
