package ru.oskelly.tests.pr.suite2_2.infrastructure.export.feed;

import org.junit.jupiter.api.Test;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.infrastructure.export.feed.FacebookFeedExporter;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class FeedsTest {
    private FacebookFeedExporter facebookFeedExporter = new FacebookFeedExporter();

    @Test
    public void facebookFeedExporter_getQuery(){
        String query = facebookFeedExporter.getQuery();
        assertNotNull(query);
        assertTrue(query.contains("xmlelement"));
	    assertTrue(query.contains(FacebookFeedExporter.productFilterStartStr));
	    assertTrue(query.contains(FacebookFeedExporter.productFilterEndStr));
    }

	@Test
	public void facebookFeedExporter_setProductFilter(){
		String query = facebookFeedExporter.getQuery();
		assertNotNull(query);
		String productFilter = "p.id IS NOT NULL";
		String changedQuery = facebookFeedExporter.setProductFilter(query, productFilter);
		assertTrue(changedQuery.contains(FacebookFeedExporter.productFilterStartStr + " " + productFilter + " " + FacebookFeedExporter.productFilterEndStr));
	}


}
