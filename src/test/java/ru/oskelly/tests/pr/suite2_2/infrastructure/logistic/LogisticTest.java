package ru.oskelly.tests.pr.suite2_2.infrastructure.logistic;

import lombok.NonNull;
import org.hibernate.envers.AuditReader;
import org.hibernate.envers.AuditReaderFactory;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.jpa.repository.JpaContext;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;

import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.build.presentation.pdf.PdfTestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.adminalert.AdminAlertRepository;
import su.reddot.domain.dao.logistic.OrderWaybillsRepository;
import su.reddot.domain.dao.logistic.WaybillOrderRepository;
import su.reddot.domain.dao.logistic.WaybillRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.order.OrderStateChangeRepository;
import su.reddot.domain.dao.product.ProductItemRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.address.Address;
import su.reddot.domain.model.addressendpoint.AddressEndpoint;
import su.reddot.domain.model.adminalert.AdminAlert;
import su.reddot.domain.model.fiscalreceiptrequest.FiscalReceiptRequestKind;
import su.reddot.domain.model.fiscalreceiptrequest.FiscalReceiptRequestType;
import su.reddot.domain.model.logistic.DestinationType;
import su.reddot.domain.model.logistic.OrderWaybills;
import su.reddot.domain.model.logistic.QWaybill;
import su.reddot.domain.model.logistic.Waybill;
import su.reddot.domain.model.logistic.event.SaleConfirmedEvent;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.order.OrderPositionState;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.address.AddressService;
import su.reddot.domain.service.addressendpoint.AddressEndpointService;
import su.reddot.domain.service.dto.LogisticStateDeliveryDTO;
import su.reddot.domain.service.fiscalreceiptrequest.FiscalReceiptRequestService;
import su.reddot.domain.service.integration.logistic.model.TimeIntervalDTO;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.logistic.CommonLogisticService;
import su.reddot.infrastructure.logistic.DeliveryState;
import su.reddot.infrastructure.logistic.FiasIds;
import su.reddot.infrastructure.logistic.LogisticService;
import su.reddot.infrastructure.logistic.TimeIntervalsService;
import su.reddot.infrastructure.logistic.aramex.AramexLogisticService;
import su.reddot.infrastructure.logistic.cse.CseLogisticService;
import su.reddot.infrastructure.logistic.dalli.DalliConfiguration;
import su.reddot.infrastructure.logistic.major.impl.MajorLogisticService;
import su.reddot.infrastructure.util.CallInTransaction;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {OskellyApplication.class},
        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT,
        properties = {"logistic.with-aramex=false",
                      "app.integration.cse.add-timestamp-to-client-number=true"})
@ActiveProfiles(profiles = AbstractSpringTest.testProfiles)
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class LogisticTest {

    private static final long ORDER_AMOUNT = 8000;

    private Order order;
    private List<Long> orderIds;
    private static List<Product> productsForOrders;
    private static final List<Long> cleanUpOrdersIDs = new ArrayList<>();
    private static final List<Long> cleanUpWaybillIDs = new ArrayList<>();
    private static final List<Long> cleanUpWaybillOrderIDs = new ArrayList<>();

    private final Long realSellerId = 23L;

    @Value("${test.api.user2-email}")
    private String sellerEmail;
    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${bank-account.payment-version}")
    private String paymentVersion;

    @Autowired
    private CommonLogisticService commonLogisticService;
    @Autowired
    private CseLogisticService cseLogisticService;
    @Autowired
    private MajorLogisticService majorLogisticService;
    @Autowired(required = false)
    private AramexLogisticService aramexLogisticService;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private ProductRepository productRepository;
    @Autowired
    private ProductItemRepository productItemRepository;
    @Autowired
    private UserService userService;
    @Autowired
    private AddressService addressService;
    @Autowired
    private AddressEndpointService addressEndpointService;
    @Autowired
    private WaybillRepository waybillRepository;
    @Autowired
    private WaybillOrderRepository waybillOrderRepository;
    @Autowired
    private OrderStateChangeRepository orderStateChangeRepository;
    @Autowired
    private AdminAlertRepository<AdminAlert> adminAlertRepository;
    @Autowired
    private OrderWaybillsRepository orderWaybillsRepository;
    @Autowired
    private FiscalReceiptRequestService fiscalReceiptRequestService;

    @Autowired
    private DalliConfiguration dalliConfiguration;

    @Autowired
    private CallInTransaction callInTransaction;

    @MockBean
    private TimeIntervalsService timeIntervalsService;

    @Autowired
    private JpaContext jpaContext;

    private User getBuyer() {
        return userService.getUserByEmail(buyerEmail);
    }

    private User getSeller() {
        return userService.getUserByEmail(sellerEmail);
    }

    private AddressEndpoint createAddressEndpoint(User user, String  country, String region, String regionFiasId, String cityName) {
        String deliveryAddressEndpointAddress = "UAE".equals(country)
                ? "Office # 210, 2nd Floor, Dubai Tower, Al Nasser Square, Deira"
                : "Ленина, д.2";
        Address address = new Address().setCountry(country).setCity(cityName)
                .setRegion(region).setRegionFiasId(regionFiasId)
                .setAddress(deliveryAddressEndpointAddress).setUser(user);
        address = addressService.saveAddress(user, address);
        String deliveryAddressEndpointPhone = "+79202341740";
        String deliveryAddressEndpointFirstName = "Иван";
        String deliveryAddressEndpointPatronymicName = "Сергеевич";
        String deliveryAddressEndpointLastName = "Белых";
        AddressEndpoint addressEndpoint = new AddressEndpoint()
                .setAddress(address)
                .setUser(user)
                .setPhone(deliveryAddressEndpointPhone)
                .setFirstName(deliveryAddressEndpointFirstName)
                .setPatronymicName(deliveryAddressEndpointPatronymicName)
                .setLastName(deliveryAddressEndpointLastName);
        return addressEndpointService.save(user, addressEndpoint);
    }

    private List<Product> getProductsForOrders() {
        if (productsForOrders == null) {
            productsForOrders = new ArrayList<>();
            List<Product> products = new ArrayList<>(productRepository.findProductsBySellerIdAndProductState(realSellerId, ProductState.PUBLISHED));
            for (int i = 0; i < 4; i++) {
                Product product = products.get(i);
                product.setSeller(getSeller());
                productRepository.saveAndFlush(product);
                product.getProductItems().forEach(pi -> {
                    pi.setHidden(false);
                    pi.setCount(10);
                    pi.setDeleteTime(null);
                    productItemRepository.saveAndFlush(pi);
                });
                productsForOrders.add(product);
            }
        }
        return productsForOrders;
    }

    private Order createOrder(String pickupCity, String pickupCountry, String pickupRegion, String pickupRegionFiasId,
                              String deliveryCity, String deliveryCountry, String deliveryRegion, String deliveryRegionFiasId) {
        Order order = new Order();
        order.setBuyer(getBuyer());
        order.setUuid(UUID.randomUUID());
        order.setState(OrderState.CREATED);
        order.setPaymentVersion(paymentVersion);
        List<Product> products = getProductsForOrders();
        for (int i = 0; i < 4; i++) {
            Product product = products.get(i);
            ProductItem productItem = productItemRepository.findAllByProduct(product).get(0);
            OrderPosition orderPosition = new OrderPosition();
            orderPosition.setState(OrderPositionState.INITIAL);
            orderPosition.setIsEffective(true);
            orderPosition.setParticipatesInPayment(true);
            orderPosition.setStateTime(LocalDateTime.now());
            orderPosition.setConfirmedTime(ZonedDateTime.now());
            orderPosition.setProductItem(productItem);
            orderPosition.setAmount(new BigDecimal(ORDER_AMOUNT / 4));
            orderPosition.setItemSaleAmount(orderPosition.getAmount());
            orderPosition.setCommission(new BigDecimal("0.15"));
            order.addPosition(orderPosition);
        }
        order.setPickupAddressEndpoint(createAddressEndpoint(getSeller(), pickupCountry, pickupRegion, pickupRegionFiasId, pickupCity));
        order.setDeliveryAddressEndpoint(createAddressEndpoint(getBuyer(), deliveryCountry, deliveryRegion, deliveryRegionFiasId, deliveryCity));
        order = orderRepository.saveAndFlush(order);
        cleanUpOrdersIDs.add(order.getId());
        return order;
    }

    @BeforeEach
    public void setUp() {
        TimeIntervalDTO timeInterval = new TimeIntervalDTO();
        timeInterval.setFromHour(10);
        timeInterval.setToHour(17);
        Mockito.when(timeIntervalsService.getInterval(3L)).thenReturn(timeInterval);

        timeInterval = new TimeIntervalDTO();
        timeInterval.setFromHour(10);
        timeInterval.setToHour(22);
        Mockito.when(timeIntervalsService.getInterval(4L)).thenReturn(timeInterval);

        timeInterval = new TimeIntervalDTO();
        timeInterval.setFromHour(9);
        timeInterval.setToHour(18);
        Mockito.when(timeIntervalsService.getInterval(5L)).thenReturn(timeInterval);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _00_createOrderByDalliInMoscowRegion() {
        order = createOrder(
                "Москва", "Россия", "Московская область", FiasIds.MOSCOW,
                "Зеленоград", "Россия", "Московская область", FiasIds.MOSCOW_REGION);
        createOrderByDalli(false, 3);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _00_createOrderByDalliInTver() {
        order = createOrder(
                "Москва", "Россия", "Московская область", FiasIds.MOSCOW,
                "Тверь", "Россия", null, FiasIds.TVER_REGION);
        createOrderByDalli(false, 5);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _00_createOrderByDalliInKaluga() {
        order = createOrder(
                "Москва", "Россия", "Московская область", FiasIds.MOSCOW,
                "Калуга", "Россия", null, FiasIds.KALUGA_REGION);
        createOrderByDalli(false, 5);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _00_createOrderByDalliFromSeller() {
        order = createOrder(
                "Москва", "Россия", "Московская область", FiasIds.MOSCOW,
                "Зеленоград", "Россия", "Московская область", FiasIds.MOSCOW_REGION);
        createOrderByDalli(true, 3);
    }

    private void createOrderByDalli(final boolean isFromSellerToOffice, final long intervalId) {
        UUID uuid = UUID.randomUUID();
        SaleConfirmedEvent e = new SaleConfirmedEvent(
                order.getId(),
                null,
                orderIds,
                isFromSellerToOffice ? DestinationType.SELLER : DestinationType.OFFICE,
                isFromSellerToOffice ? DestinationType.OFFICE : DestinationType.BUYER,
                null,
                intervalId,
                uuid.toString(),
                "Dalli",
                null,
                false);
        commonLogisticService.createOrderAndWaybill(e);

        Waybill waybill = findWaybill(uuid.toString());

        try {
            Assertions.assertNotNull(waybill.getExternalSystemId());
            final String postfix = isFromSellerToOffice
                    ? dalliConfiguration.getClientNumberS2oPostfix()
                    : dalliConfiguration.getClientNumberO2bPostfix();
            Assertions.assertEquals(order.getId() + postfix, waybill.getExternalSystemId());
            Assertions.assertEquals(uuid.toString(), waybill.getComment());
            final DestinationType destinationType = isFromSellerToOffice
                    ? DestinationType.OFFICE
                    : DestinationType.BUYER;
            Assertions.assertEquals(destinationType, waybill.getDeliveryDestinationType());
            Assertions.assertEquals("Dalli", waybill.getDeliveryCompany().getName());
            Assertions.assertNotNull(waybill.getDeclaredValue());
            Assertions.assertEquals(0, waybill.getDeclaredValue().longValue());
            Assertions.assertEquals(Integer.valueOf(1), waybill.getQuantity());

            String trackingUrl = commonLogisticService.getTrackingUrl(waybill);
            String expectedTrackingUrl = String.format("https://dalli-service.com/?number=%s&phone=%s",
                    waybill.getWaybillOrder().getExternalSystemId(),
                    order.getDeliveryAddressEndpoint().getPhone());
            Assertions.assertEquals(expectedTrackingUrl, trackingUrl);

            PdfTestUtils.validatePdf(commonLogisticService.getWaybillPDF(waybill.getId(), false));
            PdfTestUtils.validatePdf(commonLogisticService.getStickerPackPDF(waybill.getId(), false));

            final List<Waybill> waybills = waybillRepository.findByDeliveryCompanyNameAndWaybillOrderExternalSystemId(
                    "Dalli", waybill.getWaybillOrder().getExternalSystemId());
            Assertions.assertEquals(1, waybills.size());
            Assertions.assertEquals(waybill, waybills.get(0));
        } finally {
            // Подчищаем созданный заказ в Dalli API
            commonLogisticService.removeWaybillOrder(waybill.getId());
        }
    }

    /**
     * Проверяет что заказ и накладная созданы в КСЭ и загружены к нам в систему
     */
    @Test
    @Transactional
    @Rollback(value = false)
    public void _00_createOrderAndWaybillByCseInMoscow(){
        order = createOrder(
                "Москва", "Россия", "Московская область", FiasIds.MOSCOW,
                "Москва", "Россия", "Московская область", FiasIds.MOSCOW);
        UUID uuid = UUID.randomUUID();
        SaleConfirmedEvent e = new SaleConfirmedEvent(order.getId(),"", orderIds, DestinationType.SELLER, DestinationType.OFFICE, 2L, null, uuid.toString(), "CSE", null, false);
        commonLogisticService.createOrderAndWaybill(e);

        Waybill waybill = findWaybill(uuid.toString());

        Assertions.assertNotNull(waybill.getExternalSystemId());
        Assertions.assertEquals(uuid.toString(), waybill.getComment());
        Assertions.assertEquals(DestinationType.OFFICE, waybill.getDeliveryDestinationType());
        Assertions.assertEquals("CSE", waybill.getDeliveryCompany().getName());
        String trackingUrl = commonLogisticService.getTrackingUrl(waybill);
        Assertions.assertEquals("https://www.cse.ru/per/track/?numbers=" + waybill.getExternalSystemId(), trackingUrl);

        PdfTestUtils.validatePdf(commonLogisticService.getWaybillPDF(waybill.getId(), false));
        // CSE doesn't support stickers in test environment

        checkTracking(cseLogisticService, waybill);
    }

    /**
     * Проверяет что накладная без заказа созданы в КСЭ и в нашей системе
     */
    @Test
    @Transactional
    @Rollback(value = false)
    public void _00_createWaybillWithoutOrderByCseInMoscow() {
        order = createOrder(
                "Москва", "Россия", "Московская область", FiasIds.MOSCOW,
                "Москва", "Россия", "Московская область", FiasIds.MOSCOW);
        createWaybillWithoutOrderByCse();
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _00_createWaybillWithoutOrderByCseInKirov() {
        order = createOrder(
                "Киров", "Россия", "Кировская область", FiasIds.KIROV_REGION,
                "Киров", "Россия", "Кировская область", FiasIds.KIROV_REGION);
        createWaybillWithoutOrderByCse();
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _00_createWaybillWithoutOrderByCseInRussia(){
        order = createOrder(
                "Москва", "Россия", "Московская область", FiasIds.MOSCOW,
                "Казань", "Россия", "Республика Татарстан", FiasIds.TATARSTAN);
        createWaybillWithoutOrderByCse();
    }

    private void createWaybillWithoutOrderByCse() {
        UUID uuid = UUID.randomUUID();
        SaleConfirmedEvent e = new SaleConfirmedEvent(order.getId(), "", orderIds, DestinationType.OFFICE, DestinationType.BUYER, null, null, uuid.toString(), "CSE", null, false);
        commonLogisticService.createOrderAndWaybill(e);

        Waybill waybill = findWaybill(uuid.toString());

        Assertions.assertNotNull(waybill.getExternalSystemId());
        Assertions.assertEquals(uuid.toString(), waybill.getComment());
        Assertions.assertEquals(DestinationType.BUYER, waybill.getDeliveryDestinationType());
        Assertions.assertEquals("CSE", waybill.getDeliveryCompany().getName());
        Assertions.assertEquals(0, waybill.getDeclaredValue().longValue());
        Assertions.assertEquals(Integer.valueOf(1), waybill.getQuantity());

        PdfTestUtils.validatePdf(commonLogisticService.getWaybillPDF(waybill.getId(), false));
        // CSE doesn't support stickers in test environment

        checkTracking(cseLogisticService, waybill);
    }

    /**
     * Проверяет что заказ и накладная созданы в Major и загружены к нам в систему
     */
    @Test
    @Disabled("Disabled until Major test service is back")
    @Transactional
    @Rollback(value = false)
    public void _00_createOrderAndWaybillByMajorInSpb(){
        order = createOrder(
                "Санкт-Петербург", "Россия", null, FiasIds.LENINGRAD_REGION,
                "Москва", "Россия", "Московская область", FiasIds.MOSCOW);
        UUID uuid = UUID.randomUUID();
        String longComment = "5 этажное здание из красного кирпича). Проезжаем «автотехцентр», " +
                "На КПП сказать, что в 510 офис. Первый белый подъезд с колоннами, " +
                "на лифте на 4 этаж, подняться один пролёт наверх, направо до конца.\n" +
                "Контактный телефона \n+7 000 000-00-00\nСказать, что забираете серьги";
        SaleConfirmedEvent e = new SaleConfirmedEvent(order.getId(), "", orderIds, DestinationType.SELLER,
                DestinationType.OFFICE, 2L, null, uuid + longComment, "Major", null, false);
        commonLogisticService.createOrderAndWaybill(e);

        Waybill waybill = findWaybill(uuid.toString());

        Assertions.assertNotNull(waybill.getExternalSystemId());
        Assertions.assertTrue(waybill.getComment().contains(uuid.toString()));
        Assertions.assertEquals(DestinationType.OFFICE, waybill.getDeliveryDestinationType());
        Assertions.assertEquals("Major", waybill.getDeliveryCompany().getName());
        Assertions.assertEquals(order.calcProfitOfSeller().longValue(), waybill.getDeclaredValue().longValue());
        Assertions.assertEquals(Integer.valueOf(1), waybill.getQuantity());

        PdfTestUtils.validatePdf(commonLogisticService.getWaybillPDF(waybill.getId(), false));
        PdfTestUtils.validatePdf(commonLogisticService.getStickerPackPDF(waybill.getId(), false));

        checkTracking(majorLogisticService, waybill);
    }

    /**
     * Проверяет что заказ и накладная созданы в CSE и загружены к нам в систему
     */
    @Test
    @Transactional
    @Rollback(value = false)
    public void _00_createOrderAndWaybillByCseInLeningradRegion(){
        order = createOrder(
                "Выборг", "Россия", "Ленинградская область", FiasIds.LENINGRAD_REGION,
                "Москва", "Россия", "Московская область", FiasIds.MOSCOW);
        UUID uuid = UUID.randomUUID();
        SaleConfirmedEvent e = new SaleConfirmedEvent(order.getId(), "", orderIds, DestinationType.SELLER, DestinationType.OFFICE, 2L, null, uuid.toString(), "CSE", null, false);
        commonLogisticService.createOrderAndWaybill(e);

        Waybill waybill = findWaybill(uuid.toString());

        Assertions.assertNotNull(waybill.getExternalSystemId());
        Assertions.assertTrue(waybill.getComment().contains(uuid.toString()));
        Assertions.assertEquals(DestinationType.OFFICE, waybill.getDeliveryDestinationType());
        Assertions.assertEquals("CSE", waybill.getDeliveryCompany().getName());
        Assertions.assertEquals(0, waybill.getDeclaredValue().longValue());
        Assertions.assertEquals(Integer.valueOf(1), waybill.getQuantity());

        PdfTestUtils.validatePdf(commonLogisticService.getWaybillPDF(waybill.getId(), false));
        // CSE doesn't support stickers in test environment

        checkTracking(cseLogisticService, waybill);
    }

    /**
     * Проверяет что накладная без заказа созданы в Major и в нашей системе
     */
    @Test
    @Disabled("Disabled until Major test service is back")
    @Transactional
    @Rollback(value = false)
    public void _00_createWaybillWithoutOrderByMajorInMoscow(){
        order = createOrder(
                "Москва", "Россия", "Московская область", FiasIds.MOSCOW,
                "Москва", "Россия", "Московская область", FiasIds.MOSCOW);
        UUID uuid = UUID.randomUUID();
        SaleConfirmedEvent e = new SaleConfirmedEvent(order.getId(), "", orderIds, DestinationType.OFFICE, DestinationType.BUYER, null, null, uuid.toString(), "Major", null, false);
        commonLogisticService.createOrderAndWaybill(e);

        Waybill waybill = findWaybill(uuid.toString());

        Assertions.assertNotNull(waybill.getExternalSystemId());
        Assertions.assertEquals(uuid.toString(), waybill.getComment());
        Assertions.assertEquals(DestinationType.BUYER, waybill.getDeliveryDestinationType());
        Assertions.assertEquals("Major", waybill.getDeliveryCompany().getName());
        Assertions.assertEquals(order.calcProfitOfSeller().longValue(), waybill.getDeclaredValue().longValue());
        Assertions.assertEquals(Integer.valueOf(1), waybill.getQuantity());

        PdfTestUtils.validatePdf(commonLogisticService.getWaybillPDF(waybill.getId(), false));
        PdfTestUtils.validatePdf(commonLogisticService.getStickerPackPDF(waybill.getId(), false));

        checkTracking(majorLogisticService, waybill);
    }

    /**
     * Проверяет что заказ и накладная созданы в КСЭ и загружены к нам в систему
     */
    @Test
    @Transactional
    @Rollback(value = false)
    public void _00_createOrderAndWaybillByCseInRussia(){
        order = createOrder(
                "Сочи", "Россия", "Краснодарский край", FiasIds.KRASNODAR_KRAI,
                "Москва", "Россия", "Московская область", FiasIds.MOSCOW);
        UUID uuid = UUID.randomUUID();
        SaleConfirmedEvent e = new SaleConfirmedEvent(order.getId(), "", orderIds, DestinationType.SELLER, DestinationType.OFFICE, 2L, null, uuid.toString(), "CSE", null, false);
        commonLogisticService.createOrderAndWaybill(e);

        Waybill waybill = findWaybill(uuid.toString());

        Assertions.assertNotNull(waybill.getExternalSystemId());
        Assertions.assertEquals(uuid.toString(), waybill.getComment());
        Assertions.assertEquals(DestinationType.OFFICE, waybill.getDeliveryDestinationType());
        Assertions.assertEquals("CSE", waybill.getDeliveryCompany().getName());
        Assertions.assertEquals(0, waybill.getDeclaredValue().longValue());
        Assertions.assertEquals(Integer.valueOf(1), waybill.getQuantity());

        PdfTestUtils.validatePdf(commonLogisticService.getWaybillPDF(waybill.getId(), false));
        // CSE doesn't support stickers in test environment

        checkTracking(cseLogisticService, waybill);
    }

    /**
     * Проверяет что накладная без заказа созданы в ТК и в нашей системе
     */
    @Test
    @Transactional
    @Rollback(value = false)
    public void _00_createWaybillWithoutOrderAuto() {
        order = createOrder(
                "Красногорск", "Россия", "Московская область", FiasIds.MOSCOW_REGION,
                "Зеленоград", "Россия", "Московская область", FiasIds.MOSCOW_REGION);
        UUID uuid = UUID.randomUUID();
        SaleConfirmedEvent e = new SaleConfirmedEvent(
                order.getId(),
                "",
                orderIds,
                DestinationType.OFFICE,
                DestinationType.BUYER,
                null,
                4L,
                uuid.toString(),
                null,
                null,
                false);
        commonLogisticService.createOrderAndWaybill(e);

        Waybill waybill = findWaybill(uuid.toString());

        Assertions.assertNotNull(waybill.getExternalSystemId());
        Assertions.assertEquals(uuid.toString(), waybill.getComment());
        Assertions.assertEquals(DestinationType.BUYER, waybill.getDeliveryDestinationType());
        Assertions.assertEquals("CSE", waybill.getDeliveryCompany().getName());
        Assertions.assertEquals(0, waybill.getDeclaredValue().longValue());
        Assertions.assertEquals(Integer.valueOf(1), waybill.getQuantity());

        PdfTestUtils.validatePdf(commonLogisticService.getWaybillPDF(waybill.getId(), false));
        // CSE doesn't support stickers in test environment
    }

    @Test
    @Disabled("The test is ignored until we are given test Aramex stand")
    @Transactional
    @Rollback(value = false)
    public void _00_createOrderAndWaybillByAramexFromSeller() {
        createOrderAndWaybillByAramex(true);
    }

    @Test
    @Disabled("DEVALAN-1650")
    @Transactional
    @Rollback(value = false)
    public void _00_createOrderAndWaybillByAramexToBuyer() {
        createOrderAndWaybillByAramex(false);
    }

    private void createOrderAndWaybillByAramex(final boolean isFromSeller) {
        order = createOrder(
                "Dubai", "UAE", null, null,
                "Dubai", "UAE", null, null);

        final UUID uuid = UUID.randomUUID();
        final DestinationType pickupDestinationType = isFromSeller
                ? DestinationType.SELLER
                : DestinationType.OFFICE;
        final DestinationType deliveryDestinationType = isFromSeller
                ? DestinationType.OFFICE
                : DestinationType.BUYER;
        final SaleConfirmedEvent e = new SaleConfirmedEvent(
                order.getId(),
                "",
                orderIds,
                pickupDestinationType,
                deliveryDestinationType,
                3L,
                null,
                uuid.toString(),
                AramexLogisticService.NAME,
                null,
                false);
        commonLogisticService.createOrderAndWaybill(e);

        final Waybill waybill = findWaybill(uuid.toString());

        Assertions.assertNotNull(waybill.getExternalSystemId());
        Assertions.assertTrue(waybill.getComment().contains(uuid.toString()));
        Assertions.assertEquals(pickupDestinationType, waybill.getPickupDestinationType());
        Assertions.assertEquals(deliveryDestinationType, waybill.getDeliveryDestinationType());
        Assertions.assertEquals(AramexLogisticService.NAME, waybill.getDeliveryCompany().getName());
        Assertions.assertEquals(order.calcProfitOfSeller().longValue(), waybill.getDeclaredValue().longValue());
        Assertions.assertEquals(Integer.valueOf(1), waybill.getQuantity());

        PdfTestUtils.validatePdf(commonLogisticService.getWaybillPDF(waybill.getId(), false));
        PdfTestUtils.validatePdf(commonLogisticService.getStickerPackPDF(waybill.getId(), false));

        // Have to wait for some time until Aramex tracking takes up newly created waybill
        TestUtils.sleep(60);
        checkTracking(aramexLogisticService, waybill);
    }

    @Test
    @Disabled("DEVALAN-1650")
    @Transactional
    @Rollback(value = false)
    public void _00_createAramexWaybillByNumber() {
        order = createOrder(
                "Dubai", "UAE", null, null,
                "Dubai", "UAE", null, null);

        final UUID uuid = UUID.randomUUID();
        final DestinationType pickupDestinationType = DestinationType.SELLER;
        final DestinationType deliveryDestinationType = DestinationType.OFFICE;
        final String waybillNumber = "test-aramex-" + uuid;
        final SaleConfirmedEvent e = new SaleConfirmedEvent(
                order.getId(),
                "",
                orderIds,
                pickupDestinationType,
                deliveryDestinationType,
                3L,
                null,
                uuid.toString(),
                AramexLogisticService.NAME,
                waybillNumber,
                false);
        commonLogisticService.createOrderAndWaybill(e);

        final Waybill waybill = findWaybill(waybillNumber);

        Assertions.assertNotNull(waybill.getExternalSystemId());
        Assertions.assertEquals(waybillNumber, waybill.getExternalSystemId());
        Assertions.assertEquals(pickupDestinationType, waybill.getPickupDestinationType());
        Assertions.assertEquals(deliveryDestinationType, waybill.getDeliveryDestinationType());
        Assertions.assertEquals(AramexLogisticService.NAME, waybill.getDeliveryCompany().getName());
        Assertions.assertEquals(order.calcProfitOfSeller().longValue(), waybill.getDeclaredValue().longValue());
        Assertions.assertEquals(Integer.valueOf(1), waybill.getQuantity());
    }

    /**
     * Test storing tracking results in waybill, order and waybill history (audit) tables.
     */
    @Test
    public void _01_saveLogisticStateDeliveries() {
        callInTransaction.runInNewTransaction(() -> {
            cleanUpWaybillIDs.forEach(it -> {
                Order order2remove4m = waybillRepository.getOne(it).getOrder();
                fiscalReceiptRequestService.deleteReceiptRequest(order2remove4m, FiscalReceiptRequestType.DELIVERY_PAYMENT);
                fiscalReceiptRequestService.createReceiptRequest(order2remove4m, FiscalReceiptRequestKind.ADVANCE_RECEIPT);
            });
        });
        callInTransaction.runInNewTransaction(() -> cleanUpWaybillIDs.forEach(this::saveLogisticStateDeliveries));
    }

    private void saveLogisticStateDeliveries(Long waybillId) {
        final String status1 = "NEW";
        final String trackingState1 = status1 + " - State";
        final String trackingComment1 = status1 + " - Comment";
        final LocalDateTime trackingTime1 = LocalDateTime.of(2022, 10, 1, 0, 0, 0);
        final LogisticStateDeliveryDTO state1 = new LogisticStateDeliveryDTO();
        state1.setWaybillId(waybillId);
        state1.setWaybillInnerDeliveryStatusId(status1);
        state1.setDeliveryState(DeliveryState.JUST_CREATED);
        state1.setTrackingState(trackingState1);
        state1.setTrackingComment(trackingComment1);
        state1.setTrackingTime(trackingTime1);

        final String status2 = "DELIVERY";
        final String trackingState2 = status2 + " - State";
        final String trackingComment2 = status2 + " - Comment";
        final LocalDateTime trackingTime2 = LocalDateTime.of(2022, 10, 2, 0, 0, 0);
        final LogisticStateDeliveryDTO state2 = new LogisticStateDeliveryDTO();
        state2.setWaybillId(waybillId);
        state2.setWaybillInnerDeliveryStatusId(status2);
        state2.setDeliveryState(DeliveryState.FROM_OFFICE_TO_BUYER);
        state2.setTrackingState(trackingState2);
        state2.setTrackingComment(trackingComment2);
        state2.setTrackingTime(trackingTime2);

        final String status3 = "COMPLETED";
        final String trackingState3 = status3 + " - State";
        final String trackingComment3 = status3 + " - Comment";
        final LogisticStateDeliveryDTO state3 = new LogisticStateDeliveryDTO();
        state3.setWaybillId(waybillId);
        state3.setWaybillInnerDeliveryStatusId(status3);
        state3.setDeliveryState(DeliveryState.DELIVERED_TO_BUYER);
        state3.setTrackingState(trackingState3);
        state3.setTrackingComment(trackingComment3);
        state3.setTrackingTime(trackingTime2);
        state3.setSequenceNumber(1);

        commonLogisticService.saveLogisticStateDeliveries(Arrays.asList(state3, state2, state1));

        // Check waybill and order state
        final Waybill waybill = waybillRepository.getOne(waybillId);
        Assertions.assertEquals(status3, waybill.getInnerDeliveryStatusId());
        Assertions.assertEquals(trackingState3, waybill.getTrackingState());
        Assertions.assertEquals(trackingComment3, waybill.getTrackingComment());
        Assertions.assertEquals(trackingTime2, waybill.getTrackingTime());
        Assertions.assertEquals(DeliveryState.DELIVERED_TO_BUYER, waybill.getOrder().getDeliveryState());

        // Check waybill audit records
        final AuditReader auditReader = AuditReaderFactory.get(jpaContext.getEntityManagerByManagedType(Waybill.class));
        final List<Number> revisions = auditReader.getRevisions(Waybill.class, waybill.getId());
        // We expect 3 revisions:
        //  1. record inserted
        //  2. record updated to "NEW" state
        //  3. record updated to "DELIVERY" state
        //  4. record updated to "COMPLETED" state
        Assertions.assertEquals(4, revisions.size());
        final Waybill waybillRevision1 = auditReader.find(Waybill.class, waybillId, revisions.get(1));
        Assertions.assertEquals(status1, waybillRevision1.getInnerDeliveryStatusId());
        Assertions.assertEquals(trackingState1, waybillRevision1.getTrackingState());
        Assertions.assertEquals(trackingComment1, waybillRevision1.getTrackingComment());
        Assertions.assertEquals(trackingTime1, waybillRevision1.getTrackingTime());
        final Waybill waybillRevision2 = auditReader.find(Waybill.class, waybillId, revisions.get(2));
        Assertions.assertEquals(status2, waybillRevision2.getInnerDeliveryStatusId());
        Assertions.assertEquals(trackingState2, waybillRevision2.getTrackingState());
        Assertions.assertEquals(trackingComment2, waybillRevision2.getTrackingComment());
        Assertions.assertEquals(trackingTime2, waybillRevision2.getTrackingTime());
        final Waybill waybillRevision3 = auditReader.find(Waybill.class, waybillId, revisions.get(3));
        Assertions.assertEquals(status3, waybillRevision3.getInnerDeliveryStatusId());
        Assertions.assertEquals(trackingState3, waybillRevision3.getTrackingState());
        Assertions.assertEquals(trackingComment3, waybillRevision3.getTrackingComment());
        Assertions.assertEquals(trackingTime2, waybillRevision3.getTrackingTime());
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _02_deleteCreatedData() {
        if (productsForOrders != null) {
            for (Product product : productsForOrders) {
                product.setSeller(userService.getUserById(realSellerId).orElse(null));
                productRepository.saveAndFlush(product);
            }
        }
        for (Long id: cleanUpWaybillIDs){
            Waybill waybill = waybillRepository.getOne(id);
            List<OrderWaybills> orderWaybills = orderWaybillsRepository.findAllByWaybill(waybill);
            orderWaybillsRepository.deleteAll(orderWaybills);
            waybillRepository.delete(waybill);
        }
        for (Long id: cleanUpWaybillOrderIDs){
            waybillOrderRepository.deleteById(id);
        }

        orderStateChangeRepository.deleteAll();

        for (Long cleanOrderId: cleanUpOrdersIDs) {
            Order cleanOrder = orderRepository.getOne(cleanOrderId);
            List<AdminAlert> alerts = adminAlertRepository.findAllAlertsByObjectId(cleanOrder.getId(), "AdminOrderAlert");
            adminAlertRepository.deleteAll(alerts);

            orderRepository.delete(cleanOrder);
        }
    }

    @NonNull
    private Waybill findWaybill(String commentOrExternalSystemId) {
        Optional<Waybill> waybillOptional = waybillRepository.findOne(QWaybill.waybill.comment.startsWith(commentOrExternalSystemId));
        if (!waybillOptional.isPresent()) {
            waybillOptional = waybillRepository.findOne(QWaybill.waybill.externalSystemId.eq(commentOrExternalSystemId));
        }
        Assertions.assertTrue(waybillOptional.isPresent());
        Waybill waybill = waybillOptional.get();
        cleanUpWaybillIDs.add(waybill.getId());
        cleanUpWaybillOrderIDs.add(waybill.getWaybillOrder().getId());
        return waybill;
    }

    private static void checkTracking(LogisticService logisticService, Waybill waybill) {
        List<LogisticStateDeliveryDTO> logisticStateDeliveryDTOList = logisticService.trackingStateDelivery(
                Collections.singletonList(waybill));
        Assertions.assertTrue(logisticStateDeliveryDTOList.size() >= 1);
        Assertions.assertTrue(logisticStateDeliveryDTOList.size() <= 2);
        for (LogisticStateDeliveryDTO logisticStateDeliveryDTO : logisticStateDeliveryDTOList) {
            Assertions.assertEquals(waybill.getId(), logisticStateDeliveryDTO.getWaybillId());
            Assertions.assertNotNull(logisticStateDeliveryDTO.getWaybillInnerDeliveryStatusId());
            Assertions.assertNotNull(logisticStateDeliveryDTO.getTrackingState());
            Assertions.assertNotNull(logisticStateDeliveryDTO.getTrackingComment());
            Assertions.assertNotNull(logisticStateDeliveryDTO.getTrackingTime());
        }
    }
}
