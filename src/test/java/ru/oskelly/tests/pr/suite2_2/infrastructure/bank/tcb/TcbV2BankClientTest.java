package ru.oskelly.tests.pr.suite2_2.infrastructure.bank.tcb;

import lombok.SneakyThrows;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.mockserver.integration.ClientAndServer;
import org.mockserver.model.HttpRequest;
import org.mockserver.model.HttpResponse;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.infrastructure.bank.impl.tcb.TcbV2BankClient;
import su.reddot.infrastructure.bank.impl.tcb.TcbV2Configuration;
import su.reddot.infrastructure.bank.impl.tcb.request.RegisterHoldRequest;
import su.reddot.infrastructure.bank.impl.tcb.request.RegisterQrcRequest;
import su.reddot.infrastructure.bank.impl.tcb.response.*;
import su.reddot.infrastructure.bank.impl.tcb.response.card.RegisterCardBeginResponse;

import java.math.BigDecimal;
import java.net.URL;
import java.time.LocalDate;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static ru.oskelly.tests.TestUtils.sleep;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class TcbV2BankClientTest {

    static TcbV2BankClient client;
    static ClientAndServer mockServer;
    static URL localBindAddress;


    @BeforeAll
    public static void setup() throws Exception {

        TcbV2Configuration cfg = new TcbV2Configuration();
        cfg.setLogin("T2873101412ID");
        cfg.setSigningKey("zBeOj2mGi0PEMS0uDU68pAbwXt17UqCJ0ESM6vgHBaQKhmNMPj5263K7zS0ZanV2ygOgaowhOyGpUBoMQqtLuJL53TmpErHopWkaHFMAWXpHwvn7DoiU3kHZERmqbhNw0ta6qoekO1XGtdJJFoUrhXueovEZtvyOj1QdvqSpi7gbwbFBhNTXwwpd84T4So1sCiwAwFnqFFhjRsRmYdSPLLrXNNezGXF3fXHnPfeMzDs0oevQoGfPFmQ11HWSAWTR");
        cfg.setApiEndpoint("https://paytest.online.tkbbank.ru/api/v1");
        cfg.setApiBusinessEndpoint("https://paytest.online.tkbbank.ru/api/interfaces/business");
        client = new TcbV2BankClient(cfg, null);

        localBindAddress = new URL("http", "localhost", 18888, "/");
        mockServer = ClientAndServer.startClientAndServer(localBindAddress.getPort());
    }


    @AfterAll
    public static void stop() {
        mockServer.stop();
    }

    // uncomment the test  to get new cardRefId
    //@Test
    public void bindCard() throws Exception {
        String id = UUID.randomUUID().toString();
        RegisterCardBeginResponse response = client.executeBindCard(id, localBindAddress.toString());
        waitForUserToCompleteFormEntry("Visit the URL below and enter card info for CARD BIND:\n >>> %s", response.getFormURL());
        GetOrderStateResponse bindResponse = client.checkOrderState(id);
        assertNotNull(bindResponse);
        System.out.printf("Received response: %s", response);

    }

    @Test
    public void basicTest() {
        Assertions.assertNotSame(BigDecimal.ZERO, client.getBalance());
    }

//    @Test
    public void holdAndReverse() throws Exception {
        String id = holdAmountAndCheck(10_00L);

        ReverseHoldResponse reverseHoldResponse = client.executeHoldReverse(10_00, id);

        assertNotNull(reverseHoldResponse);
        assertNotNull(reverseHoldResponse.getTcbOperationId());

        sleep(10);
        HoldStatusResponse holdStatusCheck = client.checkHoldStatus(reverseHoldResponse.getExtId());

        assertNotNull(holdStatusCheck);
        assertNotNull(holdStatusCheck.getOrderInfo());
        assertTrue(holdStatusCheck.isOperationCompleted());
        assertTrue(holdStatusCheck.isReverseSuccess()); // hold no longer valid
    }

//    @Test
    public void holdAndComplete() throws Exception {
        String id = holdAmountAndCheck(200_00L);
        long completeAmount = 100_00L;

        CompleteHoldResponse holdComplete = client.executeHoldComplete(completeAmount, id);

        assertNotNull(holdComplete);
        assertNotNull(holdComplete.getTcbOperationId());

        sleep(10);
        HoldStatusResponse holdStatusCheck = client.checkHoldStatus(holdComplete.getExtId());

        assertNotNull(holdStatusCheck);
        assertTrue(holdStatusCheck.isOperationCompleted());
        assertNotNull(holdStatusCheck.getOrderInfo());
        assertFalse(holdStatusCheck.isOperationSuccessful()); // hold no longer valid
        assertTrue(holdStatusCheck.isDebitSuccess());
    }

//    @Test
    public void holdAndCompleteThenRefund() throws Exception {
        String id = holdAmountAndCheck(20_00L);
        long completeAmount = 10_00L;

        CompleteHoldResponse holdComplete = client.executeHoldComplete(completeAmount, id);

        assertNotNull(holdComplete);
        assertNotNull(holdComplete.getTcbOperationId());

        sleep(10);
        HoldStatusResponse holdStatusCheck = client.checkHoldStatus(holdComplete.getExtId());

        assertNotNull(holdStatusCheck);
        assertNotNull(holdStatusCheck.getOrderInfo());
        assertTrue(holdStatusCheck.isOperationCompleted());
        assertTrue(holdStatusCheck.isDebitSuccess());

        RefundOrderResponse refundResponse = client.executeRefundOrder(id, completeAmount, "Test refund operation of 10.00 RUB");
        assertNotNull(refundResponse);
        assertNotNull(refundResponse.getTcbOperationId());

        GetOrderStateResponse refundOperation = client.checkOrderState(refundResponse.getExtId());
        assertNotNull(refundOperation);
        assertFalse(refundOperation.isOperationCompleted()); // should be pending
    }

//    @Test
    public void orderStatusCheckManual() {
        GetOrderStateResponse check = client.checkOrderState("f417ff1f-6d54-4f36-8a5b-e960ca450265RVS");
        System.out.println(check);
    }

//    @Test
    public void testTransferWithNegativeAmount() throws Exception {
        BaseTcbApiResponse response =  client.transferMoneyToPaymentAccount(
                "40817810238100000000",
                "*********",
                "**********",
                "ООО \"ОСКЕЛИ ГРУПП\"",
                "Testing negative payout",
                1L,
                -2000,
                UUID.randomUUID()
        );

        System.out.println(response);

    }

    @Test
    @SneakyThrows
    public void getRegistryOkay() {
        client.getRegistry(LocalDate.of(2022, 8, 21));
    }

    @SneakyThrows
    public void holdOrderWithSbp() {
        String id = UUID.randomUUID().toString();
        RegisterQrcRequest registerQrcRequest = RegisterQrcRequest.builder()
                .extId(id)
                .amount(100)
                .description("Payment 4 test orders")
                .expirationMinutes(5L)
                .build();
        RegisterQrcResponse registerQrcResponse = client.executeQrc(registerQrcRequest);
        waitForUserToCompleteFormEntry("Visit the URL below:\n>>> %s", registerQrcResponse.getQrcUrl());
    }

    private String holdAmountAndCheck(long amount) throws Exception {
        String id = UUID.randomUUID().toString();
        RegisterHoldResponse response = client.executeHold(
                RegisterHoldRequest.builder()
                        .amount(amount)
                        .returnURL(localBindAddress.toString())
                        .extId(id)
                        .description("Test hold operation of 10.00 RUB")
                        .build());
        assertNotNull(response);
        assertNotNull(response.getFormURL());

        waitForUserToCompleteFormEntry("Visit the URL below and enter card info for HOLD:\n>>> %s", response.getFormURL());
        HoldStatusResponse holdStatusCheck = client.checkHoldStatus(id);
        assertNotNull(holdStatusCheck);
        assertTrue(holdStatusCheck.isOperationCompleted());
        assertTrue(holdStatusCheck.isOperationSuccessful());
        assertNotNull(holdStatusCheck.getOrderInfo());
        return id;
    }

    private void waitForUserToCompleteFormEntry(String prompt, Object... params) throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(1);

        mockServer
                .when(HttpRequest.request().withMethod("GET").withPath(localBindAddress.getPath()))
                .respond(
                        httpRequest -> {
                            latch.countDown();
                            return HttpResponse.response("Thank you, you can close the window now!");
                        }
                );

        System.out.println();
        System.out.printf(prompt, params);
        System.out.println();

        latch.await(15, TimeUnit.MINUTES);
    }
}
