package ru.oskelly.tests.pr.suite2_2.infrastructure.logistic.aramex.client;

import javax.annotation.Nullable;

import ch.qos.logback.classic.Level;
import lombok.NonNull;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.infrastructure.logistic.aramex.AramexConfiguration;
import su.reddot.infrastructure.logistic.aramex.client.AramexLocationClient;
import su.reddot.infrastructure.logistic.aramex.client.AramexShippingClient;
import su.reddot.infrastructure.logistic.aramex.client.AramexTrackingClient;
import su.reddot.infrastructure.logistic.aramex.client.soap.AramexLocationSoapClient;
import su.reddot.infrastructure.logistic.aramex.client.soap.AramexShippingSoapClient;
import su.reddot.infrastructure.logistic.aramex.client.soap.AramexTrackingSoapClient;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Disabled;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;

@Disabled("DEVALAN-1650")
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class AramexSoapClientTest extends AramexClientTest {

    private static Level messageTracingLevelBeforeTheTest;

    @BeforeAll
    public static void setMessageTracingLevel() {
        final ch.qos.logback.classic.Logger logger = getMessageTracingLogger();
        if (logger != null) {
            messageTracingLevelBeforeTheTest = logger.getLevel();
            // It is needed to log SOAP messages
            logger.setLevel(Level.TRACE);
        }
    }

    @AfterAll
    public static void restoreMessageTracingLevel() {
        final ch.qos.logback.classic.Logger logger = getMessageTracingLogger();
        if (logger != null) {
            // Restore log level as we changed it before
            logger.setLevel(messageTracingLevelBeforeTheTest);
        }
    }

    @Nullable
    private static ch.qos.logback.classic.Logger getMessageTracingLogger() {
        final Logger logger = LoggerFactory.getLogger("org.springframework.ws.client.MessageTracing");
        return logger instanceof ch.qos.logback.classic.Logger
                ? (ch.qos.logback.classic.Logger)logger
                : null;
    }

    @Override
    @NonNull
    protected AramexShippingClient createShippingClient(@NonNull final AramexConfiguration configuration) {
        final AramexShippingSoapClient client = new AramexShippingSoapClient(configuration);
        setUpClient(client, configuration.getShippingEndpoint(), "aramex.shipping.wsdl");
        return client;
    }

    @Override
    @NonNull
    protected AramexTrackingClient createTrackingClient(@NonNull final AramexConfiguration configuration) {
        final AramexTrackingSoapClient client = new AramexTrackingSoapClient(configuration);
        setUpClient(client, configuration.getTrackingEndpoint(), "aramex.tracking.wsdl");
        return client;
    }

    @Override
    @NonNull
    protected AramexLocationClient createLocationClient(@NonNull final AramexConfiguration configuration) {
        final AramexLocationSoapClient client = new AramexLocationSoapClient(configuration);
        setUpClient(client, configuration.getLocationEndpoint(), "aramex.location.wsdl");
        return client;
    }

    private static void setUpClient(
            @NonNull final WebServiceGatewaySupport client,
            @NonNull final String endpoint,
            @NonNull final String contextPath) {
        client.setDefaultUri(endpoint);
        final Jaxb2Marshaller marshaller = new Jaxb2Marshaller();
        marshaller.setContextPath(contextPath);
        client.setMarshaller(marshaller);
        client.setUnmarshaller(marshaller);
    }
}
