package ru.oskelly.tests.pr.suite2_2.infrastructure.fiscalreceipts;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableList;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.jupiter.api.*;
import org.mockserver.client.MockServerClient;
import org.mockserver.integration.ClientAndServer;
import org.mockserver.mock.action.ExpectationResponseCallback;
import org.mockserver.model.Header;
import org.mockserver.model.HttpRequest;
import org.mockserver.model.HttpResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.fiscalreceipt.FiscalReceiptRequestRepository;
import su.reddot.domain.model.counterparty.Counterparty;
import su.reddot.domain.model.counterparty.CounterpartyType;
import su.reddot.domain.model.counterparty.JurCounterparty;
import su.reddot.domain.model.fiscalreceiptrequest.FiscalReceiptRequest;
import su.reddot.domain.model.fiscalreceiptrequest.FiscalReceiptRequestKind;
import su.reddot.domain.model.fiscalreceiptrequest.FiscalReceiptRequestState;
import su.reddot.domain.model.fiscalreceiptrequest.FiscalReceiptRequestType;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.counterparty.CounterpartyService;
import su.reddot.domain.service.dto.FiscalReceiptRequestDTO;
import su.reddot.domain.service.fiscalreceiptrequest.FiscalReceiptRequestService;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.bank.commons.BankCommons;
import su.reddot.infrastructure.cashregister.CashRegister;
import su.reddot.infrastructure.cashregister.impl.starrys.type.BuyerCheckRequest;
import su.reddot.infrastructure.logistic.DeliveryState;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.Clock;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.assertj.core.api.Assertions;
import su.reddot.infrastructure.util.CallInTransaction;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockserver.integration.ClientAndServer.startClientAndServer;
import static org.mockserver.model.HttpRequest.request;
import static org.mockserver.model.HttpResponse.response;
import static org.mockserver.model.HttpStatusCode.OK_200;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class FiscalReceiptsServiceTest extends AbstractSpringTest {

    @Value("${test.receipts.send-to-email-1st}")
    private String sendReceiptToEmail1St;
    @Value("${test.receipts.send-to-email-2nd}")
    private String sendReceiptToEmail2Nd;

    @Value("${test.receipts.mock-server-host}")
    private String mockServerHost;

    @Value("${test.receipts.mock-server-receipts-port}")
    private Integer mockReceiptsServerPort;
    @Value("${test.receipts.mock-server-b2p-bank-port}")
    private Integer mockB2PServerPort;
    @Value("${test.receipts.mock-server-tcb-bank-port}")
    private Integer mockTcbServerPort;

    @Value("${test.receipts.order-id-with-1-positions}")
    private Long orderIdWith1Positions;
    // Usual receipt with full data
    private static final String VERIFY_REQUEST_WITH_1_POSITIONS = "{\"device\":\"auto\",\"requestId\":\"1118013\",\"password\":**********,\"clientId\":\"noclientid\",\"documentType\":0,\"lines\":[{\"qty\":1000,\"price\":720000,\"payAttribute\":4,\"taxId\":4,\"description\":\"Повседневное платье 98621\",\"lineAttribute\":1},{\"qty\":1000,\"price\":0,\"payAttribute\":4,\"taxId\":4,\"description\":\"Доставка \",\"lineAttribute\":4}],\"nonCash\":[720000,0,0],\"place\":\"NO-PLACE\",\"fullResponse\":true,\"correction\":false,\"correctionType\":0}";
    private static final String MOCK_RESPONSE_WITH_1_POSITION = "{\"ClientId\":\"noclientid\",\"Date\":{\"Date\":{\"Day\":5,\"Month\":12,\"Year\":21},\"Time\":{\"Hour\":21,\"Minute\":1,\"Second\":2}},\"Device\":{\"Name\":\"****************3333\",\"Address\":\"************:4444\"},\"DeviceRegistrationNumber\":\"0000000001045358\",\"DeviceSerialNumber\":\"****************3333\",\"DocNumber\":17,\"DocumentType\":0,\"FNSerialNumber\":\"9999078902010068\",\"FiscalDocNumber\":6383,\"FiscalSign\":2440876882,\"GrandTotal\":720000,\"MarkingInfo\":{},\"Path\":\"/fr/api/v2/Complex\",\"QR\":\"t=20211205T2101\\u0026s=7200.00\\u0026fn=9999078902010068\\u0026i=6383\\u0026fp=2440876882\\u0026n=1\",\"RequestId\":\"1118013\",\"Response\":{\"Error\":0},\"Responses\":[{\"Path\":\"/fr/api/v2/CloseDocument\",\"Response\":{\"CorrectionType\":0,\"Date\":{\"Date\":{\"Day\":5,\"Month\":12,\"Year\":21},\"Time\":{\"Hour\":21,\"Minute\":1,\"Second\":2}},\"DocNumber\":17,\"DocumentType\":0,\"Error\":0,\"FiscalDocNumber\":6383,\"FiscalDocument\":{\"TagID\":3,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":1000,\"TagType\":\"string\",\"Value\":\"Кассовый чек\"},{\"TagID\":1054,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1055,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1031,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1081,\"TagType\":\"money\",\"Value\":720000},{\"TagID\":9997,\"TagType\":\"money\",\"Value\":0},{\"TagID\":9996,\"TagType\":\"money\",\"Value\":720000},{\"TagID\":1020,\"TagType\":\"money\",\"Value\":720000},{\"TagID\":1196,\"TagType\":\"string\",\"Value\":\"QR\"},{\"TagID\":1215,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1216,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1217,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1060,\"TagType\":\"string\",\"Value\":\"www.nalog.ru\"},{\"TagID\":1108,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1036,\"TagType\":\"string\",\"Value\":\"****************3333\"},{\"TagID\":1209,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1048,\"TagType\":\"string\",\"Value\":\"ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ \\\"ДЕЛОВЫЕ ЛИНИИ-Тест\\\"\"},{\"TagID\":1018,\"TagType\":\"string\",\"Value\":\"7826156685  \"},{\"TagID\":1012,\"TagType\":\"unixtime\",\"Value\":\"2021-12-05T21:01:00Z\"},{\"TagID\":1037,\"TagType\":\"string\",\"Value\":\"0000000001045358    \"},{\"TagID\":1009,\"TagType\":\"string\",\"Value\":\"Россия, город Москва, улица Шарикоподшипниковская, дом 11, строение 9, индекс 115088\"},{\"TagID\":1187,\"TagType\":\"string\",\"Value\":\"NO-PLACE\"},{\"TagID\":1105,\"TagType\":\"money\",\"Value\":720000},{\"TagID\":1059,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":2102,\"TagType\":\"byte\",\"Value\":0},{\"TagID\":1079,\"TagType\":\"money\",\"Value\":720000},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":720000},{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":6},{\"TagID\":1030,\"TagType\":\"string\",\"Value\":\"Повседневное платье 98621\"},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1212,\"TagType\":\"byte\",\"Value\":1}]},{\"TagID\":1059,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":2102,\"TagType\":\"byte\",\"Value\":0},{\"TagID\":1079,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":6},{\"TagID\":1030,\"TagType\":\"string\",\"Value\":\"Доставка\"},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1212,\"TagType\":\"byte\",\"Value\":4}]},{\"TagID\":1041,\"TagType\":\"string\",\"Value\":\"9999078902010068\"},{\"TagID\":1040,\"TagType\":\"uint32\",\"Value\":6383},{\"TagID\":1077,\"TagType\":\"byte[]\",\"Value\":\"MQSRfNNS\"},{\"TagID\":1038,\"TagType\":\"uint32\",\"Value\":49},{\"TagID\":1042,\"TagType\":\"uint32\",\"Value\":17}]},\"FiscalSign\":2440876882,\"GrandTotal\":720000,\"MarkingInfo\":{},\"NonCash\":[720000,0,0],\"Password\":30,\"PaymentAgentModes\":0,\"Place\":\"NO-PLACE\",\"QR\":\"t=20211205T2101\\u0026s=7200.00\\u0026fn=9999078902010068\\u0026i=6383\\u0026fp=2440876882\\u0026n=1\",\"TaxCalculationMethod\":1,\"TaxMode\":0,\"Text\":\"КАССОВЫЙ ЧЕК/ПРИХОД\\t05-12-21 21:01\\nОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ \\\"ДЕЛОВЫЕ ЛИНИИ-Тест\\\"\\nРоссия, город Москва, улица Шарикоподшипниковская, дом 11, строение 9, индекс 115088\\nМЕСТО РАСЧЕТОВ\\tNO-PLACE\\nАВТОМАТ\\t****************3333\\nПовседневное платье 98621\\nПОЛНЫЙ РАСЧЕТ\\t1 x 7200.00 ~7200.00\\nТОВАР\\nДоставка\\nПОЛНЫЙ РАСЧЕТ\\t1 x 0.00 ~0.00\\nУСЛУГА\\n##BIG##ИТОГ\\t~7200.00\\nВСЕГО ПОЛУЧЕНО\\t~7200.00\\nЭЛЕКТРОННЫМИ\\t~7200.00\\nСУММА БЕЗ НДС\\t~7200.00\\nККТ ДЛЯ ИНТЕРНЕТ\\nИНН\\t7826156685\\nСНО\\tОСН\\nЧЕК: 17\\tСМЕНА: 49\\nСАЙТ ФНС\\twww.nalog.ru\\nРН ККТ: 0000000001045358\\tФН: 9999078902010068\\nФД: 6383\\tФП: 2440876882\",\"TurnNumber\":49}}],\"Text\":\"КАССОВЫЙ ЧЕК/ПРИХОД\\t05-12-21 21:01\\nОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ \\\"ДЕЛОВЫЕ ЛИНИИ-Тест\\\"\\nРоссия, город Москва, улица Шарикоподшипниковская, дом 11, строение 9, индекс 115088\\nМЕСТО РАСЧЕТОВ\\tNO-PLACE\\nАВТОМАТ\\t****************3333\\nПовседневное платье 98621\\nПОЛНЫЙ РАСЧЕТ\\t1 x 7200.00 ~7200.00\\nТОВАР\\nДоставка\\nПОЛНЫЙ РАСЧЕТ\\t1 x 0.00 ~0.00\\nУСЛУГА\\n##BIG##ИТОГ\\t~7200.00\\nВСЕГО ПОЛУЧЕНО\\t~7200.00\\nЭЛЕКТРОННЫМИ\\t~7200.00\\nСУММА БЕЗ НДС\\t~7200.00\\nККТ ДЛЯ ИНТЕРНЕТ\\nИНН\\t7826156685\\nСНО\\tОСН\\nЧЕК: 17\\tСМЕНА: 49\\nСАЙТ ФНС\\twww.nalog.ru\\nРН ККТ: 0000000001045358\\tФН: 9999078902010068\\nФД: 6383\\tФП: 2440876882\",\"TurnNumber\":49}";
    // Delivery receipt, Advance
    private static final String VERIFY_REQUEST_WITH_ADVANCE_RECEIPT = "{\"phoneOrEmail\":\"<EMAIL>\",\"emailOfDeviceUser\":\"<EMAIL>\",\"clientId\":\"noclientid\",\"documentType\":0,\"nonCash\":[35000,0,0],\"fullResponse\":true,\"password\":**********,\"requestId\":\"1118013-advance\",\"taxMode\":1,\"correctionType\":0,\"place\":\"NO-PLACE\",\"lines\":[{\"lineAttribute\":10,\"payAttribute\":3,\"price\":35000,\"taxId\":4,\"qty\":1000,\"description\":\"Доставка (заказ 1118013)\"}],\"correction\":false,\"device\":\"auto\",\"group\":\"0a0bdc20-bff4-4d69-8b4c-6c20b8c5b60d\"}";
    private static final String MOCK_RESPONSE_FOR_ADVANCE_RECEIPT = "{\"ClientId\":\"noclientid\",\"Date\":{\"Date\":{\"Day\":8,\"Month\":12,\"Year\":21},\"Time\":{\"Hour\":20,\"Minute\":38,\"Second\":42}},\"Device\":{\"Name\":\"****************3333\",\"Address\":\"************:4444\"},\"DeviceRegistrationNumber\":\"0000000001045358\",\"DeviceSerialNumber\":\"****************3333\",\"DocNumber\":129,\"DocumentType\":0,\"FNSerialNumber\":\"9999078902010068\",\"FiscalDocNumber\":7666,\"FiscalSign\":3790305876,\"GrandTotal\":35000,\"MarkingInfo\":{},\"Path\":\"/fr/api/v2/Complex\",\"QR\":\"t=20211208T2038\\u0026s=350.00\\u0026fn=9999078902010068\\u0026i=7666\\u0026fp=3790305876\\u0026n=1\",\"RequestId\":\"1118013UNIQUE\",\"Response\":{\"Error\":0},\"Responses\":[{\"Path\":\"/fr/api/v2/CloseDocument\",\"Response\":{\"CorrectionType\":0,\"Date\":{\"Date\":{\"Day\":8,\"Month\":12,\"Year\":21},\"Time\":{\"Hour\":20,\"Minute\":38,\"Second\":42}},\"DocNumber\":129,\"DocumentType\":0,\"Error\":0,\"FiscalDocNumber\":7666,\"FiscalDocument\":{\"TagID\":3,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":1000,\"TagType\":\"string\",\"Value\":\"Кассовый чек\"},{\"TagID\":1054,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1055,\"TagType\":\"byte\",\"Value\":2},{\"TagID\":1031,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1081,\"TagType\":\"money\",\"Value\":35000},{\"TagID\":9997,\"TagType\":\"money\",\"Value\":0},{\"TagID\":9996,\"TagType\":\"money\",\"Value\":35000},{\"TagID\":1020,\"TagType\":\"money\",\"Value\":35000},{\"TagID\":1196,\"TagType\":\"string\",\"Value\":\"QR\"},{\"TagID\":1215,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1216,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1217,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1060,\"TagType\":\"string\",\"Value\":\"www.nalog.ru\"},{\"TagID\":1108,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1036,\"TagType\":\"string\",\"Value\":\"****************3333\"},{\"TagID\":1209,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1048,\"TagType\":\"string\",\"Value\":\"ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ \\\"ДЕЛОВЫЕ ЛИНИИ-Тест\\\"\"},{\"TagID\":1018,\"TagType\":\"string\",\"Value\":\"7826156685  \"},{\"TagID\":1012,\"TagType\":\"unixtime\",\"Value\":\"2021-12-08T20:38:00Z\"},{\"TagID\":1037,\"TagType\":\"string\",\"Value\":\"0000000001045358    \"},{\"TagID\":1009,\"TagType\":\"string\",\"Value\":\"Россия, город Москва, улица Шарикоподшипниковская, дом 11, строение 9, индекс 115088\"},{\"TagID\":1187,\"TagType\":\"string\",\"Value\":\"NO-PLACE\"},{\"TagID\":1105,\"TagType\":\"money\",\"Value\":35000},{\"TagID\":1008,\"TagType\":\"string\",\"Value\":\"<EMAIL>\"},{\"TagID\":1059,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":2102,\"TagType\":\"byte\",\"Value\":0},{\"TagID\":1079,\"TagType\":\"money\",\"Value\":35000},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":35000},{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":6},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":3},{\"TagID\":1212,\"TagType\":\"byte\",\"Value\":4}]},{\"TagID\":1041,\"TagType\":\"string\",\"Value\":\"9999078902010068\"},{\"TagID\":1040,\"TagType\":\"uint32\",\"Value\":7666},{\"TagID\":1077,\"TagType\":\"byte[]\",\"Value\":\"MQTh63pU\"},{\"TagID\":1038,\"TagType\":\"uint32\",\"Value\":54},{\"TagID\":1042,\"TagType\":\"uint32\",\"Value\":129}]},\"FiscalSign\":3790305876,\"GrandTotal\":35000,\"MarkingInfo\":{},\"NonCash\":[35000,0,0],\"Password\":30,\"PaymentAgentModes\":0,\"Place\":\"NO-PLACE\",\"QR\":\"t=20211208T2038\\u0026s=350.00\\u0026fn=9999078902010068\\u0026i=7666\\u0026fp=3790305876\\u0026n=1\",\"TaxCalculationMethod\":1,\"TaxMode\":0,\"Text\":\"КАССОВЫЙ ЧЕК/ПРИХОД\\t08-12-21 20:38\\nОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ \\\"ДЕЛОВЫЕ ЛИНИИ-Тест\\\"\\nРоссия, город Москва, улица Шарикоподшипниковская, дом 11, строение 9, индекс 115088\\nМЕСТО РАСЧЕТОВ\\tNO-PLACE\\nАВТОМАТ\\t****************3333\\nАВАНС\\t1 x 350.00 ~350.00\\nУСЛУГА\\n##BIG##ИТОГ\\t~350.00\\nВСЕГО ПОЛУЧЕНО\\t~350.00\\nЭЛЕКТРОННЫМИ\\t~350.00\\nСУММА БЕЗ НДС\\t~350.00\\nККТ ДЛЯ ИНТЕРНЕТ\\nЭЛ.АДР.ПОКУПАТЕЛЯ\\<EMAIL>\\nИНН\\t7826156685\\nСНО\\tУСН ДОХОД\\nЧЕК: 129\\tСМЕНА: 54\\nСАЙТ ФНС\\twww.nalog.ru\\nРН ККТ: 0000000001045358\\tФН: 9999078902010068\\nФД: 7666\\tФП: 3790305876\",\"TurnNumber\":54}}],\"Text\":\"КАССОВЫЙ ЧЕК/ПРИХОД\\t08-12-21 20:38\\nОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ \\\"ДЕЛОВЫЕ ЛИНИИ-Тест\\\"\\nРоссия, город Москва, улица Шарикоподшипниковская, дом 11, строение 9, индекс 115088\\nМЕСТО РАСЧЕТОВ\\tNO-PLACE\\nАВТОМАТ\\t****************3333\\nАВАНС\\t1 x 350.00 ~350.00\\nУСЛУГА\\n##BIG##ИТОГ\\t~350.00\\nВСЕГО ПОЛУЧЕНО\\t~350.00\\nЭЛЕКТРОННЫМИ\\t~350.00\\nСУММА БЕЗ НДС\\t~350.00\\nККТ ДЛЯ ИНТЕРНЕТ\\nЭЛ.АДР.ПОКУПАТЕЛЯ\\<EMAIL>\\nИНН\\t7826156685\\nСНО\\tУСН ДОХОД\\nЧЕК: 129\\tСМЕНА: 54\\nСАЙТ ФНС\\twww.nalog.ru\\nРН ККТ: 0000000001045358\\tФН: 9999078902010068\\nФД: 7666\\tФП: 3790305876\",\"TurnNumber\":54}";
    // Delivery receipt, Closure
    private static final String VERIFY_REQUEST_WITH_ADVANCE_PAYMENT_RECEIPT = "{\"phoneOrEmail\":\"<EMAIL>\",\"emailOfDeviceUser\":\"<EMAIL>\",\"clientId\":\"noclientid\",\"documentType\":0,\"nonCash\":[0,0,0],\"fullResponse\":true,\"password\":**********,\"requestId\":\"1118013-payment\",\"advancePayment\":35000,\"taxMode\":1,\"correctionType\":0,\"place\":\"NO-PLACE\",\"lines\":[{\"lineAttribute\":4,\"payAttribute\":4,\"price\":35000,\"taxId\":4,\"qty\":1000,\"description\":\"Доставка (заказ 1118013)\"}],\"correction\":false,\"device\":\"auto\",\"group\":\"0a0bdc20-bff4-4d69-8b4c-6c20b8c5b60d\"}";
    private static final String MOCK_RESPONSE_FOR_ADVANCE_PAYMENT_RECEIPT = "{\"ClientId\":\"noclientid\",\"Date\":{\"Date\":{\"Day\":8,\"Month\":12,\"Year\":21},\"Time\":{\"Hour\":21,\"Minute\":34,\"Second\":33}},\"Device\":{\"Name\":\"****************3333\",\"Address\":\"************:4444\"},\"DeviceRegistrationNumber\":\"0000000001045358\",\"DeviceSerialNumber\":\"****************3333\",\"DocNumber\":158,\"DocumentType\":0,\"FNSerialNumber\":\"9999078902010068\",\"FiscalDocNumber\":7695,\"FiscalSign\":2820188457,\"GrandTotal\":35000,\"MarkingInfo\":{},\"Path\":\"/fr/api/v2/Complex\",\"QR\":\"t=20211208T2134\\u0026s=350.00\\u0026fn=9999078902010068\\u0026i=7695\\u0026fp=2820188457\\u0026n=1\",\"RequestId\":\"1118013UNIQUE-2\",\"Response\":{\"Error\":0},\"Responses\":[{\"Path\":\"/fr/api/v2/CloseDocument\",\"Response\":{\"AdvancePayment\":35000,\"CorrectionType\":0,\"Date\":{\"Date\":{\"Day\":8,\"Month\":12,\"Year\":21},\"Time\":{\"Hour\":21,\"Minute\":34,\"Second\":33}},\"DocNumber\":158,\"DocumentType\":0,\"Error\":0,\"FiscalDocNumber\":7695,\"FiscalDocument\":{\"TagID\":3,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":1000,\"TagType\":\"string\",\"Value\":\"Кассовый чек\"},{\"TagID\":1054,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1055,\"TagType\":\"byte\",\"Value\":2},{\"TagID\":1031,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1081,\"TagType\":\"money\",\"Value\":0},{\"TagID\":9997,\"TagType\":\"money\",\"Value\":0},{\"TagID\":9996,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1020,\"TagType\":\"money\",\"Value\":35000},{\"TagID\":1196,\"TagType\":\"string\",\"Value\":\"QR\"},{\"TagID\":1215,\"TagType\":\"money\",\"Value\":35000},{\"TagID\":1216,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1217,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1060,\"TagType\":\"string\",\"Value\":\"www.nalog.ru\"},{\"TagID\":1108,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1036,\"TagType\":\"string\",\"Value\":\"****************3333\"},{\"TagID\":1209,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1048,\"TagType\":\"string\",\"Value\":\"ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ \\\"ДЕЛОВЫЕ ЛИНИИ-Тест\\\"\"},{\"TagID\":1018,\"TagType\":\"string\",\"Value\":\"7826156685  \"},{\"TagID\":1012,\"TagType\":\"unixtime\",\"Value\":\"2021-12-08T21:34:00Z\"},{\"TagID\":1037,\"TagType\":\"string\",\"Value\":\"0000000001045358    \"},{\"TagID\":1009,\"TagType\":\"string\",\"Value\":\"Россия, город Москва, улица Шарикоподшипниковская, дом 11, строение 9, индекс 115088\"},{\"TagID\":1187,\"TagType\":\"string\",\"Value\":\"NO-PLACE\"},{\"TagID\":1105,\"TagType\":\"money\",\"Value\":35000},{\"TagID\":1008,\"TagType\":\"string\",\"Value\":\"<EMAIL>\"},{\"TagID\":1059,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":2102,\"TagType\":\"byte\",\"Value\":0},{\"TagID\":1079,\"TagType\":\"money\",\"Value\":35000},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":35000},{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":6},{\"TagID\":1030,\"TagType\":\"string\",\"Value\":\"Доставка\"},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1212,\"TagType\":\"byte\",\"Value\":4}]},{\"TagID\":1041,\"TagType\":\"string\",\"Value\":\"9999078902010068\"},{\"TagID\":1040,\"TagType\":\"uint32\",\"Value\":7695},{\"TagID\":1077,\"TagType\":\"byte[]\",\"Value\":\"MQSoGKkp\"},{\"TagID\":1038,\"TagType\":\"uint32\",\"Value\":54},{\"TagID\":1042,\"TagType\":\"uint32\",\"Value\":158}]},\"FiscalSign\":2820188457,\"GrandTotal\":35000,\"MarkingInfo\":{},\"NonCash\":[0,0,0],\"Password\":30,\"PaymentAgentModes\":0,\"Place\":\"NO-PLACE\",\"QR\":\"t=20211208T2134\\u0026s=350.00\\u0026fn=9999078902010068\\u0026i=7695\\u0026fp=2820188457\\u0026n=1\",\"TaxCalculationMethod\":1,\"TaxMode\":0,\"Text\":\"КАССОВЫЙ ЧЕК/ПРИХОД\\t08-12-21 21:34\\nОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ \\\"ДЕЛОВЫЕ ЛИНИИ-Тест\\\"\\nРоссия, город Москва, улица Шарикоподшипниковская, дом 11, строение 9, индекс 115088\\nМЕСТО РАСЧЕТОВ\\tNO-PLACE\\nАВТОМАТ\\t****************3333\\nДоставка\\nПОЛНЫЙ РАСЧЕТ\\t1 x 350.00 ~350.00\\nУСЛУГА\\n##BIG##ИТОГ\\t~350.00\\nВСЕГО ПОЛУЧЕНО\\t~0.00\\nПРЕДВАРИТЕЛЬНАЯ ОПЛАТА (АВАНС)\\t~350.00\\nСУММА БЕЗ НДС\\t~350.00\\nККТ ДЛЯ ИНТЕРНЕТ\\nЭЛ.АДР.ПОКУПАТЕЛЯ\\<EMAIL>\\nИНН\\t7826156685\\nСНО\\tУСН ДОХОД\\nЧЕК: 158\\tСМЕНА: 54\\nСАЙТ ФНС\\twww.nalog.ru\\nРН ККТ: 0000000001045358\\tФН: 9999078902010068\\nФД: 7695\\tФП: 2820188457\",\"TurnNumber\":54}}],\"Text\":\"КАССОВЫЙ ЧЕК/ПРИХОД\\t08-12-21 21:34\\nОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ \\\"ДЕЛОВЫЕ ЛИНИИ-Тест\\\"\\nРоссия, город Москва, улица Шарикоподшипниковская, дом 11, строение 9, индекс 115088\\nМЕСТО РАСЧЕТОВ\\tNO-PLACE\\nАВТОМАТ\\t****************3333\\nДоставка\\nПОЛНЫЙ РАСЧЕТ\\t1 x 350.00 ~350.00\\nУСЛУГА\\n##BIG##ИТОГ\\t~350.00\\nВСЕГО ПОЛУЧЕНО\\t~0.00\\nПРЕДВАРИТЕЛЬНАЯ ОПЛАТА (АВАНС)\\t~350.00\\nСУММА БЕЗ НДС\\t~350.00\\nККТ ДЛЯ ИНТЕРНЕТ\\nЭЛ.АДР.ПОКУПАТЕЛЯ\\<EMAIL>\\nИНН\\t7826156685\\nСНО\\tУСН ДОХОД\\nЧЕК: 158\\tСМЕНА: 54\\nСАЙТ ФНС\\twww.nalog.ru\\nРН ККТ: 0000000001045358\\tФН: 9999078902010068\\nФД: 7695\\tФП: 2820188457\",\"TurnNumber\":54}";

    @Value("${test.receipts.order-id-with-5-positions}")
    private Long orderIdWith5Positions = 1109854L;
    private static final String VERIFY_REQUEST_WITH_5_POSITIONS = "{\"password\":**********,\"clientId\":\"noclientid\",\"documentType\":0,\"nonCash\":[7800000,0,0],\"requestId\":\"1109854\",\"correctionType\":0,\"fullResponse\":true,\"place\":\"NO-PLACE\",\"lines\":[{\"lineAttribute\":1,\"payAttribute\":4,\"price\":2800000,\"taxId\":4,\"qty\":1000,\"description\":\"Кардиган 92580\"},{\"lineAttribute\":1,\"payAttribute\":4,\"price\":1150000,\"taxId\":4,\"qty\":1000,\"description\":\"Брюки чинос 88656\"},{\"lineAttribute\":1,\"payAttribute\":4,\"price\":1100000,\"taxId\":4,\"qty\":1000,\"description\":\"Низкие кроссовки / кеды 74410\"},{\"lineAttribute\":1,\"payAttribute\":4,\"price\":1000000,\"taxId\":4,\"qty\":1000,\"description\":\"Сандалии 84237\"},{\"lineAttribute\":1,\"payAttribute\":4,\"price\":1750000,\"taxId\":4,\"qty\":1000,\"description\":\"Слипоны 85036\"},{\"lineAttribute\":4,\"payAttribute\":4,\"price\":0,\"taxId\":4,\"qty\":1000,\"description\":\"Доставка \"}],\"correction\":false,\"device\":\"auto\"}";
    private static final String MOCK_RESPONSE_WITH_5_POSITION = "{\"ClientId\":\"noclientid\",\"Date\":{\"Date\":{\"Day\":5,\"Month\":12,\"Year\":21},\"Time\":{\"Hour\":21,\"Minute\":50,\"Second\":18}},\"Device\":{\"Name\":\"****************3333\",\"Address\":\"************:4444\"},\"DeviceRegistrationNumber\":\"0000000001045358\",\"DeviceSerialNumber\":\"****************3333\",\"DocNumber\":32,\"DocumentType\":0,\"FNSerialNumber\":\"9999078902010068\",\"FiscalDocNumber\":6398,\"FiscalSign\":2627077509,\"GrandTotal\":7800000,\"MarkingInfo\":{},\"Path\":\"/fr/api/v2/Complex\",\"QR\":\"t=20211205T2150\\u0026s=78000.00\\u0026fn=9999078902010068\\u0026i=6398\\u0026fp=2627077509\\u0026n=1\",\"RequestId\":\"1109854\",\"Response\":{\"Error\":0},\"Responses\":[{\"Path\":\"/fr/api/v2/CloseDocument\",\"Response\":{\"CorrectionType\":0,\"Date\":{\"Date\":{\"Day\":5,\"Month\":12,\"Year\":21},\"Time\":{\"Hour\":21,\"Minute\":50,\"Second\":18}},\"DocNumber\":32,\"DocumentType\":0,\"Error\":0,\"FiscalDocNumber\":6398,\"FiscalDocument\":{\"TagID\":3,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":1000,\"TagType\":\"string\",\"Value\":\"Кассовый чек\"},{\"TagID\":1054,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1055,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1031,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1081,\"TagType\":\"money\",\"Value\":7800000},{\"TagID\":9997,\"TagType\":\"money\",\"Value\":0},{\"TagID\":9996,\"TagType\":\"money\",\"Value\":7800000},{\"TagID\":1020,\"TagType\":\"money\",\"Value\":7800000},{\"TagID\":1196,\"TagType\":\"string\",\"Value\":\"QR\"},{\"TagID\":1215,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1216,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1217,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1060,\"TagType\":\"string\",\"Value\":\"www.nalog.ru\"},{\"TagID\":1108,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1036,\"TagType\":\"string\",\"Value\":\"****************3333\"},{\"TagID\":1209,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1048,\"TagType\":\"string\",\"Value\":\"ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ \\\"ДЕЛОВЫЕ ЛИНИИ-Тест\\\"\"},{\"TagID\":1018,\"TagType\":\"string\",\"Value\":\"7826156685  \"},{\"TagID\":1012,\"TagType\":\"unixtime\",\"Value\":\"2021-12-05T21:50:00Z\"},{\"TagID\":1037,\"TagType\":\"string\",\"Value\":\"0000000001045358    \"},{\"TagID\":1009,\"TagType\":\"string\",\"Value\":\"Россия, город Москва, улица Шарикоподшипниковская, дом 11, строение 9, индекс 115088\"},{\"TagID\":1187,\"TagType\":\"string\",\"Value\":\"NO-PLACE\"},{\"TagID\":1105,\"TagType\":\"money\",\"Value\":7800000},{\"TagID\":1059,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":2102,\"TagType\":\"byte\",\"Value\":0},{\"TagID\":1079,\"TagType\":\"money\",\"Value\":2800000},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":2800000},{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":6},{\"TagID\":1030,\"TagType\":\"string\",\"Value\":\"Кардиган 92580\"},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1212,\"TagType\":\"byte\",\"Value\":1}]},{\"TagID\":1059,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":2102,\"TagType\":\"byte\",\"Value\":0},{\"TagID\":1079,\"TagType\":\"money\",\"Value\":1150000},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":1150000},{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":6},{\"TagID\":1030,\"TagType\":\"string\",\"Value\":\"Брюки чинос 88656\"},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1212,\"TagType\":\"byte\",\"Value\":1}]},{\"TagID\":1059,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":2102,\"TagType\":\"byte\",\"Value\":0},{\"TagID\":1079,\"TagType\":\"money\",\"Value\":1100000},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":1100000},{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":6},{\"TagID\":1030,\"TagType\":\"string\",\"Value\":\"Низкие кроссовки / кеды 74410\"},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1212,\"TagType\":\"byte\",\"Value\":1}]},{\"TagID\":1059,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":2102,\"TagType\":\"byte\",\"Value\":0},{\"TagID\":1079,\"TagType\":\"money\",\"Value\":1000000},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":1000000},{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":6},{\"TagID\":1030,\"TagType\":\"string\",\"Value\":\"Сандалии / шлепанцы 84237\"},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1212,\"TagType\":\"byte\",\"Value\":1}]},{\"TagID\":1059,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":2102,\"TagType\":\"byte\",\"Value\":0},{\"TagID\":1079,\"TagType\":\"money\",\"Value\":1750000},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":1750000},{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":6},{\"TagID\":1030,\"TagType\":\"string\",\"Value\":\"Слипоны 85036\"},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1212,\"TagType\":\"byte\",\"Value\":1}]},{\"TagID\":1059,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":2102,\"TagType\":\"byte\",\"Value\":0},{\"TagID\":1079,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":6},{\"TagID\":1030,\"TagType\":\"string\",\"Value\":\"Доставка\"},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1212,\"TagType\":\"byte\",\"Value\":4}]},{\"TagID\":1041,\"TagType\":\"string\",\"Value\":\"9999078902010068\"},{\"TagID\":1040,\"TagType\":\"uint32\",\"Value\":6398},{\"TagID\":1077,\"TagType\":\"byte[]\",\"Value\":\"MQSclgWF\"},{\"TagID\":1038,\"TagType\":\"uint32\",\"Value\":49},{\"TagID\":1042,\"TagType\":\"uint32\",\"Value\":32}]},\"FiscalSign\":2627077509,\"GrandTotal\":7800000,\"MarkingInfo\":{},\"NonCash\":[7800000,0,0],\"Password\":30,\"PaymentAgentModes\":0,\"Place\":\"NO-PLACE\",\"QR\":\"t=20211205T2150\\u0026s=78000.00\\u0026fn=9999078902010068\\u0026i=6398\\u0026fp=2627077509\\u0026n=1\",\"TaxCalculationMethod\":1,\"TaxMode\":0,\"Text\":\"КАССОВЫЙ ЧЕК/ПРИХОД\\t05-12-21 21:50\\nОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ \\\"ДЕЛОВЫЕ ЛИНИИ-Тест\\\"\\nРоссия, город Москва, улица Шарикоподшипниковская, дом 11, строение 9, индекс 115088\\nМЕСТО РАСЧЕТОВ\\tNO-PLACE\\nАВТОМАТ\\t****************3333\\nКардиган 92580\\nПОЛНЫЙ РАСЧЕТ\\t1 x 28000.00 ~28000.00\\nТОВАР\\nБрюки чинос 88656\\nПОЛНЫЙ РАСЧЕТ\\t1 x 11500.00 ~11500.00\\nТОВАР\\nНизкие кроссовки / кеды 74410\\nПОЛНЫЙ РАСЧЕТ\\t1 x 11000.00 ~11000.00\\nТОВАР\\nСандалии / шлепанцы 84237\\nПОЛНЫЙ РАСЧЕТ\\t1 x 10000.00 ~10000.00\\nТОВАР\\nСлипоны 85036\\nПОЛНЫЙ РАСЧЕТ\\t1 x 17500.00 ~17500.00\\nТОВАР\\nДоставка\\nПОЛНЫЙ РАСЧЕТ\\t1 x 0.00 ~0.00\\nУСЛУГА\\n##BIG##ИТОГ\\t~78000.00\\nВСЕГО ПОЛУЧЕНО\\t~78000.00\\nЭЛЕКТРОННЫМИ\\t~78000.00\\nСУММА БЕЗ НДС\\t~78000.00\\nККТ ДЛЯ ИНТЕРНЕТ\\nИНН\\t7826156685\\nСНО\\tОСН\\nЧЕК: 32\\tСМЕНА: 49\\nСАЙТ ФНС\\twww.nalog.ru\\nРН ККТ: 0000000001045358\\tФН: 9999078902010068\\nФД: 6398\\tФП: 2627077509\",\"TurnNumber\":49}}],\"Text\":\"КАССОВЫЙ ЧЕК/ПРИХОД\\t05-12-21 21:50\\nОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ \\\"ДЕЛОВЫЕ ЛИНИИ-Тест\\\"\\nРоссия, город Москва, улица Шарикоподшипниковская, дом 11, строение 9, индекс 115088\\nМЕСТО РАСЧЕТОВ\\tNO-PLACE\\nАВТОМАТ\\t****************3333\\nКардиган 92580\\nПОЛНЫЙ РАСЧЕТ\\t1 x 28000.00 ~28000.00\\nТОВАР\\nБрюки чинос 88656\\nПОЛНЫЙ РАСЧЕТ\\t1 x 11500.00 ~11500.00\\nТОВАР\\nНизкие кроссовки / кеды 74410\\nПОЛНЫЙ РАСЧЕТ\\t1 x 11000.00 ~11000.00\\nТОВАР\\nСандалии / шлепанцы 84237\\nПОЛНЫЙ РАСЧЕТ\\t1 x 10000.00 ~10000.00\\nТОВАР\\nСлипоны 85036\\nПОЛНЫЙ РАСЧЕТ\\t1 x 17500.00 ~17500.00\\nТОВАР\\nДоставка\\nПОЛНЫЙ РАСЧЕТ\\t1 x 0.00 ~0.00\\nУСЛУГА\\n##BIG##ИТОГ\\t~78000.00\\nВСЕГО ПОЛУЧЕНО\\t~78000.00\\nЭЛЕКТРОННЫМИ\\t~78000.00\\nСУММА БЕЗ НДС\\t~78000.00\\nККТ ДЛЯ ИНТЕРНЕТ\\nИНН\\t7826156685\\nСНО\\tОСН\\nЧЕК: 32\\tСМЕНА: 49\\nСАЙТ ФНС\\twww.nalog.ru\\nРН ККТ: 0000000001045358\\tФН: 9999078902010068\\nФД: 6398\\tФП: 2627077509\",\"TurnNumber\":49}";

    @Value("${test.receipts.order-id-receipts-b2p}")
    private Long orderIdReceiptsB2P;
    private static final String MOCK_RESPONSE_RECEIPT_B2P_FULL = MOCK_RESPONSE_WITH_1_POSITION;
    @Value("${test.receipts.order-id-receipts-tcb}")
    private Long orderIdReceiptsTcb;

    @Value("${test.receipts.order-id-agent-receipt}")
    private Long orderIdAgentReceipt;
    private static final String AGENT_RECEIPT_ADVANCE_REQUEST = "{\"phoneOrEmail\":\"<EMAIL>\",\"emailOfDeviceUser\":\"<EMAIL>\",\"clientId\":\"noclientid\",\"documentType\":0,\"nonCash\":[3476176,0,0],\"fullResponse\":true,\"password\":**********,\"requestId\":\"1118761-advance\",\"taxMode\":1,\"correctionType\":0,\"place\":\"NO-PLACE\",\"lines\":[{\"lineAttribute\":10,\"payAttribute\":3,\"providerData\":{\"name\":\"OOO \\\"ПРИМЕР КОМ\\\"\",\"inn\":\"**********\"},\"price\":2600000,\"taxId\":6,\"qty\":1000,\"agentModes\":64,\"description\":\"Перчатки GUCCI (88773)\"},{\"lineAttribute\":10,\"payAttribute\":3,\"providerData\":{\"name\":\"OOO \\\"ПРИМЕР КОМ\\\"\",\"inn\":\"**********\"},\"price\":841176,\"taxId\":6,\"qty\":1000,\"agentModes\":64,\"description\":\"Другое GUCCI (91954)\"},{\"lineAttribute\":10,\"payAttribute\":3,\"price\":35000,\"taxId\":4,\"qty\":1000,\"description\":\"Доставка (заказ 1118761)\"}],\"correction\":false,\"device\":\"auto\",\"group\":\"0a0bdc20-bff4-4d69-8b4c-6c20b8c5b60d\"}";
    private static final String AGENT_RECEIPT_ADVANCE_RESPONSE = "{\"ClientId\":\"noclientid\",\"Date\":{\"Date\":{\"Day\":13,\"Month\":4,\"Year\":22},\"Time\":{\"Hour\":19,\"Minute\":31,\"Second\":27}},\"Device\":{\"Name\":\"10000000010000077777\",\"Address\":\"************:4444\"},\"DeviceRegistrationNumber\":\"0000000001024245\",\"DeviceSerialNumber\":\"10000000010000077777\",\"DocNumber\":185,\"DocumentType\":0,\"FNSerialNumber\":\"9999078*********\",\"FiscalDocNumber\":4012,\"FiscalSign\":**********,\"GrandTotal\":3476176,\"MarkingInfo\":{},\"Path\":\"/fr/api/v2/Complex\",\"QR\":\"t=20220413T1931\\u0026s=34761.76\\u0026fn=9999078*********\\u0026i=4012\\u0026fp=**********\\u0026n=1\",\"RequestId\":\"2022-04-13-Z-1118761-advance\",\"Response\":{\"Error\":0},\"Responses\":[{\"Path\":\"/fr/api/v2/CloseDocument\",\"Response\":{\"CorrectionType\":0,\"Date\":{\"Date\":{\"Day\":13,\"Month\":4,\"Year\":22},\"Time\":{\"Hour\":19,\"Minute\":31,\"Second\":27}},\"DocNumber\":185,\"DocumentType\":0,\"Error\":0,\"FiscalDocNumber\":4012,\"FiscalDocument\":{\"TagID\":3,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":1000,\"TagType\":\"string\",\"Value\":\"Кассовый чек\"},{\"TagID\":1054,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1055,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1031,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1081,\"TagType\":\"money\",\"Value\":3476176},{\"TagID\":9997,\"TagType\":\"money\",\"Value\":0},{\"TagID\":9996,\"TagType\":\"money\",\"Value\":3476176},{\"TagID\":1020,\"TagType\":\"money\",\"Value\":3476176},{\"TagID\":1196,\"TagType\":\"string\",\"Value\":\"QR\"},{\"TagID\":1215,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1216,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1217,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1060,\"TagType\":\"string\",\"Value\":\"www.nalog.gov.ru\"},{\"TagID\":1108,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1209,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1048,\"TagType\":\"string\",\"Value\":\"OOO \\\"1K\\\"\"},{\"TagID\":1018,\"TagType\":\"string\",\"Value\":\"9705157293  \"},{\"TagID\":1012,\"TagType\":\"unixtime\",\"Value\":\"2022-04-13T19:31:00Z\"},{\"TagID\":1037,\"TagType\":\"string\",\"Value\":\"0000000001024245    \"},{\"TagID\":1021,\"TagType\":\"string\",\"Value\":\"СИСТ.АДМИНИСТРАТОР\"},{\"TagID\":1009,\"TagType\":\"string\",\"Value\":\"Ростов-на-Дону, ул.Баумана 3/3\"},{\"TagID\":1187,\"TagType\":\"string\",\"Value\":\"NO-PLACE\"},{\"TagID\":1105,\"TagType\":\"money\",\"Value\":35000},{\"TagID\":1107,\"TagType\":\"money\",\"Value\":312834},{\"TagID\":1008,\"TagType\":\"string\",\"Value\":\"<EMAIL>\"},{\"TagID\":1117,\"TagType\":\"string\",\"Value\":\"<EMAIL>\"},{\"TagID\":1059,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":2102,\"TagType\":\"byte\",\"Value\":0},{\"TagID\":1079,\"TagType\":\"money\",\"Value\":2600000},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":2600000},{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1200,\"TagType\":\"money\",\"Value\":236364},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":3},{\"TagID\":1222,\"TagType\":\"byte\",\"Value\":64},{\"TagID\":1224,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":1225,\"TagType\":\"string\",\"Value\":\"OOO \\\"ПРИМЕР КОМ\\\"\"}]},{\"TagID\":1226,\"TagType\":\"string\",\"Value\":\"**********  \"},{\"TagID\":1212,\"TagType\":\"byte\",\"Value\":10}]},{\"TagID\":1059,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":2102,\"TagType\":\"byte\",\"Value\":0},{\"TagID\":1079,\"TagType\":\"money\",\"Value\":841176},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":841176},{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1200,\"TagType\":\"money\",\"Value\":76471},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":3},{\"TagID\":1222,\"TagType\":\"byte\",\"Value\":64},{\"TagID\":1224,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":1225,\"TagType\":\"string\",\"Value\":\"OOO \\\"ПРИМЕР КОМ\\\"\"}]},{\"TagID\":1226,\"TagType\":\"string\",\"Value\":\"**********  \"},{\"TagID\":1212,\"TagType\":\"byte\",\"Value\":10}]},{\"TagID\":1059,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":2102,\"TagType\":\"byte\",\"Value\":0},{\"TagID\":1079,\"TagType\":\"money\",\"Value\":35000},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":35000},{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":6},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":3},{\"TagID\":1212,\"TagType\":\"byte\",\"Value\":10}]},{\"TagID\":1041,\"TagType\":\"string\",\"Value\":\"9999078*********\"},{\"TagID\":1040,\"TagType\":\"uint32\",\"Value\":4012},{\"TagID\":1077,\"TagType\":\"byte[]\",\"Value\":\"MQTI+kDq\"},{\"TagID\":1038,\"TagType\":\"uint32\",\"Value\":11},{\"TagID\":1042,\"TagType\":\"uint32\",\"Value\":185}]},\"FiscalSign\":**********,\"GrandTotal\":3476176,\"MarkingInfo\":{},\"NonCash\":[3476176,0,0],\"Password\":30,\"PaymentAgentModes\":0,\"Place\":\"NO-PLACE\",\"QR\":\"t=20220413T1931\\u0026s=34761.76\\u0026fn=9999078*********\\u0026i=4012\\u0026fp=**********\\u0026n=1\",\"TaxCalculationMethod\":1,\"TaxMode\":1,\"Text\":\"КАССОВЫЙ ЧЕК/ПРИХОД\\t13-04-22 19:31\\nOOO \\\"1K\\\"\\nРостов-на-Дону, ул.Баумана 3/3\\nМЕСТО РАСЧЕТОВ\\tNO-PLACE\\nАВАНС\\t1 x 26000.00 ~26000.00\\nПЛАТЕЖ\\tНДС 10/110: ~2363.64\\nАГЕНТ\\nИНН ПОСТАВЩИКА\\t**********\\nOOO \\\"ПРИМЕР КОМ\\\"\\nАВАНС\\t1 x 8411.76 ~8411.76\\nПЛАТЕЖ\\tНДС 10/110: ~764.71\\nАГЕНТ\\nИНН ПОСТАВЩИКА\\t**********\\nOOO \\\"ПРИМЕР КОМ\\\"\\nАВАНС\\t1 x 350.00 ~350.00\\nПЛАТЕЖ\\n##BIG##ИТОГ\\t~34761.76\\nВСЕГО ПОЛУЧЕНО\\t~34761.76\\nЭЛЕКТРОННЫМИ\\t~34761.76\\nСУММА БЕЗ НДС\\t~350.00\\nСУММА НДС 10/110\\t~3128.34\\nККТ ДЛЯ ИНТЕРНЕТ\\nЭЛ.АДР.ОТПРАВИТЕЛЯ\\<EMAIL>\\nЭЛ.АДР.ПОКУПАТЕЛЯ\\<EMAIL>\\nКАССИР: СИСТ.АДМИНИСТРАТОР\\tИНН: 9705157293\\nСНО\\tОСН\\nЧЕК: 185\\tСМЕНА: 11\\nСАЙТ ФНС\\twww.nalog.gov.ru\\nРН ККТ: 0000000001024245\\tФН: 9999078*********\\nФД: 4012\\tФП: **********\",\"TurnNumber\":11}}],\"Text\":\"КАССОВЫЙ ЧЕК/ПРИХОД\\t13-04-22 19:31\\nOOO \\\"1K\\\"\\nРостов-на-Дону, ул.Баумана 3/3\\nМЕСТО РАСЧЕТОВ\\tNO-PLACE\\nАВАНС\\t1 x 26000.00 ~26000.00\\nПЛАТЕЖ\\tНДС 10/110: ~2363.64\\nАГЕНТ\\nИНН ПОСТАВЩИКА\\t**********\\nOOO \\\"ПРИМЕР КОМ\\\"\\nАВАНС\\t1 x 8411.76 ~8411.76\\nПЛАТЕЖ\\tНДС 10/110: ~764.71\\nАГЕНТ\\nИНН ПОСТАВЩИКА\\t**********\\nOOO \\\"ПРИМЕР КОМ\\\"\\nАВАНС\\t1 x 350.00 ~350.00\\nПЛАТЕЖ\\n##BIG##ИТОГ\\t~34761.76\\nВСЕГО ПОЛУЧЕНО\\t~34761.76\\nЭЛЕКТРОННЫМИ\\t~34761.76\\nСУММА БЕЗ НДС\\t~350.00\\nСУММА НДС 10/110\\t~3128.34\\nККТ ДЛЯ ИНТЕРНЕТ\\nЭЛ.АДР.ОТПРАВИТЕЛЯ\\<EMAIL>\\nЭЛ.АДР.ПОКУПАТЕЛЯ\\<EMAIL>\\nКАССИР: СИСТ.АДМИНИСТРАТОР\\tИНН: 9705157293\\nСНО\\tОСН\\nЧЕК: 185\\tСМЕНА: 11\\nСАЙТ ФНС\\twww.nalog.gov.ru\\nРН ККТ: 0000000001024245\\tФН: 9999078*********\\nФД: 4012\\tФП: **********\",\"TurnNumber\":11}";
    private static final String AGENT_RECEIPT_PAYMENT_REQUEST = "{\"phoneOrEmail\":\"<EMAIL>\",\"emailOfDeviceUser\":\"<EMAIL>\",\"clientId\":\"noclientid\",\"documentType\":0,\"nonCash\":[0,0,0],\"fullResponse\":true,\"password\":**********,\"requestId\":\"1118761-payment\",\"advancePayment\":3476176,\"taxMode\":1,\"correctionType\":0,\"place\":\"NO-PLACE\",\"lines\":[{\"lineAttribute\":1,\"payAttribute\":4,\"providerData\":{\"name\":\"OOO \\\"ПРИМЕР КОМ\\\"\",\"inn\":\"**********\"},\"price\":2600000,\"taxId\":2,\"qty\":1000,\"agentModes\":64,\"description\":\"Перчатки GUCCI (88773)\"},{\"lineAttribute\":1,\"payAttribute\":4,\"providerData\":{\"name\":\"OOO \\\"ПРИМЕР КОМ\\\"\",\"inn\":\"**********\"},\"price\":841176,\"taxId\":2,\"qty\":1000,\"agentModes\":64,\"description\":\"Другое GUCCI (91954)\"},{\"lineAttribute\":4,\"payAttribute\":4,\"price\":35000,\"taxId\":4,\"qty\":1000,\"description\":\"Доставка (заказ 1118761)\"}],\"correction\":false,\"device\":\"auto\",\"group\":\"0a0bdc20-bff4-4d69-8b4c-6c20b8c5b60d\"}";
    private static final String AGENT_RECEIPT_PAYMENT_RESPONSE = "{\"ClientId\":\"noclientid\",\"Date\":{\"Date\":{\"Day\":13,\"Month\":4,\"Year\":22},\"Time\":{\"Hour\":19,\"Minute\":31,\"Second\":29}},\"Device\":{\"Name\":\"****************3333\",\"Address\":\"************:4444\"},\"DeviceRegistrationNumber\":\"0000000001045358\",\"DeviceSerialNumber\":\"****************3333\",\"DocNumber\":337,\"DocumentType\":0,\"FNSerialNumber\":\"9999078902010068\",\"FiscalDocNumber\":57588,\"FiscalSign\":**********,\"GrandTotal\":3476176,\"MarkingInfo\":{},\"Path\":\"/fr/api/v2/Complex\",\"QR\":\"t=20220413T1931\\u0026s=34761.76\\u0026fn=9999078902010068\\u0026i=57588\\u0026fp=**********\\u0026n=1\",\"RequestId\":\"2022-04-13-Z-1118761-payment\",\"Response\":{\"Error\":0},\"Responses\":[{\"Path\":\"/fr/api/v2/CloseDocument\",\"Response\":{\"AdvancePayment\":3476176,\"CorrectionType\":0,\"Date\":{\"Date\":{\"Day\":13,\"Month\":4,\"Year\":22},\"Time\":{\"Hour\":19,\"Minute\":31,\"Second\":29}},\"DocNumber\":337,\"DocumentType\":0,\"Error\":0,\"FiscalDocNumber\":57588,\"FiscalDocument\":{\"TagID\":3,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":1000,\"TagType\":\"string\",\"Value\":\"Кассовый чек\"},{\"TagID\":1054,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1055,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1031,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1081,\"TagType\":\"money\",\"Value\":0},{\"TagID\":9997,\"TagType\":\"money\",\"Value\":0},{\"TagID\":9996,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1020,\"TagType\":\"money\",\"Value\":3476176},{\"TagID\":1196,\"TagType\":\"string\",\"Value\":\"QR\"},{\"TagID\":1215,\"TagType\":\"money\",\"Value\":3476176},{\"TagID\":1216,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1217,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1060,\"TagType\":\"string\",\"Value\":\"www.nalog.gov.ru\"},{\"TagID\":1108,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1036,\"TagType\":\"string\",\"Value\":\"****************3333\"},{\"TagID\":1209,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1048,\"TagType\":\"string\",\"Value\":\"ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ \\\"ДЕЛОВЫЕ ЛИНИИ-Тест\\\"\"},{\"TagID\":1018,\"TagType\":\"string\",\"Value\":\"7826156685  \"},{\"TagID\":1012,\"TagType\":\"unixtime\",\"Value\":\"2022-04-13T19:31:00Z\"},{\"TagID\":1037,\"TagType\":\"string\",\"Value\":\"0000000001045358    \"},{\"TagID\":1009,\"TagType\":\"string\",\"Value\":\"Россия, город Москва, улица Шарикоподшипниковская, дом 11, строение 9, индекс 115088\"},{\"TagID\":1187,\"TagType\":\"string\",\"Value\":\"NO-PLACE\"},{\"TagID\":1103,\"TagType\":\"money\",\"Value\":312834},{\"TagID\":1105,\"TagType\":\"money\",\"Value\":35000},{\"TagID\":1008,\"TagType\":\"string\",\"Value\":\"<EMAIL>\"},{\"TagID\":1117,\"TagType\":\"string\",\"Value\":\"<EMAIL>\"},{\"TagID\":1059,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":2102,\"TagType\":\"byte\",\"Value\":0},{\"TagID\":1079,\"TagType\":\"money\",\"Value\":2600000},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":2600000},{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":2},{\"TagID\":1200,\"TagType\":\"money\",\"Value\":236364},{\"TagID\":1030,\"TagType\":\"string\",\"Value\":\"Перчатки GUCCI (109738)\"},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1222,\"TagType\":\"byte\",\"Value\":64},{\"TagID\":1224,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":1225,\"TagType\":\"string\",\"Value\":\"OOO \\\"ПРИМЕР КОМ\\\"\"}]},{\"TagID\":1226,\"TagType\":\"string\",\"Value\":\"**********  \"},{\"TagID\":1212,\"TagType\":\"byte\",\"Value\":1}]},{\"TagID\":1059,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":2102,\"TagType\":\"byte\",\"Value\":0},{\"TagID\":1079,\"TagType\":\"money\",\"Value\":841176},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":841176},{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":2},{\"TagID\":1200,\"TagType\":\"money\",\"Value\":76471},{\"TagID\":1030,\"TagType\":\"string\",\"Value\":\"Другое GUCCI (113161)\"},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1222,\"TagType\":\"byte\",\"Value\":64},{\"TagID\":1224,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":1225,\"TagType\":\"string\",\"Value\":\"OOO \\\"ПРИМЕР КОМ\\\"\"}]},{\"TagID\":1226,\"TagType\":\"string\",\"Value\":\"**********  \"},{\"TagID\":1212,\"TagType\":\"byte\",\"Value\":1}]},{\"TagID\":1059,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":2102,\"TagType\":\"byte\",\"Value\":0},{\"TagID\":1079,\"TagType\":\"money\",\"Value\":35000},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":35000},{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":6},{\"TagID\":1030,\"TagType\":\"string\",\"Value\":\"Доставка\"},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1212,\"TagType\":\"byte\",\"Value\":4}]},{\"TagID\":1041,\"TagType\":\"string\",\"Value\":\"9999078902010068\"},{\"TagID\":1040,\"TagType\":\"uint32\",\"Value\":57588},{\"TagID\":1077,\"TagType\":\"byte[]\",\"Value\":\"MQSEYaU7\"},{\"TagID\":1038,\"TagType\":\"uint32\",\"Value\":180},{\"TagID\":1042,\"TagType\":\"uint32\",\"Value\":337}]},\"FiscalSign\":**********,\"GrandTotal\":3476176,\"MarkingInfo\":{},\"NonCash\":[0,0,0],\"Password\":30,\"PaymentAgentModes\":0,\"Place\":\"NO-PLACE\",\"QR\":\"t=20220413T1931\\u0026s=34761.76\\u0026fn=9999078902010068\\u0026i=57588\\u0026fp=**********\\u0026n=1\",\"TaxCalculationMethod\":1,\"TaxMode\":1,\"Text\":\"КАССОВЫЙ ЧЕК/ПРИХОД\\t13-04-22 19:31\\nОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ \\\"ДЕЛОВЫЕ ЛИНИИ-Тест\\\"\\nРоссия, город Москва, улица Шарикоподшипниковская, дом 11, строение 9, индекс 115088\\nМЕСТО РАСЧЕТОВ\\tNO-PLACE\\nАВТОМАТ\\t****************3333\\nПерчатки GUCCI (109738)\\nПОЛНЫЙ РАСЧЕТ\\t1 x 26000.00 ~26000.00\\nТОВАР\\tНДС 10%: ~2363.64\\nАГЕНТ\\nИНН ПОСТАВЩИКА\\t**********\\nOOO \\\"ПРИМЕР КОМ\\\"\\nДругое GUCCI (113161)\\nПОЛНЫЙ РАСЧЕТ\\t1 x 8411.76 ~8411.76\\nТОВАР\\tНДС 10%: ~764.71\\nАГЕНТ\\nИНН ПОСТАВЩИКА\\t**********\\nOOO \\\"ПРИМЕР КОМ\\\"\\nДоставка\\nПОЛНЫЙ РАСЧЕТ\\t1 x 350.00 ~350.00\\nУСЛУГА\\n##BIG##ИТОГ\\t~34761.76\\nВСЕГО ПОЛУЧЕНО\\t~0.00\\nПРЕДВАРИТЕЛЬНАЯ ОПЛАТА (АВАНС)\\t~34761.76\\nСУММА НДС 10%\\t~3128.34\\nСУММА БЕЗ НДС\\t~350.00\\nККТ ДЛЯ ИНТЕРНЕТ\\nЭЛ.АДР.ОТПРАВИТЕЛЯ\\<EMAIL>\\nЭЛ.АДР.ПОКУПАТЕЛЯ\\<EMAIL>\\nИНН\\t7826156685\\nСНО\\tОСН\\nЧЕК: 337\\tСМЕНА: 180\\nСАЙТ ФНС\\twww.nalog.gov.ru\\nРН ККТ: 0000000001045358\\tФН: 9999078902010068\\nФД: 57588\\tФП: **********\",\"TurnNumber\":180}}],\"Text\":\"КАССОВЫЙ ЧЕК/ПРИХОД\\t13-04-22 19:31\\nОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ \\\"ДЕЛОВЫЕ ЛИНИИ-Тест\\\"\\nРоссия, город Москва, улица Шарикоподшипниковская, дом 11, строение 9, индекс 115088\\nМЕСТО РАСЧЕТОВ\\tNO-PLACE\\nАВТОМАТ\\t****************3333\\nПерчатки GUCCI (109738)\\nПОЛНЫЙ РАСЧЕТ\\t1 x 26000.00 ~26000.00\\nТОВАР\\tНДС 10%: ~2363.64\\nАГЕНТ\\nИНН ПОСТАВЩИКА\\t**********\\nOOO \\\"ПРИМЕР КОМ\\\"\\nДругое GUCCI (113161)\\nПОЛНЫЙ РАСЧЕТ\\t1 x 8411.76 ~8411.76\\nТОВАР\\tНДС 10%: ~764.71\\nАГЕНТ\\nИНН ПОСТАВЩИКА\\t**********\\nOOO \\\"ПРИМЕР КОМ\\\"\\nДоставка\\nПОЛНЫЙ РАСЧЕТ\\t1 x 350.00 ~350.00\\nУСЛУГА\\n##BIG##ИТОГ\\t~34761.76\\nВСЕГО ПОЛУЧЕНО\\t~0.00\\nПРЕДВАРИТЕЛЬНАЯ ОПЛАТА (АВАНС)\\t~34761.76\\nСУММА НДС 10%\\t~3128.34\\nСУММА БЕЗ НДС\\t~350.00\\nККТ ДЛЯ ИНТЕРНЕТ\\nЭЛ.АДР.ОТПРАВИТЕЛЯ\\<EMAIL>\\nЭЛ.АДР.ПОКУПАТЕЛЯ\\<EMAIL>\\nИНН\\t7826156685\\nСНО\\tОСН\\nЧЕК: 337\\tСМЕНА: 180\\nСАЙТ ФНС\\twww.nalog.gov.ru\\nРН ККТ: 0000000001045358\\tФН: 9999078902010068\\nФД: 57588\\tФП: **********\",\"TurnNumber\":180}";

    @Value("${test.receipts.order-id-marking-code}")
    private Long orderIdMarkingCode;
    private static final String MILK_MARK_CODE = "0104606779771309215eZmbu{GS}93c/jz";
    private static final String SHOE_MARK_CODE = "010465019391231221%hrH\"KpTSMWFY{GS}910098{GS}923X6eHVsm9vwVe/7bE9hDqMdyJNeEbFF133mgdCQE71fwCwkITCn+xsg1hNXo380BdkqneKj+Gomb/W6uipCzbw==";
    private static final String VERIFY_REQUEST_WITH_ADVANCE_MARK_RECEIPT = "{\"phoneOrEmail\":\"<EMAIL>\",\"emailOfDeviceUser\":\"<EMAIL>\",\"clientId\":\"noclientid\",\"documentType\":0,\"nonCash\":[1500000,0,0],\"fullResponse\":true,\"password\":**********,\"requestId\":\"1110719-advance\",\"taxMode\":1,\"correctionType\":0,\"place\":\"NO-PLACE\",\"lines\":[{\"lineAttribute\":10,\"payAttribute\":3,\"providerData\":{\"name\":\"OOO \\\"ПРИМЕР КОМ\\\"\",\"inn\":\"**********\"},\"price\":500000,\"taxId\":5,\"qty\":1000,\"agentModes\":64,\"description\":\"Верхняя одежда FENDI (84307)\"},{\"lineAttribute\":10,\"payAttribute\":3,\"providerData\":{\"name\":\"OOO \\\"ПРИМЕР КОМ\\\"\",\"inn\":\"**********\"},\"price\":500000,\"taxId\":5,\"qty\":1000,\"agentModes\":64,\"description\":\"Верхняя одежда DIOR EYEWEAR (84523)\"},{\"lineAttribute\":10,\"payAttribute\":3,\"providerData\":{\"name\":\"OOO \\\"ПРИМЕР КОМ\\\"\",\"inn\":\"**********\"},\"price\":500000,\"taxId\":5,\"qty\":1000,\"agentModes\":64,\"description\":\"Ботинки CHRISTIAN DIOR (76021)\"},{\"lineAttribute\":10,\"payAttribute\":3,\"price\":0,\"taxId\":4,\"qty\":1000,\"description\":\"Доставка (заказ 1110719)\"}],\"correction\":false,\"device\":\"auto\",\"group\":\"0a0bdc20-bff4-4d69-8b4c-6c20b8c5b60d\"}";
    private static final String MOCK_RESPONSE_FOR_ADVANCE_MARK_RECEIPT = "{\"ClientId\":\"noclientid\",\"Date\":{\"Date\":{\"Day\":8,\"Month\":4,\"Year\":22},\"Time\":{\"Hour\":13,\"Minute\":40,\"Second\":3}},\"Device\":{\"Name\":\"10000000010000077777\",\"Address\":\"************:4444\"},\"DeviceRegistrationNumber\":\"0000000001024245\",\"DeviceSerialNumber\":\"10000000010000077777\",\"DocNumber\":219,\"DocumentType\":0,\"FNSerialNumber\":\"9999078*********\",\"FiscalDocNumber\":2355,\"FiscalSign\":*********,\"GrandTotal\":1500000,\"MarkingInfo\":{\"Tag2106Values\":[0,null,0,null]},\"Path\":\"/fr/api/v2/Complex\",\"QR\":\"t=20220408T1340\\u0026s=15000.00\\u0026fn=9999078*********\\u0026i=2355\\u0026fp=*********\\u0026n=1\",\"RequestId\":\"2022-04-08-13-38-1110719-advance\",\"Response\":{\"Error\":0},\"Responses\":[{\"Path\":\"/fr/api/v2/CloseDocument\",\"Response\":{\"CorrectionType\":0,\"Date\":{\"Date\":{\"Day\":8,\"Month\":4,\"Year\":22},\"Time\":{\"Hour\":13,\"Minute\":40,\"Second\":3}},\"DocNumber\":219,\"DocumentType\":0,\"Error\":0,\"FiscalDocNumber\":2355,\"FiscalDocument\":{\"TagID\":3,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":1000,\"TagType\":\"string\",\"Value\":\"Кассовый чек\"},{\"TagID\":1054,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1055,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1031,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1081,\"TagType\":\"money\",\"Value\":1500000},{\"TagID\":9997,\"TagType\":\"money\",\"Value\":0},{\"TagID\":9996,\"TagType\":\"money\",\"Value\":1500000},{\"TagID\":1020,\"TagType\":\"money\",\"Value\":1500000},{\"TagID\":1196,\"TagType\":\"string\",\"Value\":\"QR\"},{\"TagID\":1215,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1216,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1217,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1060,\"TagType\":\"string\",\"Value\":\"www.nalog.gov.ru\"},{\"TagID\":1108,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1209,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1048,\"TagType\":\"string\",\"Value\":\"OOO \\\"1K\\\"\"},{\"TagID\":1018,\"TagType\":\"string\",\"Value\":\"9705157293  \"},{\"TagID\":1012,\"TagType\":\"unixtime\",\"Value\":\"2022-04-08T13:40:00Z\"},{\"TagID\":1037,\"TagType\":\"string\",\"Value\":\"0000000001024245    \"},{\"TagID\":1021,\"TagType\":\"string\",\"Value\":\"СИСТ.АДМИНИСТРАТОР\"},{\"TagID\":1009,\"TagType\":\"string\",\"Value\":\"Ростов-на-Дону, ул.Баумана 3/3\"},{\"TagID\":1187,\"TagType\":\"string\",\"Value\":\"NO-PLACE\"},{\"TagID\":1106,\"TagType\":\"money\",\"Value\":250000},{\"TagID\":1008,\"TagType\":\"string\",\"Value\":\"<EMAIL>\"},{\"TagID\":1117,\"TagType\":\"string\",\"Value\":\"<EMAIL>\"},{\"TagID\":2107,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1059,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":2102,\"TagType\":\"byte\",\"Value\":0},{\"TagID\":1079,\"TagType\":\"money\",\"Value\":500000},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":500000},{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":3},{\"TagID\":1200,\"TagType\":\"money\",\"Value\":83333},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":3},{\"TagID\":1222,\"TagType\":\"byte\",\"Value\":64},{\"TagID\":1224,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":1225,\"TagType\":\"string\",\"Value\":\"OOO \\\"ПРИМЕР КОМ\\\"\"}]},{\"TagID\":1226,\"TagType\":\"string\",\"Value\":\"**********  \"},{\"TagID\":1163,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":1300,\"TagType\":\"string\",\"Value\":\"010465019391231221%hrH\\\"KpTSMWFY{\"}]},{\"TagID\":1212,\"TagType\":\"byte\",\"Value\":10},{\"TagID\":2115,\"TagType\":\"string\",\"Value\":\"8692\"},{\"TagID\":2106,\"TagType\":\"byte\",\"Value\":0}]},{\"TagID\":1059,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":2102,\"TagType\":\"byte\",\"Value\":0},{\"TagID\":1079,\"TagType\":\"money\",\"Value\":500000},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":500000},{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":3},{\"TagID\":1200,\"TagType\":\"money\",\"Value\":83333},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":3},{\"TagID\":1222,\"TagType\":\"byte\",\"Value\":64},{\"TagID\":1224,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":1225,\"TagType\":\"string\",\"Value\":\"OOO \\\"ПРИМЕР КОМ\\\"\"}]},{\"TagID\":1226,\"TagType\":\"string\",\"Value\":\"**********  \"},{\"TagID\":1212,\"TagType\":\"byte\",\"Value\":10}]},{\"TagID\":1059,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":2102,\"TagType\":\"byte\",\"Value\":0},{\"TagID\":1079,\"TagType\":\"money\",\"Value\":500000},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":500000},{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":3},{\"TagID\":1200,\"TagType\":\"money\",\"Value\":83333},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":3},{\"TagID\":1222,\"TagType\":\"byte\",\"Value\":64},{\"TagID\":1224,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":1225,\"TagType\":\"string\",\"Value\":\"OOO \\\"ПРИМЕР КОМ\\\"\"}]},{\"TagID\":1226,\"TagType\":\"string\",\"Value\":\"**********  \"},{\"TagID\":1163,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":1304,\"TagType\":\"string\",\"Value\":\"0104606779771309215eZmbu{GS}93c/jz\"}]},{\"TagID\":1212,\"TagType\":\"byte\",\"Value\":10},{\"TagID\":2115,\"TagType\":\"string\",\"Value\":\"4452\"},{\"TagID\":2106,\"TagType\":\"byte\",\"Value\":0}]},{\"TagID\":1059,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":2102,\"TagType\":\"byte\",\"Value\":0},{\"TagID\":1079,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":6},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":3},{\"TagID\":1212,\"TagType\":\"byte\",\"Value\":10}]},{\"TagID\":1041,\"TagType\":\"string\",\"Value\":\"9999078*********\"},{\"TagID\":1040,\"TagType\":\"uint32\",\"Value\":2355},{\"TagID\":1077,\"TagType\":\"byte[]\",\"Value\":\"MQQJQ5Qo\"},{\"TagID\":1038,\"TagType\":\"uint32\",\"Value\":6},{\"TagID\":1042,\"TagType\":\"uint32\",\"Value\":219}]},\"FiscalSign\":*********,\"GrandTotal\":1500000,\"MarkingInfo\":{\"Tag2106Values\":[0,null,0,null]},\"NonCash\":[1500000,0,0],\"Password\":30,\"PaymentAgentModes\":0,\"Place\":\"NO-PLACE\",\"QR\":\"t=20220408T1340\\u0026s=15000.00\\u0026fn=9999078*********\\u0026i=2355\\u0026fp=*********\\u0026n=1\",\"TaxCalculationMethod\":1,\"TaxMode\":1,\"Text\":\"КАССОВЫЙ ЧЕК/ПРИХОД\\t08-04-22 13:40\\nOOO \\\"1K\\\"\\nРостов-на-Дону, ул.Баумана 3/3\\nМЕСТО РАСЧЕТОВ\\tNO-PLACE\\nАВАНС\\t1 x 5000.00 ~5000.00\\nПЛАТЕЖ\\tНДС 20/120: ~833.33\\nАГЕНТ\\nИНН ПОСТАВЩИКА\\t**********\\nOOO \\\"ПРИМЕР КОМ\\\"\\n8692\\nАВАНС\\t1 x 5000.00 ~5000.00\\nПЛАТЕЖ\\tНДС 20/120: ~833.33\\nАГЕНТ\\nИНН ПОСТАВЩИКА\\t**********\\nOOO \\\"ПРИМЕР КОМ\\\"\\nАВАНС\\t1 x 5000.00 ~5000.00\\nПЛАТЕЖ\\tНДС 20/120: ~833.33\\nАГЕНТ\\nИНН ПОСТАВЩИКА\\t**********\\nOOO \\\"ПРИМЕР КОМ\\\"\\n4452\\nАВАНС\\t1 x 0.00 ~0.00\\nПЛАТЕЖ\\n##BIG##ИТОГ\\t~15000.00\\nВСЕГО ПОЛУЧЕНО\\t~15000.00\\nЭЛЕКТРОННЫМИ\\t~15000.00\\nСУММА НДС 20/120\\t~2500.00\\nККТ ДЛЯ ИНТЕРНЕТ\\nЭЛ.АДР.ОТПРАВИТЕЛЯ\\<EMAIL>\\nЭЛ.АДР.ПОКУПАТЕЛЯ\\<EMAIL>\\nКМ?\\nКАССИР: СИСТ.АДМИНИСТРАТОР\\tИНН: 9705157293\\nСНО\\tОСН\\nЧЕК: 219\\tСМЕНА: 6\\nСАЙТ ФНС\\twww.nalog.gov.ru\\nРН ККТ: 0000000001024245\\tФН: 9999078*********\\nФД: 2355\\tФП: 0*********\",\"TurnNumber\":6}}],\"Text\":\"КАССОВЫЙ ЧЕК/ПРИХОД\\t08-04-22 13:40\\nOOO \\\"1K\\\"\\nРостов-на-Дону, ул.Баумана 3/3\\nМЕСТО РАСЧЕТОВ\\tNO-PLACE\\nАВАНС\\t1 x 5000.00 ~5000.00\\nПЛАТЕЖ\\tНДС 20/120: ~833.33\\nАГЕНТ\\nИНН ПОСТАВЩИКА\\t**********\\nOOO \\\"ПРИМЕР КОМ\\\"\\n8692\\nАВАНС\\t1 x 5000.00 ~5000.00\\nПЛАТЕЖ\\tНДС 20/120: ~833.33\\nАГЕНТ\\nИНН ПОСТАВЩИКА\\t**********\\nOOO \\\"ПРИМЕР КОМ\\\"\\nАВАНС\\t1 x 5000.00 ~5000.00\\nПЛАТЕЖ\\tНДС 20/120: ~833.33\\nАГЕНТ\\nИНН ПОСТАВЩИКА\\t**********\\nOOO \\\"ПРИМЕР КОМ\\\"\\n4452\\nАВАНС\\t1 x 0.00 ~0.00\\nПЛАТЕЖ\\n##BIG##ИТОГ\\t~15000.00\\nВСЕГО ПОЛУЧЕНО\\t~15000.00\\nЭЛЕКТРОННЫМИ\\t~15000.00\\nСУММА НДС 20/120\\t~2500.00\\nККТ ДЛЯ ИНТЕРНЕТ\\nЭЛ.АДР.ОТПРАВИТЕЛЯ\\<EMAIL>\\nЭЛ.АДР.ПОКУПАТЕЛЯ\\<EMAIL>\\nКМ?\\nКАССИР: СИСТ.АДМИНИСТРАТОР\\tИНН: 9705157293\\nСНО\\tОСН\\nЧЕК: 219\\tСМЕНА: 6\\nСАЙТ ФНС\\twww.nalog.gov.ru\\nРН ККТ: 0000000001024245\\tФН: 9999078*********\\nФД: 2355\\tФП: 0*********\",\"TurnNumber\":6}";
    private static final String VERIFY_REQUEST_WITH_ADVANCE_PAYMENT_MARK_RECEIPT_ENABLED = "{\"phoneOrEmail\":\"<EMAIL>\",\"emailOfDeviceUser\":\"<EMAIL>\",\"clientId\":\"noclientid\",\"documentType\":0,\"nonCash\":[0,0,0],\"fullResponse\":true,\"password\":**********,\"requestId\":\"1110719-payment\",\"advancePayment\":1500000,\"taxMode\":1,\"correctionType\":0,\"place\":\"NO-PLACE\",\"lines\":[{\"lineAttribute\":1,\"payAttribute\":4,\"providerData\":{\"name\":\"OOO \\\"ПРИМЕР КОМ\\\"\",\"inn\":\"**********\"},\"price\":500000,\"taxId\":1,\"qty\":1000,\"agentModes\":64,\"markingCode\":\"010465019391231221%hrH\\\"KpTSMWFY{GS}910098{GS}923X6eHVsm9vwVe/7bE9hDqMdyJNeEbFF133mgdCQE71fwCwkITCn+xsg1hNXo380BdkqneKj+Gomb/W6uipCzbw==\",\"description\":\"Верхняя одежда FENDI (84307)\"},{\"lineAttribute\":1,\"payAttribute\":4,\"providerData\":{\"name\":\"OOO \\\"ПРИМЕР КОМ\\\"\",\"inn\":\"**********\"},\"price\":500000,\"taxId\":1,\"qty\":1000,\"agentModes\":64,\"description\":\"Верхняя одежда DIOR EYEWEAR (84523)\"},{\"lineAttribute\":1,\"payAttribute\":4,\"providerData\":{\"name\":\"OOO \\\"ПРИМЕР КОМ\\\"\",\"inn\":\"**********\"},\"price\":500000,\"taxId\":1,\"qty\":1000,\"agentModes\":64,\"description\":\"Ботинки CHRISTIAN DIOR (76021)\"},{\"lineAttribute\":4,\"payAttribute\":4,\"price\":0,\"taxId\":4,\"qty\":1000,\"description\":\"Доставка (заказ 1110719)\"}],\"correction\":false,\"device\":\"auto\",\"group\":\"0a0bdc20-bff4-4d69-8b4c-6c20b8c5b60d\"}";
    private static final String VERIFY_REQUEST_WITH_ADVANCE_PAYMENT_MARK_RECEIPT_DISABLED = "{\"phoneOrEmail\":\"<EMAIL>\",\"emailOfDeviceUser\":\"<EMAIL>\",\"clientId\":\"noclientid\",\"documentType\":0,\"nonCash\":[0,0,0],\"fullResponse\":true,\"password\":**********,\"requestId\":\"1110719-payment\",\"advancePayment\":1500000,\"taxMode\":1,\"correctionType\":0,\"place\":\"NO-PLACE\",\"lines\":[{\"lineAttribute\":1,\"payAttribute\":4,\"providerData\":{\"name\":\"OOO \\\"ПРИМЕР КОМ\\\"\",\"inn\":\"**********\"},\"price\":500000,\"taxId\":1,\"qty\":1000,\"agentModes\":64,\"description\":\"Верхняя одежда FENDI (84307)\"},{\"lineAttribute\":1,\"payAttribute\":4,\"providerData\":{\"name\":\"OOO \\\"ПРИМЕР КОМ\\\"\",\"inn\":\"**********\"},\"price\":500000,\"taxId\":1,\"qty\":1000,\"agentModes\":64,\"description\":\"Верхняя одежда DIOR EYEWEAR (84523)\"},{\"lineAttribute\":1,\"payAttribute\":4,\"providerData\":{\"name\":\"OOO \\\"ПРИМЕР КОМ\\\"\",\"inn\":\"**********\"},\"price\":500000,\"taxId\":1,\"qty\":1000,\"agentModes\":64,\"description\":\"Ботинки CHRISTIAN DIOR (76021)\"},{\"lineAttribute\":4,\"payAttribute\":4,\"price\":0,\"taxId\":4,\"qty\":1000,\"description\":\"Доставка (заказ 1110719)\"}],\"correction\":false,\"device\":\"auto\",\"group\":\"0a0bdc20-bff4-4d69-8b4c-6c20b8c5b60d\"}";
    private static final String MOCK_RESPONSE_FOR_ADVANCE_PAYMENT_MARK_RECEIPT = "{\"ClientId\":\"noclientid\",\"Date\":{\"Date\":{\"Day\":8,\"Month\":4,\"Year\":22},\"Time\":{\"Hour\":13,\"Minute\":55,\"Second\":25}},\"Device\":{\"Name\":\"10000000010000077777\",\"Address\":\"************:4444\"},\"DeviceRegistrationNumber\":\"0000000001024245\",\"DeviceSerialNumber\":\"10000000010000077777\",\"DocNumber\":244,\"DocumentType\":0,\"FNSerialNumber\":\"9999078*********\",\"FiscalDocNumber\":2380,\"FiscalSign\":**********,\"GrandTotal\":1500000,\"MarkingInfo\":{\"Tag2106Values\":[0,null,null,null]},\"Path\":\"/fr/api/v2/Complex\",\"QR\":\"t=20220408T1355\\u0026s=15000.00\\u0026fn=9999078*********\\u0026i=2380\\u0026fp=**********\\u0026n=1\",\"RequestId\":\"2022-04-08-13-38-1110719-payment\",\"Response\":{\"Error\":0},\"Responses\":[{\"Path\":\"/fr/api/v2/CloseDocument\",\"Response\":{\"AdvancePayment\":1500000,\"CorrectionType\":0,\"Date\":{\"Date\":{\"Day\":8,\"Month\":4,\"Year\":22},\"Time\":{\"Hour\":13,\"Minute\":55,\"Second\":25}},\"DocNumber\":244,\"DocumentType\":0,\"Error\":0,\"FiscalDocNumber\":2380,\"FiscalDocument\":{\"TagID\":3,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":1000,\"TagType\":\"string\",\"Value\":\"Кассовый чек\"},{\"TagID\":1054,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1055,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1031,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1081,\"TagType\":\"money\",\"Value\":0},{\"TagID\":9997,\"TagType\":\"money\",\"Value\":0},{\"TagID\":9996,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1020,\"TagType\":\"money\",\"Value\":1500000},{\"TagID\":1196,\"TagType\":\"string\",\"Value\":\"QR\"},{\"TagID\":1215,\"TagType\":\"money\",\"Value\":1500000},{\"TagID\":1216,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1217,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1060,\"TagType\":\"string\",\"Value\":\"www.nalog.gov.ru\"},{\"TagID\":1108,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1209,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1048,\"TagType\":\"string\",\"Value\":\"OOO \\\"1K\\\"\"},{\"TagID\":1018,\"TagType\":\"string\",\"Value\":\"9705157293  \"},{\"TagID\":1012,\"TagType\":\"unixtime\",\"Value\":\"2022-04-08T13:55:00Z\"},{\"TagID\":1037,\"TagType\":\"string\",\"Value\":\"0000000001024245    \"},{\"TagID\":1021,\"TagType\":\"string\",\"Value\":\"СИСТ.АДМИНИСТРАТОР\"},{\"TagID\":1009,\"TagType\":\"string\",\"Value\":\"Ростов-на-Дону, ул.Баумана 3/3\"},{\"TagID\":1187,\"TagType\":\"string\",\"Value\":\"NO-PLACE\"},{\"TagID\":1102,\"TagType\":\"money\",\"Value\":250000},{\"TagID\":1008,\"TagType\":\"string\",\"Value\":\"<EMAIL>\"},{\"TagID\":1117,\"TagType\":\"string\",\"Value\":\"<EMAIL>\"},{\"TagID\":2107,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1059,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":2102,\"TagType\":\"byte\",\"Value\":0},{\"TagID\":1079,\"TagType\":\"money\",\"Value\":500000},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":500000},{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1200,\"TagType\":\"money\",\"Value\":83333},{\"TagID\":1030,\"TagType\":\"string\",\"Value\":\"Верхняя одежда FENDI (104790)\"},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1222,\"TagType\":\"byte\",\"Value\":64},{\"TagID\":1224,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":1225,\"TagType\":\"string\",\"Value\":\"OOO \\\"ПРИМЕР КОМ\\\"\"}]},{\"TagID\":1226,\"TagType\":\"string\",\"Value\":\"**********  \"},{\"TagID\":1163,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":1300,\"TagType\":\"string\",\"Value\":\"010465019391231221%hrH\\\"KpTSMWFY{\"}]},{\"TagID\":1212,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":2115,\"TagType\":\"string\",\"Value\":\"8692\"},{\"TagID\":2106,\"TagType\":\"byte\",\"Value\":0}]},{\"TagID\":1059,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":2102,\"TagType\":\"byte\",\"Value\":0},{\"TagID\":1079,\"TagType\":\"money\",\"Value\":500000},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":500000},{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1200,\"TagType\":\"money\",\"Value\":83333},{\"TagID\":1030,\"TagType\":\"string\",\"Value\":\"Верхняя одежда DIOR EYEWEAR (104993)\"},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1222,\"TagType\":\"byte\",\"Value\":64},{\"TagID\":1224,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":1225,\"TagType\":\"string\",\"Value\":\"OOO \\\"ПРИМЕР КОМ\\\"\"}]},{\"TagID\":1226,\"TagType\":\"string\",\"Value\":\"**********  \"},{\"TagID\":1212,\"TagType\":\"byte\",\"Value\":1}]},{\"TagID\":1059,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":2102,\"TagType\":\"byte\",\"Value\":0},{\"TagID\":1079,\"TagType\":\"money\",\"Value\":500000},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":500000},{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1200,\"TagType\":\"money\",\"Value\":83333},{\"TagID\":1030,\"TagType\":\"string\",\"Value\":\"Детская обувь CHRISTIAN DIOR (95832)\"},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1222,\"TagType\":\"byte\",\"Value\":64},{\"TagID\":1224,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":1225,\"TagType\":\"string\",\"Value\":\"OOO \\\"ПРИМЕР КОМ\\\"\"}]},{\"TagID\":1226,\"TagType\":\"string\",\"Value\":\"**********  \"},{\"TagID\":1212,\"TagType\":\"byte\",\"Value\":1}]},{\"TagID\":1059,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":2102,\"TagType\":\"byte\",\"Value\":0},{\"TagID\":1079,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":6},{\"TagID\":1030,\"TagType\":\"string\",\"Value\":\"Доставка\"},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1212,\"TagType\":\"byte\",\"Value\":4}]},{\"TagID\":1041,\"TagType\":\"string\",\"Value\":\"9999078*********\"},{\"TagID\":1040,\"TagType\":\"uint32\",\"Value\":2380},{\"TagID\":1077,\"TagType\":\"byte[]\",\"Value\":\"MQQ9u3BZ\"},{\"TagID\":1038,\"TagType\":\"uint32\",\"Value\":6},{\"TagID\":1042,\"TagType\":\"uint32\",\"Value\":244}]},\"FiscalSign\":**********,\"GrandTotal\":1500000,\"MarkingInfo\":{\"Tag2106Values\":[0,null,null,null]},\"NonCash\":[0,0,0],\"Password\":30,\"PaymentAgentModes\":0,\"Place\":\"NO-PLACE\",\"QR\":\"t=20220408T1355\\u0026s=15000.00\\u0026fn=9999078*********\\u0026i=2380\\u0026fp=**********\\u0026n=1\",\"TaxCalculationMethod\":1,\"TaxMode\":1,\"Text\":\"КАССОВЫЙ ЧЕК/ПРИХОД\\t08-04-22 13:55\\nOOO \\\"1K\\\"\\nРостов-на-Дону, ул.Баумана 3/3\\nМЕСТО РАСЧЕТОВ\\tNO-PLACE\\n[M] Верхняя одежда FENDI (104790)\\nПОЛНЫЙ РАСЧЕТ\\t1 x 5000.00 ~5000.00\\nТОВАР\\tНДС 20%: ~833.33\\nАГЕНТ\\nИНН ПОСТАВЩИКА\\t**********\\nOOO \\\"ПРИМЕР КОМ\\\"\\n8692\\nВерхняя одежда DIOR EYEWEAR (104993)\\nПОЛНЫЙ РАСЧЕТ\\t1 x 5000.00 ~5000.00\\nТОВАР\\tНДС 20%: ~833.33\\nАГЕНТ\\nИНН ПОСТАВЩИКА\\t**********\\nOOO \\\"ПРИМЕР КОМ\\\"\\nДетская обувь CHRISTIAN DIOR (95832)\\nПОЛНЫЙ РАСЧЕТ\\t1 x 5000.00 ~5000.00\\nТОВАР\\tНДС 20%: ~833.33\\nАГЕНТ\\nИНН ПОСТАВЩИКА\\t**********\\nOOO \\\"ПРИМЕР КОМ\\\"\\nДоставка\\nПОЛНЫЙ РАСЧЕТ\\t1 x 0.00 ~0.00\\nУСЛУГА\\n##BIG##ИТОГ\\t~15000.00\\nВСЕГО ПОЛУЧЕНО\\t~0.00\\nПРЕДВАРИТЕЛЬНАЯ ОПЛАТА (АВАНС)\\t~15000.00\\nСУММА НДС 20%\\t~2500.00\\nККТ ДЛЯ ИНТЕРНЕТ\\nЭЛ.АДР.ОТПРАВИТЕЛЯ\\<EMAIL>\\nЭЛ.АДР.ПОКУПАТЕЛЯ\\<EMAIL>\\nКМ?\\nКАССИР: СИСТ.АДМИНИСТРАТОР\\tИНН: 9705157293\\nСНО\\tОСН\\nЧЕК: 244\\tСМЕНА: 6\\nСАЙТ ФНС\\twww.nalog.gov.ru\\nРН ККТ: 0000000001024245\\tФН: 9999078*********\\nФД: 2380\\tФП: **********\",\"TurnNumber\":6}}],\"Text\":\"КАССОВЫЙ ЧЕК/ПРИХОД\\t08-04-22 13:55\\nOOO \\\"1K\\\"\\nРостов-на-Дону, ул.Баумана 3/3\\nМЕСТО РАСЧЕТОВ\\tNO-PLACE\\n[M] Верхняя одежда FENDI (104790)\\nПОЛНЫЙ РАСЧЕТ\\t1 x 5000.00 ~5000.00\\nТОВАР\\tНДС 20%: ~833.33\\nАГЕНТ\\nИНН ПОСТАВЩИКА\\t**********\\nOOO \\\"ПРИМЕР КОМ\\\"\\n8692\\nВерхняя одежда DIOR EYEWEAR (104993)\\nПОЛНЫЙ РАСЧЕТ\\t1 x 5000.00 ~5000.00\\nТОВАР\\tНДС 20%: ~833.33\\nАГЕНТ\\nИНН ПОСТАВЩИКА\\t**********\\nOOO \\\"ПРИМЕР КОМ\\\"\\nДетская обувь CHRISTIAN DIOR (95832)\\nПОЛНЫЙ РАСЧЕТ\\t1 x 5000.00 ~5000.00\\nТОВАР\\tНДС 20%: ~833.33\\nАГЕНТ\\nИНН ПОСТАВЩИКА\\t**********\\nOOO \\\"ПРИМЕР КОМ\\\"\\nДоставка\\nПОЛНЫЙ РАСЧЕТ\\t1 x 0.00 ~0.00\\nУСЛУГА\\n##BIG##ИТОГ\\t~15000.00\\nВСЕГО ПОЛУЧЕНО\\t~0.00\\nПРЕДВАРИТЕЛЬНАЯ ОПЛАТА (АВАНС)\\t~15000.00\\nСУММА НДС 20%\\t~2500.00\\nККТ ДЛЯ ИНТЕРНЕТ\\nЭЛ.АДР.ОТПРАВИТЕЛЯ\\<EMAIL>\\nЭЛ.АДР.ПОКУПАТЕЛЯ\\<EMAIL>\\nКМ?\\nКАССИР: СИСТ.АДМИНИСТРАТОР\\tИНН: 9705157293\\nСНО\\tОСН\\nЧЕК: 244\\tСМЕНА: 6\\nСАЙТ ФНС\\twww.nalog.gov.ru\\nРН ККТ: 0000000001024245\\tФН: 9999078*********\\nФД: 2380\\tФП: **********\",\"TurnNumber\":6}";

    private static String lastReceiptRequestBody; // to verify requests unchanged

    private static final String REPLACE_REQUEST_JSON_ATTR_NAME = "requestId"; // TODO: need to fix this in StarrysCashRegister for usual receipts, need to use same request ID for same orders only in case of changed data we should change request ID to avoid caching on server

    @Autowired
    OrderService orderService;

    @Autowired
    UserService userService;

    @Autowired
    CounterpartyService counterpartyService;

    @Autowired
    CashRegister cashRegister;

    @Autowired
    FiscalReceiptRequestService fiscalReceiptRequestService;

    @Autowired
    FiscalReceiptRequestRepository fiscalReceiptRequestRepository;

    @Autowired
    CallInTransaction callInTransaction;

    private static ClientAndServer mockReceiptsServer;
    private static ClientAndServer mockB2PServer;
    private static ClientAndServer mockTCBServer;
    private static ObjectMapper mapper = new ObjectMapper();

    @PostConstruct
    public void setUpSuite() {
        mockReceiptsServer = Objects.isNull(mockReceiptsServer) ? startClientAndServer(mockReceiptsServerPort) : mockReceiptsServer;
        mockB2PServer = Objects.isNull(mockB2PServer) ? startClientAndServer(mockB2PServerPort) : mockB2PServer;
        mockTCBServer = Objects.isNull(mockTCBServer) ? startClientAndServer(mockTcbServerPort) : mockTCBServer;
        fiscalReceiptRequestService.setEnableMarkingInfo(null);
    }

    @AfterAll
    public static void tearDownSuite() {
        mockReceiptsServer.stop();
        mockB2PServer.stop();
        mockTCBServer.stop();
    }

    private void voidMail(String mailAddr) {
        User dupe = userService.getUserByEmail(mailAddr);
        if (Objects.isNull(dupe)) {
            return;
        }
        dupe.setEmail(UUID.randomUUID() + "@oskelly.ru");
        userService.save(dupe);
    }

    @BeforeEach
    public void init() {
        ImmutableList.of(sendReceiptToEmail1St, sendReceiptToEmail2Nd)
                .forEach(it -> callInTransaction.runInNewTransaction(() -> voidMail(it)));
    }

    private void removeReceipts4Order(long ordersId, List<FiscalReceiptRequestType> removeType) {
        Order order = orderService.getOrder(ordersId);
        //
        order.setBuyerCheck(null);
        order.setBuyerCheckStartTime(null);
        order.setBuyerCheckEndTime(null);
        //
        fiscalReceiptRequestService.getRequestsForOrder(order.getId()).stream()
                .filter(it -> Objects.isNull(removeType) || removeType.contains(it.getReceiptType()))
                .forEach(it -> fiscalReceiptRequestService.deleteReceiptRequest(order, it.getReceiptType()));
        //
        orderService.saveOrder(order);
    }

    private void mockCashRegisterServerWithReceiptData(String responseBody) {
        mockReceiptsServer.reset();
        new MockServerClient(mockServerHost, mockReceiptsServerPort)
            .when(request().withMethod("POST").withPath("/fr/api/v2/Complex"))
            .respond(new MockCashServer(responseBody));
    }

    private void mockB2PServerWithData(String responseBody) {
        mockReceiptsServer.reset();
        new MockServerClient(mockServerHost, mockB2PServerPort)
                .when(request().withMethod("POST").withPath("/webapi/Complete"))
                .respond(new MockCashServer(responseBody));
    }

    private void mockTCBServerWithData(String responseBody) {
        mockReceiptsServer.reset();
        new MockServerClient(mockServerHost, mockTcbServerPort)
                .when(request().withMethod("POST").withPath("/api/v1/card/unregistered/hold/confirm/partial"))
                .respond(new MockCashServer(responseBody));
    }

    private void replaceRequestIdInReceiptRequestJson(JSONObject receiptRequestJson) throws JSONException {
        Matcher matcherOne = Pattern.compile("(\\d*)-(.*)").matcher(receiptRequestJson.getString(REPLACE_REQUEST_JSON_ATTR_NAME));
        if (matcherOne.matches()) {
            receiptRequestJson.put(REPLACE_REQUEST_JSON_ATTR_NAME, matcherOne.group(1));
            return;
        }
        Matcher matcherTwo = Pattern.compile("(.*)-(\\d*)-(.*)").matcher(receiptRequestJson.getString(REPLACE_REQUEST_JSON_ATTR_NAME));
        if (matcherTwo.matches()) {
            receiptRequestJson.put(REPLACE_REQUEST_JSON_ATTR_NAME, matcherTwo.group(2));
            return;
        }
    }

    @SneakyThrows
    private void assertReceiptRequestEquals(String requestExpectJSON, String requestActualJSON, String requestId) {
        JSONObject expectJson = new JSONObject(requestExpectJSON);
        JSONObject actualJson = new JSONObject(requestActualJSON);
        //
        if (Objects.nonNull(requestId)) {
            Assertions.assertThat(expectJson.getString("requestId")).matches(".*" + Pattern.quote(requestId));
            Assertions.assertThat(actualJson.getString("requestId")).matches(".*" + Pattern.quote(requestId));
        }
        //
        replaceRequestIdInReceiptRequestJson(expectJson);
        replaceRequestIdInReceiptRequestJson(actualJson);
        //
        JsonNode expectNode = mapper.readTree(expectJson.toString());
        JsonNode actualNode = mapper.readTree(actualJson.toString());
        //
        assertEquals(expectNode, actualNode);
    }

    private void assertReceiptResponseEquals(String responseExpectJSON, String responseActualJSON, BigDecimal totalAmount) throws IOException {
        JsonNode expectNode = mapper.readTree(responseExpectJSON);
        JsonNode actualNode = mapper.readTree(responseActualJSON);
        //
        assertEquals(BankCommons.toAmountInCents(totalAmount).longValue(), expectNode.get("GrandTotal").longValue());
        assertEquals(BankCommons.toAmountInCents(totalAmount).longValue(), actualNode.get("GrandTotal").longValue());
        //
        assertEquals(expectNode, actualNode);
    }

    private void getOrderV1Receipts(long ordersId) {
        Order order = orderService.getOrder(orderIdWith1Positions);
        assertNotNull(order, "Unable to find order");
        order.setDeliveryCost(BigDecimal.ZERO);
        orderService.saveOrder(order);
    }

    @Test
    @Transactional
    public void _01_receiptRequestWith1PositionsOkay() throws IOException {
        getOrderV1Receipts(orderIdWith1Positions);
        commitAndStartNewTransaction();
        //
        Order order = orderService.getOrder(orderIdWith1Positions);
        mockCashRegisterServerWithReceiptData(MOCK_RESPONSE_WITH_1_POSITION);
        String receiptResponseJson = cashRegister.requestCheck(order, false, null);
        assertReceiptResponseEquals(MOCK_RESPONSE_WITH_1_POSITION, receiptResponseJson, order.getEffectiveAmount());
        assertReceiptRequestEquals(VERIFY_REQUEST_WITH_1_POSITIONS, lastReceiptRequestBody, null);
    }

    @Test
    @Transactional
    public void _02_receiptRequestWith5PositionsOkay() throws IOException {
        getOrderV1Receipts(orderIdWith5Positions);
        commitAndStartNewTransaction();
        //
        Order order = orderService.getOrder(orderIdWith5Positions);
        mockCashRegisterServerWithReceiptData(MOCK_RESPONSE_WITH_5_POSITION);
        String receiptResponseJson = cashRegister.requestCheck(order, false, null);
        assertReceiptResponseEquals(MOCK_RESPONSE_WITH_5_POSITION, receiptResponseJson, order.getEffectiveAmount());
        assertReceiptRequestEquals(VERIFY_REQUEST_WITH_5_POSITIONS, lastReceiptRequestBody, null);
    }

    private void changeUserEmail(Long userId, String userEmail) {
        User user = userService.getUserById(userId).orElseThrow(() -> new IllegalArgumentException("Unable to find user"));
        user.setEmail(userEmail);
        userService.save(user);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _03_receiptRequestDeliveryAdvancePaymentOkay() throws IOException {
        removeReceipts4Order(orderIdWith1Positions, null);
        Order orderInit = orderService.getOrder(orderIdWith1Positions);
        orderInit.setDeliveryCost(BigDecimal.valueOf(350_00, 2));
        changeUserEmail(orderInit.getBuyer().getId(), sendReceiptToEmail1St);
        commitAndStartNewTransaction();
        prepareUsualSellerCounterparty(orderIdWith1Positions);
        commitAndStartNewTransaction();
        //
        Order order = orderService.getOrder(orderIdWith1Positions);
        assertNotNull(order, "Unable to find order");
        //
        mockCashRegisterServerWithReceiptData(MOCK_RESPONSE_FOR_ADVANCE_RECEIPT);
        FiscalReceiptRequest receiptRequestAdvance = fiscalReceiptRequestService.createReceiptRequest(order, FiscalReceiptRequestKind.ADVANCE_RECEIPT);
        Assertions.assertThat(receiptRequestAdvance.getReceiptType()).isEqualTo(FiscalReceiptRequestType.DELIVERY_ADVANCE);
        BuyerCheckRequest receiptRequestAdvanceData = mapper.readValue(receiptRequestAdvance.getReceiptRequest(), BuyerCheckRequest.class);
        String receiptResponseJsonAdvance = cashRegister.requestReceipt(receiptRequestAdvanceData);
        assertReceiptResponseEquals(MOCK_RESPONSE_FOR_ADVANCE_RECEIPT, receiptResponseJsonAdvance, order.getDeliveryCost());
        assertReceiptRequestEquals(VERIFY_REQUEST_WITH_ADVANCE_RECEIPT, lastReceiptRequestBody, order.getId() + "-advance");
        //
        mockCashRegisterServerWithReceiptData(MOCK_RESPONSE_FOR_ADVANCE_PAYMENT_RECEIPT);
        FiscalReceiptRequest receiptRequestPayment = fiscalReceiptRequestService.createReceiptRequest(order, FiscalReceiptRequestKind.PAYMENT_RECEIPT);
        Assertions.assertThat(receiptRequestPayment.getReceiptType()).isEqualTo(FiscalReceiptRequestType.DELIVERY_PAYMENT);
        BuyerCheckRequest receiptRequestPaymentData = mapper.readValue(receiptRequestPayment.getReceiptRequest(), BuyerCheckRequest.class);
        String receiptResponseJsonPayment = cashRegister.requestReceipt(receiptRequestPaymentData);
        assertReceiptResponseEquals(MOCK_RESPONSE_FOR_ADVANCE_PAYMENT_RECEIPT, receiptResponseJsonPayment, order.getDeliveryCost());
        assertReceiptRequestEquals(VERIFY_REQUEST_WITH_ADVANCE_PAYMENT_RECEIPT, lastReceiptRequestBody, order.getId() + "-payment");
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _05_createSameReceiptRequestsLeadsToOnlyOneRequest() {
        // unable to create two receipt requests with same kind for one order
        Order order = orderService.getOrder(orderIdReceiptsTcb);
        assertNotNull(order,"Unable to find order");
        //
        order.setPaymentVersion(TcbBankService.SCHEMA);
        changeUserEmail(order.getBuyer().getId(), sendReceiptToEmail1St);
        //
        fiscalReceiptRequestService.createReceiptRequest(order, FiscalReceiptRequestKind.ADVANCE_RECEIPT);
        fiscalReceiptRequestService.createReceiptRequest(order, FiscalReceiptRequestKind.ADVANCE_RECEIPT);
        //
        fiscalReceiptRequestService.createReceiptRequest(order, FiscalReceiptRequestKind.PAYMENT_RECEIPT);
        fiscalReceiptRequestService.createReceiptRequest(order, FiscalReceiptRequestKind.PAYMENT_RECEIPT);
        //
        List<FiscalReceiptRequest> receiptRequests = fiscalReceiptRequestService.getRequestsForOrder(order.getId());
        assertEquals(2, receiptRequests.size()); // Only two receipt requests must be queued
    }

    @Test
    @Transactional
    public void _06_PaymentReceiptNotSentWhenNoAdvanceReceiptFound() {
        removeReceipts4Order(orderIdReceiptsTcb, null);
        removeReceipts4Order(orderIdWith1Positions, null);
        //
        Order orderWithAdvance = orderService.getOrder(orderIdWith1Positions);
        assertNotNull(orderWithAdvance, "Unable to find order");
        orderWithAdvance.setPaymentVersion(TcbBankService.SCHEMA);
        changeUserEmail(orderWithAdvance.getBuyer().getId(), sendReceiptToEmail1St);
        //
        Order orderWithPayment = orderService.getOrder(orderIdReceiptsTcb);
        assertNotNull(orderWithPayment, "Unable to find order");
        orderWithPayment.setPaymentVersion(TcbBankService.SCHEMA);
        changeUserEmail(orderWithPayment.getBuyer().getId(), sendReceiptToEmail2Nd);
        //
        fiscalReceiptRequestService.createReceiptRequest(orderWithAdvance, FiscalReceiptRequestKind.ADVANCE_RECEIPT);
        fiscalReceiptRequestService.createReceiptRequest(orderWithAdvance, FiscalReceiptRequestKind.PAYMENT_RECEIPT);
        removeReceipts4Order(orderWithAdvance.getId(), ImmutableList.of(FiscalReceiptRequestType.DELIVERY_PAYMENT));
        //
        fiscalReceiptRequestService.createReceiptRequest(orderWithPayment, FiscalReceiptRequestKind.ADVANCE_RECEIPT);
        fiscalReceiptRequestService.createReceiptRequest(orderWithPayment, FiscalReceiptRequestKind.PAYMENT_RECEIPT);
        removeReceipts4Order(orderWithPayment.getId(), ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE));
        //
        List<FiscalReceiptRequest> receiptRequests4Advance = fiscalReceiptRequestService.getRequestsForOrder(orderWithAdvance.getId());
        assertEquals(1, receiptRequests4Advance.size());
        List<FiscalReceiptRequest> receiptRequests4Payment = fiscalReceiptRequestService.getRequestsForOrder(orderWithPayment.getId());
        assertEquals(1, receiptRequests4Payment.size());
        //
        mockCashRegisterServerWithReceiptData(MOCK_RESPONSE_FOR_ADVANCE_RECEIPT); // Any callback data is okay, we need to verify that 1st receipt processed while 2nd skipped
        List<FiscalReceiptRequestDTO> processedDTOs = fiscalReceiptRequestService.processReceiptsQueue();
        //
        Long advanceReceiptId = receiptRequests4Advance.get(0).getId();
        Long paymentReceiptId = receiptRequests4Payment.get(0).getId();
        //
        Optional<FiscalReceiptRequestDTO> advanceRequest = processedDTOs.stream().filter(fr -> fr.getId().equals(advanceReceiptId)).findAny();
        Assertions.assertThat(advanceRequest).hasValueSatisfying(fr ->
                Assertions.assertThat(fr.getState()).isEqualTo(FiscalReceiptRequestState.DONE)
        );
        Optional<FiscalReceiptRequestDTO> paymentRequest = processedDTOs.stream().filter(fr -> fr.getId().equals(paymentReceiptId)).findAny();
        Assertions.assertThat(paymentRequest).hasValueSatisfying(fr ->
                Assertions.assertThat(fr.getState()).isEqualTo(FiscalReceiptRequestState.ERROR_ON_SEND)
        );
    }

    private void prepareAgentOrderProperties(Order order) {
        order.setAcquirerOrderId("10");
        order.setBuyerCheckStartTime(null);
        order.setBuyerCheck(null);
        order.setBuyerCheckEndTime(null);
        order.setDeliveryCost(new BigDecimal("350"));
        order.setPaymentVersion(TcbBankService.SCHEMA);
        order.setState(OrderState.MONEY_TRANSFERRED);
        order.setDeliveryState(DeliveryState.FROM_OFFICE_TO_BUYER);
        changeUserEmail(order.getBuyer().getId(), sendReceiptToEmail1St);
    }

    private void testVatRateIndexLimits(Counterparty counterparty) {
        counterparty.setVatRateIndex(null); // just null
        assertFalse(counterparty.isVatRateIndexValid());
        //
        counterparty.setVatRateIndex(7);    // maxRate + 1
        assertFalse(counterparty.isVatRateIndexValid());
        //
        counterparty.setVatRateIndex(0);    // minRate - 1
        assertFalse(counterparty.isVatRateIndexValid());
    }

    private void prepareAgentSellerCounterparty(Order order, Integer vatRateIndex) {
        order.getSellerUser().getCounterparties()
                .forEach(it -> counterpartyService.setDeleteTime(it, ZonedDateTime.now()));
        //
        Counterparty counterpartyData = new JurCounterparty()
                .setCompanyForm("OOO").setCompanyName("ПРИМЕР КОМ")
                .setJurAddress(null).setPhysAddress(null)
                .setBik("*********").setPaymentAccount("40702810400260004426").setCorrespondentAccount("30101810600000000774").setInn("**********")
                .setUser(order.getSellerUser());
        //
        testVatRateIndexLimits(counterpartyData);
        //
        counterpartyData.setVatRateIndex(vatRateIndex);
        assertTrue(counterpartyData.isVatRateIndexValid());
        //
        Counterparty counterpartyItem = counterpartyService.save(counterpartyData);
        order.setSellerCounterparty(counterpartyItem);
    }

    private void prepareUsualSellerCounterparty(long ordersId) {
        Order order = orderService.getOrder(ordersId);
        order.getSellerUser().getCounterparties()
                .forEach(it -> counterpartyService.setDeleteTime(it, it.getType() == CounterpartyType.LEGAL_ENTITY ? ZonedDateTime.now() : null));
    }

    private void prepareMarkingCode(Order order, List<String> markingCodeList) {
        List<OrderPosition> sortedOrderPositions = order.getOrderPositions().stream().sorted(Comparator.comparingLong(OrderPosition::getId)).collect(Collectors.toList());
        for (int i = 0; i < sortedOrderPositions.size(); i++) {
            sortedOrderPositions.get(i).setDatamatrix(markingCodeList.get(i));
            sortedOrderPositions.get(i).setMarkingCodeApprovalTime(LocalDateTime.now(Clock.systemUTC()));
        }
    }

    private long _11_receiptsWithMarkingCodeOkaySaveOrder() {
        Order order = orderService.getOrder(orderIdMarkingCode);
        assertNotNull(order, "Unable to find order");
        changeUserEmail(order.getBuyer().getId(), sendReceiptToEmail1St);
        //
        prepareAgentOrderProperties(order);
        prepareAgentSellerCounterparty(order, 2);
        prepareMarkingCode(order, Arrays.asList(null, null, SHOE_MARK_CODE));
        //
        order.setDeliveryCost(BigDecimal.ZERO);
        orderService.saveOrder(order);
        //
        removeReceipts4Order(order.getId(), null);
        //
        return order.getId();
    }

    @SneakyThrows
    private void _11_ReceiptsWithMarkingCodeOkay(boolean enableMarkingCodes) {
        fiscalReceiptRequestService.setEnableMarkingInfo(enableMarkingCodes);
        //
        long ordersId = _11_receiptsWithMarkingCodeOkaySaveOrder();
        commitAndStartNewTransaction();
        //
        Order order = orderService.getOrder(ordersId);
        //
        mockCashRegisterServerWithReceiptData(MOCK_RESPONSE_FOR_ADVANCE_MARK_RECEIPT);
        FiscalReceiptRequest receiptRequestAdvanceDb = fiscalReceiptRequestService.createReceiptRequest(order, FiscalReceiptRequestKind.ADVANCE_RECEIPT);
        Assertions.assertThat(receiptRequestAdvanceDb.getReceiptType()).isEqualTo(FiscalReceiptRequestType.AGENT_ADVANCE);
        BuyerCheckRequest receiptRequestAdvance = mapper.readValue(receiptRequestAdvanceDb.getReceiptRequest(), BuyerCheckRequest.class);
        String receiptResponseJsonAdvance = cashRegister.requestReceipt(receiptRequestAdvance);
        assertReceiptResponseEquals(MOCK_RESPONSE_FOR_ADVANCE_MARK_RECEIPT, receiptResponseJsonAdvance, order.getEffectiveAmount());
        assertReceiptRequestEquals(VERIFY_REQUEST_WITH_ADVANCE_MARK_RECEIPT, lastReceiptRequestBody, order.getId() + "-advance");
        //
        mockCashRegisterServerWithReceiptData(MOCK_RESPONSE_FOR_ADVANCE_PAYMENT_MARK_RECEIPT);
        FiscalReceiptRequest receiptRequestPaymentDb = fiscalReceiptRequestService.createReceiptRequest(order, FiscalReceiptRequestKind.PAYMENT_RECEIPT);
        Assertions.assertThat(receiptRequestPaymentDb.getReceiptType()).isEqualTo(FiscalReceiptRequestType.AGENT_PAYMENT);
        BuyerCheckRequest receiptRequestPayment = mapper.readValue(receiptRequestPaymentDb.getReceiptRequest(), BuyerCheckRequest.class);
        String receiptResponseJsonPayment = cashRegister.requestReceipt(receiptRequestPayment);
        assertReceiptResponseEquals(MOCK_RESPONSE_FOR_ADVANCE_PAYMENT_MARK_RECEIPT, receiptResponseJsonPayment, order.getEffectiveAmount());
        String expected = enableMarkingCodes
                ? VERIFY_REQUEST_WITH_ADVANCE_PAYMENT_MARK_RECEIPT_ENABLED
                : VERIFY_REQUEST_WITH_ADVANCE_PAYMENT_MARK_RECEIPT_DISABLED;
        assertReceiptRequestEquals(expected, lastReceiptRequestBody, order.getId() + "-payment");
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _07_01_ReceiptsWithMarkingCodeEnabledOkay() {
        _11_ReceiptsWithMarkingCodeOkay(true);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _07_02_ReceiptsWithMarkingCodeDisabledOkay() {
        _11_ReceiptsWithMarkingCodeOkay(false);
    }

    @AllArgsConstructor
    public static class MockCashServer implements ExpectationResponseCallback {

        private String responseBody;

        @Override
        public HttpResponse handle(HttpRequest httpRequest) {
            lastReceiptRequestBody = httpRequest.getBodyAsString();
            return response().withStatusCode(OK_200.code())
                .withHeaders(new Header("Content-Type", "application/json;charset=UTF-8"))
                .withBody(responseBody);
        }
    }

}