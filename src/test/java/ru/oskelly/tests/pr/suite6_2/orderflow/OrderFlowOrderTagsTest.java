package ru.oskelly.tests.pr.suite6_2.orderflow;

import com.google.common.collect.Lists;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.pr.common.bonuses.BonusesServiceTestConfiguration;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestTcbMock;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.domain.dao.user.SaleShipmentRouteRepository;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderExtraPropInfo;
import su.reddot.domain.model.order.OrderExtraPropValue;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.user.SaleShipmentRoute;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.adminpanel.tag.UserCommonTagCode;
import su.reddot.domain.service.adminpanel.tag.UserCommonTagService;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.order.impl.OrderExtraPropsService;
import su.reddot.domain.service.product.ProductService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.TcbBankService;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.Clock;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


@TestMethodOrder(MethodOrderer.MethodName.class)
@ContextConfiguration(classes = {OrderFlowTestUtils.class, BonusesServiceTestConfiguration.class})
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
@TestPropertySource(properties = {"payments.cb-fallback-mode=plutus/two-step-pay"})
public class OrderFlowOrderTagsTest extends AbstractSpringTest {

    @Autowired
    private Clock clock;

    @Autowired
    private UserService userService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private ProductService productService;

    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private CartTestSupport cartTestSupport;
    @Autowired
    private UserCommonTagService userCommonTagService;
    @Autowired
    private SaleShipmentRouteRepository saleShipmentRouteRepository;

    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user-password}")
    private String password;
    @Value("${test-prepayments.usual-seller-id}")
    private Long usualSellerId;
    @Value("${test-prepayments.cbord-seller-id}")
    private Long cbordSellerId;

    private final long vipSellerId = 19554;
    private final long influencerSellerId = 16533;

    private static OrderFlowTestTcbMock orderFlowTestTcbMock;

    @Value("${test.receipts.mock-server-host}")
    private String mockServerHost;
    @Value("${test.receipts.mock-server-tcb-bank-port}")
    private Integer mockTcbServerPort;

    @PostConstruct
    private void init() {
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(TcbBankService.SCHEMA));
        User buyer = userService.getUserByEmail(buyerEmail);
        ApiV2Client apiV2Client = new ApiV2Client(buyerEmail, password);
        orderFlowTestUtils.init(buyerEmail, password);
        cartTestSupport.setUserId(buyer.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
        orderFlowTestTcbMock = Objects.isNull(orderFlowTestTcbMock) ? new OrderFlowTestTcbMock(mockServerHost, mockTcbServerPort) : orderFlowTestTcbMock;
    }

    @AfterAll
    public static void done() {
        orderFlowTestTcbMock.stop();
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_OrderFlow_OrderTags_None() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(1)
                .build()
        );
        products.get(0).setBeegzStatusTime(null);
        products.get(0).setSelectedConciergeTime(null);
        productService.saveProduct(products.get(0), ProductService.UserType.SYSTEM);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.fillCart(products);
        //
        OrderService.InitOrderResult testOrder = orderFlowTestUtils.holdOrderWithPromoCodePS(products.get(0).getSeller(), null, TcbBankService.SCHEMA);
        //
        OrderDTO orderInfo = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getOrderId(), true);
        Assertions.assertThat(orderInfo.getId()).isNotEqualTo(0);
        //
        Order order = orderService.getOrder(orderInfo.getId());
        Assertions.assertThat(order.getId()).isEqualTo(orderInfo.getId());
        //
        Assertions.assertThat(order.getOrderExtraPropValues()).hasSize(0);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _02_OrderFlow_OrderTags_Beegz() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(1)
                .build()
        );
        products.get(0).setBeegzStatusTime(LocalDateTime.now(clock));
        products.get(0).setSelectedConciergeTime(null);
        productService.saveProduct(products.get(0), ProductService.UserType.SYSTEM);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.fillCart(products);
        //
        OrderService.InitOrderResult testOrder = orderFlowTestUtils.holdOrderWithPromoCodePS(products.get(0).getSeller(), null, TcbBankService.SCHEMA);
        //
        OrderDTO orderInfo = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getOrderId(), true);
        Assertions.assertThat(orderInfo.getId()).isNotEqualTo(0);
        //
        Order order = orderService.getOrder(orderInfo.getId());
        Assertions.assertThat(order.getId()).isEqualTo(orderInfo.getId());
        //
        Assertions.assertThat(OrderExtraPropsService.isOrderTagExists(order, OrderExtraPropInfo.ORDER_EXTRA_PROP_BEEGZ)).isTrue();
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _03_OrderFlow_OrderTags_Concierge() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(1)
                .build()
        );
        products.get(0).setBeegzStatusTime(null);
        products.get(0).setSelectedConciergeTime(LocalDateTime.now(clock));
        productService.saveProduct(products.get(0), ProductService.UserType.SYSTEM);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.fillCart(products);
        //
        OrderService.InitOrderResult testOrder = orderFlowTestUtils.holdOrderWithPromoCodePS(products.get(0).getSeller(), null, TcbBankService.SCHEMA);
        //
        OrderDTO orderInfo = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getOrderId(), true);
        Assertions.assertThat(orderInfo.getId()).isNotEqualTo(0);
        //
        Order order = orderService.getOrder(orderInfo.getId());
        Assertions.assertThat(order.getId()).isEqualTo(orderInfo.getId());
        //
        Assertions.assertThat(OrderExtraPropsService.isOrderTagExists(order, OrderExtraPropInfo.ORDER_EXTRA_PROP_CONCIERGE)).isTrue();
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _04_OrderFlow_OrderTags_Vip() {
        User seller = userService.getOne(vipSellerId);
        userCommonTagService.bindTagToUser(seller, UserCommonTagCode.VIP_STATUS_VIP_STATUS.name());
        userService.save(seller);
        commitAndStartNewTransaction();
        //
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(vipSellerId)
                .maxItems(1)
                .build()
        );
        products.get(0).setBeegzStatusTime(null);
        products.get(0).setSelectedConciergeTime(null);
        productService.saveProduct(products.get(0), ProductService.UserType.SYSTEM);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.fillCart(products);
        //
        OrderService.InitOrderResult testOrder = orderFlowTestUtils.holdOrderWithPromoCodePS(products.get(0).getSeller(), null, TcbBankService.SCHEMA);
        //
        OrderDTO orderInfo = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getOrderId(), true);
        Assertions.assertThat(orderInfo.getId()).isNotEqualTo(0);
        //
        Order order = orderService.getOrder(orderInfo.getId());
        Assertions.assertThat(order.getId()).isEqualTo(orderInfo.getId());
        //
        Assertions.assertThat(OrderExtraPropsService.isOrderTagExists(order, OrderExtraPropInfo.ORDER_EXTRA_PROP_VIP)).isTrue();
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _04_OrderFlow_OrderTags_Influencer() {
        User seller = userService.getOne(influencerSellerId);
        userCommonTagService.bindTagToUser(seller, UserCommonTagCode.USER_STATUS_INFLUENCER.name());
        userService.save(seller);
        commitAndStartNewTransaction();
        //
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(influencerSellerId)
                .maxItems(1)
                .build()
        );
        products.get(0).setBeegzStatusTime(null);
        products.get(0).setSelectedConciergeTime(null);
        productService.saveProduct(products.get(0), ProductService.UserType.SYSTEM);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.fillCart(products);
        //
        OrderService.InitOrderResult testOrder = orderFlowTestUtils.holdOrderWithPromoCodePS(products.get(0).getSeller(), null, TcbBankService.SCHEMA);
        //
        OrderDTO orderInfo = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getOrderId(), true);
        Assertions.assertThat(orderInfo.getId()).isNotEqualTo(0);
        //
        Order order = orderService.getOrder(orderInfo.getId());
        Assertions.assertThat(order.getId()).isEqualTo(orderInfo.getId());
        //
        Assertions.assertThat(OrderExtraPropsService.isOrderTagExists(order, OrderExtraPropInfo.ORDER_EXTRA_PROP_INFLUENCER)).isTrue();
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _05_OrderFlow_OrderTags_VipBeegzConcierge() {
        User seller = userService.getOne(vipSellerId);
        userCommonTagService.bindTagToUser(seller, UserCommonTagCode.VIP_STATUS_VIP_STATUS.name());
        userService.save(seller);
        commitAndStartNewTransaction();
        //
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(vipSellerId)
                .maxItems(1)
                .build()
        );
        products.get(0).setBeegzStatusTime(LocalDateTime.now(clock));
        products.get(0).setSelectedConciergeTime(LocalDateTime.now(clock));
        productService.saveProduct(products.get(0), ProductService.UserType.SYSTEM);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.fillCart(products);
        //
        OrderService.InitOrderResult testOrder = orderFlowTestUtils.holdOrderWithPromoCodePS(products.get(0).getSeller(), null, TcbBankService.SCHEMA);
        //
        OrderDTO orderInfo = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getOrderId(), true);
        Assertions.assertThat(orderInfo.getId()).isNotEqualTo(0);
        //
        Order order = orderService.getOrder(orderInfo.getId());
        Assertions.assertThat(order.getId()).isEqualTo(orderInfo.getId());
        //
        Assertions.assertThat(OrderExtraPropsService.isOrderTagExists(order, OrderExtraPropInfo.ORDER_EXTRA_PROP_VIP)).isTrue();
        Assertions.assertThat(OrderExtraPropsService.isOrderTagExists(order, OrderExtraPropInfo.ORDER_EXTRA_PROP_CONCIERGE)).isTrue();
        Assertions.assertThat(OrderExtraPropsService.isOrderTagExists(order, OrderExtraPropInfo.ORDER_EXTRA_PROP_BEEGZ)).isTrue();
        Assertions.assertThat(OrderExtraPropsService.isOrderTagExists(order, OrderExtraPropInfo.ORDER_EXTRA_PROP_INFLUENCER)).isFalse();
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _06_OrderFlow_OrderTags_CrossBorder() {
        SaleShipmentRoute saleShipmentRoute = saleShipmentRouteRepository.getBySystemName("EuropeDubaiMoscowGBS").get();
        //
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(cbordSellerId)
                .maxItems(1)
                .build()
        );
        products.get(0).setSaleShipmentRoute(saleShipmentRoute);
        orderFlowTestUtils.setProductPrice(products.get(0), "EUR", BigDecimal.valueOf(1_000_00, 2));
        productService.saveProduct(products.get(0), ProductService.UserType.SYSTEM);
        User seller = products.get(0).getSeller();
        seller.setSaleShipmentRoute(saleShipmentRoute);
        userService.save(seller);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.fillCart(products);
        //
        OrderService.InitOrderResult testOrder = orderFlowTestUtils.holdOrderWithPromoCodePS(products.get(0).getSeller(), null, TcbBankService.SCHEMA);
        //
        OrderDTO orderInfo = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getOrderId(), true);
        Assertions.assertThat(orderInfo.getId()).isNotEqualTo(0);
        //
        Order order = orderService.getOrder(orderInfo.getId());
        Assertions.assertThat(order.getId()).isEqualTo(orderInfo.getId());
        //
        Assertions.assertThat(OrderExtraPropsService.isOrderTagExists(order, OrderExtraPropInfo.ORDER_EXTRA_PROP_ORDER_TAG_CROSSBORDER)).isTrue();
        Assertions.assertThat(
                OrderExtraPropsService
                        .getOrderExtraPropValue(order, OrderExtraPropInfo.ORDER_EXTRA_PROP_SALE_SHIPMENT_ROUTE)
                        .map(OrderExtraPropValue::getPropValue)
        ).isEqualTo(Optional.of(saleShipmentRoute.getId().toString()));
    }

}
