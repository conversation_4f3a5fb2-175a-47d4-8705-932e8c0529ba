package ru.oskelly.tests.pr.suite6_2.orderflow;

import com.google.common.collect.Lists;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.pr.common.bonuses.BonusesServiceTestConfiguration;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestTcbMock;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.AddressTestSupport;
import su.reddot.component.CartTestSupport;
import su.reddot.component.HoldRequest;
import su.reddot.domain.model.address.Address;
import su.reddot.domain.model.address.City;
import su.reddot.domain.model.address.Country;
import su.reddot.domain.model.addressendpoint.AddressEndpoint;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.address.AddressService;
import su.reddot.domain.service.address.CityService;
import su.reddot.domain.service.address.CountryService;
import su.reddot.domain.service.addressendpoint.AddressEndpointService;
import su.reddot.domain.service.currency.CurrencyService;
import su.reddot.domain.service.dto.order.GroupedCart;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.dto.order.OrderPositionDTO;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.bank.jobs.AgentPaymentJobs;
import su.reddot.infrastructure.logistic.DeliveryState;
import su.reddot.infrastructure.util.CallInTransaction;
import su.reddot.oskelly.orderprocessing.internal.web.client.OrderMobileApi;
import su.reddot.oskelly.orderprocessing.internal.web.dto.IntegrationMobileOrderExpertiseDTO;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@TestMethodOrder(MethodOrderer.MethodName.class)
@ContextConfiguration(classes = {OrderFlowTestUtils.class, BonusesServiceTestConfiguration.class})
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
public class OrderFlowOrderDeliveryTest extends AbstractSpringTest {

    @Autowired
    private UserService userService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private CurrencyService currencyService;
    @Autowired
    private AgentPaymentJobs agentPaymentJobs;

    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private CartTestSupport cartTestSupport;
    @Autowired
    private CallInTransaction callInTransaction;

    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user-password}")
    private String password;
    @Value("${test-prepayments.usual-seller-id}")
    private Long usualSellerId;
    @Value("${test-prepayments.usual-seller-counterparty-id}")
    private Long usualSellerCounterpartyId;
    @Value("${test-prepayments.pickup-id}")
    private Long pickupId;
    @Value("${test-prepayments.delivery-id}")
    private Long deliveryId;

    private static OrderFlowTestTcbMock orderFlowTestTcbMock;

    @Value("${test.receipts.mock-server-host}")
    private String mockServerHost;
    @Value("${test.receipts.mock-server-tcb-bank-port}")
    private Integer mockTcbServerPort;

    @Autowired
    private CityService cityService;

    @Autowired
    private CountryService countryService;

    @Autowired
    private AddressService addressService;

    @Autowired
    private AddressEndpointService addressEndpointService;

    @Autowired
    private AddressTestSupport addressTestSupport;

    @MockBean
    private OrderMobileApi orderMobileApi;

    private long deliveryEpTK;
    private long deliveryEpAF;
    private long deliveryEpBW;

    private Address findOrCreateAddress(String usersEmail, long cityId) {
        User user = userService.getUserByEmail(usersEmail);
        Optional<Address> result = user.getAddressEndpoints().stream()
                .map(AddressEndpoint::getAddress)
                .filter(addr -> Objects.nonNull(addr.getCityData()))
                .filter(addr -> Objects.equals(addr.getCityData().getId(), cityId))
                .findAny();
        if (result.isPresent()) {
            return result.get();
        }
        //
        City city = cityService.findCityById(cityId);
        Country country = countryService.findById(city.getCountryId()).orElse(null);
        //
        addressTestSupport.addDeliveryCompanySupport(city.getCountryId(), cityId);
        //
        Address address = new Address();
        address.setCityData(city);
        address.setCountryData(country);
        //
        address.setCity(city.getName());
        address.setCountry(country.getName());
        address.setAddress(getClass().getSimpleName() + " street, b. 14");
        //
        address = addressService.saveAddress(user, address);
        //
        return address;
    }

    private AddressEndpoint findOrCreateAddressEndpoint(String usersEmail, Address address) {
        User user = userService.getUserByEmail(usersEmail);
        Optional<AddressEndpoint> result = user.getAddressEndpoints().stream()
                .filter(a -> a.getAddress().getId().equals(address.getId()))
                .findAny();
        if (result.isPresent()) {
            return result.get();
        }
        AddressEndpoint addressEndpoint = new AddressEndpoint();
        addressEndpoint.setAddress(address);
        addressEndpoint.setPhone("+79991234567");
        addressEndpoint.setLastName("Quality");
        addressEndpoint.setFirstName("Assurance");
        addressEndpoint = addressEndpointService.save(user, addressEndpoint);
        return addressEndpoint;
    }

    private long prepareAddressEndpointInCountry(String usersEmail, String countryCode2, long cityId) {
        City city = cityService.findCityById(cityId);
        Country country = countryService.findById(city.getCountryId()).orElse(null);
        if (!Objects.equals(country.getIsoCodeAlpha2(), countryCode2)) {
            throw new IllegalArgumentException();
        }
        //
        Address address = findOrCreateAddress(usersEmail, cityId);
        //
        AddressEndpoint addressEndpoint = findOrCreateAddressEndpoint(usersEmail, address);
        //
        return addressEndpoint.getId();
    }

    private Long prepareAdminUser() {
        User adminUser = userService.getUserByEmail(buyerEmail);
        orderFlowTestUtils.enableUserAuthority(adminUser.getId(), AuthorityName.ORDER_PAYOUTS, true);
        orderFlowTestUtils.enableUserAuthority(adminUser.getId(), AuthorityName.ORDER_MANUAL_CHANGE_DELIVERY_STATE, true);
        return adminUser.getId();
    }

    @PostConstruct
    private void init() {
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(TcbBankService.SCHEMA));
        User buyer = userService.getUserByEmail(buyerEmail);
        ApiV2Client apiV2Client = new ApiV2Client(buyerEmail, password);
        orderFlowTestUtils.init(buyerEmail, password);
        cartTestSupport.setUserId(buyer.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
        orderFlowTestTcbMock = Objects.isNull(orderFlowTestTcbMock) ? new OrderFlowTestTcbMock(mockServerHost, mockTcbServerPort) : orderFlowTestTcbMock;
        callInTransaction.runInNewTransaction(this::prepareAdminUser);
        deliveryEpAF = callInTransaction.runInNewTransaction(() -> prepareAddressEndpointInCountry(buyerEmail, "AF", 62));
        deliveryEpBW = callInTransaction.runInNewTransaction(() -> prepareAddressEndpointInCountry(buyerEmail, "BW", 15729));
        deliveryEpTK = callInTransaction.runInNewTransaction(() -> prepareAddressEndpointInCountry(buyerEmail, "TK", 169672));
        Mockito.when(orderMobileApi.getMobileOrderExpertise(Mockito.any()))
                .thenReturn(new IntegrationMobileOrderExpertiseDTO().items(Collections.emptyList()));
        callInTransaction.runInNewTransaction(() -> orderFlowTestUtils.changeToSimpleUserNoCB(usualSellerId));
    }

    @AfterAll
    public static void done() {
        orderFlowTestTcbMock.stop();
    }

    private void _01_orderFlow_orderDelivery_deliveryCostInCurrency_createOrder(long addressEndpointId, String currencyCode, BigDecimal deliveryInCurrency) {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(5)
                .build()
        );
        long sellerId = products.get(0).getSeller().getId();
        commitAndStartNewTransaction();
        //
        boolean isBaseCurrency = currencyService.getBaseCurrency().getIsoCode().equalsIgnoreCase(currencyCode);
        //
        GroupedCart cartOne = orderFlowTestUtils.fillCart(products);
        BigDecimal dc1st = cartOne.getGroup(sellerId).getDeliveryCost();
        Assertions.assertThat(dc1st).isEqualByComparingTo(BigDecimal.valueOf(500)); // Default delivery cost value
        //
        orderFlowTestUtils.setCartAddressEndpoint(addressEndpointId);
        //
        GroupedCart cart2nd = cartTestSupport.getCart(true, null);
        BigDecimal dc2nd = cart2nd.getGroup(sellerId).getDeliveryCost();
        if (isBaseCurrency) {
            Assertions.assertThat(dc2nd).isEqualByComparingTo(deliveryInCurrency); // Cost for region but in base currency
        } else {
            Assertions.assertThat(dc2nd).isNotEqualByComparingTo(deliveryInCurrency); // Cost for region but in base currency
        }
        //
        GroupedCart cart3rd = cartTestSupport.getCart(true, currencyCode);
        BigDecimal dc3rd = cart3rd.getGroup(sellerId).getDeliveryCost();
        Assertions.assertThat(dc3rd).isEqualByComparingTo(deliveryInCurrency); // Cost for region in specified currency
        //
        HoldRequest holdRequest = HoldRequest.builder()
                .paymentSystem(TcbBankService.SCHEMA)
                .build();
        //
        OrderService.InitOrderResult testOrder = cartTestSupport.holdCartWithParams(sellerId, holdRequest);
        //
        orderFlowTestUtils.callOrderHoldCallback(testOrder.getOrderId(), testOrder.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        OrderDTO orderInfo = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getOrderId(), true);
        Assertions.assertThat(orderInfo.getId()).isNotEqualTo(0);
        //
        Order order = orderService.getOrder(orderInfo.getId());
        Assertions.assertThat(order.getId()).isEqualTo(orderInfo.getId());
        //
        orderInfo.getItems().forEach(op -> orderFlowTestUtils.rejectOrApprovePosition(op.getId(), true));
        orderFlowTestUtils.changeAddressEndpoint(orderInfo.getId(), pickupId, null);
        //
        orderFlowTestUtils.takeOurselves(orderInfo.getId(), null);
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.OURSELVES_FROM_SELLER_TO_OFFICE, true);
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE, true);
        //
        for (OrderPositionDTO orderPosition : orderInfo.getItems()) {
            orderFlowTestUtils.adminsApi1expertise(orderPosition.getId(), true, OrderFlowTestUtils.ExpertiseAction.EXPERTISE_PASSED_OK, null);
        }
        //
        orderFlowTestUtils.adminPanel_Charge(orderInfo.getId());
        orderFlowTestUtils.processHoldComplete(orderInfo);
        //
        orderFlowTestUtils.sendOurselves(orderInfo.getId(), null);
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.OURSELVES_FROM_OFFICE_TO_BUYER, true);
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.DELIVERED_TO_BUYER, true);
        //
        orderFlowTestUtils.sendAgentReport(orderInfo.getId(), true);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.changeSellerCounterparty(orderInfo.getId(), usualSellerCounterpartyId, HttpStatus.Series.SUCCESSFUL);
        //
        orderFlowTestUtils.confirmAgentReport(orderInfo.getId(), true);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.prepareSellerPayout(orderInfo.getId(), true);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.loadAgentReport(orderInfo.getId());
        //
        orderFlowTestUtils.transferMoneyToSellers(orderInfo.getId());
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.MONEY_PAYMENT_WAIT);
        //
        orderFlowTestUtils.validateMoneyToSellers(orderInfo.getId());
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.COMPLETED);
        //
        agentPaymentJobs.transferMoneyToSeller();
        //
        orderFlowTestUtils.checkAdminV1OrderInfo(orderInfo.getId());
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_orderFlow_orderDelivery_deliveryCostInCurrency() {
        _01_orderFlow_orderDelivery_deliveryCostInCurrency_createOrder(deliveryEpTK, "USD", BigDecimal.valueOf(2000));
        //
        _01_orderFlow_orderDelivery_deliveryCostInCurrency_createOrder(deliveryEpBW, "EUR", BigDecimal.valueOf(3210));
        //
        _01_orderFlow_orderDelivery_deliveryCostInCurrency_createOrder(deliveryEpAF, "RUB", BigDecimal.valueOf(123456));
    }
}
