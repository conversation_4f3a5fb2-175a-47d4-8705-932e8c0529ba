package ru.oskelly.tests.pr.suite6_2.orderflow;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInfo;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.pr.common.bonuses.BonusesServiceTestConfiguration;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2ParseException;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestTcbMock;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.domain.dao.address.CountryRepository;
import su.reddot.domain.dao.commission.CommissionGridRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.dao.user.SaleShipmentRouteRepository;
import su.reddot.domain.model.address.Country;
import su.reddot.domain.model.address.CountryContextNameEnum;
import su.reddot.domain.model.agentreport.AgentReport;
import su.reddot.domain.model.banktransaction.BankOperation;
import su.reddot.domain.model.banktransaction.OperationType;
import su.reddot.domain.model.commission.CommissionGrid;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.fiscalreceiptrequest.FiscalReceiptRequestType;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPayment;
import su.reddot.domain.model.order.OrderPaymentState;
import su.reddot.domain.model.order.OrderPositionState;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.user.SaleShipmentRoute;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.adminpanel.product.domain.CommissionGridDTOV3;
import su.reddot.domain.service.commission.CommissionGridCrudService;
import su.reddot.domain.service.currency.CurrencyService;
import su.reddot.domain.service.dto.order.GroupedCart;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.bank.payments.oskelly.PaymentsServiceBankService;
import su.reddot.infrastructure.cashregister.Checkable;
import su.reddot.infrastructure.cashregister.impl.starrys.type.BuyerCheckRequest;
import su.reddot.infrastructure.util.CallInTransaction;
import su.reddot.presentation.api.v2.Api2Response;
import su.reddot.presentation.api.v2.cart.CartRequest;
import su.reddot.presentation.api.v3.domain.CommissionGridFilter;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@TestMethodOrder(MethodOrderer.MethodName.class)
@ContextConfiguration(classes = {OrderFlowTestUtils.class, BonusesServiceTestConfiguration.class})
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
@TestPropertySource(properties = {"payments.cb-fallback-mode=plutus/two-step-pay"})
public class OrderFlowCrossBorderTest extends AbstractSpringTest {

    @Autowired
    private UserService userService;
    @Autowired
    private ProductRepository productRepository;

    @Autowired
    private SaleShipmentRouteRepository saleShipmentRouteRepository;
    @Autowired
    private CountryRepository countryRepository;
    @Autowired
    private CommissionGridCrudService commissionGridCrudService;
    @Autowired
    private CommissionGridRepository commissionGridRepository;

    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private CartTestSupport cartTestSupport;
    @Autowired
    protected CallInTransaction callInTransaction;
    @Autowired
    private CurrencyService currencyService;

    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user-password}")
    private String password;
    @Value("${test-prepayments.usual-seller-id}")
    private Long usualSellerId;
    @Value("${test-prepayments.cbord-seller-id}")
    private Long cbordSellerId;
    @Value("${test-prepayments.cbord-seller-counterparty-id}")
    private Long cbordSellerCounterpartyId;
    @Value("${test-prepayments.pickup-id}")
    private Long pickupId;
    @Value("${test-prepayments.delivery-id}")
    private Long deliveryId;

    private static OrderFlowTestTcbMock orderFlowTestTcbMock;

    @Value("${test.receipts.mock-server-host}")
    private String mockServerHost;
    @Value("${test.receipts.mock-server-tcb-bank-port}")
    private Integer mockTcbServerPort;

    private static final String COUNTRY_CODE = "IT";
    public static final String C_EUR = "EUR";
    public static final String C_USD = "USD";

    private Long prepareAdminsUser() {
        User adminsUser = userService.getUserByEmail(buyerEmail);
        orderFlowTestUtils.enableUserAuthority(adminsUser.getId(), AuthorityName.ORDER_PAYOUTS, true);
        orderFlowTestUtils.enableUserAuthority(adminsUser.getId(), AuthorityName.ORDER_MANUAL_CHANGE_DELIVERY_STATE, true);
        orderFlowTestUtils.enableLoyalityProgram(adminsUser, true);
        return adminsUser.getId();
    }

    private Long saveCbGrid() {
        CommissionGridFilter commissionGridFilter = new CommissionGridFilter();
        commissionGridFilter.setName("OrderFlowCrossBorderTest");
        Optional<CommissionGridDTOV3> baseGrid = commissionGridCrudService.filterGrids(commissionGridFilter).getItems()
                .stream().findFirst();
        if (baseGrid.isPresent()) {
            return baseGrid.get().getId();
        }
        String jsonGrid = "{\"commissions\":[{\"boutiqueValue\":0.1,\"boutiqueValueScaled\":10,\"publicPrice\":50000,\"value\":0.59,\"valueScaled\":59},{\"boutiqueValue\":0.15,\"boutiqueValueScaled\":15,\"publicPrice\":150000,\"value\":0.47,\"valueScaled\":47},{\"boutiqueValue\":0.19,\"boutiqueValueScaled\":19,\"publicPrice\":*********9,\"value\":0.47,\"valueScaled\":47}],\"fixedAmount\":0,\"name\":\"OrderFlowCrossBorderTest\",\"type\":\"CUSTOM\"}";
        CommissionGridDTOV3 saveGrid = orderFlowTestUtils.fromJson(jsonGrid, new TypeReference<CommissionGridDTOV3> () {});
        CommissionGridDTOV3 gridInfo = commissionGridCrudService.edit(saveGrid);
        return gridInfo.getId();
    }

    private Long saveCbFixedGrid() {
        CommissionGridFilter commissionGridFilter = new CommissionGridFilter();
        commissionGridFilter.setName("OrderFlowFixedCommisionCrossBorderTest");
        Optional<CommissionGridDTOV3> baseGrid = commissionGridCrudService.filterGrids(commissionGridFilter).getItems()
                .stream().findFirst();
        if (baseGrid.isPresent()) {
            return baseGrid.get().getId();
        }
        String jsonGrid = "{\"commissions\":[{\"boutiqueValue\":0.1,\"boutiqueValueScaled\":10,\"publicPrice\":50000,\"value\":0.59,\"valueScaled\":59},{\"boutiqueValue\":0.15,\"boutiqueValueScaled\":15,\"publicPrice\":150000,\"value\":0.47,\"valueScaled\":47},{\"boutiqueValue\":0.19,\"boutiqueValueScaled\":19,\"publicPrice\":*********9,\"value\":0.47,\"valueScaled\":47}],\"fixedAmount\":1000.00,\"name\":\"OrderFlowFixedCommisionCrossBorderTest\",\"type\":\"CUSTOM\"}";
        CommissionGridDTOV3 saveGrid = orderFlowTestUtils.fromJson(jsonGrid, new TypeReference<CommissionGridDTOV3> () {});
        CommissionGridDTOV3 gridInfo = commissionGridCrudService.edit(saveGrid);
        return gridInfo.getId();
    }

    @PostConstruct
    private void init() {
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(TcbBankService.SCHEMA));
        User buyer = userService.getUserByEmail(buyerEmail);
        ApiV2Client apiV2Client = new ApiV2Client(buyerEmail, password);
        orderFlowTestUtils.init(buyerEmail, password);
        cartTestSupport.setUserId(buyer.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
        orderFlowTestTcbMock = Objects.isNull(orderFlowTestTcbMock) ? new OrderFlowTestTcbMock(mockServerHost, mockTcbServerPort) : orderFlowTestTcbMock;
        callInTransaction.runInNewTransaction(this::prepareAdminsUser);
        callInTransaction.runInNewTransaction(() -> orderFlowTestUtils.prepareUsualSellerData(cbordSellerCounterpartyId));
        callInTransaction.runInNewTransaction(this::saveCbGrid);
    }

    @AfterEach
    public void cleanup(TestInfo testInfo) {
        callInTransaction.runInNewTransaction(() ->
                orderFlowTestUtils.saveUser(cbordSellerId, (it) ->  {
                    it.setSaleShipmentRoute(null);
                    it.setPickupCountry(null);
                })
        );
    }

    @AfterAll
    public static void done() {
        orderFlowTestTcbMock.stop();
        orderFlowTestTcbMock = null;
    }

    public void _00_orderFlowCrossBorder_validateCart(GroupedCart cartInfo) {
        Assertions.assertThat(cartInfo.getGroups()).hasSize(1).allSatisfy(it -> {
            Assertions.assertThat(it.getDutiesAmount()).isEqualByComparingTo(BigDecimal.ZERO);
            Assertions.assertThat(it.getNumbers()).isNotNull()
                    .satisfies(orderNumbers -> Assertions.assertThat(orderNumbers.getDeliveryWithCustomsAmount()).isNull());
        });
    }

    public List<Long> _XX_orderFlowCB_setupProductsWithCurrencyList(int itemCount, BigDecimal rateValue, BigDecimal productPayoutPrice, String currencyCode, boolean isCustomCommission, boolean isFixedCommission) {
        String baseCurrencyCode = currencyService.getBaseCurrency().getIsoCode();
        orderFlowTestUtils.setCurrencyRate(currencyCode, baseCurrencyCode, rateValue);
        commitAndStartNewTransaction();
        //
        SaleShipmentRoute saleShipmentRoute = saleShipmentRouteRepository.getBySystemName("EuropeDubaiMoscowGBS")
                .orElseThrow(IllegalArgumentException::new);
        Country pickupCountry = countryRepository.findByIsoCodeAlpha2(COUNTRY_CODE);
        orderFlowTestUtils.saveUser(cbordSellerId, (it) -> {
            it.setSaleShipmentRoute(saleShipmentRoute);
            it.setPickupCountry(pickupCountry);
            if (isFixedCommission) {
                it.setCommissionGrid(commissionGridRepository.getOne(saveCbFixedGrid()));
            } else {
                it.setCommissionGrid(commissionGridRepository.getOne(saveCbGrid()));
            }
        });
        callInTransaction.runInNewTransaction(() -> orderFlowTestUtils.enableCountryContext(COUNTRY_CODE, CountryContextNameEnum.SELLER_ADDRESS));
        //
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(cbordSellerId)
                .maxItems(itemCount)
                .build()
        );
        products.forEach(it -> orderFlowTestUtils.setProductPrice(it, currencyCode, productPayoutPrice));
        if (isCustomCommission) {
            products.forEach(it -> {
                it.setCustomCommission(isCustomCommission);
                productRepository.save(it);
            });
        }
        commitAndStartNewTransaction();
        //
        return products.stream().map(Product::getId).collect(Collectors.toList());
    }

    public List<Long> _XX_orderFlowCB_setupProductsWithCurrencyList(int itemCount, String currencyCode, boolean isCustomCommission, boolean isFixedCommission) {
        return _XX_orderFlowCB_setupProductsWithCurrencyList(itemCount,
                BigDecimal.valueOf(123_45, 2),
                BigDecimal.valueOf(6543_21, 2),
                currencyCode, isCustomCommission, isFixedCommission
        );
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _00_01_orderFlowCB_turnOffCb_twoItems() {
        List<Long> productIds = _XX_orderFlowCB_setupProductsWithCurrencyList(2, C_EUR, false, false);
        //
        orderFlowTestUtils.saveUser(cbordSellerId, (it) -> it.setSaleShipmentRoute(null));
        //
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(cbordSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(cbordSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .paymentsSchema(TcbBankService.SCHEMA)
                .productIdsList(productIds).itemsCount(productIds.size())

                .validateCart(this::_00_orderFlowCrossBorder_validateCart)

                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .noAgentReportCompare(true)

                .stopPosition(OrderFlowTestUtils.TestStopPosition.HOLD)

                .build());
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.HOLD);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _00_02_orderFlowCB_enablesCb_oneItems() {
        List<Long> productIds = _XX_orderFlowCB_setupProductsWithCurrencyList(1, C_EUR, false, false);
        //
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(cbordSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(cbordSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .paymentsSchema(PaymentsServiceBankService.PAYMENT_SCHEME_PLUTUS)
                .productIdsList(productIds).itemsCount(productIds.size())

                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .noAgentReportCompare(true)

                .stopPosition(OrderFlowTestUtils.TestStopPosition.HOLD)

                .build());
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.HOLD);
    }

    private OrderDTO _00_03_orderFlowCB_enablesCb_twoItems_callTest() {
        List<Long> productIds = _XX_orderFlowCB_setupProductsWithCurrencyList(2, C_EUR, false, false);
        //
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(cbordSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(cbordSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .paymentsSchema(PaymentsServiceBankService.PAYMENT_SCHEME_PLUTUS)
                .productIdsList(productIds).itemsCount(productIds.size())

                .confirmPositions(ImmutableList.of(1, 2))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1, 2))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .noAgentReportCompare(true)

                .stopPosition(OrderFlowTestUtils.TestStopPosition.HOLD)

                .build());
        rollbackAndStartNewTransaction();
        //
        return testOrder;
    }

    private OrderDTO _00_05_orderFlowCB_enablesCb_promocode_callTest() {
        List<Long> productIds = _XX_orderFlowCB_setupProductsWithCurrencyList(1, C_EUR, false, false);
        //
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(cbordSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(cbordSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .paymentsSchema(PaymentsServiceBankService.PAYMENT_SCHEME_PLUTUS)
                .productIdsList(productIds).itemsCount(productIds.size())

                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())
                .promoCodeAmounts(BigDecimal.valueOf(777_77, 2))

                .noAgentReportCompare(true)

                .stopPosition(OrderFlowTestUtils.TestStopPosition.HOLD)

                .build());
        rollbackAndStartNewTransaction();
        //
        return testOrder;
    }

    private OrderDTO _00_09_orderFlowCB_enablesCb_custom_commission_callTest() {
        List<Long> productIds = _XX_orderFlowCB_setupProductsWithCurrencyList(1, C_EUR, true, false);
        //
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(cbordSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(cbordSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .paymentsSchema(PaymentsServiceBankService.PAYMENT_SCHEME_PLUTUS)
                .productIdsList(productIds).itemsCount(productIds.size())

                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .noAgentReportCompare(true)

                .stopPosition(OrderFlowTestUtils.TestStopPosition.HOLD)

                .build());
        rollbackAndStartNewTransaction();
        //
        return testOrder;
    }

    private OrderDTO _00_07_orderFlowCB_enablesCb_not_EUR_callTest() {
        List<Long> productIds = _XX_orderFlowCB_setupProductsWithCurrencyList(1, C_USD, false, false);
        //
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(cbordSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(cbordSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .paymentsSchema(PaymentsServiceBankService.PAYMENT_SCHEME_PLUTUS)
                .productIdsList(productIds).itemsCount(productIds.size())

                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .noAgentReportCompare(true)

                .stopPosition(OrderFlowTestUtils.TestStopPosition.HOLD)

                .build());
        rollbackAndStartNewTransaction();
        //
        return testOrder;
    }

    private OrderDTO _00_08_orderFlowCB_enablesCb_fix_commission_callTest() {
        List<Long> productIds = _XX_orderFlowCB_setupProductsWithCurrencyList(1, C_EUR, false, true);
        //
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(cbordSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(cbordSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .paymentsSchema(PaymentsServiceBankService.PAYMENT_SCHEME_PLUTUS)
                .productIdsList(productIds).itemsCount(productIds.size())

                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .noAgentReportCompare(true)

                .stopPosition(OrderFlowTestUtils.TestStopPosition.HOLD)

                .build());
        rollbackAndStartNewTransaction();
        //
        return testOrder;
    }

    private OrderDTO _00_06_orderFlowCB_enablesCb_bonuses_callTest() {
        List<Long> productIds = _XX_orderFlowCB_setupProductsWithCurrencyList(1, C_EUR, false, false);
        //
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(cbordSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(cbordSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .paymentsSchema(PaymentsServiceBankService.PAYMENT_SCHEME_PLUTUS)
                .productIdsList(productIds).itemsCount(productIds.size())

                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())
                .withdrawBonusBonuses(new BigDecimal(200.00))

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .noAgentReportCompare(true)

                .stopPosition(OrderFlowTestUtils.TestStopPosition.HOLD)

                .build());
        rollbackAndStartNewTransaction();
        //
        return testOrder;
    }

    private OrderDTO _00_08_orderFlowCB_enablesCb_not_EUR_callTest() {
        List<Long> productIds = _XX_orderFlowCB_setupProductsWithCurrencyList(1, C_USD, false, false);
        //
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(cbordSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(cbordSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .paymentsSchema(PaymentsServiceBankService.PAYMENT_SCHEME_PLUTUS)
                .productIdsList(productIds).itemsCount(productIds.size())

                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .noAgentReportCompare(true)

                .stopPosition(OrderFlowTestUtils.TestStopPosition.HOLD)

                .build());
        rollbackAndStartNewTransaction();
        //
        return testOrder;
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _00_03_orderFlowCB_enablesCb_twoItems() {
        Assertions.assertThatThrownBy(this::_00_03_orderFlowCB_enablesCb_twoItems_callTest)
                .isInstanceOf(ApiV2ParseException.class)
                .satisfies(it -> {
                    Api2Response<String> exceptionsData = orderFlowTestUtils.getRawApi2Response(((ApiV2ParseException) it).getRawData());
                    Assertions.assertThat(exceptionsData.getMessage()).matches("Заказ .*: при доставке из-за рубежа в заказе допускается только один товар, пожалуйста, оформите отдельные заказы");
                    Assertions.assertThat(exceptionsData.getHumanMessage()).matches("Заказ .*: при доставке из-за рубежа в заказе допускается только один товар, пожалуйста, оформите отдельные заказы");
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _00_04_orderFlowCB_enablesCb_twoItemsCbNullCb() {
        List<Long> crossProductList = _XX_orderFlowCB_setupProductsWithCurrencyList(1, C_EUR, false, false);
        List<Long> usualProductList = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder().sellerId(usualSellerId).maxItems(1).build())
                .stream().map(Product::getId).collect(Collectors.toList());
        List<Product> productsToCart = Stream.concat(crossProductList.stream(), usualProductList.stream())
                .map(it -> productRepository.getOne(it))
                .collect(Collectors.toList());
        //
        orderFlowTestUtils.fillCart(productsToCart);
        //
        GroupedCart cartInfo = orderFlowTestUtils.getCart(CartRequest.builder().build());
        //
        Assertions.assertThat(cartInfo.getGroups()).hasSize(2);
        OrderDTO cbOrderDTO = cartInfo.getGroups().stream()
                .filter(it -> it.getItems().stream().anyMatch(orderPosition -> crossProductList.contains(orderPosition.getProductId())))
                .findAny()
                .orElseThrow(IllegalStateException::new);
        OrderDTO uiOrderDTO = cartInfo.getGroups().stream()
                .filter(it -> it.getItems().stream().anyMatch(orderPosition -> usualProductList.contains(orderPosition.getProductId())))
                .findAny()
                .orElseThrow(IllegalStateException::new);
        //
        Assertions.assertThat(cbOrderDTO.getNumbers()).isNotNull()
                .satisfies(it -> Assertions.assertThat(it.getDeliveryWithCustomsAmount()).isNotNull())
                .satisfies(it -> Assertions.assertThat(it.getTotalItemSaleAmount()).isNotNull());
        Assertions.assertThat(uiOrderDTO.getNumbers()).isNotNull()
                .satisfies(it -> Assertions.assertThat(it.getDeliveryWithCustomsAmount()).isNull())
                .satisfies(it -> Assertions.assertThat(it.getTotalItemSaleAmount()).isNotNull());
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _00_05_orderFlowCB_enablesCb_promocode() {
        Assertions.assertThatThrownBy(this::_00_05_orderFlowCB_enablesCb_promocode_callTest)
                .isInstanceOf(ApiV2ParseException.class)
                .satisfies(it -> {
                    Api2Response<String> exceptionsData = orderFlowTestUtils.getRawApi2Response(((ApiV2ParseException) it).getRawData());
                    Assertions.assertThat(exceptionsData.getMessage()).matches("Заказ .*: при доставке из-за рубежа в заказе не допускается применение промокода");
                    Assertions.assertThat(exceptionsData.getHumanMessage()).matches("Заказ .*: при доставке из-за рубежа в заказе не допускается применение промокода");
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _00_06_orderFlowCB_enablesCb_bonuses() {
        Assertions.assertThatThrownBy(this::_00_06_orderFlowCB_enablesCb_bonuses_callTest)
                .isInstanceOf(ApiV2ParseException.class)
                .satisfies(it -> {
                    Api2Response<String> exceptionsData = orderFlowTestUtils.getRawApi2Response(((ApiV2ParseException) it).getRawData());
                    Assertions.assertThat(exceptionsData.getMessage()).matches("Заказ .*: при доставке из-за рубежа в заказе не допускается применение бонусов");
                    Assertions.assertThat(exceptionsData.getHumanMessage()).matches("Заказ .*: при доставке из-за рубежа в заказе не допускается применение бонусов");
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _00_07_orderFlowCB_enablesCb_not_EUR() {
        Assertions.assertThatThrownBy(this::_00_07_orderFlowCB_enablesCb_not_EUR_callTest)
                .isInstanceOf(ApiV2ParseException.class)
                .satisfies(it -> {
                    Api2Response<String> exceptionsData = orderFlowTestUtils.getRawApi2Response(((ApiV2ParseException) it).getRawData());
                    Assertions.assertThat(exceptionsData.getMessage()).matches("Заказ .*: при доставке из-за рубежа в заказе не допускается цена в валюте отличной от EUR");
                    Assertions.assertThat(exceptionsData.getHumanMessage()).matches("Заказ .*: при доставке из-за рубежа в заказе не допускается цена в валюте отличной от EUR");
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _00_08_orderFlowCB_enablesCb_fix_commission() {
        Assertions.assertThatThrownBy(this::_00_08_orderFlowCB_enablesCb_fix_commission_callTest)
                .isInstanceOf(ApiV2ParseException.class)
                .satisfies(it -> {
                    Api2Response<String> exceptionsData = orderFlowTestUtils.getRawApi2Response(((ApiV2ParseException) it).getRawData());
                    Assertions.assertThat(exceptionsData.getMessage()).matches("Заказ .*: при доставке из-за рубежа в заказе не допускается фиксированная комиссия");
                    Assertions.assertThat(exceptionsData.getHumanMessage()).matches("Заказ .*: при доставке из-за рубежа в заказе не допускается фиксированная комиссия");
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _00_09_orderFlowCB_enablesCb_custom_commission() {
        Assertions.assertThatThrownBy(this::_00_09_orderFlowCB_enablesCb_custom_commission_callTest)
                .isInstanceOf(ApiV2ParseException.class)
                .satisfies(it -> {
                    Api2Response<String> exceptionsData = orderFlowTestUtils.getRawApi2Response(((ApiV2ParseException) it).getRawData());
                    Assertions.assertThat(exceptionsData.getMessage()).matches("Заказ .*: при доставке из-за рубежа в заказе не допускается произвольная комиссия");
                    Assertions.assertThat(exceptionsData.getHumanMessage()).matches("Заказ .*: при доставке из-за рубежа в заказе не допускается произвольная комиссия");
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    @Disabled(value = "Добавить тест на торги")
    public void _00_10_orderFlowCB_enablesCb_bargain() {
        Assertions.assertThatThrownBy(this::_00_05_orderFlowCB_enablesCb_promocode_callTest)
                .isInstanceOf(ApiV2ParseException.class)
                .satisfies(it -> {
                    Api2Response<String> exceptionsData = orderFlowTestUtils.getRawApi2Response(((ApiV2ParseException) it).getRawData());
                    Assertions.assertThat(exceptionsData.getMessage()).matches("Заказ .*: при доставке из-за рубежа в заказе не допускается торг");
                    Assertions.assertThat(exceptionsData.getHumanMessage()).matches("Заказ .*: при доставке из-за рубежа в заказе не допускается торг");
                });
    }

    public void _01_orderFlowCrossBorder_validateCart(GroupedCart cartInfo) {
        Assertions.assertThat(cartInfo.getGroups()).hasSize(1).allSatisfy(it -> {
            Assertions.assertThat(it.getDutiesAmount()).isEqualByComparingTo(BigDecimal.ZERO);
            Assertions.assertThat(it.getNumbers()).isNotNull()
                    .satisfies(orderNumbers -> Assertions.assertThat(orderNumbers.getDeliveryWithCustomsAmount()).isNotNull());
        });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_01_orderFlowCB_happyPath_oneItems_rateStable() {
        List<Long> productIds = _XX_orderFlowCB_setupProductsWithCurrencyList(1, C_EUR, false, false);
        //
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(cbordSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(cbordSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .conciergeOrder(true)
                .crossBordsMode(true)

                .paymentsSchema(PaymentsServiceBankService.PAYMENT_SCHEME_PLUTUS)
                .productIdsList(productIds).itemsCount(1)

                .validateCart(this::_01_orderFlowCrossBorder_validateCart)

                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .deliveryCost(BigDecimal.valueOf(1189L))

                .noAgentReportCompare(true)

                .stopPosition(OrderFlowTestUtils.TestStopPosition.DELIVERY_O2B_DONE)

                .build());
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(0), it -> {
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(716_314_7255L, 4));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(716_314_7255L, 4));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(807_759_2745L, 4));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(807_759_2745L, 4));
            Assertions.assertThat(it.getAmountInForeignCurrency()).isEqualByComparingTo(BigDecimal.valueOf(6543_21, 2));
            Assertions.assertThat(it.getForeignCurrencyRate()).isEqualByComparingTo(BigDecimal.valueOf(123_45, 2));
            Assertions.assertThat(it.getProductItem().getProduct().getLastPriceConvertRate()).isEqualByComparingTo(BigDecimal.valueOf(123_45, 2));
            Assertions.assertThat(it.getState()).isEqualTo(OrderPositionState.VERIFICATION_OK);
            Assertions.assertThat(it.getLastExpertise().getDefectDiscountPrice()).isNull();
            Assertions.assertThat(it.getLastExpertise().getCleaningPrice()).isNull();
            Assertions.assertThat(it.getLastExpertise().getIsApproved()).isTrue();
        });
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), PaymentsServiceBankService.PAYMENT_SCHEME_PLUTUS, OrderPaymentState.CAPTURE_DONE);
        orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), ImmutableList.of(FiscalReceiptRequestType.CB_ADVANCE, FiscalReceiptRequestType.CB_PAYMENT));
        //
        BuyerCheckRequest cbAdvance = orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.CB_ADVANCE, 0L,
                Lists.newArrayList(807_759_27L, 117_461_00L, 717_503_73L),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_ADVANCE, OrderFlowTestUtils.ITEM_KIND_ADVANCE, OrderFlowTestUtils.ITEM_KIND_ADVANCE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE, OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE, OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE)
        );
        Assertions.assertThat(cbAdvance.getNonCash()).isEqualTo(Lists.newArrayList(orderPayment.getCaptureAmountInBase().movePointRight(2).longValue(), 0L, 0L));
        Assertions.assertThat(cbAdvance.getAdvancePayment()).isEqualTo(0L);
        //
        BuyerCheckRequest cbPayment = orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.CB_PAYMENT, 0L,
                Lists.newArrayList(807_759_27L, 117_461_00L, 717_503_73L),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_COMMODITY, OrderFlowTestUtils.ITEM_KIND_SERVICE, OrderFlowTestUtils.ITEM_KIND_SERVICE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY, OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY, OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY)
        );
        Assertions.assertThat(cbPayment.getNonCash()).isEqualTo(Lists.newArrayList(0L, 0L, 0L));
        Assertions.assertThat(cbPayment.getAdvancePayment()).isEqualTo(orderPayment.getCaptureAmountInBase().movePointRight(2).longValue());
        //
        AgentReport agentReport = orderFlowTestUtils.validateAgentReportAmnt(testOrder.getId(), "EUR", BigDecimal.valueOf(6543_21, 2));
        Assertions.assertThat(agentReport.getBankPayments()).hasSize(1).allSatisfy(it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(807_759_2745L, 4));
            Assertions.assertThat(it.getOriginalCurrencyAmount()).isEqualByComparingTo(BigDecimal.valueOf(6543_21, 2));
            Assertions.assertThat(it.getOriginalCurrencyCode()).isEqualTo("EUR");
        });
        BankOperation payoutOp = orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.SELLER_PAYOUT, BigDecimal.valueOf(6543_21, 2));
        Assertions.assertThat(payoutOp.getCurrency().getIsoCode()).isEqualTo("EUR");
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        //
        BigDecimal balanceValue = orderFlowTestUtils.adjustUserBalanceToValue(cbordSellerId, null);
        Assertions.assertThat(balanceValue).isEqualByComparingTo(BigDecimal.ZERO);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_02_orderFlowCB_happyPath_oneItems_rateChange() {
        List<Long> productIds = _XX_orderFlowCB_setupProductsWithCurrencyList(1, C_EUR, false, false);
        //
        orderFlowTestUtils.setCurrencyRate("EUR", currencyService.getBaseCurrency().getIsoCode(), BigDecimal.valueOf(124_56, 2));
        commitAndStartNewTransaction();
        //
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(cbordSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(cbordSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .conciergeOrder(true)
                .crossBordsMode(true)

                .paymentsSchema(PaymentsServiceBankService.PAYMENT_SCHEME_PLUTUS)
                .productIdsList(productIds).itemsCount(1)

                .validateCart(this::_01_orderFlowCrossBorder_validateCart)

                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .deliveryCost(BigDecimal.valueOf(1189L))

                .noAgentReportCompare(true)

                .stopPosition(OrderFlowTestUtils.TestStopPosition.DELIVERY_O2B_DONE)

                .build());
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(0), it -> {
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(722_755_7624L, 4));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(722_755_7624L, 4));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(815_022_2376L, 4));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(815_022_2376L, 4));
            Assertions.assertThat(it.getAmountInForeignCurrency()).isEqualByComparingTo(BigDecimal.valueOf(6543_21, 2));
            Assertions.assertThat(it.getForeignCurrencyRate()).isEqualByComparingTo(BigDecimal.valueOf(124_56, 2));
            Assertions.assertThat(it.getProductItem().getProduct().getLastPriceConvertRate()).isEqualByComparingTo(BigDecimal.valueOf(124_56, 2));
            Assertions.assertThat(it.getState()).isEqualTo(OrderPositionState.VERIFICATION_OK);
            Assertions.assertThat(it.getLastExpertise().getDefectDiscountPrice()).isNull();
            Assertions.assertThat(it.getLastExpertise().getCleaningPrice()).isNull();
            Assertions.assertThat(it.getLastExpertise().getIsApproved()).isTrue();
        });
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), PaymentsServiceBankService.PAYMENT_SCHEME_PLUTUS, OrderPaymentState.CAPTURE_DONE);
        orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), ImmutableList.of(FiscalReceiptRequestType.CB_ADVANCE, FiscalReceiptRequestType.CB_PAYMENT));
        //
        BuyerCheckRequest cbAdvance = orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.CB_ADVANCE, 0L,
                Lists.newArrayList(815_022_24L, 118_517_00L, 723_944_76L),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_ADVANCE, OrderFlowTestUtils.ITEM_KIND_ADVANCE, OrderFlowTestUtils.ITEM_KIND_ADVANCE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE, OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE, OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE)
        );
        Assertions.assertThat(cbAdvance.getNonCash()).isEqualTo(Lists.newArrayList(orderPayment.getCaptureAmountInBase().movePointRight(2).longValue(), 0L, 0L));
        Assertions.assertThat(cbAdvance.getAdvancePayment()).isEqualTo(0L);
        //
        BuyerCheckRequest cbPayment = orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.CB_PAYMENT, 0L,
                Lists.newArrayList(815_022_24L, 118_517_00L, 723_944_76L),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_COMMODITY, OrderFlowTestUtils.ITEM_KIND_SERVICE, OrderFlowTestUtils.ITEM_KIND_SERVICE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY, OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY, OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY)
        );
        Assertions.assertThat(cbPayment.getNonCash()).isEqualTo(Lists.newArrayList(0L, 0L, 0L));
        Assertions.assertThat(cbPayment.getAdvancePayment()).isEqualTo(orderPayment.getCaptureAmountInBase().movePointRight(2).longValue());
        //
        AgentReport agentReport = orderFlowTestUtils.validateAgentReportAmnt(testOrder.getId(), "EUR", BigDecimal.valueOf(6543_21, 2));
        Assertions.assertThat(agentReport.getBankPayments()).hasSize(1).allSatisfy(it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(815_022_2376L, 4));
            Assertions.assertThat(it.getOriginalCurrencyAmount()).isEqualByComparingTo(BigDecimal.valueOf(6543_21, 2));
            Assertions.assertThat(it.getOriginalCurrencyCode()).isEqualTo("EUR");
        });
        BankOperation payoutOp = orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.SELLER_PAYOUT, BigDecimal.valueOf(6543_21, 2));
        Assertions.assertThat(payoutOp.getCurrency().getIsoCode()).isEqualTo("EUR");
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        //
        BigDecimal balanceValue = orderFlowTestUtils.adjustUserBalanceToValue(cbordSellerId, null);
        Assertions.assertThat(balanceValue).isEqualByComparingTo(BigDecimal.ZERO);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_03_happyPath_oneItems_priceValue_696_72() {
        List<Long> productIds = _XX_orderFlowCB_setupProductsWithCurrencyList(1,
                BigDecimal.valueOf(92_02, 2),
                BigDecimal.valueOf(696_72L, 2),
                C_EUR, false, false
        );
        //
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(cbordSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(cbordSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .conciergeOrder(true)
                .crossBordsMode(true)

                .paymentsSchema(PaymentsServiceBankService.PAYMENT_SCHEME_PLUTUS)
                .productIdsList(productIds).itemsCount(1)

                .validateCart(this::_01_orderFlowCrossBorder_validateCart)

                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .deliveryCost(BigDecimal.valueOf(1189L))

                .noAgentReportCompare(true)

                .stopPosition(OrderFlowTestUtils.TestStopPosition.DELIVERY_O2B_DONE)

                .build());
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        //
        OrderDTO orderDto = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getId(), true);
        Assertions.assertThat(orderDto.getNumbers().getTotalItemSaleAmount()).isEqualByComparingTo(BigDecimal.valueOf(120_966_00L, 2));
        Assertions.assertThat(orderDto.getNumbers().getDeliveryWithCustomsAmount()).isEqualByComparingTo(BigDecimal.valueOf(8_046_00, 2));
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(0), it -> {
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(56_853_8256, 4));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(56_853_8256, 4));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(64_112_1744L, 4));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(64_112_1744L, 4));
            Assertions.assertThat(it.getAmountInForeignCurrency()).isEqualByComparingTo(BigDecimal.valueOf(696_72, 2));
            Assertions.assertThat(it.getForeignCurrencyRate()).isEqualByComparingTo(BigDecimal.valueOf(92_02, 2));
            Assertions.assertThat(it.getProductItem().getProduct().getLastPriceConvertRate()).isEqualByComparingTo(BigDecimal.valueOf(92_02, 2));
            Assertions.assertThat(it.getState()).isEqualTo(OrderPositionState.VERIFICATION_OK);
            Assertions.assertThat(it.getLastExpertise().getDefectDiscountPrice()).isNull();
            Assertions.assertThat(it.getLastExpertise().getCleaningPrice()).isNull();
            Assertions.assertThat(it.getLastExpertise().getIsApproved()).isTrue();
        });
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), PaymentsServiceBankService.PAYMENT_SCHEME_PLUTUS, OrderPaymentState.CAPTURE_DONE);
        orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), ImmutableList.of(FiscalReceiptRequestType.CB_ADVANCE, FiscalReceiptRequestType.CB_PAYMENT));
        //
        BuyerCheckRequest cbAdvance = orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.CB_ADVANCE, 0L,
                Lists.newArrayList(64_112_17L, 6_857_00L, 58_042_83L),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_ADVANCE, OrderFlowTestUtils.ITEM_KIND_ADVANCE, OrderFlowTestUtils.ITEM_KIND_ADVANCE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE, OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE, OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE)
        );
        Assertions.assertThat(cbAdvance.getNonCash()).isEqualTo(Lists.newArrayList(orderPayment.getCaptureAmountInBase().movePointRight(2).longValue(), 0L, 0L));
        Assertions.assertThat(cbAdvance.getAdvancePayment()).isEqualTo(0L);
        //
        Assertions.assertThat(cbAdvance.getLines()).hasSize(3)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getPrice()).isEqualTo(64_112_17L);
                    Assertions.assertThat(it.getTaxId()).isEqualTo(Checkable.Line.LINE_TAX_ID_VAT_NONE);
                    Assertions.assertThat(it.getProviderData().getInn()).isEqualTo("**********");
                    Assertions.assertThat(it.getProviderData().getName()).isEqualTo("ООО \"ТРИКСТИ ГЛОБАЛ ЛИМИТЕД\"");
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getPrice()).isEqualTo(6_857_00L);
                    Assertions.assertThat(it.getTaxId()).isEqualTo(Checkable.Line.LINE_TAX_ID_VAT_NONE);
                    Assertions.assertThat(it.getDescription()).isEqualTo("Таможенные пошлины");
                    Assertions.assertThat(it.getProviderData().getInn()).isEqualTo("**********");
                    Assertions.assertThat(it.getProviderData().getName()).isEqualTo("ООО \"ГБС-БРОКЕР\"");
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getPrice()).isEqualTo(58_042_83L);
                    Assertions.assertThat(it.getTaxId()).isEqualTo(Checkable.Line.LINE_TAX_ID_VAT_NONE);
                    Assertions.assertThat(it.getDescription()).isEqualTo("Комиссия агента");
                    Assertions.assertThat(it.getProviderData()).isNull();
                });
        //
        BuyerCheckRequest cbPayment = orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.CB_PAYMENT, 0L,
                Lists.newArrayList(64_112_17L, 6_857_00L, 58_042_83L),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_COMMODITY, OrderFlowTestUtils.ITEM_KIND_SERVICE, OrderFlowTestUtils.ITEM_KIND_SERVICE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY, OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY, OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY)
        );
        Assertions.assertThat(cbPayment.getNonCash()).isEqualTo(Lists.newArrayList(0L, 0L, 0L));
        Assertions.assertThat(cbPayment.getAdvancePayment()).isEqualTo(orderPayment.getCaptureAmountInBase().movePointRight(2).longValue());
        //
        AgentReport agentReport = orderFlowTestUtils.validateAgentReportAmnt(testOrder.getId(), "EUR", BigDecimal.valueOf(696_72L, 2));
        Assertions.assertThat(agentReport.getBankPayments()).hasSize(1).allSatisfy(it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(64_112_1744, 4));
            Assertions.assertThat(it.getOriginalCurrencyAmount()).isEqualByComparingTo(BigDecimal.valueOf(696_72L, 2));
            Assertions.assertThat(it.getOriginalCurrencyCode()).isEqualTo("EUR");
        });
        BankOperation payoutOp = orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.SELLER_PAYOUT, BigDecimal.valueOf(696_72L, 2));
        Assertions.assertThat(payoutOp.getCurrency().getIsoCode()).isEqualTo("EUR");
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        //
        BigDecimal balanceValue = orderFlowTestUtils.adjustUserBalanceToValue(cbordSellerId, null);
        Assertions.assertThat(balanceValue).isEqualByComparingTo(BigDecimal.ZERO);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_04_happyPath_oneItems_priceValue_172_13() {
        List<Long> productIds = _XX_orderFlowCB_setupProductsWithCurrencyList(1,
                BigDecimal.valueOf(92_02, 2),
                BigDecimal.valueOf(172_13L, 2),
                C_EUR, false, false
        );
        //
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(cbordSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(cbordSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .conciergeOrder(true)
                .crossBordsMode(true)

                .paymentsSchema(PaymentsServiceBankService.PAYMENT_SCHEME_PLUTUS)
                .productIdsList(productIds).itemsCount(1)

                .validateCart(this::_01_orderFlowCrossBorder_validateCart)

                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .deliveryCost(BigDecimal.valueOf(1189L))

                .noAgentReportCompare(true)

                .stopPosition(OrderFlowTestUtils.TestStopPosition.DELIVERY_O2B_DONE)

                .build());
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        //
        OrderDTO orderDto = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getId(), true);
        Assertions.assertThat(orderDto.getNumbers().getTotalItemSaleAmount()).isEqualByComparingTo(BigDecimal.valueOf(38_633_00L, 2));
        Assertions.assertThat(orderDto.getNumbers().getDeliveryWithCustomsAmount()).isEqualByComparingTo(BigDecimal.valueOf(1_189_00L, 2));
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(0), it -> {
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(22_793_5974L, 4));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(22_793_5974L, 4));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(15_839_4026L, 4));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(15_839_4026L, 4));
            Assertions.assertThat(it.getAmountInForeignCurrency()).isEqualByComparingTo(BigDecimal.valueOf(172_13, 2));
            Assertions.assertThat(it.getForeignCurrencyRate()).isEqualByComparingTo(BigDecimal.valueOf(92_02, 2));
            Assertions.assertThat(it.getProductItem().getProduct().getLastPriceConvertRate()).isEqualByComparingTo(BigDecimal.valueOf(92_02, 2));
            Assertions.assertThat(it.getState()).isEqualTo(OrderPositionState.VERIFICATION_OK);
            Assertions.assertThat(it.getLastExpertise().getDefectDiscountPrice()).isNull();
            Assertions.assertThat(it.getLastExpertise().getCleaningPrice()).isNull();
            Assertions.assertThat(it.getLastExpertise().getIsApproved()).isTrue();
        });
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), PaymentsServiceBankService.PAYMENT_SCHEME_PLUTUS, OrderPaymentState.CAPTURE_DONE);
        orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), ImmutableList.of(FiscalReceiptRequestType.CB_ADVANCE, FiscalReceiptRequestType.CB_PAYMENT));
        //
        BuyerCheckRequest cbAdvance = orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.CB_ADVANCE, 0L,
                Lists.newArrayList(15_839_40L, 23_982_60L),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_ADVANCE, OrderFlowTestUtils.ITEM_KIND_ADVANCE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE, OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE)
        );
        Assertions.assertThat(cbAdvance.getNonCash()).isEqualTo(Lists.newArrayList(orderPayment.getCaptureAmountInBase().movePointRight(2).longValue(), 0L, 0L));
        Assertions.assertThat(cbAdvance.getAdvancePayment()).isEqualTo(0L);
        //
        BuyerCheckRequest cbPayment = orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.CB_PAYMENT, 0L,
                Lists.newArrayList(15_839_40L, 23_982_60L),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_COMMODITY, OrderFlowTestUtils.ITEM_KIND_SERVICE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY, OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY)
        );
        Assertions.assertThat(cbPayment.getNonCash()).isEqualTo(Lists.newArrayList(0L, 0L, 0L));
        Assertions.assertThat(cbPayment.getAdvancePayment()).isEqualTo(orderPayment.getCaptureAmountInBase().movePointRight(2).longValue());
        //
        Assertions.assertThat(cbAdvance.getLines()).hasSize(2)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getPrice()).isEqualTo(15_839_40L);
                    Assertions.assertThat(it.getTaxId()).isEqualTo(Checkable.Line.LINE_TAX_ID_VAT_NONE);
                    Assertions.assertThat(it.getProviderData().getInn()).isEqualTo("**********");
                    Assertions.assertThat(it.getProviderData().getName()).isEqualTo("ООО \"ТРИКСТИ ГЛОБАЛ ЛИМИТЕД\"");
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getPrice()).isEqualTo(23_982_60L);
                    Assertions.assertThat(it.getTaxId()).isEqualTo(Checkable.Line.LINE_TAX_ID_VAT_NONE);
                    Assertions.assertThat(it.getDescription()).isEqualTo("Комиссия агента");
                    Assertions.assertThat(it.getProviderData()).isNull();
                });
        //
        AgentReport agentReport = orderFlowTestUtils.validateAgentReportAmnt(testOrder.getId(), "EUR", BigDecimal.valueOf(172_13L, 2));
        Assertions.assertThat(agentReport.getBankPayments()).hasSize(1).allSatisfy(it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(15_839_4026L, 4));
            Assertions.assertThat(it.getOriginalCurrencyAmount()).isEqualByComparingTo(BigDecimal.valueOf(172_13L, 2));
            Assertions.assertThat(it.getOriginalCurrencyCode()).isEqualTo("EUR");
        });
        BankOperation payoutOp = orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.SELLER_PAYOUT, BigDecimal.valueOf(172_13, 2));
        Assertions.assertThat(payoutOp.getCurrency().getIsoCode()).isEqualTo("EUR");
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        //
        BigDecimal balanceValue = orderFlowTestUtils.adjustUserBalanceToValue(cbordSellerId, null);
        Assertions.assertThat(balanceValue).isEqualByComparingTo(BigDecimal.ZERO);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _02_orderFlowCB_happyPath_oneItems_defects() {
        List<Long> productIds = _XX_orderFlowCB_setupProductsWithCurrencyList(1, C_EUR, false, false);
        //
        BigDecimal defectsAmount = BigDecimal.valueOf(123_456L, 0);
        //
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(cbordSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(cbordSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .conciergeOrder(true)
                .crossBordsMode(true)

                .paymentsSchema(PaymentsServiceBankService.PAYMENT_SCHEME_PLUTUS)
                .productIdsList(productIds).itemsCount(1)

                .validateCart(this::_01_orderFlowCrossBorder_validateCart)

                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Lists.newArrayList(defectsAmount.longValue()))
                .cleaningsByPositions(Collections.emptyList())

                .deliveryCost(BigDecimal.valueOf(1189L))

                .noAgentReportCompare(true)

                .stopPosition(OrderFlowTestUtils.TestStopPosition.DELIVERY_O2B_DONE)

                .build());
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(0), it -> {
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(716_314_7255L, 4));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(716_314_7255L, 4).add(defectsAmount));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(807_759_2745L, 4));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(807_759_2745L, 4).subtract(defectsAmount));
            Assertions.assertThat(it.getAmountInForeignCurrency()).isEqualByComparingTo(BigDecimal.valueOf(6543_21, 2));
            Assertions.assertThat(it.getForeignCurrencyRate()).isEqualByComparingTo(BigDecimal.valueOf(123_45, 2));
            Assertions.assertThat(it.getProductItem().getProduct().getLastPriceConvertRate()).isEqualByComparingTo(BigDecimal.valueOf(123_45, 2));
            Assertions.assertThat(it.getState()).isEqualTo(OrderPositionState.VERIFICATION_BAD_STATE);
            Assertions.assertThat(it.getLastExpertise().getDefectDiscountPrice()).isEqualByComparingTo(defectsAmount);
            Assertions.assertThat(it.getLastExpertise().getCleaningPrice()).isNull();
            Assertions.assertThat(it.getLastExpertise().getIsApproved()).isNull();
        });
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), PaymentsServiceBankService.PAYMENT_SCHEME_PLUTUS, OrderPaymentState.REFUND_DONE);
        orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), ImmutableList.of(FiscalReceiptRequestType.CB_ADVANCE, FiscalReceiptRequestType.CB_ADVANCE_ADJUST, FiscalReceiptRequestType.CB_PAYMENT));
        //
        BuyerCheckRequest cbAdvance = orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.CB_ADVANCE, 0L,
                Lists.newArrayList(807_759_27L, 117_461_00L, 717_503_73L),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_ADVANCE, OrderFlowTestUtils.ITEM_KIND_ADVANCE, OrderFlowTestUtils.ITEM_KIND_ADVANCE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE, OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE, OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE)
        );
        Assertions.assertThat(cbAdvance.getNonCash()).isEqualTo(Lists.newArrayList(orderPayment.getCaptureAmountInBase().movePointRight(2).longValue(), 0L, 0L));
        Assertions.assertThat(cbAdvance.getAdvancePayment()).isEqualTo(0L);
        //
        BuyerCheckRequest cbAdvanceAdjust = orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.CB_ADVANCE_ADJUST, 2L,
                Lists.newArrayList(123_456_00L),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_ADVANCE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE)
        );
        Assertions.assertThat(cbAdvanceAdjust.getNonCash()).isEqualTo(Lists.newArrayList(123_456_00L, 0L, 0L));
        Assertions.assertThat(cbAdvanceAdjust.getAdvancePayment()).isEqualTo(0L);
        //
        BuyerCheckRequest cbPayment = orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.CB_PAYMENT, 0L,
                Lists.newArrayList(684_303_27L, 117_461_00L, 717_503_73L),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_COMMODITY, OrderFlowTestUtils.ITEM_KIND_SERVICE, OrderFlowTestUtils.ITEM_KIND_SERVICE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY, OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY, OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY)
        );
        Assertions.assertThat(cbPayment.getNonCash()).isEqualTo(Lists.newArrayList(0L, 0L, 0L));
        Assertions.assertThat(cbPayment.getAdvancePayment()).isEqualTo(orderPayment.getCaptureAmountInBase().subtract(BigDecimal.valueOf(123_456_00L, 2)).movePointRight(2).longValue());
        //
        AgentReport agentReport = orderFlowTestUtils.validateAgentReportAmnt(testOrder.getId(), "EUR", BigDecimal.valueOf(6543_21, 2));
        Assertions.assertThat(agentReport.getBankPayments()).hasSize(1).allSatisfy(it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(807_759_2745L, 4));
            Assertions.assertThat(it.getOriginalCurrencyAmount()).isEqualByComparingTo(BigDecimal.valueOf(6543_21, 2));
            Assertions.assertThat(it.getOriginalCurrencyCode()).isEqualTo("EUR");
        });
        BankOperation payoutOp = orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.SELLER_PAYOUT, BigDecimal.valueOf(6543_21, 2));
        Assertions.assertThat(payoutOp.getCurrency().getIsoCode()).isEqualTo("EUR");
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT, OperationType.REFUND));
        //
        BigDecimal balanceValue = orderFlowTestUtils.adjustUserBalanceToValue(cbordSellerId, null);
        Assertions.assertThat(balanceValue).isEqualByComparingTo(BigDecimal.valueOf(-123_456_00, 2));
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _03_orderFlowCB_happyPath_oneItems_service() {
        List<Long> productIds = _XX_orderFlowCB_setupProductsWithCurrencyList(1, C_EUR, false, false);
        //
        BigDecimal serviceAmount = BigDecimal.valueOf(61_728L, 0);
        //
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(cbordSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(cbordSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .conciergeOrder(true)
                .crossBordsMode(true)

                .paymentsSchema(PaymentsServiceBankService.PAYMENT_SCHEME_PLUTUS)
                .productIdsList(productIds).itemsCount(1)

                .validateCart(this::_01_orderFlowCrossBorder_validateCart)

                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Lists.newArrayList(serviceAmount.longValue()))

                .deliveryCost(BigDecimal.valueOf(1189L))

                .noAgentReportCompare(true)

                .stopPosition(OrderFlowTestUtils.TestStopPosition.DELIVERY_O2B_DONE)

                .build());
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(0), it -> {
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(716_314_7255L, 4));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(716_314_7255L, 4).add(serviceAmount));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(807_759_2745L, 4));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(807_759_2745L, 4).subtract(serviceAmount));
            Assertions.assertThat(it.getAmountInForeignCurrency()).isEqualByComparingTo(BigDecimal.valueOf(6543_21, 2));
            Assertions.assertThat(it.getForeignCurrencyRate()).isEqualByComparingTo(BigDecimal.valueOf(123_45, 2));
            Assertions.assertThat(it.getProductItem().getProduct().getLastPriceConvertRate()).isEqualByComparingTo(BigDecimal.valueOf(123_45, 2));
            Assertions.assertThat(it.getState()).isEqualTo(OrderPositionState.VERIFICATION_NEED_CLEANING);
            Assertions.assertThat(it.getLastExpertise().getDefectDiscountPrice()).isNull();
            Assertions.assertThat(it.getLastExpertise().getCleaningPrice()).isEqualByComparingTo(serviceAmount);
            Assertions.assertThat(it.getLastExpertise().getIsApproved()).isNull();
        });
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), PaymentsServiceBankService.PAYMENT_SCHEME_PLUTUS, OrderPaymentState.CAPTURE_DONE);
        orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), ImmutableList.of(FiscalReceiptRequestType.CB_ADVANCE, FiscalReceiptRequestType.CB_PAYMENT));
        //
        BuyerCheckRequest cbAdvance = orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.CB_ADVANCE, 0L,
                Lists.newArrayList(807_759_27L, 117_461_00L, 717_503_73L),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_ADVANCE, OrderFlowTestUtils.ITEM_KIND_ADVANCE, OrderFlowTestUtils.ITEM_KIND_ADVANCE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE, OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE, OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE)
        );
        Assertions.assertThat(cbAdvance.getNonCash()).isEqualTo(Lists.newArrayList(orderPayment.getCaptureAmountInBase().movePointRight(2).longValue(), 0L, 0L));
        Assertions.assertThat(cbAdvance.getAdvancePayment()).isEqualTo(0L);
        //
        BuyerCheckRequest cbPayment = orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.CB_PAYMENT, 0L,
                Lists.newArrayList(807_759_27L, 117_461_00L, 717_503_73L),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_COMMODITY, OrderFlowTestUtils.ITEM_KIND_SERVICE, OrderFlowTestUtils.ITEM_KIND_SERVICE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY, OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY, OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY)
        );
        Assertions.assertThat(cbPayment.getNonCash()).isEqualTo(Lists.newArrayList(0L, 0L, 0L));
        Assertions.assertThat(cbPayment.getAdvancePayment()).isEqualTo(orderPayment.getCaptureAmountInBase().movePointRight(2).longValue());
        //
        AgentReport agentReport = orderFlowTestUtils.validateAgentReportAmnt(testOrder.getId(), "EUR", BigDecimal.valueOf(6543_21, 2));
        Assertions.assertThat(agentReport.getBankPayments()).hasSize(1).allSatisfy(it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(807_759_2745L, 4));
            Assertions.assertThat(it.getOriginalCurrencyAmount()).isEqualByComparingTo(BigDecimal.valueOf(6543_21, 2));
            Assertions.assertThat(it.getOriginalCurrencyCode()).isEqualTo("EUR");
        });
        BankOperation payoutOp = orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.SELLER_PAYOUT, BigDecimal.valueOf(6543_21, 2));
        Assertions.assertThat(payoutOp.getCurrency().getIsoCode()).isEqualTo("EUR");
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        //
        BigDecimal balanceValue = orderFlowTestUtils.adjustUserBalanceToValue(cbordSellerId, null);
        Assertions.assertThat(balanceValue).isEqualByComparingTo(BigDecimal.valueOf(-61_728_00, 2));
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _04_orderFlowCB_happyPath_oneItems_decline() {
        List<Long> productIds = _XX_orderFlowCB_setupProductsWithCurrencyList(1, C_EUR, false, false);
        //
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(cbordSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(cbordSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .conciergeOrder(true)
                .crossBordsMode(true)

                .paymentsSchema(PaymentsServiceBankService.PAYMENT_SCHEME_PLUTUS)
                .productIdsList(productIds).itemsCount(1)

                .validateCart(this::_01_orderFlowCrossBorder_validateCart)

                .confirmPositions(Collections.emptyList())
                .refusePositions(ImmutableList.of(1))

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .deliveryCost(BigDecimal.valueOf(1189L))

                .noAgentReportCompare(true)

                .stopPosition(OrderFlowTestUtils.TestStopPosition.DELIVERY_O2B_DONE)

                .build());
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.REFUND);
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(0), it -> {
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(716_314_7255L, 4));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isNull();
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(807_759_2745L, 4));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isNull();
            Assertions.assertThat(it.getAmountInForeignCurrency()).isEqualByComparingTo(BigDecimal.valueOf(6543_21, 2));
            Assertions.assertThat(it.getForeignCurrencyRate()).isEqualByComparingTo(BigDecimal.valueOf(123_45, 2));
            Assertions.assertThat(it.getProductItem().getProduct().getLastPriceConvertRate()).isEqualByComparingTo(BigDecimal.valueOf(123_45, 2));
            Assertions.assertThat(it.getState()).isEqualTo(OrderPositionState.SALE_REJECTED);
            Assertions.assertThat(it.getLastExpertise()).isNull();
        });
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), Collections.emptyList());
        //
        Order orderInRefunds = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.REFUND);
        Assertions.assertThat(orderInRefunds.getAgentReport()).isNull();
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE));
        //
        BigDecimal balanceValue = orderFlowTestUtils.adjustUserBalanceToValue(cbordSellerId, null);
        Assertions.assertThat(balanceValue).isEqualByComparingTo(BigDecimal.ZERO);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _05_orderFlowCB_happyPath_oneItems_rejects() {
        List<Long> productIds = _XX_orderFlowCB_setupProductsWithCurrencyList(1, C_EUR, false, false);
        //
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(cbordSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(cbordSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .conciergeOrder(true)
                .crossBordsMode(true)

                .paymentsSchema(PaymentsServiceBankService.PAYMENT_SCHEME_PLUTUS)
                .productIdsList(productIds).itemsCount(1)

                .validateCart(this::_01_orderFlowCrossBorder_validateCart)

                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(1))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .deliveryCost(BigDecimal.valueOf(1189L))

                .noAgentReportCompare(true)

                .stopPosition(OrderFlowTestUtils.TestStopPosition.DELIVERY_O2B_DONE)

                .build());
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.REFUND);
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(0), it -> {
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(716_314_7255L, 4));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isNull();
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(807_759_2745L, 4));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isNull();
            Assertions.assertThat(it.getAmountInForeignCurrency()).isEqualByComparingTo(BigDecimal.valueOf(6543_21, 2));
            Assertions.assertThat(it.getForeignCurrencyRate()).isEqualByComparingTo(BigDecimal.valueOf(123_45, 2));
            Assertions.assertThat(it.getProductItem().getProduct().getLastPriceConvertRate()).isEqualByComparingTo(BigDecimal.valueOf(123_45, 2));
            Assertions.assertThat(it.getState()).isEqualTo(OrderPositionState.REJECTED_AFTER_VERIFICATION);
            Assertions.assertThat(it.getLastExpertise().getDefectDiscountPrice()).isNull();
            Assertions.assertThat(it.getLastExpertise().getCleaningPrice()).isNull();
            Assertions.assertThat(it.getLastExpertise().getIsApproved()).isFalse();
        });
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), PaymentsServiceBankService.PAYMENT_SCHEME_PLUTUS, OrderPaymentState.REFUND_DONE);
        orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), ImmutableList.of(FiscalReceiptRequestType.CB_ADVANCE, FiscalReceiptRequestType.CB_ADVANCE_REFUND));
        //
        BuyerCheckRequest cbAdvance = orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.CB_ADVANCE, 0L,
                Lists.newArrayList(807_759_27L, 117_461_00L, 717_503_73L),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_ADVANCE, OrderFlowTestUtils.ITEM_KIND_ADVANCE, OrderFlowTestUtils.ITEM_KIND_ADVANCE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE, OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE, OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE)
        );
        Assertions.assertThat(cbAdvance.getNonCash()).isEqualTo(Lists.newArrayList(orderPayment.getCaptureAmountInBase().movePointRight(2).longValue(), 0L, 0L));
        Assertions.assertThat(cbAdvance.getAdvancePayment()).isEqualTo(0L);
        //
        BuyerCheckRequest cbPayment = orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.CB_ADVANCE_REFUND, 2L,
                Lists.newArrayList(807_759_27L, 117_461_00L, 717_503_73L),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_ADVANCE, OrderFlowTestUtils.ITEM_KIND_ADVANCE, OrderFlowTestUtils.ITEM_KIND_ADVANCE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE, OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE, OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE)
        );
        Assertions.assertThat(cbPayment.getNonCash()).isEqualTo(Lists.newArrayList(orderPayment.getCaptureAmountInBase().movePointRight(2).longValue(), 0L, 0L));
        Assertions.assertThat(cbPayment.getAdvancePayment()).isEqualTo(0L);
        //
        AgentReport agentReport = orderFlowTestUtils.validateAgentReportAmnt(testOrder.getId(), "EUR", BigDecimal.valueOf(6543_21, 2));
        Assertions.assertThat(agentReport.getBankPayments()).hasSize(1).allSatisfy(it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(807_759_2745L, 4));
            Assertions.assertThat(it.getOriginalCurrencyAmount()).isEqualByComparingTo(BigDecimal.valueOf(6543_21, 2));
            Assertions.assertThat(it.getOriginalCurrencyCode()).isEqualTo("EUR");
        });
        //
        BankOperation payoutOp = orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.SELLER_PAYOUT, BigDecimal.valueOf(6543_21, 2));
        Assertions.assertThat(payoutOp.getCurrency().getIsoCode()).isEqualTo("EUR");
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT, OperationType.REFUND));
        //
        BigDecimal balanceValue = orderFlowTestUtils.adjustUserBalanceToValue(cbordSellerId, null);
        Assertions.assertThat(balanceValue).isEqualByComparingTo(BigDecimal.valueOf(-807_759_2745L, 4));
    }

}
