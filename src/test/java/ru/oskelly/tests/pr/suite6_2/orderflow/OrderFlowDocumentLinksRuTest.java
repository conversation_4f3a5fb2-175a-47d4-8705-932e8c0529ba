package ru.oskelly.tests.pr.suite6_2.orderflow;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.pr.common.bonuses.BonusesServiceTestConfiguration;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestTcbMock;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.order.DocumentLinkDTO;
import su.reddot.domain.service.dto.order.DocumentType;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.bank.jobs.AgentPaymentJobs;
import su.reddot.infrastructure.logistic.DeliveryState;
import su.reddot.infrastructure.util.CallInTransaction;
import su.reddot.oskelly.orderprocessing.internal.web.client.OrderMobileApi;
import su.reddot.oskelly.orderprocessing.internal.web.dto.IntegrationMobileOrderExpertiseDTO;
import su.reddot.presentation.api.v2.Api2Response;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@TestMethodOrder(MethodOrderer.MethodName.class)
@ContextConfiguration(classes = {OrderFlowTestUtils.class, BonusesServiceTestConfiguration.class})
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
public class OrderFlowDocumentLinksRuTest extends AbstractSpringTest {

    @Autowired
    private UserService userService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private AgentPaymentJobs agentPaymentJobs;

    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private CartTestSupport cartTestSupport;
    @Autowired
    protected CallInTransaction callInTransaction;

    @MockBean
    private OrderMobileApi orderMobileApi;

    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user-password}")
    private String password;
    @Value("${test-prepayments.usual-seller-id}")
    private Long usualSellerId;
    @Value("${test-prepayments.usual-seller-counterparty-id}")
    private Long usualSellerCounterpartyId;
    @Value("${test-prepayments.pickup-id}")
    private Long pickupId;
    @Value("${test-prepayments.delivery-id}")
    private Long deliveryId;

    private static OrderFlowTestTcbMock orderFlowTestTcbMock;

    private static ObjectMapper objectMapper = new ObjectMapper();
    private static final String SELLER_PASSWORD = "SellerPassword";
    private ApiV2Client sellerApiV2Client;

    @Value("${test.receipts.mock-server-host}")
    private String mockServerHost;
    @Value("${test.receipts.mock-server-tcb-bank-port}")
    private Integer mockTcbServerPort;

    private Long prepareAdminsUser() {
        User adminsUser = userService.getUserByEmail(buyerEmail);
        orderFlowTestUtils.enableUserAuthority(adminsUser.getId(), AuthorityName.ORDER_PAYOUTS, true);
        orderFlowTestUtils.enableUserAuthority(adminsUser.getId(), AuthorityName.ORDER_MANUAL_CHANGE_DELIVERY_STATE, true);
        return adminsUser.getId();
    }

    private String prepareSellerUser() {
        User sellerUser = userService.getUserById(usualSellerId).orElse(null);
        userService.setPassword(sellerUser, SELLER_PASSWORD);
        return sellerUser.getEmail();
    }

    @PostConstruct
    private void init() {
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(TcbBankService.SCHEMA));
        User buyer = userService.getUserByEmail(buyerEmail);
        ApiV2Client apiV2Client = new ApiV2Client(buyerEmail, password);
        orderFlowTestUtils.init(buyerEmail, password);
        cartTestSupport.setUserId(buyer.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
        orderFlowTestTcbMock = Objects.isNull(orderFlowTestTcbMock) ? new OrderFlowTestTcbMock(mockServerHost, mockTcbServerPort) : orderFlowTestTcbMock;
        callInTransaction.runInNewTransaction(this::prepareAdminsUser);
        String sellersEmail = callInTransaction.runInNewTransaction(this::prepareSellerUser);
        sellerApiV2Client = new ApiV2Client(sellersEmail, SELLER_PASSWORD);
        Mockito.when(orderMobileApi.getMobileOrderExpertise(Mockito.any()))
                .thenReturn(new IntegrationMobileOrderExpertiseDTO().items(Collections.emptyList()));
        callInTransaction.runInNewTransaction(() -> orderFlowTestUtils.changeToSimpleUserNoCB(usualSellerId));
    }

    @AfterAll
    public static void done() {
        orderFlowTestTcbMock.stop();
        orderFlowTestTcbMock = null;
    }

    protected void validateAdminsDocsOnHold(Api2Response<List<DocumentLinkDTO>> adminsOrderDocuments) {
        Assertions.assertThat(adminsOrderDocuments.getData().stream().filter(it -> it.getType() == DocumentType.CERTIFICATE)).hasSize(5);
        Assertions.assertThat(adminsOrderDocuments.getData().stream().filter(it -> it.getType() == DocumentType.OSKELLY_WAYBILL_FROM_SELLER)).hasSize(1);
        Assertions.assertThat(adminsOrderDocuments.getData().stream().filter(it -> it.getType() == DocumentType.OSKELLY_WAYBILL_TO_BUYER)).hasSize(1);
        Assertions.assertThat(adminsOrderDocuments.getData()).hasSize(7);
    }

    protected void validateClientDocsOnHold(Api2Response<List<DocumentLinkDTO>> clientOrderDocuments) {
        Assertions.assertThat(clientOrderDocuments.getData()).isNull();
    }

    protected void validateAdminsDocsOnCompleted(Api2Response<List<DocumentLinkDTO>> adminsOrderDocuments) {
        Assertions.assertThat(adminsOrderDocuments.getData().stream().filter(it -> it.getType() == DocumentType.CERTIFICATE)).hasSize(5);
        Assertions.assertThat(adminsOrderDocuments.getData().stream().filter(it -> it.getType() == DocumentType.OSKELLY_WAYBILL_FROM_SELLER)).hasSize(1);
        Assertions.assertThat(adminsOrderDocuments.getData().stream().filter(it -> it.getType() == DocumentType.OSKELLY_WAYBILL_TO_BUYER)).hasSize(1);
        Assertions.assertThat(adminsOrderDocuments.getData().stream().filter(it -> it.getType() == DocumentType.DOC_ORDER_LABELS)).hasSize(1);
        Assertions.assertThat(adminsOrderDocuments.getData().stream().filter(it -> it.getType() == DocumentType.DOC_AGENT_REPORT_RU)).hasSize(1);
        Assertions.assertThat(adminsOrderDocuments.getData()).hasSize(9);
    }

    protected void validateClientDocsOnCompleted(Api2Response<List<DocumentLinkDTO>> clientOrderDocuments) {
        Assertions.assertThat(clientOrderDocuments.getData()).isNull();
    }

    protected void validateSellerDocsOnCompleted(Api2Response<List<DocumentLinkDTO>> clientOrderDocuments) {
        Assertions.assertThat(clientOrderDocuments.getData().stream().filter(it -> it.getType() == DocumentType.DOC_AGENT_REPORT_RU)).hasSize(1);
        Assertions.assertThat(clientOrderDocuments.getData()).hasSize(1);
    }

    protected void callLoadSaleReport(Long ordersId) {
        orderFlowTestUtils.loadAgentReport(ordersId);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_OrderFlow_DocumentLinksTest() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(5)
                .build()
        );
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.fillCart(products);
        //
        OrderService.InitOrderResult testOrder = orderFlowTestUtils.holdOrderWithPromoCodePS(products.get(0).getSeller(), null, TcbBankService.SCHEMA);
        //
        orderFlowTestUtils.callOrderHoldCallback(testOrder.getOrderId(), testOrder.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        validateClientDocsOnHold(orderFlowTestUtils.getClientOrderDocuments(null, testOrder.getOrderId()));
        validateAdminsDocsOnHold(orderFlowTestUtils.getAdminsOrderDocuments(null, testOrder.getOrderId()));
        //
        OrderDTO orderInfo = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getOrderId(), true);
        Assertions.assertThat(orderInfo.getId()).isNotEqualTo(0);
        //
        Order order = orderService.getOrder(orderInfo.getId());
        Assertions.assertThat(order.getId()).isEqualTo(orderInfo.getId());
        //
        long rejectPositionId = orderInfo.getItems().get(0).getId();
        orderInfo.getItems().forEach(op -> orderFlowTestUtils.rejectOrApprovePosition(op.getId(), op.getId() != rejectPositionId));
        orderFlowTestUtils.changeAddressEndpoint(orderInfo.getId(), pickupId, deliveryId);
        //
        orderFlowTestUtils.takeOurselves(orderInfo.getId(), null);
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.OURSELVES_FROM_SELLER_TO_OFFICE, true);
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE, true);
        //
        orderFlowTestUtils.adminsApi1expertise(orderInfo.getItems().get(0).getId(), true, OrderFlowTestUtils.ExpertiseAction.EXPERTISE_NO_ACTION, null);
        orderFlowTestUtils.adminsApi1expertise(orderInfo.getItems().get(1).getId(), true, OrderFlowTestUtils.ExpertiseAction.EXPERTISE_PASSED_OK, null);
        orderFlowTestUtils.adminsApi1expertise(orderInfo.getItems().get(2).getId(), true, OrderFlowTestUtils.ExpertiseAction.EXPERTISE_REJECT_IT, null);
        orderFlowTestUtils.adminsApi1expertise(orderInfo.getItems().get(3).getId(), true, OrderFlowTestUtils.ExpertiseAction.EXPERTISE_DEFECT_IT, 1234L);
        orderFlowTestUtils.adminsApi1expertise(orderInfo.getItems().get(4).getId(), true, OrderFlowTestUtils.ExpertiseAction.EXPERTISE_CLEANS_IT, 4321L);
        //
        orderFlowTestUtils.adminPanel_Charge(orderInfo.getId());
        orderFlowTestUtils.processHoldComplete(orderInfo);
        //
        orderFlowTestUtils.sendOurselves(orderInfo.getId(), null);
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.OURSELVES_FROM_OFFICE_TO_BUYER, true);
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.DELIVERED_TO_BUYER, true);
        //
        orderFlowTestUtils.sendAgentReport(orderInfo.getId(), true);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.changeSellerCounterparty(orderInfo.getId(), usualSellerCounterpartyId, HttpStatus.Series.SUCCESSFUL);
        //
        orderFlowTestUtils.confirmAgentReport(orderInfo.getId(), true);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.prepareSellerPayout(orderInfo.getId(), true);
        commitAndStartNewTransaction();
        //
        callLoadSaleReport(orderInfo.getId());
        //
        orderFlowTestUtils.transferMoneyToSellers(orderInfo.getId());
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.MONEY_PAYMENT_WAIT);
        //
        orderFlowTestUtils.validateMoneyToSellers(orderInfo.getId());
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.COMPLETED);
        //
        validateClientDocsOnCompleted(orderFlowTestUtils.getClientOrderDocuments(null, testOrder.getOrderId()));
        validateSellerDocsOnCompleted(orderFlowTestUtils.getClientOrderDocuments(sellerApiV2Client, testOrder.getOrderId()));
        validateAdminsDocsOnCompleted(orderFlowTestUtils.getAdminsOrderDocuments(null, testOrder.getOrderId()));
        //
        agentPaymentJobs.transferMoneyToSeller();
    }

}
