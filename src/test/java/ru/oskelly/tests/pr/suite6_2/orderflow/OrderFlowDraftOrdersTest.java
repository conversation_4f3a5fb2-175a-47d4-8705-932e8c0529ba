package ru.oskelly.tests.pr.suite6_2.orderflow;

import com.google.common.collect.ImmutableList;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.pr.common.bonuses.BonusesServiceTestConfiguration;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestFixtures;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.legal.LegalEntity;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderExtraPropInfo;
import su.reddot.domain.model.order.OrderExtraPropValue;
import su.reddot.domain.model.order.OrderPayment;
import su.reddot.domain.model.order.OrderPaymentState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.ProductsSalesRequest;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.dto.order.adminpanel.AdminPanelOrderDetailsDTO;
import su.reddot.domain.service.order.impl.OrderExtraPropsService;
import su.reddot.domain.service.ordersourceinfo.OrderSourceInfoService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.BoutiqueBankService;
import su.reddot.infrastructure.configparam.ConfigParamService;
import su.reddot.infrastructure.util.CallInTransaction;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.when;
import static su.reddot.domain.model.order.OrderSourceInfo.ORDER_SOURCE_INFO_MOVING_TO_BOUTIQUE;
import static su.reddot.infrastructure.configparam.ConfigParamService.CONFIG_PARAM_SOME_ITEMS_IN_BOUTIQUE_CART;


@TestMethodOrder(MethodOrderer.MethodName.class)
@ContextConfiguration(classes = {OrderFlowTestUtils.class, OrderFlowTestFixtures.class, BonusesServiceTestConfiguration.class})
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
public class OrderFlowDraftOrdersTest extends AbstractSpringTest {

    @Autowired
    private CallInTransaction callInTransaction;
    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private OrderFlowTestFixtures orderFlowTestFixtures;
    @Autowired
    private CartTestSupport cartTestSupport;
    @Autowired
    private OrderSourceInfoService orderSourceInfoService;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private UserService userService;

    @MockBean
    ConfigParamService configParamServiceMock;

    @Value("${test-prepayments.pickup-id}")
    private Long pickupId;
    @Value("${test-prepayments.delivery-id}")
    private Long deliveryId;
    @Value("${test-boutique.buyer-id}")
    private Long boutiqueBuyerId;
    @Value("${test-prepayments.usual-seller-id}")
    private Long usualSellerId;
    @Value("${test.api.user-email}")
    private String adminsMail;
    @Value("${test.api.user-password}")
    private String adminsPass;

    private final String boutiqueBuyerPassword = "password4boutique";
    private ApiV2Client apiV2Client;
    private ApiV2Client adminClient;
    private User boutiqueUser;
    private List<Long> productIds = new ArrayList<>();

    @PostConstruct
    private void init() {
        adminClient = new ApiV2Client(adminsMail, adminsPass);
        //
        boutiqueUser = callInTransaction.runInNewTransaction(() -> orderFlowTestFixtures.prepareBoutiqueUser(boutiqueBuyerId, boutiqueBuyerPassword));
        //
        apiV2Client = new ApiV2Client(boutiqueUser.getEmail(), boutiqueBuyerPassword);
        orderFlowTestUtils.init(boutiqueUser.getEmail(), boutiqueBuyerPassword);
        cartTestSupport.setUserId(boutiqueUser.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
    }

    @Test
    @Transactional
    public void _01_concierge_order_scheme_1_sell_1_conf_1_okay() {
        productIds = orderFlowTestUtils.createDraftProducts(usualSellerId, 1);
        commitAndStartNewTransaction();
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .paymentsSchema(BoutiqueBankService.BOUTIQUE_SCHEMA)
                .productIdsList(productIds)

                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .stopPosition(OrderFlowTestUtils.TestStopPosition.EXPERTISE_JUST_DONE)

                .build());
        //
        OrderDTO validateOrderDto = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getId(), true);
        Assertions.assertThat(validateOrderDto.getItems()).hasSize(1);
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(validateOrderDto.getId(), BoutiqueBankService.BOUTIQUE_SCHEMA, OrderPaymentState.AUTHORIZE_DONE);
        Assertions.assertThat(orderPayment.getAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2).add(validateOrderDto.getDeliveryCost()));
        Assertions.assertThat(orderPayment.getRefundAmountInBase()).isNull();
        //
        AdminPanelOrderDetailsDTO adminOrderInfo = orderFlowTestUtils.loadAdminOrderSuccessful(apiV2Client, testOrder.getId(), false);
        Assertions.assertThat(adminOrderInfo.getOrderInfo().getLegalEntity().getOnecUuid()).isEqualTo(OrderFlowTestFixtures.OnecEntityType.ONEC_ENTITY_CONCIERGE.getOnecUuid());
    }

    @Test
    @Transactional
    public void _02_concierge_order_scheme_2_sell_2_conf_2_okay() {
        setUpMockConfigParamService();

        productIds = orderFlowTestUtils.createDraftProducts(usualSellerId, 2);
        commitAndStartNewTransaction();
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .paymentsSchema(BoutiqueBankService.BOUTIQUE_SCHEMA)
                .noReceiptsMode(true)
                .productIdsList(productIds)

                .confirmPositions(ImmutableList.of(1, 2))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1, 2))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .stopPosition(OrderFlowTestUtils.TestStopPosition.EXPERTISE_JUST_DONE)

                .build());
        //
        OrderDTO validateOrderDto = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getId(), true);
        Assertions.assertThat(validateOrderDto.getItems()).hasSize(2);
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(validateOrderDto.getId(), BoutiqueBankService.BOUTIQUE_SCHEMA, OrderPaymentState.AUTHORIZE_DONE);
        Assertions.assertThat(orderPayment.getAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(20_000_00, 2).add(validateOrderDto.getDeliveryCost()));
        Assertions.assertThat(orderPayment.getRefundAmountInBase()).isNull();
        //
        AdminPanelOrderDetailsDTO adminOrderInfo = orderFlowTestUtils.loadAdminOrderSuccessful(apiV2Client, testOrder.getId(), false);
        Assertions.assertThat(adminOrderInfo.getOrderInfo().getLegalEntity().getOnecUuid()).isEqualTo(OrderFlowTestFixtures.OnecEntityType.ONEC_ENTITY_CONCIERGE.getOnecUuid());
    }

    @Test
    @Transactional
    public void _03_create_order_from_saleRequest_okay() {
        productIds = orderFlowTestUtils.createDraftProducts(usualSellerId, 1);
        commitAndStartNewTransaction();
        //
        String salesRequestId = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd-HH-mm-ss"));
        //
        ProductsSalesRequest productsSalesRequest = new ProductsSalesRequest();
        productsSalesRequest.setSalesRequestId(salesRequestId);
        productsSalesRequest.setProductIds(productIds);
        //
        OrderDTO validateOrderDTO = orderFlowTestUtils.createOrderFromApplicationSuccessfullWithCustomClient(apiV2Client, productsSalesRequest, true);
        Assertions.assertThat(validateOrderDTO.getItems()).hasSize(1);
        Assertions.assertThat(validateOrderDTO.getLegalEntity().getOnecUuid()).isEqualTo(LegalEntity.LEGAL_ENTITY_OSKELLY_GROUP);
        //
        rollbackAndStartNewTransaction();
        //
        Assertions.assertThat(validateOrderDTO.getItems()).hasSize(1);
        //
        Order order = orderRepository.getOne(validateOrderDTO.getId());
        //
        Optional<OrderExtraPropValue> orderExtraPropValue1st = OrderExtraPropsService.getOrderExtraPropValue(order, OrderExtraPropInfo.ORDER_EXTRA_PROP_SALES_REQUEST_ID);
        Assertions.assertThat(orderExtraPropValue1st.map(OrderExtraPropValue::getPropValue).orElse(null)).isEqualTo(salesRequestId);

        //
        Assertions.assertThat(OrderExtraPropsService.isOrderTagExists(order, OrderExtraPropInfo.ORDER_EXTRA_PROP_ORDER_TAG_SELLER_CONCIERGE)).isTrue();

        org.junit.jupiter.api.Assertions.assertEquals(orderSourceInfoService.findOrderSourceInfoByNameCached(
                ORDER_SOURCE_INFO_MOVING_TO_BOUTIQUE).get().getName(), order.getOrderSourceInfo().getName());
        //
        List<OrderExtraPropInfo> orderProps = order.getOrderExtraPropValues().stream()
                .map(it -> it.getOrderExtraProp().getId())
                .map(OrderExtraPropInfo::fromId)
                .collect(Collectors.toList());
        Assertions.assertThat(orderProps).containsOnly(
                OrderExtraPropInfo.ORDER_EXTRA_PROP_SALES_REQUEST_ID,
                OrderExtraPropInfo.ORDER_EXTRA_PROP_ORDER_TAG_SELLER_CONCIERGE);
        //
        AdminPanelOrderDetailsDTO adminOrderInfo = orderFlowTestUtils.loadAdminOrderSuccessful(apiV2Client, validateOrderDTO.getId(), false);
        Assertions.assertThat(adminOrderInfo.getOrderInfo().getLegalEntity().getOnecUuid()).isEqualTo(OrderFlowTestFixtures.OnecEntityType.ONEC_ENTITY_GROUP.getOnecUuid());
    }

    @Test
    @Disabled("Вернуться разобраться с авторизацией или дернуть метод напрямую")
    @Transactional
    public void _4_create_order_from_saleRequest_okay_change_legal_entity() {
        User adminsUser = userService.getUserByEmail(adminsMail);
        orderFlowTestUtils.enableUserAuthority(adminsUser.getId(), AuthorityName.ORDER_SELLER_CONCIERGE_MOVE_TO_BOUTIQUE, true);

        productIds = orderFlowTestUtils.createDraftProducts(usualSellerId, 1);
        commitAndStartNewTransaction();
        //
        String salesRequestId = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd-HH-mm-ss"));
        //
        ProductsSalesRequest productsSalesRequest = new ProductsSalesRequest();
        productsSalesRequest.setSalesRequestId(salesRequestId);
        productsSalesRequest.setProductIds(productIds);
        //
        OrderDTO validateOrderDTO = orderFlowTestUtils.createOrderFromApplicationSuccessfullWithCustomClient(apiV2Client, productsSalesRequest, true);
        Assertions.assertThat(validateOrderDTO.getItems()).hasSize(1);
        Assertions.assertThat(validateOrderDTO.getLegalEntity().getOnecUuid()).isEqualTo(LegalEntity.LEGAL_ENTITY_OSKELLY_GROUP);
        //
//        rollbackAndStartNewTransaction();
        //
        Assertions.assertThat(validateOrderDTO.getItems()).hasSize(1);
        //
        Order order = orderRepository.getOne(validateOrderDTO.getId());

        orderFlowTestUtils.adminsApi1SetOrderLegalEntity(ImmutableList.of(order.getId()), 2L, true);
        commitAndStartNewTransaction();
        order = orderRepository.getOne(validateOrderDTO.getId());
    }

    private void setUpMockConfigParamService() {
        reset(configParamServiceMock);
        when(configParamServiceMock.getValueAsBoolean(CONFIG_PARAM_SOME_ITEMS_IN_BOUTIQUE_CART, false))
                .thenReturn(true);
    }
}
