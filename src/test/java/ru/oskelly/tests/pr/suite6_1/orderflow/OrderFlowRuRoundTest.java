package ru.oskelly.tests.pr.suite6_1.orderflow;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.pr.common.bonuses.BonusesServiceTestConfiguration;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.util.CallInTransaction;
import su.reddot.oskelly.orderprocessing.internal.web.client.OrderMobileApi;
import su.reddot.oskelly.orderprocessing.internal.web.dto.IntegrationMobileOrderExpertiseDTO;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@TestMethodOrder(MethodOrderer.MethodName.class)
@ContextConfiguration(classes = {OrderFlowTestUtils.class, BonusesServiceTestConfiguration.class})
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
public class OrderFlowRuRoundTest extends AbstractSpringTest {

    @Autowired
    private UserService userService;

    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private CartTestSupport cartTestSupport;
    @Autowired
    protected CallInTransaction callInTransaction;

    @MockBean
    private OrderMobileApi orderMobileApi;

    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user-password}")
    private String password;
    @Value("${test-prepayments.usual-seller-id}")
    private Long usualSellerId;
    @Value("${test-prepayments.usual-seller-counterparty-id}")
    private Long usualSellerCounterpartyId;
    @Value("${test-prepayments.pickup-id}")
    private Long pickupId;
    @Value("${test-prepayments.delivery-id}")
    private Long deliveryId;

    private static OrderFlowTestTcbMock orderFlowTestTcbMock;

    @Value("${test.receipts.mock-server-host}")
    private String mockServerHost;
    @Value("${test.receipts.mock-server-tcb-bank-port}")
    private Integer mockTcbServerPort;

    private Long prepareAdminsUser() {
        User adminsUser = userService.getUserByEmail(buyerEmail);
        orderFlowTestUtils.enableUserAuthority(adminsUser.getId(), AuthorityName.ORDER_PAYOUTS, true);
        orderFlowTestUtils.enableUserAuthority(adminsUser.getId(), AuthorityName.ORDER_MANUAL_CHANGE_DELIVERY_STATE, true);
        return adminsUser.getId();
    }

    @PostConstruct
    private void init() {
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(TcbBankService.SCHEMA));
        User buyer = userService.getUserByEmail(buyerEmail);
        ApiV2Client apiV2Client = new ApiV2Client(buyerEmail, password);
        orderFlowTestUtils.init(buyerEmail, password);
        cartTestSupport.setUserId(buyer.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
        orderFlowTestTcbMock = Objects.isNull(orderFlowTestTcbMock) ? new OrderFlowTestTcbMock(mockServerHost, mockTcbServerPort) : orderFlowTestTcbMock;
        callInTransaction.runInNewTransaction(this::prepareAdminsUser);
        callInTransaction.runInNewTransaction(() -> orderFlowTestUtils.prepareUsualSellerData(usualSellerCounterpartyId));
        Mockito.when(orderMobileApi.getMobileOrderExpertise(Mockito.any()))
                .thenReturn(new IntegrationMobileOrderExpertiseDTO().items(Collections.emptyList()));
    }

    @AfterAll
    public static void done() {
        orderFlowTestTcbMock.stop();
        orderFlowTestTcbMock = null;
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_orderFlow_happyPath_priceToAmount_rounds() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(2)
                .build()
        );
        //
        Product productPriceRoundUp = products.get(0);
        Product productPriceRoundDn = products.get(1);
        //
        productPriceRoundUp.setCurrentPrice(BigDecimal.valueOf(10_000_3552, 4));
        productPriceRoundDn.setCurrentPrice(BigDecimal.valueOf(20_000_4432, 4));
        commitAndStartNewTransaction();
        //
        List<Long> productIds = products.stream().map(Product::getId).collect(Collectors.toList());
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .paymentsSchema(TcbBankService.SCHEMA)
                .productIdsList(productIds).itemsCount(2)

                .confirmPositions(ImmutableList.of(1, 2))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1, 2))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .sellerPayoutAmount(22_500_60L)

                .build());
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(0), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_36, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_36, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_500_09, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_500_27, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(2_500_09, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_36, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_500_27, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(1), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(20_000_44, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(20_000_44, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(5_000_11, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(15_000_33, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(5_000_11, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(20_000_44, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(15_000_33, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        //
        Order order = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.RETURN);
        Assertions.assertThat(order.getAmount().subtract(order.getDeliveryCost())).isEqualByComparingTo(BigDecimal.valueOf(30_000_80, 2));
        //
        OrderDTO orderInfo = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getId(), true);
        Assertions.assertThat(orderInfo.getId()).isPositive();
        //
        orderFlowTestUtils.validateOrderItems(orderInfo, productPriceRoundUp.getId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_36, 2));
            Assertions.assertThat(it.getFinalAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_36, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        orderFlowTestUtils.validateOrderItems(orderInfo, productPriceRoundDn.getId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(20_000_44, 2));
            Assertions.assertThat(it.getFinalAmount()).isEqualByComparingTo(BigDecimal.valueOf(20_000_44, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        Assertions.assertThat(orderInfo.getFinalAmount().subtract(orderInfo.getDeliveryCost())).isEqualByComparingTo(BigDecimal.valueOf(30_000_80, 2));
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_orderFlow_happyPath_priceToAmount_promoCodeAmounts_rounds() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(2)
                .build()
        );
        //
        Product productPriceRoundUp = products.get(0);
        Product productPriceRoundDn = products.get(1);
        //
        productPriceRoundUp.setCurrentPrice(BigDecimal.valueOf(10_000_3552, 4));
        productPriceRoundDn.setCurrentPrice(BigDecimal.valueOf(20_000_4432, 4));
        commitAndStartNewTransaction();
        //
        List<Long> productIds = products.stream().map(Product::getId).collect(Collectors.toList());
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .paymentsSchema(TcbBankService.SCHEMA)
                .productIdsList(productIds).itemsCount(2)
                .promoCodeAmounts(BigDecimal.valueOf(1_000_00, 2))

                .confirmPositions(ImmutableList.of(1, 2))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1, 2))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .sellerPayoutAmount(22_500_60L)

                .build());
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(0), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_36, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_667_02, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(333_34, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_500_09, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_500_27, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(2_166_75, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_667_02, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_500_27, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(1), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(20_000_44, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(19_333_78, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(666_66, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(5_000_11, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(15_000_33, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(4_333_45, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(19_333_78, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(15_000_33, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        //
        Order order = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.RETURN);
        Assertions.assertThat(order.getAmount().subtract(order.getDeliveryCost())).isEqualByComparingTo(BigDecimal.valueOf(29_000_80, 2));
        //
        OrderDTO orderInfo = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getId(), true);
        Assertions.assertThat(orderInfo.getId()).isPositive();
        //
        orderFlowTestUtils.validateOrderItems(orderInfo, productPriceRoundUp.getId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_36, 2));
            Assertions.assertThat(it.getFinalAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_36, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(333_34, 2));
        });
        orderFlowTestUtils.validateOrderItems(orderInfo, productPriceRoundDn.getId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(20_000_44, 2));
            Assertions.assertThat(it.getFinalAmount()).isEqualByComparingTo(BigDecimal.valueOf(20_000_44, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(666_66, 2));
        });
        Assertions.assertThat(orderInfo.getFinalAmount().subtract(orderInfo.getDeliveryCost())).isEqualByComparingTo(BigDecimal.valueOf(29_000_80, 2));
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_orderFlow_happyPath_priceToAmount_promoCodePercent_rounds() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(2)
                .build()
        );
        //
        Product productPriceRoundUp = products.get(0);
        Product productPriceRoundDn = products.get(1);
        //
        productPriceRoundUp.setCurrentPrice(BigDecimal.valueOf(10_000_3552, 4));
        productPriceRoundDn.setCurrentPrice(BigDecimal.valueOf(20_000_4432, 4));
        commitAndStartNewTransaction();
        //
        List<Long> productIds = products.stream().map(Product::getId).collect(Collectors.toList());
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .paymentsSchema(TcbBankService.SCHEMA)
                .productIdsList(productIds).itemsCount(2)
                .promoCodePercent(BigDecimal.valueOf(10_00, 4))

                .confirmPositions(ImmutableList.of(1, 2))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1, 2))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .sellerPayoutAmount(22_500_60L)

                .build());
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(0), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_36, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_000_32, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(1_000_04, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_500_09, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_500_27, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(1_500_05, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_000_32, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_500_27, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(1), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(20_000_44, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(18_000_40, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(2_000_04, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(5_000_11, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(15_000_33, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(3_000_07, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(18_000_40, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(15_000_33, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        //
        Order order = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.RETURN);
        Assertions.assertThat(order.getAmount().subtract(order.getDeliveryCost())).isEqualByComparingTo(BigDecimal.valueOf(27_000_72, 2));
        //
        OrderDTO orderInfo = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getId(), true);
        Assertions.assertThat(orderInfo.getId()).isPositive();
        //
        orderFlowTestUtils.validateOrderItems(orderInfo, productPriceRoundUp.getId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_36, 2));
            Assertions.assertThat(it.getFinalAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_36, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(1_000_04, 2));
        });
        orderFlowTestUtils.validateOrderItems(orderInfo, productPriceRoundDn.getId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(20_000_44, 2));
            Assertions.assertThat(it.getFinalAmount()).isEqualByComparingTo(BigDecimal.valueOf(20_000_44, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(2_000_04, 2));
        });
        Assertions.assertThat(orderInfo.getFinalAmount().subtract(orderInfo.getDeliveryCost())).isEqualByComparingTo(BigDecimal.valueOf(27_000_72, 2));
    }


    @Test
    @Transactional
    @Rollback(value = false)
    public void _02_orderFlow_happyPath_rounds() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(5)
                .build()
        );
        //
        products.get(0).setCurrentPrice(BigDecimal.valueOf(10_000_00, 2));
        products.get(1).setCurrentPrice(BigDecimal.valueOf(10_111_00, 2));
        products.get(2).setCurrentPrice(BigDecimal.valueOf(10_222_00, 2));
        products.get(3).setCurrentPrice(BigDecimal.valueOf(10_333_00, 2));
        products.get(4).setCurrentPrice(BigDecimal.valueOf(10_444_00, 2));
        //
        commitAndStartNewTransaction();
        //
        List<Long> productIds = products.stream().map(Product::getId).collect(Collectors.toList());
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .paymentsSchema(TcbBankService.SCHEMA)
                .productIdsList(productIds).itemsCount(5)

                .confirmPositions(ImmutableList.of(1, 2, 3, 4, 5))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1, 2, 3, 4, 5))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .sellerPayoutAmount(38_332_50L)

                .build());
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(0), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_500_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(2_500_00, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(1), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_111_00, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_111_00, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_527_75, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_583_25, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(2_527_75, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_111_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_583_25, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(2), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_222_00, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_222_00, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_555_50, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_666_50, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(2_555_50, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_222_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_666_50, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(3), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_333_00, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_333_00, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_583_25, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_749_75, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(2_583_25, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_333_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_749_75, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(4), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_444_00, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_444_00, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_611_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_833_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(2_611_00, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_444_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_833_00, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _03_orderFlow_promoCodeAmounts_rounds() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(5)
                .build()
        );
        //
        products.get(0).setCurrentPrice(BigDecimal.valueOf(10_000_00, 2));
        products.get(1).setCurrentPrice(BigDecimal.valueOf(10_111_00, 2));
        products.get(2).setCurrentPrice(BigDecimal.valueOf(10_222_00, 2));
        products.get(3).setCurrentPrice(BigDecimal.valueOf(10_333_00, 2));
        products.get(4).setCurrentPrice(BigDecimal.valueOf(10_444_00, 2));
        //
        commitAndStartNewTransaction();
        //
        List<Long> productIds = products.stream().map(Product::getId).collect(Collectors.toList());
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .paymentsSchema(TcbBankService.SCHEMA)
                .productIdsList(productIds).itemsCount(5)
                .promoCodeAmounts(BigDecimal.valueOf(777_77, 2))

                .confirmPositions(ImmutableList.of(1, 2, 3, 4, 5))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1, 2, 3, 4, 5))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .sellerPayoutAmount(38_332_50L)

                .build());
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(0), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_847_81, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(152.19));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_500_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(2_347_81, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_847_81, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(1), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_111_00, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_957_14, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(153_86, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_527_75, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_583_25, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(2_373_89, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_957_14, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_583_25, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(2), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_222_00, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_066_45, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(155_55, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_555_50, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_666_50, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(2_399_95, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_066_45, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_666_50, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(3), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_333_00, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_175_76, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(157_24, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_583_25, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_749_75, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(2_426_01, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_175_76, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_749_75, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(4), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_444_00, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_285_07, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(158_93, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_611_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_833_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(2_452_07, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_285_07, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_833_00, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _03_orderFlow_promoCodePercent_rounds() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(5)
                .build()
        );
        //
        products.get(0).setCurrentPrice(BigDecimal.valueOf(10_000_00, 2));
        products.get(1).setCurrentPrice(BigDecimal.valueOf(10_111_00, 2));
        products.get(2).setCurrentPrice(BigDecimal.valueOf(10_222_00, 2));
        products.get(3).setCurrentPrice(BigDecimal.valueOf(10_333_00, 2));
        products.get(4).setCurrentPrice(BigDecimal.valueOf(10_444_00, 2));
        //
        commitAndStartNewTransaction();
        //
        List<Long> productIds = products.stream().map(Product::getId).collect(Collectors.toList());
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .paymentsSchema(TcbBankService.SCHEMA)
                .productIdsList(productIds).itemsCount(5)
                .promoCodePercent(BigDecimal.valueOf(7_50, 4))

                .confirmPositions(ImmutableList.of(1, 2, 3, 4, 5))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1, 2, 3, 4, 5))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .sellerPayoutAmount(38_332_50L)

                .build());
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(0), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_250_00, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(750_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_500_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(1_750_00, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_250_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(1), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_111_00, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_352_67, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(758_33, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_527_75, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_583_25, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(1_769_42, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_352_67, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_583_25, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(2), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_222_00, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_455_35, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(766_65, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_555_50, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_666_50, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(1_788_85, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_455_35, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_666_50, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(3), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_333_00, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_558_02, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(774_98, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_583_25, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_749_75, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(1_808_27, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_558_02, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_749_75, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(4), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_444_00, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_660_70, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(783_30, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_611_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_833_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(1_827_70, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_660_70, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_833_00, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _04_orderFlow_promoCodeAmountsWithDefect_rounds() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(5)
                .build()
        );
        //
        products.get(0).setCurrentPrice(BigDecimal.valueOf(10_000_00, 2));
        products.get(1).setCurrentPrice(BigDecimal.valueOf(10_111_00, 2));
        products.get(2).setCurrentPrice(BigDecimal.valueOf(10_222_00, 2));
        products.get(3).setCurrentPrice(BigDecimal.valueOf(10_333_00, 2));
        products.get(4).setCurrentPrice(BigDecimal.valueOf(10_444_00, 2));
        //
        commitAndStartNewTransaction();
        //
        List<Long> productIds = products.stream().map(Product::getId).collect(Collectors.toList());
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .paymentsSchema(TcbBankService.SCHEMA)
                .productIdsList(productIds).itemsCount(5)
                .promoCodeAmounts(BigDecimal.valueOf(777_77, 2))

                .confirmPositions(ImmutableList.of(1, 2, 3, 4, 5))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Lists.newArrayList(null, 123L, 345L, 546L, 678L))
                .cleaningsByPositions(Collections.emptyList())

                .sellerPayoutAmount(36_640_50L)

                .build());
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(0), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_847_81, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(152.19));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_500_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(2_347_81, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_847_81, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(1), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_111_00, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_957_14, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(153_86, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_527_75, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_583_25, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(2_373_89, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_834_14, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_460_25, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.valueOf(123_00, 2));
        });
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(2), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_222_00, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_066_45, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(155_55, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_555_50, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_666_50, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(2_399_95, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_721_45, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_321_50, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.valueOf(345_00, 2));
        });
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(3), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_333_00, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_175_76, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(157_24, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_583_25, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_749_75, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(2_426_01, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_629_76, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_203_75, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.valueOf(546_00, 2));
        });
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(4), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_444_00, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_285_07, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(158_93, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_611_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_833_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(2_452_07, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_607_07, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_155_00, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.valueOf(678_00, 2));
        });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _04_orderFlow_promoCodePercentWithDefect_rounds() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(5)
                .build()
        );
        //
        products.get(0).setCurrentPrice(BigDecimal.valueOf(10_000_00, 2));
        products.get(1).setCurrentPrice(BigDecimal.valueOf(10_111_00, 2));
        products.get(2).setCurrentPrice(BigDecimal.valueOf(10_222_00, 2));
        products.get(3).setCurrentPrice(BigDecimal.valueOf(10_333_00, 2));
        products.get(4).setCurrentPrice(BigDecimal.valueOf(10_444_00, 2));
        //
        commitAndStartNewTransaction();
        //
        List<Long> productIds = products.stream().map(Product::getId).collect(Collectors.toList());
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .paymentsSchema(TcbBankService.SCHEMA)
                .productIdsList(productIds).itemsCount(5)
                .promoCodePercent(BigDecimal.valueOf(7_50, 4))

                .confirmPositions(ImmutableList.of(1, 2, 3, 4, 5))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Lists.newArrayList(null, 123L, 345L, 546L, 678L))
                .cleaningsByPositions(Collections.emptyList())

                .sellerPayoutAmount(36_640_50L)

                .build());
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(0), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_250_00, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(750_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_500_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(1_750_00, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_250_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(1), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_111_00, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_352_67, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(758_33, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_527_75, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_583_25, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(1_769_42, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_229_67, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_460_25, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.valueOf(123_00, 2));
        });
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(2), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_222_00, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_455_35, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(766_65, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_555_50, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_666_50, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(1_788_85, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_110_35, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_321_50, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.valueOf(345_00, 2));
        });
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(3), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_333_00, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_558_02, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(774_98, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_583_25, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_749_75, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(1_808_27, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_012_02, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_203_75, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.valueOf(546_00, 2));
        });
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(4), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_444_00, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_660_70, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(783_30, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_611_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_833_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(1_827_70, 2));
            Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(8_982_70, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_155_00, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.valueOf(678_00, 2));
        });
    }

}
