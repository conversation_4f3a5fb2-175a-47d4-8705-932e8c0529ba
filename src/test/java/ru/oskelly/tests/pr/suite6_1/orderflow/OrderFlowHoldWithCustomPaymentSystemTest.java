package ru.oskelly.tests.pr.suite6_1.orderflow;

import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.pr.common.bonuses.BonusesServiceTestConfiguration;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.component.HoldRequest;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderRefundReasonType;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.product.item.ProductItemService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.Best2payAndTcbBankService;
import su.reddot.infrastructure.bank.StubAndTcbBankService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.bank.payments.noon.NoonBankService;
import su.reddot.infrastructure.util.CallInTransaction;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertEquals;

@TestMethodOrder(MethodOrderer.MethodName.class)
@ContextConfiguration(classes = {OrderFlowTestUtils.class, BonusesServiceTestConfiguration.class})
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
public class OrderFlowHoldWithCustomPaymentSystemTest extends AbstractSpringTest {

    @Autowired
    private UserService userService;

    @Autowired
    private ProductItemService productItemService;

    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private CartTestSupport cartTestSupport;

    @Autowired
    private CallInTransaction callInTransaction;

    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user-password}")
    private String password;
    @Value("${test-prepayments.usual-seller-id}")
    private Long usualSellerId;
    @Value("${test.receipts.mock-server-host}")
    private String mockServerHost;
    @Value("${test.receipts.mock-server-tcb-bank-port}")
    private Integer mockTcbServerPort;
    @Value("${test.receipts.mock-server-b2p-bank-port}")
    private Integer mockB2PServerPort;
    @Value("${test.receipts.mock-server-non-bank-port}")
    private Integer mockNonServerPort;

    private static OrderFlowTestTcbMock orderFlowTestTcbMock;
    private static OrderFlowTestB2PMock orderFlowTestB2PMock;
    private static OrderFlowTestNoonMock orderFlowTestNonMock;

    private Long prepareAdminUser() {
        User adminUser = userService.getUserByEmail(buyerEmail);
        orderFlowTestUtils.enableUserAuthority(adminUser.getId(), AuthorityName.ORDER_PREPAYMENTS, true);
        orderFlowTestUtils.enableUserAuthority(adminUser.getId(), AuthorityName.ORDER_MANUAL_CHANGE_DELIVERY_STATE, true);
        return adminUser.getId();
    }

    @PostConstruct
    private void init() {
        orderFlowTestUtils.init(buyerEmail, password);
        User buyer = userService.getUserByEmail(buyerEmail);
        ApiV2Client apiV2Client = new ApiV2Client(buyerEmail, password);
        cartTestSupport.setUserId(buyer.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
        orderFlowTestTcbMock = Objects.isNull(orderFlowTestTcbMock) ? new OrderFlowTestTcbMock(mockServerHost, mockTcbServerPort) : orderFlowTestTcbMock;
        orderFlowTestB2PMock = Objects.isNull(orderFlowTestB2PMock) ? new OrderFlowTestB2PMock(mockServerHost, mockB2PServerPort) : orderFlowTestB2PMock;
        orderFlowTestNonMock = Objects.isNull(orderFlowTestNonMock) ? new OrderFlowTestNoonMock(mockServerHost, mockNonServerPort) : orderFlowTestNonMock;
        callInTransaction.runInNewTransaction(this::prepareAdminUser);
    }

    @AfterAll
    public static void done() {
        orderFlowTestTcbMock.stop();
        orderFlowTestB2PMock.stop();
        orderFlowTestNonMock.stop();
    }

    private OrderService.InitOrderResult _01_holdV1Call() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(1)
                .build()
        );
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.fillCart(products);
        User seller = products.get(0).getSeller();
        //
        cartTestSupport.setCartAddressEndpoint();
        //
        return cartTestSupport.holdCartWithParams(seller.getId(),
                HoldRequest.builder().holdEndPoint(CartTestSupport.HOLD_V1_ENDPOINT).build());
    }

    @Test
    @Transactional
    public void _01_HoldV1CallWithZeroTcbLeadsToDefaultPaymentSystemOkay() {
        orderFlowTestUtils.setTcbOrdersCount(0);
        orderFlowTestUtils.setAllowPaymentSystemChoose(Collections.emptyList());
        //
        OrderService.InitOrderResult testOrderInit = _01_holdV1Call();
        Assertions.assertThat(testOrderInit.getBank_url()).isEqualTo(StubAndTcbBankService.STUB_BANK_URL);
        Assertions.assertThat(testOrderInit.getPaymentSystem()).isEqualTo(StubAndTcbBankService.STUB_SYSTEM_NAME);
        //
        OrderService.InitOrderResult testOrderHold = orderFlowTestUtils.holdOrderSuccessfull(testOrderInit.getOrderId(), HoldRequest.builder().build(), true);
        Assertions.assertThat(testOrderHold.getBank_url()).isEqualTo(StubAndTcbBankService.STUB_BANK_URL);
        Assertions.assertThat(testOrderHold.getPaymentSystem()).isEqualTo(StubAndTcbBankService.STUB_SYSTEM_NAME);
    }

    @Test
    @Transactional
    public void _02_HoldV1CallWithSomeTcbLeadsToDefaultPaymentSystemOkay() {
        orderFlowTestUtils.setTcbOrdersCount(5);
        orderFlowTestUtils.setAllowPaymentSystemChoose(Collections.emptyList());
        //
        OrderService.InitOrderResult testOrderInit = _01_holdV1Call();
        Assertions.assertThat(testOrderInit.getBank_url()).isEqualTo(StubAndTcbBankService.STUB_BANK_URL);
        Assertions.assertThat(testOrderInit.getPaymentSystem()).isEqualTo(StubAndTcbBankService.STUB_SYSTEM_NAME);
        //
        OrderService.InitOrderResult testOrderHold = orderFlowTestUtils.holdOrderSuccessfull(testOrderInit.getOrderId(), HoldRequest.builder().build(), true);
        Assertions.assertThat(testOrderHold.getBank_url()).isEqualTo(StubAndTcbBankService.STUB_BANK_URL);
        Assertions.assertThat(testOrderHold.getPaymentSystem()).isEqualTo(StubAndTcbBankService.STUB_SYSTEM_NAME);
    }

    private OrderService.InitOrderResult holdV2WithNoPaymentSystemParameter() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(1)
                .build()
        );
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.fillCart(products);
        User seller = products.get(0).getSeller();
        //
        cartTestSupport.setCartAddressEndpoint();
        //
        return cartTestSupport.holdCartWithParams(seller.getId(), null);
    }

    @Test
    @Transactional
    public void _03_HoldV2CallWithZeroTcbLeadsToDefaultPaymentSystemOkay() {
        orderFlowTestUtils.setTcbOrdersCount(0);
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(NoonBankService.NOON_SCHEMA));
        //
        OrderService.InitOrderResult testOrderInit = holdV2WithNoPaymentSystemParameter();
        assertEquals(StubAndTcbBankService.STUB_BANK_URL, testOrderInit.getBank_url());
        assertEquals(StubAndTcbBankService.STUB_SYSTEM_NAME, testOrderInit.getPaymentSystem());
        //
        OrderService.InitOrderResult testOrderHold = orderFlowTestUtils.holdOrderSuccessfull(testOrderInit.getOrderId(), HoldRequest.builder().build(), true);
        Assertions.assertThat(testOrderHold.getBank_url()).isEqualTo(StubAndTcbBankService.STUB_BANK_URL);
        Assertions.assertThat(testOrderHold.getPaymentSystem()).isEqualTo(StubAndTcbBankService.STUB_SYSTEM_NAME);
    }

    @Test
    @Transactional
    public void _04_HoldV2CallWithSomeTcbLeadsToTcbOkay() {
        orderFlowTestUtils.setTcbOrdersCount(5);
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(NoonBankService.NOON_SCHEMA));
        //
        OrderService.InitOrderResult testOrderInit = holdV2WithNoPaymentSystemParameter();
        //assertEquals(StubAndTcbBankService.STUB_BANK_URL, testOrder1.getBank_url());
        assertEquals("tcb", testOrderInit.getPaymentSystem());
        //
        OrderService.InitOrderResult testOrderHold = orderFlowTestUtils.holdOrderSuccessfull(testOrderInit.getOrderId(), HoldRequest.builder().build(), true);
        //Assertions.assertThat(testOrderHold.getBank_url()).isEqualTo(StubAndTcbBankService.STUB_BANK_URL);
        Assertions.assertThat(testOrderHold.getPaymentSystem()).isEqualTo("tcb");
    }

    private OrderService.InitOrderResult _XX_HoldV2WithCustomPaymentSystem(String currencyCode) {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(1)
                .build()
        );
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.fillCart(products);
        User seller = products.get(0).getSeller();
        //
        cartTestSupport.setCartAddressEndpoint();
        //
        return cartTestSupport.holdCartWithParams(seller.getId(),
                HoldRequest.builder().paymentSystem(NoonBankService.NOON_SCHEMA).currencyCode(currencyCode).build());
    }

    @Test
    @Transactional
    public void _05_HoldV2CallWithCustomPSNoConfigAndSomeTcbLeadsToTcbOkay() {
        orderFlowTestUtils.setTcbOrdersCount(5);
        orderFlowTestUtils.setAllowPaymentSystemChoose(Collections.emptyList());
        //
        OrderService.InitOrderResult testOrderInit = _XX_HoldV2WithCustomPaymentSystem("RUB");
        //assertEquals(StubAndTcbBankService.STUB_BANK_URL, testOrder1.getBank_url());
        assertEquals("tcb", testOrderInit.getPaymentSystem());
        //
        OrderService.InitOrderResult testOrderHold = orderFlowTestUtils.holdOrderSuccessfull(testOrderInit.getOrderId(), HoldRequest.builder().build(), true);
        //Assertions.assertThat(testOrderHold.getBank_url()).isEqualTo(StubAndTcbBankService.STUB_BANK_URL);
        Assertions.assertThat(testOrderHold.getPaymentSystem()).isEqualTo("tcb");
    }

    @Test
    @Transactional
    public void _06_HoldV2CallWithCustomPSNoConfigAndZeroTcbLeadsToDefaultOkay() {
        orderFlowTestUtils.setTcbOrdersCount(0);
        orderFlowTestUtils.setAllowPaymentSystemChoose(Collections.emptyList());
        //
        OrderService.InitOrderResult testOrderInit = _XX_HoldV2WithCustomPaymentSystem("RUB");
        assertEquals(StubAndTcbBankService.STUB_BANK_URL, testOrderInit.getBank_url());
        assertEquals(StubAndTcbBankService.STUB_SYSTEM_NAME, testOrderInit.getPaymentSystem());
        //
        OrderService.InitOrderResult testOrderHold = orderFlowTestUtils.holdOrderSuccessfull(testOrderInit.getOrderId(), HoldRequest.builder().build(), true);
        Assertions.assertThat(testOrderHold.getBank_url()).isEqualTo(StubAndTcbBankService.STUB_BANK_URL);
        Assertions.assertThat(testOrderHold.getPaymentSystem()).isEqualTo(StubAndTcbBankService.STUB_SYSTEM_NAME);
    }

    @Test
    @Transactional
    public void _07_HoldV2CallWithCustomPSWithConfigAndSomeTcbLeadsToCustomOkay() {
        orderFlowTestUtils.setTcbOrdersCount(5);
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(NoonBankService.NOON_SCHEMA));
        //
        OrderService.InitOrderResult testOrderInit = _XX_HoldV2WithCustomPaymentSystem("AED");
        //assertEquals(StubAndTcbBankService.STUB_BANK_URL, testOrder1.getBank_url());
        assertEquals(NoonBankService.NOON_SCHEMA, testOrderInit.getPaymentSystem());
        //
        OrderService.InitOrderResult testOrderHold = orderFlowTestUtils.holdOrderSuccessfull(testOrderInit.getOrderId(), HoldRequest.builder().currencyCode("AED").build(), true);
        //Assertions.assertThat(testOrderHold.getBank_url()).isEqualTo(StubAndTcbBankService.STUB_BANK_URL);
        Assertions.assertThat(testOrderHold.getPaymentSystem()).isEqualTo(NoonBankService.NOON_SCHEMA);
    }

    @Test
    @Transactional
    public void _08_HoldV2CallWithCustomPSWithConfigAndZeroTcbLeadsToCustomOkay() {
        orderFlowTestUtils.setTcbOrdersCount(0);
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(NoonBankService.NOON_SCHEMA));
        //
        OrderService.InitOrderResult testOrderInit = _XX_HoldV2WithCustomPaymentSystem("AED");
        //assertEquals(StubAndTcbBankService.STUB_BANK_URL, testOrder1.getBank_url());
        assertEquals(NoonBankService.NOON_SCHEMA, testOrderInit.getPaymentSystem());
        //
        OrderService.InitOrderResult testOrderHold = orderFlowTestUtils.holdOrderSuccessfull(testOrderInit.getOrderId(), HoldRequest.builder().currencyCode("AED").build(), true);
        //Assertions.assertThat(testOrderHold.getBank_url()).isEqualTo(StubAndTcbBankService.STUB_BANK_URL);
        Assertions.assertThat(testOrderHold.getPaymentSystem()).isEqualTo(NoonBankService.NOON_SCHEMA);
    }

    @SneakyThrows
    public void _09_01_ConvertToPrepaymentFails(String bankSchema, String paymentSystem, String currencyCode, String bankService) {
        orderFlowTestUtils.setTcbOrdersCount(0);
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(bankSchema));
        //
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(1)
                .build()
        );
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.fillCart(products);
        User seller = products.get(0).getSeller();
        //
        cartTestSupport.setCartAddressEndpoint();
        //
        OrderService.InitOrderResult initOrder = cartTestSupport.holdCartWithParams(seller.getId(),
                HoldRequest.builder().paymentSystem(bankSchema).currencyCode(currencyCode).build());
        //
        orderFlowTestUtils.loadOrderSuccessfull(initOrder.getOrderId(), true);
        //
        Assertions.assertThat(initOrder.getPaymentSystem()).isEqualTo(paymentSystem);
        //
        ResponseEntity<String> convertHoldFail = orderFlowTestUtils.convertHoldToPrepayment(initOrder.getOrderId(), HttpStatus.Series.CLIENT_ERROR);
        Exception thrownPrepaymentUnsupported = orderFlowTestUtils.readExceptionFromText(convertHoldFail.getBody());
        Assertions.assertThat(thrownPrepaymentUnsupported.getMessage())
                .matches("Заказ .*: сервис оплаты " + bankService + ".* не поддерживает предоплаты");
    }

    @Test
    @Transactional
    public void _09_01_ConvertToPrepaymentOnBest2PayFails() {
        _09_01_ConvertToPrepaymentFails(Best2payAndTcbBankService.SCHEMA,
            "best2pay",
            "RUB",
            "Best2payAndTcbBankService");
    }

    private OrderService.InitOrderResult fillCartHoldOrder(String bankScheme, List<Product> products) {
        orderFlowTestUtils.fillCart(products);
        User seller = products.get(0).getSeller();
        //
        cartTestSupport.setCartAddressEndpoint();
        //
        return cartTestSupport.holdCartWithParams(seller.getId(), HoldRequest.builder().paymentSystem(bankScheme).build());
    }

    @Test
    @Transactional
    public void _10_01_AutoRollback_OnItemsUnavailable_Okay() {
        String bankSchema = TcbBankService.SCHEMA;
        //
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(bankSchema));
        //
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(1)
                .build()
        );
        commitAndStartNewTransaction();
        //
        products.get(0).getProductItems().forEach(pi -> {
            pi.setCount(1);
            productItemService.save(pi);
        });
        commitAndStartNewTransaction();
        //
        OrderService.InitOrderResult order1st = fillCartHoldOrder(bankSchema, products);
        //
        OrderService.InitOrderResult order2nd = fillCartHoldOrder(bankSchema, products);
        //
        orderFlowTestUtils.callOrderHoldCallback(order1st.getOrderId(), order1st.getBank_url().replace("https://", "http://"));
        //
        orderFlowTestUtils.callOrderHoldCallback(order2nd.getOrderId(), order2nd.getBank_url().replace("https://", "http://"));
        //
        rollbackAndStartNewTransaction();
        //
        Order orderSoldOk = orderFlowTestUtils.validateOrderState(order1st.getOrderId(), OrderState.HOLD);
        Order orderRefund = orderFlowTestUtils.validateOrderState(order2nd.getOrderId(), OrderState.REFUND);
        //
        Assertions.assertThat(orderSoldOk.getRefundReason()).isNull();
        Assertions.assertThat(orderRefund.getRefundReason()).isNotNull();
        Assertions.assertThat(orderRefund.getRefundReason().getName()).isEqualTo(OrderRefundReasonType.ITEMS_UNAVAILABLE);
    }

}