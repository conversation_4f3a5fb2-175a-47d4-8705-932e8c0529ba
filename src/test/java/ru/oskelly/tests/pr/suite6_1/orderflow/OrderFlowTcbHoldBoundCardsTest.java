package ru.oskelly.tests.pr.suite6_1.orderflow;

import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.pr.common.bonuses.BonusesServiceTestConfiguration;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2ParseException;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.component.HoldRequest;
import su.reddot.domain.model.banktransaction.OperationType;
import su.reddot.domain.model.counterparty.CardCounterparty;
import su.reddot.domain.model.counterparty.CounterpartyType;
import su.reddot.domain.model.order.OrderPayment;
import su.reddot.domain.model.order.OrderPaymentState;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.util.CallInTransaction;
import su.reddot.infrastructure.util.Utils;
import su.reddot.presentation.api.v2.Api2Response;

import javax.annotation.PostConstruct;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@TestMethodOrder(MethodOrderer.MethodName.class)
@ContextConfiguration(classes = {OrderFlowTestUtils.class, BonusesServiceTestConfiguration.class})
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
public class OrderFlowTcbHoldBoundCardsTest extends AbstractSpringTest {

    @Autowired
    private UserService userService;

    @Autowired
    private CallInTransaction callInTransaction;
    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private CartTestSupport cartTestSupport;

    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user-password}")
    private String password;
    @Value("${test-prepayments.usual-seller-id}")
    private Long usualSellerId;

    private static OrderFlowTestTcbMock orderFlowTestTcbMock;

    private static Long cardCpFailId;
    private static Long cardCpOkayId;
    private static Long cardCpOk02Id;

    @Value("${test.receipts.mock-server-host}")
    private String mockServerHost;
    @Value("${test.receipts.mock-server-tcb-bank-port}")
    private Integer mockTcbServerPort;


    private List<Long> prepareBuyerCounterparties() {
        User buyer = userService.getUserByEmail(buyerEmail);
        cardCpOkayId = orderFlowTestUtils.findOrCreateCardCounterpartyWithCardId(buyer, OrderFlowTestTcbMock.CLIENT_ID_RESP_OK01.toString());
        cardCpOk02Id = orderFlowTestUtils.findOrCreateCardCounterpartyWithCardId(buyer, OrderFlowTestTcbMock.CLIENT_ID_RESP_OK02.toString());
        cardCpFailId = orderFlowTestUtils.findOrCreateCardCounterpartyWithCardId(buyer, OrderFlowTestTcbMock.CLIENT_ID_RESP_FAIL.toString());
        return Lists.newArrayList(cardCpOkayId, cardCpFailId);
    }

    @PostConstruct
    private void init() {
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(TcbBankService.SCHEMA));
        User buyer = userService.getUserByEmail(buyerEmail);
        ApiV2Client apiV2Client = new ApiV2Client(buyerEmail, password);
        orderFlowTestUtils.init(buyerEmail, password);
        cartTestSupport.setUserId(buyer.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
        orderFlowTestTcbMock = Objects.isNull(orderFlowTestTcbMock) ? new OrderFlowTestTcbMock(mockServerHost, mockTcbServerPort) : orderFlowTestTcbMock;
        callInTransaction.runInNewTransaction(this::prepareBuyerCounterparties);
    }

    @AfterAll
    public static void done() {
        orderFlowTestTcbMock.stop();
    }

    private long fillCartBeforeHoldReturnSellerId() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(1)
                .build()
        );
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.fillCart(products);
        User seller = products.get(0).getSeller();
        //
        cartTestSupport.setCartAddressEndpoint();
        //
        return seller.getId();
    }

    @Test
    @SneakyThrows
    @Transactional
    @Rollback(value = false)
    public void _01_OrderFlow_TwoHoldsWithNullIdLeadsToSamePayUrl() {
        long sellerId = fillCartBeforeHoldReturnSellerId();
        //
        OrderService.InitOrderResult orderHoldInit = cartTestSupport.holdCartWithParams(sellerId,
                HoldRequest.builder().paymentSystem(TcbBankService.SCHEMA).build());
        Assertions.assertThat(orderHoldInit.getPaymentSystem()).isEqualTo(TcbBankService.TCB_HOLD_PAYMENT_SYSTEM_NAME);
        Assertions.assertThat(new URL(orderHoldInit.getBank_url())).hasParameter("redirectTo");
        Assertions.assertThat(new URL(orderHoldInit.getBank_url())).hasParameter(OrderFlowTestTcbMock.PAYURL_PARAM_CLIENT_ID, null);
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(orderHoldInit.getOrderId(), OrderState.HOLD_PROCESSING);
        orderFlowTestUtils.validateOrderPayment(orderHoldInit.getOrderId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_INPROGRESS);
        //
        OrderService.InitOrderResult orderHoldOne = orderFlowTestUtils.holdOrderSuccessfull(orderHoldInit.getOrderId(), HoldRequest.builder().build(), true);
        Assertions.assertThat(new URL(orderHoldOne.getBank_url())).hasParameter("redirectTo");
        Assertions.assertThat(new URL(orderHoldInit.getBank_url())).hasParameter(OrderFlowTestTcbMock.PAYURL_PARAM_CLIENT_ID, null);
        Assertions.assertThat(orderHoldInit.getBank_url()).isEqualTo(orderHoldOne.getBank_url());
        Assertions.assertThat(orderHoldInit).usingRecursiveComparison().isEqualTo(orderHoldOne);
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(orderHoldOne.getOrderId(), OrderState.HOLD_PROCESSING);
        orderFlowTestUtils.validateOrderPayment(orderHoldInit.getOrderId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_INPROGRESS);
        //
        OrderService.InitOrderResult orderHoldTwo = orderFlowTestUtils.holdOrderSuccessfull(orderHoldInit.getOrderId(), HoldRequest.builder().build(), true);
        Assertions.assertThat(new URL(orderHoldTwo.getBank_url())).hasParameter("redirectTo");
        Assertions.assertThat(new URL(orderHoldInit.getBank_url())).hasParameter(OrderFlowTestTcbMock.PAYURL_PARAM_CLIENT_ID, null);
        Assertions.assertThat(orderHoldInit.getBank_url()).isEqualTo(orderHoldTwo.getBank_url());
        Assertions.assertThat(orderHoldInit).usingRecursiveComparison().isEqualTo(orderHoldTwo);
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(orderHoldTwo.getOrderId(), OrderState.HOLD_PROCESSING);
        orderFlowTestUtils.validateOrderPayment(orderHoldInit.getOrderId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_INPROGRESS);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderHoldInit.getOrderId(), Lists.newArrayList(OperationType.HOLD));
    }

    @Test
    @SneakyThrows
    @Transactional
    @Rollback(value = false)
    public void _02_OrderFlow_TwoHoldsWithSameIdLeadsToSamePayUrl() {
        long sellerId = fillCartBeforeHoldReturnSellerId();
        //
        OrderService.InitOrderResult orderHoldInit = cartTestSupport.holdCartWithParams(sellerId,
                HoldRequest.builder()
                    .paymentSystem(TcbBankService.SCHEMA)
                    .paymentBuyerCounterpartyId(cardCpOkayId)
                .build());
        Assertions.assertThat(orderHoldInit.getPaymentSystem()).isEqualTo(TcbBankService.TCB_HOLD_PAYMENT_SYSTEM_NAME);
        Assertions.assertThat(new URL(orderHoldInit.getBank_url())).hasParameter("redirectTo");
        Assertions.assertThat(new URL(orderHoldInit.getBank_url())).hasParameter(OrderFlowTestTcbMock.PAYURL_PARAM_CLIENT_ID, OrderFlowTestTcbMock.CLIENT_ID_RESP_OK01.toString());
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(orderHoldInit.getOrderId(), OrderState.HOLD_PROCESSING);
        orderFlowTestUtils.validateOrderPayment(orderHoldInit.getOrderId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_INPROGRESS);
        //
        OrderService.InitOrderResult orderHoldOne = orderFlowTestUtils.holdOrderSuccessfull(orderHoldInit.getOrderId(),
                HoldRequest.builder()
                        .paymentBuyerCounterpartyId(cardCpOkayId)
                .build(),
                true);
        Assertions.assertThat(new URL(orderHoldOne.getBank_url())).hasParameter("redirectTo");
        Assertions.assertThat(new URL(orderHoldInit.getBank_url())).hasParameter(OrderFlowTestTcbMock.PAYURL_PARAM_CLIENT_ID, OrderFlowTestTcbMock.CLIENT_ID_RESP_OK01.toString());
        Assertions.assertThat(orderHoldInit.getBank_url()).isEqualTo(orderHoldOne.getBank_url());
        Assertions.assertThat(orderHoldInit).usingRecursiveComparison().isEqualTo(orderHoldOne);
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(orderHoldOne.getOrderId(), OrderState.HOLD_PROCESSING);
        orderFlowTestUtils.validateOrderPayment(orderHoldInit.getOrderId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_INPROGRESS);
        //
        OrderService.InitOrderResult orderHoldTwo = orderFlowTestUtils.holdOrderSuccessfull(orderHoldInit.getOrderId(),
                HoldRequest.builder()
                    .paymentBuyerCounterpartyId(cardCpOkayId)
                .build(),
                true);
        Assertions.assertThat(new URL(orderHoldTwo.getBank_url())).hasParameter("redirectTo");
        Assertions.assertThat(new URL(orderHoldInit.getBank_url())).hasParameter(OrderFlowTestTcbMock.PAYURL_PARAM_CLIENT_ID, OrderFlowTestTcbMock.CLIENT_ID_RESP_OK01.toString());
        Assertions.assertThat(orderHoldInit.getBank_url()).isEqualTo(orderHoldTwo.getBank_url());
        Assertions.assertThat(orderHoldInit).usingRecursiveComparison().isEqualTo(orderHoldTwo);
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(orderHoldTwo.getOrderId(), OrderState.HOLD_PROCESSING);
        orderFlowTestUtils.validateOrderPayment(orderHoldInit.getOrderId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_INPROGRESS);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderHoldInit.getOrderId(), Lists.newArrayList(OperationType.HOLD));
    }

    @Test
    @SneakyThrows
    @Transactional
    @Rollback(value = false)
    public void _02_OrderFlow_TwoHoldsFailThanOkayLeadsToOkayOrder() {
        long sellerId = fillCartBeforeHoldReturnSellerId();
        //
        OrderService.InitOrderResult orderHoldOne = cartTestSupport.holdCartWithParams(sellerId,
                HoldRequest.builder()
                        .paymentSystem(TcbBankService.SCHEMA)
                        .paymentBuyerCounterpartyId(cardCpFailId)
                .build());
        Assertions.assertThat(orderHoldOne.getPaymentSystem()).isEqualTo(TcbBankService.TCB_HOLD_PAYMENT_SYSTEM_NAME);
        Assertions.assertThat(new URL(orderHoldOne.getBank_url())).hasParameter("redirectTo");
        Assertions.assertThat(new URL(orderHoldOne.getBank_url())).hasParameter(OrderFlowTestTcbMock.PAYURL_PARAM_CLIENT_ID, OrderFlowTestTcbMock.CLIENT_ID_RESP_FAIL.toString());
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(orderHoldOne.getOrderId(), OrderState.HOLD_PROCESSING);
        OrderPayment orderPaymentOne = orderFlowTestUtils.validateOrderPayment(orderHoldOne.getOrderId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_INPROGRESS);
        //
        try {
            orderFlowTestUtils.holdOrderSuccessfull(orderHoldOne.getOrderId(),
                    HoldRequest.builder()
                            .paymentBuyerCounterpartyId(cardCpOkayId)
                            .build(), true);
            Assertions.fail("Unreachable code: must throw exception");
        } catch (ApiV2ParseException e) {
            Api2Response<String> errorRsp = orderFlowTestUtils.getRawApi2Response(e.getRawData());
            Assertions.assertThat(errorRsp.getMessage()).matches("Ошибка при оплате заказа .* \\(tcb-1.0\\), пожалуйста, повторите попытку позже");
            Assertions.assertThat(errorRsp.getHumanMessage()).matches("Ошибка при оплате заказа .* \\(tcb-1.0\\), пожалуйста, повторите попытку позже");
        }
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(orderHoldOne.getOrderId(), OrderState.HOLD_PROCESSING);
        orderFlowTestUtils.validateOrderPayment(orderHoldOne.getOrderId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_INPROGRESS);
        //
        orderFlowTestUtils.callOrderHoldCallback(orderHoldOne.getOrderId(), orderHoldOne.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(orderHoldOne.getOrderId(), OrderState.HOLD_ERROR);
        orderFlowTestUtils.validateOrderPayment(orderHoldOne.getOrderId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_FAIL);
        //
        OrderService.InitOrderResult orderHoldTwo = orderFlowTestUtils.holdOrderSuccessfull(orderHoldOne.getOrderId(),
                HoldRequest.builder()
                        .paymentBuyerCounterpartyId(cardCpOkayId)
                        .build(), true);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(orderHoldOne.getOrderId(), OrderState.HOLD_PROCESSING);
        OrderPayment orderPaymentTwo =orderFlowTestUtils.validateOrderPayment(orderHoldOne.getOrderId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_INPROGRESS);
        //
        orderFlowTestUtils.callOrderHoldCallback(orderHoldTwo.getOrderId(), orderHoldTwo.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(orderHoldOne.getOrderId(), OrderState.HOLD);
        orderFlowTestUtils.validateOrderPayment(orderHoldOne.getOrderId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_DONE);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderHoldOne.getOrderId(), Lists.newArrayList(OperationType.HOLD, OperationType.HOLD));
        //
        Assertions.assertThat(orderPaymentOne.getUuid()).isEqualTo(orderPaymentTwo.getUuid());
    }

    @Test
    @SneakyThrows
    @Transactional
    @Rollback(value = false)
    public void _03_OrderFlow_TwoHoldsOkayThanFailLeadsToOkayOrder() {
        long sellerId = fillCartBeforeHoldReturnSellerId();
        //
        OrderService.InitOrderResult orderHoldOne = cartTestSupport.holdCartWithParams(sellerId,
                HoldRequest.builder()
                        .paymentSystem(TcbBankService.SCHEMA)
                        .paymentBuyerCounterpartyId(cardCpOkayId)
                        .build());
        Assertions.assertThat(orderHoldOne.getPaymentSystem()).isEqualTo(TcbBankService.TCB_HOLD_PAYMENT_SYSTEM_NAME);
        Assertions.assertThat(new URL(orderHoldOne.getBank_url())).hasParameter("redirectTo");
        Assertions.assertThat(new URL(orderHoldOne.getBank_url())).hasParameter(OrderFlowTestTcbMock.PAYURL_PARAM_CLIENT_ID, OrderFlowTestTcbMock.CLIENT_ID_RESP_OK01.toString());
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(orderHoldOne.getOrderId(), OrderState.HOLD_PROCESSING);
        orderFlowTestUtils.validateOrderPayment(orderHoldOne.getOrderId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_INPROGRESS);
        //
        OrderService.InitOrderResult orderHoldTwo = orderFlowTestUtils.holdOrderSuccessfull(orderHoldOne.getOrderId(),
                HoldRequest.builder()
                        .paymentBuyerCounterpartyId(cardCpOkayId)
                        .build(),
                true);
        Assertions.assertThat(new URL(orderHoldTwo.getBank_url())).hasParameter("redirectTo");
        Assertions.assertThat(new URL(orderHoldTwo.getBank_url())).hasParameter(OrderFlowTestTcbMock.PAYURL_PARAM_CLIENT_ID, OrderFlowTestTcbMock.CLIENT_ID_RESP_OK01.toString());
        Assertions.assertThat(orderHoldTwo.getBank_url()).isEqualTo(orderHoldOne.getBank_url());
        Assertions.assertThat(orderHoldTwo).usingRecursiveComparison().isEqualTo(orderHoldOne);
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(orderHoldTwo.getOrderId(), OrderState.HOLD_PROCESSING);
        orderFlowTestUtils.validateOrderPayment(orderHoldTwo.getOrderId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_INPROGRESS);
        //
        orderFlowTestUtils.callOrderHoldCallback(orderHoldOne.getOrderId(), orderHoldOne.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(orderHoldOne.getOrderId(), OrderState.HOLD);
        OrderPayment orderPaymentOne = orderFlowTestUtils.validateOrderPayment(orderHoldOne.getOrderId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_DONE);
        //
        try {
            orderFlowTestUtils.holdOrderSuccessfull(orderHoldOne.getOrderId(),
                    HoldRequest.builder()
                            .paymentBuyerCounterpartyId(cardCpFailId)
                            .build(), true);
            Assertions.fail("Unreachable code: must throw exception");
        } catch (ApiV2ParseException e) {
            Api2Response<String> errorRsp = orderFlowTestUtils.getRawApi2Response(e.getRawData());
            Assertions.assertThat(errorRsp.getMessage()).matches("Заказ .*: некорректное состояние заказа HOLD");
            Assertions.assertThat(errorRsp.getHumanMessage()).matches("Заказ .*: некорректное состояние заказа HOLD");
        }
        //
        orderFlowTestUtils.validateOrderState(orderHoldOne.getOrderId(), OrderState.HOLD);
        OrderPayment orderPaymentTwo = orderFlowTestUtils.validateOrderPayment(orderHoldOne.getOrderId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_DONE);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderHoldOne.getOrderId(), Lists.newArrayList(OperationType.HOLD));
        //
        Assertions.assertThat(orderPaymentOne.getUuid()).isEqualTo(orderPaymentTwo.getUuid());
    }

    @Test
    @SneakyThrows
    @Transactional
    @Rollback(value = false)
    public void _05_OrderFlow_ClientIdFlow_ClientId01_ClientNull() {
        _XX_OrderFlow_ClientIdFlow_ClientIdXX_ClientIdYY(OrderFlowTestTcbMock.CLIENT_ID_RESP_OK01.toString(), null);
    }

    @Test
    @SneakyThrows
    @Transactional
    @Rollback(value = false)
    public void _06_OrderFlow_ClientIdFlow_ClientNull_ClientId02() {
        _XX_OrderFlow_ClientIdFlow_ClientIdXX_ClientIdYY(null, OrderFlowTestTcbMock.CLIENT_ID_RESP_OK02.toString());
    }

    @SneakyThrows
    private OrderDTO _XX_OrderFlow_ClientIdFlow_ClientIdXX_ClientIdYY(String cardId1st, String cardId2nd) {
        long sellerId = fillCartBeforeHoldReturnSellerId();
        //
        Long cardCpId1st = Objects.isNull(cardId1st)
                ? null
                : orderFlowTestUtils.findOrCreateCardCounterpartyWithCardId(cartTestSupport.getBuyer(), cardId1st);
        Long cardCpId2nd = Objects.isNull(cardId2nd)
                ? null
                : orderFlowTestUtils.findOrCreateCardCounterpartyWithCardId(cartTestSupport.getBuyer(), cardId2nd);
        //
        OrderService.InitOrderResult orderHoldOne = cartTestSupport.holdCartWithParams(sellerId,
                HoldRequest.builder().paymentSystem(TcbBankService.SCHEMA).paymentBuyerCounterpartyId(cardCpId1st)
                        .build());
        Assertions.assertThat(orderHoldOne.getPaymentSystem()).isEqualTo(TcbBankService.TCB_HOLD_PAYMENT_SYSTEM_NAME);
        Assertions.assertThat(new URL(orderHoldOne.getBank_url())).hasParameter("redirectTo");
        Assertions.assertThat(new URL(orderHoldOne.getBank_url())).hasParameter(OrderFlowTestTcbMock.PAYURL_PARAM_CLIENT_ID, cardId1st);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(orderHoldOne.getOrderId(), OrderState.HOLD_PROCESSING);
        OrderPayment orderPaymentInit = orderFlowTestUtils.validateOrderPayment(orderHoldOne.getOrderId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_INPROGRESS);
        Assertions.assertThat(orderPaymentInit.getClientId()).isEqualTo(cardId1st);
        //
        try {
            orderFlowTestUtils.holdOrderSuccessfull(orderHoldOne.getOrderId(),
                    HoldRequest.builder().paymentBuyerCounterpartyId(cardCpId2nd)
                            .build(), true);
            Assertions.fail("Unreachable code: must throw exception");
        } catch (ApiV2ParseException e) {
            Api2Response<String> errorRsp = orderFlowTestUtils.getRawApi2Response(e.getRawData());
            Assertions.assertThat(errorRsp.getMessage()).matches("Ошибка при оплате заказа .* \\(tcb-1.0\\), пожалуйста, повторите попытку позже");
            Assertions.assertThat(errorRsp.getHumanMessage()).matches("Ошибка при оплате заказа .* \\(tcb-1.0\\), пожалуйста, повторите попытку позже");
        }
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(orderHoldOne.getOrderId(), OrderState.HOLD_PROCESSING);
        orderFlowTestUtils.validateOrderPayment(orderHoldOne.getOrderId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_INPROGRESS);
        //
        orderFlowTestUtils.callOrderHoldCallback(orderHoldOne.getOrderId(), orderHoldOne.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(orderHoldOne.getOrderId(), OrderState.HOLD);
        orderFlowTestUtils.validateOrderPayment(orderHoldOne.getOrderId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_DONE);
        //
        try {
            orderFlowTestUtils.holdOrderSuccessfull(orderHoldOne.getOrderId(),
                    HoldRequest.builder().paymentBuyerCounterpartyId(cardCpId2nd)
                            .build(), true);
            Assertions.fail("Unreachable code: must throw exception");
        } catch (ApiV2ParseException e) {
            Api2Response<String> errorRsp = orderFlowTestUtils.getRawApi2Response(e.getRawData());
            Assertions.assertThat(errorRsp.getMessage()).matches("Заказ .*: некорректное состояние заказа HOLD");
            Assertions.assertThat(errorRsp.getHumanMessage()).matches("Заказ .*: некорректное состояние заказа HOLD");
        }
        //
        orderFlowTestUtils.validateOrderState(orderHoldOne.getOrderId(), OrderState.HOLD);
        OrderPayment orderPaymentDone = orderFlowTestUtils.validateOrderPayment(orderHoldOne.getOrderId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_DONE);
        Assertions.assertThat(orderPaymentDone.getClientId()).isEqualTo(cardId1st);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderHoldOne.getOrderId(), Lists.newArrayList(OperationType.HOLD));
        //
        return orderFlowTestUtils.loadOrderSuccessfull(orderHoldOne.getOrderId(), true);
    }

    private Map<Long, String> listCardCp(String email) {
        User user = userService.getUserByEmail(email);
        return user.getCounterparties().stream()
                .filter(cp -> cp.getType() == CounterpartyType.CARD)
                .map(Utils::unproxyHibernateEntity)
                .map(CardCounterparty.class::cast)
                .collect(Collectors.toMap(CardCounterparty::getId, CardCounterparty::getCardRefId));
    }



    @SneakyThrows
    private void _06_OrderFlow_HoldOrderWithClientId(Long counterpartyId) {
        long sellerId = fillCartBeforeHoldReturnSellerId();
        //
        OrderService.InitOrderResult orderHoldInit = cartTestSupport.holdCartWithParams(sellerId,
                HoldRequest.builder().paymentSystem(TcbBankService.SCHEMA).paymentBuyerCounterpartyId(counterpartyId).build());
        Assertions.assertThat(orderHoldInit.getPaymentSystem()).isEqualTo(TcbBankService.TCB_HOLD_PAYMENT_SYSTEM_NAME);
        Assertions.assertThat(new URL(orderHoldInit.getBank_url())).hasParameter("redirectTo");
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(orderHoldInit.getOrderId(), OrderState.HOLD_PROCESSING);
        orderFlowTestUtils.validateOrderPayment(orderHoldInit.getOrderId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_INPROGRESS);
        //
        orderFlowTestUtils.callOrderHoldCallback(orderHoldInit.getOrderId(), orderHoldInit.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(orderHoldInit.getOrderId(), OrderState.HOLD);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _06_OrderFlow_SuccessfulHoldLeadsToCardBind() {
        Map<Long, String> cardsListOnStart = listCardCp(buyerEmail);
        //
        _06_OrderFlow_HoldOrderWithClientId(null); // Holding 1st time (fresh card) -> must add it
        Utils.sleepMillis(2500); // AutoBind in async thread, ugly
        Map<Long, String> cardsListOn1stHold = listCardCp(buyerEmail);
        Map<Long, String> diffOn1stHold = new HashMap<>(cardsListOn1stHold);
        diffOn1stHold.keySet().removeAll(cardsListOnStart.keySet());
        Assertions.assertThat(diffOn1stHold).hasSize(1);
        //
        Long saveId = diffOn1stHold.keySet().stream().findFirst().orElse(null);
        //
        _06_OrderFlow_HoldOrderWithClientId(saveId); // Holding 2nd time (same card) -> must NOT add it
        Utils.sleepMillis(2500); // AutoBind in async thread, ugly
        Map<Long, String> cardsListOn2ndHold = listCardCp(buyerEmail);
        Map<Long, String> diffOn2ndHold = new HashMap<>(cardsListOn2ndHold);
        diffOn2ndHold.keySet().removeAll(cardsListOnStart.keySet());
        Assertions.assertThat(diffOn2ndHold).hasSize(1);
        //
        Assertions.assertThat(diffOn2ndHold).isEqualTo(diffOn1stHold);
    }

}
