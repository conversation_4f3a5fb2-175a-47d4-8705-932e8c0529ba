package ru.oskelly.tests.pr.suite6_1.orderflow;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.domain.dao.address.CountryRepository;
import su.reddot.domain.dao.user.SaleShipmentRouteRepository;
import su.reddot.domain.model.address.Country;
import su.reddot.domain.model.address.CountryContextNameEnum;
import su.reddot.domain.model.banktransaction.OperationType;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.fiscalreceiptrequest.FiscalReceiptRequestType;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPayment;
import su.reddot.domain.model.order.OrderPaymentState;
import su.reddot.domain.model.order.OrderRefundReasonType;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.duty.DutyDTO;
import su.reddot.domain.service.dto.order.GroupedCart;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.dto.order.OrderPositionDTO;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.util.CallInTransaction;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@TestMethodOrder(MethodOrderer.MethodName.class)
@ContextConfiguration(classes = {OrderFlowTestUtils.class})
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
public class OrderFlowRuDutiesTest extends AbstractSpringTest {

    @Autowired
    private UserService userService;
    @Autowired
    private CountryRepository countryRepository;
    @Autowired
    private SaleShipmentRouteRepository saleShipmentRouteRepository;

    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private CartTestSupport cartTestSupport;
    @Autowired
    protected CallInTransaction callInTransaction;

    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user-password}")
    private String password;
    @Value("${test-prepayments.i11ls-seller-id}")
    private Long seller4mOtherCountryId;
    @Value("${test-prepayments.i11ls-seller-counterparty-id}")
    private Long seller4mOtherCountryCounterpartyId;
    @Value("${test-prepayments.pickup-id}")
    private Long pickupId;
    @Value("${test-prepayments.delivery-id}")
    private Long deliveryId;

    private static OrderFlowTestTcbMock orderFlowTestTcbMock;

    @Value("${test.receipts.mock-server-host}")
    private String mockServerHost;
    @Value("${test.receipts.mock-server-tcb-bank-port}")
    private Integer mockTcbServerPort;

    private static final String COUNTRY_CODE = "IT";

    private Long prepareAdminsUser() {
        User adminsUser = userService.getUserByEmail(buyerEmail);
        orderFlowTestUtils.enableUserAuthority(adminsUser.getId(), AuthorityName.ORDER_PREPAYMENTS, true);
        orderFlowTestUtils.enableUserAuthority(adminsUser.getId(), AuthorityName.ORDER_PAYOUTS, true);
        orderFlowTestUtils.enableUserAuthority(adminsUser.getId(), AuthorityName.ORDER_MANUAL_CHANGE_DELIVERY_STATE, true);
        return adminsUser.getId();
    }

    @PostConstruct
    private void init() {
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(TcbBankService.SCHEMA));
        User buyer = userService.getUserByEmail(buyerEmail);
        ApiV2Client apiV2Client = new ApiV2Client(buyerEmail, password);
        orderFlowTestUtils.init(buyerEmail, password);
        cartTestSupport.setUserId(buyer.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
        orderFlowTestTcbMock = Objects.isNull(orderFlowTestTcbMock) ? new OrderFlowTestTcbMock(mockServerHost, mockTcbServerPort) : orderFlowTestTcbMock;
        callInTransaction.runInNewTransaction(this::prepareAdminsUser);
        callInTransaction.runInNewTransaction(() -> orderFlowTestUtils.prepareUsualSellerData(seller4mOtherCountryCounterpartyId));
        callInTransaction.runInNewTransaction(() -> orderFlowTestUtils.setCurrencyRate("EUR", "RUB", BigDecimal.valueOf(110_00, 2)));
    }

    @BeforeEach
    public void initEach() {
        callInTransaction.runInNewTransaction(() -> orderFlowTestUtils.enableCountryContext(COUNTRY_CODE, CountryContextNameEnum.SELLER_ADDRESS));
    }

    @AfterEach
    public void doneEach() {
        callInTransaction.runInNewTransaction(() -> saveSellerPickupCountryId(seller4mOtherCountryId, null));
    }

    @AfterAll
    public static void done() {
        orderFlowTestTcbMock.stop();
        orderFlowTestTcbMock = null;
    }

    private void saveSellerPickupCountryId(long userId, String countryIsoCodeAlpha2) {
        Country country = Objects.isNull(countryIsoCodeAlpha2)
                ? null
                : countryRepository.findByIsoCodeAlpha2(countryIsoCodeAlpha2);
        User user = userService.getOne(userId);
        user.setPickupCountry(country);
        userService.save(user);
    }

    private void _XX_orderFlowDuties_validateCart(GroupedCart groupedCart, BigDecimal dutiesAmount) {
        String cartData = orderFlowTestUtils.intoJson(groupedCart);
        Assertions.assertThat(groupedCart.getGroups()).allSatisfy(it -> {
            Assertions.assertThat(it.getDutiesAmount()).isEqualByComparingTo(dutiesAmount);
            BigDecimal dutiesTotalSum = CollectionUtils.emptyIfNull(it.getDuties()).stream()
                    .map(DutyDTO::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            Assertions.assertThat(dutiesTotalSum).isEqualByComparingTo(dutiesAmount);
        });
    }

    private void _01_01_fromCNToRU_happyPath_lessThan200EUR_validateCart(GroupedCart groupedCart, BigDecimal cartAmount) {
        Assertions.assertThat(groupedCart.getGroups()).hasSize(1);
        OrderDTO activeCart = groupedCart.getGroups().get(0);
        //
        Assertions.assertThat(activeCart.getFinalAmount()).isEqualByComparingTo(cartAmount);
        // Validating order duties: they must be BigDecimal.ZERO
        Assertions.assertThat(activeCart.getDutiesAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        BigDecimal dutiesTotalSum = CollectionUtils.emptyIfNull(activeCart.getDuties()).stream()
                .map(DutyDTO::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        Assertions.assertThat(dutiesTotalSum).isEqualByComparingTo(BigDecimal.ZERO);
        //
        Assertions.assertThat(activeCart.getItems()).hasSize(1);
        OrderPositionDTO activeItem = activeCart.getItems().get(0);
        //
        Assertions.assertThat(activeItem.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
        Assertions.assertThat(activeItem.getAmountsDetails().getRawSellAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
        Assertions.assertThat(activeItem.getAmountsDetails().getDisplayAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
    }

    private OrderDTO _01_01_fromCNToRU_happyPath_lessThan200EUR(boolean isPrepayMode) {
        saveSellerPickupCountryId(seller4mOtherCountryId, COUNTRY_CODE);
        commitAndStartNewTransaction();
        //
        long itemAmount = 10_000_00;
        long dutiesAmnt = 0;
        long authAmount = itemAmount + dutiesAmnt + 500_00;
        long sellAmount = itemAmount + dutiesAmnt + 500_00;
        long backAmount = authAmount - sellAmount;
        //
        OrderFlowTestUtils.TestConfig testConfig = OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(seller4mOtherCountryId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(seller4mOtherCountryCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .prepayBefOrderConfirm(isPrepayMode)
                .paymentsSchema(TcbBankService.SCHEMA)
                .itemsCount(1)

                .validateCart(it -> _01_01_fromCNToRU_happyPath_lessThan200EUR_validateCart(it, BigDecimal.valueOf(authAmount, 2)))

                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .orderPayAuthAmount(BigDecimal.valueOf(authAmount, 2))
                .orderPayCaptAmount(BigDecimal.valueOf(sellAmount, 2))
                .sellerPayoutAmount(7_500_00L)
                .noAgentReportCompare(true)

                .build();
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, testConfig);
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        //
        OrderPayment opRefund = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_DONE);
        Assertions.assertThat(opRefund.getAmountInBase()).isEqualByComparingTo(testConfig.getOrderPayAuthAmount());
        Assertions.assertThat(opRefund.getCaptureAmountInBase()).isEqualByComparingTo(testConfig.getOrderPayCaptAmount());
        Assertions.assertThat(opRefund.getRefundAmountInBase()).isNull();
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, testConfig.getOrderPayAuthAmount());
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_COMPLETE, testConfig.getOrderPayCaptAmount());
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.SELLER_PAYOUT, testConfig.getSellerPayoutAmount());
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), -1, it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(itemAmount + dutiesAmnt, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(itemAmount + dutiesAmnt, 2));
            //
            Assertions.assertThat(it.getDutiesExtraAmount()).isEqualByComparingTo(BigDecimal.valueOf(dutiesAmnt, 2));
            Assertions.assertThat(it.getDutiesExtraHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(dutiesAmnt, 2));
            Assertions.assertThat(it.getDutiesExtraEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(dutiesAmnt, 2));
        });
        //
        return testOrder;
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_01_authCapture_fromCNToRU_happyPath_lessThan200EUR() {
        OrderDTO testOrder = _01_01_fromCNToRU_happyPath_lessThan200EUR(false);
        //
        Order order = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        Assertions.assertThat(order.getPrepaymentAmount()).isNull();
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_DONE);
        Assertions.assertThat(orderPayment.getIsPrepayment()).isFalse();
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_01_captureRefunds_fromCNToRU_happyPath_lessThan200EUR() {
        OrderDTO testOrder = _01_01_fromCNToRU_happyPath_lessThan200EUR(true);
        //
        Order order = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        Assertions.assertThat(order.getPrepaymentAmount()).isEqualByComparingTo(testOrder.getFinalAmount());
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_DONE);
        Assertions.assertThat(orderPayment.getIsPrepayment()).isTrue();
    }

    private void _01_02_fromCNToRU_happyPath_moreThan200EUR_validateCart(GroupedCart groupedCart, BigDecimal cartAmount) {
        Assertions.assertThat(groupedCart.getGroups()).hasSize(1);
        OrderDTO activeCart = groupedCart.getGroups().get(0);
        //
//        Assertions.assertThat(activeCart.getFinalAmount()).isEqualByComparingTo(cartAmount);
        // Validating order duties: they must be BigDecimal.ZERO
//        Assertions.assertThat(activeCart.getDutiesAmount()).isEqualByComparingTo(BigDecimal.ZERO);
//        BigDecimal dutiesTotalSum = CollectionUtils.emptyIfNull(activeCart.getDuties()).stream()
//                .map(DutyDTO::getAmount)
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//        Assertions.assertThat(dutiesTotalSum).isEqualByComparingTo(BigDecimal.ZERO);
        //
        Assertions.assertThat(activeCart.getItems()).hasSize(1);
        OrderPositionDTO activeItem = activeCart.getItems().get(0);
        //
        Assertions.assertThat(activeItem.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(108_700_00, 2));
        Assertions.assertThat(activeItem.getFinalAmount()).isEqualByComparingTo(BigDecimal.valueOf(108_700_00, 2));
    }

    private OrderDTO _01_02_fromCNToRU_happyPath_moreThan200EUR(boolean isPrepayMode) {
        saveSellerPickupCountryId(seller4mOtherCountryId, COUNTRY_CODE);
        commitAndStartNewTransaction();
        //
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(seller4mOtherCountryId)
                .maxItems(1)
                .build()
        );
        products.get(0).setCurrentPrice(BigDecimal.valueOf(100_000_00, 2));
        commitAndStartNewTransaction();
        //
        long itemAmount = 100_000_00;
        long dutiesAmnt = 8_700_00;
        long authAmount = itemAmount + dutiesAmnt + 500_00;
        long sellAmount = itemAmount + dutiesAmnt + 500_00;
        long backAmount = authAmount - sellAmount;
        //
        List<Long> productIds = products.stream().map(Product::getId).collect(Collectors.toList());
        OrderFlowTestUtils.TestConfig testConfig = OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(seller4mOtherCountryId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(seller4mOtherCountryCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .prepayBefOrderConfirm(isPrepayMode)
                .paymentsSchema(TcbBankService.SCHEMA)
                .productIdsList(productIds).itemsCount(1)

                .validateCart(it -> _01_02_fromCNToRU_happyPath_moreThan200EUR_validateCart(it, BigDecimal.valueOf(authAmount, 2)))

                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .orderPayAuthAmount(BigDecimal.valueOf(authAmount, 2))
                .orderPayCaptAmount(BigDecimal.valueOf(sellAmount, 2))
                .sellerPayoutAmount(80_000_00L)
                .noAgentReportCompare(true)

                .build();
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, testConfig);
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        //
        OrderPayment opRefund = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_DONE);
        Assertions.assertThat(opRefund.getAmountInBase()).isEqualByComparingTo(testConfig.getOrderPayAuthAmount());
        Assertions.assertThat(opRefund.getCaptureAmountInBase()).isEqualByComparingTo(testConfig.getOrderPayCaptAmount());
        Assertions.assertThat(opRefund.getRefundAmountInBase()).isNull();
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, testConfig.getOrderPayAuthAmount());
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_COMPLETE, testConfig.getOrderPayCaptAmount());
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.SELLER_PAYOUT, testConfig.getSellerPayoutAmount());
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(0), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(itemAmount + dutiesAmnt, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(itemAmount + dutiesAmnt, 2));
            //
            Assertions.assertThat(it.getDutiesExtraAmount()).isEqualByComparingTo(BigDecimal.valueOf(dutiesAmnt, 2));
            Assertions.assertThat(it.getDutiesExtraHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(dutiesAmnt, 2));
            Assertions.assertThat(it.getDutiesExtraEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(dutiesAmnt, 2));
        });
        //
        return testOrder;
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_02_authCapture_fromCNToRU_happyPath_moreThan200EUR() {
        OrderDTO testOrder = _01_02_fromCNToRU_happyPath_moreThan200EUR(false);
        //
        Order order = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        Assertions.assertThat(order.getPrepaymentAmount()).isNull();
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_DONE);
        Assertions.assertThat(orderPayment.getIsPrepayment()).isFalse();
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_02_captureRefunds_fromCNToRU_happyPath_moreThan200EUR() {
        OrderDTO testOrder = _01_02_fromCNToRU_happyPath_moreThan200EUR(true);
        //
        Order order = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        Assertions.assertThat(order.getPrepaymentAmount()).isEqualByComparingTo(testOrder.getFinalAmount());
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_DONE);
        Assertions.assertThat(orderPayment.getIsPrepayment()).isTrue();
    }

    private OrderDTO _01_02_fromCNToRU_happyPath_moreThan200EURPromo10k(boolean isPrepayMode) {
        saveSellerPickupCountryId(seller4mOtherCountryId, COUNTRY_CODE);
        commitAndStartNewTransaction();
        //
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(seller4mOtherCountryId)
                .maxItems(1)
                .build()
        );
        products.get(0).setCurrentPrice(BigDecimal.valueOf(100_000_00, 2));
        commitAndStartNewTransaction();
        //
        long itmSalePrc = 100_000_00;
        long dutiesAmnt = 8_700_00;
        long discAmount = 10_000_00;
        long itemAmount = itmSalePrc + dutiesAmnt;
        long ordersAmnt = itemAmount + 500_00;
        long authAmount = ordersAmnt - discAmount;
        long sellAmount = ordersAmnt - discAmount;
        long backAmount = authAmount - sellAmount;
        //
        List<Long> productIds = products.stream().map(Product::getId).collect(Collectors.toList());
        OrderFlowTestUtils.TestConfig testConfig = OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(seller4mOtherCountryId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(seller4mOtherCountryCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .prepayBefOrderConfirm(isPrepayMode)
                .paymentsSchema(TcbBankService.SCHEMA)
                .productIdsList(productIds).itemsCount(1)
                .promoCodeAmounts(BigDecimal.valueOf(discAmount, 2))

                .validateCart(it -> _01_02_fromCNToRU_happyPath_moreThan200EUR_validateCart(it, BigDecimal.valueOf(authAmount, 2)))

                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .orderPayAuthAmount(BigDecimal.valueOf(authAmount, 2))
                .orderPayCaptAmount(BigDecimal.valueOf(sellAmount, 2))
                .sellerPayoutAmount(80_000_00L)
                .noAgentReportCompare(true)

                .build();
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, testConfig);
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        //
        OrderPayment opRefund = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_DONE);
        Assertions.assertThat(opRefund.getAmountInBase()).isEqualByComparingTo(testConfig.getOrderPayAuthAmount());
        Assertions.assertThat(opRefund.getCaptureAmountInBase()).isEqualByComparingTo(testConfig.getOrderPayCaptAmount());
        Assertions.assertThat(opRefund.getRefundAmountInBase()).isNull();
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, testConfig.getOrderPayAuthAmount());
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_COMPLETE, testConfig.getOrderPayCaptAmount());
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.SELLER_PAYOUT, testConfig.getSellerPayoutAmount());
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), productIds.get(0), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(itemAmount, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(itemAmount - discAmount, 2));
            //
            Assertions.assertThat(it.getDutiesExtraAmount()).isEqualByComparingTo(BigDecimal.valueOf(dutiesAmnt, 2));
            Assertions.assertThat(it.getDutiesExtraHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(dutiesAmnt, 2));
            Assertions.assertThat(it.getDutiesExtraEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(dutiesAmnt, 2));
        });
        //
        return testOrder;
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_02_authCapture_fromCNToRU_happyPath_moreThan200EURPromo10k() {
        OrderDTO testOrder = _01_02_fromCNToRU_happyPath_moreThan200EURPromo10k(false);
        //
        Order order = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        Assertions.assertThat(order.getPrepaymentAmount()).isNull();
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_DONE);
        Assertions.assertThat(orderPayment.getIsPrepayment()).isFalse();
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_02_captureRefunds_fromCNToRU_happyPath_moreThan200EURPromo10k() {
        OrderDTO testOrder = _01_02_fromCNToRU_happyPath_moreThan200EURPromo10k(true);
        //
        Order order = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        Assertions.assertThat(order.getPrepaymentAmount()).isEqualByComparingTo(testOrder.getFinalAmount());
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_DONE);
        Assertions.assertThat(orderPayment.getIsPrepayment()).isTrue();
    }

    private OrderDTO _02_fromCNToRU_declineTwoPath(boolean isPrepayMode) {
        saveSellerPickupCountryId(seller4mOtherCountryId, COUNTRY_CODE);
        commitAndStartNewTransaction();
        //
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(seller4mOtherCountryId)
                .maxItems(2)
                .build()
        );
        products.get(0).setCurrentPrice(BigDecimal.valueOf(10_000_00, 2));
        products.get(1).setCurrentPrice(BigDecimal.valueOf(100_000_00, 2));
        commitAndStartNewTransaction();
        //
        List<Long> productIds = products.stream().map(Product::getId).collect(Collectors.toList());
        OrderFlowTestUtils.TestConfig testConfig = OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(seller4mOtherCountryId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(seller4mOtherCountryCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .prepayBefOrderConfirm(isPrepayMode)
                .paymentsSchema(TcbBankService.SCHEMA)
                .productIdsList(productIds).itemsCount(1)

                .validateCart(it -> _XX_orderFlowDuties_validateCart(it, BigDecimal.ZERO))

                .confirmPositions(Collections.emptyList())
                .refusePositions(ImmutableList.of(1, 2))

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .orderPayAuthAmount(BigDecimal.valueOf(119_200_00, 2))

                .build();
        return orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, testConfig);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _02_authCapture_fromCNToRU_declineTwoPath() {
        OrderDTO testOrder = _02_fromCNToRU_declineTwoPath(false);
        //
        Order orderOnRefund = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.REFUND);
        Assertions.assertThat(orderOnRefund.getPrepaymentAmount()).isNull();
        Assertions.assertThat(orderOnRefund.getRefundReason()).isNotNull().satisfies(it ->
                Assertions.assertThat(it.getName()).isEqualTo(OrderRefundReasonType.ADMIN_ACTION)
        );
        //
        OrderPayment opRefund = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.REVERSE_DONE);
        Assertions.assertThat(opRefund.getIsPrepayment()).isFalse();
        Assertions.assertThat(opRefund.getAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(119_200_00, 2));
        Assertions.assertThat(opRefund.getCaptureAmountInBase()).isNull();
        Assertions.assertThat(opRefund.getRefundAmountInBase()).isNull();
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(), Lists.newArrayList(OperationType.HOLD, OperationType.HOLD_REVERSE));
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, 119_200_00);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_REVERSE, 119_200_00);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _02_captureRefunds_fromCNToRU_declineTwoPath() {
        OrderDTO testOrder = _02_fromCNToRU_declineTwoPath(true);
        //
        Order orderOnRefund = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.REFUND);
        Assertions.assertThat(orderOnRefund.getPrepaymentAmount()).isEqualByComparingTo(BigDecimal.valueOf(119_200_00, 2));
        Assertions.assertThat(orderOnRefund.getRefundReason()).isNotNull().satisfies(it ->
                Assertions.assertThat(it.getName()).isEqualTo(OrderRefundReasonType.ADMIN_ACTION)
        );
        OrderPayment opRefund = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.REFUND_DONE);
        Assertions.assertThat(opRefund.getIsPrepayment()).isTrue();
        Assertions.assertThat(opRefund.getAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(119_200_00, 2));
        Assertions.assertThat(opRefund.getCaptureAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(119_200_00, 2));
        Assertions.assertThat(opRefund.getRefundAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(119_200_00, 2));
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, 119_200_00);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_COMPLETE, 119_200_00);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.REFUND, 119_200_00);
    }

    private OrderDTO _03_fromCNToRU_rejectsTwoPath(boolean isPrepayMode) {
        saveSellerPickupCountryId(seller4mOtherCountryId, COUNTRY_CODE);
        commitAndStartNewTransaction();
        //
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(seller4mOtherCountryId)
                .maxItems(2)
                .build()
        );
        products.get(0).setCurrentPrice(BigDecimal.valueOf(10_000_00, 2));
        products.get(1).setCurrentPrice(BigDecimal.valueOf(100_000_00, 2));
        commitAndStartNewTransaction();
        //
        List<Long> productIds = products.stream().map(Product::getId).collect(Collectors.toList());
        OrderFlowTestUtils.TestConfig testConfig = OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(seller4mOtherCountryId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(seller4mOtherCountryCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .prepayBefOrderConfirm(isPrepayMode)
                .paymentsSchema(TcbBankService.SCHEMA)
                .productIdsList(productIds).itemsCount(1)

                .validateCart(it -> _XX_orderFlowDuties_validateCart(it, BigDecimal.ZERO))

                .confirmPositions(ImmutableList.of(1, 2))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(1, 2))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .orderPayAuthAmount(BigDecimal.valueOf(119_200_00, 2))

                .build();
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, testConfig);
        //
        Order orderOnRefund = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.REFUND);
        Assertions.assertThat(orderOnRefund.getRefundReason()).isNotNull().satisfies(it ->
                Assertions.assertThat(it.getName()).isEqualTo(OrderRefundReasonType.ADMIN_ACTION)
        );
        return testOrder;
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _03_authCapture_fromCNToRU_rejectsTwoPath() {
        OrderDTO testOrder = _03_fromCNToRU_rejectsTwoPath(false);
        //
        Order orderOnRefund = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.REFUND);
        Assertions.assertThat(orderOnRefund.getPrepaymentAmount()).isNull();
        Assertions.assertThat(orderOnRefund.getRefundReason()).isNotNull().satisfies(it ->
                Assertions.assertThat(it.getName()).isEqualTo(OrderRefundReasonType.ADMIN_ACTION)
        );
        //
        OrderPayment opRefund = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.REVERSE_DONE);
        Assertions.assertThat(opRefund.getIsPrepayment()).isFalse();
        Assertions.assertThat(opRefund.getAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(119_200_00, 2));
        Assertions.assertThat(opRefund.getCaptureAmountInBase()).isNull();
        Assertions.assertThat(opRefund.getRefundAmountInBase()).isNull();
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE));
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, 119_200_00);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_REVERSE, 119_200_00);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _03_captureRefunds_fromCNToRU_rejectsTwoPath() {
        OrderDTO testOrder = _03_fromCNToRU_rejectsTwoPath(true);
        //
        Order orderOnRefund = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.REFUND);
        Assertions.assertThat(orderOnRefund.getPrepaymentAmount()).isEqualByComparingTo(BigDecimal.valueOf(119_200_00, 2));
        Assertions.assertThat(orderOnRefund.getRefundReason()).isNotNull().satisfies(it ->
                Assertions.assertThat(it.getName()).isEqualTo(OrderRefundReasonType.ADMIN_ACTION)
        );
        //
        OrderPayment opRefund = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.REFUND_DONE);
        Assertions.assertThat(opRefund.getIsPrepayment()).isTrue();
        Assertions.assertThat(opRefund.getAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(119_200_00, 2));
        Assertions.assertThat(opRefund.getCaptureAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(119_200_00, 2));
        Assertions.assertThat(opRefund.getRefundAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(119_200_00, 2));
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, 119_200_00);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_COMPLETE, 119_200_00);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.REFUND, 119_200_00);
    }

    private OrderDTO _04_01_fromCNToRU_comboPath_oneToFive(boolean isPrepayMode) {
        saveSellerPickupCountryId(seller4mOtherCountryId, COUNTRY_CODE);
        commitAndStartNewTransaction();
        //
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(seller4mOtherCountryId)
                .sellerCounterpartyId(seller4mOtherCountryCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(5)
                .confirmPositions(ImmutableList.of(1, 2, 3, 4))
                .refusePositions(ImmutableList.of(5))

                .expertisePassPositions(ImmutableList.of(4))
                .expertiseFailPositions(ImmutableList.of(3))
                .defectsByPositions(Lists.newArrayList(null, 1234L, null, null, null))
                .cleaningsByPositions(Lists.newArrayList(4321L, null, null, null, null))

                .legalEntityOnecId(OrderFlowTestFixtures.OnecEntityType.ONEC_ENTITY_GROUP.getOnecUuid())

                .prepayBefOrderConfirm(isPrepayMode)
                .sellerPayoutAmount(46_945_00L)
                .noAgentReportCompare(true)

                .build());
        //
        return orderInfo;
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _04_01_authCapture_fromCNToRU_comboPath_oneToFive() {
        OrderDTO orderInfo = _04_01_fromCNToRU_comboPath_oneToFive(false);
        //
        Order order = orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.COMPLETED);
        Assertions.assertThat(order.getPrepaymentAmount()).isNull();
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(orderInfo.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_DONE);
        Assertions.assertThat(orderPayment.getIsPrepayment()).isFalse();
        Assertions.assertThat(orderPayment.getAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(154_100_00, 2));
        Assertions.assertThat(orderPayment.getCaptureAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(70_466_00, 2));
        Assertions.assertThat(orderPayment.getRefundAmountInBase()).isNull();
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 154_100_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 83_634_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 70_466_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 46_945_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_ADVANCE, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_ADVANCE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_PAYMENT, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_SERVICE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY));
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _04_01_captureRefunds_fromCNToRU_comboPath_oneToFive() {
        OrderDTO orderInfo = _04_01_fromCNToRU_comboPath_oneToFive(true);
        //
        Order order = orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.COMPLETED);
        Assertions.assertThat(order.getPrepaymentAmount()).isEqualByComparingTo(BigDecimal.valueOf(154_100_00, 2));
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(orderInfo.getId(), TcbBankService.SCHEMA, OrderPaymentState.REFUND_DONE);
        Assertions.assertThat(orderPayment.getIsPrepayment()).isTrue();
        Assertions.assertThat(orderPayment.getAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(154_100_00, 2));
        Assertions.assertThat(orderPayment.getCaptureAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(154_100_00, 2));
        Assertions.assertThat(orderPayment.getRefundAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(83_634_00, 2));
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.REFUND, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 154_100_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 154_100_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 83_634_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 46_945_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_ADVANCE, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_ADVANCE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_PAYMENT, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_SERVICE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY));
    }

    private OrderDTO _04_02_fromCNToRU_comboPath_fiveToOne(boolean isPrepayMode) {
        saveSellerPickupCountryId(seller4mOtherCountryId, COUNTRY_CODE);
        commitAndStartNewTransaction();
        //
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(seller4mOtherCountryId)
                .sellerCounterpartyId(seller4mOtherCountryCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(5)
                .confirmPositions(ImmutableList.of(2, 3, 4, 5))
                .refusePositions(ImmutableList.of(1))

                .expertisePassPositions(ImmutableList.of(2))
                .expertiseFailPositions(ImmutableList.of(3))
                .defectsByPositions(Lists.newArrayList(null, null, null, 1234L, null))
                .cleaningsByPositions(Lists.newArrayList(null, null, null, null, 4321L))

                .legalEntityOnecId(OrderFlowTestFixtures.OnecEntityType.ONEC_ENTITY_GROUP.getOnecUuid())

                .prepayBefOrderConfirm(isPrepayMode)
                .sellerPayoutAmount(76_945_00L)
                .noAgentReportCompare(true)

                .build());
        return orderInfo;
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _04_02_authCapture_fromCNToRU_comboPath_fiveToOne() {
        OrderDTO orderInfo = _04_02_fromCNToRU_comboPath_fiveToOne(false);
        //
        Order order = orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.COMPLETED);
        Assertions.assertThat(order.getPrepaymentAmount()).isNull();
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(orderInfo.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_DONE);
        Assertions.assertThat(orderPayment.getIsPrepayment()).isFalse();
        Assertions.assertThat(orderPayment.getAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(154_100_00, 2));
        Assertions.assertThat(orderPayment.getCaptureAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(112_791_00, 2));
        Assertions.assertThat(orderPayment.getRefundAmountInBase()).isNull();
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 154_100_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 41_309_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 112_791_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 76_945_00L);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_ADVANCE, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_ADVANCE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_PAYMENT, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_SERVICE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY));
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _04_02_captureRefunds_fromCNToRU_comboPath_fiveToOne() {
        OrderDTO orderInfo = _04_02_fromCNToRU_comboPath_fiveToOne(true);
        //
        Order order = orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.COMPLETED);
        Assertions.assertThat(order.getPrepaymentAmount()).isEqualByComparingTo(BigDecimal.valueOf(154_100_00, 2));
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(orderInfo.getId(), TcbBankService.SCHEMA, OrderPaymentState.REFUND_DONE);
        Assertions.assertThat(orderPayment.getIsPrepayment()).isTrue();
        Assertions.assertThat(orderPayment.getAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(154_100_00, 2));
        Assertions.assertThat(orderPayment.getCaptureAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(154_100_00, 2));
        Assertions.assertThat(orderPayment.getRefundAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(41_309_00, 2));
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.REFUND, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 154_100_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 154_100_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 41_309_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 76_945_00L);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_ADVANCE, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_ADVANCE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_PAYMENT, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_SERVICE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY));
    }

    private OrderDTO _05_fromCNToRU_defectTwoPath(boolean isPrepayMode) {
        saveSellerPickupCountryId(seller4mOtherCountryId, COUNTRY_CODE);
        commitAndStartNewTransaction();
        //
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(seller4mOtherCountryId)
                .maxItems(2)
                .build()
        );
        products.get(0).setCurrentPrice(BigDecimal.valueOf(10_000_00, 2));
        products.get(1).setCurrentPrice(BigDecimal.valueOf(100_000_00, 2));
        commitAndStartNewTransaction();
        //
        long authAmount = 10_000_00 + (100_000_00 + 8_700_00) + 500_00;
        //
        List<Long> productIds = products.stream().map(Product::getId).collect(Collectors.toList());
        OrderFlowTestUtils.TestConfig testConfig = OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(seller4mOtherCountryId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(seller4mOtherCountryCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .prepayBefOrderConfirm(isPrepayMode)
                .paymentsSchema(TcbBankService.SCHEMA)
                .productIdsList(productIds).itemsCount(2)

                .validateCart(it -> _XX_orderFlowDuties_validateCart(it, BigDecimal.ZERO))

                .confirmPositions(ImmutableList.of(1, 2))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(ImmutableList.of(1_000L, 10_000L))
                .cleaningsByPositions(Collections.emptyList())

                .orderPayAuthAmount(BigDecimal.valueOf(authAmount, 2))
                .noAgentReportCompare(true)
                .sellerPayoutAmount(76_500_00L)

                .build();
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, testConfig);
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        //
        return testOrder;
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _05_authCapture_fromCNToRU_defectTwoPath() {
        OrderDTO testOrder = _05_fromCNToRU_defectTwoPath(false);
        //
        Order order = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        Assertions.assertThat(order.getPrepaymentAmount()).isNull();
        //
        OrderPayment opRefund = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_DONE);
        Assertions.assertThat(opRefund.getIsPrepayment()).isFalse();
        Assertions.assertThat(opRefund.getAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(119_200_00, 2));
        Assertions.assertThat(opRefund.getCaptureAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(108_200_00, 2));
        Assertions.assertThat(opRefund.getRefundAmountInBase()).isNull();
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, BigDecimal.valueOf(119_200_00, 2));
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_REVERSE, BigDecimal.valueOf(11_000_00, 2));
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_COMPLETE, BigDecimal.valueOf(108_200_00, 2));
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.SELLER_PAYOUT, BigDecimal.valueOf(76_500_00L, 2));
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _05_captureRefunds_fromCNToRU_defectTwoPath() {
        OrderDTO testOrder = _05_fromCNToRU_defectTwoPath(true);
        //
        Order order = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        Assertions.assertThat(order.getPrepaymentAmount()).isEqualByComparingTo(BigDecimal.valueOf(119_200_00, 2));
        //
        OrderPayment opRefund = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.REFUND_DONE);
        Assertions.assertThat(opRefund.getIsPrepayment()).isTrue();
        Assertions.assertThat(opRefund.getAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(119_200_00, 2));
        Assertions.assertThat(opRefund.getCaptureAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(119_200_00, 2));
        Assertions.assertThat(opRefund.getRefundAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(11_000_00, 2));
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.REFUND, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, BigDecimal.valueOf(119_200_00, 2));
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_COMPLETE, BigDecimal.valueOf(119_200_00, 2));
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.REFUND, BigDecimal.valueOf(11_000_00, 2));
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.SELLER_PAYOUT, BigDecimal.valueOf(76_500_00L, 2));
    }

    public OrderDTO _06_fromCNToRU_cleansTwoPath(boolean isPrepayMode) {
        saveSellerPickupCountryId(seller4mOtherCountryId, COUNTRY_CODE);
        commitAndStartNewTransaction();
        //
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(seller4mOtherCountryId)
                .maxItems(2)
                .build()
        );
        products.get(0).setCurrentPrice(BigDecimal.valueOf(10_000_00, 2));
        products.get(1).setCurrentPrice(BigDecimal.valueOf(100_000_00, 2));
        commitAndStartNewTransaction();
        //
        long authAmount = (10_000_00) + (100_000_00 + 8_700_00) + 500_00;
        //
        List<Long> productIds = products.stream().map(Product::getId).collect(Collectors.toList());
        OrderFlowTestUtils.TestConfig testConfig = OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(seller4mOtherCountryId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(seller4mOtherCountryCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .prepayBefOrderConfirm(isPrepayMode)
                .paymentsSchema(TcbBankService.SCHEMA)
                .productIdsList(productIds).itemsCount(2)

                .validateCart(it -> _XX_orderFlowDuties_validateCart(it, BigDecimal.ZERO))

                .confirmPositions(ImmutableList.of(1, 2))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(ImmutableList.of(1_000L, 10_000L))

                .orderPayAuthAmount(BigDecimal.valueOf(authAmount, 2))
                .noAgentReportCompare(true)
                .sellerPayoutAmount(76_500_00L)

                .build();
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, testConfig);
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        //
        return testOrder;
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _06_authCapture_fromCNToRU_cleansTwoPath() {
        OrderDTO testOrder = _06_fromCNToRU_cleansTwoPath(false);
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        //
        OrderPayment opRefund = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_DONE);
        Assertions.assertThat(opRefund.getIsPrepayment()).isFalse();
        Assertions.assertThat(opRefund.getAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(119_200_00, 2));
        Assertions.assertThat(opRefund.getCaptureAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(119_200_00, 2));
        Assertions.assertThat(opRefund.getRefundAmountInBase()).isNull();
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, 119_200_00);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_COMPLETE, 119_200_00);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.SELLER_PAYOUT, 76_500_00);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _06_captureRefunds_fromCNToRU_cleansTwoPath() {
        OrderDTO testOrder = _06_fromCNToRU_cleansTwoPath(true);
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        //
        OrderPayment opRefund = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_DONE);
        Assertions.assertThat(opRefund.getIsPrepayment()).isTrue();
        Assertions.assertThat(opRefund.getAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(119_200_00, 2));
        Assertions.assertThat(opRefund.getCaptureAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(119_200_00, 2));
        Assertions.assertThat(opRefund.getRefundAmountInBase()).isNull();
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, 119_200_00);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_COMPLETE, 119_200_00);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.SELLER_PAYOUT, 76_500_00);
    }

    private OrderDTO _07_fromCNToRU_defectWithCleansTwoPath(boolean isPrepayMode) {
        saveSellerPickupCountryId(seller4mOtherCountryId, COUNTRY_CODE);
        commitAndStartNewTransaction();
        //
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(seller4mOtherCountryId)
                .maxItems(2)
                .build()
        );
        products.get(0).setCurrentPrice(BigDecimal.valueOf(10_000_00, 2));
        products.get(1).setCurrentPrice(BigDecimal.valueOf(100_000_00, 2));
        commitAndStartNewTransaction();
        //
        long authAmount = (10_000_00) + (100_000_00 + 8_700_00) + 500_00;
        //
        List<Long> productIds = products.stream().map(Product::getId).collect(Collectors.toList());
        OrderFlowTestUtils.TestConfig testConfig = OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(seller4mOtherCountryId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(seller4mOtherCountryCounterpartyId)
                .targetDeliveryAepId(deliveryId)

                .prepayBefOrderConfirm(isPrepayMode)
                .paymentsSchema(TcbBankService.SCHEMA)
                .productIdsList(productIds).itemsCount(2)

                .validateCart(it -> _XX_orderFlowDuties_validateCart(it, BigDecimal.ZERO))

                .confirmPositions(ImmutableList.of(1, 2))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(ImmutableList.of(1_000L, 10_000L))
                .cleaningsByPositions(ImmutableList.of(1_000L, 10_000L))
                .comboExpertiseMode(OrderFlowTestUtils.ComboExpertiseMode.DEFECTS_WITH_SERVICE)

                .orderPayAuthAmount(BigDecimal.valueOf(authAmount, 2))
                .noAgentReportCompare(true)
                .sellerPayoutAmount(65_500_00L)

                .build();
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, testConfig);
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        //
        return testOrder;
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _07_authCapture_fromCNToRU_defectWithCleansTwoPath() {
        OrderDTO testOrder = _07_fromCNToRU_defectWithCleansTwoPath(false);
        //
        Order order = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        Assertions.assertThat(order.getPrepaymentAmount()).isNull();
        //
        OrderPayment opRefund = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_DONE);
        Assertions.assertThat(opRefund.getAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(119_200_00, 2));
        Assertions.assertThat(opRefund.getCaptureAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(108_200_00, 2));
        Assertions.assertThat(opRefund.getRefundAmountInBase()).isNull();
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, 119_200_00);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_REVERSE, 11_000_00);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_COMPLETE, 108_200_00);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.SELLER_PAYOUT, 65_500_00L);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _07_captureRefunds_fromCNToRU_defectWithCleansTwoPath() {
        OrderDTO testOrder = _07_fromCNToRU_defectWithCleansTwoPath(true);
        //
        Order order = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        Assertions.assertThat(order.getPrepaymentAmount()).isEqualByComparingTo(BigDecimal.valueOf(119_200_00, 2));
        //
        OrderPayment opRefund = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.REFUND_DONE);
        Assertions.assertThat(opRefund.getAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(119_200_00, 2));
        Assertions.assertThat(opRefund.getCaptureAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(119_200_00, 2));
        Assertions.assertThat(opRefund.getRefundAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(11_000_00, 2));
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.REFUND, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, 119_200_00);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_COMPLETE, 119_200_00);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.REFUND, 11_000_00);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.SELLER_PAYOUT, 65_500_00L);
    }

}
