package ru.oskelly.tests.pr.suite6_1.orderflow;

import best2pay.xsd.Order;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import lombok.SneakyThrows;
import org.mockserver.client.MockServerClient;
import org.mockserver.integration.ClientAndServer;
import org.mockserver.mock.action.ExpectationResponseCallback;
import org.mockserver.model.Header;
import org.mockserver.model.HttpRequest;
import org.mockserver.model.HttpResponse;

import static org.mockserver.model.HttpRequest.request;
import static org.mockserver.model.HttpResponse.response;
import static org.mockserver.model.HttpStatusCode.NOT_FOUND_404;
import static org.mockserver.model.HttpStatusCode.OK_200;

public class OrderFlowTestB2PMock {

    private ClientAndServer clientAndServer;

    public OrderFlowTestB2PMock(String mockHost, int mockPort) {
        clientAndServer = ClientAndServer.startClientAndServer(mockPort);
        new MockServerClient(mockHost, mockPort)
                .when(request().withMethod("POST"))
                .respond(new MockB2PServer());
    }

    public void stop() {
        clientAndServer.stop();
    }

    public static class MockB2PServer implements ExpectationResponseCallback {

        @Override
        @SneakyThrows
        public HttpResponse handle(HttpRequest httpRequest) {
            XmlMapper objectMapper = new XmlMapper();
            objectMapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
            if ("/webapi/Register".equals(httpRequest.getPath().getValue())) {
                Order order = new Order();
                order.setId(1);
                return response().withStatusCode(OK_200.code())
                        .withHeaders(new Header("Content-Type", "application/xml;charset=UTF-8"))
                        .withBody(objectMapper.writeValueAsString(order));
            }
            return response().withStatusCode(NOT_FOUND_404.code());
        }
    }

}
