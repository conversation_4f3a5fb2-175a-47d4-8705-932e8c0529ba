package ru.oskelly.tests.pr.suite6_1.orderflow;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.SneakyThrows;
import org.mockserver.client.MockServerClient;
import org.mockserver.integration.ClientAndServer;
import org.mockserver.mock.action.ExpectationResponseCallback;
import org.mockserver.model.Header;
import org.mockserver.model.HttpRequest;
import org.mockserver.model.HttpResponse;
import org.springframework.web.util.UriComponentsBuilder;
import su.reddot.infrastructure.bank.payments.noon.client.NoonBankClient;
import su.reddot.infrastructure.bank.payments.noon.client.common.OrderStatus;
import su.reddot.infrastructure.bank.payments.noon.client.request.OrderActionRequest;
import su.reddot.infrastructure.bank.payments.noon.client.response.OrderActionResponse;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static org.mockserver.model.HttpRequest.request;
import static org.mockserver.model.HttpResponse.response;
import static org.mockserver.model.HttpStatusCode.NOT_FOUND_404;
import static org.mockserver.model.HttpStatusCode.OK_200;

public class OrderFlowTestNoonMock {

    private final ClientAndServer clientAndServer;

    public OrderFlowTestNoonMock(String mockHost, int mockPort) {
        clientAndServer = ClientAndServer.startClientAndServer(mockPort);
        new MockServerClient(mockHost, mockPort)
                .when(request())
                .respond(new MockTcbServer());
    }

    public void stop() {
        clientAndServer.stop();
    }

    public static class MockTcbServer implements ExpectationResponseCallback {

        private final HashMap<String, HashMap<String, String>> operations = new HashMap<>();

        private static final String PROP_NAME_ROUTE = "route";
        private static final String PROP_NAME_CURRENCY = "currency";
        private static final String PROP_TOTAL_AUTHORIZED_AMOUNT = "totalAuthorizedAmount";
        private static final String PROP_TOTAL_CAPTURED_AMOUNT = "totalCapturedAmount";
        private static final String PROP_NAME_ORD_ID = "orderId";
        private static final String PROP_NAME_ORD_UUID = "orderUuid";
        private static final String PROP_NAME_STATE_ID = "stateId";

        private final ObjectMapper objectMapper = new ObjectMapper()
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS, true)
                .configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true)
                .registerModule(new JavaTimeModule());

        private void setOperationProperty(String operationId, String propertyName, String propertyData) {
            HashMap<String, String> operationProperties = operations.get(operationId);
            if (Objects.isNull(operationProperties)) {
                operations.put(operationId, new HashMap<>());
            }
            operations.get(operationId).put(propertyName, propertyData);
        }

        private Optional<String> getOperationProperty(String operationId, String propertyName) {
            HashMap<String, String> operationProperties = operations.get(operationId);
            if (Objects.isNull(operationProperties)) {
                return Optional.empty();
            }
            return Optional.ofNullable(operationProperties.get(propertyName));
        }

        private String getOrderUUID(long orderId) {
            HashMap<String, String> order = operations.values().stream()
                    .filter(props -> String.valueOf(orderId).equals(props.get(PROP_NAME_ORD_ID)))
                    .findFirst()
                    .orElseThrow(() -> new IllegalArgumentException("Unable to find order with id " + orderId));
            return order.get(PROP_NAME_ORD_UUID);
        }

        private long getNextOperationOrderNumber() {
            return 220925000 + operations.size();
        }

        private String getHttpRequestJsonText(HttpRequest httpRequest) {
            return new String(httpRequest.getBodyAsRawBytes(), StandardCharsets.UTF_8);
        }

        String rspInitiate = "{\"resultCode\":0,\"message\":\"Processed successfully\",\"resultClass\":0,\"classDescription\":\"\",\"actionHint\":\"\",\"requestReference\":\"c5f29ac1-5758-43f0-9861-f80ea2641715\",\"result\":{\"nextActions\":\"ADD_PAYMENT_INFO\",\"transaction\":{\"type\":\"CAPTURE\",\"creationTime\":\"2023-01-18T10:04:25.27Z\",\"status\":\"SUCCESS\",\"amountRefunded\":0,\"id\":\"6740362655626562804953\",\"amount\":254.23,\"currency\":\"EUR\",\"transactionReference\":\"08682f01-0284-4edc-8749-652f7286b61e\",\"targetTransactionId\":\"6740362050666373604953\"},\"order\":{\"status\":\"INITIATED\",\"creationTime\":\"2023-02-02T12:18:59.38706Z\",\"errorCode\":0,\"id\":779062454307,\"amount\":15500,\"currency\":\"AED\",\"name\":\"1120025\",\"description\":\"Payment for order 1120025\",\"reference\":\"5da4fa5b-5c64-4c83-91b2-e20b78195099\",\"category\":\"pay\",\"channel\":\"Web\"},\"configuration\":{\"tokenizeCc\":false,\"returnUrl\":\"https://international.oskelly.tech/api/v2/acquirers/noonpayments-1.0/verify-payment?redirectTo=https%3A%2F%2Finternational.oskelly.tech%2Forders%2F1120025&operationId=5da4fa5b-5c64-4c83-91b2-e20b78195099\",\"locale\":\"en\",\"initiationValidity\":\"2023-02-02T12:23:58Z\",\"paymentAction\":\"Authorize\"},\"business\":{\"id\":\"oskelly\",\"name\":\"OSKELLY TRADING L.L.C\"},\"checkoutData\":{\"postUrl\":\"https://checkout-stg.noonpayments.com/en/default/index?info=aQtbeIp1McHQsW5v2KQBiELNjVbCHkjGRlG5leupifknw7oexhHQtPTxmVljgQ%3D%3D\",\"jsUrl\":\"https://checkout-stg.noonpayments.com/en/scripts/checkout?url=https%3A%2F%2Fcheckout-stg.noonpayments.com%2Fen%2Fdefault%2Findex%3Finfo%3DaQtbeIp1McHQsW5v2KQBiELNjVbCHkjGRlG5leupifknw7oexhHQtPTxmVljgQ%253D%253D\"},\"deviceFingerPrint\":{\"sessionId\":\"779062454307\"},\"paymentOptions\":[{\"method\":\"CARD_SANDBOX\",\"type\":\"Card\",\"action\":\"Card\",\"data\":{\"supportedCardBrands\":[\"VISA\",\"MASTERCARD\"],\"cvvRequired\":\"True\"}},{\"method\":\"ApplePay_Sandbox\",\"type\":\"ApplePay\",\"action\":\"ApplePay\",\"data\":{\"merchantIdentifier\":\"merchant.com.noonpayments.stg-checkout\",\"paymentRequest\":{\"countryCode\":\"AE\",\"currencyCode\":\"AED\",\"total\":{\"label\":\"OSKELLY TRADING L.L.C\",\"amount\":15500},\"supportedNetworks\":[\"visa\",\"masterCard\"],\"merchantCapabilities\":[\"supports3DS\"]}}},{\"method\":\"OSKELLY_SAMSUNGPAY_SANDBOX\",\"type\":\"SamsungPay\",\"action\":\"SamsungPay\"},{\"method\":\"OSKELLY_GOOGLEPAY_SANDBOX\",\"type\":\"GooglePay\",\"action\":\"GooglePay\",\"data\":{\"apiVersion\":2,\"apiVersionMinor\":0,\"allowedPaymentMethods\":[{\"type\":\"CARD\",\"parameters\":{\"allowedAuthMethods\":[\"PAN_ONLY\",\"CRYPTOGRAM_3DS\"],\"allowedCardNetworks\":[\"VISA\",\"MASTERCARD\"]},\"tokenizationSpecification\":{\"type\":\"PAYMENT_GATEWAY\",\"parameters\":{\"gateway\":\"noonpayments\",\"gatewayMerchantId\":\"oskelly\"}}}],\"transactionInfo\":{\"countryCode\":\"AE\",\"currencyCode\":\"AED\",\"totalPriceStatus\":\"FINAL\",\"totalPrice\":\"15500.00\"},\"merchantInfo\":{\"merchantId\":\"111222333444555666\",\"merchantName\":\"noonpayments\",\"merchantOrigin\":\"checkout.noonpayments.com\"},\"environment\":\"TEST\"}},{\"method\":\"VisaCheckout_Sandbox\",\"type\":\"VisaCheckout\",\"action\":\"VisaCheckout\",\"data\":{\"initializationData\":{\"apikey\":\"89X84EEL8CGNH69LNKBT21tZFdp46mMRMfZq_XHfyzPZyJFDk\",\"encryptionKey\":\"NEW3X8HXFI9SSACCU22R13IRxaI7DjmM4Ik1AwpJmjFPlw40A\",\"externalClientId\":\"89ddacaa-6f51-44ff-be12-701b1bc37cdf\",\"paymentRequest\":{\"currencyCode\":\"AED\",\"subtotal\":\"15500.00\",\"total\":\"15500.00\",\"orderId\":\"779062454307\",\"description\":\"1120025\"},\"settings\":{\"locale\":\"en_AE\",\"countryCode\":\"AE\",\"displayName\":\"OSKELLY TRADING L.L.C\",\"websiteUrl\":\"https://oskelly.com\",\"datalevel\":\"FULL\",\"shipping\":{\"collectShipping\":false},\"payment\":{\"cardBrands\":[\"VISA\"]},\"threeDSSetup\":{\"threeDSActive\":false,\"threeDSSuppressChallenge\":false},\"review\":{\"buttonAction\":\"Pay\"}}},\"scriptUrl\":\"https://sandbox-assets.secure.checkout.visa.com/checkout-widget/resources/js/integration/v1/sdk.js\",\"buttonUrl\":\"https://sandbox.secure.checkout.visa.com/wallet-services-web/xo/button.png?cardBrands=VISA\"}}]}}";

        @SneakyThrows
        private HttpResponse orderActionCaptures(HttpRequest httpRequest, OrderActionRequest orderActionRequest) {
            String uuid = getOrderUUID(orderActionRequest.getOrder().getId());
            //
            JsonNode initiateResponseJsonTemplate = objectMapper.readTree(rspInitiate);
            //
            OrderActionResponse orderActionResponse = objectMapper.readValue(initiateResponseJsonTemplate.toString(), OrderActionResponse.class);
            //
            setOperationProperty(uuid, PROP_NAME_STATE_ID, OrderStatus.CAPTURED.toString());
            setOperationProperty(uuid, PROP_TOTAL_CAPTURED_AMOUNT, orderActionRequest.getTransaction().getAmount().toString());
            //
            return response().withStatusCode(OK_200.code())
                    .withHeaders(new Header("Content-Type", "application/json;charset=UTF-8"))
                    .withBody(objectMapper.writeValueAsString(orderActionResponse));
        }

        @SneakyThrows
        private HttpResponse orderActionInitiate(HttpRequest httpRequest, OrderActionRequest orderActionRequest) {
            String uuid = orderActionRequest.getOrder().getReference();
            //
            String payUrl = UriComponentsBuilder.fromUriString(orderActionRequest.getConfiguration().getReturnUrl())
                    .scheme("https")
                    .build().toString();
            //
            JsonNode initiateResponseJsonTemplate = objectMapper.readTree(rspInitiate);
            ((ObjectNode) initiateResponseJsonTemplate.get("result").get("order")).put("id", getNextOperationOrderNumber());
            ((ObjectNode) initiateResponseJsonTemplate.get("result").get("order")).put("reference", uuid);
            ((ObjectNode) initiateResponseJsonTemplate.get("result").get("checkoutData")).put("postUrl", payUrl);
            //
            OrderActionResponse orderActionResponse = objectMapper.readValue(initiateResponseJsonTemplate.toString(), OrderActionResponse.class);
            //
            setOperationProperty(uuid, PROP_NAME_STATE_ID, OrderStatus.AUTHORIZED.toString());
            setOperationProperty(uuid, PROP_NAME_ROUTE, httpRequest.getPath().getValue());
            setOperationProperty(uuid, PROP_NAME_ORD_ID, orderActionResponse.getResult().getOrder().getId().toString());
            setOperationProperty(uuid, PROP_NAME_ORD_UUID, uuid);
            setOperationProperty(uuid, PROP_NAME_CURRENCY, orderActionRequest.getOrder().getCurrency());
            setOperationProperty(uuid, PROP_TOTAL_AUTHORIZED_AMOUNT, orderActionRequest.getOrder().getAmount().toString());
            //
            return response().withStatusCode(OK_200.code())
                    .withHeaders(new Header("Content-Type", "application/json;charset=UTF-8"))
                    .withBody(objectMapper.writeValueAsString(orderActionResponse));
        }

        @SneakyThrows
        private HttpResponse orderInfo(HttpRequest httpRequest) {
            String orderInfoTemplate = "{\"resultCode\":0,\"message\":\"Processed successfully\",\"resultClass\":0,\"classDescription\":\"\",\"actionHint\":\"\",\"requestReference\":\"557e6b5d-eb2c-4e4d-ba90-7231ea5c22d7\",\"result\":{\"nextActions\":\"CAPTURE,REFUND\",\"transactions\":[{\"type\":\"AUTHORIZATION\",\"authorizationCode\":\"831000\",\"creationTime\":\"2023-01-18T10:03:24.7366667Z\",\"status\":\"SUCCESS\",\"stan\":\"129930\",\"rrn\":\"************\",\"id\":\"6740362050666373604953\",\"amount\":271.64,\"currency\":\"EUR\"},{\"type\":\"CAPTURE\",\"creationTime\":\"2023-01-18T10:04:25.27Z\",\"status\":\"SUCCESS\",\"amountRefunded\":0,\"id\":\"6740362655626562804953\",\"amount\":254.23,\"currency\":\"EUR\",\"transactionReference\":\"08682f01-0284-4edc-8749-652f7286b61e\",\"targetTransactionId\":\"6740362050666373604953\"}],\"order\":{\"status\":\"PARTIALLY_CAPTURED\",\"creationTime\":\"2023-01-18T10:03:11.717Z\",\"totalAuthorizedAmount\":0,\"totalCapturedAmount\":0,\"totalRefundedAmount\":0,\"totalRemainingAmount\":0,\"totalReversedAmount\":0,\"totalSalesAmount\":0,\"errorCode\":0,\"id\":************,\"amount\":0,\"currency\":\"EUR\",\"name\":\"1121876\",\"description\":\"Payment for order 1121876\",\"reference\":\"7b52bf1b-c8aa-4f20-a284-76c8ee656255\",\"category\":\"pay\",\"channel\":\"Web\"},\"paymentDetails\":{\"instrument\":\"CARD\",\"mode\":\"Card\",\"integratorAccount\":\"CARD_SANDBOX\",\"paymentInfo\":\"445653xxxxxx1005\",\"brand\":\"VISA\",\"scheme\":\"VISA\",\"expiryMonth\":\"11\",\"expiryYear\":\"2025\",\"isNetworkToken\":\"FALSE\",\"cardType\":\"CREDIT\",\"cardCategory\":\"TEST CARD\",\"cardCountry\":\"US\",\"cardCountryName\":\"United States of America\"}}}";
            //
            List<String> pathPart = Arrays.asList(httpRequest.getPath().getValue().split("/"));
            Collections.reverse(pathPart);
            String orderReference = pathPart.get(0);
            String orderDBIDValue = getOperationProperty(orderReference, PROP_NAME_ORD_ID).get();
            //
            JsonNode initiateResponseJsonTemplate = objectMapper.readTree(orderInfoTemplate);
            ((ObjectNode) initiateResponseJsonTemplate.get("result").get("order")).put("id", orderDBIDValue);
            ((ObjectNode) initiateResponseJsonTemplate.get("result").get("order")).put("reference", orderReference);
            getOperationProperty(orderReference, PROP_NAME_STATE_ID).ifPresent(it -> {
                ((ObjectNode) initiateResponseJsonTemplate.get("result").get("order")).put("status", it);
            });
            getOperationProperty(orderReference, PROP_NAME_CURRENCY).ifPresent(it -> {
                ((ObjectNode) initiateResponseJsonTemplate.get("result").get("order")).put("currency", it);
            });
            getOperationProperty(orderReference, PROP_TOTAL_AUTHORIZED_AMOUNT).ifPresent(it -> {
                ((ObjectNode) initiateResponseJsonTemplate.get("result").get("order")).put("totalAuthorizedAmount", it);
            });
            getOperationProperty(orderReference, PROP_TOTAL_CAPTURED_AMOUNT).ifPresent(it -> {
                ((ObjectNode) initiateResponseJsonTemplate.get("result").get("order")).put("totalCapturedAmount", it);
            });
            OrderActionResponse orderActionResponse = objectMapper.readValue(initiateResponseJsonTemplate.toString(), OrderActionResponse.class);
            //
            return response().withStatusCode(OK_200.code())
                    .withHeaders(new Header("Content-Type", "application/json;charset=UTF-8"))
                    .withBody(objectMapper.writeValueAsString(orderActionResponse));
        }

        @Override
        @SneakyThrows
        public HttpResponse handle(HttpRequest httpRequest) {
            if (("/payment/v1" + NoonBankClient.ROUTE_PAYMENT_V1_ORDER_ACTION).equals(httpRequest.getPath().getValue())) {
                if (httpRequest.getMethod().getValue().equals("POST")) {
                    OrderActionRequest orderActionRequest = objectMapper.readValue(getHttpRequestJsonText(httpRequest), OrderActionRequest.class);
                    switch (orderActionRequest.getApiOperation()) {
                        case INITIATE:
                            return orderActionInitiate(httpRequest, orderActionRequest);
                        case CAPTURE:
                            return orderActionCaptures(httpRequest, orderActionRequest);
                    }
                }
            }
            if (httpRequest.getPath().getValue().startsWith("/payment/v1" + NoonBankClient.ROUTE_PAYMENT_V1_ORDER_BY_REF)) {
                return orderInfo(httpRequest);
            }
            return response().withStatusCode(NOT_FOUND_404.code());
        }
    }

}
