package ru.oskelly.tests.pr.suite6_1.orderflow;

import org.assertj.core.api.Assertions;
import org.assertj.core.data.Index;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.pr.common.bonuses.BonusesServiceTestConfiguration;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.component.HoldRequest;
import su.reddot.domain.dao.user.SaleShipmentRouteRepository;
import su.reddot.domain.model.banktransaction.BankOperation;
import su.reddot.domain.model.banktransaction.order.OrderBankOperation;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.user.SaleShipmentRoute;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.payment.impl.card.CardPaymentOptionProvider;
import su.reddot.domain.service.payment.impl.noonapplepay.NoonApplePayOptionProvider;
import su.reddot.domain.service.payment.impl.nooncard.NoonCardOptionProvider;
import su.reddot.domain.service.payment.impl.tabby.TabbySplitSettings;
import su.reddot.domain.service.payment.impl.yandexpay.YandexPayOptionProvider;
import su.reddot.domain.service.payment.impl.yandexsplit.YandexSplitOptionProvider;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.bank.payments.noon.NoonBankService;
import su.reddot.infrastructure.bank.payments.oskelly.PaymentsServiceBankService;
import su.reddot.infrastructure.util.CallInTransaction;

import javax.annotation.PostConstruct;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@TestMethodOrder(MethodOrderer.MethodName.class)
@ContextConfiguration(classes = {OrderFlowTestUtils.class, BonusesServiceTestConfiguration.class})
@Layer
@Disabled("This test requires PaymentsService: turn back with test containers")
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
public class OrderFlowHoldWithPayOptionTest extends AbstractSpringTest {

    @Autowired
    private UserService userService;
    @Autowired
    private SaleShipmentRouteRepository saleShipmentRouteRepository;

    @Autowired
    private CallInTransaction callInTransaction;

    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private CartTestSupport cartTestSupport;

    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user-password}")
    private String password;

    @Value("${test-prepayments.usual-seller-id}")
    private Long usualSellerId;
    @Value("${test-prepayments.cbord-seller-id}")
    private Long cbordSellerId;

    @Value("${test.receipts.mock-server-host}")
    private String mockServerHost;
    @Value("${test.receipts.mock-server-tcb-bank-port}")
    private Integer mockTcbServerPort;
    @Value("${test.receipts.mock-server-b2p-bank-port}")
    private Integer mockB2PServerPort;
    @Value("${test.receipts.mock-server-non-bank-port}")
    private Integer mockNonServerPort;

    private static OrderFlowTestTcbMock orderFlowTestTcbMock;
    private static OrderFlowTestB2PMock orderFlowTestB2PMock;
    private static OrderFlowTestNoonMock orderFlowTestNonMock;

    private Long prepareAdminUser() {
        User adminUser = userService.getUserByEmail(buyerEmail);
        orderFlowTestUtils.enableUserAuthority(adminUser.getId(), AuthorityName.ORDER_PREPAYMENTS, true);
        orderFlowTestUtils.enableUserAuthority(adminUser.getId(), AuthorityName.ORDER_MANUAL_CHANGE_DELIVERY_STATE, true);
        return adminUser.getId();
    }

    @PostConstruct
    private void init() {
        orderFlowTestUtils.init(buyerEmail, password);
        User buyer = userService.getUserByEmail(buyerEmail);
        ApiV2Client apiV2Client = new ApiV2Client(buyerEmail, password);
        cartTestSupport.setUserId(buyer.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
        orderFlowTestTcbMock = Objects.isNull(orderFlowTestTcbMock) ? new OrderFlowTestTcbMock(mockServerHost, mockTcbServerPort) : orderFlowTestTcbMock;
        orderFlowTestB2PMock = Objects.isNull(orderFlowTestB2PMock) ? new OrderFlowTestB2PMock(mockServerHost, mockB2PServerPort) : orderFlowTestB2PMock;
        orderFlowTestNonMock = Objects.isNull(orderFlowTestNonMock) ? new OrderFlowTestNoonMock(mockServerHost, mockNonServerPort) : orderFlowTestNonMock;
        callInTransaction.runInNewTransaction(this::prepareAdminUser);
    }

    @AfterAll
    public static void done() {
        orderFlowTestTcbMock.stop();
        orderFlowTestB2PMock.stop();
        orderFlowTestNonMock.stop();
    }

    @Test
    @Transactional
    public void payOption_card() {
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .itemsCount(1)
                .payOptionsType(CardPaymentOptionProvider.TYPE)
                .stopPosition(OrderFlowTestUtils.TestStopPosition.HOLD_PROCESSING)
                .build());
        Assertions.assertThat(orderInfo.getPayment().getPaymentVersion()).isEqualTo(TcbBankService.SCHEMA);
        Assertions.assertThat(orderInfo.getPayment().getPaymentMethod()).isNull();
    }

    @Test
    @Transactional
    public void payOption_yandexPay() {
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .itemsCount(1)
                .payOptionsType(YandexPayOptionProvider.TYPE)
                .stopPosition(OrderFlowTestUtils.TestStopPosition.HOLD_PROCESSING)
                .build());
        Assertions.assertThat(orderInfo.getPayment().getPaymentVersion()).isEqualTo(PaymentsServiceBankService.PAYMENT_SCHEME_YANDEX_PAY);
        Assertions.assertThat(orderInfo.getPayment().getPaymentMethod()).isEqualTo("pay");
    }

    @Test
    @Transactional
    public void payOption_yandexSplit() {
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .itemsCount(1)
                .payOptionsType(YandexSplitOptionProvider.TYPE)
                .stopPosition(OrderFlowTestUtils.TestStopPosition.HOLD_PROCESSING)
                .build());
        Assertions.assertThat(orderInfo.getPayment().getPaymentVersion()).isEqualTo(PaymentsServiceBankService.PAYMENT_SCHEME_YANDEX_PAY);
        Assertions.assertThat(orderInfo.getPayment().getPaymentMethod()).isEqualTo("split");
    }

    @Test
    @Transactional
    public void payOption_tabby() {
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .itemsCount(1)
                .payOptionsType(TabbySplitSettings.TYPE)
                .payCurrencyISO("AED")
                .stopPosition(OrderFlowTestUtils.TestStopPosition.HOLD_PROCESSING)
                .build());
        Assertions.assertThat(orderInfo.getPayment().getPaymentVersion()).isEqualTo(PaymentsServiceBankService.PAYMENT_SCHEME_YANDEX_PAY);
        Assertions.assertThat(orderInfo.getPayment().getPaymentMethod()).isEqualTo(PaymentsServiceBankService.PAYMENT_SCHEME_YANDEX_PAY);
    }

    @Test
    @Transactional
    public void payOption_plutusTwoStepPay() {
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .itemsCount(1)
                .payOptionsType("PLUTUS_TWO_STEP_PAY")
                .stopPosition(OrderFlowTestUtils.TestStopPosition.HOLD_PROCESSING)
                .build());
        Assertions.assertThat(orderInfo.getPayment().getPaymentVersion()).isEqualTo(PaymentsServiceBankService.PAYMENT_SCHEME_PLUTUS);
        Assertions.assertThat(orderInfo.getPayment().getPaymentMethod()).isEqualTo("two-step-pay");
    }

    @Test
    @Transactional
    public void payOption_plutusAutoCapture() {
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .itemsCount(1)
                .payOptionsType("PLUTUS_AUTO_CAPTURE")
                .stopPosition(OrderFlowTestUtils.TestStopPosition.HOLD_PROCESSING)
                .build());
        Assertions.assertThat(orderInfo.getPayment().getPaymentVersion()).isEqualTo(PaymentsServiceBankService.PAYMENT_SCHEME_PLUTUS);
        Assertions.assertThat(orderInfo.getPayment().getPaymentMethod()).isEqualTo("auto-capture");
    }

    @Test
    @Transactional
    public void payOption_noon() {
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .itemsCount(1)
                .payOptionsType("NOON")
                .stopPosition(OrderFlowTestUtils.TestStopPosition.HOLD_PROCESSING)
                .build());
        Assertions.assertThat(orderInfo.getPayment().getPaymentVersion()).isEqualTo(NoonBankService.NOON_SCHEMA);
        Assertions.assertThat(orderInfo.getPayment().getPaymentMethod()).isNull();
    }

    @Test
    @Transactional
    public void payOption_noonApplePay() {
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .itemsCount(1)
                .payOptionsType(NoonApplePayOptionProvider.TYPE)
                .stopPosition(OrderFlowTestUtils.TestStopPosition.HOLD_PROCESSING)
                .build());
        Assertions.assertThat(orderInfo.getPayment().getPaymentVersion()).isEqualTo(NoonBankService.NOON_SCHEMA);
        Assertions.assertThat(orderInfo.getPayment().getPaymentMethod()).isEqualTo("applepay");
        //
        Order orderAPay = orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.HOLD_PROCESSING);
        Assertions.assertThat(orderAPay.getBankOperations()).hasSize(1)
                .allSatisfy(it -> Assertions.assertThat(it.getBank()).isEqualTo(NoonBankService.NOON_BANK_NAME))
                .satisfies(it -> Assertions.assertThat(it.getPaymentMethod()).isEqualTo("applepay"), Index.atIndex(0));
        //
        HoldRequest holdRequest = HoldRequest.builder().type(NoonApplePayOptionProvider.TYPE).build();
        OrderService.InitOrderResult initOrder2nd = orderFlowTestUtils.holdOrderSuccessfull(orderInfo.getId(), holdRequest, true);
        OrderDTO orderInfo2nd = orderFlowTestUtils.loadOrderSuccessfull(initOrder2nd.getOrderId(), true);
        rollbackAndStartNewTransaction();
        //
        Order order2nd = orderFlowTestUtils.validateOrderState(orderInfo2nd.getId(), OrderState.HOLD_PROCESSING);
        Assertions.assertThat(order2nd.getBankOperations()).hasSize(1)
                .allSatisfy(it -> Assertions.assertThat(it.getBank()).isEqualTo(NoonBankService.NOON_BANK_NAME))
                .satisfies(it -> Assertions.assertThat(it.getPaymentMethod()).isEqualTo("applepay"), Index.atIndex(0));
        //
        Assertions.assertThat(orderAPay.getBankOperations().stream().map(BankOperation::getId).collect(Collectors.toList()))
                .isEqualTo(order2nd.getBankOperations().stream().map(BankOperation::getId).collect(Collectors.toList()));
    }

    @Test
    @Transactional
    public void payOption_noonCard() {
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .itemsCount(1)
                .payOptionsType(NoonCardOptionProvider.TYPE)
                .stopPosition(OrderFlowTestUtils.TestStopPosition.HOLD_PROCESSING)
                .build());
        Assertions.assertThat(orderInfo.getPayment().getPaymentVersion()).isEqualTo(NoonBankService.NOON_SCHEMA);
        Assertions.assertThat(orderInfo.getPayment().getPaymentMethod()).isEqualTo("card");
        //
        Order orderCard = orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.HOLD_PROCESSING);
        Assertions.assertThat(orderCard.getBankOperations()).hasSize(1)
                .allSatisfy(it -> Assertions.assertThat(it.getBank()).isEqualTo(NoonBankService.NOON_BANK_NAME))
                .satisfies(it -> Assertions.assertThat(it.getPaymentMethod()).isEqualTo("card"), Index.atIndex(0));
        //
        HoldRequest holdRequest = HoldRequest.builder().type(NoonCardOptionProvider.TYPE).build();
        OrderService.InitOrderResult initOrder2nd = orderFlowTestUtils.holdOrderSuccessfull(orderInfo.getId(), holdRequest, true);
        OrderDTO orderInfo2nd = orderFlowTestUtils.loadOrderSuccessfull(initOrder2nd.getOrderId(), true);
        rollbackAndStartNewTransaction();
        //
        Order order2nd = orderFlowTestUtils.validateOrderState(orderInfo2nd.getId(), OrderState.HOLD_PROCESSING);
        Assertions.assertThat(order2nd.getBankOperations()).hasSize(1)
                .allSatisfy(it -> Assertions.assertThat(it.getBank()).isEqualTo(NoonBankService.NOON_BANK_NAME))
                .satisfies(it -> Assertions.assertThat(it.getPaymentMethod()).isEqualTo("card"), Index.atIndex(0));
        //
        Assertions.assertThat(orderCard.getBankOperations().stream().map(BankOperation::getId).collect(Collectors.toList()))
                .isEqualTo(order2nd.getBankOperations().stream().map(BankOperation::getId).collect(Collectors.toList()));
    }

    @Test
    @Transactional
    public void payOption_noonCardThenNoonApplePay() {
        OrderDTO orderInfoCard = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .itemsCount(1)
                .payOptionsType(NoonCardOptionProvider.TYPE)
                .stopPosition(OrderFlowTestUtils.TestStopPosition.HOLD_PROCESSING)
                .build());
        Assertions.assertThat(orderInfoCard.getPayment().getPaymentVersion()).isEqualTo(NoonBankService.NOON_SCHEMA);
        Assertions.assertThat(orderInfoCard.getPayment().getPaymentMethod()).isEqualTo("card");
        //
        Order orderCard = orderFlowTestUtils.validateOrderState(orderInfoCard.getId(), OrderState.HOLD_PROCESSING);
        Assertions.assertThat(orderCard.getBankOperations()).hasSize(1)
                .allSatisfy(it -> Assertions.assertThat(it.getBank()).isEqualTo(NoonBankService.NOON_BANK_NAME))
                .satisfies(it -> Assertions.assertThat(it.getPaymentMethod()).isEqualTo("card"), Index.atIndex(0));
        //
        HoldRequest holdRequest = HoldRequest.builder().type(NoonApplePayOptionProvider.TYPE).build();
        OrderService.InitOrderResult initOrderAPay = orderFlowTestUtils.holdOrderSuccessfull(orderInfoCard.getId(), holdRequest, true);
        OrderDTO orderInfoAPay = orderFlowTestUtils.loadOrderSuccessfull(initOrderAPay.getOrderId(), true);
        rollbackAndStartNewTransaction();
        //
        Assertions.assertThat(orderInfoAPay.getId()).isEqualTo(orderInfoCard.getId());
        Assertions.assertThat(orderInfoAPay.getPayment().getPaymentVersion()).isEqualTo(orderInfoCard.getPayment().getPaymentVersion());
        Assertions.assertThat(orderInfoAPay.getPayment().getPaymentMethod()).isEqualTo("applepay");
        //
        Order orderAPay = orderFlowTestUtils.validateOrderState(orderInfoAPay.getId(), OrderState.HOLD_PROCESSING);
        List<OrderBankOperation> payOperationList = orderAPay.getBankOperations().stream()
                .sorted(Comparator.comparing(OrderBankOperation::getId))
                .collect(Collectors.toList());
        Assertions.assertThat(payOperationList).hasSize(2)
                .allSatisfy(it -> Assertions.assertThat(it.getBank()).isEqualTo(NoonBankService.NOON_BANK_NAME))
                .satisfies(it -> Assertions.assertThat(it.getPaymentMethod()).isEqualTo("card"), Index.atIndex(0))
                .satisfies(it -> Assertions.assertThat(it.getPaymentMethod()).isEqualTo("applepay"), Index.atIndex(1));
        Assertions.assertThat(payOperationList.stream().map(BankOperation::getExtraInfo).distinct().collect(Collectors.toList()))
                .hasSize(2);
    }

    public void payOption_cbOrdersFallbackValidate(List<Long> products, String payOptions, String validateScheme, String validateMethod) {
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(cbordSellerId)
                .productIdsList(products).itemsCount(1)
                .payOptionsType(payOptions)
                .stopPosition(OrderFlowTestUtils.TestStopPosition.HOLD_PROCESSING)
                .build());
        Assertions.assertThat(orderInfo.getPayment().getPaymentVersion()).isEqualTo(validateScheme);
        Assertions.assertThat(orderInfo.getPayment().getPaymentMethod()).isEqualTo(validateMethod);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void cbOrdersFallback() {
        SaleShipmentRoute saleShipmentRoute = saleShipmentRouteRepository.getBySystemName("EuropeDubaiMoscowGBS")
                .orElseThrow(IllegalArgumentException::new);
        orderFlowTestUtils.saveUser(cbordSellerId, (it) ->  it.setSaleShipmentRoute(saleShipmentRoute));
        //
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(cbordSellerId)
                .maxItems(1)
                .build()
        );
        List<Long> productIds = products.stream().map(Product::getId).collect(Collectors.toList());
        commitAndStartNewTransaction();
        //
        payOption_cbOrdersFallbackValidate(productIds, YandexPayOptionProvider.TYPE,
                PaymentsServiceBankService.PAYMENT_SCHEME_YANDEX_PAY, "pay");
        //
        payOption_cbOrdersFallbackValidate(productIds, "NOON",
                PaymentsServiceBankService.PAYMENT_SCHEME_TCB, "card");
        //
        payOption_cbOrdersFallbackValidate(productIds, CardPaymentOptionProvider.TYPE,
                PaymentsServiceBankService.PAYMENT_SCHEME_TCB, "card");
    }

}