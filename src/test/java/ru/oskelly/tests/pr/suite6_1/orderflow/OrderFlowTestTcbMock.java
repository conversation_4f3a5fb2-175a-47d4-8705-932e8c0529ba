package ru.oskelly.tests.pr.suite6_1.orderflow;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableList;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.mockserver.client.MockServerClient;
import org.mockserver.integration.ClientAndServer;
import org.mockserver.mock.action.ExpectationResponseCallback;
import org.mockserver.model.Header;
import org.mockserver.model.HttpRequest;
import org.mockserver.model.HttpResponse;
import org.springframework.web.util.UriComponentsBuilder;
import su.reddot.infrastructure.bank.impl.tcb.TcbBankClient;
import su.reddot.infrastructure.bank.impl.tcb.request.CompleteHoldRequest;
import su.reddot.infrastructure.bank.impl.tcb.request.GetOrderStateRequest;
import su.reddot.infrastructure.bank.impl.tcb.request.GetOrdersRequest;
import su.reddot.infrastructure.bank.impl.tcb.request.RefundOrderRequest;
import su.reddot.infrastructure.bank.impl.tcb.request.RegisterHoldRequest;
import su.reddot.infrastructure.bank.impl.tcb.request.RegisterOrderToExternalAccountRequest;
import su.reddot.infrastructure.bank.impl.tcb.request.RegisterOrderToRegisteredCardRequest;
import su.reddot.infrastructure.bank.impl.tcb.request.ReverseHoldRequest;
import su.reddot.infrastructure.bank.impl.tcb.response.BaseTcbApiResponse;
import su.reddot.infrastructure.bank.impl.tcb.response.CompleteHoldResponse;
import su.reddot.infrastructure.bank.impl.tcb.response.GetBalanceResponseApiV1;
import su.reddot.infrastructure.bank.impl.tcb.response.GetOrderStateResponse;
import su.reddot.infrastructure.bank.impl.tcb.response.GetOrdersResponse;
import su.reddot.infrastructure.bank.impl.tcb.response.RefundOrderResponse;
import su.reddot.infrastructure.bank.impl.tcb.response.RegisterHoldResponse;
import su.reddot.infrastructure.bank.impl.tcb.response.RegisterOrderToExternalAccountResponse;
import su.reddot.infrastructure.bank.impl.tcb.response.RegisterOrderToRegisteredCardResponse;
import su.reddot.infrastructure.bank.impl.tcb.response.ReverseHoldResponse;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.AbstractMap;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Pattern;

import static org.mockserver.model.HttpRequest.request;
import static org.mockserver.model.HttpResponse.response;
import static org.mockserver.model.HttpStatusCode.NOT_FOUND_404;
import static org.mockserver.model.HttpStatusCode.OK_200;

public class OrderFlowTestTcbMock {

    public static final String TCB_LOGIN_OSK_GROUP = "T2721101360ID";
    public static final String TCB_LOGIN_CONCIERGE = "T3657102618ID";

    public static final Long CLIENT_ID_RESP_OK01 = 20180810L;
    public static final Long CLIENT_ID_RESP_OK02 = 20180811L;
    public static final Long CLIENT_ID_RESP_FAIL = 19840401L;
    public static final Long CLIENT_ID_WAIT_OKAY = 20221010L;

    public static final Long CLIENT_ID_PAYMENT_TYPE_NULL_VALUE = 2022052601L;
    public static final Long CLIENT_ID_PAYMENT_TYPE_MIR_ACTUAL = 2022052602L;
    public static final Long CLIENT_ID_PAYMENT_TYPE_MIR_EXPIRE = 2022052603L;
    public static final Long CLIENT_ID_PAYMENT_TYPE_VISA_OKAY = 2022052604L;
    public static final Long CLIENT_ID_PAYMENT_TYPE_MASTER_OKAY = 2022052605L;
    public static final Long CLIENT_ID_PAYMENT_TYPE_MIR_NODATE = 2022052606L;

    public static final String PAYURL_PARAM_CLIENT_ID = "clientId";

    private static final String HEADER_TCB_LOGIN = "TCB-Header-Login";

    private final ClientAndServer clientAndServer;

    private final MockTcbServer mockTcbServer = new MockTcbServer();

    public OrderFlowTestTcbMock(String mockHost, int mockPort) {
        clientAndServer = ClientAndServer.startClientAndServer(mockPort);
        new MockServerClient(mockHost, mockPort)
                .when(request().withMethod("POST"))
                .respond(mockTcbServer);
    }

    public void stop() {
        clientAndServer.stop();
    }

    public Map<String, String> getOperationInfo(String operationsId) {
        return mockTcbServer.getOperationInfo(operationsId);
    }

    public void addFailOnAmountCreate(BigDecimal amount) {
        mockTcbServer.addFailOnAmountCreate(amount);
    }

    public void addFailOnAmountStatus(BigDecimal amount) {
        mockTcbServer.addFailOnAmountStatus(amount);
    }

    public static class MockTcbServer implements ExpectationResponseCallback {

        private final HashMap<String, HashMap<String, String>> operations = new HashMap<>();

        private final Set<BigDecimal> failOnAmountCreate = new HashSet<>();

        private final Set<BigDecimal> failOnAmountStatus = new HashSet<>();

        private final ObjectMapper objectMapper = new ObjectMapper()
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS, true)
                .configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true);

        public static final String PROP_NAME_TCB_LOGIN = "tcbLogin";
        public static final String PROP_NAME_ROUTE = "route";
        public static final String PROP_NAME_AMOUNT = "amount";
        public static final String PROP_NAME_ORD_ID = "orderId";
        public static final String PROP_NAME_STATE_ID = "stateId";
        public static final String PROP_NAME_CLIENT = "clientId";
        public static final String PROP_NAME_COMMENTS = "comment";
        public static final String PROP_NAME_DATETIME = "dateTime";
        public static final String PROP_NAME_CARD_BRAND = "cardBrand";
        public static final String PROP_NAME_CARD_ID = "cardRefId";
        public static final String PROP_NAME_ACCOUNT_ID = "accountId";
        public static final String PROP_NAME_ACCOUNT_INN = "accountInn";
        public static final String PROP_NAME_ACCOUNT_BIC = "accountBic";
        public static final String PROP_NAME_ACCOUNT_NAME = "accountName";

        @SneakyThrows
        public Map<String, String> getOperationInfo(String operationsId) {
            return objectMapper.readValue(objectMapper.writeValueAsString(operations.get(operationsId)), new TypeReference<Map<String, String>>() {});
        }

        public void addFailOnAmountCreate(BigDecimal amount) {
            failOnAmountCreate.add(amount);
        }

        public void addFailOnAmountStatus(BigDecimal amount) {
            failOnAmountStatus.add(amount);
        }

        private void setOperationProperty(String operationId, String propertyName, String propertyData) {
            HashMap<String, String> operationProperties = operations.get(operationId);
            if (Objects.isNull(operationProperties)) {
                operations.put(operationId, new HashMap<>());
            }
            operations.get(operationId).put(propertyName, propertyData);
        }

        private Optional<Map.Entry<String, String>> getOperationProperty(String operationId, String propertyName) {
            HashMap<String, String> operationProperties = operations.get(operationId);
            if (Objects.isNull(operationProperties)) {
                return Optional.empty();
            }
            if (!operationProperties.containsKey(propertyName)) {
                return Optional.empty();
            }
            return Optional.of(new AbstractMap.SimpleEntry<>(propertyName, operationProperties.get(propertyName)));
        }

        private long getNextOperationOrderNumber() {
            return ********* + operations.size();
        }

        private String getHttpRequestJsonText(HttpRequest httpRequest) {
            return new String(httpRequest.getBodyAsRawBytes(), StandardCharsets.UTF_8);
        }

        private void setResponseState(String extId, GetOrderStateResponse orderStateResponse) {
            String orderRoute = getOperationProperty(extId, PROP_NAME_ROUTE).map(Map.Entry::getValue).orElseThrow(IllegalStateException::new);
            switch (orderRoute) {
                case TcbBankClient.API_V1_ROUTE_HOLD_FORM:
                case TcbBankClient.API_V1_ROUTE_HOLD_FORM_REGISTERED:
                    orderStateResponse.getOrderInfo().setState(BaseTcbApiResponse.ORDER_INFO_STATUS_HOLD_SUCCESS);
                    break;
                case TcbBankClient.API_V1_ROUTE_CONFIRM:
                    orderStateResponse.getOrderInfo().setState(BaseTcbApiResponse.ORDER_INFO_STATUS_DEBIT_SUCCESS);
                    break;
                case TcbBankClient.API_V1_ROUTE_REFUND:
                    orderStateResponse.getOrderInfo().setState(BaseTcbApiResponse.ORDER_INFO_STATUS_CREDIT_SUCCESS);
                    break;
                case TcbBankClient.API_V1_ROUTE_ACCOUNT_EXTERNAL_CREDIT:
                case TcbBankClient.API_V1_ROUTE_CARD_REGISTERED_CREDIT:
                    orderStateResponse.getOrderInfo().setState(BaseTcbApiResponse.ORDER_INFO_STATUS_CREDIT_SUCCESS);
                    break;
            }
            getOperationProperty(extId, PROP_NAME_STATE_ID).ifPresent(it -> orderStateResponse.getOrderInfo().setState(Integer.parseInt(it.getValue())));
        }

        @Override
        @SneakyThrows
        public HttpResponse handle(HttpRequest httpRequest) {
            String tcbLogin = StringUtils.join(httpRequest.getHeader(HEADER_TCB_LOGIN), "");
            if (StringUtils.equalsAny(httpRequest.getPath().getValue(), TcbBankClient.API_V1_ROUTE_HOLD_FORM, TcbBankClient.API_V1_ROUTE_HOLD_FORM_REGISTERED)) {
                RegisterHoldRequest registerHoldRequest = objectMapper.readValue(getHttpRequestJsonText(httpRequest), RegisterHoldRequest.class);
                if (!Pattern.compile("Оплата по заказу \\d*").matcher(registerHoldRequest.getDescription()).matches()) {
                    throw new IllegalArgumentException("Hold description format validation failed"); // TODO match it on client / test side, validate in GetOrderState response
                }
                RegisterHoldResponse registerHoldResponse = new RegisterHoldResponse();
                registerHoldResponse.setOrderId(getNextOperationOrderNumber());
                registerHoldResponse.setExtId(registerHoldRequest.getExtId());
                String payUrl = UriComponentsBuilder.fromUriString(registerHoldRequest.getReturnURL())
                        .queryParam(PAYURL_PARAM_CLIENT_ID, registerHoldRequest.getCardRefId())
                        .scheme("https")
                        .build().toString();
                registerHoldResponse.setFormURL(payUrl);
                setOperationProperty(registerHoldResponse.getExtId(), PROP_NAME_ROUTE, httpRequest.getPath().getValue());
                setOperationProperty(registerHoldResponse.getExtId(), PROP_NAME_ORD_ID, registerHoldResponse.getOrderId().toString());
                setOperationProperty(registerHoldResponse.getExtId(), PROP_NAME_DATETIME, LocalDateTime.now().toString());
                String clientId = Objects.nonNull(registerHoldRequest.getCardRefId())
                        ? registerHoldRequest.getCardRefId().toString()
                        : LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
                setOperationProperty(registerHoldResponse.getExtId(), PROP_NAME_CLIENT, clientId);
                if (Objects.equals(CLIENT_ID_PAYMENT_TYPE_NULL_VALUE, registerHoldRequest.getCardRefId())) {
                    setOperationProperty(registerHoldResponse.getExtId(), PROP_NAME_CARD_BRAND, null);
                }
                if (Objects.equals(CLIENT_ID_PAYMENT_TYPE_MIR_ACTUAL, registerHoldRequest.getCardRefId())) {
                    setOperationProperty(registerHoldResponse.getExtId(), PROP_NAME_CARD_BRAND, "MIR");
                }
                if (Objects.equals(CLIENT_ID_PAYMENT_TYPE_VISA_OKAY, registerHoldRequest.getCardRefId())) {
                    setOperationProperty(registerHoldResponse.getExtId(), PROP_NAME_CARD_BRAND, "VISA");
                }
                if (Objects.equals(CLIENT_ID_PAYMENT_TYPE_MASTER_OKAY, registerHoldRequest.getCardRefId())) {
                    setOperationProperty(registerHoldResponse.getExtId(), PROP_NAME_CARD_BRAND, "MASTER");
                }
                if (Objects.equals(CLIENT_ID_PAYMENT_TYPE_MIR_EXPIRE, registerHoldRequest.getCardRefId())) {
                    setOperationProperty(registerHoldResponse.getExtId(), PROP_NAME_CARD_BRAND, "MIR");
                    setOperationProperty(registerHoldResponse.getExtId(), PROP_NAME_DATETIME, LocalDateTime.now().minusMonths(2).toString());
                }
                if (Objects.equals(CLIENT_ID_PAYMENT_TYPE_MIR_NODATE, registerHoldRequest.getCardRefId())) {
                    setOperationProperty(registerHoldResponse.getExtId(), PROP_NAME_CARD_BRAND, "MIR");
                    setOperationProperty(registerHoldResponse.getExtId(), PROP_NAME_DATETIME, null);
                }
                if (Objects.equals(CLIENT_ID_RESP_FAIL, registerHoldRequest.getCardRefId())) {
                    setOperationProperty(registerHoldResponse.getExtId(), PROP_NAME_STATE_ID, String.valueOf(BaseTcbApiResponse.ORDER_INFO_STATUS_ERROR));
                }
                if (Objects.equals(CLIENT_ID_WAIT_OKAY, registerHoldRequest.getCardRefId())) {
                    Thread.sleep(2500);
                }
                return response().withStatusCode(OK_200.code())
                        .withHeaders(new Header("Content-Type", "application/json;charset=UTF-8"))
                        .withBody(objectMapper.writeValueAsString(registerHoldResponse));
            }
            if (TcbBankClient.API_V1_ROUTE_ORDER_STATE.equals(httpRequest.getPath().getValue())) {
                String orderStateOkay = "{\"OrderInfo\":{\"ExtId\":\"dd649ae4-c796-4155-9673-393507c89750\",\"OrderId\":**********,\"OrderNumber\":**********,\"State\":6,\"StateDescription\":\"Успех\",\"Type\":\"FROMUNREGISTEREDCARD\",\"Amount\":1335000,\"Fee\":13350,\"DateTime\":\"2022-05-30T15:56:22\",\"StateUpdateDateTime\":\"0001-01-01T00:00:00\"},\"OrderAdditionalInfo\":{\"MercId\":\"102805\",\"ToCardNumber\":\"0000 00** **** 0000\",\"CardNumber\":\"0000 00** **** 0000\",\"CardExpYear\":\"2024\",\"CardRefID\":\"*********\",\"CardExpMonth\":\"8\",\"CardIssuingBank\":\"SBERBANK\",\"CardBrand\":\"VISA\",\"RC\":\"00\",\"MerchURL\":\"\",\"AdditionalParameters\":\"{}\",\"RRN\":\"************\",\"Data\":{\"MercId\":\"102805\",\"ToCardNumber\":\"0000 00** **** 0000\",\"CardNumber\":\"0000 00** **** 0000\",\"CardExpYear\":\"2024\",\"CardRefID\":\"*********\",\"CardExpMonth\":\"8\",\"CardIssuingBank\":\"SBERBANK\",\"CardBrand\":\"VISA\",\"RC\":\"00\",\"MerchURL\":\"\",\"AdditionalParameters\":\"{}\",\"RRN\":\"************\"}},\"ErrorInfo\":{\"ErrorCode\":0,\"ErrorMessage\":\"Успех\"}}";
                GetOrderStateRequest orderStateRequest = objectMapper.readValue(getHttpRequestJsonText(httpRequest), GetOrderStateRequest.class);
                GetOrderStateResponse orderStateResponse = objectMapper.readValue(orderStateOkay, GetOrderStateResponse.class);
                orderStateResponse.getOrderInfo().setExtId(orderStateRequest.getExtId());
                getOperationProperty(orderStateRequest.getExtId(), PROP_NAME_DATETIME).ifPresent(
                        it -> orderStateResponse.getOrderInfo().setDateTime(it.getValue())
                );
                getOperationProperty(orderStateRequest.getExtId(), PROP_NAME_CARD_BRAND).ifPresent(
                        it -> orderStateResponse.getOrderAdditionalInfo().setCardBrand(it.getValue())
                );
                getOperationProperty(orderStateRequest.getExtId(), PROP_NAME_AMOUNT).map(it -> new BigInteger(it.getValue())).ifPresent(it -> {
                    orderStateResponse.getOrderInfo().setAmount(it);
                    failIfAmountInList(BigDecimal.valueOf(it.longValue(), 2), failOnAmountStatus);
                });
                getOperationProperty(orderStateRequest.getExtId(), PROP_NAME_ORD_ID).ifPresent(
                        it -> orderStateResponse.getOrderInfo().setOrderId(it.getValue())
                );
                getOperationProperty(orderStateRequest.getExtId(), PROP_NAME_CLIENT).ifPresent(
                        it -> orderStateResponse.getOrderAdditionalInfo().setCardRefID(it.getValue())
                );
                setResponseState(orderStateRequest.getExtId(), orderStateResponse);
                return response().withStatusCode(OK_200.code())
                        .withHeaders(new Header("Content-Type", "application/json;charset=UTF-8"))
                        .withBody(objectMapper.writeValueAsString(orderStateResponse));
            }
            if (TcbBankClient.API_V1_ROUTE_CONFIRM.equals(httpRequest.getPath().getValue())) {
                CompleteHoldRequest completeHoldRequest = objectMapper.readValue(getHttpRequestJsonText(httpRequest), CompleteHoldRequest.class);
                CompleteHoldResponse completeHoldResponse = new CompleteHoldResponse();
                completeHoldResponse.setOrderId(getNextOperationOrderNumber());
                completeHoldResponse.setExtId(completeHoldRequest.getExtId() + "CNF");
                completeHoldResponse.setOrderId(0L);
                setOperationProperty(completeHoldResponse.getExtId(), PROP_NAME_AMOUNT, completeHoldRequest.getAmount().toString());
                setOperationProperty(completeHoldResponse.getExtId(), PROP_NAME_ROUTE, httpRequest.getPath().getValue());
                setOperationProperty(completeHoldResponse.getExtId(), PROP_NAME_ORD_ID, completeHoldResponse.getOrderId().toString());
                return response().withStatusCode(OK_200.code())
                        .withHeaders(new Header("Content-Type", "application/json;charset=UTF-8"))
                        .withBody(objectMapper.writeValueAsString(completeHoldResponse));

            }
            if (TcbBankClient.API_V1_ROUTE_ORDER_GET_ORDERS.equals(httpRequest.getPath().getValue())) {
                GetOrdersRequest getOrdersRequest = objectMapper.readValue(getHttpRequestJsonText(httpRequest), GetOrdersRequest.class);
                GetOrdersResponse getOrdersResponse = new GetOrdersResponse();
                getOrdersResponse.setOperations(getOperations(getOrdersRequest.getExtId()));
                return response().withStatusCode(OK_200.code())
                        .withHeaders(new Header("Content-Type", "application/json;charset=UTF-8"))
                        .withBody(objectMapper.writeValueAsString(getOrdersResponse));
            }
            if (TcbBankClient.API_V1_ROUTE_REFUND.equals(httpRequest.getPath().getValue())) {
                RefundOrderRequest refundOrderRequest = objectMapper.readValue(getHttpRequestJsonText(httpRequest), RefundOrderRequest.class);
                RefundOrderResponse refundOrderResponse = new RefundOrderResponse();
                refundOrderResponse.setOrderId(getNextOperationOrderNumber());
                refundOrderResponse.setExtId(getNextRefundOperationStringId(refundOrderRequest));
                refundOrderResponse.setOrderId(0L);
                setOperationProperty(refundOrderResponse.getExtId(), PROP_NAME_AMOUNT, refundOrderRequest.getAmount().toString());
                setOperationProperty(refundOrderResponse.getExtId(), PROP_NAME_COMMENTS, refundOrderRequest.getDescription());
                setOperationProperty(refundOrderResponse.getExtId(), PROP_NAME_ROUTE, httpRequest.getPath().getValue());
                setOperationProperty(refundOrderResponse.getExtId(), PROP_NAME_ORD_ID, refundOrderResponse.getOrderId().toString());
                return response().withStatusCode(OK_200.code())
                        .withHeaders(new Header("Content-Type", "application/json;charset=UTF-8"))
                        .withBody(objectMapper.writeValueAsString(refundOrderResponse));
            }
            if (TcbBankClient.API_V1_ROUTE_REVERSE.equals(httpRequest.getPath().getValue())) {
                ReverseHoldRequest reverseHoldRequest = objectMapper.readValue(getHttpRequestJsonText(httpRequest), ReverseHoldRequest.class);
                ReverseHoldResponse reverseHoldResponse = new ReverseHoldResponse();
                reverseHoldResponse.setOrderId(getNextOperationOrderNumber());
                reverseHoldResponse.setExtId(reverseHoldRequest.getExtId() + "RVS");
                reverseHoldResponse.setOrderId(0L);
                setOperationProperty(reverseHoldResponse.getExtId(), PROP_NAME_AMOUNT, reverseHoldRequest.getAmount().toString());
                setOperationProperty(reverseHoldResponse.getExtId(), PROP_NAME_ROUTE, httpRequest.getPath().getValue());
                setOperationProperty(reverseHoldResponse.getExtId(), PROP_NAME_ORD_ID, reverseHoldResponse.getOrderId().toString());
                return response().withStatusCode(OK_200.code())
                        .withHeaders(new Header("Content-Type", "application/json;charset=UTF-8"))
                        .withBody(objectMapper.writeValueAsString(reverseHoldResponse));
            }
            if (TcbBankClient.API_V1_ROUTE_BANKING_ACCOUNT_BALANCE.equals(httpRequest.getPath().getValue())) {
                GetBalanceResponseApiV1 getBalanceResponseApiV1 = new GetBalanceResponseApiV1();
                getBalanceResponseApiV1.setAmount(BigDecimal.valueOf(1_000_000_000_00L, 2));
                return response().withStatusCode(OK_200.code())
                        .withHeaders(new Header("Content-Type", "application/json;charset=UTF-8"))
                        .withBody(objectMapper.writeValueAsString(getBalanceResponseApiV1));
            }
            if (TcbBankClient.API_V1_ROUTE_ACCOUNT_EXTERNAL_CREDIT.equals(httpRequest.getPath().getValue())) {
                RegisterOrderToExternalAccountRequest registerOrderToExternalAccountRequest = objectMapper.readValue(getHttpRequestJsonText(httpRequest), RegisterOrderToExternalAccountRequest.class);
                if (registerOrderToExternalAccountRequest.getAmount() <= 0) {
                    throw new IllegalArgumentException("Amount is less than or equals zero");
                }
                RegisterOrderToExternalAccountResponse registerOrderToExternalAccountResponse = new RegisterOrderToExternalAccountResponse();
                registerOrderToExternalAccountResponse.setOrderId(registerOrderToExternalAccountRequest.getOrderId());
                registerOrderToExternalAccountResponse.setOrderNumber(getNextOperationOrderNumber());
                setOperationProperty(registerOrderToExternalAccountResponse.getOrderId(), PROP_NAME_TCB_LOGIN, tcbLogin);
                setOperationProperty(registerOrderToExternalAccountResponse.getOrderId(), PROP_NAME_AMOUNT, String.valueOf(registerOrderToExternalAccountRequest.getAmount()));
                setOperationProperty(registerOrderToExternalAccountRequest.getOrderId(), PROP_NAME_COMMENTS, registerOrderToExternalAccountRequest.getDescription());
                setOperationProperty(registerOrderToExternalAccountResponse.getOrderId(), PROP_NAME_ROUTE, httpRequest.getPath().getValue());
                setOperationProperty(registerOrderToExternalAccountResponse.getOrderId(), PROP_NAME_ORD_ID, registerOrderToExternalAccountResponse.getOrderId());
                setOperationProperty(registerOrderToExternalAccountResponse.getOrderId(), PROP_NAME_ACCOUNT_ID, registerOrderToExternalAccountRequest.getAccount());
                setOperationProperty(registerOrderToExternalAccountResponse.getOrderId(), PROP_NAME_ACCOUNT_INN, registerOrderToExternalAccountRequest.getInn());
                setOperationProperty(registerOrderToExternalAccountResponse.getOrderId(), PROP_NAME_ACCOUNT_BIC, registerOrderToExternalAccountRequest.getBic());
                setOperationProperty(registerOrderToExternalAccountResponse.getOrderId(), PROP_NAME_ACCOUNT_NAME, registerOrderToExternalAccountRequest.getName());
                failIfAmountInList(BigDecimal.valueOf(registerOrderToExternalAccountRequest.getAmount(), 2), failOnAmountCreate);
                return response().withStatusCode(OK_200.code())
                        .withHeaders(new Header("Content-Type", "application/json;charset=UTF-8"))
                        .withBody(objectMapper.writeValueAsString(registerOrderToExternalAccountResponse));

            }
            if (TcbBankClient.API_V1_ROUTE_CARD_REGISTERED_CREDIT.equals(httpRequest.getPath().getValue())) {
                RegisterOrderToRegisteredCardRequest registerOrderToRegisteredCardRequest = objectMapper.readValue(getHttpRequestJsonText(httpRequest), RegisterOrderToRegisteredCardRequest.class);
                RegisterOrderToRegisteredCardResponse registerOrderToRegisteredCardResponse = new RegisterOrderToRegisteredCardResponse();
                registerOrderToRegisteredCardResponse.setExtId(registerOrderToRegisteredCardRequest.getExtId());
                registerOrderToRegisteredCardResponse.setOrderId(getNextOperationOrderNumber());
                setOperationProperty(registerOrderToRegisteredCardResponse.getExtId(), PROP_NAME_TCB_LOGIN, tcbLogin);
                setOperationProperty(registerOrderToRegisteredCardResponse.getExtId(), PROP_NAME_ROUTE, httpRequest.getPath().getValue());
                setOperationProperty(registerOrderToRegisteredCardResponse.getExtId(), PROP_NAME_ORD_ID, registerOrderToRegisteredCardResponse.getOrderId().toString());
                setOperationProperty(registerOrderToRegisteredCardResponse.getExtId(), PROP_NAME_AMOUNT, String.valueOf(registerOrderToRegisteredCardRequest.getAmount()));
                setOperationProperty(registerOrderToRegisteredCardResponse.getExtId(), PROP_NAME_COMMENTS, registerOrderToRegisteredCardRequest.getDescription());
                setOperationProperty(registerOrderToRegisteredCardResponse.getExtId(), PROP_NAME_CARD_ID, registerOrderToRegisteredCardRequest.getCardRefID().toString());
                failIfAmountInList(BigDecimal.valueOf(registerOrderToRegisteredCardRequest.getAmount(), 2), failOnAmountCreate);
                return response().withStatusCode(OK_200.code())
                        .withHeaders(new Header("Content-Type", "application/json;charset=UTF-8"))
                        .withBody(objectMapper.writeValueAsString(registerOrderToRegisteredCardResponse));

            }
            return response().withStatusCode(NOT_FOUND_404.code());
        }

        private List<GetOrdersResponse.Operation> getOperations(String operationExtId) {
            GetOrdersResponse.Operation operation = new GetOrdersResponse.Operation();
            operation.setExtId(operationExtId);
            operation.setRoute(getOperationProperty(operationExtId, PROP_NAME_ROUTE).map(Map.Entry::getValue).orElse(null));
            operation.setOrderId(getOperationProperty(operationExtId, PROP_NAME_ORD_ID).map(Map.Entry::getValue).orElse(null));
            operation.setAmount(getOperationProperty(operationExtId, PROP_NAME_AMOUNT).map(it -> Long.valueOf(it.getValue())).orElse(null));
            operation.setDescription(getOperationProperty(operationExtId, PROP_NAME_COMMENTS).map(Map.Entry::getValue).orElse(null));
            return ImmutableList.of(operation);
        }

        private void failIfAmountInList(BigDecimal amount, Set<BigDecimal> failOnAmount) {
            failOnAmount.stream().filter(it -> it.compareTo(amount) == 0).findFirst().ifPresent(failAmountInList -> {
                throw new UnsupportedOperationException("Unable to perform operation: intentional fail");
            });
        }

        private String getNextRefundOperationStringId(RefundOrderRequest refundOrderRequest) {
            for (int i = 1; ; i++) {
                String nextOpId = refundOrderRequest.getExtId() + "R" + i;
                if (!getOperationProperty(nextOpId, PROP_NAME_ROUTE).isPresent()) {
                    return nextOpId;
                }
            }
        }

    }

}
