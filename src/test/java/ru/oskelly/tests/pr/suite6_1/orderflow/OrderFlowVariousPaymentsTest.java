package ru.oskelly.tests.pr.suite6_1.orderflow;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.pr.common.bonuses.BonusesServiceTestConfiguration;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2ParseException;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.domain.dao.counterparty.CounterpartyRepository;
import su.reddot.domain.model.banktransaction.BankPayment;
import su.reddot.domain.model.banktransaction.OperationType;
import su.reddot.domain.model.banktransaction.TransactionState;
import su.reddot.domain.model.banktransaction.order.OrderBankOperation;
import su.reddot.domain.model.counterparty.InternationalCounterparty;
import su.reddot.domain.model.discount.AbsolutePromoCode;
import su.reddot.domain.model.discount.FractionalPromoCode;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.currency.CurrencyRateService;
import su.reddot.domain.service.currency.CurrencyService;
import su.reddot.domain.service.dto.CurrencyDTO;
import su.reddot.domain.service.dto.order.GroupedCart;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.dto.order.OrderPositionDTO;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.bank.jobs.AgentPaymentJobs;
import su.reddot.infrastructure.bank.payments.noon.NoonBankService;
import su.reddot.infrastructure.logistic.DeliveryState;
import su.reddot.infrastructure.util.CallInTransaction;
import su.reddot.oskelly.orderprocessing.internal.web.client.OrderMobileApi;
import su.reddot.oskelly.orderprocessing.internal.web.dto.IntegrationMobileOrderExpertiseDTO;
import su.reddot.presentation.api.v2.Api2Response;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.Currency;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@TestMethodOrder(MethodOrderer.MethodName.class)
@ContextConfiguration(classes = {OrderFlowTestUtils.class, BonusesServiceTestConfiguration.class})
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
public class OrderFlowVariousPaymentsTest extends AbstractSpringTest {

    @Autowired
    private UserService userService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private AgentPaymentJobs agentPaymentJobs;
    @Autowired
    private CurrencyService currencyService;

    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private CartTestSupport cartTestSupport;
    @Autowired
    private CallInTransaction callInTransaction;
    @Autowired
    CounterpartyRepository<InternationalCounterparty> counterpartyRepository;

    @MockBean
    private OrderMobileApi orderMobileApi;

    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user-password}")
    private String password;

    private static final long testOrderSellerId = 40045;
    private static final long testOrderSellerCounterpartyTCBId = 4462;
    private static final long testOrderPickupId = 9372;
    private static final long testOrderDeliveryId = 1585L;

    private static OrderFlowTestTcbMock orderFlowTestTcbMock;
    private static OrderFlowTestNoonMock orderFlowTestNonMock;

    @Value("${test.receipts.mock-server-host}")
    private String mockServerHost;
    @Value("${test.receipts.mock-server-tcb-bank-port}")
    private Integer mockTcbServerPort;

    @Value("${test.receipts.mock-server-non-bank-port}")
    private Integer mockNonServerPort;

    private InternationalCounterparty counterparty;

    private Long prepareAdminUser() {
        User adminUser = userService.getUserByEmail(buyerEmail);
        orderFlowTestUtils.enableUserAuthority(adminUser.getId(), AuthorityName.ORDER_PAYOUTS, true);
        orderFlowTestUtils.enableUserAuthority(adminUser.getId(), AuthorityName.ORDER_MANUAL_CHANGE_DELIVERY_STATE, true);
        return adminUser.getId();
    }

    @PostConstruct
    private void init() {
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(TcbBankService.SCHEMA, NoonBankService.NOON_SCHEMA));
        User buyer = userService.getUserByEmail(buyerEmail);
        ApiV2Client apiV2Client = new ApiV2Client(buyerEmail, password);
        orderFlowTestUtils.init(buyerEmail, password);
        cartTestSupport.setUserId(buyer.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
        orderFlowTestTcbMock = Objects.isNull(orderFlowTestTcbMock) ? new OrderFlowTestTcbMock(mockServerHost, mockTcbServerPort) : orderFlowTestTcbMock;
        orderFlowTestNonMock = Objects.isNull(orderFlowTestNonMock) ? new OrderFlowTestNoonMock(mockServerHost, mockNonServerPort) : orderFlowTestNonMock;
        callInTransaction.runInNewTransaction(this::prepareAdminUser);

        User seller = userService.getOne(testOrderSellerId);

        counterparty = new InternationalCounterparty();
        counterparty.setUser(seller);
        counterparty.setIsActive(true);
        counterparty.setCreateTime(ZonedDateTime.now());
        counterparty.setChangeTime(ZonedDateTime.now());
        counterparty.setIban("****************************");
        counterparty = counterpartyRepository.saveAndFlush(counterparty);

        Mockito.when(orderMobileApi.getMobileOrderExpertise(Mockito.any()))
                .thenReturn(new IntegrationMobileOrderExpertiseDTO().items(Collections.emptyList()));
    }

    @AfterAll
    public static void done() {
        orderFlowTestTcbMock.stop();
        orderFlowTestNonMock.stop();
    }

    private void _01_XX_orderFlowCurrencies_MultipleCurrencies_ValidateExceptions(List<Product> multipleCurrenciesProductsList) {
        orderFlowTestUtils.fillCart(multipleCurrenciesProductsList);
        try {
            orderFlowTestUtils.holdOrderWithPromoCodePS(multipleCurrenciesProductsList.get(0).getSeller(), null, NoonBankService.NOON_SCHEMA);
            Assertions.fail("Unreachable code: must fall with an exception");
        } catch (ApiV2ParseException e) {
            Api2Response<String> errorRsp = orderFlowTestUtils.getRawApi2Response(e.getRawData());
            Assertions.assertThat(errorRsp.getMessage()).matches("Заказ .*: присутствуют товары с различной валютой публикации, пожалуйста, разбейте заказ по позициям");
            Assertions.assertThat(errorRsp.getHumanMessage()).matches("Заказ .*: присутствуют товары с различной валютой публикации, пожалуйста, разбейте заказ по позициям");
        }
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_01_orderFlowCurrencies_MultipleCurrencies_EUR_USD() {
        Product productWithPriceInEUR = orderFlowTestUtils.getProductsForOrdersWithSeller(testOrderSellerId, "EUR", 1).get(0);
        orderFlowTestUtils.setProductPrice(productWithPriceInEUR, "EUR", BigDecimal.valueOf(1_000_00, 2));
        //
        Product productWithPriceInUSD = orderFlowTestUtils.getProductsForOrdersWithSeller(testOrderSellerId, "USD", 1).get(0);
        orderFlowTestUtils.setProductPrice(productWithPriceInUSD, "USD", BigDecimal.valueOf(1_000_00, 2));
        //
        commitAndStartNewTransaction();
        //
        List<Product> multipleCurrenciesProductsList = ImmutableList.of(productWithPriceInEUR, productWithPriceInUSD);
        //
        _01_XX_orderFlowCurrencies_MultipleCurrencies_ValidateExceptions(multipleCurrenciesProductsList);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_02_orderFlowCurrencies_MultipleCurrencies_EUR_NUL() {
        Product productWithPriceInEUR = orderFlowTestUtils.getProductsForOrdersWithSeller(testOrderSellerId, "EUR", 1).get(0);
        Product productWithPriceInNUL = orderFlowTestUtils.getProductsForOrdersWithSeller(testOrderSellerId).get(0);
        commitAndStartNewTransaction();
        //
        List<Product> multipleCurrenciesProductsList = ImmutableList.of(productWithPriceInEUR, productWithPriceInNUL);
        //
        _01_XX_orderFlowCurrencies_MultipleCurrencies_ValidateExceptions(multipleCurrenciesProductsList);
    }

    public long _02_XX_orderFlow_currencies_processOrder(List<Product> productsList, String bankSchema) {
        orderFlowTestUtils.fillCart(productsList);
        //
        OrderService.InitOrderResult testOrder = orderFlowTestUtils.holdOrderWithPromoCodePS(productsList.get(0).getSeller(), null, bankSchema);
        //
        orderFlowTestUtils.callOrderHoldCallback(testOrder.getOrderId(), testOrder.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        OrderDTO orderInfo = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getOrderId(), true);
        Assertions.assertThat(orderInfo.getId()).isNotEqualTo(0);
        //
        Order order = orderService.getOrder(orderInfo.getId());
        Assertions.assertThat(order.getId()).isEqualTo(orderInfo.getId());
        //
        orderInfo.getItems().forEach(op -> orderFlowTestUtils.rejectOrApprovePosition(op.getId(), true));
        orderFlowTestUtils.changeAddressEndpoint(orderInfo.getId(), testOrderPickupId, testOrderDeliveryId);
        //
        orderFlowTestUtils.takeOurselves(orderInfo.getId(), null);
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.OURSELVES_FROM_SELLER_TO_OFFICE, true);
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE, true);
        //
        for (OrderPositionDTO orderPosition : orderInfo.getItems()) {
            orderFlowTestUtils.adminsApi1expertise(orderPosition.getId(), true, OrderFlowTestUtils.ExpertiseAction.EXPERTISE_PASSED_OK, null);
        }
        //
        orderFlowTestUtils.adminPanel_Charge(orderInfo.getId());
        orderFlowTestUtils.processHoldComplete(orderInfo);
        //
        orderFlowTestUtils.sendOurselves(orderInfo.getId(), null);
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.OURSELVES_FROM_OFFICE_TO_BUYER, true);
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.DELIVERED_TO_BUYER, true);
        //
        orderFlowTestUtils.sendAgentReport(orderInfo.getId(), true);
        commitAndStartNewTransaction();
        //
        if (bankSchema.equalsIgnoreCase(TcbBankService.SCHEMA)) {
            orderFlowTestUtils.changeSellerCounterparty(orderInfo.getId(), testOrderSellerCounterpartyTCBId, HttpStatus.Series.SUCCESSFUL);
        } else {
            orderFlowTestUtils.changeSellerCounterparty(orderInfo.getId(), counterparty.getId(), HttpStatus.Series.SUCCESSFUL);
        }
        //
        orderFlowTestUtils.confirmAgentReport(orderInfo.getId(), true);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.prepareSellerPayout(orderInfo.getId(), true);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.loadAgentReport(orderInfo.getId());
        //
        //orderFlowTestUtils.validateSellerPayout(orderInfo.getId(), payoutAmount, TcbBankService.SCHEMA, TransactionState.PREPARED);
        //
        orderFlowTestUtils.transferMoneyToSellers(orderInfo.getId());
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.MONEY_PAYMENT_WAIT);
        //
        orderFlowTestUtils.validateMoneyToSellers(orderInfo.getId());
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.COMPLETED);
        //
        //orderFlowTestUtils.validateSellerPayout(orderInfo.getId(), payoutAmount, TcbBankService.SCHEMA, TransactionState.DONE);
        //
        agentPaymentJobs.transferMoneyToSeller();
        //
        return orderInfo.getId();
    }

    private void _02_XX_orderFlowCurrencies_payout_in_XXX(String currencyCode, String bankSchema, boolean isBaseCurrencyOnly) {
        Product productWithPriceInXXX = orderFlowTestUtils.getProductsForOrdersWithSeller(testOrderSellerId, currencyCode, 1).get(0);
        orderFlowTestUtils.setProductPrice(productWithPriceInXXX, currencyCode, BigDecimal.valueOf(200_000_00, 2));
        commitAndStartNewTransaction();
        //
        long orderId = _02_XX_orderFlow_currencies_processOrder(Collections.singletonList(productWithPriceInXXX), bankSchema);
        //
        orderFlowTestUtils.validateAgentReportAmnt(orderId, currencyCode, BigDecimal.valueOf(168_000_00, 2));
        BankPayment bp = orderFlowTestUtils.validateSellerPayoutBP(orderId, 168_000_00L, currencyCode, bankSchema, TransactionState.DONE);
        //
        Order order = orderService.getOrder(orderId);
        OrderBankOperation bankOperation = order.getBankOperations().stream()
                .filter(bo -> bo.getOperationType() == OperationType.SELLER_PAYOUT)
                .findAny().orElseThrow(() -> new AssertionError("Unable to find bankOperation with type SELLER_PAYOUT"));
        if (isBaseCurrencyOnly) {
            Assertions.assertThat(bankOperation.getCurrency().getIsoCode()).isEqualTo(orderFlowTestUtils.getBaseCurrencyIsoCode());
            Assertions.assertThat(bankOperation.getApiSentAmount()).isEqualTo(bp.getAmount().movePointRight(2).longValue());
        } else {
            Assertions.assertThat(bankOperation.getCurrency().getIsoCode()).isEqualTo(bp.getOriginalCurrencyCode());
            Assertions.assertThat(bankOperation.getApiSentAmount()).isEqualTo(bp.getOriginalCurrencyAmount().movePointRight(2).longValue());
        }
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _02_01_orderFlowCurrencies_noon_payout_in_RUB() {
        _02_XX_orderFlowCurrencies_payout_in_XXX("RUB", NoonBankService.NOON_SCHEMA, false);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _02_02_orderFlowCurrencies_noon_payout_in_USD() {
        _02_XX_orderFlowCurrencies_payout_in_XXX("USD", NoonBankService.NOON_SCHEMA, false);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _02_03_orderFlowCurrencies_noon_payout_in_EUR() {
        _02_XX_orderFlowCurrencies_payout_in_XXX("EUR", NoonBankService.NOON_SCHEMA, false);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _03_01_orderFlowCurrencies_tcb_payout_in_RUB() {
        _02_XX_orderFlowCurrencies_payout_in_XXX("RUB", TcbBankService.SCHEMA, true);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _03_02_orderFlowCurrencies_tcb_payout_in_USD() {
        _02_XX_orderFlowCurrencies_payout_in_XXX("USD", TcbBankService.SCHEMA, true);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _03_03_orderFlowCurrencies_tcb_payout_in_EUR() {
        _02_XX_orderFlowCurrencies_payout_in_XXX("EUR", TcbBankService.SCHEMA, true);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _04_01_orderFlowCurrencies_OneCurrencyOneRate() {
        CurrencyDTO currency = currencyService.getCurrencyDTOByCodeCached("USD");
        String baseCurrencyCode = currencyService.getBaseCurrency().getIsoCode();
        BigDecimal itemOnePrice = BigDecimal.valueOf(1000_00, 2);
        BigDecimal itemTwoPrice = BigDecimal.valueOf(1000_00, 2);
        //
        orderFlowTestUtils.setCurrencyRate(currency.getIsoCode(), baseCurrencyCode,
                BigDecimal.valueOf(100_00, 2));
        Product productWithPriceInUSD01 = orderFlowTestUtils.getProductsForOrdersWithSeller(testOrderSellerId, currency.getIsoCode(), 1).get(0);
        orderFlowTestUtils.setProductPrice(productWithPriceInUSD01, currency.getIsoCode(), itemOnePrice);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.setCurrencyRate(currency.getIsoCode(), baseCurrencyCode,
                BigDecimal.valueOf(120_00, 2));
        Product productWithPriceInUSD02 = orderFlowTestUtils.getProductsForOrdersWithSeller(testOrderSellerId, currency.getIsoCode(), 1).get(0);
        orderFlowTestUtils.setProductPrice(productWithPriceInUSD02, currency.getIsoCode(), itemTwoPrice);
        commitAndStartNewTransaction();
        //
        List<Product> productsList = Lists.newArrayList(productWithPriceInUSD01, productWithPriceInUSD02);
        //
        GroupedCart cartInfo = orderFlowTestUtils.fillCart(productsList);
        Assertions.assertThat(cartInfo.getGroups()).hasSize(1);
        OrderDTO cartOrder = cartInfo.getGroup(productsList.get(0).getSeller().getId());
        Assertions.assertThat(cartOrder.getItems()).hasSize(2);
        //
        OrderPositionDTO itemOne = cartOrder.getItems().get(0);
        Assertions.assertThat(itemOne.getAmountInForeignCurrency()).isEqualByComparingTo(itemOnePrice);
        //
        OrderPositionDTO itemTwo = cartOrder.getItems().get(1);
        Assertions.assertThat(itemTwo.getAmountInForeignCurrency()).isEqualByComparingTo(itemTwoPrice);
        //
        //Assertions.assertThat(itemOne.getForeignCurrencyRate()).isEqualByComparingTo(itemTwo.getForeignCurrencyRate()); // Validate same rate in cart
        //
        OrderService.InitOrderResult testOrder = orderFlowTestUtils.holdOrderWithPromoCodePS(productsList.get(0).getSeller(), null, TcbBankService.SCHEMA);
        //
        Order order = orderService.getOrder(testOrder.getOrderId());
        Assertions.assertThat(order.getOrderPositions()).hasSize(2);
        BigDecimal rateOne = order.getOrderPositions().get(0).getForeignCurrencyRate();
        BigDecimal rateTwo = order.getOrderPositions().get(1).getForeignCurrencyRate();
        Assertions.assertThat(rateOne).isEqualByComparingTo(rateTwo); // Validate same rate in orderPosition
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _05_01_orderFlowCurrencies_sellPriceRoundNullPromoCodes() {
        List<Product> productsList = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(testOrderSellerId)
                .maxItems(2)
                .build()
        );
        Product productPriceRoundUp = productsList.get(0);
        Product productPriceRoundDn = productsList.get(1);
        productPriceRoundUp.setCurrentPrice(BigDecimal.valueOf(10_000_3552, 4));
        productPriceRoundDn.setCurrentPrice(BigDecimal.valueOf(20_000_4432, 4));
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.fillCart(ImmutableList.of(productPriceRoundUp, productPriceRoundDn));
        //
        OrderService.InitOrderResult testOrder = orderFlowTestUtils.holdOrderWithPromoCodePS(productPriceRoundUp.getSeller(), null, TcbBankService.SCHEMA);
        //
        orderFlowTestUtils.callOrderHoldCallback(testOrder.getOrderId(), testOrder.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        OrderDTO orderInfo = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getOrderId(), true);
        Assertions.assertThat(orderInfo.getId()).isPositive();
        //
        orderFlowTestUtils.validateOrderItems(orderInfo, productPriceRoundUp.getId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_36, 2));
            Assertions.assertThat(it.getFinalAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_36, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        orderFlowTestUtils.validateOrderItems(orderInfo, productPriceRoundDn.getId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(20_000_44, 2));
            Assertions.assertThat(it.getFinalAmount()).isEqualByComparingTo(BigDecimal.valueOf(20_000_44, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        Assertions.assertThat(orderInfo.getFinalAmount().subtract(orderInfo.getDeliveryCost())).isEqualByComparingTo(BigDecimal.valueOf(30_000_80, 2));
        //
        orderFlowTestUtils.validateOrderPositions(orderInfo.getId(), productPriceRoundUp.getId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_36, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_36, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        orderFlowTestUtils.validateOrderPositions(orderInfo.getId(), productPriceRoundDn.getId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(20_000_44, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(20_000_44, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        Order order = orderService.getOrder(orderInfo.getId());
        Assertions.assertThat(order.getAmount().subtract(order.getDeliveryCost())).isEqualByComparingTo(BigDecimal.valueOf(30_000_80, 2));
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _05_02_orderFlowCurrencies_sellPriceRoundWithPromoCodes_amounts() {
        String promoCodes = UUID.randomUUID().toString();
        orderFlowTestUtils.makeTestPromocodes(AbsolutePromoCode.class, promoCodes, BigDecimal.valueOf(1_000_00, 2));
        commitAndStartNewTransaction();
        //
        List<Product> productsList = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(testOrderSellerId)
                .maxItems(2)
                .build()
        );
        Product productPriceRoundUp = productsList.get(0);
        Product productPriceRoundDn = productsList.get(1);
        productPriceRoundUp.setCurrentPrice(BigDecimal.valueOf(10_000_3552, 4));
        productPriceRoundDn.setCurrentPrice(BigDecimal.valueOf(20_000_4432, 4));
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.fillCart(ImmutableList.of(productPriceRoundUp, productPriceRoundDn));
        //
        OrderService.InitOrderResult testOrder = orderFlowTestUtils.holdOrderWithPromoCodePS(productPriceRoundUp.getSeller(), promoCodes, TcbBankService.SCHEMA);
        //
        orderFlowTestUtils.callOrderHoldCallback(testOrder.getOrderId(), testOrder.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        OrderDTO orderInfo = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getOrderId(), true);
        Assertions.assertThat(orderInfo.getId()).isPositive();
        //
        orderFlowTestUtils.validateOrderItems(orderInfo, productPriceRoundUp.getId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_36, 2));
            Assertions.assertThat(it.getFinalAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_36, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(333_34, 2));
        });
        orderFlowTestUtils.validateOrderItems(orderInfo, productPriceRoundDn.getId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(20_000_44, 2));
            Assertions.assertThat(it.getFinalAmount()).isEqualByComparingTo(BigDecimal.valueOf(20_000_44, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(666_66, 2));
        });
        Assertions.assertThat(orderInfo.getFinalAmount().subtract(orderInfo.getDeliveryCost())).isEqualByComparingTo(BigDecimal.valueOf(29_000_80, 2));
        //
        orderFlowTestUtils.validateOrderPositions(orderInfo.getId(), productPriceRoundUp.getId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_36, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(9_667_02, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(333_34, 2));
        });
        orderFlowTestUtils.validateOrderPositions(orderInfo.getId(), productPriceRoundDn.getId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(20_000_44, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(19_333_78, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(666_66, 2));
        });
        Order order = orderService.getOrder(orderInfo.getId());
        Assertions.assertThat(order.getAmount().subtract(order.getDeliveryCost())).isEqualByComparingTo(BigDecimal.valueOf(29_000_80, 2));
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _05_03_orderFlowCurrencies_sellPriceRoundWithPromoCodes_percent() {
        String promoCodes = UUID.randomUUID().toString();
        orderFlowTestUtils.makeTestPromocodes(FractionalPromoCode.class, promoCodes, BigDecimal.valueOf(10, 2));
        commitAndStartNewTransaction();
        //
        List<Product> productsList = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(testOrderSellerId)
                .maxItems(2)
                .build()
        );
        Product productPriceRoundUp = productsList.get(0);
        Product productPriceRoundDn = productsList.get(1);
        productPriceRoundUp.setCurrentPrice(BigDecimal.valueOf(10_000_3552, 4));
        productPriceRoundDn.setCurrentPrice(BigDecimal.valueOf(20_000_4432, 4));
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.fillCart(ImmutableList.of(productPriceRoundUp, productPriceRoundDn));
        //
        OrderService.InitOrderResult testOrder = orderFlowTestUtils.holdOrderWithPromoCodePS(productPriceRoundUp.getSeller(), promoCodes, TcbBankService.SCHEMA);
        //
        orderFlowTestUtils.callOrderHoldCallback(testOrder.getOrderId(), testOrder.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        OrderDTO orderInfo = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getOrderId(), true);
        Assertions.assertThat(orderInfo.getId()).isPositive();
        //
        orderFlowTestUtils.validateOrderItems(orderInfo, productPriceRoundUp.getId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_36, 2));
            Assertions.assertThat(it.getFinalAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_36, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(1000_04, 2));
        });
        orderFlowTestUtils.validateOrderItems(orderInfo, productPriceRoundDn.getId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(20_000_44, 2));
            Assertions.assertThat(it.getFinalAmount()).isEqualByComparingTo(BigDecimal.valueOf(20_000_44, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(2_000_04, 2));
        });
        Assertions.assertThat(orderInfo.getFinalAmount().subtract(orderInfo.getDeliveryCost())).isEqualByComparingTo(BigDecimal.valueOf(27_000_72, 2));
        //
        orderFlowTestUtils.validateOrderPositions(orderInfo.getId(), productPriceRoundUp.getId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_36, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(9000_32, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(1000_04, 2));
        });
        orderFlowTestUtils.validateOrderPositions(orderInfo.getId(), productPriceRoundDn.getId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(20_000_44, 2));
            Assertions.assertThat(it.getHoldAmount()).isEqualByComparingTo(BigDecimal.valueOf(18_000_40, 2));
            Assertions.assertThat(it.getPromocodeAmount()).isEqualByComparingTo(BigDecimal.valueOf(2_000_04, 2));
        });
        Order order = orderService.getOrder(orderInfo.getId());
        Assertions.assertThat(order.getAmount().subtract(order.getDeliveryCost())).isEqualByComparingTo(BigDecimal.valueOf(27_000_72, 2));
    }

    private void resetAuthTimeForOrderPayments(long ordersId) {
        Order order = orderService.getOrder(ordersId);
        order.getOrderPayments().forEach(it -> it.setAuthorizeTime(null));
        orderService.saveOrder(order);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _06_orderFlowCurrencies_failIfPaymentsMiss() {
        List<Product> productsList = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(testOrderSellerId)
                .maxItems(1)
                .build()
        );
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.fillCart(productsList);
        //
        OrderService.InitOrderResult testOrder = orderFlowTestUtils.holdOrderWithPromoCodePS(productsList.get(0).getSeller(), null, TcbBankService.SCHEMA);
        //
        orderFlowTestUtils.callOrderHoldCallback(testOrder.getOrderId(), testOrder.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        resetAuthTimeForOrderPayments(testOrder.getOrderId());
        commitAndStartNewTransaction();
        //
        OrderDTO orderInfo = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getOrderId(), true);
        Assertions.assertThat(orderInfo.getId()).isNotEqualTo(0);
        //
        orderInfo.getItems().forEach(op -> orderFlowTestUtils.rejectOrApprovePosition(op.getId(), true));
        orderFlowTestUtils.changeAddressEndpoint(orderInfo.getId(), testOrderPickupId, testOrderDeliveryId);
        //
        OrderFlowTestUtils.OskellyDeliveryInfo deliveryInfo = OrderFlowTestUtils.OskellyDeliveryInfo.builder()
                .courierName("S2O-Courier").courierPhone("**********").courierDate(LocalDate.now()).callWillFail(true)
                .build();
        //
        ResponseEntity<String> sendCallFailData = orderFlowTestUtils.takeOurselves(orderInfo.getId(), deliveryInfo);
        Exception sendCallFailInfo = orderFlowTestUtils.readExceptionFromText(sendCallFailData.getBody());
        Assertions.assertThat(sendCallFailInfo.getMessage()).matches("Заказ .*: не удается подтвердить заказ, отсутствует информация об оплате");
    }

}