package ru.oskelly.tests.pr.suite6_1.orderflow;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.user.UserService;

@Component
@RequiredArgsConstructor
public class OrderFlowTestFixtures {

    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;

    @Autowired
    private UserService userService;

    public User prepareBoutiqueUser(long userId, String password) {
        User user = userService.getUserById(userId).orElseThrow(IllegalArgumentException::new);
        userService.setPassword(user, password);
        userService.save(user);
        orderFlowTestUtils.enableUserAuthority(user.getId(), AuthorityName.ORDER_MODERATION, true);
        orderFlowTestUtils.enableUserAuthority(user.getId(), AuthorityName.ORDER_PAYOUTS, true);
        orderFlowTestUtils.enableUserAuthority(user.getId(), AuthorityName.ORDER_RETURNS, true);
        orderFlowTestUtils.enableUserAuthority(user.getId(), AuthorityName.ORDER_RETURN_COMPLETED, true);
        orderFlowTestUtils.enableUserAuthority(user.getId(), AuthorityName.BOUTIQUE_SALES, true);
        orderFlowTestUtils.enableUserAuthority(user.getId(), AuthorityName.PRODUCT_MODERATION, true);
        orderFlowTestUtils.enableUserAuthority(user.getId(), AuthorityName.SPLIT_ORDER, true);
        orderFlowTestUtils.enableUserAuthority(user.getId(), AuthorityName.ORDER_BOUTIQUE_1ST_ACTION, true);
        orderFlowTestUtils.enableUserAuthority(user.getId(), AuthorityName.ORDER_MANUAL_CHANGE_DELIVERY_STATE, true);
        orderFlowTestUtils.enableUserAuthority(user.getId(), AuthorityName.ORDER_SELLER_CONCIERGE_MOVE_TO_BOUTIQUE, true);
        return user;
    }


    @RequiredArgsConstructor
    public enum OnecEntityType {

        ONEC_ENTITY_GROUP("a030ce05-238c-11ed-96e2-ea8acf8eb23d"),
        ONEC_ENTITY_CONCIERGE("a030ce0c-238c-11ed-96e2-ea8acf8eb23d");

        @Getter
        private final String onecUuid;

    }

}
