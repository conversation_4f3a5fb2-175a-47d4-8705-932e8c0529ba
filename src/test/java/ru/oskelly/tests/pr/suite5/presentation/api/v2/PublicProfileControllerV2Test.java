package ru.oskelly.tests.pr.suite5.presentation.api.v2;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.TestApiConfiguration;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.PublicProfileDTO;
import su.reddot.domain.service.dto.UserDTO;
import su.reddot.domain.service.following.FollowingService;
import su.reddot.domain.service.user.UserService;
import su.reddot.presentation.api.v2.Api2Response;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_05)
public class PublicProfileControllerV2Test extends AbstractSpringTest {
    @Autowired
    private TestApiConfiguration testApiConfiguration;
    @Value("${test.api.user-email}")
    private String email;
    @Value("${test.api.user-password}")
    private String password;
	@Value("${test.api.user-id}")
	private Long userId;
	@Value("${test.api.user2-id}")
	private Long user2Id;
	@Value("${test.api.user3-id}")
	private Long user3Id;
	@Value("${test.api.user4-id}")
	private Long user4Id;
	@Value("${test.api.celebrity-user-id}")
	private Long celebrityId;

	//Пользователь от имени которого будут проводиться запросы
	private static User user;

	//ID продавца, чей профиль буем дергать
	private static Long profileId;

	//ID случайного подписчика на продавца. На него мы тоже будем подписываться.
	private static Long followerId;

	//ID случайного подписанта продавца (на кого подписан продавец). На него мы тоже будем подписываться.
	private static Long followingId;

	@Autowired
	private UserService userService;
	@Autowired
	private FollowingService followingService;

    static ApiV2Client apiV2Client;

    @BeforeEach
    public void initialize() {
        if(apiV2Client == null) apiV2Client = new ApiV2Client(email, password);
		if (profileId == null) {
			profileId = user4Id;
			followingService.follow(user4Id, user3Id);
			followingService.follow(user2Id, user4Id);
		}
        if(user == null){
        	user = getUser();
        	followingService.unfollowAll(user);
        }
    }

	private User getUser(){
		return userService.getUserById(userId).orElse(null);
	}

    private String getServiceUrl(){
        return testApiConfiguration.getServerUrl() + "/api/v2/publicprofile";
    }

	private String getProfileUrl(Long userId){
		return getServiceUrl() + "/" + userId;
	}

	private String getFollowersUrl() {return getServiceUrl() + "/followers";}

	private String getFollowingsUrl() {return getServiceUrl() + "/followings";}

	private String getFollowingToggleUrl() {return getServiceUrl() + "/following/toggle";}

	private String getFollowUrl(Long userId) {return getServiceUrl() + "/following/" + userId;}

	//Обращение к основному запросу профиля выдает данные.
	//Эти же данные выдаются при бращении к отдельным запросам по спискам подписчиков/подписантов
    @Test
    public void _00_getProfileSuccessful(){
    	//Не авторизован. Успешное получение профиля без доп. полей
	    getProfileSuccessful(profileId, false, false, false, false, false);
	    //Не авторизован. Успешное получение профиля с доп. полями
	    getProfileSuccessful(profileId, true, true, true, false, false);

	    //Авторизация. Успешное получение профиля без доп. полей
	    getProfileSuccessful(profileId, false, false, false, true, false);

	    //Авторизован (предыдущим запросом). Успешное получение профиля с доп. полями
		PublicProfileDTO profile = getProfileSuccessful(profileId, true, true, true, false, false);

	    //ID первого подписчика продавца
	    followerId = profile.getFollowers().get(0).getId();

	    //ID первого подписанта продавца (на кого подписан сам продавец)
	    followingId = profile.getFollowings().get(0).getId();

	    //На этот момент мы еще не подписаны на данного пользователя
	    assertFalse(profile.getIsFollowed());

	    //Получение подписчиков отдельным списком
	    Set<Long> followers = toUserIdSet(getUsersListSuccessful(getFollowersUrl(), profileId));

	    //Получение подписантов отдельным списком
		Set<Long> followings = toUserIdSet(getUsersListSuccessful(getFollowingsUrl(), profileId));

	    //Списки подписчиков/подписантов отдельных и тех, что в профиле не отличаются
	    assertEquals(followers, toUserIdSet(profile.getFollowers()));
	    assertEquals(followings, toUserIdSet(profile.getFollowings()));
    }

	@Test
	public void _01_getCelebrityProfileSuccessful(){
		// Проверка того, что для селебрити в профиле возвращается модель селебрити
		getProfileSuccessful(celebrityId, false, false, false, false, true);
	}

	private static Set<Long> toUserIdSet(List<UserDTO> users) {
		return users.stream().map(UserDTO::getId).collect(Collectors.toSet());
	}

    //Проверка подписки/отписки при помощи toggle
    @Test
	public void _01_testToggleFollowing(){
	    testFollowing(true);
    }

	//Проверка подписки/отписки при помощи раздельных методов
	@Test
	public void _02_testFollowingAndUnfollowing(){
		testFollowing(false);
	}

	private PublicProfileDTO getProfileSuccessful(Long userId, boolean withFollowers, boolean withFollowings,
												  boolean withCity, boolean withAuthorizeParams,
												  boolean isCelebrity){
		Map<String, String> params = new HashMap<>();
		if(withFollowers) params.put("withFollowers", "true");
		if(withFollowings) params.put("withFollowings", "true");
		if(withCity) params.put("withCity", "true");
		ResponseEntity<Api2Response<PublicProfileDTO>> response = apiV2Client.request(getProfileUrl(userId), params, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<PublicProfileDTO>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		assertNotNull(response.getBody().getData());
		PublicProfileDTO profile = response.getBody().getData();
		//Если запрашивали подписчиков, то они есть
		if (withFollowers) {
			assertFalse(CollectionUtils.isEmpty(profile.getFollowers()));
		} else {
			assertTrue(CollectionUtils.isEmpty(profile.getFollowers()));
		}
		//Если запрашивали подписантов, то они есть
		if (withFollowings) {
			assertFalse(CollectionUtils.isEmpty(profile.getFollowings()));
		} else {
			assertTrue(CollectionUtils.isEmpty(profile.getFollowings()));
		}
		//Если запрашивали город, то он есть
		if (withCity) {
			assertFalse(StringUtils.isEmpty(profile.getCity()));
		} else {
			assertTrue(StringUtils.isEmpty(profile.getCity()));
		}

		if(isCelebrity) {
			assertNotNull(profile.getSocialAccount());
		} else {
			assertNull(profile.getSocialAccount());
		}

		assertEquals("http://localhost:8080/profile/" + userId, profile.getLink());
		return profile;
	}

	private List<UserDTO> getUsersListSuccessful(String url, Long userId){
		ResponseEntity<Api2Response<List<UserDTO>>> response = apiV2Client.request(url, TestUtils.getOneParamAsMap("userId", userId.toString()), HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<UserDTO>>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		assertNotNull(response.getBody().getData());
		return response.getBody().getData();
	}

	private Boolean toggleFollowing(Long userId){
		ResponseEntity<Api2Response<Boolean>> response = apiV2Client.request(getFollowingToggleUrl(), TestUtils.getOneParamAsMap("userId", userId.toString()), HttpMethod.POST, null, new ParameterizedTypeReference<Api2Response<Boolean>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		assertTrue(response.getBody().getMessage().startsWith("Подписка"));
		assertNotNull(response.getBody().getData());
		return response.getBody().getData();
	}

	//Подписывает/отписывает авторизованного пользователя на/от указанного пользователя
	private Boolean followRequestSuccessful(Long userId, HttpMethod method){
		ResponseEntity<Api2Response<Boolean>> response = apiV2Client.request(getFollowUrl(userId), null, method, null, new ParameterizedTypeReference<Api2Response<Boolean>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		assertTrue(response.getBody().getData());
		return response.getBody().getData();
	}

	//Проверка подписки на профиль и пользователей из его подскисков подписчиков подписантов
	private void checkFollowingToProfileAndFirstSublistsUsers(Long userId, Long followerId, Long followingId, boolean isFollowed){
		//Получаем профиль со вложенными списками
		PublicProfileDTO profile = getProfileSuccessful(userId, true, true, false, false, false);
		UserDTO follower = profile.getFollowers().stream().filter(u -> u.getId().equals(followerId)).findFirst().orElse(null);
		UserDTO following = profile.getFollowings().stream().filter(u -> u.getId().equals(followingId)).findFirst().orElse(null);

		//Проверяем наличие подписки
		assertEquals(isFollowed, profile.getIsFollowed());
		assertEquals(isFollowed, follower.getIsFollowed());
		assertEquals(isFollowed, following.getIsFollowed());
	}

	private void follow(Long userId, boolean useToggle){
		if(useToggle) {
			Boolean result = toggleFollowing(userId);
			assertEquals(true, result);
		}
		else{
			followRequestSuccessful(userId, HttpMethod.PUT);
		}
	}

	private void unfollow(Long userId, boolean useToggle){
		if(useToggle) {
			Boolean result = toggleFollowing(userId);
			assertEquals(false, result);
		}
		else{
			followRequestSuccessful(userId, HttpMethod.DELETE);
		}
	}

	//Проверка подписки с помощью toggle или раздельных методов
	private void testFollowing(boolean useToggle){
		//Сначала отписываемся от всех
		followingService.unfollowAll(user);

		//Подписываться будем на профиль, подписчика и подписанта.

		//Сейчас мы на них не подписаны
		checkFollowingToProfileAndFirstSublistsUsers(profileId, followerId, followingId, false);

		//Подписываемся
		follow(profileId, useToggle);
		follow(followerId, useToggle);
		follow(followingId, useToggle);

		//Теперь мы на них подписаны
		checkFollowingToProfileAndFirstSublistsUsers(profileId, followerId, followingId, true);

		//Отписываемся
		unfollow(profileId, useToggle);
		unfollow(followerId, useToggle);
		unfollow(followingId, useToggle);

		//Мы снова не подписаны на них
		checkFollowingToProfileAndFirstSublistsUsers(profileId, followerId, followingId, false);
	}

}
