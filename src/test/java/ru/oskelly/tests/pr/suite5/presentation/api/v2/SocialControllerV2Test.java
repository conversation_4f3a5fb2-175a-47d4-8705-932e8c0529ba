package ru.oskelly.tests.pr.suite5.presentation.api.v2;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.TestApiConfiguration;
import su.reddot.domain.service.dto.social.InstagramAccountDTO;
import su.reddot.domain.service.dto.social.SocialAccountDTO;
import su.reddot.presentation.api.v2.Api2Response;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_05)
public class SocialControllerV2Test extends AbstractSpringTest {
    @Autowired
    private TestApiConfiguration testApiConfiguration;
    @Value("${test.api.user-email}")
    private String email;
    @Value("${test.api.user-password}")
    private String password;
	@Value("${test.api.user-id}")
	private Long userId;

	//Клиент для работы с существующим тестовым пользователем
    static ApiV2Client apiV2Client;

    @BeforeEach
    public void initialize() {
        if(apiV2Client == null){
        	apiV2Client = new ApiV2Client(email, password);
        }
    }

    private String getServiceUrl(){
        return testApiConfiguration.getServerUrl() + "/api/v2/social";
    }

	private String getSocialAccountsUrl(){
		return getServiceUrl() + "/accounts";
	}
	private String getInstagramSupportAccountUrl(){
		return getServiceUrl() + "/accounts/instagram/support";
	}

    @Test
    public void _0_socialAccounts_guest_OK(){
        ResponseEntity<Api2Response<List<SocialAccountDTO>>> response = apiV2Client.request(getSocialAccountsUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<SocialAccountDTO>>>() {}, false);
	    assertTrue(response.getStatusCode().is2xxSuccessful());
	    assertFalse(response.getBody().getData().isEmpty());
    }

	@Test
	public void _0_instagramSupportAccount_guest_OK(){
		ResponseEntity<Api2Response<InstagramAccountDTO>> response = apiV2Client.request(getInstagramSupportAccountUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<InstagramAccountDTO>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertFalse(response.getBody().getData().getInstagramOptions().isEmpty());
	}

	@Test
	public void _1_socialAccounts_user_OK(){
		ResponseEntity<Api2Response<List<SocialAccountDTO>>> response = apiV2Client.request(getSocialAccountsUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<SocialAccountDTO>>>() {}, true);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertFalse(response.getBody().getData().isEmpty());
	}

	@Test
	public void _1_instagramSupportAccount_user_OK(){
		ResponseEntity<Api2Response<InstagramAccountDTO>> response = apiV2Client.request(getInstagramSupportAccountUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<InstagramAccountDTO>>() {}, true);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertFalse(response.getBody().getData().getInstagramOptions().isEmpty());
	}


}
