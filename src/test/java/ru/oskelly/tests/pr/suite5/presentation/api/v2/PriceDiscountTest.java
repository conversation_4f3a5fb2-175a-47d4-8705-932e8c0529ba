package ru.oskelly.tests.pr.suite5.presentation.api.v2;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.ToString;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.util.UriComponentsBuilder;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.TestApiConfiguration;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.address.CurrencyRepository;
import su.reddot.domain.dao.commission.CommissionRepository;
import su.reddot.domain.dao.currency.CurrencyRateRepository;
import su.reddot.domain.dao.product.PriceDiscountCycleRepository;
import su.reddot.domain.dao.product.PriceDiscountRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.address.Currency;
import su.reddot.domain.model.currency.CurrencyRate;
import su.reddot.domain.model.product.PriceDiscount;
import su.reddot.domain.model.product.PriceDiscountCycle;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.SalesChannel;
import su.reddot.domain.service.adminpanel.product.PriceDiscountService;
import su.reddot.domain.service.adminpanel.product.impl.PriceDiscountSettings;
import su.reddot.domain.service.currency.CurrencyRateService;
import su.reddot.domain.service.currency.CurrencyService;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.presentation.api.v2.Api2Response;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_05)
public class PriceDiscountTest extends AbstractSpringTest {

    @Autowired
    private TestApiConfiguration testApiConfiguration;
    @Value("${test.api.user-id}")
    private Long userId;
    @Value("${test.api.user-email}")
    private String email;
    @Value("${test.api.user-password}")
    private String password;
    private Long publishedProductId;

    private static List<String> cookie = new ArrayList<>();

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ProductRepository productRepository;

    @Autowired
    private PriceDiscountService priceDiscountService;

    @Autowired
    private PriceDiscountCycleRepository cycleRepository;

    @Autowired
    private PriceDiscountRepository priceDiscountRepository;

    @Autowired
    private PriceDiscountSettings priceDiscountSettings;

    @Autowired
    private CurrencyRateService currencyRateService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private CommissionRepository commissionRepository;

    @Autowired
    private CurrencyRateRepository currencyRateRepository;

    @Autowired
    private CurrencyRepository currencyRepository;

    @Autowired
    private CurrencyService currencyService;

    private TestRestTemplate restTemplate = TestUtils.getTestRestTemplate();

    private BigDecimal eurRateValue;

    private static final BigDecimal TEST_EUR_RATE = new BigDecimal("100.00");

    private final static Map<Integer, PriceContainerDTO> CURRENCY_DISCOUNT_COMMISSION_CHANGE_MAP = new HashMap<>();
    private final static Map<Integer, PriceContainerDTO> DISCOUNT_COMMISSION_CHANGE_MAP = new HashMap<>();
    private final static Map<Integer, PriceContainerDTO> DISCOUNT_MAP = new HashMap<>();

    static {
        //публикация 1345.00 eur
        //суммы для курса евро 100
        //комиссия 0.16
        CURRENCY_DISCOUNT_COMMISSION_CHANGE_MAP.put(1, new PriceContainerDTO(new BigDecimal("150000.00"),
                new BigDecimal("126000.00"), new BigDecimal("1210.50")));
        //тут корректируется сумма
        //комиссия 0.20
        CURRENCY_DISCOUNT_COMMISSION_CHANGE_MAP.put(2, new PriceContainerDTO(new BigDecimal("137550.00"),
                new BigDecimal("110034.00"), new BigDecimal("1089.45")));

        //------------------

        //публикация 170000.00 rub
        //комиссия 0.16
        DISCOUNT_COMMISSION_CHANGE_MAP.put(1, new PriceContainerDTO(new BigDecimal("153000.00"),
                new BigDecimal("128520.00"), null));
        //комиссия 0.20
        DISCOUNT_COMMISSION_CHANGE_MAP.put(2, new PriceContainerDTO(new BigDecimal("137700.00"),
                new BigDecimal("110160.00"), null));

        //------------------

        //публикация 120000.00 rub
        //комиссия 0.20
        DISCOUNT_MAP.put(1, new PriceContainerDTO(new BigDecimal("108000.00"),
                new BigDecimal("86400.00"), null));
        //комиссия 0.20
        DISCOUNT_MAP.put(2, new PriceContainerDTO(new BigDecimal("97200.00"),
                new BigDecimal("77760.00"), null));
    }

    @BeforeEach
    public void saveEurRate() {
        eurRateValue = updateEurRateAndReturnOld(TEST_EUR_RATE);
    }

    private BigDecimal updateEurRateAndReturnOld(BigDecimal newEurRate) {
        Long baseCurrencyId = currencyService.getBaseCurrency().getId();

        Optional<Currency> eur = currencyRepository.findByIsoCode("EUR");
        CurrencyRate eurRate = currencyRateRepository.findByCurrencyIdAndCurrencyToId(eur.get().getId(),
                baseCurrencyId);
        BigDecimal oldRateValue = eurRate.getRateValue();

        eurRate.setRateValue(newEurRate);
        currencyRateRepository.save(eurRate);

        return oldRateValue;
    }

    @Data
    @AllArgsConstructor
    @ToString
    private static class PriceContainerDTO {
        private BigDecimal currentPrice;
        private BigDecimal currentPriceWithoutCommission;
        private BigDecimal currentPriceInCurrency;
    }

    @Test
    public void testSimpleDiscount() {
        BigDecimal price = new BigDecimal("120000.00");
        Long productId = publishSimpleProduct(price);

        testDiscount(productId, DISCOUNT_MAP);
    }
    @Test
    public void testSimpleDiscountWithCommissionChange() {
        BigDecimal price = new BigDecimal("170000.00");
        Long productId = publishSimpleProduct(price);

        testDiscount(productId, DISCOUNT_COMMISSION_CHANGE_MAP);
    }

    @Test
    public void testCurrencyDiscountWithCommissionChange() {
        BigDecimal price = new BigDecimal("1345.00");
        Long productId = publishProductWithCurrency(price);

        testDiscount(productId, CURRENCY_DISCOUNT_COMMISSION_CHANGE_MAP);
    }

    private void testDiscount(Long productId, Map<Integer, PriceContainerDTO> assertMap) {
        //Создание "скидки"
        priceDiscountService.startDiscount(productId, userId);

        //цикл должен создаться, но скидка сразу не должна произойти
        Optional<PriceDiscountCycle> priceDiscountCycleOptional = cycleRepository.findTopByProductIdOrderByCreateDateDesc(productId);
        assertTrue(priceDiscountCycleOptional.isPresent());

        PriceDiscountCycle priceDiscountCycle = priceDiscountCycleOptional.get();
        assertEquals(productId, priceDiscountCycle.getProductId());
        assertEquals(userId, priceDiscountCycle.getStartModeratorId());
        assertEquals(0, priceDiscountCycle.getCount());
        assertNull(priceDiscountCycle.getStopDate());

        List<PriceDiscount> discounts = priceDiscountRepository.findAllByCycleIdOrderByCreateDateAsc(priceDiscountCycle.getId());
        assertEquals(0, discounts.size());

        //попытка дернуть "планировщик"
        priceDiscountService.discount();
        //т.к. прошло очень мало времени, "скидки" пока быть не должно
        discounts = priceDiscountRepository.findAllByCycleIdOrderByCreateDateAsc(priceDiscountCycle.getId());
        assertEquals(0, discounts.size());

        //запускаем цикл скидок
        Integer cyclesCount = priceDiscountSettings.getMaxCount();
        Long durationMills = Duration.of(priceDiscountSettings.getPeriod(),
                priceDiscountSettings.getPeriodChronoUnit()).toMillis() + 100;

        //пытаемся снизить цену столько раз, сколько указано в настройках
        for (int i = 0; i < cyclesCount; i++) {
            sleep(durationMills);

            priceDiscountService.discount();
        }

        discounts = priceDiscountRepository.findAllByCycleIdOrderByCreateDateAsc(priceDiscountCycle.getId());
        assertEquals(cyclesCount, discounts.size());

        Product product = productRepository.getById(productId);

        for (int i = 1; i <= cyclesCount; i++) {
            PriceContainerDTO priceContainer = assertMap.get(i);
            PriceDiscount priceDiscount = discounts.get(i - 1);

            assertEquals(0, priceContainer.getCurrentPrice().compareTo(priceDiscount.getNewCurrentPrice()));
            assertEquals(0, priceContainer.getCurrentPriceWithoutCommission().compareTo(priceDiscount.getNewCurrentPriceWithoutCommission()));

            if (product.getCurrentPriceCurrencyId() != null) {
                assertEquals(0, priceContainer.getCurrentPriceInCurrency().compareTo(priceDiscount.getNewCurrentPriceInCurrency()));
            }
        }

        PriceDiscount lastDiscount = discounts.get(discounts.size() - 1);

        assertEquals(0, product.getCurrentPrice().compareTo(lastDiscount.getNewCurrentPrice()));
        assertEquals(0, product.getCurrentPriceWithoutCommission().compareTo(lastDiscount.getNewCurrentPriceWithoutCommission()));

        if (product.getCurrentPriceCurrencyId() != null) {
            assertEquals(0, product.getCurrentPriceInCurrency().compareTo(lastDiscount.getNewCurrentPriceInCurrency()));
        }

        //после завершения цикла скидок пытаемся еще раз дернуть планировщик - не должно быть никаких изменений
        //попытка дернуть "планировщик"
        sleep(durationMills);
        priceDiscountService.discount();

        priceDiscountCycle = cycleRepository.findById(priceDiscountCycle.getId()).get();

        assertNotNull(priceDiscountCycle.getStopDate());
        assertEquals(cyclesCount, priceDiscountCycle.getCount());

        discounts = priceDiscountRepository.findAllByCycleIdOrderByCreateDateAsc(priceDiscountCycle.getId());
        assertEquals(cyclesCount, discounts.size());
    }

    private void sleep(Long mills) {
        try {
            Thread.sleep(mills);
        } catch (Throwable t) {
            //ignored
        }
    }

    private Long publishSimpleProduct(BigDecimal price) {
        ProductDTO productDTO = new ProductDTO();
        productDTO.setBrandId(3L);
        productDTO.setCategoryId(154L);
        productDTO.setSalesChannel(SalesChannel.WEBSITE);
        productDTO.setPrice(price);

        return publishProduct(productDTO);
    }

    private Long publishProductWithCurrency(BigDecimal price) {
        ProductDTO productDTO = new ProductDTO();
        productDTO.setBrandId(3L);
        productDTO.setCategoryId(154L);
        productDTO.setSalesChannel(SalesChannel.WEBSITE);

        Long baseCurrencyId = currencyService.getBaseCurrency().getId();

        BigDecimal priceInRub = price.multiply(currencyRateService.findCurrencyRateWithCurrencyIdAndCurrencyToId(2L,
                baseCurrencyId).getRateValue());
        productDTO.setPrice(priceInRub);
        productDTO.setCurrentPriceInCurrency(price);
        //euro
        productDTO.setCurrentPriceCurrencyId(2L);

        return publishProduct(productDTO);
    }

    private Long publishProduct(ProductDTO productDTO) {
        ResponseEntity<Api2Response<Long>> response = restTemplate.exchange(getUriWithParams(getPublishUrl(), getAuthorizeParams()),
                HttpMethod.POST, getHttpEntity(productDTO), new ParameterizedTypeReference<Api2Response<Long>>() {
                });

        publishedProductId = response.getBody().getData();
        return publishedProductId;
    }

    @AfterEach
    public void clean() {
        updateEurRateAndReturnOld(eurRateValue);

        if (publishedProductId == null) {
            return;
        }

        List<PriceDiscountCycle> cycles = cycleRepository.findAllByProductId(publishedProductId);

        cycles.forEach(cycle -> priceDiscountRepository.deleteAll(priceDiscountRepository.findAllByCycleIdOrderByCreateDateAsc(cycle.getId())));

        cycleRepository.deleteAll(cycles);

        productRepository.delete(productRepository.getById(publishedProductId));
    }

    private HttpEntity getHttpEntity(Object body) {
        return body == null ? new HttpEntity<String>(getHeaders(null)) : new HttpEntity<Object>(body, getHeaders(null));
    }

    private String getPublishAndGetResultUrl() {
        return testApiConfiguration.getServerUrl() + "/api/v2/productpublication/publishAndGetResult";
    }

    private String getPublishUrl() {
        return testApiConfiguration.getServerUrl() + "/api/v2/productpublication/publish";
    }

    private String getProductPATCHUrl() {
        return testApiConfiguration.getServerUrl() + "/api/v3/admin/product/edit";
    }

    private HttpEntity getHttpEntity(Object body, MediaType mediaType) {
        return body == null ? new HttpEntity<String>(getHeaders(mediaType)) : new HttpEntity<Object>(body, getHeaders(mediaType));
    }

    private HttpHeaders getHeaders(MediaType mediaType) {
        HttpHeaders result = new HttpHeaders();
        if (mediaType != null) result.setContentType(mediaType);
        ApiV2Client.defaultHeaders.forEach((k, v) -> result.set(k, v));
        cookie.forEach(s -> {
            result.add(HttpHeaders.COOKIE, s);
        });
        return result;
    }

    private String getUriWithParams(String url, Map<String, String> params) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(url);
        params.forEach(builder::queryParam);
        return builder.toUriString();
    }

    private Map<String, String> getAuthorizeParams() {
        Map<String, String> result = new HashMap<>();
        result.put("email", email);
        result.put("password", TestUtils.getApiHashedPassword(password, email));
        return result;
    }
}
