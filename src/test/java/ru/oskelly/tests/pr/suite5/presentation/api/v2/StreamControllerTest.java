package ru.oskelly.tests.pr.suite5.presentation.api.v2;

/*
 * Created by <PERSON>
 */

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.security.core.Authentication;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.AccountTestSupport;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.notification.NotificationRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.dao.streamsale.StreamFollowingRepository;
import su.reddot.domain.dao.streamsale.StreamRepository;
import su.reddot.domain.dao.userban.UserBanRepository;
import su.reddot.domain.model.notification.streamsale.*;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.streamsale.Stream;
import su.reddot.domain.model.streamsale.StreamFollowing;
import su.reddot.domain.model.streamsale.enums.StreamCover;
import su.reddot.domain.model.streamsale.enums.StreamStatus;
import su.reddot.domain.model.user.User;
import su.reddot.domain.model.user.userban.BanType;
import su.reddot.domain.model.user.userban.UserBan;
import su.reddot.domain.service.comment.product.ProductCommentService;
import su.reddot.domain.service.dto.streamsale.StreamCreateOrUpdateRequestDTO;
import su.reddot.domain.service.dto.streamsale.StreamDetailedResponseDTO;
import su.reddot.domain.service.dto.streamsale.StreamProductDTO;
import su.reddot.domain.service.dto.userban.UserBanActionDTO;
import su.reddot.domain.service.dto.userban.UserBanDTO;
import su.reddot.domain.service.following.FollowingService;
import su.reddot.domain.service.like.LikeService;
import su.reddot.domain.service.task.ScheduledNotificationRunner;
import su.reddot.domain.service.product.ProductService;
import su.reddot.domain.service.streamsale.StreamService;
import su.reddot.domain.service.subscription.SubscriptionService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.security.SecurityService;
import su.reddot.presentation.api.v2.Api2Response;
import su.reddot.presentation.api.v2_1.Api2_1Response;
import su.reddot.presentation.api.v2_1.ErrorType;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@EnableAsync
@AutoConfigureMockMvc
@ConditionalOnProperty("app.streamsale.enabled")
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_05)
// стримы отключены
@Disabled
public class StreamControllerTest extends AbstractSpringTest {

    @Autowired
    private SecurityService securityService;

    @Autowired
    private StreamService streamService;

    @Autowired
    private StreamRepository streamRepository;

    @Autowired
    private StreamFollowingRepository streamFollowingRepository;

    @Autowired
    private ProductRepository productRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private FollowingService followingService;

    @Autowired
    private ProductService productService;

    @Autowired
    private ProductCommentService commentService;

    @Autowired
    private LikeService likeService;

    @Autowired
    private SubscriptionService subscriptionService;

    @Autowired
    private NotificationRepository notificationRepository;

    @Autowired
    private ScheduledNotificationRunner scheduledNotificationRunner;

    @Autowired
    private UserBanRepository userBanRepository;

    @Autowired
    private AccountTestSupport accountTestSupport;

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private WebApplicationContext context;

    @Autowired
    private OpenEntityManagerInViewFilter openEntityManagerInViewFilter;

    static ApiV2Client apiV2Client;

    private ObjectMapper mapper;

    @Value("${test.api.user-email}")
    private String adminEmail;
    @Value("${test.api.user-password}")
    private String adminPassword;
    @Value("${test.api.user-id}")
    private Long adminUserId;
    @Value("${app.streamsale.broadcast-id}")
    private String testStreamBroadcastId;

    private static ProductRepository staticProductRepository;
    private static UserRepository staticUserRepository;

    private static final Map<String, TestUser> NEW_USER_LOGIN_PASS = new HashMap<>();
    private static final Map<Long, List<Product>> OLD_PRODUCT_BY_USER = new HashMap<>();
    public static final String TEST_DESCRIPTION = "description";
    public static final String TEST_TITLE = "title";
    public static final StreamCover TEST_COVER = StreamCover.COVER_1;
    public static final String PATH_PREFIX = "/api/v2/streamsale";

    @BeforeEach
    public void init() throws Exception {

        //Инициализация статических полей для их использование
        //в методе аннотированным @AfterClass для возврата продуктов
        //в начальное состояние в самом конце всех тестов
        if (staticProductRepository == null) {
            staticProductRepository = productRepository;
        }
        if (staticUserRepository == null) {
            staticUserRepository = userRepository;
        }
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.context).addFilters(openEntityManagerInViewFilter).build();
        if (mapper == null) {
            mapper = new ObjectMapper();
            mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
            mapper.registerModule(new JavaTimeModule());
        }
        if (NEW_USER_LOGIN_PASS.size() != 5) {
            for (int i = 1; i < 6; i++) {
                User user = userRepository.findByNickname("david_test_" + i);
                if (user == null) {
                    createNewUser("david_test_" + i, i);
                } else {
                    NEW_USER_LOGIN_PASS.put("david_test_" + i, new TestUser(user.getId(), "david_test_" + i + "@david.am", "12345!", user.getNickname()));
                }
            }
        }
        resetAndClearChangeData();
    }

    @AfterEach
    public void destroy() {
        resetAndClearChangeData();
    }

    @AfterAll
    public static void rollBackProductData() {
        if (!OLD_PRODUCT_BY_USER.isEmpty()) {
            List<Product> productList = new ArrayList<>();
            OLD_PRODUCT_BY_USER.forEach((userId, products) -> {
                staticUserRepository.findById(userId)
                        .ifPresent(user -> {
                            products.forEach(product -> product.setSeller(user));
                            productList.addAll(products);
                        });
            });
            staticProductRepository.saveAll(productList);
            OLD_PRODUCT_BY_USER.clear();
        }
    }

    @Test
    public void createRightStreamTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);
        assertEquals(TEST_COVER, dto.getCover());
        assertEquals(TEST_TITLE, dto.getTitle());
        assertEquals(TEST_DESCRIPTION, dto.getDescription());
        assertEquals(StreamStatus.ANNOUNCE, dto.getStatus());
        assertNotNull(dto.getProducts());
        assertFalse(dto.getProducts().isEmpty());
        assertEquals(5, dto.getProducts().size());
        List<Long> actualProductIds = dto.getProducts().stream().map(StreamProductDTO::getId).collect(Collectors.toList());

        for (Long productId : productIds) {
            assertTrue(actualProductIds.contains(productId));
        }

        assertNotNull(dto.getAuthor());
        assertEquals(dto.getAuthor().getId(), NEW_USER_LOGIN_PASS.get("david_test_1").getId());
        assertEquals(dto.getAuthor().getNickName(), NEW_USER_LOGIN_PASS.get("david_test_1").getNickName());

        assertTrue(dto.getCanDelete());
        assertTrue(dto.getCanUpdate());
        assertFalse(dto.getCanStart());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void createStreamWhenUserHaveNotAccessTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        removeUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });
        assertNotNull(response);
        assertNull(response.getData());
        assertNotNull(response.getError());
        assertEquals("Вы не можете создать эфир. Чтобы открыть возможность проведения эфиров, вам необходимо опубликовать 50 товаров и совершить 10 успешных продаж на платформе OSKELLY", response.getError().getHumanMessage());

        logout(authentication);
    }

    @Test
    public void createStreamWhenStreamCoverNullTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(null, TEST_TITLE, TEST_DESCRIPTION, productIds, 3L, null, null, null, 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(response);
        assertNull(response.getData());
        assertNotNull(response.getError());
        assertEquals("Cover : null", response.getError().getData());
        assertEquals("Вы должны добавить обложку для эфира", response.getError().getHumanMessage());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void createStreamWhenStreamTitleNullOrEmptyTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(TEST_COVER, null, TEST_DESCRIPTION, productIds, 3L, null, null, null, 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(response);
        assertNull(response.getData());
        assertNotNull(response.getError());
        assertEquals("Title : null", response.getError().getData());
        assertEquals("Вы должны добавить название для эфира", response.getError().getHumanMessage());

        result = createStream(TEST_COVER, "", TEST_DESCRIPTION, productIds, 3L, null, null, null, 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(response);
        assertNull(response.getData());
        assertNotNull(response.getError());
        assertEquals("Title : null", response.getError().getData());
        assertEquals("Вы должны добавить название для эфира", response.getError().getHumanMessage());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void createStreamWhenStreamDescriptionNullOrEmptyTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(TEST_COVER, TEST_TITLE, null, productIds, 3L, null, null, null, 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(response);
        assertNull(response.getData());
        assertNotNull(response.getError());
        assertEquals("Description : null", response.getError().getData());
        assertEquals("Вы должны добавить описание для эфира", response.getError().getHumanMessage());

        result = createStream(TEST_COVER, TEST_TITLE, "", productIds, 3L, null, null, null, 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(response);
        assertNull(response.getData());
        assertNotNull(response.getError());
        assertEquals("Description : null", response.getError().getData());
        assertEquals("Вы должны добавить описание для эфира", response.getError().getHumanMessage());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void createStreamWhenStreamIncludeOtherUserProductTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        List<Long> otherUserProductIds = getProductIdsForNewUser(user, 6, 0);
        productIds.addAll(otherUserProductIds);

        MvcResult result = createStream(productIds, 3L, null, null, null, 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(response);
        assertNull(response.getData());
        assertNotNull(response.getError());
        assertEquals(ErrorType.VALIDATION, response.getError().getCode());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void createStreamWhenStreamNotHaveProductTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        MvcResult result = createStream(new ArrayList<>(), 3L, null, null, null, 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(response);
        assertNull(response.getData());
        assertNotNull(response.getError());
        assertEquals("Product : 0", response.getError().getData());
        assertEquals("Вы должны добавить минимум 1 товар", response.getError().getHumanMessage());

        result = createStream(null, 3L, null, null, null, 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(response);
        assertNull(response.getData());
        assertNotNull(response.getError());
        assertEquals("Product : 0", response.getError().getData());
        assertEquals("Вы должны добавить минимум 1 товар", response.getError().getHumanMessage());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void createStreamWhenStreamProductsCountMoreThanTwentyFiveTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 26, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(response);
        assertNull(response.getData());
        assertNotNull(response.getError());
        assertEquals("Product : > 25", response.getError().getData());
        assertEquals("Вы не можете добавить более " + 25 + " товаров в один эфир", response.getError().getHumanMessage());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void createStreamWhenStreamProductsStateNotPublishedTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 7, 2);

        MvcResult result = createStream(productIds, 3L, null, null, null, 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(response);
        assertNull(response.getData());
        assertNotNull(response.getError());
        assertEquals(ErrorType.VALIDATION, response.getError().getCode());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void createStreamWhenStartDateBeforeNowTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 6, 0);

        MvcResult result = createStream(productIds, null, null, null, 20L, 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(response);
        assertNull(response.getData());
        assertNotNull(response.getError());
        assertEquals(ErrorType.VALIDATION, response.getError().getCode());
        assertEquals("Вы не можете установить дату начала эфира ранее, чем текущая дата", response.getError().getHumanMessage());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void createStreamWhenStartDateAfterNowLessFortyFiveMinutesTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 3, 0);

        MvcResult result = createStream(productIds, null, 30L, null, null, 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(response);
        assertNull(response.getData());
        assertNotNull(response.getError());
        assertEquals(ErrorType.VALIDATION, response.getError().getCode());
        assertEquals("Время до отложенного эфира должно быть больше 1 часа", response.getError().getHumanMessage());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void createStreamWhenStartDateAfterThirtyDaysTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 1, 0);

        MvcResult result = createStream(productIds, 32L, null, null, null, 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(response);
        assertNull(response.getData());
        assertNotNull(response.getError());
        assertEquals(ErrorType.VALIDATION, response.getError().getCode());
        assertEquals("Время до начала отложенного эфира не может быть больше 30 дней", response.getError().getHumanMessage());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void createRightStreamWithStartingDateIsNullTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, null, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);
        assertEquals(TEST_COVER, dto.getCover());
        assertEquals(TEST_TITLE, dto.getTitle());
        assertEquals(TEST_DESCRIPTION, dto.getDescription());
        assertEquals(StreamStatus.ANNOUNCE, dto.getStatus());
        assertNotNull(dto.getProducts());
        assertFalse(dto.getProducts().isEmpty());
        assertEquals(5, dto.getProducts().size());
        List<Long> actualProductIds = dto.getProducts().stream().map(StreamProductDTO::getId).collect(Collectors.toList());

        for (Long productId : productIds) {
            assertTrue(actualProductIds.contains(productId));
        }

        assertNotNull(dto.getAuthor());
        assertEquals(dto.getAuthor().getId(), NEW_USER_LOGIN_PASS.get("david_test_1").getId());
        assertEquals(dto.getAuthor().getNickName(), NEW_USER_LOGIN_PASS.get("david_test_1").getNickName());

        assertTrue(dto.getCanDelete());
        assertFalse(dto.getCanUpdate());
        assertTrue(dto.getCanStart());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void updateStreamTest() throws Exception{
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);
        assertNotNull(dto.getStartingDate());

        Long streamId = dto.getId();
        assertNotNull(streamId);

        List<Long> newProductIds = getProductIdsForNewUser(user, 4, 0);

        ZonedDateTime oldStartingDate = dto.getStartingDate();

        result = updateStream(streamId, StreamCover.COVER_2, TEST_TITLE + 1, TEST_DESCRIPTION + 1, newProductIds, ZonedDateTime.now(ZoneOffset.UTC).plusDays(1L), 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        dto = response.getData();
        assertNotNull(dto);

        assertEquals(StreamCover.COVER_2, dto.getCover());
        assertEquals(TEST_TITLE + 1, dto.getTitle());
        assertEquals(TEST_DESCRIPTION + 1, dto.getDescription());
        assertNotEquals(oldStartingDate, dto.getStartingDate());
        assertEquals(4, dto.getProducts().size());

        List<Long> equalProductIds = dto.getProducts()
                .stream()
                .map(StreamProductDTO::getId)
                .collect(Collectors.toList());
        Collections.sort(newProductIds);
        Collections.sort(equalProductIds);

        assertEquals(newProductIds, equalProductIds);

        newProductIds.remove(1);
        newProductIds.remove(2);

        result = updateStream(streamId, StreamCover.COVER_2, TEST_TITLE + 1, TEST_DESCRIPTION + 1, newProductIds, ZonedDateTime.now(ZoneOffset.UTC).plusDays(1L), 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        dto = response.getData();
        assertNotNull(dto);
        assertEquals(2, dto.getProducts().size());

        equalProductIds = dto.getProducts()
                .stream()
                .map(StreamProductDTO::getId)
                .collect(Collectors.toList());

        Collections.sort(newProductIds);
        Collections.sort(equalProductIds);
        assertEquals(newProductIds, equalProductIds);

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }
    @Test
    public void updateStreamWhenStreamCoverNullTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);

        Long streamId = dto.getId();
        assertNotNull(streamId);

        result = updateStream(streamId, null, dto.getTitle(), dto.getDescription(), productIds, dto.getStartingDate(), 400);

        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> errorResponse = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(errorResponse);
        assertNull(errorResponse.getData());
        assertNotNull(errorResponse.getError());
        assertEquals("Cover : null", errorResponse.getError().getData());
        assertEquals("Вы должны добавить обложку для эфира", errorResponse.getError().getHumanMessage());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void updateStreamWhenStreamTitleNullOrEmptyTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);

        Long streamId = dto.getId();
        assertNotNull(streamId);

        result = updateStream(streamId, dto.getCover(), null, dto.getDescription(), productIds, dto.getStartingDate(), 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> errorResponse = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(errorResponse);
        assertNull(errorResponse.getData());
        assertNotNull(errorResponse.getError());
        assertEquals("Title : null", errorResponse.getError().getData());
        assertEquals("Вы должны добавить название для эфира", errorResponse.getError().getHumanMessage());

        result = updateStream(streamId, dto.getCover(), "", dto.getDescription(), productIds, dto.getStartingDate(), 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        errorResponse = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(errorResponse);
        assertNull(errorResponse.getData());
        assertNotNull(errorResponse.getError());
        assertEquals("Title : null", errorResponse.getError().getData());
        assertEquals("Вы должны добавить название для эфира", errorResponse.getError().getHumanMessage());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void updateStreamWhenStreamDescriptionNullOrEmptyTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);

        Long streamId = dto.getId();
        assertNotNull(streamId);

        result = updateStream(streamId, dto.getCover(), dto.getTitle(), null, productIds, dto.getStartingDate(), 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> errorResponse = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(errorResponse);
        assertNull(errorResponse.getData());
        assertNotNull(errorResponse.getError());
        assertEquals("Description : null", errorResponse.getError().getData());
        assertEquals("Вы должны добавить описание для эфира", errorResponse.getError().getHumanMessage());

        result = updateStream(streamId, dto.getCover(), dto.getTitle(), "", productIds, dto.getStartingDate(), 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        errorResponse = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(errorResponse);
        assertNull(errorResponse.getData());
        assertNotNull(errorResponse.getError());
        assertEquals("Description : null", errorResponse.getError().getData());
        assertEquals("Вы должны добавить описание для эфира", errorResponse.getError().getHumanMessage());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void updateStreamWhenStreamIncludeOtherUserProductTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);

        Long streamId = dto.getId();
        assertNotNull(streamId);

        List<Long> otherUserProductIds = getProductIdsForNewUser(user, 6, 0);
        productIds.addAll(otherUserProductIds);

        result = updateStream(streamId, dto.getCover(), dto.getTitle(), dto.getDescription(), productIds, dto.getStartingDate(), 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> errorResponse = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(errorResponse);
        assertNull(errorResponse.getData());
        assertNotNull(errorResponse.getError());
        assertEquals(ErrorType.VALIDATION, errorResponse.getError().getCode());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void updateStreamWhenStreamNotHaveProductTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);

        Long streamId = dto.getId();
        assertNotNull(streamId);

        productIds.clear();

        result = updateStream(streamId, dto.getCover(), dto.getTitle(), dto.getDescription(), productIds, dto.getStartingDate(), 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> errorResponse = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(errorResponse);
        assertNull(errorResponse.getData());
        assertNotNull(errorResponse.getError());
        assertEquals(ErrorType.VALIDATION, errorResponse.getError().getCode());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void updateStreamWhenStreamProductsCountMoreThanTwentyFiveTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);

        Long streamId = dto.getId();
        assertNotNull(streamId);

        productIds = getProductIdsForNewUser(user, 26, 0);

        result = updateStream(streamId, dto.getCover(), dto.getTitle(), dto.getDescription(), productIds, dto.getStartingDate(), 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> errorResponse = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(errorResponse);
        assertNull(errorResponse.getData());
        assertNotNull(errorResponse.getError());
        assertEquals(ErrorType.VALIDATION, errorResponse.getError().getCode());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void updateStreamWhenStreamProductsStateNotPublishedTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);

        Long streamId = dto.getId();
        assertNotNull(streamId);

        productIds.addAll(getProductIdsForNewUser(user, 0, 1));

        result = updateStream(streamId, dto.getCover(), dto.getTitle(), dto.getDescription(), productIds, dto.getStartingDate(), 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> errorResponse = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(errorResponse);
        assertNull(errorResponse.getData());
        assertNotNull(errorResponse.getError());
        assertEquals(ErrorType.VALIDATION, errorResponse.getError().getCode());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void updateStreamWhenStartDateBeforeNowTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);

        Long streamId = dto.getId();
        assertNotNull(streamId);

        ZonedDateTime newDate = ZonedDateTime.now(ZoneOffset.UTC);

        result = updateStream(streamId, dto.getCover(), dto.getTitle(), dto.getDescription(), productIds, newDate, 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> errorResponse = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(errorResponse);
        assertNull(errorResponse.getData());
        assertEquals(ErrorType.VALIDATION, errorResponse.getError().getCode());
        assertEquals("Вы не можете установить дату начала эфира ранее, чем текущая дата", errorResponse.getError().getHumanMessage());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void updateStreamWhenStartDateAfterNowLessFortyFiveMinutesTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);

        Long streamId = dto.getId();
        assertNotNull(streamId);

        result = updateStream(streamId, dto.getCover(), dto.getTitle(), dto.getDescription(), productIds, ZonedDateTime.now(ZoneOffset.UTC).plusMinutes(30L), 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> errorResponse = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(errorResponse);
        assertNull(errorResponse.getData());
        assertEquals(ErrorType.VALIDATION, errorResponse.getError().getCode());
        assertEquals("Время до отложенного эфира должно быть больше 1 часа", errorResponse.getError().getHumanMessage());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void updateStreamWhenStartDateAfterThirtyDaysTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);

        Long streamId = dto.getId();
        assertNotNull(streamId);

        result = updateStream(streamId, dto.getCover(), dto.getTitle(), dto.getDescription(), productIds, dto.getStartingDate().plusDays(29L), 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> errorResponse = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(errorResponse);
        assertNull(errorResponse.getData());
        assertEquals(ErrorType.VALIDATION, errorResponse.getError().getCode());
        assertEquals("Время до начала отложенного эфира не может быть больше 30 дней", errorResponse.getError().getHumanMessage());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void updateRightStreamWithStartingDateIsNullTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);

        Long streamId = dto.getId();
        assertNotNull(streamId);

        result = updateStream(streamId, productIds, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        dto = response.getData();
        assertNotNull(dto);
        assertEquals(TEST_COVER, dto.getCover());
        assertEquals(TEST_TITLE, dto.getTitle());
        assertEquals(TEST_DESCRIPTION, dto.getDescription());
        assertEquals(StreamStatus.ANNOUNCE, dto.getStatus());
        assertNotNull(dto.getProducts());
        assertFalse(dto.getProducts().isEmpty());
        assertEquals(5, dto.getProducts().size());
        List<Long> actualProductIds = dto.getProducts().stream().map(StreamProductDTO::getId).collect(Collectors.toList());

        for (Long productId : productIds) {
            assertTrue(actualProductIds.contains(productId));
        }

        assertNotNull(dto.getAuthor());
        assertEquals(dto.getAuthor().getId(), NEW_USER_LOGIN_PASS.get("david_test_1").getId());
        assertEquals(dto.getAuthor().getNickName(), NEW_USER_LOGIN_PASS.get("david_test_1").getNickName());

        assertTrue(dto.getCanDelete());
        assertFalse(dto.getCanUpdate());
        assertTrue(dto.getCanStart());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void updateStreamByOtherUserTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);

        Long streamId = dto.getId();
        assertNotNull(streamId);
        logout(authentication);

        authentication = login(NEW_USER_LOGIN_PASS.get("david_test_2").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_2").getPassword());
        User otherUser = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_2").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        result = updateStream(streamId, productIds, dto.getStartingDate(), 403);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> errorResponse = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(errorResponse);
        assertNull(errorResponse.getData());
        assertEquals("У Вас нет прав на редактирование этого эфира", errorResponse.getError().getHumanMessage());

        removeUserCreateStreamAccess(otherUser);
        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void updateStreamWhenStreamInLiveTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);

        Long streamId = dto.getId();
        assertNotNull(streamId);

        Optional<Stream> optionalStream = streamRepository.findById(streamId);
        Stream stream = null;
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
        } else {
            fail("Неправильный первичный ключ прямого эфира или данные не сохранились в базе");
        }
        assertNotNull(stream);
        assertNotNull(stream.getStreamStatus());
        stream.setStreamStatus(StreamStatus.LIVE);
        streamRepository.saveAndFlush(stream);

        result = updateStream(streamId, productIds, dto.getStartingDate(), 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> errorResponse = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });

        assertNotNull(errorResponse);
        assertNull(errorResponse.getData());
        assertEquals("Вы не можете обновить этот эфир", errorResponse.getError().getHumanMessage());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void updateStreamWhenStreamStartingDateLessOneHourNowTimeTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);

        Long streamId = dto.getId();
        assertNotNull(streamId);

        Optional<Stream> optionalStream = streamRepository.findById(streamId);
        Stream stream = null;
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
        } else {
            fail("Неправильный первичный ключ прямого эфира или данные не сохранились в базе");
        }
        assertNotNull(stream);
        assertNotNull(stream.getStartingDate());
        stream.setStartingDate(ZonedDateTime.now(ZoneOffset.UTC).plusMinutes(40L));
        streamRepository.saveAndFlush(stream);

        result = updateStream(streamId, productIds, dto.getStartingDate(), 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> errorResponse = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });

        assertNotNull(errorResponse);
        assertNull(errorResponse.getData());
        assertEquals("Вы не можете обновить этот эфир", errorResponse.getError().getHumanMessage());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void canUpdateStreamStateTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);
        assertTrue(dto.getCanUpdate());

        Long streamId = dto.getId();
        assertNotNull(streamId);

        Optional<Stream> optionalStream = streamRepository.findById(streamId);
        Stream stream = null;
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
        } else {
            fail("Неправильный первичный ключ прямого эфира или данные не сохранились в базе");
        }
        assertNotNull(stream);
        assertNotNull(stream.getStreamStatus());
        stream.setStreamStatus(StreamStatus.LIVE);
        streamRepository.saveAndFlush(stream);

        result = getStream(streamId, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        dto = response.getData();
        assertNotNull(dto);
        assertFalse(dto.getCanUpdate());

        optionalStream = streamRepository.findById(streamId);
        stream = null;
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
        } else {
            fail("Неправильный первичный ключ прямого эфира или данные не сохранились в базе");
        }
        assertNotNull(stream);
        assertNotNull(stream.getStreamStatus());
        stream.setStreamStatus(StreamStatus.ARCHIVED);
        streamRepository.saveAndFlush(stream);

        result = getStream(streamId, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        dto = response.getData();
        assertNotNull(dto);
        assertFalse(dto.getCanUpdate());

        optionalStream = streamRepository.findById(streamId);
        stream = null;
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
        } else {
            fail("Неправильный первичный ключ прямого эфира или данные не сохранились в базе");
        }
        assertNotNull(stream);
        assertNotNull(stream.getStreamStatus());
        stream.setStreamStatus(StreamStatus.BANNED);
        streamRepository.saveAndFlush(stream);

        result = getStream(streamId, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        dto = response.getData();
        assertNotNull(dto);
        assertFalse(dto.getCanUpdate());

        optionalStream = streamRepository.findById(streamId);
        stream = null;
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
        } else {
            fail("Неправильный первичный ключ прямого эфира или данные не сохранились в базе");
        }
        assertNotNull(stream);
        assertNotNull(stream.getStartingDate());
        stream.setStartingDate(ZonedDateTime.now(ZoneOffset.UTC).plusMinutes(40L));
        streamRepository.saveAndFlush(stream);

        result = getStream(streamId, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        dto = response.getData();
        assertNotNull(dto);
        assertFalse(dto.getCanUpdate());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void deleteStreamTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);

        Long streamId = dto.getId();
        assertNotNull(streamId);

        result = deleteStream(streamId, 200);

        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<Long> deleteResponse = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<Long>>() {
        });
        assertNotNull(deleteResponse);
        assertNotNull(deleteResponse.getData());
        assertEquals(streamId, deleteResponse.getData());

        Optional<Stream> optionalStream = streamRepository.findById(streamId);
        Stream stream = null;
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
        } else {
            fail("Неправильный первичный ключ прямого эфира или данные не сохранились в базе");
        }
        assertNotNull(stream);
        assertNotNull(stream.getDeletionDate());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void deleteStreamByOtherUserTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);

        Long streamId = dto.getId();
        assertNotNull(streamId);
        logout(authentication);

        authentication = login(NEW_USER_LOGIN_PASS.get("david_test_2").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_2").getPassword());
        User otherUser = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_2").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        result = deleteStream(streamId, 403);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> errorResponse = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(errorResponse);
        assertNull(errorResponse.getData());
        assertEquals("У Вас нет прав на удаление этого эфира", errorResponse.getError().getHumanMessage());

        removeUserCreateStreamAccess(otherUser);
        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void deleteStreamWhenStateLiveTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);

        Long streamId = dto.getId();
        assertNotNull(streamId);

        Optional<Stream> optionalStream = streamRepository.findById(streamId);
        Stream stream = null;
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
        } else {
            fail("Неправильный первичный ключ прямого эфира или данные не сохранились в базе");
        }
        assertNotNull(stream);
        assertNotNull(stream.getStreamStatus());
        stream.setStreamStatus(StreamStatus.LIVE);
        streamRepository.saveAndFlush(stream);

        result = deleteStream(streamId, 400);

        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> errorResponse = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(errorResponse);
        assertNull(errorResponse.getData());
        assertEquals("Вы не можете удалить этот эфир", errorResponse.getError().getHumanMessage());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void deleteStreamWhenStateBannedTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);

        Long streamId = dto.getId();
        assertNotNull(streamId);

        Optional<Stream> optionalStream = streamRepository.findById(streamId);
        Stream stream = null;
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
        } else {
            fail("Неправильный первичный ключ прямого эфира или данные не сохранились в базе");
        }
        assertNotNull(stream);
        assertNotNull(stream.getStreamStatus());
        stream.setStreamStatus(StreamStatus.BANNED);
        streamRepository.saveAndFlush(stream);

        result = deleteStream(streamId, 400);

        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> errorResponse = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(errorResponse);
        assertNull(errorResponse.getData());
        assertEquals("Вы не можете удалить этот эфир", errorResponse.getError().getHumanMessage());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void canDeleteStreamStateTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);
        assertTrue(dto.getCanDelete());

        Long streamId = dto.getId();
        assertNotNull(streamId);

        Optional<Stream> optionalStream = streamRepository.findById(streamId);
        Stream stream = null;
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
        } else {
            fail("Неправильный первичный ключ прямого эфира или данные не сохранились в базе");
        }
        assertNotNull(stream);
        assertNotNull(stream.getStreamStatus());
        stream.setStreamStatus(StreamStatus.LIVE);
        streamRepository.saveAndFlush(stream);

        result = getStream(streamId, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        dto = response.getData();
        assertNotNull(dto);
        assertFalse(dto.getCanDelete());

        optionalStream = streamRepository.findById(streamId);
        stream = null;
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
        } else {
            fail("Неправильный первичный ключ прямого эфира или данные не сохранились в базе");
        }
        assertNotNull(stream);
        assertNotNull(stream.getStreamStatus());
        stream.setStreamStatus(StreamStatus.BANNED);
        streamRepository.saveAndFlush(stream);

        result = getStream(streamId, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        dto = response.getData();
        assertNotNull(dto);
        assertFalse(dto.getCanDelete());

        optionalStream = streamRepository.findById(streamId);
        stream = null;
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
        } else {
            fail("Неправильный первичный ключ прямого эфира или данные не сохранились в базе");
        }
        assertNotNull(stream);
        assertNotNull(stream.getStartingDate());
        stream.setDeletionDate(ZonedDateTime.now(ZoneOffset.UTC));
        streamRepository.saveAndFlush(stream);

        result = getStream(streamId, 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<String> errorResponse = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<String>>() {
        });
        assertNotNull(errorResponse);
        assertNull(errorResponse.getData());
        assertNotNull(errorResponse.getError());
        assertEquals(ErrorType.LOGIC, errorResponse.getError().getCode());
        assertEquals("Данный эфир удален", errorResponse.getError().getHumanMessage());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void createStreamNotificationsTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        User followerUser = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_2").getEmail());
        User subscribeUser = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_3").getEmail());
        User commentUser = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_4").getEmail());
        User likeUser = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_5").getEmail());

        Map<String, Long> map = addStreamDefaultFollowersIfNotExists(user, productIds, followerUser, subscribeUser, commentUser, likeUser);
        assertNotNull(map.get("FOLLOWER"));
        assertNotNull(map.get("PRICE"));
        assertNotNull(map.get("COMMENT"));
        assertNotNull(map.get("LIKE"));

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        Long streamId = response.getData().getId();
        assertNotNull(streamId);

        TestUtils.sleep(1);
        Stream stream = null;
        Optional<Stream> optionalStream = streamRepository.findById(streamId);
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
            assertNotNull(stream);
            assertTrue(stream.getIsReadySendNotification());
        } else {
            fail("Стрим должен был быть найден");
        }

        scheduledNotificationRunner.sendStreamAndUserDataForStreamNotifications();

        TestUtils.sleep(1);

        List<StreamNotification> notificationList = notificationRepository.findAllByUser(map.get("FOLLOWER"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("FOLLOWER"), notificationList.get(0).getUser().getId());
        assertEquals(StreamCreateNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        notificationList = notificationRepository.findAllByUser(map.get("PRICE"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("PRICE"), notificationList.get(0).getUser().getId());
        assertEquals(StreamCreateNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        notificationList = notificationRepository.findAllByUser(map.get("COMMENT"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("COMMENT"), notificationList.get(0).getUser().getId());
        assertEquals(StreamCreateNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        notificationList = notificationRepository.findAllByUser(map.get("LIKE"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("LIKE"), notificationList.get(0).getUser().getId());
        assertEquals(StreamCreateNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        scheduledNotificationRunner.sendStreamAndUserDataForStreamNotifications();
        TestUtils.sleep(1);
        optionalStream = streamRepository.findById(streamId);
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
            assertNotNull(stream);
            assertFalse(stream.getIsReadySendNotification());
        } else {
            fail("Стрим должен был быть найден");
        }

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void deleteStreamNotificationsTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        User followerUser = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_2").getEmail());
        User subscribeUser = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_3").getEmail());
        User commentUser = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_4").getEmail());
        User likeUser = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_5").getEmail());

        Map<String, Long> map = addStreamDefaultFollowersIfNotExists(user, productIds, followerUser, subscribeUser, commentUser, likeUser);
        assertNotNull(map.get("FOLLOWER"));
        assertNotNull(map.get("PRICE"));
        assertNotNull(map.get("COMMENT"));
        assertNotNull(map.get("LIKE"));

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        Long streamId = response.getData().getId();
        assertNotNull(streamId);

        TestUtils.sleep(1);
        Stream stream = null;
        Optional<Stream> optionalStream = streamRepository.findById(streamId);
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
            assertNotNull(stream);
            assertTrue(stream.getIsReadySendNotification());
        } else {
            fail("Стрим должен был быть найден");
        }

        scheduledNotificationRunner.sendStreamAndUserDataForStreamNotifications();

        TestUtils.sleep(1);

        List<StreamNotification> notificationList = notificationRepository.findAllByUser(map.get("FOLLOWER"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("FOLLOWER"), notificationList.get(0).getUser().getId());
        assertEquals(StreamCreateNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        notificationList = notificationRepository.findAllByUser(map.get("PRICE"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("PRICE"), notificationList.get(0).getUser().getId());
        assertEquals(StreamCreateNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        notificationList = notificationRepository.findAllByUser(map.get("COMMENT"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("COMMENT"), notificationList.get(0).getUser().getId());
        assertEquals(StreamCreateNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        notificationList = notificationRepository.findAllByUser(map.get("LIKE"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("LIKE"), notificationList.get(0).getUser().getId());
        assertEquals(StreamCreateNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        scheduledNotificationRunner.sendStreamAndUserDataForStreamNotifications();
        TestUtils.sleep(1);
        optionalStream = streamRepository.findById(streamId);
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
            assertNotNull(stream);
            assertFalse(stream.getIsReadySendNotification());
        } else {
            fail("Стрим должен был быть найден");
        }

        notificationRepository.deleteAllInBatch();

        deleteStream(streamId, 200);
        TestUtils.sleep(1);
        optionalStream = streamRepository.findById(streamId);
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
            assertNotNull(stream);
            assertTrue(stream.getIsReadySendNotification());
        } else {
            fail("Стрим должен был быть найден");
        }

        scheduledNotificationRunner.sendStreamAndUserDataForStreamNotifications();

        TestUtils.sleep(1);

        notificationList = notificationRepository.findAllByUser(map.get("FOLLOWER"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("FOLLOWER"), notificationList.get(0).getUser().getId());
        assertEquals(StreamFinishNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        notificationList = notificationRepository.findAllByUser(map.get("PRICE"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("PRICE"), notificationList.get(0).getUser().getId());
        assertEquals(StreamFinishNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        notificationList = notificationRepository.findAllByUser(map.get("COMMENT"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("COMMENT"), notificationList.get(0).getUser().getId());
        assertEquals(StreamFinishNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        notificationList = notificationRepository.findAllByUser(map.get("LIKE"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("LIKE"), notificationList.get(0).getUser().getId());
        assertEquals(StreamFinishNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        scheduledNotificationRunner.sendStreamAndUserDataForStreamNotifications();
        TestUtils.sleep(1);
        optionalStream = streamRepository.findById(streamId);
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
            assertNotNull(stream);
            assertFalse(stream.getIsReadySendNotification());
        } else {
            fail("Стрим должен был быть найден");
        }

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void autoDeleteStreamNotificationsTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        User followerUser = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_2").getEmail());
        User subscribeUser = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_3").getEmail());
        User commentUser = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_4").getEmail());
        User likeUser = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_5").getEmail());

        Map<String, Long> map = addStreamDefaultFollowersIfNotExists(user, productIds, followerUser, subscribeUser, commentUser, likeUser);
        assertNotNull(map.get("FOLLOWER"));
        assertNotNull(map.get("PRICE"));
        assertNotNull(map.get("COMMENT"));
        assertNotNull(map.get("LIKE"));

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        Long streamId = response.getData().getId();
        assertNotNull(streamId);

        TestUtils.sleep(1);
        Stream stream = null;
        Optional<Stream> optionalStream = streamRepository.findById(streamId);
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
            assertNotNull(stream);
            assertTrue(stream.getIsReadySendNotification());
        } else {
            fail("Стрим должен был быть найден");
        }

        scheduledNotificationRunner.sendStreamAndUserDataForStreamNotifications();

        TestUtils.sleep(1);

        List<StreamNotification> notificationList = notificationRepository.findAllByUser(map.get("FOLLOWER"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("FOLLOWER"), notificationList.get(0).getUser().getId());
        assertEquals(StreamCreateNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        notificationList = notificationRepository.findAllByUser(map.get("PRICE"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("PRICE"), notificationList.get(0).getUser().getId());
        assertEquals(StreamCreateNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        notificationList = notificationRepository.findAllByUser(map.get("COMMENT"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("COMMENT"), notificationList.get(0).getUser().getId());
        assertEquals(StreamCreateNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        notificationList = notificationRepository.findAllByUser(map.get("LIKE"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("LIKE"), notificationList.get(0).getUser().getId());
        assertEquals(StreamCreateNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        scheduledNotificationRunner.sendStreamAndUserDataForStreamNotifications();
        TestUtils.sleep(1);
        optionalStream = streamRepository.findById(streamId);
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
            assertNotNull(stream);
            assertFalse(stream.getIsReadySendNotification());
        } else {
            fail("Стрим должен был быть найден");
        }

        notificationRepository.deleteAllInBatch();
        stream.setCreateDate(ZonedDateTime.now(ZoneOffset.UTC).minusDays(1L));
        stream.setStartingDate(ZonedDateTime.now(ZoneOffset.UTC).minusMinutes(40L));
        streamRepository.saveAndFlush(stream);

        streamService.autoDeleteAndArchivedStreams();
        TestUtils.sleep(1);
        optionalStream = streamRepository.findById(streamId);
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
            assertNotNull(stream);
            assertTrue(stream.getIsReadySendNotification());
        } else {
            fail("Стрим должен был быть найден");
        }

        scheduledNotificationRunner.sendStreamAndUserDataForStreamNotifications();

        TestUtils.sleep(1);

        notificationList = notificationRepository.findAllByUser(map.get("FOLLOWER"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("FOLLOWER"), notificationList.get(0).getUser().getId());
        assertEquals(StreamFinishNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        notificationList = notificationRepository.findAllByUser(map.get("PRICE"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("PRICE"), notificationList.get(0).getUser().getId());
        assertEquals(StreamFinishNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        notificationList = notificationRepository.findAllByUser(map.get("COMMENT"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("COMMENT"), notificationList.get(0).getUser().getId());
        assertEquals(StreamFinishNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        notificationList = notificationRepository.findAllByUser(map.get("LIKE"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("LIKE"), notificationList.get(0).getUser().getId());
        assertEquals(StreamFinishNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        scheduledNotificationRunner.sendStreamAndUserDataForStreamNotifications();
        TestUtils.sleep(1);
        optionalStream = streamRepository.findById(streamId);
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
            assertNotNull(stream);
            assertFalse(stream.getIsReadySendNotification());
        } else {
            fail("Стрим должен был быть найден");
        }

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    @Disabled("No Bambuser at this moment")
    public void startStreamNowTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, null, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);
        Long streamId = dto.getId();
        assertNotNull(streamId);

        result = startStream(streamId, testStreamBroadcastId, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });
        assertNotNull(response);
        assertNotNull(response.getData());
        dto = response.getData();
        assertNotNull(dto);
        assertNotNull(dto.getResourceURI());

        Optional<Stream> optionalStream = streamRepository.findById(streamId);
        Stream stream = null;
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
            assertNotNull(stream);
            assertFalse(stream.getIsReadySendNotification());
        } else {
            fail("Стрим должен был быть найден");
        }

        assertEquals(testStreamBroadcastId, stream.getBroadCastId());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    @Disabled("No Bambuser at this moment")
    public void startStreamNowNotificationTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        User followerUser = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_2").getEmail());
        User subscribeUser = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_3").getEmail());
        User commentUser = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_4").getEmail());
        User likeUser = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_5").getEmail());

        Map<String, Long> map = addStreamDefaultFollowersIfNotExists(user, productIds, followerUser, subscribeUser, commentUser, likeUser);
        assertNotNull(map.get("FOLLOWER"));
        assertNotNull(map.get("PRICE"));
        assertNotNull(map.get("COMMENT"));
        assertNotNull(map.get("LIKE"));

        MvcResult result = createStream(productIds, null, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        Long streamId = response.getData().getId();
        assertNotNull(streamId);

        TestUtils.sleep(1);
        Stream stream = null;
        Optional<Stream> optionalStream = streamRepository.findById(streamId);
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
            assertNotNull(stream);
            assertFalse(stream.getIsReadySendNotification());
        } else {
            fail("Стрим должен был быть найден");
        }

        scheduledNotificationRunner.sendStreamAndUserDataForStreamNotifications();

        TestUtils.sleep(1);

        notificationRepository.deleteAllInBatch();

        startStream(streamId, testStreamBroadcastId, 200);

        TestUtils.sleep(1);
        optionalStream = streamRepository.findById(streamId);
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
            assertNotNull(stream);
            assertTrue(stream.getIsReadySendNotification());
        } else {
            fail("Стрим должен был быть найден");
        }

        scheduledNotificationRunner.sendStreamAndUserDataForStreamNotifications();

        TestUtils.sleep(1);

        List<StreamNotification> notificationList = notificationRepository.findAllByUser(map.get("FOLLOWER"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("FOLLOWER"), notificationList.get(0).getUser().getId());
        assertEquals(StreamStartNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        notificationList = notificationRepository.findAllByUser(map.get("PRICE"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("PRICE"), notificationList.get(0).getUser().getId());
        assertEquals(StreamStartNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        notificationList = notificationRepository.findAllByUser(map.get("COMMENT"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("COMMENT"), notificationList.get(0).getUser().getId());
        assertEquals(StreamStartNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        notificationList = notificationRepository.findAllByUser(map.get("LIKE"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("LIKE"), notificationList.get(0).getUser().getId());
        assertEquals(StreamStartNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        scheduledNotificationRunner.sendStreamAndUserDataForStreamNotifications();
        TestUtils.sleep(1);
        optionalStream = streamRepository.findById(streamId);
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
            assertNotNull(stream);
            assertFalse(stream.getIsReadySendNotification());
        } else {
            fail("Стрим должен был быть найден");
        }

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    @Disabled("No Bambuser at this moment")
    public void startDeferredStreamTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);
        Long streamId = dto.getId();
        assertNotNull(streamId);

        TestUtils.sleep(2);

        Optional<Stream> optionalStream = streamRepository.findById(streamId);
        Stream stream = null;
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
            assertNotNull(stream);
            assertTrue(stream.getIsReadySendNotification());
        } else {
            fail("Стрим должен был быть найден");
        }

        stream.setCreateDate(ZonedDateTime.now(ZoneOffset.UTC).minusHours(2L));
        stream.setStartingDate(ZonedDateTime.now(ZoneOffset.UTC).minusMinutes(3L));
        streamRepository.saveAndFlush(stream);

        result = startStream(streamId, testStreamBroadcastId, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });
        assertNotNull(response);
        assertNotNull(response.getData());
        dto = response.getData();
        assertNotNull(dto);
        assertNotNull(dto.getResourceURI());

        optionalStream = streamRepository.findById(streamId);
        stream = null;
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
            assertNotNull(stream);
            assertTrue(stream.getIsReadySendNotification());
        } else {
            fail("Стрим должен был быть найден");
        }

        assertEquals(testStreamBroadcastId, stream.getBroadCastId());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    @Disabled("No Bambuser at this moment")
    public void startDeferredStreamNotificationTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        User followerUser = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_2").getEmail());
        User subscribeUser = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_3").getEmail());
        User commentUser = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_4").getEmail());
        User likeUser = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_5").getEmail());

        Map<String, Long> map = addStreamDefaultFollowersIfNotExists(user, productIds, followerUser, subscribeUser, commentUser, likeUser);
        assertNotNull(map.get("FOLLOWER"));
        assertNotNull(map.get("PRICE"));
        assertNotNull(map.get("COMMENT"));
        assertNotNull(map.get("LIKE"));

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        Long streamId = response.getData().getId();
        assertNotNull(streamId);

        TestUtils.sleep(2);
        Stream stream = null;
        Optional<Stream> optionalStream = streamRepository.findById(streamId);
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
            assertNotNull(stream);
            assertTrue(stream.getIsReadySendNotification());
        } else {
            fail("Стрим должен был быть найден");
        }

        scheduledNotificationRunner.sendStreamAndUserDataForStreamNotifications();

        TestUtils.sleep(1);

        List<StreamNotification> notificationList = notificationRepository.findAllByUser(map.get("FOLLOWER"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("FOLLOWER"), notificationList.get(0).getUser().getId());
        assertEquals(StreamCreateNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        notificationList = notificationRepository.findAllByUser(map.get("PRICE"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("PRICE"), notificationList.get(0).getUser().getId());
        assertEquals(StreamCreateNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        notificationList = notificationRepository.findAllByUser(map.get("COMMENT"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("COMMENT"), notificationList.get(0).getUser().getId());
        assertEquals(StreamCreateNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        notificationList = notificationRepository.findAllByUser(map.get("LIKE"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("LIKE"), notificationList.get(0).getUser().getId());
        assertEquals(StreamCreateNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        scheduledNotificationRunner.sendStreamAndUserDataForStreamNotifications();
        TestUtils.sleep(1);
        optionalStream = streamRepository.findById(streamId);
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
            assertNotNull(stream);
            assertFalse(stream.getIsReadySendNotification());
        } else {
            fail("Стрим должен был быть найден");
        }

        stream.setCreateDate(ZonedDateTime.now(ZoneOffset.UTC).minusHours(2L));
        stream.setStartingDate(ZonedDateTime.now(ZoneOffset.UTC).minusMinutes(3L));
        streamRepository.saveAndFlush(stream);

        result = startStream(streamId, testStreamBroadcastId, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });
        assertNotNull(response);
        assertNotNull(response.getData());

        notificationRepository.deleteAllInBatch();

        optionalStream = streamRepository.findById(streamId);
        stream = null;
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
            assertNotNull(stream);
            assertTrue(stream.getIsReadySendNotification());
        } else {
            fail("Стрим должен был быть найден");
        }

        assertEquals(testStreamBroadcastId, stream.getBroadCastId());

        scheduledNotificationRunner.sendStreamAndUserDataForStreamNotifications();

        TestUtils.sleep(1);

        notificationList = notificationRepository.findAllByUser(map.get("FOLLOWER"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("FOLLOWER"), notificationList.get(0).getUser().getId());
        assertEquals(StreamStartNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        notificationList = notificationRepository.findAllByUser(map.get("PRICE"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("PRICE"), notificationList.get(0).getUser().getId());
        assertEquals(StreamStartNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        notificationList = notificationRepository.findAllByUser(map.get("COMMENT"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("COMMENT"), notificationList.get(0).getUser().getId());
        assertEquals(StreamStartNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        notificationList = notificationRepository.findAllByUser(map.get("LIKE"));
        assertNotNull(notificationList);
        assertFalse(notificationList.isEmpty());
        assertEquals(stream.getId(), notificationList.get(0).getStream().getId());
        assertEquals(map.get("LIKE"), notificationList.get(0).getUser().getId());
        assertEquals(StreamStartNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        scheduledNotificationRunner.sendStreamAndUserDataForStreamNotifications();
        TestUtils.sleep(1);
        optionalStream = streamRepository.findById(streamId);
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
            assertNotNull(stream);
            assertFalse(stream.getIsReadySendNotification());
        } else {
            fail("Стрим должен был быть найден");
        }

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void streamStartOneHourReminderNotificationTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);

        Long streamId = dto.getId();
        assertNotNull(streamId);

        Stream stream = null;
        Optional<Stream> optionalStream = streamRepository.findById(streamId);
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
            assertNotNull(stream);
        } else {
            fail("Стрим должен был быть найден");
        }

        stream.setCreateDate(ZonedDateTime.now(ZoneOffset.UTC).minusHours(10L));
        stream.setStartingDate(ZonedDateTime.now(ZoneOffset.UTC).minusMinutes(56L));
        streamRepository.saveAndFlush(stream);

        scheduledNotificationRunner.startStreamOneHourReminder(Collections.singletonList(streamId));
        TestUtils.sleep(2);
        List<StreamNotification> notificationList =  notificationRepository.findAllByUser(user.getId());
        assertFalse(notificationList.isEmpty());
        assertEquals(user.getId(), notificationList.get(0).getUser().getId());
        assertEquals(StreamOneHourReminderNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void streamStartFiveMinutesReminderNotificationTest() throws Exception{
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);

        Long streamId = dto.getId();
        assertNotNull(streamId);

        Stream stream = null;
        Optional<Stream> optionalStream = streamRepository.findById(streamId);
        if (optionalStream.isPresent()) {
            stream = optionalStream.get();
            assertNotNull(stream);
        } else {
            fail("Стрим должен был быть найден");
        }

        stream.setCreateDate(ZonedDateTime.now(ZoneOffset.UTC).minusHours(10L));
        stream.setStartingDate(ZonedDateTime.now(ZoneOffset.UTC).minusMinutes(3L));
        streamRepository.saveAndFlush(stream);

        scheduledNotificationRunner.startStreamFiveMinutesReminder(Collections.singletonList(streamId));
        TestUtils.sleep(2);
        List<StreamNotification> notificationList =  notificationRepository.findAllByUser(user.getId());
        assertFalse(notificationList.isEmpty());
        assertEquals(user.getId(), notificationList.get(0).getUser().getId());
        assertEquals(StreamFiveMinutesReminderNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void canUserCreateStreamNotificationTest() {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        removeUserCreateStreamAccess(user);

        getProductIdsForNewUser(user, 5, 0);
        scheduledNotificationRunner.canCreateStreamNotification();
        TestUtils.sleep(2);
        List<CanCreateStreamNotification> notificationList =  notificationRepository.findAllByUser(user.getId());
        assertTrue(notificationList.isEmpty());
        resetProductsToOldState();
        TestUtils.sleep(1);

        getProductIdsForNewUser(user, 0, 10);
        scheduledNotificationRunner.canCreateStreamNotification();
        TestUtils.sleep(2);
        notificationList =  notificationRepository.findAllByUser(user.getId());
        assertTrue(notificationList.isEmpty());
        resetProductsToOldState();
        TestUtils.sleep(1);

        getProductIdsForNewUser(user, 30, 10);
        scheduledNotificationRunner.canCreateStreamNotification();
        TestUtils.sleep(2);
        notificationList =  notificationRepository.findAllByUser(user.getId());
        assertTrue(notificationList.isEmpty());
        resetProductsToOldState();
        TestUtils.sleep(1);

        getProductIdsForNewUser(user, 33, 17);
        scheduledNotificationRunner.canCreateStreamNotification();
        TestUtils.sleep(2);
        notificationList =  notificationRepository.findAllByUser(user.getId());
        assertFalse(notificationList.isEmpty());
        assertEquals(user.getId(), notificationList.get(0).getUser().getId());
        assertEquals(CanCreateStreamNotification.class.getSimpleName(), notificationList.get(0).getDtype());

        user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        assertTrue(user.getCanCreateStream());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void subscribeToStreamTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);

        Long streamId = dto.getId();
        assertNotNull(streamId);
        logout(authentication);

        authentication = login(NEW_USER_LOGIN_PASS.get("david_test_2").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_2").getPassword());
        User follower = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_2").getEmail());
        assertNotNull(follower);

        boolean subscribe = subscribeToStream(streamId);
        assertTrue(subscribe);
        StreamFollowing streamFollowing = streamFollowingRepository.findByStreamIdAndUserId(streamId, follower.getId());
        assertNotNull(streamFollowing);
        assertTrue(streamFollowing.getIsSubscribed());
        assertEquals(streamId, streamFollowing.getStreamId());
        assertEquals(follower.getId(), streamFollowing.getUserId());

        StreamDetailedResponseDTO getData = getStream(streamId);
        assertNotNull(getData);
        assertTrue(getData.getIsSubscribed());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void unsubscribeOnStreamTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        assertNotNull(user);
        addUserCreateStreamAccess(user);

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });

        assertNotNull(response);
        assertNotNull(response.getData());
        StreamDetailedResponseDTO dto = response.getData();
        assertNotNull(dto);

        Long streamId = dto.getId();
        assertNotNull(streamId);
        logout(authentication);

        authentication = login(NEW_USER_LOGIN_PASS.get("david_test_2").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_2").getPassword());
        User follower = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_2").getEmail());
        assertNotNull(follower);

        boolean subscribe = subscribeToStream(streamId);
        assertTrue(subscribe);
        StreamFollowing streamFollowing = streamFollowingRepository.findByStreamIdAndUserId(streamId, follower.getId());
        assertNotNull(streamFollowing);
        assertTrue(streamFollowing.getIsSubscribed());
        assertEquals(streamId, streamFollowing.getStreamId());
        assertEquals(follower.getId(), streamFollowing.getUserId());

        StreamDetailedResponseDTO getData = getStream(streamId);
        assertNotNull(getData);
        assertTrue(getData.getIsSubscribed());

        boolean unsubscribe = unsubscribeOnStream(streamId);
        assertTrue(unsubscribe);
        streamFollowing = streamFollowingRepository.findByStreamIdAndUserId(streamId, follower.getId());
        assertNull(streamFollowing);

        getData = getStream(streamId);
        assertNotNull(getData);
        assertFalse(getData.getIsSubscribed());

        removeUserCreateStreamAccess(user);
        logout(authentication);
    }

    @Test
    public void createStreamWhenUserIsBannedTest() throws Exception {
        Authentication authentication = login(adminEmail, adminPassword);
        userBanRepository.deleteAllInBatch();
        Long bannedUserId = NEW_USER_LOGIN_PASS.get("david_test_1").getId();
        addStreamBan(bannedUserId);
        List<UserBan> userBans = userBanRepository.findUserActiveAllBansOnAction(bannedUserId);
        assertNotNull(userBans);
        assertFalse(userBans.isEmpty());
        assertEquals(1, userBans.size());
        assertEquals(BanType.STREAM_BAN, userBans.get(0).getBanType());
        assertEquals(bannedUserId, userBans.get(0).getUserId());
        logout(authentication);

        authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        addUserCreateStreamAccess(user);
        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        Api2_1Response<UserBanDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<UserBanDTO>>() {
        });
        assertNotNull(response);
        assertNull(response.getData());
        assertNotNull(response.getError());
        assertNotNull(response.getError().getData());
        UserBanDTO userBanDTO = response.getError().getData();
        assertEquals(BanType.STREAM_BAN, userBanDTO.getBanType());
        assertEquals(ErrorType.BAN, response.getError().getCode());
        assertEquals("Вы заблокированы и не можете создать эфир", response.getError().getHumanMessage());

        UserBanDTO actualBan = new UserBanDTO().map(userBans.get(0));

        assertEquals(actualBan.getId(), userBanDTO.getId());

        removeUserCreateStreamAccess(user);
        userBanRepository.deleteAllInBatch();
        logout(authentication);
    }

    @Test
    public void updateStreamWhenUserIsBannedTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        Long bannedUserId = user.getId();
        assertNotNull(bannedUserId);
        addUserCreateStreamAccess(user);
        userBanRepository.deleteAllInBatch();

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });
        assertNotNull(response);
        assertNotNull(response.getData());
        Long streamId = response.getData().getId();
        assertNotNull(streamId);

        logout(authentication);
        authentication = login(adminEmail, adminPassword);
        addStreamBan(bannedUserId);
        logout(authentication);

        authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        result = updateStream(streamId, productIds, ZonedDateTime.now(ZoneOffset.UTC).plusDays(2L), 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        Api2_1Response<UserBanDTO> errorResponse = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<UserBanDTO>>() {
        });
        assertNotNull(errorResponse);
        assertNull(errorResponse.getData());
        assertNotNull(errorResponse.getError());
        assertNotNull(errorResponse.getError().getData());
        UserBanDTO userBanDTO = errorResponse.getError().getData();
        assertEquals(BanType.STREAM_BAN, userBanDTO.getBanType());
        assertEquals(ErrorType.BAN, errorResponse.getError().getCode());
        assertEquals("Вы заблокированы и не можете обновить эфир", errorResponse.getError().getHumanMessage());

        removeUserCreateStreamAccess(user);
        userBanRepository.deleteAllInBatch();
        logout(authentication);
    }

    @Test
    public void deleteStreamWhenUserIsBannedTest() throws Exception {
        Authentication authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        User user = userService.getUserByEmail(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail());
        Long bannedUserId = user.getId();
        assertNotNull(bannedUserId);
        addUserCreateStreamAccess(user);
        userBanRepository.deleteAllInBatch();

        List<Long> productIds = getProductIdsForNewUser(user, 5, 0);

        MvcResult result = createStream(productIds, 3L, null, null, null, 200);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });
        assertNotNull(response);
        assertNotNull(response.getData());
        Long streamId = response.getData().getId();
        assertNotNull(streamId);

        logout(authentication);
        authentication = login(adminEmail, adminPassword);
        addStreamBan(bannedUserId);
        logout(authentication);

        authentication = login(NEW_USER_LOGIN_PASS.get("david_test_1").getEmail(), NEW_USER_LOGIN_PASS.get("david_test_1").getPassword());
        result = deleteStream(streamId, 400);
        assertNotNull(result);
        assertNotNull(result.getResponse());
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        Api2_1Response<UserBanDTO> errorResponse = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<UserBanDTO>>() {
        });
        assertNotNull(errorResponse);
        assertNull(errorResponse.getData());
        assertNotNull(errorResponse.getError());
        assertNotNull(errorResponse.getError().getData());
        UserBanDTO userBanDTO = errorResponse.getError().getData();
        assertEquals(BanType.STREAM_BAN, userBanDTO.getBanType());
        assertEquals(ErrorType.BAN, errorResponse.getError().getCode());
        assertEquals("Вы заблокированы и не можете удалить эфир", errorResponse.getError().getHumanMessage());

        removeUserCreateStreamAccess(user);
        userBanRepository.deleteAllInBatch();
        logout(authentication);
    }

    @Data
    @AllArgsConstructor
    private static class TestUser {
        private Long id;
        private String email;
        private String password;
        private String nickName;
    }

    private void createNewUser(String userName, int i) {
        apiV2Client = new ApiV2Client(null, null);
        String password = "12345!";

        UserService.EmailRegistrationRequest request = new UserService.EmailRegistrationRequest()
                .setRegisterEmail("david_test_" + i + "@david.am")
                .setRegisterNickname(userName)
                .setRegisterPassword(password)
                .setRegisterConfirmPassword(password)
                .setRegisterPhone("+7" + RandomStringUtils.randomNumeric(10));

        ResponseEntity<Api2Response<Long>> response = apiV2Client.request(accountTestSupport.getRegisterUrl(), null, HttpMethod.POST, TestUtils.getMultivalueMapWithObjectFields(request), new ParameterizedTypeReference<Api2Response<Long>>() {
        }, false);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        Long newUserId = response.getBody().getData();
        assertNotNull(newUserId);

        TestUser testUser = new TestUser(newUserId, "david_test_" + i + "@david.am", password, userName);
        NEW_USER_LOGIN_PASS.put(userName, testUser);
        apiV2Client.logout();
    }

    private List<Long> getProductIdsForNewUser(User user, int publishedProductLimit, int soldProductLimit) {
        List<Product> productList = new ArrayList<>();
        if (publishedProductLimit > 0) {
            productList.addAll(productRepository.getNotPresentedUserLimitedProductsByState(ProductState.PUBLISHED.name(), user.getId(), publishedProductLimit));
        }
        if (soldProductLimit > 0) {
            productList.addAll(productRepository.getNotPresentedUserLimitedProductsByState(ProductState.SOLD.name(), user.getId(), soldProductLimit));
        }

        List<Product> newProductList = new ArrayList<>();
        resetProductsToOldState();
        productList
                .forEach(product -> {
                    if (OLD_PRODUCT_BY_USER.get(product.getSeller().getId()) == null) {
                        List<Product> products = new ArrayList<>();
                        products.add(product);
                        OLD_PRODUCT_BY_USER.put(product.getSeller().getId(), products);
                    } else {
                        OLD_PRODUCT_BY_USER.get(product.getSeller().getId()).add(product);
                    }
                    product.setSeller(user);
                    newProductList.add(product);
                });
        productRepository.saveAll(newProductList);
        return newProductList.stream().map(Product::getId).collect(Collectors.toList());
    }

    private void resetProductsToOldState() {
        if (!OLD_PRODUCT_BY_USER.isEmpty()) {
            List<Product> productList = new ArrayList<>();
            OLD_PRODUCT_BY_USER.forEach((userId, products) -> {
                userRepository.findById(userId)
                        .ifPresent(user -> {
                            products.forEach(product -> product.setSeller(user));
                            productList.addAll(products);
                        });
            });
            productRepository.saveAll(productList);
            OLD_PRODUCT_BY_USER.clear();
        }
    }

    private MvcResult createStream(List<Long> productIds, Long plusDays, Long plusMinutes, Long minusDays, Long minusMinutes, int status) throws Exception {

        ZonedDateTime zonedDateTime = ZonedDateTime.now(ZoneOffset.UTC);
        if (plusDays != null) {
            zonedDateTime = zonedDateTime.plusDays(plusDays);
        } else if (plusMinutes != null) {
            zonedDateTime = zonedDateTime.plusMinutes(plusMinutes);
        } else if (minusDays != null) {
            zonedDateTime = zonedDateTime.minusDays(minusDays);
        } else if (minusMinutes != null) {
            zonedDateTime = zonedDateTime.minusMinutes(minusMinutes);
        } else {
            zonedDateTime = null;
        }
        StreamCreateOrUpdateRequestDTO requestDTO = new StreamCreateOrUpdateRequestDTO()
                .setDescription(TEST_DESCRIPTION)
                .setTitle(TEST_TITLE)
                .setProductIds(productIds)
                .setCover(TEST_COVER)
                .setStartingDate(zonedDateTime);

        String json = mapper.writeValueAsString(requestDTO);

        return mockMvc.perform(post(PATH_PREFIX + "/stream")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(json))
                .andExpect(status().is(status))
                .andReturn();

    }

    private MvcResult createStream(StreamCover streamCover, String title, String description, List<Long> productIds, Long plusDays, Long plusMinutes, Long minusDays, Long minusMinutes, int status) throws Exception {

        ZonedDateTime zonedDateTime = ZonedDateTime.now(ZoneOffset.UTC);
        if (plusDays != null) {
            zonedDateTime = zonedDateTime.plusDays(plusDays);
        } else if (plusMinutes != null) {
            zonedDateTime = zonedDateTime.plusMinutes(plusMinutes);
        } else if (minusDays != null) {
            zonedDateTime = zonedDateTime.minusDays(minusDays);
        } else if (minusMinutes != null) {
            zonedDateTime = zonedDateTime.minusMinutes(minusMinutes);
        } else {
            zonedDateTime = null;
        }

        StreamCreateOrUpdateRequestDTO requestDTO = new StreamCreateOrUpdateRequestDTO()
                .setDescription(description)
                .setTitle(title)
                .setProductIds(productIds)
                .setCover(streamCover)
                .setStartingDate(zonedDateTime);

        String json = mapper.writeValueAsString(requestDTO);

        return mockMvc.perform(post(PATH_PREFIX + "/stream")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(json))
                .andExpect(status().is(status))
                .andReturn();
    }

    private MvcResult updateStream(Long streamId, List<Long> productIds, ZonedDateTime startingDate, int status) throws Exception {

        StreamCreateOrUpdateRequestDTO requestDTO = new StreamCreateOrUpdateRequestDTO()
                .setDescription(TEST_DESCRIPTION)
                .setTitle(TEST_TITLE)
                .setProductIds(productIds)
                .setCover(TEST_COVER)
                .setStartingDate(startingDate);

        String json = mapper.writeValueAsString(requestDTO);


        return mockMvc.perform(patch(PATH_PREFIX + "/stream")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .param("streamId", String.valueOf(streamId))
                .content(json))
                .andExpect(status().is(status))
                .andReturn();
    }

    private MvcResult updateStream(Long streamId, StreamCover streamCover, String title, String description, List<Long> productIds, ZonedDateTime startingDate, int status) throws Exception {

        StreamCreateOrUpdateRequestDTO requestDTO = new StreamCreateOrUpdateRequestDTO()
                .setDescription(description)
                .setTitle(title)
                .setProductIds(productIds)
                .setCover(streamCover)
                .setStartingDate(startingDate);

        String json = mapper.writeValueAsString(requestDTO);

        return mockMvc.perform(patch(PATH_PREFIX + "/stream")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .param("streamId", String.valueOf(streamId))
                .content(json))
                .andExpect(status().is(status))
                .andReturn();
    }

    private MvcResult startStream(Long streamId, String broadcastId, int status) throws Exception {
        return mockMvc.perform(post(PATH_PREFIX + "/stream/start")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .param("streamId", String.valueOf(streamId))
                .param("broadcastId", broadcastId))
                .andExpect(status().is(status))
                .andReturn();
    }

    private StreamDetailedResponseDTO getStream(Long streamId) throws Exception {
        MvcResult result =  mockMvc.perform(get(PATH_PREFIX + "/stream")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .param("streamId", String.valueOf(streamId)))
                .andReturn();
        Api2_1Response<StreamDetailedResponseDTO> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<StreamDetailedResponseDTO>>() {
        });
        return response.getData();
    }

    private MvcResult getStream(Long streamId, int status) throws Exception {
        return mockMvc.perform(get(PATH_PREFIX + "/stream")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .param("streamId", String.valueOf(streamId)))
                .andExpect(status().is(status))
                .andReturn();
    }

    private Boolean subscribeToStream(Long streamId) throws Exception {
        MvcResult result = mockMvc.perform(post(PATH_PREFIX + "/stream/subscribe")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .param("streamId", String.valueOf(streamId)))
                .andExpect(status().isOk())
                .andReturn();
        Api2_1Response<Boolean> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<Boolean>>() {
        });
        return response.getData();
    }

    private Boolean unsubscribeOnStream(Long streamId) throws Exception {
        MvcResult result = mockMvc.perform(post(PATH_PREFIX + "/stream/unsubscribe")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .param("streamId", String.valueOf(streamId)))
                .andExpect(status().isOk())
                .andReturn();
        Api2_1Response<Boolean> response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2_1Response<Boolean>>() {
        });
        return response.getData();
    }

    private MvcResult deleteStream(Long streamId, int status) throws Exception {
        return mockMvc.perform(delete(PATH_PREFIX + "/stream")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .param("streamId", String.valueOf(streamId)))
                .andExpect(status().is(status))
                .andReturn();
    }

    private void addUserCreateStreamAccess(User user) {
        user.setCanCreateStream(true);
        userService.save(user);
    }

    private void removeUserCreateStreamAccess(User user) {
        user.setCanCreateStream(false);
        userService.save(user);
    }

    private void addStreamBan(Long userId) throws Exception {
        UserBanActionDTO publishBanDTO = new UserBanActionDTO(userId, BanType.STREAM_BAN,
                ZonedDateTime.now().plusDays(7L), "Test", null);

        String json = mapper.writeValueAsString(publishBanDTO);
        mockMvc.perform(post("/adminpanel/bans")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(json))
                .andExpect(status().isOk());
    }

    private Authentication login(String email, String password) {
        return securityService.authenticateByEmailAndRawPassword(email, password);
    }

    private void logout(Authentication authentication) {
        if (authentication.isAuthenticated()) {
            authentication.setAuthenticated(false);
        }
    }

    private void resetAndClearChangeData() {
        notificationRepository.deleteAllInBatch();
        streamFollowingRepository.deleteAllInBatch();
        streamRepository.deleteAllInBatch();
        resetProductsToOldState();
    }

    private Map<String, Long> addStreamDefaultFollowersIfNotExists(User user, List<Long> productIds, User followerUser, User subscribeUser, User commentUser, User likeUser) {

        final Map<String, Long> map = new HashMap<>();

        List<Long> followerIds = followingService.getFollowerIds(user);
        if (followerIds.isEmpty()) {
            followingService.toggle(followerUser, user);
            map.put("FOLLOWER", followerUser.getId());
        } else {
            map.put("FOLLOWER", followerIds.get(0));
        }

        List<Long> usersWhomCommentedProduct = commentService.getAllPublishersByProducts(productIds);
        if (usersWhomCommentedProduct.isEmpty()) {
            commentService.publishComment(commentUser.getId(), productIds.get(1), null, "Test Comment", null);
            map.put("COMMENT", commentUser.getId());
        } else {
            map.put("COMMENT", usersWhomCommentedProduct.get(0));
        }

        List<Long> usersWhomLikedProduct = likeService.findUsersWhichLikedProducts(productIds);
        if (usersWhomLikedProduct.isEmpty()) {
            productRepository.findById(productIds.get(2))
                    .ifPresent(product -> likeService.like(product, likeUser));
            map.put("LIKE", likeUser.getId());
        } else {
            map.put("LIKE", usersWhomLikedProduct.get(0));
        }
        return map;
    }
}
