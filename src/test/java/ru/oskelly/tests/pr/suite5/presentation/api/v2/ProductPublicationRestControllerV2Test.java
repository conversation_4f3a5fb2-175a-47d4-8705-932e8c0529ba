package ru.oskelly.tests.pr.suite5.presentation.api.v2;

import com.amazonaws.services.s3.AmazonS3;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableList;
import lombok.SneakyThrows;
import org.apache.commons.collections4.map.SingletonMap;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.util.UriComponentsBuilder;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.pr.common.bonuses.BonusesServiceTestConfiguration;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.TestApiConfiguration;
import su.reddot.domain.dao.BrandRepository;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.category.CategoryRepository;
import su.reddot.domain.dao.notification.NotificationRepository;
import su.reddot.domain.dao.product.ProductItemRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.exception.DefectImageException;
import su.reddot.domain.exception.ForbiddenException;
import su.reddot.domain.exception.ImageException;
import su.reddot.domain.exception.ProductNotFoundException;
import su.reddot.domain.exception.WrongDefectImageCommentException;
import su.reddot.domain.exception.WrongDefectImagesCountException;
import su.reddot.domain.exception.WrongImagesCountException;
import su.reddot.domain.model.Brand;
import su.reddot.domain.model.activity.product.ProductActivity;
import su.reddot.domain.model.activity.product.publication.CreateDraftActivity;
import su.reddot.domain.model.activity.product.publication.DeleteProductActivity;
import su.reddot.domain.model.activity.product.publication.EditDraftActivity;
import su.reddot.domain.model.activity.product.publication.EditOnModerationActivity;
import su.reddot.domain.model.activity.product.publication.EditSecondEditionActivity;
import su.reddot.domain.model.activity.product.publication.SendToModerationActivity;
import su.reddot.domain.model.activity.product.publication.UploadDefectPhotoActivity;
import su.reddot.domain.model.activity.product.publication.UploadProductPhotoActivity;
import su.reddot.domain.model.category.Category;
import su.reddot.domain.model.localization.SizeTypeLocalized;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.notification.product.CartPriceDecreasedNotification;
import su.reddot.domain.model.notification.product.LikePriceDecreasedNotification;
import su.reddot.domain.model.notification.product.PrivateSellerProductPublishedNotification;
import su.reddot.domain.model.notification.product.ProductNotification;
import su.reddot.domain.model.notification.product.ProductsPublishedNotification;
import su.reddot.domain.model.notification.product.publication.ModerationFailedNotification;
import su.reddot.domain.model.notification.product.publication.ModerationPassedNotification;
import su.reddot.domain.model.notification.product.publication.ModerationRejectedNotification;
import su.reddot.domain.model.notification.product.publication.ProductSentToModerationNotification;
import su.reddot.domain.model.notification.product.publication.ProductSentToModerationNotificationAgain;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.ProductRejectReason;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.product.SalesChannel;
import su.reddot.domain.model.size.SizeType;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.activity.ActivityService;
import su.reddot.domain.service.adminpanel.tag.UserCommonTagService;
import su.reddot.domain.service.adminpanel.tag.domain.UpdateUserTagsRequest;
import su.reddot.domain.service.adminpanel.tag.domain.UserCommonTagDTO;
import su.reddot.domain.service.brand.BrandService;
import su.reddot.domain.service.cart.CartService;
import su.reddot.domain.service.catalog.CategoryService;
import su.reddot.domain.service.catalog.CategoryTree;
import su.reddot.domain.service.comment.product.ProductCommentService;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.domain.service.dto.BrandDTO;
import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.domain.service.dto.ProductImageDTO;
import su.reddot.domain.service.dto.ProductRejectReasonDTO;
import su.reddot.domain.service.dto.SizeValueDTO;
import su.reddot.domain.service.dto.attribute.AttributeDTO;
import su.reddot.domain.service.dto.attribute.AttributeValueDTO;
import su.reddot.domain.service.following.FollowingService;
import su.reddot.domain.service.like.LikeService;
import su.reddot.domain.service.notification.NotificationService;
import su.reddot.domain.service.product.ProductRejectReasonService;
import su.reddot.domain.service.product.ProductService;
import su.reddot.domain.service.productpublication.Conversion;
import su.reddot.domain.service.productpublication.exception.RequiredAttributeNotSetException;
import su.reddot.domain.service.productpublication.exception.WrongAdditionalSizeException;
import su.reddot.domain.service.productpublication.exception.WrongAttributeValueException;
import su.reddot.domain.service.productpublication.exception.WrongBrandException;
import su.reddot.domain.service.productpublication.exception.WrongCategoryException;
import su.reddot.domain.service.productpublication.exception.WrongConditionException;
import su.reddot.domain.service.productpublication.exception.WrongImageException;
import su.reddot.domain.service.productpublication.exception.WrongPriceException;
import su.reddot.domain.service.productpublication.exception.WrongSizeException;
import su.reddot.domain.service.subscription.SubscriptionService;
import su.reddot.domain.service.task.ScheduledNotificationRunner;
import su.reddot.domain.service.task.ScheduledPrivateSellerProductPublicationNotificationCreatorRunner;
import su.reddot.domain.service.user.UserService;
import su.reddot.domain.service.user.tag.UserTagService;
import su.reddot.infrastructure.security.EmailAuthenticationFilter;
import su.reddot.infrastructure.util.Utils;
import su.reddot.presentation.api.v2.Api2Response;

import java.awt.image.BufferedImage;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@ContextConfiguration(classes = {BonusesServiceTestConfiguration.class})
@DevSuite(value = TestSuiteName.TEST_SUITE_05)
public class ProductPublicationRestControllerV2Test extends AbstractSpringTest {

    @Autowired
    private TestApiConfiguration testApiConfiguration;

	@Value("${test.api.user-id}")
	private Long userId;
    @Value("${test.api.user-email}")
    private String email;
    @Value("${test.api.user-password}")
    private String password;

	@Value("${test.api.user2-id}")
	private Long user2Id;

    @Value("${test.api.user2-email}")
    private String user2email;

    @Value("${test.api.user2-password}")
    private String user2password;

    @Value("${app.publication.moderation-timeout-hours}")
    private long moderationTimeoutHours;

	@Value("${test.temp-dir}")
	private String tmpDirPath;

    private Long wrongBrandId = -1L;
    private Long brandId = 3L;
    private Long wrongCategoryId = 2L;
    private Long categoryId = 154L;
    private Long category2Id = 153L;
    private List<Long> attributeIds = Arrays.asList(25L, 138L);
    private List<Long> attributeIds2 = Arrays.asList(26L, 139L);
    private List<Long> wrongAttributesNotFound = Arrays.asList(2L, 138L);
    private List<Long> wrongAttributesNotSupported = Arrays.asList(3L, 138L);
    private SizeType sizeType = SizeType.RU;
    private List<Long> sizeIds = Arrays.asList(269L, 270L);
    private List<Long> wrongSizeIds = Arrays.asList(269L, 270L, 5000000L);
    private List<Long> additionalSizeIds =  Arrays.asList(4L);
    private List<Long> wrongAdditionalSizeIds =  Arrays.asList(4L, 5L);
    private Integer additionalSizeValue = 300;
    private int sizeCount = 1;
    private int wrongSizeCount = 0;
    private Long conditionId = 1L;
    private Long wrongConditionId = 100L;
    private int imagesCount = 4;
	private int defectImagesCount = 3;
    private List<Long> wrongImageIds = Arrays.asList(3L, 4L, 5L, 6L);
	private List<Long> wrongDefectImageIds = Arrays.asList(99999999990L, 99999999991L, 99999999992L);
    private BigDecimal price = new BigDecimal(1000000);
    private BigDecimal wrongPrice = new BigDecimal(100);
	private BigDecimal rrpPrice = new BigDecimal(5000000);
    private String rejectImageComment = "На фото товара не допускаются изображения женщин с размером груди менее 100";
    private BigDecimal rejectOldPrice = new BigDecimal(1000000000);
    private String rejectPriceComment = "Слишком высокая цена за рваные колготки!";
    private String rejectOldDescription = "Продаю офигенские колготки, которые надевала на концерте в Бобруйске! Передаю привет своим фанатам!";
    private String rejectDescriptionComment = "Запрещено передавать приветы в описании товара";
    private String rejectOtherComment = "Бирка должна быть оригинальной";
    private BigDecimal rejectPrice = new BigDecimal(1000000000);
    private long femaleBeautyLeafCategoryId = 364L;

    @Autowired
    private ProductRejectReasonService rejectService;
    @Autowired
    UserService userService;
    @Autowired
    SubscriptionService subscriptionService;
    @Autowired
	FollowingService followingService;
    @Autowired
    ProductCommentService commentService;
    @Autowired
    LikeService likeService;
    @Autowired
	ProductService productService;
	@Autowired
	CategoryService categoryService;
    @Autowired
	NotificationService notificationService;
    @Autowired
	CartService cartService;
	@Autowired
	ActivityService activityService;
	@Autowired
    BrandService brandService;
	@Autowired
    ScheduledNotificationRunner scheduledNotificationRunner;
    @Autowired
    ScheduledPrivateSellerProductPublicationNotificationCreatorRunner scheduledPrivateSellerProductPublicationNotificationCreatorRunner;
    @Autowired
    CommissionGridService commissionGridService;

    @Autowired
    UserRepository userRepository;
    @Autowired
    private ProductRepository productRepository;
    @Autowired
    private ProductItemRepository productItemRepository;
	@Autowired
	CategoryRepository categoryRepository;
    @Autowired
    NotificationRepository notificationRepository;
    @Autowired
    BrandRepository brandRepository;

    @Autowired
    UserTagService userTagService;

    @Autowired
    private AmazonS3 amazonS3client;

    @Autowired
    private MessageSourceAccessor messageSourceAccessor;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private UserCommonTagService userCommonTagService;

    private static boolean isBeforeExecuted = false;

    private static List<String> cookie;
    private static Long draftId;
    private static List<ProductImageDTO> imagesDTO = new ArrayList<>();
	private static List<ProductImageDTO> defectImagesDTO = new ArrayList<>();

    private TestRestTemplate restTemplate = TestUtils.getTestRestTemplate();

    private String getServiceAddress(){
        return testApiConfiguration.getServerUrl() + "/api/v2/productpublication";
    }

    private String getDraftsUrl(){
        return getServiceAddress() + "/drafts";
    }

    private String getPublishUrl(){
        return getServiceAddress() + "/publish";
    }

    private String getPublishAndGetResultUrl(){
        return getServiceAddress() + "/publishAndGetResult";
    }

    private String getProductUrl(Long productId){
        return getServiceAddress() + "/product/" + productId;
    }

    private String getUploadProductImageUrl(){
        return getServiceAddress() + "/uploadProductImage";
    }

	private String getUploadDefectImageUrl(){
		return getServiceAddress() + "/uploadDefectImage";
	}

    private String getDeleteProductImageUrl(){
        return getServiceAddress() + "/deleteProductImage";
    }

	private String getDeleteDefectImageUrl(){
		return getServiceAddress() + "/deleteDefectImage";
	}

    private String getDeleteProductUrl(Long productId){
        return getServiceAddress() + "/product/" + productId;
    }

	private String getRejectsUrl(){
		return getServiceAddress() + "/rejects";
	}

	private String getSecondEditionsUrl(){
		return getServiceAddress() + "/secondEditionProducts";
	}

	private String getModerationProductsUrl(){
		return getServiceAddress() + "/moderationProducts";
	}

    private String getProductsUrl(){
        return getServiceAddress() + "/products";
    }

    private String getProductsPageUrl(){
        return getServiceAddress() + "/productsPage";
    }

    private String getCommissionUrl(double price){
        return getServiceAddress() + "/commission?price="+price;
    }

    private String getPriceWithCommissionUrl(){
        return getServiceAddress() + "/getPriceWithCommission";
    }

    private String getPriceWithoutCommissionUrl(){
        return getServiceAddress() + "/getPriceWithoutCommission";
    }

    private String getConversionUrl(){
		return getServiceAddress() + "/conversion";
	}

    private String getProductsCountUrl(){
        return getServiceAddress() + "/productsCount";
    }

    private String getProductsCountsUrl(){
        return getServiceAddress() + "/productsCounts";
    }

    private String getCategoryTreeUrl(){
        return getServiceAddress() + "/categoryTree";
    }

	private String getAttributesUrl(){
		return getServiceAddress() + "/attributes";
	}

	private String getBrandsUrl(){
		return getServiceAddress() + "/brands";
	}

	private String getAttributesUrl(Long categoryId){
		return getAttributesUrl() + "/" + categoryId;
	}

	private String getCatalogAddress(){
		return testApiConfiguration.getServerUrl() + "/api/v2/catalog";
	}

	private String getCatalogProductUrl(Long productId){
		return getCatalogAddress() + "/products/" + productId;
	}

    private Map<Long, Integer> getAdditionalSizeValues(boolean wrongAdditionalSizes){
        Map<Long, Integer> result = new HashMap<>();
        List<Long> ids = wrongAdditionalSizes ? wrongAdditionalSizeIds : additionalSizeIds;
        ids.forEach(id -> {
            result.put(id, additionalSizeValue);
        });
        return result;
    }

    private List<SizeValueDTO> getSizes(boolean wrongSizes, boolean withAdditionalSizes, boolean wrongAdditionalSizes, boolean wrongCount) {
        List<SizeValueDTO> result = new ArrayList<>();
        List<Long> mySizeIds = wrongSizes ? wrongSizeIds : sizeIds;
        Map<Long, Integer> additionalSizeValues = getAdditionalSizeValues(wrongAdditionalSizes);
        int mySizeCount = wrongCount ? wrongSizeCount : sizeCount;

        mySizeIds.forEach(sizeId -> {
            Map<Long, Integer> myAdditionalSizeValues = withAdditionalSizes ? additionalSizeValues : null;
            SizeValueDTO sizeDTO = new SizeValueDTO().setId(sizeId).setCount(mySizeCount).setAdditionalSizeValues(myAdditionalSizeValues).setSku(sizeId.toString());
            result.add(sizeDTO);
        });

        return result;
    }

    private Map<String, String> getAuthorizeParams(){
        Map<String, String> result = new HashMap<>();
        result.put("email", email);
        result.put("password", TestUtils.getApiHashedPassword(password, email));
        return result;
    }

    private Map<String, String> getUser2AuthorizeParams(){
        Map<String, String> result = new HashMap<>();
        result.put("email", user2email);
        result.put("password", TestUtils.getApiHashedPassword(user2password, user2email));
        return result;
    }

    private HttpHeaders getAuthorizeHeaders(){
        HttpHeaders headers = new HttpHeaders();
        headers.add(EmailAuthenticationFilter.AUTH_EMAIL_HEADER, email);
        headers.add(EmailAuthenticationFilter.AUTH_HASHED_PASS_HEADER, TestUtils.getApiHashedPassword(password, email));
        return headers;
    }

    private HttpHeaders getUser2AuthorizeHeaders() {
        HttpHeaders headers = new HttpHeaders();

        headers.add(EmailAuthenticationFilter.AUTH_EMAIL_HEADER, user2email);
        headers.add(
                EmailAuthenticationFilter.AUTH_HASHED_PASS_HEADER,
                TestUtils.getApiHashedPassword(user2password, user2email)
        );

        return headers;
    }

    private String getUriWithParams(String url, Map<String, String> params){
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(url);
        params.forEach(builder::queryParam);
        return builder.toUriString();
    }

    private HttpHeaders getHeaders(MediaType mediaType){
        HttpHeaders result = new HttpHeaders();
        if(mediaType != null) result.setContentType(mediaType);
        ApiV2Client.defaultHeaders.forEach((k, v) -> result.set(k, v));
        cookie.forEach(s -> {
            result.add(HttpHeaders.COOKIE, s);
        });
        return result;
    }

    private HttpEntity getHttpEntity(Object body, MediaType mediaType){
        return body == null ? new HttpEntity<String>(getHeaders(mediaType)) : new HttpEntity<Object>(body, getHeaders(mediaType));
    }

	private HttpEntity getHttpEntity(Object body){
		return body == null ? new HttpEntity<String>(getHeaders(null)) : new HttpEntity<Object>(body, getHeaders(null));
	}

	@SneakyThrows
    private ProductDTO updateProductSuccessful(ProductDTO request) {
        String stringRequest = objectMapper.writeValueAsString(request);
        ResponseEntity<String> response = restTemplate.exchange(getPublishAndGetResultUrl(), HttpMethod.POST, getHttpEntity(stringRequest, MediaType.APPLICATION_JSON), String.class);
		assertSame(response.getStatusCode(), HttpStatus.OK);
		assertNotNull(response.getBody());
        Api2Response<ProductDTO> body = objectMapper.readValue(response.getBody(), new TypeReference<Api2Response<ProductDTO>>() {});
        assertNotNull(body);
		assertNotNull(body.getData().getProductState());
		assertEquals(draftId, body.getData().getProductId());
		return body.getData();
	}

    private void updateProductUnSuccessful(ProductDTO request, Class errorType){
        ResponseEntity<String> response = restTemplate.exchange(getPublishUrl(), HttpMethod.POST, getHttpEntity(request, MediaType.APPLICATION_JSON), String.class );
	    assertSame(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertTrue(TestUtils.hasError(response.getBody(), errorType));
    }

    private ProductDTO getProductSuccessful(Long id){
        ResponseEntity<String> response = restTemplate.exchange(getProductUrl(id), HttpMethod.GET, getHttpEntity(null), String.class);
	    assertSame(response.getStatusCode(), HttpStatus.OK);
	    ProductDTO productDTO = getProductDtoApiResponseFromString(response.getBody()).getData();
	    assertEquals(draftId, productDTO.getProductId());
	    assertNotNull(productDTO.getCategory());
	    assertNotNull(productDTO.getCategory().getId());
	    assertNotNull(productDTO.getCategory().getDisplayName());
	    assertNotNull(productDTO.getCategory().getFullName());
        assertNotNull(productDTO.getCategory().getDefaultSizeType());
	    assertNotNull(productDTO.getBrand());
	    assertNotNull(productDTO.getBrand().getId());
	    assertNotNull(productDTO.getBrand().getName());
	    assertNotNull(productDTO.getCreateTimestamp());
	    assertNotNull(productDTO.getChangeTimestamp());
	    assertNotNull(productDTO.getProductStateTimestamp());
	    assertNotNull(productDTO.getParentCategories());
	    assertFalse(productDTO.getParentCategories().isEmpty());
        return productDTO;
    }

    @SneakyThrows
    private Api2Response<ProductDTO> getProductDtoApiResponseFromString(String stringBody) {
        return objectMapper.readValue(stringBody, new TypeReference<Api2Response<ProductDTO>>() {});
    }

	@SneakyThrows
    private ProductDTO getProductFromCatalogSuccessful(Long id){
		ResponseEntity<String> response = restTemplate.exchange(getCatalogProductUrl(id), HttpMethod.GET, getHttpEntity(null), String.class);
		assertSame(response.getStatusCode(), HttpStatus.OK);
        Api2Response<ProductDTO> body = objectMapper.readValue(response.getBody(), new TypeReference<Api2Response<ProductDTO>>(){});
		ProductDTO productDTO = body.getData();
		assertEquals(draftId, productDTO.getProductId());
		assertNotNull(productDTO.getCategory());
		assertNotNull(productDTO.getCategory().getId());
		assertNotNull(productDTO.getCategory().getDisplayName());
        assertNotNull(productDTO.getCategory().getFullName());
        assertNotNull(productDTO.getCategory().getDefaultSizeType());
		assertNotNull(productDTO.getBrand());
		assertNotNull(productDTO.getBrand().getId());
		assertNotNull(productDTO.getBrand().getName());
		assertNotNull(productDTO.getCreateTimestamp());
		assertNotNull(productDTO.getChangeTimestamp());
		assertNotNull(productDTO.getProductStateTimestamp());
		assertNotNull(productDTO.getParentCategories());
		assertFalse(productDTO.getParentCategories().isEmpty());
		assertNotNull(productDTO.getImages());
		assertNotNull(productDTO.getDefectImages());
		assertEquals(productDTO.getImages().size(), imagesDTO.size());
		assertEquals(productDTO.getDefectImages().size(), defectImagesDTO.size());

		for(ProductImageDTO image : productDTO.getImages()){

            // так как ресайз в отдельном сервисе, то надо проверить, что загрузилось исходное изображение
			TestUtils.assertImageAvailable(
                    TestUtils.getUrlPath(testApiConfiguration.getServerUrl(),  image.getPath().replaceFirst("item-", "")), tmpDirPath);
		}

		for(ProductImageDTO image : productDTO.getDefectImages()){
            // так как ресайз в отдельном сервисе, то надо проверить, что загрузилось исходное изображение
			TestUtils.assertImageAvailable(
                    TestUtils.getUrlPath(testApiConfiguration.getServerUrl(),  image.getPath().replaceFirst("item-", "")), tmpDirPath);
		}

		return body.getData();
	}

    private void setAttributesUnSuccessful(List<Long> wrongAttributes){
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        request.setAttributeValueIds(wrongAttributes);

        updateProductUnSuccessful(request, WrongAttributeValueException.class);

        ResponseEntity<Api2Response<ProductDTO>> response2 = restTemplate.exchange(getProductUrl(draftId), HttpMethod.GET, getHttpEntity(null), new ParameterizedTypeReference<Api2Response<ProductDTO>>() {});
	    assertSame(response2.getStatusCode(), HttpStatus.OK);
        assertTrue(!Utils.equalsIgnoreOrder(wrongAttributes, response2.getBody().getData().getAttributeValueIds()));
	    assertNull(response2.getBody().getData().getAttributeValueIds());
    }

    private List<ProductImageDTO> getWrongImagesDTO(){
        List<ProductImageDTO> result = new ArrayList<>();
        wrongImageIds.forEach(id -> {
            result.add(new ProductImageDTO(id));
        });
        return result;
    }

	private List<ProductImageDTO> getWrongDefectImagesDTO(){
		List<ProductImageDTO> result = new ArrayList<>();
		wrongDefectImageIds.forEach(id -> {
			result.add(new ProductImageDTO(id).setComment("Дефект " + id));
		});
		return result;
	}

	private List<ProductImageDTO> getDefectImagesDTO(){
		List<ProductImageDTO> result = new ArrayList<>();
		for(ProductImageDTO image : defectImagesDTO){
			result.add(new ProductImageDTO(image.getId()).setComment(image.getComment()));
		}
		return result;
	}

    private ProductImageDTO uploadProductImageSuccessful(File file, Long productId, int imageOrder) throws IOException {
	    return uploadAnyImageSuccessful(getUploadProductImageUrl(), file, productId, imageOrder, null);
    }

	private ProductImageDTO uploadDefectImageSuccessful(File file, Long productId, int imageOrder, String comment) throws IOException {
		return uploadAnyImageSuccessful(getUploadDefectImageUrl(), file, productId, imageOrder, comment);
	}

	private ProductImageDTO uploadAnyImageSuccessful(String serviceUrl, File file, Long productId, int imageOrder, String comment) throws IOException {
		HttpHeaders headers = getHeaders(MediaType.MULTIPART_FORM_DATA);
		MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
		FileSystemResource fileResource = new FileSystemResource(file);
		body.add("image", fileResource);
		body.add("productId", productId);
		body.add("imageOrder", imageOrder);
		if(comment != null) body.add("comment", comment);

		HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
		ResponseEntity<Api2Response<ProductImageDTO>> response = restTemplate.exchange(serviceUrl, HttpMethod.POST, requestEntity, new ParameterizedTypeReference<Api2Response<ProductImageDTO>>() {});
		assertSame(response.getStatusCode(), HttpStatus.OK);
		String imagePath = response.getBody().getData().getPath();
        // так как ресайз в отдельном сервисе, то надо проверить, что загрузилось исходное изображение
		downloadImageSuccessful(imagePath.replaceFirst("item-", ""));
		return response.getBody().getData();
	}

    private List<Long> getImageIds(List<ProductImageDTO> images){
        if(images == null) return null;
        return images.stream().map(i -> i.getId()).collect(Collectors.toList());
    }

    @SneakyThrows
    private ProductDTO getProductDTOByIdFromResponse(ResponseEntity<String> response, Long productId) {
        assertSame(response.getStatusCode(), HttpStatus.OK);
        assertNotNull(response.getBody());
        Api2Response<List<ProductDTO>> body = objectMapper.readValue(response.getBody(), new TypeReference<Api2Response<List<ProductDTO>>>(){});
        assertNotNull(body.getData());
        assertFalse(body.getData().isEmpty());
        return body.getData().stream().filter(dto -> dto.getProductId().equals(productId)).findFirst().get();
    }

    private Product getProduct(Long productId){
        Product product = productRepository.findById(productId).orElse(null);
        assertNotNull(product);
        return product;
    }

    private User getUser(){
        User user = userService.getUserByEmail(email);
        assertNotNull(user);
        return user;
    }

    private void setProductState(Long productId, ProductState productState){
	    productService.updateState(productId, productState, ProductService.UserType.SYSTEM);
    }

    @SneakyThrows
    private boolean resourceContainsListWithProduct(String url, Long productId){
	    ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, getHttpEntity(null, null), new ParameterizedTypeReference<String>() {});
        assertSame(response.getStatusCode(), HttpStatus.OK);
        assertNotNull(response.getBody());
        Api2Response<List<ProductDTO>> body = objectMapper.readValue(response.getBody(), new TypeReference<Api2Response<List<ProductDTO>>>(){});
        if(body.getData() == null || body.getData().isEmpty()) return false;
        List<Long> productIds = body.getData().stream().map(ProductDTO::getProductId).collect(Collectors.toList());
        return productIds.contains(productId);
    }

    private ProductDTO getProductDTOByIdFromResource(String url, Long productId){
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, getHttpEntity(null, null), String.class);
        return getProductDTOByIdFromResponse(response, productId);
    }

    private BigDecimal getCommission(double price){
        ResponseEntity<Api2Response<BigDecimal>> response = restTemplate.exchange(getCommissionUrl(price), HttpMethod.GET, getHttpEntity(null), new ParameterizedTypeReference<Api2Response<BigDecimal>>() {});
        assertNotNull(response);
        assertNotNull(response.getBody());
        BigDecimal commission = response.getBody().getData();
        assertNotNull(commission);
        assertTrue(commission.doubleValue() > 0);
        assertTrue(commission.doubleValue() < 1);
        return commission;
    }

    private BigDecimal getPriceWithoutCommission(BigDecimal priceWithCommission, Long categoryId){
        Map<String, String> params = new HashMap<>();
        params.put("priceWithCommission", priceWithCommission.toString());
        if(categoryId != null) params.put("categoryId", categoryId.toString());
        String url = getUriWithParams(getPriceWithoutCommissionUrl(), params);
        ResponseEntity<Api2Response<BigDecimal>> response = restTemplate.exchange(url, HttpMethod.GET, getHttpEntity(null), new ParameterizedTypeReference<Api2Response<BigDecimal>>() {});
        assertNotNull(response);
        assertNotNull(response.getBody());
        BigDecimal result = response.getBody().getData();
        assertNotNull(result);
        return result;
    }

    private BigDecimal getPriceWithCommission(BigDecimal priceWithoutCommission, Long categoryId, boolean isPro) {
        Map<String, String> params = new HashMap<>();

        params.put("priceWithoutCommission", priceWithoutCommission.toString());

        if (categoryId != null) {
            params.put("categoryId", categoryId.toString());
        }

        String url = getUriWithParams(getPriceWithCommissionUrl(), params);
        ResponseEntity<Api2Response<BigDecimal>> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                isPro ? getHttpEntity(null) : new HttpEntity<>(getUser2AuthorizeHeaders()),
                new ParameterizedTypeReference<Api2Response<BigDecimal>>() {}
        );

        assertNotNull(response);
        assertNotNull(response.getBody());

        BigDecimal result = response.getBody().getData();

        assertNotNull(result);

        return result;
    }

    private Conversion getConversion(Double priceWithCommission,
                                     Double priceWithoutCommission,
                                     Long sellerId,
                                     SalesChannel salesChannel,
                                     boolean isPro) {
	    Map<String, String> params = new HashMap<>();

	    if (priceWithCommission != null) {
            params.put("priceWithCommission", priceWithCommission.toString());
        }
	    if (priceWithoutCommission != null) {
            params.put("priceWithoutCommission", priceWithoutCommission.toString());
        }
        if (sellerId != null) {
            params.put("sellerId", sellerId.toString());
        }
        if (salesChannel != null) {
            params.put("salesChannel", salesChannel.name());
        }

	    String url = getUriWithParams(getConversionUrl(), params);
	    ResponseEntity<Api2Response<Conversion>> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                isPro ? getHttpEntity(null) : new HttpEntity<>(getUser2AuthorizeHeaders()),
                new ParameterizedTypeReference<Api2Response<Conversion>>() {}
        );

	    assertNotNull(response);
	    assertNotNull(response.getBody());

	    Conversion result = response.getBody().getData();

	    assertNotNull(result);

	    return result;
    }

    private boolean downloadImage404(String imagePath) {
        try {
            URL url = new URL(TestUtils.getUrlPath(testApiConfiguration.getServerUrl(), imagePath));
            try (BufferedInputStream in = new BufferedInputStream(url.openStream())) {
                in.reset();
            }
        } catch (FileNotFoundException e) {
            return true;
        } catch (Exception e) {
            return false;
        }
        return false;
    }
    private BufferedImage downloadImageSuccessful(String imagePath) throws IOException {
	    //Ждем несколько секунд, т.к. на обработку картинки уходит время
	    TestUtils.sleep(3);
	    URL url = new URL(TestUtils.getUrlPath(testApiConfiguration.getServerUrl(), imagePath));
	    BufferedImage image = TestUtils.loadImageFromUrl(url, tmpDirPath);
	    assertNotNull(image);
	    assertTrue(image.getWidth() > 0);
	    assertTrue(image.getHeight() > 0);
	    return image;
    }

    private void cleanup(){
		User seller = getUser();
	    List<Product> sellerProducts = productRepository.getAllBySeller(seller);
	    for(Product product : sellerProducts){
	    	if(product.getProductState() == ProductState.DELETED) continue;
	    	try {
			    productService.delete(product, ProductService.UserType.SYSTEM);
		    }
		    catch (ForbiddenException e){
		    }
	    }
        userTagService.saveTags(seller, Collections.emptyList());
	    followingService.unfollow(userRepository.getOne(user2Id), seller);
	    //Для ускорения тестов лучше удалить все уведомления
	    //TestUtils.deleteUserNotifications(userId, notificationRepository);
        //TestUtils.deleteUserNotifications(user2Id, notificationRepository);
	    jdbcTemplate.execute("DELETE FROM notification");
    }

    @BeforeEach
    public void init(){
		if(isBeforeExecuted) return;
	    isBeforeExecuted = true;
		cleanup();
		followingService.follow(userRepository.getOne(user2Id), getUser());
        amazonS3client.createBucket("oskelly");
    }

    @Test
    public void _00_unauthorizedAccessDenied(){
        ResponseEntity<String> response = restTemplate.exchange(getDraftsUrl(), HttpMethod.GET, null, String.class);
	    assertSame(HttpStatus.FORBIDDEN, response.getStatusCode());
        assertTrue(TestUtils.hasError(response.getBody(), AccessDeniedException.class));
    }

    @Test
    public void _01_getSessionCookieAndDrafts(){
        ResponseEntity<String> response = restTemplate.exchange(getUriWithParams(getDraftsUrl(), getAuthorizeParams()), HttpMethod.GET, null, String.class);
        HttpHeaders headers = response.getHeaders();
        cookie = headers.get(HttpHeaders.SET_COOKIE);
        assertTrue(cookie.stream().filter(s -> s.contains("SESSION=")).count() > 0);
	    assertSame(HttpStatus.OK, response.getStatusCode());
	    //Пустой массив не упаковывается в JSON
        assertFalse(response.getBody().contains("\"data\":["));
    }

    @Test
    public void _02_getDraftsBySessionCookie(){
        ResponseEntity<String> response = restTemplate.exchange(getDraftsUrl(), HttpMethod.GET, getHttpEntity(null), String.class);
	    assertSame(response.getStatusCode(), HttpStatus.OK);
	    //Пустой массив не упаковывается в JSON
        assertFalse(response.getBody().contains("\"data\":["));
    }

    @Test
    public void _03_addDraftWithNoBrandFailed(){
        ProductDTO request = new ProductDTO();
        ResponseEntity<String> response = restTemplate.exchange(getPublishUrl(), HttpMethod.POST, getHttpEntity(request), String.class);
	    assertSame(response.getStatusCode(), HttpStatus.BAD_REQUEST);
        assertTrue(TestUtils.hasError(response.getBody(), WrongBrandException.class));
    }

    @Test
    public void _04_addDraftWithWrongBrandFailed(){
        ProductDTO request = new ProductDTO();
        request.setBrandId(wrongBrandId);
        request.setCategoryId(categoryId);
        ResponseEntity<String> response = restTemplate.exchange(getPublishUrl(), HttpMethod.POST, getHttpEntity(request), String.class);
	    assertSame(response.getStatusCode(), HttpStatus.BAD_REQUEST);
        assertTrue(TestUtils.hasError(response.getBody(), WrongBrandException.class));
    }

    @Test
    public void _04_01_addDraftWithHiddenBrandFailed(){
        BrandDTO hideBrand = brandService.getBrandDTO(brandId);
        //
        hideBrand.setIsHidden(true);
        brandService.updateBrand(hideBrand);
        //
        ProductDTO request = new ProductDTO();
        //
        request.setBrandId(brandId);
        request.setCategoryId(categoryId);
        ResponseEntity<String> response = restTemplate.exchange(getPublishUrl(), HttpMethod.POST, getHttpEntity(request), String.class);
        assertSame(response.getStatusCode(), HttpStatus.BAD_REQUEST);
        assertTrue(TestUtils.hasError(response.getBody(), WrongBrandException.class));
        //
        hideBrand.setIsHidden(false);
        brandService.updateBrand(hideBrand);
    }

    @Test
    public void _05_addDraftWithNoCategoryFailed(){
        ProductDTO request = new ProductDTO();
        request.setBrandId(brandId);
        ResponseEntity<String> response = restTemplate.exchange(getPublishUrl(), HttpMethod.POST, getHttpEntity(request), String.class);
	    assertSame(response.getStatusCode(), HttpStatus.BAD_REQUEST);
        assertTrue(TestUtils.hasError(response.getBody(), WrongCategoryException.class));
    }

    @Test
    public void _06_01_addDraftWithWrongCategoryFailed(){
        ProductDTO request = new ProductDTO();
        request.setBrandId(brandId);
        request.setCategoryId(wrongCategoryId);
        ResponseEntity<String> response = restTemplate.exchange(getPublishUrl(), HttpMethod.POST, getHttpEntity(request), String.class);
        assertSame(response.getStatusCode(), HttpStatus.BAD_REQUEST);
        assertTrue(TestUtils.hasError(response.getBody(), WrongCategoryException.class));
    }

    //Физик пытается установить категорию, в которой ему нельзя публиковать
    @Test
    public void _06_02_addDraftWithUnavailableCategoryFailed(){
        setUserPhys();
        ProductDTO request = new ProductDTO();
        request.setBrandId(brandId);
        request.setCategoryId(femaleBeautyLeafCategoryId);
        ResponseEntity<String> response = restTemplate.exchange(getPublishUrl(), HttpMethod.POST, getHttpEntity(request), String.class);
        assertSame(response.getStatusCode(), HttpStatus.BAD_REQUEST);
        assertTrue(TestUtils.hasError(response.getBody(), WrongCategoryException.class));
        assertTrue(response.getBody().contains("Публикация в данной категории невозможна"));
    }

    @Test
    public void _07_addDraftSuccessful(){
        ProductDTO request = new ProductDTO();
        request.setBrandId(brandId);
        request.setCategoryId(categoryId);
        ResponseEntity<Api2Response<Long>> response = restTemplate.exchange(getPublishUrl(), HttpMethod.POST, getHttpEntity(request), new ParameterizedTypeReference<Api2Response<Long>>() {} );
        draftId = response.getBody().getData();
	    assertSame(response.getStatusCode(), HttpStatus.OK);
        assertTrue(draftId != null && draftId > 0);
        //Активность добавления черновика появилась
	    getLastActivity(CreateDraftActivity.class, userId, draftId);
    }

    @Test
    public void _08_getProductSuccessful(){
        getProductSuccessful(draftId);
    }

    @Test
    public void _09_getProductWithNoCookieFailed(){
        ResponseEntity<String> response = restTemplate.exchange(getProductUrl(draftId), HttpMethod.GET, null, String.class);
        assertTrue(response.getStatusCode() == HttpStatus.FORBIDDEN);
        assertTrue(TestUtils.hasError(response.getBody(), AccessDeniedException.class));
    }
    @Test
    public void _10_1_setAttributesFailed() {
        setAttributesUnSuccessful(wrongAttributesNotFound);
        setAttributesUnSuccessful(wrongAttributesNotSupported);
    }

    //Физик пытается измени категорию на ту, в которой ему нельзя публиковать
    @Test
    public void _10_02_changeCategoryToUnavailableFailed(){
        setUserPhys();

        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        request.setCategoryId(femaleBeautyLeafCategoryId);
        ResponseEntity<String> response = restTemplate.exchange(getPublishUrl(), HttpMethod.POST, getHttpEntity(request), String.class);
        assertSame(response.getStatusCode(), HttpStatus.BAD_REQUEST);
        assertTrue(TestUtils.hasError(response.getBody(), WrongCategoryException.class));
        assertTrue(response.getBody().contains("Публикация в данной категории невозможна"));
    }

    @Test
    public void _11_01_getProductsCountSuccessful() {
        Map<String, String> params = getAuthorizeParams();
        params.put("productState", ProductState.DRAFT.name());
        ResponseEntity<Api2Response<Integer>> response = restTemplate.exchange(getUriWithParams(getProductsCountUrl(), params), HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<Integer>>() {});
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        assertTrue(response.getBody().getData() > 0);
    }

    @Test
    public void _11_02_getProductsCountHeaderAuthSuccessful() {
        HttpHeaders authorizeHeaders = getAuthorizeHeaders();
        ResponseEntity<Api2Response<Integer>> response =
                restTemplate.exchange(
                        getUriWithParams(getProductsCountUrl(), new SingletonMap<>("productState", ProductState.DRAFT.name())),
                        HttpMethod.GET,
                        new HttpEntity<>(authorizeHeaders),
                        new ParameterizedTypeReference<Api2Response<Integer>>() {});
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        assertTrue(response.getBody().getData() > 0);
    }

    @Test
    public void _12_getProductsCountsSuccessful() {
        Map<String, String> params = getAuthorizeParams();
        params.put("productStates", ProductState.DRAFT.name() + "," + ProductState.PUBLISHED.name());
        ResponseEntity<Api2Response<Map<ProductState, Integer>>> response = restTemplate.exchange(getUriWithParams(getProductsCountsUrl(), params), HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<Map<ProductState, Integer>>>() {});
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        assertTrue(response.getBody().getData().containsKey(ProductState.DRAFT));
        assertTrue(response.getBody().getData().get(ProductState.DRAFT) > 0);
    }

    @Test
    public void _12_getProductsCountsNoParamsSuccessful() {
        Map<String, String> params = getAuthorizeParams();
        ResponseEntity<Api2Response<Map<ProductState, Integer>>> response = restTemplate.exchange(getUriWithParams(getProductsCountsUrl(), params), HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<Map<ProductState, Integer>>>() {});
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        assertTrue(response.getBody().getData().containsKey(ProductState.DRAFT));
        assertTrue(response.getBody().getData().get(ProductState.DRAFT) > 0);
    }

    /**
     * Продавец видит дерево категорий, прошник - одно, физик - другое
     */
    @Test
    public void _13_getCategoryTreeProSuccessful() {
        Map<String, String> params = getAuthorizeParams();

        //Пробуем получить дерево под гостем
        ResponseEntity<Api2Response<CategoryTree>> response = restTemplate.exchange(getCategoryTreeUrl(), HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<CategoryTree>>() {});
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        CategoryTree categoryTree = response.getBody().getData();
        categoryTree.updateAllCategoriesMap(); //После десериализации нужна переиндексация
        assertFalse(categoryTree.containsCategory(femaleBeautyLeafCategoryId)); //Гость не видит категории Бьюти

        //Делаем продавца физиком
        setUserPhys();

        response = restTemplate.exchange(getUriWithParams(getCategoryTreeUrl(), params), HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<CategoryTree>>() {});
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        categoryTree = response.getBody().getData();
        categoryTree.updateAllCategoriesMap(); //После десериализации нужна переиндексация
        assertFalse(categoryTree.containsCategory(femaleBeautyLeafCategoryId)); //Физик не видит категории Бьюти

        //Делаем продавца прошником
        setUserPro();

        response = restTemplate.exchange(getUriWithParams(getCategoryTreeUrl(), params), HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<CategoryTree>>() {});
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        categoryTree = response.getBody().getData();
        categoryTree.updateAllCategoriesMap(); //После десериализации нужна переиндексация
        assertTrue(categoryTree.containsCategory(femaleBeautyLeafCategoryId)); //Прошник видит категорию Бьюти

    }

    @Test
    public void _20_setSizeTypeSuccessful() {
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        request.setSizeType(SizeTypeLocalized.fromSizeType(sizeType, messageSourceAccessor));

        ProductDTO result = updateProductSuccessful(request);
        assertNotNull(result.getFieldsLackingForModeration());
	    assertFalse(result.getFieldsLackingForModeration().isEmpty());
        ProductDTO productDTO = getProductSuccessful(draftId);
        assertNotNull(productDTO.getSizeType());
        assertSame(sizeType, productDTO.getSizeType().getSizeType());
    }

    @Test
    public void _21_setSizeWithoutAdditionalSizesSuccessful() {
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        List<SizeValueDTO> sizes = getSizes(false, false, false, false);
        request.setSizes(sizes);

        updateProductSuccessful(request);
        ProductDTO updatedProductDTO = getProductSuccessful(draftId);
	    assertNotNull(updatedProductDTO.getSizes());
	    assertFalse(updatedProductDTO.getSizes().isEmpty());
        assertEquals(sizes.get(0).getSku(), updatedProductDTO.getSizes().get(0).getSku());
    }

    @Test
    public void _22_setSizeWithAdditionalSizesSuccessful() {
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        List<SizeValueDTO> sizes = getSizes(false, true, false, false);
        request.setSizes(sizes);

        updateProductSuccessful(request);
	    ProductDTO updatedProductDTO = getProductSuccessful(draftId);
	    assertNotNull(updatedProductDTO.getSizes());
	    assertFalse(updatedProductDTO.getSizes().isEmpty());
	    assertNotNull(updatedProductDTO.getSizes().get(0).getAdditionalSizeValues());
	    assertFalse(updatedProductDTO.getSizes().get(0).getAdditionalSizeValues().isEmpty());
	    assertEquals(additionalSizeValue, updatedProductDTO.getSizes().get(0).getAdditionalSizeValues().get(additionalSizeIds.get(0)));
    }

    @Test
    public void _23_setSizeWithoutAdditionalSizesWrongCountUnSuccessful(){
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        List<SizeValueDTO> sizes = getSizes(false, false, false, true);
        request.setSizes(sizes);

        updateProductUnSuccessful(request, WrongSizeException.class);
    }

    @Test
    public void _24_setSizeWithWrongAdditionalSizesUnSuccessful(){
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        List<SizeValueDTO> sizes = getSizes(false, true, true, false);
        request.setSizes(sizes);

        updateProductUnSuccessful(request, WrongAdditionalSizeException.class);
    }

    @Test
    public void _25_setWrongSizesWithoutAdditionalSizesUnSuccessful(){
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        List<SizeValueDTO> sizes = getSizes(true, false, false, false);
        request.setSizes(sizes);

        updateProductUnSuccessful(request, WrongSizeException.class);
    }










    @Test
    public void _30_setSimpleValuesSuccessful(){
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);

        long randomLong = Math.round(Math.random() * 1000);
        boolean randomBoolean = randomLong > 500l;

        String description = "Описание товара " + randomLong;
        boolean isVintage = randomBoolean;
        String model = "Модель " + randomLong;
        String origin = "Происхождение " + randomLong;
        BigDecimal purchasePrice = new BigDecimal(randomLong);
        Integer purchaseYear = Integer.parseInt("" + randomLong * 2);
        String vendorCode = "Артикул производителя " + randomLong;
        String storeCode = "Артикул бутика " + randomLong;

        request.setDescription(description);
        request.setIsVintage(isVintage);
        request.setModel(model);
        request.setOrigin(origin);
        request.setPurchasePrice(purchasePrice);
        request.setPurchaseYear(purchaseYear);
        request.setVendorCode(vendorCode);
        request.setStoreCode(storeCode);

        updateProductSuccessful(request);

        ProductDTO updatedProduct = getProductSuccessful(draftId);

        assertEquals(description, updatedProduct.getDescription());
        assertTrue(updatedProduct.getIsVintage() != null && isVintage == updatedProduct.getIsVintage());
        assertEquals(model, updatedProduct.getModel());
        assertEquals(origin, updatedProduct.getOrigin());
        assertEquals(purchasePrice, updatedProduct.getPurchasePrice());
        assertEquals(purchaseYear, updatedProduct.getPurchaseYear());
        assertEquals(vendorCode, updatedProduct.getVendorCode());
        assertEquals(storeCode, updatedProduct.getStoreCode());

    }

    @Test
    public void _31_setDescriptionWithXSS(){
        String badText =
                "<p><a href='http://h4ck3rz-53cr3t-ar3a.com' onclick='stealCookies()' onfocus='stealCookies()'>Click me</a></p>";
        String cleanText = "<p><a href=\"http://h4ck3rz-53cr3t-ar3a.com\">Click me</a></p>";

        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        request.setDescription(badText);

        updateProductSuccessful(request);
        ProductDTO updatedProduct = getProductSuccessful(draftId);

        assertEquals(cleanText, updatedProduct.getDescription());
    }















    @Test
    public void _40_setConditionSuccessful(){
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        request.setConditionId(conditionId);

        updateProductSuccessful(request);
        assertTrue(conditionId.equals(getProductSuccessful(draftId).getConditionId()));
    }

    @Test
    public void _41_setConditionUnSuccessful(){
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        request.setConditionId(wrongConditionId);

        updateProductUnSuccessful(request, WrongConditionException.class);
        assertTrue(!wrongConditionId.equals(getProductSuccessful(draftId).getConditionId()));
        assertEquals(conditionId, getProductSuccessful(draftId).getConditionId());
    }













    @Test
    public void _50_0_setWrongImagesCountOnProductUnSuccessful(){
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        request.setImages(getWrongImagesDTO());

        updateProductUnSuccessful(request, WrongImagesCountException.class);

        assertNull(getProductSuccessful(draftId).getImages());
    }

	@Test
	public void _50_1_setWrongDefectImagesCountOnProductUnSuccessful(){
		ProductDTO request = new ProductDTO();
		request.setProductId(draftId);
		request.setDefectImages(getWrongDefectImagesDTO());

		updateProductUnSuccessful(request, WrongDefectImagesCountException.class);

		assertNull(getProductSuccessful(draftId).getDefectImages());
	}

    @Test
    public void _51_0_uploadImagesSuccessful() throws URISyntaxException, IOException {
        for(int i = 1; i <= imagesCount; i++) {
            URL url = this.getClass().getClassLoader().getResource("img/product/" + i + ".jpg");
            File file = Paths.get(url.toURI()).toFile();
            ProductImageDTO productImageDTO = uploadProductImageSuccessful(file, draftId, i);
            assertNotNull(productImageDTO.getId());
            imagesDTO.add(productImageDTO);
        }
        ProductDTO updatedProduct = getProductSuccessful(draftId);
        assertNotNull(updatedProduct.getImages());
        assertEquals(updatedProduct.getImages().size(), imagesCount);
        assertEquals(getImageIds(updatedProduct.getImages()), getImageIds(imagesDTO));
	    for(ProductImageDTO image : updatedProduct.getImages()){
            // так как ресайз в отдельном сервисе, то надо проверить, что загрузилось исходное изображение
	        downloadImageSuccessful(image.getPath().replaceFirst("item-", ""));
        }

	    //Активность загрузки фото товара появилась
	    getLastActivity(UploadProductPhotoActivity.class, userId, draftId);
    }

	@Test
	public void _51_1_uploadDefectImagesSuccessful() throws URISyntaxException, IOException {
		for(int i = 1; i <= defectImagesCount; i++) {
			URL url = this.getClass().getClassLoader().getResource("img/defect/" + i + ".jpg");
			File file = Paths.get(url.toURI()).toFile();
			String comment = "Дефект " + i;
			ProductImageDTO productImageDTO = uploadDefectImageSuccessful(file, draftId, i, comment);
			assertNotNull(productImageDTO.getId());
			assertEquals(comment, productImageDTO.getComment());
			defectImagesDTO.add(productImageDTO);
		}
		ProductDTO updatedProduct = getProductSuccessful(draftId);
		assertNotNull(updatedProduct.getDefectImages());
		assertEquals(updatedProduct.getDefectImages().size(), defectImagesCount);
		assertEquals(getImageIds(updatedProduct.getDefectImages()), getImageIds(defectImagesDTO));
		for(ProductImageDTO image : updatedProduct.getDefectImages()){
            // так как ресайз в отдельном сервисе, то надо проверить, что загрузилось исходное изображение
            downloadImageSuccessful(image.getPath().replaceFirst("item-", ""));
		}

		//Активность загрузки фото товара появилась
		getLastActivity(UploadDefectPhotoActivity.class, userId, draftId);
	}

	//Обновление фотографии. На первую позицию грузим вторую фотку
	@Test
	public void _51_2_updateImageSuccessful() throws URISyntaxException, IOException {
		URL url = this.getClass().getClassLoader().getResource("img/product/" + 2 + ".jpg");
		File file = Paths.get(url.toURI()).toFile();
		ProductImageDTO productImageDTO = uploadProductImageSuccessful(file, draftId, 1);
		assertNotNull(productImageDTO.getId());

		//Старое изображение удаляется, новое добавляется. ID будет другой, order тот же
        String oldPath = imagesDTO.get(0).getPath();
		imagesDTO.remove(imagesDTO.get(0));
		imagesDTO.add(0, productImageDTO);

		ProductDTO updatedProduct = getProductSuccessful(draftId);
		assertNotNull(updatedProduct.getImages());
		assertEquals(updatedProduct.getImages().size(), imagesCount);
		assertEquals(getImageIds(updatedProduct.getImages()), getImageIds(imagesDTO));
		//Проверяем эквивалентность картинок на 1 и 2-й позициях
        // так как ресайз в отдельном сервисе, то надо проверить, что загрузилось исходное изображение
		BufferedImage image1 = downloadImageSuccessful(
                updatedProduct.getImages().get(0).getPath().replaceFirst("item-", ""));
		BufferedImage image2 = downloadImageSuccessful(
                updatedProduct.getImages().get(1).getPath().replaceFirst("item-", ""));
		TestUtils.assertSameImages(image1, image2);

        // проверяем, что старое изображение удалено
        assertTrue(downloadImage404(oldPath));
	}

	//Обновление фотографии дефекта. На первую позицию грузим вторую фотку
	@Test
	public void _51_3_updateDefectImageSuccessful() throws URISyntaxException, IOException {
		URL url = this.getClass().getClassLoader().getResource("img/defect/" + 2 + ".jpg");
		File file = Paths.get(url.toURI()).toFile();
		String newComment = "Новый комментарий на фото с дефектом";
		ProductImageDTO productImageDTO = uploadDefectImageSuccessful(file, draftId, 1, newComment);
		assertNotNull(productImageDTO.getId());

		//Старое изображение удаляется, новое добавляется. ID будет другой, order тот же
        String oldPath = defectImagesDTO.get(0).getPath();
		defectImagesDTO.remove(defectImagesDTO.get(0));
		defectImagesDTO.add(0, productImageDTO);
		assertEquals(newComment, productImageDTO.getComment());

		ProductDTO updatedProduct = getProductSuccessful(draftId);
		assertNotNull(updatedProduct.getDefectImages());
		assertEquals(updatedProduct.getDefectImages().size(), defectImagesCount);
		assertEquals(getImageIds(updatedProduct.getDefectImages()), getImageIds(defectImagesDTO));
		//Проверяем эквивалентность картинок на 1 и 2-й позициях
        // так как ресайз в отдельном сервисе, то надо проверить, что загрузилось исходное изображение
		BufferedImage image1 = downloadImageSuccessful(
                updatedProduct.getDefectImages().get(0).getPath().replaceFirst("item-", ""));
		BufferedImage image2 = downloadImageSuccessful(
                updatedProduct.getDefectImages().get(1).getPath().replaceFirst("item-", ""));
		TestUtils.assertSameImages(image1, image2);

        // проверяем, что старое изображение удалено
        assertTrue(downloadImage404(oldPath));
	}

    @Test
    public void _51_4_updateDefectImageCommentXSS() throws URISyntaxException, IOException {
        URL url = this.getClass().getClassLoader().getResource("img/defect/" + 2 + ".jpg");
        File file = Paths.get(url.toURI()).toFile();
        String badComment = "<p><a href='http://h4ck.com' onfocus='stC()'>What?</a></p>";
        String cleanComment = "<p><a href=\"http://h4ck.com\">What?</a></p>";
        ProductImageDTO productImageDTO = uploadDefectImageSuccessful(file, draftId, 1, badComment);
        assertNotNull(productImageDTO.getId());

        //Старое изображение удаляется, новое добавляется. ID будет другой, order тот же
        String oldPath = defectImagesDTO.get(0).getPath();
        defectImagesDTO.remove(defectImagesDTO.get(0));
        defectImagesDTO.add(0, productImageDTO);

        assertEquals(cleanComment, productImageDTO.getComment());
    }

    @Test
    public void _52_0_setWrongImagesIdsOnOrderFailed(){
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        request.setImages(getWrongImagesDTO());

        updateProductUnSuccessful(request, ImageException.class);

        ProductDTO updatedProduct = getProductSuccessful(draftId);
        assertTrue(!getImageIds(updatedProduct.getImages()).equals(wrongImageIds));
        assertEquals(getImageIds(updatedProduct.getImages()), getImageIds(imagesDTO));
    }

	@Test
	public void _52_1_setWrongDefectImagesIdsOnOrderFailed(){
		ProductDTO request = new ProductDTO();
		request.setProductId(draftId);
		request.setDefectImages(getWrongDefectImagesDTO());

		updateProductUnSuccessful(request, DefectImageException.class);

		ProductDTO updatedProduct = getProductSuccessful(draftId);
		assertTrue(!getImageIds(updatedProduct.getDefectImages()).equals(wrongDefectImageIds));
		assertEquals(getImageIds(updatedProduct.getDefectImages()), getImageIds(defectImagesDTO));
	}

	@Test
	public void _52_3_setNullCommentForDefectImagesIdsOnOrderFailed(){
		ProductDTO request = new ProductDTO();
		request.setProductId(draftId);
		List<ProductImageDTO> defectImageDTOs = getDefectImagesDTO();
		for(ProductImageDTO image : defectImageDTOs){
			image.setComment(null);
		}
		request.setDefectImages(defectImageDTOs);

		updateProductUnSuccessful(request, WrongDefectImageCommentException.class);
	}

	@Test
	public void _52_4_setLongCommentForDefectImagesIdsOnOrderFailed(){
		ProductDTO request = new ProductDTO();
		request.setProductId(draftId);
		List<ProductImageDTO> defectImageDTOs = getDefectImagesDTO();
		for(ProductImageDTO image : defectImageDTOs){
			image.setComment("0123456789 0123456789 0123456789 0123456789 0123456789 0123456789");
		}
		request.setDefectImages(defectImageDTOs);

		updateProductUnSuccessful(request, WrongDefectImageCommentException.class);
	}

    @Test
    public void _53_0_setImagesOrderSuccessful(){
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);

        List<ProductImageDTO> reversedImagesDTO = new ArrayList<>(imagesDTO);
        Collections.reverse(reversedImagesDTO);

        request.setImages(reversedImagesDTO);

        updateProductSuccessful(request);

        ProductDTO updatedProduct = getProductSuccessful(draftId);

        assertTrue(!getImageIds(updatedProduct.getImages()).equals(getImageIds(imagesDTO)));
	    assertEquals(getImageIds(updatedProduct.getImages()), getImageIds(reversedImagesDTO));
	    for(int i = 0; i < updatedProduct.getImages().size(); i++){
            ProductImageDTO imageDTO = updatedProduct.getImages().get(i);
            assertTrue(imageDTO.getOrder().intValue() == i + 1);
        }

        imagesDTO = reversedImagesDTO;
    }

	@Test
	public void _53_1_setDefectImagesOrderSuccessful(){
		ProductDTO request = new ProductDTO();
		request.setProductId(draftId);

		List<ProductImageDTO> reversedImagesDTO = new ArrayList<>(defectImagesDTO);
		Collections.reverse(reversedImagesDTO);

		for(ProductImageDTO image : reversedImagesDTO){
			image.setComment("Измененный комментарий " + image.getId());
		}

		request.setDefectImages(reversedImagesDTO);

		updateProductSuccessful(request);

		ProductDTO updatedProduct = getProductSuccessful(draftId);

		assertTrue(!getImageIds(updatedProduct.getDefectImages()).equals(getImageIds(defectImagesDTO)));
		assertEquals(getImageIds(updatedProduct.getDefectImages()), getImageIds(reversedImagesDTO));
		for(int i = 0; i < updatedProduct.getDefectImages().size(); i++){
			ProductImageDTO imageDTO = updatedProduct.getDefectImages().get(i);
			assertTrue(imageDTO.getOrder().intValue() == i + 1);
			assertEquals("Измененный комментарий " + imageDTO.getId(), imageDTO.getComment());
		}

		defectImagesDTO = reversedImagesDTO;
	}

    @Test
    public void _54_0_deleteImageSuccessful(){
        int deleteImageIndex = (int) Math.round(Math.random() * (imagesCount - 1));
        Long deleteImageId = imagesDTO.get(deleteImageIndex).getId();

        HttpHeaders headers = getHeaders(MediaType.APPLICATION_FORM_URLENCODED);
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("productId", draftId.toString());
        body.add("imageId", deleteImageId.toString());

        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
        ResponseEntity<Api2Response<Long>> response = restTemplate.exchange(getDeleteProductImageUrl(), HttpMethod.POST, requestEntity, new ParameterizedTypeReference<Api2Response<Long>>() {});
	    assertSame(response.getStatusCode(), HttpStatus.OK);
	    assertEquals(deleteImageId, response.getBody().getData());

        ProductDTO updatedProduct = getProductSuccessful(draftId);

        assertTrue(!getImageIds(updatedProduct.getImages()).contains(deleteImageId));
	    assertEquals(updatedProduct.getImages().size(), imagesCount - 1);

        imagesCount--;
        imagesDTO = updatedProduct.getImages();
    }

	@Test
	public void _54_1_deleteDefectImageSuccessful(){
		int deleteImageIndex = (int) Math.round(Math.random() * (defectImagesCount - 1));
		Long deleteImageId = defectImagesDTO.get(deleteImageIndex).getId();

		HttpHeaders headers = getHeaders(MediaType.APPLICATION_FORM_URLENCODED);
		MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
		body.add("productId", draftId.toString());
		body.add("imageId", deleteImageId.toString());

		HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
		ResponseEntity<Api2Response<Long>> response = restTemplate.exchange(getDeleteDefectImageUrl(), HttpMethod.POST, requestEntity, new ParameterizedTypeReference<Api2Response<Long>>() {});
		assertSame(response.getStatusCode(), HttpStatus.OK);
		assertEquals(deleteImageId, response.getBody().getData());

		ProductDTO updatedProduct = getProductSuccessful(draftId);

		assertTrue(!getImageIds(updatedProduct.getDefectImages()).contains(deleteImageId));
		assertEquals(updatedProduct.getDefectImages().size(), defectImagesCount - 1);

		defectImagesCount--;
		defectImagesDTO = updatedProduct.getDefectImages();
	}

    @Test
    public void _55_deleteImageUnSuccessful(){
        int deleteImageIndex = (int) Math.round(Math.random() * (imagesCount - 1));
        Long deleteImageId = wrongImageIds.get(deleteImageIndex);

        HttpHeaders headers = getHeaders(MediaType.APPLICATION_FORM_URLENCODED);
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("productId", draftId.toString());
        body.add("imageId", deleteImageId.toString());

        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
        ResponseEntity<String> response = restTemplate.exchange(getDeleteProductImageUrl(), HttpMethod.POST, requestEntity, String.class);
	    assertSame(response.getStatusCode(), HttpStatus.BAD_REQUEST);
        assertTrue(TestUtils.hasError(response.getBody(), WrongImageException.class));

        ProductDTO updatedProduct = getProductSuccessful(draftId);

	    assertEquals(getImageIds(updatedProduct.getImages()), getImageIds(imagesDTO));
    }













    @Test
    public void _60_sendToModerationUnSuccessful(){
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        request.setProductState(ProductState.NEED_MODERATION);

        updateProductUnSuccessful(request, RequiredAttributeNotSetException.class);

        ProductDTO dto = getProductSuccessful(draftId);
	    assertNotNull(dto.getFieldsLackingForModeration());
	    assertFalse(dto.getFieldsLackingForModeration().isEmpty());
    }

    @Test
    public void _61_0_setAttributesSuccessful(){
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        request.setAttributeValueIds(attributeIds);

        updateProductSuccessful(request);
        assertTrue(Utils.equalsIgnoreOrder(attributeIds, getProductSuccessful(draftId).getAttributeValueIds()));

	    //Активность редактирования черновика появилась
	    getLastActivity(EditDraftActivity.class, userId, draftId);
    }

    @Test
    public void _61_1_updateAttributesSuccessful(){
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        request.setAttributeValueIds(attributeIds2);

        updateProductSuccessful(request);
        assertTrue(Utils.equalsIgnoreOrder(attributeIds2, getProductSuccessful(draftId).getAttributeValueIds()));
    }

    //Нельзя установить цену, ниже минимальной
    @Test
    public void _61_2_setWrongPriceFailed(){
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        request.setPrice(wrongPrice);
        request.setProductState(ProductState.NEED_MODERATION);
        updateProductUnSuccessful(request, WrongPriceException.class);
    }

    //В некоторых разделах возможна установка цены ниже минимальной
    @Test
    public void _61_3_setSmallPriceForBeautySuccessful(){
        //На всякий случай делаем продавца прошником, чтобы он мог публиковать в разделе Beauty
        setUserPro();
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        request.setCategoryId(femaleBeautyLeafCategoryId);
        request.setPrice(wrongPrice);
        updateProductSuccessful(request);
    }

    //Нельзя вернуть прежнюю категорию с минимальным значением цены 5000р, если уже установлена более низкая цена
    @Test
    public void _61_4_changeCategoryWithHigherMinPriceFailed(){
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        request.setCategoryId(categoryId);
        request.setProductState(ProductState.NEED_MODERATION);
        updateProductUnSuccessful(request, WrongPriceException.class);
    }

    //Можно вернуть прежнюю категорию с прежней ценой
    @Test
    public void _61_4_changeCategoryWithHigherMinPriceSuccessful(){
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        request.setCategoryId(categoryId);
        request.setPrice(price);
        updateProductSuccessful(request);
    }

    @Test
    public void _62_sendToModerationSuccessful(){
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        request.setPrice(price);
        //Заодно проверим поддержку поля rrpPrice (рекомендованная цена)
	    request.setRrpPrice(rrpPrice);
	    ProductDTO dto = updateProductSuccessful(request);

	    //Проверяем, сохранилась ли rrpPrice
	    assertEquals(rrpPrice, dto.getRrpPrice());

        //Теперь все необходимые поля заполнены, незаполненных полей нет
	    assertNull(dto.getFieldsLackingForModeration());

	    //Публикуем!
	    request.setProductState(ProductState.NEED_MODERATION);

	    dto = updateProductSuccessful(request);

	    //Незаполненных полей нет
	    assertNull(dto.getFieldsLackingForModeration());

		//Отправленный на модерацию товар не видно в списке черновиков
	    assertFalse(resourceContainsListWithProduct(getDraftsUrl(), draftId));

        //Но его данные все еще можно получить, обратившись непосредственно
	    assertEquals(getProductSuccessful(draftId).getProductId(), draftId);

        ProductSentToModerationNotification notification = (ProductSentToModerationNotification) getLastProductNotification(draftId, getUser().getId(), ProductSentToModerationNotification.class, false, false);

	    //Активность отправки на модерацию появилась
	    getLastActivity(SendToModerationActivity.class, userId, draftId);
    }

    @Test
    public void _63_editSentToModerationProductSuccessful(){
        String description = "Описание после отправки на модерацию";
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        request.setDescription(description);

        updateProductSuccessful(request);
	    assertEquals(getProductSuccessful(draftId).getDescription(), description);

	    //Товар остался в списке модерируемых
	    assertTrue(resourceContainsListWithProduct(getModerationProductsUrl(), draftId));

	    //Активность редактиновани на модерации появилась
	    getLastActivity(EditOnModerationActivity.class, userId, draftId);
    }

    @Test
    public void _64_getSentToModerationHoursRemainsSuccessful(){
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        request.setProductState(ProductState.NEED_MODERATION);

        updateProductSuccessful(request);
        ProductDTO productDTO = getProductDTOByIdFromResource(getModerationProductsUrl(), draftId);
        assertNotNull(productDTO);
        assertNotNull(productDTO.getModerationHoursRemains());
        assertTrue(productDTO.getModerationHoursRemains() <= moderationTimeoutHours);
    }

    @Test
    public void _65_0_productPublishedByNonProUserSuccessful(){
		//Пользователь физик
	    userRepository.save(getUser().setProStatusTime(null));

        setProductState(draftId, ProductState.PUBLISHED);

        Map<String, String> params = getAuthorizeParams();
        params.put("productStates", ProductState.PUBLISHED.name());
        ResponseEntity<String> response = restTemplate.exchange(getUriWithParams(getProductsUrl(), params), HttpMethod.GET, getHttpEntity(null), String.class);
        assertNotNull(response);
        ProductDTO productDTO = getProductDTOByIdFromResponse(response, draftId);
        assertNotNull(productDTO);
        assertNotNull(productDTO.getPublishTimestamp());
        assertEquals(productDTO.getPrice(), productDTO.getStartPrice());
        assertTrue(productDTO.getPublishTimestamp() > 0);
        assertTrue(productDTO.getPublishTimestamp() < System.currentTimeMillis());

        scheduledPrivateSellerProductPublicationNotificationCreatorRunner.createNotifications();

	    ProductDTO productDTOfromCatalog = getProductFromCatalogSuccessful(draftId);

	    ModerationPassedNotification sellerNotification = (ModerationPassedNotification) getLastProductNotification(draftId, getUser().getId(), ModerationPassedNotification.class, false, false);
        PrivateSellerProductPublishedNotification followerNotification = (PrivateSellerProductPublishedNotification) getLastProductNotification(draftId, user2Id, PrivateSellerProductPublishedNotification.class, false, false);
    }

    @Transactional
	@Test
	public void _65_1_productPublishedByProUserSuccessful(){
		//Пользователь бутик
		userRepository.save(getUser().setProStatusTime(LocalDateTime.now()));

		productService.create24hoursProPublishedProductsNotifications();

		commitAndStartNewTransaction();

		ProductsPublishedNotification followerNotification = (ProductsPublishedNotification) getLastProductNotification(draftId, user2Id, ProductsPublishedNotification.class, false, false);
		Category mainCategory = categoryRepository.findAllParents(categoryId).get(0);
		assertTrue(followerNotification.getBaseMessage().contains("1 мужской товар"));

		//Пользователь снова физик
		userRepository.save(getUser().setProStatusTime(null));
	}

    @Transactional
    @Test
    public void _65_2_productsPublishedNotificationIsNotReadWhenProductStatusWasSold(){
        checkNotifications(user2Id, ProductState.SOLD, 8, false, false, false,
                false, ProductsPublishedNotification.class);
    }

    @Transactional
    @Test
    public void _65_2_ProductsPublishedNotificationIsReadWhenProductStatusWasSold(){
        checkNotifications(user2Id, ProductState.SOLD, 11, false, false, false,
                true, ProductsPublishedNotification.class);
    }

    @Transactional
    @Test
    public void _65_2_productsPublishedNotificationIsNotReadWhenProductStatusWasHidden(){
        checkNotifications(user2Id, ProductState.HIDDEN, 8, false, false, false,
                false, ProductsPublishedNotification.class);
    }

    @Transactional
    @Test
    public void _65_2_ProductsPublishedNotificationIsReadWhenProductStatusWasHidden(){
        checkNotifications(user2Id, ProductState.HIDDEN, 11, false, false, false,
                true, ProductsPublishedNotification.class);
    }

    @Transactional
    @Test
    public void _65_2_productsPublishedNotificationIsNotReadWhenProductStatusWasDeleted(){
        checkNotifications(user2Id, ProductState.DELETED, 8, false, false, false,
                false, ProductsPublishedNotification.class);
    }

    @Transactional
    @Test
    public void _65_2_ProductsPublishedNotificationIsReadWhenProductStatusWasDeleted(){
        checkNotifications(user2Id, ProductState.DELETED, 11, false, false, false,
                true, ProductsPublishedNotification.class);
    }

    @Transactional
    @Test
    public void _65_2_productsPublishedNotificationIsNotReadWhenProductStatusWasRejected(){
        checkNotifications(user2Id, ProductState.REJECTED, 8, false, false, false,
                false, ProductsPublishedNotification.class);
    }

    @Transactional
    @Test
    public void _65_2_ProductsPublishedNotificationIsReadWhenProductStatusWasRejected(){
        checkNotifications(user2Id, ProductState.REJECTED, 11, false, false, false,
                true, ProductsPublishedNotification.class);
    }

    @Transactional
    @Test
    public void _65_2_productsPublishedNotificationIsNotReadWhenProductStatusWasDraft(){
        checkNotifications(user2Id, ProductState.DRAFT, 8, false, false, false,
                false, ProductsPublishedNotification.class);
    }

    @Transactional
    @Test
    public void _65_2_ProductsPublishedNotificationIsReadWhenProductStatusWasDraft(){
        checkNotifications(user2Id, ProductState.DRAFT, 11, false, false, false,
                true, ProductsPublishedNotification.class);
    }

    @Transactional
    @Test
    public void _65_2_productsPublishedNotificationIsNotReadWhenProductStatusWasNeedRetouch(){
        checkNotifications(user2Id, ProductState.NEED_RETOUCH, 8, false, false, false,
                false, ProductsPublishedNotification.class);
    }

    @Transactional
    @Test
    public void _65_2_ProductsPublishedNotificationIsReadWhenProductStatusWasNeedRetouch(){
        checkNotifications(user2Id, ProductState.NEED_RETOUCH, 11, false, false, false,
                true, ProductsPublishedNotification.class);
    }

    @Transactional
    @Test
    public void _65_2_productsPublishedNotificationIsNotReadWhenProductStatusWasRetouchDone(){
        checkNotifications(user2Id, ProductState.RETOUCH_DONE, 8, false, false, false,
                false, ProductsPublishedNotification.class);
    }

    @Transactional
    @Test
    public void _65_2_ProductsPublishedNotificationIsReadWhenProductStatusWasRetouchDone(){
        checkNotifications(user2Id, ProductState.RETOUCH_DONE, 11, false, false, false,
                true, ProductsPublishedNotification.class);
    }

    @Transactional
    @Test
    public void _65_2_productsPublishedNotificationIsNotReadWhenProductStatusWasNeedModeration(){
        checkNotifications(user2Id, ProductState.NEED_MODERATION, 8, false, false, false,
                false, ProductsPublishedNotification.class);
    }

    @Transactional
    @Test
    public void _65_2_ProductsPublishedNotificationIsReadWhenProductStatusWasNeedModeration(){
        checkNotifications(user2Id, ProductState.NEED_MODERATION, 11, false, false, false,
                true, ProductsPublishedNotification.class);
    }

    @Transactional
    @Test
    public void _65_2_productsPublishedNotificationIsNotReadWhenProductStatusWasSecondEdition(){
        checkNotifications(user2Id, ProductState.SECOND_EDITION, 8, false, false, false,
                false, ProductsPublishedNotification.class);
    }

    @Transactional
    @Test
    public void _65_2_ProductsPublishedNotificationIsReadWhenProductStatusWasSecondEdition(){
        checkNotifications(user2Id, ProductState.SECOND_EDITION, 11, false, false, false,
                true, ProductsPublishedNotification.class);
    }

    @Test
    public void _66_getSellerRecievesSumSuccessful(){
        ResponseEntity<String> response = restTemplate.exchange(getProductsUrl(), HttpMethod.GET, getHttpEntity(null), String.class);
        assertNotNull(response);
        ProductDTO productDTO = getProductDTOByIdFromResponse(response, draftId);
        assertNotNull(productDTO);
        assertNotNull(productDTO.getSellerRecievesSum());

        BigDecimal commission = productDTO.getCommissionProc().divide(new BigDecimal(100));

        assertTrue(productDTO.getSellerRecievesSum().doubleValue() > 0);
        assertEquals(new Double(productDTO.getPrice().doubleValue() - productDTO.getSellerRecievesSum().doubleValue()), new Double(productDTO.getPrice().doubleValue() * commission.doubleValue()));
    }

    //Изменения статусов после редактирования

	//Цена снизилась, статус не меняется
	@Transactional
    @Test
    public void _68_01_getPriceDecreaseStateNotChangedSuccessful(){
		Product product = getProduct(draftId);
		User user = getUser();

		//Ставим лайк данному товару, чтобы получить соответствующее уведомление после снижения цены
	    likeService.like(product, user);

	    //Добавляем данный товар в корзину, чтобы получить соотв. уведомление
	    Long sizeId = product.getAvailableProductItems().get(0).getSize().getId();
	    cartService.addItemToUser(draftId, sizeId, 1, user);

	    commitAndStartNewTransaction();

        //Уменьшаем стоимость товара и он остается опубликованным, в черновики не попадает
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        BigDecimal newPrice = price.multiply(BigDecimal.valueOf(0.6));
        request.setPrice(newPrice);
        updateProductSuccessful(request);

        ProductDTO productDTO = getProductSuccessful(draftId);
        assertTrue(productDTO.getProductState() == ProductState.PUBLISHED);

        commitAndStartNewTransaction();


	    LikePriceDecreasedNotification likeNotification = (LikePriceDecreasedNotification) getLastProductNotification(draftId, getUser().getId(), LikePriceDecreasedNotification.class, false, false);
	    assertNull(likeNotification.getSubTitle());
	    CartPriceDecreasedNotification cartPriceDecreasedNotification = (CartPriceDecreasedNotification) getLastProductNotification(draftId, getUser().getId(), CartPriceDecreasedNotification.class, false, false);
	    assertNull(cartPriceDecreasedNotification.getSubTitle());
    }

    //Если новая цена null, то корректно отрабатываем
    @Test
    public void _68_02_changeNothingWithNullPriceSuccessful(){
        setUserIsPro(true);

        setProductState(draftId, ProductState.PUBLISHED);

        ProductDTO beforePriceChange = getProductSuccessful(draftId);

        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        request.setPrice(null);

        updateProductSuccessful(request);

        ProductDTO afterPriceChange = getProductSuccessful(draftId);

        // цена не меняется
        assertEquals(beforePriceChange.getPrice(), afterPriceChange.getPrice());
    }

    @Transactional
    @Test
    public void _68_02_likePriceDecreasedNotificationIsNotReadWhenProductStatusWasSold(){
        checkNotifications(userId, ProductState.SOLD, 8, false, false, false,
                false, LikePriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_03_likePriceDecreasedNotificationIsReadWhenProductStatusWasSold(){
        checkNotifications(userId, ProductState.SOLD, 11, false, false, false,
                true, LikePriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_02_likePriceDecreasedNotificationIsNotReadWhenProductStatusWasDraft(){
        checkNotifications(userId, ProductState.DRAFT, 8, false, false, false,
                false, LikePriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_03_likePriceDecreasedNotificationIsReadWhenProductStatusWasDraft(){
        checkNotifications(userId, ProductState.DRAFT, 11, false, false, false,
                true, LikePriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_02_likenPriceDecreasedNotificationIsNotReadWhenProductStatusWasDeleted(){
        checkNotifications(userId, ProductState.DELETED, 8, false, false, false,
                false, LikePriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_03_likePriceDecreasedNotificationIsReadWhenProductStatusWasDeleted(){
        checkNotifications(userId, ProductState.DELETED, 11, false, false, false,
                true, LikePriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_02_likePriceDecreasedNotificationIsNotReadWhenProductStatusWasSecondEdition(){
        checkNotifications(userId, ProductState.SECOND_EDITION, 8, false, false, false,
                false, LikePriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_03_likePriceDecreasedNotificationIsReadWhenProductStatusWasSecondEdition(){
        checkNotifications(userId, ProductState.SECOND_EDITION, 11, false, false, false,
                true, LikePriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_02_likePriceDecreasedNotificationIsNotReadWhenProductStatusWasNeedModeration(){
        checkNotifications(userId, ProductState.NEED_MODERATION, 8, false, false, false,
                false, LikePriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_03_likePriceDecreasedNotificationIsReadWhenProductStatusWasNeedModeration(){
        checkNotifications(userId, ProductState.NEED_MODERATION, 11, false, false, false,
                true, LikePriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_02_likePriceDecreasedNotificationIsNotReadWhenProductStatusWasNeedRetouch(){
        checkNotifications(userId, ProductState.NEED_RETOUCH, 8, false, false, false,
                false, LikePriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_03_likePriceDecreasedNotificationIsReadWhenProductStatusWasNeedRetouch(){
        checkNotifications(userId, ProductState.NEED_RETOUCH, 11, false, false, false,
                true, LikePriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_02_likePriceDecreasedNotificationIsNotReadWhenProductStatusWasRetouchDone(){
        checkNotifications(userId, ProductState.RETOUCH_DONE, 8, false, false, false,
                false, LikePriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_03_likePriceDecreasedNotificationIsReadWhenProductStatusWasRetouchDone(){
        checkNotifications(userId, ProductState.RETOUCH_DONE, 11, false, false, false,
                true, LikePriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_02_likePriceDecreasedNotificationIsNotReadWhenProductStatusWasRejected(){
        checkNotifications(userId, ProductState.REJECTED, 8, false, false, false,
                false, LikePriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_03_likePriceDecreasedNotificationIsReadWhenProductStatusWasRejected(){
        checkNotifications(userId, ProductState.REJECTED, 11, false, false, false,
                true, LikePriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_02_likePriceDecreasedNotificationIsNotReadWhenProductStatusWasHidden(){
        checkNotifications(userId, ProductState.HIDDEN, 8, false, false, false,
                false, LikePriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_03_likePriceDecreasedNotificationIsReadWhenProductStatusWasHidden(){
        checkNotifications(userId, ProductState.HIDDEN, 11, false, false, false,
                true, LikePriceDecreasedNotification.class);
    }




    @Transactional
    @Test
    public void _68_02_cartPriceDecreasedNotificationIsNotReadWhenProductStatusWasSold(){
        checkNotifications(userId, ProductState.SOLD, 8, false, false, false,
                false, CartPriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_03_cartPriceDecreasedNotificationIsReadWhenProductStatusWasSold(){
        checkNotifications(userId, ProductState.SOLD, 11, false, false, false,
                true, CartPriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_02_cartPriceDecreasedNotificationIsNotReadWhenProductStatusWasDraft(){
        checkNotifications(userId, ProductState.DRAFT, 8, false, false, false,
                false, CartPriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_03_cartPriceDecreasedNotificationIsReadWhenProductStatusWasDraft(){
        checkNotifications(userId, ProductState.DRAFT, 11, false, false, false,
                true, CartPriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_02_cartPriceDecreasedNotificationIsNotReadWhenProductStatusWasDeleted(){
        checkNotifications(userId, ProductState.DELETED, 8, false, false, false,
                false, CartPriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_03_cartPriceDecreasedNotificationIsReadWhenProductStatusWasDeleted(){
        checkNotifications(userId, ProductState.DELETED, 11, false, false, false,
                true, CartPriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_02_cartPriceDecreasedNotificationIsNotReadWhenProductStatusWasSecondEdition(){
        checkNotifications(userId, ProductState.SECOND_EDITION, 8, false, false, false,
                false, CartPriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_03_cartPriceDecreasedNotificationIsReadWhenProductStatusWasSecondEdition(){
        checkNotifications(userId, ProductState.SECOND_EDITION, 11, false, false, false,
                true, CartPriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_02_cartPriceDecreasedNotificationIsNotReadWhenProductStatusWasNeedModeration(){
        checkNotifications(userId, ProductState.NEED_MODERATION, 8, false, false, false,
                false, CartPriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_03_cartPriceDecreasedNotificationIsReadWhenProductStatusWasNeedModeration(){
        checkNotifications(userId, ProductState.NEED_MODERATION, 11, false, false, false,
                true, CartPriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_02_cartPriceDecreasedNotificationIsNotReadWhenProductStatusWasNeedRetouch(){
        checkNotifications(userId, ProductState.NEED_RETOUCH, 8, false, false, false,
                false, CartPriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_03_cartPriceDecreasedNotificationIsReadWhenProductStatusWasNeedRetouch(){
        checkNotifications(userId, ProductState.NEED_RETOUCH, 11, false, false, false,
                true, CartPriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_02_cartPriceDecreasedNotificationIsNotReadWhenProductStatusWasRetouchDone(){
        checkNotifications(userId, ProductState.RETOUCH_DONE, 8, false, false, false,
                false, CartPriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_03_cartPriceDecreasedNotificationIsReadWhenProductStatusWasRetouchDone(){
        checkNotifications(userId, ProductState.RETOUCH_DONE, 11, false, false, false,
                true, CartPriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_02_cartPriceDecreasedNotificationIsNotReadWhenProductStatusWasRejected(){
        checkNotifications(userId, ProductState.REJECTED, 8, false, false, false,
                false, CartPriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_03_cartPriceDecreasedNotificationIsReadWhenProductStatusWasRejected(){
        checkNotifications(userId, ProductState.REJECTED, 11, false, false, false,
                true, CartPriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_02_cartPriceDecreasedNotificationIsNotReadWhenProductStatusWasHidden(){
        checkNotifications(userId, ProductState.HIDDEN, 8, false, false, false,
                false, CartPriceDecreasedNotification.class);
    }

    @Transactional
    @Test
    public void _68_03_cartPriceDecreasedNotificationIsReadWhenProductStatusWasHidden(){
        checkNotifications(userId, ProductState.HIDDEN, 11, false, false, false,
                true, CartPriceDecreasedNotification.class);
    }




    private void checkNotifications(Long notificationTargetUserId, ProductState productState, int minutes, boolean productItemDeleteTime, boolean productItemHidden,
                                    boolean productIteZeroCount, Boolean expectedReadCondition, Class<? extends ProductNotification> clazz) {
        Product product = getProduct(draftId);
        changeProductProperties(product.getId(), productState, minutes, productItemDeleteTime, productItemHidden, productIteZeroCount);
        commitTransaction();
        // запускаем раннер уведомлений, в котором закрываем уведомление (аргументом передаем уведомление как лист)
        scheduledNotificationRunner.closeNotifications(100);
        //Таск занимает некоторое время
	    TestUtils.sleep(1);
        // закрываем транзакцию и открываем новую
        startNewTransaction();
        ProductNotification productNotification = findNotification(product.getId(), notificationTargetUserId, clazz);
        System.out.println(productNotification.getId());
        boolean isRead = productNotification.isRead();
        System.out.println(isRead);

        revertProduct(product.getId());

        productNotification.setReadTime(null);
        notificationRepository.save(productNotification);
        commitTransaction();
        Assertions.assertEquals(expectedReadCondition, isRead);
    }


    private ProductNotification findNotification(Long productId, Long userId, Class<? extends ProductNotification> clazz) {
//        User user = userService.getUserById(userId).orElse(null);
        List<Notification> notifications = notificationRepository.findAllByUserAndDtype(userId, clazz.getSimpleName());
        ProductNotification notification = null;
        for(Notification n : notifications){
            if(n.getClass() != clazz) continue;
            ProductNotification pn = (ProductNotification) n;
            Product p = pn.getProduct();
            User targetUser = pn.getTargetUser().orElse(null);
            if(p == null || targetUser == null) continue;
            if(p.getId().equals(productId) && targetUser.getId().equals(userId)){
                //found!
                notification = pn;
                break;
            }
        }
        return notification;
    }

    private void revertProduct(Long productID) {
        Product product = productRepository.getOne(productID);
        product.setProductState(ProductState.PUBLISHED);
        productRepository.save(product);
    }

    private void revertProductItem(long productId, long productItemId) {
        ProductItem productItem = productItemRepository.getOne(productItemId);
        productItem.setDeleteTime(null);
        productItem.setHidden(false);
        productItem.setCount(15);
        productItemRepository.save(productItem);
        revertProduct(productId);
    }

    private void changeProductProperties(Long productId, ProductState productState, int changeTimeAgeMinute, boolean productItemDeleteTime, boolean productItemHidden,
                                         boolean productItemZeroCount) {
        Product product = productRepository.getOne(productId);
        ProductItem productItem = productItemRepository.getOne(product.getProductItems().get(0).getId());
        product.setProductState(productState);
        product.setChangeTime(ZonedDateTime.now().minusMinutes(changeTimeAgeMinute));
        productRepository.save(product);
        if (!productItemDeleteTime && !productItemHidden && !productItemZeroCount) {
            return;
        }
        if (productItemDeleteTime) {
            productItem.setDeleteTime(LocalDateTime.now().minusMinutes(changeTimeAgeMinute));
        }
        if (productItemHidden) {
            productItem.setHidden(true);
        }
        if (productItemZeroCount) {
            productItem.setCount(0);
        }
        productItem.setChangeTime(ZonedDateTime.now().minusMinutes(changeTimeAgeMinute));
        productItemRepository.save(productItem);
    }




	//Физик. Цена повысилась, статус поменялся на NEED_MODERATION
    @Test
    public void _69_1_getPriceIncreaseStateChangedSuccessful(){
		User user = getUser();
		boolean isPro = Boolean.TRUE == user.isPro();
		LocalDateTime proStatusTime = user.getProStatusTime();
		//Данный тест применим только к ф   изикам.
		if(isPro){
			user.setProStatusTime(null);
            user.setCommissionGrid(commissionGridService.getProCommissionGrid());
			userRepository.saveAndFlush(user);
		}

        //Увеличиваем стоимость товара и он перестает быть опубликованным, попадает на модерацию
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        request.setPrice(price.multiply(new BigDecimal(1.2)));
        ProductDTO result = updateProductSuccessful(request);
        assertSame(ProductState.NEED_MODERATION, result.getProductState());

        ProductDTO productDTO = getProductSuccessful(draftId);
	    assertSame(ProductState.NEED_MODERATION, productDTO.getProductState());

	    if(isPro){
		    user.setProStatusTime(proStatusTime);
            user.setCommissionGrid(commissionGridService.getProCommissionGrid());
		    userRepository.saveAndFlush(user);
	    }
    }

	//Физик или бутик меняет фотки. Для первой статус меняется, для остальных - нет
	@Test
	public void _69_2_photoChanged_OK() throws IOException, URISyntaxException {
		photoChangedOnPublishedProduct(false, 1, ProductState.NEED_MODERATION);
		photoChangedOnPublishedProduct(true, 1, ProductState.NEED_MODERATION);

		photoChangedOnPublishedProduct(false, 2, ProductState.PUBLISHED);
		photoChangedOnPublishedProduct(true, 2, ProductState.PUBLISHED);
	}

    //Обновление фото опубликованного товара. Проверяем, изменится ли статус товара для прошника или физика
	//В зависимости от номера картинки
	private void photoChangedOnPublishedProduct(boolean isPro, int imageOrder, ProductState expectedState) throws URISyntaxException, IOException {
		User user = getUser();
		//Сохраняем, чтобы вернуть как было в конце теста
		boolean currentUserIsPro = Boolean.TRUE == user.isPro();
		setUserIsPro(isPro);
		//Делаем товар опубликованным
		setProductState(draftId, ProductState.PUBLISHED);
		//Обновляем картинку
		URL url = this.getClass().getClassLoader().getResource("img/product/" + imageOrder + ".jpg");
		File file = Paths.get(url.toURI()).toFile();
		ProductImageDTO productImageDTO = uploadProductImageSuccessful(file, draftId, imageOrder);
		assertNotNull(productImageDTO.getId());
		//Убеждаемся, что статус товара соответствует ожиданиям
		ProductDTO productDTO = getProductSuccessful(draftId);
		assertSame(expectedState, productDTO.getProductState());
		//Возвращаем прежжнй статус пользователя
		setUserIsPro(currentUserIsPro);
	}

	private void setUserIsPro(boolean isPro){
		User user = getUser();

		if (user.isPro() == isPro) {
            return;
        }

		if (isPro) {
            user.setProStatusTime(LocalDateTime.now());
            user.setCommissionGrid(commissionGridService.getProCommissionGrid());
        } else {
            user.setProStatusTime(null);
            user.setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        }
		userRepository.saveAndFlush(user);
	}







	@Test
	public void _70_getRejectedProductsSuccessful(){
		setProductState(draftId, ProductState.NEED_MODERATION);
		Product product = getProduct(draftId);
		User user = getUser();

        ProductRejectReason reason = new ProductRejectReason().setImageComment(rejectImageComment)
                .setPrice(rejectPrice)
                .setProduct(product)
                .setPriceComment(rejectPriceComment)
                .setDescriptionComment(rejectDescriptionComment)
                .setOtherComment(rejectOtherComment)
                .setRejector(user)
                .setCreateTime(ZonedDateTime.now())
                .setOldPrice(rejectOldPrice)
                .setOldDescription(rejectOldDescription);
        rejectService.saveRejectReason(reason);

        setProductState(draftId, ProductState.REJECTED);

        assertTrue(resourceContainsListWithProduct(getRejectsUrl(), draftId));
		assertFalse(resourceContainsListWithProduct(getDraftsUrl(), draftId));
	}

    @Test
    public void _71_getRejectedReasonSuccessful(){
        ProductDTO productDTO = getProductDTOByIdFromResource(getRejectsUrl(), draftId);
        assertNotNull(productDTO);
        ProductRejectReasonDTO rejectReasonDTO = productDTO.getRejectReason();
        assertNotNull(rejectReasonDTO);
        assertEquals(rejectReasonDTO.getImageComment(), rejectImageComment);
        assertEquals(rejectReasonDTO.getPriceComment(), rejectPriceComment);
        assertEquals(rejectReasonDTO.getOldDescription(), rejectOldDescription);
        assertEquals(rejectReasonDTO.getDescriptionComment(), rejectDescriptionComment);
        assertEquals(rejectReasonDTO.getOtherComment(), rejectOtherComment);
        assertEquals(rejectReasonDTO.getPrice().longValue(), rejectPrice.longValue());
        assertEquals(rejectReasonDTO.getOldPrice().longValue(), rejectOldPrice.longValue());
        assertEquals(rejectReasonDTO.getRejectorId(), getUser().getId().longValue());

        assertFalse(resourceContainsListWithProduct(getDraftsUrl(), draftId));

	    ModerationRejectedNotification notification = (ModerationRejectedNotification) getLastProductNotification(draftId, getUser().getId(), ModerationRejectedNotification.class, false, false);
	    assertEquals(rejectReasonDTO.getId(), notification.getProductRejectReason().getId());
	    assertEquals("Ознакомьтесь с комментариями модератора в личном кабинете.", notification.getBaseMessage());

    }

	@Test
	public void _72_getSecondEditionProductsSuccessful(){
		//Сначала нужно ставить NEED_MODERATION, иначе уведомление не будет создано
		setProductState(draftId, ProductState.NEED_MODERATION);

		Product product = getProduct(draftId);
		User user = getUser();

		ProductRejectReason reason = new ProductRejectReason().setImageComment(rejectImageComment)
				.setPrice(rejectPrice)
				.setProduct(product)
				.setPriceComment(rejectPriceComment)
				.setDescriptionComment(rejectDescriptionComment)
				.setOtherComment(rejectOtherComment)
				.setRejector(user)
				.setCreateTime(ZonedDateTime.now())
				.setOldPrice(rejectOldPrice)
				.setOldDescription(rejectOldDescription);
		rejectService.saveRejectReason(reason);

		setProductState(draftId, ProductState.SECOND_EDITION);
		assertTrue(resourceContainsListWithProduct(getSecondEditionsUrl(), draftId));
		assertFalse(resourceContainsListWithProduct(getDraftsUrl(), draftId));

		assertNotNull(getProductSuccessful(draftId).getRejectReason());

		ModerationFailedNotification notification = (ModerationFailedNotification) getLastProductNotification(draftId, getUser().getId(), ModerationFailedNotification.class, true, false);

		//Исправили недочеты, снова отправляем на модерацию
		ProductDTO request = new ProductDTO();
		request.setProductId(draftId);
		//Публикуем!
		request.setProductState(ProductState.NEED_MODERATION);

		ProductDTO updateProductDTO = updateProductSuccessful(request);

		//Теперь действие по уведомлению выполнено
		ModerationFailedNotification notification2 = (ModerationFailedNotification) getLastProductNotification(draftId, getUser().getId(), ModerationFailedNotification.class, true, true);

		//И это то же самое уведомление
		assertEquals(notification.getId(), notification2.getId());

		//Кроме того, теперь у нас есть новое уведомление об отправленном на повторную модерацию товаре
		ProductSentToModerationNotificationAgain productSentToModerationNotificationAgain = (ProductSentToModerationNotificationAgain) getLastProductNotification(draftId, getUser().getId(), ProductSentToModerationNotificationAgain.class, false, false);

		//Активность повторного редактинования появилась
		getLastActivity(EditSecondEditionActivity.class, userId, draftId);
	}

	@Test
	public void _73_getModerationProductsSuccessful(){
		setProductState(draftId, ProductState.NEED_MODERATION);
		assertTrue(resourceContainsListWithProduct(getModerationProductsUrl(), draftId));
		assertFalse(resourceContainsListWithProduct(getDraftsUrl(), draftId));

        assertNull(getProductSuccessful(draftId).getRejectReason());
	}

	@Test
	public void _74_getModerationProductsOnRetouchSuccessful(){
		setProductState(draftId, ProductState.NEED_RETOUCH);
		assertTrue(resourceContainsListWithProduct(getModerationProductsUrl(), draftId));
		assertFalse(resourceContainsListWithProduct(getDraftsUrl(), draftId));

        assertNull(getProductSuccessful(draftId).getRejectReason());
	}

	@Test
	public void _75_getModerationProductsOnRetouchDoneSuccessful(){
		setProductState(draftId, ProductState.RETOUCH_DONE);
		assertTrue(resourceContainsListWithProduct(getModerationProductsUrl(), draftId));
		assertFalse(resourceContainsListWithProduct(getDraftsUrl(), draftId));

        assertNull(getProductSuccessful(draftId).getRejectReason());
	}

    @Test
    public void _76_getModerationProductsOnRetouchDoneSuccessful(){
        setProductState(draftId, ProductState.RETOUCH_DONE);
        assertTrue(resourceContainsListWithProduct(getModerationProductsUrl(), draftId));
        assertFalse(resourceContainsListWithProduct(getDraftsUrl(), draftId));

        assertNull(getProductSuccessful(draftId).getRejectReason());
    }

    @Test
    public void _77_getProductCommentsCountSuccessful(){
        int commentsCount = 5;
        for(int i = 0; i < commentsCount; i++){
            //commentService.publishComment("comment " + i, getUser().getId(), draftId, TimeZone.getDefault());
	        commentService.publishComment(getUser().getId(), draftId, null, "comment " + i, null);
        }
        setProductState(draftId, ProductState.PUBLISHED);
        ResponseEntity<String> response = restTemplate.exchange(getProductsUrl(), HttpMethod.GET, getHttpEntity(null), String.class);
        assertNotNull(response);
        ProductDTO productDTO = getProductDTOByIdFromResponse(response, draftId);
        assertNotNull(productDTO);
        assertTrue(productDTO.getCommentsCount() == commentsCount);
    }

    @Test
    public void _78_getProductLikesCountSuccessful(){
        Product product = getProduct(draftId);
        likeService.like(product, getUser());
        ResponseEntity<String> response = restTemplate.exchange(getProductsUrl(), HttpMethod.GET, getHttpEntity(null), String.class);
        assertNotNull(response);
        ProductDTO productDTO = getProductDTOByIdFromResponse(response, draftId);
        assertNotNull(productDTO);
        assertTrue(productDTO.getLikesCount() == 1);
    }


    //Если новая цена null то корректно отрабатываем
    @Test
    public void _79_changeNothingWithNullPriceSuccessful(){
        ProductDTO request = new ProductDTO();
        request.setProductId(draftId);
        request.setCategoryId(categoryId);
        request.setPrice(null);
        updateProductSuccessful(request);
    }




    @Test
    public void _80_deleteProductSuccessful(){
        ResponseEntity<Api2Response<Long>> response = restTemplate.exchange(getDeleteProductUrl(draftId), HttpMethod.DELETE, getHttpEntity(null), new ParameterizedTypeReference<Api2Response<Long>>() {});
	    assertSame(response.getStatusCode(), HttpStatus.OK);
	    assertEquals(response.getBody().getData(), draftId);

	    //Товар исчез из списке черновиков
	    assertFalse(resourceContainsListWithProduct(getDraftsUrl(), draftId));

	    //Активность уделения товара появилась
	    getLastActivity(DeleteProductActivity.class, userId, draftId);
    }

    @Test
    public void _81_getProductUnSuccessful(){
        ResponseEntity<String> response = restTemplate.exchange(getProductUrl(draftId), HttpMethod.GET, getHttpEntity(null), String.class);
	    assertSame(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertTrue(TestUtils.hasError(response.getBody(), ProductNotFoundException.class));
    }

    @Test
    public void _82_getCommissionSuccessful(){
        getCommission(100000);
    }

    @Test
    public void _83_getPriceWithCommissionSuccessful() {
		// Если сумма 0 - 59999, то комиссия должна быть 25%
		double case1PriceWithoutCommission = 11999;
		double case1PriceWithCommission = getPriceWithCommission(
                new BigDecimal(case1PriceWithoutCommission),
                null,
                false
        ).doubleValue();
		double commissionCase1 = 0.25;
        assertEquals(
                0,
                Math.round(
                        case1PriceWithCommission - case1PriceWithCommission * commissionCase1 -
                                case1PriceWithoutCommission
                )
        );

		// Если сумма 60000 - 149999, то комиссия должна быть 20%
		double case2PriceWithoutCommission = 54000;
		double case2PriceWithCommission = getPriceWithCommission(
                new BigDecimal(case2PriceWithoutCommission),
                null,
                false
        ).doubleValue();
		double commissionCase2 = 0.2;
        assertEquals(
                0,
                Math.round(
                        case2PriceWithCommission - case2PriceWithCommission * commissionCase2 -
                                case2PriceWithoutCommission
                )
        );

		// Если сумма >= 150000, то комиссия должна быть 16%
		double case3PriceWithoutCommission = 130400;
		double case3PriceWithCommission = getPriceWithCommission(
                new BigDecimal(case3PriceWithoutCommission),
                null,
                false
        ).doubleValue();
		double commissionCase3 = 0.16;
        assertEquals(
                0,
                Math.round(
                        case3PriceWithCommission - case3PriceWithCommission * commissionCase3 -
                                case3PriceWithoutCommission
                )
        );
	}

    @Test
    public void _84_getPriceWithCommissionSuccessfulForProUser() {
        // Если сумма 0 - 59999, то комиссия должна быть 25%
        double case1PriceWithoutCommission = 11999;
        double case1PriceWithCommission = getPriceWithCommission(
                new BigDecimal(case1PriceWithoutCommission),
                null,
                true
        ).doubleValue();
        double commissionCase1 = 0.25;
        assertEquals(
                0,
                Math.round(
                        case1PriceWithCommission - case1PriceWithCommission * commissionCase1 -
                                case1PriceWithoutCommission
                )
        );

        // Если сумма 60000 - 149999, то комиссия должна быть 20%
        double case2PriceWithoutCommission = 54000;
        double case2PriceWithCommission = getPriceWithCommission(
                new BigDecimal(case2PriceWithoutCommission),
                null,
                true
        ).doubleValue();
        double commissionCase2 = 0.2;
        assertEquals(
                0,
                Math.round(
                        case2PriceWithCommission - case2PriceWithCommission * commissionCase2 -
                                case2PriceWithoutCommission
                )
        );

        // Если сумма >= 150000, то комиссия должна быть 16%
        double case3PriceWithoutCommission = 130400;
        double case3PriceWithCommission = getPriceWithCommission(
                new BigDecimal(case3PriceWithoutCommission),
                null,
                true
        ).doubleValue();
        double commissionCase3 = 0.16;
        assertEquals(
                0,
                Math.round(
                        case3PriceWithCommission - case3PriceWithCommission * commissionCase3 -
                                case3PriceWithoutCommission
                )
        );
    }

	@Test
	public void _85_getConversionSuccessful() {
		// Если продавец хочет получить 46500, то он получит 48000. Такой вот приятный сюрприз.
		Conversion conversion = getConversion(null, 46500.0, null, null, false);
		assertTrue(60000.0 == conversion.getPriceWithCommission().doubleValue());
		assertTrue(48000.0 == conversion.getPriceWithoutCommission().doubleValue());
		assertTrue(0.2 == conversion.getCommission());
	}

    @Test
    public void _86_getConversionBySellerIdShouldThrowExceptionForNonModeratorOrAdminUser() {
        Assertions.assertThrows(RestClientException.class, () -> {
            getConversion(null, 22500.0, 222L, null, false);
        });
    }

	@Test
	public void _90_getAttributeSuccessful(){
		ResponseEntity<Api2Response<List<AttributeDTO>>> response = restTemplate.exchange(getAttributesUrl(categoryId), HttpMethod.GET, getHttpEntity(null), new ParameterizedTypeReference<Api2Response<List<AttributeDTO>>>() {});
		assertSame(response.getStatusCode(), HttpStatus.OK);
		assertNotNull(response.getBody().getData());
		assertFalse(response.getBody().getData().isEmpty());
		assertAttributesListCorrect(response.getBody().getData());
	}

	public void validateBrandResponse_9X_(ResponseEntity<Api2Response<List<BrandDTO>>> response, int validateSize) {
        assertSame(response.getStatusCode(), HttpStatus.OK);
        assertNotNull(response.getBody().getData());
        assertFalse(response.getBody().getData().isEmpty());
        assertEquals(validateSize, response.getBody().getData().size());
        assertBrandsListCorrect(response.getBody().getData());
    }

	@Test
	public void _91_getBrandsSuccessful(){
		ResponseEntity<Api2Response<List<BrandDTO>>> response = restTemplate.exchange(getBrandsUrl(), HttpMethod.GET, getHttpEntity(null), new ParameterizedTypeReference<Api2Response<List<BrandDTO>>>() {});
        List<Brand> publishBrandsList = brandRepository.getAllBrands().stream().filter(b -> !Boolean.TRUE.equals(b.getIsHidden())).collect(Collectors.toList());
        validateBrandResponse_9X_(response, publishBrandsList.size());
	}

    @Test
    public void _92_hideShowBrandSuccessful(){
        ResponseEntity<Api2Response<List<BrandDTO>>> response = restTemplate.exchange(getBrandsUrl(), HttpMethod.GET, getHttpEntity(null), new ParameterizedTypeReference<Api2Response<List<BrandDTO>>>() {});
        int availBrandsListSize = response.getBody().getData().size();
        validateBrandResponse_9X_(response, availBrandsListSize);
        assertTrue(availBrandsListSize > 0);
        //
        BrandDTO someBrand = response.getBody().getData().get(0);
        //
        someBrand.setIsHidden(true); // Hide brand: we`ll receive <count - 1> and check that there is not brand with this ID in response
        brandService.updateBrand(someBrand);
        response = restTemplate.exchange(getBrandsUrl(), HttpMethod.GET, getHttpEntity(null), new ParameterizedTypeReference<Api2Response<List<BrandDTO>>>() {});
        validateBrandResponse_9X_(response, availBrandsListSize - 1);
        BrandDTO lostBrand = response.getBody().getData().stream().filter(b -> b.getId().equals(someBrand.getId())).findAny().orElse(null);
        assertNull(lostBrand);
        //
        someBrand.setIsHidden(false); // Show brand back: we`ll receive <count> again and check that brand with ID is back
        brandService.updateBrand(someBrand);
        response = restTemplate.exchange(getBrandsUrl(), HttpMethod.GET, getHttpEntity(null), new ParameterizedTypeReference<Api2Response<List<BrandDTO>>>() {});
        validateBrandResponse_9X_(response, availBrandsListSize);
        BrandDTO findBrand = response.getBody().getData().stream().filter(b -> b.getId().equals(someBrand.getId())).findAny().orElse(null);
        assertNotNull(findBrand);
    }

    @Test
    public void _93_getProductsByStoreCode() {
        // Добавляем новый товар с указанием storeCode
        Long product1Id = createProductWithStoreCode("StoreCode1");

        // проверяем, что он находится, причем регистронезависимо

        List<ProductDTO> products = findProductsByStoreCode("storecode1");

        assertEquals(1, products.size());
        assertEquals(product1Id, products.get(0).getProductId());
        assertEquals("StoreCode1", products.get(0).getStoreCode());


        // добавляем еще один товар с другим кодом
        Long product2Id = createProductWithStoreCode("StoreCode2");

        // проверяем, что он не попадает в предыдущий запрос
        products = findProductsByStoreCode("storecode1");

        assertEquals(1, products.size());
        assertEquals(product1Id, products.get(0).getProductId());
        assertEquals("StoreCode1", products.get(0).getStoreCode());

        // пока на уровне модели нет никаких ограничений на создание товаров продавца с одинаковыми PLU,
        // поэтому может быть несколько вариантов
        Long product3Id = createProductWithStoreCode("storeCode1");

        // проверяем, что вернулись оба товара 1 и 3
        products = findProductsByStoreCode("storecode1");

        assertEquals(2, products.size());
        assertTrue(product1Id.equals(products.get(0).getProductId()) || product3Id.equals(products.get(0).getProductId()));

        // поиск по PLU привязан к конкретному продавцу,
        // поэтому если запросить из-под другого пользователя, то записи уже не вернутся

        cookie = Collections.emptyList();

        Map<String, String> params = getUser2AuthorizeParams();
        params.put("storeCode", "storecode1");
        ResponseEntity<Api2Response<Page<ProductDTO>>> response = restTemplate.exchange(
                getUriWithParams(getProductsPageUrl(), params),
                HttpMethod.GET,
                getHttpEntity(null),
                new ParameterizedTypeReference<Api2Response<Page<ProductDTO>>>() {});
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        assertNotNull(response.getBody().getData().getItems());
        assertEquals(0, response.getBody().getData().getItems().size());


        // вернем куки на место
        authorizeCookies();
    }

    @Test
    public void _94_testUserTagsAffectProduct() {
        // для начала создадим пару товаров
        Long product1Id = createProductWithStoreCode("");
        Long product2Id = createProductWithStoreCode("");

        // проверим, что у них пустые метки beegz и concierge
        Product product1 = productRepository.findById(product1Id).get();
        Product product2 = productRepository.findById(product2Id).get();

        assertNull(product1.getBeegzStatusTime());
        assertNull(product1.getSelectedConciergeTime());
        assertNull(product2.getBeegzStatusTime());
        assertNull(product2.getSelectedConciergeTime());

        // теперь у текущего пользователя проставим теги и проверим, что метки его товаров обновились
        User user = getUser();
        userTagService.saveTags(user, Collections.singletonList(UserTagService.BEEGZ_TAG));

        product1 = productRepository.findById(product1Id).get();
        product2 = productRepository.findById(product2Id).get();

        assertNotNull(product1.getBeegzStatusTime());
        assertNull(product1.getSelectedConciergeTime());
        assertNotNull(product2.getBeegzStatusTime());
        assertNull(product2.getSelectedConciergeTime());

        user = getUser();

        //удаление всех тегов, связанных с консьержем
        removeTagsWithCheck(user);

        //добавление старого тега
        userTagService.saveTags(user, ImmutableList.of(UserTagService.BEEGZ_TAG, UserTagService.CONCIERGE_USER_TAG));

        product1 = productRepository.findById(product1Id).get();
        product2 = productRepository.findById(product2Id).get();

        assertNotNull(product1.getBeegzStatusTime());
        assertNotNull(product1.getSelectedConciergeTime());
        assertNotNull(product2.getBeegzStatusTime());
        assertNotNull(product2.getSelectedConciergeTime());

        // а теперь проверим, что у новых товаров проставились метки
        Long product3Id = createProductWithStoreCode("");
        Product product3 = productRepository.findById(product3Id).get();

        assertNotNull(product3.getBeegzStatusTime());
        assertNotNull(product3.getSelectedConciergeTime());

        //проверка наличия новых тегов (они должны добавиться вместе со старым)
        List<UserCommonTagDTO> userCommonTagDTOS = userCommonTagService.getTagsByUser(user.getId());

        assertTrue(userCommonTagDTOS.stream().anyMatch(tag ->
                tag.getCode().equalsIgnoreCase(UserCommonTagService.CONCIERGE_TAG_CODE)));
        assertTrue(userCommonTagDTOS.stream().anyMatch(tag ->
                tag.getCode().equalsIgnoreCase(UserCommonTagService.PAYMENT_METHOD_PREPAY)));
        assertTrue(userCommonTagDTOS.stream().anyMatch(tag ->
                tag.getCode().equalsIgnoreCase(UserCommonTagService.BEEGZ_TAG_CODE)));

        //удаление тегов
        removeTagsWithCheck(user);

        //после удаления параметры продуктов не должны измениться
        product1 = productRepository.findById(product1Id).get();
        product2 = productRepository.findById(product2Id).get();
        assertNotNull(product1.getSelectedConciergeTime());
        assertNotNull(product2.getSelectedConciergeTime());
        assertNotNull(product1.getBeegzStatusTime());

        //но у нового продукта conciergeTime быть не должно
        Long product4Id = createProductWithStoreCode("");
        Product product4 = productRepository.findById(product4Id).get();
        assertNull(product4.getSelectedConciergeTime());
        assertNull(product4.getBeegzStatusTime());

        //теперь добавляем новый тег. Старый не должен добавиться, но параметры продуктов
        // должны меняться при добавлении тега и при создании продукта
        userCommonTagService.updateUserTags(UpdateUserTagsRequest.builder().userId(user.getId()).tagIds(
                Arrays.asList(getPaymentMethodTagId(), getBeegzTagId())).build());

        userCommonTagDTOS = userCommonTagService.getTagsByUser(user.getId());

        assertTrue(userTagService.getTags(user).isEmpty());
        assertTrue(userCommonTagDTOS.stream().anyMatch(tag -> tag.getCode().equalsIgnoreCase(UserCommonTagService.PAYMENT_METHOD_PREPAY)));
        assertTrue(userCommonTagDTOS.stream().anyMatch(tag -> tag.getCode().equalsIgnoreCase(UserCommonTagService.BEEGZ_TAG_CODE)));

        product4 = productRepository.findById(product4Id).get();
        assertNotNull(product4.getSelectedConciergeTime());
        assertNotNull(product4.getBeegzStatusTime());

        Long product5Id = createProductWithStoreCode("");
        Product product5 = productRepository.findById(product5Id).get();
        assertNotNull(product5.getSelectedConciergeTime());
        assertNotNull(product5.getBeegzStatusTime());

        //добавляем старый тег
        userTagService.saveTags(user, ImmutableList.of(UserTagService.BEEGZ_TAG, UserTagService.CONCIERGE_USER_TAG));

        //удалеям - новые должны удалиться
        userTagService.saveTags(user, null);

        userCommonTagDTOS = userCommonTagService.getTagsByUser(user.getId());

        assertFalse(userCommonTagDTOS.stream().anyMatch(tag ->
                tag.getCode().equalsIgnoreCase(UserCommonTagService.CONCIERGE_TAG_CODE)));
        assertFalse(userCommonTagDTOS.stream().anyMatch(tag ->
                tag.getCode().equalsIgnoreCase(UserCommonTagService.PAYMENT_METHOD_PREPAY)));
        assertFalse(userCommonTagDTOS.stream().anyMatch(tag ->
                tag.getCode().equalsIgnoreCase(UserCommonTagService.BEEGZ_TAG_CODE)));

        //проверка удаления старого тега beegz после удаления нового
        userTagService.saveTags(user, ImmutableList.of(UserTagService.BEEGZ_TAG));
        userCommonTagService.updateUserTags(UpdateUserTagsRequest.builder().userId(user.getId()).tagIds(
                Arrays.asList(getBeegzTagId())).build());

        assertTrue(userTagService.getTags(user).stream().anyMatch(tag ->
                tag.equalsIgnoreCase(UserTagService.BEEGZ_TAG)));

        userCommonTagService.updateUserTags(UpdateUserTagsRequest.builder().userId(user.getId()).tagIds(
                Arrays.asList()).build());

        assertFalse(userTagService.getTags(user).stream().anyMatch(tag ->
                tag.equalsIgnoreCase(UserTagService.BEEGZ_TAG)));
    }

    private Long getPaymentMethodTagId() {
        return userCommonTagService.getTagByCode(UserCommonTagService.PAYMENT_METHOD_PREPAY).getId();
    }

    private Long getBeegzTagId() {
        return userCommonTagService.getTagByCode(UserCommonTagService.BEEGZ_TAG_CODE).getId();
    }

    private void removeTagsWithCheck(User user) {
        userTagService.saveTags(user, null);
        assertTrue(userTagService.getTags(user).isEmpty());

        userCommonTagService.unbindTagFromUser(user, UserCommonTagService.CONCIERGE_TAG_CODE);
        userCommonTagService.unbindTagFromUser(user, UserCommonTagService.PAYMENT_METHOD_PREPAY);
        userCommonTagService.unbindTagFromUser(user, UserCommonTagService.BEEGZ_TAG_CODE);

        List<UserCommonTagDTO> userCommonTagDTOS = userCommonTagService.getTagsByUser(user.getId());

        assertFalse(userCommonTagDTOS.stream().anyMatch(tag ->
                tag.getCode().equalsIgnoreCase(UserCommonTagService.CONCIERGE_TAG_CODE)));
        assertFalse(userCommonTagDTOS.stream().anyMatch(tag ->
                tag.getCode().equalsIgnoreCase(UserCommonTagService.PAYMENT_METHOD_PREPAY)));
        assertFalse(userCommonTagDTOS.stream().anyMatch(tag ->
                tag.getCode().equalsIgnoreCase(UserCommonTagService.BEEGZ_TAG_CODE)));
    }

    private void authorizeCookies() {
        ResponseEntity<String> response = restTemplate.exchange(getUriWithParams(getDraftsUrl(), getAuthorizeParams()), HttpMethod.GET, null, String.class);
        HttpHeaders headers = response.getHeaders();
        cookie = headers.get(HttpHeaders.SET_COOKIE);
    }

    private Long createProductWithStoreCode(String storeCode) {
        ProductDTO request = new ProductDTO();
        request.setBrandId(brandId);
        request.setCategoryId(categoryId);
        request.setStoreCode(storeCode);

        ResponseEntity<Api2Response<Long>> response = restTemplate.exchange(getPublishUrl(), HttpMethod.POST, getHttpEntity(request), new ParameterizedTypeReference<Api2Response<Long>>() {} );

        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        assertSame(response.getStatusCode(), HttpStatus.OK);

        Long productId = response.getBody().getData();
        assertTrue(productId != null && productId > 0);

        return productId;
    }

    private List<ProductDTO> findProductsByStoreCode(String storeCode) {
        ResponseEntity<Api2Response<Page<ProductDTO>>> response = restTemplate.exchange(
                getUriWithParams(getProductsPageUrl(), new SingletonMap<>("storeCode", storeCode)), HttpMethod.GET, getHttpEntity(null),
                new ParameterizedTypeReference<Api2Response<Page<ProductDTO>>>() {});
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        assertNotNull(response.getBody().getData().getItems());

        return response.getBody().getData().getItems();
    }

    @Test
	public void _99_finalize(){
		cleanup();
	}


	private void assertAttributesListCorrect(List<AttributeDTO> attributes){
		for(AttributeDTO attribute : attributes){
			assertAttributeCorrect(attribute);
		}
	}

	private void assertAttributeCorrect(AttributeDTO attribute){
		assertNotNull(attribute.getId());
		assertNotNull(attribute.getName());
		assertNotNull(attribute.getIsRequired());
		assertNotNull(attribute.getShowFilter());
		assertNotNull(attribute.getAttributeValues());
		assertFalse(attribute.getAttributeValues().isEmpty());
		for(AttributeValueDTO attributeValue : attribute.getAttributeValues()){
			assertAttributeValueCorrect(attributeValue);
		}
	}

	private void assertAttributeValueCorrect(AttributeValueDTO attributeValue){
		assertNotNull(attributeValue.getId());
		assertNotNull(attributeValue.getValue());
		assertNotNull(attributeValue.getTransliterateValue());
	}

	private void assertBrandsListCorrect(List<BrandDTO> brands){
		for(BrandDTO brand : brands){
			assertBrandCorrect(brand);
		}
	}

	private void assertBrandCorrect(BrandDTO brand){
		assertNotNull(brand.getId());
		assertNotNull(brand.getName());
		assertNotNull(brand.getUrlName());
	}

	private ProductNotification getLastProductNotification(Long productId, Long userId, Class<? extends ProductNotification> clazz, boolean needAction, boolean actionCompleted){
		TestUtils.sleep(2);//Создание уведомления занимает некоторое время
		User user = userService.getUserById(userId).orElse(null);
		List<Notification> notifications = notificationService.getRawNotifications(50, user);
		ProductNotification notification = null;
		for(Notification n : notifications){
			if(n.getClass() != clazz) continue;
			ProductNotification pn = (ProductNotification) n;
			Product p = pn.getProduct();
			User targetUser = pn.getTargetUser().orElse(null);
			if(p == null || targetUser == null) continue;
			if(p.getId().equals(productId) && targetUser.getId().equals(userId)){
				//found!
				notification = pn;
				break;
			}
		}
		assertNotNull(notification);
		if(needAction && actionCompleted)
		    assertTrue(notification.isRead());
		else
		    assertFalse(notification.isRead());
		assertEquals(userId, notification.getUser().getId());
		assertTrue(needAction == notification.isNeedAction());
		assertTrue(actionCompleted == notification.isActionCompleted());
		assertTrue(ZonedDateTime.now().minusMinutes(1).isBefore(notification.getCreateTime()));
		return notification;
	}

	private ProductActivity getLastActivity(Class<? extends ProductActivity> type, Long userId, Long productId){
		return (ProductActivity) TestUtils.getLastActivityChecked(activityService, type, userId, null, productId);
	}

    //Делает продавца прошником
    private void setUserPro(){
        User user = getUser();
        user.setProStatusTime(LocalDateTime.now());
        userService.save(user);
    }

    //Делает продавца физиком
    private void setUserPhys(){
        User user = getUser();
        user.setProStatusTime(null);
        userService.save(user);
    }

}
