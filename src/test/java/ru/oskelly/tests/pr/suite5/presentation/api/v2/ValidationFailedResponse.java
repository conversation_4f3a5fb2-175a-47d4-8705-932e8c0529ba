package ru.oskelly.tests.pr.suite5.presentation.api.v2;

import lombok.Data;
import ru.oskelly.tests.pr.suite4.presentation.api.v2.ErrorResponse;
import su.reddot.infrastructure.util.Utils;

import java.util.Collection;
import java.util.Map;

@Data
public class ValidationFailedResponse extends ErrorResponse {
	private Map<String, String> errorData;

	//Содержит ошибку с заданным ключом
	public boolean containsError(String key){
		return errorData != null && errorData.containsKey(key);
	}

	//Содержит ошибку с заданными ключом/значением
	public boolean containsError(String key, String value){
		return errorData != null && value.equals(errorData.get(key));
	}

	//Содержит только заданные ошибки по ключам и никаких больше
	public boolean containsOnlyErrors(Collection<String> keys){
		if(errorData == null && (keys == null || keys.isEmpty())) return true;
		return Utils.equalsIgnoreOrder(errorData.keySet(), keys);
	}

	//Содержит только заданные ошибки по ключ/значение и никаких больше
	public boolean containsOnlyErrors(Map<String, String> keyValues){
		if(errorData == null && (keyValues == null || keyValues.isEmpty())) return true;
		return Utils.equalsIgnoreOrder(errorData, keyValues);
	}

}
