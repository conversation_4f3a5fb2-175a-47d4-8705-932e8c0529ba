package ru.oskelly.tests.pr.suite5.presentation.api.v2;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.TestApiConfiguration;
import su.reddot.domain.model.category.Category;
import su.reddot.domain.model.primary.PrimaryContent;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.service.adminpanel.primary.AdminPrimaryDataService;
import su.reddot.domain.service.catalog.AvailableFilters;
import su.reddot.domain.service.product.ProductService;
import su.reddot.presentation.adminpanel.v2.dto.PrimaryPageCondition;
import su.reddot.presentation.api.v2.Api2Response;

import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_05)
public class PrimaryControllerApiV2Test extends AbstractSpringTest {
    @Autowired
    private TestApiConfiguration testApiConfiguration;
    @Value("${test.api.user-email}")
    private String email;
    @Value("${test.api.user-password}")
    private String password;
	@Value("${test.api.user-id}")
	private Long userId;

	private String[] bases = {"muzhskoe", "zhenskoe", "detskoe"};

    static ApiV2Client apiV2Client;

    @Autowired
    private AdminPrimaryDataService adminPrimaryDataService;

	@Autowired
	private ProductService productService;

	@Autowired
	private CacheManager cacheManager;

    @BeforeEach
    public void initialize() {
        if(apiV2Client == null) apiV2Client = new ApiV2Client(email, password);
    }

	private String getAllCallUrl(String base){
		return testApiConfiguration.getServerUrl() + "/api/v2/primary/allCall?base=" + base;
	}

	private Map<String, Object> loadAllCallSuccessful(String base, boolean withAuthorizeParams){
		ResponseEntity<Api2Response<Map<String, Object>>> response = apiV2Client.request(getAllCallUrl(base), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<Map<String, Object>>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		assertNotNull(response.getBody().getData());
		//Соц. аккаунты имеются
		assertNotNull(response.getBody().getData().get("social_accounts"));
		//Инстаграм аккаунт пользователя поддержки имеется
		assertNotNull(response.getBody().getData().get("instagram_support_account"));
		return response.getBody().getData();
	}


	@Test
	public void _00_allCallAvailable_guest(){
    	for(String base : bases){
		    loadAllCallSuccessful(base, false);
	    }
	}

	@Test
	public void _01_allCallAvailable_user(){
		for(String base : bases){
			loadAllCallSuccessful(base, true);
		}
	}

	/**
	 * Баннер по фильтру каталога принимает в админки мультизначения полей (категории, бренды, атрибуты и состояния товаров).
	 * И все это попадает в выдачу АПИ соотв баннера
	 */
	//Временно выключено, т.к. роняет многоядерный комп
	@Test
	@Disabled
	public void _02_bannerCatalogFiltersMultivalueAdminSavingAndApiCheck(){
		int bannerNumber = 1;

		int bannerIndex = 0;

		String bannerTypeName = "BANNER_" + bannerNumber;

		String title = "Баннер с мультизначениями";
		String subtitle = "Выбирайте товары из мультикатегорий!";

		String imageWeb = "imageWeb_" + bannerIndex;
		String imageMobile = "imageMobile_" + bannerIndex;

		int categoryIdsCount = 3; //По столько значений ID категорий будем сохранять в баннере
		int brandIdsCount = 3; //По столько значений ID баннеров будем сохранять в баннере
		int attributeValueIdsCount = 3; //По столько значений ID значений атрибутов будем сохранять в баннере
		int productConditionIdsCount = 2; //По столько значений ID состояний товаров будем сохранять в баннере

		Long baseCategoryId = 2L;
		Category baseCategory = Category.getCategory(baseCategoryId);
		String baseCategoryUrl = baseCategory.getUrlName();
		AdminPrimaryDataService.PrimaryCategoryNewResale basePrimaryCategory =
				new AdminPrimaryDataService.PrimaryCategoryNewResale(baseCategoryId, baseCategory.getDisplayName(),
						baseCategoryUrl, null);

		//Спецификация фильтра для выборки тестовых значений
		ProductService.FilterSpecification spec = new ProductService.FilterSpecification().categoriesIds(Arrays.asList(baseCategoryId));

		//Получаем доступные значения фильтров для корневой ветки
		AvailableFilters availableFilters = productService.getAvailableFilters(spec, ProductService.UserType.HUMAN);

		//Берем себе несколько категорий для тестирования
		List<Long> bannerFilterCategoryIds = availableFilters.getCategory().subList(0, categoryIdsCount);

		//Получаем доступные значения фильтров для корневой ветки с выбранными нами категориями
		spec.categoriesIds(bannerFilterCategoryIds);
		availableFilters = productService.getAvailableFilters(spec, ProductService.UserType.HUMAN);

		//Берем себе несколько брендов для тестирования
		List<Long> bannerFilterBrandIds = availableFilters.getBrand().subList(0, brandIdsCount);

		//Получаем доступные значения фильтров для корневой ветки с выбранными нами категориями и брендами
		spec.interestingBrands(bannerFilterBrandIds);
		availableFilters = productService.getAvailableFilters(spec, ProductService.UserType.HUMAN);

		//Берем себе несколько атрибутов для тестирования
		List<Long> bannerFilterAttributeValueIds = availableFilters.getFilter().subList(0, attributeValueIdsCount);

		//Получаем доступные значения фильтров для корневой ветки с выбранными нами категориями, брендами и атрибутами
		spec.interestingAttributeValues(bannerFilterAttributeValueIds);
		availableFilters = productService.getAvailableFilters(spec, ProductService.UserType.HUMAN);

		//Берем себе несколько состояний товаров для тестирования
		List<Long> bannerFilterProductConditionIds = availableFilters.getProductCondition().size() >= productConditionIdsCount
			? availableFilters.getProductCondition().subList(0, productConditionIdsCount)
			: availableFilters.getProductCondition();

		//Получаем доступные значения фильтров для корневой ветки с выбранными нами категориями, брендами, атрибутами и состояниями товаров
		spec.interestingConditions(bannerFilterProductConditionIds);
		availableFilters = productService.getAvailableFilters(spec, ProductService.UserType.HUMAN);

		//Получаем список товаров по нашему фильтру для сравнения с выдачей по баннеру
		List<Product> products = productService.getRawProductsList(spec, null, null, null, ProductService.UserType.HUMAN);

		//Получам ожидаемый список ID товаров для сравнения с выдачей по баннеру
		Set<Long> productIds = products.stream().map(p -> p.getId()).collect(Collectors.toSet());

		AdminPrimaryDataService.BannerFilter filter = new AdminPrimaryDataService.BannerFilter();
		filter.setCategoryIds(bannerFilterCategoryIds);
		filter.setBrandIds(bannerFilterBrandIds);
		filter.setAttributeValueIds(bannerFilterAttributeValueIds);
		filter.setProductConditionIds(bannerFilterProductConditionIds);

		PrimaryContent.BannerCatalogFilters banner = new PrimaryContent.BannerCatalogFilters(
			PrimaryContent.PrimaryType.BANNER,
			null,
			AdminPrimaryDataService.BannerType.valueOf(bannerTypeName),
				title,
				subtitle,
			imageWeb,
			imageMobile,
			basePrimaryCategory,
			filter,
			null,
			null
		);

		//Сохраняем баннер
		adminPrimaryDataService.updateBannerCatalogFilters(banner, bannerIndex, PrimaryPageCondition.ALL);

		//Чистим кэш
		cacheManager.getCache("AdminPrimaryDataService.getBannersCached").clear();

		//Запрашиваем всю информацию о главной странице
		Map<String, Object> indexPageInfo = loadAllCallSuccessful(baseCategoryUrl, false);

		//Ищем свой баннер в выдаче
		LinkedHashMap<String, Object> resultBannerMap = (LinkedHashMap<String, Object>) ((List) indexPageInfo.get("BANNER")).get(0);

		//Проверяем содержимое баннера в выдаче
		assertEquals(title, resultBannerMap.get("title"));
		assertEquals(bannerTypeName, resultBannerMap.get("bannerType"));
		assertEquals(subtitle, resultBannerMap.get("subtitle"));
		assertEquals(imageWeb, resultBannerMap.get("imageWeb"));
		assertEquals(imageMobile, resultBannerMap.get("imageMobile"));

		//Счетчик количества товаров в выдаче соответствует ожидаемому
		assertEquals(products.size(), resultBannerMap.get("productsTotal"));

		//Ищем фильтр в выдаче баннера
		LinkedHashMap<String, Object> resultBannerFilterMap = (LinkedHashMap<String, Object>) resultBannerMap.get("filter");

		//Ищем категории из выдачи фильтра баннера
		Set<Long> resultBannerFilterCategoryIds = ((List<Integer>) resultBannerFilterMap.get("categoryIds")).stream().map(i -> new Long(i)).collect(Collectors.toSet());

		//Категории из выдачи фильтра баннера соответствуют ожидаемому
		assertEquals(new HashSet(bannerFilterCategoryIds), new HashSet(resultBannerFilterCategoryIds));

		//Ищем бренды из выдачи фильтра баннера
		Set<Long> resultBannerFilterBrandIds = ((List<Integer>) resultBannerFilterMap.get("brandIds")).stream().map(i -> new Long(i)).collect(Collectors.toSet());

		//Бренды из выдачи фильтра баннера соответствуют ожидаемому
		assertEquals(new HashSet(bannerFilterBrandIds), new HashSet(resultBannerFilterBrandIds));

		//Ищем значения атрибутов из выдачи фильтра баннера
		Set<Long> resultBannerFilterAttributeValueIds = ((List<Integer>) resultBannerFilterMap.get("attributeValueIds")).stream().map(i -> new Long(i)).collect(Collectors.toSet());

		//Значения атрибутов из выдачи фильтра баннера соответствуют ожидаемому
		assertEquals(new HashSet(bannerFilterAttributeValueIds), new HashSet(resultBannerFilterAttributeValueIds));

		//Ищем состоятия товаров из выдачи фильтра баннера
		Set<Long> resultBannerFilterProductConditionIds = ((List<Integer>) resultBannerFilterMap.get("productConditionIds")).stream().map(i -> new Long(i)).collect(Collectors.toSet());

		//Состояния товаров из выдачи фильтра баннера соответствуют ожидаемому
		assertEquals(new HashSet(bannerFilterProductConditionIds), new HashSet(resultBannerFilterProductConditionIds));

		//Берем товары из выдачи баннера
		List<LinkedHashMap<String, Object>> resultBannerProducts = (List<LinkedHashMap<String, Object>>) resultBannerMap.get("products");

		//Фактическое кол-во товаров в выдаче соответствует ожидаемому
		assertEquals(products.size(), resultBannerProducts.size());

		//Получаем ID товаров из выдачи
		Set<Long> bannerProductIds = resultBannerProducts.stream().map(p -> new Long((Integer) p.get("id"))).collect(Collectors.toSet());

		//ID товаров из выдачи баннера соответствуют ожиданиям
		assertEquals(new HashSet(productIds), new HashSet(bannerProductIds));

	}

}
