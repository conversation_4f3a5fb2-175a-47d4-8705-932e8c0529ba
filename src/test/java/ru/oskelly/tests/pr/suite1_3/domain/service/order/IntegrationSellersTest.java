package ru.oskelly.tests.pr.suite1_3.domain.service.order;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.service.order.impl.DefaultOrderService;
import su.reddot.infrastructure.configuration.OskellyApplication;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {OskellyApplication.class})
@ActiveProfiles(profiles = AbstractSpringTest.testProfiles)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class IntegrationSellersTest {

    @Autowired
    OrderRepository orderRepository;
    @Autowired
    DefaultOrderService defaultOrderService;

    @Disabled("Тест интеграции с сервисом интеграции, для тестирования вручную")
    @Test
    @Transactional
    public void test() throws InterruptedException {
        Order order = orderRepository.findById(1119834L).get();
        defaultOrderService.onHold(order);
    }
}
