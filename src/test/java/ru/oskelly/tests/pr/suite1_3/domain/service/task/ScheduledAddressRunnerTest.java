package ru.oskelly.tests.pr.suite1_3.domain.service.task;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.address.AddressRepository;
import su.reddot.domain.dao.address.AddressValidationRawInfoRepository;
import su.reddot.domain.model.address.Address;
import su.reddot.domain.model.address.AddressValidationRawInfo;
import su.reddot.domain.service.address.AddressService;
import su.reddot.domain.service.dadata.DadataService;
import su.reddot.domain.service.dto.AddressValidatedDTO;
import su.reddot.domain.service.task.ScheduledAddressRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class ScheduledAddressRunnerTest extends AbstractSpringTest {
    @Autowired
    private ScheduledAddressRunner scheduledAddressRunner;
	@Autowired
	private AddressRepository addressRepository;
	@Autowired
	private AddressValidationRawInfoRepository addressValidationRawInfoRepository;
	@Autowired
	private AddressService addressService;

	@Autowired
	private ObjectMapper objectMapper;

	private final List<Long> validAddressIds = Arrays.asList(2L, 3L, 4L, 16L, 18L, 172L, 306L);

	private final String NULL_VALUE_IN_ADDR = "NULL_VALUE_IN_ADDR";
	private final Map<Long, Map<String, String>> validAddressPropIdsMap = ImmutableMap.<Long, Map<String, String>>builder()
			.put(2L, ImmutableMap.<String, String>builder()
					.put("fiasId", "243b8d45-e107-45f2-9bca-03f2922aca9e")

					.put("region_fias_id", "963073ee-4dfc-48bd-9a70-d2dfc6bd1f31")
					.put("city_fias_id", "86e5bae4-ef58-4031-b34f-5e9ff914cd55")
					.put("settlement_fias_id", NULL_VALUE_IN_ADDR)

					.put("settlement", NULL_VALUE_IN_ADDR)
					.put("settlement_type", NULL_VALUE_IN_ADDR)
					.put("street", "Вокзальная")
					.put("street_type", "ул")
					.put("house", "32а")
					.put("house_type", "д")
					.put("block", NULL_VALUE_IN_ADDR)
					.put("block_type", NULL_VALUE_IN_ADDR)
					.put("flat", NULL_VALUE_IN_ADDR)
					.put("flat_type", NULL_VALUE_IN_ADDR)
					.build()
			)
			.put(3L, ImmutableMap.<String, String>builder()
					.put("fiasId", NULL_VALUE_IN_ADDR)

					.put("region_fias_id", "0c5b2444-70a0-4932-980c-b4dc0d3f02b5")
					.put("city_fias_id", "0c5b2444-70a0-4932-980c-b4dc0d3f02b5")
					.put("settlement_fias_id", NULL_VALUE_IN_ADDR)

					.put("settlement", NULL_VALUE_IN_ADDR)
					.put("settlement_type", NULL_VALUE_IN_ADDR)
					.put("street", "Поклонная")
					.put("street_type", "ул")
					.put("house", "9")
					.put("house_type", "д")
					.put("block", NULL_VALUE_IN_ADDR)
					.put("block_type", NULL_VALUE_IN_ADDR)
					.put("flat", "1326")
					.put("flat_type", "кв")
					.build()
			)
			.put(4L, ImmutableMap.<String, String>builder()
					.put("fiasId", "546f1f9f-2b68-413a-92b9-dd02c186b7fd")

					.put("region_fias_id", "0c5b2444-70a0-4932-980c-b4dc0d3f02b5")
					.put("city_fias_id", "0c5b2444-70a0-4932-980c-b4dc0d3f02b5")
					.put("settlement_fias_id", NULL_VALUE_IN_ADDR)

					.put("settlement", NULL_VALUE_IN_ADDR)
					.put("settlement_type", NULL_VALUE_IN_ADDR)
					.put("street", "Академика Янгеля")
					.put("street_type", "ул")
					.put("blockType", "к")
					.put("house", "1")
					.put("house_type", "д")
					.put("block", "1")
					.put("block_type", "к")
					.put("flat", "246")
					.put("flat_type", "кв")
					.build()
			)
			.put(16L, ImmutableMap.<String, String>builder()
					.put("fiasId", "58861a32-cda2-4338-92d0-8d6b2a5621c5")

					.put("region_fias_id", "c2deb16a-0330-4f05-821f-1d09c93331e6")
					.put("city_fias_id", "c2deb16a-0330-4f05-821f-1d09c93331e6")
					.put("settlement_fias_id", NULL_VALUE_IN_ADDR)

					.put("settlement", NULL_VALUE_IN_ADDR)
					.put("settlement_type", NULL_VALUE_IN_ADDR)
					.put("street", "Фермское")
					.put("street_type", "ш")
					.put("blockType", "литера")
					.put("house", "12")
					.put("house_type", "д")
					.put("block", "Д")
					.put("block_type", "литера")
					.put("flat", "231")
					.put("flat_type", "кв")
					.build()
			)
			.put(18L, ImmutableMap.<String, String>builder()
					.put("fiasId", "0f8375ac-63b5-4e25-aec2-361a2b18dbee")

					.put("region_fias_id", "88cd27e2-6a8a-4421-9718-719a28a0a088")
					.put("city_fias_id", "555e7d61-d9a7-4ba6-9770-6caa8198c483")
					.put("settlement_fias_id", NULL_VALUE_IN_ADDR)

					.put("settlement", NULL_VALUE_IN_ADDR)
					.put("settlement_type", NULL_VALUE_IN_ADDR)
					.put("street", "Бетанкура")
					.put("street_type", "ул")
					.put("house", "3В")
					.put("house_type", "д")
					.put("block", NULL_VALUE_IN_ADDR)
					.put("block_type", NULL_VALUE_IN_ADDR)
					.put("flat", NULL_VALUE_IN_ADDR)
					.put("flat_type", NULL_VALUE_IN_ADDR)
					.build()
			)
			.put(172L, ImmutableMap.<String, String>builder()
					.put("fiasId", "d66f2eb9-0ebe-4b3b-be72-e8334f34fd53")

					.put("region_fias_id", "6466c988-7ce3-45e5-8b97-90ae16cb1249")
					.put("city_fias_id", "8eeed222-72e7-47c3-ab3a-9a553c31cf72")
					.put("settlement_fias_id", "d26e1ac1-c1ec-4705-bba8-101391581535")

					.put("settlement", "Университетский")
					.put("settlement_type", "мкр")
					.put("street", NULL_VALUE_IN_ADDR)
					.put("street_type", NULL_VALUE_IN_ADDR)
					.put("house", "40")
					.put("house_type", "д")
					.put("block", NULL_VALUE_IN_ADDR)
					.put("block_type", NULL_VALUE_IN_ADDR)
					.put("flat", "31")
					.put("flat_type", "кв")
					.build()
			)
			.put(306L, ImmutableMap.<String, String>builder()
					.put("fiasId", "1b0d9eb4-483d-45e2-bd38-bd0fb214c498")

					.put("region_fias_id", "0c5b2444-70a0-4932-980c-b4dc0d3f02b5")
					.put("city_fias_id", "ec44c0ee-bf24-41c8-9e1c-76136ab05cbf")
					.put("settlement_fias_id", NULL_VALUE_IN_ADDR)

					.put("settlement", NULL_VALUE_IN_ADDR)
					.put("settlement_type", NULL_VALUE_IN_ADDR)
					.put("street", NULL_VALUE_IN_ADDR)
					.put("street_type", NULL_VALUE_IN_ADDR)
					.put("house", "1805")
					.put("house_type", "к")
					.put("block", NULL_VALUE_IN_ADDR)
					.put("block_type", NULL_VALUE_IN_ADDR)
					.put("flat", "45")
					.put("flat_type", "кв")
					.build()
			)
			.build();

	private boolean initialized = false;

	@BeforeEach
    public void _00_initInvalidate() {
		if(initialized) return;
		jdbcTemplate.execute("UPDATE address SET is_city_validated = true, is_address_validated = true, last_address_validation_time = now()");
		//
		jdbcTemplate.execute("UPDATE address SET is_city_validated = false, is_address_validated = false, last_city_validation_time = NULL, last_address_validation_time = NULL, is_checked = false, fias_id = null, address_details = null, dadata_full_address = null WHERE id IN (" + validAddressIds.stream().map(i -> i.toString()).collect(Collectors.joining(",")) + ")");
		//
		jdbcTemplate.execute("UPDATE address set address = 'ул Вокзальная, д 32а' WHERE id = 2"); // We`re testing Address Runner, so we need to set correct addresses
		jdbcTemplate.execute("UPDATE address set address = 'ул Поклонная, д 9, кв 1326', dadata_full_address = '121293, Россия, г Москва, р-н Дорогомилово, ул Поклонная, д 9, кв 1326' WHERE id = 3");
		jdbcTemplate.execute("UPDATE address set address = 'ул Академика Янгеля, 1 к 1, 246' WHERE id = 4");
		jdbcTemplate.execute("UPDATE address set address = 'Фермское шоссе, 12 Д, 231' WHERE id = 16");
		jdbcTemplate.execute("UPDATE address set address = 'Канавинский р-н, ул Бетанкура, д 3В' WHERE id = 18");
		jdbcTemplate.execute("UPDATE address set address = 'Университетский, 40, 31' WHERE id = 172");
		jdbcTemplate.execute("UPDATE address set address = '1805 кв 45' WHERE id = 306");
		//
		initialized = true;
    }

    @Test
    public void _01_validateAddresses() {
	    List<Address> allAddresses = addressRepository.findAllById(validAddressIds);
		Assertions.assertThat(allAddresses).allSatisfy(it -> {
			Assertions.assertThat(it.getFiasId()).isNull();
			Assertions.assertThat(it.getAddressDetails()).isNull();
		});
		//
		scheduledAddressRunner.validateAddresses();
	    //На валидацию уходит определенное время
	    TestUtils.sleep(60);

		List<Address> validatedAddresses = addressRepository.findAllById(validAddressIds);
		for (Address address : validatedAddresses) {
			assertAddressValidated(address);
		}
	}

	@Test
	public void _02_validateAddress_01() {
		validateAddress(null, "Москва", "Новаторов, 6, 24");
	}

	@Test
	public void _02_validateAddress_02() {
		validateAddress(null, "Санкт-Петербург", "Приморский проспект, 190, 132");
	}

	@Test
	public void _02_validateAddress_03() {
		validateAddress(null, "Санкт-Петербург", "Пулковское шоссе, 14Г, 78");
	}

	@Test
	public void _02_validateAddress_04() {
		validateAddress("Краснодарский край", "Сочи", "Кирпичная, 2 корпус 3, 195");
	}

	@Test
	public void _02_validateAddress_05() {
		validateAddress(null, "Москва", "Тверской бульвар, 3, 37");
	}

	@Test
	public void _02_validateAddress_06() {
		validateAddress(null, "Санкт-Петербург", "Красуцкого, 3 корп м, 258");
	}

	@Test
	public void _02_validateAddress_07() {
		validateAddress(null, "Ставрополь", "Черниговская, 1, 11");
	}

	@Test
	public void _02_validateAddress_08() {
		validateAddress(null, "Санкт-Петербург", "Площадь Стачек 9, офис 421");
	}

	@Test
	public void _02_validateAddress_09() {
		validateAddress(null, "Москва", "Шелепихинская набережная, 34 к 3, 849");
	}

	private void validateAddress(String region, String city, String address) {
		Address addressObj = new Address();
		addressObj.setRegion(region);
		addressObj.setCity(city);
		addressObj.setAddress(address);

		AddressValidatedDTO validatedAddress = addressService.validateAddresses(
				Collections.singletonList(addressObj)).get(0);

		Assertions.assertThat(validatedAddress.isAddressValidated()).isTrue();
	}

    @SneakyThrows
	private void assertAddressValidated(Address address) {
		Assertions.assertThat(address.getIsCityValidated()).isTrue();
		Assertions.assertThat(address.getIsAddressValidated()).isTrue();
		Assertions.assertThat(address.getLastAddressValidationTime()).isNotNull();
		Assertions.assertThat(address.getLastCityValidationTime()).isNotNull();
		//
		Assertions.assertThat(address.getChangeTime()).isNotNull();
		Assertions.assertThat(address.getAddressDetails()).isNotNull();
		//
		AddressValidationRawInfo addressValidationRawInfo = addressValidationRawInfoRepository.findById(address.getId()).orElse(null);
		Assertions.assertThat(addressValidationRawInfo).isNotNull();
		//
		Map<String, Object> rawSuggestion = objectMapper.readValue(addressValidationRawInfo.getDadataSuggestionJson(), new TypeReference<Map<String, Object>>() {});
		//
		Assertions.assertThat(rawSuggestion).containsEntry("raw_rsp_unrestricted_value", address.getDadataFullAddress());
		Assertions.assertThat(rawSuggestion).containsEntry("region_fias_id", address.getRegionFiasId());
		Assertions.assertThat(rawSuggestion).containsEntry("city_fias_id", address.getCityFiasId());
		Assertions.assertThat(rawSuggestion).containsEntry("postal_code", address.getZipCode());

		if (!StringUtils.isEmpty(validAddressPropIdsMap.get(address.getId()).get("fias_id"))) {
			Assertions.assertThat(rawSuggestion).containsEntry("fias_id", address.getFiasId());
		}
		//
		Assertions.assertThat(address.getDadataAddress())
				.isEqualTo(DadataService.DadataSuggestion.getAddress(address.getDadataFullAddress(), rawSuggestion));
		//
		validateExpectingValue(address.getId(), rawSuggestion,"region_fias_id", address.getRegionFiasId());
		validateExpectingValue(address.getId(), rawSuggestion,"city_fias_id", address.getCityFiasId());
		validateExpectingValue(address.getId(), rawSuggestion,"settlement_fias_id", address.getSettlementFiasId());
		//
		Assertions.assertThat(StringUtils.defaultString(address.getAddressDetails().getSettlement(), NULL_VALUE_IN_ADDR))
				.isEqualTo(validAddressPropIdsMap.get(address.getId()).get("settlement"));
		Assertions.assertThat(StringUtils.defaultString(address.getAddressDetails().getSettlementType(), NULL_VALUE_IN_ADDR))
				.isEqualTo(validAddressPropIdsMap.get(address.getId()).get("settlement_type"));
		Assertions.assertThat(StringUtils.defaultString(address.getAddressDetails().getStreet(), NULL_VALUE_IN_ADDR))
				.isEqualTo(validAddressPropIdsMap.get(address.getId()).get("street"));
		Assertions.assertThat(StringUtils.defaultString(address.getAddressDetails().getStreetType(), NULL_VALUE_IN_ADDR))
				.isEqualTo(validAddressPropIdsMap.get(address.getId()).get("street_type"));
		Assertions.assertThat(StringUtils.defaultString(address.getAddressDetails().getHouse(), NULL_VALUE_IN_ADDR))
				.isEqualTo(validAddressPropIdsMap.get(address.getId()).get("house"));
		Assertions.assertThat(StringUtils.defaultString(address.getAddressDetails().getHouseType(), NULL_VALUE_IN_ADDR))
				.isEqualTo(validAddressPropIdsMap.get(address.getId()).get("house_type"));
		Assertions.assertThat(StringUtils.defaultString(address.getAddressDetails().getBlock(), NULL_VALUE_IN_ADDR))
				.isEqualTo(validAddressPropIdsMap.get(address.getId()).get("block"));
		Assertions.assertThat(StringUtils.defaultString(address.getAddressDetails().getBlockType(), NULL_VALUE_IN_ADDR))
				.isEqualTo(validAddressPropIdsMap.get(address.getId()).get("block_type"));
		Assertions.assertThat(StringUtils.defaultString(address.getAddressDetails().getFlat(), NULL_VALUE_IN_ADDR))
				.isEqualTo(validAddressPropIdsMap.get(address.getId()).get("flat"));
		Assertions.assertThat(StringUtils.defaultString(address.getAddressDetails().getFlatType(), NULL_VALUE_IN_ADDR))
				.isEqualTo(validAddressPropIdsMap.get(address.getId()).get("flat_type"));
	}

	private void validateExpectingValue(long addrId, Map<String, Object> rawSuggestion, String fieldId, String fieldValue) {
		String expectingValue = validAddressPropIdsMap.get(addrId).get(fieldId);
		Assertions.assertThat(expectingValue).isNotNull();
		if (Objects.equals(expectingValue, NULL_VALUE_IN_ADDR)) {
			Assertions.assertThat(fieldValue).isNull();
			Assertions.assertThat(rawSuggestion).doesNotContainKey(fieldId);
		} else {
			Assertions.assertThat(fieldValue).isEqualTo(expectingValue);
			Assertions.assertThat(rawSuggestion).containsEntry(fieldId, expectingValue);
		}
	}

}
