package ru.oskelly.tests.pr.suite1_3.domain.service.loyalty;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.util.ReflectionTestUtils;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.order.UserOrdersSumGroupByUserView;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.bonuses.BonusesService;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.domain.service.loyalty.LoyaltyService;
import su.reddot.domain.service.loyalty.api.LoyaltyAccountsControllerApi;
import su.reddot.domain.service.loyalty.model.LoyaltyAccountShortDto;
import su.reddot.domain.service.loyalty.model.LoyaltyStatus;
import su.reddot.domain.service.loyalty.model.ResponseBodyDtoLoyaltyAccountShortDto;
import su.reddot.domain.service.order.OrderService;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.util.CallInTransaction;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;

import static java.lang.Boolean.TRUE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

//TODO включить, когда будет включаться программа лояльности V2
@Disabled
@SpringBootTest(classes =  {OskellyApplication.class})
@ExtendWith(SpringExtension.class)
@ActiveProfiles(AbstractSpringTest.testProfiles)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
@Slf4j
public class DefaultLoyaltyServiceTest {

    @Autowired
    private LoyaltyService loyaltyService;

    @MockBean
    private BonusesService bonusesService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private CommissionGridService commissionGridService;

    @Autowired
    private OrderService orderService;

    @MockBean
    private LoyaltyAccountsControllerApi accountsController;

    @Autowired
    private CallInTransaction callInTransaction;

    OrderRepository mockOrderRepository;

    @Test
    public void testAcceptLoyaltyProgramV2() {
        mockOrderRepository = mock(OrderRepository.class);
        ReflectionTestUtils.setField(orderService, "orderRepository", mockOrderRepository);
        doAnswer(invocation -> null).when(bonusesService).transferBonusesWelcome(any());

        User userNotAcceptedV2 = createUser(null);
        User userAcceptedV2 = createUser(true);
        User userAcceptedV1ButNotAcceptedV2 = createUser(true,null, null);

        //Проверка успешного кейса
        mockOrdersSum(userNotAcceptedV2.getId(), 100);
        mockStatus(LoyaltyStatus.WHITE);
        loyaltyService.acceptLoyaltyProgramV2(userNotAcceptedV2.getId());
        verifyResultAndResetMocks(1, 1, userNotAcceptedV2, true, true);

        //Пользователь не найден
        loyaltyService.acceptLoyaltyProgramV2(-1L);
        mockStatus(LoyaltyStatus.WHITE);
        verifyResultAndResetMocks(0, 0, null, null, null);

        //Пользователь уже принял программу лояльности
        loyaltyService.acceptLoyaltyProgramV2(userAcceptedV2.getId());
        verifyResultAndResetMocks(0, 0, null, null, null);

        //Пользователь уже принял программу лояльности первой версии, но не принял вторую
        mockStatus(LoyaltyStatus.WHITE);
        loyaltyService.acceptLoyaltyProgramV2(userAcceptedV1ButNotAcceptedV2.getId());
        verifyResultAndResetMocks(0, 1, userAcceptedV1ButNotAcceptedV2, null, true);

        //Пользователь имеет статус SILVER и еще не принял программу лояльности
        mockOrdersSum(userNotAcceptedV2.getId(), 350000);
        mockStatus(LoyaltyStatus.SILVER);
        loyaltyService.acceptLoyaltyProgramV2(userNotAcceptedV2.getId());
        verifyResultAndResetMocks(0, 1, userNotAcceptedV2, null, true);

        //Пользователь имеет статус BLACK и еще не принял программу лояльности
        mockOrdersSum(userNotAcceptedV2.getId(), 3500000);
        mockStatus(LoyaltyStatus.BLACK);
        loyaltyService.acceptLoyaltyProgramV2(userNotAcceptedV2.getId());
        verifyResultAndResetMocks(0, 1, userNotAcceptedV2, null, true);
    }

    private void verifyResultAndResetMocks(int bonusesServiceCount, int loyaltyServiceCount, User user, Boolean resetV1, Boolean resetV2) {
        verify(bonusesService, times(bonusesServiceCount)).transferBonusesWelcome(any());
        verify(accountsController, times(loyaltyServiceCount)).recalcAccountStatus(any(), any());
        reset(bonusesService);
        reset(accountsController);

        if (user != null) {
            callInTransaction.runInNewTransaction(() -> {
                User u = userRepository.findById(user.getId()).orElseThrow(RuntimeException::new);
                if (TRUE.equals(resetV1)) {
                    u.setIsLoyaltyProgramAccepted(null);
                }

                if (TRUE.equals(resetV2)) {
                    u.setIsLoyaltyProgramV2Accepted(null);
                }
                userRepository.save(u);
            });

        }
    }

    private void mockStatus(LoyaltyStatus status) {
        ResponseBodyDtoLoyaltyAccountShortDto resp = new ResponseBodyDtoLoyaltyAccountShortDto();
        LoyaltyAccountShortDto data = new LoyaltyAccountShortDto();
        data.setStatus(status);
        resp.setData(data);
        when(accountsController.recalcAccountStatus(any(), any())).thenReturn(resp);
    }

    private void mockOrdersSum(Long buyerId, Integer amount) {
        UserOrdersSumGroupByUserView item = new UserOrdersSumGroupByUserView() {
            @Override
            public Long getBuyerId() {
                return buyerId;
            }

            @Override
            public BigDecimal getAmount() {
                return amount == null ? null : new BigDecimal(amount);
            }
        };

        List<UserOrdersSumGroupByUserView> result = new ArrayList<>();
        result.add(item);

        when(mockOrderRepository.getAllFinishedBuyersOrdersSumGroupByUser(any(), any(), any())).thenReturn(result);
    }

    private User createUser(Boolean isLoyaltyProgramV2Accepted) {
        return createUser(isLoyaltyProgramV2Accepted, null);
    }

    private User createUser(Boolean isLoyaltyProgramV2Accepted, LocalDateTime birthday) {
        return createUser(null, isLoyaltyProgramV2Accepted, birthday);
    }

    private User createUser(Boolean isLoyaltyProgramAccepted, Boolean isLoyaltyProgramV2Accepted, LocalDateTime birthday) {
        return userRepository.saveAndFlush(
                new User()
                        .setNickname(RandomStringUtils.randomAlphabetic(10))
                        .setUserType(User.UserType.SIMPLE_USER)
                        .setBirthDate(birthday)
                        .setCommissionGrid(commissionGridService.getDefaultCommissionGrid())
                        .setIsLoyaltyProgramAccepted(isLoyaltyProgramAccepted)
                        .setIsLoyaltyProgramV2Accepted(isLoyaltyProgramV2Accepted == null ? null : ZonedDateTime.now())
                        .setChangeTime(LocalDateTime.now())
        );
    }
}
