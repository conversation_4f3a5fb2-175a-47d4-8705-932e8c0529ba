package ru.oskelly.tests.pr.suite1_3.domain.service.productpublication;

import com.google.common.collect.ImmutableList;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.MockPublisherConfiguration;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.BrandRepository;
import su.reddot.domain.dao.SizeRepository;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.activity.ActivityTestRepository;
import su.reddot.domain.dao.category.CategoryRepository;
import su.reddot.domain.dao.device.DeviceRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.activity.Activity;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.size.Size;
import su.reddot.domain.model.user.SellerType;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.catalog.CategoryService;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.domain.service.dto.CategoryDTO;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.domain.service.dto.SizeValueDTO;
import su.reddot.domain.service.product.PriceChangedEvent;
import su.reddot.domain.service.product.PriceDroppedEvent;
import su.reddot.domain.service.product.item.ProductItemService;
import su.reddot.domain.service.productpublication.ProductPublicationService;
import su.reddot.domain.service.productpublication.exception.WrongCategoryException;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.security.SecurityService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;
import static su.reddot.domain.service.catalog.CategoryService.ROOT_CATEGORY_ID;

@ActiveProfiles(AbstractSpringTest.testProfiles)
@SpringBootTest(classes = {OskellyApplication.class, MockPublisherConfiguration.class})
@ExtendWith(SpringExtension.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class DefaultProductPublicationTest {

    @Autowired
    ProductPublicationService defaultProductPublicationService;
    @Autowired
    UserRepository userRepository;
    @Autowired
    ActivityTestRepository<Activity> activityTestRepository;
    @Autowired
    BrandRepository brandRepository;
    @Autowired
    ProductRepository productRepository;
    @Autowired
    ApplicationEventPublisher publisher;
    @Autowired
    DeviceRepository deviceRepository;
    @Autowired
    CommissionGridService commissionGridService;
    @Autowired
    CategoryService categoryService;
    @MockBean
    SecurityService securityService;
    @Autowired
    SizeRepository sizeRepository;
    @Autowired
    CategoryRepository categoryRepository;
    @Autowired
    ProductItemService productItemService;

    @Value("${app.publication.phys-disabled-category-ids}")
    List<Long> physDisabledCategoryIds;

    private User currentUser;
    private Product publishedProduct;
    private Product draftProduct;

    @BeforeEach
    public void init() {
        currentUser = createUser(true, false);

        given(securityService.getCurrentAuthorizedUser()).willReturn(currentUser);
        given(securityService.getCurrentAuthorizedUserId()).willReturn(currentUser.getId());

        publishedProduct = createProduct();
    }

    private User createUser(boolean pro, boolean canPublishToDisabledCategories) {
        User user = new User()
                .setNickname(RandomStringUtils.randomAlphabetic(5))
                .setUserType(User.UserType.SIMPLE_USER)
                .setProStatusTime(pro ? LocalDateTime.now() : null)
                .setChangeTime(LocalDateTime.now())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid())
                .setCanPublishToDisabledCategoriesStatusTime(canPublishToDisabledCategories ? ZonedDateTime.now() : null)
                .setSellerType(SellerType.INDIVIDUAL);
        return userRepository.saveAndFlush(user);
    }

    private Product createProduct() {
        Product product = new Product();
        product.setBrand(brandRepository.getOne(ROOT_CATEGORY_ID));
        product.setCategoryId(44L);
        product.setSeller(currentUser);
        product.setCurrentPrice(BigDecimal.valueOf(10000));
        product.setProductState(ProductState.PUBLISHED);
        return productRepository.saveAndFlush(product);
    }

    @AfterEach
    public void cleanup() {
        activityTestRepository.deleteByUserIdOrGuestToken(currentUser.getId(), null);
        deviceRepository.deleteAll(deviceRepository.findDevicesByUserId(currentUser.getId()));
        productRepository.delete(publishedProduct);
        if (draftProduct != null) {
            productRepository.delete(draftProduct);
        }
        userRepository.delete(currentUser);
        reset(publisher);
    }

    @Test
    public void publishProductPublishesPriceDroppedEventWhenPriceDecreasedMoreThan10Percent() {
        BigDecimal oldPrice = publishedProduct.getCurrentPrice();
        BigDecimal newPrice = oldPrice.multiply(BigDecimal.valueOf(0.8));
        ProductDTO productDTO = new ProductDTO()
                .setProductId(publishedProduct.getId())
                .setPrice(newPrice);

        defaultProductPublicationService.publishProduct(productDTO);

        ArgumentCaptor<Object> priceDroppedEventArgumentCaptor = ArgumentCaptor.forClass(Object.class);

        verify(publisher, Mockito.atLeastOnce()).publishEvent(priceDroppedEventArgumentCaptor.capture());
        Optional<Object> priceDroppedEventOptional = priceDroppedEventArgumentCaptor.getAllValues()
                .stream()
                .filter(priceChangedEvent -> priceChangedEvent instanceof PriceChangedEvent)
                .findFirst();
        assertThat(priceDroppedEventOptional).isPresent();
        PriceDroppedEvent priceDroppedEvent = (PriceDroppedEvent) priceDroppedEventOptional.get();
        assertThat(priceDroppedEvent.getOldPrice()).isEqualByComparingTo(oldPrice);
        assertThat(priceDroppedEvent.getNewPrice()).isEqualByComparingTo(newPrice);
        assertThat(priceDroppedEvent.getProductWithAlteredPrice().getId()).isEqualByComparingTo(publishedProduct.getId());
    }

    @Test
    public void publishProductPublishesPriceChangedEventWhenPriceChanged() {
        BigDecimal oldPrice = publishedProduct.getCurrentPrice();
        BigDecimal newPrice = oldPrice.multiply(BigDecimal.valueOf(1.2));
        ProductDTO productDTO = new ProductDTO()
                .setProductId(publishedProduct.getId())
                .setPrice(newPrice);

        defaultProductPublicationService.publishProduct(productDTO);

        ArgumentCaptor<Object> priceChangedEventArgumentCaptor = ArgumentCaptor.forClass(Object.class);

        verify(publisher, atLeastOnce()).publishEvent(priceChangedEventArgumentCaptor.capture());


        Optional<Object> priceChangedEventOptional = priceChangedEventArgumentCaptor.getAllValues()
                .stream()
                .filter(priceChangedEvent -> priceChangedEvent instanceof PriceChangedEvent)
                .findFirst();
        assertThat(priceChangedEventOptional).isPresent();
        PriceChangedEvent priceChangedEvent = (PriceChangedEvent) priceChangedEventOptional.get();
        assertThat(priceChangedEvent.getOldPrice()).isEqualByComparingTo(oldPrice);
        assertThat(priceChangedEvent.getNewPrice()).isEqualByComparingTo(newPrice);
        assertThat(priceChangedEvent.getProductWithAlteredPrice().getId()).isEqualByComparingTo(publishedProduct.getId());
    }

    @Test
    public void testPublishToDisabledCategory() {

        User user = createUser(false, false);

        given(securityService.getCurrentAuthorizedUser()).willReturn(user);
        given(securityService.getCurrentAuthorizedUserId()).willReturn(user.getId());

        // проверка выдачи категорий для неодобренного пользователя
        List<CategoryDTO> categories = defaultProductPublicationService.getChildrenCategories(ROOT_CATEGORY_ID);
        assertFalse(physDisabledCategoriesExists(categories));

        Long leafDisabledCategoryId = categoryService.getLeafCategoryIdsCached(physDisabledCategoryIds.get(0)).stream()
                .findAny()
                .get();

        ProductDTO productDTO = new ProductDTO()
                .setCategoryId(leafDisabledCategoryId)
                .setBrandId(brandRepository.getOne(ROOT_CATEGORY_ID).getId());

        // попытка публикации неодобренным пользователем товара в недоступной категории
        assertThrows(WrongCategoryException.class, () -> defaultProductPublicationService.publishProduct(productDTO));

        user = createUser(false, true);

        given(securityService.getCurrentAuthorizedUser()).willReturn(user);
        given(securityService.getCurrentAuthorizedUserId()).willReturn(user.getId());

        // проверка выдачи категорий для одобренного пользователя
        categories = defaultProductPublicationService.getChildrenCategories(ROOT_CATEGORY_ID);
        assertTrue(physDisabledCategoriesExists(categories));

        // публикация товара одобренным пользователем в недоступной категории
        Long productId = defaultProductPublicationService.publishProduct(productDTO);
        assertTrue(productId != null && productId > 0);
    }

    private boolean physDisabledCategoriesExists(List<CategoryDTO> categories) {
        for (CategoryDTO cat : categories) {
            if (physDisabledCategoryIds.contains(cat.getId())) {
                return true;
            } else if (cat.getHasChildren()) {
                if (physDisabledCategoriesExists(cat.getChildren())) return true;
            }
        }
        return false;
    }

    @Test
    public void testPublishWithSizesAndCustomSizeInfo() {

        draftProduct = new Product();
        draftProduct.setBrand(brandRepository.getOne(1L));
        draftProduct.setCategoryId(10L);
        draftProduct.setSeller(currentUser);
        draftProduct.setCurrentPrice(BigDecimal.valueOf(10000));
        draftProduct.setProductState(ProductState.DRAFT);
        draftProduct = productRepository.saveAndFlush(draftProduct);

        List<Size> sizes = sizeRepository
                .findSizesSortedByOrdering(ImmutableList.of(categoryRepository.findById(draftProduct.getCategoryId()).get()));

        long firstSizeId = sizes.get(0).getId();
        int firstSizeCount1 = 1;
        int firstSizeCount2 = 1;
        String firstSizeCustomSizeType = "CST1";
        String firstSizeCustomSizeValue = "20";

        long secondSizeId = sizes.get(1).getId();
        int secondSizeCount = 2;
        String secondSizeCustomSizeType = "CST2";
        String secondSizeCustomSizeValue = "40";

        ProductDTO publishRequest = new ProductDTO()
                .setProductId(draftProduct.getId())
                .setSizes(ImmutableList.of(
                        new SizeValueDTO()
                                .setId(firstSizeId)
                                .setCount(firstSizeCount1)
                                .setProductCustomSizeType("ST1REMOVE")
                                .setProductCustomSizeValue("10")
                                .setSku("SKU1"),
                        new SizeValueDTO()
                                .setId(firstSizeId)
                                .setCount(firstSizeCount2)
                                .setProductCustomSizeType(firstSizeCustomSizeType)
                                .setProductCustomSizeValue(firstSizeCustomSizeValue)
                                .setSku("SKU1"),
                        new SizeValueDTO()
                                .setId(secondSizeId)
                                .setCount(secondSizeCount)
                                .setProductCustomSizeType(secondSizeCustomSizeType)
                                .setProductCustomSizeValue(secondSizeCustomSizeValue)
                                .setSku("SKU2")));

        defaultProductPublicationService.publishProduct(publishRequest);

        List<ProductItem> productItems = productItemService.getAvailableProductItems(draftProduct.getId());

        assertEquals(2, productItems.size());

        productItems.forEach(pi -> {
            if (pi.getSize().getId() == firstSizeId) {
                assertEquals(firstSizeCount1 + firstSizeCount2, pi.getCount());
                assertEquals(firstSizeCustomSizeType, pi.getCustomSizeType());
                assertEquals(firstSizeCustomSizeValue, pi.getCustomSizeValue());
                assertEquals("SKU1", pi.getSku());
            } else if (pi.getSize().getId() == secondSizeId) {
                assertEquals(secondSizeCount, pi.getCount());
                assertEquals(secondSizeCustomSizeType, pi.getCustomSizeType());
                assertEquals(secondSizeCustomSizeValue, pi.getCustomSizeValue());
                assertEquals("SKU2", pi.getSku());
            }
        });

        // проверка подчистки отсутствующих в запросе размеров

        long thirdSizeId = sizes.get(2).getId();
        int thirdSizeCount = 3;
        String thirdSizeCustomSizeType = "CST3";
        String thirdSizeCustomSizeValue = "60";

        publishRequest = new ProductDTO()
                .setProductId(draftProduct.getId())
                .setSizes(ImmutableList.of(new SizeValueDTO()
                                .setId(thirdSizeId)
                                .setCount(thirdSizeCount)
                                .setProductCustomSizeType(thirdSizeCustomSizeType)
                                .setProductCustomSizeValue(thirdSizeCustomSizeValue)));

        defaultProductPublicationService.publishProduct(publishRequest);

        productItems = productItemService.getAvailableProductItems(draftProduct.getId());

        assertEquals(1, productItems.size());

        ProductItem productItem = productItems.get(0);
        assertEquals(thirdSizeCount, productItem.getCount());
        assertEquals(thirdSizeCustomSizeType, productItem.getCustomSizeType());
        assertEquals(thirdSizeCustomSizeValue, productItem.getCustomSizeValue());
    }
}
