package ru.oskelly.tests.pr.suite1_3.domain.service.order;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.MockPublisherConfiguration;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.product.ProductItemLocationRepository;
import su.reddot.domain.dao.product.ProductItemRepository;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.order.OrderSource;
import su.reddot.domain.model.order.OrderSourceInfo;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.ProductItemLocation;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.delivery.SetDeliveryStateDTO;
import su.reddot.domain.service.ordersourceinfo.OrderSourceInfoService;
import su.reddot.domain.service.product.item.ProductItemService;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.delivery.DeliveryService;
import su.reddot.infrastructure.logistic.DeliveryState;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static su.reddot.domain.model.order.OrderSourceInfo.ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME;
import static su.reddot.domain.model.order.OrderSourceInfo.ORDER_SOURCE_INFO_OSKELLY_STOCK_NAME;
import static su.reddot.domain.model.product.ProductItemLocation.LOCATION_CODE_BOUTIQUE_STOLESHNIKOV;
import static su.reddot.domain.model.product.ProductItemLocation.LOCATION_CODE_WAREHOUSE;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {OskellyApplication.class, MockPublisherConfiguration.class, DefaultOrderServiceTest.MockClockConfiguration.class})
@ActiveProfiles(profiles = AbstractSpringTest.testProfiles)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class DeliveryServiceTest extends AbstractSpringTest {

    @Autowired
    private DeliveryService deliveryService;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ProductItemLocationRepository productItemLocationRepository;

    @Autowired
    private ProductItemRepository productItemRepository;

    @Autowired
    private ProductItemService productItemService;

    @Autowired
    private OrderSourceInfoService orderSourceInfoService;

    private static final long SELLER_USER_ID = 19554L;

    @Test
    @Transactional
    @WithMockUser(authorities = {"ORDER_MANUAL_CHANGE_DELIVERY_STATE"})
    public void testDelivery() {

        OrderSourceInfo stolOrderSourceInfo = orderSourceInfoService.findOrderSourceInfoByNameCached(
                ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME).get();
        OrderSourceInfo stockOrderSourceInfo = orderSourceInfoService.findOrderSourceInfoByNameCached(
                ORDER_SOURCE_INFO_OSKELLY_STOCK_NAME).get();

        // перемещаем часть заказов в оффлайн
        User seller = userRepository.findById(SELLER_USER_ID).get();

        List<Order> orders = orderRepository.findAllSalesBySeller(seller);

        ProductItemLocation stolLocation = productItemLocationRepository.getProductItemLocationByCode(
                LOCATION_CODE_BOUTIQUE_STOLESHNIKOV).get();
        ProductItemLocation stockLocation = productItemLocationRepository.getProductItemLocationByCode(
                LOCATION_CODE_WAREHOUSE).get();

        // проверяем перемещение товара в заказе в бутик stol

        Order stolOrder = orders.get(0);
        stolOrder.setOrderSource(OrderSource.BOUTIQUE);
        stolOrder.setOrderSourceInfo(stolOrderSourceInfo);
        stolOrder.setDeliveryState(DeliveryState.OURSELVES_DELIVERY_TODAY_TO_BUYER);
        stolOrder.getOrderPositions().forEach(it -> {
            ProductItem item = it.getProductItem();
            productItemService.removeStock(item, stolLocation);
            productItemService.removeStock(item, stockLocation);

            item.setCount(1);
            productItemRepository.save(item);
        });

        commitAndStartNewTransaction();

        // убеждаемся, что в заказе не осталось айтемов в бутиках

        stolOrder = orderRepository.findById(stolOrder.getId()).get();

        assertFalse(stolOrder.getOrderPositions().stream()
                .map(OrderPosition::getProductItem)
                .anyMatch(it ->
                        it.getStocks().stream()
                                .anyMatch(s ->
                                        s.getLocation().getCode().equals(LOCATION_CODE_BOUTIQUE_STOLESHNIKOV)
                                                || s.getLocation().getCode().equals(LOCATION_CODE_WAREHOUSE))));

        // меняем статус доставки

        deliveryService.setDeliveryState(
                new SetDeliveryStateDTO(stolOrder.getId(), DeliveryState.DELIVERED_TO_BUYER, false));

        commitAndStartNewTransaction();

        // проверяем, что айтемы оказались в бутике stol, в stocks есть запись с бутиком stol, на складе по-прежнему нет

        stolOrder = orderRepository.findById(stolOrder.getId()).get();
        assertTrue(stolOrder.getOrderPositions().stream()
                .map(OrderPosition::getProductItem)
                .anyMatch(it ->
                        it.getInBoutique() != null
                                && it.getStocks().stream()
                                .anyMatch(s -> s.getLocation().getCode().equals(LOCATION_CODE_BOUTIQUE_STOLESHNIKOV))));
        assertFalse(stolOrder.getOrderPositions().stream()
                .map(OrderPosition::getProductItem)
                .anyMatch(it ->
                        it.getStocks().stream()
                                .anyMatch(s -> s.getLocation().getCode().equals(LOCATION_CODE_WAREHOUSE))));


        // проверяем перемещение товара в заказе на склад

        orders = orderRepository.findAllSalesBySeller(seller);

        Order stockOrder = orders.get(1);
        stockOrder.setOrderSource(OrderSource.BOUTIQUE);
        stockOrder.setOrderSourceInfo(stockOrderSourceInfo);
        stockOrder.setDeliveryState(DeliveryState.OURSELVES_DELIVERY_TODAY_TO_BUYER);
        stockOrder.getOrderPositions().forEach(it -> {
            ProductItem item = it.getProductItem();
            productItemService.removeStock(item, stolLocation);
            productItemService.removeStock(item, stockLocation);

            item.setCount(1);
            productItemRepository.save(item);
        });

        commitAndStartNewTransaction();

        // убеждаемся, что в заказе не осталось айтемов в бутиках

        stockOrder = orderRepository.findById(stockOrder.getId()).get();

        assertFalse(stockOrder.getOrderPositions().stream()
                .map(OrderPosition::getProductItem)
                .anyMatch(it ->
                        it.getStocks().stream()
                                .anyMatch(s ->
                                        s.getLocation().getCode().equals(LOCATION_CODE_BOUTIQUE_STOLESHNIKOV)
                                                || s.getLocation().getCode().equals(LOCATION_CODE_WAREHOUSE))));

        // меняем статус доставки

        deliveryService.setDeliveryState(
                new SetDeliveryStateDTO(stockOrder.getId(), DeliveryState.DELIVERED_TO_BUYER, false));

        commitAndStartNewTransaction();

        // проверяем, что в stocks есть запись со складом, в бутике stol по-прежнему нет

        stockOrder = orderRepository.findById(stockOrder.getId()).get();
        assertTrue(stockOrder.getOrderPositions().stream()
                .map(OrderPosition::getProductItem)
                .anyMatch(it -> it.getInBoutique() == null &&
                        it.getStocks().stream()
                                .anyMatch(s -> s.getLocation().getCode().equals(LOCATION_CODE_WAREHOUSE))));
        assertFalse(stockOrder.getOrderPositions().stream()
                .map(OrderPosition::getProductItem)
                .anyMatch(it -> it.getStocks().stream()
                        .anyMatch(s -> s.getLocation().getCode().equals(LOCATION_CODE_BOUTIQUE_STOLESHNIKOV))));
    }
}