package ru.oskelly.tests.pr.suite1_3.domain.service.order.track;

import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import su.reddot.domain.model.order.OrderSource;
import su.reddot.domain.service.dto.order.track.PositionDetailsDTO;

import java.util.Optional;

@Slf4j
public class DiscountAmountTestsTest extends OrderTrackServiceOskellyExpertiseBaseTest {
    private static final String DISCONT_SHOULD_SHOW_SEVERAL_POSITIONS_CONTEXT = "/mocked-orders/discontamount/several/shouldshow";
    private static final String DISCONT_SHOULD_SHOW_SINGLE_POSITIONS_CONTEXT = "/mocked-orders/discontamount/single/shouldshow";
    private static final String DISCONT_SHOULD_NOT_SHOW_SEVERAL_POSITIONS_CONTEXT = "/mocked-orders/discontamount/several/shouldnotshow";
    private static final String DISCONT_SHOULD_NOT_SHOW_SINGLE_POSITIONS_CONTEXT = "/mocked-orders/discontamount/single/shouldnotshow";
    private static final String DEFECTS_CONTEXT_POSTFIX = "/defects";
    private static final String CHANGES_CONTEXT_POSTFIX = "/changes";
    private static final String CHANGESANDDEFECTS_CONTEXT_POSTFIX = "/changesanddefects";

    @Nested
    class SeveralPositionsTest {
        @Nested
        class ShouldShowDiscountAmountTest {
            @Test
            public void testForDefects() {
                expectExpertiseStageForAllSnippets(
                    BUYER_CONTEXT, DISCONT_SHOULD_SHOW_SEVERAL_POSITIONS_CONTEXT + DEFECTS_CONTEXT_POSTFIX
                ).expect(expertiseStage -> {
                    Optional<PositionDetailsDTO> positionWithDiscontAmount = expertiseStage.getPositions().stream()
                        .filter(p -> p.getPositionId() == 1515151).findFirst();
                    String positionText = positionWithDiscontAmount.get().getText();
                    Assertions.assertThat(positionText).startsWith("st.DEFECT_MATCHING.SUCCESS.position.Buyer.singular[1515151 brand1515151|");
                    Assertions.assertThat(positionText).endsWith("₽]");
                });
                expectExpertiseStageForAllSnippets(
                    SELLER_CONTEXT, DISCONT_SHOULD_SHOW_SEVERAL_POSITIONS_CONTEXT + DEFECTS_CONTEXT_POSTFIX
                ).expect(expertiseStage -> {
                    Optional<PositionDetailsDTO> positionWithDiscontAmount = expertiseStage.getPositions().stream()
                        .filter(p -> p.getPositionId() == 1515151).findFirst();
                    String positionText = positionWithDiscontAmount.get().getText();
                    Assertions.assertThat(positionText).startsWith("st.DEFECT_MATCHING.SUCCESS.position.Seller.singular[1515151 brand1515151|");
                    Assertions.assertThat(positionText).endsWith("₽]");
                });
            }

            @Test
            public void testForChanges() {
                expectExpertiseStageForAllSnippets(
                    BUYER_CONTEXT, DISCONT_SHOULD_SHOW_SEVERAL_POSITIONS_CONTEXT + CHANGES_CONTEXT_POSTFIX
                ).expect(expertiseStage -> {
                    Optional<PositionDetailsDTO> positionWithDiscontAmount = expertiseStage.getPositions().stream()
                        .filter(p -> p.getPositionId() == 1515151).findFirst();
                    String positionText = positionWithDiscontAmount.get().getText();
                    Assertions.assertThat(positionText).startsWith("st.CHANGES_MATCHING.SUCCESS.position.Buyer.singular[1515151 brand1515151|");
                    Assertions.assertThat(positionText).endsWith("₽]");
                });
                expectExpertiseStageForAllSnippets(
                    SELLER_CONTEXT, DISCONT_SHOULD_SHOW_SEVERAL_POSITIONS_CONTEXT + CHANGES_CONTEXT_POSTFIX
                ).expect(expertiseStage -> {
                    Optional<PositionDetailsDTO> positionWithDiscontAmount = expertiseStage.getPositions().stream()
                        .filter(p -> p.getPositionId() == 1515151).findFirst();
                    String positionText = positionWithDiscontAmount.get().getText();
                    Assertions.assertThat(positionText).startsWith("st.CHANGES_MATCHING.SUCCESS.position.Seller.singular[1515151 brand1515151|");
                    Assertions.assertThat(positionText).endsWith("₽]");
                });
                expectExpertiseStageForAllSnippets(
                        SELLER_CONTEXT, DISCONT_SHOULD_SHOW_SEVERAL_POSITIONS_CONTEXT + CHANGES_CONTEXT_POSTFIX,
                        OrderSource.BOUTIQUE
                ).expect(expertiseStage -> {
                    Optional<PositionDetailsDTO> positionWithDiscontAmount = expertiseStage.getPositions().stream()
                                                                                           .filter(p -> p.getPositionId() == 1515151).findFirst();
                    String positionText = positionWithDiscontAmount.get().getText();
                    Assertions.assertThat(positionText).startsWith("st.CHANGES_MATCHING.SUCCESS.boutiquePosition.Seller.singular[1515151 brand1515151|");
                    Assertions.assertThat(positionText).endsWith("₽]");
                });
            }

            @Test
            public void testForChangesAndDefects() {
                expectExpertiseStageForAllSnippets(
                    BUYER_CONTEXT, DISCONT_SHOULD_SHOW_SEVERAL_POSITIONS_CONTEXT + CHANGESANDDEFECTS_CONTEXT_POSTFIX
                ).expect(expertiseStage -> {
                    Optional<PositionDetailsDTO> positionWithDiscontAmount = expertiseStage.getPositions().stream()
                        .filter(p -> p.getPositionId() == 1515151).findFirst();
                    String positionText = positionWithDiscontAmount.get().getText();
                    Assertions.assertThat(positionText).startsWith("st.DEFECT_MATCHING.SUCCESS.position.Buyer.singular[1515151 brand1515151|");
                    Assertions.assertThat(positionText).endsWith("₽]");
                });
                expectExpertiseStageForAllSnippets(
                        SELLER_CONTEXT, DISCONT_SHOULD_SHOW_SEVERAL_POSITIONS_CONTEXT + CHANGESANDDEFECTS_CONTEXT_POSTFIX
                ).expect(expertiseStage -> {
                    Optional<PositionDetailsDTO> positionWithDiscontAmount = expertiseStage.getPositions().stream()
                                                                                           .filter(p -> p.getPositionId() == 1515151).findFirst();
                    String positionText = positionWithDiscontAmount.get().getText();
                    Assertions.assertThat(positionText).startsWith("st.DEFECT_MATCHING.SUCCESS.position.Seller.singular[1515151 brand1515151|");
                    Assertions.assertThat(positionText).endsWith("₽]");
                });
                expectExpertiseStageForAllSnippets(
                        SELLER_CONTEXT, DISCONT_SHOULD_SHOW_SEVERAL_POSITIONS_CONTEXT + CHANGESANDDEFECTS_CONTEXT_POSTFIX,
                        OrderSource.BOUTIQUE
                ).expect(expertiseStage -> {
                    Optional<PositionDetailsDTO> positionWithDiscontAmount = expertiseStage.getPositions().stream()
                                                                                           .filter(p -> p.getPositionId() == 1515151).findFirst();
                    String positionText = positionWithDiscontAmount.get().getText();
                    Assertions.assertThat(positionText).startsWith("st.DEFECT_MATCHING.SUCCESS.boutiquePosition.Seller.singular[1515151 brand1515151|");
                    Assertions.assertThat(positionText).endsWith("₽]");
                });
            }
        }

        @Nested
        class ShouldNotShowDiscountAmountTest {
            @Test
            public void testForDefects() {
                expectExpertiseStageForAllSnippets(
                    BUYER_CONTEXT, DISCONT_SHOULD_NOT_SHOW_SEVERAL_POSITIONS_CONTEXT + DEFECTS_CONTEXT_POSTFIX
                ).expect(expertiseStage -> {
                    Optional<PositionDetailsDTO> positionWithDiscontAmount = expertiseStage.getPositions().stream()
                        .filter(p -> p.getPositionId() == 1515151).findFirst();
                    String positionText = positionWithDiscontAmount.get().getText();
                    Assertions.assertThat(positionText).startsWith("st.DEFECT_MATCHING.SUCCESS.zeroDiscount.position.Buyer.singular[1515151 brand1515151]");
                });
                expectExpertiseStageForAllSnippets(
                    SELLER_CONTEXT, DISCONT_SHOULD_NOT_SHOW_SEVERAL_POSITIONS_CONTEXT + DEFECTS_CONTEXT_POSTFIX
                ).expect(expertiseStage -> {
                    Optional<PositionDetailsDTO> positionWithDiscontAmount = expertiseStage.getPositions().stream()
                        .filter(p -> p.getPositionId() == 1515151).findFirst();
                    String positionText = positionWithDiscontAmount.get().getText();
                    Assertions.assertThat(positionText).startsWith("st.DEFECT_MATCHING.SUCCESS.zeroDiscount.position.Seller.singular[1515151 brand1515151]");
                });
            }

            @Test
            public void testForChanges() {
                expectExpertiseStageForAllSnippets(
                    BUYER_CONTEXT, DISCONT_SHOULD_NOT_SHOW_SEVERAL_POSITIONS_CONTEXT + CHANGES_CONTEXT_POSTFIX
                ).expect(expertiseStage -> {
                    Optional<PositionDetailsDTO> positionWithDiscontAmount = expertiseStage.getPositions().stream()
                        .filter(p -> p.getPositionId() == 1515151).findFirst();
                    String positionText = positionWithDiscontAmount.get().getText();
                    Assertions.assertThat(positionText).startsWith("st.CHANGES_MATCHING.SUCCESS.zeroDiscount.position.Buyer.singular[1515151 brand1515151]");
                });
                // проверим, что для покупателя не поменяется сообщение, если заказ бутиковый
                expectExpertiseStageForAllSnippets(
                        BUYER_CONTEXT, DISCONT_SHOULD_NOT_SHOW_SEVERAL_POSITIONS_CONTEXT + CHANGES_CONTEXT_POSTFIX,
                        OrderSource.BOUTIQUE
                ).expect(expertiseStage -> {
                    Optional<PositionDetailsDTO> positionWithDiscontAmount = expertiseStage.getPositions().stream()
                                                                                           .filter(p -> p.getPositionId() == 1515151).findFirst();
                    String positionText = positionWithDiscontAmount.get().getText();
                    Assertions.assertThat(positionText).startsWith("st.CHANGES_MATCHING.SUCCESS.zeroDiscount.position.Buyer.singular[1515151 brand1515151]");
                });
                expectExpertiseStageForAllSnippets(
                    SELLER_CONTEXT, DISCONT_SHOULD_NOT_SHOW_SEVERAL_POSITIONS_CONTEXT + CHANGES_CONTEXT_POSTFIX
                ).expect(expertiseStage -> {
                    Optional<PositionDetailsDTO> positionWithDiscontAmount = expertiseStage.getPositions().stream()
                        .filter(p -> p.getPositionId() == 1515151).findFirst();
                    String positionText = positionWithDiscontAmount.get().getText();
                    Assertions.assertThat(positionText).startsWith("st.CHANGES_MATCHING.SUCCESS.zeroDiscount.position.Seller.singular[1515151 brand1515151]");
                });
                // если заказ бутиковый, то сообщение должно чуть чуть поменяться
                expectExpertiseStageForAllSnippets(
                        SELLER_CONTEXT, DISCONT_SHOULD_NOT_SHOW_SEVERAL_POSITIONS_CONTEXT + CHANGES_CONTEXT_POSTFIX,
                        OrderSource.BOUTIQUE
                ).expect(expertiseStage -> {
                    Optional<PositionDetailsDTO> positionWithDiscontAmount = expertiseStage.getPositions().stream()
                                                                                           .filter(p -> p.getPositionId() == 1515151).findFirst();
                    String positionText = positionWithDiscontAmount.get().getText();
                    Assertions.assertThat(positionText).startsWith("st.CHANGES_MATCHING.SUCCESS.zeroDiscount.boutiquePosition.Seller.singular[1515151 brand1515151]");
                });
            }

            @Test
            public void testForChangesAndDefects() {
                expectExpertiseStageForAllSnippets(
                    BUYER_CONTEXT, DISCONT_SHOULD_NOT_SHOW_SEVERAL_POSITIONS_CONTEXT + CHANGESANDDEFECTS_CONTEXT_POSTFIX
                ).expect(expertiseStage -> {
                    Optional<PositionDetailsDTO> positionWithDiscontAmount = expertiseStage.getPositions().stream()
                        .filter(p -> p.getPositionId() == 1515151).findFirst();
                    String positionText = positionWithDiscontAmount.get().getText();
                    Assertions.assertThat(positionText).isEqualTo("st.DEFECT_MATCHING.SUCCESS.zeroDiscount.position.Buyer.singular[1515151 brand1515151]");
                });
                expectExpertiseStageForAllSnippets(
                    SELLER_CONTEXT, DISCONT_SHOULD_NOT_SHOW_SEVERAL_POSITIONS_CONTEXT + CHANGESANDDEFECTS_CONTEXT_POSTFIX
                ).expect(expertiseStage -> {
                    Optional<PositionDetailsDTO> positionWithDiscontAmount = expertiseStage.getPositions().stream()
                        .filter(p -> p.getPositionId() == 1515151).findFirst();
                    String positionText = positionWithDiscontAmount.get().getText();
                    Assertions.assertThat(positionText).isEqualTo("st.DEFECT_MATCHING.SUCCESS.zeroDiscount.position.Seller.singular[1515151 brand1515151]");
                });
                expectExpertiseStageForAllSnippets(
                        SELLER_CONTEXT, DISCONT_SHOULD_NOT_SHOW_SEVERAL_POSITIONS_CONTEXT + CHANGESANDDEFECTS_CONTEXT_POSTFIX,
                        OrderSource.BOUTIQUE
                ).expect(expertiseStage -> {
                    Optional<PositionDetailsDTO> positionWithDiscontAmount = expertiseStage.getPositions().stream()
                                                                                           .filter(p -> p.getPositionId() == 1515151).findFirst();
                    String positionText = positionWithDiscontAmount.get().getText();
                    Assertions.assertThat(positionText).isEqualTo("st.DEFECT_MATCHING.SUCCESS.zeroDiscount.boutiquePosition.Seller.singular[1515151 brand1515151]");
                });
            }
        }
    }

    @Nested
    class SinglePositionsTest {
        @Nested
        class ShouldShowDiscountAmountTest {
            @Test
            public void testForDefects() {
                expectExpertiseStageForAllSnippets(
                    BUYER_CONTEXT, DISCONT_SHOULD_SHOW_SINGLE_POSITIONS_CONTEXT + DEFECTS_CONTEXT_POSTFIX
                ).expect(expertiseStage -> {
                    String descriptionText = expertiseStage.getDescription();
                    Assertions.assertThat(expertiseStage.getPositions()).isNull();
                    Assertions.assertThat(descriptionText).startsWith("st.DEFECT_MATCHING.SUCCESS.description.Buyer.singular[1515151 brand1515151|");
                    Assertions.assertThat(descriptionText).endsWith("₽]");
                });
                expectExpertiseStageForAllSnippets(
                    SELLER_CONTEXT, DISCONT_SHOULD_SHOW_SINGLE_POSITIONS_CONTEXT + DEFECTS_CONTEXT_POSTFIX
                ).expect(expertiseStage -> {
                    String descriptionText = expertiseStage.getDescription();
                    Assertions.assertThat(expertiseStage.getPositions()).isNull();
                    Assertions.assertThat(descriptionText).startsWith("st.DEFECT_MATCHING.SUCCESS.description.Seller.singular[");
                    Assertions.assertThat(descriptionText).endsWith("₽]");
                });

                expectExpertiseStageForAllSnippets(
                        SELLER_CONTEXT, DISCONT_SHOULD_SHOW_SINGLE_POSITIONS_CONTEXT + DEFECTS_CONTEXT_POSTFIX,
                        OrderSource.BOUTIQUE
                ).expect(expertiseStage -> {
                    String descriptionText = expertiseStage.getDescription();
                    Assertions.assertThat(expertiseStage.getPositions()).isNull();
                    Assertions.assertThat(descriptionText).startsWith("st.DEFECT_MATCHING.SUCCESS.boutiqueDescription.Seller.singular[");
                    Assertions.assertThat(descriptionText).endsWith("₽]");
                });
            }

            @Test
            public void testForChanges() {
                expectExpertiseStageForAllSnippets(
                    BUYER_CONTEXT, DISCONT_SHOULD_SHOW_SINGLE_POSITIONS_CONTEXT + CHANGES_CONTEXT_POSTFIX
                ).expect(expertiseStage -> {
                    String descriptionText = expertiseStage.getDescription();
                    Assertions.assertThat(expertiseStage.getPositions()).isNull();
                    Assertions.assertThat(descriptionText).startsWith("st.CHANGES_MATCHING.SUCCESS.description.Buyer.singular[1515151 brand1515151|");
                    Assertions.assertThat(descriptionText).endsWith("₽]");
                });
                expectExpertiseStageForAllSnippets(
                        SELLER_CONTEXT, DISCONT_SHOULD_SHOW_SINGLE_POSITIONS_CONTEXT + CHANGES_CONTEXT_POSTFIX
                ).expect(expertiseStage -> {
                    String descriptionText = expertiseStage.getDescription();
                    Assertions.assertThat(expertiseStage.getPositions()).isNull();
                    Assertions.assertThat(descriptionText).startsWith("st.CHANGES_MATCHING.SUCCESS.description.Seller.singular[");
                    Assertions.assertThat(descriptionText).endsWith("₽]");
                });
                expectExpertiseStageForAllSnippets(
                        SELLER_CONTEXT, DISCONT_SHOULD_SHOW_SINGLE_POSITIONS_CONTEXT + CHANGES_CONTEXT_POSTFIX,
                        OrderSource.BOUTIQUE
                ).expect(expertiseStage -> {
                    String descriptionText = expertiseStage.getDescription();
                    Assertions.assertThat(expertiseStage.getPositions()).isNull();
                    Assertions.assertThat(descriptionText).startsWith("st.CHANGES_MATCHING.SUCCESS.boutiqueDescription.Seller.singular[");
                    Assertions.assertThat(descriptionText).endsWith("₽]");
                });
            }

            @Test
            public void testForChangesAndDefects() {
                expectExpertiseStageForAllSnippets(
                    BUYER_CONTEXT, DISCONT_SHOULD_SHOW_SINGLE_POSITIONS_CONTEXT + CHANGESANDDEFECTS_CONTEXT_POSTFIX
                ).expect(expertiseStage -> {
                    String descriptionText = expertiseStage.getDescription();
                    Assertions.assertThat(expertiseStage.getPositions()).isNull();
                    Assertions.assertThat(descriptionText).startsWith("st.DEFECT_MATCHING.SUCCESS.description.Buyer.singular[1515151 brand1515151|");
                    Assertions.assertThat(descriptionText).endsWith("₽]");
                });
                expectExpertiseStageForAllSnippets(
                    SELLER_CONTEXT, DISCONT_SHOULD_SHOW_SINGLE_POSITIONS_CONTEXT + CHANGESANDDEFECTS_CONTEXT_POSTFIX
                ).expect(expertiseStage -> {
                    String descriptionText = expertiseStage.getDescription();
                    Assertions.assertThat(expertiseStage.getPositions()).isNull();
                    Assertions.assertThat(descriptionText).startsWith("st.DEFECT_MATCHING.SUCCESS.description.Seller.singular[");
                    Assertions.assertThat(descriptionText).endsWith("₽]");
                });
            }
        }

        @Nested
        class ShouldNotShowDiscountAmountTest {
            @Test
            public void testForDefects() {
                expectExpertiseStageForAllSnippets(
                    BUYER_CONTEXT, DISCONT_SHOULD_NOT_SHOW_SINGLE_POSITIONS_CONTEXT + DEFECTS_CONTEXT_POSTFIX
                ).expect(expertiseStage -> {
                    String descriptionText = expertiseStage.getDescription();
                    Assertions.assertThat(expertiseStage.getPositions()).isNull();
                    Assertions.assertThat(descriptionText).startsWith("st.DEFECT_MATCHING.SUCCESS.zeroDiscount.description.Buyer.singular[1515151 brand1515151]");
                });
                expectExpertiseStageForAllSnippets(
                    SELLER_CONTEXT, DISCONT_SHOULD_NOT_SHOW_SINGLE_POSITIONS_CONTEXT + DEFECTS_CONTEXT_POSTFIX
                ).expect(expertiseStage -> {
                    String descriptionText = expertiseStage.getDescription();
                    Assertions.assertThat(expertiseStage.getPositions()).isNull();
                    Assertions.assertThat(descriptionText).startsWith("st.DEFECT_MATCHING.SUCCESS.zeroDiscount.description.Seller.singular");
                });
            }

            @Test
            public void testForChanges() {
                expectExpertiseStageForAllSnippets(
                    BUYER_CONTEXT, DISCONT_SHOULD_NOT_SHOW_SINGLE_POSITIONS_CONTEXT + CHANGES_CONTEXT_POSTFIX
                ).expect(expertiseStage -> {
                    String descriptionText = expertiseStage.getDescription();
                    Assertions.assertThat(expertiseStage.getPositions()).isNull();
                    Assertions.assertThat(descriptionText).startsWith("st.CHANGES_MATCHING.SUCCESS.zeroDiscount.description.Buyer.singular[1515151 brand1515151]");
                });
                expectExpertiseStageForAllSnippets(
                    SELLER_CONTEXT, DISCONT_SHOULD_NOT_SHOW_SINGLE_POSITIONS_CONTEXT + CHANGES_CONTEXT_POSTFIX
                ).expect(expertiseStage -> {
                    String descriptionText = expertiseStage.getDescription();
                    Assertions.assertThat(expertiseStage.getPositions()).isNull();
                    Assertions.assertThat(descriptionText).startsWith("st.CHANGES_MATCHING.SUCCESS.zeroDiscount.description.Seller.singular");
                });
                expectExpertiseStageForAllSnippets(
                        SELLER_CONTEXT, DISCONT_SHOULD_NOT_SHOW_SINGLE_POSITIONS_CONTEXT + CHANGES_CONTEXT_POSTFIX,
                        OrderSource.BOUTIQUE
                ).expect(expertiseStage -> {
                    String descriptionText = expertiseStage.getDescription();
                    Assertions.assertThat(expertiseStage.getPositions()).isNull();
                    Assertions.assertThat(descriptionText).startsWith("st.CHANGES_MATCHING.SUCCESS.zeroDiscount.boutiqueDescription.Seller.singular");
                });
            }

            @Test
            public void testForChangesAndDefects() {
                expectExpertiseStageForAllSnippets(
                    BUYER_CONTEXT, DISCONT_SHOULD_NOT_SHOW_SINGLE_POSITIONS_CONTEXT + CHANGESANDDEFECTS_CONTEXT_POSTFIX
                ).expect(expertiseStage -> {
                    String descriptionText = expertiseStage.getDescription();
                    Assertions.assertThat(expertiseStage.getPositions()).isNull();
                    Assertions.assertThat(descriptionText).startsWith("st.DEFECT_MATCHING.SUCCESS.zeroDiscount.description.Buyer.singular[1515151 brand1515151]");
                });
                expectExpertiseStageForAllSnippets(
                        SELLER_CONTEXT, DISCONT_SHOULD_NOT_SHOW_SINGLE_POSITIONS_CONTEXT + CHANGESANDDEFECTS_CONTEXT_POSTFIX
                ).expect(expertiseStage -> {
                    String descriptionText = expertiseStage.getDescription();
                    Assertions.assertThat(expertiseStage.getPositions()).isNull();
                    Assertions.assertThat(descriptionText).startsWith("st.DEFECT_MATCHING.SUCCESS.zeroDiscount.description.Seller.singular");
                });
                expectExpertiseStageForAllSnippets(
                        SELLER_CONTEXT, DISCONT_SHOULD_NOT_SHOW_SINGLE_POSITIONS_CONTEXT + CHANGESANDDEFECTS_CONTEXT_POSTFIX,
                        OrderSource.BOUTIQUE
                ).expect(expertiseStage -> {
                    String descriptionText = expertiseStage.getDescription();
                    Assertions.assertThat(expertiseStage.getPositions()).isNull();
                    Assertions.assertThat(descriptionText).startsWith("st.DEFECT_MATCHING.SUCCESS.zeroDiscount.boutiqueDescription.Seller.singular");
                });
            }
        }
    }
}
