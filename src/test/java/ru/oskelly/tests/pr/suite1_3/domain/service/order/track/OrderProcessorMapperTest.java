package ru.oskelly.tests.pr.suite1_3.domain.service.order.track;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import su.reddot.domain.service.order.track.expertise.AuthenticityFailedType;
import su.reddot.domain.service.order.track.expertise.ExpertiseStageProgress;
import su.reddot.domain.service.order.track.expertise.ExpertiseStageType;
import su.reddot.domain.service.order.track.expertise.ExpertiseState;
import su.reddot.domain.service.order.track.expertise.ItemGroup;
import su.reddot.domain.service.order.track.expertise.OrderProcessingExpertiseMapper;
import su.reddot.oskelly.orderprocessing.internal.web.dto.IntegrationMobileOrderExpertiseDTO;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class OrderProcessorMapperTest extends OrderMapperBaseTest {

    private static final BigDecimal ZERO = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);

    @Nested
    public class Case_Several_1_AllDefectsNegative {
        public static final String CONTEXT = "mocked-orders/several/2_all-defects-negative";

        @Test
        public void shouldConvert_expertiseFailed_buyer() {
            IntegrationMobileOrderExpertiseDTO extendedExpertise = loadSnippet("7_1");
            OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
                extendedExpertise, Object::toString, (positionId) -> true, BUYER_CONTEXT
            );

            ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
            Assertions.assertThat(expertiseState.getStageType()).isEqualTo(ExpertiseStageType.FINISHED);
            Assertions.assertThat(expertiseState.getStageProgress()).isEqualTo(ExpertiseStageProgress.FAILED);

            ItemGroup itemGroup = expertiseState.getItemGroups().get(0);

            Assertions.assertThat(itemGroup.getStageType()).isEqualTo(ExpertiseStageType.DEFECT_MATCHING);
            Assertions.assertThat(itemGroup.getStageProgress()).isEqualTo(ExpertiseStageProgress.FAILED);
        }

        private IntegrationMobileOrderExpertiseDTO loadSnippet(String name) {
            return loadSnippetByStartName(CONTEXT, name);
        }
    }

    @Nested
    public class Case_Several_1_AllFake {
        public static final String CONTEXT = "mocked-orders/several/1_all-fake";

        @Test
        public void shouldConvert_expertiseFailed_buyer() {
            IntegrationMobileOrderExpertiseDTO extendedExpertise = loadSnippet("7_1");
            OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
                extendedExpertise, Object::toString, (positionId) -> true, BUYER_CONTEXT
            );

            ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
            Assertions.assertThat(expertiseState.getStageType()).isEqualTo(ExpertiseStageType.FINISHED);
            Assertions.assertThat(expertiseState.getStageProgress()).isEqualTo(ExpertiseStageProgress.FAILED);

            ItemGroup itemGroup = expertiseState.getItemGroups().get(0);

            Assertions.assertThat(itemGroup.getStageType()).isEqualTo(ExpertiseStageType.AUTHENTICITY);
            Assertions.assertThat(itemGroup.getStageProgress()).isEqualTo(ExpertiseStageProgress.FAILED);
            Assertions.assertThat(itemGroup.getAuthenticityFailedType()).isEqualTo(AuthenticityFailedType.NOT_ORIGINAL);
        }

        private IntegrationMobileOrderExpertiseDTO loadSnippet(String name) {
            return loadSnippetByStartName(CONTEXT, name);
        }
    }

    @Nested
    public class Case_Single_5_JustFinished {
        public static final String CONTEXT = "mocked-orders/single/5_just_finished_expertise";

        @Test
        public void shouldConvert_deffectReconcilled_waitPacking() {
            IntegrationMobileOrderExpertiseDTO extendedExpertise = loadSnippet("1121980");
            OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
                extendedExpertise, Object::toString, (positionId) -> true, BUYER_CONTEXT
            );

            ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
            Assertions.assertThat(expertiseState.getStageType()).isEqualTo(ExpertiseStageType.PRESELLING_PREPARATION);
            Assertions.assertThat(expertiseState.getStageProgress()).isEqualTo(ExpertiseStageProgress.IN_PROGRESS);

            ItemGroup itemGroup = expertiseState.getItemGroups().get(0);

            Assertions.assertThat(itemGroup.getStageType()).isEqualTo(ExpertiseStageType.DEFECT_MATCHING);
            Assertions.assertThat(itemGroup.getStageProgress()).isEqualTo(ExpertiseStageProgress.SUCCESS);
        }

        @Test
        public void shouldConvert_noDefects_waitPacking() {
            IntegrationMobileOrderExpertiseDTO extendedExpertise = loadSnippet("1121986");
            OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
                extendedExpertise, Object::toString, (positionId) -> true, BUYER_CONTEXT
            );

            ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
            Assertions.assertThat(expertiseState.getStageType()).isEqualTo(ExpertiseStageType.PRESELLING_PREPARATION);
            Assertions.assertThat(expertiseState.getStageProgress()).isEqualTo(ExpertiseStageProgress.IN_PROGRESS);

            ItemGroup itemGroup = expertiseState.getItemGroups().get(0);

            Assertions.assertThat(itemGroup.getStageType()).isEqualTo(ExpertiseStageType.AUTHENTICITY);
            Assertions.assertThat(itemGroup.getStageProgress()).isEqualTo(ExpertiseStageProgress.SUCCESS);
        }

        @Test
        public void shouldConvert_freePreselling_waitPreselling() {
            IntegrationMobileOrderExpertiseDTO extendedExpertise = loadSnippet("1121987");
            OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
                extendedExpertise, Object::toString, (positionId) -> true, BUYER_CONTEXT
            );

            ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
            Assertions.assertThat(expertiseState.getStageType()).isEqualTo(ExpertiseStageType.PRESELLING_PREPARATION);
            Assertions.assertThat(expertiseState.getStageProgress()).isEqualTo(ExpertiseStageProgress.IN_PROGRESS);

            ItemGroup itemGroup = expertiseState.getItemGroups().get(0);

            Assertions.assertThat(itemGroup.getStageType()).isEqualTo(ExpertiseStageType.AUTHENTICITY);
            Assertions.assertThat(itemGroup.getStageProgress()).isEqualTo(ExpertiseStageProgress.SUCCESS);
        }

        @Test
        public void shouldConvert_paidPreselling_waitPreselling() {
            IntegrationMobileOrderExpertiseDTO extendedExpertise = loadSnippet("1121988");
            OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
                extendedExpertise, Object::toString, (positionId) -> true, BUYER_CONTEXT
            );

            ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
            Assertions.assertThat(expertiseState.getStageType()).isEqualTo(ExpertiseStageType.PRESELLING_PREPARATION);
            Assertions.assertThat(expertiseState.getStageProgress()).isEqualTo(ExpertiseStageProgress.IN_PROGRESS);

            ItemGroup itemGroup = expertiseState.getItemGroups().get(0);

            Assertions.assertThat(itemGroup.getStageType()).isEqualTo(ExpertiseStageType.DEFECT_MATCHING);
            Assertions.assertThat(itemGroup.getStageProgress()).isEqualTo(ExpertiseStageProgress.SUCCESS);
        }

        private IntegrationMobileOrderExpertiseDTO loadSnippet(String name) {
            return loadSnippetByStartName(CONTEXT, name);
        }
    }


    @Nested
    public class Case_Single_1_Happypath_ProductTest {
        public static final String CONTEXT = "mocked-orders/single/1_happypath";

        @Test
        public void shouldConvert_1_happypath() {
            IntegrationMobileOrderExpertiseDTO extendedExpertise = loadSnippet("1_0");
            OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
                extendedExpertise, Object::toString, (positionId) -> true, BUYER_CONTEXT
            );

            ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
            Assertions.assertThat(expertiseState.needToShowItemsSeparately()).isFalse();
            Assertions.assertThat(expertiseState.getTotalDiscountAmount()).isEqualTo(ZERO);
            Assertions.assertThat(expertiseState.getStageType()).isEqualTo(ExpertiseStageType.IN_QUEUE);
            Assertions.assertThat(expertiseState.getStageProgress()).isEqualTo(ExpertiseStageProgress.IN_PROGRESS);
        }


        private IntegrationMobileOrderExpertiseDTO loadSnippet(String name) {
            return loadSnippetByStartName(CONTEXT, name);
        }
    }

    @Nested
    public class Case_6_NotReconciledChangesTest {
        public static final String CONTEXT_SEVERAL = "mocked-orders/several/6_notreconciledchanges";

        @Test
        public void shouldConvert_Several_Authenticity_Buyer() {
            IntegrationMobileOrderExpertiseDTO extendedExpertise = loadSnippet(CONTEXT_SEVERAL, "3_1");
            OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
                extendedExpertise, Object::toString, (positionId) -> true, BUYER_CONTEXT
            );

            ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
            Assertions.assertThat(expertiseState.needToShowItemsSeparately()).isFalse();
            Assertions.assertThat(expertiseState.getTotalDiscountAmount()).isEqualTo(ZERO);
            Assertions.assertThat(expertiseState.getStageType()).isEqualTo(ExpertiseStageType.AUTHENTICITY);
            Assertions.assertThat(expertiseState.getStageProgress()).isEqualTo(ExpertiseStageProgress.IN_PROGRESS);
        }

        @Test
        public void shouldConvert_Several_Authenticity_Seller() {
            IntegrationMobileOrderExpertiseDTO extendedExpertise = loadSnippet(CONTEXT_SEVERAL, "3_1");
            OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
                extendedExpertise, Object::toString, (positionId) -> true, SELLER_CONTEXT
            );

            ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
            Assertions.assertThat(expertiseState.needToShowItemsSeparately()).isFalse();
            Assertions.assertThat(expertiseState.getTotalDiscountAmount()).isEqualTo(ZERO);
            Assertions.assertThat(expertiseState.getStageType()).isEqualTo(ExpertiseStageType.AUTHENTICITY);
            Assertions.assertThat(expertiseState.getStageProgress()).isEqualTo(ExpertiseStageProgress.IN_PROGRESS);
        }

        @Test
        public void shouldConvert_Several_WaitDefectMatching_Buyer() {
            IntegrationMobileOrderExpertiseDTO extendedExpertise = loadSnippet(CONTEXT_SEVERAL, "4_0");
            OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
                extendedExpertise, Object::toString, (positionId) -> true, BUYER_CONTEXT
            );

            ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
            Assertions.assertThat(expertiseState.needToShowItemsSeparately()).isTrue();
            Assertions.assertThat(expertiseState.getTotalDiscountAmount()).isEqualTo(ZERO);
            Assertions.assertThat(expertiseState.getStageType()).isEqualTo(ExpertiseStageType.DEFECT_MATCHING);
            Assertions.assertThat(expertiseState.getStageProgress()).isEqualTo(ExpertiseStageProgress.IN_PROGRESS);
        }

        @Test
        public void shouldConvert_Several_WaitDefectMatching_Seller() {
            IntegrationMobileOrderExpertiseDTO extendedExpertise = loadSnippet(CONTEXT_SEVERAL, "4_0");
            OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
                extendedExpertise, Object::toString, (positionId) -> true, SELLER_CONTEXT
            );

            ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
            Assertions.assertThat(expertiseState.needToShowItemsSeparately()).isTrue();
            Assertions.assertThat(expertiseState.getTotalDiscountAmount()).isEqualTo(ZERO);
            Assertions.assertThat(expertiseState.getStageType()).isEqualTo(ExpertiseStageType.DEFECT_MATCHING);
            Assertions.assertThat(expertiseState.getStageProgress()).isEqualTo(ExpertiseStageProgress.IN_PROGRESS);
        }

        @Test
        public void shouldConvert_Several_DefectMatchingInProgress_Buyer() {
            IntegrationMobileOrderExpertiseDTO extendedExpertise = loadSnippet(CONTEXT_SEVERAL, "4_1-inprogress");
            OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
                extendedExpertise, Object::toString, (positionId) -> true, BUYER_CONTEXT
            );

            ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
            Assertions.assertThat(expertiseState.needToShowItemsSeparately()).isTrue();
            Assertions.assertThat(expertiseState.getTotalDiscountAmount()).isEqualTo(ZERO);
            Assertions.assertThat(expertiseState.getStageType()).isEqualTo(ExpertiseStageType.DEFECT_MATCHING);
            Assertions.assertThat(expertiseState.getStageProgress()).isEqualTo(ExpertiseStageProgress.IN_PROGRESS);
        }

        @Test
        public void shouldConvert_Several_DefectMatchingInProgress_Seller() {
            IntegrationMobileOrderExpertiseDTO extendedExpertise = loadSnippet(CONTEXT_SEVERAL, "4_1-inprogress");
            OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
                extendedExpertise, Object::toString, (positionId) -> true, SELLER_CONTEXT
            );

            ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
            Assertions.assertThat(expertiseState.needToShowItemsSeparately()).isTrue();
            Assertions.assertThat(expertiseState.getTotalDiscountAmount()).isEqualTo(ZERO);
            Assertions.assertThat(expertiseState.getStageType()).isEqualTo(ExpertiseStageType.DEFECT_MATCHING);
            Assertions.assertThat(expertiseState.getStageProgress()).isEqualTo(ExpertiseStageProgress.IN_PROGRESS);
        }

        @Test
        public void shouldConvert_Several_DefectMatchingPressedReject_Buyer() {
            IntegrationMobileOrderExpertiseDTO extendedExpertise = loadSnippet(CONTEXT_SEVERAL, "4_1-pressed");
            OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
                extendedExpertise, Object::toString, (positionId) -> true, BUYER_CONTEXT
            );

            ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
            Assertions.assertThat(expertiseState.needToShowItemsSeparately()).isTrue();
            Assertions.assertThat(expertiseState.getTotalDiscountAmount()).isEqualTo(ZERO);
            Assertions.assertThat(expertiseState.getStageType()).isEqualTo(ExpertiseStageType.DEFECT_MATCHING);
            Assertions.assertThat(expertiseState.getStageProgress()).isEqualTo(ExpertiseStageProgress.IN_PROGRESS);
        }

        @Test
        public void shouldConvert_Several_DefectMatchingPressedReject_Seller() {
            IntegrationMobileOrderExpertiseDTO extendedExpertise = loadSnippet(CONTEXT_SEVERAL, "4_1-pressed");
            OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
                extendedExpertise, Object::toString, (positionId) -> true, SELLER_CONTEXT
            );

            ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
            Assertions.assertThat(expertiseState.needToShowItemsSeparately()).isTrue();
            Assertions.assertThat(expertiseState.getTotalDiscountAmount()).isEqualTo(ZERO);
            Assertions.assertThat(expertiseState.getStageType()).isEqualTo(ExpertiseStageType.DEFECT_MATCHING);
            Assertions.assertThat(expertiseState.getStageProgress()).isEqualTo(ExpertiseStageProgress.IN_PROGRESS);
        }

        @Test
        public void shouldConvert_Several_WaitPresellingPreparation_Buyer() {
            IntegrationMobileOrderExpertiseDTO extendedExpertise = loadSnippet(CONTEXT_SEVERAL, "5_0");
            OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
                extendedExpertise, Object::toString, (positionId) -> true, BUYER_CONTEXT
            );

            ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
            Assertions.assertThat(expertiseState.needToShowItemsSeparately()).isTrue();
            Assertions.assertThat(expertiseState.getTotalDiscountAmount()).isEqualTo(ZERO);
            Assertions.assertThat(expertiseState.getStageType()).isEqualTo(ExpertiseStageType.PRESELLING_PREPARATION);
            Assertions.assertThat(expertiseState.getStageProgress()).isEqualTo(ExpertiseStageProgress.IN_PROGRESS);
        }

        @Test
        public void shouldConvert_Several_WaitPresellingPreparation_Seller() {
            IntegrationMobileOrderExpertiseDTO extendedExpertise = loadSnippet(CONTEXT_SEVERAL, "5_0");
            OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
                extendedExpertise, Object::toString, (positionId) -> true, SELLER_CONTEXT
            );

            ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
            Assertions.assertThat(expertiseState.needToShowItemsSeparately()).isTrue();
            Assertions.assertThat(expertiseState.getTotalDiscountAmount()).isEqualTo(ZERO);
            Assertions.assertThat(expertiseState.getStageType()).isEqualTo(ExpertiseStageType.PRESELLING_PREPARATION);
            Assertions.assertThat(expertiseState.getStageProgress()).isEqualTo(ExpertiseStageProgress.IN_PROGRESS);
        }

        @Test
        public void shouldConvert_Several_PresellingPreparation_Buyer() {
            IntegrationMobileOrderExpertiseDTO extendedExpertise = loadSnippet(CONTEXT_SEVERAL, "5_1");
            OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
                extendedExpertise, Object::toString, (positionId) -> true, BUYER_CONTEXT
            );

            ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
            Assertions.assertThat(expertiseState.needToShowItemsSeparately()).isTrue();
            Assertions.assertThat(expertiseState.getTotalDiscountAmount()).isEqualTo(ZERO);
            Assertions.assertThat(expertiseState.getStageType()).isEqualTo(ExpertiseStageType.PRESELLING_PREPARATION);
            Assertions.assertThat(expertiseState.getStageProgress()).isEqualTo(ExpertiseStageProgress.IN_PROGRESS);
        }

        @Test
        public void shouldConvert_Several_PresellingPreparation_Seller() {
            IntegrationMobileOrderExpertiseDTO extendedExpertise = loadSnippet(CONTEXT_SEVERAL, "5_1");
            OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
                extendedExpertise, Object::toString, (positionId) -> true, SELLER_CONTEXT
            );

            ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
            Assertions.assertThat(expertiseState.needToShowItemsSeparately()).isTrue();
            Assertions.assertThat(expertiseState.getTotalDiscountAmount()).isEqualTo(ZERO);
            Assertions.assertThat(expertiseState.getStageType()).isEqualTo(ExpertiseStageType.PRESELLING_PREPARATION);
            Assertions.assertThat(expertiseState.getStageProgress()).isEqualTo(ExpertiseStageProgress.IN_PROGRESS);
        }

        @Test
        public void shouldConvert_Several_WaitPacking_Buyer() {
            IntegrationMobileOrderExpertiseDTO extendedExpertise = loadSnippet(CONTEXT_SEVERAL, "6_0");
            OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
                extendedExpertise, Object::toString, (positionId) -> true, BUYER_CONTEXT
            );

            ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
            Assertions.assertThat(expertiseState.needToShowItemsSeparately()).isTrue();
            Assertions.assertThat(expertiseState.getTotalDiscountAmount()).isEqualTo(ZERO);
            Assertions.assertThat(expertiseState.getStageType()).isEqualTo(ExpertiseStageType.PRESELLING_PREPARATION);
            Assertions.assertThat(expertiseState.getStageProgress()).isEqualTo(ExpertiseStageProgress.IN_PROGRESS);
        }

        @Test
        public void shouldConvert_Several_WaitPacking_Seller() {
            IntegrationMobileOrderExpertiseDTO extendedExpertise = loadSnippet(CONTEXT_SEVERAL, "6_0");
            OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
                extendedExpertise, Object::toString, (positionId) -> true, SELLER_CONTEXT
            );

            ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
            Assertions.assertThat(expertiseState.needToShowItemsSeparately()).isTrue();
            Assertions.assertThat(expertiseState.getTotalDiscountAmount()).isEqualTo(ZERO);
            Assertions.assertThat(expertiseState.getStageType()).isEqualTo(ExpertiseStageType.PRESELLING_PREPARATION);
            Assertions.assertThat(expertiseState.getStageProgress()).isEqualTo(ExpertiseStageProgress.IN_PROGRESS);
        }

        @Test
        public void shouldConvert_Several_Packing_Buyer() {
            IntegrationMobileOrderExpertiseDTO extendedExpertise = loadSnippet(CONTEXT_SEVERAL, "6_1");
            OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
                extendedExpertise, Object::toString, (positionId) -> true, BUYER_CONTEXT
            );

            ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
            Assertions.assertThat(expertiseState.needToShowItemsSeparately()).isTrue();
            Assertions.assertThat(expertiseState.getTotalDiscountAmount()).isEqualTo(ZERO);
            Assertions.assertThat(expertiseState.getStageType()).isEqualTo(ExpertiseStageType.PACKING);
            Assertions.assertThat(expertiseState.getStageProgress()).isEqualTo(ExpertiseStageProgress.IN_PROGRESS);
        }

        @Test
        public void shouldConvert_Several_Packing_Seller() {
            IntegrationMobileOrderExpertiseDTO extendedExpertise = loadSnippet(CONTEXT_SEVERAL, "6_1");
            OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
                extendedExpertise, Object::toString, (positionId) -> true, SELLER_CONTEXT
            );

            ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
            Assertions.assertThat(expertiseState.needToShowItemsSeparately()).isTrue();
            Assertions.assertThat(expertiseState.getTotalDiscountAmount()).isEqualTo(ZERO);
            Assertions.assertThat(expertiseState.getStageType()).isEqualTo(ExpertiseStageType.PACKING);
            Assertions.assertThat(expertiseState.getStageProgress()).isEqualTo(ExpertiseStageProgress.IN_PROGRESS);
        }

        @Test
        public void shouldConvert_Several_Finished_Buyer() {
            IntegrationMobileOrderExpertiseDTO extendedExpertise = loadSnippet(CONTEXT_SEVERAL, "7_0");
            OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
                extendedExpertise, Object::toString, (positionId) -> true, BUYER_CONTEXT
            );

            ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
            Assertions.assertThat(expertiseState.needToShowItemsSeparately()).isTrue();
            Assertions.assertThat(expertiseState.getTotalDiscountAmount()).isEqualTo(ZERO);
            Assertions.assertThat(expertiseState.getStageType()).isEqualTo(ExpertiseStageType.FINISHED);
            Assertions.assertThat(expertiseState.getStageProgress()).isEqualTo(ExpertiseStageProgress.SUCCESS);
        }

        @Test
        public void shouldConvert_Several_Finished_Seller() {
            IntegrationMobileOrderExpertiseDTO extendedExpertise = loadSnippet(CONTEXT_SEVERAL, "7_0");
            OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
                extendedExpertise, Object::toString, (positionId) -> true, SELLER_CONTEXT
            );

            ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
            Assertions.assertThat(expertiseState.needToShowItemsSeparately()).isTrue();
            Assertions.assertThat(expertiseState.getTotalDiscountAmount()).isEqualTo(ZERO);
            Assertions.assertThat(expertiseState.getStageType()).isEqualTo(ExpertiseStageType.FINISHED);
            Assertions.assertThat(expertiseState.getStageProgress()).isEqualTo(ExpertiseStageProgress.SUCCESS);
        }

        private IntegrationMobileOrderExpertiseDTO loadSnippet(String context, String name) {
            return loadSnippetByStartName(context, name);
        }
    }
}
