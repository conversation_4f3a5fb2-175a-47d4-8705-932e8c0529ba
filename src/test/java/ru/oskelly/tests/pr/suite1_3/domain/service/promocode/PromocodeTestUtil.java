package ru.oskelly.tests.pr.suite1_3.domain.service.promocode;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import su.reddot.domain.dao.BrandRepository;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.product.ProductItemRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.applyRule.FilterRelation;
import su.reddot.domain.model.discount.PromoCode;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.order.OrderPositionState;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.promocode.PromocodeDTOFull;
import su.reddot.domain.service.dto.promocode.PromocodeFilterDTO;
import su.reddot.domain.service.promocode.PromoCodeService;
import su.reddot.domain.service.promocode.model.PromoCodeAppliedNumberType;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class PromocodeTestUtil {
    public static final String TEST_PROMO_CODE_3 = "TestPromoCode3";
    public static final String TEST_PROMO_CODE_2 = "TestPromoCode2";
    public static final String TEST_PROMO_CODE = "TestPromoCode";
    public static final String TEST_PROMO_CODE_UPPER = "TESTPROMOCODE";
    public static final String TEST_PROMO_CODE_UPPER_WITH_SPACE = "TESTPROMOCODE ";

    public static final String TEST_PROMO_CODE_GENERATED_PREFIX = "TEST_PROMO_CODE_GENERATED_";
    public static final String TEST_GENERATED_VALUE_AA1 = "AA1";
    public static final String TEST_GENERATED_VALUE_AA2 = "AA2";
    public static final String TEST_GENERATED_VALUE_AA003 = "AA003";
    public static final String TEST_GENERATED_VALUE_AA004 = "AA004";
    public static final String TEST_GENERATED_VALUE_AA005 = "AA005";

    public static final String EXPECTED_DUPLICATE_ERROR_PART_1 = "Промокод с кодом '";
    public static final String EXPECTED_DUPLICATE_ERROR_PART_2 = "' уже существует";
    public static final String COPY_POSTFIX = "_copy";

    @Autowired
    PromoCodeService defaultPromocodeService;
    @Autowired
    OrderRepository orderRepository;
    @Autowired
    ProductRepository productRepository;
    @Autowired
    BrandRepository brandRepository;
    @Autowired
    ProductItemRepository productItemRepository;
    @Autowired
    UserRepository userRepository;

    public void addOrderPositions(Order order, BigDecimal amount, ProductState state) {
        val product = new Product();
        product.setBrand(brandRepository.findById(1L).get());
        product.setCategoryId(1L);
        product.setSeller(userRepository.findById(1L).get());
        product.setProductConditionId(1L);
        product.setProductState(state);
        val savedProduct = productRepository.saveAndFlush(product);

        val productItem = new ProductItem();
        productItem.setProduct(savedProduct);
        val savedItem = productItemRepository.saveAndFlush(productItem);

        val op = new OrderPosition();
        op.setProductItem(savedItem);
        op.setAmount(amount);
        op.setState(OrderPositionState.READY_TO_SHIP);

        order.setOrderPosition(op);
    }

    public PromoCode getPromoCodeWithOrderSummaryAmountCompositeFilter(Set<PromocodeFilterDTO> filters) {
        val promocodeDTOFull = new PromocodeDTOFull()
            .setOrdersAmountSummaryFilters(filters);
        promocodeDTOFull.setCode(TEST_PROMO_CODE);
        promocodeDTOFull.setPercent(BigDecimal.valueOf(5));
        return defaultPromocodeService.createPromoCode(promocodeDTOFull);
    }

    public PromoCode getPromoCodeWithOrderAmountCompositeFilter(Set<PromocodeFilterDTO> filters) {
        val promocodeDTOFull = new PromocodeDTOFull()
            .setOrderAmountFilters(filters);
        promocodeDTOFull.setCode(TEST_PROMO_CODE);
        promocodeDTOFull.setPercent(BigDecimal.valueOf(5));
        return defaultPromocodeService.createPromoCode(promocodeDTOFull);
    }

    public Order createOrder(BigDecimal amount, OrderState state, User buyer) {
        return createOrderWithPromoCode(amount, state, null, buyer, ProductState.PUBLISHED);
    }

    public Order createOrder(BigDecimal amount, OrderState state, User buyer, ProductState productState) {
        return createOrderWithPromoCode(amount, state, null, buyer, productState);
    }

    public Order createOrderWithPromoCode(BigDecimal amount, OrderState state, PromoCode promoCode, User buyer) {
        return createOrderWithPromoCode(amount, state, promoCode, buyer, ProductState.PUBLISHED);
    }

    public Order createOrderWithPromoCode(BigDecimal amount, OrderState state, PromoCode promoCode, User buyer, ProductState productState) {
        val order = new Order();
        order.setUuid(UUID.randomUUID());
        order.setState(OrderState.CREATED); // чтобы установить createTime
        if (state == OrderState.COMPLETED) {
            order.setState(OrderState.HOLD); // чтобы установить hold_time
        }
        if (state != OrderState.CREATED) {
            order.setState(state);
        }
        order.setGuestToken("GUEST_TOKEN");
        order.setBuyer(buyer);
        order.setAmount(amount);
        order.setEffectiveAmount(BigDecimal.valueOf(1010));
        order.setPromoCode(promoCode);
        addOrderPositions(order, amount, productState);

        return orderRepository.saveAndFlush(order);
    }

    /**
     * Создание одноразового промокода
     *
     * @return одноразовый промокод
     */
    public PromoCode createOneTimePromoCode() {
        final Set<PromocodeFilterDTO> promocodeFilterDTOS = new HashSet<>();
        promocodeFilterDTOS.add(new PromocodeFilterDTO(100L, FilterRelation.GREATER));
        promocodeFilterDTOS.add(new PromocodeFilterDTO(40000L, FilterRelation.LESS));
        val createPromocodeRequest = new PromocodeDTOFull()
            .setOrdersAmountSummaryFilters(promocodeFilterDTOS);
        createPromocodeRequest.setCode(TEST_PROMO_CODE_3);
        createPromocodeRequest.setPercent(BigDecimal.valueOf(5));
        createPromocodeRequest.setNumberOfApplies(1);
        createPromocodeRequest.setAppliedNumberType(PromoCodeAppliedNumberType.GLOBALLY);

        return defaultPromocodeService.createPromoCode(createPromocodeRequest);
    }

    public PromoCode createPromoCodeWithOrderAmountFilter(Long expected, FilterRelation filterRelation) {
        return defaultPromocodeService.createPromoCode(
            createFullDtoPromoCodeWithOrderAmountFilter(expected, filterRelation));
    }

    public PromocodeDTOFull createFullDtoPromoCodeWithOrderAmountFilter(Long expected, FilterRelation filterRelation) {
        return createDefaultPromocodeDTOFull()
            .setOrderAmountFilters(Collections.singleton(new PromocodeFilterDTO(expected, filterRelation)));
    }

    public static PromocodeDTOFull createDefaultPromocodeDTOFull() {
        val promocodeDTOFull = new PromocodeDTOFull();
        promocodeDTOFull.setCode(TEST_PROMO_CODE);
        promocodeDTOFull.setPercent(BigDecimal.valueOf(5));
        return promocodeDTOFull;
    }
}
