package ru.oskelly.tests.pr.suite1_3.domain.service.search;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.service.dto.search.SearchContextType;
import su.reddot.domain.service.dto.search.SearchTabDTO;
import su.reddot.domain.service.search.SearchService;
import su.reddot.domain.service.search.SearchTabType;
import su.reddot.infrastructure.configuration.OskellyApplication;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@SpringBootTest(classes =  {OskellyApplication.class})
@ExtendWith(SpringExtension.class)
@ActiveProfiles(AbstractSpringTest.testProfiles)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
@Slf4j
public class SearchServiceTest {

    @Autowired
    private SearchService searchService;

    @Test
    public void testGetSearchTabs() {
        checkDefaultSequence(searchService.getSearchTabs(null));
        checkDefaultSequence(searchService.getSearchTabs(SearchContextType.DEFAULT));
        checkSocialSequence(searchService.getSearchTabs(SearchContextType.SOCIAL));
    }

    private void checkDefaultSequence(List<SearchTabDTO> tabs) {
        checkBase(
                Arrays.asList(SearchTabType.PRODUCTS, SearchTabType.BRANDS, SearchTabType.USERS, SearchTabType.POSTS, SearchTabType.HASHTAGS),
                tabs
        );
    }

    private void checkSocialSequence(List<SearchTabDTO> tabs) {
        checkBase(
                Arrays.asList(SearchTabType.POSTS, SearchTabType.HASHTAGS, SearchTabType.PRODUCTS, SearchTabType.BRANDS, SearchTabType.USERS),
                tabs
        );
    }

    private void checkBase(List<SearchTabType> types, List<SearchTabDTO> tabs) {
        assertNotNull(types);
        assertNotNull(tabs);
        assertEquals(5, SearchTabType.values().length);
        assertEquals(SearchTabType.values().length, types.size());
        assertEquals(types.size(), tabs.size());
        for (int i = 0; i < types.size(); i++) {
            SearchTabType type = types.get(i);
            assertNotNull(type);
            SearchTabDTO dto = tabs.get(i);
            assertNotNull(dto);
            assertEquals(type, SearchTabType.valueOf(dto.getCode()));
        }
    }
}