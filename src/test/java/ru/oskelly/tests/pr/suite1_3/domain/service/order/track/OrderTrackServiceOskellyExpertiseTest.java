package ru.oskelly.tests.pr.suite1_3.domain.service.order.track;

import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import su.reddot.domain.model.order.OrderSource;
import su.reddot.domain.service.dto.order.track.OrderStageDTO;
import su.reddot.domain.service.dto.order.track.OrderStageDTO.ProgressState;
import su.reddot.domain.service.dto.order.track.OrderStageDTO.SuccessState;
import su.reddot.domain.service.dto.order.track.PositionDetailsDTO;

import java.util.Arrays;

@Slf4j
public class OrderTrackServiceOskellyExpertiseTest extends OrderTrackServiceOskellyExpertiseBaseTest {

    public static final String HAPPYPATH_WITHOUT_DEFECTS_CONTEXT = "happypath/nodefects";
    public static final String HAPPYPATH_WITH_DEFECTS_2_PRODUCTS_CONTEXT = "happypath/defects2products";
    public static final String HAPPYPATH_WITH_DEFECTS_1_PRODUCT_CONTEXT = "happypath/defects1product";
    public static final String DIFFERENTORDERS_CONTEXT = "differentorders";

    @Test
    public void shouldSetUpdatedAtFromOrderProcessing() {
        OrderStageDTO expertiseStage = expectExpertiseStage(
            BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1122109-order-with-updated-at"
        );
        Assertions.assertThat(expertiseStage.getUpdatedAt()).isEqualTo("2020-12-30T00:05Z");
    }

    @Test
    public void shouldCreateOrderTrack_ForBuyer_oneProduct_noDefects() {
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITHOUT_DEFECTS_CONTEXT, "1_0_wait-unpacking",
            null, "st.IN_QUEUE.IN_PROGRESS.description.Buyer.singular"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITHOUT_DEFECTS_CONTEXT, "1_1_unpacking",
            null, "st.UNPACKING.IN_PROGRESS.description.Buyer.singular"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITHOUT_DEFECTS_CONTEXT, "2_0_wait-quality-control",
            null, "st.UNPACKING.IN_PROGRESS.description.Buyer.singular"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITHOUT_DEFECTS_CONTEXT, "2_1_quality-control",
            null, "st.QUALITY_CONTROL.IN_PROGRESS.description.Buyer.singular"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITHOUT_DEFECTS_CONTEXT, "3_0_wait-authenticity",
            null, "st.QUALITY_CONTROL.IN_PROGRESS.description.Buyer.singular"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITHOUT_DEFECTS_CONTEXT, "3_1_authenticity",
            null, "st.AUTHENTICITY.IN_PROGRESS.description.Buyer.singular"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITHOUT_DEFECTS_CONTEXT, "3_2_authenticity-PRODUCT-ORIGINAL-button-pressed",
            null, "st.AUTHENTICITY.IN_PROGRESS.description.Buyer.singular"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITHOUT_DEFECTS_CONTEXT, "6_0-wait-packing",
            null, "st.AUTHENTICITY.SUCCESS.description.Buyer.singular"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITHOUT_DEFECTS_CONTEXT, "6_1_packing",
            null, "st.PACKING.IN_PROGRESS.description.Buyer.singular"
        );
        expect_Description_and_Complete(
            BUYER_CONTEXT, HAPPYPATH_WITHOUT_DEFECTS_CONTEXT, "7_0_finished",
            SuccessState.SUCCEEDED, "st.FINISHED.SUCCESS.description.Buyer"
        );
    }

    @Test
    public void shouldCreateOrderTrack_ForBuyer_twoProducts_withDefects() {
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_2_PRODUCTS_CONTEXT, "1_0_wait-unpacking",
            null, "st.IN_QUEUE.IN_PROGRESS.description.Buyer.plural"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_2_PRODUCTS_CONTEXT, "1_1_unpacking",
            null, "st.UNPACKING.IN_PROGRESS.description.Buyer.plural"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_2_PRODUCTS_CONTEXT, "2_0_wait-quality-control",
            null, "st.UNPACKING.IN_PROGRESS.description.Buyer.plural"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_2_PRODUCTS_CONTEXT, "2_1_quality-control",
            null, "st.QUALITY_CONTROL.IN_PROGRESS.description.Buyer.plural"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_2_PRODUCTS_CONTEXT, "3_0_wait-authenticity",
            null, "st.QUALITY_CONTROL.IN_PROGRESS.description.Buyer.plural"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_2_PRODUCTS_CONTEXT, "3_1_authenticity",
            null, "st.AUTHENTICITY.IN_PROGRESS.description.Buyer.plural"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_2_PRODUCTS_CONTEXT, "3_2_authenticity-ADDED-DEFECT",
            null, "st.AUTHENTICITY.IN_PROGRESS.description.Buyer.plural"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_2_PRODUCTS_CONTEXT, "3_3_authenticity-ADDED-ANOTHER-DEFECT",
            null, "st.AUTHENTICITY.IN_PROGRESS.description.Buyer.plural"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_2_PRODUCTS_CONTEXT, "3_4_authenticity-PRODUCT-1-ORIGINAL-button-pressed",
            null, "st.AUTHENTICITY.IN_PROGRESS.description.Buyer.plural"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_2_PRODUCTS_CONTEXT, "3_5_authenticity-PRODUCT-2-FAKE-button-pressed",
            null, "st.AUTHENTICITY.IN_PROGRESS.description.Buyer.plural"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_2_PRODUCTS_CONTEXT, "4_0_wait-defect-matching",
            null, "st.AUTHENTICITY.IN_PROGRESS.description.Buyer.plural"
        );
        expect_Positions_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_2_PRODUCTS_CONTEXT, "4_1_defect-matching",
            SuccessState.PARTIALLY_SUCCEEDED, Arrays.asList(
                new PositionDetailsDTO()
                    .setPositionId(1495935L)
                    .setText(""),
                new PositionDetailsDTO()
                    .setPositionId(1495933L)
                    .setText("")
            )
        );
        expect_Positions_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_2_PRODUCTS_CONTEXT, "4_2_defect-matching-ONE-DEFECT-RECONSILED",
            SuccessState.PARTIALLY_SUCCEEDED, Arrays.asList(
                new PositionDetailsDTO()
                    .setPositionId(1495935L)
                    .setText(""),
                new PositionDetailsDTO()
                    .setPositionId(1495933L)
                    .setText("")
            )
        );
        expect_Positions_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_2_PRODUCTS_CONTEXT, "4_3_defect-matching-PRODUCT-1-ALL-DEFECT-RECONSILED",
            SuccessState.PARTIALLY_SUCCEEDED, Arrays.asList(
                new PositionDetailsDTO()
                    .setPositionId(1495935L)
                    .setText(""),
                new PositionDetailsDTO()
                    .setPositionId(1495933L)
                    .setText("")
            )
        );
        expect_Positions_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_2_PRODUCTS_CONTEXT, "6_0-wait-packing-FINISH-DEFECT-MATCHING",
            SuccessState.PARTIALLY_SUCCEEDED, Arrays.asList(
                new PositionDetailsDTO()
                    .setPositionId(1495935L)
                    .setText(""),
                new PositionDetailsDTO()
                    .setPositionId(1495933L)
                    .setText("")
            )
        );
        expect_Positions_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_2_PRODUCTS_CONTEXT, "6_1_packing",
            SuccessState.PARTIALLY_SUCCEEDED, Arrays.asList(
                new PositionDetailsDTO()
                    .setPositionId(1495935L)
                    .setText(""),
                new PositionDetailsDTO()
                    .setPositionId(1495933L)
                    .setText("")
            )
        );
        expect_Positions_and_InProgress(
            SELLER_CONTEXT, HAPPYPATH_WITH_DEFECTS_2_PRODUCTS_CONTEXT, "6_1_packing",
            SuccessState.PARTIALLY_SUCCEEDED, Arrays.asList(
                new PositionDetailsDTO()
                    .setPositionId(1495935L)
                    .setText(""),
                new PositionDetailsDTO()
                    .setPositionId(1495933L)
                    .setText("")
            )
        );
        expect_Positions_and_Complete(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_2_PRODUCTS_CONTEXT, "7_0_finished",
            SuccessState.PARTIALLY_SUCCEEDED, Arrays.asList(
                new PositionDetailsDTO()
                    .setPositionId(1495935L)
                    .setText(""),
                new PositionDetailsDTO()
                    .setPositionId(1495933L)
                    .setText("")
            )
        );
    }

    @Test
    public void shouldCreateOrderTrack_ForBuyer_singleProduct_withDefects() {
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_1_PRODUCT_CONTEXT, "1_0_wait-unpacking",
            null, "st.IN_QUEUE.IN_PROGRESS.description.Buyer.singular"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_1_PRODUCT_CONTEXT, "1_1_unpacking",
            null, "st.UNPACKING.IN_PROGRESS.description.Buyer.singular"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_1_PRODUCT_CONTEXT, "2_0_wait-quality-control",
            null, "st.UNPACKING.IN_PROGRESS.description.Buyer.singular"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_1_PRODUCT_CONTEXT, "2_1_quality-control",
            null, "st.QUALITY_CONTROL.IN_PROGRESS.description.Buyer.singular"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_1_PRODUCT_CONTEXT, "3_0_wait-authenticity",
            null, "st.QUALITY_CONTROL.IN_PROGRESS.description.Buyer.singular"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_1_PRODUCT_CONTEXT, "3_1_authenticity",
            null, "st.AUTHENTICITY.IN_PROGRESS.description.Buyer.singular"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_1_PRODUCT_CONTEXT, "3_2_authenticity-ADDED-DEFECT",
            null, "st.AUTHENTICITY.IN_PROGRESS.description.Buyer.singular"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_1_PRODUCT_CONTEXT, "3_3_authenticity-ADDED-ANOTHER-DEFECT",
            null, "st.AUTHENTICITY.IN_PROGRESS.description.Buyer.singular"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_1_PRODUCT_CONTEXT, "3_4_authenticity-PRODUCT-1-ORIGINAL-button-pressed",
            null, "st.AUTHENTICITY.IN_PROGRESS.description.Buyer.singular"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_1_PRODUCT_CONTEXT, "3_5_authenticity-PRODUCT-2-FAKE-button-pressed",
            null, "st.AUTHENTICITY.IN_PROGRESS.description.Buyer.singular"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_1_PRODUCT_CONTEXT, "4_0_wait-defect-matching",
            null, "st.AUTHENTICITY.IN_PROGRESS.description.Buyer.singular"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_1_PRODUCT_CONTEXT, "4_1_defect-matching",
            SuccessState.PARTIALLY_SUCCEEDED, "st.DEFECT_MATCHING.IN_PROGRESS.description.Buyer.singular"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_1_PRODUCT_CONTEXT, "4_2_defect-matching-ONE-DEFECT-RECONSILED",
            SuccessState.PARTIALLY_SUCCEEDED, "st.DEFECT_MATCHING.IN_PROGRESS.description.Buyer.singular"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_1_PRODUCT_CONTEXT, "4_3_defect-matching-PRODUCT-1-ALL-DEFECT-RECONSILED",
            SuccessState.PARTIALLY_SUCCEEDED, "st.DEFECT_MATCHING.IN_PROGRESS.description.Buyer.singular"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_1_PRODUCT_CONTEXT, "6_0-wait-packing-FINISH-DEFECT-MATCHING",
            SuccessState.PARTIALLY_SUCCEEDED, "st.DEFECT_MATCHING.SUCCESS.description.Buyer.singular[1495935 brand1495935|600 ₽]"
        );
        expect_Description_and_InProgress(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_1_PRODUCT_CONTEXT, "6_1_packing",
            null, "st.PACKING.IN_PROGRESS.description.Buyer.singular"
        );
        expect_Description_and_Complete(
            BUYER_CONTEXT, HAPPYPATH_WITH_DEFECTS_1_PRODUCT_CONTEXT, "7_0_finished",
            SuccessState.SUCCEEDED, "st.FINISHED.SUCCESS.description.Buyer"
        );
    }

    @Nested
    class DifferentOrdersTest {
        @Test
        public void singleProductZeroDiscountForDefect_1122109() {
            OrderStageDTO expertiseStage = expectExpertiseStage(
                SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1122109-zero-discount-order"
            );
            Assertions.assertThat(expertiseStage.getDescription())
                .isEqualTo("st.DEFECT_MATCHING.SUCCESS.zeroDiscount.description.Seller.singular[1519795 brand1519795]");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);

            expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1122109-zero-discount-order",
                    OrderSource.BOUTIQUE
            );
            Assertions.assertThat(expertiseStage.getDescription())
                      .isEqualTo("st.DEFECT_MATCHING.SUCCESS.zeroDiscount.boutiqueDescription.Seller.singular[1519795 brand1519795]");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);

            expertiseStage = expectExpertiseStage(
                BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1122109-zero-discount-order"
            );
            Assertions.assertThat(expertiseStage.getDescription())
                .isEqualTo("st.DEFECT_MATCHING.SUCCESS.zeroDiscount.description.Buyer.singular[1519795 brand1519795]");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);
        }

        @Test
        public void defectMatchingDifferentItems_1122106() {
            OrderStageDTO expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1122106-defect-matching-differents-items"
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(null);
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);
            expectAllPositionsTexts(
                    expertiseStage,
                    "st.DEFECT_MATCHING.IN_PROGRESS.position.Seller.singular[1519093 brand1519093]",
                    "st.CHANGES_MATCHING.IN_PROGRESS.position.Seller.singular[1519095 brand1519095]",
                    "st.AUTHENTICITY.SUCCESS.position.Seller.singular[1519091 brand1519091]"
            );

            expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1122106-defect-matching-differents-items"
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(null);
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);
            expectAllPositionsTexts(
                    expertiseStage,
                    "st.DEFECT_MATCHING.IN_PROGRESS.position.Buyer.singular[1519093 brand1519093]",
                    "st.CHANGES_MATCHING.IN_PROGRESS.position.Buyer.singular[1519095 brand1519095]",
                    "st.AUTHENTICITY.SUCCESS.position.Buyer.singular[1519091 brand1519091]"
            );
        }

        @Test
        public void waitPreSellingPreparation_1122062() {
            OrderStageDTO expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1122062-wait-preselling-preparation"
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(null);
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);
            expectAllPositionsTexts(
                    expertiseStage,
                    "st.CHANGES_MATCHING.SUCCESS.position.Seller.singular[1514749 brand1514749|500 ₽]",
                    "st.AUTHENTICITY.SUCCESS.position.Seller.singular[1514745 brand1514745]",
                    "st.CHANGES_MATCHING.FAILED.position.Seller.singular[1514747 brand1514747]"
            );

            expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1122062-wait-preselling-preparation",
                    OrderSource.BOUTIQUE
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(null);
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);
            expectAllPositionsTexts(
                    expertiseStage,
                    "st.CHANGES_MATCHING.SUCCESS.boutiquePosition.Seller.singular[1514749 brand1514749|500 ₽]",
                    "st.AUTHENTICITY.SUCCESS.boutiquePosition.Seller.singular[1514745 brand1514745]",
                    "st.CHANGES_MATCHING.FAILED.boutiquePosition.Seller.singular[1514747 brand1514747]"
            );

            expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1122062-wait-preselling-preparation"
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(null);
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);
            expectAllPositionsTexts(
                    expertiseStage,
                    "st.CHANGES_MATCHING.SUCCESS.position.Buyer.singular[1514749 brand1514749|500 ₽]",
                    "st.AUTHENTICITY.SUCCESS.position.Buyer.singular[1514745 brand1514745]",
                    "st.CHANGES_MATCHING.FAILED.position.Buyer.singular[1514747 brand1514747]"
            );
        }

        @Test
        public void packing_1122062() {
            OrderStageDTO expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1122062-packing"
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(null);
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);
            expectAllPositionsTexts(
                    expertiseStage,
                    "st.FINISHED.FAILED.position.Seller.singular[1514747 brand1514747]",
                    "st.PACKING.IN_PROGRESS.position.Seller.plural[1514749 brand1514749, 1514745 brand1514745]"
            );

            expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1122062-packing"
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(null);
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);
            expectAllPositionsTexts(
                    expertiseStage,
                    "st.FINISHED.FAILED.position.Buyer.singular[1514747 brand1514747]",
                    "st.PACKING.IN_PROGRESS.position.Buyer.plural[1514749 brand1514749, 1514745 brand1514745]"
            );
        }

        @Test
        public void expertiseFailedFakeSinglePosition_1605490() {
            OrderStageDTO expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1605490-expertise-failed-fake-single-position"
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(null);
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);

            expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1605490-expertise-failed-fake-single-position"
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.AUTHENTICITY.FAILED.NOT_ORIGINAL.description.Buyer.singular[336246503 brand336246503]"
            );
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);
        }

        @Test
        public void failedChangesNotReconciled_1317924() {
            // TODO: this is the case of changes not reconciled
            OrderStageDTO expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1317924-failed-changes-not-reconsiled"
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.CHANGES_MATCHING.FAILED.description.Buyer.singular"
            );
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);
        }

        @Test
        public void waitAuthenticity_1599911() {
            OrderStageDTO expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1599911-wait-authencity"
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "disabledDescription.Buyer.singular"
            );
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.UPCOMING);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(null);
        }

        @Test
        public void expertiseFailed_1444057() {
            OrderStageDTO expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1444057-expertise-failed"
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.CHANGES_MATCHING.FAILED.description.Buyer.singular"
            );
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);
        }

        @Test
        public void rejectedPosition_1122020() {
            OrderStageDTO expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1122020-rejected-position"
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.DEFECT_MATCHING.IN_PROGRESS.description.Buyer.plural"
            );
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(null);

            expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1122020-rejected-position"
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.DEFECT_MATCHING.IN_PROGRESS.description.Seller.plural"
            );
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(null);
        }

        @Test
        public void expertiseFailed_1591202_prod() {
            OrderStageDTO expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1591202-expertise-failed-prod"
            );
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);

            expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1591202-expertise-failed-prod"
            );
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);
        }

        @Test
        public void expertiseComplete_1121870() {
            OrderStageDTO expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1121870-expertise-complete"
            );
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.SUCCEEDED);

            expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1121870-expertise-complete"
            );
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.SUCCEEDED);
        }

        @Test
        public void waitDefectMatching_1121699() {
            OrderStageDTO expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1121699-wait-defect-matching"
            );
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);

            expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1121699-wait-defect-matching"
            );
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
        }

        @Test
        public void allFakeExpertiseComplete_1121880() {
            OrderStageDTO expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1121880-all-fake-expertise-complete"
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.AUTHENTICITY.FAILED.NOT_ORIGINAL.description.Buyer.plural[1497242 brand1497242, 1497240 brand1497240, 1497238 brand1497238]");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);

            expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1121880-all-fake-expertise-complete"
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(null);
            expectAllPositionsTexts(
                    expertiseStage,
                    "st.AUTHENTICITY.FAILED.NOT_ORIGINAL.position.Seller[1497242 brand1497242|fake]",
                    "st.AUTHENTICITY.FAILED.NOT_ORIGINAL.position.Seller[1497240 brand1497240|fake]",
                    "st.AUTHENTICITY.FAILED.NOT_ORIGINAL.position.Seller[1497238 brand1497238|fake]"
            );
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);

            expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1121880-all-fake-expertise-complete",
                    OrderSource.BOUTIQUE
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(null);
            expectAllPositionsTexts(
                    expertiseStage,
                    "st.AUTHENTICITY.FAILED.NOT_ORIGINAL.boutiquePosition.Seller[1497242 brand1497242|fake]",
                    "st.AUTHENTICITY.FAILED.NOT_ORIGINAL.boutiquePosition.Seller[1497240 brand1497240|fake]",
                    "st.AUTHENTICITY.FAILED.NOT_ORIGINAL.boutiquePosition.Seller[1497238 brand1497238|fake]"
            );
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);
        }

        @Test
        public void allDefectsNegative_1121992() {
            OrderStageDTO expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1121992-all-defects-negative"
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo("st.DEFECT_MATCHING.FAILED.description.Buyer.plural");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);

            expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1121992-all-defects-negative"
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo("st.DEFECT_MATCHING.FAILED.description.Seller.plural");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);

            expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1121992-all-defects-negative",
                    OrderSource.BOUTIQUE
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo("st.DEFECT_MATCHING.FAILED.boutiqueDescription.Seller.plural");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);
        }

        @Test
        public void partFakeExpertiseComplete() {
            OrderStageDTO expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, "part-fake-expertise-complete"
            );
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);

            expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, "part-fake-expertise-complete"
            );
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);
        }

        @Test
        public void cannotDetermine_1121983() {
            OrderStageDTO expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1121983-cannot-determine"
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo("st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.description.Seller.singular[1504085 brand1504085]");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);

            expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1121983-cannot-determine", OrderSource.BOUTIQUE
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo("st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.boutiqueDescription.Seller.singular[1504085 brand1504085]");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);


            expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1121983-cannot-determine"
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo("st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.description.Buyer.singular[1504085 brand1504085]");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);
        }

        @Test
        public void notOriginal_1121983() {
            OrderStageDTO expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1121983-not-original"
            );
            Assertions.assertThat(expertiseStage.getDescription()).isNull();
            expectAllPositionsTexts(
                    expertiseStage,
                    "st.AUTHENTICITY.FAILED.NOT_ORIGINAL.position.Seller[1504085 brand1504085|fake]"
            );
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);

            expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1121983-not-original"
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo("st.AUTHENTICITY.FAILED.NOT_ORIGINAL.description.Buyer.singular[1504085 brand1504085]");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);

        }

        @Test
        public void packing_1122161() {
            OrderStageDTO expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1122161-packing"
            );
            Assertions.assertThat(expertiseStage.getDescription()).isNull();
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);
            expectAllPositionsTexts(
                    expertiseStage,
                    "st.AUTHENTICITY.SUCCESS.position.Seller.singular[1525646 brand1525646]",
                    "st.DEFECT_MATCHING.SUCCESS.zeroDiscount.position.Seller.plural[1525650 brand1525650, 1525648 brand1525648]"
            );
            expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1122161-packing",
                    OrderSource.BOUTIQUE
            );
            Assertions.assertThat(expertiseStage.getDescription()).isNull();
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);
            expectAllPositionsTexts(
                    expertiseStage,
                    "st.AUTHENTICITY.SUCCESS.boutiquePosition.Seller.singular[1525646 brand1525646]",
                    "st.DEFECT_MATCHING.SUCCESS.zeroDiscount.boutiquePosition.Seller.plural[1525650 brand1525650, 1525648 brand1525648]"
            );

            expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1122161-packing", OrderSource.BOUTIQUE
            );
            Assertions.assertThat(expertiseStage.getDescription()).isNull();
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);
            expectAllPositionsTexts(
                    expertiseStage,
                    "st.AUTHENTICITY.SUCCESS.boutiquePosition.Seller.singular[1525646 brand1525646]",
                    "st.DEFECT_MATCHING.SUCCESS.zeroDiscount.boutiquePosition.Seller.plural[1525650 brand1525650, 1525648 brand1525648]"
            );

            expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1122161-packing"
            );
            Assertions.assertThat(expertiseStage.getDescription()).isNull();
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);
            expectAllPositionsTexts(
                    expertiseStage,
                    "st.AUTHENTICITY.SUCCESS.position.Buyer.singular[1525646 brand1525646]",
                    "st.DEFECT_MATCHING.SUCCESS.zeroDiscount.position.Buyer.plural[1525650 brand1525650, 1525648 brand1525648]"
            );
        }

        @Test
        public void packing_1122162() {
            OrderStageDTO expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1122162-packing"
            );
            Assertions.assertThat(expertiseStage.getDescription())
                    .isEqualTo("st.CHANGES_MATCHING.SUCCESS.zeroDiscount.description.Seller.singular[1525696 brand1525696]");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);

            expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, "1122162-packing"
            );
            Assertions.assertThat(expertiseStage.getDescription())
                    .isEqualTo("st.CHANGES_MATCHING.SUCCESS.zeroDiscount.description.Buyer.singular[1525696 brand1525696]");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);
        }

        @Test
        public void authenticityInProgressFake() {
            String opSnippet = "1122189-authenticity-in-progress-fake";
            OrderStageDTO expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.AUTHENTICITY.IN_PROGRESS.description.Seller.singular");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isNull();

            expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.AUTHENTICITY.IN_PROGRESS.description.Buyer.singular");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isNull();
        }

        @Test
        public void authenticityInProgressCannotDetermine() {
            String opSnippet = "1122189-authenticity-in-progress-cannot-determine";
            OrderStageDTO expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.AUTHENTICITY.IN_PROGRESS.description.Seller.singular");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isNull();

            expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.AUTHENTICITY.IN_PROGRESS.description.Buyer.singular");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isNull();
        }

        @Test
        public void authenticityCompletedFake() {
            String opSnippet = "1122189-authenticity-completed-fake";
            OrderStageDTO expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            expectAllPositionsTexts(
                    expertiseStage,
                    "st.AUTHENTICITY.FAILED.NOT_ORIGINAL.position.Seller[1529605 brand1529605|лоиправпрпрол]"
            );
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);

            expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.AUTHENTICITY.FAILED.NOT_ORIGINAL.description.Buyer.singular[1529605 brand1529605]");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);
        }

        @Test
        public void authenticityCompletedCannotDetermine() {
            String opSnippet = "1122189-authenticity-completed-cannot-determine";
            OrderStageDTO expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.description.Seller.singular[1529605 brand1529605]");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);

            expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet, OrderSource.BOUTIQUE
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.boutiqueDescription.Seller.singular[1529605 brand1529605]");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);

            expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.description.Buyer.singular[1529605 brand1529605]");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);
        }

        @Test
        public void preSellingPreparationFailed() {
            String opSnippet = "1513020";
            OrderStageDTO expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.FINISHED.FAILED.description.Seller.singular");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);

            expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.FINISHED.FAILED.description.Buyer.singular");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);
        }

        @Test
        public void returnedToSeller() {
            String opSnippet = "1368438";
            OrderStageDTO expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.FINISHED.FAILED.description.Seller.singular");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);

            expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.FINISHED.FAILED.description.Buyer.singular");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);
        }

        @Test
        public void boutiquePreparationForPublication() {
            String opSnippet = "1121435-photo-studio-plural";
            OrderStageDTO expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.PREPARATION_FOR_PUBLICATION.IN_PROGRESS.description.Seller.plural");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isNull();

            opSnippet = "1121435-photo-studio-singular";
            expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.PREPARATION_FOR_PUBLICATION.IN_PROGRESS.description.Seller.singular");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isNull();

            opSnippet = "1121435-delivery-to-stock.json";
            expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.WAIT_DELIVERY_TO_STOCK.IN_PROGRESS.description.Seller.plural");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isNull();
        }

        @Test
        public void reconciliationWithBuyerNotStarted() {
            String opSnippet = "1120993-reconciliation-with-buyer-not-started";

            OrderStageDTO expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.DEFECT_MATCHING.IN_PROGRESS.description.Seller.singular");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);

            expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.AUTHENTICITY.IN_PROGRESS.description.Buyer.singular");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isNull();
        }

        @Test
        public void reconciliationWithBuyerStarted() {
            String opSnippet = "1120993-reconciliation-with-buyer-started";

            OrderStageDTO expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.DEFECT_MATCHING.IN_PROGRESS.description.Seller.singular");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);

            expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.DEFECT_MATCHING.IN_PROGRESS.description.Buyer.singular");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);
        }

        @Test
        public void reconciliationWithBuyerNotStartedDifferent() {
            String opSnippet = "1120993-reconciliation-with-different-buyer-started";

            OrderStageDTO expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.DEFECT_MATCHING.IN_PROGRESS.description.Seller.plural");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);

            expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            expectAllPositionsTexts(
                    expertiseStage,
                    "st.AUTHENTICITY.SUCCESS.position.Buyer.singular[1534146 brand1534146]",
                    "st.DEFECT_MATCHING.IN_PROGRESS.position.Buyer.singular[1534145 brand1534145]"
            );
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);
        }

        @Test
        public void waitPresellingPreparationWithoutBuyerReconciliation() {
            String opSnippet = "1121044-wait-preselling-preparation-without-buyer-reconciliation";

            OrderStageDTO expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.DEFECT_MATCHING.SUCCESS.zeroDiscount.description.Seller.singular[1535556 brand1535556]");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);

            expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.PRESELLING_PREPARATION.IN_PROGRESS.description.Buyer.singular");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isNull();
        }

        @Test
        public void waitPresellingPreparationWithAndWithoutBuyerReconciliation() {
            String opSnippet = "1121044-wait-preselling-preparation-with-and-without-buyer-reconciliation";

            OrderStageDTO expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.DEFECT_MATCHING.SUCCESS.zeroDiscount.description.Seller.plural[1535556 brand1535556, 1535557 brand1535557]");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);

            expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            expectAllPositionsTexts(
                    expertiseStage,
                    "st.PRESELLING_PREPARATION.IN_PROGRESS.position.Buyer.singular[1535556 brand1535556]",
                    "st.DEFECT_MATCHING.SUCCESS.zeroDiscount.position.Buyer.singular[1535557 brand1535557]"
            );
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.IN_PROGRESS);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);
        }

        @Test
        public void secondaryReconciliationFailed() {
            String opSnippet = "3345773-secondary-reconciliation-failed";

            OrderStageDTO expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.DEFECT_MATCHING.FAILED.description.Seller.singular");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);

            expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.DEFECT_MATCHING.FAILED.description.Buyer.singular");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);
        }

        @Test
        public void commentChangeReconciliationFailed() {
            String opSnippet = "3362081-comment-change-reconciliation-failed";

            OrderStageDTO expertiseStage = expectExpertiseStage(
                    SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.CHANGES_MATCHING.FAILED.description.Seller.singular");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);

            expertiseStage = expectExpertiseStage(
                    BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
            );
            Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(
                    "st.CHANGES_MATCHING.FAILED.description.Buyer.singular");
            Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
            Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);
        }
    }
}
