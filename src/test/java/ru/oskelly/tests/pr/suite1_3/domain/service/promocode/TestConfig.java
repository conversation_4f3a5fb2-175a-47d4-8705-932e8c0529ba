package ru.oskelly.tests.pr.suite1_3.domain.service.promocode;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import su.reddot.domain.service.promocode.PromoCodeIdGenerator;

import java.util.Arrays;
import java.util.List;

import static ru.oskelly.tests.pr.suite1_3.domain.service.promocode.PromocodeTestUtil.TEST_GENERATED_VALUE_AA003;
import static ru.oskelly.tests.pr.suite1_3.domain.service.promocode.PromocodeTestUtil.TEST_GENERATED_VALUE_AA004;
import static ru.oskelly.tests.pr.suite1_3.domain.service.promocode.PromocodeTestUtil.TEST_GENERATED_VALUE_AA005;
import static ru.oskelly.tests.pr.suite1_3.domain.service.promocode.PromocodeTestUtil.TEST_GENERATED_VALUE_AA1;
import static ru.oskelly.tests.pr.suite1_3.domain.service.promocode.PromocodeTestUtil.TEST_GENERATED_VALUE_AA2;

@TestConfiguration
@Import(PromocodeTestUtil.class)
public class TestConfig {
    @Bean
    @Primary
    public PromoCodeIdGenerator testIdGeneratorService() {
        return new PromoCodeIdGenerator() {

            private final List<String> seq = Arrays.asList(
                TEST_GENERATED_VALUE_AA1,
                TEST_GENERATED_VALUE_AA2,
                TEST_GENERATED_VALUE_AA003,
                TEST_GENERATED_VALUE_AA004,
                TEST_GENERATED_VALUE_AA004,
                TEST_GENERATED_VALUE_AA005
            );

            private int pointer = 0;

            @Override
            public String nextVal() {
                String next = seq.get(pointer);
                pointer = (pointer == seq.size() - 1) ? 0 : pointer + 1;
                return next;
            }
        };
    }
}
