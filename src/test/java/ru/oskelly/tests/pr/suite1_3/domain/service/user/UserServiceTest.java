package ru.oskelly.tests.pr.suite1_3.domain.service.user;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.exception.UserDeletedException;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.adminpanel.user.AdminUserService;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.domain.service.user.UserService;
import su.reddot.domain.service.user.UserServiceImpl;
import su.reddot.infrastructure.configuration.OskellyApplication;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;

import static org.assertj.core.api.Assertions.assertThatThrownBy;

@SpringBootTest(classes = OskellyApplication.class)
@ActiveProfiles(AbstractSpringTest.testProfiles)
@ExtendWith(SpringExtension.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class UserServiceTest {

    private static final Long USER_ID = -1L;
    private static final String NICKNAME = "user-service-test-user";
    private static final String NICKNAME_2 = "user2-service-test-user";

    @Autowired
    UserServiceImpl userService;
    @Autowired
    UserRepository userRepository;
    @Autowired
    CommissionGridService commissionGridService;
    @Autowired
    AdminUserService adminUserService;

    @Test
    public void getUserDtoThrowsUserDeletedExceptionWhenUserDeleted() {
        User deletedUser = new User()
                .setId(USER_ID)
                .setNickname(NICKNAME)
                .setDeleteTime(ZonedDateTime.now())
                .setUserType(User.UserType.SIMPLE_USER)
                .setChangeTime(LocalDateTime.now())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        User finalDeletedUser = userRepository.save(deletedUser);

        assertThatThrownBy(() -> userService.getUserDTO(finalDeletedUser.getId(), new UserService.UserRequest()))
                .isInstanceOf(UserDeletedException.class);


        userRepository.delete(deletedUser);
    }

    @Test
    @WithMockUser(authorities = {"USER_DELETE"})
    public void adminDeleteRestoreUser() {
        User user = new User()
                .setNickname(NICKNAME_2)
                .setUserType(User.UserType.SIMPLE_USER)
                .setChangeTime(LocalDateTime.now())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        userRepository.save(user);
        adminUserService.deleteUser(user.getId());
        User newUser = userService.getUserById(user.getId()).get();
        Assertions.assertNotEquals(null, newUser.getDeleteTime());
        adminUserService.restoreUser(user.getId());
        newUser = userService.getUserById(user.getId()).get();
        Assertions.assertEquals(null, newUser.getDeleteTime());
        userRepository.delete(user);

    }
}
