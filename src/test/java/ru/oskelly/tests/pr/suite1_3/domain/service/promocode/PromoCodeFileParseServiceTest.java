package ru.oskelly.tests.pr.suite1_3.domain.service.promocode;

import org.apache.http.entity.ContentType;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.io.ClassPathResource;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.util.StreamUtils;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.model.Brand;
import su.reddot.domain.model.category.Category;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.brand.DefaultBrandService;
import su.reddot.domain.service.catalog.impl.NestedSetsCategoryService;
import su.reddot.domain.service.dto.BrandDTO;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.domain.service.dto.UserDTO;
import su.reddot.domain.service.dto.promocode.PromocodeReferenceLinkDTO;
import su.reddot.domain.service.product.DefaultProductService;
import su.reddot.domain.service.product.ProductService;
import su.reddot.domain.service.promocode.PromoCodeFileParseService;
import su.reddot.domain.service.promocode.exception.FileParseException;
import su.reddot.domain.service.promocode.model.ParseItemType;
import su.reddot.domain.service.user.UserServiceImpl;
import su.reddot.infrastructure.configuration.OskellyApplication;

import java.io.IOException;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = {OskellyApplication.class})
@ExtendWith(SpringExtension.class)
@ActiveProfiles(AbstractSpringTest.testProfiles)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
class PromoCodeFileParseServiceTest {
    @Autowired
    PromoCodeFileParseService service;
    @MockBean
    UserServiceImpl userService;
    @MockBean
    DefaultBrandService brandService;
    @MockBean
    NestedSetsCategoryService categoryService;
    @MockBean
    DefaultProductService productService;

    @Test
    @DisplayName("Выбрасывается ошибка при обработке файла с неподдерживаемым расширением")
    void throwsOnUnsupportedFile() {
        // Arrange
        final MockMultipartFile mockMultipartFile = new MockMultipartFile(
            "file",
            "filename.txt",
            ContentType.MULTIPART_FORM_DATA.toString(),
            (byte[]) null);

        // Act & Assert
        assertThatThrownBy(() -> service.convertFileData(mockMultipartFile, ParseItemType.BRAND))
            .isInstanceOf(RuntimeException.class);
    }

    /**
     * {@link PromoCodeFileParseServiceTest#parseEveryItemType)}
     */
    private static Stream<Arguments> itemTypeProvider() {
        return Stream.of(
            Arguments.of(ParseItemType.BUYER),
            Arguments.of(ParseItemType.SELLER),
            Arguments.of(ParseItemType.BRAND),
            Arguments.of(ParseItemType.CATEGORY),
            Arguments.of(ParseItemType.PRODUCT)
        );
    }

    @ParameterizedTest
    @MethodSource("itemTypeProvider")
    @DisplayName("Корректная обработка всех вариаций ParseItemType")
    void parseEveryItemType(ParseItemType itemType) throws IOException {
        // Arrange
        final MockMultipartFile mockMultipartFile = getMultipartFile("test1.csv");

        lenient().when(brandService.findById(anyLong()))
            .thenAnswer(invocationOnMock -> Optional.of(new Brand().setId(invocationOnMock.getArgument(0)).setName("itemName")));
        lenient().when(userService.getUserById(anyLong()))
            .thenAnswer(invocationOnMock -> Optional.of(new User().setId(invocationOnMock.getArgument(0)).setNickname("itemName")));
        lenient().when(categoryService.getCategory(anyLong()))
            .thenAnswer(invocationOnMock -> new Category().setId(invocationOnMock.getArgument(0)).setDisplayName("itemName"));
        lenient().when(productService.getRawProduct(anyLong(), eq(ProductService.UserType.HUMAN)))
            .thenAnswer(invocationOnMock -> Optional.of(new Product().setName("itemName")));

        // Act
        final Set<PromocodeReferenceLinkDTO> parseResult = service.convertFileData(mockMultipartFile, itemType);

        // Assert
        assertThat(parseResult)
            .hasSize(3)
            .extracting(PromocodeReferenceLinkDTO::getId)
            .containsExactlyInAnyOrder(1L, 2L, 3L);

        if (itemType == ParseItemType.BRAND) {
            verify(brandService, times(3)).findById(anyLong());
        } else if (itemType == ParseItemType.CATEGORY) {
            verify(categoryService, times(3)).getCategory(anyLong());
        } else if (itemType == ParseItemType.PRODUCT) {
            verify(productService, times(3)).getRawProduct(anyLong(), eq(ProductService.UserType.HUMAN));
        } else if (itemType == ParseItemType.SELLER
            || itemType == ParseItemType.BUYER) {

            verify(userService, times(3)).getUserById(anyLong());
        }
    }

    /**
     * {@link PromoCodeFileParseServiceTest#correctFileParse)}
     */
    private static Stream<String> correctFileNameProvider() {
        return Stream.of(
            "test1.csv",
            "test1.xls",
            "test1.xlsx"
        );
    }

    @ParameterizedTest
    @MethodSource("correctFileNameProvider")
    @DisplayName("Проверка корректного парсинга файлов поддерживаемых расширений")
    void correctFileParse(String fileName) throws IOException {
        // Arrange
        final MockMultipartFile mockMultipartFile = getMultipartFile(fileName);
        when(brandService.findById(anyLong()))
            .thenAnswer(invocationOnMock -> Optional.of(new Brand().setId(invocationOnMock.getArgument(0)).setName("itemName")));

        // Act
        final Set<PromocodeReferenceLinkDTO> parseResult = service.convertFileData(mockMultipartFile, ParseItemType.BRAND);

        // Assert
        assertThat(parseResult)
            .hasSize(3)
            .extracting(PromocodeReferenceLinkDTO::getId)
            .containsExactlyInAnyOrder(1L, 2L, 3L);
    }

    /**
     * {@link PromoCodeFileParseServiceTest#corruptedFileParse)}
     */
    private static Stream<String> corruptedFileNameProvider() {
        return Stream.of(
            "wrongFile.csv",
            "wrongFile2.csv",
            "wrongFile.xls",
            "wrongFile2.xls",
            "wrongFile.xlsx",
            "wrongFile2.xlsx"
        );
    }

    @ParameterizedTest
    @MethodSource("corruptedFileNameProvider")
    @DisplayName("При парсинга файлов с некорректным содержимым выбрасывается исключение")
    void corruptedFileParse(String fileName) throws IOException {
        // Arrange
        final MockMultipartFile mockMultipartFile = getMultipartFile(fileName);
        when(brandService.findById(anyLong()))
            .thenAnswer(invocationOnMock -> Optional.of(new Brand().setId(invocationOnMock.getArgument(0)).setName("itemName")));

        // Act & Assert
        assertThatThrownBy(() -> service.convertFileData(mockMultipartFile, ParseItemType.BRAND))
            .isInstanceOf(FileParseException.class);
    }

    private MockMultipartFile getMultipartFile(String originalFilename) throws IOException {
        final ClassPathResource resource = new ClassPathResource("promocode/files/" + originalFilename);
        final byte[] bytes = StreamUtils.copyToByteArray(resource.getInputStream());

        return new MockMultipartFile(
            "name",
            originalFilename,
            ContentType.MULTIPART_FORM_DATA.toString(),
            bytes);
    }
}
