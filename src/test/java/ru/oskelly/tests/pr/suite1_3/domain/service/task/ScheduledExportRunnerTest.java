package ru.oskelly.tests.pr.suite1_3.domain.service.task;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.service.master.FtpUploader;
import su.reddot.domain.service.task.ScheduledExportRunner;
import su.reddot.infrastructure.util.FileUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertTrue;

@Disabled
@Profile({"test"})
@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class ScheduledExportRunnerTest extends AbstractSpringTest {
    @Autowired
    private ScheduledExportRunner scheduledExportRunner;

	@Value("${app.master.ftp-host}")
	private String ftpHost;

	@Value("${app.master.ftp-1c-user}")
	private String ftp1CUser;
	@Value("${app.master.ftp-1c-pass}")
	private String ftp1CPass;
	@Value("${app.master.ftp-1c-remote-path}")
	private String ftp1CRemotePath;

	@Value("${app.master.ftp-partner-user}")
	private String ftpPartnerUser;
	@Value("${app.master.ftp-partner-pass}")
	private String ftpPartnerPass;
	@Value("${app.master.ftp-partner-remote-path}")
	private String ftpPartnerRemotePath;

	@Value("${app.master.ftp-marketing-user}")
	private String ftpMarketingUser;
	@Value("${app.master.ftp-marketing-pass}")
	private String ftpMarketingPass;
	@Value("${app.master.ftp-marketing-remote-path}")
	private String ftpMarketingRemotePath;

	@Value("${app.master.ftp-accounting-user}")
	private String ftpAccountingUser;
	@Value("${app.master.ftp-accounting-pass}")
	private String ftpAccountingPass;
	@Value("${app.master.ftp-accounting-remote-path}")
	private String ftpAccountingRemotePath;

	@Value("${app.feedExport.oneass.defaultPathToExportFile}")
	private String defaultOneassExportPath;
	@Value("${app.feedExport.oneass.sales-filename}")
	private String oneassSalesFileName;
	@Value("${app.feedExport.oneass.products-filename}")
	private String oneassProductsFileName;
	@Value("${app.feedExport.oneass.customers-filename}")
	private String oneassCustomersFileName;
	@Value("${app.feedExport.oneass.bank-accounts-filename}")
	private String oneassBankAccountsFileName;
	@Value("${app.feedExport.oneass.twelve-storeez-filename}")
	private String oneass12StoreezFileName;
	@Value("${app.feedExport.yml.defaultPathToExportFile}")
	private String partnerYmlPath;
	@Value("${app.feedExport.yml.filename}")
	private String partnerYmlFileName;

	@Value("${app.feedExport.yml-with-model.defaultPathToExportFile}")
	private String partnerYmlWithModelPath;
	@Value("${app.feedExport.yml-with-model.filename}")
	private String partnerYmlWithModelFileName;

	@Value("${app.feedExport.gmc.defaultPathToExportFile}")
	private String partnerGmcPath;
	@Value("${app.feedExport.gmc.filename}")
	private String partnerGmcFileName;

	@Value("${app.feedExport.facebook.defaultPathToExportFile}")
	private String partnerFacebookPath;
	@Value("${app.feedExport.facebook.filename}")
	private String partnerFacebookFileName;

	@Value("${app.feedExport.catalog-report.defaultPathToExportFile}")
	private String catalogReportLocalExportPath;
	@Value("${app.feedExport.catalog-report.filename}")
	private String catalogReportLocalFileName;

	@Value("${app.feedExport.cohort.defaultPathToExportFile}")
	private String cohortLocalExportPath;

	@Value("${app.feedExport.payments.defaultPathToExportFile}")
	private String paymentsLocalExportPath;
	@Value("${app.feedExport.payments.filename}")
	private String paymentsLocalFileName;

	@Value("${app.feedExport.payments_in.defaultPathToExportFile}")
	private String paymentsInLocalExportPath;
	@Value("${app.feedExport.payments_in.filename}")
	private String paymentsInLocalFileName;

	@Value("${app.feedExport.orders.defaultPathToExportFile}")
	private String ordersLocalExportPath;
	@Value("${app.feedExport.orders.filename}")
	private String ordersLocalFileName;

	@Value("${app.feedExport.payedOrders.defaultPathToExportFile}")
	private String payedOrdersLocalExportPath;
	@Value("${app.feedExport.payedOrders.filename}")
	private String payedOrdersLocalFileName;

	@Value("${app.feedExport.orderProducts.defaultPathToExportFile}")
	private String orderProductsLocalExportPath;
	@Value("${app.feedExport.orderProducts.filename}")
	private String orderProductsLocalFileName;

	@Value("${app.feedExport.publishedProducts.defaultPathToExportFile}")
	private String publishedProductsLocalExportPath;
	@Value("${app.feedExport.publishedProducts.filename}")
	private String publishedProductsLocalFileName;

	@Value("${app.feedExport.products.defaultPathToExportFile}")
	private String productsLocalExportPath;
	@Value("${app.feedExport.products.filename}")
	private String productsLocalFileName;

	@Value("${app.feedExport.moderationProducts.defaultPathToExportFile}")
	private String moderationProductsLocalExportPath;
	@Value("${app.feedExport.moderationProducts.filename}")
	private String moderationProductsLocalFileName;

	@Value("${app.feedExport.users.defaultPathToExportFile}")
	private String usersLocalExportPath;
	@Value("${app.feedExport.users.filename}")
	private String usersLocalFileName;

	@Value("${app.feedExport.notificationDeliveryReport.defaultPathToExportFile}")
	private String notificationDeliveryReportLocalExportPath;
	@Value("${app.feedExport.notificationDeliveryReport.filename}")
	private String notificationDeliveryReportLocalFileName;

    @BeforeEach
    public void init() {
		cleanLocalAndFtpFolders();
    }



    @Test
	@Transactional
	@Rollback(value = false)
    public void _01_ExportAndUploadToFTPOneAssFiles() {
        scheduledExportRunner.oneAssExportTask();
		File oneAssFolder = new File(defaultOneassExportPath);
		assertTrue(oneAssFolder.isDirectory());
		assertTrue(checkAllOneassFilesInFolder(oneAssFolder));
		assertTrue(checkAllOneassFilesInFtpFolder());
    }

    @Test
	@Transactional
	@Rollback(value = false)
    public void _03_ExportAndUploadToFTPYmlWithModelFile() {
        scheduledExportRunner.ymlWithModelExportTask();
		File partnerYmlWithModelFolder = new File(partnerYmlWithModelPath);
		assertTrue(partnerYmlWithModelFolder.isDirectory());
		assertTrue(getNameFilesOfFolder(partnerYmlWithModelFolder).contains(partnerYmlWithModelFileName));

		FtpUploader ftpPartnerUploader = new FtpUploader(ftpHost, ftpPartnerUser, ftpPartnerPass);
		assertTrue(ftpPartnerUploader.getFilenamesOfFolder(ftpPartnerRemotePath).contains(partnerYmlWithModelFileName));
		ftpPartnerUploader.disconnect();
    }

    @Test
	@Transactional
	@Rollback(value = false)
    public void _04_ExportAndUploadToFTPGmsFile() {
        scheduledExportRunner.gmcExportTask();
		File partnerGmcFolder = new File(partnerGmcPath);
		assertTrue(partnerGmcFolder.isDirectory());
		assertTrue(getNameFilesOfFolder(partnerGmcFolder).contains(partnerGmcFileName));

		FtpUploader ftpPartnerUploader = new FtpUploader(ftpHost, ftpPartnerUser, ftpPartnerPass);
		assertTrue(ftpPartnerUploader.getFilenamesOfFolder(ftpPartnerRemotePath).contains(partnerGmcFileName));
		ftpPartnerUploader.disconnect();
    }

    @Test
    public void _05_ExportAndUploadToFTPFacebookFile() {
        scheduledExportRunner.facebookExportTask();
		File partnerFolder = new File(partnerFacebookPath);
		assertTrue(partnerFolder.isDirectory());
		assertTrue(getNameFilesOfFolder(partnerFolder).contains(partnerFacebookFileName));

		FtpUploader ftpPartnerUploader = new FtpUploader(ftpHost, ftpPartnerUser, ftpPartnerPass);
		assertTrue(ftpPartnerUploader.getFilenamesOfFolder(ftpPartnerRemotePath).contains(partnerFacebookFileName));
		ftpPartnerUploader.disconnect();
    }

    //Очень медленно работает. Пусть поак будет выключено.
	/*@Test
	public void _06_ExportAndUploadToFTPCohortFile() {
		Map<String,String> paths = scheduledExportRunner.cohortExportTask();
		assertEquals(16, paths.size());
		for (Map.Entry<String, String> path : paths.entrySet()) {
			File folder = new File(path.getKey());
			assertTrue(folder.isDirectory());
			assertTrue(getNameFilesOfFolder(folder).contains(path.getValue()));

			FtpUploader uploader = new FtpUploader(ftpHost, ftpMarketingUser, ftpMarketingPass);
			String remotePath = ftpMarketingRemotePath + "cohort/" + path.getKey() . replace(cohortLocalExportPath, "");
			assertTrue(uploader.getFilenamesOfFolder(remotePath).contains(path.getValue()));
			uploader.disconnect();
		}
	}*/

	@Test
	public void _07_ExportAndUploadToFTPCatalogReportFile() {
		scheduledExportRunner.catalogExportTask();

		File folder = new File(catalogReportLocalExportPath);
		assertTrue(folder.isDirectory());
		assertTrue(getNameFilesOfFolder(folder).contains(catalogReportLocalFileName));

		FtpUploader uploader = new FtpUploader(ftpHost, ftpMarketingUser, ftpMarketingPass);
		assertTrue(uploader.getFilenamesOfFolder(ftpMarketingRemotePath).contains(catalogReportLocalFileName));
		uploader.disconnect();
	}

	@Test
	public void _08_01_ExportAndUploadToFTPPaymentsFile() {
		scheduledExportRunner.paymentsExportTask();

		File folder = new File(paymentsLocalExportPath);
		assertTrue(folder.isDirectory());
		assertTrue(getNameFilesOfFolder(folder).contains(paymentsLocalFileName));

		FtpUploader uploader = new FtpUploader(ftpHost, ftpAccountingUser, ftpAccountingPass);
		assertTrue(uploader.getFilenamesOfFolder(ftpAccountingRemotePath).contains(paymentsLocalFileName));
		uploader.disconnect();
	}

	@Test
	public void _08_02_ExportAndUploadToFTPPaymentsInFile() {
		scheduledExportRunner.paymentsInExportTask();

		File folder = new File(paymentsInLocalExportPath);
		assertTrue(folder.isDirectory());
		assertTrue(getNameFilesOfFolder(folder).contains(paymentsInLocalFileName));

		FtpUploader uploader = new FtpUploader(ftpHost, ftpAccountingUser, ftpAccountingPass);
		assertTrue(uploader.getFilenamesOfFolder(ftpAccountingRemotePath).contains(paymentsInLocalFileName));
		uploader.disconnect();
	}

	@Test
	public void _09_01_ExportAndUploadToFTPOrdersFile() {
		scheduledExportRunner.ordersExportTask();

		File folder = new File(ordersLocalExportPath);
		assertTrue(folder.isDirectory());
		assertTrue(getNameFilesOfFolder(folder).contains(ordersLocalFileName));

		FtpUploader uploader = new FtpUploader(ftpHost, ftpAccountingUser, ftpAccountingPass);
		assertTrue(uploader.getFilenamesOfFolder(ftpAccountingRemotePath).contains(ordersLocalFileName));
		uploader.disconnect();
	}

	@Test
	public void _09_02_ExportAndUploadToFTPPayedOrdersFile() {
		scheduledExportRunner.payedOrdersExportTask();

		File folder = new File(payedOrdersLocalExportPath);
		assertTrue(folder.isDirectory());
		assertTrue(getNameFilesOfFolder(folder).contains(payedOrdersLocalFileName));

		FtpUploader uploader = new FtpUploader(ftpHost, ftpAccountingUser, ftpAccountingPass);
		assertTrue(uploader.getFilenamesOfFolder(ftpAccountingRemotePath).contains(payedOrdersLocalFileName));
		uploader.disconnect();
	}

	@Test
	public void _09_02_ExportAndUploadToFTPOrderProductsFile() {
		scheduledExportRunner.orderProductsExportTask();

		File folder = new File(orderProductsLocalExportPath);
		assertTrue(folder.isDirectory());
		assertTrue(getNameFilesOfFolder(folder).contains(orderProductsLocalFileName));

		FtpUploader uploader = new FtpUploader(ftpHost, ftpAccountingUser, ftpAccountingPass);
		assertTrue(uploader.getFilenamesOfFolder(ftpAccountingRemotePath).contains(orderProductsLocalFileName));
		uploader.disconnect();
	}

	@Test
	public void _10_ExportAndUploadToFTPPublishedProductsFile() {
		scheduledExportRunner.publishedProductsExportTask();

		File folder = new File(publishedProductsLocalExportPath);
		assertTrue(folder.isDirectory());
		assertTrue(getNameFilesOfFolder(folder).contains(publishedProductsLocalFileName));

		FtpUploader uploader = new FtpUploader(ftpHost, ftpAccountingUser, ftpAccountingPass);
		assertTrue(uploader.getFilenamesOfFolder(ftpAccountingRemotePath).contains(publishedProductsLocalFileName));
		uploader.disconnect();
	}

	@Test
	public void _11_ExportAndUploadToFTPProductsFile() {
		scheduledExportRunner.productsExportTask();

		File folder = new File(productsLocalExportPath);
		assertTrue(folder.isDirectory());
		assertTrue(getNameFilesOfFolder(folder).contains(productsLocalFileName));

		FtpUploader uploader = new FtpUploader(ftpHost, ftpAccountingUser, ftpAccountingPass);
		assertTrue(uploader.getFilenamesOfFolder(ftpAccountingRemotePath).contains(productsLocalFileName));
		uploader.disconnect();
	}

	@Test
	public void _12_ExportAndUploadToFTPModerationProductsFile() {
		scheduledExportRunner.moderationProductsExportTask();

		File folder = new File(moderationProductsLocalExportPath);
		assertTrue(folder.isDirectory());
		assertTrue(getNameFilesOfFolder(folder).contains(moderationProductsLocalFileName));

		FtpUploader uploader = new FtpUploader(ftpHost, ftpAccountingUser, ftpAccountingPass);
		assertTrue(uploader.getFilenamesOfFolder(ftpAccountingRemotePath).contains(moderationProductsLocalFileName));
		uploader.disconnect();
	}

	@Test
	public void _13_ExportAndUploadToFTPUsersFile() {
		scheduledExportRunner.usersExportTask();

		File folder = new File(usersLocalExportPath);
		assertTrue(folder.isDirectory());
		assertTrue(getNameFilesOfFolder(folder).contains(usersLocalFileName));

		FtpUploader uploader = new FtpUploader(ftpHost, ftpAccountingUser, ftpAccountingPass);
		assertTrue(uploader.getFilenamesOfFolder(ftpAccountingRemotePath).contains(usersLocalFileName));
		uploader.disconnect();
	}

	@Test
	public void _14_ExportAndUploadToFTPNotificationDeliveryReportFile() {
		scheduledExportRunner.notificationDeliveryReportExportTask();

		File folder = new File(notificationDeliveryReportLocalExportPath);
		assertTrue(folder.isDirectory());
		assertTrue(getNameFilesOfFolder(folder).contains(notificationDeliveryReportLocalFileName));

		FtpUploader uploader = new FtpUploader(ftpHost, ftpAccountingUser, ftpAccountingPass);
		assertTrue(uploader.getFilenamesOfFolder(ftpAccountingRemotePath).contains(notificationDeliveryReportLocalFileName));
		uploader.disconnect();
	}

    @AfterEach
    public void fin() {
		cleanLocalAndFtpFolders();
    }

	private void cleanLocalAndFtpFolders() {
		FileUtils.cleanDirectory(defaultOneassExportPath);
		FileUtils.cleanDirectory(partnerFacebookPath);
		FileUtils.cleanDirectory(partnerGmcPath);
		FileUtils.cleanDirectory(partnerYmlPath);
		FileUtils.cleanDirectory(partnerYmlWithModelPath);
		FileUtils.cleanDirectory(cohortLocalExportPath);
		FileUtils.cleanDirectory(paymentsLocalExportPath);
		FileUtils.cleanDirectory(usersLocalExportPath);
		FileUtils.cleanDirectory(notificationDeliveryReportLocalExportPath);
		cleanFtpFolders();
	}

	public void cleanFtpFolders() {
		FtpUploader ftp1CUploader = new FtpUploader(ftpHost, ftp1CUser, ftp1CPass);
		ftp1CUploader.cleanFolder(ftp1CRemotePath);
		ftp1CUploader.disconnect();

		FtpUploader ftpPartnerUploader = new FtpUploader(ftpHost, ftpPartnerUser, ftpPartnerPass);
		ftpPartnerUploader.cleanFolder(ftpPartnerRemotePath);
		ftpPartnerUploader.disconnect();

		FtpUploader ftpMarketingUploader = new FtpUploader(ftpHost, ftpMarketingUser, ftpMarketingPass);
		ftpMarketingUploader.cleanFolder(ftpMarketingRemotePath);
		ftpMarketingUploader.disconnect();

		FtpUploader ftpAccountingUploader = new FtpUploader(ftpHost, ftpAccountingUser, ftpAccountingPass);
		ftpAccountingUploader.cleanFolder(ftpAccountingRemotePath);
		ftpAccountingUploader.disconnect();
	}

	public boolean checkAllOneassFilesInFolder(File oneAssFolder) {
		List<String> nameFilesOfFolder = getNameFilesOfFolder(oneAssFolder);
		return nameFilesOfFolder.contains(oneassBankAccountsFileName)
				&& nameFilesOfFolder.contains(oneassCustomersFileName)
				&& nameFilesOfFolder.contains(oneassProductsFileName)
				&& nameFilesOfFolder.contains(oneassSalesFileName)
				&& nameFilesOfFolder.contains(oneass12StoreezFileName);
	}

	private List<String> getNameFilesOfFolder(File folder) {
		List<String> filesOfFolder = new ArrayList<>();
		for (File item : folder.listFiles()) {
			filesOfFolder.add(item.getName());
		}
		return filesOfFolder;
	}

	public boolean checkAllOneassFilesInFtpFolder() {
		FtpUploader ftp1CUploader = new FtpUploader(ftpHost, ftp1CUser, ftp1CPass);
		List<String> nameFilesOfFtpFolder = ftp1CUploader.getFilenamesOfFolder(ftp1CRemotePath);
		ftp1CUploader.disconnect();
		return nameFilesOfFtpFolder.contains(oneassBankAccountsFileName)
				&& nameFilesOfFtpFolder.contains(oneassCustomersFileName)
				&& nameFilesOfFtpFolder.contains(oneassProductsFileName)
				&& nameFilesOfFtpFolder.contains(oneassSalesFileName);
	}

}
