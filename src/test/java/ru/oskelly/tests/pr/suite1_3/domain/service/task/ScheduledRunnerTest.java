package ru.oskelly.tests.pr.suite1_3.domain.service.task;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockserver.client.MockServerClient;
import org.mockserver.integration.ClientAndServer;
import org.mockserver.model.Header;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.product.ProductItemRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.order.OrderPositionState;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.BuyerCheckDTO;
import su.reddot.domain.service.task.ScheduledRunner;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.logistic.DeliveryState;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockserver.integration.ClientAndServer.startClientAndServer;
import static org.mockserver.model.HttpRequest.request;
import static org.mockserver.model.HttpResponse.response;
import static org.mockserver.model.HttpStatusCode.OK_200;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class ScheduledRunnerTest extends AbstractSpringTest {
    public static final String MOCK_SERVER_HOST = "localhost";
    public static final Integer MOCK_SERVER_PORT = 8182;

    private ClientAndServer mockServer;

    @Autowired
    private ScheduledRunner scheduledRunner;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private UserService userService;
    @Autowired
    private ProductRepository productRepository;
    @Autowired
    private ProductItemRepository productItemRepository;

    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user2-email}")
    private String sellerEmail;

    private Long realSellerId = 23L;

    private static Order order;
    private static List<Product> productsForOrders;

    private final String MOCK_RECEIPT_RESPONSE_FROM_CHECKONLINE = "{\"ClientId\":\"oskelly.ru\",\"Date\":{\"Date\":{\"Day\":28,\"Month\":2,\"Year\":20},\"Time\":{\"Hour\":11,\"Minute\":23,\"Second\":15}}," +
            "\"Device\":{\"Name\":\"00000003820046280614\",\"Address\":\"***********:4444\"},\"DeviceRegistrationNumber\":\"0001377633048987\",\"DeviceSerialNumber\":\"00000003820046280614\"," +
            "\"DocNumber\":1,\"DocumentType\":0,\"FNSerialNumber\":\"9252440*********\",\"FiscalDocNumber\":1803,\"FiscalSign\":3425414938,\"GrandTotal\":280000,\"Path\":\"/fr/api/v2/Complex\"," +
            "\"QR\":\"t=20200228T1123\\u0026s=2800.00\\u0026fn=9252440*********\\u0026i=1803\\u0026fp=3425414938\\u0026n=1\",\"RequestId\":\"1094264-3c379fc4-13c0-4a65-b02b-42942b8d8e29\"," +
            "\"Response\":{\"Error\":0},\"Responses\":[{\"Path\":\"/fr/api/v2/OpenTurn\",\"Response\":{\"Error\":0,\"FiscalDocNumber\":1802," +
            "\"FiscalDocument\":{\"TagID\":2,\"TagType\":\"stlv\",\"Value\":[{\"TagID\":1000,\"TagType\":\"string\",\"Value\":\"Отчет об открытии смены\"}," +
            "{\"TagID\":1188,\"TagType\":\"string\",\"Value\":\"001\"},{\"TagID\":1189,\"TagType\":\"byte\",\"Value\":2},{\"TagID\":1209,\"TagType\":\"byte\",\"Value\":2}," +
            "{\"TagID\":1048,\"TagType\":\"string\",\"Value\":\"ООО \\\"ОСКЕЛИ ГРУПП\\\"\"},{\"TagID\":1018,\"TagType\":\"string\",\"Value\":\"7714966339  \"}," +
            "{\"TagID\":1012,\"TagType\":\"unixtime\",\"Value\":\"2020-02-28T11:23:00Z\"},{\"TagID\":1037,\"TagType\":\"string\",\"Value\":\"0001377633048987    \"}," +
            "{\"TagID\":1021,\"TagType\":\"string\",\"Value\":\"СИСТ.АДМИНИСТРАТОР\"},{\"TagID\":1009,\"TagType\":\"string\",\"Value\":\"Россия, город Москва, улица Шарикоподшипниковская, дом 11, строение 9\"}," +
            "{\"TagID\":1187,\"TagType\":\"string\",\"Value\":\"http://oskelly.ru, https://oskelly.ru\"},{\"TagID\":1041,\"TagType\":\"string\",\"Value\":\"9252440*********\"}," +
            "{\"TagID\":1040,\"TagType\":\"uint32\",\"Value\":1802},{\"TagID\":1077,\"TagType\":\"byte[]\",\"Value\":\"IwR9/juX\"},{\"TagID\":1038,\"TagType\":\"uint32\",\"Value\":206}]},\"Password\":30,\"TurnNumber\":206}}," +
            "{\"Path\":\"/fr/api/v2/CloseDocument\",\"Response\":{\"Date\":{\"Date\":{\"Day\":28,\"Month\":2,\"Year\":20},\"Time\":{\"Hour\":11,\"Minute\":23,\"Second\":15}}," +
            "\"DocNumber\":1,\"DocumentType\":0,\"Error\":0,\"FiscalDocNumber\":1803,\"FiscalDocument\":{\"TagID\":3,\"TagType\":\"stlv\"," +
            "\"Value\":[{\"TagID\":1000,\"TagType\":\"string\",\"Value\":\"Кассовый чек\"},{\"TagID\":1054,\"TagType\":\"byte\",\"Value\":1}," +
            "{\"TagID\":1055,\"TagType\":\"byte\",\"Value\":4},{\"TagID\":1031,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1081,\"TagType\":\"money\",\"Value\":280000},{\"TagID\":9997,\"TagType\":\"money\",\"Value\":0}," +
            "{\"TagID\":9996,\"TagType\":\"money\",\"Value\":280000},{\"TagID\":1020,\"TagType\":\"money\",\"Value\":280000},{\"TagID\":1196,\"TagType\":\"string\",\"Value\":\"QR\"}," +
            "{\"TagID\":1215,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1216,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1217,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1060,\"TagType\":\"string\"," +
            "\"Value\":\"www.nalog.ru\"},{\"TagID\":1108,\"TagType\":\"byte\",\"Value\":1},{\"TagID\":1209,\"TagType\":\"byte\",\"Value\":2},{\"TagID\":1048,\"TagType\":\"string\",\"Value\":\"ООО \\\"ОСКЕЛИ ГРУПП\\\"\"}," +
            "{\"TagID\":1018,\"TagType\":\"string\",\"Value\":\"7714966339  \"},{\"TagID\":1012,\"TagType\":\"unixtime\",\"Value\":\"2020-02-28T11:23:00Z\"},{\"TagID\":1037,\"TagType\":\"string\",\"Value\":\"0001377633048987    \"}," +
            "{\"TagID\":1021,\"TagType\":\"string\",\"Value\":\"СИСТ.АДМИНИСТРАТОР\"},{\"TagID\":1009,\"TagType\":\"string\",\"Value\":\"Россия, город Москва, улица Шарикоподшипниковская, дом 11, строение 9\"}," +
            "{\"TagID\":1187,\"TagType\":\"string\",\"Value\":\"OSKELLY.RU\"},{\"TagID\":1105,\"TagType\":\"money\",\"Value\":280000},{\"TagID\":1059,\"TagType\":\"stlv\"," +
            "\"Value\":[{\"TagID\":1079,\"TagType\":\"money\",\"Value\":280000},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":280000}," +
            "{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":6},{\"TagID\":1030,\"TagType\":\"string\",\"Value\":\"Ремень 33984\"},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":4}]},{\"TagID\":1059,\"TagType\":\"stlv\"," +
            "\"Value\":[{\"TagID\":1079,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1023,\"TagType\":\"qty\",\"Value\":1000},{\"TagID\":1043,\"TagType\":\"money\",\"Value\":0},{\"TagID\":1199,\"TagType\":\"byte\",\"Value\":6}," +
            "{\"TagID\":1030,\"TagType\":\"string\",\"Value\":\"Доставка Ремень 33984\"},{\"TagID\":1214,\"TagType\":\"byte\",\"Value\":4}]},{\"TagID\":1041,\"TagType\":\"string\",\"Value\":\"9252440*********\"}," +
            "{\"TagID\":1040,\"TagType\":\"uint32\",\"Value\":1803},{\"TagID\":1077,\"TagType\":\"byte[]\",\"Value\":\"MQTMK68a\"},{\"TagID\":1038,\"TagType\":\"uint32\",\"Value\":206}," +
            "{\"TagID\":1042,\"TagType\":\"uint32\",\"Value\":1}]},\"FiscalSign\":3425414938,\"GrandTotal\":280000,\"NonCash\":[280000,0,0],\"Password\":30,\"PaymentAgentModes\":0,\"Place\":\"OSKELLY.RU\"," +
            "\"QR\":\"t=20200228T1123\\u0026s=2800.00\\u0026fn=9252440*********\\u0026i=1803\\u0026fp=3425414938\\u0026n=1\",\"TaxCalculationMethod\":1,\"TaxMode\":0," +
            "\"Text\":\"КАССОВЫЙ ЧЕК/ПРИХОД\\t28-02-20 11:23\\nООО \\\"ОСКЕЛИ ГРУПП\\\"\\nРоссия, город Москва, улица Шарикоподшипниковская, дом 11, строение 9\\nМЕСТО РАСЧЕТОВ\\tOSKELLY.RU\\nРемень 33984\\nПОЛНЫЙ РАСЧЕТ\\t ~2800.00\\n" +
            "Доставка Ремень 33984\\nПОЛНЫЙ РАСЧЕТ\\t ~0.00\\n##BIG##ИТОГ\\t~2800.00\\nВСЕГО ПОЛУЧЕНО\\t~2800.00\\nБЕЗНАЛИЧНЫМИ\\t~2800.00\\nСУММА БЕЗ НДС\\t~2800.00\\nККТ ДЛЯ ИНТЕРНЕТ\\nКАССИР: СИСТ.АДМИНИСТРАТОР\\tИНН: 7714966339\\n" +
            "СНО\\tУСН ДОХОД - РАСХОД\\nЧЕК: 1\\tСМЕНА: 206\\nСАЙТ ФНС\\twww.nalog.ru\\nРН ККТ: 0001377633048987\\tФН: 9252440*********\\nФД: 1803\\tФП: 3425414938\",\"TurnNumber\":206}}]}";

    @BeforeEach
    public void init() {
        mockServer = startClientAndServer(MOCK_SERVER_PORT);
    }

    @AfterEach
    public void tearDown() {
        mockServer.stop();
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _00_init_order() {
        order = createOrder();
        order.setState(OrderState.MONEY_TRANSFERRED);
        order.setDeliveryState(DeliveryState.OURSELVES_DELIVERY_TO_BUYER);
        order.setEffectiveAmount(order.getAmount());
        order.setFinishedForBuyer(true);
        orderRepository.saveAndFlush(order);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_setBuyerCheckForOrder() {
        final int countOrders = 1;
        new MockServerClient(MOCK_SERVER_HOST, MOCK_SERVER_PORT)
                .when(request().withMethod("POST").withPath("/fr/api/v2/Complex"))
                .respond(response().withStatusCode(OK_200.code())
                        .withHeaders(new Header("Content-Type", "application/json;charset=UTF-8"))
                        .withBody(MOCK_RECEIPT_RESPONSE_FROM_CHECKONLINE)
                );
        assertNotNull(order);
        commitTransaction();
        List<BuyerCheckDTO> syncedBuyerCheckDTOs = scheduledRunner.syncOrdersWithoutBuyerCheck(countOrders);
        // На выполнение таска требуется время
	    TestUtils.sleep(1);
        assertNotNull(syncedBuyerCheckDTOs);
        assertEquals(countOrders, syncedBuyerCheckDTOs.size());
        assertEquals(order.getId(), syncedBuyerCheckDTOs.get(0).getOrderId());
        startNewTransaction();
        Order savedOrderWithBuyerCheck = orderRepository.getOne(syncedBuyerCheckDTOs.get(0).getOrderId());
        assertNotNull(savedOrderWithBuyerCheck);
        assertNotNull(savedOrderWithBuyerCheck.getBuyerCheck());
        assertEquals(MOCK_RECEIPT_RESPONSE_FROM_CHECKONLINE, savedOrderWithBuyerCheck.getBuyerCheck());
        assertEquals(MOCK_RECEIPT_RESPONSE_FROM_CHECKONLINE, syncedBuyerCheckDTOs.get(0).getBuyerCheckJson());
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void  _02_cleanup() {
        if(productsForOrders != null){
            for(Product product : productsForOrders){
                product.setSeller(userService.getUserById(realSellerId).orElse(null));
                productRepository.saveAndFlush(product);
            }
        }
        if(order != null) orderRepository.delete(order);
    }

    private Order createOrder(){
        Order order = new Order();
        order.setBuyer(getBuyer());
        order.setUuid(UUID.randomUUID());
        order.setState(OrderState.CREATED);
        List<Product> products = getProductsForOrders();
        BigDecimal orderAmount = new BigDecimal(0);
        for(int i = 0; i < 3; i++){
            Product product = products.get(i);
            ProductItem productItem = productItemRepository.findAllByProduct(product).get(0);
            OrderPosition orderPosition = new OrderPosition();
            orderPosition.setState(OrderPositionState.VERIFICATION_OK);
            orderPosition.setIsEffective(true);
            orderPosition.setParticipatesInPayment(true);
            orderPosition.setStateTime(LocalDateTime.now());
            orderPosition.setProductItem(productItem);
            BigDecimal amount = product.getCurrentPrice();
            orderAmount = orderAmount.add(amount);
            orderPosition.setAmount(amount);
            orderPosition.setItemSaleAmount(orderPosition.getAmount());
            orderPosition.setCommission(new BigDecimal("0.15"));
            order.addPosition(orderPosition);
        }
        order.setAmount(orderAmount);
        orderRepository.saveAndFlush(order);
        return order;
    }

    private User getBuyer(){
        return userService.getUserByEmail(buyerEmail);
    }

    private List<Product> getProductsForOrders(){
        if(productsForOrders == null) {
            productsForOrders = new ArrayList<>();
            List<Product> products = productRepository.findProductsBySellerIdAndProductState(realSellerId, ProductState.PUBLISHED);
            for (int i = 0; i < 3; i++) {
                Product product = products.get(i);
                product.setSeller(getSeller());
                productRepository.saveAndFlush(product);
                productsForOrders.add(product);
            }
        }
        return productsForOrders;
    }

    private User getSeller(){
        return userService.getUserByEmail(sellerEmail);
    }

}
