package ru.oskelly.tests.pr.suite1_3.domain.service.productpublication.ftp;

import com.amazonaws.services.s3.AmazonS3;
import lombok.Setter;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.TestApiConfiguration;
import su.reddot.domain.service.productpublication.ftp.FtpDto;
import su.reddot.domain.service.productpublication.ftp.FtpService;
import su.reddot.infrastructure.service.imageProcessing.ProcessingType;
import su.reddot.infrastructure.util.FileUtils;

import java.awt.image.BufferedImage;
import java.io.IOException;
import java.net.URL;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@Disabled
@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class FtpServiceTest extends AbstractSpringTest {
    private static final String PATH_TO_FOLDER = "/1234567890";
    private static final String PATH_TO_FILES = "/1234567890/Женское/Платья/0001";

    @Autowired
    private TestApiConfiguration testApiConfiguration;

	@Value("${test.temp-dir}")
	private String tmpDirPath;

    @Value("${resources.images.pathToDir}${app.publication.images-ftp-tmp-path}")
    @Setter
    private String tmpImagesPath;

    @Autowired
    private FtpService ftpService;

    @Autowired
    private AmazonS3 amazonS3client;

	@BeforeEach
	public void init(){
		cleanup();
        amazonS3client.createBucket("oskelly");
	}

    @Test
    public void _01_testGetFolders() {
        List<FtpDto> ftpDtos = ftpService.getFolders(PATH_TO_FOLDER);
        assertEquals(2, ftpDtos.size());
        assertEquals("Женское", ftpDtos.get(0).getName());
        assertEquals("Мужское", ftpDtos.get(1).getName());
    }

    @Test
    public void _02_testGetPhotos() throws IOException {
        List<FtpDto> ftpDtos = ftpService.getPhotos(PATH_TO_FILES);
        assertEquals(6, ftpDtos.size());
        int j;
        for(int i = 0; i < 6; i++){
        	j = i + 1;
        	FtpDto ftpDto = ftpDtos.get(i);
	        assertEquals("0" + j + ".jpg", ftpDto.getName());
            String url = ftpDto.getUrl();
            if(!url.startsWith("http")) {
                url = testApiConfiguration.getServerUrl() + url;
            }
	        BufferedImage image = TestUtils.loadImageFromUrl(url, tmpDirPath);
	        assertNotNull(image);
	        assertEquals(ProcessingType.TINY.getProps().getWidth(), image.getWidth());
	        assertEquals(ProcessingType.TINY.getProps().getHeight(), image.getHeight());
        }
    }

    @AfterEach
    public void fin(){
	    cleanup();
    }

    private void cleanup(){
	    FileUtils.cleanDirectory(tmpDirPath);
	    FileUtils.cleanDirectory(tmpImagesPath + PATH_TO_FOLDER, true);
    }

}