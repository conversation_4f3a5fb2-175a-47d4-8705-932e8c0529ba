package ru.oskelly.tests.pr.suite1_3.domain.service.order;

import com.google.common.collect.ImmutableList;
import lombok.NonNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import su.reddot.domain.model.expertise.Expertise;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.order.OrderPositionState;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.currency.CurrencyConverter;
import su.reddot.domain.service.currency.DefaultCurrencyConverter;
import su.reddot.domain.service.delivery.DefaultDeliveryCostService;
import su.reddot.domain.service.dto.CurrencyDTO;
import su.reddot.domain.service.dto.order.OrderNumbersDTO;
import su.reddot.domain.service.order.impl.OrderNumbersCalculator;
import su.reddot.domain.service.order.impl.OrderProcessingExpertiseInitializer;
import su.reddot.domain.service.order.impl.OrderRequestContext;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Unit tests for {@link OrderNumbersCalculator}.
 */
public class OrderNumbersCalculatorTest {

    private Order order;
    private final User sellerUser = new User().setId(1L);

    private OrderPosition position1;
    private OrderPosition position2;

    private final OrderProcessingExpertiseInitializer opExpertiseInitializer = mock(OrderProcessingExpertiseInitializer.class);
    private final DefaultDeliveryCostService deliveryCostService = mock(DefaultDeliveryCostService.class);

    private final OrderNumbersCalculator calculator = new OrderNumbersCalculator(deliveryCostService);

    private ProductItem makeProductItem() {
        Product product = new Product();
        product.setSeller(sellerUser);
        //
        ProductItem productItem = new ProductItem();
        productItem.setProduct(product);
        //
        return productItem;
    }

    @BeforeEach
    public void setUp() {
        position1 = new OrderPosition();
        position1.setProductItem(makeProductItem());
        position1.setParticipatesInPayment(true);
        position1.setState(OrderPositionState.VERIFICATION_OK);
        position1.setAmount(bigDecimal(20_000));
        position1.setItemSaleAmount(position1.getAmount());
        position1.setCommission(bigDecimal(0.25));
        final Expertise expertise1 = new Expertise();
        expertise1.setIsApproved(true);
        expertise1.setDefectDiscountPrice(bigDecimal(2_000));
        position1.setExpertises(Collections.singletonList(expertise1));

        position2 = new OrderPosition();
        position2.setProductItem(makeProductItem());
        position2.setParticipatesInPayment(true);
        position2.setState(OrderPositionState.VERIFICATION_OK);
        position2.setAmount(bigDecimal(15_000));
        position2.setItemSaleAmount(position2.getAmount());
        position2.setCommission(bigDecimal(0.25));
        position2.setPromocodeAmount(bigDecimal(1_000));
        final Expertise expertise2 = new Expertise();
        expertise2.setIsApproved(true);
        position2.setExpertises(Collections.singletonList(expertise2));


        order = new Order();
        order.setState(OrderState.HOLD_COMPLETED);
        order.setDeliveryCost(bigDecimal(500));

        final List<OrderPosition> orderPositions = ImmutableList.of(position1, position2);
        order.setOrderPositions(orderPositions);
        expertise1.setOrder(order);
        expertise2.setOrder(order);

        when(opExpertiseInitializer.getExpertise()).thenReturn(null);
    }

    @Test
    public void testForSeller() {
        final OrderNumbersDTO numbers = calcNumbers(true);

        assertThat(numbers.getTotalAmount()).isEqualByComparingTo(bigDecimal(24_750));
        assertThat(numbers.getTotalOriginalAmount()).isEqualByComparingTo(bigDecimal(26_250));
        assertThat(numbers.getTotalBuyerAmount()).isEqualByComparingTo(bigDecimal(33_000));
        assertThat(numbers.getPromocodeAmount()).isNull();
        assertThat(numbers.getDiscountAmount()).isNull();
    }

    @Test
    public void testForSellerAllRejected() {
        position1.setState(OrderPositionState.SALE_REJECTED);
        position2.setState(OrderPositionState.SALE_REJECTED);
        final OrderNumbersDTO numbers = calcNumbers(true);

        assertThat(numbers.getTotalAmount()).isEqualByComparingTo(bigDecimal(26_250));
        assertThat(numbers.getTotalOriginalAmount()).isEqualByComparingTo(bigDecimal(26_250));
        assertThat(numbers.getTotalBuyerAmount()).isEqualByComparingTo(bigDecimal(35_000));
        assertThat(numbers.getPromocodeAmount()).isNull();
        assertThat(numbers.getDiscountAmount()).isNull();
    }

    @Test
    public void testForSellerAllPickupDeclined() {
        position1.setState(OrderPositionState.PICKUP_DECLINED);
        position2.setState(OrderPositionState.PICKUP_DECLINED);
        final OrderNumbersDTO numbers = calcNumbers(true);

        assertThat(numbers.getTotalAmount()).isEqualByComparingTo(bigDecimal(26_250));
        assertThat(numbers.getTotalOriginalAmount()).isEqualByComparingTo(bigDecimal(26_250));
        assertThat(numbers.getTotalBuyerAmount()).isEqualByComparingTo(bigDecimal(35_000));
        assertThat(numbers.getPromocodeAmount()).isNull();
        assertThat(numbers.getDiscountAmount()).isNull();
    }

    @Test
    public void testForSellerAllFailed() {
        position1.setState(OrderPositionState.REJECTED_AFTER_VERIFICATION);
        position2.setState(OrderPositionState.REJECTED_AFTER_VERIFICATION);
        final OrderNumbersDTO numbers = calcNumbers(true);

        assertThat(numbers.getTotalAmount()).isEqualByComparingTo(bigDecimal(26_250));
        assertThat(numbers.getTotalOriginalAmount()).isEqualByComparingTo(bigDecimal(26_250));
        assertThat(numbers.getTotalBuyerAmount()).isEqualByComparingTo(bigDecimal(35_000));
        assertThat(numbers.getPromocodeAmount()).isNull();
        assertThat(numbers.getDiscountAmount()).isNull();
    }

    @Test
    public void testForSellerAllPickupDeclinedAndFailed() {
        position1.setState(OrderPositionState.PICKUP_DECLINED);
        position2.setState(OrderPositionState.REJECTED_AFTER_VERIFICATION);
        final OrderNumbersDTO numbers = calcNumbers(true);

        assertThat(numbers.getTotalAmount()).isEqualByComparingTo(bigDecimal(11_250));
        assertThat(numbers.getTotalOriginalAmount()).isEqualByComparingTo(bigDecimal(11_250));
        assertThat(numbers.getTotalBuyerAmount()).isEqualByComparingTo(bigDecimal(15_000));
        assertThat(numbers.getPromocodeAmount()).isNull();
        assertThat(numbers.getDiscountAmount()).isNull();
    }

    @Test
    public void testForSellerAllRejectedAndFailed() {
        position1.setState(OrderPositionState.REJECTED_AFTER_VERIFICATION);
        position2.setState(OrderPositionState.SALE_REJECTED);
        final OrderNumbersDTO numbers = calcNumbers(true);

        assertThat(numbers.getTotalAmount()).isEqualByComparingTo(bigDecimal(15_000));
        assertThat(numbers.getTotalOriginalAmount()).isEqualByComparingTo(bigDecimal(15_000));
        assertThat(numbers.getTotalBuyerAmount()).isEqualByComparingTo(bigDecimal(20_000));
        assertThat(numbers.getPromocodeAmount()).isNull();
        assertThat(numbers.getDiscountAmount()).isNull();
    }

    @Test
    public void testForBuyer() {
        final OrderNumbersDTO numbers = calcNumbers(false);

        assertThat(numbers.getTotalAmount()).isEqualByComparingTo(bigDecimal(32_500));
        assertThat(numbers.getTotalOriginalAmount()).isEqualByComparingTo(bigDecimal(34_500));
        assertThat(numbers.getTotalBuyerAmount()).isNull();
        assertThat(numbers.getPromocodeAmount()).isEqualByComparingTo(bigDecimal(1_000));
        assertThat(numbers.getDiscountAmount()).isEqualByComparingTo(bigDecimal(2_000));
    }

    @Test
    public void testForBuyerAllRejected() {
        position1.setState(OrderPositionState.SALE_REJECTED);
        position2.setState(OrderPositionState.SALE_REJECTED);
        final OrderNumbersDTO numbers = calcNumbers(false);

        assertThat(numbers.getTotalAmount()).isEqualByComparingTo(bigDecimal(35_000));
        assertThat(numbers.getTotalOriginalAmount()).isEqualByComparingTo(bigDecimal(35_000));
        assertThat(numbers.getTotalBuyerAmount()).isNull();
        assertThat(numbers.getPromocodeAmount()).isNull();
        assertThat(numbers.getDiscountAmount()).isNull();
    }

    @Test
    public void testForBuyerAllPickupDeclined() {
        position1.setState(OrderPositionState.PICKUP_DECLINED);
        position2.setState(OrderPositionState.PICKUP_DECLINED);
        final OrderNumbersDTO numbers = calcNumbers(false);

        assertThat(numbers.getTotalAmount()).isEqualByComparingTo(bigDecimal(35_000));
        assertThat(numbers.getTotalOriginalAmount()).isEqualByComparingTo(bigDecimal(35_000));
        assertThat(numbers.getTotalBuyerAmount()).isNull();
        assertThat(numbers.getPromocodeAmount()).isNull();
        assertThat(numbers.getDiscountAmount()).isNull();
    }

    @Test
    public void testForBuyerAllFailed() {
        position1.setState(OrderPositionState.REJECTED_AFTER_VERIFICATION);
        position2.setState(OrderPositionState.REJECTED_AFTER_VERIFICATION);
        final OrderNumbersDTO numbers = calcNumbers(false);

        assertThat(numbers.getTotalAmount()).isEqualByComparingTo(bigDecimal(35_000));
        assertThat(numbers.getTotalOriginalAmount()).isEqualByComparingTo(bigDecimal(35_000));
        assertThat(numbers.getTotalBuyerAmount()).isNull();
        assertThat(numbers.getPromocodeAmount()).isNull();
        assertThat(numbers.getDiscountAmount()).isNull();
    }

    @Test
    public void testForBuyerAllPickupDeclinedAndFailed() {
        position1.setState(OrderPositionState.PICKUP_DECLINED);
        position2.setState(OrderPositionState.REJECTED_AFTER_VERIFICATION);
        final OrderNumbersDTO numbers = calcNumbers(false);

        assertThat(numbers.getTotalAmount()).isEqualByComparingTo(bigDecimal(15_000));
        assertThat(numbers.getTotalOriginalAmount()).isEqualByComparingTo(bigDecimal(15_000));
        assertThat(numbers.getTotalBuyerAmount()).isNull();
        assertThat(numbers.getPromocodeAmount()).isNull();
        assertThat(numbers.getDiscountAmount()).isNull();
    }

    @Test
    public void testForBuyerAllRejectedAndFailed() {
        position1.setState(OrderPositionState.REJECTED_AFTER_VERIFICATION);
        position2.setState(OrderPositionState.SALE_REJECTED);
        final OrderNumbersDTO numbers = calcNumbers(false);

        assertThat(numbers.getTotalAmount()).isEqualByComparingTo(bigDecimal(20_000));
        assertThat(numbers.getTotalOriginalAmount()).isEqualByComparingTo(bigDecimal(20_000));
        assertThat(numbers.getTotalBuyerAmount()).isNull();
        assertThat(numbers.getPromocodeAmount()).isNull();
        assertThat(numbers.getDiscountAmount()).isNull();
    }

    private static BigDecimal bigDecimal(final long value) {
        return BigDecimal.valueOf(value);
    }

    private static BigDecimal bigDecimal(final double value) {
        return BigDecimal.valueOf(value);
    }

    @NonNull
    private OrderNumbersDTO calcNumbers(boolean isForSeller) {
        final OrderRequestContext context = createContext(isForSeller);
        return calculator.calcNumbers(order, context, opExpertiseInitializer);
    }

    private static OrderRequestContext createContext(final boolean isForSeller) {
        final CurrencyDTO currency = new CurrencyDTO();
        final CurrencyConverter currencyConverter = new DefaultCurrencyConverter(currency, BigDecimal.ONE);
        return OrderRequestContext.builder()
                .isForBuyer(!isForSeller)
                .isForSeller(isForSeller)
                .isPro(false)
                .currencyConverter(currencyConverter)
                .build();
    }

}
