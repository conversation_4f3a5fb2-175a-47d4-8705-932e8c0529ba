package ru.oskelly.tests.pr.suite1_3.domain.service.following;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.model.Brand;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.adminpanel.tag.UserCommonTagCode;
import su.reddot.domain.service.adminpanel.tag.UserCommonTagService;
import su.reddot.domain.service.brand.BrandService;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.domain.service.dto.FollowingsProposalsTabDTO;
import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.dto.PageRequest;
import su.reddot.domain.service.dto.UserDTO;
import su.reddot.domain.service.following.FollowingService;
import su.reddot.domain.service.following.FollowingsProposalsTabType;
import su.reddot.domain.service.like.LikeService;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.security.SecurityService;

import javax.sql.rowset.serial.SerialArray;
import javax.sql.rowset.serial.SerialException;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@SpringBootTest(classes =  {OskellyApplication.class})
@ExtendWith(SpringExtension.class)
@ActiveProfiles(AbstractSpringTest.testProfiles)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
@Slf4j
public class DefaultFollowingServiceTest extends AbstractSpringTest {

    @Autowired
    private FollowingService followingService;

    @Autowired
    private UserCommonTagService userCommonTagService;

    @Autowired
    private CommissionGridService commissionGridService;

    @Autowired
    private LikeService likeService;

    @Autowired
    private BrandService brandService;

    @Autowired
    private UserRepository userRepository;

    @MockBean
    private SecurityService securityService;

    private static final Long U_1 = 21L;
    private static final Long U_2 = 848L;
    private static final Long U_3 = 26L;
    private static final Long U_4 = 42052L;
    private static final Long U_5 = 158L;
    private static final Long U_6 = 164L;
    private static final Long U_7 = 262L;
    private static final Long U_8 = 3624L;
    private static final Long U_9 = 10L;
    private static final Long U_10 = 100L;

    private static final Long B_1 = 504L;
    private static final Long B_2 = 1860L;
    private static final Long B_3 = 1198L;
    private static final Long B_4 = 174L;
    private static final Long B_5 = 104L;

    private final List<Long> userIds = Arrays.asList(U_1, U_2, U_3, U_4, U_5, U_6, U_7, U_8, U_9, U_10);

    @Test
    @Transactional
    public void testFollowingsProposals() throws SerialException {
        //Добавление user_common_tag_binding для выбранных пользователей
        Long groupId = userCommonTagService.getGroupByCode("USER_STATUS").getId();
        Long influencerTagId = userCommonTagService.getTagByCode(UserCommonTagCode.USER_STATUS_INFLUENCER.name()).getId();
        Long stylistTagId = userCommonTagService.getTagByCode(UserCommonTagService.STYLIST_TAG_CODE).getId();
        Long celebrityTagId = userCommonTagService.getTagByCode(UserCommonTagCode.USER_STATUS_CELEBRITY.name()).getId();

        userIds.forEach(u -> userCommonTagService.unbindAllTagsFromUserByGroup(u, groupId));

        addUserStatusCommonTags(
                IntStream.range(0, userIds.size()).filter(i -> i % 2 == 0).mapToObj(userIds::get).collect(Collectors.toList()),
                influencerTagId,
                groupId
        );
        addUserStatusCommonTags(
                IntStream.range(0, userIds.size()).filter(i -> i % 2 != 0).mapToObj(userIds::get).collect(Collectors.toList()),
                stylistTagId,
                groupId
        );
        addUserStatusCommonTags(
                IntStream.range(0, userIds.size()).filter(i -> i % 3 == 0).mapToObj(userIds::get).collect(Collectors.toList()),
                celebrityTagId,
                groupId
        );

        //Добавление одного фолловера одному пользователю (для рейтинга)
        followingService.follow(U_2, U_1);
        followingService.follow(U_3, U_1);
        followingService.follow(U_4, U_1);
        followingService.follow(U_5, U_1);
        followingService.follow(U_6, U_1);
        followingService.follow(U_7, U_1);
        followingService.follow(U_8, U_1);
        followingService.follow(U_9, U_1);
        followingService.follow(U_10, U_1);

        //ожидаемая маппировка тегов
        Map<Long, Long[]> expectedProposals = new HashMap<>();
        expectedProposals.put(U_1, Arrays.asList(influencerTagId, celebrityTagId).toArray(new Long[2]));
        expectedProposals.put(U_2, Collections.singletonList(stylistTagId).toArray(new Long[1]));
        expectedProposals.put(U_3, Collections.singletonList(influencerTagId).toArray(new Long[1]));
        expectedProposals.put(U_4, Arrays.asList(stylistTagId, celebrityTagId).toArray(new Long[2]));
        expectedProposals.put(U_5, Collections.singletonList(influencerTagId).toArray(new Long[1]));
        expectedProposals.put(U_6, Collections.singletonList(stylistTagId).toArray(new Long[1]));
        expectedProposals.put(U_7, Arrays.asList(influencerTagId, celebrityTagId).toArray(new Long[2]));
        expectedProposals.put(U_8, Collections.singletonList(stylistTagId).toArray(new Long[1]));
        expectedProposals.put(U_9, Collections.singletonList(influencerTagId).toArray(new Long[1]));
        expectedProposals.put(U_10, Arrays.asList(stylistTagId, celebrityTagId).toArray(new Long[2]));

        //генерация таблицы с рекомендациями
        followingService.fillFollowingsProposals();

        //проверка заполненности таблицы с рекомендациями
        SqlRowSet sqlRowSet = jdbcTemplate.queryForRowSet("select * from following_proposal order by id");
        checkProposalsInDb(sqlRowSet, expectedProposals);

        PageRequest pageRequest = PageRequest.of(1, 20);
        Brand b1 = brandService.getOne(B_1);
        Brand b2 = brandService.getOne(B_2);
        Brand b3 = brandService.getOne(B_3);
        Brand b4 = brandService.getOne(B_4);
        Brand b5 = brandService.getOne(B_5);

        //Проверка выдачи рекомендаций в зависимости от того, какие бренды лайкнул пользователь
        List<FollowingsProposalsTabType> expectedTabCodes = Arrays.asList(
                FollowingsProposalsTabType.CELEBRITY, FollowingsProposalsTabType.INFLUENCER, FollowingsProposalsTabType.STYLIST
        );
        List<Integer> expectedTabCounts = Arrays.asList(4, 5, 5);

        createUser("test_1", Arrays.asList(b1, b2, b3, b4, b5));
        Page<UserDTO> page1 = followingService.getFollowingsProposalsPage(pageRequest, null);
        checkGetProposals(Arrays.asList(U_1, U_9, U_3, U_7, U_2, U_5, U_8, U_10, U_6, U_4), page1);
        List<FollowingsProposalsTabDTO> tabs1 = followingService.getFollowingsProposalsTabs();
        checkGetProposalsTabs(expectedTabCodes, expectedTabCounts, tabs1);

        Page<UserDTO> page11 = followingService.getFollowingsProposalsPage(pageRequest, FollowingsProposalsTabType.INFLUENCER);
        checkGetProposals(Arrays.asList(U_1, U_9, U_3, U_7, U_5), page11);

        Page<UserDTO> page12 = followingService.getFollowingsProposalsPage(pageRequest, FollowingsProposalsTabType.CELEBRITY);
        checkGetProposals(Arrays.asList(U_1, U_7, U_10, U_4), page12);

        Page<UserDTO> page13 = followingService.getFollowingsProposalsPage(pageRequest, FollowingsProposalsTabType.STYLIST);
        checkGetProposals(Arrays.asList(U_2, U_8, U_10, U_6, U_4), page13);

        createUser("test_2", Collections.singletonList(b5));
        Page<UserDTO> page2 = followingService.getFollowingsProposalsPage(pageRequest, null);
        checkGetProposals(Arrays.asList(U_9, U_1, U_3, U_10, U_5, U_6, U_7, U_2, U_8, U_4), page2);
        List<FollowingsProposalsTabDTO> tabs2 = followingService.getFollowingsProposalsTabs();
        checkGetProposalsTabs(expectedTabCodes, expectedTabCounts, tabs2);

        createUser("test_3", Arrays.asList(b2, b3));
        Page<UserDTO> page3 = followingService.getFollowingsProposalsPage(pageRequest, null);
        checkGetProposals(Arrays.asList(U_1, U_7, U_9, U_3, U_2, U_10, U_5, U_6, U_8, U_4), page3);
        List<FollowingsProposalsTabDTO> tabs3 = followingService.getFollowingsProposalsTabs();
        checkGetProposalsTabs(expectedTabCodes, expectedTabCounts, tabs3);

        createUser("test_4", Collections.emptyList());
        Page<UserDTO> page4 = followingService.getFollowingsProposalsPage(pageRequest, null);
        checkGetProposals(Arrays.asList(U_1, U_9, U_3, U_10, U_5, U_6, U_7, U_2, U_8, U_4), page4);
        List<FollowingsProposalsTabDTO> tabs4 = followingService.getFollowingsProposalsTabs();
        checkGetProposalsTabs(expectedTabCodes, expectedTabCounts, tabs4);
    }

    private void createUser(String nickname, List<Brand> likedBrands) {
        User user = new User()
                .setNickname(nickname)
                .setUserType(User.UserType.SIMPLE_USER)
                .setChangeTime(LocalDateTime.now())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid())
                .setIsLoyaltyProgramV2Accepted(ZonedDateTime.now())
                .setCounterparties(Collections.emptyList());

        User user1 = userRepository.saveAndFlush(user);

        likedBrands.forEach(brand -> likeService.like(brand, user1));

        when(securityService.getCurrentAuthorizedUserId()).thenReturn(user1.getId());
    }

    private void checkGetProposals(List<Long> expected, Page<UserDTO> page) {
        assertNotNull(page);
        assertNotNull(expected);
        assertNotNull(page.getItems());
        assertEquals(expected.size(), page.getItems().size());
        for (int i = 0; i < expected.size(); i++) {
            UserDTO dto = page.getItems().get(i);
            assertNotNull(dto);
            assertEquals(expected.get(i), dto.getId());
        }
    }

    private void checkGetProposalsTabs(
            List<FollowingsProposalsTabType> expectedTabs, List<Integer> expectedCounts, List<FollowingsProposalsTabDTO> tabs
    ) {
        assertNotNull(tabs);
        assertNotNull(expectedTabs);
        assertNotNull(expectedCounts);
        assertEquals(expectedTabs.size(), expectedCounts.size());
        assertEquals(expectedTabs.size(), tabs.size());
        for (int i = 0; i < expectedTabs.size(); i++) {
            FollowingsProposalsTabDTO dto = tabs.get(i);
            assertNotNull(dto);
            assertNotNull(dto.getCode());
            assertEquals(expectedTabs.get(i).name(), dto.getCode());
            assertEquals(expectedCounts.get(i), dto.getCount());
        }
    }

    private void addUserStatusCommonTags(List<Long> users, Long tagId, Long groupId) {
        users.forEach(userId -> userCommonTagService.bindTagToUserWithMultiSelectionAndWithoutEventsAndInterceptors(tagId, userId, groupId));
    }

    private void checkProposalsInDb(SqlRowSet sqlRowSet, Map<Long, Long[]> expectedProposals) throws SerialException {
        assertTrue(sqlRowSet.next());

        do {
            checkProposalsTags(
                    expectedProposals.get(sqlRowSet.getLong("user_id")),
                    ((Object[]) ((SerialArray) sqlRowSet.getObject("tag_ids")).getArray())
            );
        } while (sqlRowSet.next());
    }

    private void checkProposalsTags(Long[] expectedTagIds, Object[] tagIds) {
        assertNotNull(expectedTagIds);
        assertNotNull(tagIds);
        assertEquals(expectedTagIds.length, tagIds.length);
        for (int i = 0; i < expectedTagIds.length; i++) {
            assertEquals(expectedTagIds[i], (Long) tagIds[i]);
        }
    }
}