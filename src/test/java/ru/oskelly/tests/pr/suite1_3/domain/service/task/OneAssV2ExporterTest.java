package ru.oskelly.tests.pr.suite1_3.domain.service.task;

import java.io.IOException;
import java.time.ZoneId;
import java.util.TimeZone;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.NonNull;
import lombok.SneakyThrows;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.service.staticresource.StaticResourceBalancer;
import su.reddot.infrastructure.configparam.ConfigParamService;
import su.reddot.infrastructure.configparam.DefaultConfigParamService;
import su.reddot.infrastructure.export.feed.FeedExporter;
import su.reddot.infrastructure.export.feed.oneass.BankAccountsV2Exporter;
import su.reddot.infrastructure.export.feed.oneass.BoutiqueProductsExporter;
import su.reddot.infrastructure.export.feed.oneass.BoutiqueSellersExporter;
import su.reddot.infrastructure.export.feed.oneass.CustomersV2Exporter;
import su.reddot.infrastructure.export.feed.oneass.PaymentsToSellersCsvExporter;
import su.reddot.infrastructure.export.feed.oneass.PaymentsToSellersExporter;
import su.reddot.infrastructure.export.feed.oneass.ProductCategoriesExporter;
import su.reddot.infrastructure.export.feed.oneass.ProductsV2Exporter;
import su.reddot.infrastructure.export.feed.oneass.SalesV2Exporter;

import org.junit.jupiter.api.Test;
import org.postgresql.ds.PGSimpleDataSource;
import org.springframework.jdbc.core.SqlRowSetResultSetExtractor;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
@Disabled("Тест выгрузки в 1С: позволяет как проверить выгрузки, так и сделать какие-то специфические выгрузки (указать креды к базе)")
public class OneAssV2ExporterTest {

    private static final String PATH = replaceHomePaths("~/oskelly/data/export/1c/");
    private static final PGSimpleDataSource dataSource = new PGSimpleDataSource();

    static {
        dataSource.setServerName("fillWithRealData");
        dataSource.setPortNumber(6432);
        dataSource.setDatabaseName("oskelly");
        dataSource.setUser("fillWithRealData");
        dataSource.setPassword("fillWithRealData");
    }

    private static String replaceHomePaths(String path) {
        return path.replaceFirst("^~", System.getProperty("user.home"));
    }

    private static final StaticResourceBalancer staticResourceBalancer = new StaticResourceBalancer() {
        @Override
        public String getImagesPrefix(){
            return "http://static.oskelly.ru/img";
        }
    };

    private static final ConfigParamService configParamService = new DefaultConfigParamService(new ObjectMapper()) {
        @Override
        public int getValueAsInteger(String key) {
            return 365;
        }
    };

    @BeforeEach
    public void setUp() {
        TimeZone.setDefault(TimeZone.getTimeZone(ZoneId.of("UTC")));
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void testBankAccounts() throws IOException {
        FeedExporter exporter = new BankAccountsV2Exporter(configParamService) {
            @Override
            public String getDefaultExportPath() {
                return PATH;
            }

            @Override
            public String getExportName() {
                return "BankAccounts.xml";
            }

            @Override
            public boolean isBeautifyXml() {
                return true;
            }

            @Override
            public SqlRowSet queryData() {
                return queryDataFromDs(this);
            }
        };

        exporter.defaultDelivery();
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void testSales() throws IOException {
        FeedExporter exporter = new SalesV2Exporter(configParamService) {
            @Override
            public String getDefaultExportPath() {
                return PATH;
            }

            @Override
            public String getExportName() {
                return "Sales.xml";
            }

            @Override
            public boolean isBeautifyXml() {
                return true;
            }

            @Override
            public SqlRowSet queryData() {
                return queryDataFromDs(this);
            }
        };

        exporter.defaultDelivery();
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void testProducts() throws IOException {
        FeedExporter exporter = new ProductsV2Exporter(configParamService, staticResourceBalancer) {
            @Override
            public String getDefaultExportPath() {
                return PATH;
            }

            @Override
            public String getExportName() {
                return "Products.xml";
            }

            @Override
            public boolean isBeautifyXml() {
                return true;
            }

            @SneakyThrows
            @Override
            public SqlRowSet queryData() {
                return queryDataFromDs(this);
            }

            @Override
            @NonNull
            public SqlRowSet getCategoriesRowSet(@NonNull final String sql) {
                return queryDataFromDs(sql);
            }
        };

        exporter.defaultDelivery();
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void testBoutiqueProducts() throws IOException {
        FeedExporter exporter = new BoutiqueProductsExporter(configParamService, staticResourceBalancer) {
            @Override
            public String getDefaultExportPath() {
                return PATH;
            }

            @Override
            public String getExportName() {
                return "BoutiqueProducts.xml";
            }

            @Override
            public boolean isBeautifyXml() {
                return true;
            }

            @SneakyThrows
            @Override
            public SqlRowSet queryData() {
                return queryDataFromDs(this);
            }

            @Override
            @NonNull
            public SqlRowSet getCategoriesRowSet(@NonNull final String sql) {
                return queryDataFromDs(sql);
            }
        };

        exporter.defaultDelivery();
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void testCustomers() throws IOException {
        FeedExporter exporter = new CustomersV2Exporter(configParamService) {
            @Override
            public String getDefaultExportPath() {
                return PATH;
            }

            @Override
            public String getExportName() {
                return "Customers.xml";
            }

            @Override
            public boolean isBeautifyXml() {
                return true;
            }

            @Override
            public SqlRowSet queryData() {
                return queryDataFromDs(this);
            }
        };

        exporter.defaultDelivery();
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void testBoutiqueSellers() throws IOException {
        FeedExporter exporter = new BoutiqueSellersExporter(configParamService) {
            @Override
            public String getDefaultExportPath() {
                return PATH;
            }

            @Override
            public String getExportName() {
                return "BoutiqueSellers.xml";
            }

            @Override
            public boolean isBeautifyXml() {
                return true;
            }

            @Override
            public SqlRowSet queryData() {
                return queryDataFromDs(this);
            }
        };

        exporter.defaultDelivery();
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void testPayments() throws IOException {
        FeedExporter exporter = new PaymentsToSellersExporter(configParamService) {
            @Override
            public String getDefaultExportPath() {
                return PATH;
            }

            @Override
            public String getExportName() {
                return "PaymentsToSellers.xml";
            }

            @Override
            public boolean isBeautifyXml() {
                return true;
            }

            @Override
            protected SqlRowSet queryData(String query) {
                return queryDataFromDs(query);
            }
        };

        exporter.defaultDelivery();
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void testPaymentsToSellersCsv() throws IOException {
        FeedExporter exporter = new PaymentsToSellersCsvExporter() {
            @Override
            public String getDefaultExportPath() {
                return PATH;
            }

            @Override
            protected SqlRowSet queryData() {
                return queryDataFromDs(getQuery());
            }
        };

        exporter.defaultDelivery();
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void testProductCategories() throws IOException {
        FeedExporter exporter = new ProductCategoriesExporter() {
            @Override
            public String getDefaultExportPath() {
                return PATH;
            }

            @Override
            public SqlRowSet queryData() {
                return queryDataFromDs(this);
            }
        };

        exporter.defaultDelivery();
    }

    private static SqlRowSet queryDataFromDs(FeedExporter exporter) {
        String sql = exporter.getQuery();
        return queryDataFromDs(sql);
    }

    @SneakyThrows
    private static SqlRowSet queryDataFromDs(String sql) {
        return new SqlRowSetResultSetExtractor().extractData(
                dataSource.getConnection().createStatement().executeQuery(sql));
    }
}
