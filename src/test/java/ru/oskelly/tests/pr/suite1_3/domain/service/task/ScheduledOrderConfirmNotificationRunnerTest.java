package ru.oskelly.tests.pr.suite1_3.domain.service.task;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.model.addressendpoint.AddressEndpoint;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.master.MasterService;
import su.reddot.domain.service.master.MasterServiceRequest;
import su.reddot.domain.service.task.ScheduledOrderConfirmNotificationRunner;
import su.reddot.domain.service.task.config.ScheduledOrderConfirmNotificationProperties;
import su.reddot.infrastructure.analytics.OskellyAnalyticsClient;
import su.reddot.infrastructure.chat.UseDeskChannel;
import su.reddot.infrastructure.chat.usedesk.UseDeskService;
import su.reddot.infrastructure.configparam.ConfigParamService;

import java.io.IOException;
import java.net.URISyntaxException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Locale;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class ScheduledOrderConfirmNotificationRunnerTest {

    public static final long USER_ID = 1235L;
    public static final long USE_DESK_ID = 12L;
    public static final String TEST_PHONE = "+77771234712";
    public static final String TEST_TAG = "test_tag";
    public static final long ORDER_ID = 0L;
    @Mock
    OrderRepository orderRepository;
    @Mock
    UseDeskService useDeskService;
    @Mock
    MessageSource messageSource;
    @Mock
    ConfigParamService configParamService;
    @Mock
    MasterService masterService;
    @Mock
    OskellyAnalyticsClient oskellyAnalyticsClient;

    private ScheduledOrderConfirmNotificationRunner runner;

    @BeforeEach
    public void init() throws IOException, URISyntaxException {
        ScheduledOrderConfirmNotificationProperties properties = new ScheduledOrderConfirmNotificationProperties();
        properties.setHost("test.host");
        properties.setNoticeAfter(Duration.parse("PT4H"));
        properties.setTag(TEST_TAG);
        runner = new ScheduledOrderConfirmNotificationRunner(
                orderRepository,
                useDeskService,
                messageSource,
                properties,
                configParamService,
                masterService,
                oskellyAnalyticsClient
        );
    }

    @Test
    public void schedulerCallsUseDeskNotification() {
        User seller = new User();
        seller.setId(USER_ID);

        AddressEndpoint addressEndpoint = new AddressEndpoint();
        addressEndpoint.setPhone(TEST_PHONE);

        Product product = new Product();
        product.setSeller(seller);
        product.setPickupAddressEndpoint(addressEndpoint);

        ProductItem productItem = new ProductItem();
        productItem.setProduct(product);

        OrderPosition orderPosition = new OrderPosition();
        orderPosition.setProductItem(productItem);

        Order order = new Order();
        order.setId(ORDER_ID);
        order.setOrderPosition(orderPosition);

        List<Order> orders = new ArrayList<>();
        orders.add(order);
        when(orderRepository.getNeedNotifyToConfirmOrdersPredicate(any())).thenReturn(new BooleanBuilder());
        when(orderRepository.findAll(any(Predicate.class), any(Pageable.class))).thenReturn(new PageImpl<>(orders));
        when(useDeskService.getUseDeskId(any())).thenReturn(USE_DESK_ID);
        when(useDeskService.sendNotification(anyLong(), any(), anyString(), anyString(), anyString(), anyString())).thenReturn(true);
        when(messageSource.getMessage(eq("seller.order.accept.expire"), any(Object[].class), eq(Locale.ROOT))).thenReturn("Test message");

        runner.sendOrderConfirmNotificationsToSellers();

        verify(useDeskService, times(1)).sendNotification(
                eq(USE_DESK_ID),
                eq(UseDeskChannel.WHATSAPP),
                eq(TEST_PHONE),
                eq("Уведомление продавца о необходимости подтвердить заказ"),
                eq("Test message"),
                eq(TEST_TAG)
        );

        ArgumentCaptor<MasterServiceRequest> masterServiceRequestArgumentCaptor = ArgumentCaptor.forClass(MasterServiceRequest.class);
        verify(masterService, times(1)).send(masterServiceRequestArgumentCaptor.capture());
        MasterServiceRequest masterServiceRequest = masterServiceRequestArgumentCaptor.getValue();
        Object requestEntityObject = masterServiceRequest.getRequestEntityObject();
        assertThat(requestEntityObject).isInstanceOf(List.class);
        assertThat((List<Long>) requestEntityObject).containsExactly(ORDER_ID);
        assertThat(masterServiceRequest.getUrl()).isEqualTo("/api/v2/master/order/markAsNotified");
    }

    @Test
    public void schedulerDoNothingIfNeedToConfirmOrdersNotFound() {
        when(orderRepository.getNeedNotifyToConfirmOrdersPredicate(any())).thenReturn(new BooleanBuilder());
        when(orderRepository.findAll(any(Predicate.class), any(Pageable.class))).thenReturn(new PageImpl<>(Collections.emptyList()));

        runner.sendOrderConfirmNotificationsToSellers();

        verify(useDeskService, never()).getUseDeskId(any());
        verify(useDeskService, never()).sendNotification(anyLong(), any(), anyString(), anyString(), anyString(), anyString());
        verify(masterService, never()).send(any());
    }

    @Test
    public void shouldNotNotifyUserInStopList() throws IOException {
        User seller = new User();
        seller.setId(100L);

        AddressEndpoint addressEndpoint = new AddressEndpoint();
        addressEndpoint.setPhone(TEST_PHONE);

        Product product = new Product();
        product.setSeller(seller);
        product.setPickupAddressEndpoint(addressEndpoint);

        ProductItem productItem = new ProductItem();
        productItem.setProduct(product);

        OrderPosition orderPosition = new OrderPosition();
        orderPosition.setProductItem(productItem);

        Order order = new Order();
        order.setId(-1L);
        order.setOrderPosition(orderPosition);

        List<Order> orders = new ArrayList<>();
        orders.add(order);
        when(orderRepository.getNeedNotifyToConfirmOrdersPredicate(any())).thenReturn(new BooleanBuilder());
        when(orderRepository.findAll(any(Predicate.class), any(Pageable.class))).thenReturn(new PageImpl<>(orders));
        when(configParamService.getValueAsListCached(anyString())).thenReturn(Collections.singletonList("100"));
        //Повторно инициализируем, чтобы обновить стоп-лист

        runner.sendOrderConfirmNotificationsToSellers();

        verify(useDeskService, never()).getUseDeskId(
                any()
        );

        verify(useDeskService, never()).sendNotification(
                anyLong(),
                any(UseDeskChannel.class),
                anyString(),
                anyString(),
                anyString(),
                anyString()
        );
    }

    @Test
    public void shouldSkipOrdersWhenNoPickupAddressEndpoint() {
        User seller = new User();
        seller.setId(USER_ID);

        Product product = new Product();
        product.setSeller(seller);
        product.setPickupAddressEndpoint(null);

        ProductItem productItem = new ProductItem();
        productItem.setProduct(product);

        OrderPosition orderPosition = new OrderPosition();
        orderPosition.setProductItem(productItem);

        Order order = new Order();
        order.setId(ORDER_ID);
        order.setOrderPosition(orderPosition);

        List<Order> orders = new ArrayList<>();
        orders.add(order);
        when(orderRepository.getNeedNotifyToConfirmOrdersPredicate(any())).thenReturn(new BooleanBuilder());
        when(orderRepository.findAll(any(Predicate.class), any(Pageable.class))).thenReturn(new PageImpl<>(orders));

        runner.sendOrderConfirmNotificationsToSellers();

        verify(useDeskService, never()).sendNotification(
                anyLong(),
                any(UseDeskChannel.class),
                anyString(),
                anyString(),
                anyString(),
                anyString()
        );

        verify(useDeskService, never()).getUseDeskId(any());

        ArgumentCaptor<MasterServiceRequest> masterServiceRequestArgumentCaptor = ArgumentCaptor.forClass(MasterServiceRequest.class);
        verify(masterService, times(1)).send(masterServiceRequestArgumentCaptor.capture());
        MasterServiceRequest masterServiceRequest = masterServiceRequestArgumentCaptor.getValue();
        Object requestEntityObject = masterServiceRequest.getRequestEntityObject();
        assertThat(requestEntityObject).isInstanceOf(List.class);
        assertThat((List<Long>) requestEntityObject).containsExactly(ORDER_ID);
        assertThat(masterServiceRequest.getUrl()).isEqualTo("/api/v2/master/order/markAsNotified");
    }
}
