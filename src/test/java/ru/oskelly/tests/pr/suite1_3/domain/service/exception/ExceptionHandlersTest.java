package ru.oskelly.tests.pr.suite1_3.domain.service.exception;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.http.HttpStatus;
import org.springframework.mock.http.client.MockClientHttpResponse;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.exception.NotFoundException;
import su.reddot.domain.exception.bonuses.BonusesBadRequestException;
import su.reddot.domain.exception.bonuses.LoyaltyCardBadRequestException;
import su.reddot.domain.service.bonuses.BonusesService;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.configuration.exception.BaseResponseErrorHandler;
import su.reddot.infrastructure.configuration.exception.ResponseBody;
import su.reddot.infrastructure.configuration.exception.handlers.bonuses.BonusesBadRequestErrorBodyHandler;
import su.reddot.infrastructure.configuration.exception.handlers.bonuses.BonusesNotFoundErrorBodyHandler;
import su.reddot.infrastructure.configuration.exception.handlers.loyaltycards.LoyaltyCardsAbstractErrorBodyHandler;
import su.reddot.infrastructure.configuration.exception.handlers.loyaltycards.LoyaltyCardsBadRequestErrorBodyHandler;
import su.reddot.infrastructure.configuration.exception.handlers.loyaltycards.LoyaltyCardsNotFoundErrorBodyHandler;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest(classes =  {OskellyApplication.class})
@ExtendWith(SpringExtension.class)
@ActiveProfiles(AbstractSpringTest.testProfiles)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
@Slf4j
public class ExceptionHandlersTest {

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private MessageSourceAccessor messageSourceAccessor;

    @Autowired
    @Qualifier("bonusesBaseResponseErrorHandler")
    private BaseResponseErrorHandler bonusesErrorHandler;

    @Autowired
    @Qualifier("loyaltyCardsBaseResponseErrorHandler")
    private BaseResponseErrorHandler loyaltyCardsErrorHandler;

    @Test
    public void testLoyalCardNotFoundErrorBodyHandler() {
        String code = LoyaltyCardsNotFoundErrorBodyHandler.CODE;
        Class<?> clazz = NotFoundException.class;
        HttpStatus status = HttpStatus.NOT_FOUND;

        testLoyaltyCardsErrorBodyHandler(
                clazz, status, code,
                LoyaltyCardsNotFoundErrorBodyHandler.TYPE_CARD, null,
                new LoyaltyCardsHandlerParamsBuilder().addUserId("1").build()
        );
    }

    @Test
    public void testLoyalCardBadRequestErrorBodyHandler() {
        String code = LoyaltyCardsBadRequestErrorBodyHandler.CODE_ALREADY_BOUND;
        Class<?> clazz = LoyaltyCardBadRequestException.class;
        HttpStatus status = HttpStatus.BAD_REQUEST;

        testLoyaltyCardsErrorBodyHandler(
                clazz, status, code,
                null, LoyaltyCardsBadRequestErrorBodyHandler.TMPL_CARD_ALREADY_BOUND,
                new LoyaltyCardsHandlerParamsBuilder().addCardId("1").addCardUserId("2").addUserId("3").build()
        );

        testLoyaltyCardsErrorBodyHandler(
                clazz, status, code,
                null, LoyaltyCardsBadRequestErrorBodyHandler.TMPL_CARD_ALREADY_EXISTS,
                new LoyaltyCardsHandlerParamsBuilder().addUserId("1").addPhone("2").addCardId("3").build()
        );
    }

    @Test
    public void testBonusesNotFoundErrorBodyHandler() {
        String code = BonusesNotFoundErrorBodyHandler.CODE;
        Class<?> clazz = NotFoundException.class;
        HttpStatus status = HttpStatus.NOT_FOUND;

        testBonusesErrorBodyHandler(
                clazz, status, code,
                BonusesNotFoundErrorBodyHandler.TYPE_TRANSACTION, BonusesNotFoundErrorBodyHandler.TMPL_TRANSACTION_ID,
                new BonusesHandlerParamsBuilder().addTransactionId("1").build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, code,
                BonusesNotFoundErrorBodyHandler.TYPE_TRANSACTION, BonusesNotFoundErrorBodyHandler.TMPL_WITHDRAW_ACCOUNT_AND_ORDER,
                new BonusesHandlerParamsBuilder().addAccountId("1").addOrderId("2").build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, code,
                BonusesNotFoundErrorBodyHandler.TYPE_TEMPLATE, null,
                new BonusesHandlerParamsBuilder().addCode("test").build()
        );
    }

    @Test
    public void testBonusesBadRequestErrorBodyHandlerObjectRequired() {
        Class<?> clazz = BonusesBadRequestException.class;
        HttpStatus status = HttpStatus.BAD_REQUEST;

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_OBJECT_REQUIRED,
                BonusesBadRequestErrorBodyHandler.TYPE_ORDER, null,
                new BonusesHandlerParamsBuilder().addTransactionId("1").build()
        );
    }

    @Test
    public void testBonusesBadRequestErrorBodyHandlerObjectNonUnique() {
        Class<?> clazz = BonusesBadRequestException.class;
        HttpStatus status = HttpStatus.BAD_REQUEST;

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_OBJECT_NON_UNIQUE,
                BonusesBadRequestErrorBodyHandler.TYPE_TEMPLATE, null,
                new BonusesHandlerParamsBuilder().addSize("2").addCode("test").build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_OBJECT_NON_UNIQUE,
                BonusesBadRequestErrorBodyHandler.TYPE_ORDER, null,
                new BonusesHandlerParamsBuilder().addAccountId("1").addOrderId("2").build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_OBJECT_NON_UNIQUE,
                BonusesBadRequestErrorBodyHandler.TYPE_TRANSACTION, null,
                new BonusesHandlerParamsBuilder().addSize("2").addAccountId("1").addOrderId("2").build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_OBJECT_NON_UNIQUE,
                BonusesBadRequestErrorBodyHandler.TYPE_TRANSACTION, BonusesBadRequestErrorBodyHandler.TMPL_TRN_IDEMPOTENCY,
                new BonusesHandlerParamsBuilder().addIdempotencyKey("1").build()
        );
    }

    @Test
    public void testBonusesBadRequestErrorBodyHandlerObjectMustBeNull() {
        Class<?> clazz = BonusesBadRequestException.class;
        HttpStatus status = HttpStatus.BAD_REQUEST;

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_OBJECT_MUST_BE_NULL,
                BonusesBadRequestErrorBodyHandler.TYPE_ORDER, null,
                new BonusesHandlerParamsBuilder().addTransactionId("1").build()
        );
    }

    @Test
    public void testBonusesBadRequestErrorBodyHandlerWrongState() {
        Class<?> clazz = BonusesBadRequestException.class;
        HttpStatus status = HttpStatus.BAD_REQUEST;

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_WRONG_STATE,
                null, BonusesBadRequestErrorBodyHandler.TMPL_TRN_HAS_NO_CHILDREN,
                new BonusesHandlerParamsBuilder().addTransactionId("1").build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_WRONG_STATE,
                null, BonusesBadRequestErrorBodyHandler.TMPL_TRN_CHILDREN_SIZE_DIF,
                new BonusesHandlerParamsBuilder().addTransactionId("1").addChildrenSize("2").addItemsSize("3").build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_WRONG_STATE,
                null, BonusesBadRequestErrorBodyHandler.TMPL_TRN_CHILD_ABSENT,
                new BonusesHandlerParamsBuilder().addTransactionId("1").addChildTransactionId("2").build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_WRONG_STATE,
                null, BonusesBadRequestErrorBodyHandler.TMPL_TRN_CANCEL_BONUSES_DIF,
                new BonusesHandlerParamsBuilder().addTransactionId("1").addChildTransactionId("2").addBonuses("3").addAmount("4").build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_WRONG_STATE,
                null, BonusesBadRequestErrorBodyHandler.TMPL_TRN_CANCEL_MONEY_DIF,
                new BonusesHandlerParamsBuilder().addTransactionId("1").addChildTransactionId("2").addMoney("3").addAmount("4").build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_WRONG_STATE,
                null, BonusesBadRequestErrorBodyHandler.TMPL_TRN_ALREADY_EXISTS,
                new BonusesHandlerParamsBuilder().addAccountId("1").addOrderId("1").build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_WRONG_STATE,
                null, BonusesBadRequestErrorBodyHandler.TMPL_TRN_WRONG_STATE,
                new BonusesHandlerParamsBuilder().addTransactionId("1").addState("2").build()
        );
    }

    @Test
    public void testBonusesBadRequestErrorBodyHandlerWrongAmount() {
        Class<?> clazz = BonusesBadRequestException.class;
        HttpStatus status = HttpStatus.BAD_REQUEST;

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_WRONG_AMOUNT,
                null, BonusesBadRequestErrorBodyHandler.TMPL_BONUSES_MORE_ZERO,
                new BonusesHandlerParamsBuilder().build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_WRONG_AMOUNT,
                null, BonusesBadRequestErrorBodyHandler.TMPL_MONEY_MORE_ZERO,
                new BonusesHandlerParamsBuilder().build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_WRONG_AMOUNT,
                null, BonusesBadRequestErrorBodyHandler.TMPL_BONUSES_OR_MONEY_NOT_NULL,
                new BonusesHandlerParamsBuilder().build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_WRONG_AMOUNT,
                null, BonusesBadRequestErrorBodyHandler.TMPL_TOTAL_MORE_ZERO,
                new BonusesHandlerParamsBuilder().build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_WRONG_AMOUNT,
                null, BonusesBadRequestErrorBodyHandler.TMPL_BONUSES_OR_MONEY_OR_TOTAL_NOT_NULL,
                new BonusesHandlerParamsBuilder().build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_WRONG_AMOUNT,
                null, BonusesBadRequestErrorBodyHandler.TMPL_TOTAL_NOT_NULL_OTHER_NULL,
                new BonusesHandlerParamsBuilder().addBonuses("1").addMoney("2").build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_WRONG_AMOUNT,
                null, BonusesBadRequestErrorBodyHandler.TMPL_WITHDRAW_NEGATIVE_IMPOSSIBLE,
                new BonusesHandlerParamsBuilder().addWithdraw("1").addAccountId("2").addOrderId("3").build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_WRONG_AMOUNT,
                null, BonusesBadRequestErrorBodyHandler.TMPL_WITHDRAW_NOT_ENOUGH,
                new BonusesHandlerParamsBuilder().addSum("1").addWithdraw("2").addAccountId("3").addOrderId("4").build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_WRONG_AMOUNT,
                null, BonusesBadRequestErrorBodyHandler.TMPL_TRN_TOTAL_NOT_NULL,
                new BonusesHandlerParamsBuilder().addTransactionId("1").addAmount("2").build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_WRONG_AMOUNT,
                null, BonusesBadRequestErrorBodyHandler.TMPL_BONUSES_MORE_ZERO_OR_NULL,
                new BonusesHandlerParamsBuilder().build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_WRONG_AMOUNT,
                null, BonusesBadRequestErrorBodyHandler.TMPL_MONEY_MORE_ZERO_OR_NULL,
                new BonusesHandlerParamsBuilder().build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_WRONG_AMOUNT,
                null, BonusesBadRequestErrorBodyHandler.TMPL_TRNS_BONUSES_DIF,
                new BonusesHandlerParamsBuilder().addTransactionId("1").addBonuses("2").addAmount("3").build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_WRONG_AMOUNT,
                null, BonusesBadRequestErrorBodyHandler.TMPL_TRNS_MONEY_DIF,
                new BonusesHandlerParamsBuilder().addTransactionId("1").addMoney("2").addAmount("3").build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_WRONG_AMOUNT,
                null, BonusesBadRequestErrorBodyHandler.TMPL_TRN_RET_BONUSES_WRONG,
                new BonusesHandlerParamsBuilder().addAccountId("1").addOrderId("2").addAmount("3").addTrnVal("4").addBonuses("5").build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_WRONG_AMOUNT,
                null, BonusesBadRequestErrorBodyHandler.TMPL_TRN_RET_MONEY_WRONG,
                new BonusesHandlerParamsBuilder().addAccountId("1").addOrderId("2").addAmount("3").addTrnVal("4").addMoney("5").build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_WRONG_AMOUNT,
                null, BonusesBadRequestErrorBodyHandler.TMPL_TRN_RET_UNKNOWN,
                new BonusesHandlerParamsBuilder().addAccountId("1").addOrderId("2").addAmount("3").build()
        );

        testBonusesErrorBodyHandler(
                clazz, status, BonusesBadRequestException.CODE_WRONG_AMOUNT,
                null, BonusesBadRequestErrorBodyHandler.TMPL_TRN_BONUSES_OR_MONEY_NOT_NULL,
                new BonusesHandlerParamsBuilder().addAccountId("1").addOrderId("2").build()
        );
    }

    private void testBonusesErrorBodyHandler(
            Class<?> clazz, HttpStatus status, String code, String type, String template, Map<String, String> params
    ) {
        testErrorBodyHandler(bonusesErrorHandler, clazz, status, code, type, template, params);
    }

    private void testLoyaltyCardsErrorBodyHandler(
            Class<?> clazz, HttpStatus status, String code, String type, String template, Map<String, String> params
    ) {
        testErrorBodyHandler(loyaltyCardsErrorHandler, clazz, status, code, type, template, params);
    }

    private void testErrorBodyHandler(
            BaseResponseErrorHandler handler, Class<?> clazz, HttpStatus status, String code, String type, String template, Map<String, String> params
    ) {
        try {
            handler.handleError(
                    new MockClientHttpResponse(getResponseBytes(createResponseBody(code, type, template, params)), status)
            );
            assertThat(false).isTrue();
        } catch (Exception e) {
            assertThat(clazz.isInstance(e)).isTrue();
            assertThat(e.getMessage()).isNotNull();
            assertThat(Objects.equals(e.getMessage(), messageSourceAccessor.getMessage(BonusesService.DEFAULT_UNEXPECTED_EXCEPTION))).isFalse();
            for (String param: params.keySet()) {
                assertThat(e.getMessage().contains(params.get(param))).isTrue();
            }
        }
    }

    private static class LoyaltyCardsHandlerParamsBuilder {
        private Map<String, String> params = new HashMap<>();
        Map<String, String> build() {
            Map<String, String> result = params;
            params = null;
            return result;
        }
        LoyaltyCardsHandlerParamsBuilder addUserId(String value) {
            addParamToMap(LoyaltyCardsAbstractErrorBodyHandler.PARAM_USER_ID, value);
            return this;
        }
        LoyaltyCardsHandlerParamsBuilder addCardId(String value) {
            addParamToMap(LoyaltyCardsAbstractErrorBodyHandler.PARAM_CARD_ID, value);
            return this;
        }
        LoyaltyCardsHandlerParamsBuilder addCardUserId(String value) {
            addParamToMap(LoyaltyCardsAbstractErrorBodyHandler.PARAM_CARD_USER_ID, value);
            return this;
        }
        LoyaltyCardsHandlerParamsBuilder addPhone(String value) {
            addParamToMap(LoyaltyCardsAbstractErrorBodyHandler.PARAM_PHONE, value);
            return this;
        }
        private void addParamToMap(String name, String value) {
            params.put(name, value);
        }
    }

    private static class BonusesHandlerParamsBuilder {
        private Map<String, String> params = new HashMap<>();
        Map<String, String> build() {
            Map<String, String> result = params;
            params = null;
            return result;
        }
        BonusesHandlerParamsBuilder addTransactionId(String value) {
            addParamToMap(BonusesNotFoundErrorBodyHandler.PARAM_TRANSACTION_ID, value);
            return this;
        }
        BonusesHandlerParamsBuilder addChildTransactionId(String value) {
            addParamToMap(BonusesNotFoundErrorBodyHandler.PARAM_CHILD_TRANSACTION_ID, value);
            return this;
        }
        BonusesHandlerParamsBuilder addChildrenSize(String value) {
            addParamToMap(BonusesNotFoundErrorBodyHandler.PARAM_CHILDREN_SIZE, value);
            return this;
        }
        BonusesHandlerParamsBuilder addItemsSize(String value) {
            addParamToMap(BonusesNotFoundErrorBodyHandler.PARAM_ITEMS_SIZE, value);
            return this;
        }
        BonusesHandlerParamsBuilder addBonuses(String value) {
            addParamToMap(BonusesNotFoundErrorBodyHandler.PARAM_BONUSES, value);
            return this;
        }
        BonusesHandlerParamsBuilder addMoney(String value) {
            addParamToMap(BonusesNotFoundErrorBodyHandler.PARAM_MONEY, value);
            return this;
        }
        BonusesHandlerParamsBuilder addWithdraw(String value) {
            addParamToMap(BonusesNotFoundErrorBodyHandler.PARAM_WITHDRAW, value);
            return this;
        }
        BonusesHandlerParamsBuilder addSum(String value) {
            addParamToMap(BonusesNotFoundErrorBodyHandler.PARAM_SUM, value);
            return this;
        }
        BonusesHandlerParamsBuilder addTrnVal(String value) {
            addParamToMap(BonusesNotFoundErrorBodyHandler.PARAM_TRN_VAL, value);
            return this;
        }
        BonusesHandlerParamsBuilder addAmount(String value) {
            addParamToMap(BonusesNotFoundErrorBodyHandler.PARAM_AMOUNT, value);
            return this;
        }
        BonusesHandlerParamsBuilder addState(String value) {
            addParamToMap(BonusesNotFoundErrorBodyHandler.PARAM_STATE, value);
            return this;
        }
        BonusesHandlerParamsBuilder addAccountId(String value) {
            addParamToMap(BonusesNotFoundErrorBodyHandler.PARAM_ACCOUNT_ID, value);
            return this;
        }
        BonusesHandlerParamsBuilder addOrderId(String value) {
            addParamToMap(BonusesNotFoundErrorBodyHandler.PARAM_ORDER_ID, value);
            return this;
        }
        BonusesHandlerParamsBuilder addCode(String value) {
            addParamToMap(BonusesNotFoundErrorBodyHandler.PARAM_CODE, value);
            return this;
        }
        BonusesHandlerParamsBuilder addIdempotencyKey(String value) {
            addParamToMap(BonusesNotFoundErrorBodyHandler.PARAM_IDEMPOTENCY_KEY, value);
            return this;
        }
        BonusesHandlerParamsBuilder addSize(String value) {
            addParamToMap(BonusesNotFoundErrorBodyHandler.PARAM_SIZE, value);
            return this;
        }
        private void addParamToMap(String name, String value) {
            params.put(name, value);
        }
    }

    private ResponseBody createResponseBody(
            String code, String type, String template, Map<String, String> params
    ) {
        return new ResponseBody(
                new ResponseBody.ResponseErrorBody(
                        code,
                        new HashMap<String, Object>() {{
                            put(
                                    "params", params
                            );
                            if (type != null) {
                                put("type", type);
                            }
                            if (template != null) {
                                put("template", template);
                            }
                        }},
                        "msg",
                        null
                )
        );
    }

    private byte[] getResponseBytes(ResponseBody response) {
        try {
            return objectMapper.writeValueAsBytes(response);
        } catch (Exception e) {
        }
        throw new RuntimeException();
    }
}
