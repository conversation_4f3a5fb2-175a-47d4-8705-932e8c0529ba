package ru.oskelly.tests.pr.suite1_3.domain.service.order.track;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import lombok.NonNull;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import su.reddot.domain.service.currency.DefaultCurrencyConverter;
import su.reddot.domain.service.dto.CurrencyDTO;
import su.reddot.domain.service.order.impl.OrderRequestContext;
import su.reddot.oskelly.orderprocessing.internal.web.dto.IntegrationMobileOrderExpertiseDTO;

import java.io.File;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public abstract class OrderProcessorExpertiseMockedBaseTest {

    public static final String RUB_CURRENCY_SIGN = "₽";
    private static final CurrencyDTO RUB_CURRENCY = new CurrencyDTO()
        .setId(1L)
        .setName("Russian ruble")
        .setSign(RUB_CURRENCY_SIGN)
        .setIsoCode("RUB")
        .setIsoNumber(643);

    private static final ObjectMapper mapper = new ObjectMapper()
            .registerModule(new JavaTimeModule());

    private static final String BASE_SNIPPETS_PATH = "/json/orderprocessingexpertise";

    protected static final OrderRequestContext BUYER_CONTEXT = OrderRequestContext.builder()
            .isForBuyer(true)
            .isForSeller(false)
            .isPro(false)
            .currencyConverter(new DefaultCurrencyConverter(RUB_CURRENCY, BigDecimal.ONE))
            .build();

    protected static final OrderRequestContext SELLER_CONTEXT = OrderRequestContext.builder()
            .isForBuyer(false)
            .isForSeller(true)
            .isPro(false)
            .currencyConverter(new DefaultCurrencyConverter(RUB_CURRENCY, BigDecimal.ONE))
            .build();

    protected static final OrderRequestContext PRO_SELLER_CONTEXT = OrderRequestContext.builder()
            .isForBuyer(false)
            .isForSeller(false)
            .isPro(true)    // TODO: Strange, talk to somebody
            .currencyConverter(new DefaultCurrencyConverter(RUB_CURRENCY, BigDecimal.ONE))
            .build();

    @NonNull
    @SneakyThrows
    protected final IntegrationMobileOrderExpertiseDTO loadSnippetByStartName(
        @NonNull final String context,
        @NonNull final String snippetStartWith
    ) {
        Path basePath = Paths.get(BASE_SNIPPETS_PATH, context);
        List<File> foundFilePaths = Arrays
            .stream(
                Objects.requireNonNull(new ClassPathResource(basePath.toString())
                    .getFile()
                    .listFiles()
                )
            )
            .filter(file -> file.getName().startsWith(snippetStartWith))
            .collect(Collectors.toList());

        if (foundFilePaths.size() > 1) {
            throw new IllegalArgumentException("several files found");
        }
        if (foundFilePaths.size() == 0) {
            throw new IllegalArgumentException("no files found");
        }
        final Resource resource = new ClassPathResource(
            foundFilePaths.get(0).toString()
        );
        final String json = IOUtils.toString(foundFilePaths.get(0).toURI(), StandardCharsets.UTF_8);
        return mapper.readValue(json, new TypeReference<IntegrationMobileOrderExpertiseDTO>() {
        });
    }

    @NonNull
    @SneakyThrows
    protected final IntegrationMobileOrderExpertiseDTO loadSnippetByName(
        @NonNull final String fullRelativeName
    ) {
        final Resource resource = new ClassPathResource(
            Paths.get(BASE_SNIPPETS_PATH, fullRelativeName).toString()
        );
        final String json = IOUtils.toString(resource.getURI(), StandardCharsets.UTF_8);
        return mapper.readValue(json, new TypeReference<IntegrationMobileOrderExpertiseDTO>() {
        });
    }

    @NonNull
    @SneakyThrows
    protected final List<String> findAllSnippets(
        @NonNull final String context,
        @NonNull final String snippetStartWith
    ) {
        List<String> fileNames = new ArrayList<>();
        recursive(fileNames, context, snippetStartWith);
        return fileNames.stream()
            .map(name -> name.replace(context + "/", ""))
            .collect(Collectors.toList());
    }

    @SneakyThrows
    private void recursive(List<String> fileNames, String name, String snippetStartWith) {
        Path basePath = Paths.get(BASE_SNIPPETS_PATH, name);
        File baseFile = new ClassPathResource(basePath.toString()).getFile();
        if (baseFile.isDirectory()) {
            Arrays.stream(Objects.requireNonNull(baseFile.listFiles())).forEach(file -> {
                recursive(fileNames, Paths.get(name, file.getName()).toString(), snippetStartWith);
            });
        } else {
            if (name.contains(snippetStartWith)) {
                fileNames.add(name);
            }
        }
    }
}
