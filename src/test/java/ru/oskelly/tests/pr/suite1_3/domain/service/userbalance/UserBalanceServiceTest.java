package ru.oskelly.tests.pr.suite1_3.domain.service.userbalance;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.exception.userbalance.UserBalanceChangeOperationExistsException;
import su.reddot.domain.model.userbalance.UserBalanceChange;
import su.reddot.domain.model.userbalance.UserBalanceChangeMode;
import su.reddot.domain.service.dto.userbalance.UserBalanceChangeParams;
import su.reddot.domain.service.userbalance.UserBalanceService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

@TestMethodOrder(MethodOrderer.MethodName.class)
@ContextConfiguration(classes = {OrderFlowTestUtils.class})
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class UserBalanceServiceTest extends AbstractSpringTest {

    @Autowired
    private UserBalanceService userBalanceService;

    private final long userId = 838;

    private String getUniqueOperationId(String operationId) {
        return operationId + "-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
    }

    private void _01_UserBalance_Credit_Validate(BigDecimal creditAmount) {
        BigDecimal initialBalance = userBalanceService.getBalance(userId);
        //
        UserBalanceChange userBalanceChange = userBalanceService.credit(UserBalanceChangeParams.builder()
                .userId(userId)
                .amount(creditAmount)
                .operationId(getUniqueOperationId("_01_UserBalance_Credit_Validate"))
                .comment("_01_UserBalance_Credit_Validate")
                .objectId("_01_UserBalance_Credit_Validate_ObjectId")
                .accountId("_01_UserBalance_Credit_Validate_AccountId")
                .userBalanceChangeMode(UserBalanceChangeMode.AUTO).build());
        Assertions.assertThat(userBalanceChange.getObjectId()).isEqualTo("_01_UserBalance_Credit_Validate_ObjectId");
        Assertions.assertThat(userBalanceChange.getAccountId()).isEqualTo("_01_UserBalance_Credit_Validate_AccountId");
        //
        BigDecimal changedBalance = userBalanceService.getBalance(userId);
        Assertions.assertThat(initialBalance.add(creditAmount)).isEqualByComparingTo(changedBalance);
    }

    @Test
    public void _01_UserBalance_Credit_Okay() {
        _01_UserBalance_Credit_Validate(BigDecimal.valueOf(1_23, 2));
        _01_UserBalance_Credit_Validate(BigDecimal.valueOf(1_234_56, 2));
    }

    private void _02_UserBalance_Debit_Validate(BigDecimal debitAmount) {
        BigDecimal initialBalance = userBalanceService.getBalance(userId);
        //
        UserBalanceChange userBalanceChange = userBalanceService.debit(UserBalanceChangeParams.builder()
                .userId(userId)
                .amount(debitAmount)
                .operationId(getUniqueOperationId("_02_UserBalance_Debit_Validate"))
                .comment("_02_UserBalance_Debit_Validate")
                .objectId("_02_UserBalance_Debit_Validate_ObjectId")
                .accountId("_02_UserBalance_Debit_Validate_AccountId")
                .userBalanceChangeMode(UserBalanceChangeMode.AUTO)
                .build());
        Assertions.assertThat(userBalanceChange.getObjectId()).isEqualTo("_02_UserBalance_Debit_Validate_ObjectId");
        Assertions.assertThat(userBalanceChange.getAccountId()).isEqualTo("_02_UserBalance_Debit_Validate_AccountId");
        //
        BigDecimal changedBalance = userBalanceService.getBalance(userId);
        Assertions.assertThat(initialBalance.subtract(debitAmount)).isEqualByComparingTo(changedBalance);
    }

    @Test
    public void _02_UserBalance_Debit_Okay() {
        _02_UserBalance_Debit_Validate(BigDecimal.valueOf(2_46, 2));
        _02_UserBalance_Debit_Validate(BigDecimal.valueOf(4_321_01, 2));
    }

    @Test
    public void _03_UserBalance_SameOperationsId_OnDebit_Fail() {
        String debitOpId = getUniqueOperationId("debit-one");
        userBalanceService.debit(UserBalanceChangeParams.builder()
                .userId(userId)
                .amount(BigDecimal.ONE)
                .operationId(debitOpId)
                .comment("_03_UserBalance_SameOperationsId_OnDebit_Fail")
                .userBalanceChangeMode(UserBalanceChangeMode.AUTO)
                .build());
        Assertions.assertThatExceptionOfType(UserBalanceChangeOperationExistsException.class)
                .isThrownBy(() -> userBalanceService.debit(UserBalanceChangeParams.builder()
                        .userId(userId)
                        .amount(BigDecimal.ONE)
                        .operationId(debitOpId)
                        .comment("_03_UserBalance_SameOperationsId_OnDebit_Fail")
                        .userBalanceChangeMode(UserBalanceChangeMode.AUTO)
                        .build()));
        Assertions.assertThatExceptionOfType(UserBalanceChangeOperationExistsException.class)
                .isThrownBy(() -> userBalanceService.credit(UserBalanceChangeParams.builder()
                        .userId(userId)
                        .amount(BigDecimal.ONE)
                        .operationId(debitOpId)
                        .comment("_03_UserBalance_SameOperationsId_OnDebit_Fail")
                        .userBalanceChangeMode(UserBalanceChangeMode.AUTO)
                        .build()));
    }

    @Test
    public void _04_UserBalance_SameOperationsId_OnCredit_Fail() {
        String debitOpId = getUniqueOperationId("credit-one");
        userBalanceService.credit(UserBalanceChangeParams.builder()
                .userId(userId)
                .amount(BigDecimal.ONE)
                .operationId(debitOpId)
                .comment("_04_UserBalance_SameOperationsId_OnCredit_Fail")
                .userBalanceChangeMode(UserBalanceChangeMode.AUTO)
                .build());
        Assertions.assertThatExceptionOfType(UserBalanceChangeOperationExistsException.class)
                .isThrownBy(() -> userBalanceService.debit(UserBalanceChangeParams.builder()
                        .userId(userId)
                        .amount(BigDecimal.ONE)
                        .operationId(debitOpId)
                        .comment("_04_UserBalance_SameOperationsId_OnCredit_Fail")
                        .userBalanceChangeMode(UserBalanceChangeMode.AUTO)
                        .build()));
        Assertions.assertThatExceptionOfType(UserBalanceChangeOperationExistsException.class)
                .isThrownBy(() -> userBalanceService.credit(UserBalanceChangeParams.builder()
                        .userId(userId)
                        .amount(BigDecimal.ONE)
                        .operationId(debitOpId)
                        .comment("_04_UserBalance_SameOperationsId_OnCredit_Fail")
                        .userBalanceChangeMode(UserBalanceChangeMode.AUTO)
                        .build()));
    }

    @Test
    public void _05_UserBalance_NotExistentUserGetsZeroBalance() {
        BigDecimal maxValueBalance = userBalanceService.getBalance(Long.MAX_VALUE);
        Assertions.assertThat(maxValueBalance).isEqualByComparingTo(BigDecimal.ZERO);
        BigDecimal minValueBalance = userBalanceService.getBalance(Long.MAX_VALUE);
        Assertions.assertThat(minValueBalance).isEqualByComparingTo(BigDecimal.ZERO);
    }

    @Test
    public void _06_UserBalance_OperationsCommentsOkay() {
        String creditOpId = getUniqueOperationId("credit-comment");
        userBalanceService.credit(UserBalanceChangeParams.builder()
                .userId(userId)
                .amount(BigDecimal.ONE)
                .operationId(creditOpId)
                .comment("creditComment")
                .objectId("_06_UserBalance_OperationsCommentsOkay_CreditObjectId")
                .accountId("_06_UserBalance_OperationsCommentsOkay_CreditAccountId")
                .userBalanceChangeMode(UserBalanceChangeMode.AUTO)
                .build());
        Optional<UserBalanceChange> creditOperation = userBalanceService.getOperationById(creditOpId);
        Assertions.assertThat(creditOperation).isPresent();
        Assertions.assertThat(creditOperation.get().getComment()).isEqualTo("creditComment");
        Assertions.assertThat(creditOperation.get().getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
        Assertions.assertThat(creditOperation.get().getObjectId()).isEqualTo("_06_UserBalance_OperationsCommentsOkay_CreditObjectId");
        Assertions.assertThat(creditOperation.get().getAccountId()).isEqualTo("_06_UserBalance_OperationsCommentsOkay_CreditAccountId");
        //
        String debitOpId = getUniqueOperationId("debit-comment");
        userBalanceService.credit(UserBalanceChangeParams.builder()
                .userId(userId)
                .amount(BigDecimal.ONE)
                .operationId(debitOpId)
                .comment("debitComment")
                .objectId("_06_UserBalance_OperationsCommentsOkay_DebitObjectId")
                .accountId("_06_UserBalance_OperationsCommentsOkay_DebitAccountId")
                .userBalanceChangeMode(UserBalanceChangeMode.AUTO)
                .build());
        Optional<UserBalanceChange> debitOperation = userBalanceService.getOperationById(debitOpId);
        Assertions.assertThat(debitOperation).isPresent();
        Assertions.assertThat(debitOperation.get().getComment()).isEqualTo("debitComment");
        Assertions.assertThat(debitOperation.get().getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
        Assertions.assertThat(debitOperation.get().getObjectId()).isEqualTo("_06_UserBalance_OperationsCommentsOkay_DebitObjectId");
        Assertions.assertThat(debitOperation.get().getAccountId()).isEqualTo("_06_UserBalance_OperationsCommentsOkay_DebitAccountId");
    }

}
