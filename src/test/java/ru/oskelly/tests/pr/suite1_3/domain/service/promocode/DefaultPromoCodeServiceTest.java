package ru.oskelly.tests.pr.suite1_3.domain.service.promocode;

import lombok.val;
import org.apache.commons.lang3.RandomStringUtils;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.applyrule.ApplyRuleRepository;
import su.reddot.domain.dao.discount.PromoCodeRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.exception.PromoCodeRequestException;
import su.reddot.domain.model.Authority;
import su.reddot.domain.model.applyRule.ApplyRule;
import su.reddot.domain.model.applyRule.ApplyRuleFilterType;
import su.reddot.domain.model.applyRule.ApplyRuleFilterValue;
import su.reddot.domain.model.applyRule.FilterRelation;
import su.reddot.domain.model.device.DeviceDtype;
import su.reddot.domain.model.discount.PromoCode;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.model.user.UserAuthorityBinding;
import su.reddot.domain.service.adminpanel.tag.UserCommonTagService;
import su.reddot.domain.service.adminpanel.tag.domain.UserCommonTagDTO;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.domain.service.device.DeviceService;
import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.dto.PageRequest;
import su.reddot.domain.service.dto.promocode.PromocodeDTOFull;
import su.reddot.domain.service.dto.promocode.PromocodeFilterDTO;
import su.reddot.domain.service.dto.promocode.PromocodeReferenceLinkDTO;
import su.reddot.domain.service.promocode.PromoCodeService;
import su.reddot.domain.service.promocode.exception.PromoCodeAccessException;
import su.reddot.domain.service.promocode.exception.PromoCodeEditException;
import su.reddot.domain.service.promocode.model.PromoCodeApplicability;
import su.reddot.domain.service.promocode.model.PromoCodeAppliedNumberType;
import su.reddot.domain.service.promocode.model.PromoCodeResetRange;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.security.SecurityService;

import java.math.BigDecimal;
import java.time.Clock;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Stream;

import static java.util.Collections.singletonList;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.assertj.core.api.Assertions.failBecauseExceptionWasNotThrown;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;
import static ru.oskelly.tests.pr.suite1_3.domain.service.promocode.PromocodeTestUtil.COPY_POSTFIX;
import static ru.oskelly.tests.pr.suite1_3.domain.service.promocode.PromocodeTestUtil.EXPECTED_DUPLICATE_ERROR_PART_1;
import static ru.oskelly.tests.pr.suite1_3.domain.service.promocode.PromocodeTestUtil.EXPECTED_DUPLICATE_ERROR_PART_2;
import static ru.oskelly.tests.pr.suite1_3.domain.service.promocode.PromocodeTestUtil.TEST_GENERATED_VALUE_AA003;
import static ru.oskelly.tests.pr.suite1_3.domain.service.promocode.PromocodeTestUtil.TEST_GENERATED_VALUE_AA004;
import static ru.oskelly.tests.pr.suite1_3.domain.service.promocode.PromocodeTestUtil.TEST_GENERATED_VALUE_AA005;
import static ru.oskelly.tests.pr.suite1_3.domain.service.promocode.PromocodeTestUtil.TEST_GENERATED_VALUE_AA1;
import static ru.oskelly.tests.pr.suite1_3.domain.service.promocode.PromocodeTestUtil.TEST_GENERATED_VALUE_AA2;
import static ru.oskelly.tests.pr.suite1_3.domain.service.promocode.PromocodeTestUtil.TEST_PROMO_CODE;
import static ru.oskelly.tests.pr.suite1_3.domain.service.promocode.PromocodeTestUtil.TEST_PROMO_CODE_2;
import static ru.oskelly.tests.pr.suite1_3.domain.service.promocode.PromocodeTestUtil.TEST_PROMO_CODE_3;
import static ru.oskelly.tests.pr.suite1_3.domain.service.promocode.PromocodeTestUtil.TEST_PROMO_CODE_GENERATED_PREFIX;
import static ru.oskelly.tests.pr.suite1_3.domain.service.promocode.PromocodeTestUtil.TEST_PROMO_CODE_UPPER;
import static ru.oskelly.tests.pr.suite1_3.domain.service.promocode.PromocodeTestUtil.TEST_PROMO_CODE_UPPER_WITH_SPACE;

@SpringBootTest(classes = {OskellyApplication.class, TestConfig.class})
@ExtendWith(SpringExtension.class)
@ActiveProfiles(AbstractSpringTest.testProfiles)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
class DefaultPromoCodeServiceTest {
    @Autowired
    PromoCodeService defaultPromocodeService;
    @Autowired
    PromocodeTestUtil promocodeTestUtil;

    @Autowired
    ApplyRuleRepository applyRuleRepository;
    @Autowired
    PromoCodeRepository promoCodeRepository;

    @Autowired
    OrderRepository orderRepository;
    @Autowired
    UserRepository userRepository;
    @MockBean
    SecurityService securityService;
    @MockBean
    UserCommonTagService userCommonTagService;
    @MockBean
    Clock clock;
    @MockBean
    DeviceService deviceService;

    @Autowired
    CommissionGridService commissionGridService;

    private User currentUser;
    private User anotherUser;

    @BeforeEach
    void init() {
        when(clock.instant())
            .thenReturn(LocalDateTime.now().toInstant(ZoneOffset.UTC));
        when(clock.getZone())
            .thenReturn(ZoneOffset.UTC);
        currentUser = createAndSaveRandomUser();
        anotherUser = createAndSaveRandomUser();

        lenient().when(deviceService.getCurrentDeviceInfo())
            .thenReturn(new DeviceService.DeviceInfo()
                .setDeviceDtype(DeviceDtype.OtherDevice));
    }

    private @NotNull User createAndSaveRandomUser() {
        return userRepository.saveAndFlush(new User()
            .setNickname(RandomStringUtils.randomAlphabetic(5))
            .setUserType(User.UserType.SIMPLE_USER)
            .setCommissionGrid(commissionGridService.getDefaultCommissionGrid())
            .setChangeTime(LocalDateTime.now()));
    }

    @AfterEach
    void cleanup() {
        applyRuleRepository.deleteAll();
        orderRepository.deleteAll(orderRepository.findAllBuyersOrders(currentUser));
        orderRepository.deleteAll(orderRepository.findAllBuyersOrders(anotherUser));
        promoCodeRepository.findFirstByCode(TEST_PROMO_CODE_3).stream().findFirst().ifPresent(promoCodeRepository::delete);
        promoCodeRepository.findFirstByCode(TEST_PROMO_CODE_2).stream().findFirst().ifPresent(promoCodeRepository::delete);
        promoCodeRepository.findFirstByCode(TEST_PROMO_CODE).stream().findFirst().ifPresent(promoCodeRepository::delete);
        promoCodeRepository.findFirstByCode(TEST_PROMO_CODE + COPY_POSTFIX + "1").stream().findFirst().ifPresent(promoCodeRepository::delete);
        promoCodeRepository.findFirstByCode(TEST_PROMO_CODE + COPY_POSTFIX + "2").stream().findFirst().ifPresent(promoCodeRepository::delete);
        promoCodeRepository.findFirstByCode(TEST_PROMO_CODE + COPY_POSTFIX + "1" + COPY_POSTFIX + "1").stream().findFirst().ifPresent(promoCodeRepository::delete);
        promoCodeRepository.findFirstByCode(TEST_PROMO_CODE_UPPER).stream().findFirst().ifPresent(promoCodeRepository::delete);
        promoCodeRepository.findFirstByCode(TEST_PROMO_CODE_UPPER_WITH_SPACE).stream().findFirst().ifPresent(promoCodeRepository::delete);
        promoCodeRepository.findFirstByCode(TEST_PROMO_CODE_GENERATED_PREFIX + TEST_GENERATED_VALUE_AA1).stream().findFirst().ifPresent(promoCodeRepository::delete);
        promoCodeRepository.findFirstByCode(TEST_PROMO_CODE_GENERATED_PREFIX + TEST_GENERATED_VALUE_AA2).stream().findFirst().ifPresent(promoCodeRepository::delete);
        promoCodeRepository.findFirstByCode(TEST_PROMO_CODE_GENERATED_PREFIX + TEST_GENERATED_VALUE_AA003).stream().findFirst().ifPresent(promoCodeRepository::delete);
        promoCodeRepository.findFirstByCode(TEST_PROMO_CODE_GENERATED_PREFIX + TEST_GENERATED_VALUE_AA004).stream().findFirst().ifPresent(promoCodeRepository::delete);
        promoCodeRepository.findFirstByCode(TEST_PROMO_CODE_GENERATED_PREFIX + TEST_GENERATED_VALUE_AA005).stream().findFirst().ifPresent(promoCodeRepository::delete);

        userRepository.deleteById(currentUser.getId());
        userRepository.deleteById(anotherUser.getId());
    }

    @Test
    @DisplayName("Клонирование промокода без переноса статистики и прошлого пользователя")
    void clonePromoCode() {
        // Arrange
        val expectedFilterValue = -1L;
        final PromoCode existingPromoCode = promocodeTestUtil.createPromoCodeWithOrderAmountFilter(expectedFilterValue, FilterRelation.EQUALS);

        // Act
        final PromoCode clonedPromoCode = defaultPromocodeService.clonePromoCode(existingPromoCode.getId());
        final PromoCode clonedPromoCode2 = defaultPromocodeService.clonePromoCode(existingPromoCode.getId());
        final PromoCode doubleClonedPromoCode = defaultPromocodeService.clonePromoCode(clonedPromoCode.getId());

        // Assert
        assertThat(clonedPromoCode.getCode()).isEqualTo(TEST_PROMO_CODE + COPY_POSTFIX + "1");
        assertThat(clonedPromoCode2.getCode()).isEqualTo(TEST_PROMO_CODE + COPY_POSTFIX + "2");
        assertThat(doubleClonedPromoCode.getCode()).isEqualTo(TEST_PROMO_CODE + COPY_POSTFIX + "1" + COPY_POSTFIX + "1");

        final Set<ApplyRuleFilterValue> filterValues = applyRuleRepository.findById(clonedPromoCode.getApplyRuleId())
            .orElseThrow(() -> new RuntimeException("ApplyRule not found by given ID"))
            .getFilterValues();
        assertThat(filterValues).hasSize(3);

        final ApplyRuleFilterValue orderCountRule = filterValues.stream()
            .filter(filterValue -> filterValue.getFilterType().equals(ApplyRuleFilterType.ORDER_AMOUNT))
            .findFirst()
            .orElseThrow(() -> new RuntimeException("ApplyRuleFilterValue with type ORDER_AMOUNT not found"));

        assertThat(orderCountRule.getFilterType()).isEqualTo(ApplyRuleFilterType.ORDER_AMOUNT);
        assertThat(orderCountRule.getValue()).isEqualTo(expectedFilterValue);
        assertThat(orderCountRule.getRelation()).isEqualByComparingTo(FilterRelation.EQUALS);
    }

    // region CreatePromoCodeTests

    @Test
    void generatePromoCode() {
        val discount = new BigDecimal("5.0");
        val beginPrice = new BigDecimal("50000.0");
        val durationDays = 14;
        final Set<PromocodeReferenceLinkDTO> exceptSellers = new HashSet<>();
        final Set<PromocodeReferenceLinkDTO> sellers = new HashSet<>();
        val d1 = ZonedDateTime.now();
        val description = "generated";
        Integer typeId = null;

        //Базовй тест
        final PromoCode pc1 = defaultPromocodeService.generateAndSavePromoCode(TEST_PROMO_CODE_GENERATED_PREFIX, discount, beginPrice, durationDays, null, exceptSellers, d1, description, typeId);
        assertThat(pc1.getCode()).isEqualTo(TEST_PROMO_CODE_GENERATED_PREFIX + TEST_GENERATED_VALUE_AA1);
        assertThat(pc1.getApplyRuleId()).isNotNull(); // Т.к. флаги устройства в любом случае создают правило
        final ApplyRule ar1 = applyRuleRepository.findById(pc1.getApplyRuleId())
            .orElseThrow(() -> new RuntimeException("ApplyRule not found by given ID"));
        assertThat(ar1.getFilterValues()).hasSize(2); //Есть только правила устройств, т.к. список продавцов для исключения пуст изначально
        assertThat(ar1.getFilterValues())
            .extracting(ApplyRuleFilterValue::getValue)
            .containsExactlyInAnyOrder(1L, 1L); // по умолчанию разрешено всем устройствам

        //Тест с исключением продавцов
        val exceptSeller = new PromocodeReferenceLinkDTO();
        exceptSeller.setId(1234567890L);
        exceptSellers.add(exceptSeller);
        final PromoCode pc2 = defaultPromocodeService.generateAndSavePromoCode(TEST_PROMO_CODE_GENERATED_PREFIX, discount, beginPrice, durationDays, null, exceptSellers, d1, description, typeId);
        assertThat(pc2.getCode()).isEqualTo(TEST_PROMO_CODE_GENERATED_PREFIX + TEST_GENERATED_VALUE_AA2);
        assertThat(pc2.getApplyRuleId()).isNotNull();
        final ApplyRule ar2 = applyRuleRepository.findById(pc2.getApplyRuleId())
            .orElseThrow(() -> new RuntimeException("ApplyRule not found by given ID"));
        assertThat(ar2.getFilterValues()).hasSize(3);

        //Тест с включением продавцов
        val seller = new PromocodeReferenceLinkDTO();
        seller.setId(2234567890L);
        sellers.add(seller);
        final PromoCode pc3 = defaultPromocodeService.generateAndSavePromoCode(TEST_PROMO_CODE_GENERATED_PREFIX, discount, beginPrice, durationDays, sellers, null, d1, description, typeId);
        assertThat(pc3.getCode()).isEqualTo(TEST_PROMO_CODE_GENERATED_PREFIX + TEST_GENERATED_VALUE_AA003);
        assertThat(pc3.getApplyRuleId()).isNotNull();
        final ApplyRule ar3 = applyRuleRepository.findById(pc3.getApplyRuleId())
            .orElseThrow(() -> new RuntimeException("ApplyRule not found by given ID"));
        assertThat(ar3.getFilterValues()).hasSize(3);

        //Тест на генератор случайных чисел, который два раза подряд выдает одно и то же значение
        exceptSellers.clear();
        final PromoCode pc4 = defaultPromocodeService.generateAndSavePromoCode(TEST_PROMO_CODE_GENERATED_PREFIX, discount, beginPrice, durationDays, null, exceptSellers, d1, description, typeId);
        assertThat(pc4.getCode()).isEqualTo(TEST_PROMO_CODE_GENERATED_PREFIX + TEST_GENERATED_VALUE_AA004);
        final PromoCode pc5 = defaultPromocodeService.generateAndSavePromoCode(TEST_PROMO_CODE_GENERATED_PREFIX, discount, beginPrice, durationDays, null, exceptSellers, d1, description, typeId);
        assertThat(pc5.getCode()).isEqualTo(TEST_PROMO_CODE_GENERATED_PREFIX + TEST_GENERATED_VALUE_AA005);
    }

    /**
     * {@link DefaultPromoCodeServiceTest#createPromoCodeWithDifferentFilters}
     */
    private static Stream<Arguments> createDifferentFiltersProvider() {
        final long expectedFilterValue = -1L;
        return Stream.of(
            Arguments.of( // 1
                PromocodeTestUtil.createDefaultPromocodeDTOFull()
                    .setOrdersCountFilters(Collections.singleton(new PromocodeFilterDTO(expectedFilterValue, FilterRelation.EQUALS))),
                ApplyRuleFilterType.BUYER_ORDERS_COUNT),
            Arguments.of( // 2
                PromocodeTestUtil.createDefaultPromocodeDTOFull()
                    .setExceptCategoriesList(Collections.singleton(new PromocodeReferenceLinkDTO(expectedFilterValue, "name"))),
                ApplyRuleFilterType.EXCEPT_CATEGORY),
            Arguments.of( // 3
                PromocodeTestUtil.createDefaultPromocodeDTOFull()
                    .setExceptBrandsList(Collections.singleton(new PromocodeReferenceLinkDTO(expectedFilterValue, "name"))),
                ApplyRuleFilterType.EXCEPT_BRAND),
            Arguments.of( // 4
                PromocodeTestUtil.createDefaultPromocodeDTOFull()
                    .setOrdersRelativeNumbers(Collections.singleton(new PromocodeFilterDTO().setValue(expectedFilterValue))),
                ApplyRuleFilterType.BUYER_ORDER_NUMBERS),
            Arguments.of( // 5
                PromocodeTestUtil.createDefaultPromocodeDTOFull()
                    .setExceptProductList(Collections.singleton(new PromocodeReferenceLinkDTO(expectedFilterValue, "name"))),
                ApplyRuleFilterType.EXCEPT_PRODUCTS),
            Arguments.of( // 6
                PromocodeTestUtil.createDefaultPromocodeDTOFull()
                    .setProductList(Collections.singleton(new PromocodeReferenceLinkDTO(expectedFilterValue, "name"))),
                ApplyRuleFilterType.PRODUCTS),
            Arguments.of( // 7
                PromocodeTestUtil.createDefaultPromocodeDTOFull()
                    .setOrderAmountFilters(Collections.singleton(new PromocodeFilterDTO(expectedFilterValue, FilterRelation.EQUALS))),
                ApplyRuleFilterType.ORDER_AMOUNT),
            Arguments.of( // 8
                PromocodeTestUtil.createDefaultPromocodeDTOFull()
                    .setOrdersCountFilters(Collections.singleton(new PromocodeFilterDTO(expectedFilterValue, FilterRelation.EQUALS))),
                ApplyRuleFilterType.BUYER_ORDERS_COUNT),
            Arguments.of( // 9
                PromocodeTestUtil.createDefaultPromocodeDTOFull()
                    .setProductConditionList(Collections.singleton(new PromocodeReferenceLinkDTO(expectedFilterValue, "name"))),
                ApplyRuleFilterType.PRODUCT_CONDITION),
            Arguments.of( // 10
                PromocodeTestUtil.createDefaultPromocodeDTOFull()
                    .setExceptProductConditionList(Collections.singleton(new PromocodeReferenceLinkDTO(expectedFilterValue, "name"))),
                ApplyRuleFilterType.EXCEPT_PRODUCT_CONDITION),
            Arguments.of( // 11
                PromocodeTestUtil.createDefaultPromocodeDTOFull()
                    .setUserLoyaltyTagList(Collections.singleton(new PromocodeReferenceLinkDTO(expectedFilterValue, "name"))),
                ApplyRuleFilterType.USER_LOYALTY_TAG)
        );
    }

    @ParameterizedTest
    @MethodSource("createDifferentFiltersProvider")
    @DisplayName("Создание промокода с разными фильтрами")
    void createPromoCodeWithDifferentFilters(PromocodeDTOFull promocodeDTOFull, ApplyRuleFilterType expectedRuleFilterType) {
        // Act
        final PromoCode promoCode = defaultPromocodeService.createPromoCode(promocodeDTOFull);

        // Assert
        final Set<ApplyRuleFilterValue> filterValues = applyRuleRepository.findById(promoCode.getApplyRuleId())
            .orElseThrow(() -> new RuntimeException("ApplyRule not found by given ID"))
            .getFilterValues();

        assertThat(filterValues).hasSize(3);

        assertThat(filterValues)
            .extracting(ApplyRuleFilterValue::getFilterType)
            .containsExactlyInAnyOrder(expectedRuleFilterType, ApplyRuleFilterType.IOS_APPLICATION, ApplyRuleFilterType.ANDROID_APPLICATION);

        assertThat(filterValues)
            .extracting(ApplyRuleFilterValue::getValue)
            .containsExactlyInAnyOrder(0L, 0L, -1L); // два ноля это стандартные значения для типов устройств, -1L - ожидаемое значение создаваемого фильтра
    }

    /**
     * {@link DefaultPromoCodeServiceTest#createPromoCodeWithApplicationFilter}
     */
    private static Stream<PromocodeDTOFull> applicationFilterProvider() {
        return Stream.of(
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setIosApplication(true),
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setAndroidApplication(true)
        );
    }

    @ParameterizedTest
    @MethodSource("applicationFilterProvider")
    @DisplayName("Создание промокода с фильтрами по устройствам")
    void createPromoCodeWithApplicationFilter(PromocodeDTOFull promocodeDTOFull) {
        // Act
        final PromoCode promoCode = defaultPromocodeService.createPromoCode(promocodeDTOFull);

        // Assert
        final Set<ApplyRuleFilterValue> filterValues = applyRuleRepository.findById(promoCode.getApplyRuleId())
            .orElseThrow(() -> new RuntimeException("ApplyRule not found by given ID"))
            .getFilterValues();

        assertThat(filterValues).hasSize(2);
        assertThat(filterValues)
            .extracting(ApplyRuleFilterValue::getFilterType)
            .containsExactlyInAnyOrder(ApplyRuleFilterType.IOS_APPLICATION, ApplyRuleFilterType.ANDROID_APPLICATION);
        assertThat(filterValues)
            .extracting(ApplyRuleFilterValue::getValue)
            .containsExactlyInAnyOrder(0L, 1L);
    }

    @Test
    @DisplayName("Попытка обновления промокода другим пользователем генерирует ошибку доступа")
    void noAccessUpdateOtherUserPromoCode() {
        // Arrange
        when(securityService.getCurrentAuthorizedUser())
            .thenReturn(currentUser, new User().setId(2L));

        val promocodeDTOFull = PromocodeTestUtil.createDefaultPromocodeDTOFull()
            .setOrdersCountFilters(Collections.singleton(new PromocodeFilterDTO(-1L, FilterRelation.EQUALS)));
        final PromoCode savedPromoCode = defaultPromocodeService.createPromoCode(promocodeDTOFull);

        val updateDto = PromocodeTestUtil.createDefaultPromocodeDTOFull()
            .setOrdersCountFilters(Collections.singleton(new PromocodeFilterDTO(13L, FilterRelation.GREATER)));
        updateDto.setId(savedPromoCode.getId());

        // Act & Assert
        assertThatThrownBy(() -> defaultPromocodeService.updatePromoCode(savedPromoCode.getId(), updateDto))
            .isInstanceOf(PromoCodeAccessException.class);
    }

    @Test
    void createAndUpdatePromocode() {
        // Arrange create
        when(securityService.getCurrentAuthorizedUser())
            .thenReturn(currentUser);
        val createPromocodeRequest = PromocodeTestUtil.createDefaultPromocodeDTOFull()
            .setBrandsList(Collections.singleton(new PromocodeReferenceLinkDTO().setName("ADIDAS").setId(2507L)))
            .setCategoriesList(Collections.singleton(new PromocodeReferenceLinkDTO().setId(153L)));

        // Act create
        final PromoCode promoCode = defaultPromocodeService.createPromoCode(createPromocodeRequest);

        // Arrange update
        final PromocodeDTOFull updatePromocodeRequest = PromocodeTestUtil.createDefaultPromocodeDTOFull()
            .setBrandsList(Collections.singleton(new PromocodeReferenceLinkDTO().setName("ADIDAS").setId(2507L)))
            .setCategoriesList(Collections.singleton(new PromocodeReferenceLinkDTO().setId(153L)));
        updatePromocodeRequest.setId(promoCode.getId());

        // Act update
        defaultPromocodeService.updatePromoCode(promoCode.getId(), updatePromocodeRequest);

        // Assert
        assertThat(promoCode.getCreatedBy())
            .isEqualTo(currentUser);
        final Optional<ApplyRule> applyRuleOptional = applyRuleRepository.findById(promoCode.getApplyRuleId());
        assertThat(applyRuleOptional).isPresent();
        final Set<ApplyRuleFilterValue> filterValues = applyRuleOptional.get().getFilterValues();
        assertThat(filterValues).hasSize(4);
    }

    @Test
    void createPromocodeSuccessfulOnDuplicateEqualsRelation() {
        // Arrange
        final Set<PromocodeFilterDTO> promocodeFilterDTOS = new HashSet<>();
        promocodeFilterDTOS.add(new PromocodeFilterDTO(1L, FilterRelation.EQUALS));
        promocodeFilterDTOS.add(new PromocodeFilterDTO(2L, FilterRelation.EQUALS));

        final PromocodeDTOFull createPromocodeRequest = PromocodeTestUtil.createDefaultPromocodeDTOFull()
            .setOrdersCountFilters(promocodeFilterDTOS);

        // Act & Assert
        try {
            defaultPromocodeService.createPromoCode(createPromocodeRequest);
        } catch (PromoCodeEditException e) {
            assertThat(e.getMessage()).isEqualTo("Дублируется условие LESS");
        }
    }

    @Test
    void createPromocodeThrowsExceptionOnDuplicateRelation() {
        final Set<PromocodeFilterDTO> promocodeFilterDTOS = new HashSet<>();
        promocodeFilterDTOS.add(new PromocodeFilterDTO(1000L, FilterRelation.LESS));
        promocodeFilterDTOS.add(new PromocodeFilterDTO(5000L, FilterRelation.LESS));

        final PromocodeDTOFull createPromocodeRequest = PromocodeTestUtil.createDefaultPromocodeDTOFull()
            .setOrdersCountFilters(promocodeFilterDTOS);

        // Act & Assert
        try {
            defaultPromocodeService.createPromoCode(createPromocodeRequest);
        } catch (PromoCodeEditException e) {
            assertThat(e.getMessage()).isEqualTo("Дублируется условие фильтров");
        }
    }

    @Test
    @DisplayName("Сохранение промокода с периодом сброса лимита")
    void savePromoCodeWithResetRange() {
        // Arrange
        final PromocodeDTOFull createPromocodeRequest = PromocodeTestUtil.createDefaultPromocodeDTOFull();
        createPromocodeRequest.setResetRange(PromoCodeResetRange.WEEK);
        createPromocodeRequest.setNumberOfApplies(2);
        createPromocodeRequest.setAppliedNumberType(PromoCodeAppliedNumberType.PER_BUYER);

        // Act
        final PromoCode savedPromoCode = defaultPromocodeService.createPromoCode(createPromocodeRequest);

        // Assert
        assertThat(savedPromoCode.getResetRange()).isEqualTo(PromoCodeResetRange.WEEK);
        assertThat(savedPromoCode.getNumberOfApplies()).isEqualTo(2);
    }

    /**
     * Поставщик для {@link DefaultPromoCodeServiceTest#savePromoCodeWithBeginPrice}
     */
    private static Stream<PromocodeDTOFull> savePromoCodeWithBeginPriceProvider() {
        return Stream.of(
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setOrderAmountFilters(Collections.singleton(new PromocodeFilterDTO(100L, FilterRelation.GREATER))),
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setOrderAmountFilters(Collections.singleton(new PromocodeFilterDTO(100L, FilterRelation.EQUALS_OR_GREATER)))
        );
    }

    @ParameterizedTest
    @MethodSource("savePromoCodeWithBeginPriceProvider")
    @DisplayName("Сохранение промокода, где подставляется beginPrice из PromocodeDTOFull")
    void savePromoCodeWithBeginPrice(PromocodeDTOFull createPromocodeRequest) {
        // Act
        final PromoCode savedPromoCode = defaultPromocodeService.createPromoCode(createPromocodeRequest);

        // Assert
        assertThat(savedPromoCode.getBeginPrice())
            .isEqualTo(createPromocodeRequest.getOrderAmountFilters()
                .stream()
                .findFirst()
                .map(PromocodeFilterDTO::getValue)
                .map(BigDecimal::valueOf)
                .orElseThrow(() -> new RuntimeException("Обязано быть значение фильтра")));
    }

    @Test
    void createPromocodeThrowsWithDuplicateCode() {
        // Arrange
        final Set<PromocodeFilterDTO> promocodeFilterDTOS = new HashSet<>();
        final PromocodeDTOFull createPromocodeRequest = PromocodeTestUtil.createDefaultPromocodeDTOFull()
            .setOrdersCountFilters(promocodeFilterDTOS);

        final Set<PromocodeFilterDTO> promocodeFilterDTOS2 = new HashSet<>();
        final PromocodeDTOFull createPromocodeRequest2 = PromocodeTestUtil.createDefaultPromocodeDTOFull()
            .setOrdersCountFilters(promocodeFilterDTOS2);
        createPromocodeRequest2.setCode(TEST_PROMO_CODE_UPPER);

        final Set<PromocodeFilterDTO> promocodeFilterDTOS3 = new HashSet<>();
        final PromocodeDTOFull createPromocodeRequest3 = PromocodeTestUtil.createDefaultPromocodeDTOFull()
            .setOrdersCountFilters(promocodeFilterDTOS3);
        createPromocodeRequest3.setCode(TEST_PROMO_CODE_UPPER);

        // Act & Assert
        defaultPromocodeService.createPromoCode(createPromocodeRequest);

        try {
            defaultPromocodeService.createPromoCode(createPromocodeRequest2);
            fail("Должна вылететь ошибка дубликата промокода");
        } catch (PromoCodeEditException e) {
            assertThat(e.getMessage().contains(EXPECTED_DUPLICATE_ERROR_PART_1) && e.getMessage().contains(EXPECTED_DUPLICATE_ERROR_PART_2)).isTrue();
        }

        try {
            defaultPromocodeService.createPromoCode(createPromocodeRequest3);
            fail("Должна вылететь ошибка дубликата промокода");
        } catch (PromoCodeEditException e) {
            assertThat(e.getMessage().contains(EXPECTED_DUPLICATE_ERROR_PART_1) && e.getMessage().contains(EXPECTED_DUPLICATE_ERROR_PART_2)).isTrue();
        }
    }
    // endregion

    // region UpdatePromoCodeTests

    @Test
    @DisplayName("Обновление промокода с заполненного beginPrice на пустое")
    void updateBeginPrice() {
        // Arrange
        final Authority admin = new Authority();
        admin.setId(1L);
        admin.setName(AuthorityName.ADMIN);
        final User user = new User()
            .setId(2L);
        user.setUserAuthorityBindings(
            singletonList(UserAuthorityBinding.builder()
                .user(user)
                .authority(admin)
                .build()));
        when(securityService.getCurrentAuthorizedUser())
            .thenReturn(currentUser, user);

        val createPromocodeRequest = PromocodeTestUtil.createDefaultPromocodeDTOFull()
            .setOrderAmountFilters(Collections.singleton(new PromocodeFilterDTO(100L, FilterRelation.GREATER)));
        final PromoCode arrangeSave = defaultPromocodeService.createPromoCode(createPromocodeRequest);

        // Act
        final PromoCode savedPromoCode = defaultPromocodeService.updatePromoCode(arrangeSave.getId(), PromocodeTestUtil.createDefaultPromocodeDTOFull());

        // Assert
        assertThat(savedPromoCode.getBeginPrice())
            .isNull();
    }

    /**
     * {@link DefaultPromoCodeServiceTest#updateOtherUserPromoCodeWithRole}
     */
    private static Stream<?> userWithRoleProvider() {
        final User user = new User()
            .setId(2L);
        final Authority admin = new Authority();
        admin.setId(1L);
        admin.setName(AuthorityName.ADMIN);
        final Authority promoCodeAdmin = new Authority();
        promoCodeAdmin.setId(2L);
        promoCodeAdmin.setName(AuthorityName.PROMOCODES_ADMINISTRATION);

        return Stream.of(
            user.setUserAuthorityBindings(
                singletonList(UserAuthorityBinding.builder()
                    .user(user)
                    .authority(admin)
                    .build())),
            user.setUserAuthorityBindings(
                singletonList(UserAuthorityBinding.builder()
                    .user(user)
                    .authority(promoCodeAdmin)
                    .build()))
        );
    }

    @ParameterizedTest
    @MethodSource("userWithRoleProvider")
    @DisplayName("Обновление промокода другим пользователем с требуемой ролью происходит без ошибок")
    void updateOtherUserPromoCodeWithRole(User updatingUser) {
        // Arrange
        when(securityService.getCurrentAuthorizedUser())
            .thenReturn(currentUser, updatingUser);

        val promocodeDTOFull = PromocodeTestUtil.createDefaultPromocodeDTOFull()
            .setOrdersCountFilters(Collections.singleton(new PromocodeFilterDTO(-1L, FilterRelation.EQUALS)));
        final PromoCode savedPromoCode = defaultPromocodeService.createPromoCode(promocodeDTOFull);

        val updateDto = PromocodeTestUtil.createDefaultPromocodeDTOFull()
            .setOrdersCountFilters(Collections.singleton(new PromocodeFilterDTO(13L, FilterRelation.GREATER)));
        updateDto.setId(savedPromoCode.getId());

        // Act
        final PromoCode updatedPromoCode = defaultPromocodeService.updatePromoCode(savedPromoCode.getId(), updateDto);

        // Assert
        final Set<ApplyRuleFilterValue> filterValues = applyRuleRepository.findById(updatedPromoCode.getApplyRuleId())
            .orElseThrow(() -> new RuntimeException("ApplyRule not found by given ID"))
            .getFilterValues();

        assertThat(filterValues).hasSize(3);
        assertThat(filterValues)
            .extracting(ApplyRuleFilterValue::getFilterType)
            .containsExactlyInAnyOrder(ApplyRuleFilterType.BUYER_ORDERS_COUNT, ApplyRuleFilterType.IOS_APPLICATION, ApplyRuleFilterType.ANDROID_APPLICATION);
        assertThat(filterValues)
            .extracting(ApplyRuleFilterValue::getValue)
            .containsExactlyInAnyOrder(0L, 0L, 13L);
    }

    @Test
    void updatePromocodeThrowsWithDuplicateCode() {
        // Arrange
        when(securityService.getCurrentAuthorizedUser())
            .thenReturn(currentUser);
        final Set<PromocodeFilterDTO> promocodeFilterDTOS = new HashSet<>();
        final PromocodeDTOFull createPromocodeRequest = PromocodeTestUtil.createDefaultPromocodeDTOFull()
            .setOrdersCountFilters(promocodeFilterDTOS);
        defaultPromocodeService.createPromoCode(createPromocodeRequest);

        final Set<PromocodeFilterDTO> promocodeFilterDTOS2 = new HashSet<>();
        final PromocodeDTOFull createPromocodeRequest2 = PromocodeTestUtil.createDefaultPromocodeDTOFull()
            .setOrdersCountFilters(promocodeFilterDTOS2);
        createPromocodeRequest2.setCode(TEST_PROMO_CODE_2);
        final PromoCode promoCode2 = defaultPromocodeService.createPromoCode(createPromocodeRequest2);

        final Set<PromocodeFilterDTO> promocodeFilterDTOS3 = new HashSet<>();
        final PromocodeDTOFull updatePromocode2Request = PromocodeTestUtil.createDefaultPromocodeDTOFull()
            .setOrdersCountFilters(promocodeFilterDTOS3);

        // Act & Assert
        try {
            defaultPromocodeService.updatePromoCode(promoCode2.getId(), updatePromocode2Request);
            fail("Должна вылететь ошибка дубликата промокода");
        } catch (PromoCodeEditException e) {
            assertThat(
                e.getMessage().contains(EXPECTED_DUPLICATE_ERROR_PART_1)
                    && e.getMessage().contains(EXPECTED_DUPLICATE_ERROR_PART_2))
                .isTrue();
        }
    }
    // endregion

    //region PromoCodeApplicabilityTests

    @Test
    void isPromoCodeApplicableToOrderReturnsTrueWhenUserOrderCountEqualsExpected() {
        // Arrange
        final PromocodeDTOFull promocodeDTOFull = PromocodeTestUtil.createDefaultPromocodeDTOFull()
            .setOrdersCountFilters(Collections.singleton(new PromocodeFilterDTO(1L, FilterRelation.EQUALS)));
        final PromoCode promoCode = defaultPromocodeService.createPromoCode(promocodeDTOFull);

        final Order order = promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser);

        // Act
        final PromoCodeApplicability applicability = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, order);

        // Assert
        assertThat(applicability.isApplicable()).isTrue();
    }

    @Test
    @DisplayName("Промокод применим по сумме всех заказов пользователя")
    void isApplicableByBuyerOrdersAmount() {
        // Arrange
        final PromocodeDTOFull promocodeDTOFull = PromocodeTestUtil.createDefaultPromocodeDTOFull()
            .setOrdersAmountSummaryFilters(Collections.singleton(new PromocodeFilterDTO(2L, FilterRelation.EQUALS)));
        final PromoCode promoCode = defaultPromocodeService.createPromoCode(promocodeDTOFull);

        promocodeTestUtil.createOrder(BigDecimal.valueOf(1), OrderState.COMPLETED, currentUser, ProductState.SOLD);
        promocodeTestUtil.createOrder(BigDecimal.valueOf(1), OrderState.COMPLETED, currentUser, ProductState.SOLD);
        final Order order = promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.CREATED, currentUser);

        // Act
        final PromoCodeApplicability applicability = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, order);

        // Assert
        assertThat(applicability.isApplicable()).isTrue();
    }

    /**
     * {@link DefaultPromoCodeServiceTest#isPromoCodeApplicableToOrderComparing}
     */
    private static Stream<Arguments> applicableRangeForOrderProvider() {
        return Stream.of(
            Arguments.of(FilterRelation.GREATER, Arrays.asList(true, false, false)),
            Arguments.of(FilterRelation.EQUALS_OR_GREATER, Arrays.asList(true, true, false)),
            Arguments.of(FilterRelation.EQUALS_OR_LESS, Arrays.asList(false, true, true)),
            Arguments.of(FilterRelation.EQUALS_OR_LESS, Arrays.asList(false, true, true))
        );
    }

    @ParameterizedTest
    @MethodSource("applicableRangeForOrderProvider")
    void isPromoCodeApplicableToOrderComparing(FilterRelation filterRelation, List<Boolean> expected) {
        // Arrange
        final PromocodeDTOFull promocodeDTOFull = PromocodeTestUtil.createDefaultPromocodeDTOFull()
            .setOrdersCountFilters(Collections.singleton(new PromocodeFilterDTO(2L, filterRelation)));
        final PromoCode promoCode = defaultPromocodeService.createPromoCode(promocodeDTOFull);

        final List<Order> orders = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            orders.add(promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser));
        }

        // Act
        final List<Boolean> result = new ArrayList<>(3);
        result.add(defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(0)).isApplicable());
        orderRepository.deleteById(orders.get(0).getId());
        result.add(defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(1)).isApplicable());
        orderRepository.deleteById(orders.get(1).getId());
        result.add(defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(2)).isApplicable());

        // Assert
        assertThat(result)
            .containsExactlyElementsOf(expected);
    }

    @Test
    void isPromoCodeApplicableToOrderReturnsTrueWhenUserOrderCountBetweenExpectedExclusive() {
        // Arrange
        final Set<PromocodeFilterDTO> filterDTOS = new HashSet<>();
        filterDTOS.add(new PromocodeFilterDTO(1L, FilterRelation.GREATER));
        filterDTOS.add(new PromocodeFilterDTO(3L, FilterRelation.LESS));
        final PromocodeDTOFull promocodeDTOFull = PromocodeTestUtil.createDefaultPromocodeDTOFull()
            .setOrdersCountFilters(filterDTOS);
        final PromoCode promoCode = defaultPromocodeService.createPromoCode(promocodeDTOFull);

        final List<Order> orders = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            orders.add(promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser));
        }

        // Act & Assert
        // При 3 заказах FALSE
        final PromoCodeApplicability applicability1 = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(0));
        assertThat(applicability1.isApplicable()).isFalse();

        // При 2 заказах TRUE
        orderRepository.deleteById(orders.get(0).getId());
        final PromoCodeApplicability applicability2 = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(1));
        assertThat(applicability2.isApplicable()).isTrue();

        // При 1 заказе FALSE
        orderRepository.deleteById(orders.get(1).getId());
        final PromoCodeApplicability applicability3 = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(2));
        assertThat(applicability3.isApplicable()).isFalse();
    }

    @Test
    void isPromoCodeApplicableToOrderReturnsTrueWhenUserOrderCountBetweenExpectedInclusive() {
        // Arrange
        final Set<PromocodeFilterDTO> filterDTOS = new HashSet<>();
        filterDTOS.add(new PromocodeFilterDTO(2L, FilterRelation.EQUALS_OR_GREATER));
        filterDTOS.add(new PromocodeFilterDTO(4L, FilterRelation.EQUALS_OR_LESS));
        final PromocodeDTOFull promocodeDTOFull = PromocodeTestUtil.createDefaultPromocodeDTOFull()
            .setOrdersCountFilters(filterDTOS);
        final PromoCode promoCode = defaultPromocodeService.createPromoCode(promocodeDTOFull);

        final List<Order> orders = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            orders.add(promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser));
        }

        // Act & Assert
        // При 5 заказах FALSE
        final PromoCodeApplicability applicability1 = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(0));
        assertThat(applicability1.isApplicable()).isFalse();

        // При 4 заказах TRUE
        orderRepository.deleteById(orders.get(0).getId());
        final PromoCodeApplicability applicability2 = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(1));
        assertThat(applicability2.isApplicable()).isTrue();

        // При 3 заказах TRUE
        orderRepository.deleteById(orders.get(1).getId());
        final PromoCodeApplicability applicability3 = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(2));
        assertThat(applicability3.isApplicable()).isTrue();

        // При 2 заказах TRUE
        orderRepository.deleteById(orders.get(2).getId());
        final PromoCodeApplicability applicability4 = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(3));
        assertThat(applicability4.isApplicable()).isTrue();

        // При 1 заказе FALSE
        orderRepository.deleteById(orders.get(3).getId());
        final PromoCodeApplicability applicability5 = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(4));
        assertThat(applicability5.isApplicable()).isFalse();
    }

    /**
     * {@link DefaultPromoCodeServiceTest#isPromoCodeApplicableToOrderAmount}
     */
    private static Stream<?> applicableRangeForOrderAmountProvider() {
        return Stream.of(
            Arguments.of(FilterRelation.EQUALS, Arrays.asList(false, true, false)),
            Arguments.of(FilterRelation.GREATER, Arrays.asList(false, false, true)),
            Arguments.of(FilterRelation.EQUALS_OR_GREATER, Arrays.asList(false, true, true)),
            Arguments.of(FilterRelation.LESS, Arrays.asList(true, false, false)),
            Arguments.of(FilterRelation.EQUALS_OR_LESS, Arrays.asList(true, true, false))
        );
    }

    @ParameterizedTest
    @MethodSource("applicableRangeForOrderAmountProvider")
    void isPromoCodeApplicableToOrderAmount(FilterRelation filterRelation, List<Boolean> expected) {
        // Arrange
        final PromoCode promoCode = promocodeTestUtil.createPromoCodeWithOrderAmountFilter(2000L, filterRelation);

        final List<Order> orders = new ArrayList<>();
        orders.add(promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser));
        orders.add(promocodeTestUtil.createOrder(BigDecimal.valueOf(2000), OrderState.COMPLETED, currentUser));
        orders.add(promocodeTestUtil.createOrder(BigDecimal.valueOf(3000), OrderState.COMPLETED, currentUser));

        // Act
        final List<Boolean> result = new ArrayList<>();
        result.add(defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(0)).isApplicable());
        result.add(defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(1)).isApplicable());
        result.add(defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(2)).isApplicable());

        // Assert
        assertThat(result)
            .containsExactlyElementsOf(expected);
    }

    @Test
    void isPromoCodeApplicableToOrderReturnsTrueWhenOrderAmountBetweenExpectedExclusive() {
        // Arrange
        final Set<PromocodeFilterDTO> filterDTOS = new HashSet<>();
        filterDTOS.add(new PromocodeFilterDTO(1000L, FilterRelation.GREATER));
        filterDTOS.add(new PromocodeFilterDTO(3000L, FilterRelation.LESS));
        final PromoCode promoCode = promocodeTestUtil.getPromoCodeWithOrderAmountCompositeFilter(filterDTOS);

        final List<Order> orders = new ArrayList<>();
        orders.add(promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser));
        orders.add(promocodeTestUtil.createOrder(BigDecimal.valueOf(2000), OrderState.COMPLETED, currentUser));
        orders.add(promocodeTestUtil.createOrder(BigDecimal.valueOf(3000), OrderState.COMPLETED, currentUser));

        // Act & Assert
        // При отсутствии заказов FALSE
        final Order newOrder = promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.CREATED, currentUser);
        final PromoCodeApplicability applicability1 = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, newOrder);
        assertThat(applicability1.isApplicable()).isFalse();

        // При сумме заказа 1000 FALSE
        final PromoCodeApplicability applicability2 = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(0));
        assertThat(applicability2.isApplicable()).isFalse();

        // При сумме заказа 2000 TRUE
        final PromoCodeApplicability applicability3 = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(1));
        assertThat(applicability3.isApplicable()).isTrue();

        // При сумме заказа 3000 FALSE
        final PromoCodeApplicability applicability4 = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(2));
        assertThat(applicability4.isApplicable()).isFalse();
    }

    @Test
    void isPromoCodeApplicableToOrderReturnsTrueWhenOrderAmountBetweenExpectedInclusive() {
        // Arrange
        final Set<PromocodeFilterDTO> filterDTOS = new HashSet<>();
        filterDTOS.add(new PromocodeFilterDTO(2000L, FilterRelation.EQUALS_OR_GREATER));
        filterDTOS.add(new PromocodeFilterDTO(3000L, FilterRelation.EQUALS_OR_LESS));
        final PromoCode promoCode = promocodeTestUtil.getPromoCodeWithOrderAmountCompositeFilter(filterDTOS);

        final List<Order> orders = new ArrayList<>();
        orders.add(promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser));
        orders.add(promocodeTestUtil.createOrder(BigDecimal.valueOf(2000), OrderState.COMPLETED, currentUser));
        orders.add(promocodeTestUtil.createOrder(BigDecimal.valueOf(3000), OrderState.COMPLETED, currentUser));
        orders.add(promocodeTestUtil.createOrder(BigDecimal.valueOf(4000), OrderState.COMPLETED, currentUser));

        // Act & Assert
        // При отсутствии заказов FALSE
        final Order newOrder = promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.CREATED, currentUser);
        final PromoCodeApplicability applicability = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, newOrder);
        assertThat(applicability.isApplicable()).isFalse();

        // При сумме заказа 1000 FALSE
        final PromoCodeApplicability applicability2 = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(0));
        assertThat(applicability2.isApplicable()).isFalse();

        // При сумме заказа 2000 TRUE
        final PromoCodeApplicability applicability3 = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(1));
        assertThat(applicability3.isApplicable()).isTrue();

        // При сумме заказа 3000 TRUE
        final PromoCodeApplicability applicability4 = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(2));
        assertThat(applicability4.isApplicable()).isTrue();

        // При сумме заказа 4000 FALSE
        final PromoCodeApplicability applicability5 = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(3));
        assertThat(applicability5.isApplicable()).isFalse();
    }

    @Test
    void isPromoCodeApplicableToOrderReturnsTrueWhenOrderSummaryAmountBetweenExpectedInclusive() {
        // Arrange
        final Set<PromocodeFilterDTO> filterDTOS = new HashSet<>();
        filterDTOS.add(new PromocodeFilterDTO(2000L, FilterRelation.EQUALS_OR_GREATER));
        filterDTOS.add(new PromocodeFilterDTO(3000L, FilterRelation.EQUALS_OR_LESS));
        final PromoCode promoCode = promocodeTestUtil.getPromoCodeWithOrderSummaryAmountCompositeFilter(filterDTOS);

        final List<Order> orders = new ArrayList<>();

        // Act & Assert
        // При отсутствии заказов FALSE
        final Order newOrder = promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.CREATED, currentUser);
        PromoCodeApplicability applicability = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, newOrder);
        assertThat(applicability.isApplicable()).isFalse();

        // При сумме заказов 1000 FALSE
        orders.add(promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser));
        applicability = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(0));
        assertThat(applicability.isApplicable()).isFalse();

        // При сумме заказа 2000 TRUE
        orders.add(promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser));
        applicability = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(1));
        assertThat(applicability.isApplicable()).isTrue();

        // При сумме заказа 3000 TRUE
        orders.add(promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser));
        applicability = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(2));
        assertThat(applicability.isApplicable()).isTrue();

        // При сумме заказа 4000 FALSE
        orders.add(promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser));
        applicability = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(3));
        assertThat(applicability.isApplicable()).isFalse();
    }

    @Test
    void isPromoCodeApplicableToOrderReturnsTrueWhenOrderSummaryAmountBetweenExpectedExclusive() {
        Set<PromocodeFilterDTO> filterDTOS = new HashSet<>();
        filterDTOS.add(new PromocodeFilterDTO(2000L, FilterRelation.GREATER));
        filterDTOS.add(new PromocodeFilterDTO(4000L, FilterRelation.LESS));
        PromoCode promoCode = promocodeTestUtil.getPromoCodeWithOrderSummaryAmountCompositeFilter(filterDTOS);

        List<Order> orders = new ArrayList<>();

        // При отсутствии заказов FALSE
        Order newOrder = promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.CREATED, currentUser);
        PromoCodeApplicability applicability = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, newOrder);
        assertThat(applicability.isApplicable()).isFalse();

        // При сумме заказов 1000 FALSE
        orders.add(promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser));
        applicability = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(0));
        assertThat(applicability.isApplicable()).isFalse();
        // При сумме заказа 2000 FALSE
        orders.add(promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser));
        applicability = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(1));
        assertThat(applicability.isApplicable()).isFalse();
        // При сумме заказа 3000 TRUE
        orders.add(promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser));
        applicability = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(2));
        assertThat(applicability.isApplicable()).isTrue();
        // При сумме заказа 4000 FALSE
        orders.add(promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser));
        applicability = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(3));
        assertThat(applicability.isApplicable()).isFalse();
        // При сумме заказа 5000 FALSE
        orders.add(promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser));
        applicability = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, orders.get(4));
        assertThat(applicability.isApplicable()).isFalse();
    }

    @Test
    @DisplayName("При применении использовании одноразового промокода возвращается ошибка на втором разе")
    void isOnetimePromoCodeApplicableToOrderReturnsFalseWhenOtherOrderContainsThisPromoCode() {
        final PromoCode promoCode = promocodeTestUtil.createOneTimePromoCode();

        //Создаем заказ и пробуем можно ли его применить первый раз
        final Order newOrder = promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser);
        final PromoCodeApplicability applicability = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, newOrder);
        assertThat(applicability.isApplicable()).isTrue();

        //Создаем еще один заказ сразу с этим промокодом и проверяем, что он нормально создался
        final Order newOrder2 = promocodeTestUtil.createOrderWithPromoCode(BigDecimal.valueOf(1000), OrderState.COMPLETED, promoCode, currentUser);
        assertThat(newOrder2.getPromoCode().getNumberOfApplies()).isEqualTo(1);
        assertThat(newOrder2.getPromoCode().getNumberOfAppliesType()).isEqualTo(PromoCodeAppliedNumberType.GLOBALLY);

        //Создаем еще один заказ без промокода и пытаемся проверить можно ли на нем применить этот промокод
        final Order newOrder3 = promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.HOLD, currentUser);
        final PromoCodeApplicability applicability2 = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, newOrder3);
        assertThat(applicability2.isApplicable()).isFalse();
    }

    /**
     * {@link DefaultPromoCodeServiceTest#applicablePromoCodeToUserDevice}
     */
    private static Stream<Arguments> deviceTypeProvider() {
        final PromocodeDTOFull iosRequest = PromocodeTestUtil.createDefaultPromocodeDTOFull()
            .setIosApplication(true);
        final PromocodeDTOFull androidRequest = PromocodeTestUtil.createDefaultPromocodeDTOFull()
            .setAndroidApplication(true);
        final PromocodeDTOFull webRequest = PromocodeTestUtil.createDefaultPromocodeDTOFull();

        return Stream.of(
            Arguments.of(webRequest, DeviceDtype.AppleDevice, false),// 1
            Arguments.of(webRequest, DeviceDtype.OtherAppleDevice, false), // 2
            Arguments.of(webRequest, DeviceDtype.AndroidDevice, false), // 3
            Arguments.of(webRequest, DeviceDtype.OtherAndroidDevice, false), // 4
            Arguments.of(iosRequest, DeviceDtype.AndroidDevice, false), // 5
            Arguments.of(iosRequest, DeviceDtype.AndroidDevice, false), // 6
            Arguments.of(androidRequest, DeviceDtype.AppleDevice, false), // 7
            Arguments.of(androidRequest, DeviceDtype.AppleDevice, false), // 8

            Arguments.of(webRequest, DeviceDtype.OtherLinuxDevice, true), // 9
            Arguments.of(webRequest, DeviceDtype.OtherMacOsDevice, true), // 10
            Arguments.of(webRequest, DeviceDtype.OtherDevice, true), // 11
            Arguments.of(webRequest, DeviceDtype.OtherWindowsDevice, true), // 12
            Arguments.of(iosRequest, DeviceDtype.AppleDevice, true), // 13
            Arguments.of(iosRequest, DeviceDtype.OtherAppleDevice, true), // 14
            Arguments.of(androidRequest, DeviceDtype.AndroidDevice, true), // 15
            Arguments.of(androidRequest, DeviceDtype.OtherAndroidDevice, true) // 16
        );
    }

    @ParameterizedTest
    @MethodSource("deviceTypeProvider")
    void applicablePromoCodeToUserDevice(PromocodeDTOFull createPromoCodeRequest,
                                         DeviceDtype deviceDtype,
                                         boolean expectedResult) {
        // Arrange
        when(deviceService.getCurrentDeviceInfo())
            .thenReturn(new DeviceService.DeviceInfo()
                .setDeviceDtype(deviceDtype));

        final PromoCode promoCode = defaultPromocodeService.createPromoCode(createPromoCodeRequest);
        final Order newOrder = promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser);

        // Act
        final PromoCodeApplicability applicability = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, newOrder);

        // Assert
        assertThat(applicability.isApplicable()).isEqualTo(expectedResult);
    }

    /**
     * {@link DefaultPromoCodeServiceTest#applicablePromoCodeByFilterType}
     */
    private static Stream<PromocodeDTOFull> applicablePromoCodeRequestProvider() {
        return Stream.of(
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setBrandsList(Collections.singleton(new PromocodeReferenceLinkDTO(1L, "qwe"))),
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setCategoriesList(Collections.singleton(new PromocodeReferenceLinkDTO(1L, "qwe"))),
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setSellersList(Collections.singleton(new PromocodeReferenceLinkDTO(1L, "qwe"))),
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setProductList(Collections.singleton(new PromocodeReferenceLinkDTO(1L, "qwe"))),
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setExceptSellersList(Collections.singleton(new PromocodeReferenceLinkDTO(-1L, "qwe"))),
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setExceptBrandsList(Collections.singleton(new PromocodeReferenceLinkDTO(-1L, "qwe"))),
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setExceptCategoriesList(Collections.singleton(new PromocodeReferenceLinkDTO(-1L, "qwe"))),
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setExceptProductList(Collections.singleton(new PromocodeReferenceLinkDTO(-1L, "qwe"))),
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setProductConditionList(Collections.singleton(new PromocodeReferenceLinkDTO(1L, "qwe"))),
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setExceptProductConditionList(Collections.singleton(new PromocodeReferenceLinkDTO(-1L, "qwe"))),
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setUserLoyaltyTagList(Collections.singleton(new PromocodeReferenceLinkDTO(1L, "qwe")))
        );
    }

    @ParameterizedTest
    @MethodSource("applicablePromoCodeRequestProvider")
    void applicablePromoCodeByFilterType(PromocodeDTOFull createPromoCodeRequest) {
        // Arrange
        final Order newOrder = promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser);
        createPromoCodeRequest.setProductList(
            Collections.singleton(
                new PromocodeReferenceLinkDTO(
                    newOrder.getOrderPositions()
                        .get(0).getProductItem()
                        .getProduct().getId(),
                    "qwe")));
        final PromoCode promoCode = defaultPromocodeService.createPromoCode(createPromoCodeRequest);
        val commonTagDTO = new UserCommonTagDTO();
        commonTagDTO.setId(1L);
        when(userCommonTagService.getTagsByUser(anyLong()))
            .thenReturn(singletonList(commonTagDTO));

        // Act
        final PromoCodeApplicability applicability = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, newOrder);

        // Assert
        assertThat(applicability.isApplicable()).isTrue();
    }

    /**
     * {@link DefaultPromoCodeServiceTest#promoCodeNotAllowedByFilterType}
     */
    private static Stream<PromocodeDTOFull> disallowedPromoCodeRequestProvider() {
        return Stream.of(
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setBuyersList(Collections.singleton(new PromocodeReferenceLinkDTO(-1L, "qwe"))),
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setBrandsList(Collections.singleton(new PromocodeReferenceLinkDTO(-1L, "qwe"))),
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setCategoriesList(Collections.singleton(new PromocodeReferenceLinkDTO(-1L, "qwe"))),
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setSellersList(Collections.singleton(new PromocodeReferenceLinkDTO(-1L, "qwe"))),
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setProductList(Collections.singleton(new PromocodeReferenceLinkDTO(-1L, "qwe"))),
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setExceptSellersList(Collections.singleton(new PromocodeReferenceLinkDTO(1L, "qwe"))),
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setExceptBrandsList(Collections.singleton(new PromocodeReferenceLinkDTO(1L, "qwe"))),
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setExceptCategoriesList(Collections.singleton(new PromocodeReferenceLinkDTO(1L, "qwe"))),
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setProductConditionList(Collections.singleton(new PromocodeReferenceLinkDTO(-1L, "qwe"))),
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setExceptProductConditionList(Collections.singleton(new PromocodeReferenceLinkDTO(1L, "qwe"))),
            PromocodeTestUtil.createDefaultPromocodeDTOFull()
                .setUserLoyaltyTagList(Collections.singleton(new PromocodeReferenceLinkDTO(-1L, "qwe")))
        );
    }

    @ParameterizedTest
    @MethodSource("disallowedPromoCodeRequestProvider")
    void promoCodeNotAllowedByFilterType(PromocodeDTOFull createPromoCodeRequest) {
        // Arrange
        final Order newOrder = promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser);
        createPromoCodeRequest.setExceptProductList(
            Collections.singleton(
                new PromocodeReferenceLinkDTO(
                    newOrder.getOrderPositions()
                        .get(0).getProductItem()
                        .getProduct().getId(),
                    "qwe")));
        final PromoCode promoCode = defaultPromocodeService.createPromoCode(createPromoCodeRequest);
        val commonTagDTO = new UserCommonTagDTO();
        commonTagDTO.setId(1L);
        when(userCommonTagService.getTagsByUser(anyLong()))
            .thenReturn(singletonList(commonTagDTO));

        // Act
        final PromoCodeApplicability applicability = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, newOrder);

        // Assert
        assertThat(applicability.isApplicable()).isFalse();
    }

    @Test
    @DisplayName("Можно применить промокод к текущему пользователю")
    void applicablePromoCodeToCurrentUser() {
        // Arrange
        val createPromoCodeRequest = new PromocodeDTOFull()
            .setBuyersList(Collections.singleton(new PromocodeReferenceLinkDTO(currentUser.getId(), "qwe")));
        createPromoCodeRequest.setCode(TEST_PROMO_CODE_3);
        createPromoCodeRequest.setPercent(BigDecimal.valueOf(5));
        final PromoCode promoCode = defaultPromocodeService.createPromoCode(createPromoCodeRequest);
        final Order newOrder = promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser);

        // Act
        final PromoCodeApplicability applicability = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, newOrder);

        // Assert
        assertThat(applicability.isApplicable()).isTrue();
    }

    @Test
    @DisplayName("Разрешено применить промокод по фильтру номера заказа")
    void promoCodeAllowedByOrderNumber() {
        // Arrange
        final PromocodeDTOFull createPromoCodeRequest = PromocodeTestUtil.createDefaultPromocodeDTOFull()
            .setOrdersRelativeNumbers(Collections.singleton(new PromocodeFilterDTO(4L, FilterRelation.EQUALS)));
        final PromoCode promoCode = defaultPromocodeService.createPromoCode(createPromoCodeRequest);

        final Order order1 = promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser);
        promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser);
        promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser);
        promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.HOLD_ERROR, currentUser); // будет пропущен
        promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.DELETED, currentUser); // будет пропущен
        promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.CANCELED, currentUser); // будет пропущен
        final Order newOrder = promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.CREATED, currentUser);

        // Act
        final PromoCodeApplicability applicability = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, newOrder);

        // Assert
        assertThat(applicability.isApplicable()).isTrue();
    }

    @Test
    @DisplayName("Запрещено применить промокод по фильтру номера заказа")
    void promoCodeNotAllowedByOrderNumber() {
        // Arrange
        final PromocodeDTOFull createPromoCodeRequest = PromocodeTestUtil.createDefaultPromocodeDTOFull()
            .setOrdersRelativeNumbers(Collections.singleton(new PromocodeFilterDTO(2L, FilterRelation.EQUALS)));
        final PromoCode promoCode = defaultPromocodeService.createPromoCode(createPromoCodeRequest);

        promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser);
        promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.COMPLETED, currentUser);
        final Order newOrder = promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.CREATED, currentUser);

        // Act
        final PromoCodeApplicability applicability = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, newOrder);

        // Assert
        assertThat(applicability.isApplicable()).isFalse();
    }

    @Test
    @DisplayName("Запрещено применить глобальный промокод выше заданного количества раз")
    void promoCodeNotAllowedCountExceed() {
        // Arrange
        final PromocodeDTOFull createPromoCodeRequest = PromocodeTestUtil.createDefaultPromocodeDTOFull();
        createPromoCodeRequest.setNumberOfApplies(2);
        createPromoCodeRequest.setAppliedNumberType(PromoCodeAppliedNumberType.GLOBALLY);
        final PromoCode promoCode = defaultPromocodeService.createPromoCode(createPromoCodeRequest);

        promocodeTestUtil.createOrderWithPromoCode(BigDecimal.valueOf(1000), OrderState.COMPLETED, promoCode, currentUser);
        promocodeTestUtil.createOrderWithPromoCode(BigDecimal.valueOf(1000), OrderState.COMPLETED, promoCode, anotherUser);
        final Order newOrder = promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.HOLD, currentUser);

        // Act
        final PromoCodeApplicability applicability = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, newOrder);

        // Assert
        assertThat(applicability.isApplicable()).isFalse();
    }

    @Test
    @DisplayName("Запрещено применить промокод в рамках лимита сброса для покупателя")
    void promoCodeNotAllowedWithinRange() {
        // Arrange
        final PromocodeDTOFull createPromoCodeRequest = PromocodeTestUtil.createDefaultPromocodeDTOFull();
        createPromoCodeRequest.setResetRange(PromoCodeResetRange.WEEK);
        createPromoCodeRequest.setNumberOfApplies(2);
        createPromoCodeRequest.setAppliedNumberType(PromoCodeAppliedNumberType.PER_BUYER);
        final PromoCode promoCode = defaultPromocodeService.createPromoCode(createPromoCodeRequest);

        promocodeTestUtil.createOrderWithPromoCode(BigDecimal.valueOf(1000), OrderState.COMPLETED, promoCode, currentUser);
        promocodeTestUtil.createOrderWithPromoCode(BigDecimal.valueOf(1000), OrderState.COMPLETED, promoCode, currentUser);
        final Order newOrder = promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.CREATED, currentUser);

        // Act
        final PromoCodeApplicability applicability = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, newOrder);

        // Assert
        assertThat(applicability.isApplicable()).isFalse();
    }

    @Test
    @DisplayName("Можно применить промокод в рамках лимита сброса для другого покупателя")
    void promoCodeAllowedWithinRangeAnotherBuyer() {
        // Arrange
        final PromocodeDTOFull createPromoCodeRequest = PromocodeTestUtil.createDefaultPromocodeDTOFull();
        createPromoCodeRequest.setResetRange(PromoCodeResetRange.WEEK);
        createPromoCodeRequest.setNumberOfApplies(2);
        createPromoCodeRequest.setAppliedNumberType(PromoCodeAppliedNumberType.PER_BUYER);
        final PromoCode promoCode = defaultPromocodeService.createPromoCode(createPromoCodeRequest);

        promocodeTestUtil.createOrderWithPromoCode(BigDecimal.valueOf(1000), OrderState.COMPLETED, promoCode, currentUser);
        promocodeTestUtil.createOrderWithPromoCode(BigDecimal.valueOf(1000), OrderState.COMPLETED, promoCode, currentUser);
        final Order newOrder = promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.HOLD, anotherUser);

        // Act
        final PromoCodeApplicability applicability = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, newOrder);

        // Assert
        assertThat(applicability.isApplicable()).isTrue();
    }

    @Test
    @DisplayName("Применить промокод с использованием сброса лимита")
    void promoCodeAllowedWithinRange() {
        // Arrange
        final PromocodeDTOFull createPromoCodeRequest = PromocodeTestUtil.createDefaultPromocodeDTOFull();
        createPromoCodeRequest.setResetRange(PromoCodeResetRange.WEEK);
        createPromoCodeRequest.setNumberOfApplies(2);
        createPromoCodeRequest.setAppliedNumberType(PromoCodeAppliedNumberType.PER_BUYER);
        final PromoCode promoCode = defaultPromocodeService.createPromoCode(createPromoCodeRequest);

        promocodeTestUtil.createOrderWithPromoCode(BigDecimal.valueOf(1000), OrderState.COMPLETED, promoCode, currentUser);
        promocodeTestUtil.createOrderWithPromoCode(BigDecimal.valueOf(1000), OrderState.COMPLETED, promoCode, currentUser);
        final Order newOrder = promocodeTestUtil.createOrder(BigDecimal.valueOf(1000), OrderState.HOLD, currentUser);
        when(clock.instant())
            .thenReturn(LocalDateTime.now().plusWeeks(1).toInstant(ZoneOffset.UTC));

        // Act
        final PromoCodeApplicability applicability = defaultPromocodeService.checkIfPromoCodeApplicableToOrder(promoCode, newOrder);

        // Assert
        assertThat(applicability.isApplicable()).isTrue();
    }
    // endregion

    @Test
    void getPromocodes() {
        // проверка того, что в выборке нет удаленных промокодов
        Page<PromoCode> promocodes =
            defaultPromocodeService.getPromocodes(null, null, null, null, null, null, PageRequest.oneMaximumSizePage());
        promocodes.getItems().forEach(item -> assertThat(item.isDeleted()).isFalse());
        // так как не указали сортировку, то должна сработать дефолтная. Проверим ее
        boolean prevIsActive = promocodes.getItems().get(0).isActive();
        ZonedDateTime prevStartsAt = promocodes.getItems().get(0).getStartsAt();
        ZonedDateTime prevCreatedAt = promocodes.getItems().get(0).getCreatedAt();

        for (int i = 1; i < promocodes.getItems().size(); i++) {
            PromoCode promoCode = promocodes.getItems().get(i);
            // сначала идут все активные
            if (!prevIsActive) {
                assertThat(promoCode.isActive()).isFalse();
            }

            if (prevIsActive == promoCode.isActive()) {
                // сначала null'ы, а потом по убыванию
                if (prevStartsAt != null) {
                    assertThat(promoCode.getStartsAt()).isBeforeOrEqualTo(prevStartsAt);
                }
            }

            if (prevIsActive == promoCode.isActive() && Objects.equals(prevStartsAt, promoCode.getStartsAt())) {
                // по убыванию
                assertThat(promoCode.getCreatedAt()).isBeforeOrEqualTo(prevCreatedAt);
            }

            prevIsActive = promoCode.isActive();
            prevStartsAt = promoCode.getStartsAt();
            prevCreatedAt = promoCode.getCreatedAt();
        }

        // проверим, что фильтрация активных фильтров работает
        promocodes =
            defaultPromocodeService.getPromocodes(true, null, null, null, null, null, PageRequest.of(1, 10));
        // все промокоды должны быть применимы в текущем времени
        promocodes.getItems().forEach(item -> assertThat(item.isApplicable()).isTrue());

        // теперь проверим, что работает фильтр по содержанию кода
        promocodes =
            defaultPromocodeService.getPromocodes(true, null, null, null, "osk", null, PageRequest.of(1, 10));
        // все промокоды должны быть применимы в текущем времени
        promocodes.getItems().forEach(item -> assertThat(item.isApplicable()).isTrue());
        // все промокоды должны содержать osk в своем коде
        promocodes.getItems().forEach(item -> assertThat(item.getCode()).containsIgnoringCase("osk"));

        promocodes =
            defaultPromocodeService.getPromocodes(null, null, null, null, "osk", null, PageRequest.of(1, 10));
        long oskItems = promocodes.getTotalAmount();
        // все промокоды должны содержать osk в своем коде
        promocodes.getItems().forEach(item -> assertThat(item.getCode()).containsIgnoringCase("osk"));

        // проверим, что может быть так, что ничего не вернется
        promocodes =
            defaultPromocodeService.getPromocodes(null, null, null, null, UUID.randomUUID().toString(), null, PageRequest.of(1, 10));
        assertThat(promocodes.getTotalAmount()).isEqualTo(0L);

        // теперь проверим сортировку вместе с фильтром
        promocodes =
            defaultPromocodeService.getPromocodes(null, null, null, null, "osk", "code_desc", PageRequest.of(1, 10));
        assertThat(promocodes.getTotalAmount()).isEqualTo(oskItems);
        String prevCode = promocodes.getItems().get(0).getCode().toLowerCase();
        for (int i = 1; i < promocodes.getItems().size(); i++) {
            PromoCode promoCode = promocodes.getItems().get(i);
            assertThat(promoCode.getCode().toLowerCase()).isLessThanOrEqualTo(prevCode);
            prevCode = promoCode.getCode().toLowerCase();
        }

        // а теперь остальные сортировки без фильтра
        promocodes =
            defaultPromocodeService.getPromocodes(null, null, null, null, null, "code_asc", PageRequest.of(1, 10));
        prevCode = promocodes.getItems().get(0).getCode().toLowerCase();
        for (int i = 1; i < promocodes.getItems().size(); i++) {
            PromoCode promoCode = promocodes.getItems().get(i);
            assertThat(promoCode.getCode().toLowerCase()).isGreaterThanOrEqualTo(prevCode);
            prevCode = promoCode.getCode().toLowerCase();
        }

        promocodes =
            defaultPromocodeService.getPromocodes(null, null, null, null, null, "expiresAt_asc", PageRequest.of(1, 10));
        ZonedDateTime prevExpiresAt = promocodes.getItems().get(0).getExpiresAt();
        for (int i = 1; i < promocodes.getItems().size(); i++) {
            PromoCode promoCode = promocodes.getItems().get(i);

            // null'ы должны быть в конце
            if (prevExpiresAt == null) {
                assertThat(promoCode.getExpiresAt()).isNull();
            } else {
                if (promoCode.getExpiresAt() != null) {
                    assertThat(promoCode.getExpiresAt()).isAfterOrEqualTo(prevExpiresAt);
                }
            }
            prevExpiresAt = promoCode.getExpiresAt();
        }

        promocodes =
            defaultPromocodeService.getPromocodes(null, null, null, null, null, "expiresAt_desc", PageRequest.of(1, 10));
        prevExpiresAt = promocodes.getItems().get(0).getExpiresAt();
        for (int i = 1; i < promocodes.getItems().size(); i++) {
            PromoCode promoCode = promocodes.getItems().get(i);

            // null'ы должны быть в начале
            if (prevExpiresAt != null) {
                assertThat(promoCode.getExpiresAt()).isBeforeOrEqualTo(prevExpiresAt);
            }
            prevExpiresAt = promoCode.getExpiresAt();
        }

        promocodes =
            defaultPromocodeService.getPromocodes(null, null, null, null, null, "beginPrice_asc", PageRequest.of(1, 10));
        BigDecimal prevBeginPrice = promocodes.getItems().get(0).getBeginPrice();
        for (int i = 1; i < promocodes.getItems().size(); i++) {
            PromoCode promoCode = promocodes.getItems().get(i);

            // null'ы должны быть в начале
            if (prevBeginPrice != null) {
                assertThat(promoCode.getBeginPrice()).isGreaterThanOrEqualTo(prevBeginPrice);
            }
            prevBeginPrice = promoCode.getBeginPrice();
        }

        promocodes =
            defaultPromocodeService.getPromocodes(null, null, null, null, null, "beginPrice_desc", PageRequest.of(1, 10));
        prevBeginPrice = promocodes.getItems().get(0).getBeginPrice();
        for (int i = 1; i < promocodes.getItems().size(); i++) {
            PromoCode promoCode = promocodes.getItems().get(i);

            // null'ы должны быть в конце
            if (prevBeginPrice == null) {
                assertThat(promoCode.getBeginPrice()).isNull();
            } else {
                if (promoCode.getBeginPrice() != null) {
                    assertThat(promoCode.getBeginPrice()).isLessThanOrEqualTo(prevBeginPrice);
                }
            }
            prevBeginPrice = promoCode.getBeginPrice();
        }

        // а теперь проверим, что выкинется ошибка, если попробуем отсортировать незнакомой сортировкой
        try {
            defaultPromocodeService.getPromocodes(null, null, null, null, null, "abracadabra", PageRequest.of(1, 10));
            failBecauseExceptionWasNotThrown(PromoCodeRequestException.class);
        } catch (PromoCodeRequestException ex) {
            assertThat(ex.getMessage())
                .isEqualTo("Не удалось найти подходящий обработчик для сортировки с кодом abracadabra");
        }
    }

    @Test
    public void getPromocodesWithOneTimePromoCodeFilter() {
        Page<PromoCode> promocodes;

        promocodeTestUtil.createOneTimePromoCode();

        // получаем количество активных промокодов
        promocodes =
            defaultPromocodeService.getPromocodes(true, null, null, null, null, null, PageRequest.of(1, 10));
        long totalCount = promocodes.getTotalAmount();

        //добавляем фильтр исключающий одноразовые промокоды
        promocodes =
            defaultPromocodeService.getPromocodes(true, true, null, null, null, null, PageRequest.of(1, 10));
        assertThat(promocodes.getTotalAmount()).isLessThanOrEqualTo(totalCount - 1);

        //добавляем фильтр НЕисключающий одноразовые промокоды
        promocodes =
            defaultPromocodeService.getPromocodes(true, false, null, null, null, null, PageRequest.of(1, 10));
        assertThat(promocodes.getTotalAmount()).isEqualTo(totalCount);
    }

    @Test
    public void getPromocodesWithCreatedAtFilterAndSort() {
        // добавляем фильтр From по дате создания промокода
        LocalDate from = LocalDate.ofYearDay(2020, 1);
        Page<PromoCode> promocodes =
            defaultPromocodeService.getPromocodes(true, null, from, null, null, null, PageRequest.of(1, 10));
        for (int i = 0; i < promocodes.getItems().size(); i++) {
            PromoCode promoCode = promocodes.getItems().get(i);
            assertThat(promoCode.getCreatedAt()).isAfterOrEqualTo(from.atStartOfDay(ZoneOffset.UTC));
        }

        // добавляем фильтр To по дате создания промокода
        LocalDate to = LocalDate.ofYearDay(2020, 180);
        promocodes =
            defaultPromocodeService.getPromocodes(true, null, null, to, null, null, PageRequest.of(1, 10));
        for (int i = 0; i < promocodes.getItems().size(); i++) {
            PromoCode promoCode = promocodes.getItems().get(i);
            assertThat(promoCode.getCreatedAt()).isBeforeOrEqualTo(to.atStartOfDay(ZoneOffset.UTC));
        }

        // добавляем фильтр по дате создания промокода
        from = LocalDate.ofYearDay(2020, 1);
        to = LocalDate.ofYearDay(2020, 365);
        promocodes =
            defaultPromocodeService.getPromocodes(true, null, from, to, null, null, PageRequest.of(1, 10));
        for (int i = 0; i < promocodes.getItems().size(); i++) {
            PromoCode promoCode = promocodes.getItems().get(i);
            assertThat(promoCode.getCreatedAt()).isAfterOrEqualTo(from.atStartOfDay(ZoneOffset.UTC));
            assertThat(promoCode.getCreatedAt()).isBeforeOrEqualTo(to.atStartOfDay(ZoneOffset.UTC));
        }

        // проверим сортировку по createdAt вместе с фильтром
        promocodes =
            defaultPromocodeService.getPromocodes(true, null, from, to, null, "createdAt_desc", PageRequest.of(1, 10));
        ZonedDateTime prevCodeCreatedAt = promocodes.getItems().get(0).getCreatedAt();
        for (int i = 1; i < promocodes.getItems().size(); i++) {
            PromoCode promoCode = promocodes.getItems().get(i);
            assertThat(promoCode.getCreatedAt()).isBeforeOrEqualTo(prevCodeCreatedAt);
            prevCodeCreatedAt = promoCode.getCreatedAt();
        }

        promocodes =
            defaultPromocodeService.getPromocodes(true, null, from, to, null, "createdAt_asc", PageRequest.of(1, 10));
        prevCodeCreatedAt = promocodes.getItems().get(0).getCreatedAt();
        for (int i = 1; i < promocodes.getItems().size(); i++) {
            PromoCode promoCode = promocodes.getItems().get(i);
            assertThat(promoCode.getCreatedAt()).isAfterOrEqualTo(prevCodeCreatedAt);
            prevCodeCreatedAt = promoCode.getCreatedAt();
        }
    }
}
