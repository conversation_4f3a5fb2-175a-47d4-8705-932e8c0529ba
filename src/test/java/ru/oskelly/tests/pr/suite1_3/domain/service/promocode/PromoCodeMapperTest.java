package ru.oskelly.tests.pr.suite1_3.domain.service.promocode;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import lombok.val;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.applyrule.ApplyRuleRepository;
import su.reddot.domain.model.applyRule.ApplyRule;
import su.reddot.domain.model.applyRule.ApplyRuleFilterType;
import su.reddot.domain.model.applyRule.ApplyRuleFilterValue;
import su.reddot.domain.model.discount.AbsolutePromoCode;
import su.reddot.domain.model.discount.FractionalPromoCode;
import su.reddot.domain.model.discount.PromoCode;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.adminpanel.tag.UserCommonTagService;
import su.reddot.domain.service.adminpanel.tag.domain.UserCommonTagDTO;
import su.reddot.domain.service.dto.UserDTO;
import su.reddot.domain.service.dto.promocode.PromocodeDTOBase;
import su.reddot.domain.service.dto.promocode.PromocodeDTOFull;
import su.reddot.domain.service.dto.promocode.PromocodeFilterDTO;
import su.reddot.domain.service.dto.promocode.PromocodeReferenceLinkDTO;
import su.reddot.domain.service.dto.promocode.PromocodeTypeDTO;
import su.reddot.domain.service.promocode.PromoCodeMapper;
import su.reddot.domain.service.promocode.model.PromoCodeAppliedNumberType;
import su.reddot.domain.service.promocode.model.PromoCodeResetRange;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.configuration.OskellyApplication;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = {OskellyApplication.class})
@ExtendWith(SpringExtension.class)
@ActiveProfiles(AbstractSpringTest.testProfiles)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
class PromoCodeMapperTest {
    public static final ZoneId ZONE_UTC = ZoneId.of("UTC");
    @Autowired
    PromoCodeMapper mapper;
    @MockBean
    UserService userService;
    @MockBean
    ApplyRuleRepository applyRuleRepository;
    @MockBean
    UserCommonTagService userCommonTagService;

    /**
     * {@link PromoCodeMapperTest#mapToDtoBase}
     */
    private static Stream<PromoCode> promoCodeProvider() {
        return Stream.of(
            new FractionalPromoCode()
                .setValue(new BigDecimal("0.22")),
            new AbsolutePromoCode()
                .setValue(new BigDecimal("220"))
        );
    }

    @Test
    void mapFromDtoBaseBusinessFields(){
        PromocodeDTOBase promocodeDTOFull = new PromocodeDTOFull()
            .setCode("123")
            .setCreatedAt(ZonedDateTime.of(2025, 1, 1, 1, 1, 2, 0, ZONE_UTC))
            .setNumberOfApplies(3)
            .setAmount(BigDecimal.valueOf(123))
            .setBarter(true)
            .setBeginPrice(BigDecimal.TEN)
            .setDealsCount(2)
            .setDescription("description")
            .setExpiresAt(ZonedDateTime.of(2025, 1, 1, 1, 1, 1, 0, ZONE_UTC))
            .setId(321)
            .setResetRange(PromoCodeResetRange.WEEK)
            .setStartsAt(ZonedDateTime.of(2025, 1, 1, 1, 1, 3, 0, ZONE_UTC))
            .setType(new PromocodeTypeDTO().setId(1).setName("name"));

        AbsolutePromoCode promoCodeEntity = new AbsolutePromoCode();
        mapper.copyPromoCodeDtoToEntity(promocodeDTOFull, promoCodeEntity);

        assertEquals(0, promocodeDTOFull.getAmount().compareTo(promoCodeEntity.getValue()));
        assertEquals(promocodeDTOFull.getBarter(), promoCodeEntity.getBarter());
        assertEquals(0, promocodeDTOFull.getBeginPrice().compareTo(promoCodeEntity.getBeginPrice()));
        assertEquals(promocodeDTOFull.getCode(), promoCodeEntity.getCode());
        assertEquals(promocodeDTOFull.getDescription(), promoCodeEntity.getDescription());
        assertEquals(promocodeDTOFull.getExpiresAt(), promoCodeEntity.getExpiresAt());
        assertEquals(promocodeDTOFull.getNumberOfApplies(), promoCodeEntity.getNumberOfApplies());
        assertEquals(promocodeDTOFull.getResetRange(), promoCodeEntity.getResetRange());
        assertEquals(promocodeDTOFull.getStartsAt(), promoCodeEntity.getStartsAt());
    }

    @ParameterizedTest
    @MethodSource("promoCodeProvider")
    @DisplayName("Маппинг промокода в PromocodeDTOBase")
    void mapToDtoBase(PromoCode mappingFrom) {
        // Arrange
        mappingFrom.setCode("test");
        mappingFrom.setNumberOfApplies(1);
        mappingFrom.setNumberOfAppliesType(PromoCodeAppliedNumberType.GLOBALLY);
        mappingFrom.setCreatedAt(ZonedDateTime.now());
        mappingFrom.setExpiresAt(ZonedDateTime.now());
        mappingFrom.setStartsAt(ZonedDateTime.now());
        mappingFrom.setCreatedBy(new User());
        mappingFrom.setResetRange(PromoCodeResetRange.MONTH);

        when(userService.getUserDTO(any(User.class)))
            .thenReturn(new UserDTO());

        // Act
        final PromocodeDTOBase promoCode = mapper.copyPromocodeToPromocodeDTOBase(mappingFrom, new PromocodeDTOBase());

        // Assert
        assertThat(promoCode.getNumberOfApplies()).isEqualTo(1);
        assertThat(promoCode.getAppliedNumberType()).isEqualTo(PromoCodeAppliedNumberType.GLOBALLY);
        assertThat(promoCode.getResetRange()).isEqualTo(PromoCodeResetRange.MONTH);
        assertThat(promoCode.getCreatedAt()).isNotNull();
        assertThat(promoCode.getExpiresAt()).isNotNull();
        assertThat(promoCode.getStartsAt()).isNotNull();
        assertThat(promoCode.getCreatedBy()).isNotNull();

        if (mappingFrom instanceof FractionalPromoCode) {
            assertThat(promoCode.getPercent()).isEqualTo(new BigDecimal("22.00"));
            assertThat(promoCode.getAmount()).isNull();
        } else {
            assertThat(promoCode.getAmount()).isEqualTo(new BigDecimal("220.00"));
            assertThat(promoCode.getPercent()).isNull();
        }
    }

    @Test
    @DisplayName("Маппинг промокода в PromocodeDTOFull без связанного правила даёт пустые списки")
    void mapToFullDtoWithNoRule() {
        // Arrange
        final AbsolutePromoCode promoCode = new AbsolutePromoCode();
        promoCode.setValue(new BigDecimal("123"));

        // Act
        final PromocodeDTOFull promocodeDTOFull = mapper.copyPromocodeToPromocodeDTOFull(promoCode);

        // Assert
        assertThat(promocodeDTOFull.getBrandsList()).isEmpty();
        assertThat(promocodeDTOFull.getCategoriesList()).isEmpty();
        assertThat(promocodeDTOFull.getBuyersList()).isEmpty();
        assertThat(promocodeDTOFull.getSellersList()).isEmpty();
        assertThat(promocodeDTOFull.getExceptSellersList()).isEmpty();
        assertThat(promocodeDTOFull.getOrdersCountFilters()).isEmpty();
        assertThat(promocodeDTOFull.getOrderAmountFilters()).isEmpty();
        assertThat(promocodeDTOFull.getOrdersAmountSummaryFilters()).isEmpty();

        verifyNoInteractions(applyRuleRepository);
    }

    @Test
    @DisplayName("Маппинг промокода в PromocodeDTOFull")
    void mapToFullDto() {
        // Arrange
        final AbsolutePromoCode promoCode = new AbsolutePromoCode();
        promoCode.setValue(new BigDecimal("123"));
        promoCode.setApplyRuleId(1L);
        promoCode.setResetRange(PromoCodeResetRange.MONTH);
        promoCode.setNumberOfApplies(2);

        final ApplyRule applyRule = new ApplyRule();
        applyRule.setFilterValues(ImmutableSet.<ApplyRuleFilterValue>builder()
            .add(ApplyRuleFilterValue.builder()
                .filterType(ApplyRuleFilterType.BRAND)
                .value(1L)
                .build())
            .add(ApplyRuleFilterValue.builder()
                .filterType(ApplyRuleFilterType.CATEGORY)
                .value(2L)
                .build())
            .add(ApplyRuleFilterValue.builder()
                .filterType(ApplyRuleFilterType.BUYER)
                .value(3L)
                .build())
            .add(ApplyRuleFilterValue.builder()
                .filterType(ApplyRuleFilterType.SELLER)
                .value(4L)
                .build())
            .add(ApplyRuleFilterValue.builder()
                .filterType(ApplyRuleFilterType.EXCEPT_SELLER)
                .value(5L)
                .build())
            .add(ApplyRuleFilterValue.builder()
                .filterType(ApplyRuleFilterType.BUYER_ORDERS_COUNT)
                .value(6L)
                .build())
            .add(ApplyRuleFilterValue.builder()
                .filterType(ApplyRuleFilterType.ORDER_AMOUNT)
                .value(7L)
                .build())
            .add(ApplyRuleFilterValue.builder()
                .filterType(ApplyRuleFilterType.BUYER_ORDERS_AMOUNT)
                .value(8L)
                .build())
            .add(ApplyRuleFilterValue.builder()
                .filterType(ApplyRuleFilterType.EXCEPT_BRAND)
                .value(9L)
                .build())
            .add(ApplyRuleFilterValue.builder()
                .filterType(ApplyRuleFilterType.EXCEPT_CATEGORY)
                .value(10L)
                .build())
            .add(ApplyRuleFilterValue.builder()
                .filterType(ApplyRuleFilterType.EXCEPT_PRODUCTS)
                .value(11L)
                .build())
            .add(ApplyRuleFilterValue.builder()
                .filterType(ApplyRuleFilterType.PRODUCTS)
                .value(12L)
                .build())
            .add(ApplyRuleFilterValue.builder()
                .filterType(ApplyRuleFilterType.BUYER_ORDER_NUMBERS)
                .value(13L)
                .build())
            .add(ApplyRuleFilterValue.builder()
                .filterType(ApplyRuleFilterType.IOS_APPLICATION)
                .value(1L)
                .build())
            .add(ApplyRuleFilterValue.builder()
                .filterType(ApplyRuleFilterType.ANDROID_APPLICATION)
                .value(1L)
                .build())
            .add(ApplyRuleFilterValue.builder()
                .filterType(ApplyRuleFilterType.EXCEPT_PRODUCT_CONDITION)
                .value(14L)
                .build())
            .add(ApplyRuleFilterValue.builder()
                .filterType(ApplyRuleFilterType.PRODUCT_CONDITION)
                .value(15L)
                .build())
            .add(ApplyRuleFilterValue.builder()
                .filterType(ApplyRuleFilterType.USER_LOYALTY_TAG)
                .value(16L)
                .build())
            .build());
        when(applyRuleRepository.getOne(1L))
            .thenReturn(applyRule);
        when(userService.getUserDTO(anyLong()))
            .thenReturn(new UserDTO());
        when(userCommonTagService.getTagById(anyLong()))
            .thenReturn(new UserCommonTagDTO());

        // Act
        final PromocodeDTOFull promocodeDTOFull = mapper.copyPromocodeToPromocodeDTOFull(promoCode);

        // Assert
        val referenceLinkToValue = ImmutableMap.<Collection<PromocodeReferenceLinkDTO>, Long>builder()
            .put(promocodeDTOFull.getBrandsList(), 1L)
            .put(promocodeDTOFull.getCategoriesList(), 2L)
            .put(promocodeDTOFull.getBuyersList(), 3L)
            .put(promocodeDTOFull.getSellersList(), 4L)
            .put(promocodeDTOFull.getExceptSellersList(), 5L)
            .put(promocodeDTOFull.getExceptBrandsList(), 9L)
            .put(promocodeDTOFull.getExceptCategoriesList(), 10L)
            .put(promocodeDTOFull.getExceptProductList(), 11L)
            .put(promocodeDTOFull.getProductList(), 12L)
            .put(promocodeDTOFull.getExceptProductConditionList(), 14L)
            .put(promocodeDTOFull.getProductConditionList(), 15L)
            .put(promocodeDTOFull.getUserLoyaltyTagList(), 16L)
            .build();
        for (val entry : referenceLinkToValue.entrySet()) {
            assertThat(entry.getKey())
                .hasSize(1)
                .extracting(PromocodeReferenceLinkDTO::getId)
                .containsExactly(entry.getValue());
        }

        val promoCodeFilterToValue = ImmutableMap.<Collection<PromocodeFilterDTO>, Long>builder()
            .put(promocodeDTOFull.getOrdersCountFilters(), 6L)
            .put(promocodeDTOFull.getOrderAmountFilters(), 7L)
            .put(promocodeDTOFull.getOrdersAmountSummaryFilters(), 8L)
            .put(promocodeDTOFull.getOrdersRelativeNumbers(), 13L)
            .build();
        for (val entry : promoCodeFilterToValue.entrySet()) {
            assertThat(entry.getKey())
                .hasSize(1)
                .extracting(PromocodeFilterDTO::getValue)
                .containsExactly(entry.getValue());
        }

        assertThat(promocodeDTOFull.isIosApplication()).isTrue();
        assertThat(promocodeDTOFull.isAndroidApplication()).isTrue();
        assertThat(promocodeDTOFull.getResetRange()).isEqualTo(PromoCodeResetRange.MONTH);
        assertThat(promocodeDTOFull.getNumberOfApplies()).isEqualTo(2);
    }
}
