package ru.oskelly.tests.pr.suite1_3.domain.service.bonuses;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.pr.common.bonuses.BonusesControllerApiStub;
import ru.oskelly.tests.pr.common.bonuses.BonusesServiceTestConfiguration;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.bonuses.OrderBonusesTransactionRepository;
import su.reddot.domain.dao.bonuses.UserBonusesAccountRepository;
import su.reddot.domain.dao.bonuses.UserLoyaltyExtendedInfoRepository;
import su.reddot.domain.dao.notification.NotificationRepository;
import su.reddot.domain.exception.OskellyException;
import su.reddot.domain.model.bonuses.OrderBonusesTransaction;
import su.reddot.domain.model.bonuses.OrderBonusesTransactionParams;
import su.reddot.domain.model.bonuses.OrderBonusesTransactionParamsProductItemId;
import su.reddot.domain.model.bonuses.UserBonusesAccount;
import su.reddot.domain.model.bonuses.UserLoyaltyExtendedInfo;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.notification.profile.BirthdayPromoCodeNotification;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.bonuses.BonusesControllerAPI;
import su.reddot.domain.service.bonuses.BonusesService;
import su.reddot.domain.service.bonuses.model.TransactionDTO;
import su.reddot.domain.service.bonuses.transferprograms.BirthdayBonusesTransferProgram;
import su.reddot.domain.service.bonuses.transferprograms.WelcomeBonusesTransferProgram;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.domain.service.dto.bonuses.BonusesBalanceDTO;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.presentation.api.v2.bonuses.dto.offline.BonusesOfflineSplitItem;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

import static java.math.BigDecimal.ZERO;
import static java.util.concurrent.TimeUnit.SECONDS;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@SpringBootTest(classes =  {OskellyApplication.class, BonusesServiceTestConfiguration.class})
@ExtendWith(SpringExtension.class)
@ActiveProfiles(AbstractSpringTest.testProfiles)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
@Slf4j
public class DefaultBonusesServiceTest {

    private static final Long BONUSES_TEST_ORDER_ID = 1L;
    private static final BigDecimal TEST_BONUSES_AMOUNT = new BigDecimal(100);
    private static final BigDecimal TEST_MONEY_AMOUNT = new BigDecimal(50);
    private static final Long BONUSES_TEST_EXCEPTION_ORDER_ID = 2L;

    @Autowired
    private BonusesService bonusesService;

    @Autowired
    private OrderBonusesTransactionRepository orderBonusesTransactionRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private CommissionGridService commissionGridService;

    @Autowired
    private WelcomeBonusesTransferProgram welcomeBonusesTransferProgram;

    @Autowired
    private BirthdayBonusesTransferProgram birthdayBonusesTransferProgram;

    @Autowired
    private UserBonusesAccountRepository origUserBonusesAccountRepository;

    @Autowired
    private BonusesControllerAPI bonusesControllerAPI;

    @MockBean
    private UserBonusesAccountRepository userBonusesAccountRepository;

    @Autowired
    private UserLoyaltyExtendedInfoRepository userLoyaltyExtendedInfoRepository;

    @Autowired
    private NotificationRepository<Notification> notificationRepository;

    private User currentUser;

    @BeforeEach
    public void init() {
        orderBonusesTransactionRepository.deleteAll();
        currentUser = createUser(true);
        UserBonusesAccount userBonusesAccount = new UserBonusesAccount();
        userBonusesAccount.setBonusesAccountId(UUID.randomUUID().toString());
        when(userBonusesAccountRepository.findByUserId(any(Long.class)))
                .thenReturn(Optional.of(userBonusesAccount));
    }

    @AfterEach
    public void cleanup() {
        orderBonusesTransactionRepository.deleteAll();
    }

    @Test
    public void testGetOrCreateUserBonusesAccount() throws ExecutionException, InterruptedException, TimeoutException {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(5);
        executor.setThreadNamePrefix("TestBonusesPool-");
        executor.initialize();

        UserBonusesAccountRepository mockUserBonusesAccountRepository = mock(UserBonusesAccountRepository.class);

        ReflectionTestUtils.setField(bonusesService, "userBonusesAccountRepository", mockUserBonusesAccountRepository);

        doAnswer(
                invocation -> origUserBonusesAccountRepository.findByUserId(currentUser.getId())
        ).when(mockUserBonusesAccountRepository).findByUserId(any(Long.class));
        doAnswer(invocation -> {
            origUserBonusesAccountRepository.lockTable();
            return null;
        }).when(mockUserBonusesAccountRepository).lockTable();
        doAnswer(
                invocation -> {
                    try {
                        Thread.sleep(10000L);
                    } catch (InterruptedException e) {
                        log.warn("Interrupt error");
                    }
                    return origUserBonusesAccountRepository.save(invocation.getArgument(0));
                }
        ).when(mockUserBonusesAccountRepository).save(any(UserBonusesAccount.class));

        Boolean usersAreEqual = CompletableFuture
                .supplyAsync(
                        () -> bonusesService.getOrCreateUserBonusesAccount(currentUser.getId()),
                        executor
                )
                .thenCombineAsync(
                        CompletableFuture
                                .supplyAsync(
                                        () -> bonusesService.getOrCreateUserBonusesAccount(currentUser.getId()),
                                        executor
                                ),
                        (user1, user2) -> Objects.equals(user1.getId(), user2.getId()),
                        executor
                )
                .get(60, SECONDS);
        assertThat(usersAreEqual).isTrue();
    }

    @Test
    public void testTransferBonusesManual() {
        BigDecimal bonusesAmount = TEST_BONUSES_AMOUNT;
        BigDecimal moneyAmount = TEST_MONEY_AMOUNT;
        BonusesBalanceDTO balance0 = bonusesService.getBalance(currentUser.getId());

        //проверка ручной транзакции со сгораемыми бонусами после вызова бонусного сервиса
        try {
            bonusesService.transferBonusesManual(currentUser.getId(), bonusesAmount, null, "", "", null);
            assertThat(true).isTrue();
        } catch (Exception e) {
            assertThat(false).isTrue();
        }
        BonusesBalanceDTO balance1 = bonusesService.getBalance(currentUser.getId());
        assertThat(balance1.getAmount().getBonuses().compareTo(balance0.getAmount().getBonuses().add(bonusesAmount)) == 0).isTrue();

        //проверка ручной транзакции с НЕсгораемыми бонусами после вызова бонусного сервиса
        try {
            bonusesService.transferBonusesManual(currentUser.getId(), null, moneyAmount, "", "", null);
            assertThat(true).isTrue();
        } catch (Exception e) {
            assertThat(false).isTrue();
        }
        BonusesBalanceDTO balance2 = bonusesService.getBalance(currentUser.getId());
        assertThat(balance2.getAmount().getMoney().compareTo(balance0.getAmount().getMoney().add(moneyAmount)) == 0).isTrue();
    }

    @Test
    public void testTransferBonusesWelcome() {
        User newUser = createUser(true);

        //проверка начисления
        BonusesBalanceDTO balance0 = bonusesService.getBalance(newUser.getId());
        BigDecimal programAmount = welcomeBonusesTransferProgram.getSum();

        try {
            bonusesService.transferBonusesWelcome(newUser.getId());
            assertThat(true).isTrue();
        } catch (Exception e) {
            assertThat(false).isTrue();
        }
        BonusesBalanceDTO balance1 = bonusesService.getBalance(newUser.getId());
        assertThat(balance1.getAmount().getBonuses().compareTo(balance0.getAmount().getBonuses().add(programAmount)) == 0).isTrue();

        //повторное начисление велком бонусов
        try {
            bonusesService.transferBonusesWelcome(newUser.getId());
            assertThat(false).isTrue();
        } catch (Exception e) {
            assertThat(true).isTrue();
        }

        //проверка попытки начисления, в случае если программа начисления выключена
        ReflectionTestUtils.setField(welcomeBonusesTransferProgram, "enabled", false);
        try {
            bonusesService.transferBonusesWelcome(newUser.getId());
            assertThat(true).isTrue();
        } catch (Exception e) {
            assertThat(false).isTrue();
        }
        BonusesBalanceDTO balance2 = bonusesService.getBalance(newUser.getId());
        assertThat(balance2.getAmount().getBonuses().compareTo(balance1.getAmount().getBonuses()) == 0).isTrue();
    }

    @Test
    @Transactional
    public void testTransferBonusesBirthdayUsersSelection() {
        LocalDate d = LocalDate.of(1990, Month.FEBRUARY, 1);
        LocalDateTime dt = d.atStartOfDay();
        ZonedDateTime dz = ZonedDateTime.of(dt, ZonedDateTime.now().getZone());
        ZonedDateTime borderDate = dz.minusDays(birthdayBonusesTransferProgram.getCheckPeriodInDays());

        User userNotSuitableMinus100 = createUser(true, dt.minusDays(100));
        User userNotSuitablePlus8 = createUser(true, dt.plusDays(8));
        User userNotSuitablePlus1ButNotified = createUser(true, dt.plusDays(1));
        createUserBonusesExtendedInfo(userNotSuitablePlus1ButNotified.getId(), dz);
        User userNotSuitablePlus1ButWithPromo = createUser(true, dt.plusDays(1));
        createPromocodeNotification(userNotSuitablePlus1ButWithPromo.getId(), dz.minusDays(10));

        User userSuitable1 = createUser(true, dt.plusDays(6));
        User userSuitable2 = createUser(true, dt);
        User userSuitable3 = createUser(true, dt.plusDays(1));
        createUserBonusesExtendedInfo(userSuitable3.getId(), dz.minusDays(360));
        User userSuitable4 = createUser(true, dt.plusDays(1));
        createPromocodeNotification(userSuitable4.getId(), dz.minusDays(360));

        List<String> birthdays = Arrays.asList("0201", "0202", "0203", "0204", "0205", "0206", "0207", "0208");

        List<String> methodResult = birthdayBonusesTransferProgram.getBirthdays(d);
        assertThat(methodResult).isNotNull();
        assertThat(methodResult.size()).isEqualTo(birthdays.size());
        assertThat(birthdays.stream().filter(b -> methodResult.stream().anyMatch(mr -> Objects.equals(mr, b))).count()).isEqualTo(birthdays.size());

        List<Long> userIds = userLoyaltyExtendedInfoRepository.findUserIdsToTransferBirthdayBonuses(birthdays, borderDate);

        checkContains(userIds, userSuitable1.getId());
        checkContains(userIds, userSuitable2.getId());
        checkContains(userIds, userSuitable3.getId());
        checkContains(userIds, userSuitable4.getId());

        checkNotContains(userIds, userNotSuitableMinus100.getId());
        checkNotContains(userIds, userNotSuitablePlus8.getId());
        checkNotContains(userIds, userNotSuitablePlus1ButNotified.getId());
        checkNotContains(userIds, userNotSuitablePlus1ButWithPromo.getId());
    }

    private void createUserBonusesExtendedInfo(Long userId, ZonedDateTime time) {
        UserLoyaltyExtendedInfo n = new UserLoyaltyExtendedInfo();
        n.setUserId(userId);
        n.setBirthdayBonusesTransferredAt(time);
        userLoyaltyExtendedInfoRepository.save(n);
    }

    private void createPromocodeNotification(Long userId, ZonedDateTime time) {
        BirthdayPromoCodeNotification notification = new BirthdayPromoCodeNotification();
        Map<String, Object> params = new HashMap<>();
        notification.setParamsFromMap(params);
        notification.setUser(new User().setId(userId));
        notification.setCreateTime(time);
        notificationRepository.save(notification);
    }

    private void checkContains(List<Long> list, Long value) {
        assertThat(list.stream().filter(l -> Objects.equals(l, value)).count() == 1L).isTrue();
    }

    private void checkNotContains(List<Long> list, Long value) {
        assertThat(list.stream().noneMatch(l -> Objects.equals(l, value))).isTrue();
    }

    @Test
    public void testTransferBonusesBirthday() {
        User newUser = createUser(true, LocalDateTime.now());

        BonusesBalanceDTO balance0 = bonusesService.getBalance(newUser.getId());
        BigDecimal programAmount = new BigDecimal(1000);

        //обычное начисление бонусов
        try {
            bonusesService.transferBonusesBirthday(newUser.getId(), programAmount);
            assertThat(true).isTrue();
        } catch (Exception e) {
            assertThat(false).isTrue();
        }
        BonusesBalanceDTO balance1 = bonusesService.getBalance(newUser.getId());
        assertThat(balance1.getAmount().getBonuses().compareTo(balance0.getAmount().getBonuses().add(programAmount)) == 0).isTrue();

        //повторное начисление (ошибка выбрасывается и баланс не меняется)
        try {
            bonusesService.transferBonusesBirthday(newUser.getId(), programAmount);
            assertThat(false).isTrue();
        } catch (Exception e) {
            assertThat(true).isTrue();
        }
        BonusesBalanceDTO balance2 = bonusesService.getBalance(newUser.getId());
        assertThat(balance2.getAmount().getBonuses().compareTo(balance1.getAmount().getBonuses()) == 0).isTrue();

        ReflectionTestUtils.setField(birthdayBonusesTransferProgram, "enabled", false);

        try {
            bonusesService.transferBonusesBirthday(newUser.getId(), programAmount);
            assertThat(true).isTrue();
        } catch (Exception e) {
            assertThat(false).isTrue();
        }
        BonusesBalanceDTO balance3 = bonusesService.getBalance(newUser.getId());
        assertThat(balance3.getAmount().getBonuses().compareTo(balance2.getAmount().getBonuses()) == 0).isTrue();
    }

    @Test
    public void testTransferBonuses() {
        Long orderId = BONUSES_TEST_ORDER_ID;
        BigDecimal bonusesAmount = TEST_BONUSES_AMOUNT; //c(100);
        BigDecimal moneyAmount = TEST_MONEY_AMOUNT; //c(50);
        //проверка сохранения транзакции после вызова бонусного сервиса
        OrderBonusesTransaction trnFromService = bonusesService.transferBonusesOrder(currentUser, bonusesAmount, moneyAmount, "", "", null, orderId, null);
        assertThat(trnFromService).isNotNull();

        OrderBonusesTransaction trnFromDB = getTransaction(
                orderBonusesTransactionRepository.findByOrderIdAndUserIdAndTrnType(orderId, currentUser.getId(), TransactionDTO.TrnTypeEnum.TRANSFER)
        );
        assertThat(trnFromDB.getBonusAmount().compareTo(bonusesAmount)).isEqualTo(0);
        assertThat(trnFromDB.getMoneyAmount().compareTo(moneyAmount)).isEqualTo(0);

        //проверка, что повторный вызов сохранения по этому же заказу вернет предыдущую транзакцию
        trnFromService = bonusesService.transferBonusesOrder(currentUser, bonusesAmount, moneyAmount, "", "", null, orderId, null);
        assertThat(trnFromService).isNotNull();

        OrderBonusesTransaction trnFromDB2 = getTransaction(
                orderBonusesTransactionRepository.findByOrderIdAndUserIdAndTrnType(orderId, currentUser.getId(), TransactionDTO.TrnTypeEnum.TRANSFER)
        );
        assertThat(trnFromDB2.getId().equals(trnFromDB.getId())).isTrue();

        //проверка, что при наличии ошибки, будет сохранена ошибочная транзакция
        orderId = BONUSES_TEST_EXCEPTION_ORDER_ID;
        trnFromService = bonusesService.transferBonusesOrder(currentUser, bonusesAmount, moneyAmount, "", "", null, orderId, null);
        assertThat(trnFromService).isNotNull();

        OrderBonusesTransaction trnFromDB3 = getTransaction(
                orderBonusesTransactionRepository.findByOrderIdAndUserIdAndTrnType(orderId, currentUser.getId(), TransactionDTO.TrnTypeEnum.TRANSFER)
        );
        assertThat(trnFromDB3.getErrorText()).isNotNull();

        //проверка, что при повторном вызове и при наличии ошибки, будет возвращена та же ошибочная транзакция
        trnFromService = bonusesService.transferBonusesOrder(currentUser, bonusesAmount, moneyAmount, "", "", BONUSES_TEST_EXCEPTION_ORDER_ID, orderId, null);
        assertThat(trnFromService).isNotNull();

        OrderBonusesTransaction trnFromDB4 = getTransaction(
                orderBonusesTransactionRepository.findByOrderIdAndUserIdAndTrnType(orderId, currentUser.getId(), TransactionDTO.TrnTypeEnum.TRANSFER)
        );
        assertThat(trnFromDB4.getId().equals(trnFromDB3.getId())).isTrue();

        //проверка, что транзакция проходит, если убрать выброс исключения и вызвать специальный метод повторного вызова
        getBonusesControllerApiStub().setIsExceptionThrowing(false);
        trnFromService = bonusesService.transferBonusesAfterError(trnFromDB4.getId());
        assertThat(trnFromService).isNotNull();

        OrderBonusesTransaction trnFromDB5 = getTransaction(
                Collections.singletonList(orderBonusesTransactionRepository.findById(trnFromService.getId()).orElseThrow(RuntimeException::new))
        );
        assertThat(trnFromDB5.getId().equals(trnFromDB3.getId())).isTrue();
        assertThat(trnFromDB5.getTrnState()).isNotNull();
        getBonusesControllerApiStub().setIsExceptionThrowing(true);
    }

    @Test
    public void testWithdrawBonusesManual() {
        BigDecimal bonusesAmount = TEST_BONUSES_AMOUNT;
        BigDecimal moneyAmount = TEST_MONEY_AMOUNT;
        BonusesBalanceDTO balance0 = bonusesService.getBalance(currentUser.getId());

        //проверка ручной транзакции со сгораемыми бонусами после вызова бонусного сервиса
        try {
            bonusesService.withdrawBonusesManual(currentUser.getId(), bonusesAmount, null, "", "");
            assertThat(true).isTrue();
        } catch (Exception e) {
            assertThat(false).isTrue();
        }
        BonusesBalanceDTO balance1 = bonusesService.getBalance(currentUser.getId());
        assertThat(balance1.getAmount().getBonuses().compareTo(balance0.getAmount().getBonuses().subtract(bonusesAmount)) == 0).isTrue();

        //проверка ручной транзакции с НЕсгораемыми бонусами после вызова бонусного сервиса
        try {
            bonusesService.withdrawBonusesManual(currentUser.getId(), null, moneyAmount, "", "");
            assertThat(true).isTrue();
        } catch (Exception e) {
            assertThat(false).isTrue();
        }
        BonusesBalanceDTO balance2 = bonusesService.getBalance(currentUser.getId());
        assertThat(balance2.getAmount().getMoney().compareTo(balance0.getAmount().getMoney().subtract(moneyAmount)) == 0).isTrue();
    }

    @Test
    public void testWithdrawBonusesHold() {
        Long orderId = BONUSES_TEST_ORDER_ID;
        BigDecimal bonusesAmount = TEST_BONUSES_AMOUNT; //c(100);
        BigDecimal moneyAmount = TEST_MONEY_AMOUNT; //c(50);
        //проверка сохранения транзакции после вызова бонусного сервиса
        OrderBonusesTransaction trnFromService = bonusesService.withdrawBonusesHold(
                currentUser, bonusesAmount, moneyAmount, "", "", null, orderId, null
        );
        assertThat(trnFromService).isNotNull();

        OrderBonusesTransaction trnFromDB = getTransaction(
                orderBonusesTransactionRepository.findByOrderIdAndUserIdAndTrnType(orderId, currentUser.getId(), TransactionDTO.TrnTypeEnum.WITHDRAW)
        );
        assertThat(trnFromDB.getBonusAmount().compareTo(bonusesAmount)).isEqualTo(0);
        assertThat(trnFromDB.getMoneyAmount().compareTo(moneyAmount)).isEqualTo(0);

        //проверка, что повторный вызов сохранения по этому же заказу вернет предыдущую транзакцию
        trnFromService = bonusesService.withdrawBonusesHold(
                currentUser, bonusesAmount, moneyAmount, "", "", null, orderId, null
        );
        assertThat(trnFromService).isNotNull();

        OrderBonusesTransaction trnFromDB2 = getTransaction(
                orderBonusesTransactionRepository.findByOrderIdAndUserIdAndTrnType(orderId, currentUser.getId(), TransactionDTO.TrnTypeEnum.WITHDRAW)
        );
        assertThat(trnFromDB2.getId().equals(trnFromDB.getId())).isTrue();
        assertThat(TransactionDTO.StateEnum.HOLD.equals(trnFromDB2.getTrnState())).isTrue();
        assertThat(trnFromDB2.getTrnState().equals(trnFromDB.getTrnState())).isTrue();

        //проверка, что при наличии ошибки, будет выброшено исключение
        orderId = BONUSES_TEST_EXCEPTION_ORDER_ID;
        try {
            bonusesService.withdrawBonusesHold(
                    currentUser, bonusesAmount, moneyAmount, "", "", null, orderId, null
            );
            assertThat(false).isTrue();
        } catch (Exception e) {
            assertThat(e instanceof OskellyException).isTrue();
        }
    }

    @Test
    public void testWithdrawBonusesCancel() {
        processHoldTransaction(TransactionDTO.StateEnum.CANCELED, c(100), c(50));
    }

    @Test
    public void testWithdrawBonusesCommit() {
        processHoldTransaction(TransactionDTO.StateEnum.COMMITTED, c(100), c(50));
    }

    private void processHoldTransaction(TransactionDTO.StateEnum targetState, BigDecimal bonusesAmount, BigDecimal moneyAmount) {
        Long orderId = BONUSES_TEST_ORDER_ID;

        //Создание транзакции списания в состоянии HOLD
        createHoldTransaction(bonusesAmount, moneyAmount, orderId);

        //выполнение нужной операции с транзакцией (перевод в targetState)
        OrderBonusesTransaction trnFromService = processHoldTransactionInternal(targetState, orderId);

        OrderBonusesTransaction trnFromDB2 = getTransaction(
                orderBonusesTransactionRepository.findByOrderIdAndUserIdAndTrnType(orderId, currentUser.getId(), TransactionDTO.TrnTypeEnum.WITHDRAW)
        );
        assertThat(Objects.equals(trnFromService.getId(), trnFromDB2.getId())).isTrue();
        assertThat(targetState.equals(trnFromDB2.getTrnState())).isTrue();

        //проверка, что с данной транзакцией больше нельзя сделать ничего
        try {
            processHoldTransactionInternal(targetState, orderId);
            assertThat(false).isTrue();
        } catch (Exception e) {
            assertThat(e instanceof OskellyException).isTrue();
        }
    }

    private void createHoldTransaction(BigDecimal bonusesAmount, BigDecimal moneyAmount, Long orderId) {
        OrderBonusesTransaction trnFromService = bonusesService.withdrawBonusesHold(
                currentUser, bonusesAmount, moneyAmount, "", "", null, orderId, null
        );
        assertThat(trnFromService).isNotNull();

        OrderBonusesTransaction trnFromDB = getTransaction(
                orderBonusesTransactionRepository.findByOrderIdAndUserIdAndTrnType(orderId, currentUser.getId(), TransactionDTO.TrnTypeEnum.WITHDRAW)
        );
        assertThat(TransactionDTO.StateEnum.HOLD.equals(trnFromDB.getTrnState())).isTrue();
        assertThat(Objects.equals(trnFromService.getId(), trnFromDB.getId())).isTrue();
    }

    private OrderBonusesTransaction processHoldTransactionInternal(TransactionDTO.StateEnum targetState, Long orderId) {
        OrderBonusesTransaction result;
        switch (targetState) {
            case CANCELED: {
                result = bonusesService.withdrawBonusesCancel(currentUser.getId(), orderId);
                break;
            }
            case COMMITTED: {
                result = bonusesService.withdrawBonusesCommit(currentUser.getId(), orderId);
                break;
            }
            default: throw new RuntimeException();
        }
        return result;
    }

    @Test
    public void testReturnBonusesOnlyIfInCommittedState() {
        Long orderId = BONUSES_TEST_ORDER_ID;

        //создаем транзакцию списания в состоянии HOLD
        createHoldTransaction(TEST_BONUSES_AMOUNT, TEST_MONEY_AMOUNT, orderId);

        //проверяем, что мы не можем сделать возврат при наличии незакомиченной транзакции
        OrderBonusesTransaction trnFromService = bonusesService.returnBonusesOnlyIfInCommittedState(currentUser, orderId);
        assertThat(trnFromService).isNull();

        //комитим транзакцию списания
        OrderBonusesTransaction trnFromService2 = processHoldTransactionInternal(TransactionDTO.StateEnum.COMMITTED, orderId);
        assertThat(trnFromService2).isNotNull();
        assertThat(Objects.equals(TransactionDTO.StateEnum.COMMITTED, trnFromService2.getTrnState())).isTrue();

        //делаем еще одну попытку возврата, но с уже закомиченной транзакцией списания
        OrderBonusesTransaction trnFromService3 = bonusesService.returnBonusesOnlyIfInCommittedState(currentUser, orderId);
        assertThat(trnFromService3).isNotNull();
        assertThat(Objects.equals(TransactionDTO.TrnTypeEnum.RETURN, trnFromService3.getTrnType())).isTrue();
    }

    @Test
    public void testReturnBonuses() {
        Long orderId = BONUSES_TEST_ORDER_ID;
        BigDecimal bonusesAmount = TEST_BONUSES_AMOUNT; //c(100);
        BigDecimal moneyAmount = TEST_MONEY_AMOUNT; //c(50);

        //Создание транзакции возврата
        OrderBonusesTransaction trnFromService = bonusesService.returnBonuses(currentUser, bonusesAmount, moneyAmount, orderId);
        assertThat(trnFromService).isNotNull();

        OrderBonusesTransaction trnFromDB = getTransaction(
                orderBonusesTransactionRepository.findByOrderIdAndUserIdAndTrnType(orderId, currentUser.getId(), TransactionDTO.TrnTypeEnum.RETURN)
        );
        assertThat(trnFromDB.getBonusAmount().compareTo(bonusesAmount)).isEqualTo(0);
        assertThat(trnFromDB.getMoneyAmount().compareTo(moneyAmount)).isEqualTo(0);
        assertThat(Objects.equals(trnFromService.getId(), trnFromDB.getId())).isTrue();

        //проверка, что повторный вызов сохранения по этому же заказу вернет предыдущую транзакцию
        trnFromService = bonusesService.returnBonuses(currentUser, bonusesAmount, moneyAmount, orderId);
        assertThat(trnFromService).isNotNull();

        OrderBonusesTransaction trnFromDB2 = getTransaction(
                orderBonusesTransactionRepository.findByOrderIdAndUserIdAndTrnType(orderId, currentUser.getId(), TransactionDTO.TrnTypeEnum.RETURN)
        );
        assertThat(Objects.equals(trnFromDB.getId(), trnFromDB2.getId())).isTrue();

        //проверка, что при наличии ошибки, будет сохранена ошибочная транзакция
        orderId = BONUSES_TEST_EXCEPTION_ORDER_ID;
        trnFromService = bonusesService.returnBonuses(currentUser, bonusesAmount, moneyAmount, orderId);
        assertThat(trnFromService).isNotNull();

        OrderBonusesTransaction trnFromDB3 = getTransaction(
                Collections.singletonList(orderBonusesTransactionRepository.findById(trnFromService.getId()).orElseThrow(RuntimeException::new))
        );
        assertThat(trnFromDB3.getErrorText()).isNotNull();

        //проверка, что при повторном вызове и при наличии ошибки, будет возвращена та же ошибочная транзакция
        trnFromService = bonusesService.returnBonuses(currentUser, bonusesAmount, moneyAmount, orderId);
        assertThat(trnFromService).isNotNull();

        OrderBonusesTransaction trnFromDB4 = getTransaction(
                Collections.singletonList(orderBonusesTransactionRepository.findById(trnFromService.getId()).orElseThrow(RuntimeException::new))
        );
        assertThat(trnFromDB4.getId().equals(trnFromDB3.getId())).isTrue();

        //проверка, что транзакция проходит, если убрать выброс исключения и вызвать специальный метод повторного вызова
        getBonusesControllerApiStub().setIsExceptionThrowing(false);
        trnFromService = bonusesService.returnBonusesAfterError(trnFromDB4.getId());
        assertThat(trnFromService).isNotNull();

        OrderBonusesTransaction trnFromDB5 = getTransaction(
                Collections.singletonList(orderBonusesTransactionRepository.findById(trnFromService.getId()).orElseThrow(RuntimeException::new))
        );
        assertThat(trnFromDB5.getId().equals(trnFromDB3.getId())).isTrue();
        assertThat(trnFromDB5.getTrnState()).isNotNull();
        getBonusesControllerApiStub().setIsExceptionThrowing(true);
    }

    @Test
    public void testSplitBonuses() {
        final Long ORDER_1 = 10L;
        final Long ORDER_2 = 11L;
        final Long ORDER_3 = 12L;
        final Long ORDER_4 = 13L;
        final Long PRODUCT_ITEM_1 = 21L;
        final Long PRODUCT_ITEM_2 = 22L;
        final Long PRODUCT_ITEM_3 = 23L;
        BigDecimal bonusesAmount = new BigDecimal(100);
        BigDecimal moneyAmount = new BigDecimal(50);
        BigDecimal bonusesAll = bonusesAmount.multiply(new BigDecimal(2));
        BigDecimal moneyAll = moneyAmount.multiply(new BigDecimal(1));

        OrderBonusesTransactionParams params = new OrderBonusesTransactionParams();
        List<OrderBonusesTransactionParamsProductItemId> offlineItems = new ArrayList<>();
        offlineItems.add(new OrderBonusesTransactionParamsProductItemId(PRODUCT_ITEM_1, bonusesAmount, ZERO));
        offlineItems.add(new OrderBonusesTransactionParamsProductItemId(PRODUCT_ITEM_1, bonusesAmount, ZERO));
        offlineItems.add(new OrderBonusesTransactionParamsProductItemId(PRODUCT_ITEM_2, ZERO, moneyAmount));
        offlineItems.add(new OrderBonusesTransactionParamsProductItemId(PRODUCT_ITEM_3, ZERO, ZERO));
        params.setOfflineItems(offlineItems);

        //проверка сохранения транзакции после вызова бонусного сервиса
        OrderBonusesTransaction trnFromService1 = bonusesService.withdrawBonusesHold(
                currentUser, bonusesAll, moneyAll, "", "", null, null, params
        );
        assertThat(trnFromService1).isNotNull();

        OrderBonusesTransaction trnFromDB1 = getTransaction(
                orderBonusesTransactionRepository.findAll()
        );
        assertThat(TransactionDTO.StateEnum.HOLD.equals(trnFromDB1.getTrnState())).isTrue();
        assertThat(trnFromDB1.getBonusAmount().compareTo(bonusesAll)).isEqualTo(0);
        assertThat(trnFromDB1.getMoneyAmount().compareTo(moneyAll)).isEqualTo(0);

        List<BonusesOfflineSplitItem> productItemsAndOrders = new ArrayList<>();
        productItemsAndOrders.add(new BonusesOfflineSplitItem(PRODUCT_ITEM_1, Arrays.asList(ORDER_1, ORDER_2)));
        productItemsAndOrders.add(new BonusesOfflineSplitItem(PRODUCT_ITEM_2, Collections.singletonList(ORDER_3)));
        productItemsAndOrders.add(new BonusesOfflineSplitItem(PRODUCT_ITEM_3, Collections.singletonList(ORDER_4)));

        OrderBonusesTransaction trnFromService2 = bonusesService.splitBonuses(trnFromService1.getBonusTransactionId(), productItemsAndOrders);
        OrderBonusesTransaction trnFromDB2 = getTransaction(
                Collections.singletonList(orderBonusesTransactionRepository.findById(trnFromService1.getId()).orElse(null))
        );
        assertThat(trnFromService2).isNotNull();
        assertThat(Objects.equals(trnFromDB2.getBonusTransactionId(), trnFromService2.getBonusTransactionId())).isTrue();
        assertThat(TransactionDTO.StateEnum.CANCELED.equals(trnFromDB2.getTrnState())).isTrue();

        OrderBonusesTransaction trnFromDB2_1 = getTransaction(
                orderBonusesTransactionRepository.findByOrderIdAndUserIdAndTrnType(ORDER_1, currentUser.getId(), TransactionDTO.TrnTypeEnum.WITHDRAW)
        );
        assertThat(trnFromDB2_1.getBonusAmount().compareTo(bonusesAmount)).isEqualTo(0);
        assertThat(trnFromDB2_1.getMoneyAmount().compareTo(ZERO)).isEqualTo(0);

        OrderBonusesTransaction trnFromDB2_2 = getTransaction(
                orderBonusesTransactionRepository.findByOrderIdAndUserIdAndTrnType(ORDER_2, currentUser.getId(), TransactionDTO.TrnTypeEnum.WITHDRAW)
        );
        assertThat(trnFromDB2_2.getBonusAmount().compareTo(bonusesAmount)).isEqualTo(0);
        assertThat(trnFromDB2_2.getMoneyAmount().compareTo(ZERO)).isEqualTo(0);

        OrderBonusesTransaction trnFromDB2_3 = getTransaction(
                orderBonusesTransactionRepository.findByOrderIdAndUserIdAndTrnType(ORDER_3, currentUser.getId(), TransactionDTO.TrnTypeEnum.WITHDRAW)
        );
        assertThat(trnFromDB2_3.getBonusAmount().compareTo(ZERO)).isEqualTo(0);
        assertThat(trnFromDB2_3.getMoneyAmount().compareTo(moneyAmount)).isEqualTo(0);

        checkTransactionIsNull(
                orderBonusesTransactionRepository.findByOrderIdAndUserIdAndTrnType(ORDER_4, currentUser.getId(), TransactionDTO.TrnTypeEnum.WITHDRAW)
        );
    }

    @Test
    public void testLoyaltyProgramV2NotAcceptedByUser() {
        User notLoyalUser = createUser(null);
        Long orderId = BONUSES_TEST_ORDER_ID;
        BigDecimal bonusesAmount = TEST_BONUSES_AMOUNT;
        BigDecimal moneyAmount = TEST_MONEY_AMOUNT;

        //Return
        OrderBonusesTransaction trnFromService = bonusesService.returnBonuses(notLoyalUser, bonusesAmount, moneyAmount, orderId);
        assertThat(trnFromService).isNull();
        checkTransactionIsNull(orderBonusesTransactionRepository.findByOrderIdAndUserIdAndTrnType(orderId, notLoyalUser.getId(), TransactionDTO.TrnTypeEnum.RETURN));

        //Transfer
        trnFromService = bonusesService.transferBonusesOrder(notLoyalUser, bonusesAmount, moneyAmount, "", "", null, orderId, null);
        assertThat(trnFromService).isNotNull();

        //Transfer manual - тут игнорируется принятие/не принятие/игнор программы лояльности
        BonusesBalanceDTO balance0 = bonusesService.getBalance(notLoyalUser.getId());
        try {
            bonusesService.transferBonusesManual(notLoyalUser.getId(), bonusesAmount, null, "", "", null);
            assertThat(true).isTrue();
        } catch (Exception e) {
            assertThat(false).isTrue();
        }
        BonusesBalanceDTO balance1 = bonusesService.getBalance(notLoyalUser.getId());
        assertThat(balance1.getAmount().getBonuses().compareTo(balance0.getAmount().getBonuses().add(bonusesAmount)) == 0).isTrue();

        //Withdraw
        trnFromService = bonusesService.withdrawBonusesHold(
                notLoyalUser, bonusesAmount, moneyAmount, "", "", null, orderId, null
        );
        assertThat(trnFromService).isNull();
        checkTransactionIsNull(orderBonusesTransactionRepository.findByOrderIdAndUserIdAndTrnType(orderId, currentUser.getId(), TransactionDTO.TrnTypeEnum.WITHDRAW));

        //Withdraw manual - тут игнорируется принятие/не принятие/игнор программы лояльности
        try {
            bonusesService.withdrawBonusesManual(notLoyalUser.getId(), bonusesAmount, null, "", "");
            assertThat(true).isTrue();
        } catch (Exception e) {
            assertThat(false).isTrue();
        }
        BonusesBalanceDTO balance2 = bonusesService.getBalance(notLoyalUser.getId());
        assertThat(balance2.getAmount().getBonuses().compareTo(balance1.getAmount().getBonuses().subtract(bonusesAmount)) == 0).isTrue();
    }

    private void checkTransactionIsNull(List<OrderBonusesTransaction> transactions) {
        assertThat(transactions).isNotNull();
        assertThat(transactions.size()).isEqualTo(0);
    }

    private User createUser(Boolean isLoyaltyProgramV2Accepted) {
        return createUser(isLoyaltyProgramV2Accepted, null);
    }

    private User createUser(Boolean isLoyaltyProgramV2Accepted, LocalDateTime birthday) {
        return userRepository.saveAndFlush(
                new User()
                        .setNickname(RandomStringUtils.randomAlphabetic(10))
                        .setUserType(User.UserType.SIMPLE_USER)
                        .setBirthDate(birthday)
                        .setCommissionGrid(commissionGridService.getDefaultCommissionGrid())
                        //TODO setIsLoyaltyProgramAccepted(true) - на время режима работы приложения, когда сервис лояльности ВЫКЛЮЧЕН
                        .setIsLoyaltyProgramAccepted(isLoyaltyProgramV2Accepted)
                        .setIsLoyaltyProgramV2Accepted(isLoyaltyProgramV2Accepted == null ? null : ZonedDateTime.now())
                        .setChangeTime(LocalDateTime.now())
        );
    }

    private OrderBonusesTransaction getTransaction(List<OrderBonusesTransaction> transactions) {
        assertThat(transactions).isNotNull();
        assertThat(transactions.size()).isEqualTo(1);
        assertThat(transactions.get(0)).isNotNull();
        return transactions.get(0);
    }

    private BigDecimal c(int value) {
        return new BigDecimal(value);
    }

    private BonusesControllerApiStub getBonusesControllerApiStub() {
        return ((BonusesControllerApiStub) bonusesControllerAPI);
    }
}
