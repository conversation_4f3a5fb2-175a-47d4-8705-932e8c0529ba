package ru.oskelly.tests.pr.suite1_3.domain.service.order.track;


import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import su.reddot.domain.service.dto.order.track.OrderStageDTO;


public class StatusesTest extends OrderTrackServiceOskellyExpertiseBaseTest {
	private static final String STATUSES_IN_PROGRESS_CONTEXT = "/mocked-orders/statuses/inprogress";
	private static final String STATUSES_FINISHED_SUCCESS_CONTEXT = "/mocked-orders/statuses/finished/success";
	private static final String STATUSES_PARTIALLY_FINISHED_CONTEXT = "/mocked-orders/statuses/partially/finished";
	private static final String STATUSES_PARTIALLY_INPROGRESS_CONTEXT = "/mocked-orders/statuses/partially/inprogress";
	private static final String STATUSES_FINISHED_FAILED_CONTEXT = "/mocked-orders/statuses/finished/failed";

	@Nested
	public class RecursiveSnippetsTest {
		@Test
		public void shouldHave_InProgress_Status() {
			expectExpertiseStageForAllSnippets(
					BUYER_CONTEXT, STATUSES_IN_PROGRESS_CONTEXT
			).expect(expertiseStage -> {
				Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(OrderStageDTO.ProgressState.IN_PROGRESS);
				Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(null);
			});
		}

		@Test
		public void shouldHave_PartiallyInProgress_Status() {
			expectExpertiseStageForAllSnippets(
					BUYER_CONTEXT, STATUSES_PARTIALLY_INPROGRESS_CONTEXT
			).expect(expertiseStage -> {
				Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(OrderStageDTO.ProgressState.IN_PROGRESS);
				Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(OrderStageDTO.SuccessState.PARTIALLY_SUCCEEDED);
			});
		}

		@Test
		public void shouldHave_PartiallyComplete_Status() {
			expectExpertiseStageForAllSnippets(
					BUYER_CONTEXT, STATUSES_PARTIALLY_FINISHED_CONTEXT
			).expect(expertiseStage -> {
				Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(OrderStageDTO.ProgressState.COMPLETE);
				Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(OrderStageDTO.SuccessState.PARTIALLY_SUCCEEDED);
			});
		}

		@Test
		public void shouldHave_FinishedFailed_Status() {
			expectExpertiseStageForAllSnippets(
					BUYER_CONTEXT, STATUSES_FINISHED_FAILED_CONTEXT
			).expect(expertiseStage -> {
				Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(OrderStageDTO.ProgressState.COMPLETE);
				Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(OrderStageDTO.SuccessState.FAILED);
			});
		}

		@Test
		public void shouldHave_FinishedSuccess_Status() {
			expectExpertiseStageForAllSnippets(
					BUYER_CONTEXT, STATUSES_FINISHED_SUCCESS_CONTEXT
			).expect(expertiseStage -> {
				Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(OrderStageDTO.ProgressState.COMPLETE);
				Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(OrderStageDTO.SuccessState.SUCCEEDED);
			});
		}
	}

	@Nested
	public class SingleSnippetTest {
		@Test
		public void checkSingleFinishedFailedSnippet() {
			OrderStageDTO expertiseStage = expectExpertiseStageExact(
					SELLER_CONTEXT, STATUSES_FINISHED_FAILED_CONTEXT,
					"several/changes/1/5_0.json"
			);
			Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(OrderStageDTO.ProgressState.COMPLETE);
			Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(OrderStageDTO.SuccessState.FAILED);
		}

		@Test
		public void checkSingleFinishedSuccessSnippet() {
			OrderStageDTO expertiseStage = expectExpertiseStageExact(
					SELLER_CONTEXT, STATUSES_FINISHED_SUCCESS_CONTEXT,
					"7_0.json"
			);
			Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(OrderStageDTO.ProgressState.COMPLETE);
			Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(OrderStageDTO.SuccessState.SUCCEEDED);
		}

		@Test
		public void checkSingleInProgressSnippet() {
			OrderStageDTO expertiseStage = expectExpertiseStageExact(
					SELLER_CONTEXT, STATUSES_IN_PROGRESS_CONTEXT,
					"nodefects/3_1_pressed-cannot-determine.json"
			);
			Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(OrderStageDTO.ProgressState.IN_PROGRESS);
			Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(null);
		}

		@Test
		public void checkSinglePartiallyInProgressSuccessSnippet() {
			OrderStageDTO expertiseStage = expectExpertiseStageExact(
					SELLER_CONTEXT, STATUSES_PARTIALLY_INPROGRESS_CONTEXT,
					"changes/4_0.json"
			);
			Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(OrderStageDTO.ProgressState.IN_PROGRESS);
			Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(OrderStageDTO.SuccessState.PARTIALLY_SUCCEEDED);
		}

		@Test
		public void checkSinglePartiallyCompleteSuccessSnippet() {
			OrderStageDTO expertiseStage = expectExpertiseStageExact(
					SELLER_CONTEXT, STATUSES_PARTIALLY_FINISHED_CONTEXT,
					"defects2products/7_0.json"
			);
			Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(OrderStageDTO.ProgressState.COMPLETE);
			Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(OrderStageDTO.SuccessState.PARTIALLY_SUCCEEDED);
		}
	}
}