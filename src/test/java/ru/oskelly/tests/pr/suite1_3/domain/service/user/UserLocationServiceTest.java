package ru.oskelly.tests.pr.suite1_3.domain.service.user;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

import java.util.UUID;

import javax.annotation.Nullable;

import lombok.NonNull;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.exception.OskellyException;
import su.reddot.domain.model.address.Address;
import su.reddot.domain.model.logistic.DestinationType;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.AddressDTO;
import su.reddot.domain.service.integration.logistic.model.DeliveryOptionDTO;
import su.reddot.domain.service.user.UserLocationService;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.logistic.DeliveryOptionsService;
import su.reddot.infrastructure.logistic.FiasIds;
import su.reddot.infrastructure.security.SecurityService;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@SpringBootTest(classes = OskellyApplication.class)
@ActiveProfiles(AbstractSpringTest.testProfiles)
@ExtendWith(SpringExtension.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class UserLocationServiceTest {

    private static final Address ADDRESS_IN_MOSCOW = getAddressInMoscow();
    private static final AddressDTO ADDRESS_IN_MOSCOW_DTO = getAddressInMoscowDTO();
    private static final Address ADDRESS_IN_KRASNODAR = getAddressInKrasnodar();
    private static final AddressDTO ADDRESS_IN_KRASNODAR_DTO = getAddressInKrasnodarDTO();

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserLocationService userLocationService;

    @MockBean
    private SecurityService securityService;

    @MockBean
    private DeliveryOptionsService deliveryOptionsService;

    @Value("${test.api.user-id}")
    private Long userId;

    @BeforeEach
    void setUp() {
        mockDeliveryOptionForAddress(ADDRESS_IN_MOSCOW, 1, 1);
        mockDeliveryOptionForAddress(ADDRESS_IN_KRASNODAR, 2, 3);
    }

    /**
     * Установка местоположения авторизованным пользователем.
     */
    @Test
    void testSetUserLocationAuthorized() {
        User user = userRepository.getOne(userId);
        when(securityService.getCurrentAuthorizedUser()).thenReturn(user);

        // Сначала удаляем местоположение - на всякий случай, если в базе остался мусор
        userLocationService.deleteLocation();
        assertNull(userLocationService.getLocation());
        assertNull(userLocationService.getMaxDeliveryDays());

        // Устанавливаем местоположение в Москве
        userLocationService.setLocation(ADDRESS_IN_MOSCOW_DTO);
        AddressDTO location = userLocationService.getLocation();
        verifyAddress(ADDRESS_IN_MOSCOW_DTO, location);

        // Устанавливаем местоположение в Краснодаре
        userLocationService.setLocation(ADDRESS_IN_KRASNODAR_DTO);
        location = userLocationService.getLocation();
        verifyAddress(ADDRESS_IN_KRASNODAR_DTO, location);

        // Сбрасываем местоположение
        userLocationService.deleteLocation();
        assertNull(userLocationService.getLocation());
        assertNull(userLocationService.getMaxDeliveryDays());
    }

    /**
     * Установка местоположения неавторизованным пользователем с гостевым токеном.
     */
    @Test
    void testSetUserLocationGuestToken() {
        when(securityService.getCurrentAuthorizedUser()).thenReturn(null);
        when(securityService.getGuestToken()).thenReturn(UUID.randomUUID().toString());

        // Устанавливаем местоположение в Москве
        userLocationService.setLocation(ADDRESS_IN_MOSCOW_DTO);
        AddressDTO location = userLocationService.getLocation();
        verifyAddress(ADDRESS_IN_MOSCOW_DTO, location);

        // Устанавливаем местоположение в Краснодаре
        userLocationService.setLocation(ADDRESS_IN_KRASNODAR_DTO);
        location = userLocationService.getLocation();
        verifyAddress(ADDRESS_IN_KRASNODAR_DTO, location);

        // Сбрасываем местоположение
        userLocationService.deleteLocation();
        assertNull(userLocationService.getLocation());
        assertNull(userLocationService.getMaxDeliveryDays());
    }

    /**
     * Установка местоположения неавторизованным пользователем без гостевого токена.
     */
    @Test
    void testSetUserLocationNotAuthorized() {
        when(securityService.getCurrentAuthorizedUser()).thenReturn(null);
        when(securityService.getGuestToken()).thenReturn(null);

        assertThrows(OskellyException.class, () -> userLocationService.setLocation(ADDRESS_IN_MOSCOW_DTO));
    }


    @NonNull
    private static Address getAddressInMoscow() {
        Address address = new Address();
        address.setCountry("Россия");
        address.setRegion("Москва");
        address.setCity("Москва");
        address.setRegionFiasId(FiasIds.MOSCOW);
        address.setCityFiasId(FiasIds.MOSCOW);
        return address;
    }

    @NonNull
    private static AddressDTO getAddressInMoscowDTO() {
        AddressDTO address = new AddressDTO();
        address.setCountry("Россия");
        address.setRegion("Москва");
        address.setCity("Москва");
        address.setRegionFiasId(FiasIds.MOSCOW);
        address.setCityFiasId(FiasIds.MOSCOW);
        return address;
    }

    @NonNull
    private static Address getAddressInKrasnodar() {
        Address address = new Address();
        address.setCountry("Россия");
        address.setRegion("Краснодарский край");
        address.setCity("Краснодар");
        address.setRegionFiasId(FiasIds.KRASNODAR_KRAI);
        address.setCityFiasId(FiasIds.KRASNODAR);
        return address;
    }

    @NonNull
    private static AddressDTO getAddressInKrasnodarDTO() {
        AddressDTO address = new AddressDTO();
        address.setCountry("Россия");
        address.setRegion("Краснодарский край");
        address.setCity("Краснодар");
        address.setRegionFiasId(FiasIds.KRASNODAR_KRAI);
        address.setCityFiasId(FiasIds.KRASNODAR);
        return address;
    }

    private void mockDeliveryOptionForAddress(Address address, int minDeliveryDays, int maxDeliveryDays) {
        DeliveryOptionDTO deliveryOption = new DeliveryOptionDTO();
        deliveryOption.setMinDeliveryDays(minDeliveryDays);
        deliveryOption.setMaxDeliveryDays(maxDeliveryDays);
        when(deliveryOptionsService.getHighestPriorityDeliveryOption(address, DestinationType.OFFICE))
                .thenReturn(deliveryOption);
    }

    private static void verifyAddress(AddressDTO expected, @Nullable AddressDTO actual) {
        assertNotNull(actual);
        assertEquals(expected.getCountry(), actual.getCountry());
        assertEquals(expected.getRegion(), actual.getRegion());
        assertEquals(expected.getCity(), actual.getCity());
        assertEquals(expected.getRegionFiasId(), actual.getRegionFiasId());
        assertEquals(expected.getCityFiasId(), actual.getCityFiasId());
    }
}
