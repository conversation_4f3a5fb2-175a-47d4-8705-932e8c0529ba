package ru.oskelly.tests.pr.suite1_3.domain.service.task;


import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.adminalert.AdminAlertRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.adminalert.AdminAlert;
import su.reddot.domain.model.adminalert.product.AdminProductAlert24hoursStateModeration;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.adminalert.AdminAlertService;
import su.reddot.domain.service.task.ScheduledAdminAlertRunner;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.util.Utils;

import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class ScheduledAdminAlertRunnerTest extends AbstractSpringTest {

    @Autowired
    ScheduledAdminAlertRunner scheduledAdminAlertRunner;
    @Autowired
    private ProductRepository productRepository;
    //Вырезаем старые офферы
    /*@Autowired
    private OfferRepository offerRepository;*/
    @Autowired
    private UserService userService;
    //Вырезаем старые офферы
    /*
    @Autowired
    private OfferService offerService;*/
    @Autowired
    private AdminAlertService<AdminAlert> adminAlertService;
    @Autowired
    private AdminAlertRepository<AdminAlert> adminAlertRepository;

    private static Product productForAlerts;

    private static boolean initialized = false;


    //Вырезаем старые офферы
    /*
    private static List<Offer> offersForAlerts;
    private static List<Product> productsForOffers;

     */


    private static ProductState productStateClean;


    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user-password}")
    private String buyerPassword;

    @Value("${test.api.user2-email}")
    private String sellerEmail;
    private Long realSellerId = 23L;

    @BeforeEach
    public void init(){
        if(initialized) return;
        adminAlertRepository.deleteAll();
        //Вырезаем старые офферы
        //offerRepository.deleteAll(offerService.getActualOutgoingOffers(getBuyer()));
        initialized = true;
    }

    @Test
    public void _00_alert_product() {
        productForAlerts = makeProductNeedModerationState();
	    jdbcTemplate.execute("UPDATE product SET product_state_time = NOW() WHERE id != " + productForAlerts.getId());
        scheduledAdminAlertRunner.createAdminProductAlerts();
        //Таск занимает некоторое время
	    TestUtils.sleep(1);

        List<Long> objectIds = Arrays.asList(productForAlerts.getId());
        assertAlertsCreated(objectIds, AdminProductAlert24hoursStateModeration.class);
    }

    //Выключаем старые офферы
    /*
    @Transactional
    @Test
    public void _01_alert_offers() {
	    jdbcTemplate.execute("DELETE FROM offer");
        offersForAlerts = makeOffers();
        commitTransaction();
        scheduledAdminAlertRunner.createAdminOfferAlerts();
	    //Таск занимает некоторое время
	    TestUtils.sleep(1);

        List<Long> objectIds = offersForAlerts.stream().map(o -> o.getId()).collect(Collectors.toList());
        assertAlertsCreated(objectIds, AdminOfferAlertNotAcceptedBySellerDuringTheDay.class);
    }
     */

    @Test
    public void _02_clean() {
        //отчистка продуктов
        if (productForAlerts != null) {
            productForAlerts.setProductState(productStateClean);
            productRepository.saveAndFlush(productForAlerts);
        }

        //отчистка торгов
        //Выключаем старые офферы
        //offersForAlerts.stream().forEach(offer -> offerRepository.deleteById(offer.getId()));
    }

    private void assertAlertsCreated(List<Long> objectIds, Class<? extends AdminAlert> adminAlertClass){
        for(Long objectId : objectIds) {
            List<AdminAlert> alerts = adminAlertService.getAdminAlertsByObjectId(objectId, adminAlertClass);
            //Алерты есть
            assertFalse(alerts.isEmpty());
            for(AdminAlert alert : alerts){
                Class entityClass = Utils.getEntityClass(alert);
                assertSame(adminAlertClass, entityClass);
                assertTrue(alert.getObjectId().equals(objectId));
                //Алерт создан недавно
                assertTrue(alert.getCreateTime().isAfter(ZonedDateTime.now().minusMinutes(3)));
            }
        }
    }

    //Вырезаем старые офферы
    /*
    private List<Offer> makeOffers() {
        if(offersForAlerts == null){
            offersForAlerts = new ArrayList<>();
        }

        productsForOffers = getProductsForOffers(5);
        assertNotNull(productsForOffers);
        for (Product productsForOffer : productsForOffers) {
            long offerProductId = productsForOffer.getId();
            assertNotNull(offerProductId);
            long offerSizeId = productsForOffer.getAvailableProductItems().get(0).getSize().getId();
            assertNotNull(offerSizeId);
            BigDecimal offerPrice = productsForOffer.getCurrentPrice();
            offerPrice = offerPrice.subtract(new BigDecimal("10"));
            assertNotNull(offerPrice);
            OfferDetails offerDetails = offerService.makeAnOffer(getBuyer(), offerProductId, offerSizeId, offerPrice);
            assertNotNull(offerDetails);
            Offer offer = offerRepository.getOne(offerDetails.getHistory().get(0).getOfferId());
            assertNotNull(offer);
            offer.setCreatedAt(offer.getCreatedAt().minusDays(14l));
            offerRepository.saveAndFlush(offer);
            offersForAlerts.add(offer);
        }
        return offersForAlerts;
    }

    private List<Product> getProductsForOffers(Integer productsCount) {
        List<Product> productsReturn = new ArrayList<>();
        List<Product> products = productRepository.getAllBySeller(getSeller()).stream().filter(x -> x.getProductState() == ProductState.PUBLISHED).collect(Collectors.toList());
        if(products.size() < productsCount){
            products = productRepository.findTop10ByProductState(ProductState.PUBLISHED);
        }
        for (int i = 0; i < productsCount && i < products.size(); i++) {
            Product product = products.get(i);
            product.setSeller(getSeller());
            productRepository.saveAndFlush(product);
            productsReturn.add(product);
        }
        return productsReturn;
    }

     */



    private Product makeProductNeedModerationState() {
        productForAlerts = getProductForAlerts();
        assertNotNull(productForAlerts);
        productStateClean = productForAlerts.getProductState();
        productForAlerts.setProductState(ProductState.NEED_MODERATION);
        productForAlerts.setProductStateTime(ZonedDateTime.now().minusHours(25));
        productRepository.saveAndFlush(productForAlerts);
        return productForAlerts;
    }

    private Product getProductForAlerts() {
        if (productForAlerts == null) {
            productForAlerts = productRepository.findProductsBySellerIdAndProductState(realSellerId, ProductState.PUBLISHED).stream().collect(Collectors.toList()).get(0);
        }
        return productForAlerts;
    }

    private User getBuyer() {
        return userService.getUserByEmail(buyerEmail);
    }

    private User getSeller() {
        return userService.getUserByEmail(sellerEmail);
    }
}