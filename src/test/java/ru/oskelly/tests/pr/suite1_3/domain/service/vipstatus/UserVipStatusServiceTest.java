package ru.oskelly.tests.pr.suite1_3.domain.service.vipstatus;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.tags.UserCommonTagBindingRepository;
import su.reddot.domain.dao.uservipstatus.UserVipStatusRepository;
import su.reddot.domain.model.tags.UserCommonTagBinding;
import su.reddot.domain.model.user.User;
import su.reddot.domain.model.user.vipstatus.UserVipStatus;
import su.reddot.domain.model.user.vipstatus.VipStatusManagementMode;
import su.reddot.domain.service.adminpanel.tag.UserCommonTagService;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.domain.service.user.vipstatus.UserPurchaseVolumeData;
import su.reddot.domain.service.user.vipstatus.UserPurchaseVolumeProvider;
import su.reddot.domain.service.user.vipstatus.UserVipStatusService;
import su.reddot.infrastructure.configparam.ConfigParamService;
import su.reddot.infrastructure.configuration.OskellyApplication;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest(classes = {OskellyApplication.class})
@ExtendWith(SpringExtension.class)
@ActiveProfiles(AbstractSpringTest.testProfiles)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
@Slf4j
public class UserVipStatusServiceTest {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserVipStatusRepository userVipStatusRepository;

    @Autowired
    private UserVipStatusService userVipStatusService;

    @MockBean
    private UserPurchaseVolumeProvider userPurchaseVolumeProvider;

    @Autowired
    private UserCommonTagBindingRepository userCommonTagBindingRepository;

    @Autowired
    private UserCommonTagService userCommonTagService;

    @Autowired
    private CommissionGridService commissionGridService;

    @MockBean
    private ConfigParamService configParamService;

    private static final BigDecimal VIP_ELIGIBILITY_AMOUNT = BigDecimal.valueOf(3_000_000);

    @BeforeEach
    public void init() {
        userCommonTagBindingRepository.deleteAll();
        userVipStatusRepository.deleteAll();
        Mockito.when(configParamService.getValueAsBigDecimalNullableCached(Mockito.anyString()))
                .thenReturn(VIP_ELIGIBILITY_AMOUNT);
    }

    @AfterEach
    public void cleanup() {
        userCommonTagBindingRepository.deleteAll();
        userVipStatusRepository.deleteAll();
    }

    @Test
    public void testAutoVipStatusGrantedAndRevoked() {
        // Создаем пользователя без VIP статуса
        User user = createUser();

        // Устанавливаем объем покупок ниже порогового значения
        Map<Long, List<UserPurchaseVolumeData>> purchaseVolumes = new HashMap<>();
        purchaseVolumes.put(
                user.getId(),
                Arrays.asList(
                        new UserPurchaseVolumeData(user.getId(), VIP_ELIGIBILITY_AMOUNT.subtract(BigDecimal.ONE), 1),
                        new UserPurchaseVolumeData(user.getId(), VIP_ELIGIBILITY_AMOUNT.subtract(BigDecimal.TEN), 2),
                        new UserPurchaseVolumeData(user.getId(), VIP_ELIGIBILITY_AMOUNT.subtract(BigDecimal.ONE), 3)
                )
        );

        Mockito.when(userPurchaseVolumeProvider.getPurchaseVolumesData(3))
                .thenReturn(purchaseVolumes);

        // Запускаем пересчет
        userVipStatusService.recalculateVipStatuses();

        // Проверяем, что пользователь не получил VIP статус
        List<UserCommonTagBinding> bindings = userCommonTagBindingRepository.findAll();
        assertThat(bindings).isEmpty();

        List<UserVipStatus> userVipStatuses = userVipStatusRepository.findAll();
        assertThat(userVipStatuses).isEmpty();

        // Устанавливаем объем покупок выше порогового значения
        purchaseVolumes.clear();
        purchaseVolumes.put(
                user.getId(),
                Arrays.asList(
                        new UserPurchaseVolumeData(user.getId(), BigDecimal.valueOf(1000000), 1),
                        new UserPurchaseVolumeData(user.getId(), VIP_ELIGIBILITY_AMOUNT.add(BigDecimal.ONE), 2),
                        new UserPurchaseVolumeData(user.getId(), BigDecimal.valueOf(2000000), 3)
                )
        );

        Mockito.when(userPurchaseVolumeProvider.getPurchaseVolumesData(3))
                .thenReturn(purchaseVolumes);

        // Запускаем пересчет
        userVipStatusService.recalculateVipStatuses();

        // Проверяем, что пользователь получил VIP статус
        bindings = userCommonTagBindingRepository.findAll();
        assertThat(bindings).hasSize(1);
        assertThat(bindings.get(0).getUserId()).isEqualTo(user.getId());

        userVipStatuses = userVipStatusRepository.findAll();
        assertThat(userVipStatuses).hasSize(1);
        assertThat(userVipStatuses.get(0).getUserId()).isEqualTo(user.getId());
        assertThat(userVipStatuses.get(0).getManagementMode()).isEqualTo(VipStatusManagementMode.AUTO);
        assertThat(userVipStatuses.get(0).getPurchaseVolumes()).size().isEqualTo(3);

        // Устанавливаем объем покупок ниже порогового значения
        purchaseVolumes.clear();
        purchaseVolumes.put(
                user.getId(),
                Arrays.asList(
                        new UserPurchaseVolumeData(user.getId(), VIP_ELIGIBILITY_AMOUNT.subtract(BigDecimal.ONE), 1),
                        new UserPurchaseVolumeData(user.getId(), VIP_ELIGIBILITY_AMOUNT.subtract(BigDecimal.TEN), 2),
                        new UserPurchaseVolumeData(user.getId(), VIP_ELIGIBILITY_AMOUNT.subtract(BigDecimal.ONE), 3)
                )
        );

        Mockito.when(userPurchaseVolumeProvider.getPurchaseVolumesData(3))
                .thenReturn(purchaseVolumes);

        // Запускаем пересчет
        userVipStatusService.recalculateVipStatuses();

        // Проверяем, что VIP статус был отозван
        bindings = userCommonTagBindingRepository.findAll();
        assertThat(bindings).isEmpty();

        userVipStatuses = userVipStatusRepository.findAll();
        assertThat(userVipStatuses).isEmpty();
    }

    @Test
    public void testAdminVipStatusPreserved() {
        // Создаем пользователя с VIP статусом, установленным администратором
        User user = createUser();
        userCommonTagService.bindTagToUser(user, UserCommonTagService.VIP_STATUS_TAG_CODE);

        // Проверяем начальное состояние
        List<UserCommonTagBinding> bindings = userCommonTagBindingRepository.findAll();
        assertThat(bindings).hasSize(1);
        assertThat(bindings.get(0).getUserId()).isEqualTo(user.getId());

        List<UserVipStatus> userVipStatuses = userVipStatusRepository.findAll();
        assertThat(userVipStatuses).hasSize(1);
        assertThat(userVipStatuses.get(0).getUserId()).isEqualTo(user.getId());
        assertThat(userVipStatuses.get(0).getManagementMode()).isEqualTo(VipStatusManagementMode.ADMIN);
        assertThat(userVipStatuses.get(0).getPurchaseVolumes()).isEmpty();

        // Устанавливаем объем покупок ниже порогового значения
        Map<Long, List<UserPurchaseVolumeData>> purchaseVolumes = new HashMap<>();
        purchaseVolumes.put(
                user.getId(),
                Arrays.asList(
                        new UserPurchaseVolumeData(user.getId(), VIP_ELIGIBILITY_AMOUNT.subtract(BigDecimal.ONE), 1),
                        new UserPurchaseVolumeData(user.getId(), VIP_ELIGIBILITY_AMOUNT.subtract(BigDecimal.TEN), 2),
                        new UserPurchaseVolumeData(user.getId(), VIP_ELIGIBILITY_AMOUNT.subtract(BigDecimal.TEN), 3)
                )
        );

        Mockito.when(userPurchaseVolumeProvider.getPurchaseVolumesData(3))
                .thenReturn(purchaseVolumes);

        // Запускаем пересчет
        userVipStatusService.recalculateVipStatuses();

        // Проверяем, что VIP статус сохранился и появились данные по объемам покупок
        bindings = userCommonTagBindingRepository.findAll();
        assertThat(bindings).hasSize(1);
        assertThat(bindings.get(0).getUserId()).isEqualTo(user.getId());

        userVipStatuses = userVipStatusRepository.findAll();
        assertThat(userVipStatuses).hasSize(1);
        assertThat(userVipStatuses.get(0).getUserId()).isEqualTo(user.getId());
        assertThat(userVipStatuses.get(0).getPurchaseVolumes()).size().isEqualTo(3);

        // Убираем данные по объемам покупок
        purchaseVolumes.clear();

        // Запускаем пересчет
        userVipStatusService.recalculateVipStatuses();

        // Проверяем, что VIP статус сохранился и данные по объемам покупок почистились
        bindings = userCommonTagBindingRepository.findAll();
        assertThat(bindings).hasSize(1);
        assertThat(bindings.get(0).getUserId()).isEqualTo(user.getId());

        userVipStatuses = userVipStatusRepository.findAll();
        assertThat(userVipStatuses).hasSize(1);
        assertThat(userVipStatuses.get(0).getUserId()).isEqualTo(user.getId());
        assertThat(userVipStatuses.get(0).getPurchaseVolumes()).isEmpty();
    }

    @Test
    public void testAutoVipStatusGrantedAndRevokedByAdmin() {
        // Создаем пользователя без VIP статуса
        User user = createUser();

        // Устанавливаем объем покупок выше порогового значения
        Map<Long, List<UserPurchaseVolumeData>> purchaseVolumes = new HashMap<>();
        purchaseVolumes.put(
                user.getId(),
                Arrays.asList(
                        new UserPurchaseVolumeData(user.getId(), BigDecimal.valueOf(1000000), 1),
                        new UserPurchaseVolumeData(user.getId(), VIP_ELIGIBILITY_AMOUNT.add(BigDecimal.ONE), 2),
                        new UserPurchaseVolumeData(user.getId(), BigDecimal.valueOf(2000000), 3)
                )
        );

        Mockito.when(userPurchaseVolumeProvider.getPurchaseVolumesData(3))
                .thenReturn(purchaseVolumes);

        // Запускаем пересчет
        userVipStatusService.recalculateVipStatuses();

        // Проверяем, что пользователь получил VIP статус
        List<UserCommonTagBinding> bindings = userCommonTagBindingRepository.findAll();
        assertThat(bindings).hasSize(1);
        assertThat(bindings.get(0).getUserId()).isEqualTo(user.getId());

        List<UserVipStatus> userVipStatuses = userVipStatusRepository.findAll();
        assertThat(userVipStatuses).hasSize(1);
        assertThat(userVipStatuses.get(0).getUserId()).isEqualTo(user.getId());
        assertThat(userVipStatuses.get(0).getManagementMode()).isEqualTo(VipStatusManagementMode.AUTO);
        assertThat(userVipStatuses.get(0).getPurchaseVolumes()).size().isEqualTo(3);

        // Устанавливаем VIP статус администратором
        userCommonTagService.unbindTagFromUser(user, UserCommonTagService.VIP_STATUS_TAG_CODE);

        // Проверяем, что переключился
        bindings = userCommonTagBindingRepository.findAll();
        assertThat(bindings).isEmpty();

        userVipStatuses = userVipStatusRepository.findAll();
        assertThat(userVipStatuses).hasSize(1);
        assertThat(userVipStatuses.get(0).getUserId()).isEqualTo(user.getId());
        assertThat(userVipStatuses.get(0).getManagementMode()).isEqualTo(VipStatusManagementMode.ADMIN);
        assertThat(userVipStatuses.get(0).getPurchaseVolumes()).size().isEqualTo(3);
    }

    private User createUser() {
        return userRepository.saveAndFlush(new User()
                .setNickname(RandomStringUtils.randomAlphabetic(10))
                .setUserType(User.UserType.SIMPLE_USER)
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid())
                .setChangeTime(LocalDateTime.now())
        );
    }
}
