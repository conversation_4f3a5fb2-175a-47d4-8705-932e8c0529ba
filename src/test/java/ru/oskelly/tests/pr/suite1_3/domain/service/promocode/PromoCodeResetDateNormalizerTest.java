package ru.oskelly.tests.pr.suite1_3.domain.service.promocode;

import lombok.val;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.service.promocode.PromoCodeResetDateNormalizer;
import su.reddot.domain.service.promocode.model.ResetDateRange;

import java.time.Clock;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.TemporalAdjusters;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static su.reddot.domain.service.promocode.model.PromoCodeResetRange.HALF_YEAR;
import static su.reddot.domain.service.promocode.model.PromoCodeResetRange.MONTH;
import static su.reddot.domain.service.promocode.model.PromoCodeResetRange.QUARTER;
import static su.reddot.domain.service.promocode.model.PromoCodeResetRange.WEEK;
import static su.reddot.domain.service.promocode.model.PromoCodeResetRange.YEAR;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
class PromoCodeResetDateNormalizerTest {
    Clock clock;

    @BeforeEach
    void setUp() {
        final Instant fixedInstant = Instant.parse("2025-01-09T00:00:00Z");
        clock = Clock.fixed(fixedInstant, ZoneId.of("UTC"));
    }

    @Test
    @DisplayName("Проверка периода - неделя")
    void getDateRangeWeek() {
        // Arrange
        val expectedStart = ZonedDateTime.now(clock).with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        val expectedEnd = ZonedDateTime.now(clock).with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));

        // Act
        final ResetDateRange period = PromoCodeResetDateNormalizer.getDateRange(WEEK, clock);

        // Assert
        assertEquals(expectedStart.toLocalDate(), period.getStartRange().toLocalDate(), "Start of the week is incorrect");
        assertEquals(expectedEnd.toLocalDate(), period.getEndRange().toLocalDate(), "End of the week is incorrect");
    }

    @Test
    @DisplayName("Проверка периода - месяц")
    void getDateRangeMonth() {
        // Arrange
        val expectedStart = ZonedDateTime.now(clock).with(TemporalAdjusters.firstDayOfMonth());
        val expectedEnd = ZonedDateTime.now(clock).with(TemporalAdjusters.lastDayOfMonth());

        // Act
        final ResetDateRange period = PromoCodeResetDateNormalizer.getDateRange(MONTH, clock);

        // Assert
        assertEquals(expectedStart.toLocalDate(), period.getStartRange().toLocalDate(), "Start of the month is incorrect");
        assertEquals(expectedEnd.toLocalDate(), period.getEndRange().toLocalDate(), "End of the month is incorrect");
    }

    @Test
    @DisplayName("Проверка периода - квартал")
    void getDateRangeQuarter() {
        // Arrange
        final ZonedDateTime now = ZonedDateTime.now(clock);
        final int currentQuarter = (now.getMonthValue() - 1) / 3 + 1;
        val expectedStart = now.withMonth((currentQuarter - 1) * 3 + 1)
            .with(TemporalAdjusters.firstDayOfMonth());
        val expectedEnd = now.withMonth(currentQuarter * 3)
            .with(TemporalAdjusters.lastDayOfMonth());

        // Act
        final ResetDateRange period = PromoCodeResetDateNormalizer.getDateRange(QUARTER, clock);

        // Assert
        assertEquals(expectedStart.toLocalDate(), period.getStartRange().toLocalDate(), "Start of the quarter is incorrect");
        assertEquals(expectedEnd.toLocalDate(), period.getEndRange().toLocalDate(), "End of the quarter is incorrect");
    }

    @Test
    @DisplayName("Проверка периода - полгода. Первая половина.")
    void getDateRangeHalfYearFirstHalf() {
        // Arrange
        final Instant fixedInstant = Instant.parse("2025-03-15T00:00:00Z");
        clock = Clock.fixed(fixedInstant, ZoneId.of("UTC"));

        val expectedStart = ZonedDateTime.now(clock).with(TemporalAdjusters.firstDayOfYear());
        val expectedEnd = ZonedDateTime.now(clock).withMonth(6).with(TemporalAdjusters.lastDayOfMonth());

        // Act
        final ResetDateRange period = PromoCodeResetDateNormalizer.getDateRange(HALF_YEAR, clock);

        // Assert
        assertEquals(expectedStart.toLocalDate(), period.getStartRange().toLocalDate(), "Start of the first half-year is incorrect");
        assertEquals(expectedEnd.toLocalDate(), period.getEndRange().toLocalDate(), "End of the first half-year is incorrect");
    }

    @Test
    @DisplayName("Проверка периода - полгода. Вторая половина.")
    void getDateRangeHalfYearSecondHalf() {
        // Arrange
        final Instant fixedInstant = Instant.parse("2025-08-15T00:00:00Z");
        clock = Clock.fixed(fixedInstant, ZoneId.of("UTC"));

        val expectedStart = ZonedDateTime.now(clock)
            .withMonth(7)
            .with(TemporalAdjusters.firstDayOfMonth());
        val expectedEnd = ZonedDateTime.now(clock).with(TemporalAdjusters.lastDayOfYear());

        // Act
        final ResetDateRange period = PromoCodeResetDateNormalizer.getDateRange(HALF_YEAR, clock);

        // Assert
        assertEquals(expectedStart.toLocalDate(), period.getStartRange().toLocalDate(), "Start of the second half-year is incorrect");
        assertEquals(expectedEnd.toLocalDate(), period.getEndRange().toLocalDate(), "End of the second half-year is incorrect");
    }

    @Test
    @DisplayName("Проверка периода - год")
    void getDateRangeYear() {
        // Arrange
        val expectedStart = ZonedDateTime.now(clock).with(TemporalAdjusters.firstDayOfYear());
        val expectedEnd = ZonedDateTime.now(clock).with(TemporalAdjusters.lastDayOfYear());

        // Act
        final ResetDateRange period = PromoCodeResetDateNormalizer.getDateRange(YEAR, clock);

        // Assert
        assertEquals(expectedStart.toLocalDate(), period.getStartRange().toLocalDate(), "Start of the year is incorrect");
        assertEquals(expectedEnd.toLocalDate(), period.getEndRange().toLocalDate(), "End of the year is incorrect");
    }
}
