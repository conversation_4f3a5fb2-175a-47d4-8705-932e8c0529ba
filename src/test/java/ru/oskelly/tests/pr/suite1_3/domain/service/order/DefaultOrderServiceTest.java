package ru.oskelly.tests.pr.suite1_3.domain.service.order;

import com.google.common.collect.ImmutableList;
import org.apache.commons.lang3.RandomStringUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.MockPublisherConfiguration;
import ru.oskelly.tests.pr.common.bonuses.BonusesControllerApiStub;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.BrandRepository;
import su.reddot.domain.dao.SizeRepository;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.address.AddressRepository;
import su.reddot.domain.dao.addressendpoint.AddressEndpointRepository;
import su.reddot.domain.dao.bonuses.UserBonusesAccountRepository;
import su.reddot.domain.dao.order.OrderPositionRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.order.SaleRejectionReasonRepository;
import su.reddot.domain.dao.product.ProductItemRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.exception.OrderException;
import su.reddot.domain.model.activity.Activity;
import su.reddot.domain.model.activity.order.OrderAnotherPaidActivity;
import su.reddot.domain.model.activity.order.OrderFirstPaidActivity;
import su.reddot.domain.model.address.Address;
import su.reddot.domain.model.addressendpoint.AddressEndpoint;
import su.reddot.domain.model.bonuses.UserBonusesAccount;
import su.reddot.domain.model.currency.CurrencyRate;
import su.reddot.domain.model.logistic.LogisticsInfo;
import su.reddot.domain.model.logistic.TimeInterval;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.order.OrderPositionState;
import su.reddot.domain.model.order.OrderSource;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.order.SaleRejectionReason;
import su.reddot.domain.model.order.SaleRejectionReasonType;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.activity.ActivityService;
import su.reddot.domain.service.address.CountryService;
import su.reddot.domain.service.bonuses.BonusesControllerAPI;
import su.reddot.domain.service.cart.CartService;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.domain.service.currency.CurrencyConverter;
import su.reddot.domain.service.currency.CurrencyRateService;
import su.reddot.domain.service.currency.CurrencyService;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.order.Discount;
import su.reddot.domain.service.order.InitOrderRequest;
import su.reddot.domain.service.order.OrderPaymentOkayParameters;
import su.reddot.domain.service.order.OrderPaymentService;
import su.reddot.domain.service.order.impl.DefaultOrderService;
import su.reddot.domain.service.order.impl.OrderRequestContext;
import su.reddot.domain.service.order.impl.OrderStateTitle;
import su.reddot.domain.service.order.impl.OrderStateTitleService;
import su.reddot.domain.service.order.impl.PositionStateTitle;
import su.reddot.domain.service.ordersourceinfo.OrderSourceInfoService;
import su.reddot.domain.service.payment.PaymentOption;
import su.reddot.domain.service.payment.impl.SplitOption;
import su.reddot.domain.service.payment.impl.noonapplepay.NoonApplePayOptionProvider;
import su.reddot.domain.service.payment.impl.nooncard.NoonCardOptionProvider;
import su.reddot.domain.service.payment.impl.sbp.SBPOptionProvider;
import su.reddot.domain.service.payment.impl.tabby.TabbySplitSettings;
import su.reddot.domain.service.payment.impl.yandexpay.YandexPayOption;
import su.reddot.domain.service.payment.impl.yandexpay.YandexPayOptionProvider;
import su.reddot.domain.service.payment.impl.yandexsplit.YandexSplitOptionProvider;
import su.reddot.infrastructure.bank.payments.oskelly.PaymentsServiceBankService;
import su.reddot.infrastructure.configparam.ConfigParamService;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.security.SecurityService;

import java.math.BigDecimal;
import java.time.Clock;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static java.math.BigDecimal.ZERO;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {OskellyApplication.class, MockPublisherConfiguration.class, DefaultOrderServiceTest.MockClockConfiguration.class, DefaultOrderServiceTest.BonusesServiceTestConfig.class})
@ActiveProfiles(profiles = AbstractSpringTest.testProfiles)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class DefaultOrderServiceTest {

    private static final int CLOCK_YEAR = 2014;
    private static final int CLOCK_MONTH = 12;
    private static final int CLOCK_DAY = 30;
    private static final long TEST_TIME_INTERVAL_ID = 3L;

    @Autowired
    UserRepository userRepository;
    @Autowired
    OrderRepository orderRepository;
    @Autowired
    BrandRepository brandRepository;
    @Autowired
    ProductItemRepository productItemRepository;
    @Autowired
    ProductRepository productRepository;
    @Autowired
    SizeRepository sizeRepository;
    @Autowired
    AddressRepository addressRepository;
    @Autowired
    AddressEndpointRepository addressEndpointRepository;
    @Autowired
    OrderPositionRepository orderPositionRepository;
    @Autowired
    SaleRejectionReasonRepository rejectionReasonRepository;
    @Autowired
    DefaultOrderService defaultOrderService;
    @Autowired
    private OrderStateTitleService orderStateTitleService;
    @Autowired
    OrderPaymentService orderPaymentService;
    @Autowired
    PaymentsServiceBankService paymentsServiceBankService;
    @Autowired
    BonusesControllerAPI bonusesControllerAPI;
    @Autowired
    OrderSourceInfoService orderSourceInfoService;

    @Autowired
    CommissionGridService commissionGridService;
    @Autowired
    ApplicationEventPublisher publisher;
    @MockBean
    ActivityService activityService;
    @Autowired
    CountryService countryService;
    @Autowired
    SecurityService securityService;
    private User buyer;
    private User effectiveBuyer;
    private User seller;
    private Product product;
    private ProductItem productItem;
    private Address pickupAddress;
    private Address deliveryAddress;
    private AddressEndpoint deliveryAddressEndpoint;
    private AddressEndpoint pickupAddressEndpoint;
    private OrderPosition firstOrderPosition;
    private OrderPosition secondPosition;

    @MockBean
    CartService cartService;
    @MockBean
    ConfigParamService configParamService;
    @MockBean
    private UserBonusesAccountRepository userBonusesAccountRepository;

    @Autowired
    private CurrencyService currencyService;

    @MockBean
    private CurrencyRateService currencyRateService;

    @BeforeEach
    public void init() {
        buyer = getUser();
        seller = getUser("testSellerUser123");
        product = getProduct(seller, new BigDecimal(1000));
        productItem = getProductItem(product);
        pickupAddress = getAddress("BigCity", seller);
        deliveryAddress = getAddress("small city", buyer);
        deliveryAddressEndpoint = getAddressEndpoint(buyer, deliveryAddress);
        pickupAddressEndpoint = getAddressEndpoint(seller, pickupAddress);
        firstOrderPosition = getOrderPosition(product, productItem);
        secondPosition = getOrderPosition(product, productItem);
    }


    @AfterEach
    public void cleanup() {
        reset(publisher);

        productItemRepository.delete(productItem);

        productRepository.delete(product);

        if (firstOrderPosition.getSaleRejectionReason() != null) {
            rejectionReasonRepository.delete(firstOrderPosition.getSaleRejectionReason());
        }
        if (secondPosition.getSaleRejectionReason() != null) {
            rejectionReasonRepository.delete(secondPosition.getSaleRejectionReason());
        }
        orderPositionRepository.delete(firstOrderPosition);
        orderPositionRepository.delete(secondPosition);

        addressEndpointRepository.delete(pickupAddressEndpoint);
        addressEndpointRepository.delete(deliveryAddressEndpoint);

        addressRepository.delete(pickupAddress);
        addressRepository.delete(deliveryAddress);

        List<Order> allByBuyerOrderByIdDesc = orderRepository.findAllByBuyerOrderByIdDesc(buyer);
        orderRepository.deleteAll(allByBuyerOrderByIdDesc);

        userRepository.delete(seller);
        userRepository.delete(buyer);
        if (effectiveBuyer != null) {
            userRepository.delete(effectiveBuyer);
        }
    }

    @Test
    @Transactional
    public void firstPaidOrderActivityShouldNotDuplicate() throws InterruptedException {
        Order firstOrder = getOrder("b8c4d726-7555-4e5f-bf6b-26f5e7a0037c");
        Thread.sleep(1000);
        Order secondOrder = getOrder("ba6efeea-da83-41e9-bfa6-aed47dc7096c");

        firstOrder = orderRepository.saveAndFlush(firstOrder);
        firstOrder.addPosition(firstOrderPosition);
        secondOrder.addPosition(secondPosition);
        secondPosition = orderPositionRepository.saveAndFlush(secondPosition);

        OrderPaymentOkayParameters paymentOkayParameters01 = OrderPaymentOkayParameters.builder()
                .bankService(paymentsServiceBankService)
                .paymentType(null).transactionId("12313").acquirerOrderId("23414")
                .build();
        secondOrder.setState(OrderState.HOLD_PROCESSING);
        secondOrder.setPaymentVersion(PaymentsServiceBankService.PAYMENT_SCHEME_GENERAL);
        orderPaymentService.initOrderPayment(secondOrder, null, null);
        orderPaymentService.setOrderAsPaymentOkay(secondOrder, paymentOkayParameters01);
        secondOrder = orderRepository.saveAndFlush(secondOrder);

        defaultOrderService.onHold(secondOrder);
        Thread.sleep(1000);

        OrderPaymentOkayParameters paymentOkayParameters02 = OrderPaymentOkayParameters.builder()
                .bankService(paymentsServiceBankService)
                .paymentType(null).transactionId("12314").acquirerOrderId("12313")
                .build();
        firstOrder.setState(OrderState.HOLD_PROCESSING);
        firstOrder.setPaymentVersion(PaymentsServiceBankService.PAYMENT_SCHEME_GENERAL);
        orderPaymentService.initOrderPayment(firstOrder, null, null);
        orderPaymentService.setOrderAsPaymentOkay(firstOrder, paymentOkayParameters02);
        defaultOrderService.onHold(firstOrder);

        firstOrderPosition = orderPositionRepository.saveAndFlush(firstOrderPosition);

        ArgumentCaptor<Activity> captor = ArgumentCaptor.forClass(Activity.class);
        then(activityService)
                .should(times(2))
                .saveNow(captor.capture());

        List<Activity> allValues = captor.getAllValues();

        assertThat(allValues.get(0)).isInstanceOf(OrderFirstPaidActivity.class);
        assertThat(allValues.get(1)).isInstanceOf(OrderAnotherPaidActivity.class);
    }

    @Test
    @Transactional
    public void initHoldSetsOrderSource() {
        Order order = getOrder("b8c4d726-7555-4e5f-bf6b-26f5e7a0037c");
        order.setState(OrderState.HOLD_PROCESSING);
        InitOrderRequest initOrderRequest = InitOrderRequest.builder().requestPaymentSystem("TEST").paymentBuyerCounterpartyId(1L).build();

        order.addPosition(firstOrderPosition);
        firstOrderPosition = orderPositionRepository.saveAndFlush(firstOrderPosition);
        order = orderRepository.saveAndFlush(order);

        assertNull(order.getSeller());

        defaultOrderService.initHold(order, buyer, initOrderRequest);

        assertNotNull(order.getSeller());

        Optional<Order> orderOptional = orderRepository.findById(order.getId());
        assertThat(orderOptional).isPresent();
        order = orderOptional.get();
        assertThat(order.getOrderSource()).isEqualByComparingTo(OrderSource.WEB);
    }

    @Test
    @Transactional
    public void testEffectiveBuyer() {
        LocalDateTime now = LocalDateTime.now();
        effectiveBuyer = getUser();
        Order order = getOrder("b8c4d726-7555-4e5f-bf6b-26f5e7a0037d", buyer, seller);
        order.setState(OrderState.COMPLETED);
        order.setEffectiveBuyer(effectiveBuyer);
        order.setSoldTime(now);
        order.setActionOrderSourceInfo(orderSourceInfoService.findOrderSourceInfoByNameCached("Boutique Stoleshnykov").get());
        order.setUuid(UUID.randomUUID());
        order = orderRepository.saveAndFlush(order);

        assertNotNull(order);

        OrderDTO dto = defaultOrderService.getOrderDTO(order, true, false, false);

        assertNotNull(dto);
        assertThat(Boolean.TRUE.equals(dto.getIsEffectiveBuyer())).isTrue();
        assertNotNull(dto.getSoldTime());
        assertThat(dto.getSoldTime().equals(now.atOffset(ZoneOffset.UTC).toZonedDateTime())).isTrue();

        assertNotNull(dto.getBoutiqueAddress());
    }

    /**
     * Распределение сгораемых бонусов на одну позицию в незавершенном ранее заказе
     */
    @Test
    @Transactional
    public void testInitHoldCheckBonusesWithdrawDistributionForExistingOrder() {
        BigDecimal amountExpected = new BigDecimal(500);
        BigDecimal bonusesExpected = new BigDecimal(500);
        BigDecimal moneyExpected = ZERO;
        UserBonusesAccount userBonusesAccount = new UserBonusesAccount();
        userBonusesAccount.setBonusesAccountId(UUID.randomUUID().toString());
        when(userBonusesAccountRepository.findByUserId(any(Long.class)))
                .thenReturn(Optional.of(userBonusesAccount));
        Order order = bonusesDistributionCheck(500,1000, 500, false, true);

        assertThat(order).isNotNull();
        assertThat(bonusesExpected.compareTo(order.getWithdrawBonusBonuses()) == 0).isTrue();
        assertThat(moneyExpected.compareTo(order.getWithdrawMoneyBonuses()) == 0).isTrue();
        assertThat(amountExpected.compareTo(order.getAmount()) == 0).isTrue();

        assertThat(order.getOrderPositions()).isNotNull();
        assertThat(order.getOrderPositions().size() == 1).isTrue();
        assertThat(bonusesExpected.compareTo(order.getOrderPositions().get(0).getWithdrawBonusBonuses()) == 0).isTrue();
        assertThat(moneyExpected.compareTo(order.getOrderPositions().get(0).getWithdrawMoneyBonuses()) == 0).isTrue();
        assertThat(order.getOrderPositions().get(0).getEffectiveWithdrawBonusBonuses()).isNull();
        assertThat(order.getOrderPositions().get(0).getEffectiveWithdrawMoneyBonuses()).isNull();
    }

    /**
     * Распределение сгораемых бонусов на одну позицию в заказе
     */
    @Test
    @Transactional
    public void testInitHoldCheckBonusesWithdrawDistribution1() {
        BigDecimal amountExpected = new BigDecimal(500);
        BigDecimal bonusesExpected = new BigDecimal(500);
        BigDecimal moneyExpected = ZERO;
        UserBonusesAccount userBonusesAccount = new UserBonusesAccount();
        userBonusesAccount.setBonusesAccountId(UUID.randomUUID().toString());
        when(userBonusesAccountRepository.findByUserId(any(Long.class)))
                .thenReturn(Optional.of(userBonusesAccount));
        Order order = bonusesDistributionCheck(500,1000, 500, false, false);

        assertThat(order).isNotNull();
        assertThat(bonusesExpected.compareTo(order.getWithdrawBonusBonuses()) == 0).isTrue();
        assertThat(moneyExpected.compareTo(order.getWithdrawMoneyBonuses()) == 0).isTrue();
        assertThat(amountExpected.compareTo(order.getAmount()) == 0).isTrue();

        assertThat(order.getOrderPositions()).isNotNull();
        assertThat(order.getOrderPositions().size() == 1).isTrue();
        assertThat(bonusesExpected.compareTo(order.getOrderPositions().get(0).getWithdrawBonusBonuses()) == 0).isTrue();
        assertThat(moneyExpected.compareTo(order.getOrderPositions().get(0).getWithdrawMoneyBonuses()) == 0).isTrue();
        assertThat(order.getOrderPositions().get(0).getEffectiveWithdrawBonusBonuses()).isNull();
        assertThat(order.getOrderPositions().get(0).getEffectiveWithdrawMoneyBonuses()).isNull();
    }

    /**
     * Обычный заказ без бонусов. Проверка, что поля, в которых в заказе хранятся бонусы пустые
     */
    @Test
    @Transactional
    public void testInitHoldCheckBonusesWithdrawDistribution2() {
        BigDecimal amountExpected = new BigDecimal(1000);
        Order order = bonusesDistributionCheck(0,1000, 500, false, false);

        assertThat(order).isNotNull();
        assertThat(order.getWithdrawBonusBonuses()).isNull();
        assertThat(order.getWithdrawMoneyBonuses()).isNull();
        assertThat(amountExpected.compareTo(order.getAmount()) == 0).isTrue();

        assertThat(order.getOrderPositions()).isNotNull();
        assertThat(order.getOrderPositions().size() == 1).isTrue();
        assertThat(order.getOrderPositions().get(0).getWithdrawBonusBonuses()).isNull();
        assertThat(order.getOrderPositions().get(0).getWithdrawMoneyBonuses()).isNull();
        assertThat(order.getOrderPositions().get(0).getEffectiveWithdrawBonusBonuses()).isNull();
        assertThat(order.getOrderPositions().get(0).getEffectiveWithdrawMoneyBonuses()).isNull();
    }

    /**
     * Распределение на две позиции заказа с недостатком бонусного бюджета
     */
    @Test
    @Transactional
    public void testInitHoldCheckBonusesWithdrawDistribution3() {
        BigDecimal amountExpected = new BigDecimal(1500);
        BigDecimal bonusesExpected = new BigDecimal(200);
        BigDecimal moneyExpected = new BigDecimal(300);
        BigDecimal positionBonusesExpected = new BigDecimal(100);
        BigDecimal positionMoneyExpected = new BigDecimal(150);

        UserBonusesAccount userBonusesAccount = new UserBonusesAccount();
        userBonusesAccount.setBonusesAccountId(UUID.randomUUID().toString());
        when(userBonusesAccountRepository.findByUserId(any(Long.class)))
                .thenReturn(Optional.of(userBonusesAccount));

        Order order = bonusesDistributionCheck(500,200, 300, true, false);

        assertThat(order).isNotNull();
        assertThat(bonusesExpected.compareTo(order.getWithdrawBonusBonuses()) == 0).isTrue();
        assertThat(moneyExpected.compareTo(order.getWithdrawMoneyBonuses()) == 0).isTrue();
        assertThat(amountExpected.compareTo(order.getAmount()) == 0).isTrue();

        assertThat(order.getOrderPositions()).isNotNull();
        assertThat(order.getOrderPositions().size() == 2).isTrue();
        for (OrderPosition position: order.getOrderPositions()) {
            assertThat(positionBonusesExpected.compareTo(position.getWithdrawBonusBonuses()) == 0).isTrue();
            assertThat(positionMoneyExpected.compareTo(position.getWithdrawMoneyBonuses()) == 0).isTrue();
            assertThat(position.getEffectiveWithdrawBonusBonuses()).isNull();
            assertThat(position.getEffectiveWithdrawMoneyBonuses()).isNull();
        }
    }

    /**
     * Распределение на две позиции заказа с недостатком бонусного бюджета
     */
    @Test
    @Transactional
    public void testInitHoldCheckBonusesTransferDistribution() {
        BigDecimal amountExpected = new BigDecimal(2000);

        Order order = bonusesDistributionCheck(0,200, 300, true, false);

        assertThat(order).isNotNull();
        assertThat(order.getWithdrawBonusBonuses()).isNull();
        assertThat(order.getWithdrawMoneyBonuses()).isNull();
        assertThat(ZERO.compareTo(order.getTransferredBonuses()) == 0).isTrue();
        assertThat(amountExpected.compareTo(order.getAmount()) == 0).isTrue();

        assertThat(order.getOrderPositions()).isNotNull();
        assertThat(order.getOrderPositions().size() == 2).isTrue();
        for (OrderPosition position: order.getOrderPositions()) {
            assertThat(ZERO.compareTo(position.getTransferredBonuses()) == 0).isTrue();
            assertThat(position.getEffectiveWithdrawBonusBonuses()).isNull();
            assertThat(position.getEffectiveWithdrawMoneyBonuses()).isNull();
            assertThat(position.getEffectiveTransferredBonuses()).isNull();
        }
    }

    private void setUserBonusesBalance(int bonuses, int money) {
        BonusesControllerApiStub bonusesApi = (BonusesControllerApiStub) bonusesControllerAPI;
        bonusesApi.setBalance(new BigDecimal(bonuses), new BigDecimal(money));
    }

    private Order bonusesDistributionCheck(
            int bonusesToDistribute, int bonusesBalance, int moneyBalance, boolean twoPositions, boolean bonusesAlreadyInOrder
    ) {
        setUserBonusesBalance(bonusesBalance, moneyBalance);

        BigDecimal bonusesToDistributeInitRequest = bonusesAlreadyInOrder ? null : new BigDecimal(bonusesToDistribute);
        BigDecimal bonusesToDistributeAlreadyInOrder = bonusesAlreadyInOrder ? new BigDecimal(bonusesToDistribute) : null;

        Order order = getOrder("b8c4d726-7555-4e5f-bf6b-26f5e7a0037c", buyer);
        order.setState(OrderState.HOLD_PROCESSING);
        InitOrderRequest initOrderRequest = InitOrderRequest
                .builder()
                .requestPaymentSystem("TEST")
                .paymentBuyerCounterpartyId(1L)
                .quitOnPaymentsInit(true)
                .bonuses(bonusesToDistributeInitRequest)
                .initialCallFromHoldV2(!bonusesAlreadyInOrder)
                .build();

        order.addPosition(firstOrderPosition);
        firstOrderPosition = orderPositionRepository.saveAndFlush(firstOrderPosition);
        if (twoPositions) {
            order.addPosition(secondPosition);
            secondPosition = orderPositionRepository.saveAndFlush(secondPosition);
        }

        if (bonusesAlreadyInOrder) {
            order.setWithdrawBonusBonuses(bonusesToDistributeAlreadyInOrder);
        }

        order = orderRepository.saveAndFlush(order);

        assertNull(order.getSeller());

        //Распределение бонусов, как и реальное списание работает в два этапа. initHold вызывается сначала с quitOnPaymentsInit = true, а потом с = false
        //вызов с true
        defaultOrderService.initHold(order, buyer, initOrderRequest);
        //вызов с false
        initOrderRequest = InitOrderRequest
                .builder()
                .requestPaymentSystem("TEST")
                .paymentBuyerCounterpartyId(1L)
                .bonuses(bonusesToDistributeInitRequest)
                .quitOnPaymentsInit(false)
                .build();
        defaultOrderService.initHold(order, buyer, initOrderRequest);

        Optional<Order> orderOptional = orderRepository.findById(order.getId());
        assertThat(orderOptional).isPresent();
        return orderOptional.get();
    }

    @Test
    public void shouldReturnAvailablePickupDates() {
        Collection<LocalDate> availablePickupDates = defaultOrderService.getAvailablePickupDates();
        Assertions.assertThat(availablePickupDates).containsExactlyInAnyOrder(
                LocalDate.of(CLOCK_YEAR, CLOCK_MONTH, CLOCK_DAY + 1),
                LocalDate.of(CLOCK_YEAR + 1, 1, 1),
                LocalDate.of(CLOCK_YEAR + 1, 1, 2)
        );
    }

    @Test
    @Transactional
    @WithMockUser(authorities = {"ROLE_USER"})
    @Disabled
    public void shouldSetupPickupDate() {
        Order testOrder = getOrder("0a6cb740-0c2c-11ee-be56-0242ac120002");
        LocalDate[] availablePickupDates = defaultOrderService.getAvailablePickupDates().toArray(new LocalDate[0]);
        TimeInterval[] availableTimeIntervals = defaultOrderService.getTimeIntervals().toArray(new TimeInterval[0]);
        LocalDate selectedPickupDate = availablePickupDates[0];
        TimeInterval selectedTimeInterval = availableTimeIntervals[0];

        defaultOrderService.setPickupDateAndTimeInterval(
                testOrder.getId(),
                OffsetDateTime.of(selectedPickupDate, LocalTime.of(0, 0, 0), ZoneOffset.UTC),
                selectedTimeInterval.getId()
        );
        Order updatedOrder = orderRepository.getOne(testOrder.getId());

        LogisticsInfo logisticsInfo = updatedOrder.getSellerLogisticsInfo();
        Assertions.assertThat(logisticsInfo.getPickupDate().getYear()).isEqualTo(selectedPickupDate.getYear());
        Assertions.assertThat(logisticsInfo.getPickupDate().getMonthValue()).isEqualTo(selectedPickupDate.getMonthValue());
        Assertions.assertThat(logisticsInfo.getPickupDate().getDayOfMonth()).isEqualTo(selectedPickupDate.getDayOfMonth());
        Assertions.assertThat(logisticsInfo.getPickupTimeIntervalId()).isEqualTo(TEST_TIME_INTERVAL_ID);
    }

    @Test
    @Transactional
    @WithMockUser(authorities = {"ROLE_USER"}, username = "testSellerUser123")
    @Disabled
    public void shouldCreateRejectionReason_whenDeclineWithReason() {
        Order testOrder = getOrder("0a6cb740-0c2c-11ee-be56-0242ac120002");
        testOrder.addPosition(firstOrderPosition);
        testOrder.addPosition(secondPosition);
        orderPositionRepository.flush();

        defaultOrderService.handleConfirmOrderPositionSale(
            testOrder.getId(),
            firstOrderPosition.getId(),
            false,
            null,
            null,
            new SaleRejectionReason()
                .setReasonTypeId(SaleRejectionReasonType.Type.CHANGE_DECISION.getId())
                .setComment("reason comment"),
            false
        );

        OrderPosition rejectionFirstPosition = orderPositionRepository.getOne(firstOrderPosition.getId());
        Assertions.assertThat(rejectionFirstPosition.getSaleRejectionReason()).isNotNull();
        Assertions.assertThat(rejectionFirstPosition.getSaleRejectionReason().getReasonTypeId())
            .isEqualTo(SaleRejectionReasonType.Type.CHANGE_DECISION.getId());
        Assertions.assertThat(rejectionFirstPosition.getSaleRejectionReason().getComment()).isEqualTo("reason comment");

        OrderPosition rejectionSecondPosition = orderPositionRepository.getOne(secondPosition.getId());
        Assertions.assertThat(rejectionSecondPosition.getSaleRejectionReason()).isNull();
    }

    @Test
    @Transactional
    @WithMockUser(authorities = {"ROLE_USER"}, username = "testSellerUser123")
    @Disabled
    public void shouldNotCreateRejectReason_whenDeclineWithoutReason() {
        Order testOrder = getOrder("0a6cb740-0c2c-11ee-be56-0242ac120002");
        testOrder.addPosition(firstOrderPosition);
        testOrder.addPosition(secondPosition);
        orderPositionRepository.flush();

        defaultOrderService.handleConfirmOrderPositionSale(
            testOrder.getId(),
            firstOrderPosition.getId(),
            false,
            null,
            null,
            null,
            false
        );

        OrderPosition rejectionFirstPosition = orderPositionRepository.getOne(firstOrderPosition.getId());
        Assertions.assertThat(rejectionFirstPosition.getSaleRejectionReason()).isNull();

        OrderPosition rejectionSecondPosition = orderPositionRepository.getOne(secondPosition.getId());
        Assertions.assertThat(rejectionSecondPosition.getSaleRejectionReason()).isNull();
    }

    @Test
    @Transactional
    @WithMockUser(authorities = {"ROLE_USER"}, username = "testSellerUser123")
    @Disabled
    public void shouldNotCreateSeveralRejectReasonsForOneOrderPosition_whenDoConfirmDeclineSeveralTimes() {
        Order testOrder = getOrder("0a6cb740-0c2c-11ee-be56-0242ac120002");
        testOrder.addPosition(firstOrderPosition);
        testOrder.addPosition(secondPosition);
        orderPositionRepository.flush();

        defaultOrderService.handleConfirmOrderPositionSale(
            testOrder.getId(),
            firstOrderPosition.getId(),
            false,
            null,
            null,
            new SaleRejectionReason()
                .setReasonTypeId(SaleRejectionReasonType.Type.CHANGE_DECISION.getId())
                .setComment("reason comment"),
            false
        );
        List<SaleRejectionReason> reasonsAfterFirstRejection = rejectionReasonRepository.findAll();
        Assertions.assertThat(reasonsAfterFirstRejection).hasSize(1);

        defaultOrderService.handleConfirmOrderPositionSale(
            testOrder.getId(),
            firstOrderPosition.getId(),
            true,
            null,
            null,
            null,
            false
        );
        List<SaleRejectionReason> reasonsAfterFirstConfirmation = rejectionReasonRepository.findAll();
        Assertions.assertThat(reasonsAfterFirstConfirmation).hasSize(0);

        defaultOrderService.handleConfirmOrderPositionSale(
            testOrder.getId(),
            firstOrderPosition.getId(),
            false,
            null,
            null,
            new SaleRejectionReason()
                .setReasonTypeId(SaleRejectionReasonType.Type.CHANGE_DECISION.getId())
                .setComment("reason comment"),
            false
        );
        List<SaleRejectionReason> reasonsAfterSecondRejection = rejectionReasonRepository.findAll();
        Assertions.assertThat(reasonsAfterSecondRejection).hasSize(1);
    }

    private static OrderPosition getOrderPosition(Product product, ProductItem productItem) {
        OrderPosition firstOrderPosition = new OrderPosition();
        firstOrderPosition.setState(OrderPositionState.VERIFICATION_OK);
        firstOrderPosition.setIsEffective(true);
        firstOrderPosition.setParticipatesInPayment(true);
        firstOrderPosition.setStateTime(LocalDateTime.now());
        firstOrderPosition.setProductItem(productItem);
        BigDecimal amount = product.getCurrentPrice();
        firstOrderPosition.setAmount(amount);
        firstOrderPosition.setItemSaleAmount(amount);
        firstOrderPosition.setCommission(BigDecimal.valueOf(15.0));
        return firstOrderPosition;
    }

    private Order getOrderWithBuyerAndSeller(BigDecimal amount) {
        Product testProduct = getProduct(seller, amount);
        ProductItem testProductItem = getProductItem(testProduct);
        OrderPosition testOrderPosition = getOrderPosition(testProduct, testProductItem);
        Order testOrder = getOrder(UUID.randomUUID().toString(), securityService.getCurrentAuthorizedUser(), seller);
        testOrder.addPosition(testOrderPosition);
        testOrder.setAmount(amount);
        return testOrder;
        //return getOrder(name, buyer);
    }

    private Order getOrder(String name) {
        return getOrder(name, buyer);
    }

    private Order getOrder(String name, User b) {
        return getOrder(name, b, null);
    }

    private Order getOrder(String name, User b, User s) {
        Order order = new Order();
        order.setUuid(UUID.fromString(name));
        order.setState(OrderState.CREATED);
        if (s != null) {
            order.setSeller(s);
        }
        order.setBuyer(b);
        order.setAmount(BigDecimal.ONE);
        order.setDeliveryAddressEndpoint(deliveryAddressEndpoint);
        order.setPickupAddressEndpoint(pickupAddressEndpoint);
        order.setEffectiveAmount(BigDecimal.valueOf(100));
        order = orderRepository.saveAndFlush(order);
        return order;
    }

    private AddressEndpoint getAddressEndpoint(User buyer, Address secondAddress) {
        AddressEndpoint addressEndpoint = new AddressEndpoint();
        addressEndpoint.setAddress(secondAddress);
        addressEndpoint.setUser(buyer);
        addressEndpoint.setPhone("123456");
        addressEndpoint.setFirstName("Test");
        addressEndpoint.setLastName("Test");
        addressEndpoint = addressEndpointRepository.saveAndFlush(addressEndpoint);
        return addressEndpoint;
    }

    private Address getAddress(String BigCity, User seller) {
        Address address = new Address();
        address.setCity(BigCity);
        address.setUser(seller);
        address = addressRepository.saveAndFlush(address);
        return address;
    }

    private ProductItem getProductItem(Product product) {
        ProductItem productItem = new ProductItem();
        productItem.setProduct(product);
        productItem.setSize(sizeRepository.getOne(1L));
        productItem = productItemRepository.saveAndFlush(productItem);
        return productItem;
    }

    private Product getProduct(User seller, BigDecimal currentPrice) {
        Product product = new Product();
        product.setBrand(brandRepository.getOne(1L));
        product.setCategoryId(2L);
        product.setProductConditionId(1L);
        product.setSeller(seller);
        product.setCurrentPrice(currentPrice);
        product.setProductState(ProductState.PUBLISHED);
        product = productRepository.saveAndFlush(product);
        return product;
    }

    private User getUser() {
        return getUser(RandomStringUtils.randomAlphabetic(5));
    }

    private User getUser(String nickname) {
        User buyer = new User()
                .setNickname(nickname)
                .setUserType(User.UserType.SIMPLE_USER)
                .setChangeTime(LocalDateTime.now())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid())
                //TODO при включении сервиса лояльности нужно проверить и убрать setIsLoyaltyProgramAccepted(true)
                .setIsLoyaltyProgramAccepted(true)
                .setIsLoyaltyProgramV2Accepted(ZonedDateTime.now())
                .setCounterparties(Collections.emptyList());
        buyer = userRepository.saveAndFlush(buyer);
        return buyer;
    }

    @Test
    public void markOrdersSellerConfirmOrderNotifiedSetFlag() {
        Order order = new Order();
        order.setUuid(UUID.randomUUID());
        order.setState(OrderState.CREATED);
        order.setBuyer(buyer);
        order.setAmount(BigDecimal.ONE);
        order = orderRepository.save(order);

        defaultOrderService.markOrdersSellerConfirmOrderNotified(Collections.singletonList(order.getId()));

        Optional<Order> optionalOrder = orderRepository.findById(order.getId());
        assertThat(optionalOrder.get().getSellerOrderConfirmNotified()).isTrue();

        orderRepository.delete(order);
    }

    @Test
    public void testEachPositionStateTitleIsTranslated() {
        final List<OrderRequestContext> contexts = ImmutableList.of(
                OrderRequestContext.builder().isForBuyer(true).isForSeller(false).isPro(false).currencyConverter(mock(CurrencyConverter.class)).build(),
                OrderRequestContext.builder().isForBuyer(false).isForSeller(true).isPro(false).currencyConverter(mock(CurrencyConverter.class)).build(),
                OrderRequestContext.builder().isForBuyer(false).isForSeller(false).isPro(true).currencyConverter(mock(CurrencyConverter.class)).build()); // TODO: Strange, talk to somebody
        for (OrderRequestContext context : contexts) {
            for (PositionStateTitle stateTitle : PositionStateTitle.values()) {
                defaultOrderService.getStateTitleMessage(stateTitle, context);
            }
        }
    }

    @Test
    public void testEachOrderStateTitleIsTranslated() {
        for (String userContext : ImmutableList.of("Buyer", "Seller", "OnlineBoutique")) {
            for (OrderStateTitle stateTitle : OrderStateTitle.values()) {
                orderStateTitleService.getTitleString(stateTitle, userContext);
            }
        }
    }

    @WithMockUser(authorities = {"ROLE_USER"})
    @Transactional
    @Test
    public void testGetPaymentOptions() {

        //если orderid и sellerid null
        assertThrows(OrderException.class, () -> {
            defaultOrderService.getPaymentOptions(null, null, null,
                    false, false, false, false, null);
        });


        Order testOrder = getOrderWithBuyerAndSeller(new BigDecimal(200));
        orderRepository.save(testOrder);

        when(configParamService.getValueAsDoubleCached(configParamService.CONFIG_PARAM_SPLIT_PRODUCT_MAX_PRICE))
                .thenReturn(1000000d);
        when(configParamService.getValueAsDoubleCached(configParamService.CONFIG_PARAM_SPLIT_PRODUCT_MIN_PRICE))
                .thenReturn(10d);
        when(configParamService.getValueAsDoubleCached(configParamService.CONFIG_PARAM_YANDEX_PLUS_POINTS_PRODUCT_MAX_AMOUNT))
                .thenReturn(1000000d);
        when(configParamService.getValueAsDoubleCached(configParamService.CONFIG_PARAM_NOON_APPLE_PAY_MAX_PRICE))
                .thenReturn(1000000d);
        when(configParamService.getValueAsBoolean(eq(configParamService.CONFIG_PARAM_YANDEX_PLUS_POINTS_CALC_FOR_CONCIERGE_PRODUCTS), anyBoolean()))
                .thenReturn(true);

        // with OrderId not null
        List<PaymentOption> paymentOptionsOrder = defaultOrderService.getPaymentOptions(testOrder.getId(), null, null, false, false,
                false, false, null);

        assertTrue(paymentOptionsOrder.size() > 0);

        // if doesn't suppport yandex then no yandex payment options
        assertTrue(paymentOptionsOrder.stream()
                .noneMatch(p -> p.getType().equals(YandexSplitOptionProvider.TYPE)));
        assertTrue(paymentOptionsOrder.stream()
                .noneMatch(p -> p.getType().equals(YandexPayOptionProvider.TYPE)));

        // if doesn't suppport sbp then no sbp payment options
        assertTrue(paymentOptionsOrder.stream()
                .noneMatch(p -> p.getType().equals(SBPOptionProvider.TYPE)));

        assertTrue(paymentOptionsOrder.stream()
                .anyMatch(p -> p.getType().equals(NoonCardOptionProvider.TYPE)));

        // Всегда возвращается NoonApplePayOptionProvider, если только не выключен в настройках
        // и если сумма заказа не выше лимита
        assertTrue(paymentOptionsOrder.stream()
                .anyMatch(p -> p.getType().equals(NoonApplePayOptionProvider.TYPE)));

        assertTrue(paymentOptionsOrder.stream()
                .noneMatch(p -> p.getType().equals(TabbySplitSettings.TYPE)));


        when(configParamService.getValueAsDoubleCached(configParamService.CONFIG_PARAM_NOON_APPLE_PAY_MAX_PRICE))
                .thenReturn(100d);

        paymentOptionsOrder = defaultOrderService.getPaymentOptions(testOrder.getId(), null,
                null, false, true, false, false, null);

        assertTrue(paymentOptionsOrder.stream()
                .noneMatch(p -> p.getType().equals(YandexSplitOptionProvider.TYPE)));
        assertTrue(paymentOptionsOrder.stream()
                .noneMatch(p -> p.getType().equals(YandexPayOptionProvider.TYPE)));
        assertTrue(paymentOptionsOrder.stream()
                .anyMatch(p -> p.getType().equals(SBPOptionProvider.TYPE)));
        assertTrue(paymentOptionsOrder.stream()
                .anyMatch(p -> p.getType().equals(NoonCardOptionProvider.TYPE)));
        // так как сумма заказа теперь больше лимита, то этот способ уже не возвращается
        assertTrue(paymentOptionsOrder.stream()
                .noneMatch(p -> p.getType().equals(NoonApplePayOptionProvider.TYPE)));
        assertTrue(paymentOptionsOrder.stream()
                .noneMatch(p -> p.getType().equals(TabbySplitSettings.TYPE)));


        when(configParamService.getValueAsDoubleCached(configParamService.CONFIG_PARAM_NOON_APPLE_PAY_MAX_PRICE))
                .thenReturn(1000000d);

        paymentOptionsOrder = defaultOrderService.getPaymentOptions(testOrder.getId(),
                null, null, true, false, false, false, null);

        assertTrue(paymentOptionsOrder.stream()
                .anyMatch(p -> p.getType().equals(YandexSplitOptionProvider.TYPE)));
        assertTrue(paymentOptionsOrder.stream()
                .anyMatch(p -> p.getType().equals(YandexPayOptionProvider.TYPE)));
        assertTrue(paymentOptionsOrder.stream()
                .noneMatch(p -> p.getType().equals(SBPOptionProvider.TYPE)));
        assertTrue(paymentOptionsOrder.stream()
                .anyMatch(p -> p.getType().equals(NoonCardOptionProvider.TYPE)));
        assertTrue(paymentOptionsOrder.stream()
                .anyMatch(p -> p.getType().equals(NoonApplePayOptionProvider.TYPE)));
        assertTrue(paymentOptionsOrder.stream()
                .noneMatch(p -> p.getType().equals(TabbySplitSettings.TYPE)));

        //Проверка на минимальную сумму заказа для использования yandex split
        Order testOrderYandexSplitMin = getOrderWithBuyerAndSeller(new BigDecimal(5));
        orderRepository.save(testOrderYandexSplitMin);

        paymentOptionsOrder = defaultOrderService.getPaymentOptions(testOrderYandexSplitMin.getId(),
                null, null, true, false, false, false, null);

        assertTrue(paymentOptionsOrder.stream()
                .noneMatch(p -> p.getType().equals(YandexSplitOptionProvider.TYPE)));

        //
        String aedCurrency = "AED";

        presetRate(aedCurrency, new BigDecimal("50.00"));
        when(configParamService.getValueAsList(ConfigParamService.CONFIG_PARAM_TABBY_COUNTRIES)).thenReturn(ImmutableList.of("AE"));
        User user = securityService.getCurrentAuthorizedUser();
        user.setCountry(countryService.findByAlpha2Code("AE"));
        userRepository.save(user);

        paymentOptionsOrder = defaultOrderService.getPaymentOptions(testOrder.getId(), null,
                null, false, false, true, false, aedCurrency);

        assertTrue(paymentOptionsOrder.stream()
                .noneMatch(p -> p.getType().equals(YandexSplitOptionProvider.TYPE)));
        assertTrue(paymentOptionsOrder.stream()
                .noneMatch(p -> p.getType().equals(YandexPayOptionProvider.TYPE)));
        assertTrue(paymentOptionsOrder.stream()
                .noneMatch(p -> p.getType().equals(SBPOptionProvider.TYPE)));
        assertTrue(paymentOptionsOrder.stream()
                .anyMatch(p -> p.getType().equals(NoonCardOptionProvider.TYPE)));
        assertTrue(paymentOptionsOrder.stream()
                .anyMatch(p -> p.getType().equals(NoonApplePayOptionProvider.TYPE)));
        assertTrue(paymentOptionsOrder.stream()
                .anyMatch(p -> p.getType().equals(TabbySplitSettings.TYPE)));

        user.setCountry(null);
        userRepository.save(user);
        paymentOptionsOrder = defaultOrderService.getPaymentOptions(testOrder.getId(), null,
                null, false, false, true, false, aedCurrency);
        assertFalse(paymentOptionsOrder.stream()
                .anyMatch(p -> p.getType().equals(TabbySplitSettings.TYPE)));

    }

    @WithMockUser(authorities = {"ROLE_USER"})
    @Transactional
    @Test
    public void testGetYandexSplitPaymentOption() {

        when(configParamService.getValueAsDoubleCached(configParamService.CONFIG_PARAM_SPLIT_PRODUCT_MAX_PRICE))
                .thenReturn(1000000d);

        Order testOrder = getOrderWithBuyerAndSeller(new BigDecimal(200));
        orderRepository.save(testOrder);

        List<PaymentOption> paymentOptionsOrder = defaultOrderService.getPaymentOptions(testOrder.getId(), null,
                null, true, false, false, false, null);

        Optional<PaymentOption> splitPayment = paymentOptionsOrder.stream()
                .filter(p -> p.getType().equals(YandexSplitOptionProvider.TYPE)).findFirst();
        assertTrue(splitPayment.isPresent());
        assertTrue(splitPayment.get() instanceof SplitOption);

        SplitOption yandexSplitOption = (SplitOption) splitPayment.get();
        assertTrue(yandexSplitOption.getFirstPayment().equals(new BigDecimal(50d)));
        assertTrue(yandexSplitOption.getRemainingPayment().equals(new BigDecimal(150d)));

        OrderDTO orderDtoTest = new OrderDTO();
        orderDtoTest.setFinalAmount(new BigDecimal(199.5d));

        OrderDTO orderDtoWithDiscountTest = new OrderDTO();
        orderDtoWithDiscountTest.setFinalAmount(new BigDecimal(200.4d));

        Discount discountTest = new Discount();
        discountTest.discountValue = new BigDecimal(100.3d);

        orderDtoWithDiscountTest.setDiscount(discountTest);

        //with Discount
        when(cartService.getOrderDTOWithParams(eq(1L), any())).thenReturn(Optional.of(orderDtoWithDiscountTest));
        assertTrue(defaultOrderService.getPaymentOptions(null, 1L, "",
                true, false, false, false, null).size() > 0);
        List<PaymentOption> paymentOptionsWithDiscount = defaultOrderService.getPaymentOptions(null, 1L,
                "", true, false, false, false, null);

        splitPayment = paymentOptionsWithDiscount.stream()
                .filter(p -> p.getType().equals(YandexSplitOptionProvider.TYPE)).findFirst();
        assertTrue(splitPayment.isPresent());
        assertTrue(splitPayment.get() instanceof SplitOption);

        yandexSplitOption = (SplitOption) splitPayment.get();
        assertTrue(yandexSplitOption.getFirstPayment().equals(new BigDecimal(25d)));
        assertTrue(yandexSplitOption.getRemainingPayment().equals(new BigDecimal(75d)));

        //without Discount
        when(cartService.getOrderDTOWithParams(eq(2L), any())).thenReturn(Optional.of(orderDtoTest));
        assertTrue(defaultOrderService.getPaymentOptions(null, 2L, "",
                true, false, false, false, null).size() > 0);
        List<PaymentOption> paymentOptions = defaultOrderService.getPaymentOptions(null, 2L,
                "", true, false, false, false, null);

        splitPayment = paymentOptions.stream()
                .filter(p -> p.getType().equals(YandexSplitOptionProvider.TYPE)).findFirst();
        assertTrue(splitPayment.isPresent());
        assertTrue(splitPayment.get() instanceof SplitOption);

        yandexSplitOption = (SplitOption) splitPayment.get();
        assertTrue(yandexSplitOption.getFirstPayment().equals(new BigDecimal(50d)));
        assertTrue(yandexSplitOption.getRemainingPayment().equals(new BigDecimal(150d)));

        //с учетом бонусов
        UserBonusesAccount userBonusesAccount = new UserBonusesAccount();
        userBonusesAccount.setBonusesAccountId(UUID.randomUUID().toString());
        when(userBonusesAccountRepository.findByUserId(any(Long.class)))
                .thenReturn(Optional.of(userBonusesAccount));
        setUserBonusesBalance(40, 0);
        Order testOrder2 = getOrderWithBuyerAndSeller(new BigDecimal(200));
        orderRepository.save(testOrder2);

        List<PaymentOption> paymentOptionsOrder2 = defaultOrderService.getPaymentOptions(testOrder2.getId(), null,
                null, true, false, false, true, null);

        Optional<PaymentOption> splitPayment2 = paymentOptionsOrder2.stream()
                .filter(p -> p.getType().equals(YandexSplitOptionProvider.TYPE)).findFirst();
        assertTrue(splitPayment2.isPresent());
        assertTrue(splitPayment2.get() instanceof SplitOption);

        SplitOption yandexSplitOption2 = (SplitOption) splitPayment2.get();
        assertTrue(yandexSplitOption2.getFirstPayment().equals(new BigDecimal(40d)));
        assertTrue(yandexSplitOption2.getRemainingPayment().equals(new BigDecimal(120d)));
    }

    private void presetRate(String currencyCode, BigDecimal rate) {
        long convertToCurrencyId = currencyService.getCurrencyDTOByCodeCached(currencyCode).getId();
        long baseCurrencyId = currencyService.getBaseCurrency().getId();

        CurrencyRate aedRate = new CurrencyRate();
        aedRate.setRateValue(rate);
        when(currencyRateService.findCurrencyRateWithCurrencyIdAndCurrencyToId(convertToCurrencyId,
                baseCurrencyId)).thenReturn(aedRate);
    }

    @WithMockUser(authorities = {"ROLE_USER"})
    @Transactional
    @Test
    public void testGetTabbySplitPaymentOption() {

        when(configParamService.getValueAsDoubleCached(configParamService.CONFIG_PARAM_SPLIT_PRODUCT_MAX_PRICE))
                .thenReturn(1000000d);
        when(configParamService.getValueAsList(ConfigParamService.CONFIG_PARAM_TABBY_COUNTRIES)).thenReturn(ImmutableList.of("AE"));

        Order testOrder = getOrderWithBuyerAndSeller(new BigDecimal(20000));
        orderRepository.save(testOrder);

        String tabbySupportedCurrency = "AED";

        presetRate(tabbySupportedCurrency, new BigDecimal("50.00"));
        User user = securityService.getCurrentAuthorizedUser();
        user.setCountry(countryService.findByAlpha2Code("AE"));
        userRepository.save(user);
        List<PaymentOption> paymentOptionsOrder = defaultOrderService.getPaymentOptions(testOrder.getId(),
                null, null, false, false, true, false, tabbySupportedCurrency);

        Optional<PaymentOption> splitPayment = paymentOptionsOrder.stream()
                .filter(p -> p.getType().equals(TabbySplitSettings.TYPE)).findFirst();
        assertTrue(splitPayment.isPresent());

        SplitOption splitOption = (SplitOption) splitPayment.get();
        assertTrue(splitOption.getFirstPayment().equals(new BigDecimal(100d)));
        assertTrue(splitOption.getRemainingPayment().equals(new BigDecimal(300d)));

        OrderDTO orderDtoTest = new OrderDTO();
        orderDtoTest.setFinalAmount(new BigDecimal(1990.5d));

        OrderDTO orderDtoWithDiscountTest = new OrderDTO();
        orderDtoWithDiscountTest.setFinalAmount(new BigDecimal(2000.4d));

        Discount discountTest = new Discount();
        discountTest.discountValue = new BigDecimal(1000.3d);

        orderDtoWithDiscountTest.setDiscount(discountTest);

        //with Discount
        when(cartService.getOrderDTOWithParams(eq(1L), any())).thenReturn(Optional.of(orderDtoWithDiscountTest));

        List<PaymentOption> paymentOptionsWithDiscount = defaultOrderService.getPaymentOptions(null,
                1L, "", false, false, true, false, tabbySupportedCurrency);

        assertTrue(paymentOptionsWithDiscount.size() > 0);

        splitPayment = paymentOptionsWithDiscount.stream()
                .filter(p -> p.getType().equals(TabbySplitSettings.TYPE)).findFirst();
        assertTrue(splitPayment.isPresent());

        splitOption = (SplitOption) splitPayment.get();
        assertTrue(splitOption.getFirstPayment().equals(new BigDecimal(5d)));
        assertTrue(splitOption.getRemainingPayment().equals(new BigDecimal(15d)));

        //without Discount
        when(cartService.getOrderDTOWithParams(eq(2L), any())).thenReturn(Optional.of(orderDtoTest));
        assertTrue(defaultOrderService.getPaymentOptions(null, 2L, "",
                false, false, true, false, null).size() > 0);
        List<PaymentOption> paymentOptions = defaultOrderService.getPaymentOptions(null, 2L,
                "", false, false, true, false, tabbySupportedCurrency);

        splitPayment = paymentOptions.stream()
                .filter(p -> p.getType().equals(TabbySplitSettings.TYPE)).findFirst();
        assertTrue(splitPayment.isPresent());

        splitOption = (SplitOption) splitPayment.get();
        assertTrue(splitOption.getFirstPayment().equals(new BigDecimal(10d)));
        assertTrue(splitOption.getRemainingPayment().equals(new BigDecimal(30d)));
    }

    @WithMockUser(authorities = {"ROLE_USER"})
    @Transactional
    @Test
    public void testGetYandexPayPaymentOption() {

        when(configParamService.getValueAsDoubleCached(configParamService.CONFIG_PARAM_YANDEX_PLUS_POINTS_PRODUCT_MAX_AMOUNT))
                .thenReturn(150000d);
        when(configParamService.getValueAsInteger(configParamService.CONFIG_PARAM_YANDEX_PLUS_POINTS_ORDER_MAX_POINTS_COUNT))
                .thenReturn(10000);
        when(configParamService.getValueAsDoubleCached(configParamService.CONFIG_PARAM_YANDEX_PLUS_POINTS_PERCENT))
                .thenReturn(5d);
        when(configParamService.getValueAsBoolean(eq(configParamService.CONFIG_PARAM_YANDEX_PLUS_POINTS_CALC_FOR_CONCIERGE_PRODUCTS), anyBoolean()))
                .thenReturn(true);

        // попытка получить способы оплат по созданному заказу с превышением макс. суммы
        Order testOrder1 = getOrderWithBuyerAndSeller(new BigDecimal(200000));
        orderRepository.save(testOrder1);

        List<PaymentOption> paymentOptionsOrder = defaultOrderService
                .getPaymentOptions(testOrder1.getId(), null, null, true, false,
                        false, false, null);

        Optional<PaymentOption> yandexPayPaymentOpt = paymentOptionsOrder.stream()
                .filter(p -> p.getType().equals(YandexPayOptionProvider.TYPE))
                .findFirst();
        assertTrue(yandexPayPaymentOpt.isPresent());
        assertTrue(yandexPayPaymentOpt.get() instanceof YandexPayOption);

        YandexPayOption yandexPayOption = (YandexPayOption) yandexPayPaymentOpt.get();
        assertNull(yandexPayOption.getYandexPlus());

        // получение способов оплат по созданному заказу с укладывающейся в максимум суммой
        Order testOrder2 = getOrderWithBuyerAndSeller(new BigDecimal(100000));
        orderRepository.save(testOrder2);

        paymentOptionsOrder = defaultOrderService
                .getPaymentOptions(testOrder2.getId(), null, null, true, false,
                        false, false, null);

        yandexPayPaymentOpt = paymentOptionsOrder.stream()
                .filter(p -> p.getType().equals(YandexPayOptionProvider.TYPE))
                .findFirst();
        assertTrue(yandexPayPaymentOpt.isPresent());
        assertTrue(yandexPayPaymentOpt.get() instanceof YandexPayOption);

        yandexPayOption = (YandexPayOption) yandexPayPaymentOpt.get();
        assertNotNull(yandexPayOption.getYandexPlus());
        assertEquals(5000, yandexPayOption.getYandexPlus().getPoints());


        // попытка получить способы оплат по корзине с превышением макс. суммы
        OrderDTO orderDtoWithDiscountTest = new OrderDTO();
        orderDtoWithDiscountTest.setFinalAmount(new BigDecimal("200000"));

        when(cartService.getOrderDTOWithParams(eq(1L), any())).thenReturn(Optional.of(orderDtoWithDiscountTest));

        paymentOptionsOrder = defaultOrderService
                .getPaymentOptions(null, 1L, "", true,
                        false, false, false, null);

        yandexPayPaymentOpt = paymentOptionsOrder.stream()
                .filter(p -> p.getType().equals(YandexPayOptionProvider.TYPE))
                .findFirst();
        assertTrue(yandexPayPaymentOpt.isPresent());
        assertTrue(yandexPayPaymentOpt.get() instanceof YandexPayOption);

        yandexPayOption = (YandexPayOption) yandexPayPaymentOpt.get();
        assertNull(yandexPayOption.getYandexPlus());

        // получение способов оплат по корзине с укладывающейся в максимум суммой
        orderDtoWithDiscountTest = new OrderDTO();
        orderDtoWithDiscountTest.setFinalAmount(new BigDecimal("100000"));

        when(cartService.getOrderDTOWithParams(eq(1L), any())).thenReturn(Optional.of(orderDtoWithDiscountTest));

        paymentOptionsOrder = defaultOrderService
                .getPaymentOptions(null, 1L, "", true,
                        false, false, false, null);

        yandexPayPaymentOpt = paymentOptionsOrder.stream()
                .filter(p -> p.getType().equals(YandexPayOptionProvider.TYPE))
                .findFirst();
        assertTrue(yandexPayPaymentOpt.isPresent());
        assertTrue(yandexPayPaymentOpt.get() instanceof YandexPayOption);

        yandexPayOption = (YandexPayOption) yandexPayPaymentOpt.get();
        assertNotNull(yandexPayOption.getYandexPlus());
        assertEquals(5000, yandexPayOption.getYandexPlus().getPoints());

        // получение способов оплат по созданному заказу со списанием бонусов
        Order testOrder3 = getOrderWithBuyerAndSeller(new BigDecimal(10000));

        UserBonusesAccount userBonusesAccount = new UserBonusesAccount();
        userBonusesAccount.setBonusesAccountId(UUID.randomUUID().toString());
        when(userBonusesAccountRepository.findByUserId(any(Long.class)))
                .thenReturn(Optional.of(userBonusesAccount));
        setUserBonusesBalance(4000, 0);

        orderRepository.save(testOrder3);

        paymentOptionsOrder = defaultOrderService
                .getPaymentOptions(testOrder3.getId(), null, null, true, false,
                        false, true, null);

        yandexPayPaymentOpt = paymentOptionsOrder.stream()
                .filter(p -> p.getType().equals(YandexPayOptionProvider.TYPE))
                .findFirst();
        assertTrue(yandexPayPaymentOpt.isPresent());
        assertTrue(yandexPayPaymentOpt.get() instanceof YandexPayOption);

        yandexPayOption = (YandexPayOption) yandexPayPaymentOpt.get();
        assertNotNull(yandexPayOption.getYandexPlus());
        assertEquals(300, yandexPayOption.getYandexPlus().getPoints());
    }


    @TestConfiguration
    static class MockClockConfiguration {
        @Bean("testClock")
        @Primary
        Clock testClock() {
            return Clock.fixed(
                    LocalDate.of(CLOCK_YEAR, CLOCK_MONTH, CLOCK_DAY).atStartOfDay().toInstant(ZoneOffset.UTC),
                    ZoneId.of(ZoneOffset.UTC.getId()));
        }
    }

    @TestConfiguration
    static class BonusesServiceTestConfig {
        @Bean
        @Primary
        public BonusesControllerAPI testBonusesControllerAPI() {
            return new BonusesControllerApiStub();
        }
    }
}
