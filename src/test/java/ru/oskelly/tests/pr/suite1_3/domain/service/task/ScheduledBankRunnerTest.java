package ru.oskelly.tests.pr.suite1_3.domain.service.task;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockserver.client.MockServerClient;
import org.mockserver.integration.ClientAndServer;
import org.mockserver.model.Header;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.bankaccount.BankBalanceHistoryRepository;
import su.reddot.domain.dao.bankaccount.BankPaymentRepository;
import su.reddot.domain.dao.notification.NotificationRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.order.OrderStateChangeRepository;
import su.reddot.domain.dao.product.ProductItemRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.agentreport.AgentReport;
import su.reddot.domain.model.agentreport.AgentReportState;
import su.reddot.domain.model.banktransaction.BankBalanceHistory;
import su.reddot.domain.model.banktransaction.BankPayment;
import su.reddot.domain.model.banktransaction.TransactionState;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.notification.order.OrderNotification;
import su.reddot.domain.model.notification.order.SaleCompletedNotification;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.order.OrderPositionState;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.agentreport.AgentReportService;
import su.reddot.domain.service.counterparty.CounterpartyService;
import su.reddot.domain.service.dto.BankMoneyTransferDTO;
import su.reddot.domain.service.dto.BankPaymentDTO;
import su.reddot.domain.service.dto.order.AgentReportParams;
import su.reddot.domain.service.notification.NotificationService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.StubAndTcbBankService;
import su.reddot.infrastructure.bank.impl.tcb.TcbConfiguration;
import su.reddot.infrastructure.bank.impl.tcb.response.GetBalanceResponseApiV1;
import su.reddot.infrastructure.bank.impl.tcb.response.GetOrderStateResponse;
import su.reddot.infrastructure.bank.impl.tcb.response.GetStatementABSResponse;
import su.reddot.infrastructure.bank.impl.tcb.response.RegisterOrderToExternalAccountResponse;
import su.reddot.infrastructure.bank.impl.tcb.type.ErrorInfo;
import su.reddot.infrastructure.bank.impl.tcb.type.OrderInfo;
import su.reddot.infrastructure.bank.jobs.ScheduledBankRunner;
import su.reddot.infrastructure.logistic.DeliveryState;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockserver.integration.ClientAndServer.startClientAndServer;
import static org.mockserver.model.HttpRequest.request;
import static org.mockserver.model.HttpResponse.response;
import static org.mockserver.model.HttpStatusCode.OK_200;
import static su.reddot.domain.model.order.OrderState.COMPLETED;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class ScheduledBankRunnerTest extends AbstractSpringTest {
    public static final BigDecimal TRANSFERED_SUM = new BigDecimal(7000000);
    public static final String HOST = "localhost";
    public static final int PORT = 8181;

    @Value("${bank-account.payment-version}")
    private String paymentVersion;

    @Autowired
    private ScheduledBankRunner scheduledBankRunner;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private ProductItemRepository productItemRepository;
    @Autowired
    private CounterpartyService counterpartyService;
    @Autowired
    private UserService userService;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private ProductRepository productRepository;
    @Autowired
    private BankBalanceHistoryRepository bankBalanceHistoryRepository;
    @Autowired
    private BankPaymentRepository bankPaymentRepository;
    @Autowired
    private NotificationRepository<Notification> notificationRepository;
    @Autowired
    private OrderStateChangeRepository orderStateChangeRepository;
    @Autowired
    private TcbConfiguration prop;
    @Autowired
    private AgentReportService agentReportService;

    private ClientAndServer mockServer;


    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user2-email}")
    private String sellerEmail;

    private final ObjectMapper mapper = new ObjectMapper();
    private final Long realSellerId = 23L;

    private static Order order;
    private static List<Product> productsForOrders;

    @BeforeEach
    public void init() throws JsonProcessingException {
        mockServer = startClientAndServer(PORT);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _00_init_order() {
        order = createOrder();
        order.setState(OrderState.HOLD_COMPLETED);
        order.setPaymentVersion(StubAndTcbBankService.SCHEMA);
        order.setDeliveryState(DeliveryState.DELIVERED_TO_BUYER);
        order.setEffectiveAmount(order.getAmount());
        order.setFinishedForBuyer(true);
        order.setPaymentVersion(paymentVersion);
        orderRepository.saveAndFlush(order);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_transferMoneyByAcquirer() throws IOException {
        Optional<BankBalanceHistory> bankBalanceHistory = bankBalanceHistoryRepository.findFirstByOrderByCreateTimeDesc();
        BigDecimal lastBalance = bankBalanceHistory
                .map(BankBalanceHistory::getBalance)
                .orElse(new BigDecimal(BigInteger.ZERO));

        GetBalanceResponseApiV1 getBalanceResponseApiV1 = new GetBalanceResponseApiV1();
        getBalanceResponseApiV1.setAmount(lastBalance.add(TRANSFERED_SUM));
        new MockServerClient(HOST, PORT)
                .when(
                        request()
                                .withMethod("POST")
                                .withPath("/api/v1/banking/account/balance")
                )
                .respond(
                        response()
                                .withStatusCode(OK_200.code())
                                .withHeaders(
                                        new Header("Content-Type", "application/json"))
                                .withBody(mapper.writeValueAsString(getBalanceResponseApiV1))
                );

        GetStatementABSResponse.Transaction transaction = new GetStatementABSResponse.Transaction();
        transaction.setAccountCredit(prop.getAccountNumber());
        transaction.setTotalAmount(TRANSFERED_SUM);
        GetStatementABSResponse getStatementABSResponse = new GetStatementABSResponse();
        getStatementABSResponse.setTransactions(Arrays.asList(transaction));
        new MockServerClient(HOST, PORT)
                .when(
                        request()
                                .withMethod("POST")
                                .withPath("/api/v1/getstatementABS")
                )
                .respond(
                        response()
                                .withStatusCode(OK_200.code())
                                .withHeaders(
                                        new Header("Content-Type", "application/json"))
                                .withBody(mapper.writeValueAsString(getStatementABSResponse))
                );

        commitTransaction();
        BankMoneyTransferDTO bankMoneyTransferDTO = scheduledBankRunner.checkTransferMoneyByAcquirer(paymentVersion);
        //На выполнение таска требуется время
        TestUtils.sleep(1);
        assertNotNull(bankMoneyTransferDTO);
        assertEquals(TRANSFERED_SUM, bankMoneyTransferDTO.getAmount());
        assertTrue(bankMoneyTransferDTO.getOrdersWithMoneyTransferredIds().contains(order.getId()));
        assertNotNull(bankMoneyTransferDTO.getPeriodStart());
        assertNotNull(bankMoneyTransferDTO.getPeriodEnd());

        startNewTransaction();
        order = orderRepository.findById(order.getId()).get();
        assertNotNull(order.getBankMoneyTransfer());
        assertNotNull(order.getBankMoneyTransfer().getPeriodStart());
        assertNotNull(order.getBankMoneyTransfer().getPeriodEnd());
        assertEquals(TRANSFERED_SUM, order.getBankMoneyTransfer().getAmount());
        assertEquals(order.getBankMoneyTransfer().getPeriodStart(), bankMoneyTransferDTO.getPeriodStart());
        assertEquals(order.getBankMoneyTransfer().getPeriodEnd(), bankMoneyTransferDTO.getPeriodEnd());
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _02_tranferMoneyToSellersStep() throws IOException {
        Optional<BankBalanceHistory> bankBalanceHistory = bankBalanceHistoryRepository.findFirstByOrderByCreateTimeDesc();
        BigDecimal lastBalance = bankBalanceHistory.isPresent() ?
                bankBalanceHistory.get().getBalance() :
                new BigDecimal(BigInteger.ZERO);

        GetBalanceResponseApiV1 getBalanceResponseApiV1 = new GetBalanceResponseApiV1();
        getBalanceResponseApiV1.setAmount(lastBalance);
        new MockServerClient(HOST, PORT)
                .when(
                        request()
                                .withMethod("POST")
                                .withPath("/api/v1/banking/account/balance")
                )
                .respond(
                        response()
                                .withStatusCode(OK_200.code())
                                .withHeaders(
                                        new Header("Content-Type", "application/json"))
                                .withBody(mapper.writeValueAsString(getBalanceResponseApiV1))
                );

        RegisterOrderToExternalAccountResponse registerOrderToExternalAccountResponse = new RegisterOrderToExternalAccountResponse();
        registerOrderToExternalAccountResponse.setOrderNumber(new Long(********));
        new MockServerClient(HOST, PORT)
                .when(
                        request()
                                .withMethod("POST")
                                .withPath("/api/v1/account/external/credit")
                )
                .respond(
                        response()
                                .withStatusCode(OK_200.code())
                                .withHeaders(
                                        new Header("Content-Type", "application/json"))
                                .withBody(mapper.writeValueAsString(registerOrderToExternalAccountResponse))
                );

        order = orderRepository.findById(order.getId()).get();
        agentReportService.sendToSeller(order, new AgentReportParams(order.getId(), Boolean.FALSE));
        commitAndStartNewTransaction();
        order = orderRepository.findById(order.getId()).get();
        assertNotNull(order.getAgentReport());
        agentReportService.confirm(order.getAgentReport(), Collections.emptyList(), new AgentReportParams(order.getId(), Boolean.FALSE));
        commitAndStartNewTransaction();
        TestUtils.sleep(3);
        order = orderRepository.findById(order.getId()).get();
        AgentReport agentReport = order.getAgentReport();

        // Проверка 1 этапа согласно алгоритму безопастной сделки когда AgentReportState.PAYMENT_START
        assertSame(AgentReportState.PAYMENT_START, agentReport.getState());
        assertNotNull(agentReport.getConfirmedTime());
        assertNotNull(agentReport.getStateTime());

        agentReportService.preparePaymentForAgentReports();
        commitAndStartNewTransaction();
        TestUtils.sleep(3);

        // Проверка 2 этапа согласно алгоритму безопастной сделки когда AgentReportState.PAYMENT_PREPARED
        order = orderRepository.findById(order.getId()).get();
        agentReport = order.getAgentReport();
        BankPayment bankPayment = agentReport.getLastBankPayment();

        assertSame(AgentReportState.PAYMENT_PREPARED, agentReport.getState());
        assertNotNull(bankPayment.getUuid());
        assertNotNull(bankPayment.getCreateTime());
        assertSame(TransactionState.PREPARED, bankPayment.getState());
        assertEquals(1, agentReport.getBankPayments().stream()
                .filter(bp -> TransactionState.PREPARED == bp.getState())
                .count());
        assertNotNull(bankPayment.getAmount());
        commitTransaction();

        initMockGetOrderStateResponse(null, new ErrorInfo());
        scheduledBankRunner.transferMoneyToSellers();
        TestUtils.sleep(5);

        // Проверка 3 этапа согласно алгоритму безопастной сделки когда AgentReportState.PAYMENT_INPROGRESS
        startNewTransaction();
        order = orderRepository.findById(order.getId()).get();
        agentReport = order.getAgentReport();
        bankPayment = agentReport.getLastBankPayment();

        assertSame(AgentReportState.PAYMENT_INPROGRESS, agentReport.getState());
        assertSame(OrderState.MONEY_PAYMENT_WAIT, order.getState());
        assertSame(TransactionState.INPROGRESS, bankPayment.getState());
        assertEquals(1, agentReport.getBankPayments().stream()
                .filter(bp -> TransactionState.INPROGRESS == bp.getState())
                .count());
        assertNotNull(bankPayment.getBank());
        assertNotNull(bankPayment.getBankTransactionId());

    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _03_checkTranferMoneyToSellersAndCompletedStep() throws IOException {
        Optional<BankBalanceHistory> bankBalanceHistory = bankBalanceHistoryRepository.findFirstByOrderByCreateTimeDesc();
        BigDecimal lastBalance = bankBalanceHistory.isPresent() ?
                bankBalanceHistory.get().getBalance() :
                new BigDecimal(BigInteger.ZERO);

        GetBalanceResponseApiV1 getBalanceResponseApiV1 = new GetBalanceResponseApiV1();
        getBalanceResponseApiV1.setAmount(lastBalance);
        new MockServerClient(HOST, PORT)
                .when(
                        request()
                                .withMethod("POST")
                                .withPath("/api/v1/banking/account/balance")
                )
                .respond(
                        response()
                                .withStatusCode(OK_200.code())
                                .withHeaders(
                                        new Header("Content-Type", "application/json"))
                                .withBody(mapper.writeValueAsString(getBalanceResponseApiV1))
                );

        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setState(0);
        initMockGetOrderStateResponse(orderInfo, null);
        List<BankPaymentDTO> bankPaymentDTOs = scheduledBankRunner.checkTransferMoneyToSeller();
        TestUtils.sleep(5);

        // Проверка 4 этапа согласно алгоритму безопастной сделки когда AgentReportState.PAYMENT_INPROGRESS
        commitAndStartNewTransaction();
        order = orderRepository.findById(order.getId()).get();
        AgentReport agentReport = order.getAgentReport();
        BankPayment bankPayment = agentReport.getLastBankPayment();
        assertTrue(bankPaymentDTOs != null && !bankPaymentDTOs.isEmpty());
        final BankPayment finalBankPayment = bankPayment;
        assertTrue(bankPaymentDTOs.stream()
                .filter(bpDTO -> bpDTO.getOrderId().equals(order.getId()) && bpDTO.getPaymentId().equals(finalBankPayment.getId()))
                .count() > 0);
        final BankPayment finalBankPayment1 = bankPayment;
        BankPaymentDTO bankPaymentDTO = bankPaymentDTOs.stream()
                .filter(bpDTO -> bpDTO.getOrderId().equals(order.getId()) && bpDTO.getPaymentId().equals(finalBankPayment1.getId()))
                .collect(Collectors.toList())
                .get(0);
        assertNotNull(bankPaymentDTO);

        Order savedOrder = orderRepository.findById(order.getId()).get();
        AgentReport savedAgentReport = order.getAgentReport();
        BankPayment savedBankPayment = bankPaymentRepository.findById(bankPayment.getId()).get();

        assertNotNull(savedOrder);
        assertNotNull(savedBankPayment);
        assertSame(AgentReportState.PAYMENT_COMPLETED, savedAgentReport.getState());
        assertSame(COMPLETED, savedOrder.getState());
        assertTrue(savedOrder.getFinishedForSeller());
        assertSame(TransactionState.DONE, savedBankPayment.getState());
        commitAndStartNewTransaction();

        order = orderRepository.findById(order.getId()).get();
        //проверяем, что создается уведомление для продавца
        Notification notification = assertNotificationCreated(order.getId(), order.getSellerUser().getId(), SaleCompletedNotification.class, false, false, 3, true);
        notificationRepository.delete(notification);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _04_cleanup() {
        if(productsForOrders != null){
            for(Product product : productsForOrders){
                product.setSeller(userService.getUserById(realSellerId).orElse(null));
                productRepository.saveAndFlush(product);
            }
        }
        orderStateChangeRepository.deleteAll();
        if(order != null) orderRepository.delete(order);
    }

    @AfterEach
    public void fin() {
        mockServer.stop();
    }

    private Order createOrder(){
        Order order = new Order();
        order.setBuyer(getBuyer());
        order.setUuid(UUID.randomUUID());
        order.setState(OrderState.CREATED);
        List<Product> products = getProductsForOrders();
        BigDecimal orderAmount = new BigDecimal(0);
        for(int i = 0; i < 3; i++){
            Product product = products.get(i);
            ProductItem productItem = productItemRepository.findAllByProduct(product).get(0);
            OrderPosition orderPosition = new OrderPosition();
            orderPosition.setState(OrderPositionState.VERIFICATION_OK);
            orderPosition.setIsEffective(true);
            orderPosition.setParticipatesInPayment(true);
            orderPosition.setStateTime(LocalDateTime.now());
            orderPosition.setProductItem(productItem);
            BigDecimal amount = product.getCurrentPrice();
            orderAmount = orderAmount.add(amount);
            orderPosition.setAmount(amount);
            orderPosition.setItemSaleAmount(orderPosition.getAmount());
            orderPosition.setCommission(new BigDecimal(0.15));
            order.addPosition(orderPosition);
        }
        order.setSellerCounterparty(counterpartyService.findById(51L));
        order.setAmount(orderAmount);
        orderRepository.saveAndFlush(order);
        return order;
    }

    private User getBuyer(){
        return userService.getUserByEmail(buyerEmail);
    }

    private List<Product> getProductsForOrders(){
        if(productsForOrders == null) {
            productsForOrders = new ArrayList<>();
            List<Product> products = new ArrayList<>(productRepository.findProductsBySellerIdAndProductState(realSellerId, ProductState.PUBLISHED));
            for (int i = 0; i < 3; i++) {
                Product product = products.get(i);
                product.setSeller(getSeller());
                productRepository.saveAndFlush(product);
                productsForOrders.add(product);
            }
        }
        return productsForOrders;
    }

    private User getSeller(){
        return userService.getUserByEmail(sellerEmail);
    }

    private OrderNotification assertNotificationCreated(Long orderId, Long userId, Class<? extends OrderNotification> clazz, boolean needAction, boolean actionCompleted, int minutesPeriod, boolean forSeller){
        Order ord = orderRepository.getOne(orderId);
        TestUtils.sleep(3);//Создание уведомления занимает некоторое время
        List<Notification> notifications = notificationService.getRawNotifications(100, forSeller? ord.getSellerUser() : ord.getBuyer());
        OrderNotification notification = null;
        for(Notification n : notifications){
            if(n.getClass() != clazz) continue;

            OrderNotification on = (OrderNotification) n;
            Order o = on.getOrder();
            User u = n.getUser();
            User targetUser = n.getTargetUser().orElse(null);
            if (o == null || targetUser == null) continue;
            if (o.getId().equals(ord.getId()) && u.getId().equals(userId) && targetUser.getId().equals(userId)) {
                //found!
                notification = on;
                break;
            }
        }
        assertNotNull(notification);
        //Если действие выполнено, то уведомление становится прочитанным
        if(needAction && actionCompleted)
            assertTrue(notification.isRead());
        else
            assertFalse(notification.isRead());
        assertEquals(needAction, notification.isNeedAction());
        assertEquals(actionCompleted, notification.isActionCompleted());
        assertTrue(ZonedDateTime.now().minusMinutes(minutesPeriod).isBefore(notification.getCreateTime()));
        return notification;
    }

    private void initMockGetOrderStateResponse(OrderInfo orderInfo, ErrorInfo errorInfo) throws JsonProcessingException {
        GetOrderStateResponse getOrderStateResponse = new GetOrderStateResponse();
        getOrderStateResponse.setOrderInfo(orderInfo);
        getOrderStateResponse.setErrorInfo(errorInfo);
        MockServerClient mockServerClient = new MockServerClient(HOST, PORT);
        mockServerClient
                .when(
                        request()
                                .withMethod("POST")
                                .withPath("/api/v1/order/state")
                )
                .respond(
                        response()
                                .withStatusCode(OK_200.code())
                                .withHeaders(
                                        new Header("Content-Type", "application/json"))
                                .withBody(mapper.writeValueAsString(getOrderStateResponse))
                );
    }

}
