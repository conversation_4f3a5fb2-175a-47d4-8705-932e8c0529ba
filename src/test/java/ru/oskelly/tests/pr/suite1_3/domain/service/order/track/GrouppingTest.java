package ru.oskelly.tests.pr.suite1_3.domain.service.order.track;


import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import su.reddot.domain.service.order.track.expertise.ExpertiseState;
import su.reddot.domain.service.order.track.expertise.OrderProcessingExpertiseMapper;

public class GrouppingTest extends OrderMapperBaseTest {
	private static final String SHOULD_SHOW_SEPARATELY = "/mocked-orders/groupping/needtoshowseparately";
	private static final String NEED_TO_GROUP = "/mocked-orders/groupping/needtogroup";

	@Test
	public void shouldShowSeparatelyForBuyer() {
		expectExpertiseStageForAllSnippets(SHOULD_SHOW_SEPARATELY).expect(extendedExpertise -> {
			OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
				extendedExpertise, Object::toString, (positionId) -> true, BUYER_CONTEXT
			);
			ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
			Assertions.assertThat(expertiseState.needToShowItemsSeparately()).isTrue();
		});
	}

	@Test
	public void shouldGroupForBuyer() {
		expectExpertiseStageForAllSnippets(NEED_TO_GROUP).expect(extendedExpertise -> {
			OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
				extendedExpertise, Object::toString, (positionId) -> true, BUYER_CONTEXT
			);
			ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
			Assertions.assertThat(expertiseState.needToShowItemsSeparately()).isFalse();
		});
	}

	@Test
	public void shouldShowSeparatelyForSeller() {
		expectExpertiseStageForAllSnippets(SHOULD_SHOW_SEPARATELY).expect(extendedExpertise -> {
			OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
				extendedExpertise, Object::toString, (positionId) -> true, SELLER_CONTEXT
			);
			ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
			Assertions.assertThat(expertiseState.needToShowItemsSeparately()).isTrue();
		});
	}

	@Test
	public void shouldGroupForSeller() {
		expectExpertiseStageForAllSnippets(NEED_TO_GROUP).expect(extendedExpertise -> {
			OrderProcessingExpertiseMapper orderProcessingExpertiseMapper = new OrderProcessingExpertiseMapper(
				extendedExpertise, Object::toString, (positionId) -> true, SELLER_CONTEXT
			);
			ExpertiseState expertiseState = orderProcessingExpertiseMapper.getExpertiseState();
			Assertions.assertThat(expertiseState.needToShowItemsSeparately()).isFalse();
		});
	}
}