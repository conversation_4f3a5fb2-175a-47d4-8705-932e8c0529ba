package ru.oskelly.tests.pr.suite1_3.domain.service.order.track;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import su.reddot.domain.service.dto.order.track.OrderStageDTO;
import su.reddot.domain.service.dto.order.track.OrderStageDTO.ProgressState;
import su.reddot.domain.service.dto.order.track.OrderStageDTO.SuccessState;
import su.reddot.domain.service.dto.order.track.PositionDetailsDTO;

import static org.assertj.core.api.Assertions.assertThat;

@Slf4j
public class OrderTrackServiceOskellyExpertiseItemDestroyedTest extends OrderTrackServiceOskellyExpertiseBaseTest {

    public static final String DIFFERENTORDERS_CONTEXT = "differentorders";

    @Test
    public void destroyedItem() {
        String opSnippet = "1120853-destroyed-single";
        OrderStageDTO expertiseStage = expectExpertiseStage(
                SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
        );
        assertThat(expertiseStage.getDescription())
                .isEqualTo("st.FINISHED.FAILED.DESTROYED.description.Seller.singular");
        assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
        assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);

        expertiseStage = expectExpertiseStage(
                BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
        );
        assertThat(expertiseStage.getDescription())
                .isEqualTo("st.FINISHED.FAILED.DESTROYED.description.Buyer.singular");
        assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
        assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);
    }

    @Test
    public void destroyedItems() {
        String opSnippet = "1120853-destroyed-plural";
        OrderStageDTO expertiseStage = expectExpertiseStage(
                SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
        );
        assertThat(expertiseStage.getDescription())
                .isEqualTo("st.FINISHED.FAILED.DESTROYED.description.Seller.plural");
        assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
        assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);

        expertiseStage = expectExpertiseStage(
                BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
        );
        assertThat(expertiseStage.getDescription())
                .isEqualTo("st.FINISHED.FAILED.DESTROYED.description.Buyer.plural");
        assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
        assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);
    }

    @Test
    public void destroyedItemWithFakeItem() {
        String opSnippet = "1120853-destroyed-and-fake-single";
        OrderStageDTO expertiseStage = expectExpertiseStage(
                SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
        );

        String code = expertiseStage.getPositions().stream()
                .map(PositionDetailsDTO::getText)
                .filter(it -> it.contains("DESTROYED"))
                .findFirst().orElse("");
        assertThat(code).isEqualTo("st.FINISHED.FAILED.DESTROYED.position.Seller.singular[1526751 brand1526751]");
        assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
        assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);

        expertiseStage = expectExpertiseStage(
                BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
        );
        code = expertiseStage.getPositions().stream()
                .map(PositionDetailsDTO::getText)
                .filter(it -> it.contains("DESTROYED"))
                .findFirst().orElse("");
        assertThat(code).isEqualTo("st.FINISHED.FAILED.DESTROYED.position.Buyer.singular[1526751 brand1526751]");
        assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
        assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);
    }

    @Test
    public void destroyedItemsWithFakeItems() {
        String opSnippet = "1120853-destroyed-and-fake-plural";
        OrderStageDTO expertiseStage = expectExpertiseStage(
                SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
        );

        String code = expertiseStage.getPositions().stream()
                .map(PositionDetailsDTO::getText)
                .filter(it -> it.contains("DESTROYED"))
                .findFirst().orElse("");
        assertThat(code).isEqualTo("st.FINISHED.FAILED.DESTROYED.position.Seller.plural[1526751 brand1526751, 1526752 brand1526752]");
        assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
        assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);

        expertiseStage = expectExpertiseStage(
                BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
        );
        code = expertiseStage.getPositions().stream()
                .map(PositionDetailsDTO::getText)
                .filter(it -> it.contains("DESTROYED"))
                .findFirst().orElse("");
        assertThat(code).isEqualTo("st.FINISHED.FAILED.DESTROYED.position.Buyer.plural[1526751 brand1526751, 1526752 brand1526752]");
        assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
        assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.FAILED);
    }

    @Test
    public void destroyedItemWithOriginalItem() {
        String opSnippet = "1120853-destroyed-and-original-single";
        OrderStageDTO expertiseStage = expectExpertiseStage(
                SELLER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
        );

        String code = expertiseStage.getPositions().stream()
                .map(PositionDetailsDTO::getText)
                .filter(it -> it.contains("DESTROYED"))
                .findFirst().orElse("");
        assertThat(code).isEqualTo("st.FINISHED.FAILED.DESTROYED.position.Seller.singular[1526751 brand1526751]");
        assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
        assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);

        expertiseStage = expectExpertiseStage(
                BUYER_CONTEXT, DIFFERENTORDERS_CONTEXT, opSnippet
        );
        code = expertiseStage.getPositions().stream()
                .map(PositionDetailsDTO::getText)
                .filter(it -> it.contains("DESTROYED"))
                .findFirst().orElse("");
        assertThat(code).isEqualTo("st.FINISHED.FAILED.DESTROYED.position.Buyer.singular[1526751 brand1526751]");
        assertThat(expertiseStage.getProgressState()).isEqualTo(ProgressState.COMPLETE);
        assertThat(expertiseStage.getSuccessState()).isEqualTo(SuccessState.PARTIALLY_SUCCEEDED);
    }
}
