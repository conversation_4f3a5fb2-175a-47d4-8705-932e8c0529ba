package ru.oskelly.tests.pr.suite1_3.domain.service.order.track;

import lombok.NonNull;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mockito;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceResolvable;
import org.springframework.context.NoSuchMessageException;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import su.reddot.domain.model.Brand;
import su.reddot.domain.model.expertise.Expertise;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.order.OrderPositionState;
import su.reddot.domain.model.order.OrderSource;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.service.dto.order.track.OrderStageDTO;
import su.reddot.domain.service.dto.order.track.OrderTrackDTO;
import su.reddot.domain.service.dto.order.track.PositionDetailsDTO;
import su.reddot.domain.service.estimation.EventDateEstimationService;
import su.reddot.domain.service.order.impl.OrderRequestContext;
import su.reddot.domain.service.order.track.expertise.OskellyExpertiseOrderStageProcessor;
import su.reddot.domain.service.orderPosition.OrderPositionService;
import su.reddot.domain.service.product.ProductService;
import su.reddot.oskelly.orderprocessing.internal.web.client.OrderMobileApi;
import su.reddot.oskelly.orderprocessing.internal.web.dto.IntegrationMobileOrderExpertiseDTO;
import su.reddot.oskelly.orderprocessing.internal.web.dto.IntegrationMobileOrderItemDTO;

import java.nio.file.Paths;
import java.time.Clock;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;

@Slf4j
public abstract class OrderTrackServiceOskellyExpertiseBaseTest extends OrderProcessorExpertiseMockedBaseTest {

    private static final String BASE_MESSAGE_CODE_PART = "service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.";
    private static final long TEST_ORDER_ID = 234L;


    private static final int CLOCK_YEAR = 2020;
    private static final int CLOCK_MONTH = 12;
    private static final int CLOCK_DAY = 30;
    private static final Clock clock = Clock.fixed(
        LocalDate.of(CLOCK_YEAR, CLOCK_MONTH, CLOCK_DAY).atStartOfDay().toInstant(ZoneOffset.UTC),
        ZoneId.of(ZoneOffset.UTC.getId()));

    protected CodesAwareMessageSource messageSource = new CodesAwareMessageSource();

    private final OrderMobileApi mobileApi = Mockito.mock(OrderMobileApi.class);
    private final ProductService productService = Mockito.mock(ProductService.class);
    private final OrderPositionService orderPositionService = Mockito.mock(OrderPositionService.class);
    private final EventDateEstimationService eventDateEstimationService = Mockito.mock(EventDateEstimationService.class);

    @BeforeEach
    public void init() {
        messageSource.clearAllUsedMessageCodes();
    }

    @AfterEach
    public void postChecks() {
        Map<String, Integer> allUsedMessageCodesWithArgsCount = messageSource.getAllUsedMessageCodesWithArgsCount();
        ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
        messageSource.addBasenames("messages");
        messageSource.setFallbackToSystemLocale(false);
        Locale ruLocale = Locale.forLanguageTag("ru-RU");
        Locale enLocale = Locale.forLanguageTag("en-US");
        Locale.setDefault(ruLocale);
        allUsedMessageCodesWithArgsCount.forEach((code, argsCount) -> {
            String ruMessage = messageSource.getMessage(code, null, ruLocale);
            String enMessage = messageSource.getMessage(code, null, enLocale);
            Assertions.assertThat(enMessage)
                .overridingErrorMessage("seems there is no message for en locale.\n\tcode=%s", code)
                .isNotEqualTo(ruMessage);
            int expectedArgsCount = extractArgsNumber(ruMessage);
            Assertions.assertThat(expectedArgsCount)
                .overridingErrorMessage(
                    "args count are not equals.\n\tcode=%s\n\texpected argsCount=%d\n\tactual argsCount=%d",
                    code,
                    expectedArgsCount,
                    argsCount
                )
                .isEqualTo(argsCount);
        });
    }

    private int extractArgsNumber(String message) {
        int argsCount = 0;
        while (message.contains("{" + argsCount + "}")) {
            argsCount++;
        }
        return argsCount;
    }

    protected void expectAllPositionsTexts(OrderStageDTO expertiseStage, String... positionTexts) {
        Assertions.assertThat(expertiseStage.getPositions()).isNotNull();
        Assertions.assertThat(expertiseStage.getPositions()).hasSize(positionTexts.length);
        for (int i = 0; i < positionTexts.length; i++) {
            log.debug("checking position {}", i);
            String positionText = expertiseStage.getPositions().get(i).getText();
            Assertions.assertThat(Arrays.asList(positionTexts)).contains(positionText);
        }
    }

    protected void expect_Description_and_InProgress(
        OrderRequestContext userContext,
        String orderProcessingContext,
        String orderProcessingSnippet,
        OrderStageDTO.SuccessState successState,
        String description
    ) {
        OrderTrackDTO orderTrack = getExpertiseOrderTrackDTO(
            userContext, loadSnippetByStartName(orderProcessingContext, orderProcessingSnippet)
        );
        OrderStageDTO expertiseStage = expectExpertiseStageInOrderTrack(orderTrack);

        Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(OrderStageDTO.ProgressState.IN_PROGRESS);
        Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(successState);
        Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(description);
        Assertions.assertThat(expertiseStage.getUpdatedAt()).isEqualTo(OffsetDateTime.now(clock).plusMinutes(5));
        Assertions.assertThat(expertiseStage.getPositions()).isNull();
    }

    protected void expect_Description_and_Complete(
        OrderRequestContext userContext,
        String orderProcessingContext,
        String orderProcessingSnippet,
        OrderStageDTO.SuccessState successState,
        String description
    ) {
        OrderTrackDTO orderTrack = getExpertiseOrderTrackDTO(
            userContext, loadSnippetByStartName(orderProcessingContext, orderProcessingSnippet)
        );
        OrderStageDTO expertiseStage = expectExpertiseStageInOrderTrack(orderTrack);

        Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(OrderStageDTO.ProgressState.COMPLETE);
        Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(successState);
        Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(description);
        Assertions.assertThat(expertiseStage.getUpdatedAt()).isEqualTo(OffsetDateTime.now(clock).plusMinutes(5));
        Assertions.assertThat(expertiseStage.getPositions()).isNull();
    }

    protected void expect_Positions_and_InProgress(
        OrderRequestContext userContext,
        String orderProcessingContext,
        String orderProcessingSnippet,
        OrderStageDTO.SuccessState successState,
        List<PositionDetailsDTO> positions
    ) {
        OrderTrackDTO orderTrack = getExpertiseOrderTrackDTO(
            userContext, loadSnippetByStartName(orderProcessingContext, orderProcessingSnippet)
        );
        OrderStageDTO expertiseStage = expectExpertiseStageInOrderTrack(orderTrack);

        Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(OrderStageDTO.ProgressState.IN_PROGRESS);
        Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(successState);
        Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(null);
        Assertions.assertThat(expertiseStage.getUpdatedAt()).isEqualTo(OffsetDateTime.now(clock).plusMinutes(5));
        expectPositions(positions, expertiseStage);
    }

    protected void expect_Positions_and_Complete(
        OrderRequestContext userContext,
        String orderProcessingContext,
        String orderProcessingSnippet,
        OrderStageDTO.SuccessState successState,
        List<PositionDetailsDTO> positions
    ) {
        OrderTrackDTO orderTrack = getExpertiseOrderTrackDTO(
            userContext, loadSnippetByStartName(orderProcessingContext, orderProcessingSnippet)
        );
        OrderStageDTO expertiseStage = expectExpertiseStageInOrderTrack(orderTrack);

        Assertions.assertThat(expertiseStage.getProgressState()).isEqualTo(OrderStageDTO.ProgressState.COMPLETE);
        Assertions.assertThat(expertiseStage.getSuccessState()).isEqualTo(successState);
        Assertions.assertThat(expertiseStage.getDescription()).isEqualTo(null);
        Assertions.assertThat(expertiseStage.getUpdatedAt()).isEqualTo(OffsetDateTime.now(clock).plusMinutes(5));
        expectPositions(positions, expertiseStage);
    }

    protected OrderStageDTO expectExpertiseStage(
        OrderRequestContext userContext,
        String orderProcessingContext,
        String orderProcessingSnippet
    ) {
        return expectExpertiseStage(userContext, orderProcessingContext, orderProcessingSnippet, null);
    }

    protected OrderStageDTO expectExpertiseStage(
            OrderRequestContext userContext,
            String orderProcessingContext,
            String orderProcessingSnippet,
            OrderSource orderSource
    ) {
        OrderTrackDTO orderTrack = getExpertiseOrderTrackDTO(
                userContext, loadSnippetByStartName(orderProcessingContext, orderProcessingSnippet),
                orderSource
        );
        return expectExpertiseStageInOrderTrack(orderTrack);
    }

    protected OrderStageDTO expectExpertiseStageExact(
        OrderRequestContext userContext,
        String orderProcessingContext,
        String orderProcessingSnippet
    ) {
        OrderTrackDTO orderTrack = getExpertiseOrderTrackDTO(
            userContext, loadSnippetByName(Paths.get(orderProcessingContext, orderProcessingSnippet).toString())
        );
        return expectExpertiseStageInOrderTrack(orderTrack);
    }

    protected ExpectConsumerFactory<OrderStageDTO> expectExpertiseStageForAllSnippets(
            OrderRequestContext userContext,
            String orderProcessingContext
    ) {
        return expectExpertiseStageForAllSnippets(userContext, orderProcessingContext, "", null);
    }


    protected ExpectConsumerFactory<OrderStageDTO> expectExpertiseStageForAllSnippets(
            OrderRequestContext userContext,
            String orderProcessingContext,
            OrderSource orderSource
    ) {
        return expectExpertiseStageForAllSnippets(userContext, orderProcessingContext, "", orderSource);
    }

    protected ExpectConsumerFactory<OrderStageDTO> expectExpertiseStageForAllSnippets(
        OrderRequestContext userContext,
        String orderProcessingContext,
        String snippetStartWith
    ) {
        return expectExpertiseStageForAllSnippets(userContext, orderProcessingContext, snippetStartWith, null);
    }

    protected ExpectConsumerFactory<OrderStageDTO> expectExpertiseStageForAllSnippets(
            OrderRequestContext userContext,
            String orderProcessingContext,
            String snippetStartWith,
            OrderSource orderSource
    ) {
        return consumer -> {
            List<String> allSnippets = findAllSnippets(orderProcessingContext, snippetStartWith);

            Assertions.assertThat(allSnippets).isNotEmpty();
            allSnippets.forEach(orderProcessingSnippet -> {
                OrderTrackDTO orderTrack = getExpertiseOrderTrackDTO(
                        userContext, loadSnippetByName(
                                Paths.get(orderProcessingContext, orderProcessingSnippet).toString()
                        ),
                        orderSource
                );
                OrderStageDTO orderStage = expectExpertiseStageInOrderTrack(orderTrack);
                consumer.accept(orderStage);
            });
        };
    }


    private void expectPositions(List<PositionDetailsDTO> expectedPositions, OrderStageDTO expertiseStage) {
        Assertions.assertThat(expertiseStage.getPositions()).isNotNull();
        List<PositionDetailsDTO> trackPositions = expertiseStage.getPositions();
        Assertions.assertThat(trackPositions.size()).isEqualTo(expectedPositions.size());
    }

    private OrderStageDTO expectExpertiseStageInOrderTrack(OrderTrackDTO orderTrack) {
        Assertions.assertThat(orderTrack).isNotNull();
        Assertions.assertThat(orderTrack.getOrderStages()).hasSize(1);
        OrderStageDTO expertiseStage = orderTrack.getOrderStages().get(0);
        Assertions.assertThat(expertiseStage.getType()).isEqualTo(OrderStageDTO.Type.EXPERTISE);
        return expertiseStage;
    }

    private OrderTrackDTO getExpertiseOrderTrackDTO(
        OrderRequestContext context, IntegrationMobileOrderExpertiseDTO orderProcessingExpertise) {
        return getExpertiseOrderTrackDTO(context, orderProcessingExpertise, null);
    }

    private OrderTrackDTO getExpertiseOrderTrackDTO(
            OrderRequestContext context,
            IntegrationMobileOrderExpertiseDTO orderProcessingExpertise,
            OrderSource orderSource) {
        Mockito
                .when(mobileApi.getMobileOrderExpertise(any()))
                .thenReturn(
                        orderProcessingExpertise
                );
        OrderTrackDTO orderTrack = new OrderTrackDTO();
        OskellyExpertiseOrderStageProcessor processor = mockOrderStageProcessor(
                context,
                orderTrack,
                orderProcessingExpertise,
                orderSource
        );
        orderTrack.addOrderStage(
                processor.createOrderStage()
        );
        return orderTrack;
    }

    private OskellyExpertiseOrderStageProcessor mockOrderStageProcessor(
        OrderRequestContext context,
        OrderTrackDTO orderTrack,
        IntegrationMobileOrderExpertiseDTO orderProcessingExpertise,
        OrderSource orderSource
    ) {
        Mockito.when(orderPositionService.getById(anyLong()))
            .thenAnswer(invocationOnMock -> {
                Long positionId = invocationOnMock.getArgument(0);
                Product product = new Product();
                Brand brand = new Brand();
                brand.setName("brand" + positionId.toString());
                product.setBrand(brand);
                product.setId(positionId);
                ProductItem productItem = new ProductItem();
                productItem.setProduct(product);
                OrderPosition orderPosition = new OrderPosition();
                if (positionId <= 9999999 && positionId >= 9999900) {
                    orderPosition.setState(OrderPositionState.SALE_REJECTED);
                } else {
                    orderPosition.setState(OrderPositionState.SALE_CONFIRMED);
                    orderPosition.setConfirmedTime(ZonedDateTime.now(clock));
                }
                orderPosition.setProductItem(productItem);
                return Optional.of(orderPosition);
            });
        Mockito.when(productService.getProductDisplayName(any(Product.class)))
            .thenAnswer(invocationOnMock -> {
                Product product = invocationOnMock.getArgument(0);
                return product.getId().toString();
            });

        return new OskellyExpertiseOrderStageProcessor(
            new MessageSourceAccessor(messageSource),
            mockedOrder(orderProcessingExpertise.getItems(), orderSource),
            orderTrack,
            context,
            orderProcessingExpertise,
            orderPositionService,
            productService,
            eventDateEstimationService
        );
    }

    @NonNull
    @SneakyThrows
    private Order mockedOrder(List<IntegrationMobileOrderItemDTO> items) {
        return mockedOrder(items, null);
    }

    @NonNull
    @SneakyThrows
    private Order mockedOrder(List<IntegrationMobileOrderItemDTO> items, OrderSource orderSource) {
        List<Expertise> expertiseList = new ArrayList<>();
        Expertise expertise1 = new Expertise().setCreateTime(ZonedDateTime.now(clock).plusMinutes(5));
        expertiseList.add(expertise1);

        Order order = new Order();
        order.setId(TEST_ORDER_ID);
        order.setExpertises(expertiseList);
        order.setChangeTime(ZonedDateTime.now(clock));
        order.setOrderSource(orderSource);

        items.forEach(item -> {
            OrderPosition orderPosition = new OrderPosition();
            orderPosition.setId(item.getOrderPositionId());
            order.addPosition(orderPosition);
        });

        return order;
    }

    protected static class CodesAwareMessageSource implements MessageSource {
        private Map<String, Integer> codes = new HashMap<>();

        public void clearAllUsedMessageCodes() {
            codes.clear();
        }

        public Map<String, Integer> getAllUsedMessageCodesWithArgsCount() {
            return codes;
        }

        @Override
        public String getMessage(String code, Object[] args, String defaultMessage, Locale locale) {
            return getMessageWithArgs(code, args);
        }

        @Override
        public String getMessage(String code, Object[] args, Locale locale) throws NoSuchMessageException {
            return getMessageWithArgs(code, args);
        }

        @Override
        public String getMessage(MessageSourceResolvable resolvable, Locale locale) throws NoSuchMessageException {
            return getMessageWithArgs(resolvable.getCodes()[0], resolvable.getArguments());
        }

        private String getMessageWithArgs(String code, Object[] args) {
            codes.put(code, args.length);
            if (args.length == 0) {
                return cutCode(code);
            } else {
                return cutCode(code) + Arrays.stream(args)
                    .map(String::valueOf)
                    .collect(Collectors.joining("|", "[", "]"));
            }
        }

        private String cutCode(String code) {
            return code.substring(BASE_MESSAGE_CODE_PART.length());
        }
    }

    @FunctionalInterface
    public interface ExpectConsumerFactory<T> {
        void expect(Consumer<T> consumer);
    }
}
