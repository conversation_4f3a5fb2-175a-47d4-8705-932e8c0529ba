package ru.oskelly.tests.pr.suite1_3.domain.service.bonuses;

import lombok.Builder;
import lombok.Getter;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceResolvable;
import org.springframework.context.NoSuchMessageException;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.util.ReflectionTestUtils;
import ru.oskelly.tests.pr.common.bonuses.BonusesControllerApiStub;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.bonuses.UserBonusesAccountRepository;
import su.reddot.domain.exception.OskellyException;
import su.reddot.domain.model.bonuses.UserBonusesAccount;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.service.bonuses.BonusesService;
import su.reddot.domain.service.bonuses.DefaultBonusesService;
import su.reddot.domain.service.dto.UserDTO;
import su.reddot.domain.service.dto.bonuses.BonusesAmountDTO;
import su.reddot.domain.service.dto.bonuses.BonusesBalanceDTO;
import su.reddot.domain.service.dto.bonuses.BonusesInfoDTO;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.dto.order.OrderPositionDTO;
import su.reddot.domain.service.order.Discount;
import su.reddot.domain.service.loyalty.LoyaltyService;
import su.reddot.infrastructure.util.CallInTransaction;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;
import static java.math.BigDecimal.ONE;
import static java.math.BigDecimal.ZERO;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;

/**
 *    Unit-тест. Предназначен для тестирования метода распределения бонусов на основе позиций заказа.
 *    ---------------------------------------------------------------------------------------------------
 *    S - сумма заказа
 *    L - лимит на списание
 *    B - сгораемые бонусы
 *    M - НЕсгораемые бонусы (деньги)
 *    List of opAmount - список стоимостей позиций заказа, где opAmount - стоимость позиции
 *    List of opProhibitedFlag - список стоимостей позиций заказа, где opProhibitedFlag - признак того, что товар запрещен ко покупке за сгораемые бонусы
 *    Q - сумма на разрешенные товары
 *    P - сумма на запрещенные товары и Q + P = S
 *    R - сколько осталось списать, после первого шага, т.е. распределения сгораемых бонусов
 *    T - вся остаточная стоимость товаров, после распределения сгораемых бонусов. Считается как T = P + Q(ост).
 *    ---------------------------------------------------------------------------------------------------
 *    Аргументы для проверки арифметики распределения. Ошибки здесь не проверяются.
 *    Считаем, что B + M >= L, S >= L, Q + P = S, распределение между B + M не противоречит распределению Q + P,
 *    т.е. баланса B + M достаточно для корректного списания Q + P. Точно достаточно M для покрытия P и остатков от Q, не покрытых B.
 *    Так же учитываем, что T >= R, т.е. не оказалось вдруг что на 2-м шаге надо распределить больше чем в принципе возможно.
 *    ---------------------------------------------------------------------------------------------------
 *    Что проверяем в распределении с помощью метода testDistribution:
 *    0. Позиций в заказе 5 (не меняем в рамках теста)
 *    1. B >= L || B < L
 *    2. MIN(B, L) < Q || MIN(B, L) >= Q. B достаточно для покрытия Q (B > Q) и B НЕдостаточно для покрытия Q (B < Q) (общее условие достаточности баланса с учетом НЕсгораемых бонусов выполняется, без ошибок)
 *    3. R = T || R < T
 *    4. Q "вариант1" || Q "вариант2", дав разных варианта для проверки арифметики
 *    Итого: 16 кейсов.
 *    ---------------------------------------------------------------------------------------------------
 *    Дополнительно разными методами проверям разные корнер-кейсы.
 *    R > T (P + Q(ост)) и (bonusesAmount > value1) Невероятные условия. Проверить фактически невозможно.
 *    ---------------------------------------------------------------------------------------------------
 *
 */
@ExtendWith(MockitoExtension.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class DefaultBonusesServiceDistributionTest {

    MessageSourceAccessor messageSourceAccessor = new MessageSourceAccessorStub();

    private BonusesService bonusesService;
    private LoyaltyService loyaltyService;
    private BonusesControllerApiStub bonusesControllerApiStub;

    private static final Long BUYER_ID = 2L;

    private BonusesService.BonusesDistributionMultipliers multipliers;

    @BeforeEach
    public void init() throws NoSuchFieldException {
        multipliers = new BonusesService.BonusesDistributionMultipliers(ZERO, ZERO, ZERO);
        bonusesControllerApiStub = new BonusesControllerApiStub();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(5);
        executor.setThreadNamePrefix("TestBonusesPool-");
        executor.initialize();
        UserBonusesAccountRepository userBonusesAccountRepository = mock(UserBonusesAccountRepository.class);
        lenient().when(userBonusesAccountRepository.findByUserId(any())).thenReturn(Optional.of(new UserBonusesAccount()));
        bonusesService = new DefaultBonusesService(bonusesControllerApiStub, messageSourceAccessor, executor, null, userBonusesAccountRepository, new CallInTransaction(), null, null, null, null, null, null);
        loyaltyService = mock(LoyaltyService.class);
        setWithdrawPercent(100);
        setTransferPercent(5);
        setTransferBonusesLimit(500);
        ReflectionTestUtils.setField(bonusesService, "socketTimeout", Duration.ofSeconds(30));
        ReflectionTestUtils.setField(bonusesService, "self", bonusesService);
        ReflectionTestUtils.setField(bonusesService, "notLowerRest", BigDecimal.ONE);
        ReflectionTestUtils.setField(bonusesService, "loyaltyService", loyaltyService);
    }

    @ParameterizedTest
    @MethodSource({"argumentsArithmetic"})
    public void testBonusesDistribution(
            BonusesArgument arg
    ) {
        assertNotNull(arg.getOpAmounts());
        assertNotNull(arg.getOpProhibitedFlags());
        assertNotNull(arg.getExpectedOpBonusesAmount());
        assertNotNull(arg.getExpectedOpMoneyAmount());

        BigDecimal startCleanAmount = c(arg.getS());
        BigDecimal withdrawLimit = c(arg.getL());
        BigDecimal bonuses = c(arg.getB());
        BigDecimal money = c(arg.getM());
        BigDecimal qInit = c(arg.getQ());

        List<BigDecimal> opAmounts = c2(arg.getOpAmounts());
        List<Boolean> opProhibitedFlags = b(arg.getOpProhibitedFlags());

        BigDecimal expectedBonusesTotal = c(arg.getExpectedBonusesTotal());
        BigDecimal expectedMoneyTotal = c(arg.getExpectedMoneyTotal());
        List<BigDecimal> expOpBonusesAmount = c2(arg.getExpectedOpBonusesAmount());
        List<BigDecimal> expOpMoneyAmount = c2(arg.getExpectedOpMoneyAmount());

        //Проверка корректности тестовых данных
        assertNotNull(bonuses);
        assertNotNull(money);
        assertNotNull(qInit);
        //B + M >= L
        boolean balanceMinusWithdrawLimitSignumGreaterOrEqualsZero = bonuses.add(money).subtract(withdrawLimit).signum() >= 0;
        assertTrue(balanceMinusWithdrawLimitSignumGreaterOrEqualsZero);
        //S >= L
        assertTrue(startCleanAmount.subtract(withdrawLimit).signum() >= 0);
        //Q + P = S
        assertNotNull(opAmounts);
        assertNotNull(opProhibitedFlags);
        assertEquals(opProhibitedFlags.size(), opAmounts.size());
        BigDecimal p = ZERO;
        BigDecimal q = ZERO;
        for (int i = 0; i < opAmounts.size(); i++) {
            if (opProhibitedFlags.get(i)) {
                p = p.add(opAmounts.get(i));
            } else {
                q = q.add(opAmounts.get(i));
            }
        }
        assertEquals(0, qInit.subtract(q).signum());
        assertEquals(0, startCleanAmount.subtract(p.add(q)).signum());
        //распределение между B + M не противоречит распределению Q + P (упрощенная проверка)
        if (withdrawLimit.subtract(p.add(q)).signum() >= 0) {
            //В случае если L равен балансу
            assertTrue((bonuses.add(money)).subtract(q.add(p)).signum() >= 0);
        }
        //В случае если L меньше баланса уже проверено balanceMinusWithdrawLimitSignumGreaterOrEqualsZero

        assertNotNull(bonusesService);
        BonusesAmountDTO amount = new BonusesAmountDTO(bonuses, money, bonuses.add(money));
        BonusesBalanceDTO bonusesBalance = new BonusesBalanceDTO(amount, ZERO);
        List<BonusesService.Position> positions = new ArrayList<>();
        Map<Long, BonusesAmountDTO> expected = new HashMap<>();

        int i = 0;
        for (BigDecimal opAmount: opAmounts) {
            long id = Integer.valueOf(i).longValue();

            positions.add(createPosition(id, id, id, opAmount, opProhibitedFlags.get(i)));

            BonusesAmountDTO resOp = new BonusesAmountDTO(expOpBonusesAmount.get(i), expOpMoneyAmount.get(i), expOpBonusesAmount.get(i).add(expOpMoneyAmount.get(i)));
            expected.put(Integer.valueOf(i).longValue(), resOp);
            i++;
        }

        BonusesService.DistributeBonusesResult result = bonusesService.distributeBonuses("1", startCleanAmount, withdrawLimit, bonusesBalance, positions, false, multipliers);

        assertEquals(0, expectedBonusesTotal.subtract(result.getBonusBonuses()).signum());
        assertEquals(0, expectedMoneyTotal.subtract(result.getMoneyBonuses()).signum());

        for (Map.Entry<Long, BonusesAmountDTO> entry : expected.entrySet()) {
            BonusesAmountDTO resultOp = result.getDistribution().get(entry.getKey());
            assertNotNull(resultOp);
            assertNotNull(resultOp.getBonuses());
            assertNotNull(resultOp.getMoney());
            assertNotNull(resultOp.getTotal());
            assertEquals(0, entry.getValue().getBonuses().subtract(resultOp.getBonuses()).signum());
            assertEquals(0, entry.getValue().getMoney().subtract(resultOp.getMoney()).signum());
            assertEquals(0, entry.getValue().getTotal().subtract(resultOp.getTotal()).signum());
        }
    }

    private static Stream<Arguments> argumentsArithmetic() {
        return Stream.of(
                // B >= L, Min_BL < Q, R = T (= 0), Qв1
                Arguments.of(
                        BonusesArgument.builder()
                                .s(900)
                                .l(600)
                                .b(700)
                                .m(400)
                                .q(650)
                                .opAmounts(Stream.of(200, 100, 150, 200, 250))
                                .opProhibitedFlags(Stream.of(FALSE, FALSE, FALSE, FALSE, TRUE))
                                .expectedBonusesTotal(600)
                                .expectedMoneyTotal(0)
                                .expectedOpBonusesAmount(Stream.of(184, 91, 138, 187, 0))
                                .expectedOpMoneyAmount(Stream.of(0, 0, 0, 0, 0))
                                .build()
                ),
                // B >= L, Min_BL < Q, R = T (= 0), Qв2
                Arguments.of(
                        BonusesArgument.builder()
                                .s(1000)
                                .l(900)
                                .b(1200)
                                .m(0)
                                .q(1000)
                                .opAmounts(Stream.of(200, 200, 200, 200, 200))
                                .opProhibitedFlags(Stream.of(FALSE, FALSE, FALSE, FALSE, FALSE))
                                .expectedBonusesTotal(900)
                                .expectedMoneyTotal(0)
                                .expectedOpBonusesAmount(Stream.of(180, 180, 180, 180, 180))
                                .expectedOpMoneyAmount(Stream.of(0, 0, 0, 0, 0))
                                .build()
                ),
                // B >= L, Min_BL < Q, R < T, Qв1 - Тест неприменим, т.к. при прочих условиях, получается что всегда R = T = 0
                // B >= L, Min_BL < Q, R < T, Qв2  - Тест неприменим, т.к. при прочих условиях, получается что всегда R = T = 0

                // B >= L, Min_BL >= Q, R = T, Qв1 - без разницы, т.к. Min_BL >= Q и всё Q будет закрыто сгораемыми бонусами
                // +
                // B >= L, Min_BL >= Q, R = T, Qв2 - без разницы, т.к. Min_BL >= Q и всё Q будет закрыто сгораемыми бонусами
                Arguments.of(
                        BonusesArgument.builder()
                                .s(905)
                                .l(900)
                                .b(950)
                                .m(400)
                                .q(654)
                                .opAmounts(Stream.of(201, 101, 151, 201, 251))
                                .opProhibitedFlags(Stream.of(FALSE, FALSE, FALSE, FALSE, TRUE))
                                .expectedBonusesTotal(650)
                                .expectedMoneyTotal(250)
                                .expectedOpBonusesAmount(Stream.of(200, 100, 150, 200, 0))
                                .expectedOpMoneyAmount(Stream.of(0, 0, 0, 0, 250))
                                .build()
                ),
                // B >= L, Min_BL >= Q, R < T, Qв1 - без разницы, т.к. Min_BL >= Q и всё Q будет закрыто сгораемыми бонусами
                // +
                // B >= L, Min_BL >= Q, R < T, Qв2 - без разницы, т.к. Min_BL >= Q и всё Q будет закрыто сгораемыми бонусами
                Arguments.of(
                        BonusesArgument.builder()
                                .s(1005)
                                .l(900)
                                .b(950)
                                .m(450)
                                .q(453)
                                .opAmounts(Stream.of(201, 101, 151, 201, 351))
                                .opProhibitedFlags(Stream.of(FALSE, FALSE, FALSE, TRUE, TRUE))
                                .expectedBonusesTotal(450)
                                .expectedMoneyTotal(450)
                                .expectedOpBonusesAmount(Stream.of(200, 100, 150, 0, 0))
                                .expectedOpMoneyAmount(Stream.of(0, 0, 0, 163, 287))
                                .build()
                ),
                // B < L, Min_BL < Q, R = T (= 0), Qв1
                Arguments.of(
                        BonusesArgument.builder()
                                .s(905)
                                .l(900)
                                .b(500)
                                .m(500)
                                .q(654)
                                .opAmounts(Stream.of(201, 101, 151, 201, 251))
                                .opProhibitedFlags(Stream.of(FALSE, FALSE, FALSE, FALSE, TRUE))
                                .expectedBonusesTotal(500)
                                .expectedMoneyTotal(400)
                                .expectedOpBonusesAmount(Stream.of(153, 76, 115, 156, 0))
                                .expectedOpMoneyAmount(Stream.of(47, 24, 35, 44, 250))
                                .build()
                ),
                // B < L, Min_BL < Q, R = T (= 0), Qв2
                Arguments.of(
                        BonusesArgument.builder()
                                .s(1205)
                                .l(900)
                                .b(800)
                                .m(500)
                                .q(1205)
                                .opAmounts(Stream.of(201, 201, 201, 201, 401))
                                .opProhibitedFlags(Stream.of(FALSE, FALSE, FALSE, FALSE, FALSE))
                                .expectedBonusesTotal(800)
                                .expectedMoneyTotal(100)
                                .expectedOpBonusesAmount(Stream.of(132, 132, 132, 132, 272))
                                .expectedOpMoneyAmount(Stream.of(17, 17, 17, 17, 32))
                                .build()
                ),
                // B < L, Min_BL < Q, R < T, Qв1
                Arguments.of(
                        BonusesArgument.builder()
                                .s(1105)
                                .l(900)
                                .b(500)
                                .m(500)
                                .q(654)
                                .opAmounts(Stream.of(201, 101, 151, 201, 451))
                                .opProhibitedFlags(Stream.of(FALSE, FALSE, FALSE, FALSE, TRUE))
                                .expectedBonusesTotal(500)
                                .expectedMoneyTotal(400)
                                .expectedOpBonusesAmount(Stream.of(153, 76, 115, 156, 0))
                                .expectedOpMoneyAmount(Stream.of(31, 16, 23, 29, 301))
                                .build()
                ),
                // B < L, Min_BL < Q, R < T, Qв2
                Arguments.of(
                        BonusesArgument.builder()
                                .s(1005)
                                .l(1000)
                                .b(500)
                                .m(500)
                                .q(804)
                                .opAmounts(Stream.of(201, 201, 201, 201, 201))
                                .opProhibitedFlags(Stream.of(FALSE, FALSE, FALSE, FALSE, TRUE))
                                .expectedBonusesTotal(500)
                                .expectedMoneyTotal(500)
                                .expectedOpBonusesAmount(Stream.of(125, 125, 125, 125, 0))
                                .expectedOpMoneyAmount(Stream.of(75, 75, 75, 75, 200))
                                .build()
                ),
                // B < L, Min_BL >= Q, R = T, Qв1
                Arguments.of(
                        BonusesArgument.builder()
                                .s(905)
                                .l(900)
                                .b(500)
                                .m(500)
                                .q(453)
                                .opAmounts(Stream.of(201, 101, 151, 251, 201))
                                .opProhibitedFlags(Stream.of(FALSE, FALSE, FALSE, TRUE, TRUE))
                                .expectedBonusesTotal(450)
                                .expectedMoneyTotal(450)
                                .expectedOpBonusesAmount(Stream.of(200, 100, 150, 0, 0))
                                .expectedOpMoneyAmount(Stream.of(0, 0, 0, 250, 200))
                                .build()
                ),
                // B < L, Min_BL >= Q, R = T, Qв2
                Arguments.of(
                        BonusesArgument.builder()
                                .s(905)
                                .l(900)
                                .b(350)
                                .m(700)
                                .q(303)
                                .opAmounts(Stream.of(201, 51, 51, 351, 251))
                                .opProhibitedFlags(Stream.of(FALSE, FALSE, FALSE, TRUE, TRUE))
                                .expectedBonusesTotal(300)
                                .expectedMoneyTotal(600)
                                .expectedOpBonusesAmount(Stream.of(200, 50, 50, 0, 0))
                                .expectedOpMoneyAmount(Stream.of(0, 0, 0, 350, 250))
                                .build()
                ),
                // B < L, Min_BL >= Q, R < T, Qв1
                Arguments.of(
                        BonusesArgument.builder()
                                .s(1005)
                                .l(900)
                                .b(600)
                                .m(450)
                                .q(453)
                                .opAmounts(Stream.of(201, 101, 151, 201, 351))
                                .opProhibitedFlags(Stream.of(FALSE, FALSE, FALSE, TRUE, TRUE))
                                .expectedBonusesTotal(450)
                                .expectedMoneyTotal(450)
                                .expectedOpBonusesAmount(Stream.of(200, 100, 150, 0, 0))
                                .expectedOpMoneyAmount(Stream.of(0, 0, 0, 163, 287))
                                .build()
                ),
                // B < L, Min_BL >= Q, R < T, Qв2
                Arguments.of(
                        BonusesArgument.builder()
                                .s(1505)
                                .l(1100)
                                .b(600)
                                .m(800)
                                .q(354)
                                .opAmounts(Stream.of(201, 51, 51, 51, 1151))
                                .opProhibitedFlags(Stream.of(FALSE, FALSE, FALSE, FALSE, TRUE))
                                .expectedBonusesTotal(350)
                                .expectedMoneyTotal(750)
                                .expectedOpBonusesAmount(Stream.of(200, 50, 50, 50, 0))
                                .expectedOpMoneyAmount(Stream.of(0, 0, 0, 0, 750)).build())
        );
    }

    private static List<BigDecimal> c2(Stream<Integer> in) {
        return in.map(BigDecimal::new).collect(Collectors.toList());
    }

    private static List<Boolean> b(Stream<Boolean> in) {
        return in.collect(Collectors.toList());
    }

    @Test
    //B + M < L
    public void testExceptionBalanceIsNotEnough() {
        long orderId = 1L;
        long val1 = 100L;
        long val2 = 10L;
        OskellyException ex = assertThrows(OskellyException.class, () -> passDistributeBonusesArgs(orderId, val1, val1, val2, val2));
        Assertions.assertTrue(ex.getMessage().contains("" + orderId) && ex.getMessage().contains("" + val1));
    }

    @Test
    // S < L
    public void testExceptionLimitIncorrect() {
        long orderId = 1L;
        long val1 = 100L;
        long val2 = 10L;
        OskellyException ex = assertThrows(OskellyException.class, () -> passDistributeBonusesArgs(orderId, val1, val1 * 2, val2, val2));
        Assertions.assertTrue(ex.getMessage().contains("" + orderId) && ex.getMessage().contains("" + val1) && ex.getMessage().contains("" + val2));
    }

    @Test
    // B + M не покрывает P + Q
    public void testExceptionMoneyIsNotEnough() {
        long orderId = 1L;
        long val1 = 100L;
        long val2 = 55L;
        List<BonusesService.Position> positions = new ArrayList<>();

        positions.add(createPosition(1L, 1L, 1L, new BigDecimal(val1), true));
        positions.add(createPosition(2L, 2L, 2L, new BigDecimal(val1), true));

        OskellyException ex = assertThrows(OskellyException.class, () -> passDistributeBonusesArgs(orderId, val1, val1 - positions.size(), val2, val2, positions));
        Assertions.assertTrue(ex.getMessage().contains("" + orderId) && ex.getMessage().contains("" + (val2 + val2)));
    }

    @Test
    public void testExceptionConsiderConfigWithdrawPercentIncorrect() {
        setWithdrawPercent(30);
        long orderId = 1L;
        long val1 = 100L;
        long val2 = 100L;
        OskellyException ex = assertThrows(OskellyException.class, () -> passDistributeBonusesArgs(orderId, val1 * 3, val1, val2, val2));
        Assertions.assertTrue(ex.getMessage().contains("" + orderId) && ex.getMessage().contains("" + val1) && ex.getMessage().contains("" + val2));
    }

    @Test
    // Распределение на 1 товар ценой 1 двух (2) бонусов
    public void testExtraSmallDistribution() {
        long orderId = 1L;
        long val1 = 1L;
        //распределение одного сгораемого бонуса на обычный товар
        List<BonusesService.Position> positions = new ArrayList<>();
        positions.add(createPosition(1L, 1L, 1L, new BigDecimal(1L), false));
        OskellyException ex = assertThrows(OskellyException.class, () -> passDistributeBonusesArgs(orderId, 1L, val1, 1L, 0L, positions));
        Assertions.assertTrue(ex.getMessage().contains("" + orderId) && ex.getMessage().contains("" + val1));

        //распределение одного НЕсгораемого бонуса на запрещенный товар
        List<BonusesService.Position> positions2 = new ArrayList<>();
        positions2.add(createPosition(1L, 1L, 1L, new BigDecimal(val1), true));
        ex = assertThrows(OskellyException.class, () -> passDistributeBonusesArgs(orderId, val1, val1, 0L, val1, positions2));
        Assertions.assertTrue(ex.getMessage().contains("" + orderId) && ex.getMessage().contains("" + val1));
    }

    @ParameterizedTest
    @MethodSource({"returnArgumentsArithmetic"})
    public void testBonusesReturnDistribution(
            BonusesReturnArgument arg
    ) {
        assertEquals(
                0,
                arg.getPositionEffectiveAmount()
                        + arg.getEffectiveWithdrawBonuses()
                        + arg.getEffectiveWithdrawMoney()
                        + arg.getExpertiseDefectDiscountPrice()
                        - arg.getWithdrawAmount()
                        - arg.getWithdrawBonuses()
                        - arg.getWithdrawMoney()
        );
        BigDecimal expertiseDefectDiscountPrice = c(arg.getExpertiseDefectDiscountPrice());
        BigDecimal withdrawAmount = c(arg.getWithdrawAmount());
        BigDecimal withdrawBonuses = c(arg.getWithdrawBonuses());
        BigDecimal withdrawMoney = c(arg.getWithdrawMoney());

        BigDecimal positionEffectiveAmount = c(arg.getPositionEffectiveAmount());
        BigDecimal effectiveWithdrawBonuses = c(arg.getEffectiveWithdrawBonuses());
        BigDecimal effectiveWithdrawMoney = c(arg.getEffectiveWithdrawMoney());

        BonusesService.DistributeBonusesReturnResult result = bonusesService.distributeBonusesReturn(
                expertiseDefectDiscountPrice, withdrawAmount, withdrawBonuses, withdrawMoney, false
        );

        assertEquals(0, positionEffectiveAmount.compareTo(result.getPositionEffectiveAmount()));
        assertEquals(0, effectiveWithdrawBonuses.compareTo(result.getEffectiveBonusBonuses()));
        assertEquals(0, effectiveWithdrawMoney.compareTo(result.getEffectiveMoneyBonuses()));
    }

    private static Stream<Arguments> returnArgumentsArithmetic() {
        return Stream.of(
                Arguments.of(
                        BonusesReturnArgument.builder()
                                .expertiseDefectDiscountPrice(1000)
                                .withdrawAmount(500)
                                .withdrawBonuses(600)
                                .withdrawMoney(400)
                                .positionEffectiveAmount(1)
                                .effectiveWithdrawBonuses(499)
                                .effectiveWithdrawMoney(0)
                                .build()
                ),
                Arguments.of(
                        BonusesReturnArgument.builder()
                                .expertiseDefectDiscountPrice(1000)
                                .withdrawAmount(500)
                                .withdrawBonuses(400)
                                .withdrawMoney(600)
                                .positionEffectiveAmount(1)
                                .effectiveWithdrawBonuses(400)
                                .effectiveWithdrawMoney(99)
                                .build()
                ),
                Arguments.of(
                        BonusesReturnArgument.builder()
                                .expertiseDefectDiscountPrice(1000)
                                .withdrawAmount(1100)
                                .withdrawBonuses(250)
                                .withdrawMoney(150)
                                .positionEffectiveAmount(100)
                                .effectiveWithdrawBonuses(250)
                                .effectiveWithdrawMoney(150)
                                .build()
                )
        );
    }

    @ParameterizedTest
    @MethodSource({"argumentsApplyToCartArithmetic"})
    public void testApplyBonusesToCart(
            ApplyToCartArgument arg
    ) {
        int SIZE = 5;
        assertNotNull(arg.getBuyerIsNull());
        assertNotNull(arg.getPromoCodeIsApplied());
        assertNotNull(arg.getWithdrawPercent());
        assertNotNull(arg.getTransferPercent());
        assertNotNull(arg.getTransferBonusesLimit());
        assertNotNull(arg.getBonusesBalance());
        assertNotNull(arg.getMoneyBalance());
        assertNotNull(arg.getOrderAmount());
        assertNotNull(arg.getOpAmounts());
        assertNotNull(arg.getOpProhibitedFlags());
        assertNotNull(arg.getOpAvailableToBuyFlags());
        assertNotNull(arg.getExpectedOpBonusesAmountTransferred());
        assertNotNull(arg.getExpectedOpMoneyAmountTransferred());
        assertNotNull(arg.getExpectedOpBonusesAmountWithdraw());
        assertNotNull(arg.getExpectedOpMoneyAmountWithdraw());

        setWithdrawPercent(arg.getWithdrawPercent());
        setTransferPercent(arg.getTransferPercent());
        setTransferBonusesLimit(arg.getTransferBonusesLimit());

        BigDecimal bonuses = c(arg.getBonusesBalance());
        BigDecimal money = c(arg.getMoneyBalance());

        BigDecimal startCleanAmount = c(arg.getOrderAmount());

        List<BigDecimal> opAmounts = c2(arg.getOpAmounts());
        List<Boolean> opProhibitedFlags = b(arg.getOpProhibitedFlags());
        List<Boolean> opAvailableFlags = b(arg.getOpAvailableToBuyFlags());

        BigDecimal expectedBonusesAmountTransferred = c(arg.getExpectedBonusesAmountTransferred());
        BigDecimal expectedMoneyAmountTransferred = c(arg.getExpectedMoneyAmountTransferred());
        BigDecimal expectedBonusesAmountWithdraw = c(arg.getExpectedBonusesAmountWithdraw());
        BigDecimal expectedMoneyAmountWithdraw = c(arg.getExpectedMoneyAmountWithdraw());

        List<BigDecimal> expOpBonusesAmountTransferred = c2(arg.getExpectedOpBonusesAmountTransferred());
        List<BigDecimal> expOpMoneyAmountTransferred = c2(arg.getExpectedOpMoneyAmountTransferred());
        List<BigDecimal> expOpBonusesAmountWithdraw = c2(arg.getExpectedOpBonusesAmountWithdraw());
        List<BigDecimal> expOpMoneyAmountWithdraw = c2(arg.getExpectedOpMoneyAmountWithdraw());

        assertEquals(SIZE, opAmounts.size());
        assertEquals(SIZE, opProhibitedFlags.size());
        assertEquals(SIZE, opAvailableFlags.size());
        assertEquals(SIZE, expOpBonusesAmountTransferred.size());
        assertEquals(SIZE, expOpMoneyAmountTransferred.size());
        assertEquals(SIZE, expOpBonusesAmountWithdraw.size());
        assertEquals(SIZE, expOpMoneyAmountWithdraw.size());

        assertEquals(0, startCleanAmount.compareTo(opAmounts.stream().reduce(ZERO, BigDecimal::add)));

        assertNotNull(bonusesService);

        bonusesControllerApiStub.setBalance(bonuses, money);

        OrderDTO dto = new OrderDTO();
        dto.setSeller(new UserDTO(1L));
        if (!arg.getBuyerIsNull()) {
            dto.setBuyer(new UserDTO(BUYER_ID));
        }
        if (arg.getPromoCodeIsApplied()) {
            Discount discount = new Discount();
            discount.discountValue = ONE;
            dto.setDiscount(discount);
        }
        dto.setFinalAmount(startCleanAmount);
        dto.setItems(new ArrayList<>());

        for (int i = 0; i < SIZE; i++) {
            long id = Integer.valueOf(i).longValue();

            OrderPositionDTO orderPositionDTO = new OrderPositionDTO();
            orderPositionDTO.setId(id);
            orderPositionDTO.setAmount(opAmounts.get(i));
            orderPositionDTO.setFinalAmount(opAmounts.get(i));
            orderPositionDTO.setBonusesProhibited(opProhibitedFlags.get(i));
            orderPositionDTO.setAvailable(opAvailableFlags.get(i));

            dto.getItems().add(orderPositionDTO);
        }

        bonusesService.applyBonusesToCart(Collections.singletonList(dto), false);

        assertBonusesInfoNotNull(dto.getBonusesInfo());

        assertEquals(0, expectedBonusesAmountTransferred.subtract(dto.getBonusesInfo().getTransferBonusesAmount().getBonuses()).signum());
        assertEquals(0, expectedMoneyAmountTransferred.subtract(dto.getBonusesInfo().getTransferBonusesAmount().getMoney()).signum());
        assertEquals(0, expectedBonusesAmountWithdraw.subtract(dto.getBonusesInfo().getWithdrawBonusesAmount().getBonuses()).signum());
        assertEquals(0, expectedMoneyAmountWithdraw.subtract(dto.getBonusesInfo().getWithdrawBonusesAmount().getMoney()).signum());

        assertEquals(SIZE, dto.getItems().size());

        for (int i = 0; i < SIZE; i++) {
            OrderPositionDTO resOp = dto.getItems().get(i);

            assertBonusesInfoNotNull(resOp.getBonusesInfo());

            assertEquals(0, expOpBonusesAmountTransferred.get(i).subtract(resOp.getBonusesInfo().getTransferBonusesAmount().getBonuses()).signum());
            assertEquals(0, expOpMoneyAmountTransferred.get(i).subtract(resOp.getBonusesInfo().getTransferBonusesAmount().getMoney()).signum());
            assertEquals(0, expOpBonusesAmountWithdraw.get(i).subtract(resOp.getBonusesInfo().getWithdrawBonusesAmount().getBonuses()).signum());
            assertEquals(0, expOpMoneyAmountWithdraw.get(i).subtract(resOp.getBonusesInfo().getWithdrawBonusesAmount().getMoney()).signum());
        }
    }

    private void assertBonusesInfoNotNull(BonusesInfoDTO bonusesInfo) {
        assertNotNull(bonusesInfo);
        assertNotNull(bonusesInfo.getTransferBonusesAmount());
        assertNotNull(bonusesInfo.getTransferBonusesAmount().getBonuses());
        assertNotNull(bonusesInfo.getTransferBonusesAmount().getMoney());
        assertNotNull(bonusesInfo.getTransferBonusesAmount());
        assertNotNull(bonusesInfo.getTransferBonusesAmount().getBonuses());
        assertNotNull(bonusesInfo.getTransferBonusesAmount().getMoney());
    }

    private static Stream<Arguments> argumentsApplyToCartArithmetic() {
        return Stream.concat(
                argumentsApplyToCartArithmeticGeneral(),
                Stream.concat(
                        argumentsApplyToCartArithmeticWithdrawPercent(),
                        Stream.concat(
                                argumentsApplyToCartArithmeticTransferPercent(),
                                argumentsApplyToCartArithmeticTransferBonusesLimit()
                        )
                )
        );
    }

    private static Stream<Arguments> argumentsApplyToCartArithmeticGeneral() {
        final int withdrawPercent = 100;
        final int transferPercent = 5;
        final int transferBonusesLimit = 100000;
        return Stream.of(
                //Пользователь не авторизован, есть разные товары
                Arguments.of(
                        ApplyToCartArgument.builder()
                                .buyerIsNull(true)
                                .promoCodeIsApplied(false)
                                .withdrawPercent(withdrawPercent)
                                .transferPercent(transferPercent)
                                .transferBonusesLimit(transferBonusesLimit)
                                .bonusesBalance(1000)
                                .moneyBalance(500)
                                .orderAmount(500)
                                .opAmounts(Stream.of(100, 100, 100, 100, 100))
                                .opProhibitedFlags(Stream.of(false, false, false, false, true))
                                .opAvailableToBuyFlags(Stream.of(true, true, true, true, true))

                                .expectedOpBonusesAmountTransferred(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpMoneyAmountTransferred(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpBonusesAmountWithdraw(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpMoneyAmountWithdraw(Stream.of(0, 0, 0, 0, 0))
                                .expectedBonusesAmountTransferred(0)
                                .expectedMoneyAmountTransferred(0)
                                .expectedBonusesAmountWithdraw(0)
                                .expectedMoneyAmountWithdraw(0)
                                .build()
                ),
                //Пользователь авторизован, но применил промокод
                Arguments.of(
                        ApplyToCartArgument.builder()
                                .buyerIsNull(false)
                                .promoCodeIsApplied(true)
                                .transferPercent(transferPercent)
                                .transferBonusesLimit(transferBonusesLimit)
                                .withdrawPercent(100)
                                .bonusesBalance(1000)
                                .moneyBalance(500)
                                .orderAmount(500)
                                .opAmounts(Stream.of(100, 100, 100, 100, 100))
                                .opProhibitedFlags(Stream.of(false, false, false, false, true))
                                .opAvailableToBuyFlags(Stream.of(true, true, true, true, true))

                                .expectedOpBonusesAmountTransferred(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpMoneyAmountTransferred(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpBonusesAmountWithdraw(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpMoneyAmountWithdraw(Stream.of(0, 0, 0, 0, 0))
                                .expectedBonusesAmountTransferred(0)
                                .expectedMoneyAmountTransferred(0)
                                .expectedBonusesAmountWithdraw(0)
                                .expectedMoneyAmountWithdraw(0)
                                .build()
                ),
                //Пользователь авторизован, есть разные товары
                Arguments.of(
                        ApplyToCartArgument.builder()
                                .buyerIsNull(false)
                                .promoCodeIsApplied(false)
                                .withdrawPercent(withdrawPercent)
                                .transferPercent(transferPercent)
                                .transferBonusesLimit(transferBonusesLimit)
                                .bonusesBalance(1000)
                                .moneyBalance(500)
                                .orderAmount(500)
                                .opAmounts(Stream.of(100, 100, 100, 100, 100))
                                .opProhibitedFlags(Stream.of(false, false, false, false, true))
                                .opAvailableToBuyFlags(Stream.of(true, true, true, true, true))

                                .expectedOpBonusesAmountTransferred(Stream.of(5, 5, 5, 5, 5))
                                .expectedOpMoneyAmountTransferred(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpBonusesAmountWithdraw(Stream.of(99, 99, 99, 99, 0))
                                .expectedOpMoneyAmountWithdraw(Stream.of(0, 0, 0, 0, 99))
                                .expectedBonusesAmountTransferred(25)
                                .expectedMoneyAmountTransferred(0)
                                .expectedBonusesAmountWithdraw(396)
                                .expectedMoneyAmountWithdraw(99)
                                .build()
                ),
                //Пользователь авторизован, есть разные товары, бюджета недостаточно для полного покрытия
                Arguments.of(
                        ApplyToCartArgument.builder()
                                .buyerIsNull(false)
                                .promoCodeIsApplied(false)
                                .withdrawPercent(withdrawPercent)
                                .transferPercent(transferPercent)
                                .transferBonusesLimit(transferBonusesLimit)
                                .bonusesBalance(100)
                                .moneyBalance(50)
                                .orderAmount(500)
                                .opAmounts(Stream.of(100, 100, 100, 100, 100))
                                .opProhibitedFlags(Stream.of(false, false, false, false, true))
                                .opAvailableToBuyFlags(Stream.of(true, true, true, true, true))

                                .expectedOpBonusesAmountTransferred(Stream.of(5, 5, 5, 5, 5))
                                .expectedOpMoneyAmountTransferred(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpBonusesAmountWithdraw(Stream.of(25, 25, 25, 25, 0))
                                .expectedOpMoneyAmountWithdraw(Stream.of(9, 9, 9, 9, 14))
                                .expectedBonusesAmountTransferred(25)
                                .expectedMoneyAmountTransferred(0)
                                .expectedBonusesAmountWithdraw(100)
                                .expectedMoneyAmountWithdraw(50)
                                .build()
                ),
                //Пользователь авторизован, есть разные товары, бонусного бюджета недостаточно для полного покрытия, денежного бюджета достаточно
                Arguments.of(
                        ApplyToCartArgument.builder()
                                .buyerIsNull(false)
                                .promoCodeIsApplied(false)
                                .withdrawPercent(withdrawPercent)
                                .transferPercent(transferPercent)
                                .transferBonusesLimit(transferBonusesLimit)
                                .bonusesBalance(100)
                                .moneyBalance(500)
                                .orderAmount(500)
                                .opAmounts(Stream.of(100, 100, 100, 100, 100))
                                .opProhibitedFlags(Stream.of(false, false, false, false, true))
                                .opAvailableToBuyFlags(Stream.of(true, true, true, true, true))

                                .expectedOpBonusesAmountTransferred(Stream.of(5, 5, 5, 5, 5))
                                .expectedOpMoneyAmountTransferred(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpBonusesAmountWithdraw(Stream.of(25, 25, 25, 25, 0))
                                .expectedOpMoneyAmountWithdraw(Stream.of(74, 74, 74, 74, 99))
                                .expectedBonusesAmountTransferred(25)
                                .expectedMoneyAmountTransferred(0)
                                .expectedBonusesAmountWithdraw(100)
                                .expectedMoneyAmountWithdraw(395)
                                .build()
                ),
                //Пользователь авторизован, есть товары только с возможностью списать сгораемые бонусы
                Arguments.of(
                        ApplyToCartArgument.builder()
                                .buyerIsNull(false)
                                .promoCodeIsApplied(false)
                                .withdrawPercent(withdrawPercent)
                                .transferPercent(transferPercent)
                                .transferBonusesLimit(transferBonusesLimit)
                                .bonusesBalance(1000)
                                .moneyBalance(500)
                                .orderAmount(500)
                                .opAmounts(Stream.of(100, 100, 100, 100, 100))
                                .opProhibitedFlags(Stream.of(false, false, false, false, false))
                                .opAvailableToBuyFlags(Stream.of(true, true, true, true, true))

                                .expectedOpBonusesAmountTransferred(Stream.of(5, 5, 5, 5, 5))
                                .expectedOpMoneyAmountTransferred(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpBonusesAmountWithdraw(Stream.of(99, 99, 99, 99, 99))
                                .expectedOpMoneyAmountWithdraw(Stream.of(0, 0, 0, 0, 0))
                                .expectedBonusesAmountTransferred(25)
                                .expectedMoneyAmountTransferred(0)
                                .expectedBonusesAmountWithdraw(495)
                                .expectedMoneyAmountWithdraw(0)
                                .build()
                ),
                //Пользователь авторизован, все товары запрещают списание сгораемых баллов
                Arguments.of(
                        ApplyToCartArgument.builder()
                                .buyerIsNull(false)
                                .promoCodeIsApplied(false)
                                .withdrawPercent(withdrawPercent)
                                .transferPercent(transferPercent)
                                .transferBonusesLimit(transferBonusesLimit)
                                .bonusesBalance(1000)
                                .moneyBalance(500)
                                .orderAmount(500)
                                .opAmounts(Stream.of(100, 100, 100, 100, 100))
                                .opProhibitedFlags(Stream.of(true, true, true, true, true))
                                .opAvailableToBuyFlags(Stream.of(true, true, true, true, true))

                                .expectedOpBonusesAmountTransferred(Stream.of(5, 5, 5, 5, 5))
                                .expectedOpMoneyAmountTransferred(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpBonusesAmountWithdraw(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpMoneyAmountWithdraw(Stream.of(99, 99, 99, 99, 99))
                                .expectedBonusesAmountTransferred(25)
                                .expectedMoneyAmountTransferred(0)
                                .expectedBonusesAmountWithdraw(0)
                                .expectedMoneyAmountWithdraw(495)
                                .build()
                ),
                //Пользователь авторизован, все товары запрещают списание сгораемых баллов, денежного баланса недостаточно
                Arguments.of(
                        ApplyToCartArgument.builder()
                                .buyerIsNull(false)
                                .withdrawPercent(withdrawPercent)
                                .transferPercent(transferPercent)
                                .transferBonusesLimit(transferBonusesLimit)
                                .promoCodeIsApplied(false)
                                .bonusesBalance(1000)
                                .moneyBalance(200)
                                .orderAmount(500)
                                .opAmounts(Stream.of(100, 100, 100, 100, 100))
                                .opProhibitedFlags(Stream.of(true, true, true, true, true))
                                .opAvailableToBuyFlags(Stream.of(true, true, true, true, true))

                                .expectedOpBonusesAmountTransferred(Stream.of(5, 5, 5, 5, 5))
                                .expectedOpMoneyAmountTransferred(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpBonusesAmountWithdraw(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpMoneyAmountWithdraw(Stream.of(40, 40, 40, 40, 40))
                                .expectedBonusesAmountTransferred(25)
                                .expectedMoneyAmountTransferred(0)
                                .expectedBonusesAmountWithdraw(0)
                                .expectedMoneyAmountWithdraw(200)
                                .build()
                )
        );
    }

    private static Stream<Arguments> argumentsApplyToCartArithmeticWithdrawPercent() {
        final int transferPercent = 5;
        final int transferBonusesLimit = 100000;
        return Stream.of(
                Arguments.of(
                        ApplyToCartArgument.builder()
                                .buyerIsNull(false)
                                .promoCodeIsApplied(false)
                                .withdrawPercent(10)
                                .transferPercent(transferPercent)
                                .transferBonusesLimit(transferBonusesLimit)
                                .bonusesBalance(1000)
                                .moneyBalance(500)
                                .orderAmount(500)
                                .opAmounts(Stream.of(100, 100, 100, 100, 100))
                                .opProhibitedFlags(Stream.of(false, false, false, false, true))
                                .opAvailableToBuyFlags(Stream.of(true, true, true, true, true))

                                .expectedOpBonusesAmountTransferred(Stream.of(5, 5, 5, 5, 5))
                                .expectedOpMoneyAmountTransferred(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpBonusesAmountWithdraw(Stream.of(10, 10, 10, 10, 0))
                                .expectedOpMoneyAmountWithdraw(Stream.of(0, 0, 0, 0, 10))
                                .expectedBonusesAmountTransferred(25)
                                .expectedMoneyAmountTransferred(0)
                                .expectedBonusesAmountWithdraw(40)
                                .expectedMoneyAmountWithdraw(10)
                                .build()
                ),
                //Пользователь авторизован, есть только разрешенные для списания бонусов товары, и есть ограничение на процент списания
                Arguments.of(
                        ApplyToCartArgument.builder()
                                .buyerIsNull(false)
                                .promoCodeIsApplied(false)
                                .withdrawPercent(10)
                                .transferPercent(transferPercent)
                                .transferBonusesLimit(transferBonusesLimit)
                                .bonusesBalance(1000)
                                .moneyBalance(500)
                                .orderAmount(500)
                                .opAmounts(Stream.of(100, 100, 100, 100, 100))
                                .opProhibitedFlags(Stream.of(false, false, false, false, false))
                                .opAvailableToBuyFlags(Stream.of(true, true, true, true, true))

                                .expectedOpBonusesAmountTransferred(Stream.of(5, 5, 5, 5, 5))
                                .expectedOpMoneyAmountTransferred(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpBonusesAmountWithdraw(Stream.of(10, 10, 10, 10, 10))
                                .expectedOpMoneyAmountWithdraw(Stream.of(0, 0, 0, 0, 0))
                                .expectedBonusesAmountTransferred(25)
                                .expectedMoneyAmountTransferred(0)
                                .expectedBonusesAmountWithdraw(50)
                                .expectedMoneyAmountWithdraw(0)
                                .build()
                ),
                //Пользователь авторизован, есть разные товары, бюджета недостаточно для полного покрытия, процент списания ограничен
                Arguments.of(
                        ApplyToCartArgument.builder()
                                .buyerIsNull(false)
                                .promoCodeIsApplied(false)
                                .withdrawPercent(50)
                                .transferPercent(transferPercent)
                                .transferBonusesLimit(transferBonusesLimit)
                                .bonusesBalance(100)
                                .moneyBalance(50)
                                .orderAmount(500)
                                .opAmounts(Stream.of(100, 100, 100, 100, 100))
                                .opProhibitedFlags(Stream.of(false, false, false, false, true))
                                .opAvailableToBuyFlags(Stream.of(true, true, true, true, true))

                                .expectedOpBonusesAmountTransferred(Stream.of(5, 5, 5, 5, 5))
                                .expectedOpMoneyAmountTransferred(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpBonusesAmountWithdraw(Stream.of(25, 25, 25, 25, 0))
                                .expectedOpMoneyAmountWithdraw(Stream.of(8, 8, 8, 8, 18))
                                .expectedBonusesAmountTransferred(25)
                                .expectedMoneyAmountTransferred(0)
                                .expectedBonusesAmountWithdraw(100)
                                .expectedMoneyAmountWithdraw(50)
                                .build()
                ),
                //Пользователь авторизован, есть разные товары, бонусного бюджета недостаточно для полного покрытия, денежного бюджета достаточно
                Arguments.of(
                        ApplyToCartArgument.builder()
                                .buyerIsNull(false)
                                .promoCodeIsApplied(false)
                                .withdrawPercent(50)
                                .transferPercent(transferPercent)
                                .transferBonusesLimit(transferBonusesLimit)
                                .bonusesBalance(100)
                                .moneyBalance(500)
                                .orderAmount(500)
                                .opAmounts(Stream.of(100, 100, 100, 100, 100))
                                .opProhibitedFlags(Stream.of(false, false, false, false, true))
                                .opAvailableToBuyFlags(Stream.of(true, true, true, true, true))

                                .expectedOpBonusesAmountTransferred(Stream.of(5, 5, 5, 5, 5))
                                .expectedOpMoneyAmountTransferred(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpBonusesAmountWithdraw(Stream.of(25, 25, 25, 25, 0))
                                .expectedOpMoneyAmountWithdraw(Stream.of(25, 25, 25, 25, 50))
                                .expectedBonusesAmountTransferred(25)
                                .expectedMoneyAmountTransferred(0)
                                .expectedBonusesAmountWithdraw(100)
                                .expectedMoneyAmountWithdraw(150)
                                .build()
                ),
                //Пользователь авторизован, есть товары только с возможностью списать сгораемые бонусы
                Arguments.of(
                        ApplyToCartArgument.builder()
                                .buyerIsNull(false)
                                .promoCodeIsApplied(false)
                                .withdrawPercent(50)
                                .transferPercent(transferPercent)
                                .transferBonusesLimit(transferBonusesLimit)
                                .bonusesBalance(1000)
                                .moneyBalance(500)
                                .orderAmount(500)
                                .opAmounts(Stream.of(100, 100, 100, 100, 100))
                                .opProhibitedFlags(Stream.of(false, false, false, false, false))
                                .opAvailableToBuyFlags(Stream.of(true, true, true, true, true))

                                .expectedOpBonusesAmountTransferred(Stream.of(5, 5, 5, 5, 5))
                                .expectedOpMoneyAmountTransferred(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpBonusesAmountWithdraw(Stream.of(50, 50, 50, 50, 50))
                                .expectedOpMoneyAmountWithdraw(Stream.of(0, 0, 0, 0, 0))
                                .expectedBonusesAmountTransferred(25)
                                .expectedMoneyAmountTransferred(0)
                                .expectedBonusesAmountWithdraw(250)
                                .expectedMoneyAmountWithdraw(0)
                                .build()
                ),
                //Пользователь авторизован, все товары запрещают списание сгораемых баллов
                Arguments.of(
                        ApplyToCartArgument.builder()
                                .buyerIsNull(false)
                                .promoCodeIsApplied(false)
                                .withdrawPercent(50)
                                .transferPercent(transferPercent)
                                .transferBonusesLimit(transferBonusesLimit)
                                .bonusesBalance(1000)
                                .moneyBalance(500)
                                .orderAmount(500)
                                .opAmounts(Stream.of(100, 100, 100, 100, 100))
                                .opProhibitedFlags(Stream.of(true, true, true, true, true))
                                .opAvailableToBuyFlags(Stream.of(true, true, true, true, true))

                                .expectedOpBonusesAmountTransferred(Stream.of(5, 5, 5, 5, 5))
                                .expectedOpMoneyAmountTransferred(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpBonusesAmountWithdraw(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpMoneyAmountWithdraw(Stream.of(50, 50, 50, 50, 50))
                                .expectedBonusesAmountTransferred(25)
                                .expectedMoneyAmountTransferred(0)
                                .expectedBonusesAmountWithdraw(0)
                                .expectedMoneyAmountWithdraw(250)
                                .build()
                ),
                //Пользователь авторизован, все товары запрещают списание сгораемых баллов, денежного баланса недостаточно
                Arguments.of(
                        ApplyToCartArgument.builder()
                                .buyerIsNull(false)
                                .promoCodeIsApplied(false)
                                .withdrawPercent(50)
                                .transferPercent(transferPercent)
                                .transferBonusesLimit(transferBonusesLimit)
                                .bonusesBalance(1000)
                                .moneyBalance(200)
                                .orderAmount(500)
                                .opAmounts(Stream.of(100, 100, 100, 100, 100))
                                .opProhibitedFlags(Stream.of(true, true, true, true, true))
                                .opAvailableToBuyFlags(Stream.of(true, true, true, true, true))

                                .expectedOpBonusesAmountTransferred(Stream.of(5, 5, 5, 5, 5))
                                .expectedOpMoneyAmountTransferred(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpBonusesAmountWithdraw(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpMoneyAmountWithdraw(Stream.of(40, 40, 40, 40, 40))
                                .expectedBonusesAmountTransferred(25)
                                .expectedMoneyAmountTransferred(0)
                                .expectedBonusesAmountWithdraw(0)
                                .expectedMoneyAmountWithdraw(200)
                                .build()
                ),
                //Пользователь авторизован, есть разные товары (проданные и запрещенные),
                // бонусного бюджета НЕдостаточно для полного покрытия, денежного бюджета НЕдостаточно
                Arguments.of(
                        ApplyToCartArgument.builder()
                                .buyerIsNull(false)
                                .promoCodeIsApplied(false)
                                .withdrawPercent(50)
                                .transferPercent(transferPercent)
                                .transferBonusesLimit(transferBonusesLimit)
                                .bonusesBalance(50)
                                .moneyBalance(0)
                                .orderAmount(500)
                                .opAmounts(Stream.of(100, 100, 100, 100, 100))
                                .opProhibitedFlags(Stream.of(false, false, false, false, true))
                                .opAvailableToBuyFlags(Stream.of(true, true, false, false, true))

                                .expectedOpBonusesAmountTransferred(Stream.of(8, 8, 0, 0, 9))
                                .expectedOpMoneyAmountTransferred(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpBonusesAmountWithdraw(Stream.of(25, 25, 0, 0, 0))
                                .expectedOpMoneyAmountWithdraw(Stream.of(0, 0, 0, 0, 0))
                                .expectedBonusesAmountTransferred(25)
                                .expectedMoneyAmountTransferred(0)
                                .expectedBonusesAmountWithdraw(50)
                                .expectedMoneyAmountWithdraw(0)
                                .build()
                ),
                //Пользователь авторизован, есть разные товары (проданные и запрещенные),
                // бонусного бюджета НЕдостаточно для полного покрытия, денежного бюджета достаточно
                Arguments.of(
                        ApplyToCartArgument.builder()
                                .buyerIsNull(false)
                                .promoCodeIsApplied(false)
                                .withdrawPercent(50)
                                .transferPercent(transferPercent)
                                .transferBonusesLimit(transferBonusesLimit)
                                .bonusesBalance(50)
                                .moneyBalance(500)
                                .orderAmount(500)
                                .opAmounts(Stream.of(100, 100, 100, 100, 100))
                                .opProhibitedFlags(Stream.of(false, false, false, false, true))
                                .opAvailableToBuyFlags(Stream.of(true, true, false, false, true))

                                .expectedOpBonusesAmountTransferred(Stream.of(8, 8, 0, 0, 9))
                                .expectedOpMoneyAmountTransferred(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpBonusesAmountWithdraw(Stream.of(25, 25, 0, 0, 0))
                                .expectedOpMoneyAmountWithdraw(Stream.of(25, 25, 0, 0, 50))
                                .expectedBonusesAmountTransferred(25)
                                .expectedMoneyAmountTransferred(0)
                                .expectedBonusesAmountWithdraw(50)
                                .expectedMoneyAmountWithdraw(100)
                                .build()
                ),
                //Пользователь авторизован, есть разные товары (проданные и запрещенные),
                // бонусного бюджета достаточно для полного покрытия, денежного бюджета достаточно
                Arguments.of(
                        ApplyToCartArgument.builder()
                                .buyerIsNull(false)
                                .promoCodeIsApplied(false)
                                .withdrawPercent(50)
                                .transferPercent(transferPercent)
                                .transferBonusesLimit(transferBonusesLimit)
                                .bonusesBalance(500)
                                .moneyBalance(500)
                                .orderAmount(500)
                                .opAmounts(Stream.of(100, 100, 100, 100, 100))
                                .opProhibitedFlags(Stream.of(false, false, false, false, true))
                                .opAvailableToBuyFlags(Stream.of(true, true, false, false, true))

                                .expectedOpBonusesAmountTransferred(Stream.of(8, 8, 0, 0, 9))
                                .expectedOpMoneyAmountTransferred(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpBonusesAmountWithdraw(Stream.of(50, 50, 0, 0, 0))
                                .expectedOpMoneyAmountWithdraw(Stream.of(0, 0, 0, 0, 50))
                                .expectedBonusesAmountTransferred(25)
                                .expectedMoneyAmountTransferred(0)
                                .expectedBonusesAmountWithdraw(100)
                                .expectedMoneyAmountWithdraw(50)
                                .build()
                )
        );
    }

    private static Stream<Arguments> argumentsApplyToCartArithmeticTransferPercent() {
        final int withdrawPercent = 10;
        final int transferBonusesLimit = 100000;
        return Stream.of(
                Arguments.of(
                        ApplyToCartArgument.builder()
                                .buyerIsNull(false)
                                .promoCodeIsApplied(false)
                                .withdrawPercent(withdrawPercent)
                                .transferPercent(1)
                                .transferBonusesLimit(transferBonusesLimit)
                                .bonusesBalance(1000)
                                .moneyBalance(500)
                                .orderAmount(500)
                                .opAmounts(Stream.of(100, 100, 100, 100, 100))
                                .opProhibitedFlags(Stream.of(false, false, false, false, false))
                                .opAvailableToBuyFlags(Stream.of(true, true, true, true, true))

                                .expectedOpBonusesAmountTransferred(Stream.of(1, 1, 1, 1, 1))
                                .expectedOpMoneyAmountTransferred(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpBonusesAmountWithdraw(Stream.of(10, 10, 10, 10, 10))
                                .expectedOpMoneyAmountWithdraw(Stream.of(0, 0, 0, 0, 0))
                                .expectedBonusesAmountTransferred(5)
                                .expectedMoneyAmountTransferred(0)
                                .expectedBonusesAmountWithdraw(50)
                                .expectedMoneyAmountWithdraw(0)
                                .build()
                ),
                Arguments.of(
                        ApplyToCartArgument.builder()
                                .buyerIsNull(false)
                                .promoCodeIsApplied(false)
                                .withdrawPercent(withdrawPercent)
                                .transferPercent(10)
                                .transferBonusesLimit(transferBonusesLimit)
                                .bonusesBalance(1000)
                                .moneyBalance(500)
                                .orderAmount(500)
                                .opAmounts(Stream.of(100, 100, 100, 100, 100))
                                .opProhibitedFlags(Stream.of(false, false, false, false, false))
                                .opAvailableToBuyFlags(Stream.of(true, true, true, true, true))

                                .expectedOpBonusesAmountTransferred(Stream.of(10, 10, 10, 10, 10))
                                .expectedOpMoneyAmountTransferred(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpBonusesAmountWithdraw(Stream.of(10, 10, 10, 10, 10))
                                .expectedOpMoneyAmountWithdraw(Stream.of(0, 0, 0, 0, 0))
                                .expectedBonusesAmountTransferred(50)
                                .expectedMoneyAmountTransferred(0)
                                .expectedBonusesAmountWithdraw(50)
                                .expectedMoneyAmountWithdraw(0)
                                .build()
                )
        );
    }

    private static Stream<Arguments> argumentsApplyToCartArithmeticTransferBonusesLimit() {
        final int withdrawPercent = 10;
        final int transferPercent = 5;
        return Stream.of(
                Arguments.of(
                        ApplyToCartArgument.builder()
                                .buyerIsNull(false)
                                .promoCodeIsApplied(false)
                                .withdrawPercent(withdrawPercent)
                                .transferPercent(transferPercent)
                                .transferBonusesLimit(10)
                                .bonusesBalance(1000)
                                .moneyBalance(500)
                                .orderAmount(500)
                                .opAmounts(Stream.of(100, 100, 100, 100, 100))
                                .opProhibitedFlags(Stream.of(false, false, false, false, false))
                                .opAvailableToBuyFlags(Stream.of(true, true, true, true, true))

                                .expectedOpBonusesAmountTransferred(Stream.of(2, 2, 2, 2, 2))
                                .expectedOpMoneyAmountTransferred(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpBonusesAmountWithdraw(Stream.of(10, 10, 10, 10, 10))
                                .expectedOpMoneyAmountWithdraw(Stream.of(0, 0, 0, 0, 0))
                                .expectedBonusesAmountTransferred(10)
                                .expectedMoneyAmountTransferred(0)
                                .expectedBonusesAmountWithdraw(50)
                                .expectedMoneyAmountWithdraw(0)
                                .build()
                ),
                Arguments.of(
                        ApplyToCartArgument.builder()
                                .buyerIsNull(false)
                                .promoCodeIsApplied(false)
                                .withdrawPercent(withdrawPercent)
                                .transferPercent(transferPercent)
                                .transferBonusesLimit(20)
                                .bonusesBalance(1000)
                                .moneyBalance(500)
                                .orderAmount(500)
                                .opAmounts(Stream.of(100, 100, 100, 100, 100))
                                .opProhibitedFlags(Stream.of(false, false, false, false, false))
                                .opAvailableToBuyFlags(Stream.of(true, true, true, true, true))

                                .expectedOpBonusesAmountTransferred(Stream.of(4, 4, 4, 4, 4))
                                .expectedOpMoneyAmountTransferred(Stream.of(0, 0, 0, 0, 0))
                                .expectedOpBonusesAmountWithdraw(Stream.of(10, 10, 10, 10, 10))
                                .expectedOpMoneyAmountWithdraw(Stream.of(0, 0, 0, 0, 0))
                                .expectedBonusesAmountTransferred(20)
                                .expectedMoneyAmountTransferred(0)
                                .expectedBonusesAmountWithdraw(50)
                                .expectedMoneyAmountWithdraw(0)
                                .build()
                )
        );
    }

    private void passDistributeBonusesArgs(Long orderId, Long s, Long l, Long b, Long m) {
        passDistributeBonusesArgs(orderId, s, l, b, m, new ArrayList<>());
    }

    private BonusesService.Position createPosition(Long productId, Long piId, Long opId, BigDecimal amount, boolean isProhibited) {
        Product product = new Product();
        product.setId(productId);
        product.setBonusesProhibitedTime(isProhibited ? LocalDateTime.now() : null);

        ProductItem pi = new ProductItem();
        pi.setId(piId);
        pi.setProduct(product);

        OrderPosition op = new OrderPosition();
        op.setId(opId);
        op.setAmount(amount);
        op.setItemSaleAmount(amount);
        op.setProductItem(pi);

        return BonusesService.Position.fromOrderPosition(op, isProhibited);
    }

    private void setTransferPercent(int percent) {
        ReflectionTestUtils.setField(multipliers, "transferPercent", new BigDecimal(percent));
        lenient().when(loyaltyService.getLoyaltyBonusesDistributionMultipliersCached(any())).thenReturn(multipliers);
    }

    private void setTransferBonusesLimit(int limit) {
        ReflectionTestUtils.setField(multipliers, "transferBonusesLimit", new BigDecimal(limit));
        lenient().when(loyaltyService.getLoyaltyBonusesDistributionMultipliersCached(any())).thenReturn(multipliers);
    }

    private void setWithdrawPercent(int percent) {
        ReflectionTestUtils.setField(multipliers, "withdrawPercent", new BigDecimal(percent));
        lenient().when(loyaltyService.getLoyaltyBonusesDistributionMultipliersCached(any())).thenReturn(multipliers);
    }

    private void passDistributeBonusesArgs(Long orderId, Long s, Long l, Long b, Long m, List<BonusesService.Position> positions) {
        bonusesService.distributeBonuses(
                orderId == null ? null : orderId.toString(),
                BigDecimal.valueOf(s),
                BigDecimal.valueOf(l),
                new BonusesBalanceDTO(
                        new BonusesAmountDTO(
                                BigDecimal.valueOf(b),
                                BigDecimal.valueOf(m),
                                BigDecimal.valueOf(b + m)
                        ),
                        ZERO
                ),
                positions,
                false,
                multipliers
        );
    }

    private static BigDecimal c(int v) {
        return new BigDecimal(v);
    }

    @Builder
    @Getter
    public static class BonusesArgument {
        //Входные значения
        int s;                              //S
        int l;                              //L
        int b;                              //B
        int m;                              //M
        int q;                              //Q - параметр лишний, но необходим, т.к. в месте вызова будет явно бросаться в глаза
        Stream<Integer> opAmounts;          // opAmounts и opProhibitedFlags дадут P и Q
        Stream<Boolean> opProhibitedFlags;  // ^^^

        //Ожидаемый результат
        int expectedBonusesTotal;
        int expectedMoneyTotal;
        Stream<Integer> expectedOpBonusesAmount;
        Stream<Integer> expectedOpMoneyAmount;
    }

    @Builder
    @Getter
    public static class BonusesReturnArgument {
        int expertiseDefectDiscountPrice;
        int withdrawAmount;
        int withdrawBonuses;
        int withdrawMoney;

        //Ожидаемый результат
        int positionEffectiveAmount;
        int effectiveWithdrawBonuses;
        int effectiveWithdrawMoney;
    }

    @Builder
    @Getter
    public static class ApplyToCartArgument {
        Boolean buyerIsNull;
        Boolean promoCodeIsApplied;
        Integer withdrawPercent;
        Integer transferPercent;
        Integer transferBonusesLimit;
        Integer bonusesBalance;
        Integer moneyBalance;

        Integer orderAmount;
        Stream<Integer> opAmounts;
        Stream<Boolean> opProhibitedFlags;
        Stream<Boolean> opAvailableToBuyFlags;

        //Ожидаемый результат
        Stream<Integer> expectedOpBonusesAmountTransferred;
        Stream<Integer> expectedOpMoneyAmountTransferred;
        Stream<Integer> expectedOpBonusesAmountWithdraw;
        Stream<Integer> expectedOpMoneyAmountWithdraw;

        Integer expectedBonusesAmountTransferred;
        Integer expectedMoneyAmountTransferred;
        Integer expectedBonusesAmountWithdraw;
        Integer expectedMoneyAmountWithdraw;
    }

    private static class MessageSourceAccessorStub extends MessageSourceAccessor {
        public MessageSourceAccessorStub() {
            super(new MessageSource() {
                @Override
                public String getMessage(@NotNull String s, Object[] objects, String s1, @NotNull Locale locale) {
                    return null;
                }

                @NotNull
                @Override
                public String getMessage(@NotNull String s, Object[] objects, @NotNull Locale locale) throws NoSuchMessageException {
                    StringBuilder result = new StringBuilder();
                    if (objects != null) {
                        for (Object object : objects) {
                            result.append(object);
                            result.append(" ");
                        }
                    }
                    return result.toString();
                }

                @NotNull
                @Override
                public String getMessage(@NotNull MessageSourceResolvable messageSourceResolvable, @NotNull Locale locale) throws NoSuchMessageException {
                    return "";
                }
            });
        }
    }
}