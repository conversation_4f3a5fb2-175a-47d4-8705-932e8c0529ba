package ru.oskelly.tests.pr.suite1_3.domain.service.order.track;

import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import su.reddot.domain.model.order.OrderSource;
import su.reddot.domain.service.dto.order.track.OrderStageDTO;

@Slf4j
public class FinishScreensLogicTest extends OrderTrackServiceOskellyExpertiseBaseTest {
    private static final String SINGLE_CONTEXT = "/mocked-orders/finishedscreens/single";
    private static final String SEVERAL_CONTEXT = "/mocked-orders/finishedscreens/several";
    private static final String RESOLVED_CONTEXT_POSTFIX = "/resolved";
    private static final String FINISHED_CONTEXT_POSTFIX = "/finished";
    private static final String PACKING_CONTEXT_POSTFIX = "/packing";

    @Nested
    class SinglePositionTest {
        @Nested
        class BuyerContextTest {
            @Test
            public void testForFinished() {
                OrderStageDTO orderStage = expectExpertiseStage(
                    BUYER_CONTEXT, SINGLE_CONTEXT + FINISHED_CONTEXT_POSTFIX, "7_1"
                );
                Assertions.assertThat(orderStage.getDescription())
                    .isEqualTo("st.CHANGES_MATCHING.FAILED.description.Buyer.singular");

                orderStage = expectExpertiseStage(
                    BUYER_CONTEXT, SINGLE_CONTEXT + FINISHED_CONTEXT_POSTFIX, "7_0"
                );
                Assertions.assertThat(orderStage.getDescription())
                    .isEqualTo("st.FINISHED.SUCCESS.description.Buyer");
            }

            @Test
            public void testForResolved_Completed() {
                OrderStageDTO orderStage = expectExpertiseStage(
                    BUYER_CONTEXT, SINGLE_CONTEXT + RESOLVED_CONTEXT_POSTFIX, "5_0-compl"
                );
                Assertions.assertThat(orderStage.getDescription())
                    .isEqualTo("st.CHANGES_MATCHING.SUCCESS.zeroDiscount.description.Buyer.singular[1515151 brand1515151]");

                orderStage = expectExpertiseStage(
                    BUYER_CONTEXT, SINGLE_CONTEXT + RESOLVED_CONTEXT_POSTFIX, "5_1-compl"
                );
                Assertions.assertThat(orderStage.getDescription())
                    .isEqualTo("st.CHANGES_MATCHING.SUCCESS.zeroDiscount.description.Buyer.singular[1515151 brand1515151]");

                orderStage = expectExpertiseStage(
                    BUYER_CONTEXT, SINGLE_CONTEXT + RESOLVED_CONTEXT_POSTFIX, "6_0-compl"
                );
                Assertions.assertThat(orderStage.getDescription())
                    .isEqualTo("st.CHANGES_MATCHING.SUCCESS.zeroDiscount.description.Buyer.singular[1515151 brand1515151]");
            }

            @Test
            public void testForResolved_Negative() {
                OrderStageDTO orderStage = expectExpertiseStage(
                    BUYER_CONTEXT, SINGLE_CONTEXT + RESOLVED_CONTEXT_POSTFIX, "5_0-neg"
                );
                Assertions.assertThat(orderStage.getDescription())
                    .isEqualTo("st.CHANGES_MATCHING.FAILED.description.Buyer.singular");

                orderStage = expectExpertiseStage(
                    BUYER_CONTEXT, SINGLE_CONTEXT + RESOLVED_CONTEXT_POSTFIX, "5_1-neg"
                );
                Assertions.assertThat(orderStage.getDescription())
                    .isEqualTo("st.CHANGES_MATCHING.FAILED.description.Buyer.singular");

                orderStage = expectExpertiseStage(
                    BUYER_CONTEXT, SINGLE_CONTEXT + RESOLVED_CONTEXT_POSTFIX, "6_0-neg"
                );
                Assertions.assertThat(orderStage.getDescription())
                    .isEqualTo("st.CHANGES_MATCHING.FAILED.description.Buyer.singular");
            }

            @Test
            public void testForPacking_Completed() {
                OrderStageDTO orderStage = expectExpertiseStage(
                    BUYER_CONTEXT, SINGLE_CONTEXT + PACKING_CONTEXT_POSTFIX, "6_1-compl"
                );
                Assertions.assertThat(orderStage.getDescription())
                    .isEqualTo("st.PACKING.IN_PROGRESS.description.Buyer.singular");
            }

            @Test
            public void testForPacking_Negative() {
                OrderStageDTO orderStage = expectExpertiseStage(
                    BUYER_CONTEXT, SINGLE_CONTEXT + PACKING_CONTEXT_POSTFIX, "6_1-neg"
                );
                Assertions.assertThat(orderStage.getDescription())
                    .isEqualTo("st.PACKING.IN_PROGRESS.description.Buyer.singular");
            }
        }

        @Nested
        class SellerContextTest {
            @Test
            public void testForFinished() {
                OrderStageDTO orderStage = expectExpertiseStage(
                    SELLER_CONTEXT, SINGLE_CONTEXT + FINISHED_CONTEXT_POSTFIX, "7_1"
                );
                Assertions.assertThat(orderStage.getDescription())
                    .isEqualTo("st.CHANGES_MATCHING.FAILED.description.Seller.singular");

                orderStage = expectExpertiseStage(
                    SELLER_CONTEXT, SINGLE_CONTEXT + FINISHED_CONTEXT_POSTFIX, "7_0"
                );
                Assertions.assertThat(orderStage.getDescription())
                    .isEqualTo("st.FINISHED.SUCCESS.description.Seller");

                orderStage = expectExpertiseStage(
                        SELLER_CONTEXT, SINGLE_CONTEXT + FINISHED_CONTEXT_POSTFIX, "7_0",
                        OrderSource.BOUTIQUE
                );
                Assertions.assertThat(orderStage.getDescription())
                          .isEqualTo("st.FINISHED.SUCCESS.boutiqueDescription.Seller");
            }

            @Test
            public void testForResolved_Completed() {
                OrderStageDTO orderStage = expectExpertiseStage(
                    SELLER_CONTEXT, SINGLE_CONTEXT + RESOLVED_CONTEXT_POSTFIX, "5_0-compl"
                );
                Assertions.assertThat(orderStage.getDescription())
                    .isEqualTo("st.CHANGES_MATCHING.SUCCESS.zeroDiscount.description.Seller.singular[1515151 brand1515151]");

                orderStage = expectExpertiseStage(
                    SELLER_CONTEXT, SINGLE_CONTEXT + RESOLVED_CONTEXT_POSTFIX, "5_1-compl"
                );
                Assertions.assertThat(orderStage.getDescription())
                    .isEqualTo("st.CHANGES_MATCHING.SUCCESS.zeroDiscount.description.Seller.singular[1515151 brand1515151]");

                orderStage = expectExpertiseStage(
                    SELLER_CONTEXT, SINGLE_CONTEXT + RESOLVED_CONTEXT_POSTFIX, "6_0-compl"
                );
                Assertions.assertThat(orderStage.getDescription())
                    .isEqualTo("st.CHANGES_MATCHING.SUCCESS.zeroDiscount.description.Seller.singular[1515151 brand1515151]");

                orderStage = expectExpertiseStage(
                        SELLER_CONTEXT, SINGLE_CONTEXT + RESOLVED_CONTEXT_POSTFIX, "6_0-compl",
                        OrderSource.BOUTIQUE
                );
                Assertions.assertThat(orderStage.getDescription())
                          .isEqualTo("st.CHANGES_MATCHING.SUCCESS.zeroDiscount.boutiqueDescription.Seller.singular[1515151 brand1515151]");
            }

            @Test
            public void testForResolved_Negative() {
                OrderStageDTO orderStage = expectExpertiseStage(
                    SELLER_CONTEXT, SINGLE_CONTEXT + RESOLVED_CONTEXT_POSTFIX, "5_0-neg"
                );
                Assertions.assertThat(orderStage.getDescription())
                    .isEqualTo("st.CHANGES_MATCHING.FAILED.description.Seller.singular");

                orderStage = expectExpertiseStage(
                    SELLER_CONTEXT, SINGLE_CONTEXT + RESOLVED_CONTEXT_POSTFIX, "5_1-neg"
                );
                Assertions.assertThat(orderStage.getDescription())
                    .isEqualTo("st.CHANGES_MATCHING.FAILED.description.Seller.singular");

                orderStage = expectExpertiseStage(
                    SELLER_CONTEXT, SINGLE_CONTEXT + RESOLVED_CONTEXT_POSTFIX, "6_0-neg"
                );
                Assertions.assertThat(orderStage.getDescription())
                    .isEqualTo("st.CHANGES_MATCHING.FAILED.description.Seller.singular");

                orderStage = expectExpertiseStage(
                        SELLER_CONTEXT, SINGLE_CONTEXT + RESOLVED_CONTEXT_POSTFIX, "6_0-neg",
                        OrderSource.BOUTIQUE
                );
                Assertions.assertThat(orderStage.getDescription())
                          .isEqualTo("st.CHANGES_MATCHING.FAILED.boutiqueDescription.Seller.singular");
            }

            @Test
            public void testForPacking_Completed() {
                OrderStageDTO orderStage = expectExpertiseStage(
                    SELLER_CONTEXT, SINGLE_CONTEXT + PACKING_CONTEXT_POSTFIX, "6_1-compl"
                );
                Assertions.assertThat(orderStage.getDescription())
                    .isEqualTo("st.PACKING.IN_PROGRESS.description.Seller.singular");
            }

            @Test
            public void testForPacking_Negative() {
                OrderStageDTO orderStage = expectExpertiseStage(
                    SELLER_CONTEXT, SINGLE_CONTEXT + PACKING_CONTEXT_POSTFIX, "6_1-neg"
                );
                Assertions.assertThat(orderStage.getDescription())
                    .isEqualTo("st.PACKING.IN_PROGRESS.description.Seller.singular");
            }
        }
    }

    @Nested
    class SeveralPositionTest {
        @Nested
        class BuyerContextTest {
            @Test
            public void testForFinished() {
                OrderStageDTO orderStage = expectExpertiseStage(
                    BUYER_CONTEXT, SEVERAL_CONTEXT + FINISHED_CONTEXT_POSTFIX, "7_0"
                );
                Assertions.assertThat(orderStage.getDescription()).isEqualTo(null);
                expectAllPositionsTexts(
                    orderStage,
                    "st.FINISHED.SUCCESS.position.Buyer.plural[1514749 brand1514749, 1514745 brand1514745]",
                    "st.FINISHED.FAILED.position.Buyer.singular[1514747 brand1514747]"
                );
            }

            @Test
            public void testForResolved() {
                OrderStageDTO orderStage = expectExpertiseStage(
                    BUYER_CONTEXT, SEVERAL_CONTEXT + RESOLVED_CONTEXT_POSTFIX, "5_0"
                );
                Assertions.assertThat(orderStage.getDescription()).isEqualTo(null);
                expectAllPositionsTexts(
                    orderStage,
                    "st.CHANGES_MATCHING.SUCCESS.position.Buyer.singular[1514749 brand1514749|500 ₽]",
                    "st.AUTHENTICITY.SUCCESS.position.Buyer.singular[1514745 brand1514745]",
                    "st.CHANGES_MATCHING.FAILED.position.Buyer.singular[1514747 brand1514747]"
                );
                orderStage = expectExpertiseStage(
                    BUYER_CONTEXT, SEVERAL_CONTEXT + RESOLVED_CONTEXT_POSTFIX, "5_1"
                );
                Assertions.assertThat(orderStage.getDescription()).isEqualTo(null);
                expectAllPositionsTexts(
                    orderStage,
                    "st.CHANGES_MATCHING.SUCCESS.position.Buyer.singular[1514749 brand1514749|500 ₽]",
                    "st.AUTHENTICITY.SUCCESS.position.Buyer.singular[1514745 brand1514745]",
                    "st.CHANGES_MATCHING.FAILED.position.Buyer.singular[1514747 brand1514747]"
                );
                orderStage = expectExpertiseStage(
                    BUYER_CONTEXT, SEVERAL_CONTEXT + RESOLVED_CONTEXT_POSTFIX, "6_0"
                );
                Assertions.assertThat(orderStage.getDescription()).isEqualTo(null);
                expectAllPositionsTexts(
                    orderStage,
                    "st.CHANGES_MATCHING.SUCCESS.position.Buyer.singular[1514749 brand1514749|500 ₽]",
                    "st.AUTHENTICITY.SUCCESS.position.Buyer.singular[1514745 brand1514745]",
                    "st.CHANGES_MATCHING.FAILED.position.Buyer.singular[1514747 brand1514747]"
                );
            }

            @Test
            public void testForPacking() {
                OrderStageDTO orderStage = expectExpertiseStage(
                    BUYER_CONTEXT, SEVERAL_CONTEXT + PACKING_CONTEXT_POSTFIX, "6_1"
                );
                Assertions.assertThat(orderStage.getDescription()).isEqualTo(null);
                expectAllPositionsTexts(
                    orderStage,
                    "st.PACKING.IN_PROGRESS.position.Buyer.plural[1514749 brand1514749, 1514745 brand1514745]",
                    "st.FINISHED.FAILED.position.Buyer.singular[1514747 brand1514747]"
                );
            }
        }

        @Nested
        class SellerContextTest {
            @Test
            public void testForFinished() {
                OrderStageDTO orderStage = expectExpertiseStage(
                    SELLER_CONTEXT, SEVERAL_CONTEXT + FINISHED_CONTEXT_POSTFIX, "7_0"
                );
                Assertions.assertThat(orderStage.getDescription()).isEqualTo(null);
                expectAllPositionsTexts(
                    orderStage,
                    "st.FINISHED.SUCCESS.position.Seller.plural[1514749 brand1514749, 1514745 brand1514745]",
                    "st.FINISHED.FAILED.position.Seller.singular[1514747 brand1514747]"
                );

                orderStage = expectExpertiseStage(
                        SELLER_CONTEXT, SEVERAL_CONTEXT + FINISHED_CONTEXT_POSTFIX, "7_0",
                        OrderSource.BOUTIQUE
                );
                Assertions.assertThat(orderStage.getDescription()).isEqualTo(null);
                expectAllPositionsTexts(
                        orderStage,
                        "st.FINISHED.SUCCESS.boutiquePosition.Seller.plural[1514749 brand1514749, 1514745 brand1514745]",
                        "st.FINISHED.FAILED.position.Seller.singular[1514747 brand1514747]"
                );
            }

            @Test
            public void testForResolved() {
                OrderStageDTO orderStage = expectExpertiseStage(
                    SELLER_CONTEXT, SEVERAL_CONTEXT + RESOLVED_CONTEXT_POSTFIX, "5_0"
                );
                Assertions.assertThat(orderStage.getDescription()).isEqualTo(null);
                expectAllPositionsTexts(
                    orderStage,
                    "st.CHANGES_MATCHING.SUCCESS.position.Seller.singular[1514749 brand1514749|500 ₽]",
                    "st.AUTHENTICITY.SUCCESS.position.Seller.singular[1514745 brand1514745]",
                    "st.CHANGES_MATCHING.FAILED.position.Seller.singular[1514747 brand1514747]"
                );
                orderStage = expectExpertiseStage(
                        SELLER_CONTEXT, SEVERAL_CONTEXT + RESOLVED_CONTEXT_POSTFIX, "5_0",
                        OrderSource.BOUTIQUE
                );
                Assertions.assertThat(orderStage.getDescription()).isEqualTo(null);
                expectAllPositionsTexts(
                        orderStage,
                        "st.CHANGES_MATCHING.SUCCESS.boutiquePosition.Seller.singular[1514749 brand1514749|500 ₽]",
                        "st.AUTHENTICITY.SUCCESS.boutiquePosition.Seller.singular[1514745 brand1514745]",
                        "st.CHANGES_MATCHING.FAILED.boutiquePosition.Seller.singular[1514747 brand1514747]"
                );
                orderStage = expectExpertiseStage(
                    SELLER_CONTEXT, SEVERAL_CONTEXT + RESOLVED_CONTEXT_POSTFIX, "5_1"
                );
                Assertions.assertThat(orderStage.getDescription()).isEqualTo(null);
                expectAllPositionsTexts(
                    orderStage,
                    "st.CHANGES_MATCHING.SUCCESS.position.Seller.singular[1514749 brand1514749|500 ₽]",
                    "st.AUTHENTICITY.SUCCESS.position.Seller.singular[1514745 brand1514745]",
                    "st.CHANGES_MATCHING.FAILED.position.Seller.singular[1514747 brand1514747]"
                );
                orderStage = expectExpertiseStage(
                    SELLER_CONTEXT, SEVERAL_CONTEXT + RESOLVED_CONTEXT_POSTFIX, "6_0"
                );
                Assertions.assertThat(orderStage.getDescription()).isEqualTo(null);
                expectAllPositionsTexts(
                    orderStage,
                    "st.CHANGES_MATCHING.SUCCESS.position.Seller.singular[1514749 brand1514749|500 ₽]",
                    "st.AUTHENTICITY.SUCCESS.position.Seller.singular[1514745 brand1514745]",
                    "st.CHANGES_MATCHING.FAILED.position.Seller.singular[1514747 brand1514747]"
                );
                orderStage = expectExpertiseStage(
                        SELLER_CONTEXT, SEVERAL_CONTEXT + RESOLVED_CONTEXT_POSTFIX, "6_0",
                        OrderSource.BOUTIQUE
                );
                Assertions.assertThat(orderStage.getDescription()).isEqualTo(null);
                expectAllPositionsTexts(
                        orderStage,
                        "st.CHANGES_MATCHING.SUCCESS.boutiquePosition.Seller.singular[1514749 brand1514749|500 ₽]",
                        "st.AUTHENTICITY.SUCCESS.boutiquePosition.Seller.singular[1514745 brand1514745]",
                        "st.CHANGES_MATCHING.FAILED.boutiquePosition.Seller.singular[1514747 brand1514747]"
                );
            }

            @Test
            public void testForPacking() {
                OrderStageDTO orderStage = expectExpertiseStage(
                    SELLER_CONTEXT, SEVERAL_CONTEXT + PACKING_CONTEXT_POSTFIX, "6_1"
                );
                Assertions.assertThat(orderStage.getDescription()).isEqualTo(null);
                expectAllPositionsTexts(
                    orderStage,
                    "st.PACKING.IN_PROGRESS.position.Seller.plural[1514749 brand1514749, 1514745 brand1514745]",
                    "st.FINISHED.FAILED.position.Seller.singular[1514747 brand1514747]"
                );
            }
        }
    }
}
