package ru.oskelly.tests.pr.suite2.infrastructure.mailganer;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.*;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ResourceUtils;
import org.springframework.web.client.RestTemplate;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.mailganer.MailganerClient;
import su.reddot.infrastructure.mailganer.response.SendTriggerResponse;

import java.net.URI;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.*;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withStatus;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = OskellyApplication.class, properties = {
        "mailganer.api_key=test_api_key"
})
@ActiveProfiles(AbstractSpringTest.testProfiles)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class MailganerClientTest {
    public static final String TRIGGER_ID = "12314";
    public static final String EMAIL = "<EMAIL>";
    @Autowired
    RestTemplate restTemplate;
    @Autowired
    MailganerClient mailganerClient;
    @Value("${mailganer.trigger_url}")
    private String triggerUrl;

    private MockRestServiceServer mockServer;

    @BeforeEach
    public void init() {
        mockServer = MockRestServiceServer.createServer(restTemplate);
    }

    @Test
    public void sendTriggerSendsTriggerAndReturnsSuccessfulResponse() {
        Resource resource = new ClassPathResource("/json/mailganer/SendTriggerResponseSuccess.json");

        MultiValueMap<String, String> expectedParams = new LinkedMultiValueMap<>();
        expectedParams.add("trigger_id", TRIGGER_ID);
        expectedParams.add("email", EMAIL);
        expectedParams.add("api_key", "test_api_key");
        expectedParams.add("doi_ok", "1");
        mockServer.expect(ExpectedCount.once(), requestTo(triggerUrl))
                .andExpect(method(HttpMethod.POST))
                .andExpect(content().contentType("application/x-www-form-urlencoded;charset=UTF-8"))
                .andExpect(content().formData(expectedParams))
                .andExpect(header(HttpHeaders.AUTHORIZATION, "CodeRequest test_api_key"))
                .andRespond(withStatus(HttpStatus.OK).contentType(MediaType.APPLICATION_JSON).body(resource));

        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("trigger_id", TRIGGER_ID);
        params.add("email", EMAIL);

        ResponseEntity<SendTriggerResponse> sendTriggerResponseResponseEntity = mailganerClient.sendTrigger(params);

        mockServer.verify();

        SendTriggerResponse sendTriggerResponse = sendTriggerResponseResponseEntity.getBody();
        assertThat(sendTriggerResponse).isNotNull();
        assertThat(sendTriggerResponse.getStatus()).isEqualTo(1);
        assertThat(sendTriggerResponse.getError()).isNull();
        assertThat(sendTriggerResponse.getStatusText()).isEqualTo("success");
    }

    @Test
    public void sendTriggerReturnsErrorResponseOnMailganerError() {
        Resource resource = new ClassPathResource("/json/mailganer/SendTriggerResponseError.json");

        mockServer.expect(ExpectedCount.once(), requestTo(triggerUrl))
                .andRespond(withStatus(HttpStatus.OK).contentType(MediaType.APPLICATION_JSON).body(resource));

        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("trigger_id", TRIGGER_ID);
        params.add("email", EMAIL);
        ResponseEntity<SendTriggerResponse> sendTriggerResponseResponseEntity = mailganerClient.sendTrigger(params);

        mockServer.verify();

        SendTriggerResponse sendTriggerResponse = sendTriggerResponseResponseEntity.getBody();
        assertThat(sendTriggerResponse).isNotNull();
        assertThat(sendTriggerResponse.getStatus()).isEqualTo(-1);
        assertThat(sendTriggerResponse.getError()).isEqualTo("Trigger not active");
        assertThat(sendTriggerResponse.getStatusText()).isEqualTo("error");
    }
}
