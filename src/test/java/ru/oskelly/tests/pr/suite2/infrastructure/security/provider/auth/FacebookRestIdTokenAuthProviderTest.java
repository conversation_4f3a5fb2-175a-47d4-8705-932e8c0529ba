package ru.oskelly.tests.pr.suite2.infrastructure.security.provider.auth;


import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.exception.UserDeletedException;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.security.provider.auth.FacebookRestIdTokenAuthProvider;
import su.reddot.infrastructure.security.token.FacebookRestIdToken;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;

import static org.assertj.core.api.Assertions.assertThatThrownBy;

@ActiveProfiles(profiles = AbstractSpringTest.testProfiles)
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
@SpringBootTest(classes = OskellyApplication.class)
@ExtendWith(SpringExtension.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class FacebookRestIdTokenAuthProviderTest {
    private static final String NICKNAME = "user-facebook-rest-id-auth-provider";
    private static final String FACEBOOK_REST_ID = "test-facebook-rest-id";
    private static final String FB_UUID = "fb-uuid";

    @Autowired
    FacebookRestIdTokenAuthProvider facebookRestIdTokenAuthProvider;
    @Autowired
    UserRepository userRepository;
    @Autowired
    CommissionGridService commissionGridService;

    @Test
    public void authenticateThrowsUserDeletedExceptionWhenUserDeleted() {

        User deletedUser = new User()
                .setNickname(NICKNAME)
                .setDeleteTime(ZonedDateTime.now())
                .setRestFacebookId(FACEBOOK_REST_ID)
                .setUserFbUuid(FB_UUID)
                .setUserType(User.UserType.SIMPLE_USER)
                .setChangeTime(LocalDateTime.now())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        userRepository.save(deletedUser);

        assertThatThrownBy(() -> facebookRestIdTokenAuthProvider.authenticate(new FacebookRestIdToken(FACEBOOK_REST_ID, FB_UUID)))
                .isInstanceOf(UserDeletedException.class);

        userRepository.delete(deletedUser);
    }
}
