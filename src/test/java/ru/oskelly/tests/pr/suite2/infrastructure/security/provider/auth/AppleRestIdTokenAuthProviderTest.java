package ru.oskelly.tests.pr.suite2.infrastructure.security.provider.auth;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.exception.UserDeletedException;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.security.oauth.AppleClient;
import su.reddot.infrastructure.security.provider.auth.AppleRestIdTokenAuthProvider;
import su.reddot.infrastructure.security.token.AppleRestIdToken;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.BDDMockito.given;

@ActiveProfiles(profiles = AbstractSpringTest.testProfiles)
@SpringBootTest(classes = OskellyApplication.class)
@ExtendWith(SpringExtension.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class AppleRestIdTokenAuthProviderTest {

    private static final String NICKNAME = "user-test-apple-rest-id";
    private static final String APPLE_REST_ID = "test-rest-apple-id";
    private static final String APPLE_AUTHORIZATION_CODE = "appleAuthorizationCode";

    @Autowired
    AppleRestIdTokenAuthProvider appleRestIdTokenAuthProvider;
    @Autowired
    UserRepository userRepository;
    @Autowired
    CommissionGridService commissionGridService;

    @MockBean
    AppleClient appleClient;

    @Test
    public void authenticateThrowsUserDeletedExceptionWhenUserDeleted() {

        given(appleClient.performUserRequest(anyString(), anyString(), anyBoolean()))
                .willReturn(Collections.singletonMap("appleUserId", APPLE_REST_ID));

        User deletedUser = new User()
                .setNickname(NICKNAME)
                .setDeleteTime(ZonedDateTime.now())
                .setRestAppleId(APPLE_REST_ID)
                .setUserAppleUuid(APPLE_AUTHORIZATION_CODE)
                .setUserType(User.UserType.SIMPLE_USER)
                .setChangeTime(LocalDateTime.now())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        userRepository.save(deletedUser);

        assertThatThrownBy(() -> appleRestIdTokenAuthProvider.authenticate(new AppleRestIdToken(APPLE_REST_ID, APPLE_AUTHORIZATION_CODE)))
                .isInstanceOf(UserDeletedException.class);

        userRepository.delete(deletedUser);
    }
}
