package ru.oskelly.tests.pr.suite2.infrastructure.boutique;

import com.opencsv.CSVWriter;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.exception.OrderException;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderSource;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.SalesChannel;
import su.reddot.domain.service.boutique.BoutiqueImportCsvFileVersion;
import su.reddot.domain.service.boutique.BoutiqueImportCsvService;
import su.reddot.domain.service.boutique.BoutiqueImportService;
import su.reddot.infrastructure.configuration.OskellyApplication;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStreamWriter;
import java.util.List;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {OskellyApplication.class})
@ActiveProfiles(profiles = AbstractSpringTest.testProfiles)
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class BoutiqueImportServiceTest {

    @Autowired
    private BoutiqueImportService boutiqueImportService;

    @Autowired
    private BoutiqueImportCsvService boutiqueImportCsvService;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private ProductRepository productRepository;

    @Value("${test-product-item.product-id}")
    private long productId;

    @Value("${test-product-item.size-id}")
    private long sizeId;

    @Value("${test-product-item.size-name}")
    private String sizeName;

    private SalesChannel oldSaleChannel;

    @BeforeEach
    @Transactional
    public void setUp() {
        final Product product = productRepository.getOne(productId);
        if (product.getSalesChannel() != SalesChannel.BOUTIQUE_AND_WEBSITE) {
            oldSaleChannel = product.getSalesChannel();
            product.setSalesChannel(SalesChannel.BOUTIQUE_AND_WEBSITE);
            productRepository.save(product);
        } else {
            oldSaleChannel = null;
        }
    }

    @AfterEach
    @Transactional
    public void restoreProductsSaleChannel() {
        if (oldSaleChannel != null) {
            final Product product = productRepository.getOne(productId);
            product.setSalesChannel(oldSaleChannel);
            productRepository.save(product);
        }
    }

    @Test
    @Transactional
    @WithMockUser(authorities = {"ADMIN", "ORDER_MANUAL_CHANGE_DELIVERY_STATE"})
    public void testCreateOneOrderForOneSize() {
        final List<Long> orderIds = boutiqueImportService.createBoutiqueOrders(productId, sizeId, null, 1L, false, false);
        verifyBoutiqueOrders(orderIds, 1, 1L);
    }

    @Test
    @Transactional
    @WithMockUser(authorities = {"ADMIN", "ORDER_MANUAL_CHANGE_DELIVERY_STATE"})
    public void testCreateMultipleOrdersForOneSize() {
        final List<Long> orderIds = boutiqueImportService.createBoutiqueOrders(productId, sizeId, null, null, false, false);
        verifyBoutiqueOrders(orderIds, 4, 1L);
    }

    @Test
    @Transactional
    @WithMockUser(authorities = {"ADMIN", "ORDER_MANUAL_CHANGE_DELIVERY_STATE"})
    public void testCreateMultipleOrdersForOneSizeName() {
        final List<Long> orderIds = boutiqueImportService.createBoutiqueOrders(productId, null, sizeName, null, false, false);
        verifyBoutiqueOrders(orderIds, 4, 1L);
    }

    @Test
    @Transactional
    @WithMockUser(authorities = {"ADMIN"})
    public void testThrowExceptionIfBothSizeIdAndSizeNameAreApplied() {
        Assertions.assertThatExceptionOfType(IllegalArgumentException.class).isThrownBy(() ->
                        boutiqueImportService.createBoutiqueOrders(productId, sizeId, sizeName, null, false, false))
                .withMessage("Both filters sizeId and sizeName are applied. Use one of them either sizeId or sizeName");
    }

    @Test
    @Transactional
    @WithMockUser(authorities = {"ADMIN", "ORDER_MANUAL_CHANGE_DELIVERY_STATE"})
    public void testCreateMultipleOrdersForAllSizes() {
        final List<Long> orderIds = boutiqueImportService.createBoutiqueOrders(productId, null, null, null, false, false);
        verifyBoutiqueOrders(orderIds, 9, 1L);
    }

    @Test
    @Transactional
    @WithMockUser(authorities = {"ADMIN"})
    public void testCreateOrdersWithExceedingCount() {
        Assertions.assertThatExceptionOfType(OrderException.class).isThrownBy(() ->
                boutiqueImportService.createBoutiqueOrders(productId, sizeId, null, 5L, false, false));
    }

    @Test
    @SneakyThrows
    @Transactional
    @WithMockUser(authorities = {"ADMIN", "ORDER_MANUAL_CHANGE_DELIVERY_STATE"})
    public void testCreateOrdersFromCsvV1() {
        final List<String> report = boutiqueImportCsvService.createBoutiqueOrders(
                mockCsvFileV1().getInputStream(),
                BoutiqueImportCsvFileVersion.V1
        );
        Assertions.assertThat(report.size()).isEqualTo(3);
        Assertions.assertThat(report.get(0)).contains("Строка 1: созданы заказ(ы) - ");
        Assertions.assertThat(report.get(1)).contains("Строка 2: созданы заказ(ы) - ");
        Assertions.assertThat(report.get(2)).contains("Строка 3: созданы заказ(ы) - ");
    }

    @Test
    @SneakyThrows
    @Transactional
    @WithMockUser(authorities = {"ADMIN", "ORDER_MANUAL_CHANGE_DELIVERY_STATE"})
    public void testCreateOrdersFromCsvV2() {
        final List<String> report = boutiqueImportCsvService.createBoutiqueOrders(
                mockCsvFileV2().getInputStream(),
                BoutiqueImportCsvFileVersion.V2
        );
        Assertions.assertThat(report.size()).isEqualTo(3);
        Assertions.assertThat(report.get(0)).contains("Строка 1: созданы заказ(ы) - ");
        Assertions.assertThat(report.get(1)).contains("Строка 2: созданы заказ(ы) - ");
        Assertions.assertThat(report.get(2)).contains("Строка 3: созданы заказ(ы) - ");
    }

    @SneakyThrows
    private MultipartFile mockCsvFileV1() {
        final ByteArrayOutputStream csvOutputStream = new ByteArrayOutputStream();
        try (final CSVWriter csvWriter = new CSVWriter(new OutputStreamWriter(csvOutputStream))) {
            csvWriter.writeNext(new String[]{String.valueOf(productId), String.valueOf(sizeId), "1"});
            csvWriter.writeNext(new String[]{String.valueOf(productId), String.valueOf(sizeId)});
            csvWriter.writeNext(new String[]{String.valueOf(productId)});
        }
        final InputStream csvInputStream = new ByteArrayInputStream(csvOutputStream.toByteArray());
        final MultipartFile file = Mockito.mock(MultipartFile.class);
        Mockito.when(file.getInputStream()).thenReturn(csvInputStream);
        return file;
    }

    @SneakyThrows
    private MultipartFile mockCsvFileV2() {
        final ByteArrayOutputStream csvOutputStream = new ByteArrayOutputStream();
        try (final CSVWriter csvWriter = new CSVWriter(
                new OutputStreamWriter(csvOutputStream),
                ';', '"', '"', "\n"
        )) {
            csvWriter.writeNext(new String[]{String.valueOf(productId), String.valueOf(sizeName), "1"});
            csvWriter.writeNext(new String[]{String.valueOf(productId), String.valueOf(sizeName)});
            csvWriter.writeNext(new String[]{String.valueOf(productId)});
        }
        final InputStream csvInputStream = new ByteArrayInputStream(csvOutputStream.toByteArray());
        final MultipartFile file = Mockito.mock(MultipartFile.class);
        Mockito.when(file.getInputStream()).thenReturn(csvInputStream);
        return file;
    }

    private void verifyBoutiqueOrders(final List<Long> orderIds, final long expectedCount, final long orderSourceInfoId) {
        Assertions.assertThat(orderIds.size()).isEqualTo(expectedCount);
        for (final long orderId : orderIds) {
            final Order order = orderRepository.getOne(orderId);
            Assertions.assertThat(order.getOrderSource()).isEqualTo(OrderSource.BOUTIQUE);
            Assertions.assertThat(order.getState()).isEqualTo(OrderState.MONEY_TRANSFERRED);
            Assertions.assertThat(order.getOrderSourceInfo().getId() == orderSourceInfoId);
        }
    }
}
