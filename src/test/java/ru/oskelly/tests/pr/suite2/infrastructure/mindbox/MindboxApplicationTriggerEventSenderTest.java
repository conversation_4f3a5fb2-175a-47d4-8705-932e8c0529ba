package ru.oskelly.tests.pr.suite2.infrastructure.mindbox;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.util.ResourceUtils;
import org.springframework.web.client.RestTemplate;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.event.ChangeEnvironmentEvent;
import su.reddot.domain.event.UserRegisterEvent;
import su.reddot.domain.event.UserSubscriptionEvent;
import su.reddot.domain.model.device.DeviceDtype;
import su.reddot.domain.model.discount.AbsolutePromoCode;
import su.reddot.domain.model.discount.PromoCode;
import su.reddot.domain.model.notification.UserSubscriptionType;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.user.SellerType;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.cart.CartService;
import su.reddot.domain.service.cart.ChangeCartEvent;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.domain.service.device.DeviceService;
import su.reddot.domain.service.like.ChangeWishlistEvent;
import su.reddot.domain.service.order.OrderPaidEvent;
import su.reddot.domain.service.user.ChangeUserEvent;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.mindbox.MindboxApplicationTriggerEventSender;
import su.reddot.infrastructure.mindbox.client.dto.ChangeEnvironmentRequest;
import su.reddot.infrastructure.mindbox.client.dto.CustomerRequest;
import su.reddot.infrastructure.mindbox.client.dto.RegisterCustomerRequest;
import su.reddot.infrastructure.mindbox.client.dto.SubscribeCustomerRequest;
import su.reddot.infrastructure.util.ProductionEnvironment;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.springframework.test.web.client.match.MockRestRequestMatchers.*;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withStatus;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = OskellyApplication.class,
        properties = {
                "app.integration.mindbox.triggers.enabled=true",
                "app.integration.mindbox.triggers.user-register.enabled=true",
                "app.integration.mindbox.triggers.user-change.enabled=true",
                "app.integration.mindbox.triggers.order-confirm.enabled=true",
                "app.integration.mindbox.triggers.user-subscription.enabled=true",
                "app.integration.mindbox.triggers.order-paid.enabled=true",
                "app.integration.mindbox.triggers.cart-change.enabled=true",
                "app.integration.mindbox.triggers.wishlist-change.enabled=true"
        })
@ActiveProfiles(AbstractSpringTest.testProfiles)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class MindboxApplicationTriggerEventSenderTest {

    @Autowired
    MindboxApplicationTriggerEventSender mindboxApplicationTriggerEventSender;

    @Autowired
    RestTemplate restTemplate;
    @Autowired
    CartService cartService;
    @Autowired
    UserRepository userRepository;
    @Autowired
    CommissionGridService commissionGridService;

    private MockRestServiceServer mockServer;
    @Autowired
    private ObjectMapper objectMapper;

    private static final String NICKNAME = "test-mindox-nickname";

    private User currentUser;


    @BeforeEach
    public void init() {
        mockServer = MockRestServiceServer.createServer(restTemplate);

        User user = new User()
                .setNickname(NICKNAME)
                .setUserType(User.UserType.SIMPLE_USER)
                .setChangeTime(LocalDateTime.now())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        currentUser = userRepository.save(user);
    }

    @AfterEach
    public void cleanup() {
        mockServer.reset();

        userRepository.delete(currentUser);
    }

    @Test
    public void onUserRegisterSetSubscriptionsForUser() {

        UserRegisterEvent userRegisterEvent = new UserRegisterEvent();
        userRegisterEvent.setInstanceId("instance");


        userRegisterEvent.setDeviceInfo(getDeviceInfo());
        userRegisterEvent.setSubscriptionApprove(true);
        userRegisterEvent.setUserId(currentUser.getId());
        userRegisterEvent.setEmail("<EMAIL>");
        userRegisterEvent.setPhone("88005553535");


        mockServer.expect(ExpectedCount.once(), requestTo("https://api.mindbox.ru/v3/operations/async?endpointId=_FAKE_oskelly-iOs-sandbox&operation=MobileRegisterCustomer&deviceUUID=mindbox_uuid"))
                .andExpect(method(HttpMethod.POST))
                .andExpect(content().json("{" +
                        "  \"customer\": {" +
                        "    \"subscriptions\": [" +
                        "      {" +
                        "        \"pointOfContact\": \"Mobilepush\"," +
                        "        \"topic\": \"GENERAL_PUSH\"," +
                        "        \"isSubscribed\": true" +
                        "      }," +
                        "      {" +
                        "        \"pointOfContact\": \"Email\"," +
                        "        \"topic\": \"GENERAL_PUSH\"," +
                        "        \"isSubscribed\": true" +
                        "      }," +
                        "      {" +
                        "        \"pointOfContact\": \"SMS\"," +
                        "        \"topic\": \"GENERAL_WHATSAPP\"," +
                        "        \"isSubscribed\": true" +
                        "      }," +
                        "      {" +
                        "        \"pointOfContact\": \"Mobilepush\"," +
                        "        \"topic\": \"REQUIRING_ATTENTION\"," +
                        "        \"isSubscribed\": true" +
                        "      }," +
                        "      {" +
                        "        \"pointOfContact\": \"Email\"," +
                        "        \"topic\": \"REQUIRING_ATTENTION\"," +
                        "        \"isSubscribed\": true" +
                        "      }," +
                        "      {" +
                        "        \"pointOfContact\": \"Email\"," +
                        "        \"topic\": \"MESSAGE_OSKELLY\"," +
                        "        \"isSubscribed\": true" +
                        "      }," +
                        "      {" +
                        "        \"pointOfContact\": \"SMS\"," +
                        "        \"topic\": \"WA_REQUIRING_ATTENTION\"," +
                        "        \"isSubscribed\": true" +
                        "      }," +
                        "      {" +
                        "        \"pointOfContact\": \"Mobilepush\"," +
                        "        \"topic\": \"MESSAGE_OSKELLY\"," +
                        "        \"isSubscribed\": true" +
                        "      }," +
                        "      {" +
                        "        \"pointOfContact\": \"Email\"," +
                        "        \"topic\": \"MARKETING_AND_SOCIAL\"," +
                        "        \"isSubscribed\": true" +
                        "      }," +
                        "      {" +
                        "        \"pointOfContact\": \"Mobilepush\"," +
                        "        \"topic\": \"MARKETING_AND_SOCIAL\"," +
                        "        \"isSubscribed\": true" +
                        "      }" +
                        "    ]" +
                        "  }" +
                        "}"))
                .andRespond(withStatus(HttpStatus.OK));

        mindboxApplicationTriggerEventSender.onUserRegisterEvent(userRegisterEvent);
    }


    @Test
    public void onUserSubscriptionEventSetsTopicsInMindbox() throws IOException {
        Set<Long> notificationGroupIds = new HashSet<>();
        notificationGroupIds.add(6L);
        notificationGroupIds.add(7L);
        notificationGroupIds.add(8L);
        UserSubscriptionEvent userSubscriptionEvent = new UserSubscriptionEvent();
        userSubscriptionEvent.setNotificationGroupIds(notificationGroupIds);
        userSubscriptionEvent.setInstanceId("test-instance-id");
        userSubscriptionEvent.setDeviceInfo(getDeviceInfo());
        userSubscriptionEvent.setContact("test-email");
        userSubscriptionEvent.setUserId(currentUser.getId());
        userSubscriptionEvent.setType(UserSubscriptionType.EMAIL);

        mockServer.expect(ExpectedCount.once(), requestTo("https://api.mindbox.ru/v3/operations/async?endpointId=_FAKE_oskelly-iOs-sandbox&operation=Mobile.EditCustomerByEmail&deviceUUID=mindbox_uuid"))
                .andExpect(method(HttpMethod.POST))
                .andExpect(jsonPath("$.customer.email").value("test-email"))
                .andExpect(jsonPath("$.customer.ids.clientIdBuyer").value(currentUser.getId()))
                .andExpect(jsonPath("$.customer.ids.clientIdSeller").value(currentUser.getId()))
                .andExpect(jsonPath("$.customer.subscriptions[0].topic").value("GENERAL_PUSH"))
                .andExpect(jsonPath("$.customer.subscriptions[1].topic").value("REQUIRING_ATTENTION"))
                .andExpect(jsonPath("$.customer.subscriptions[2].topic").value("MESSAGE_OSKELLY"))
                .andRespond(withStatus(HttpStatus.OK));

        mockServer.expect(ExpectedCount.once(), requestTo("https://api.mindbox.ru/v3/operations/async?endpointId=_FAKE_oskelly-iOs-sandbox&operation=Mobile.EditCustomerByEmail&deviceUUID=mindbox_uuid"))
                .andExpect(method(HttpMethod.POST))
                .andExpect(jsonPath("$.customer.email").value("test-email"))
                .andExpect(jsonPath("$.customer.ids.clientIdBuyer").value(currentUser.getId()))
                .andExpect(jsonPath("$.customer.ids.clientIdSeller").value(currentUser.getId()))
                .andExpect(jsonPath("$.customer.subscriptions[0].topic").value("MARKETING_AND_SOCIAL"))
                .andExpect(jsonPath("$.customer.subscriptions[1].topic").value("GENERAL_WHATSAPP"))
                .andExpect(jsonPath("$.customer.subscriptions[2].topic").value("WA_REQUIRING_ATTENTION"))
                .andRespond(withStatus(HttpStatus.OK));

        mindboxApplicationTriggerEventSender.onUserSubscriptionEvent(userSubscriptionEvent);

    }

    @Test
    public void onUserRegisterEventSendsRegisterCustomerRequest() throws IOException {
        LocalDateTime dateTime = LocalDateTime.now();
        DeviceService.DeviceInfo deviceInfo = getDeviceInfo();
        UserRegisterEvent userRegisterEvent = new UserRegisterEvent(
                1L,
                "<EMAIL>",
                "Михал Палыч Терентьев",
                "88005553535",
                "",
                "test_user",
                dateTime,
                deviceInfo,
                true,
                false,
                false
        );
        userRegisterEvent.setInstanceId("test-instance");

        RegisterCustomerRequest expected = objectMapper.readValue(ResourceUtils.getFile("classpath:json/mindbox/RegisterCustomerRequest.json"), RegisterCustomerRequest.class);

        mockServer.expect(ExpectedCount.times(1), requestTo("https://api.mindbox.ru/v3/operations/async?endpointId=_FAKE_oskelly-iOs-sandbox&operation=MobileRegisterCustomer&deviceUUID=mindbox_uuid"))
                .andExpect(clientHttpRequest -> System.out.println(clientHttpRequest.getBody()))
                .andExpect(content().json(objectMapper.writeValueAsString(expected), true))
                .andRespond(withStatus(HttpStatus.OK));

        mindboxApplicationTriggerEventSender.onUserRegisterEvent(userRegisterEvent);

        mockServer.verify();
    }

    @Test
    public void onUserRegisterEventSendsRegisterCustomerRequestAfterSync() throws IOException {
        LocalDateTime dateTime = LocalDateTime.now();
        DeviceService.DeviceInfo deviceInfo = getDeviceInfo();
        UserRegisterEvent userRegisterEvent = new UserRegisterEvent(
                1L,
                "<EMAIL>",
                "Михал Палыч Терентьев",
                "88005553535",
                null,
                "test_user",
                dateTime,
                null,
                true,
                false,
                true
        );
        userRegisterEvent.setInstanceId("test-instance");

        RegisterCustomerRequest expected = objectMapper.readValue(ResourceUtils.getFile("classpath:json/mindbox/RegisterCustomerRequestAfterSync.json"), RegisterCustomerRequest.class);

        mockServer.expect(ExpectedCount.times(1), requestTo("https://api.mindbox.ru/v3/operations/async?endpointId=_FAKE_oskelly.ru&operation=WebsiteRegisterCustomer"))
                .andExpect(clientHttpRequest -> System.out.println(clientHttpRequest.getBody()))
                .andExpect(content().json(objectMapper.writeValueAsString(expected), true))
                .andRespond(withStatus(HttpStatus.OK));

        mindboxApplicationTriggerEventSender.onUserRegisterEvent(userRegisterEvent);

        mockServer.verify();
    }

    @Test
    public void onChangeUserEventSendsCustomerRequest() throws IOException {
        DeviceService.DeviceInfo deviceInfo = getDeviceInfo();
        ChangeUserEvent changeUserEvent = new ChangeUserEvent()
                .setId(1L)
                .setEmail("<EMAIL>")
                .setFullName("Михал Палыч Терентьев")
                .setPhone("88005553535")
                .setNickName("test_user");
        changeUserEvent.setInstanceId("test-instance");
        changeUserEvent.setDeviceInfo(deviceInfo);

        CustomerRequest expected = objectMapper.readValue(ResourceUtils.getFile("classpath:json/mindbox/ChangeUserCustomerRequest.json"), CustomerRequest.class);

        mockServer.expect(ExpectedCount.times(1), requestTo("https://api.mindbox.ru/v3/operations/async?endpointId=_FAKE_oskelly-iOs-sandbox&operation=Mobile.EditCustomerByEmail&deviceUUID=mindbox_uuid"))
                .andExpect(content().json(objectMapper.writeValueAsString(expected), true))
                .andRespond(withStatus(HttpStatus.OK));

        mindboxApplicationTriggerEventSender.onChangeUserEvent(changeUserEvent);

        mockServer.verify();

    }

    @Test
    public void onOrderConfirmSendsCreateOrderRequests() throws IOException {
        OrderPaidEvent.OrderDto orderDto = new OrderPaidEvent.OrderDto()
                .setOrderId(3L)
                .setBuyerId(1L)
                .setSellerId(2L)
                .setSellerType(SellerType.INDIVIDUAL)
                .setBuyerUserType(User.UserType.SIMPLE_USER)
                .setBuyerEmail("<EMAIL>")
                .setBuyerPhone("************")
                .setOrderComission(450.0)
                .setTotalPrice(3000.0);

        PromoCode promoCode = new AbsolutePromoCode()
                .setValue(BigDecimal.valueOf(100))
                .setCreatedAt(ZonedDateTime.now())
                .setCode("PEPEGA");


        OrderPaidEvent.OrderPositionDto firstOrderPositionDto = new OrderPaidEvent.OrderPositionDto()
                .setStatus(OrderPosition.Status.SALE_CONFIRMED)
                .setBasePricePerItem(1000.0)
                .setPromoCodeValue(85.0)
                .setPromoCode(promoCode.getCode())
                .setNumber(1)
                .setProductItemId(5L);

        OrderPaidEvent.OrderPositionDto secondOrderPosition = new OrderPaidEvent.OrderPositionDto()
                .setBasePricePerItem(1000.0)
                .setStatus(OrderPosition.Status.SALE_CONFIRMED)
                .setPromoCodeValue(15.0)
                .setNumber(2)
                .setProductItemId(5L)
                .setPromoCode("PEPEGA")
                .setPromoCodeValue(15.0);


        orderDto.setOrderPositions(Arrays.asList(firstOrderPositionDto, secondOrderPosition));
        orderDto.setDeliverFromCity("BigCity");
        orderDto.setDeliverToCity("Small city");
        orderDto.setDeliveryCost(350.0);
        orderDto.setPromoCode(promoCode.getCode());
        orderDto.setPromoCodeValue(promoCode.getValue().doubleValue());
        orderDto.setConfirmedTime(ZonedDateTime.of(LocalDateTime.of(2022, 7, 10, 11, 12, 13), ZoneId.systemDefault()));

        OrderPaidEvent event = new OrderPaidEvent(orderDto, new DeviceService.DeviceInfo().setMindboxClientUuid("buyer-uuid"), new DeviceService.DeviceInfo().setMindboxClientUuid("seller-uuid"));
        event.setInstanceId("sasddada").setDeviceInfo(new DeviceService.DeviceInfo().setMindboxClientUuid("sadad"));


        JsonNode sellRequest = objectMapper.readTree(ResourceUtils.getFile("classpath:json/mindbox/CreateOrderSellRequest.json"));
        JsonNode buyRequest = objectMapper.readTree(ResourceUtils.getFile("classpath:json/mindbox/CreateOrderBuyRequest.json"));

        mockServer.expect(ExpectedCount.times(1), requestTo("https://api.mindbox.ru/v3/operations/async?endpointId=_FAKE_oskelly.ru&operation=Website.CreateOrderSell&deviceUUID=seller-uuid"))
                .andExpect(content().json(objectMapper.writeValueAsString(sellRequest), true))
                .andRespond(withStatus(HttpStatus.OK));
        mockServer.expect(ExpectedCount.times(1), requestTo("https://api.mindbox.ru/v3/operations/async?endpointId=_FAKE_oskelly.ru&operation=Website.CreateOrderBuy&deviceUUID=buyer-uuid"))
                .andExpect(content().json(objectMapper.writeValueAsString(buyRequest), true))
                .andRespond(withStatus(HttpStatus.OK));

        mindboxApplicationTriggerEventSender.onOrderConfirmedEvent(event);

        mockServer.verify();
    }


    @Test
    public void onChangeCartEventSendSetKorzinaItemRequest() throws IOException {

        List<ChangeCartEvent.OrderPositionDto> orderPositionDtos = new ArrayList<>();

        orderPositionDtos.add(new ChangeCartEvent.OrderPositionDto()
                .setItemPrice(1005.0)
                .setCount(100)
                .setProductItemId(1L));

        orderPositionDtos.add(new ChangeCartEvent.OrderPositionDto()
                .setItemPrice(100.0)
                .setCount(4)
                .setProductItemId(2L));

        ChangeCartEvent changeCartEvent = new ChangeCartEvent(orderPositionDtos);
        changeCartEvent.setInstanceId("test");
        changeCartEvent.setDeviceInfo(getDeviceInfo());

        JsonNode setKorzinaItemRequest = objectMapper.readTree(ResourceUtils.getFile("classpath:json/mindbox/SetKorzinaItemRequest.json"));
        mockServer.expect(ExpectedCount.times(1), requestTo("https://api.mindbox.ru/v3/operations/async?endpointId=_FAKE_oskelly-iOs-sandbox&operation=SetKorzinaItemListMobile&deviceUUID=mindbox_uuid"))
                .andExpect(content().json(objectMapper.writeValueAsString(setKorzinaItemRequest), true))
                .andRespond(withStatus(HttpStatus.OK));


        mindboxApplicationTriggerEventSender.onChangeCartEvent(changeCartEvent);

        mockServer.verify();
    }

    @Test
    public void onChangeCartEventSendResetKorzinaItemRequestWhenOrderPositionsAreEmpty() {
        ChangeCartEvent changeCartEvent = new ChangeCartEvent(Collections.emptyList());
        changeCartEvent.setInstanceId("test");
        changeCartEvent.setDeviceInfo(getDeviceInfo());

        mockServer.expect(ExpectedCount.times(1), requestTo("https://api.mindbox.ru/v3/operations/async?endpointId=_FAKE_oskelly-iOs-sandbox&operation=ResetKorzinaItemListMobile&deviceUUID=mindbox_uuid"))
                .andExpect(content().string(""))
                .andRespond(withStatus(HttpStatus.OK));


        mindboxApplicationTriggerEventSender.onChangeCartEvent(changeCartEvent);

        mockServer.verify();
    }

    @Test
    public void onChangeWishlistEvent() throws IOException {
        List<ChangeWishlistEvent.ProductDto> productDtos = new ArrayList<>();

        productDtos.add(new ChangeWishlistEvent.ProductDto()
                .setPrice(1005.0)
                .setProductId(1L));

        productDtos.add(new ChangeWishlistEvent.ProductDto()
                .setPrice(100.0)
                .setProductId(2L));

        ChangeWishlistEvent changeWishlistEvent = new ChangeWishlistEvent(productDtos);
        changeWishlistEvent.setInstanceId("test");
        changeWishlistEvent.setDeviceInfo(getDeviceInfo());

        JsonNode setWishlistItemRequest = objectMapper.readTree(ResourceUtils.getFile("classpath:json/mindbox/SetWishlistItemListRequest.json"));
        mockServer.expect(ExpectedCount.times(1), requestTo("https://api.mindbox.ru/v3/operations/async?endpointId=_FAKE_oskelly-iOs-sandbox&operation=SetWishListItemListMobile&deviceUUID=mindbox_uuid"))
                .andExpect(content().json(objectMapper.writeValueAsString(setWishlistItemRequest), true))
                .andRespond(withStatus(HttpStatus.OK));


        mindboxApplicationTriggerEventSender.onChangeWishlistEvent(changeWishlistEvent);

        mockServer.verify();
    }

    @Test
    public void onChangeEnvironmentEventSendsCustomerRequest() throws IOException {
        DeviceService.DeviceInfo deviceInfo = getDeviceInfo();
        ChangeEnvironmentEvent changeEnvironmentEvent = new ChangeEnvironmentEvent()
                .setEnvironment(ProductionEnvironment.RU);
        changeEnvironmentEvent.setInstanceId("test-instance");
        changeEnvironmentEvent.setDeviceInfo(deviceInfo);

//        ChangeEnvironmentRequest expected = objectMapper.readValue(ResourceUtils.getFile("classpath:json/mindbox/ChangeEnvironmentCustomerRequest.json"), ChangeEnvironmentRequest.class);
//
//        mockServer.expect(ExpectedCount.times(1), requestTo("https://api.mindbox.ru/v3/operations/async?endpointId=_FAKE_oskelly-iOs-sandbox&operation=Mobile.ChangeEnvironment&deviceUUID=mindbox_uuid"))
//                .andExpect(content().json(objectMapper.writeValueAsString(expected), true))
//                .andRespond(withStatus(HttpStatus.OK));
//
        mindboxApplicationTriggerEventSender.onChangeEnvironmentEvent(changeEnvironmentEvent);

        mockServer.verify();

    }


    private DeviceService.DeviceInfo getDeviceInfo() {
        return new DeviceService.DeviceInfo()
                .setMindboxClientUuid("mindbox_uuid")
                .setDeviceDtype(DeviceDtype.AppleDevice)
                .setUserAgent("test-user-agent")
                .setRemoteAddr("127.0.0.1");
    }
}
