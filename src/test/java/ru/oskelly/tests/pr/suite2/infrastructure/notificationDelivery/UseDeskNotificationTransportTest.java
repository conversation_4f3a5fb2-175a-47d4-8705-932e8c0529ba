package ru.oskelly.tests.pr.suite2.infrastructure.notificationDelivery;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.MockBeans;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.MockPublisherConfiguration;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.BrandRepository;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.address.AddressRepository;
import su.reddot.domain.dao.addressendpoint.AddressEndpointRepository;
import su.reddot.domain.dao.comment.CommentRepository;
import su.reddot.domain.dao.configparam.ConfigParamRepository;
import su.reddot.domain.dao.notification.NotificationRepository;
import su.reddot.domain.dao.notificationDelivery.NotificationDeliveryRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.Comment;
import su.reddot.domain.model.address.Address;
import su.reddot.domain.model.addressendpoint.AddressEndpoint;
import su.reddot.domain.model.notification.comment.NewCommentNotification;
import su.reddot.domain.model.notificationDelivery.NotificationDelivery;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.domain.service.dto.NotificationDeliveryResult;
import su.reddot.domain.service.task.ScheduledNotificationDeliveryTaskRunner;
import su.reddot.infrastructure.chat.UseDeskChannel;
import su.reddot.infrastructure.chat.usedesk.UseDeskService;
import su.reddot.infrastructure.configparam.ConfigParamService;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.notificationDelivery.usedesk.UsedeskNotificationTransport;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.times;

@ExtendWith(SpringExtension.class)
@SpringBootTest(
        classes = {
                OskellyApplication.class,
                MockPublisherConfiguration.class
        },
        properties = {
                "app.usedesk-whatsapp.usedesk.enabled=true"
        }
)
@MockBeans({
        @MockBean(ScheduledNotificationDeliveryTaskRunner.class)
})
@ActiveProfiles(profiles = AbstractSpringTest.testProfiles)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class UseDeskNotificationTransportTest {
    public static final String PICKUP_ADDRESS_PHONE = "88005553535";
    @Autowired
    BrandRepository brandRepository;
    @Autowired
    ProductRepository productRepository;
    @Autowired
    CommissionGridService commissionGridService;
    @Autowired
    UserRepository userRepository;
    @Autowired
    AddressRepository addressRepository;
    @Autowired
    AddressEndpointRepository addressEndpointRepository;
    @Autowired
    NotificationRepository<NewCommentNotification> notificationRepository;
    @Autowired
    NotificationDeliveryRepository notificationDeliveryRepository;
    @Autowired
    ConfigParamRepository configParamRepository;

    @MockBean
    UseDeskService useDeskService;
    @MockBean
    ConfigParamService configParamService;

    @Autowired
    UsedeskNotificationTransport usedeskNotificationTransport;
    @Autowired
    CommentRepository commentRepository;
    private User seller;
    private User buyer;
    private Product product;
    private Address address;
    private AddressEndpoint addressEndpoint;
    private Comment comment;

    @BeforeEach
    public void init() {
        User seller = new User()
                .setNickname(RandomStringUtils.randomAlphabetic(5))
                .setUserType(User.UserType.SIMPLE_USER)
                .setChangeTime(LocalDateTime.now())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid())
                .setCounterparties(Collections.emptyList());
        this.seller = userRepository.saveAndFlush(seller);


        User buyer = new User()
                .setNickname(RandomStringUtils.randomAlphabetic(5))
                .setUserType(User.UserType.SIMPLE_USER)
                .setChangeTime(LocalDateTime.now())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid())
                .setCounterparties(Collections.emptyList());
        this.buyer = userRepository.saveAndFlush(buyer);


        Address address = new Address();
        address.setCity("BigCity");
        address.setUser(seller);
        this.address = addressRepository.saveAndFlush(address);

        AddressEndpoint addressEndpoint = new AddressEndpoint();
        addressEndpoint.setAddress(address);
        addressEndpoint.setUser(seller);
        addressEndpoint.setPhone("123456");
        addressEndpoint.setFirstName("Test");
        addressEndpoint.setLastName("Test");
        addressEndpoint.setPhone(PICKUP_ADDRESS_PHONE);
        this.addressEndpoint = addressEndpointRepository.saveAndFlush(addressEndpoint);

        Product product = new Product();
        product.setBrand(brandRepository.getOne(1L));
        product.setCategoryId(2L);
        product.setSeller(seller);
        product.setCurrentPrice(BigDecimal.valueOf(1000));
        product.setProductState(ProductState.PUBLISHED);
        product.setPickupAddressEndpoint(this.addressEndpoint);
        this.product = productRepository.saveAndFlush(product);


        Comment comment = new Comment()
                .setText("TestComment")
                .setPublisher(buyer)
                .setProduct(product)
                .setPublishTime(ZonedDateTime.now().minusHours(6));
        this.comment = commentRepository.save(comment);

        given(configParamService.getValueAsList(anyString())).willReturn(Collections.emptyList());
    }

    @AfterEach
    public void cleanup() {
        notificationRepository.deleteAll(notificationRepository.findAllByUser(seller.getId()));
        notificationRepository.deleteAll(notificationRepository.findAllByUser(buyer.getId()));
        commentRepository.delete(comment);
        addressEndpointRepository.delete(addressEndpoint);
        addressRepository.delete(address);
        productRepository.delete(product);
        userRepository.delete(buyer);
        userRepository.delete(seller);
    }

    @Test
    public void deliverNotificationShouldUsePhoneFromNotification() throws IOException {
        NewCommentNotification newCommentNotification = new NewCommentNotification();
        newCommentNotification.setUser(seller);
        newCommentNotification.setComment(comment);
        newCommentNotification.setCreateTime(ZonedDateTime.now().minusHours(5));

        newCommentNotification = notificationRepository.save(newCommentNotification);

        NotificationDelivery notificationDelivery = new NotificationDelivery()
                .setNotificationId(newCommentNotification.getId())
                .setChannel("onesignal")
                .setSuccessfulSentTime(ZonedDateTime.now())
                .setCreateTime(ZonedDateTime.now().minusDays(2));

        notificationDelivery = notificationDeliveryRepository.save(notificationDelivery);


        given(useDeskService.getUseDeskId(any())).willReturn(1L);

        usedeskNotificationTransport.deliverNotification(newCommentNotification.getId());

        then(useDeskService)
                .should(times(1))
                .sendNotification(
                        anyLong(),
                        eq(UseDeskChannel.WHATSAPP),
                        eq(PICKUP_ADDRESS_PHONE),
                        anyString(),
                        eq("Здравствуйте, это Oskelly. Вы забыли ответить на комментарий вашего потенциального покупателя. Проверьте, пожалуйста, уведомления в Личном Кабинете."),
                        anyString()
                );
        notificationDeliveryRepository.delete(notificationDelivery);

    }


    @Test
    public void deliverNotificationsShouldSilentlySkipIfTooEarlyToRemind() {
        NewCommentNotification newCommentNotification = new NewCommentNotification();
        newCommentNotification.setUser(seller);
        newCommentNotification.setComment(comment);
        newCommentNotification.setCreateTime(ZonedDateTime.now());

        newCommentNotification = notificationRepository.save(newCommentNotification);

        NotificationDelivery notificationDelivery = new NotificationDelivery()
                .setNotificationId(newCommentNotification.getId())
                .setChannel("onesignal")
                .setSuccessfulSentTime(ZonedDateTime.now())
                .setCreateTime(ZonedDateTime.now());

        notificationDelivery = notificationDeliveryRepository.save(notificationDelivery);

        NotificationDeliveryResult notificationDeliveryResult = usedeskNotificationTransport.deliverNotification(newCommentNotification.getId());
        assertThat(notificationDeliveryResult.getType()).isEqualByComparingTo(NotificationDeliveryResult.ResultType.SKIPPED_SILENTLY);
        assertThat(notificationDeliveryResult.getMessage()).isEqualTo("Too early to remind");

        notificationDeliveryRepository.delete(notificationDelivery);
    }

    @Test
    public void deliverNotificationReturnSkippedStatusIfUserIsInStopList() {
        given(configParamService.getValueAsListCached(eq(ConfigParamService.CONFIG_PARAM_NOTIFICATIONS_CHANNEL_WHATSAPP_USERS_STOP_LIST))).willReturn(Collections.singletonList(seller.getId().toString()));
        NewCommentNotification newCommentNotification = new NewCommentNotification();
        newCommentNotification.setUser(seller);
        newCommentNotification.setComment(comment);
        newCommentNotification.setCreateTime(ZonedDateTime.now());

        newCommentNotification = notificationRepository.save(newCommentNotification);

        NotificationDelivery notificationDelivery = new NotificationDelivery()
                .setNotificationId(newCommentNotification.getId())
                .setChannel("onesignal")
                .setSuccessfulSentTime(ZonedDateTime.now())
                .setCreateTime(ZonedDateTime.now().minusHours(5));

        notificationDelivery = notificationDeliveryRepository.save(notificationDelivery);

        NotificationDeliveryResult notificationDeliveryResult = usedeskNotificationTransport.deliverNotification(newCommentNotification.getId());
        assertThat(notificationDeliveryResult.getType()).isEqualByComparingTo(NotificationDeliveryResult.ResultType.SKIPPED);
        assertThat(notificationDeliveryResult.getMessage()).isEqualTo("User in stop-list");

        notificationDeliveryRepository.delete(notificationDelivery);
    }

}
