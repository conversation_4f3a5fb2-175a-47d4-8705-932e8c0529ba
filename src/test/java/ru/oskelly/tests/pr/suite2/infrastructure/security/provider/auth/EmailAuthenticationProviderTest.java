package ru.oskelly.tests.pr.suite2.infrastructure.security.provider.auth;


import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.exception.UserDeletedException;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.security.authentication.encoding.MessageDigestPasswordEncoder;
import su.reddot.infrastructure.security.authentication.encoding.PasswordEncoder;
import su.reddot.infrastructure.security.provider.auth.EmailAuthenticationProvider;
import su.reddot.infrastructure.security.token.EmailAuthenticationToken;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.BDDMockito.given;

@ActiveProfiles(profiles = AbstractSpringTest.testProfiles)
@SpringBootTest(classes = OskellyApplication.class)
@ExtendWith(SpringExtension.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class EmailAuthenticationProviderTest {

    private static final String NICKNAME = "user-test-email-authentication-provider";
    private static final String USER_EMAIL = "<EMAIL>";
    private static final String TEST_PASSWORD = "test-password";
    public static final String API_HASHED_PASSWORD = "api-hashed-password";

    @Autowired
    EmailAuthenticationProvider emailAuthenticationProvider;

    @Autowired
    UserRepository userRepository;
    @Autowired
    CommissionGridService commissionGridService;

    @MockBean
    MessageDigestPasswordEncoder passwordEncoder;

    @Test
    public void authenticateThrowsUserDeletedExceptionWhenUserDeleted() {

        given(passwordEncoder.encodePassword(anyString(), anyString()))
                .willReturn(API_HASHED_PASSWORD);

        User deletedUser = new User()
                .setNickname(NICKNAME)
                .setDeleteTime(ZonedDateTime.now())
                .setEmail(USER_EMAIL)
                .setUserType(User.UserType.SIMPLE_USER)
                .setChangeTime(LocalDateTime.now())
                .setApiHashedPassword(API_HASHED_PASSWORD)
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        userRepository.save(deletedUser);

        assertThatThrownBy(() -> emailAuthenticationProvider.authenticate(new EmailAuthenticationToken(USER_EMAIL, TEST_PASSWORD)))
                .isInstanceOf(UserDeletedException.class);

        userRepository.delete(deletedUser);
    }

}
