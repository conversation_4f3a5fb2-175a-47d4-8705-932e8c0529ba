package ru.oskelly.tests.pr.suite2.infrastructure.notificationDelivery;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.MockBeans;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestClientException;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.notification.NotificationRepository;
import su.reddot.domain.model.localization.SupportedLanguage;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.NotificationDeliveryResult;
import su.reddot.domain.service.dto.mailganer.MailganerTriggerDto;
import su.reddot.domain.service.task.ScheduledNotificationDeliveryTaskRunner;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.mailganer.MailganerClient;
import su.reddot.infrastructure.mailganer.MailganerTriggerService;
import su.reddot.infrastructure.mailganer.response.SendTriggerResponse;
import su.reddot.infrastructure.notificationDelivery.mailganer.MailganerNotificationTransport;
import su.reddot.infrastructure.notificationDelivery.mailganer.trigger.MailganerTriggerInfo;
import su.reddot.infrastructure.notificationDelivery.mailganer.trigger.sale.SaleRejectedMailganerTrigger;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.mock;
import static org.mockito.BDDMockito.never;
import static org.mockito.BDDMockito.then;
import static org.mockito.BDDMockito.times;
import static org.mockito.BDDMockito.willReturn;

@ExtendWith(SpringExtension.class)
@SpringBootTest(
        classes = {
                OskellyApplication.class
        },
        properties = {
                "app.notificationDelivery.mailganer.enabled=true",
                "app.push.with-testmode=false"
        }
)
@MockBeans({
        @MockBean(ScheduledNotificationDeliveryTaskRunner.class)
})
@ActiveProfiles(profiles = AbstractSpringTest.testProfiles)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class MailganerNotificationTransportTest {
    public static final long NOTIFICATION_ID = -1L;
    public static final String MAILGANER_EXCEPTION_MESSAGE = "Some exception";

    @MockBean
    MailganerClient mailganerClient;
    @MockBean
    NotificationRepository<Notification> notificationRepository;
    @SpyBean
    SaleRejectedMailganerTrigger saleRejectedMailganerTrigger;
    @Autowired
    MailganerTriggerService mailganerTriggerService;
    @Autowired
    MailganerNotificationTransport mailganerNotificationTransport;

    @Value("${internationalVersion}")
    private Boolean isInternationalVersion;
    @Autowired
    List<MailganerTriggerInfo> mailganerTriggerInfos;

    @Test
    public void deliverNotificationReturnsSkippedResultWhenNotificationDeleted() {
        given(notificationRepository.findById(anyLong()))
                .willReturn(Optional.empty());

        NotificationDeliveryResult notificationDeliveryResult = mailganerNotificationTransport.deliverNotification(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getType()).isEqualByComparingTo(NotificationDeliveryResult.ResultType.SKIPPED);
        assertThat(notificationDeliveryResult.getMessage()).isEqualTo("Уведомление было удалено");
        assertThat(notificationDeliveryResult.getNotificationId()).isEqualTo(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getChannel()).isEqualTo("mailganer");

        then(mailganerClient).should(never()).sendTrigger(any());


        Notification notification = mock(Notification.class);
        given(notification.isDeleted()).willReturn(true);
        given(notificationRepository.findById(anyLong()))
                .willReturn(Optional.of(notification));

        notificationDeliveryResult = mailganerNotificationTransport.deliverNotification(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getType()).isEqualByComparingTo(NotificationDeliveryResult.ResultType.SKIPPED);
        assertThat(notificationDeliveryResult.getMessage()).isEqualTo("Уведомление было удалено");
        assertThat(notificationDeliveryResult.getNotificationId()).isEqualTo(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getChannel()).isEqualTo("mailganer");
    }


    @Test
    public void deliverNotificationReturnsSkippedResultWhenNotificationIsRead() {
        Notification notification = mock(Notification.class);
        given(notification.isDeleted()).willReturn(false);
        given(notification.isRead()).willReturn(true);
        given(notificationRepository.findById(anyLong()))
                .willReturn(Optional.of(notification));

        NotificationDeliveryResult notificationDeliveryResult = mailganerNotificationTransport.deliverNotification(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getType()).isEqualByComparingTo(NotificationDeliveryResult.ResultType.SKIPPED);
        assertThat(notificationDeliveryResult.getMessage()).isEqualTo("Уведомление было прочитано");
        assertThat(notificationDeliveryResult.getNotificationId()).isEqualTo(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getChannel()).isEqualTo("mailganer");

        then(mailganerClient).should(never()).sendTrigger(any());
    }

    @Test
    public void deliverNotificationReturnsSkippedResultWhenActionCompleted() {
        Notification notification = mock(Notification.class);
        given(notification.isDeleted()).willReturn(false);
        given(notification.isRead()).willReturn(false);
        given(notification.isActionCompleted()).willReturn(true);
        given(notificationRepository.findById(anyLong()))
                .willReturn(Optional.of(notification));

        NotificationDeliveryResult notificationDeliveryResult = mailganerNotificationTransport.deliverNotification(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getType()).isEqualByComparingTo(NotificationDeliveryResult.ResultType.SKIPPED);
        assertThat(notificationDeliveryResult.getMessage()).isEqualTo("Уведомление было выполнено");
        assertThat(notificationDeliveryResult.getNotificationId()).isEqualTo(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getChannel()).isEqualTo("mailganer");

        then(mailganerClient).should(never()).sendTrigger(any());
    }

    @Test
    public void deliverNotificationReturnsSkippedResultWhenThereIsNoRegisteredUser() {
        Notification notification = mock(Notification.class);
        given(notification.isDeleted()).willReturn(false);
        given(notification.isRead()).willReturn(false);
        given(notification.isActionCompleted()).willReturn(false);
        given(notification.getUser()).willReturn(null);
        given(notification.getId()).willReturn(NOTIFICATION_ID);
        given(notificationRepository.findById(anyLong()))
                .willReturn(Optional.of(notification));

        NotificationDeliveryResult notificationDeliveryResult = mailganerNotificationTransport.deliverNotification(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getType()).isEqualByComparingTo(NotificationDeliveryResult.ResultType.SKIPPED);
        assertThat(notificationDeliveryResult.getMessage()).isEqualTo("Пользователь не указан");
        assertThat(notificationDeliveryResult.getNotificationId()).isEqualTo(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getChannel()).isEqualTo("mailganer");

        then(mailganerClient).should(never()).sendTrigger(any());
    }

    @Test
    public void deliverNotificationReturnsSkippedResultWhenUserWithoutEmail() {
        Notification notification = mock(Notification.class);
        given(notification.isDeleted()).willReturn(false);
        given(notification.isRead()).willReturn(false);
        given(notification.isActionCompleted()).willReturn(false);
        User user = new User();
        user.setEmail(null);
        given(notification.getUser()).willReturn(user);
        given(notification.getId()).willReturn(NOTIFICATION_ID);
        given(notificationRepository.findById(anyLong()))
                .willReturn(Optional.of(notification));

        NotificationDeliveryResult notificationDeliveryResult = mailganerNotificationTransport.deliverNotification(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getType()).isEqualByComparingTo(NotificationDeliveryResult.ResultType.SKIPPED);
        assertThat(notificationDeliveryResult.getMessage()).isEqualTo("Отсутствует email");
        assertThat(notificationDeliveryResult.getNotificationId()).isEqualTo(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getChannel()).isEqualTo("mailganer");

        then(mailganerClient).should(never()).sendTrigger(any());
    }


    @Test
    public void deliverNotificationReturnsSkippedResultWhenTriggerNotFound() {
        Notification notification = mock(Notification.class);
        given(notification.isDeleted()).willReturn(false);
        given(notification.isRead()).willReturn(false);
        given(notification.isActionCompleted()).willReturn(false);
        User user = new User();
        user.setEmail("<EMAIL>");
        given(notification.getUser()).willReturn(user);
        given(notification.getId()).willReturn(NOTIFICATION_ID);
        given(notification.getDtype()).willReturn("NonExistentTrigger");
        given(notificationRepository.findById(anyLong()))
                .willReturn(Optional.of(notification));

        NotificationDeliveryResult notificationDeliveryResult = mailganerNotificationTransport.deliverNotification(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getType()).isEqualByComparingTo(NotificationDeliveryResult.ResultType.SKIPPED);
        assertThat(notificationDeliveryResult.getMessage()).isEqualTo("Отсутствует триггер");
        assertThat(notificationDeliveryResult.getNotificationId()).isEqualTo(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getChannel()).isEqualTo("mailganer");

        then(mailganerClient).should(never()).sendTrigger(any());
    }

    @Test
    public void deliverNotificationReturnsFailedResultOnMailganerErrorResponse() {
        Notification notification = mock(Notification.class);
        given(notification.isDeleted()).willReturn(false);
        given(notification.isRead()).willReturn(false);
        given(notification.isActionCompleted()).willReturn(false);
        User user = new User();
        user.setEmail("<EMAIL>");
        given(notification.getUser()).willReturn(user);
        given(notification.getId()).willReturn(NOTIFICATION_ID);
        given(notification.getDtype()).willReturn("SaleRejectedNotification");

        willReturn(new LinkedMultiValueMap<>()).given(saleRejectedMailganerTrigger).getAdditionalParams(any());

        given(notificationRepository.findById(anyLong()))
                .willReturn(Optional.of(notification));

        SendTriggerResponse sendTriggerResponse = new SendTriggerResponse();
        sendTriggerResponse.setError("I'm a baaaaaaaad request");
        ResponseEntity<SendTriggerResponse> responseEntity = new ResponseEntity<>(sendTriggerResponse, HttpStatus.BAD_REQUEST);
        given(mailganerClient.sendTrigger(any()))
                .willReturn(responseEntity);

        NotificationDeliveryResult notificationDeliveryResult = mailganerNotificationTransport.deliverNotification(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getType()).isEqualByComparingTo(NotificationDeliveryResult.ResultType.FAILED);
        assertThat(notificationDeliveryResult.getMessage()).contains("I'm a baaaaaaaad request");
        assertThat(notificationDeliveryResult.getMetadata()).isEqualTo("400 BAD_REQUEST");
        assertThat(notificationDeliveryResult.getNotificationId()).isEqualTo(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getChannel()).isEqualTo("mailganer");

        sendTriggerResponse.setError("Some internal mailganer error");
        responseEntity = new ResponseEntity<>(sendTriggerResponse, HttpStatus.OK);
        given(mailganerClient.sendTrigger(any()))
                .willReturn(responseEntity);

        notificationDeliveryResult = mailganerNotificationTransport.deliverNotification(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getType()).isEqualByComparingTo(NotificationDeliveryResult.ResultType.FAILED);
        assertThat(notificationDeliveryResult.getMessage()).contains("Some internal mailganer error");
        assertThat(notificationDeliveryResult.getMetadata()).isEqualTo("200 OK");
        assertThat(notificationDeliveryResult.getNotificationId()).isEqualTo(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getChannel()).isEqualTo("mailganer");
    }


    @Test
    public void deliverNotificationReturnsFailedResultOnMailganerClientException() {
        Notification notification = mock(Notification.class);
        given(notification.isDeleted()).willReturn(false);
        given(notification.isRead()).willReturn(false);
        given(notification.isActionCompleted()).willReturn(false);
        User user = new User();
        user.setEmail("<EMAIL>");
        given(notification.getUser()).willReturn(user);
        given(notification.getId()).willReturn(NOTIFICATION_ID);
        given(notification.getDtype()).willReturn("SaleRejectedNotification");

        willReturn(new LinkedMultiValueMap<>()).given(saleRejectedMailganerTrigger).getAdditionalParams(any());

        given(notificationRepository.findById(anyLong()))
                .willReturn(Optional.of(notification));

        given(mailganerClient.sendTrigger(any()))
                .willThrow(new RestClientException(MAILGANER_EXCEPTION_MESSAGE));

        NotificationDeliveryResult notificationDeliveryResult = mailganerNotificationTransport.deliverNotification(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getType()).isEqualByComparingTo(NotificationDeliveryResult.ResultType.FAILED);
        assertThat(notificationDeliveryResult.getMessage()).isEqualTo(MAILGANER_EXCEPTION_MESSAGE);
        assertThat(notificationDeliveryResult.getNotificationId()).isEqualTo(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getChannel()).isEqualTo("mailganer");

        then(mailganerClient).should(times(1)).sendTrigger(any());
    }

    /**
     * Check that all mailgan triggers are added to mailgan_trigger table, have trigger id and have current locale
     */
    @Test
    public void checkThatTriggerHasId(){
        assertThat(mailganerTriggerInfos.size()).isNotZero();
        mailganerTriggerInfos.forEach(mt -> checkThatTriggerExistsAndHaveTriggerId(mt.getTriggerName(null)));
    }

    private void checkThatTriggerExistsAndHaveTriggerId(String triggerName){
        MailganerTriggerDto dto = mailganerTriggerService.findTriggerByTrigger(triggerName);
        assertThat(dto).isNotNull(); //Trigger exists
        assertThat(dto.getTriggerId()).isNotNull(); //Trigger ID isn't empty
//        Теперь можно использовать 0 как Trigger ID в таком случае отправка будет пропущена
//        assertThat(dto.getTriggerId()).isNotZero(); //Trigger ID isn't 0

        SupportedLanguage supportedLanguage = isInternationalVersion ? SupportedLanguage.EN: SupportedLanguage.RU;
        assertThat(dto.getSupportedLanguage()).isEqualTo(supportedLanguage); //Trigger supports current locale
    }
}
