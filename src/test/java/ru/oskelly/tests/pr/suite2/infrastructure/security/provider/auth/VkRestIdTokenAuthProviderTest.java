package ru.oskelly.tests.pr.suite2.infrastructure.security.provider.auth;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.exception.UserDeletedException;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.security.provider.auth.VkRestIdTokenAuthProvider;
import su.reddot.infrastructure.security.token.VkRestIdToken;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;

import static org.assertj.core.api.Assertions.assertThatThrownBy;

@ActiveProfiles(profiles = AbstractSpringTest.testProfiles)
@SpringBootTest(classes = OskellyApplication.class)
@ExtendWith(SpringExtension.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class VkRestIdTokenAuthProviderTest {

    private static final String NICKNAME = "user-vk-rest-id-auth-provider";
    private static final String VK_REST_ID = "vk-rest-id";
    private static final String VK_UUID = "vk-uuid";

    @Autowired
    UserRepository userRepository;
    @Autowired
    CommissionGridService commissionGridService;
    @Autowired
    VkRestIdTokenAuthProvider vkRestIdTokenAuthProvider;


    @Test
    public void authenticateThrowsUserDeletedExceptionWhenUserDeleted() {
        User deletedUser = new User()
                .setNickname(NICKNAME)
                .setDeleteTime(ZonedDateTime.now())
                .setRestVkId(VK_REST_ID)
                .setUserVkUuid(VK_UUID)
                .setUserType(User.UserType.SIMPLE_USER)
                .setChangeTime(LocalDateTime.now())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        userRepository.save(deletedUser);

        assertThatThrownBy(() -> vkRestIdTokenAuthProvider.authenticate(new VkRestIdToken(VK_REST_ID, VK_UUID)))
                .isInstanceOf(UserDeletedException.class);

        userRepository.delete(deletedUser);
    }
}
