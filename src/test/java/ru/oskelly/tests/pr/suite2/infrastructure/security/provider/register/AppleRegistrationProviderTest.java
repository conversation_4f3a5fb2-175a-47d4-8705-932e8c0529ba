package ru.oskelly.tests.pr.suite2.infrastructure.security.provider.register;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.MockBeans;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.security.SecurityService;
import su.reddot.infrastructure.security.oauth.AppleClient;
import su.reddot.infrastructure.security.provider.register.AppleRegistrationProvider;
import su.reddot.infrastructure.security.provider.register.OAuthRegistrationResult;
import su.reddot.infrastructure.security.view.OAuthRegistrationRequest;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {OskellyApplication.class})
@ActiveProfiles(profiles = AbstractSpringTest.testProfiles)
@MockBeans({
        @MockBean(SecurityService.class),
        @MockBean(UserService.class)
})
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class AppleRegistrationProviderTest {
    private static final String TEST_REST_APPLE_ID = "test_rest_apple_id";
    private static final String TEST_APPLE_KID = "test_apple_kid";
    private static final String TEST_AUTHORIZATION_CODE = "test_authorization_code";
    private static final String TEST_NICKNAME = "apple_user_nickname";
    private static final String TEST_EMAIL =  TEST_NICKNAME + "@email.com";
    @Autowired
    private UserRepository userRepository;

    @MockBean
    private AppleClient appleClient;

    @Autowired
    AppleRegistrationProvider appleRegistrationProvider;

    @Test
    public void registerGeneratesNicknameFromEmail() {
        OAuthRegistrationRequest registrationRequest = new OAuthRegistrationRequest(
                null,
                null,
                TEST_APPLE_KID,
                TEST_AUTHORIZATION_CODE,
                null,
                null,
                null,
                null,
                true
        );
        doReturn(getAppleClientResponseMock(TEST_REST_APPLE_ID, TEST_EMAIL)).when(appleClient).performUserRequest(anyString(), anyString(), anyBoolean());

        OAuthRegistrationResult register = appleRegistrationProvider.register(registrationRequest);

        Optional<User> user = userRepository.findById(register.getUserId());
        assertThat(user).isPresent();
        assertThat(user.get().getNickname()).isEqualTo(TEST_NICKNAME);

        userRepository.deleteById(register.getUserId());
    }

    // Неизвестно сколько раз до этого теста будет вызван sequence для никнеймов, поэтому проверяем тут исключительно упорядоченность
    @Test
    public void registerGeneratesIncrementingNicknameIfGeneratedNicknameExists() {
        OAuthRegistrationRequest registrationRequest = new OAuthRegistrationRequest(
                null,
                null,
                "apple_kid_1",
                TEST_AUTHORIZATION_CODE,
                null,
                TEST_EMAIL,
                null,
                null,
                true
        );

        String secondEmail = TEST_NICKNAME + "@other_email.com";
        String thirdEmail = TEST_NICKNAME + "@and_another_email.com";

        doReturn(getAppleClientResponseMock(TEST_REST_APPLE_ID, TEST_EMAIL))
                .doReturn(getAppleClientResponseMock(TEST_REST_APPLE_ID + 2, secondEmail))
                .doReturn(getAppleClientResponseMock(TEST_REST_APPLE_ID + 3, thirdEmail))
                .when(appleClient)
                .performUserRequest(anyString(), anyString(), anyBoolean());

        OAuthRegistrationResult register = appleRegistrationProvider.register(registrationRequest);

        registrationRequest.setEmail(secondEmail);
        registrationRequest.setAppleKid("apple_kid_2");
        OAuthRegistrationResult secondRegister = appleRegistrationProvider.register(registrationRequest);

        registrationRequest.setEmail(thirdEmail);
        registrationRequest.setAppleKid("apple_kid_3");
        OAuthRegistrationResult thirdRegister = appleRegistrationProvider.register(registrationRequest);

        Optional<User> secondUser = userRepository.findById(secondRegister.getUserId());
        assertThat(secondUser).isPresent();
        assertThat(secondUser.get().getNickname()).matches(TEST_NICKNAME + "#\\d+");

        Optional<User> thirdUser = userRepository.findById(thirdRegister.getUserId());
        assertThat(thirdUser).isPresent();
        assertThat(thirdUser.get().getNickname()).matches(TEST_NICKNAME + "#\\d+");

        Long secondId = Long.parseLong(secondUser.get().getNickname().split("#")[1]);
        Long thirdId = Long.parseLong(thirdUser.get().getNickname().split("#")[1]);
        assertThat(thirdId).isGreaterThan(secondId);

        userRepository.deleteById(register.getUserId());
        userRepository.deleteById(secondRegister.getUserId());
        userRepository.deleteById(thirdRegister.getUserId());
    }


    private Map<String, Object> getAppleClientResponseMock(String userId, String email) {
        Map<String, Object> response = new HashMap<>();
        response.put("appleUserId", userId);
        response.put("appleUserEmail", email);
        return response;
    }
}
