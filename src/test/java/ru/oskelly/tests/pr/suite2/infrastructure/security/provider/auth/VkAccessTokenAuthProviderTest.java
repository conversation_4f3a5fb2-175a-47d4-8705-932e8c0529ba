package ru.oskelly.tests.pr.suite2.infrastructure.security.provider.auth;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.exception.UserDeletedException;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.security.oauth.VKClient;
import su.reddot.infrastructure.security.provider.auth.VkAccessTokenAuthProvider;
import su.reddot.infrastructure.security.token.VkAccessToken;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;

@ActiveProfiles(profiles = AbstractSpringTest.testProfiles)
@SpringBootTest(classes = OskellyApplication.class)
@ExtendWith(SpringExtension.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class VkAccessTokenAuthProviderTest {
    private static final String NICKNAME = "user-vk-access-token-auth-provider";
    private static final String VK_ACCESS_TOKEN = "vk-access-token";
    private static final String VK_REST_ID = "vk-rest-id";

    @Autowired
    VkAccessTokenAuthProvider vkAccessTokenAuthProvider;
    @Autowired
    UserRepository userRepository;
    @Autowired
    CommissionGridService commissionGridService;

    @MockBean
    VKClient vkClient;

    @Test
    public void authenticateThrowsUserDeletedExceptionWhenUserDeleted() {

        given(vkClient.performUserRequest(eq(VK_ACCESS_TOKEN), eq("id")))
                .willReturn(Collections.singletonMap("id", VK_REST_ID));

        User deletedUser = new User()
                .setNickname(NICKNAME)
                .setDeleteTime(ZonedDateTime.now())
                .setRestVkId(VK_REST_ID)
                .setUserType(User.UserType.SIMPLE_USER)
                .setChangeTime(LocalDateTime.now())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        userRepository.save(deletedUser);

        assertThatThrownBy(() -> vkAccessTokenAuthProvider.authenticate(new VkAccessToken(VK_ACCESS_TOKEN)))
                .isInstanceOf(UserDeletedException.class);

        userRepository.delete(deletedUser);
    }
}
