package ru.oskelly.tests.pr.suite3.presentation.api.v2;

import com.amazonaws.services.s3.AmazonS3;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.NonNull;
import org.apache.commons.lang3.RandomStringUtils;
import org.assertj.core.api.Assertions;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.transaction.TestTransaction;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.pr.suite5.presentation.api.v2.ValidationFailedResponse;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.AccountTestSupport;
import su.reddot.component.TestApiConfiguration;
import su.reddot.component.TestApplicationEventListener;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.activity.ActivityTestRepository;
import su.reddot.domain.dao.address.AddressRepository;
import su.reddot.domain.dao.addressendpoint.AddressEndpointRepository;
import su.reddot.domain.dao.notification.NotificationGroupUserBindingRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.event.UserSubscriptionEvent;
import su.reddot.domain.model.Brand;
import su.reddot.domain.model.activity.Activity;
import su.reddot.domain.model.address.Address;
import su.reddot.domain.model.addressendpoint.AddressEndpoint;
import su.reddot.domain.model.counterparty.CountryCounterpartyType;
import su.reddot.domain.model.notification.NotificationGroup;
import su.reddot.domain.model.notification.UserSubscriptionType;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.user.PasswordResetToken;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.address.AddressService;
import su.reddot.domain.service.address.CountryService;
import su.reddot.domain.service.dto.AccountCredsDTO;
import su.reddot.domain.service.dto.AccountDTO;
import su.reddot.domain.service.dto.AddressDTO;
import su.reddot.domain.service.dto.AddressEndpointDTO;
import su.reddot.domain.service.dto.BubblesDTO;
import su.reddot.domain.service.dto.CounterpartyDTO;
import su.reddot.domain.service.dto.CounterpartyDTO.CounterpartyType;
import su.reddot.domain.service.dto.CountryDTO;
import su.reddot.domain.service.dto.notification.NotificationGroupDTO;
import su.reddot.domain.service.dto.userfile.UserfileDTO;
import su.reddot.domain.service.following.FollowingService;
import su.reddot.domain.service.like.LikeService;
import su.reddot.domain.service.notification.NotificationService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.configuration.amqp.UserAuthEventRabbitSender;
import su.reddot.infrastructure.security.SecurityService;
import su.reddot.infrastructure.security.oauth.FacebookClient;
import su.reddot.infrastructure.security.provider.auth.FbAccessTokenAuthProvider;
import su.reddot.infrastructure.security.provider.register.FacebookRegistrationProvider;
import su.reddot.infrastructure.security.provider.register.FacebookRegistrationResult;
import su.reddot.infrastructure.security.provider.register.OAuthRegistrationResult;
import su.reddot.infrastructure.security.view.OAuthRegistrationRequest;
import su.reddot.infrastructure.security.view.SocialLoginRequest;
import su.reddot.infrastructure.security.view.SocialLoginResponse;
import su.reddot.infrastructure.util.CallInTransaction;
import su.reddot.infrastructure.util.Utils;
import su.reddot.presentation.api.v2.Api2Response;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.doAnswer;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_03)
public class AccountControllerV2Test extends AbstractSpringTest {
	public static final String FIRST_PASSWORD = "abcdef1235%";
	@Value("${test.api.user-email}")
    private String email;
    @Value("${test.api.user-password}")
    private String password;
	@Value("${test.api.user-id}")
	private Long userId;
	@Value("${test.api.user-nickname}")
	private String userNickname;
	@Value("${test.api.user2-id}")
	private Long user2Id;
	@Value("${test.api.user2-nickname}")
	private String user2Nickname;
	@Value("${test.api.address-endpoint-test-order-id}")
	private Long orderId;

	/** email пользователя с MASTER_USER authority */
	@Value("${app.master.user-email}")
	private String masterUserEmail;

	/** Пароль пользователя с MASTER_USER authority */
	@Value("${app.master.user-password}")
	private String masterUserPass;

	@MockBean
	private FacebookClient facebookClient;

	@Autowired
	private AddressService addressService;
	@Autowired
	private UserService userService;
	@Autowired
	FollowingService followingService;
	@Autowired
	NotificationService notificationService;
	@Autowired
	LikeService likeService;
	@Autowired
	CountryService countryService;
	@Autowired
	ModelMapper modelMapper;

	@Autowired
	private AddressRepository addressRepository;
	@Autowired
	private AddressEndpointRepository addressEndpointRepository;
	@Autowired
	private OrderRepository orderRepository;
	@Autowired
	private ActivityTestRepository<Activity> activityTestRepository;
	@Autowired
	private NotificationGroupUserBindingRepository notificationGroupUserBindingRepository;
	@Autowired
	private UserRepository userRepository;
	@Autowired
	private AccountTestSupport accountTestSupport;
	@Autowired
	private TestApiConfiguration testApiConfiguration;
	@Autowired
	private FbAccessTokenAuthProvider fbRestAuthProvider;
	@Autowired
	private FacebookRegistrationProvider fbRegistrationProvider;
	@Autowired
	private TestApplicationEventListener testApplicationEventListener;

	@Autowired
	private AmazonS3 amazonS3client;

	@Autowired
	private CallInTransaction callInTransaction;

	@MockBean
	UserAuthEventRabbitSender userAuthEventRabbitSender;

	@Value("${test.temp-dir}")
	private String tmpDirPath;

	private static String country = "Россия";
	private static String region = "Тамбовская обл.";
	private static String city = "г.Уварово";
	private static String cityFiasId = "5a54fa19-c17f-40a8-9d81-0fc826cba781";
	private static String zipCode = "7656433";
	private static String address = "ул.Коммунистическая, д.1А";
	private static String phone = "+79202341740";
	private static String phone2 = "+79204771677";
	private static String wrongPhone = "+79200";

	private static LocalDateTime birthDate = LocalDateTime.of(1982, 6, 16, 0, 0);
	private static LocalDateTime anotherBirthDate = LocalDateTime.now().minusYears(50);

	private static String cntPassport = "3648 436261";
	private static String cntInnPhys = "*********900";
	private static String cntInnIp = "*********900";
	private static String cntInnJur = "*********9";
	private static String cntOGRN = "*********9011";
	private static String cntOGRNIP = "*********901234";
	private static String cntKPP = "*********";
	private static String cntCompanyForm = "ПАО";
	private static String cntCompanyName = "Оскелли Груп";
	private static String cntBik = "*********";
	private static String cntCorrespondingAccount = "*********90123456789";
	private static String cntPaymentAccount = "98765432109876543211";
	private static String cntFirstName = "Дмитрий";
	private static String cntPatronymicName = "Иванович";
	private static String cntLastName = "Белых";

	private static final String CNT_IBAN = "********";
	private static final String CNT_SWIFT_CODE = "********";
	private static final String CNT_ROUTING = "********";

	private static ZonedDateTime cardExpireTimeInZone0 = ZonedDateTime.of(2024, 4, 30, 20, 59, 59, 0, ZoneId.of("UTC"));
	private static ZonedDateTime cardExpireTimeInZone3 = ZonedDateTime.of(2024, 4, 30, 23, 59, 59, 0, ZoneId.of("Europe/Moscow"));

	private static Long counterpartyId;
	private static Long counterparty2Id;
	private static List<Long> cleanCounterpartiesIDs = new ArrayList<>();

	private static Long addressEndpointId;
	private static Long addressEndpoint2Id;

	//Будем регистрировать пользователя и сохранять сюда для последующего удачения
	private static User newUser;

	//Клиент для работы с существующим тестовым пользователем
    static ApiV2Client apiV2Client;

    //Клиент для работы с регистрацией
	static ApiV2Client registrationApiV2Client;

	/** Клиент с кредами под пользователем с MASTER_USER authority */
	static ApiV2Client apiV2MasterClient;

	//Some tests with JSON data parse
	private ObjectMapper objectMapper;

    @BeforeEach
    public void initialize() {
		if (Objects.isNull(objectMapper)) {
			objectMapper = new ObjectMapper();
			objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
			objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			objectMapper.registerModule(new JavaTimeModule()); // to load ZonedDateTime from JSON
		}
		if(apiV2Client == null){
        	apiV2Client = new ApiV2Client(email, password);
        	User user = getUser();
        	if(!user.getNickname().equals(userNickname)){
        		user.setNickname(userNickname);
        		userRepository.save(user);
	        }
	        User user2 = getUser2();
	        if(!user2.getNickname().equals(user2Nickname)){
		        user2.setNickname(user2Nickname);
		        userRepository.save(user2);
	        }
        }
		if(apiV2MasterClient == null){
			apiV2MasterClient = new ApiV2Client(masterUserEmail, masterUserPass);
		}
		MockitoAnnotations.initMocks(this);
		fbRegistrationProvider.setClient(facebookClient);

		amazonS3client.createBucket("oskelly");
    }

	@AfterEach
	public void tearDown() {
		cleanCounterpartiesIDs.forEach(this::deleteCounterpartySeccessfull);
		cleanCounterpartiesIDs.clear();
	}

	private User getUser(){
		return userService.getUserById(userId).orElse(null);
	}
	private User getUser2(){
		return userService.getUserById(user2Id).orElse(null);
	}

	////// РЕГИСТРАЦИЯ ///////////////////

	@Test
	public void _00_0_registerValidation(){
		registrationApiV2Client = new ApiV2Client(null, null);

		Map<String, String> errors = Utils.mapOf(new String[][]{
				{ "registerEmail", "Не указан email" },
				{ "registerPassword", "Не указан пароль" },
				{ "registerConfirmPassword", "Не указан пароль подтверждения" },
				{ "registerNickname", "Не указан псевдоним" },
		});

		UserService.EmailRegistrationRequest request = new UserService.EmailRegistrationRequest();

		//Отправляем пустой запрос и получаем ошибки вадидации о незаполненных полях
		assertRegisterValidationFailed(request, errors);

		//E-mail заполнен, ошибка исчезла
		request.setRegisterEmail(RandomStringUtils.randomAlphabetic(8) + "@mail.ru");
		errors.remove("registerEmail");
		assertRegisterValidationFailed(request, errors);

		//Password заполнен, ошибка исчезла
		request.setRegisterPassword(RandomStringUtils.randomAlphabetic(8));
		errors.remove("registerPassword");
		assertRegisterValidationFailed(request, errors);

		//ConfirmPassword заполнен, ошибка исчезла
		request.setRegisterConfirmPassword(RandomStringUtils.randomAlphabetic(8));
		errors.remove("registerConfirmPassword");
		assertRegisterValidationFailed(request, errors);

		//Nickname заполнен, ошибка исчезла, но формат e-mail неверный и слишком короткий пароль
		request.setRegisterNickname(RandomStringUtils.randomAlphabetic(8).toLowerCase());
		request.setRegisterEmail(RandomStringUtils.randomAlphabetic(8));
		request.setRegisterPassword(RandomStringUtils.randomAlphabetic(2));
		errors.remove("registerNickname");
		errors.put("registerEmail", "Неверный формат email");
		errors.put("registerPassword", "Слишком короткий пароль");
		assertRegisterValidationFailed(request, errors);

		//Формат e-mail учтен, пароль имеет корректную длину, но подтверждающий пароль не соответствует
		request.setRegisterEmail(RandomStringUtils.randomAlphabetic(8) + "@mail.ru");
		request.setRegisterPassword(RandomStringUtils.randomAlphabetic(5));
		errors.remove("registerEmail");
		errors.remove("registerPassword");
		errors.put("registerConfirmPassword", "Пароли не совпадают");
		assertRegisterValidationFailed(request, errors);

		//Никнейм слишком короткий
		request.setRegisterConfirmPassword(request.getRegisterPassword());
		request.setRegisterNickname("__");
		errors.remove("registerConfirmPassword");
		errors.put("registerNickname", "Минимальная допустимая длина: 3");
		assertRegisterValidationFailed(request, errors);

		//Никнейм слишком длинный
		request.setRegisterNickname(RandomStringUtils.randomAlphabetic(17).toLowerCase());
		errors.put("registerNickname", "Максимальная допустимая длина: 15");
		assertRegisterValidationFailed(request, errors);

		//Никнейм не соответствует формату (недопустимый символ)
		request.setRegisterNickname(RandomStringUtils.randomAlphabetic(7).toUpperCase() + "(");
		errors.put("registerNickname", "Имеются недопустимые символы. Допускаются только латинские буквы, цифры, символы подчеркивания и тире");
		assertRegisterValidationFailed(request, errors);

		//Никнейм не соответствует формату (пробел)
		request.setRegisterNickname(RandomStringUtils.randomAlphabetic(4).toLowerCase() + " ");
		errors.put("registerNickname", "Имеются недопустимые символы. Допускаются только латинские буквы, цифры, символы подчеркивания и тире");
		assertRegisterValidationFailed(request, errors);

		//Никнейм не соответствует формату (русские символы)
		request.setRegisterNickname(RandomStringUtils.randomAlphabetic(4).toLowerCase() + "русский");
		errors.put("registerNickname", "Имеются недопустимые символы. Допускаются только латинские буквы, цифры, символы подчеркивания и тире");
		assertRegisterValidationFailed(request, errors);

		//Никнейм занят
		request.setRegisterNickname(getUser().getNickname());
		errors.put("registerNickname", "Псевдоним уже зарегистрирован в системе");
		assertRegisterValidationFailed(request, errors);

		//E-mail занят
		request.setRegisterEmail(getUser().getEmail());
		request.setRegisterNickname(RandomStringUtils.randomAlphabetic(8).toLowerCase());
		errors.remove("registerNickname");
		errors.put("registerEmail", "Пользователь уже зарегистрирован в системе");
		assertRegisterValidationFailed(request, errors);
	}

	@Test
	public void _00_1_0_1_registerOK(){
    	String password = FIRST_PASSWORD;

		UserService.EmailRegistrationRequest request = new UserService.EmailRegistrationRequest()
				.setRegisterEmail(RandomStringUtils.randomAlphabetic(8) + "@mail.ru")
				.setRegisterNickname(RandomStringUtils.randomAlphabetic(8).toLowerCase())
				.setRegisterPassword(password)
				.setRegisterConfirmPassword(password)
				.setRegisterPhone("+7" + RandomStringUtils.randomNumeric(10));


		//Регистрируем пользователя
		Long newUserId = accountTestSupport.registerUser(registrationApiV2Client, request);

		//Получаем нового пользователя из б.д.
		newUser = userRepository.findById(newUserId).orElse(null);
		assertNotNull(newUser);

		//Сверяем его поля
		assertTrue(request.getRegisterEmail().equalsIgnoreCase(newUser.getEmail()));
		assertEquals(request.getRegisterNickname(), newUser.getNickname());
		assertEquals(request.getRegisterPhone(), newUser.getPhone());

		//Убеждаемся, что пользователь авторизован. Он может увидеть свой аккаунт.
		AccountDTO accountDTO = getAccountSuccessful(registrationApiV2Client, false);
		assertEquals(newUserId, accountDTO.getId());

		//Выходим
		registrationApiV2Client.logout();

		//Теперь пользователь не видит свой аккаунт
		ResponseEntity<String> responseForbidden = registrationApiV2Client.request(accountTestSupport.getServiceUrl(), null, HttpMethod.GET, null, String.class, false);
		assertSame(HttpStatus.FORBIDDEN, responseForbidden.getStatusCode());

		//Авторизуемся повторно при получении аккаунта. Авторизация удалась, аккаунт виден
		registrationApiV2Client = new ApiV2Client(request.getRegisterEmail(), request.getRegisterPassword());
		accountDTO = getAccountSuccessful(registrationApiV2Client, true);
		assertEquals(newUserId, accountDTO.getId());
	}

	@Test
	public void _00_1_0_2_loginWithAuthSuccess() {
		ApiV2Client client = new ApiV2Client(null, null);
		String email = newUser.getEmail();
		MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
		params.add("email", email);
		params.add("password", TestUtils.getApiHashedPassword(FIRST_PASSWORD, email));
		long userId = accountTestSupport.authSuccessful(client, params);
		assertEquals(newUser.getId(), (Long)userId);

		verify(userAuthEventRabbitSender, times(1)).processEvent(any());
	}


	@Test
	public void _00_1_1_registerWithoutConsentSetsNotificationGroups(){
		String password = RandomStringUtils.randomAlphabetic(8);

		UserService.EmailRegistrationRequest request = new UserService.EmailRegistrationRequest()
				.setRegisterEmail(RandomStringUtils.randomAlphabetic(8) + "@mail.ru")
				.setRegisterNickname(RandomStringUtils.randomAlphabetic(8).toLowerCase())
				.setRegisterPassword(password)
				.setRegisterConfirmPassword(password)
				.setRegisterPhone("+7" + RandomStringUtils.randomNumeric(10))
				.setSubscriptionApprove(false);


		//Регистрируем пользователя
		Long newUserId = accountTestSupport.registerUser(registrationApiV2Client, request);

		//Получаем нового пользователя из б.д.
		User registeredUser = userRepository.findById(newUserId).orElse(null);
		assertNotNull(registeredUser);

		ApiV2Client apiV2Client = new ApiV2Client(request.getRegisterEmail(), request.getRegisterPassword());

		ResponseEntity<Api2Response<List<NotificationGroupDTO>>> getEmailGroupsResponse = apiV2Client.request(accountTestSupport.getEmailGroupsUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<NotificationGroupDTO>>>() {}, true);
		List<NotificationGroupDTO> emailGroups = getEmailGroupsResponse.getBody().getData();
		Assertions.assertThat(emailGroups).noneMatch(NotificationGroupDTO::getIsSelected);

		ResponseEntity<Api2Response<List<NotificationGroupDTO>>> getPushGroupsResponse = apiV2Client.request(accountTestSupport.getPushGroupsUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<NotificationGroupDTO>>>() {}, true);
		List<NotificationGroupDTO> pushGroups = getPushGroupsResponse.getBody().getData();
		Assertions.assertThat(pushGroups).hasSize(4).allMatch(NotificationGroupDTO::getIsSelected);

		jdbcTemplate.execute("DELETE FROM public.device where public.device.user_id = " + registeredUser.getId());
		userRepository.delete(registeredUser);
	}

	@Test
	public void _00_2_0_socialRegisterValidation(){
		//Используем новый клиент без параметров авторизации
		registrationApiV2Client = new ApiV2Client(null, null);

		OAuthRegistrationRequest request = null;

		//Отправляем пустой запрос (NULL) и получаем ошибку регистрации org.springframework.http.converter.HttpMessageNotReadableException
		//с сообщением "Required request body is missing..."
		assertSocialRegisterReturnsFailed(request, "Required request body is missing");

		request = new OAuthRegistrationRequest();

		//Отправляем пустой запрос {} и получаем ошибку регистрации su.reddot.domain.exception.SocialRegistrationFailedException
		//с сообщением "Не удалось зарегистрировать пользователя. Передайте следующие данные: ['fb_token'] или ['vk_token' и 'vk_user_id']"
		assertSocialRegisterReturnsFailed(request, "Не удалось зарегистрировать пользователя. Передайте следующие данные: ['fb_token'] или ['vk_token' и 'vk_user_id'] или ['apple_kid' и 'apple_authorization_code']");

		//Отправляем в запросе e-mail неверного формата и получаем ошибку валидации
		request.setEmail("dddd__rr");
		Map<String, String> errors = Utils.mapOf(new String[][]{
				{ "email", "Неверный формат email" },
		});
		assertSocialRegisterValidationFailed(request, errors);
	}

	@Test
	public void _00_2_1_socialRegisterFacebookOK(){
		String fbToken = "fb_access_token";
		String email = "<EMAIL>";
		String nickname = "nickname_oskelly";
		String restFacebookId = "123";
		String refCode = "123";

		Mockito.when(facebookClient.performUserRequest(fbToken, "id,email,name,picture.type(square).width(300)")).thenReturn(new HashMap<String, Object>(){{put("id", restFacebookId);}});

		//Удаляем из базы пользователей с таким email/nickname
		jdbcTemplate.execute(String.format("DELETE FROM public.user WHERE email='%s' OR nickname='%s'", email, nickname));

		//Обнуляем в базе пользователей совпадающие rest_facebook_id/user_fb_uuid
		jdbcTemplate.execute(String.format("UPDATE public.user SET rest_facebook_id=NULL, user_fb_uuid=NULL WHERE rest_facebook_id='%s'", restFacebookId));

		//Используем новый клиент без параметров авторизации
		registrationApiV2Client = new ApiV2Client(null, null);

		OAuthRegistrationRequest request = new OAuthRegistrationRequest(fbToken, null, null, null, null, email, nickname, refCode, true);
		request.setEmail(email);
		request.setNickname(nickname);
		request.setStylistRef(refCode);

		FacebookRegistrationResult registrationResult = (FacebookRegistrationResult) socialRegisterSuccess(request, FacebookRegistrationResult.class);

		assertNotNull(registrationResult.getUserId());
		assertEquals(restFacebookId, registrationResult.getFacebookId());
		assertNotNull(registrationResult.getFbUuid());
	}

	@Test
	public void _00_3_0_socialLoginValidation(){
		//Используем новый клиент без параметров авторизации
		registrationApiV2Client = new ApiV2Client(null, null);

		SocialLoginRequest request = null;

		//Отправляем пустой запрос (NULL) и получаем ошибку авторизации org.springframework.http.converter.HttpMessageNotReadableException
		//с сообщением "Required request body is missing..."
		accountTestSupport.assertSocialLoginReturnsFailed(registrationApiV2Client, request, "Required request body is missing");

		request = new SocialLoginRequest(null, null, null, null, null, null);

		//Отправляем пустой запрос {} и получаем ошибку авторизации su.reddot.domain.exception.SocialLoginFailedException
		//с сообщением "Не удалось авторизовать пользователя. Передайте следующие данные: fb_token или vk_token"
		accountTestSupport.assertSocialLoginReturnsFailed(registrationApiV2Client, request, "Не удалось авторизовать пользователя. Передайте следующие данные: fb_token или vk_token или (apple_kid и apple_authorization_code)");
	}

	/**
	 * Успешная авторизация через фейсбук
	 */
	@Test
	public void _00_4_0_socialLoginFacebookAccessTokenAndFacebookIdOK(){
		//Используем новый клиент без параметров авторизации
		registrationApiV2Client = new ApiV2Client(null, null);

		String fbToken = "fb_token";
		String restFacebookId = "123";
		String userFacebookUuid = "123_uuid";

		//Обнуляем в базе пользователей совпадающие rest_facebook_id/user_fb_uuid
		jdbcTemplate.execute(String.format("UPDATE public.user SET rest_facebook_id=NULL, user_fb_uuid=NULL WHERE rest_facebook_id='%s' OR user_fb_uuid='%s'", restFacebookId, userFacebookUuid));

		User user = userRepository.findById(userId).orElse(null);
		user.setRestFacebookId(restFacebookId);
		user.setUserFbUuid(userFacebookUuid);
		userRepository.save(user);

		Mockito.when(facebookClient.performUserRequest(fbToken, "id")).thenReturn(new HashMap<String, Object>(){{put("id", restFacebookId);}});

		//Авторизуемся по токену
		SocialLoginRequest request = new SocialLoginRequest(fbToken, null, null, null, null, null);
		SocialLoginResponse response = accountTestSupport.socialLoginSuccessful(registrationApiV2Client, request);

		String responseRestFacebookId = response.getRestFacebookId();
		String userFbUuid = response.getUserFbUuid();
		assertEquals(restFacebookId, responseRestFacebookId);
		assertEquals(userFacebookUuid, userFbUuid);

		//Авторизован
		AccountDTO accountDTO = accountTestSupport.getAccountSuccessful(registrationApiV2Client, false);
		//Пользователь верный
		assertEquals(userId, accountDTO.getId());

		//Используем новый клиент без параметров авторизации
		registrationApiV2Client = new ApiV2Client(null, null);

		//Авторизуемся по restFacebookId/userFacebookUuid
		MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
		params.add("facebookId", restFacebookId);
		params.add("password", userFacebookUuid);
		Long restFacebookIdUserId = accountTestSupport.authSuccessful(registrationApiV2Client, params);
		//Пользователь в респонзе верный
		assertEquals(userId, restFacebookIdUserId);

		//Авторизован
		accountDTO = accountTestSupport.getAccountSuccessful(registrationApiV2Client, false);
		//Пользователь верный
		assertEquals(userId, accountDTO.getId());

	}

	@Test
	public void _00_7_0_resetValidation() {
		//Для начала выходим
		registrationApiV2Client.logout();

		Map<String, String> errors = Utils.mapOf(new String[][]{
				{"email", "Не указан адрес электронной почты"}
		});

		//Отправляем пустой запрос на сброс пароля
		assertResetPasswordValidationFailed(null, errors);

		//E-mail невалиден
		errors.put("email", "Неверный формат адреса электронной почты");
		assertResetPasswordValidationFailed("невалид_недомен.незона", errors);

		//Не нашли такой e-mail. Проверьте, что вы написали правильно
		errors.put("email", "Не нашли такой e-mail. Проверьте, что вы написали правильно");
		assertResetPasswordValidationFailed(RandomStringUtils.randomAlphabetic(15).toLowerCase() + "@" + RandomStringUtils.randomAlphabetic(5).toLowerCase() + ".xx", errors);
	}

	@Test
	public void _00_7_1_resetPasswordOK(){
		//Отправленные письма с подтверждающими УРЛами email -> text
		Map<String, String> sentConfirmations = new HashMap<>();

		//Будем восстанавливать пароль для пользователя, созданного в предыдущем тесте
		String email = newUser.getEmail();

		//Для начала выходим
		registrationApiV2Client.logout();

		//Перехватываем отправку уведомления notificationSender'ом
		doAnswer(invocation -> {
			String to = invocation.getArgument(0);
			String subject = invocation.getArgument(1);
			String text = invocation.getArgument(2);

			assertNotNull(to);
			assertNotNull(subject);
			assertNotNull(text);

			//Это наш пользователь
			assertEquals(email, to);

			//Тема правильная
			assertEquals("Восстановление пароля", subject);

			//Текст содержит никнейм пользователя
			assertTrue(text.contains(newUser.getNickname()));

			//Текст содержит токен
			assertTrue(text.contains("/reset_password?t="));

			sentConfirmations.put(to, text);

			return null;
		}).when(notificationSender).sendViaRegistrationBox(any(String.class), any(String.class), any(String.class));

		//Сбрасываем пароля пользователя
		ResponseEntity<Api2Response<Boolean>> response = registrationApiV2Client.request(accountTestSupport.getResetPasswordUrl(), null, HttpMethod.POST, TestUtils.getOneParamAsMultiValueMap("email", email), new ParameterizedTypeReference<Api2Response<Boolean>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		Boolean result = response.getBody().getData();
		//Сервис вернул true - успех
		assertTrue(result);

		//Письмо с подтверждением пароля отправлялось
		assertTrue(sentConfirmations.containsKey(email));

		String text = sentConfirmations.get(email);

		//Получаем обновленного пользователя из б.д.
		newUser = userRepository.findById(newUser.getId()).orElse(null);
		assertNotNull(newUser);

		//Отправленное письмо содержит токен сброса пароля
		assertTrue(text.contains(newUser.getPasswordResetToken().getValue()));
	}

	//Установка нового пароля с использованием полученого токена
	@Test
	public void _00_7_2_setPassword() {
		//Используем новый клиент без параметров авторизации
		registrationApiV2Client = new ApiV2Client(null, null);

		Map<String, String> errors = Utils.mapOf(new String[][]{
				{"password", "Не указан пароль"},
				{"passwordOnceMore", "Не указан подтверждающий пароль"},
				{"resetToken", "Не указан токен"},
		});

		SecurityService.NewPasswordRequest request = null;

		//Отправляем пустой запрос (NULL) на установку пароля и получаем ошибки валидации
		assertSetPasswordValidationFailed(request, errors);

		request = new SecurityService.NewPasswordRequest();

		//Отправляем пустой объект запроса на установку пароля и получаем ошибки валидации
		assertSetPasswordValidationFailed(request, errors);

		//Добавляем пароль, ошибка пустого пароля исчезает
		request.setPassword("eee");
		errors.remove("password");
		assertSetPasswordValidationFailed(request, errors);

		//Добавляем подтврждающий пароль, ошибка пустого подтверждающего пароля исчезает
		request.setPasswordOnceMore("eee____");
		errors.remove("passwordOnceMore");
		assertSetPasswordValidationFailed(request, errors);

		//Добавляем токен, ошибка пустого токена исчезает. Появляется ошибка о том, что пароли не совпадают.
		request.setResetToken("tknjjjjjjjjj");
		errors.remove("resetToken");
		errors.put("newPasswordRequest", "Пароли не совпадают");
		assertSetPasswordValidationFailed(request, errors);

		//Добавляем токен, ошибка пустого токена исчезает
		request.setPasswordOnceMore(request.getPassword());
		errors.remove("newPasswordRequest");
		errors.put("resetToken", "Ссылка недействительна");
		assertSetPasswordValidationFailed(request, errors);

		//Добавляем правильный токен от пользователя у которого мы сбрасывали пароль в предыдущем тесте, но делаем его устаревшим (более 2-х часов)
		newUser = userRepository.findById(newUser.getId()).orElse(null);
		PasswordResetToken passwordResetToken = newUser.getPasswordResetToken();
		passwordResetToken.setCreatedAt(ZonedDateTime.now().minusHours(3));
		newUser = userRepository.save(newUser);
		request.setResetToken(passwordResetToken.getValue());;
		errors.put("resetToken", "Срок действия ссылки истек");
		assertSetPasswordValidationFailed(request, errors);

		//Снова делаем токен не просроченным, но ставим пометку о том, что он уже был использован
		passwordResetToken = newUser.getPasswordResetToken();
		passwordResetToken.setCreatedAt(ZonedDateTime.now().minusHours(1));
		passwordResetToken.setUsedAt(ZonedDateTime.now().minusMinutes(30));
		newUser = userRepository.save(newUser);
		//Тот же текст ошибки
		errors.put("resetToken", "Срок действия ссылки истек");
		assertSetPasswordValidationFailed(request, errors);

		//Снова делаем токен не использованным
		passwordResetToken = newUser.getPasswordResetToken();
		passwordResetToken.setUsedAt(null);
		newUser = userRepository.save(newUser);
		//Теперь все отрабатывает. Пароль устанавливается
		setPasswordValidationSuccessful(request);

		//С новым паролем можно авторизоваться
		long authorizedUserId = rawAuthSuccessful(newUser.getEmail(), request.getPassword());
		assertEquals(newUser.getId().longValue(), authorizedUserId);

		//Тееперь пользователь авторизован и видит свой аккаунт
		AccountDTO account = getAccountSuccessful(registrationApiV2Client, false);
		assertEquals(newUser.getId(), account.getId());
		assertEquals(newUser.getEmail(), account.getEmail());

		//Удаляем пользователя из базы
		//userRepository.delete(newUser);
		//newUser = null;
		//registrationApiV2Client = null;

	}

	//Установка нового пароля с использованием старого пароля
	@Test
	public void _00_7_8_updatePassword() {
		//Используем новый клиент
		registrationApiV2Client = new ApiV2Client(newUser.getEmail(), "eee");

		Map<String, String> errors = Utils.mapOf(new String[][]{
				{"newPassword", "Не указан пароль"},
				{"newPasswordOnceMore", "Не указан подтверждающий пароль"},
				{"oldPassword", "Не указан старый пароль"},
		});

		SecurityService.UpdatePasswordRequest request = null;

		//Мы пока не авторизованы. Отправляем пустой запрос (NULL) на установку пароля и получаем ошибки авторизации
		assertUpdatePasswordAuthFailed(request);

		//Авторизуемся и отправляем пустой запрос (NULL) на установку пароля и получаем ошибки валидации
		assertUpdatePasswordValidationFailed(request, errors, true);

		request = new SecurityService.UpdatePasswordRequest();

		//Добавляем пароль, ошибка пустого пароля исчезает. Мы уже авторизованы, так что повторно не авторизуемся
		request.setNewPassword("yyy");
		errors.remove("newPassword");
		assertUpdatePasswordValidationFailed(request, errors, false);

		//Добавляем подтврждающий пароль, ошибка пустого подтверждающего пароля исчезает
		request.setNewPasswordOnceMore("eee____");
		errors.remove("newPasswordOnceMore");
		assertUpdatePasswordValidationFailed(request, errors, false);

		//Добавляем старый пароль, ошибка отсутствия старого пароля исчезает. Появляется ошибка о том, что пароли не совпадают.
		request.setOldPassword("fff");
		errors.remove("oldPassword");
		errors.put("updatePasswordRequest", "Пароли не совпадают");
		assertUpdatePasswordValidationFailed(request, errors, false);

		//Ставим правильный второй пароль. Появляется ошибка о том, что текущий пароль неверный
		request.setNewPasswordOnceMore(request.getNewPassword());
		errors.put("updatePasswordRequest", "Неверный текущий пароль");
		assertUpdatePasswordValidationFailed(request, errors, false);

		//Ставим корректный старый пароль
		request.setOldPassword("eee");

		//Теперь все отрабатывает. Пароль устанавливается
		updatePasswordValidationSuccessful(request);

		//С новым паролем можно авторизоваться
		long authorizedUserId = rawAuthSuccessful(newUser.getEmail(), request.getNewPassword());
		assertEquals(newUser.getId().longValue(), authorizedUserId);

		//Тееперь пользователь авторизован и видит свой аккаунт
		AccountDTO account = getAccountSuccessful(registrationApiV2Client, false);
		assertEquals(newUser.getId(), account.getId());
		assertEquals(newUser.getEmail(), account.getEmail());

		//Удаляем пользователя из базы
		jdbcTemplate.execute("DELETE FROM public.device where public.device.user_id = " + newUser.getId());
		userRepository.delete(newUser);
		newUser = null;
		registrationApiV2Client = null;

	}

	//////////////////////////////////////


    @Test
    public void _01_0_getAccountUnAuthorizedUnSuccessfull(){
        ResponseEntity<String> response = apiV2Client.request(accountTestSupport.getServiceUrl(), null, HttpMethod.GET, null, String.class, false);
	    assertSame(HttpStatus.FORBIDDEN, response.getStatusCode());
    }

	@Test
	public void _01_1_getAccountAuthorizedSuccessfull(){
		AccountDTO accountDTO = getAccountSuccessful();
		assertAccountIsCorrect(accountDTO);
	}

	@Test
	public void _02_01_updateAccountUnAuthorizedUnSuccessfull(){
		AccountDTO accountDTO = getAccountSuccessful(false);
		accountDTO.setPhone("+***********");
		apiV2Client.logout();
		ResponseEntity<String> response = apiV2Client.request(accountTestSupport.getServiceUrl(), null, HttpMethod.PUT, accountDTO, String.class, false);
		assertSame(HttpStatus.FORBIDDEN, response.getStatusCode());
	}

	@Test
	public void _02_02_updateAccountAuthorizedSuccessfull(){
    	User user = getUser();
		String newPhone = user.getPhone().equals(phone) ? phone2 : phone;
		User.Sex newSex = user.getSex() != null ? user.getSex() == User.Sex.MALE ? User.Sex.FEMALE : User.Sex.MALE : User.Sex.MALE;

		AccountDTO accountDTO = getAccountSuccessful();
		accountDTO.setPhone(newPhone);
		accountDTO.setSex(newSex);

		if(user.getBirthDate() == null) accountDTO.setBirthDate(Utils.getSecondsFromLocalDateTime(birthDate));

		AccountDTO updatedAccount = updateAccountSuccessful(accountDTO);
		assertAccountIsCorrect(accountDTO);
		assertEquals(newPhone, updatedAccount.getPhone());
		assertSame(newSex, updatedAccount.getSex());
	}

	@Test
	public void _02_03_updateAccountWrongPhoneUnsuccessful(){
		AccountDTO accountDTO = getAccountSuccessful();
		accountDTO.setPhone(wrongPhone);
		ResponseEntity<String> response = apiV2Client.request(accountTestSupport.getServiceUrl(), null, HttpMethod.PUT, accountDTO, String.class, false);
		assertSame(HttpStatus.BAD_REQUEST, response.getStatusCode());
		assertTrue(response.getBody().contains("PhoneException"));
	}

	@Test
	public void _02_04_updateAccountChangeBirthDateUnsuccessful(){
		AccountDTO accountDTO = getAccountSuccessful();
		accountDTO.setBirthDate(Utils.getSecondsFromLocalDateTime(anotherBirthDate));
		ResponseEntity<String> response = apiV2Client.request(accountTestSupport.getServiceUrl(), null, HttpMethod.PUT, accountDTO, String.class, false);
		assertSame(HttpStatus.BAD_REQUEST, response.getStatusCode());
		assertTrue(response.getBody().contains("BirthDateException"));
	}

	@Test
	public void _02_05_updateAccountSetWrongNicknameUnsuccessful(){
    	String[] wrongNicknames = {"ываыва ываыв", "ABCHGHGHGHHJ*"};
    	for(String wrongNickname : wrongNicknames){
		    AccountDTO accountDTO = getAccountSuccessful();
		    accountDTO.setNickname(wrongNickname);
		    ResponseEntity<String> response = apiV2Client.request(accountTestSupport.getServiceUrl(), null, HttpMethod.PUT, accountDTO, String.class, false);
		    assertSame(HttpStatus.BAD_REQUEST, response.getStatusCode());
		    assertTrue(response.getBody().contains("NicknameException"));
	    }
	}

	@Test
	public void _02_06_updateAccountSetUsedWrongNicknameUnsuccessful(){
		String usedNickname = getUser2().getNickname();
		AccountDTO accountDTO = getAccountSuccessful();
		accountDTO.setNickname(usedNickname);
		ResponseEntity<String> response = apiV2Client.request(accountTestSupport.getServiceUrl(), null, HttpMethod.PUT, accountDTO, String.class, false);
		assertSame(HttpStatus.BAD_REQUEST, response.getStatusCode());
		assertTrue(response.getBody().contains("NicknameException"));
		assertTrue(response.getBody().contains("Пользователь с таким nickname уже зарегистрирован"));
	}

	@Test
	public void _02_07_updateAccountChangeNicknameSuccessful(){
    	String oldNickname = userNickname;
    	String newNickname = oldNickname + RandomStringUtils.randomAlphabetic(5).toLowerCase();
		AccountDTO accountDTO = getAccountSuccessful();

		//Меняем никнейм
		accountDTO.setNickname(newNickname);
		accountDTO = updateAccountSuccessful(accountDTO);
		assertEquals(newNickname, accountDTO.getNickname());
		User updatedUser = getUser();
		assertEquals(newNickname, updatedUser.getNickname());

		//Возвращаем старый никнейм
		accountDTO.setNickname(oldNickname);
		accountDTO = updateAccountSuccessful(accountDTO);
		assertEquals(oldNickname, accountDTO.getNickname());
		updatedUser = getUser();
		assertEquals(oldNickname, updatedUser.getNickname());
	}


	//Попытка загрузить аватар неавторизованным пользователем
	@Test
	public void _03_0_updateAccountAvatarValidationUnauthorizesFailed(){
		apiV2Client.logout();
		ResponseEntity<String> response = apiV2Client.request(accountTestSupport.getUploadAvatarUrl(), null, HttpMethod.POST, null, String.class, false);
		assertTrue(response.getStatusCode() == HttpStatus.FORBIDDEN);
		assertTrue(TestUtils.hasError(response.getBody(), AccessDeniedException.class));
	}

	//Попытка загрузить аватар без передачи файла
	@Test
	public void _03_1_updateAccountAvatarNoImageFailed() throws IOException {
		Map<String, String> expectedErrors = Utils.mapOf(new String[][]{
				{ "image", "Отсутствует изображение" }
		});

		assertUploadAvatarFailed(null, expectedErrors, true);
	}

	//Попытка загрузить аватар некорректного размера
	@Test
	public void _03_2_updateAccountAvatarValidationFailed() throws URISyntaxException, IOException {
    	Map<String, String> fileNameToErrors = Utils.mapOf(new String[][]{
			    { "100x100-24.jpg", "Размер картинки должен быть в пределах 145x145 - 300x300" },
			    { "200x188-52.jpg", "Допускаются только квадратные изображения" },
			    { "305-224-24.jpg", "Размер картинки должен быть в пределах 145x145 - 300x300" },
			    { "305-224-75.jpg", "Слишком большой размер загружаемого файла" }
	    });

    	for(String fileName : fileNameToErrors.keySet()){
    		String expectedError = fileNameToErrors.get(fileName);
		    Map<String, String> expectedErrors = Utils.mapOf(new String[][]{
				    { "image", expectedError }
		    });
		    assertUploadAvatarFailedByName(fileName, expectedErrors);
	    }
	}

	//Загрузка аватара корректного размера
	@Test
	public void _03_3_updateAccountAvatarSuccessful() throws URISyntaxException, IOException {
		assertUploadAvatarSuccessfulByName("200x200-53.jpg");
	}

	//Попытка загрузить файл неавторизованным пользователем
	@Test
	public void _04_0_uploadUserfileUnauthorizes_failed() throws IOException, URISyntaxException {
		apiV2Client.logout();
		MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
		body.add("file", new FileSystemResource(getUserfileForUploadByName("1.zip")));
		body.add("title", "Файл");
		ResponseEntity<String> response = apiV2Client.request(accountTestSupport.getUserfilesUrl(), null, HttpMethod.POST, body, String.class, false);
		assertTrue(response.getStatusCode() == HttpStatus.FORBIDDEN);
		assertTrue(TestUtils.hasError(response.getBody(), AccessDeniedException.class));
	}

	//Попытка загрузить файл без передачи файла
	@Test
	public void _04_1_uploadUserfileNoFile_failed(){
		MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
		body.add("title", "Файл");
		ResponseEntity<String> response = apiV2Client.request(accountTestSupport.getUserfilesUrl(), null, HttpMethod.POST, body, String.class, true);
		assertSame(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
		assertTrue(TestUtils.hasError(response.getBody(), org.springframework.web.multipart.MultipartException.class));
	}

	//Успешная загрузка файла
	@Test
	public void _04_2_uploadUserfile_successful() throws URISyntaxException, IOException {
		assertUploadUserfileSuccessfulByName("1.zip", "Первый файл");
	}

	@Test
	@Transactional
	@Rollback(value = false)
	public void _09_addCounterpartyNullValueUnSuccessfull(){
		ResponseEntity<String> response = apiV2Client.request(accountTestSupport.getCounterpartyUrl(), null, HttpMethod.PUT, null, String.class, false);
		assertSame(HttpStatus.BAD_REQUEST, response.getStatusCode());
	}

	@Test
	@Transactional
	@Rollback(value = false)
	public void _11_1_addIPCounterpartyWrongNameUnSuccessfull(){
		AccountDTO accountDTO = getAccountSuccessful();
		int initialCounterpartiesCount = listSize(accountDTO.getCounterparties());
		ResponseEntity<String> response = apiV2Client.request(accountTestSupport.getCounterpartyUrl(), null, HttpMethod.PUT, getCounterpartyIPDTO().setFirstName("иНДИВИдуальный Предприниматель Ваня"), String.class, false);
		assertSame(HttpStatus.BAD_REQUEST, response.getStatusCode());
		assertTrue(response.getBody().contains("CounterpartyException"));
		AccountDTO changedAccountDTO = getAccountSuccessful();
		int newCounterpartiesCount = listSize(accountDTO.getCounterparties());
		assertSame(initialCounterpartiesCount, newCounterpartiesCount);
	}

	@Test
	@Transactional
	@Rollback(value = false)
	public void _11_2_addIPCounterpartySuccessfull(){
		AccountDTO accountDTO = getAccountSuccessful();
		int initialCounterpartiesCount = listSize(accountDTO.getCounterparties());
		ResponseEntity<Api2Response<CounterpartyDTO>> response = apiV2Client.request(accountTestSupport.getCounterpartyUrl(), null, HttpMethod.PUT, getCounterpartyIPDTO(), new ParameterizedTypeReference<Api2Response<CounterpartyDTO>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		assertNotNull(response.getBody().getData());
		CounterpartyDTO counterpartyDTO = response.getBody().getData();
		assertNotNull(counterpartyDTO.getId());
		counterpartyId = counterpartyDTO.getId();
		assertNotNull(counterpartyDTO.getJurAddress());
		assertNull(counterpartyDTO.getPhysAddress());
		assertNotNull(counterpartyDTO.getJurAddress().getId());
		assertEquals(counterpartyDTO.getJurAddress().getAddress(), address);
		AccountDTO changedAccountDTO = getAccountSuccessful();
		int changedCounterpartiesCount = listSize(changedAccountDTO.getCounterparties());
		assertSame(changedCounterpartiesCount, initialCounterpartiesCount + 1);
		if(initialCounterpartiesCount == 0){ //Это первый контрагент пользователя, значит он должен автоматически стать активным
			assertTrue(Utils.isTrue(counterpartyDTO.getIsActive()));
		}
	}

	@Test
	@Transactional
	@Rollback(value = false)
	public void _12_addJurCounterpartyActiveSuccessfull(){
		AccountDTO accountDTO = getAccountSuccessful();
		int initialCounterpartiesCount = listSize(accountDTO.getCounterparties());
		ResponseEntity<Api2Response<CounterpartyDTO>> response = apiV2Client.request(accountTestSupport.getCounterpartyUrl(), null, HttpMethod.PUT, getCounterpartyJurDTO().setIsActive(true), new ParameterizedTypeReference<Api2Response<CounterpartyDTO>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		assertNotNull(response.getBody().getData());
		CounterpartyDTO counterpartyDTO = response.getBody().getData();
		assertNotNull(counterpartyDTO.getId());
		counterparty2Id = counterpartyDTO.getId();
		assertNotNull(counterpartyDTO.getIsActive());
		assertTrue(counterpartyDTO.getIsActive());
		AccountDTO changedAccountDTO = getAccountSuccessful();
		int changedCounterpartiesCount = listSize(changedAccountDTO.getCounterparties());
		assertSame(changedCounterpartiesCount, initialCounterpartiesCount + 1);
		//Проверим, что у нас в итоговом списке контрагентов только один является активным.
		int activeCount = 0;
		Long activeId = null;
		for(CounterpartyDTO c : changedAccountDTO.getCounterparties()){
			if(Utils.isTrue(c.getIsActive())){
				activeCount ++;
				activeId = c.getId();
			}
		}
		assertSame(1, activeCount);
		assertEquals(counterparty2Id, activeId);
	}

	@Test
	@Transactional
	@Rollback(value = false)
	public void _13_changeCounterpartySuccessfull(){
		AccountDTO accountDTO = getAccountSuccessful();
		int initialCounterpartiesCount = listSize(accountDTO.getCounterparties());
		CounterpartyDTO counterpartyDTO = accountDTO.getCounterparties().stream().filter(c -> c.getId().equals(counterpartyId)).findFirst().orElse(null);
		assertTrue(Utils.isFalse(counterpartyDTO.getIsActive()));
		counterpartyDTO.setIsActive(true);
		counterpartyDTO.setLastName("Белов");
		ResponseEntity<Api2Response<CounterpartyDTO>> response = apiV2Client.request(accountTestSupport.getCounterpartyUrl(), null, HttpMethod.PUT, counterpartyDTO, new ParameterizedTypeReference<Api2Response<CounterpartyDTO>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		assertNotNull(response.getBody().getData());

		AccountDTO changedAccountDTO = getAccountSuccessful();
		int changedCounterpartiesCount = listSize(changedAccountDTO.getCounterparties());
		assertSame(initialCounterpartiesCount, changedCounterpartiesCount);
		//Проверим, что у нас в итоговом списке контрагентов только один является активным.
		int activeCount = 0;
		Long activeId = null;
		for(CounterpartyDTO c : changedAccountDTO.getCounterparties()){
			if(Utils.isTrue(c.getIsActive())){
				activeCount ++;
				activeId = c.getId();
			}
		}
		assertSame(1, activeCount);
		assertEquals(counterpartyId, activeId);
	}

	private boolean isDateTimeFormatISOz(String dateTimeString) {
		if (Pattern.compile("\\d\\d\\d\\d-\\d\\d-\\d\\dT\\d\\d:\\d\\dZ").matcher(dateTimeString).matches()) // 2024-04-30T20:00Z
			return true;
		if (Pattern.compile("\\d\\d\\d\\d-\\d\\d-\\d\\dT\\d\\d:\\d\\d:\\d\\dZ").matcher(dateTimeString).matches()) // 2024-04-30T20:00:01Z
			return true;
		return false;
	}

	private void validateServerCardCounterpartyDtoDateTimesOkay(CounterpartyDTO cardCounterparty) { // Must be in timezone 0 from server
		assertEquals(cardExpireTimeInZone0, cardCounterparty.getCardExpireTime());
		assertTrue(Duration.between(cardCounterparty.getCardBindTime(), ZonedDateTime.now()).getSeconds() < 60);
	}

	@Test
	@Transactional
	@Rollback(value = false)
	public void _14_addCardCounterpartyFieldsFormatSuccess() throws IOException, JSONException {
		AccountDTO sourceAccountDTO = accountTestSupport.getAccountSuccessful(apiV2Client, false);
		int initialCounterpartiesCount = listSize(sourceAccountDTO.getCounterparties());
		//
		CounterpartyDTO counterpartyInZone0 = getCounterpartyCardDTO().setCardNumber("4784 76** **** 9434").setCardExpireTime(cardExpireTimeInZone0); // Save counterparty with datetime in zone 0
		String stringRequestZone0 = objectMapper.writeValueAsString(counterpartyInZone0);
		JSONObject jsonRequestZone0 = new JSONObject(stringRequestZone0);
		assertTrue(isDateTimeFormatISOz(jsonRequestZone0.getString("cardExpireTime"))); // Here we have "2024-04-30T20:59:59Z" in request
		ResponseEntity<String> responseZone0 = apiV2Client.request(accountTestSupport.getCounterpartyUrl(), null, HttpMethod.PUT, MediaType.APPLICATION_JSON, stringRequestZone0, String.class, false);
		//
		JSONObject jsonResponseZone0 = new JSONObject(responseZone0.getBody()); // validate response part
		assertTrue(isDateTimeFormatISOz(jsonResponseZone0.getJSONObject("data").getString("cardExpireTime")));
		assertTrue(isDateTimeFormatISOz(jsonResponseZone0.getJSONObject("data").getString("cardBindTime")));
		CounterpartyDTO responseCounterparty0 = objectMapper.readValue(jsonResponseZone0.getString("data"), CounterpartyDTO.class);
		cleanCounterpartiesIDs.add(responseCounterparty0.getId());
		validateServerCardCounterpartyDtoDateTimesOkay(responseCounterparty0);
		//
		CounterpartyDTO counterpartyInZone3 = getCounterpartyCardDTO().setCardNumber("4784 76** **** 7254").setCardExpireTime(cardExpireTimeInZone3); // Save counterparty with datetime in zone 3
		String stringRequestZone3 = objectMapper.writeValueAsString(counterpartyInZone3);
		JSONObject jsonRequestZone3 = new JSONObject(stringRequestZone3);
		assertFalse(isDateTimeFormatISOz(jsonRequestZone3.getString("cardExpireTime"))); // Here we have "2024-04-30T23:59:59+03:00" in request and will handle it correctly
		ResponseEntity<String> responseZone3 = apiV2Client.request(accountTestSupport.getCounterpartyUrl(), null, HttpMethod.PUT, MediaType.APPLICATION_JSON, counterpartyInZone0, String.class, false);
		//
		JSONObject jsonResponseZone3 = new JSONObject(responseZone3.getBody());// validate response part
		assertTrue(isDateTimeFormatISOz(jsonResponseZone3.getJSONObject("data").getString("cardExpireTime")));
		assertTrue(isDateTimeFormatISOz(jsonResponseZone3.getJSONObject("data").getString("cardBindTime")));
		CounterpartyDTO responseCounterparty3 = objectMapper.readValue(jsonResponseZone3.getString("data"), CounterpartyDTO.class);
		cleanCounterpartiesIDs.add(responseCounterparty3.getId());
		validateServerCardCounterpartyDtoDateTimesOkay(responseCounterparty3);
		//
		ResponseEntity<String> responseAccount = apiV2Client.request(accountTestSupport.getServiceUrl(), null, HttpMethod.GET, MediaType.APPLICATION_JSON, null, String.class, false);
		JSONObject jsonResponseAccount = new JSONObject(responseAccount.getBody());// validate response part
		JSONArray jsonAccountCounterparties = jsonResponseAccount.getJSONObject("data").getJSONArray("counterparties");
		List<JSONObject> jsonAccountCPlist = IntStream.range(0, jsonAccountCounterparties.length()).mapToObj(jsonAccountCounterparties::optJSONObject).collect(Collectors.toList());
		//
		JSONObject jsonAccountCounterparty0 = jsonAccountCPlist.stream().filter(cp -> responseCounterparty0.getId().equals(cp.optLong("id"))).findFirst().orElseThrow(IllegalStateException::new);
		assertTrue(isDateTimeFormatISOz(jsonAccountCounterparty0.getString("cardExpireTime")));
		assertTrue(isDateTimeFormatISOz(jsonAccountCounterparty0.getString("cardBindTime")));
		JSONObject jsonAccountCounterparty3 = jsonAccountCPlist.stream().filter(cp -> responseCounterparty3.getId().equals(cp.optLong("id"))).findFirst().orElseThrow(IllegalStateException::new);
		assertTrue(isDateTimeFormatISOz(jsonAccountCounterparty3.getString("cardExpireTime")));
		assertTrue(isDateTimeFormatISOz(jsonAccountCounterparty3.getString("cardBindTime")));
		//
		AccountDTO targetAccountDTO = accountTestSupport.getAccountSuccessful(apiV2Client, false); // Receive +2 counterparties
		int changedCounterpartiesCount = listSize(targetAccountDTO.getCounterparties());
		assertSame(changedCounterpartiesCount, initialCounterpartiesCount + 1 + 1);
		// Both must have same values -> zone 0
		CounterpartyDTO verifyCounterpartyInZone0 = targetAccountDTO.getCounterparties().stream().filter(cp -> cp.getId().equals(responseCounterparty0.getId())).findFirst().orElse(null);
		validateServerCardCounterpartyDtoDateTimesOkay(verifyCounterpartyInZone0);
		//
		CounterpartyDTO verifyCounterpartyInZone3 = targetAccountDTO.getCounterparties().stream().filter(cp -> cp.getId().equals(responseCounterparty3.getId())).findFirst().orElse(null);
		validateServerCardCounterpartyDtoDateTimesOkay(verifyCounterpartyInZone3);
	}

	@Test
	@Transactional
	@Rollback(value = false)
	public void _15_addInternationalCounterparty() {
		// Сбрасываем продавцу признак ЮЛ, чтобы не было ошибки при добавлении реквизитов ФЛ
		callInTransaction.runInNewTransaction(() -> {
			User user = getUser();
			user.setProStatusTime(null);
			userService.save(user);
		});
		getAccountSuccessful();

		// 1. Create new international counterparty
		final CounterpartyDTO counterpartyInput = getCounterpartyInternationalDTO();
		final ParameterizedTypeReference<Api2Response<CounterpartyDTO>> responseType
				= new ParameterizedTypeReference<Api2Response<CounterpartyDTO>>() {};
		final CounterpartyDTO counterpartyResult = request(accountTestSupport.getCounterpartyUrl(), HttpMethod.PUT,
				counterpartyInput, responseType);
		final Long counterpartyId = counterpartyResult.getId();
		assertNotNull(counterpartyId);
		assertEqual(counterpartyInput, counterpartyResult);

		// 2. Request account and check counterparties
		AccountDTO accountDto = getAccountSuccessful();
		Optional<CounterpartyDTO> counterpartyResultOpt = accountDto.getCounterparties().stream()
				.filter(counterparty -> Objects.equals(counterparty.getId(), counterpartyId))
				.findAny();
		assertTrue(counterpartyResultOpt.isPresent());
		assertEqual(counterpartyInput, counterpartyResultOpt.get());

		// 3. Delete created counterparty
		deleteCounterpartySeccessfull(counterpartyId);

		// 4. Check that counterparty has been deleted
		accountDto = getAccountSuccessful();
		counterpartyResultOpt = accountDto.getCounterparties().stream()
				.filter(counterparty -> Objects.equals(counterparty.getId(), counterpartyId))
				.findAny();
		assertFalse(counterpartyResultOpt.isPresent());

		// Возвращаем продавцу признак ЮЛ
		callInTransaction.runInNewTransaction(() -> {
			User user = getUser();
			user.setProStatusTime(LocalDateTime.now());
			userService.save(user);
		});
	}

	private static void assertEqual(
			@NonNull final CounterpartyDTO counterparty1,
			@NonNull final CounterpartyDTO counterparty2) {
		Assertions.assertThat(counterparty1)
				.usingRecursiveComparison()
				.ignoringFields("id", "createTiestamp", "isCard", "userId")
				.isEqualTo(counterparty2);
	}

	@Test
	public void _20_addAddressEndpointSuccessfull(){
		AccountDTO accountDTO = getAccountSuccessful();
		int initialAddressEndpointsCount = listSize(accountDTO.getAddressEndpoints());
		ResponseEntity<Api2Response<AddressEndpointDTO>> response = apiV2Client.request(accountTestSupport.getAddressEndpointUrl(), null, HttpMethod.PUT, getAddressEndpointDTO(), new ParameterizedTypeReference<Api2Response<AddressEndpointDTO>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		assertNotNull(response.getBody().getData());
		AddressEndpointDTO addressEndpointDTO = response.getBody().getData();
		assertNotNull(addressEndpointDTO.getId());
		addressEndpointId = addressEndpointDTO.getId();
		assertNotNull(addressEndpointDTO.getAddress());
		assertNotNull(addressEndpointDTO.getAddress().getId());
		assertEquals(addressEndpointDTO.getAddress().getAddress(), address);
		AccountDTO changedAccountDTO = getAccountSuccessful();
		int changedAddressEndpointsCount = listSize(changedAccountDTO.getAddressEndpoints());
		assertSame(initialAddressEndpointsCount + 1, changedAddressEndpointsCount);
	}

	@Test
	public void _21_editAddressEndpointUnUsedInOrdersSavesSameEntity(){
		AccountDTO accountDTO = getAccountSuccessful();
		int initialAddressEndpointsCount = listSize(accountDTO.getAddressEndpoints());
		AddressEndpointDTO previouslyCreatedAddressEndpoint = accountDTO.getAddressEndpoints().stream().filter(ae -> ae.getId().equals(addressEndpointId)).findFirst().orElse(null);

		previouslyCreatedAddressEndpoint.setPhone(phone2);

		ResponseEntity<Api2Response<AddressEndpointDTO>> response = apiV2Client.request(accountTestSupport.getAddressEndpointUrl(), null, HttpMethod.PUT, previouslyCreatedAddressEndpoint, new ParameterizedTypeReference<Api2Response<AddressEndpointDTO>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		assertNotNull(response.getBody().getData());

		AddressEndpointDTO changedEndpointDTO = response.getBody().getData();
		assertNotNull(changedEndpointDTO.getId());

		assertEquals(previouslyCreatedAddressEndpoint.getId(), changedEndpointDTO.getId());
		assertEquals(previouslyCreatedAddressEndpoint.getAddress(), changedEndpointDTO.getAddress());

		AccountDTO changedAccountDTO = getAccountSuccessful();
		int changedAddressEndpointsCount = listSize(changedAccountDTO.getAddressEndpoints());
		assertSame(changedAddressEndpointsCount, initialAddressEndpointsCount);
	}

	@Test
	@Transactional
	@Rollback(value = false)
	public void _22_editAddressEndpointUsedInOrdersCreatesNewOne_0_prepare(){
		AddressEndpoint addressEndpoint = addressEndpointRepository.findById(addressEndpointId).orElse(null);
		Order order = orderRepository.findById(orderId).orElse(null);
		order.setDeliveryAddressEndpoint(addressEndpoint);
		orderRepository.saveAndFlush(order);
	}

	@Test
	public void _22_editAddressEndpointUsedInOrdersCreatesNewOne_1(){
		AccountDTO accountDTO = getAccountSuccessful();
		int initialAddressEndpointsCount = listSize(accountDTO.getAddressEndpoints());
		AddressEndpointDTO previouslyCreatedAddressEndpoint = accountDTO.getAddressEndpoints().stream().filter(ae -> ae.getId().equals(addressEndpointId)).findFirst().orElse(null);

		previouslyCreatedAddressEndpoint.setPhone(phone);

		ResponseEntity<Api2Response<AddressEndpointDTO>> response = apiV2Client.request(accountTestSupport.getAddressEndpointUrl(), null, HttpMethod.PUT, previouslyCreatedAddressEndpoint, new ParameterizedTypeReference<Api2Response<AddressEndpointDTO>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		assertNotNull(response.getBody().getData());

		AddressEndpointDTO changedEndpointDTO = response.getBody().getData();
		assertNotNull(changedEndpointDTO.getId());
		addressEndpoint2Id = changedEndpointDTO.getId();

		assertNotEquals(previouslyCreatedAddressEndpoint.getId(), changedEndpointDTO.getId());
		assertEquals(previouslyCreatedAddressEndpoint.getAddress(), changedEndpointDTO.getAddress());

		AccountDTO changedAccountDTO = getAccountSuccessful();
		int changedAddressEndpointsCount = listSize(changedAccountDTO.getAddressEndpoints());
		assertSame(changedAddressEndpointsCount, initialAddressEndpointsCount + 1);
	}

	@Test
	@Transactional
	@Rollback(value = false)
	public void _30_getBubbles() throws InvocationTargetException, IllegalAccessException {
		BubblesDTO bubblesDTO = accountTestSupport.getBubblesSuccessful(apiV2Client, false);
		int total = 0;
		Method[] methods = BubblesDTO.class.getDeclaredMethods();
		for(Method method : methods){
			if(method.getParameterCount() == 0 && method.getName().startsWith("get") && !method.getName().contains("Total") && !method.getName().contains("BargainBubbles")){
				Integer value = (Integer) method.invoke(bubblesDTO);
				if(value != null) total += value;
			}
		}
		assertEquals(total, bubblesDTO.getTotal().intValue());
	}

	private void _23_deleteAddressEndpointOkay(Long aepId) {
		AccountDTO accountDTOWithAEP = getAccountSuccessful();
		Assertions.assertThat(accountDTOWithAEP.getAddressEndpoints().stream().filter(aep -> aep.getId().equals(aepId)).count())
				.isEqualTo(1);
		deleteAddressEndpointSuccessfull(aepId);
		AccountDTO accountDTONoneAEP = getAccountSuccessful();
		Assertions.assertThat(accountDTONoneAEP.getAddressEndpoints().stream().filter(aep -> aep.getId().equals(aepId)).count())
				.isEqualTo(0);
	}

	@Test
	public void _23_1_deleteAddressEndpoint_UsedInOrders_Okay(){
		_23_deleteAddressEndpointOkay(addressEndpointId);
	}

	@Test
	public void _23_2_deleteAddressEndpoint_UnUsedInOrders_Okay(){
		_23_deleteAddressEndpointOkay(addressEndpoint2Id);
	}

	/**
	 * Гость, который еще не определился со своим полом
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _30_unauthorizedSexIsNull(){
		User user = getUser();
		user.setSex(null);
		userService.save(user);
		TestUtils.deleteActivities(user.getId(), apiV2Client.getOskCookie(), activityTestRepository);
		apiV2Client.logout();
		TestTransaction.flagForCommit();
		TestTransaction.end();

		//Пол еще не установлен как для гостя, так и для авторизованного

		//Гость
		User.Sex sex = getSexSuccessful(false);
		assertNull(sex);

		//Авторизованный
		sex = getSexSuccessful(true);
		assertNull(sex);

		apiV2Client.logout();
	}

	/**
	 * Гость определяется с полом и он сохраняется
	 */
	@Test
	public void _31_unauthorizedSetSex(){
		setSexSuccessful(User.Sex.FEMALE, false);

		User.Sex sex = getSexSuccessful(false);
		assertSame(User.Sex.FEMALE, sex);
	}

	/**
	 * Пользователь авторизуется и обретает пол гостя, под которым он сидел
	 */
	@Test
	public void _32_authorizedGetSex(){
		User.Sex sex = getSexSuccessful(true);
		assertSame(User.Sex.FEMALE, sex);

		User user = getUser();
		assertSame(User.Sex.FEMALE, user.getSex());
	}

	/**
	 * Авторизованны пользователь меняет пол
	 */
	@Test
	public void _33_authorizedSetSex(){
		setSexSuccessful(User.Sex.MALE, true);

		User.Sex sex = getSexSuccessful(false);
		assertSame(User.Sex.MALE, sex);
	}

	/**
	 * Пользователь выходит, но под гостем сохраняет свой пол
	 */
	@Test
	public void _34_unauthorizedSetSex(){
		apiV2Client.logout();

		User.Sex sex = setSexSuccessful(User.Sex.MALE, false);
		assertSame(User.Sex.MALE, sex);
	}


	/**
	 * Неавторизованный пользователь не может получить настройки групп рассылки пушей
	 */
	@Test
	public void _40_unauthorized_getPushGroups_failed(){
		ResponseEntity<String> response = apiV2Client.request(accountTestSupport.getPushGroupsUrl(), null, HttpMethod.GET, null, String.class, false);
		assertSame(HttpStatus.FORBIDDEN, response.getStatusCode());
	}

	/**
	 * Пользователь никогда не редактировал группы рассылки, у него должны быть отмечены все группы по умолчанию
	 */
	@Test
	public void _41_1_authorized_getPushGroups_notEdited_OK(){
		User user = getUser();

		//Удаляем информацию о подписках пользователя
		notificationGroupUserBindingRepository.deleteAll(notificationGroupUserBindingRepository.findAllByUserId(user.getId()));
		user.setNotificationGroupsChangeTime(null);
		userService.save(user);

		List<NotificationGroupDTO> groups = getPushGroupsSuccessfull(true);
		//Всегда приходят 4 видимые для пользователя группы
		assertEquals(4, groups.size());
		//Все группы отмечены
		for(NotificationGroupDTO group : groups){
			assertTrue(group.getIsSelected());
		}

		//Проверка на то, что пользователь может получить все виды уведомлений (по всем группам)
		assertUserCanReceiveNotificationsByGroups(user, Arrays.asList(1L, 2L, 3L), true, UserSubscriptionType.MOBILE_PUSH);
	}

	@Test
	public void _41_2_authorized_getEmailGroups_notEdited_OK(){
		User user = getUser();

		//Удаляем информацию о подписках пользователя
		notificationGroupUserBindingRepository.deleteAll(notificationGroupUserBindingRepository.findAllByUserId(user.getId()));
		user.setNotificationGroupsChangeTime(null);
		userService.save(user);

		List<NotificationGroupDTO> groups = getEmailGroupsSuccessfull(true);
		//Всегда приходят 4 видимые для пользователя группы
		assertEquals(4, groups.size());
		//Все группы отмечены
		for(NotificationGroupDTO group : groups){
			assertTrue(group.getIsSelected());
		}

		//Проверка на то, что пользователь может получить все виды уведомлений (по всем группам)
		assertUserCanReceiveNotificationsByGroups(user, Arrays.asList(7L, 8L, 9L), true, UserSubscriptionType.EMAIL);
	}

	@Test
	public void _41_3_authorized_getWhatsappGroups_notEdited_OK(){
		User user = getUser();

		//Удаляем информацию о подписках пользователя
		notificationGroupUserBindingRepository.deleteAll(notificationGroupUserBindingRepository.findAllByUserId(user.getId()));
		user.setNotificationGroupsChangeTime(null);
		userService.save(user);

		List<NotificationGroupDTO> groups = getWhatsappGroupsSuccessfull(true);
		//Всегда приходят 2 видимые для пользователя группы
		assertEquals(2, groups.size());
		//Все группы отмечены
		for(NotificationGroupDTO group : groups){
			assertTrue(group.getIsSelected());
		}

		//Проверка на то, что пользователь может получить все виды уведомлений (по всем группам)
		assertUserCanReceiveNotificationsByGroups(user, Arrays.asList(10L, 11L), true, UserSubscriptionType.WHATSAPP);
	}

	/**
	 * Пользователь устанавливает группы рассылки
	 */
	@Test
	public void _42_1_authorized_setPushGroups_OK(){
		//Чистим последнее сохраненное событие
		testApplicationEventListener.setLastUserMobilePushEvent(null);
		User user = getUser();

		List<Long> userSelectedGroupIds = Arrays.asList(1L, 3L, 5L /*Общая группа (главный выключатель пушей)*/ );
		List<String> userSelectedGroupStrIds = userSelectedGroupIds.stream().map(id -> id.toString()).collect(Collectors.toList());

		ResponseEntity<Api2Response<List<NotificationGroupDTO>>> response = apiV2Client.request(accountTestSupport.getPushGroupsUrl(), TestUtils.getOneParamAsMap("notificationGroupIds", String.join(",", userSelectedGroupStrIds)), HttpMethod.PUT, null, new ParameterizedTypeReference<Api2Response<List<NotificationGroupDTO>>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());

		List<NotificationGroupDTO> groups = getPushGroupsSuccessfull(false);

		//Отмечены только выбранные группы в ответе на запрос установки
		for(NotificationGroupDTO group : response.getBody().getData()){
			assertEquals(userSelectedGroupIds.contains(group.getId()), group.getIsSelected());
		}

		//Отмечены только выбранные группы в ответе на запрос получения списка пользовательских групп
		for(NotificationGroupDTO group : groups){
			assertEquals(userSelectedGroupIds.contains(group.getId()), group.getIsSelected());
		}

		//Проверка на то, что пользователь может получить все виды уведомлений по включенным группам и не может получить по выключенным
		assertUserCanReceiveNotificationsByGroups(user, Arrays.asList(1L, 3L), true, UserSubscriptionType.MOBILE_PUSH);
		assertUserCanReceiveNotificationsByGroups(user, Arrays.asList(4L), false, UserSubscriptionType.MOBILE_PUSH);

		//Проверяем событие
		UserSubscriptionEvent lastEvent = testApplicationEventListener.getLastUserMobilePushEvent();
		//Событие дошло
		assertNotNull(lastEvent);
		assertEquals(user.getId(), lastEvent.getUserId());
		assertSame(UserSubscriptionType.MOBILE_PUSH, lastEvent.getType());
		//Проверяем что в событии те же ID что мы и отсылали
		assertArrayEquals(userSelectedGroupIds.toArray(), lastEvent.getNotificationGroupIds().toArray(new Long[0]));
	}


	@Test
	public void _42_2_authorized_setEmailGroups_OK(){
		//Чистим последнее сохраненное событие
		testApplicationEventListener.setLastUserMobilePushEvent(null);
		User user = getUser();

		List<Long> userSelectedGroupIds = Arrays.asList(7L, 9L, 6L /*Общая группа (главный выключатель e-mail)*/ );
		List<String> userSelectedGroupStrIds = userSelectedGroupIds.stream().map(id -> id.toString()).collect(Collectors.toList());

		ResponseEntity<Api2Response<List<NotificationGroupDTO>>> response = apiV2Client.request(accountTestSupport.getEmailGroupsUrl(), TestUtils.getOneParamAsMap("notificationGroupIds", String.join(",", userSelectedGroupStrIds)), HttpMethod.PUT, null, new ParameterizedTypeReference<Api2Response<List<NotificationGroupDTO>>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());

		List<NotificationGroupDTO> groups = getEmailGroupsSuccessfull(false);

		//Отмечены только выбранные группы в ответе на запрос установки
		for(NotificationGroupDTO group : response.getBody().getData()){
			assertEquals(userSelectedGroupIds.contains(group.getId()), group.getIsSelected());
		}

		//Отмечены только выбранные группы в ответе на запрос получения списка пользовательских групп
		for(NotificationGroupDTO group : groups){
			assertEquals(userSelectedGroupIds.contains(group.getId()), group.getIsSelected());
		}

		//Проверка на то, что пользователь может получить все виды уведомлений по включенным группам и не может получить по выключенным
		assertUserCanReceiveNotificationsByGroups(user, Arrays.asList(7L, 9L), true, UserSubscriptionType.EMAIL);
		assertUserCanReceiveNotificationsByGroups(user, Collections.singletonList(8L), false, UserSubscriptionType.EMAIL);

		//Проверяем событие
		UserSubscriptionEvent lastEvent = testApplicationEventListener.getLastUserMobilePushEvent();
		//Событие дошло
		assertNotNull(lastEvent);
		assertEquals(user.getId(), lastEvent.getUserId());
		assertSame(UserSubscriptionType.EMAIL, lastEvent.getType());
		//Проверяем что в событии те же ID что мы и отсылали
		Assertions.assertThat(userSelectedGroupIds).containsExactlyInAnyOrderElementsOf(lastEvent.getNotificationGroupIds());
	}


	@Test
	public void _42_3_authorized_setWhatsappGroups_OK(){
		//Чистим последнее сохраненное событие
		testApplicationEventListener.setLastUserMobilePushEvent(null);
		User user = getUser();

		List<Long> userSelectedGroupIds = Arrays.asList(11L, 10L /*Общая группа (главный выключатель e-mail)*/ );
		List<String> userSelectedGroupStrIds = userSelectedGroupIds.stream().map(id -> id.toString()).collect(Collectors.toList());

		ResponseEntity<Api2Response<List<NotificationGroupDTO>>> response = apiV2Client.request(accountTestSupport.getWhatsappGroupsUrl(), TestUtils.getOneParamAsMap("notificationGroupIds", String.join(",", userSelectedGroupStrIds)), HttpMethod.PUT, null, new ParameterizedTypeReference<Api2Response<List<NotificationGroupDTO>>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());

		List<NotificationGroupDTO> groups = getWhatsappGroupsSuccessfull(false);

		//Отмечены только выбранные группы в ответе на запрос установки
		for(NotificationGroupDTO group : response.getBody().getData()){
			assertEquals(userSelectedGroupIds.contains(group.getId()), group.getIsSelected());
		}

		//Отмечены только выбранные группы в ответе на запрос получения списка пользовательских групп
		for(NotificationGroupDTO group : groups){
			assertEquals(userSelectedGroupIds.contains(group.getId()), group.getIsSelected());
		}

		//Проверка на то, что пользователь может получить все виды уведомлений по включенным группам и не может получить по выключенным
		assertUserCanReceiveNotificationsByGroups(user, Arrays.asList(10L, 11L), true, UserSubscriptionType.WHATSAPP);}

	/**
	 * Пользователь снимает галочку с главной группы и больше не может получить никакого уведомления
	 */
	@Test
	public void _43_1_authorized_resetGeneralPushGroup_OK(){
		User user = getUser();

		List<Long> userSelectedGroupIds = Arrays.asList(1L, 3L);
		List<String> userSelectedGroupStrIds = userSelectedGroupIds.stream().map(id -> id.toString()).collect(Collectors.toList());

		ResponseEntity<Api2Response<List<NotificationGroupDTO>>> response = apiV2Client.request(accountTestSupport.getPushGroupsUrl(), TestUtils.getOneParamAsMap("notificationGroupIds", String.join(",", userSelectedGroupStrIds)), HttpMethod.PUT, null, new ParameterizedTypeReference<Api2Response<List<NotificationGroupDTO>>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());

		List<NotificationGroupDTO> groups = getPushGroupsSuccessfull(false);

		//Проверка на то, что пользователь не может получить никакие уведоления без отметки главной группы
		assertUserCanReceiveNotificationsByGroups(user, Arrays.asList(1L, 2L, 3L), false, UserSubscriptionType.MOBILE_PUSH);
	}

	@Test
	public void _43_2_authorized_resetGeneralEmailGroup_OK(){
		User user = getUser();

		List<Long> userSelectedGroupIds = Arrays.asList(7L, 9L);
		List<String> userSelectedGroupStrIds = userSelectedGroupIds.stream().map(Object::toString).collect(Collectors.toList());

		ResponseEntity<Api2Response<List<NotificationGroupDTO>>> response = apiV2Client.request(accountTestSupport.getEmailGroupsUrl(), TestUtils.getOneParamAsMap("notificationGroupIds", String.join(",", userSelectedGroupStrIds)), HttpMethod.PUT, null, new ParameterizedTypeReference<Api2Response<List<NotificationGroupDTO>>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());

		List<NotificationGroupDTO> groups = getPushGroupsSuccessfull(false);

		//Проверка на то, что пользователь не может получить никакие уведоления без отметки главной группы
		assertUserCanReceiveNotificationsByGroups(user, Arrays.asList(7L, 8L, 9L), false, UserSubscriptionType.EMAIL);
	}

	@Test
	public void _43_2_authorized_resetGeneralWhatsappGroup_OK(){
		User user = getUser();

		List<Long> userSelectedGroupIds = Collections.singletonList(11L);
		List<String> userSelectedGroupStrIds = userSelectedGroupIds.stream().map(Object::toString).collect(Collectors.toList());

		ResponseEntity<Api2Response<List<NotificationGroupDTO>>> response = apiV2Client.request(accountTestSupport.getWhatsappGroupsUrl(), TestUtils.getOneParamAsMap("notificationGroupIds", String.join(",", userSelectedGroupStrIds)), HttpMethod.PUT, null, new ParameterizedTypeReference<Api2Response<List<NotificationGroupDTO>>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());

		List<NotificationGroupDTO> groups = getWhatsappGroupsSuccessfull(false);

		//Проверка на то, что пользователь не может получить никакие уведоления без отметки главной группы
		assertUserCanReceiveNotificationsByGroups(user, Collections.singletonList(11L), false, UserSubscriptionType.WHATSAPP);
	}


	/**
	 * Пользователь снимает галочки со всех групп и больше ни на что не подписан
	 */
	@Test
	public void _44_1_authorized_resetPushGroups_OK(){
		//Чистим последнее сохраненное событие
		testApplicationEventListener.setLastUserMobilePushEvent(null);
		User user = getUser();

		ResponseEntity<Api2Response<List<NotificationGroupDTO>>> response = apiV2Client.request(accountTestSupport.getPushGroupsUrl(), null, HttpMethod.PUT, null, new ParameterizedTypeReference<Api2Response<List<NotificationGroupDTO>>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());

		List<NotificationGroupDTO> groups = getPushGroupsSuccessfull(false);

		//Не отмечена ни одна из групп в ответе на запрос установки
		for(NotificationGroupDTO group : response.getBody().getData()){
			assertEquals(false, group.getIsSelected());
		}

		//Не отмечена ни одна из групп в ответе на запрос получения списка пользовательских групп
		for(NotificationGroupDTO group : groups){
			assertEquals(false, group.getIsSelected());
		}

		//Проверка на то, что пользователь не может получить никакие уведоления
		assertUserCanReceiveNotificationsByGroups(user, Arrays.asList(1L, 2L, 3L), false, UserSubscriptionType.MOBILE_PUSH);


		//Проверяем событие
		UserSubscriptionEvent lastEvent = testApplicationEventListener.getLastUserMobilePushEvent();
		//Событие дошло
		assertNotNull(lastEvent);
		assertEquals(user.getId(), lastEvent.getUserId());
		assertSame(UserSubscriptionType.MOBILE_PUSH, lastEvent.getType());
		//Проверяем что в событии не пришли никакие ID
		assertArrayEquals(new Long[0], lastEvent.getNotificationGroupIds().toArray(new Long[0]));
	}


	@Test
	public void _44_2_authorized_resetEmailGroups_OK(){
		//Чистим последнее сохраненное событие
		testApplicationEventListener.setLastUserMobilePushEvent(null);
		User user = getUser();

		ResponseEntity<Api2Response<List<NotificationGroupDTO>>> response = apiV2Client.request(accountTestSupport.getEmailGroupsUrl(), null, HttpMethod.PUT, null, new ParameterizedTypeReference<Api2Response<List<NotificationGroupDTO>>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());

		List<NotificationGroupDTO> groups = getEmailGroupsSuccessfull(false);

		//Не отмечена ни одна из групп в ответе на запрос установки
		for(NotificationGroupDTO group : response.getBody().getData()){
			assertEquals(false, group.getIsSelected());
		}

		//Не отмечена ни одна из групп в ответе на запрос получения списка пользовательских групп
		for(NotificationGroupDTO group : groups){
			assertEquals(false, group.getIsSelected());
		}

		//Проверка на то, что пользователь не может получить никакие уведоления
		assertUserCanReceiveNotificationsByGroups(user, Arrays.asList(7L, 8L, 9L), false, UserSubscriptionType.MOBILE_PUSH);


		//Проверяем событие
		UserSubscriptionEvent lastEvent = testApplicationEventListener.getLastUserMobilePushEvent();
		//Событие дошло
		assertNotNull(lastEvent);
		assertEquals(user.getId(), lastEvent.getUserId());
		assertSame(UserSubscriptionType.EMAIL, lastEvent.getType());
		//Проверяем что в событии не пришли никакие ID
		assertArrayEquals(new Long[0], lastEvent.getNotificationGroupIds().toArray(new Long[0]));
	}

	@Test
	public void _44_3_authorized_resetEmailGroups_OK(){
		//Чистим последнее сохраненное событие
		testApplicationEventListener.setLastUserMobilePushEvent(null);
		User user = getUser();

		ResponseEntity<Api2Response<List<NotificationGroupDTO>>> response = apiV2Client.request(accountTestSupport.getWhatsappGroupsUrl(), null, HttpMethod.PUT, null, new ParameterizedTypeReference<Api2Response<List<NotificationGroupDTO>>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());

		List<NotificationGroupDTO> groups = getWhatsappGroupsSuccessfull(false);

		//Не отмечена ни одна из групп в ответе на запрос установки
		for(NotificationGroupDTO group : response.getBody().getData()){
			assertEquals(false, group.getIsSelected());
		}

		//Не отмечена ни одна из групп в ответе на запрос получения списка пользовательских групп
		for(NotificationGroupDTO group : groups){
			assertEquals(false, group.getIsSelected());
		}

		//Проверка на то, что пользователь не может получить никакие уведоления
		assertUserCanReceiveNotificationsByGroups(user, Collections.singletonList(10L), false, UserSubscriptionType.WHATSAPP);
	}

	/**
	 * Проверяет список ID пользователей, на которых я подписан
	 */
	@Test
	public void _50_authorized_followingIds_OK(){
		User me = getUser();
		//Отписываемся от всех
		followingService.unfollowAll(me);
		//Берем 10 первых продавцов и подписываемся на них
		List<Long> followingIds = userRepository.findSellerIds().subList(0, 10);
		for(Long followingId : followingIds){
			User following = userService.getUserById(followingId).orElse(null);
			followingService.follow(me, following);
		}
		//Запрашиваем список ID пользователей на которых мы подписаны
		ResponseEntity<Api2Response<List<Long>>> response = apiV2Client.request(accountTestSupport.getFollowingIds(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<Long>>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		List<Long> followingIdsFromResponse = response.getBody().getData();
		assertNotNull(followingIdsFromResponse);
		//Убеждаемся, что он эквивалентен списку из 10-ти продавцов на которых мы подписались
		assertEquals(new HashSet<>(followingIds), new HashSet<>(followingIdsFromResponse));
	}

	@Test
	public void _51_getAccountCreds_OK() {
		ResponseEntity<Api2Response<AccountCredsDTO>> response = apiV2MasterClient.request(accountTestSupport.getCredsUrl(),
				TestUtils.getOneParamAsMap("accountEmail", email), HttpMethod.GET, null,
				new ParameterizedTypeReference<Api2Response<AccountCredsDTO>>() {}, true);

		assertTrue(response.getStatusCode().is2xxSuccessful());
		AccountCredsDTO credsDTO = response.getBody().getData();
		assertNotNull(credsDTO);
		assertEquals(credsDTO.getId(), userId);
		assertEquals(credsDTO.getEmail(), email);
		assertEquals(credsDTO.getNickname(), userNickname);

		String sentHashPass = TestUtils.getApiHashedPassword(password, email);
		assertEquals(credsDTO.getApiHashedPassword(), TestUtils.getApiHashedPassword(sentHashPass, email));
	}

	@Test
	public void _51_getAccountCreds_unauthorized_FAILED() {
		ResponseEntity<Api2Response<String>> response = apiV2Client.request(accountTestSupport.getCredsUrl(),
				TestUtils.getOneParamAsMap("accountEmail", email), HttpMethod.GET, null,
				new ParameterizedTypeReference<Api2Response<String>>() {}, false);

		assertSame(HttpStatus.FORBIDDEN, response.getStatusCode());
	}

	@Test
	public void _51_getAccountCreds_notMaster_FAILED() {
		ResponseEntity<Api2Response<String>> response = apiV2Client.request(accountTestSupport.getCredsUrl(),
				TestUtils.getOneParamAsMap("accountEmail", email), HttpMethod.GET, null,
				new ParameterizedTypeReference<Api2Response<String>>() {}, true);

		assertSame(HttpStatus.FORBIDDEN, response.getStatusCode());
	}

	//////////////////////////////////////

	@Test
	@Transactional
	@Rollback(value = false)
	public void _90_cleanSuccessfull_0(){
		Order order = orderRepository.findById(orderId).orElse(null);
		order.setDeliveryAddressEndpoint(null);
		orderRepository.saveAndFlush(order);
	}

	@Test
	@Transactional
	@Rollback(value = false)
	public void _90_cleanSuccessfull_1(){
		if(newUser != null) userRepository.delete(newUser);

		AccountDTO accountDTO = getAccountSuccessful();
		int initialCounterpartiesCount = listSize(accountDTO.getCounterparties());

		if(counterpartyId != null) assertEquals(counterpartyId, deleteCounterpartySeccessfull(counterpartyId).getId());
		if(counterparty2Id != null) assertEquals(counterparty2Id, deleteCounterpartySeccessfull(counterparty2Id).getId());

		AccountDTO changedAccountDTO = getAccountSuccessful();
		int changedCounterpartiesCount = listSize(changedAccountDTO.getCounterparties());

		addressEndpointRepository.findById(addressEndpointId).ifPresent(addressEndpointRepository::delete);
		addressEndpointRepository.findById(addressEndpoint2Id).ifPresent(addressEndpointRepository::delete);

		assertSame(initialCounterpartiesCount, changedCounterpartiesCount + 2);

		Address address = addressService.getAddress(getUser(), getAddressDTO());
		addressRepository.delete(address);

	}

	private AddressDTO getAddressDTO(){
		return new AddressDTO().setZipCode(zipCode).setCountry(country).setRegion(region)
				.setCity(city).setCityFiasId(cityFiasId).setAddress(address);
	}

	private CounterpartyDTO getCounterpartyIPDTO(){
		return new CounterpartyDTO().setType(CounterpartyDTO.CounterpartyType.IP).setJurAddress(getAddressDTO())
				.setOrgn(cntOGRNIP).setInn(cntInnIp)
				.setBik(cntBik).setCorrespondentAccount(cntCorrespondingAccount).setPaymentAccount(cntPaymentAccount)
				.setFirstName(cntFirstName).setPatronymicName(cntPatronymicName).setLastName(cntLastName);
	}

	private CounterpartyDTO getCounterpartyJurDTO(){
		return new CounterpartyDTO().setType(CounterpartyDTO.CounterpartyType.JUR).setJurAddress(getAddressDTO())
				.setPhysAddress(getAddressDTO()).setOrgn(cntOGRN).setInn(cntInnJur)
				.setBik(cntBik).setCorrespondentAccount(cntCorrespondingAccount).setPaymentAccount(cntPaymentAccount)
				.setFirstName(cntFirstName).setPatronymicName(cntPatronymicName).setLastName(cntLastName)
				.setCompanyForm(cntCompanyForm).setCompanyName(cntCompanyName).setKpp(cntKPP);
	}

	private CounterpartyDTO getCounterpartyCardDTO() {
		return new CounterpartyDTO().setType(CounterpartyDTO.CounterpartyType.CARD).setCardBrand("VISA").setCardBindBank("TCB").setCardHolder("Card Holder")
				.setCardRefId("0");
	}

	@NonNull
	private CounterpartyDTO getCounterpartyInternationalDTO() {
		CountryDTO countryDTO = countryService.findById(2).map(it -> modelMapper.map(it, CountryDTO.class)).get();
		return new CounterpartyDTO()
				.setType(CounterpartyType.INTERNATIONAL)
				.setFirstName(cntFirstName)
				.setPatronymicName(cntPatronymicName)
				.setLastName(cntLastName)
				.setPaymentAccount(cntPaymentAccount)
				.setIban(CNT_IBAN)
				.setSwiftCode(CNT_SWIFT_CODE)
				.setRoutingNumber(CNT_ROUTING)
				.setIsActive(true)
				.setCountryCounterpartyType(CountryCounterpartyType.UAE_COUNTERPARTY)
				.setCountry(countryDTO);
	}

	private AddressEndpointDTO getAddressEndpointDTO(){
		return new AddressEndpointDTO().setAddress(getAddressDTO()).setPhone(phone)
				.setFirstName(cntFirstName).setPatronymicName(cntPatronymicName).setLastName(cntLastName);
	}

	private AccountDTO getAccountSuccessful(){
		return getAccountSuccessful(true);
	}

	private AccountDTO getAccountSuccessful(boolean withAuthorizeParams){
		return getAccountSuccessful(apiV2Client, withAuthorizeParams);
	}

	private AccountDTO getAccountSuccessful(ApiV2Client apiV2Client, boolean withAuthorizeParams){
		return accountTestSupport.getAccountSuccessful(apiV2Client, withAuthorizeParams);
	}

	private AccountDTO updateAccountSuccessful(AccountDTO accountDTO){
		return accountTestSupport.updateAccountSuccessful(accountDTO, apiV2Client, false);
	}

	private User.Sex getSexSuccessful(boolean withAuthorizeParams){
		ResponseEntity<Api2Response<User.Sex>> response = apiV2Client.request(accountTestSupport.getSexUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<User.Sex>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		return response.getBody().getData();
	}

	private User.Sex setSexSuccessful(User.Sex sex, boolean withAuthorizeParams){
		ResponseEntity<Api2Response<User.Sex>> response = apiV2Client.request(accountTestSupport.getSexUrl(), TestUtils.getOneParamAsMap("sex", sex.name()), HttpMethod.PUT, null, new ParameterizedTypeReference<Api2Response<User.Sex>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		return response.getBody().getData();
	}

	private void assertAccountIsCorrect(AccountDTO accountDTO){
		User user = userService.getUserById(accountDTO.getId()).orElse(null);
		assertEquals(userId, accountDTO.getId());
		assertEquals(user.getNickname(), accountDTO.getNickname());
		assertEquals(user.getEmail(), accountDTO.getEmail());
		assertEquals(user.getPhone(), accountDTO.getPhone());
		assertSame(user.getSex(), accountDTO.getSex());
		assertEquals(Utils.getSecondsFromLocalDateTime(user.getBirthDate()), accountDTO.getBirthDate());
		assertEquals(likeService.countByLiker(user, Product.class), accountDTO.getProductLikesCount().intValue());
		assertEquals(likeService.countByLiker(user, Brand.class), accountDTO.getBrandLikesCount().intValue());
		assertEquals(likeService.countByUser(user), accountDTO.getLikesCount().intValue());
	}

	private List<NotificationGroupDTO> getPushGroupsSuccessfull(boolean withAuthorizeParams){
		ResponseEntity<Api2Response<List<NotificationGroupDTO>>> response = apiV2Client.request(accountTestSupport.getPushGroupsUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<NotificationGroupDTO>>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		assertNotNull(response.getBody().getData());
		assertFalse(response.getBody().getData().isEmpty());
		return response.getBody().getData();
	}

	private List<NotificationGroupDTO> getEmailGroupsSuccessfull(boolean withAuthorizeParams){
		ResponseEntity<Api2Response<List<NotificationGroupDTO>>> response = apiV2Client.request(accountTestSupport.getEmailGroupsUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<NotificationGroupDTO>>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		assertNotNull(response.getBody().getData());
		assertFalse(response.getBody().getData().isEmpty());
		return response.getBody().getData();
	}

	private List<NotificationGroupDTO> getWhatsappGroupsSuccessfull(boolean withAuthorizeParams){
		ResponseEntity<Api2Response<List<NotificationGroupDTO>>> response = apiV2Client.request(accountTestSupport.getWhatsappGroupsUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<NotificationGroupDTO>>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		assertNotNull(response.getBody().getData());
		assertFalse(response.getBody().getData().isEmpty());
		return response.getBody().getData();
	}

	private CounterpartyDTO deleteCounterpartySeccessfull(Long counterpartyId){
		ResponseEntity<Api2Response<CounterpartyDTO>> response = apiV2Client.request(accountTestSupport.getCounterpartyUrl(), TestUtils.getOneParamAsMap("counterpartyId", counterpartyId), HttpMethod.DELETE, null, new ParameterizedTypeReference<Api2Response<CounterpartyDTO>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		assertNotNull(response.getBody().getData());
		return response.getBody().getData();
	}

	private AddressEndpointDTO deleteAddressEndpointSuccessfull(Long addressEndpointId){
		ResponseEntity<Api2Response<AddressEndpointDTO>> response = apiV2Client.request(accountTestSupport.getAddressEndpointUrl(), TestUtils.getOneParamAsMap("addressEndpointId", addressEndpointId), HttpMethod.DELETE, null, new ParameterizedTypeReference<Api2Response<AddressEndpointDTO>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		assertNotNull(response.getBody().getData());
		return response.getBody().getData();
	}

	private int listSize(List list){
		if(list == null || list.isEmpty()) return 0;
		return list.size();
	}

	private void assertRegisterValidationFailed(UserService.EmailRegistrationRequest request, Map<String, String> errors){
		ResponseEntity<ValidationFailedResponse> response = registrationApiV2Client.request(accountTestSupport.getRegisterUrl(), null, HttpMethod.POST, TestUtils.getMultivalueMapWithObjectFields(request), new ParameterizedTypeReference<ValidationFailedResponse>() {}, false);
		assertTrue(response.getStatusCode().is4xxClientError());
		ValidationFailedResponse validationFailedResponse = response.getBody();
		assertNotNull(validationFailedResponse);
		assertTrue(validationFailedResponse.containsOnlyErrors(errors));
	}

	private void assertSocialRegisterReturnsFailed(OAuthRegistrationRequest request, String expectedMessage){
		ResponseEntity<String> response = registrationApiV2Client.request(accountTestSupport.getSocialRegisterUrl(), null, HttpMethod.POST, request, String.class, false);
		assertTrue(response.getStatusCode().is4xxClientError() || response.getStatusCode().is5xxServerError());
		String responseBody = response.getBody();
		assertNotNull(responseBody);
		assertTrue(responseBody.replace(" : ", ":").contains("\"message\":\"" + expectedMessage));
	}

	private void assertSocialRegisterValidationFailed(OAuthRegistrationRequest request, Map<String, String> errors){
		ResponseEntity<ValidationFailedResponse> response = registrationApiV2Client.request(accountTestSupport.getSocialRegisterUrl(), null, HttpMethod.POST, request, new ParameterizedTypeReference<ValidationFailedResponse>() {}, false);
		assertTrue(response.getStatusCode().is4xxClientError());
		ValidationFailedResponse validationFailedResponse = response.getBody();
		assertNotNull(validationFailedResponse);
		assertTrue(validationFailedResponse.containsOnlyErrors(errors));
	}

	private OAuthRegistrationResult socialRegisterSuccess(@NonNull OAuthRegistrationRequest request, @NonNull Class<? extends OAuthRegistrationResult> clazz){
		ResponseEntity<Api2Response> response = registrationApiV2Client.request(accountTestSupport.getSocialRegisterUrl(), null, HttpMethod.POST, request, Api2Response.class, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		Api2Response responseBody = response.getBody();
		assertNotNull(responseBody);
		assertNotNull(responseBody.getData());
		try {
			String dataJSon = objectMapper.writeValueAsString(responseBody.getData());
			return objectMapper.readValue(dataJSon, clazz);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}

	private void assertResetPasswordValidationFailed(String email, Map<String, String> errors){
		MultiValueMap<String, String> params = email == null ? null : TestUtils.getOneParamAsMultiValueMap("email", email);
		ResponseEntity<ValidationFailedResponse> response = registrationApiV2Client.request(accountTestSupport.getResetPasswordUrl(), null, HttpMethod.POST, params, new ParameterizedTypeReference<ValidationFailedResponse>() {}, false);
		assertTrue(response.getStatusCode().is4xxClientError());
		ValidationFailedResponse validationFailedResponse = response.getBody();
		assertNotNull(validationFailedResponse);
		assertTrue(validationFailedResponse.containsOnlyErrors(errors));
	}

	private void assertSetPasswordValidationFailed(SecurityService.NewPasswordRequest request, Map<String, String> errors){
		ResponseEntity<ValidationFailedResponse> response = registrationApiV2Client.request(accountTestSupport.getSetPasswordUrl(), null, HttpMethod.PUT, TestUtils.getMultivalueMapWithObjectFields(request), new ParameterizedTypeReference<ValidationFailedResponse>() {}, false);
		assertTrue(response.getStatusCode().is4xxClientError());
		ValidationFailedResponse validationFailedResponse = response.getBody();
		assertNotNull(validationFailedResponse);
		assertTrue(validationFailedResponse.containsOnlyErrors(errors));
	}

	private void assertUpdatePasswordAuthFailed(SecurityService.UpdatePasswordRequest request){
		ResponseEntity<String> response = registrationApiV2Client.request(accountTestSupport.getUpdatePasswordUrl(), null, HttpMethod.PATCH, TestUtils.getMultivalueMapWithObjectFields(request), String.class, false);
		assertTrue(response.getStatusCode() == HttpStatus.FORBIDDEN);
		assertTrue(TestUtils.hasError(response.getBody(), AccessDeniedException.class));
	}

	private void assertUpdatePasswordValidationFailed(SecurityService.UpdatePasswordRequest request, Map<String, String> errors, boolean withAuthorizeParams){
		ResponseEntity<ValidationFailedResponse> response = registrationApiV2Client.request(accountTestSupport.getUpdatePasswordUrl(), null, HttpMethod.PATCH, TestUtils.getMultivalueMapWithObjectFields(request), new ParameterizedTypeReference<ValidationFailedResponse>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is4xxClientError());
		ValidationFailedResponse validationFailedResponse = response.getBody();
		assertNotNull(validationFailedResponse);
		assertTrue(validationFailedResponse.containsOnlyErrors(errors));
	}

	private void setPasswordValidationSuccessful(SecurityService.NewPasswordRequest request){
		ResponseEntity<Api2Response<Boolean>> response = registrationApiV2Client.request(accountTestSupport.getSetPasswordUrl(), null, HttpMethod.PUT, TestUtils.getMultivalueMapWithObjectFields(request), new ParameterizedTypeReference<Api2Response<Boolean>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		Api2Response<Boolean> resultObj = response.getBody();
		assertNotNull(resultObj);
		assertTrue(resultObj.getData());
		assertEquals("Пароль установлен", resultObj.getMessage());
	}

	private void updatePasswordValidationSuccessful(SecurityService.UpdatePasswordRequest request){
		ResponseEntity<Api2Response<Boolean>> response = registrationApiV2Client.request(accountTestSupport.getUpdatePasswordUrl(), null, HttpMethod.PATCH, TestUtils.getMultivalueMapWithObjectFields(request), new ParameterizedTypeReference<Api2Response<Boolean>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		Api2Response<Boolean> resultObj = response.getBody();
		assertNotNull(resultObj);
		assertTrue(resultObj.getData());
		assertEquals("Пароль изменен", resultObj.getMessage());
	}

	//Авторизация по сырому (незашифрованному) паролю
	private long rawAuthSuccessful(String email, String password){
		return accountTestSupport.rawAuthSuccessful(registrationApiV2Client, email, password);
	}

	private List<String> getNotificationGroupDtypes(Long notificationGroupId){
		NotificationGroup notificationGroup = notificationService.getNotificationGroup(notificationGroupId);
		return notificationGroup.getNotificationGroupDtypeBindings().stream().map(ngdb -> ngdb.getNotificationDtype()).collect(Collectors.toList());
	}

	private void assertUserCanReceiveNotificationsByGroup(User user, Long notificationGroupId, boolean canReceive, UserSubscriptionType subscriptionType){
		List<String> dtypes = getNotificationGroupDtypes(notificationGroupId);
		for(String dtype : dtypes){
			assertEquals(canReceive, notificationService.shouldSendNotification(user, dtype, subscriptionType));
		}
	}

	private void assertUserCanReceiveNotificationsByGroups(User user, List<Long> notificationGroupIds, boolean canReceive, UserSubscriptionType subscriptionType){
		for(Long notificationGroupId : notificationGroupIds){
			assertUserCanReceiveNotificationsByGroup(user, notificationGroupId, canReceive, subscriptionType);
		}
	}

	private void assertUploadAvatarFailedByName(String fileName, Map<String, String> expectedErrors) throws IOException, URISyntaxException {
		URL url = this.getClass().getClassLoader().getResource("img/profile/" + fileName);
		File file = Paths.get(url.toURI()).toFile();
		assertUploadAvatarFailed(file, expectedErrors, true);
	}

	private void assertUploadAvatarFailed(File file, Map<String, String> expectedErrors, boolean withAuthorizeParams) throws IOException {
		MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
		if(file != null && file.exists()) {
			FileSystemResource fileResource = new FileSystemResource(file);
			body.add("image", fileResource);
		}

		ResponseEntity<ValidationFailedResponse> response = apiV2Client.request(accountTestSupport.getUploadAvatarUrl(), null, HttpMethod.POST, MediaType.MULTIPART_FORM_DATA, body, new ParameterizedTypeReference<ValidationFailedResponse>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is4xxClientError());
		ValidationFailedResponse validationFailedResponse = response.getBody();
		assertNotNull(validationFailedResponse);
		assertTrue(validationFailedResponse.containsOnlyErrors(expectedErrors));
	}

	private String assertUploadAvatarSuccessfulByName(String fileName) throws IOException, URISyntaxException {
		URL url = this.getClass().getClassLoader().getResource("img/profile/" + fileName);
		File file = Paths.get(url.toURI()).toFile();
		return assertUploadAvatarSuccessful(file, true);
	}

	private String assertUploadAvatarSuccessful(File file, boolean withAuthorizeParams) throws IOException {
		MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
		if(file != null && file.exists()) {
			FileSystemResource fileResource = new FileSystemResource(file);
			body.add("image", fileResource);
		}

		ResponseEntity<Api2Response<String>> response = apiV2Client.request(accountTestSupport.getUploadAvatarUrl(), null, HttpMethod.POST, MediaType.MULTIPART_FORM_DATA, body, new ParameterizedTypeReference<Api2Response<String>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		String imageUrl = response.getBody().getData();
		TestUtils.assertImageAvailable(TestUtils.getUrlPath(testApiConfiguration.getServerUrl(), imageUrl), tmpDirPath);
		return imageUrl;
	}

	private UserfileDTO assertUploadUserfileSuccessfulByName(String fileName, String title) throws IOException, URISyntaxException {
		return assertUploadUserfileSuccessful(getUserfileForUploadByName(fileName), title, true);
	}

	private File getUserfileForUploadByName(String fileName) throws IOException, URISyntaxException{
		URL url = this.getClass().getClassLoader().getResource("userfiles/" + fileName);
		return Paths.get(url.toURI()).toFile();
	}

	private UserfileDTO assertUploadUserfileSuccessful(File file, String title, boolean withAuthorizeParams) throws IOException {
		MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
		if(file != null && file.exists()) {
			FileSystemResource fileResource = new FileSystemResource(file);
			body.add("file", fileResource);
			body.add("title", title);
		}

		ResponseEntity<Api2Response<UserfileDTO>> response = apiV2Client.request(accountTestSupport.getUserfilesUrl(), null, HttpMethod.POST, MediaType.MULTIPART_FORM_DATA, body, new ParameterizedTypeReference<Api2Response<UserfileDTO>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		UserfileDTO result = response.getBody().getData();
		String fileUrl = result.getPath();
		TestUtils.assertFileAvailable(TestUtils.getUrlPath(testApiConfiguration.getServerUrl(), fileUrl), tmpDirPath);
		assertEquals(title, result.getTitle());
		return result;
	}

	@NonNull
	private <REQUEST_TYPE, RESPONSE_TYPE> RESPONSE_TYPE request(
			@NonNull final String url,
			@NonNull final HttpMethod httpMethod,
			@NonNull final REQUEST_TYPE requestBody,
			@NonNull final ParameterizedTypeReference<Api2Response<RESPONSE_TYPE>> responseType) {
		final ResponseEntity<Api2Response<RESPONSE_TYPE>> response = apiV2Client.request(
				url, null, httpMethod, requestBody, responseType, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		final RESPONSE_TYPE responseEntity = response.getBody().getData();
		assertNotNull(responseEntity);
		return responseEntity;
	}
}
