package ru.oskelly.tests.pr.suite3.presentation.api.v2;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.TestApiConfiguration;
import su.reddot.domain.dao.address.*;
import su.reddot.domain.model.address.*;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.address.DefaultCityUserSearchHistoryService;
import su.reddot.domain.service.dto.AddressDTO;
import su.reddot.domain.service.dto.CityDTO;
import su.reddot.domain.service.dto.CountryDTO;
import su.reddot.domain.service.user.UserService;
import su.reddot.presentation.api.v2.Api2Response;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_03)
public class AddressControllerV2Test extends AbstractSpringTest {
    @Autowired
    private TestApiConfiguration testApiConfiguration;
    @Value("${test.api.user-email}")
    private String email;
    @Value("${test.api.user-password}")
    private String password;

    //Клиент для работы с API
    static ApiV2Client apiV2Client;

    @Autowired
    private CountryRepository countryRepository;
    @Autowired
    private CityRepository cityRepository;
    @Autowired
    private UserService userService;
    @Autowired
    private DefaultCityUserSearchHistoryService defaultCityUserSearchHistoryService;

    @Autowired
    private AddressSearchHistoryRepository addressSearchHistoryRepository;

    private String getServiceUrl() {
        return testApiConfiguration.getServerUrl() + "/api/v2/address";
    }

    private String getBigCitiesUrl() {
        return getServiceUrl() + "/big-cities";
    }

    private String getSearchHistoryUrl() {
        return getServiceUrl() + "/search-history";
    }

    private Map<Integer, String> bigCitiesMap = new LinkedHashMap<Integer, String>() {{
        put(129, "Москва");
        put(112, "Санкт-Петербург");
        put(100, "Новосибирск");
        put(86, "Казань");
        put(83, "Екатеринбург");
    }};

    @BeforeEach
    public void initialize() {
        if (apiV2Client == null) {
            apiV2Client = new ApiV2Client(email, password);
        }
    }

    @Test
    public void testSearchCitiesByKeyWord() {
        Country country = countryRepository.findByIsoCodeAlpha2("RU"); // Russia

        Map<String, String> params = new HashMap<>();
        params.put("query", "Moscow");
        params.put("countryId", String.valueOf(country.getId()));
        params.put("supportedOnly", "false");
        ResponseEntity<Api2Response<List<CityDTO>>> response = apiV2Client.request(getServiceUrl() + "/cities-int", params, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<CityDTO>>>() {
        }, false);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());

        List<CityDTO> data = response.getBody().getData();
        assertNotEquals(0, data.size());
    }

    @Test
    public void testSearchCitiesHistory() {
        Country country = countryRepository.findByIsoCodeAlpha2("RU");
        City city = cityRepository.findById(162252L).orElse(null); //Moscow

        assertNotNull(city);
        assertNotNull(country);

        User userByEmail = userService.getUserByEmail(email);

        Map<String, String> params = new HashMap<>();
        params.put("countryId", String.valueOf(country.getId()));

        ResponseEntity<Api2Response<Boolean>> putResponse = apiV2Client.request(getServiceUrl() + "/city-search-history-int/" + city.getId(), null, HttpMethod.PUT, null, new ParameterizedTypeReference<Api2Response<Boolean>>() {
        }, true);
        assertTrue(putResponse.getStatusCode().is2xxSuccessful());
        assertNotNull(putResponse.getBody());
        assertNotNull(putResponse.getBody().getData());
        ResponseEntity<Api2Response<List<CityDTO>>> response = apiV2Client.request(getServiceUrl() + "/city-search-history-int", params, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<CityDTO>>>() {
        }, true);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());

        List<CityDTO> data = response.getBody().getData();
        assertNotEquals(0, data.size());

        defaultCityUserSearchHistoryService.deleteByUserId(userByEmail.getId());
    }

    @Test
    public void testSearchBitCities() {
        Map<String, String> params = new HashMap<>();
        params.put("query", "Moscow");
        params.put("countryId", "191");
        params.put("supportedOnly", "false");
        ResponseEntity<Api2Response<List<CityDTO>>> response = apiV2Client.request(getServiceUrl() + "/cities-int", params, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<CityDTO>>>() {
        }, false);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());

        List<CityDTO> data = response.getBody().getData();
        assertNotEquals(0, data.size());
    }

    @Test
    public void testGetCountriesList() {
        Map<String, String> params = new HashMap<>();
        params.put("context", CountryContextNameEnum.APPLICATION.name());
        ResponseEntity<Api2Response<List<CountryDTO>>> response = apiV2Client.request(getServiceUrl() + "/countries", params, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<CountryDTO>>>() {
        }, false);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());

        List<CountryDTO> data = response.getBody().getData();
        assertNotEquals(0, data.size());
    }

    //Проверка городов-миллионников
    @Test
    public void testBigCities() {
        List<AddressDTO> bigCities = getBigCities();
        List<Integer> cityKeys = new ArrayList<>(bigCitiesMap.keySet());
        for (int i = 0; i < cityKeys.size(); i++) {
            //short cityCode = cityKeys.get(i).shortValue();
            String cityName = bigCitiesMap.get(cityKeys.get(i));
            AddressDTO resultCity = bigCities.get(i);
            assertEquals(cityName, resultCity.getCity());
        }
    }

    //История поиска по адресам для гостя
    @Test
    public void testSearchHistoryGuest() {
        addressSearchHistoryRepository.deleteAll();
        apiV2Client.logout();
        int searchHistorySize = 5;
        List<AddressDTO> bigCities = getBigCities();
        //Сохраним города миллионники в истории поиска. Каждый по 2 раза.
        for (AddressDTO addressDTO : bigCities) {
            addToSearchHistory(addressDTO);
            addToSearchHistory(addressDTO);
        }
        //Получаем историю поиска и проверяем корректноть. Должны быть последние 5 городов миллионников
        List<AddressDTO> searchHistoryCities = getSearchHistory(false);
        assertEquals(searchHistorySize, searchHistoryCities.size());
        for (int i = 0; i < searchHistorySize; i++) {
            int j = bigCities.size() - i - 1;
            assertEquals(bigCities.get(j), searchHistoryCities.get(i));
        }

        //Авторизованный пользователь с той же гостевой кукой видит то же самое.
        List<AddressDTO> searchHistoryCitiesAuthorized = getSearchHistory(true);
        assertEquals(searchHistoryCities, searchHistoryCitiesAuthorized);

        //Авторизованный пользователь с другой гостевой кукой видит пустой список.
        apiV2Client.clearCookies();
        List<AddressDTO> searchHistoryCitiesAuthorizedNewCookie = getSearchHistory(true);
        assertNull(searchHistoryCitiesAuthorizedNewCookie);

        //Мы авторизованы, кука другая (история чиста)
        //Сохраним города миллионники в истории поиска в обратном порядке. Каждый по 3 раза.
        for (int i = bigCities.size() - 1; i >= 0; i--) {
            AddressDTO addressDTO = bigCities.get(i);
            addToSearchHistory(addressDTO);
            addToSearchHistory(addressDTO);
            addToSearchHistory(addressDTO);
        }
        //Получаем историю поиска и проверяем корректноть. Должны быть первые 5 городов миллионников
        searchHistoryCities = getSearchHistory(false);
        assertEquals(searchHistorySize, searchHistoryCities.size());
        for (int i = 0; i < searchHistorySize; i++) {
            assertEquals(bigCities.get(i), searchHistoryCities.get(i));
        }
    }


    private List<AddressDTO> getBigCities() {
        ResponseEntity<Api2Response<List<AddressDTO>>> response = apiV2Client.request(getBigCitiesUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<AddressDTO>>>() {
        }, false);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        return response.getBody().getData();
    }

    private List<AddressDTO> getSearchHistory(boolean withAuthorizeParams) {
        ResponseEntity<Api2Response<List<AddressDTO>>> response = apiV2Client.request(getSearchHistoryUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<AddressDTO>>>() {
        }, withAuthorizeParams);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        return response.getBody().getData();
    }

    private void addToSearchHistory(AddressDTO addressDTO) {
        ResponseEntity<Api2Response<Boolean>> response = apiV2Client.request(getSearchHistoryUrl(), null, HttpMethod.PUT, addressDTO, new ParameterizedTypeReference<Api2Response<Boolean>>() {
        }, false);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().getData());
    }

}
