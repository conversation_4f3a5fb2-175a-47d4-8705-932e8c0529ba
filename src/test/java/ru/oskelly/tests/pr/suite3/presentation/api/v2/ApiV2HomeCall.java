package ru.oskelly.tests.pr.suite3.presentation.api.v2;

import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import su.reddot.domain.model.adminpanel.v2.PrimaryPageType;
import su.reddot.domain.service.dto.primary.AdditionalBannerDTO;
import su.reddot.domain.service.dto.primary.BannerDTO;
import su.reddot.domain.service.dto.primary.PrimaryContentDTO;
import su.reddot.presentation.api.v2_1.Api2_1Response;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

public class ApiV2HomeCall {

    public static Api2_1Response<PrimaryContentDTO<AdditionalBannerDTO>> promoBanner(ApiV2Client apiV2Client, String apiUrl, Long countryId) {
        String url = apiUrl + "/api/v2/home/<USER>";
        Map<String, String> reqGetParams = new HashMap<>();
        Optional.ofNullable(countryId).ifPresent(it -> reqGetParams.put("countryId", it.toString()));
        //
        ResponseEntity<Api2_1Response<PrimaryContentDTO<AdditionalBannerDTO>>> responseData = apiV2Client.request(url,
                reqGetParams,
                HttpMethod.GET,
                MediaType.APPLICATION_JSON,
                null,
                new ParameterizedTypeReference<Api2_1Response<PrimaryContentDTO<AdditionalBannerDTO>>>() {},
                Objects.nonNull(apiV2Client.getEmail()));
        return responseData.getBody();
    }

    public static Api2_1Response<List<BannerDTO>> primaryBanners(ApiV2Client apiV2Client, String apiUrl, PrimaryPageType category, Long countryId) {
        String url = apiUrl + "/api/v2/home/<USER>";
        Map<String, String> reqGetParams = new HashMap<>();
        Optional.ofNullable(countryId).ifPresent(it -> reqGetParams.put("countryId", it.toString()));
        Optional.ofNullable(category).ifPresent(it -> reqGetParams.put("category", it.toString()));
        //
        ResponseEntity<Api2_1Response<List<BannerDTO>>> responseData = apiV2Client.request(url,
                reqGetParams,
                HttpMethod.GET,
                MediaType.APPLICATION_JSON,
                null,
                new ParameterizedTypeReference<Api2_1Response<List<BannerDTO>>>() {},
                Objects.nonNull(apiV2Client.getEmail()));
        return responseData.getBody();
    }

}
