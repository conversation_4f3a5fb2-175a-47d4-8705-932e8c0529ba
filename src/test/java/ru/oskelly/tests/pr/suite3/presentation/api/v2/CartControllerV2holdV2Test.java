package ru.oskelly.tests.pr.suite3.presentation.api.v2;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.TestMethodOrder;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;

@Slf4j
@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_03)
public class CartControllerV2holdV2Test extends CartControllerV2Test {

    @Override
    protected String getHoldProcedureName(){
        return CartTestSupport.HOLD_V2_ENDPOINT;
    }


}
