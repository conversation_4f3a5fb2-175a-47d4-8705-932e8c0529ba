package ru.oskelly.tests.pr.suite3.presentation.api.v2;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.TestApiConfiguration;
import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.dto.order.adminpanel.AdminPanelOrderDTO;
import su.reddot.domain.service.dto.order.adminpanel.AdminPanelOrderDetailsDTO;
import su.reddot.presentation.api.v2.Api2Response;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_03)
public class OPOrderControllerApiV2Test extends AbstractSpringTest {
 	@Autowired
 	private TestApiConfiguration testApiConfiguration;
	@Value("${test.api.user-email}")
	private String adminEmail;
	@Value("${test.api.user-password}")
	private String adminPassword;
	@Value("${test.api.user2-email}")
	private String notadminEmail;
	@Value("${test.api.user2-password}")
	private String notadminPassword;

	static ApiV2Client adminClient;
	static ApiV2Client notadminClient;

	private static boolean initialized = false;

	@BeforeEach
	public void init(){
		if(initialized) return;
		cleanup();
		adminClient = new ApiV2Client(adminEmail, adminPassword);
		notadminClient = new ApiV2Client(notadminEmail, notadminPassword);
		initialized = true;
	}

	private void cleanup(){
		// Somthing to do
	}

	private String getServiceUrl() {
		return testApiConfiguration.getServerUrl() + "/api/v2/admin";
	}
	private String getOrdersUrl() {
		return getServiceUrl() + "/orders";
	}
	private String getOrderDetailsUrl() {
		return getServiceUrl() + "/orders/";
	}

	/**
	 * Без аавторизации недоступно
	 */
	@Test
	public void _01_01_getOrders_unauthorized_failed() {
		ResponseEntity<String> responseEntity = adminClient.request(getOrdersUrl(), null, HttpMethod.GET, null, String.class, false);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Доступ запрещен"));
	}

	/**
	 * С авторизацией заказы доступны
	 */
	@Test
	public void _01_02_getOrders_authorized_with_admin_OK() {
		ResponseEntity<Api2Response<Page<AdminPanelOrderDTO>>> responseEntity = adminClient.request(getOrdersUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<Page<AdminPanelOrderDTO>>>() {}, true);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody());
		assertTrue(responseEntity.getBody().getData().getItems().size() > 0); // default page size?
	}

	@Test
	public void _01_03_getOrders_authorized_with_not_admin_failed() {
		ResponseEntity<String> responseEntity = notadminClient.request(getOrdersUrl(), null, HttpMethod.GET, null, String.class, false);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Доступ запрещен"));
	}

	/**
	 * Without authentication order details are not available
	 */
	@Test
	public void _01_01_getOrderDetails_unauthorized_failed() {
		ResponseEntity<String> responseEntity = adminClient.request(getOrderDetailsUrl() + "1", null, HttpMethod.GET, null, String.class, false);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Доступ запрещен"));
	}

	/**
	 * With authentication order details are available
	 */
	@Test
	public void _01_02_getOrderDetails_authorized_with_admin_OK() {
		ResponseEntity<Api2Response<Page<AdminPanelOrderDTO>>> orders = adminClient.request(getOrdersUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<Page<AdminPanelOrderDTO>>>() {}, true);
		Long id = orders.getBody().getData().getItems().get(0).getId();

		ResponseEntity<Api2Response<AdminPanelOrderDetailsDTO>> responseEntity = adminClient.request(getOrderDetailsUrl() + id, null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<AdminPanelOrderDetailsDTO>>() {}, true);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody());
		assertNotNull(responseEntity.getBody().getData());
	}

	@Test
	public void _01_03_getOrderDetails_authorized_with_not_admin_failed() {
		ResponseEntity<String> responseEntity = notadminClient.request(getOrderDetailsUrl() + "1", null, HttpMethod.GET, null, String.class, false);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Доступ запрещен"));
	}

}