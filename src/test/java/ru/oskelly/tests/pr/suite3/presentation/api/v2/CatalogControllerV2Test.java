package ru.oskelly.tests.pr.suite3.presentation.api.v2;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.hibernate.Hibernate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.pr.common.bonuses.BonusesServiceTestConfiguration;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CatalogTestSupport;
import su.reddot.component.TestApiConfiguration;
import su.reddot.domain.dao.activity.ActivityRepository;
import su.reddot.domain.dao.activity.ActivityTestRepository;
import su.reddot.domain.dao.like.BaseLikeRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.activity.Activity;
import su.reddot.domain.model.activity.product.ViewProductActivity;
import su.reddot.domain.model.like.ProductLike;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.notification.like.FirstProductLikeNotification;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.activity.ActivityService;
import su.reddot.domain.service.catalog.*;
import su.reddot.domain.service.catalog.menu.CatalogMenu;
import su.reddot.domain.service.dto.*;
import su.reddot.domain.service.dto.size.SizeTypeDTO;
import su.reddot.domain.service.like.type.ToggleResult;
import su.reddot.domain.service.notification.NotificationService;
import su.reddot.domain.service.product.ProductInfoRequest;
import su.reddot.domain.service.product.ProductService;
import su.reddot.domain.service.product.item.ProductItemService;
import su.reddot.domain.service.user.UserService;
import su.reddot.presentation.api.v2.Api2Response;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@ContextConfiguration(classes = {OrderFlowTestUtils.class, BonusesServiceTestConfiguration.class})
@DevSuite(value = TestSuiteName.TEST_SUITE_03)
public class CatalogControllerV2Test extends AbstractSpringTest {
	@Value("${test.api.user-id}")
	private Long userId;
    @Value("${test.api.user-email}")
    private String email;
    @Value("${test.api.user-password}")
    private String password;

    @Autowired
	private TestApiConfiguration testApiConfiguration;

	@Autowired
	private ProductRepository productRepository;

	@Autowired
	private ActivityTestRepository<Activity> activityTestRepository;

	@Autowired
	private ActivityRepository<Activity> activityRepository;

	@Autowired
	private BaseLikeRepository likeRepository;

	@Autowired
	private NotificationService notificationService;

	@Autowired
	private CategoryService categoryService;

	@Autowired
	private ActivityService activityService;

	@Autowired
	private ProductService productService;

	@Autowired
	private ProductItemService productItemService;

	@Autowired
	private UserService userService;

	@Autowired
	private CatalogTestSupport catalogTestSupport;

	@Autowired
	private ObjectMapper objectMapper;

    private Long unexistedProductId = 1L;

    private static ApiV2Client apiV2Client;

    private long[] rootCategoryIds = {2L, 105L, 188L};

    @BeforeEach
    public void initialize() {
        if(apiV2Client == null) apiV2Client = new ApiV2Client(email, password);
        catalogTestSupport.setApiV2Client(apiV2Client);
    }

    private String getServiceUrl(){
        return testApiConfiguration.getServerUrl() + "/api/v2/catalog";
    }
    private String getProductsUrl(){ return getServiceUrl() + "/products"; }
	private String getMyLikedProductsUrl(){ return getProductsUrl() + "/liked"; }
	private String getMyLastSeenProductsUrl(){ return getProductsUrl() + "/lastSeen"; }
	private String getMyFollowedProductsUrl(){ return getProductsUrl() + "/pricefollowings"; }
	private String getSingleProductUrl(Long productId){ return getProductsUrl() + "/" + productId; }
	private String getProductLikeUrl(Long productId){ return getProductsUrl() + "/" + productId + "/like"; }
	private String getProductDislikeUrl(Long productId){ return getProductsUrl() + "/" + productId + "/dislike"; }
	private String getProductToggleUrl(Long productId){ return getProductsUrl() + "/" + productId + "/toggle"; }
	private String getAvailableFiltersUrl(){ return getServiceUrl() + "/availableFilters"; }
	private String getSizeTreeUrl(){ return getServiceUrl() + "/sizeTree"; }
	private String getSizesUrl(Long categoryId){ return getServiceUrl() + "/sizes/" + categoryId; }
	private String getAttributeTreeUrl(){ return getServiceUrl() + "/attributeTree"; }
	private String getCategoryTreeUrl(){ return getServiceUrl() + "/categoryTree"; }

	private String getCategoriesUrl(Long categoryId){ return getServiceUrl() + "/categories/" + categoryId; }

	private String getBrandsUrl(){ return getServiceUrl() + "/brands"; }
	private String getBrandsTopUrl(){ return getBrandsUrl() + "/top"; }
	private String getSingleBrandByIdUrl(Long id){ return getBrandsUrl() + "/" + id; }
	private String getSingleBrandByUrlNameUrl(String urlName){ return getBrandsUrl() + "/name/" + urlName; }
	private String getMyLikedBrandsUrl(){ return getBrandsUrl() + "/liked"; }
	private String getBrandLikeUrl(Long brandId){ return getBrandsUrl() + "/" + brandId + "/like"; }
	private String getBrandDislikeUrl(Long brandId){ return getBrandsUrl() + "/" + brandId + "/dislike"; }
	private String getBrandToggleUrl(Long brandId){ return getBrandsUrl() + "/" + brandId + "/toggle"; }

	/////////// Guest //////////////

    @Test
    public void _00_getProductList_guest_noParams_successful(){
	    Page<ProductDTO> page = catalogTestSupport.getProductsPageSuccessful(null, false);
		assertFalse(page.getItems().isEmpty());
    }

	@Test
	public void _01_01_getProductList_guest_PUBLISHED_successful(){
		getAnySingleProductWithCheckingActivitySuccessful();
	}

	@Test
	public void _01_02_getProduct_guest_PUBLISHED_successful(){
		Page<ProductDTO> page = catalogTestSupport.getProductsPageSuccessful(catalogTestSupport.getFilterParams(ProductState.PUBLISHED), false);
		assertFalse(page.getItems().isEmpty());
	}

	@Test
	public void _02_getProductList_guest_NOT_PUBLISHED_forbidden(){
    	for(ProductState productState : ProductState.values()){
    		if(productState == ProductState.PUBLISHED) continue;
		    assertProductListForbidden(catalogTestSupport.getFilterParams(productState), false);
	    }
	}

	@Test
	public void _03_getProduct_NOT_FOUND(){
		assertProductNotFount(unexistedProductId);
	}

	/**
	 * Получаем доступные фильтры без дерева размеров
	 */
	@Test
	public void _04_01_getAvailableFilters_no_sizeTree_successful(){
		AvailableFilters availableFilters = getAvailableFilterssuccessful(null, false);
		assertNull(availableFilters.getSizeTree());
	}

	/**
	 * Получаем доступные фильтры с деревом размеров
	 */
	@Test
	public void _04_02_getAvailableFilters_with_sizeTree_successful(){
		AvailableFilters availableFilters = getAvailableFilterssuccessful(TestUtils.getOneParamAsMap("withSizeTree", "true"), false);
		assertNotNull(availableFilters.getSizeTree());
	}

	/**
	 * Получаем дерево размеров
	 */
	@Test
	public void _04_03_getSizeTree_successful(){
		SizeTree sizeTree = getSizeTreesuccessful(null, false);
		//Заодно сравним, соврадает ли результат с деревом, включенным в availableFilters
		AvailableFilters availableFilters = getAvailableFilterssuccessful(TestUtils.getOneParamAsMap("withSizeTree", "true"), false);
		assertEquals(sizeTree, availableFilters.getSizeTree());
	}

	/**
	 * Получаем список размеров
	 */
	@Test
	public void _04_04_getSizes_successful(){
		List<SizeTypeDTO> sizes = getSizesSuccessful(rootCategoryIds[0], false);
	}

	/**
	 * Получаем доступные фильтры без дерева атрибутов
	 */
	@Test
	public void _05_01_getAvailableFilters_no_attributeTree_successful(){
		AvailableFilters availableFilters = getAvailableFilterssuccessful(null, false);
		assertNull(availableFilters.getAttributeTree());
	}

	/**
	 * Получаем доступные фильтры с деревом атрибутов
	 */
	@Test
	public void _05_02_getAvailableFilters_with_attributeTree_successful(){
		AvailableFilters availableFilters = getAvailableFilterssuccessful(TestUtils.getOneParamAsMap("withAttributeTree", "true"), false);
		assertNotNull(availableFilters.getAttributeTree());
	}

	/**
	 * Получаем дерево атрибутов
	 */
	@Test
	public void _05_03_getAttributeTree_successful(){
		AttributeTree attributeTree = getAttributeTreeSuccessful(null, false);
		//Заодно сравним, совпадает ли результат с деревом, включенным в availableFilters
		AvailableFilters availableFilters = getAvailableFilterssuccessful(TestUtils.getOneParamAsMap("withAttributeTree", "true"), false);
		assertEquals(attributeTree, availableFilters.getAttributeTree());
	}

	/**
	 * Получаем дерево категорий
	 */
	@Test
	public void _05_04_getCategoryTree_successful(){
		CategoryTree categoryTree = getCategoryTreeSuccessful(null, false);
		//Заодно сравним, соврадает ли результат с деревом, включенным в availableFilters
		AvailableFilters availableFilters = getAvailableFilterssuccessful(TestUtils.getOneParamAsMap("withCategoryTree", "true"), false);
		assertEquals(categoryTree, availableFilters.getCategoryTree());
	}

	/**
	 * Получаем подкатегории
	 */
	@Test
	public void _05_06_getSubcategories_successful(){
		getSubCategoriesSuccessful(rootCategoryIds[0], false);
	}

	@Test
	public void _06_getBrandsTop_successful(){
		int brandsCount = 10;
		getTopBrandssuccessful(brandsCount, null);
		for(long rootCategoryId : rootCategoryIds){
			getTopBrandssuccessful(brandsCount , rootCategoryId);
		}
	}

	@Test
	public void _07_getSingleBrand_successful(){
		BrandDTO anyBrand = getAnyBrand();
		BrandDTO brandById = getSingleBrandByIdsuccessful(anyBrand.getId(), false);
		BrandDTO brandByUrlName = getSingleBrandByUrlNamesuccessful(anyBrand.getUrlName(), false);
		assertEquals(anyBrand, brandById);
		assertEquals(anyBrand, brandByUrlName);
		assertEquals(brandByUrlName, brandById);
	}



	/////////// Authorized user //////////////

	@Test
	public void _10_getProductList_authorized_noParams_successful(){
		Page<ProductDTO> page = catalogTestSupport.getProductsPageSuccessful(null, true);
		assertFalse(page.getItems().isEmpty());
	}

	@Test
	public void _11_getProductList_authorized_PUBLISHED_successful(){
		Page<ProductDTO> page = catalogTestSupport.getProductsPageSuccessful(catalogTestSupport.getFilterParams(ProductState.PUBLISHED), false);
		assertFalse(page.getItems().isEmpty());
	}

	@Test
	public void _12_getProductList_authorized_SOLD_successful(){
		Page<ProductDTO> page = catalogTestSupport.getProductsPageSuccessful(catalogTestSupport.getFilterParams(ProductState.SOLD), false);
		assertFalse(page.getItems().isEmpty());
	}

	//Выборка товаров, имеющих слайды
	@Transactional
	@Test
	public void _13_getProductPageWithSlides_successful(){
		//Кол-во товаров для тестирования
		int productsCount = 5;

		//Берем несколько товаров для тестирования
		List<ProductDTO> products = productService.getProductDTOPage(new ProductInfoRequest(), null, null, ProductService.UserType.HUMAN).getItems().subList(0, productsCount);
		List<Long> productIds = products.stream().map(p -> p.getProductId()).collect(Collectors.toList());

		//Проверяем, что список не пуст
		assertFalse(productIds.isEmpty());

		Map<Long, Integer> slidesCountInfo = new HashMap<>();
		for(int i = 0; i < productIds.size(); i++){
			slidesCountInfo.put(productIds.get(i), i + 1);
		}
		productService.updateProductSlidesCount(slidesCountInfo);
		commitAndStartNewTransaction();

		//Параметры hasSlides=true
		Map<String, String> params = new HashMap<String, String>() {{
			put("hasSlides", "true");
		}};

		//Запрашиваем товары с параметром hasSlides=true
		Page<ProductDTO> resultProductsPage = catalogTestSupport.getProductsPageSuccessful(params, false);
		List<Long> resultProductIds = resultProductsPage.getItems().stream().map(p -> p.getProductId()).collect(Collectors.toList());

		//Проверяем содержимое
		assertEquals(productIds.size(), resultProductIds.size());
		assertEquals(new HashSet<>(productIds), new HashSet(resultProductIds));

		//Запрашиваем id товаров с параметром hasSlides=true
		Page<Long> resultProductIdsPage = catalogTestSupport.getProductIdsPageSuccessful(params, false);

		//Проверяем содержимое
		assertEquals(productIds.size(), resultProductIdsPage.getItems().size());
		assertEquals(new HashSet<>(productIds), new HashSet(resultProductIdsPage.getItems()));

	}

	@Test
	public void _20_getSingleProduct_authorizedSuccessful(){
		getAnySingleProductWithCheckingActivitySuccessful();
	}

	@Transactional
	@Test
	public void _30_likeProductSuccessful(){
		commitTransaction();
		removeAllUserLikes();
		List<ProductDTO> noLikesList = getMyLikedProductssuccessful(false);
		//Лайкнутых товаров нет
		assertNull(noLikesList);

		ProductDTO anyProduct = getAnyProduct();
		User seller = userService.getUserById(anyProduct.getSeller().getId()).orElse(null);

		//Удаляем все уведомления продавца
		jdbcTemplate.execute("delete from notification where user_id=" + seller.getId());

		//Лайкаем товар
		assertTrue(likeProduct(anyProduct.getProductId(), false));

		//Товар появился в списке лайкнутых
		assertTrue(productIsInMyLikeList(anyProduct.getProductId()));

		//Продавец получил уведомление о лайке
		//На создание уведомления уходит время
		TestUtils.sleep(3);

		startNewTransaction();
		//Берем последнее уведомление продавца
		FirstProductLikeNotification newProductLikeNotification =
				(FirstProductLikeNotification) TestUtils.getLastNotification(seller, FirstProductLikeNotification.class, false, false, notificationService);
		//Оно существует
		assertNotNull(newProductLikeNotification);
		//Уведомление ссылается на лайкнутый товар
		assertEquals(((ProductLike) Hibernate.unproxy(newProductLikeNotification.getLike())).getProduct().getId(), anyProduct.getProductId());
		commitTransaction();

		//Второй раз товар не лайкается, но возвращает успех операции
		assertTrue(likeProduct(anyProduct.getProductId(), false));

		//Товар остался в списке лайкнутых
		assertTrue(productIsInMyLikeList(anyProduct.getProductId()));

		//Дизлайкаем товар
		assertTrue(dislikeProduct(anyProduct.getProductId(), false));

		//Уведомление больше не существует, т.к. лайк удален и вместе с ним удалилось уведомление
		Notification notification = notificationService.getNotification(newProductLikeNotification.getId());
		assertNull(notification);

		//Товар изчез из списка лайкнутых
		assertFalse(productIsInMyLikeList(anyProduct.getProductId()));

		//Второй раз товар не дизлайкается, но возвращается успех
		assertTrue(dislikeProduct(anyProduct.getProductId(), false));

		//Товара нет в списке лайкнутых
		assertFalse(productIsInMyLikeList(anyProduct.getProductId()));

		//Тогглим товар
		ToggleResult toggleResult = toggleProduct(anyProduct.getProductId(), false);
		//Товар лайкнут, больше его лайкать нельзя
		assertFalse(toggleResult.isCanBeLiked());
		//Товар появился в списке лайкнутых
		assertTrue(productIsInMyLikeList(anyProduct.getProductId()));

		startNewTransaction();
		//В статусах отличных от PUBLISHED товар не виден в списках
		for(ProductState productState : ProductState.values()) {
			if(productState == ProductState.PUBLISHED) continue;
			TestUtils.setProductState(anyProduct.getProductId(), ProductState.SOLD, productRepository);

			commitAndStartNewTransaction();
			//Товара нет в списке лайкнутых
			assertFalse(productIsInMyLikeList(anyProduct.getProductId()));
		}

		//В статусе PUBLISHED товар видно в списке лайкнутых
		TestUtils.setProductState(anyProduct.getProductId(), ProductState.PUBLISHED, productRepository);

		commitAndStartNewTransaction();

		//Товар появился в списке лайкнутых
		assertTrue(productIsInMyLikeList(anyProduct.getProductId()));

		//Снова тогглим товар
		toggleResult = toggleProduct(anyProduct.getProductId(), false);
		//Товар дизлайкнут, его можно лайкать снова
		assertTrue(toggleResult.isCanBeLiked());
		//Товар исчез из списка лайкнутых
		assertFalse(productIsInMyLikeList(anyProduct.getProductId()));
	}

	@Test
	public void _50_likeBrandSuccessful(){
		removeAllUserLikes();
		List<BrandDTO> noLikesList = getMyLikedBrandsSuccessful(false);
		//Лайкнутых брендов нет
		assertNull(noLikesList);

		BrandDTO anyBrand = getAnyBrand();

		//Лайкаем бренд
		assertTrue(likeBrand(anyBrand.getId(), false));

		//Бренд появился в списке лайкнутых
		assertTrue(brandIsInMyLikeList(anyBrand.getId()));

		//Второй раз бренд не лайкается, но возвращает успех
		assertTrue(likeBrand(anyBrand.getId(), false));

		//Бренд остался в списке лайкнутых
		assertTrue(brandIsInMyLikeList(anyBrand.getId()));

		//Дизлайкаем бренд
		assertTrue(dislikeBrand(anyBrand.getId(), false));

		//Бренд изчез из списка лайкнутых
		assertFalse(brandIsInMyLikeList(anyBrand.getId()));

		//Второй раз бренд не дизлайкается, но возвращается успех
		assertTrue(dislikeBrand(anyBrand.getId(), false));

		//Бренда нет в списке лайкнутых
		assertFalse(brandIsInMyLikeList(anyBrand.getId()));

		//Тогглим бренд
		ToggleResult toggleResult = toggleBrand(anyBrand.getId(), false);
		//Бренд лайкнут, больше его лайкать нельзя
		assertFalse(toggleResult.isCanBeLiked());
		//Бренд появился в списке лайкнутых
		assertTrue(brandIsInMyLikeList(anyBrand.getId()));

		//Снова тогглим бренд
		toggleResult = toggleBrand(anyBrand.getId(), false);
		//Бренд дизлайкнут, его можно лайкать снова
		assertTrue(toggleResult.isCanBeLiked());
		//Бренд исчез из списка лайкнутых
		assertFalse(brandIsInMyLikeList(anyBrand.getId()));
	}


	@Transactional
	@Test
	public void _60_getFiltersAndProductsInBoutique_successful(){
		//Кол-во товаров для тестирования
		int productsCount = 5;

		//Берем несколько товаров для тестирования
		List<ProductDTO> products = productService.getProductDTOPage(new ProductInfoRequest(), null, null, ProductService.UserType.HUMAN).getItems().subList(0, productsCount);
		List<Long> productIds = products.stream().map(p -> p.getProductId()).collect(Collectors.toList());

		//Проверяем, что список не пуст
		assertFalse(productIds.isEmpty());

		Set<Long> updatedItems = new HashSet<>();

		for(int i = 0; i < productIds.size(); i++) {
			List<ProductItem> availableProductItems = productItemService.getAvailableProductItems(productIds.get(i));
			ProductItem productItem = availableProductItems.get(0);
			productItem = productItemService.setProductItemInBoutique(productItem, LocalDateTime.now());
			updatedItems.add(productItem.getId());
		}
		commitAndStartNewTransaction();

		Map<String, String> params = new HashMap<String, String>() {{
			put("isInBoutique", "true");
		}};

		Page<ProductDTO> resultProductsPage = catalogTestSupport.getProductsPageSuccessful(params, false);
		List<Long> resultProductIds = resultProductsPage.getItems().stream().map(ProductDTO::getProductId).collect(Collectors.toList());

		//Проверяем содержимое
		assertEquals(productIds.size(), resultProductIds.size());
		assertEquals(new HashSet<>(productIds), new HashSet<>(resultProductIds));

		//Запрашиваем id товаров с параметром isInBoutique=true
		Page<Long> resultProductIdsPage = catalogTestSupport.getProductIdsPageSuccessful(params, false);

		//Проверяем содержимое
		assertEquals(productIds.size(), resultProductIdsPage.getItems().size());
		assertEquals(new HashSet<>(productIds), new HashSet<>(resultProductIdsPage.getItems()));

		// возвращаем айтемы обратно
		updatedItems.forEach(itemId -> {
			ProductItem item = productItemService.findById(itemId);
			productItemService.setProductItemInBoutique(item, null);
		});

		commitAndStartNewTransaction();
	}

	@Test
	public void _61_getCatalogMenu() throws IOException {
		ObjectMapper mapper = new ObjectMapper();

		assertPlainAndContentMenuEqual(mapper, "6", true, false);
		assertPlainAndContentMenuEqual(mapper, "6", false, false);
		assertPlainAndContentMenuEqual(mapper, "6", false, true);
		assertPlainAndContentMenuEqual(mapper, "6", true, true);

		assertPlainAndContentMenuEqual(mapper, "5", true, false);
		assertPlainAndContentMenuEqual(mapper, "5", false, false);

		assertPlainAndContentMenuEqual(mapper, "4", true, false);
		assertPlainAndContentMenuEqual(mapper, "4", false, false);

		assertPlainAndContentMenuEqual(mapper, "3", false, false);

		// на всякий случай проверим, что разные версии меню отличаются
		CatalogMenu catalogMenu = catalogTestSupport.getCatalogMenuSuccessful("4", true, false);
		String catalogMenuRaw = catalogTestSupport.getCatalogMenuStringSuccessful("4", false, false);
		assertNotEquals(mapper.valueToTree(catalogMenu), mapper.readTree(catalogMenuRaw));

		// если тянуть несуществующую версию, то должна быть ошибка
		catalogTestSupport.getCatalogMenuUnsuccessful("xxx", false);
		catalogTestSupport.getCatalogMenuUnsuccessful("3", true);
	}

	private void assertPlainAndContentMenuEqual(ObjectMapper mapper,
												String version,
												boolean isExperimentalVersion,
												boolean productRequestEnabled) throws IOException {
		CatalogMenu catalogMenu = catalogTestSupport.getCatalogMenuSuccessful(
				version, isExperimentalVersion, productRequestEnabled);
		String catalogMenuRaw = catalogTestSupport.getCatalogMenuStringSuccessful(
				version, isExperimentalVersion, productRequestEnabled);

		assertEquals(catalogMenu.getVersion(), version);

		// сравним еще и с текстовой версией
		assertEquals(mapper.valueToTree(catalogMenu), mapper.readTree(catalogMenuRaw));
	}

	private boolean likeProduct(Long productId, boolean withAuthorizeParams){
		ResponseEntity<Api2Response<Boolean>> response = apiV2Client.request(getProductLikeUrl(productId), null, HttpMethod.PUT, null, new ParameterizedTypeReference<Api2Response<Boolean>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		return response.getBody().getData();
	}

	private boolean dislikeProduct(Long productId, boolean withAuthorizeParams){
		ResponseEntity<Api2Response<Boolean>> response = apiV2Client.request(getProductDislikeUrl(productId), null, HttpMethod.PUT, null, new ParameterizedTypeReference<Api2Response<Boolean>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		return response.getBody().getData();
	}

	private ToggleResult toggleProduct(Long productId, boolean withAuthorizeParams){
		ResponseEntity<Api2Response<ToggleResult>> response = apiV2Client.request(getProductToggleUrl(productId), null, HttpMethod.PUT, null, new ParameterizedTypeReference<Api2Response<ToggleResult>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		return response.getBody().getData();
	}

	private boolean productIsInMyLikeList(Long productId){
		return productIsInList(getMyLikedProductssuccessful(false), productId);
	}

	private boolean productIsInMyFollowingList(Long productId){
		return productIsInList(getMyFollowedProductsSuccessful(false), productId);
	}

	private boolean productIsInList(List<ProductDTO> productList, Long productId){
		if(productList == null) return false;
		for(ProductDTO productDTO : productList){
			if(productId.equals(productDTO.getProductId())) return true;
		}
		return false;
	}

	private boolean likeBrand(Long brandId, boolean withAuthorizeParams){
		ResponseEntity<Api2Response<Boolean>> response = apiV2Client.request(getBrandLikeUrl(brandId), null, HttpMethod.PUT, null, new ParameterizedTypeReference<Api2Response<Boolean>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		return response.getBody().getData();
	}

	private boolean dislikeBrand(Long brandId, boolean withAuthorizeParams){
		ResponseEntity<Api2Response<Boolean>> response = apiV2Client.request(getBrandDislikeUrl(brandId), null, HttpMethod.PUT, null, new ParameterizedTypeReference<Api2Response<Boolean>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		return response.getBody().getData();
	}

	private ToggleResult toggleBrand(Long brandId, boolean withAuthorizeParams){
		ResponseEntity<Api2Response<ToggleResult>> response = apiV2Client.request(getBrandToggleUrl(brandId), null, HttpMethod.PUT, null, new ParameterizedTypeReference<Api2Response<ToggleResult>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		return response.getBody().getData();
	}

	private boolean brandIsInMyLikeList(Long brandId){
		return brandIsInMyLikeList(getMyLikedBrandsSuccessful(false), brandId);
	}

	private boolean brandIsInMyLikeList(List<BrandDTO> brandList, Long brandId){
		if(brandList == null) return false;
		for(BrandDTO brandDTO : brandList){
			if(brandId.equals(brandDTO.getId())) return true;
		}
		return false;
	}

	private void removeAllUserLikes(){
		likeRepository.deleteAll(likeRepository.findAllByUser(getUser()));
	}

	private List<BrandDTO> getTopBrandssuccessful(int brandsCount , Long rootCategoryId){
		Collection<Long> leafCategoryIds = rootCategoryId != null ? categoryService.getLeafCategoryIdsCached(rootCategoryId):
				categoryService.getLeafCategoryIdsCached(1L);
		Map<String, String> params = new HashMap<>();
		if(rootCategoryId != null) params.put("rootCategoryId", rootCategoryId.toString());
		params.put("count", "" + brandsCount);
		List<BrandDTO> brands = getBrandsTopsuccessful(params, false);
		assertSame(brandsCount, brands.size());
		List<Integer> counts = brands.stream().map(BrandDTO::getProductsCount).collect(Collectors.toList());
		List<Integer> countsSorted = new ArrayList<>(counts);
		countsSorted.sort(Comparator.reverseOrder());
		assertEquals(countsSorted, counts);
		for(BrandDTO brand : brands){
			assertEquals(productRepository.countBrandProducts(brand.getId(), leafCategoryIds), brand.getProductsCount().intValue());
		}
		return brands;
	}

	private User getUser(){
		return userService.getUserById(userId).orElse(null);
	}

	private Map<String, String> getAuthorizeParams(){
		Map<String, String> result = new HashMap<>();
		result.put("email", email);
		result.put("password", TestUtils.getApiHashedPassword(password, email));
		return result;
	}

	private List<BrandDTO> getBrandsTopsuccessful(Map<String, String> getParams, boolean withAuthorizeParams){
		ResponseEntity<Api2Response<List<BrandDTO>>> response = apiV2Client.request(getBrandsTopUrl(), getParams, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<BrandDTO>>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		List<BrandDTO> brands = response.getBody().getData();
		assertNotNull(brands);
		assertFalse(brands.isEmpty());
		return brands;
	}

	private BrandDTO getSingleBrandByIdsuccessful(Long id, boolean withAuthorizeParams){
		ResponseEntity<Api2Response<BrandDTO>> response = apiV2Client.request(getSingleBrandByIdUrl(id), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<BrandDTO>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		BrandDTO brand = response.getBody().getData();
		assertNotNull(brand);
		return brand;
	}

	private BrandDTO getSingleBrandByUrlNamesuccessful(String urlName, boolean withAuthorizeParams){
		ResponseEntity<Api2Response<BrandDTO>> response = apiV2Client.request(getSingleBrandByUrlNameUrl(urlName), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<BrandDTO>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		BrandDTO brand = response.getBody().getData();
		assertNotNull(brand);
		return brand;
	}

	@SneakyThrows
	private AvailableFilters getAvailableFilterssuccessful(Map<String, String> getParams, boolean withAuthorizeParams){
		ResponseEntity<String> response = apiV2Client.request(getAvailableFiltersUrl(), getParams, HttpMethod.GET, null, String.class, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		Api2Response<AvailableFilters> body = objectMapper.readValue(response.getBody(), new TypeReference<Api2Response<AvailableFilters>>(){});
		AvailableFilters availableFilters = body.getData();
		assertNotNull(availableFilters);
		assertNotNull(availableFilters.getCategory());
		assertFalse(availableFilters.getCategory().isEmpty());
		return availableFilters;
	}

	@SneakyThrows
	private SizeTree getSizeTreesuccessful(Map<String, String> getParams, boolean withAuthorizeParams){
		ResponseEntity<String> response = apiV2Client.request(getSizeTreeUrl(), getParams, HttpMethod.GET, null, String.class, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		Api2Response<SizeTree> body = objectMapper.readValue(response.getBody(), new TypeReference<Api2Response<SizeTree>>(){});
		SizeTree sizeTree = body.getData();
		assertNotNull(sizeTree);
		assertNotNull(sizeTree.getRootCategory());
		return sizeTree;
	}

	private List<SizeTypeDTO> getSizesSuccessful(Long categoryId, boolean withAuthorizeParams){
		ResponseEntity<Api2Response<List<SizeTypeDTO>>> response = apiV2Client.request(getSizesUrl(categoryId), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<SizeTypeDTO>>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		List<SizeTypeDTO> sizes = response.getBody().getData();
		assertNotNull(sizes);
		assertFalse(sizes.isEmpty());
		return sizes;
	}

	private AttributeTree getAttributeTreeSuccessful(Map<String, String> getParams, boolean withAuthorizeParams){
		ResponseEntity<Api2Response<AttributeTree>> response = apiV2Client.request(getAttributeTreeUrl(), getParams, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<AttributeTree>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		AttributeTree attributeTree = response.getBody().getData();
		assertNotNull(attributeTree);
		assertNotNull(attributeTree.getRootCategory());
		return attributeTree;
	}

	private CategoryTree getCategoryTreeSuccessful(Map<String, String> getParams, boolean withAuthorizeParams){
		ResponseEntity<Api2Response<CategoryTree>> response = apiV2Client.request(getCategoryTreeUrl(), getParams, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<CategoryTree>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		CategoryTree categoryTree = response.getBody().getData();
		assertNotNull(categoryTree);
		assertNotNull(categoryTree.getRootCategory());
		return categoryTree;
	}

	private List<CategoryDTO> getSubCategoriesSuccessful(Long categoryId, boolean withAuthorizeParams){
		ResponseEntity<Api2Response<List<CategoryDTO>>> response = apiV2Client.request(getCategoriesUrl(categoryId), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<CategoryDTO>>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		List<CategoryDTO> categories = response.getBody().getData();
		assertNotNull(categories);
		assertFalse(categories.isEmpty());
		for(CategoryDTO category : categories){
			assertNotNull(category.getProductsCount());
			assertNotNull(category.getHasChildren());
		}
		return categories;
	}

	@SneakyThrows
	private List<ProductDTO> getMyLikedProductssuccessful(boolean withAuthorizeParams){
		ResponseEntity<String> response = apiV2Client.request(getMyLikedProductsUrl(), null, HttpMethod.GET, null, String.class, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		Api2Response<List<ProductDTO>> body = objectMapper.readValue(response.getBody(), new TypeReference<Api2Response<List<ProductDTO>>>(){});
		List<ProductDTO> products = body.getData();
		return products;
	}

	@SneakyThrows
	private List<ProductDTO> getMyFollowedProductsSuccessful(boolean withAuthorizeParams){
		ResponseEntity<String> response = apiV2Client.request(getMyFollowedProductsUrl(), null, HttpMethod.GET, null, String.class, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		Api2Response<List<ProductDTO>> body = objectMapper.readValue(response.getBody(), new TypeReference<Api2Response<List<ProductDTO>>>(){});
		List<ProductDTO> products = body.getData();
		return products;
	}

	private List<BrandDTO> getMyLikedBrandsSuccessful(boolean withAuthorizeParams){
		ResponseEntity<Api2Response<List<BrandDTO>>> response = apiV2Client.request(getMyLikedBrandsUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<BrandDTO>>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		List<BrandDTO> brands = response.getBody().getData();
		return brands;
	}

	private void assertProductListForbidden(Map<String, String> getParams, boolean withAuthorizeParams){
		ResponseEntity<String> response = apiV2Client.request(getProductsUrl(), getParams, HttpMethod.GET, null, String.class, withAuthorizeParams);
		assertTrue(HttpStatus.FORBIDDEN == response.getStatusCode());
	}

	private void assertProductNotFount(Long productId){
		ResponseEntity<String> response = apiV2Client.request(getSingleProductUrl(productId), null, HttpMethod.GET, null, String.class, false);
		assertTrue(HttpStatus.NOT_FOUND == response.getStatusCode());
	}

	private ProductDTO getAnySingleProductWithCheckingActivitySuccessful(){
		//Удаляем все активности пользователя/гостя, чтобы убедиться, что после запроса появится наша.
		activityTestRepository.deleteByUserIdOrGuestToken(userId, apiV2Client.getOskCookie());

		ProductDTO anyProduct = getAnyProduct();

		activityService.saveAllFromQueue();

		Activity lastProductAccessActivity = activityRepository.findLastActivity(anyProduct.getProductId(),
				ViewProductActivity.class.getSimpleName(), userId, apiV2Client.getOskCookie());
		assertNotNull(lastProductAccessActivity);
		assertNotNull(lastProductAccessActivity.getUpdateTime());

		TestUtils.checkTestActivity(lastProductAccessActivity);

		assertTrue(isOnFirstPositionOfLastSeenResult(anyProduct.getProductId()));

		return anyProduct;
	}

	@SneakyThrows
	private List<ProductDTO> getLastSeenProductsNotEmptySuccessful(boolean withAuthorizeParams){
		ResponseEntity<String> response = apiV2Client.request(getMyLastSeenProductsUrl(), null, HttpMethod.GET, null, String.class, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		Api2Response<List<ProductDTO>> body = objectMapper.readValue(response.getBody(), new TypeReference<Api2Response<List<ProductDTO>>>(){});
		assertNotNull(body.getData());
		assertFalse(body.getData().isEmpty());
		return body.getData();
	}

	private boolean listContainsProduct(List<ProductDTO> list, Long productId){
		if(list == null || list.isEmpty()) return false;
		for(ProductDTO p : list){
			if(p.getProductId().equals(productId)) return true;
		}
		return false;
	}

	private boolean listContainsProductOnFirstPosition(List<ProductDTO> list, Long productId){
		if(list == null || list.isEmpty()) return false;
		return list.get(0).getProductId().equals(productId);
	}

	private boolean isOnFirstPositionOfLastSeenResult(Long productId){
		return listContainsProductOnFirstPosition(getLastSeenProductsNotEmptySuccessful(false), productId);
	}

	private ProductDTO getAnyProduct(){
		Long anyProductId = catalogTestSupport.getProductsPageSuccessful(catalogTestSupport.getFilterParams(ProductState.PUBLISHED), false).getItems().get(10).getProductId();
		ProductDTO product = catalogTestSupport.getProductSuccessful(anyProductId, false);
		catalogTestSupport.assertValidProductDTO(product);
		return product;
	}

	private void setProductPrice(Long productId, BigDecimal newPrice){
		Product product = productService.getRawProduct(productId, ProductService.UserType.SYSTEM).get();
		BigDecimal pr = product.getCurrentPrice();
		product.setCurrentPrice(newPrice);
		productService.saveProduct(product, ProductService.UserType.SYSTEM);
		return;
	}

	private BrandDTO getAnyBrand(){
		return getTopBrandssuccessful(1, null).get(0);
	}

}
