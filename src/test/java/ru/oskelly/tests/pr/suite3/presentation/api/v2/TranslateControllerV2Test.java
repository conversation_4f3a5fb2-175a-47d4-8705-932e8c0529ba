package ru.oskelly.tests.pr.suite3.presentation.api.v2;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.TestApiConfiguration;
import su.reddot.domain.model.translate.CommentTranslated;
import su.reddot.domain.model.translate.ProductRequestTranslated;
import su.reddot.domain.model.translate.ProductTranslated;
import su.reddot.domain.service.dto.translate.CommentTranslatedDTO;
import su.reddot.domain.service.dto.translate.ProductTranslatedDTO;
import su.reddot.domain.service.translate.CommentTranslateService;
import su.reddot.domain.service.translate.ProductRequestTranslateService;
import su.reddot.domain.service.translate.ProductTranslateService;
import su.reddot.presentation.api.v2.Api2Response;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_03)
public class TranslateControllerV2Test extends AbstractSpringTest {

    @Autowired
    private TestApiConfiguration testApiConfiguration;

    @Value("${test.api.user-email}")
    private String email;

    @Value("${test.api.user-password}")
    private String password;

    @MockBean
    private CommentTranslateService commentTranslateService;
    @MockBean
    private ProductTranslateService productTranslateService;
    @MockBean
    private ProductRequestTranslateService productRequestTranslateService;

    //Клиент для работы с API
    static ApiV2Client apiV2Client;

    @BeforeEach
    public void initialize() {
        if (apiV2Client == null) {
            apiV2Client = new ApiV2Client(email, password);
        }
    }

    private String getServiceUrl() {
        return testApiConfiguration.getServerUrl() + "/api/v2/translate";
    }

    @Test
    public void _01_01_testFindCommentTranslate() {
        CommentTranslated commentTranslated = new CommentTranslated();
        commentTranslated.setText("hello");
        when(commentTranslateService.findTranslatedByEntityId(anyLong())).thenReturn(commentTranslated);

        Map<String, String> params = new HashMap<>();
        params.put("commentId", "1");
        ResponseEntity<Api2Response<CommentTranslatedDTO>> response = apiV2Client.request(getServiceUrl() + "/comment", params, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<CommentTranslatedDTO>>() {
        }, false);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        CommentTranslatedDTO data = response.getBody().getData();
        assertEquals(data.getText(), "hello");
    }

    @Test
    public void _01_02_testFindProductTranslate() {
        ProductTranslated productTranslated = new ProductTranslated();
        productTranslated.setDescription("hello");
        when(productTranslateService.findTranslatedByEntityId(anyLong())).thenReturn(productTranslated);

        Map<String, String> params = new HashMap<>();
        params.put("productId", "1");
        ResponseEntity<Api2Response<ProductTranslatedDTO>> response = apiV2Client.request(getServiceUrl() + "/product", params, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<ProductTranslatedDTO>>() {
        }, false);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        ProductTranslatedDTO data = response.getBody().getData();
        assertEquals(data.getDescription(), "hello");
    }

    @Test
    public void _01_03_testFindProductRequestTranslate() {
        ProductRequestTranslated productRequestTranslated = new ProductRequestTranslated();
        productRequestTranslated.setDescription("hello");
        when(productRequestTranslateService.findTranslatedByEntityId(anyLong())).thenReturn(productRequestTranslated);

        Map<String, String> params = new HashMap<>();
        params.put("productRequestId", "1");
        ResponseEntity<Api2Response<ProductTranslatedDTO>> response = apiV2Client.request(getServiceUrl() + "/productRequest", params, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<ProductTranslatedDTO>>() {
        }, false);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        ProductTranslatedDTO data = response.getBody().getData();
        assertEquals(data.getDescription(), "hello");
    }

}
