package ru.oskelly.tests.pr.suite3.presentation.api.v2;

import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.api.Assertions;
import org.assertj.core.util.Strings;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.transaction.TestTransaction;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.pr.common.bonuses.BonusesServiceTestConfiguration;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.TestApiConfiguration;
import su.reddot.domain.dao.activity.ActivityTestRepository;
import su.reddot.domain.dao.address.AddressRepository;
import su.reddot.domain.dao.addressendpoint.AddressEndpointRepository;
import su.reddot.domain.dao.adminalert.AdminAlertRepository;
import su.reddot.domain.dao.agentreport.AgentReportRepository;
import su.reddot.domain.dao.agentreportpayments.AgentReportPaymentsRepository;
import su.reddot.domain.dao.bankaccount.BankPaymentRepository;
import su.reddot.domain.dao.bonuses.UserBonusesAccountRepository;
import su.reddot.domain.dao.counterparty.CounterpartyRepository;
import su.reddot.domain.dao.deliverycompany.DeliveryCompanyRepository;
import su.reddot.domain.dao.expertise.ExpertiseRepository;
import su.reddot.domain.dao.logistic.OrderWaybillsRepository;
import su.reddot.domain.dao.logistic.WaybillOrderRepository;
import su.reddot.domain.dao.logistic.WaybillRepository;
import su.reddot.domain.dao.notification.NotificationRepository;
import su.reddot.domain.dao.order.OrderPositionRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.order.OrderStateChangeRepository;
import su.reddot.domain.dao.product.ProductItemRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.dao.userfile.UserfileRepository;
import su.reddot.domain.model.activity.Activity;
import su.reddot.domain.model.activity.order.OrderActivity;
import su.reddot.domain.model.activity.order.OrderAnotherConfirmedForBuyerActivity;
import su.reddot.domain.model.activity.order.OrderAnotherPaidActivity;
import su.reddot.domain.model.activity.order.OrderFirstConfirmedForBuyerActivity;
import su.reddot.domain.model.activity.order.OrderFirstPaidActivity;
import su.reddot.domain.model.address.Address;
import su.reddot.domain.model.addressendpoint.AddressEndpoint;
import su.reddot.domain.model.adminalert.AdminAlert;
import su.reddot.domain.model.adminalert.order.AdminOrderAlert;
import su.reddot.domain.model.adminalert.order.waybill.AdminOrderAlertWaybillCreationErrorWrongAddress;
import su.reddot.domain.model.agentreport.AgentReport;
import su.reddot.domain.model.agentreport.AgentReportState;
import su.reddot.domain.model.banktransaction.BankPayment;
import su.reddot.domain.model.bonuses.OrderBonusesTransaction;
import su.reddot.domain.model.bonuses.UserBonusesAccount;
import su.reddot.domain.model.counterparty.CardCounterparty;
import su.reddot.domain.model.counterparty.Counterparty;
import su.reddot.domain.model.counterparty.PhysCounterparty;
import su.reddot.domain.model.device.DeviceDtype;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.expertise.Expertise;
import su.reddot.domain.model.fiscalreceiptrequest.FiscalReceiptRequestType;
import su.reddot.domain.model.logistic.DeliveryCompany;
import su.reddot.domain.model.logistic.DestinationType;
import su.reddot.domain.model.logistic.OrderWaybills;
import su.reddot.domain.model.logistic.QDeliveryCompany;
import su.reddot.domain.model.logistic.TimeInterval;
import su.reddot.domain.model.logistic.Waybill;
import su.reddot.domain.model.logistic.WaybillOrder;
import su.reddot.domain.model.logistic.WaybillRequisite;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.notification.order.OrderAgentReportConfirmedNotification;
import su.reddot.domain.model.notification.order.OrderConfirmedNotification;
import su.reddot.domain.model.notification.order.OrderConfirmedPartlyNotification;
import su.reddot.domain.model.notification.order.OrderDeliveredToBuyerNeedAgentReportNotification;
import su.reddot.domain.model.notification.order.OrderDeliveredToBuyerNotification;
import su.reddot.domain.model.notification.order.OrderDeliveredToBuyerRequestConfirmationNotification;
import su.reddot.domain.model.notification.order.OrderDeliveredToBuyerWaitingForAgentReportNotification;
import su.reddot.domain.model.notification.order.OrderDeliveredToExpertiseNotification;
import su.reddot.domain.model.notification.order.OrderDeliveringFromOfficeToBuyerNotification;
import su.reddot.domain.model.notification.order.OrderDeliveringFromSellerToOfficeNotification;
import su.reddot.domain.model.notification.order.OrderExpertisePassedNotification;
import su.reddot.domain.model.notification.order.OrderExpertisePassedPartlyNotification;
import su.reddot.domain.model.notification.order.OrderHeldNotification;
import su.reddot.domain.model.notification.order.OrderNeedConfirmationNotification;
import su.reddot.domain.model.notification.order.OrderNotification;
import su.reddot.domain.model.notification.order.OrderPickingUpFromOfficeNotification;
import su.reddot.domain.model.notification.order.OrderPickupDeclinedNotification;
import su.reddot.domain.model.notification.order.OrderRejectedNotification;
import su.reddot.domain.model.notification.order.SaleConfirmedNotification;
import su.reddot.domain.model.notification.order.SaleConfirmedPartlyNotification;
import su.reddot.domain.model.notification.order.SaleDeliveredToExpertiseNotification;
import su.reddot.domain.model.notification.order.SaleDeliveringFromOfficeToBuyerNotification;
import su.reddot.domain.model.notification.order.SaleDeliveringFromSellerToOfficeNotification;
import su.reddot.domain.model.notification.order.SaleExpertisePassedNotification;
import su.reddot.domain.model.notification.order.SaleExpertisePassedPartlyNotification;
import su.reddot.domain.model.notification.order.SalePickingUpFromSellerNotification;
import su.reddot.domain.model.notification.order.SalePickupDeclinedNotification;
import su.reddot.domain.model.notification.order.SaleRejectedNotification;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.order.OrderPositionState;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.order.OrderStatus;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.model.userfile.Userfile;
import su.reddot.domain.service.account.AccountService;
import su.reddot.domain.service.activity.ActivityService;
import su.reddot.domain.service.address.AddressService;
import su.reddot.domain.service.addressendpoint.AddressEndpointService;
import su.reddot.domain.service.adminalert.AdminAlertService;
import su.reddot.domain.service.adminpanel.orders.AdminConfirmationService;
import su.reddot.domain.service.adminpanel.orders.AdminOrdersService;
import su.reddot.domain.service.agentreport.AgentReportService;
import su.reddot.domain.service.bonuses.BonusesService;
import su.reddot.domain.service.bonuses.model.TransactionDTO;
import su.reddot.domain.service.counterparty.CounterpartyService;
import su.reddot.domain.service.delivery.DeliveryCostService;
import su.reddot.domain.service.device.DeviceService;
import su.reddot.domain.service.dto.LogisticStateDeliveryDTO;
import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.dto.bonuses.BonusesBalanceDTO;
import su.reddot.domain.service.dto.delivery.DeliveryParamRequestDTO;
import su.reddot.domain.service.dto.delivery.SaveDeliveryStateDTO;
import su.reddot.domain.service.dto.notification.NotificationDTO;
import su.reddot.domain.service.dto.order.AgentReportDTO;
import su.reddot.domain.service.dto.order.AgentReportParams;
import su.reddot.domain.service.dto.order.ChargeParams;
import su.reddot.domain.service.dto.order.ExpertiseDTO;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.dto.order.OrderPositionDTO;
import su.reddot.domain.service.dto.returninfo.ReturnInfoDTO;
import su.reddot.domain.service.dto.returninfo.ReturnPositionDTO;
import su.reddot.domain.service.dto.returninfo.ReturnStepChain;
import su.reddot.domain.service.dto.returninfo.ReturnStepDTO;
import su.reddot.domain.service.duty.DutyService;
import su.reddot.domain.service.fiscalreceiptrequest.FiscalReceiptRequestService;
import su.reddot.domain.service.notification.NotificationService;
import su.reddot.domain.service.order.InitOrderRequest;
import su.reddot.domain.service.order.OrderPaymentOkayParameters;
import su.reddot.domain.service.order.OrderPaymentService;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.Best2payAndTcbBankService;
import su.reddot.infrastructure.bank.StubAndTcbBankService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.configparam.ConfigParamService;
import su.reddot.infrastructure.logistic.CommonLogisticService;
import su.reddot.infrastructure.logistic.DeliveryState;
import su.reddot.infrastructure.util.CallInTransaction;
import su.reddot.presentation.api.v2.Api2Response;
import su.reddot.presentation.api.v2.order.OrderRequest;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.when;
import static su.reddot.domain.service.dto.returninfo.ReturnStepDTO.Type.DISABLED;
import static su.reddot.domain.service.dto.returninfo.ReturnStepDTO.Type.WAITING;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_03)
@ContextConfiguration(classes = {OrderFlowTestUtils.class, BonusesServiceTestConfiguration.class})
public class OrderControllerApiV2Test extends AbstractSpringTest {
 	@Autowired
 	private TestApiConfiguration testApiConfiguration;
	@Value("${test.api.user-id}")
	private Long buyerId;
	@Value("${test.api.user-email}")
	private String buyerEmail;
	@Value("${test.api.user-password}")
	private String buyerPassword;
	@Value("${test.api.user2-email}")
	private String sellerEmail;
	@Value("${test.api.user2-password}")
	private String sellerPassword;

	private final Long realSellerId = 23L;

	@Value("${bank-account.payment-version}")
	private String paymentVersion;

	private static final String deliveryAddressEndpointCountry = "Россия";
	private static final String deliveryAddressEndpointZip = "436842";
	private static final String deliveryAddressEndpointRegion = "Брянская область";
	private static final String deliveryAddressEndpointCity = "Брянск";
	private static final String deliveryAddressEndpointAddress = "Цветной бульвар, д.1";
	private static final String deliveryAddressEndpointPhone = "+***********";
	private static final String deliveryAddressEndpointFirstName = "Иван";
	private static final String deliveryAddressEndpointaPatronymicName = "Сергеевич";
	private static final String deliveryAddressEndpointaLastName = "Белых";

	private static final String cntInnPhys = "************";
	private static final String cntBik = "*********";
	private static final String cntCorrespondingAccount = "*********90123456789";
	private static final String cntPaymentAccount = "98765432109876543211";
	private static final String cntFirstName = "Дмитрий";
	private static final String cntPatronymicName = "Иванович";
	private static final String cntLastName = "Белых";

	private static final Long someonesOrderId = 1085600L;

	private static Order order;
	private static final List<Long> cleanUpOrdersIDs = new ArrayList<>();

	private static List<Product> productsForOrders;
	private static Address pickupAddress;
	private static AddressEndpoint pickupAddressEndpoint;
	private static Counterparty sellerCounterparty;
	private static Address deliveryAddress;
	private static AddressEndpoint deliveryAddressEndpoint;
	private static Counterparty buyerCounterparty;

	static ApiV2Client buyerClient;
	static ApiV2Client sellerClient;

	static long approvedExpertiseId;
	static long unapprovedExpertiseId;

	static long returnInfoId;

	@Autowired
	private UserService userService;
	@Autowired
	private AddressEndpointService addressEndpointService;
	@Autowired
	private AddressService addressService;
	@Autowired
	private CounterpartyService counterpartyService;
	@Autowired
	private OrderService orderService;
	@Autowired
	private OrderPaymentService orderPaymentService;
	@Autowired
	private StubAndTcbBankService paymentsServiceBankService;
	@Autowired
	private AgentReportService agentRepostService;
	@Autowired
	private NotificationService notificationService;
	@Autowired
	private ProductRepository productRepository;
	@Autowired
	private ProductItemRepository productItemRepository;
	@Autowired
	private OrderRepository orderRepository;
	@Autowired
	private OrderPositionRepository orderPositionRepository;
	@Autowired
	private AddressEndpointRepository addressEndpointRepository;
	@Autowired
	private AddressRepository addressRepository;
	@Autowired
	private CounterpartyRepository counterpartyRepository;
	@Autowired
	private ExpertiseRepository expertiseRepository;
	@Autowired
	private AdminAlertRepository adminAlertRepository;
	@Autowired
	private BankPaymentRepository bankPaymentRepository;
	@Autowired
	private AdminAlertService<AdminOrderAlert> adminAlertService;
	@Autowired
	private WaybillRepository waybillRepository;
	@Autowired
	private WaybillOrderRepository waybillOrderRepository;
	@Autowired
	private CommonLogisticService commonLogisticService;
	@Autowired
	private NotificationRepository<Notification> notificationRepository;
	@Autowired
	private AgentReportRepository agentReportRepository;
	@Autowired
	private UserfileRepository userfileRepository;
	@Autowired
	private DeliveryCompanyRepository deliveryCompanyRepository;
	@Autowired
	private OrderStateChangeRepository orderStateChangeRepository;
	@Autowired
	private AccountService accountService;
	@Autowired
	private ActivityService activityService;
	@Autowired
	private ActivityTestRepository<Activity> activityTestRepository;
	@Autowired
	private ConfigParamService configParamService;
	@Autowired
	private DeliveryCostService deliveryCostService;
	@Autowired
	private AdminConfirmationService adminConfirmationService;
	@Autowired
	private AdminOrdersService adminOrdersService;
	@Autowired
	private AgentReportPaymentsRepository agentReportPaymentsRepository;
	@Autowired
	private OrderWaybillsRepository orderWaybillsRepository;
	@Autowired
	private OrderFlowTestUtils orderFlowTestUtils;
	@Autowired
	private CallInTransaction callInTransaction;
	@Autowired
	private FiscalReceiptRequestService fiscalReceiptRequestService;
	@Autowired
	private BonusesService bonusesService;
	@SpyBean
	DeviceService deviceService;
	@MockBean
	private UserBonusesAccountRepository userBonusesAccountRepository;


	@Value("${order.request_delivery_confirmation_from_buyer}")
	private boolean requestDeliveryConfirmationFromBuyer;

	private User getBuyer() {
		User user = userService.getUserByEmail(buyerEmail);
		if (user != null) {
			//TODO при включении сервиса лояльности нужно проверить и убрать setIsLoyaltyProgramAccepted(true)
			user.setIsLoyaltyProgramAccepted(true);
			user.setIsLoyaltyProgramV2Accepted(ZonedDateTime.now());
		}
		return user;
	}

	private User getSeller() {
		User user = userService.getUserByEmail(sellerEmail);
		if (user != null) {
			//TODO при включении сервиса лояльности нужно проверить и убрать setIsLoyaltyProgramAccepted(true)
			user.setIsLoyaltyProgramAccepted(true);
			user.setIsLoyaltyProgramV2Accepted(ZonedDateTime.now());
		}
		return user;
	}

	private String getServiceUrl() {
		return testApiConfiguration.getServerUrl() + "/api/v2/orders";
	}

	private String getOrderUrl(@NonNull Long orderId) {
		return getServiceUrl() + "/" + orderId;
	}

	private String getBuyerOrdersUrl() {
		return getServiceUrl() + "/buyerOrders";
	}

	private String getSellerOrdersUrl() {
		return getServiceUrl() + "/sellerOrders";
	}

	private String getPickupAddressEndpointUrl(@NonNull Long orderId) {
		return getOrderUrl(orderId) + "/pickupAddressEndpoint";
	}

	private String getDeliveryAddressEndpointUrl(@NonNull Long orderId, @NonNull Long addressEndpointId) {
		return getOrderUrl(orderId) + "/deliveryAddressEndpoint/" + addressEndpointId;
	}

	private String getSellerCounterpartyUrl(@NonNull Long orderId) {
		return getOrderUrl(orderId) + "/sellerCounterparty";
	}

	private String getOrderConfirmUrl(@NonNull Long orderId) {
		return getOrderUrl(orderId) + "/confirm";
	}

	private String getOrderPositionConfirmUrl(@NonNull Long orderId) {
		return getOrderUrl(orderId) + "/confirmPosition";
	}

	private String getTimeIntervalsUrl() {
		return getServiceUrl() + "/timeIntervals";
	}

	private String getPickupTimeIntervalSetUrl(@NonNull Long orderId) {
		return getOrderUrl(orderId) + "/pickupTimeInterval";
	}

	private String getPickupCommentSetUrl(@NonNull Long orderId) {
		return getOrderUrl(orderId) + "/pickupComment";
	}

	private String getSellerOrdersFinishedUrl() {
		return getSellerOrdersUrl() + "/finished";
	}

	private String getReturnInfoServiceUrl() {
		return getServiceUrl() + "/returns";
	}

	private String getReturnInfoBuyerPageUrl() {
		return getReturnInfoServiceUrl() + "/buyer";
	}

	private String getReturnInfoSellerPageUrl() {
		return getReturnInfoServiceUrl() + "/seller";
	}

	private String getReturnInfoDetailedUrl(long returnInfoId) {
		return getReturnInfoServiceUrl() + "/" + returnInfoId;
	}

	private String getSetExpertisePassedForOrderPositionUrl() {
		return getAdminOrdersUrl() + "/set-expertise-passed";
	}

	private String getSetExpertisePassedWithDefectForOrderPositionUrl() {
		return getAdminOrdersUrl() + "/set-defect";
	}

	private String getSetExpertiseRejectedForOrderPositionUrl() {
		return getAdminOrdersUrl() + "/set-expertise-reject";
	}

	private String getSetExpertisePassedWithCleaningForOrderPositionUrl() {
		return getAdminOrdersUrl() + "/set-cleaning";
	}

	private String getPickupDeclinedUrl(@NonNull Long orderId) {
		return getAdminOrdersUrl() + "/pickup-declined/" + orderId;
	}

	private String getOrderAfterExpertiseByOurselvesConfirmUrl(@NonNull Long orderId) {
		return getAdminOrdersUrl() + "/send-order-after-expertise/ourselves/" + orderId;
	}

	private String getRefundOrderAfterExpertiseUrl(@NonNull Long orderId) {
		return getAdminOrdersUrl() + "/refund/" + orderId;
	}

	private String getSendConfirmedOrderUrl(@NonNull Long orderId) {
		return getAdminOrdersUrl() + "/send-confirmed-order/ourselves/" + orderId;
	}

	private String getAdminOrdersUrl() {
		return testApiConfiguration.getServerUrl() + "/adminpanel/orders";
	}

	private String getAgentReportServiceUrl() {
		return testApiConfiguration.getServerUrl() + "/api/v2/agentreports";
	}

	private String getAgentReportConfirmationUrl(Long agentReportId) {
		return getAgentReportServiceUrl() + "/confirm/" + agentReportId;
	}

	private String generateAgentReportPDFUrl(Long agentReportId) {
		return getAgentReportServiceUrl() + "/pdf/" + agentReportId;
	}

	private List<Product> getProductsForOrders() {
		if (productsForOrders == null) {
			productsForOrders = new ArrayList<>();
			List<Product> products = productRepository.findProductsBySellerIdAndProductState(realSellerId, ProductState.PUBLISHED).stream().collect(Collectors.toList());
			for (int i = 0; i < 4; i++) {
				Product product = products.get(i);
				product.setSeller(getSeller());
				productRepository.saveAndFlush(product);
				product.getProductItems().stream().forEach(pi -> {
					pi.setHidden(false);
					pi.setCount(10);
					pi.setDeleteTime(null);
					productItemRepository.saveAndFlush(pi);
				});
				productsForOrders.add(product);
			}
		}
		return productsForOrders;
	}

	private Order createOrder(List<Product> products) {
		Order order = new Order();
		order.setBuyer(getBuyer());
		order.setUuid(UUID.randomUUID());
		order.setState(OrderState.CREATED);
		order.setPaymentVersion(paymentVersion);
		BigDecimal orderAmount = new BigDecimal(0);
		for (int i = 0; i < products.size(); i++) {
			Product product = products.get(i);
			ProductItem productItem = productItemRepository.findAllByProduct(product).get(0);
			OrderPosition orderPosition = new OrderPosition();
			orderPosition.setState(OrderPositionState.INITIAL);
			orderPosition.setStateTime(LocalDateTime.now());
			orderPosition.setProductItem(productItem);
			//orderPosition.setCount(i + 1);
			//BigDecimal amount = product.getCurrentPrice().multiply(new BigDecimal(orderPosition.getCount()));
			BigDecimal amount = product.getCurrentPrice();
			orderAmount = orderAmount.add(amount);
			orderPosition.setAmount(amount);
			orderPosition.setItemSaleAmount(orderPosition.getAmount());
			//orderPosition.setCommission(amount.multiply(new BigDecimal(0.15)));
			orderPosition.setCommission(new BigDecimal(0.15));
			order.addPosition(orderPosition);
		}
		order.setAmount(orderAmount);
		order.setDeliveryCost(deliveryCostService.getDefaultCost());
		orderRepository.saveAndFlush(order);
		cleanUpOrdersIDs.add(order.getId());
		return order;
	}

	private OrderDTO loadOrderSuccessfull(Long orderId, ApiV2Client client, boolean withAuthoriseParams) {
		ResponseEntity<Api2Response<OrderDTO>> responseEntity = client.request(getOrderUrl(orderId), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<OrderDTO>>() {
		}, withAuthoriseParams);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody());
		assertNotNull(responseEntity.getBody().getData());
		assertOrderIsValid(responseEntity.getBody().getData());
		return responseEntity.getBody().getData();
	}

	public static List<OrderDTO> loadListSuccessfull(String url, ApiV2Client client, boolean withAuthoriseParams) {
		ResponseEntity<Api2Response<List<OrderDTO>>> responseEntity = client.request(url, null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<OrderDTO>>>() {
		}, withAuthoriseParams);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody());
		if (responseEntity.getBody().getData() != null) {
			for (OrderDTO order : responseEntity.getBody().getData()) {
				assertOrderIsValid(order);
			}
		}
		return responseEntity.getBody().getData();
	}

	private OrderDTO loadOrderFromListSuccessfull(String url, Long orderId, ApiV2Client client, boolean withAuthoriseParams) {
		List<OrderDTO> orders = loadListSuccessfull(url, client, withAuthoriseParams);
		OrderDTO result = null;
		for (OrderDTO order : orders) {
			if (order.getId().equals(orderId)) {
				result = order;
				break;
			}
		}
		assertNotNull(result);
		return result;
	}

	private List<TimeInterval> loadTimeIntervalsSuccessful(ApiV2Client client, boolean withAuthoriseParams) {
		ResponseEntity<Api2Response<List<TimeInterval>>> responseEntity = client.request(getTimeIntervalsUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<TimeInterval>>>() {
		}, withAuthoriseParams);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody());
		return responseEntity.getBody().getData();
	}

	private void assertLoadOrderForbidden(Long orderId, ApiV2Client client, boolean withAuthoriseParams) {
		ResponseEntity<String> responseEntity = client.request(getOrderUrl(orderId), null, HttpMethod.GET, null, String.class, withAuthoriseParams);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
	}

	private void assertLoadListForbidden(String url, ApiV2Client client, boolean withAuthoriseParams) {
		ResponseEntity<String> responseEntity = client.request(url, null, HttpMethod.GET, null, String.class, withAuthoriseParams);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
	}

	private void assertListDoesntContainProduct(String url, Long orderId, ApiV2Client client, boolean withAuthoriseParams) {
		List<OrderDTO> orders = loadListSuccessfull(url, client, withAuthoriseParams);
		if (orders == null) return;
		OrderDTO result = null;
		for (OrderDTO order : orders) {
			if (order.getId().equals(orderId)) {
				result = order;
				break;
			}
		}
		assertNull(result);
	}

	private AddressEndpoint createAddressEndpoint(User user) {
		Address address = new Address().setZipCode(deliveryAddressEndpointZip).setCountry(deliveryAddressEndpointCountry).setCity(deliveryAddressEndpointCity)
				.setRegion(deliveryAddressEndpointRegion).setAddress(deliveryAddressEndpointAddress).setUser(user);
		address = addressService.saveAddress(user, address);
		AddressEndpoint addressEndpoint = new AddressEndpoint().setAddress(address).setUser(user).setPhone(deliveryAddressEndpointPhone)
				.setFirstName(deliveryAddressEndpointFirstName).setPatronymicName(deliveryAddressEndpointaPatronymicName).setLastName(deliveryAddressEndpointaLastName);
		return addressEndpointService.save(user, addressEndpoint);
	}

	private Counterparty createCounterparty(User user) {
		Counterparty counterparty = new PhysCounterparty().setUser(user).setBik(cntBik).setCorrespondentAccount(cntCorrespondingAccount).setPaymentAccount(cntPaymentAccount)
				.setInn(cntInnPhys).setFirstName(cntFirstName).setPatronymicName(cntPatronymicName).setLastName(cntLastName)
				.setPhysAddress(pickupAddress);
		return counterpartyService.save(counterparty);
	}

	private Counterparty createCardCardCounterparty(User user) {
		Counterparty counterparty = new CardCounterparty().setCardBrand("VISA").setCardBindBank("TCB").setCardHolder("Binder Fresh").setCardRefId("********")
				.setCardNumber("4784 76** **** 6604").setCardExpireTime(ZonedDateTime.now().plusYears(1)).setUser(user);
		return counterpartyService.save(counterparty);
	}

	private MultiValueMap<String, String> getOrderPositionConfirmationParams(Long orderPositionId, Boolean doConfirmSale) {
		MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
		if (orderPositionId != null) params.add("orderPositionId", orderPositionId.toString());
		if (doConfirmSale != null) params.add("doConfirmSale", doConfirmSale.toString());
		return params;
	}

	private OrderPositionDTO getOrderPositionById(@NonNull OrderDTO orderDTO, @NonNull Long orderPositionId) {
		if (orderDTO.getItems() == null || orderDTO.getItems().isEmpty()) return null;
		for (OrderPositionDTO orderPositionDTO : orderDTO.getItems()) {
			if (orderPositionId.equals(orderPositionDTO.getId())) return orderPositionDTO;
		}
		return null;
	}

	private OrderPosition getOrderPosition(@NonNull Order order, int i) {
		return orderPositionRepository.findByOrderOrderByIdDesc(order).get(i);
		//return order.getOrderPositions().get(i);
	}

	private void setOrderState(Long orderId, OrderState orderState) {
		Order o = getOrder(orderId);
		o.setState(orderState);
		orderService.saveOrder(o);
	}

	private Order getOrder(Long orderId) {
		return orderRepository.getOne(orderId);
	}

	@Transactional
	@Rollback(value = false)
	@Test
	public void _00_initialize() {
		if (order == null) {
			order = createOrder(getProductsForOrders());
		}
	}

	private Long configClient() {
		User user = userService.getUserByEmail(buyerEmail);
		orderFlowTestUtils.enableUserAuthority(user.getId(), AuthorityName.ORDER_MANUAL_CHANGE_DELIVERY_STATE, true);
		//TODO при включении сервиса лояльности нужно проверить и убрать setIsLoyaltyProgramAccepted(true)
		user.setIsLoyaltyProgramAccepted(true);
		user.setIsLoyaltyProgramV2Accepted(ZonedDateTime.now());
		return user.getId();
	}

	@BeforeEach // to be able to run tests one-by-one
	public void _00_initialize_eachTest() {
		callInTransaction.runInNewTransaction(this::configClient);
		if (buyerClient == null) {
			buyerClient = new ApiV2Client(buyerEmail, buyerPassword);
			sellerClient = new ApiV2Client(sellerEmail, sellerPassword);
		}
	}

	/**
	 * Без авторизации сервис недоступен
	 */
	@Test
	public void _01_requestOrderUnauthorisedForbidden() {
		assertLoadOrderForbidden(order.getId(), buyerClient, false);
		assertLoadListForbidden(getBuyerOrdersUrl(), buyerClient, false);
		assertLoadListForbidden(getSellerOrdersUrl(), sellerClient, false);
	}

	/**
	 * Покупатель видит свой заказ во всех статусах кроме DELETED, CREATED
	 */
	//Очень медленный тест
	//@Disabled
	@Test
	@Transactional
	@Rollback(value = false)
	public void _02_buyerSeesHisOrderInAllStatesExceptDeletedAndCreated() {
		TestTransaction.end();
		boolean authorised = false;
		OrderState[] orderStates = OrderState.values();
		for (OrderState orderState : orderStates) {

			startNewTransaction();
			setOrderState(order.getId(), orderState);
			commitTransaction();

			if (orderState == OrderState.DELETED || orderState == OrderState.CREATED) {
				assertLoadOrderForbidden(order.getId(), buyerClient, !authorised);
				assertListDoesntContainProduct(getBuyerOrdersUrl(), order.getId(), buyerClient, !authorised);
			} else {
				OrderDTO orderDTO = loadOrderSuccessfull(order.getId(), buyerClient, !authorised);
				//Заказы в статусе HOLD_PROCESSING в списке не приходят
				if (orderState != OrderState.HOLD_PROCESSING) {
					OrderDTO orderDTOfromList = loadOrderFromListSuccessfull(getBuyerOrdersUrl(), order.getId(), buyerClient, !authorised);
					assertEquals(orderDTO.getId(), orderDTOfromList.getId());
				}
			}
			authorised = true;
		}
	}

	/**
	 * Покупатель может установить точку доставки в незавершенный заказ
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _02_buyerSetsDeliveryAddressEndpointToUnfinishedOrder_successull() {
		assertNull(order.getDeliveryAddressEndpoint());
		deliveryAddressEndpoint = createAddressEndpoint(getBuyer());

		commitAndStartNewTransaction();
		setOrderState(order.getId(), OrderState.HOLD_PROCESSING);
		commitTransaction();

		ResponseEntity<Api2Response<OrderDTO>> responseEntity = buyerClient.request(getDeliveryAddressEndpointUrl(order.getId(), deliveryAddressEndpoint.getId()), null, HttpMethod.PUT, null, new ParameterizedTypeReference<Api2Response<OrderDTO>>() {
		}, true);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody());
		assertNotNull(responseEntity.getBody().getData());
		assertNotNull(responseEntity.getBody().getData().getDeliveryAddressEndpoint());
		assertNotNull(responseEntity.getBody().getData().getDeliveryAddressEndpoint().getId());
		assertEquals(deliveryAddressEndpoint.getId(), responseEntity.getBody().getData().getDeliveryAddressEndpoint().getId());
	}


	/**
	 * Продавец видит свой заказ во всех статусах кроме OrderState.DELETED, OrderState.CANCELED, OrderState.CREATED, OrderState.HOLD_PROCESSING, OrderState.HOLD_ERROR
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _03_sellerSeesHisPayedOrderExceptUnpayedAndSomeOthers() {
		TestTransaction.end();
		List<OrderState> sellerDisallowedStates = Arrays.asList(OrderState.DELETED, OrderState.CANCELED, OrderState.CREATED, OrderState.HOLD_PROCESSING, OrderState.HOLD_ERROR);

		boolean authorised = false;
		OrderState[] orderStates = OrderState.values();
		for (OrderState orderState : orderStates) {

			startNewTransaction();
			setOrderState(order.getId(), orderState);
			commitTransaction();

			if (sellerDisallowedStates.contains(orderState)) {
				assertLoadOrderForbidden(order.getId(), sellerClient, !authorised);
				assertListDoesntContainProduct(getSellerOrdersUrl(), order.getId(), sellerClient, !authorised);
			} else {
				OrderDTO orderDTO = loadOrderSuccessfull(order.getId(), sellerClient, !authorised);
				OrderDTO orderDTOfromList = loadOrderFromListSuccessfull(getSellerOrdersUrl(), order.getId(), sellerClient, !authorised);
				assertEquals(orderDTO.getId(), orderDTOfromList.getId());
			}
			authorised = true;

			//Очень медленно работает, сокращаем тест пока
			//break;
		}
	}

	/**
	 * При оплате создается уведомление для продавца и покупателя
	 * + делается списание бонусов (и сразу же комит этого списания)
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _04_onHoldCreatesNotificationsForSellerAndBuyer() {
		given(deviceService.getCurrentDeviceInfo())
				.willReturn(new DeviceService.DeviceInfo()
						.setGuestToken("guest-token")
						.setDeviceDtype(DeviceDtype.OtherDevice));

		TestTransaction.end();
		TestTransaction.start();
		order = orderRepository.getOne(order.getId());
		//order.setDeliveryAddressEndpoint(createAddressEndpoint(order.getBuyer()));
		setOrderState(order.getId(), OrderState.HOLD_PROCESSING);

		assertNull(order.getSeller());

		BigDecimal bonusesToDistribute = (order.getAmount().compareTo(new BigDecimal(1000)) > 0) ? new BigDecimal(1000) : order.getAmount();
		UserBonusesAccount userBonusesAccount = new UserBonusesAccount();
		userBonusesAccount.setBonusesAccountId(UUID.randomUUID().toString());
		when(userBonusesAccountRepository.findByUserId(any(Long.class)))
				.thenReturn(Optional.of(userBonusesAccount));

		InitOrderRequest initOrderRequest = InitOrderRequest
				.builder()
				.quitOnPaymentsInit(true)
				.bonuses(bonusesToDistribute)
				.initialCallFromHoldV2(true)
				.build();
		orderService.initHold(order, order.getBuyer(), initOrderRequest);
		initOrderRequest = InitOrderRequest
				.builder()
				.bonuses(bonusesToDistribute)
				.quitOnPaymentsInit(false)
				.initialCallFromHoldV2(true)
				.build();
		orderService.initHold(order, order.getBuyer(), initOrderRequest);
		OrderBonusesTransaction trn = bonusesService.withdrawBonusesCommit(order.getBuyer().getId(), order.getId());
		assertNotNull(order.getSeller());
		assertNotNull(trn);
		assertEquals(TransactionDTO.StateEnum.COMMITTED, trn.getTrnState());

		setOrderState(order.getId(), OrderState.HOLD);
		orderService.onHold(order);
		commitAndStartNewTransaction();
		assertNotificationCreated(order.getId(), order.getSellerUser().getId(), OrderNeedConfirmationNotification.class, true, false, 1, true);
		assertNotificationCreated(order.getId(), order.getBuyer().getId(), OrderHeldNotification.class, false, false, 1, false);

		commitAndStartNewTransaction();

		//Проверяем создание активности
		//Сначала принудительно их сохраняем
		activityService.saveAllFromQueue();

		commitAndStartNewTransaction();

		//Получаем последнюю активность OrderFirstPaidActivity по данному заказу
		OrderFirstPaidActivity orderFirstPaidActivity = (OrderFirstPaidActivity) activityTestRepository.findLastActivityByObjectIdAndDtype(order.getId(), OrderFirstPaidActivity.class.getSimpleName());
		//Получаем последнюю активность OrderAnotherPaidActivity по данному заказу
		OrderAnotherPaidActivity orderAnotherPaidActivity = (OrderAnotherPaidActivity) activityTestRepository.findLastActivityByObjectIdAndDtype(order.getId(), OrderAnotherPaidActivity.class.getSimpleName());

		OrderActivity anyActivity = orderFirstPaidActivity != null ? orderFirstPaidActivity : orderAnotherPaidActivity;
		//Одна из этих активностей должна существовать

		//Активность существует
		assertNotNull(anyActivity);
		//Активность ни к кому не привязана
		assertNull(anyActivity.getUserId());
		assertNull(anyActivity.getGuestToken());
	}

	/**
	 * Продавец не может установить точку заборa заказа на несуществующий заказ
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _20_sellerSetsPickupAddressEndpoint_unexistedOrder_unsuccessull() {
		setOrderState(order.getId(), OrderState.HOLD);
		commitAndStartNewTransaction();
		List<OrderPosition> orderPositions = orderRepository.getOne(order.getId()).getOrderPositions();
		for (OrderPosition orderPosition : orderPositions) {
			orderPosition.setParticipatesInPayment(true);
			orderPositionRepository.saveAndFlush(orderPosition);
		}
		commitTransaction();

		pickupAddressEndpoint = createAddressEndpoint(getSeller());
		pickupAddress = pickupAddressEndpoint.getAddress();

		Long unexistedOrderId = order.getId() * 2 + 8666;

		ResponseEntity<String> responseEntity = sellerClient.request(getPickupAddressEndpointUrl(unexistedOrderId), null, HttpMethod.PUT, TestUtils.getOneParamAsMultiValueMap("pickupAddressEndpointId", pickupAddressEndpoint.getId()), String.class, false);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Заказ не найден"));
	}

	/**
	 * Продавец не может установить точку заборa заказа на чужой заказ
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _21_sellerSetsPickupAddressEndpoint_someonesOrder_unsuccessull() {
		ResponseEntity<String> responseEntity = sellerClient.request(getPickupAddressEndpointUrl(someonesOrderId), null, HttpMethod.PUT, TestUtils.getOneParamAsMultiValueMap("pickupAddressEndpointId", pickupAddressEndpoint.getId()), String.class, false);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Вы не являетесь продавцом"));
	}

	/**
	 * Продавец не может установить нусуществующую точку заборa
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _22_sellerSetsPickupAddressEndpoint_unexistedAddressEndpoint_unsuccessull() {
		Long unexistedAddressEndpointId = pickupAddressEndpoint.getId() * 2 + 4578;
		ResponseEntity<String> responseEntity = sellerClient.request(getPickupAddressEndpointUrl(order.getId()), null, HttpMethod.PUT, TestUtils.getOneParamAsMultiValueMap("pickupAddressEndpointId", unexistedAddressEndpointId), String.class, false);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Точка вывоза не найдена"));
	}

	/**
	 * Продавец не может установить чужую точку заборa
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _23_sellerSetsPickupAddressEndpoint_someonesAddressEndpoint_unsuccessull() {
		deliveryAddressEndpoint = createAddressEndpoint(getBuyer());
		deliveryAddress = deliveryAddressEndpoint.getAddress();
		commitTransaction();

		ResponseEntity<String> responseEntity = sellerClient.request(getPickupAddressEndpointUrl(order.getId()), null, HttpMethod.PUT, TestUtils.getOneParamAsMultiValueMap("pickupAddressEndpointId", deliveryAddressEndpoint.getId()), String.class, false);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Точка вывоза вам не принадлежит"));
	}

	/**
	 * Продавец не может установить незаполненную точку заборa
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _24_sellerSetsPickupAddressEndpoint_uncompleteAddressEndpoint_unsuccessull() {
		pickupAddressEndpoint.setPhone(null);
		addressEndpointRepository.saveAndFlush(pickupAddressEndpoint);
		commitTransaction();

		ResponseEntity<String> responseEntity = sellerClient.request(getPickupAddressEndpointUrl(order.getId()), null, HttpMethod.PUT, TestUtils.getOneParamAsMultiValueMap("pickupAddressEndpointId", pickupAddressEndpoint.getId()), String.class, false);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Точка вывоза заполнена не полностью"));

		pickupAddressEndpoint.setPhone(deliveryAddressEndpointPhone);
		addressEndpointRepository.saveAndFlush(pickupAddressEndpoint);
	}

	/**
	 * Продавец удачно устанавливает точку забора и получает ее вместе с информацией о заказе
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _25_sellerSetsPickupAddressEndpoint_successull() {
		ResponseEntity<Api2Response<OrderDTO>> responseEntity = sellerClient.request(getPickupAddressEndpointUrl(order.getId()), null, HttpMethod.PUT, TestUtils.getOneParamAsMultiValueMap("pickupAddressEndpointId", pickupAddressEndpoint.getId()), new ParameterizedTypeReference<Api2Response<OrderDTO>>() {
		}, false);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody());
		assertNotNull(responseEntity.getBody().getData());
		assertNotNull(responseEntity.getBody().getData().getPickupAddressEndpoint());
		assertNotNull(responseEntity.getBody().getData().getPickupAddressEndpoint().getId());
		assertEquals(pickupAddressEndpoint.getId(), responseEntity.getBody().getData().getPickupAddressEndpoint().getId());
	}

	/**
	 * Продавец не может установить контрагента на несуществующий заказ
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _30_sellerSetsCounterparty_unexistedOrder_unsuccessull() {
		sellerCounterparty = createCounterparty(getSeller());

		Long unexistedOrderId = order.getId() * 2 + 8485;

		ResponseEntity<String> responseEntity = sellerClient.request(getSellerCounterpartyUrl(unexistedOrderId), null, HttpMethod.PUT, TestUtils.getOneParamAsMultiValueMap("sellerCounterpartyId", sellerCounterparty.getId()), String.class, false);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Заказ не найден"));
	}

	/**
	 * Продавец не может установить контрагента на чужой заказ
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _31_sellerSetsCounterparty_someonesOrder_unsuccessull() {
		ResponseEntity<String> responseEntity = sellerClient.request(getSellerCounterpartyUrl(someonesOrderId), null, HttpMethod.PUT, TestUtils.getOneParamAsMultiValueMap("sellerCounterpartyId", sellerCounterparty.getId()), String.class, false);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Вы не являетесь продавцом"));
	}

	/**
	 * Продавец не может установить несуществующего контрагента
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _32_sellerSetsCounterparty_unexistedCounterparty_unsuccessull() {
		Long unexistedCounterpartyId = sellerCounterparty.getId() * 2 + 4783;

		ResponseEntity<String> responseEntity = sellerClient.request(getSellerCounterpartyUrl(order.getId()), null, HttpMethod.PUT, TestUtils.getOneParamAsMultiValueMap("sellerCounterpartyId", unexistedCounterpartyId), String.class, false);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Контрагент не найден"));
	}

	/**
	 * Продавец не может установить чужого контрагента
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _33_sellerSetsCounterparty_someonesCounterparty_unsuccessull() {
		buyerCounterparty = createCounterparty(getBuyer());
		commitTransaction();

		ResponseEntity<Api2Response<String>> responseEntity = sellerClient.request(getSellerCounterpartyUrl(order.getId()), null, HttpMethod.PUT, TestUtils.getOneParamAsMultiValueMap("sellerCounterpartyId", buyerCounterparty.getId()), new ParameterizedTypeReference<Api2Response<String>>() {}, false);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(Pattern.compile("Заказ .* \\(продавец .*\\): не удается задать реквизиты .* \\(пользователь .*\\)").matcher(responseEntity.getBody().getMessage()).matches());
	}

	/**
	 * Продавец удачно устанавливает контрагента и получает его вместе с информацией о заказе
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _34_sellerSetsCounterparty_successull() {
		ResponseEntity<Api2Response<OrderDTO>> responseEntity = sellerClient.request(getSellerCounterpartyUrl(order.getId()), null, HttpMethod.PUT, TestUtils.getOneParamAsMultiValueMap("sellerCounterpartyId", sellerCounterparty.getId()), new ParameterizedTypeReference<Api2Response<OrderDTO>>() {
		}, false);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody());
		assertNotNull(responseEntity.getBody().getData());
		assertNotNull(responseEntity.getBody().getData().getSellerCounterparty());
		assertNotNull(responseEntity.getBody().getData().getSellerCounterparty().getId());
		assertEquals(sellerCounterparty.getId(), responseEntity.getBody().getData().getSellerCounterparty().getId());
	}

	@Test
	@Transactional
	@Rollback(value = false)
	public void _35_setSellerCardCounterpartyOnBest2PayFail() {
		Order thisOrder = _83_X_confirmAgentReportCreateOrder(Best2payAndTcbBankService.SCHEMA);
		orderRepository.save(thisOrder);
		Counterparty counterparty = createCardCardCounterparty(getSeller());
		commitAndStartNewTransaction();
		//
		ResponseEntity<Api2Response<String>> responseEntity = sellerClient.request(getSellerCounterpartyUrl(thisOrder.getId()), null, HttpMethod.PUT, TestUtils.getOneParamAsMultiValueMap("sellerCounterpartyId", counterparty.getId()), new ParameterizedTypeReference<Api2Response<String>>() {}, true);
		//
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		Assertions.assertThat(responseEntity.getBody().getMessage())
				.matches("Заказ .*, выплата на банковскую карту недоступна: используйте вывод средств по реквизитам \\(контрагент: .*, \\[b2p\\+tkb\\]\\)");
	}

	private void allowCardPayoutForUser(Long userId) {
		String paramValue = configParamService.getValueAsString(ConfigParamService.CONFIG_PARAM_PAYOUTS_CARD_PAYOUTS_USERS);
		Collection<String> userList = Arrays.stream(StringUtils.split(paramValue, ",")).collect(Collectors.toCollection(HashSet::new));
		if (userList.contains(userId.toString())) {
			return;
		}
		userList.add(userId.toString());
		configParamService.setValueAsString(ConfigParamService.CONFIG_PARAM_PAYOUTS_CARD_PAYOUTS_USERS, StringUtils.join(userList, ","));
	}

	@Test
	@Transactional
	@Rollback(value = false)
	public void _36_setSellerCardCounterpartyOnTCBSuccess() {
		Order thisOrder = _83_X_confirmAgentReportCreateOrder(TcbBankService.SCHEMA);
		orderRepository.save(thisOrder);
		//
		allowCardPayoutForUser(getSeller().getId());
		//
		Counterparty counterparty = createCardCardCounterparty(getSeller());
		commitAndStartNewTransaction();
		//
		ResponseEntity<Api2Response<OrderDTO>> responseEntity = sellerClient.request(getSellerCounterpartyUrl(thisOrder.getId()), null, HttpMethod.PUT, TestUtils.getOneParamAsMultiValueMap("sellerCounterpartyId", counterparty.getId()), new ParameterizedTypeReference<Api2Response<OrderDTO>>() {}, true);
		//
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertEquals(counterparty.getId(), responseEntity.getBody().getData().getSellerCounterparty().getId());
	}

	@Test
	@Transactional
	@Rollback(value = false)
	public void _37_setSellerCardCounterpartyOnTCBExpiredFail() {
		Order thisOrder = _83_X_confirmAgentReportCreateOrder(TcbBankService.SCHEMA);
		orderRepository.save(thisOrder);
		//
		allowCardPayoutForUser(getSeller().getId());
		//
		Counterparty counterparty = createCardCardCounterparty(getSeller());
		counterpartyRepository.save(counterparty);
		commitAndStartNewTransaction();
		// --> ExpireDate set
		((CardCounterparty) counterparty).setCardExpireTime(ZonedDateTime.now().minusMonths(1));
		counterpartyRepository.save(counterparty);
		commitAndStartNewTransaction();
		//
		ResponseEntity<Api2Response<String>> responseEntity01 = sellerClient.request(getSellerCounterpartyUrl(thisOrder.getId()), null, HttpMethod.PUT, TestUtils.getOneParamAsMultiValueMap("sellerCounterpartyId", counterparty.getId()), new ParameterizedTypeReference<Api2Response<String>>() {}, true);
		//
		assertTrue(responseEntity01.getStatusCode().is4xxClientError());
		assertTrue(Pattern.compile("Заказ .*: срок действия карты .* близок к истечению или уже истек \\(контрагент: .*\\)").matcher(responseEntity01.getBody().getMessage()).matches());
		// --> ExpireDate null
		((CardCounterparty) counterparty).setCardExpireTime(null);
		counterpartyRepository.save(counterparty);
		commitAndStartNewTransaction();
		//
		ResponseEntity<Api2Response<String>> responseEntity02 = sellerClient.request(getSellerCounterpartyUrl(thisOrder.getId()), null, HttpMethod.PUT, TestUtils.getOneParamAsMultiValueMap("sellerCounterpartyId", counterparty.getId()), new ParameterizedTypeReference<Api2Response<String>>() {}, true);
		//
		assertTrue(responseEntity02.getStatusCode().is4xxClientError());
		assertTrue(Pattern.compile("Заказ .*: срок действия карты .* не указан \\(контрагент: .*\\)").matcher(responseEntity02.getBody().getMessage()).matches());
	}

	/**
	 * Покупатель получает только свои данные (адрес доставки и контрагент) и не видит данные продавца
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _40_buyerSeesHisAddressEndpointAndCounterparty_butDoesntSeeSellersAddressEndpointAndCounterparty() {
		//В заказе уже кое-что поменялось. Нужно обновить.
		order = orderRepository.findById(order.getId()).orElse(null);
		order.setDeliveryAddressEndpoint(deliveryAddressEndpoint);
		order.setBuyerCounterparty(buyerCounterparty);
		orderRepository.saveAndFlush(order);
		commitTransaction();

		OrderDTO orderDTO = loadOrderSuccessfull(order.getId(), buyerClient, false);

		assertNull(orderDTO.getSellerCounterparty());
		assertNull(orderDTO.getPickupAddressEndpoint());

		assertNotNull(orderDTO.getDeliveryAddressEndpoint());
		assertNotNull(orderDTO.getBuyerCounterparty());

		assertEquals(deliveryAddressEndpoint.getId(), orderDTO.getDeliveryAddressEndpoint().getId());
		assertEquals(buyerCounterparty.getId(), orderDTO.getBuyerCounterparty().getId());
	}

	/**
	 * Продавец получает только свои данные (адрес вывоза и контрагент) и не видит данные покупателя
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _41_sellerSeesHisAddressEndpointAndCounterparty_butDoesntSeeBuyersAddressEndpointAndCounterparty() {
		OrderDTO orderDTO = loadOrderSuccessfull(order.getId(), sellerClient, false);

		assertNull(orderDTO.getBuyerCounterparty());
		assertNull(orderDTO.getDeliveryAddressEndpoint());

		assertNotNull(orderDTO.getPickupAddressEndpoint());
		assertNotNull(orderDTO.getSellerCounterparty());

		assertEquals(pickupAddressEndpoint.getId(), orderDTO.getPickupAddressEndpoint().getId());
		assertEquals(sellerCounterparty.getId(), orderDTO.getSellerCounterparty().getId());
	}

	/**
	 * Продавец не может подтвердить несуществующий
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _50_sellerCantConfirmUnexistedOrder() {
		Long unexistedOrderId = order.getId() * 2 + 845;
		ResponseEntity<String> responseEntity = sellerClient.request(getOrderConfirmUrl(unexistedOrderId), null, HttpMethod.PATCH, null, String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Заказ не найден"));
	}

	/**
	 * Продавец не может подтвердить чужой заказ
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _51_sellerCantConfirmSomeonesOrder() {
		ResponseEntity<String> responseEntity = sellerClient.request(getOrderConfirmUrl(someonesOrderId), null, HttpMethod.PATCH, null, String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Вы не являетесь продавцом"));
	}

	/**
	 * Продавец не может подтвердить заказ, имеющий неподтвержденные позиции
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _52_sellerCantConfirmOrderWithUnconfirmedPositions() {
		ResponseEntity<String> responseEntity = sellerClient.request(getOrderConfirmUrl(order.getId()), null, HttpMethod.PATCH, null, String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("не все позиции в заказе подтверждены"));
	}


	/**
	 * Продавец не может подтвердить несуществующую позицию
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _60_sellerCantConfirmUnexistedOrderPosition() {
		Long unexistedOrderPositionId = getOrderPosition(order, 0).getId() * 2 + 44528;
		ResponseEntity<String> responseEntity = sellerClient.request(getOrderPositionConfirmUrl(order.getId()), null, HttpMethod.PATCH, getOrderPositionConfirmationParams(unexistedOrderPositionId, true), String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Позиция не найдена"));
	}

	/**
	 * Продавец не может подтвердить чужую позицию
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _61_sellerCantConfirmSomeonesOrderPosition() {
		OrderPosition someonesOrderPosition = getOrderPosition(orderRepository.findById(someonesOrderId).orElse(null), 0);
		ResponseEntity<String> responseEntity = sellerClient.request(getOrderPositionConfirmUrl(order.getId()), null, HttpMethod.PATCH, getOrderPositionConfirmationParams(someonesOrderPosition.getId(), true), String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Позиция не найдена"));
	}

	/**
	 * Продавец подтверждает часть позиций, часть отменяет
	 * Отменяется первая позиция, остальные подтверждаются
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _62_sellerConfirmsOrderPositions_successfull() {
		List<OrderPosition> orderPositions = orderPositionRepository.findByOrderOrderByIdDesc(order);
		for (int i = 0; i < orderPositions.size(); i++) {
			OrderPosition orderPosition = orderPositions.get(i);
			Boolean confirm = i != 0;
			ResponseEntity<Api2Response<OrderDTO>> responseEntity = sellerClient.request(getOrderPositionConfirmUrl(order.getId()), null, HttpMethod.PATCH, getOrderPositionConfirmationParams(orderPosition.getId(), confirm), new ParameterizedTypeReference<Api2Response<OrderDTO>>() {
			}, true);
			assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
			assertNotNull(responseEntity.getBody());
			assertNotNull(responseEntity.getBody().getData());
			OrderPositionDTO orderPositionDTO = getOrderPositionById(responseEntity.getBody().getData(), orderPosition.getId());
			assertNotNull(orderPositionDTO);
			assertSame(confirm, orderPositionDTO.getIsConfirmed());
		}
	}

	/**
	 * Продавец меняет решение и отменяет 2-ю позицию
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _63_sellerRejectsOrderPosition_successfull() {
		List<OrderPosition> orderPositions = orderPositionRepository.findByOrderOrderByIdDesc(order);
		OrderPosition orderPosition = orderPositions.get(1);
		ResponseEntity<Api2Response<OrderDTO>> responseEntity = sellerClient.request(getOrderPositionConfirmUrl(order.getId()), null, HttpMethod.PATCH, getOrderPositionConfirmationParams(orderPosition.getId(), false), new ParameterizedTypeReference<Api2Response<OrderDTO>>() {
		}, true);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody());
		assertNotNull(responseEntity.getBody().getData());
		OrderPositionDTO orderPositionDTO = getOrderPositionById(responseEntity.getBody().getData(), orderPosition.getId());
		assertNotNull(orderPositionDTO);
		assertSame(false, orderPositionDTO.getIsConfirmed());
	}

	/**
	 * Получение списка временных периодов
	 */
	@Test
	public void _64_getTimeIntervals_successful() {
		List<TimeInterval> timeIntervals = loadTimeIntervalsSuccessful(sellerClient, true);
		assertEquals(3, timeIntervals.size());
	}

	/**
	 * Продавец пытается установить неправильный временной интервал
	 */
	@Test
	public void _65_sellerSetsPickupTimeInterval_failed() {
		Long wrongIntervalId = 567L;
		ResponseEntity<String> responseEntity = sellerClient.request(getPickupTimeIntervalSetUrl(order.getId()), null, HttpMethod.PUT, TestUtils.getOneParamAsMultiValueMap("timeIntervalId", wrongIntervalId), String.class, false);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Недоступный ID интервала времени"));
	}

	/**
	 * Продавец устанавливает временной период забора заказа
	 */
	@Test
	public void _66_sellerSetsPickupTimeInterval_successful() {
		Long intervalId = 2L;
		ResponseEntity<Api2Response<OrderDTO>> responseEntity = sellerClient.request(getPickupTimeIntervalSetUrl(order.getId()), null, HttpMethod.PUT, TestUtils.getOneParamAsMultiValueMap("timeIntervalId", intervalId), new ParameterizedTypeReference<Api2Response<OrderDTO>>() {
		}, false);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody());
		OrderDTO orderDTO = responseEntity.getBody().getData();
		assertNotNull(orderDTO);
		assertEquals(intervalId, orderDTO.getPickupTimeIntervalId());
	}

	/**
	 * Продавец устанавливает комментарий к забору заказа
	 */
	@Test
	public void _67_sellerSetsPickupComment_successful() {
		String comment = "Прошу направить блондинку 90-60-90";
		ResponseEntity<Api2Response<OrderDTO>> responseEntity = sellerClient.request(getPickupCommentSetUrl(order.getId()), null, HttpMethod.PUT, TestUtils.getOneParamAsMultiValueMap("comment", comment), new ParameterizedTypeReference<Api2Response<OrderDTO>>() {
		}, false);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody());
		OrderDTO orderDTO = responseEntity.getBody().getData();
		assertNotNull(orderDTO);
		assertEquals(comment, orderDTO.getPickupComment());
	}

	/**
	 * Продавец успешно подтверждает заказ
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _70_sellerConfirmsOrder_successful() {
		//Удаляем все активности из системы
		jdbcTemplate.execute("DELETE FROM activity");

		commitAndStartNewTransaction();

		order = getOrder(order.getId());
		order.setState(OrderState.HOLD_PROCESSING);
		OrderPaymentOkayParameters paymentOkayParameters = OrderPaymentOkayParameters.builder()
				.bankService(paymentsServiceBankService).paymentType(null).transactionId("70").acquirerOrderId("70")
				.build();
		orderPaymentService.setOrderAsPaymentOkay(order, paymentOkayParameters);

		//До подтверждения заказа уведомление по этому заказу имело состояние actionCompleted=false
		assertNotificationCreated(order.getId(), order.getSellerUser().getId(), OrderNeedConfirmationNotification.class, true, false, 3, true);
		commitAndStartNewTransaction();

		ResponseEntity<Api2Response<OrderDTO>> responseEntity = sellerClient.request(getOrderConfirmUrl(order.getId()), null, HttpMethod.PATCH, null, new ParameterizedTypeReference<Api2Response<OrderDTO>>() {
		}, true);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody());
		OrderDTO orderDTO = responseEntity.getBody().getData();
		assertNotNull(orderDTO);
		assertEquals(order.getId(), orderDTO.getId());
		assertNotNull(orderDTO.getConfirmedAmount());
		assertTrue(order.getAmount().longValue() > orderDTO.getConfirmedAmount().longValue());

		commitAndStartNewTransaction();

		//С этого момента уведомление по этому заказу меняет состояние на actionCompleted=true
		assertNotificationCreated(order.getId(), order.getSellerUser().getId(), OrderNeedConfirmationNotification.class, true, true, 3, true);
		//assertNotificationCreated(order.getId(), order.getBuyer().getId(), OrderConfirmedNotification.class, false, false, 3, false);
		//Т.к. часть позиций отменена, создается уведомление о частичном подтверждении заказа
		Notification notification = assertNotificationCreated(order.getId(), order.getBuyer().getId(), OrderConfirmedPartlyNotification.class, false, false, 3, false);
		//для продавца
		Notification notification2 = assertNotificationCreated(order.getId(), order.getSellerUser().getId(), SaleConfirmedPartlyNotification.class, false, false, 3, true);
		notificationRepository.delete(notification);
		notificationRepository.delete(notification2);

		//проверяем, что уведомлений о полном подтверджении заказа не создано
		assertNotificationNOTCreated(order.getId(), order.getBuyer().getId(), OrderConfirmedNotification.class, false);
		assertNotificationNOTCreated(order.getId(), order.getSellerUser().getId(), SaleConfirmedNotification.class, true);

		commitAndStartNewTransaction();

		//Проверяем создание активности
		//Сначала принудительно их сохраняем
		activityService.saveAllFromQueue();

		commitAndStartNewTransaction();

		//Получаем последнюю активность OrderFirstConfirmedForBuyerActivity по данному заказу
		OrderFirstConfirmedForBuyerActivity orderFirstConfirmedForBuyerActivity = (OrderFirstConfirmedForBuyerActivity) activityTestRepository.findLastActivityByObjectIdAndDtype(order.getId(), OrderFirstConfirmedForBuyerActivity.class.getSimpleName());
		//Получаем последнюю активность OrderAnotherConfirmedForBuyerActivity по данному заказу
		OrderAnotherConfirmedForBuyerActivity orderAnotherConfirmedForBuyerActivity = (OrderAnotherConfirmedForBuyerActivity) activityTestRepository.findLastActivityByObjectIdAndDtype(order.getId(), OrderAnotherConfirmedForBuyerActivity.class.getSimpleName());

		OrderActivity anyActivity = orderFirstConfirmedForBuyerActivity != null ? orderFirstConfirmedForBuyerActivity : orderAnotherConfirmedForBuyerActivity;
		//Одна из этих активностей должна существовать

		//Активность существует
		assertNotNull(anyActivity);
		//Проверка привязки продавца к активности
		assertEquals(order.getSellerUser().getId(), anyActivity.getUserId());
		//Гостевой токен тоже сохранен
		assertNotNull(anyActivity.getGuestToken());

		//Айтемы с неподтвержденных позиций более недоступны
		order = orderRepository.getOne(order.getId());
		for (OrderPosition op : order.getOrderPositions()) {
			ProductItem pi = op.getProductItem();
			boolean rejected = op.getState() == OrderPositionState.SALE_REJECTED;
			assertEquals(rejected, !pi.isAvailableToBuy(order.getOrderSource()));
			assertEquals(rejected, pi.isHidden());
		}

		DeliveryParamRequestDTO params = DeliveryParamRequestDTO.builder()
				.orderId(order.getId()).courierName("Имя курьера").courierPhone("123456789").pickupDate(ZonedDateTime.now())
				.isOrderProcessingNotificationRequired(false).build();
		orderService.confirmOrderAndSendToPickupOurselves(params);
		List<AdminOrderAlert> alerts = adminAlertService.getAdminAlertsByObjectId(order.getId(), AdminOrderAlertWaybillCreationErrorWrongAddress.class);
		assertEquals(0, alerts.size());
		commitAndStartNewTransaction();
		Notification notification3 = assertNotificationCreated(order.getId(), order.getSellerUser().getId(), SalePickingUpFromSellerNotification.class, false, false, 3, true);
		notificationRepository.delete(notification3);
	}

	/**
	 * Продавец не может подтвердить заказ повторно
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _80_sellerCantConfirmOrderAgain() {
		ResponseEntity<String> responseEntity = sellerClient.request(getOrderConfirmUrl(order.getId()), null, HttpMethod.PATCH, null, String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Нельзя подтвердить/отменить подтвержденный заказ"));
	}

	/**
	 * Продавец не может подтвердить позицию в уже подтвержденном заказе
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _81_sellerCantConfirmOrderPositionOnConfirmedOrder() {
		OrderPosition orderPosition = getOrderPosition(order, 0);

		ResponseEntity<String> responseEntity = sellerClient.request(getOrderPositionConfirmUrl(order.getId()), null, HttpMethod.PATCH, getOrderPositionConfirmationParams(orderPosition.getId(), true), String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Нельзя подтвердить/отменить позицию в подтвержденном заказе"));
	}

	/**
	 * Если по позиции был возврат, то эту информацию получает покупатель в виде флага isMoneyReturned
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _82_orderPositionMoneyReturned() {
		List<Long> moneyReturnedPositionIds = new ArrayList<>();

		List<OrderPosition> orderPositions = orderPositionRepository.findByOrderOrderByIdDesc(order);

		for (int i = 0; i < orderPositions.size(); i++) {
			OrderPosition orderPosition = orderPositions.get(i);
			if (i == 0 || i == 3) {
				orderPosition.setState(OrderPositionState.SALE_REJECTED);
				orderPosition.setConfirmedTime(null);
				moneyReturnedPositionIds.add(orderPosition.getId());
			} else if (i == 1) {
				orderPosition.setState(OrderPositionState.REJECTED_AFTER_VERIFICATION);
				moneyReturnedPositionIds.add(orderPosition.getId());
			} else {
				orderPosition.setState(OrderPositionState.SALE_CONFIRMED);
				orderPosition.setConfirmedTime(ZonedDateTime.now());
			}
		}
		orderPositionRepository.saveAll(orderPositions);

		commitTransaction();

		OrderDTO sellerOrderDTO = loadOrderSuccessfull(order.getId(), sellerClient, false);
		OrderDTO buyerOrderDTO = loadOrderSuccessfull(order.getId(), buyerClient, false);

		for (int i = 0; i < sellerOrderDTO.getItems().size(); i++) {
			OrderPositionDTO sellerOrderPositionDTO = sellerOrderDTO.getItems().get(i);
			OrderPositionDTO buyerOrderPositionDTO = buyerOrderDTO.getItems().get(i);
			if (moneyReturnedPositionIds.contains(sellerOrderPositionDTO.getId())) {
				assertNull(sellerOrderPositionDTO.getIsMoneyReturned());
				//DEBUG
				if (buyerOrderPositionDTO.getIsMoneyReturned() == null) {
					System.out.println("buyerOrderPositionDTO.getIsMoneyReturned(): " + buyerOrderPositionDTO.getIsMoneyReturned());
					System.out.println("buyerOrderPositionDTO.getId(): " + buyerOrderPositionDTO.getId());
					System.out.println("buyerOrderDTO.getId(): " + buyerOrderDTO.getId());
					System.out.println("moneyReturnedPositionIds: " + moneyReturnedPositionIds.stream().map(id -> id.toString()).collect(Collectors.joining(",")));
					System.exit(0);
					assertTrue(buyerOrderPositionDTO.getIsMoneyReturned());
				}
			} else {
				assertNull(sellerOrderPositionDTO.getIsMoneyReturned());
				//DEBUG
				if (buyerOrderPositionDTO.getIsMoneyReturned() != null) {
					System.out.println("buyerOrderPositionDTO.getIsMoneyReturned(): " + buyerOrderPositionDTO.getIsMoneyReturned());
					System.out.println("buyerOrderPositionDTO.getId(): " + buyerOrderPositionDTO.getId());
					System.out.println("buyerOrderDTO.getId(): " + buyerOrderDTO.getId());
					System.out.println("moneyReturnedPositionIds: " + moneyReturnedPositionIds.stream().map(id -> id.toString()).collect(Collectors.joining(",")));
					System.exit(0);
					assertNull(buyerOrderPositionDTO.getIsMoneyReturned());
				}
			}
		}

	}

	/**
	 * Первая позиция проходит экспертизу, вторая нет. По остальным инфы нет.
	 */

	@Test
	@Transactional
	@Rollback(value = false)
	public void _83_0_prepare() {
		TestTransaction.end();

		OrderPosition orderPosition;
		Expertise expertise;

		TestTransaction.start();
		Order o = orderRepository.getOne(order.getId());
		o.setDeliveryState(DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE);
		orderRepository.saveAndFlush(o);

		orderPosition = getOrderPosition(order, 0);
		//orderPosition.setState(OrderPositionState.SALE_CONFIRMED);
		orderPosition.setState(OrderPositionState.VERIFICATION_OK);
		expertise = new Expertise().setIsApproved(true).setCreateTime(ZonedDateTime.now())
				.setOrder(order).setOrderPosition(orderPosition).setPickupFrom(DestinationType.SELLER);
		expertiseRepository.saveAndFlush(expertise);
		orderPositionRepository.saveAndFlush(orderPosition);
		approvedExpertiseId = orderPosition.getId();

		orderPosition = getOrderPosition(order, 1);
		//orderPosition.setState(OrderPositionState.SALE_CONFIRMED);
		orderPosition.setState(OrderPositionState.VERIFICATION_BAD_STATE);
		expertise = new Expertise().setIsApproved(false).setCreateTime(ZonedDateTime.now())
				.setOrder(order).setOrderPosition(orderPosition).setPickupFrom(DestinationType.SELLER)
				.setRejectionReason("Носки с дырками");
		expertiseRepository.saveAndFlush(expertise);
		orderPositionRepository.saveAndFlush(orderPosition);
		unapprovedExpertiseId = orderPosition.getId();
	}

	@Test
	@Transactional
	@Rollback(value = false)
	public void _83_1_expertised() {
		ExpertiseDTO expertiseDTO;

		OrderDTO orderDTO = loadOrderSuccessfull(order.getId(), sellerClient, false);

		//Заодно проверим, есть ли прибыль (сумма, оторую получит продавец) в заказе
		assertNotNull(orderDTO.getSellerReceivesAmount());

		for (int i = 0; i < orderDTO.getItems().size(); i++) {
			OrderPositionDTO orderPositionDTO = orderDTO.getItems().get(i);
			//Заодно проверим, есть ли сумма, прибыль и комиссия в позиции заказа
			assertNotNull(orderPositionDTO.getAmount());
			assertNotNull(orderPositionDTO.getSellerReceivesAmount());
			assertNotNull(orderPositionDTO.getCommission());

			if (orderPositionDTO.getId() == approvedExpertiseId) {
				assertNotNull(orderPositionDTO.getExpertises());
				assertFalse(orderPositionDTO.getExpertises().isEmpty());
				expertiseDTO = orderPositionDTO.getExpertises().get(0);
				assertNotNull(expertiseDTO);
				assertTrue(expertiseDTO.getIsApproved());
			} else if (orderPositionDTO.getId() == unapprovedExpertiseId) {
				assertNotNull(orderPositionDTO.getExpertises());
				assertFalse(orderPositionDTO.getExpertises().isEmpty());
				expertiseDTO = orderPositionDTO.getExpertises().get(0);
				assertNotNull(expertiseDTO);
				assertFalse(expertiseDTO.getIsApproved());
				assertEquals("Носки с дырками", expertiseDTO.getRejectionReason());
			} else {
				assertNull(orderPositionDTO.getExpertises());
			}
		}

	}

	//Отправка уведемления продавцу для подтверждения отчета агента
	@Test
	@Transactional
	@Rollback(value = false)
	public void _83_2_agentReportRequest() {
		TestTransaction.end();
		TestTransaction.start();
		setOrderState(order.getId(), OrderState.HOLD_COMPLETED);
		order = orderRepository.getOne(order.getId());
		//Проставляет позициям заказа isEffective = true
		orderService.getCompletionReadiness(order);
		agentRepostService.sendToSeller(order, new AgentReportParams(order.getId(), Boolean.FALSE));
		TestTransaction.end();

		TestTransaction.start();
//		order = getOrder(order.getId());
		OrderNotification orderNotification = assertNotificationCreated(order.getId(), order.getSellerUser().getId(), OrderDeliveredToBuyerNeedAgentReportNotification.class, true, false, 3, true);
		assertTrue(orderNotification.getTargetObjectImage().isPresent());
		TestTransaction.end();

		//Проверка на дубликат
		TestTransaction.start();
		order = orderRepository.getOne(order.getId());
		agentRepostService.sendToSeller(order, new AgentReportParams(order.getId(), Boolean.FALSE));
		TestTransaction.end();

		TestTransaction.start();
		order = orderRepository.getOne(order.getId());
		OrderNotification orderNotification2 = assertNotificationCreated(order.getId(), order.getSellerUser().getId(), OrderDeliveredToBuyerNeedAgentReportNotification.class, true, false, 3, true);
		TestTransaction.end();
		//Это то же самое уведомление. Новое не создавалось
		assertEquals(orderNotification.getId(), orderNotification2.getId());

	}

	//Продавец видит отчет агента через АПИ
	@Test
	@Transactional
	@Rollback(value = false)
	public void _83_3_sellerSeesHisAgentReport() {
		AgentReport agentReport = orderRepository.getOne(order.getId()).getAgentReport();
		ResponseEntity<Api2Response<List<AgentReportDTO>>> responseEntity = sellerClient.request(getAgentReportServiceUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<AgentReportDTO>>>() {
		}, true);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody());
		assertNotNull(responseEntity.getBody().getData());
		List<AgentReportDTO> agentReportDTOs = responseEntity.getBody().getData();
		assertFalse(agentReportDTOs.isEmpty());
		boolean found = false;
		for (AgentReportDTO dto : agentReportDTOs) {
			if (dto.getId().equals(agentReport.getId())) {
				found = true;
				break;
			}
		}
		assertTrue(found);
	}

	//Продавец подтверждает отчет агента через АПИ
	@Transactional
	@Test
	public void _83_4_sellerConfirmsAgentReport() {
		AgentReport agentReport = orderRepository.getOne(order.getId()).getAgentReport();
		ResponseEntity<Api2Response<Long>> responseEntity = sellerClient.request(getAgentReportConfirmationUrl(agentReport.getId()), null, HttpMethod.PATCH, null, new ParameterizedTypeReference<Api2Response<Long>>() {
		}, true);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody());
		assertNotNull(responseEntity.getBody().getData());
		Long confirmedAgentReportId = responseEntity.getBody().getData();
		assertEquals(agentReport.getId(), confirmedAgentReportId);

		commitAndStartNewTransaction();

		order = getOrder(order.getId());
		//В уведомлении теперь указано, что действие выполнено
		OrderNotification orderNotification = assertNotificationCreated(order.getId(), order.getSellerUser().getId(), OrderDeliveredToBuyerNeedAgentReportNotification.class, true, true, 3, true);
		//уведомление о подтверждении отчета агента продавцу
		Notification notification2 = assertNotificationCreated(order.getId(), order.getSellerUser().getId(), OrderAgentReportConfirmedNotification.class, false, false, 3, true);
		assertTrue(orderNotification.getTargetObjectImage().isPresent());
		notificationRepository.delete(notification2);
	}

	//Продавец снова подтверждает отчет агента через АПИ
	@Transactional
	@Test
	public void _83_5_sellerConfirmsAgentReport_failed() {
		AgentReport agentReport = orderRepository.getOne(order.getId()).getAgentReport();
		ResponseEntity<String> responseEntity = sellerClient.request(getAgentReportConfirmationUrl(agentReport.getId()), null, HttpMethod.PATCH, null, String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("AgentReportException"));
		assertTrue(responseEntity.getBody().contains(MessageFormat.format("Отчет о продаже для заказа {0, number} уже подтвержден", agentReport.getOrder().getId())));
	}

	@Transactional
	@Test
	@Rollback(value = false)
	public void _83_6_confirmAgentReportWithPayoutToCardBest2PayFail() {
		Order thisOrder = _83_X_confirmAgentReportCreateOrder(Best2payAndTcbBankService.SCHEMA);
		thisOrder.setSellerCounterparty(createCardCardCounterparty(getSeller()));
		orderRepository.save(thisOrder);
		//
		thisOrder = orderRepository.getOne(thisOrder.getId());
		agentRepostService.sendToSeller(thisOrder, new AgentReportParams(thisOrder.getId(), Boolean.FALSE));
		commitAndStartNewTransaction();
		//
		AgentReport agentReport = orderRepository.getOne(thisOrder.getId()).getAgentReport();
		ResponseEntity<Api2Response<String>> responseEntity = sellerClient.request(getAgentReportConfirmationUrl(agentReport.getId()), null, HttpMethod.PATCH, null, new ParameterizedTypeReference<Api2Response<String>>() {}, true);
		//
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().getMessage().contains(MessageFormat.format("Для заказа {0, number} указаны реквизиты вывода на карту (", thisOrder.getId())));
		assertTrue(responseEntity.getBody().getMessage().contains("): не поддерживаются на данный момент"));
	}

	@Transactional
	@Test
	@Rollback(value = false)
	public void _83_7_confirmAgentReportWithPayoutToCardTCBSuccess() {
		Order thisOrder = _83_X_confirmAgentReportCreateOrder(TcbBankService.SCHEMA);
		thisOrder.setSellerCounterparty(createCardCardCounterparty(getSeller()));
		orderRepository.save(thisOrder);
		//
		thisOrder = orderRepository.getOne(thisOrder.getId());
		agentRepostService.sendToSeller(thisOrder, new AgentReportParams(thisOrder.getId(), Boolean.FALSE));
		commitAndStartNewTransaction();
		//
		AgentReport agentReport = orderRepository.getOne(thisOrder.getId()).getAgentReport();
		ResponseEntity<Api2Response<Long>> responseEntity = sellerClient.request(getAgentReportConfirmationUrl(agentReport.getId()), null, HttpMethod.PATCH, null, new ParameterizedTypeReference<Api2Response<Long>>() {}, true);
		//
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertEquals(agentReport.getId(), responseEntity.getBody().getData());
	}

	@Transactional
	@Test
	@Rollback(value = false)
	public void _83_8_confirmAgentReportWithPayoutToCardTCBSuccessAndBonusesTransfer() {
		Order thisOrder = _83_X_confirmAgentReportCreateOrder(TcbBankService.SCHEMA);
		BigDecimal bonuses = BigDecimal.ZERO;
		for (OrderPosition position: thisOrder.getOrderPositions()) {
			BigDecimal b = new BigDecimal(100);
			position.setEffectiveTransferredBonuses(b);
			bonuses = bonuses.add(b);
		}
		UserBonusesAccount userBonusesAccount = new UserBonusesAccount();
		userBonusesAccount.setBonusesAccountId(UUID.randomUUID().toString());
		when(userBonusesAccountRepository.findByUserId(any(Long.class)))
				.thenReturn(Optional.of(userBonusesAccount));
		BonusesBalanceDTO balance1 = bonusesService.getBalance(thisOrder.getBuyer().getId());
		thisOrder.setSellerCounterparty(createCardCardCounterparty(getSeller()));
		orderRepository.save(thisOrder);
		//
		thisOrder = orderRepository.getOne(thisOrder.getId());
		agentRepostService.sendToSeller(thisOrder, new AgentReportParams(thisOrder.getId(), Boolean.FALSE));
		//Здесь ранее была проверка того, что после отправки отчета агента были начислены бонусы.
		//Сейчас проверка удалена, т.к. бонусы начисляются отдельным шедулером через 14 дней после отправки отчета агента.
		commitAndStartNewTransaction();

		//
		AgentReport agentReport = orderRepository.getOne(thisOrder.getId()).getAgentReport();
		ResponseEntity<Api2Response<Long>> responseEntity = sellerClient.request(getAgentReportConfirmationUrl(agentReport.getId()), null, HttpMethod.PATCH, null, new ParameterizedTypeReference<Api2Response<Long>>() {}, true);
		//
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertEquals(agentReport.getId(), responseEntity.getBody().getData());
	}

	private Order _83_X_confirmAgentReportCreateOrder(String paymentVersion) {
		Order testOrder = createOrder(getProductsForOrders());
		testOrder.setState(OrderState.MONEY_TRANSFERRED);
		testOrder.setDeliveryState(DeliveryState.DELIVERED_TO_BUYER);
		testOrder.setConfirmedTime(ZonedDateTime.now());
		testOrder.setPaymentVersion(paymentVersion);
		//
		for(OrderPosition op : testOrder.getOrderPositions()) {
			op.setState(OrderPositionState.VERIFICATION_OK);
			op.setParticipatesInPayment(true);
			op.setConfirmedTime(ZonedDateTime.now());
			op.setIsEffective(true);
			op.setAmount(new BigDecimal(100));
			op.setItemSaleAmount(op.getAmount());
		}
		//
		return testOrder;
	}

	/**
	 * Отмена заказа целиком без адреса забора.
	 */

	@Test
	@Transactional
	@Rollback(value = false)
	public void _84_0_prepare() {
		Order o = orderRepository.getOne(order.getId());
		o.setDeliveryState(null);
		o.setDeliveryAddressEndpoint(createAddressEndpoint(order.getBuyer()));
		o.setConfirmedTime(null);

		orderRepository.saveAndFlush(o);

		for (OrderPosition op : o.getOrderPositions()) {
			op.setState(OrderPositionState.SALE_REJECTED);
			op.setConfirmedTime(null);
			orderPositionRepository.saveAndFlush(op);
		}

	}

	//Отмена заказа целиком без адреса забора.

	@Test
	@Transactional
	@Rollback(value = false)
	public void _84_1_rejected_without_pickup_address() {
		order = getOrder(order.getId());
		orderPaymentService.initOrderPayment(order, null, null);
		order.setState(OrderState.HOLD_PROCESSING);
		OrderPaymentOkayParameters paymentOkayParameters = OrderPaymentOkayParameters.builder()
				.bankService(paymentsServiceBankService).paymentType(null).transactionId("_84_1_rejected_without_pickup_address").acquirerOrderId("_84_1_rejected_without_pickup_address")
				.build();
		orderPaymentService.setOrderAsPaymentOkay(order, paymentOkayParameters);
		order.getAgentReport().setState(AgentReportState.PAYMENT_CANCELED);
		agentRepostService.saveAgentReport(order.getAgentReport());
		commitAndStartNewTransaction();
		//
		order = getOrder(order.getId());
		ResponseEntity<Api2Response<OrderDTO>> responseEntity = sellerClient.request(getOrderConfirmUrl(order.getId()), null, HttpMethod.PATCH, null, new ParameterizedTypeReference<Api2Response<OrderDTO>>() {
		}, true);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody());
		OrderDTO orderDTO = responseEntity.getBody().getData();
		assertNotNull(orderDTO);
		assertEquals(order.getId(), orderDTO.getId());
		assertSame(OrderState.REFUND, orderDTO.getState());
		Notification notification = assertNotificationCreated(order.getId(), order.getBuyer().getId(), OrderRejectedNotification.class, false, false, 3, false);
		//для продавца
		Notification notification2 = assertNotificationCreated(order.getId(), order.getSellerUser().getId(), SaleRejectedNotification.class, false, false, 3, true);

		notificationRepository.delete(notification);
		notificationRepository.delete(notification2);

	}

	@Test
	@Transactional
	@Rollback(value = false)
	public void _84_2_orderStatusPresent() {
		OrderDTO orderDTO = loadOrderSuccessfull(order.getId(), sellerClient, false);
		assertNotNull(orderDTO.getOrderStatus());
		assertNotNull(orderDTO.getOrderStatusTitle());
		assertSame(OrderStatus.ORDER_REFUND, orderDTO.getOrderStatus());
		assertEquals("Возврат средств", orderDTO.getOrderStatusTitle());
	}

	@Test
	@Transactional
	@Rollback(value = false)
	public void _85_0_prepare() {
		TestTransaction.end();

		TestTransaction.start();
		Order o = orderRepository.getOne(order.getId());
		o.setDeliveryState(null);
		o.setState(OrderState.HOLD);
		o.setPickupAddressEndpoint(pickupAddressEndpoint);
		o.setConfirmedTime(null);
		o.setDeliveryAddressEndpoint(deliveryAddressEndpoint);
		o.setBuyerCounterparty(buyerCounterparty);
		orderService.onHold(o);
		//sellerClient.request(getPickupAddressEndpointUrl(o.getId()), null, HttpMethod.PUT, TestUtils.getOneParamAsMultiValueMap("pickupAddressEndpointId", pickupAddressEndpoint.getId()), new ParameterizedTypeReference<Api2Response<OrderDTO>>() {}, false);
		AgentReport agentReport = orderRepository.getOne(o.getId()).getAgentReport();
		List<BankPayment> payments = agentReport.getBankPayments();
		if (!payments.isEmpty()) bankPaymentRepository.deleteAll(payments);
		agentReportPaymentsRepository.deleteAll(agentReport.getAgentReportPayments());
		agentReportRepository.delete(agentReport);
		o.setAgentReport(null);
		orderRepository.saveAndFlush(o);

		for (int i = 0; i < o.getOrderPositions().size(); i++) {
			OrderPosition orderPosition = o.getOrderPositions().get(i);
			Boolean confirm = true;
			ResponseEntity<Api2Response<OrderDTO>> responseEntity = sellerClient.request(getOrderPositionConfirmUrl(o.getId()), null, HttpMethod.PATCH, getOrderPositionConfirmationParams(orderPosition.getId(), confirm), new ParameterizedTypeReference<Api2Response<OrderDTO>>() {
			}, true);
		}
		WaybillOrder waybillOrder = new WaybillOrder();
		waybillOrder.setCreateTime(ZonedDateTime.now())
				.setExternalSystemId(null)
				.setUuid(UUID.randomUUID());
		WaybillRequisite requisite = new WaybillRequisite();
		requisite.setPhone(deliveryAddressEndpointPhone)
				.setZipCode(deliveryAddressEndpointZip)
				.setName(deliveryAddressEndpointaLastName + " " + deliveryAddressEndpointFirstName + " " + deliveryAddressEndpointaPatronymicName)
				.setAddress(deliveryAddressEndpointAddress);
		DeliveryCompany deliveryCompany = deliveryCompanyRepository
				.findOne(QDeliveryCompany.deliveryCompany.name.eq("Major"))
				.orElse(null);
		Waybill waybill = new Waybill();
		waybill.setUuid(UUID.randomUUID())
				.setPickupRequisite(requisite)
				.setDeliveryRequisite(requisite)
				.setPickupDestinationType(DestinationType.OFFICE)
				.setDeliveryDestinationType(DestinationType.SELLER)
				.setOrder(o)
				.setWaybillOrder(waybillOrder)
				.setState(Waybill.State.PENDING)
				.setDeliveryCompany(deliveryCompany);
		waybillOrderRepository.save(waybillOrder);
		waybillRepository.save(waybill);

	}

	@Test
	@Transactional
	@Rollback(value = false)
	public void _85_1_sellerConfirmsFullOrder_successful() {
		order = getOrder(order.getId());
		orderPaymentService.initOrderPayment(order, null, null);
		order.setState(OrderState.HOLD_PROCESSING);
		OrderPaymentOkayParameters paymentOkayParameters = OrderPaymentOkayParameters.builder()
				.bankService(paymentsServiceBankService).paymentType(null).transactionId("70").acquirerOrderId("70")
				.build();
		orderPaymentService.setOrderAsPaymentOkay(order, paymentOkayParameters);
		commitAndStartNewTransaction();

		ResponseEntity<Api2Response<OrderDTO>> responseEntity = sellerClient.request(getOrderConfirmUrl(order.getId()), null, HttpMethod.PATCH, null, new ParameterizedTypeReference<Api2Response<OrderDTO>>() {
		}, true);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody());
		OrderDTO orderDTO = responseEntity.getBody().getData();
		assertNotNull(orderDTO);
		assertEquals(order.getId(), orderDTO.getId());
		assertNotNull(orderDTO.getConfirmedAmount());

		//для покупателя
		Notification notification = assertNotificationCreated(order.getId(), order.getBuyer().getId(), OrderConfirmedNotification.class, false, false, 3, false);
		//для продавца
		Notification notification2 = assertNotificationCreated(order.getId(), order.getSellerUser().getId(), SaleConfirmedNotification.class, false, false, 3, true);

		//проверяем, что не создались уведомления о частичном подтверждении
		assertNotificationNOTCreated(order.getId(), order.getBuyer().getId(), OrderConfirmedPartlyNotification.class, false);
		assertNotificationNOTCreated(order.getId(), order.getSellerUser().getId(), SaleConfirmedPartlyNotification.class, true);

		notificationRepository.delete(notification);
		notificationRepository.delete(notification2);
		notificationRepository.delete(notification2);
	}

	@Test
	@Transactional
	@Rollback(value = false)
	public void _86_0_picking_up_from_seller_ourselves_notification_created_successful() {
		TestTransaction.end();
		TestTransaction.start();

		order = getOrder(order.getId());
		Map<String, String> courierParams = new HashMap<String, String>();
		courierParams.put("courierName", "Test");
		courierParams.put("courierPhone", "+77777777777");
		courierParams.put("courierDate", "" + ZonedDateTime.now().toEpochSecond());
		ResponseEntity<?> responseEntity = buyerClient.request(getSendConfirmedOrderUrl(order.getId()), courierParams, HttpMethod.PUT, null, String.class, true);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		Notification notification = assertNotificationCreated(order.getId(), order.getSellerUser().getId(), SalePickingUpFromSellerNotification.class, false, false, 3, true);

		notificationRepository.delete(notification);
	}

	@Test
	@Transactional
	@Rollback(value = false)
	@WithMockUser(authorities = {"MASTER_USER"})
	public void _86_1_picking_up_from_seller_logistics_notification_created_successful() {
		order = getOrder(order.getId());
		//Для проходжения теста откатываемся на шаг назад после предыдущего теста
		order.setDeliveryState(null);
		orderRepository.save(order);
		commitAndStartNewTransaction();

		order = getOrder(order.getId());
		LogisticStateDeliveryDTO test = new LogisticStateDeliveryDTO()
				.setWaybillId(order.getLastWaybill().getId())
				.setDeliveryState(DeliveryState.PICKING_UP_FROM_SELLER)
				.setWaybillInnerDeliveryStatusId("-1");
		commonLogisticService.saveLogisticStateDeliveries(Collections.singletonList(test));
		commitAndStartNewTransaction();
		order = getOrder(order.getId());
		Notification notification = assertNotificationCreated(order.getId(), order.getSellerUser().getId(), SalePickingUpFromSellerNotification.class, false, false, 3, true);
		notificationRepository.delete(notification);
	}

	//Курьер забрал заказ у продавца
	@Test
	@Transactional
	@Rollback(value = false)
	@WithMockUser(authorities = {"MASTER_USER"})
	public void _86_2_Delivering_FromSellerToOffice_logistics_notification_created_successful() {
		order = getOrder(order.getId());
		LogisticStateDeliveryDTO test = new LogisticStateDeliveryDTO()
				.setWaybillId(order.getLastWaybill().getId())
				.setDeliveryState(DeliveryState.FROM_SELLER_TO_OFFICE)
				.setWaybillInnerDeliveryStatusId("-1");
		commonLogisticService.saveLogisticStateDeliveries(Collections.singletonList(test));
		commitAndStartNewTransaction();
		order = getOrder(order.getId());
		//для покупателя
		Notification notification = assertNotificationCreated(order.getId(), order.getBuyer().getId(), OrderDeliveringFromSellerToOfficeNotification.class, false, false, 3, false);
		//для продавца
		Notification notification2 = assertNotificationCreated(order.getId(), order.getSellerUser().getId(), SaleDeliveringFromSellerToOfficeNotification.class, false, false, 3, true);

		notificationRepository.delete(notification);
		notificationRepository.delete(notification2);
	}

	@Test
	@Transactional
	@Rollback(value = false)
	public void _86_3_Delivering_FromSellerToOffice_notification_created_successful() {
		order = getOrder(order.getId());
		//
		order.setDeliveryState(null);
		//
		orderService.saveOrderDeliveryState(new SaveDeliveryStateDTO(order, DeliveryState.OURSELVES_PICKING_UP_FROM_SELLER, false));
		orderService.saveOrderDeliveryState(new SaveDeliveryStateDTO(order, DeliveryState.OURSELVES_FROM_SELLER_TO_OFFICE, false));
		commitAndStartNewTransaction();
		Notification notification = assertNotificationCreated(order.getId(), order.getBuyer().getId(), OrderDeliveringFromSellerToOfficeNotification.class, false, false, 3, false);
		Notification notification2 = assertNotificationCreated(order.getId(), order.getSellerUser().getId(), SaleDeliveringFromSellerToOfficeNotification.class, false, false, 3, true);

		notificationRepository.delete(notification);
		notificationRepository.delete(notification2);
	}


	@Test
	@Transactional
	@Rollback(value = false)
	@WithMockUser(authorities = {"MASTER_USER"})
	public void _87_1_PickupDeclined_notification_created_successful() {
		order = getOrder(order.getId());
		//Для проходжения теста откатываемся на шаг назад после предыдущего теста
		order.setDeliveryState(null);
		orderRepository.save(order);
		commitAndStartNewTransaction();

		order = getOrder(order.getId());
		LogisticStateDeliveryDTO test = new LogisticStateDeliveryDTO()
				.setWaybillId(order.getLastWaybill().getId())
				.setDeliveryState(DeliveryState.PICKUP_DECLINED)
				.setWaybillInnerDeliveryStatusId("-1");
		commonLogisticService.saveLogisticStateDeliveries(Collections.singletonList(test));
		commitAndStartNewTransaction();
		order = getOrder(order.getId());
		Notification notification = assertNotificationCreated(order.getId(), order.getBuyer().getId(), OrderPickupDeclinedNotification.class, false, false, 3, false);
		Notification notification2 = assertNotificationCreated(order.getId(), order.getSellerUser().getId(), SalePickupDeclinedNotification.class, false, false, 3, true);

		notificationRepository.delete(notification);
		notificationRepository.delete(notification2);
	}

	@Test
	@Transactional
	@Rollback(value = false)
	public void _87_2_PickupDeclined_notification_created_successful() {
		order = getOrder(order.getId());
		//Для проходжения теста откатываемся на шаг назад после предыдущего теста
		orderPaymentService.initOrderPayment(order, null, null);
		order.setState(OrderState.HOLD_PROCESSING);
		OrderPaymentOkayParameters paymentOkayParameters = OrderPaymentOkayParameters.builder()
				.bankService(paymentsServiceBankService).paymentType(null).transactionId("87_2").acquirerOrderId("87_2")
				.build();
		orderPaymentService.setOrderAsPaymentOkay(order, paymentOkayParameters);
		order.setDeliveryState(null);
		order.setTransactionId("-1");
		orderRepository.save(order);
		commitAndStartNewTransaction();

		order = getOrder(order.getId());

		ResponseEntity<?> responseEntity = buyerClient.request(getPickupDeclinedUrl(order.getId()), null, HttpMethod.PUT, null, String.class, true);
		Notification notification = assertNotificationCreated(order.getId(), order.getBuyer().getId(), OrderPickupDeclinedNotification.class, false, false, 3, false);
		Notification notification2 = assertNotificationCreated(order.getId(), order.getSellerUser().getId(), SalePickupDeclinedNotification.class, false, false, 3, true);

		notificationRepository.delete(notification);
		notificationRepository.delete(notification2);
	}

	@Test
	@Transactional
	@Rollback(value = false)
	@WithMockUser(authorities = {"MASTER_USER"})
	public void _88_1_DeliveredFromSellerToOffice_notification_created_successful() {
		setOrderState(order.getId(), OrderState.HOLD);
		commitAndStartNewTransaction();

		order = getOrder(order.getId());
		LogisticStateDeliveryDTO test = new LogisticStateDeliveryDTO()
				.setWaybillId(order.getLastWaybill().getId())
				.setDeliveryState(DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE)
				.setWaybillInnerDeliveryStatusId("-1");
		commonLogisticService.saveLogisticStateDeliveries(Collections.singletonList(test));
		commitAndStartNewTransaction();
		order = getOrder(order.getId());
		Notification notification = assertNotificationCreated(order.getId(), order.getBuyer().getId(), OrderDeliveredToExpertiseNotification.class, false, false, 3, false);
		Notification notification2 = assertNotificationCreated(order.getId(), order.getSellerUser().getId(), SaleDeliveredToExpertiseNotification.class, false, false, 3, true);

		notificationRepository.delete(notification);
		notificationRepository.delete(notification2);
	}

	@Test
	@Transactional
	@Rollback(value = false)
	public void _88_2_DeliveredFromSellerToOffice_notification_created_successful() {
		order = getOrder(order.getId());
		order.setDeliveryState(null);
		orderRepository.save(order);
		commitAndStartNewTransaction();

		order = getOrder(order.getId());
		orderService.saveOrderDeliveryState(new SaveDeliveryStateDTO(order, null, false));
		orderService.saveOrderDeliveryState(new SaveDeliveryStateDTO(order, DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE, false));
		commitAndStartNewTransaction();
		Notification notification = assertNotificationCreated(order.getId(), order.getBuyer().getId(), OrderDeliveredToExpertiseNotification.class, false, false, 3, false);
		Notification notification2 = assertNotificationCreated(order.getId(), order.getSellerUser().getId(), SaleDeliveredToExpertiseNotification.class, false, false, 3, true);

		notificationRepository.delete(notification);
		notificationRepository.delete(notification2);
	}

	@Test
	@Transactional
	@Rollback(value = false)
	@WithMockUser(authorities = {"ADMIN"})
	public void _89_1_expertise_passed_notification_created_successful() {
		// prepare order state
		order = getOrder(order.getId());
		orderPaymentService.initOrderPayment(order, null, null);
		orderService.applyAndSaveOrderPositionDuties(order, DutyService.CalculationStage.HOLD);
		order.setState(OrderState.HOLD_PROCESSING);
		OrderPaymentOkayParameters paymentOkayParameters = OrderPaymentOkayParameters.builder()
				.bankService(paymentsServiceBankService)
				.paymentType(null)
				.transactionId("test_89_1")
				.acquirerOrderId("test_89_1")
				.build();
		orderPaymentService.setOrderAsPaymentOkay(order, paymentOkayParameters);
		setOrderState(order.getId(), OrderState.HOLD);
		commitAndStartNewTransaction();

		// pass expertise
		order.getOrderPositions().forEach(it-> adminConfirmationService.setExpertisePassed(it.getId()));
		commitAndStartNewTransaction();

		// charge money
		adminOrdersService.chargeMoney(new ChargeParams(order.getId(), true, false));
		commitAndStartNewTransaction();

		// check notifications
		Notification buyerNotification = assertNotificationCreated(order.getId(), order.getBuyer().getId(), OrderExpertisePassedNotification.class, false, false, 3, false);
		Notification sellerNotification = assertNotificationCreated(order.getId(), order.getSellerUser().getId(), SaleExpertisePassedNotification.class, false, false, 3, true);

		notificationRepository.delete(buyerNotification);
		notificationRepository.delete(sellerNotification);
	}

	@Test
	@Transactional
	@Rollback(value = false)
	@WithMockUser(authorities = {"ADMIN"})
	public void _90_1_expertise_passed_partly_notification_created_successful() {
		// prepare order state
		order = getOrder(order.getId());
		orderPaymentService.initOrderPayment(order, null, null);
		orderService.applyAndSaveOrderPositionDuties(order, DutyService.CalculationStage.HOLD);
		order.setState(OrderState.HOLD_PROCESSING);
		OrderPaymentOkayParameters paymentOkayParameters = OrderPaymentOkayParameters.builder()
				.bankService(paymentsServiceBankService)
				.paymentType(null)
				.transactionId("test_89_1")
				.acquirerOrderId("test_89_1")
				.build();
		orderPaymentService.setOrderAsPaymentOkay(order, paymentOkayParameters);
		setOrderState(order.getId(), OrderState.HOLD);
		commitAndStartNewTransaction();

		// pass expertise partly
		order.getOrderPositions().forEach(it-> {
					if (it.getId() % 2 == 0) {
						adminConfirmationService.setExpertisePassed(it.getId());
					} else {
						adminConfirmationService.setExpertiseReject(it.getId(), "conclusion", "reason");
					}
				}
		);
		commitAndStartNewTransaction();

		// charge money
		adminOrdersService.chargeMoney(new ChargeParams(order.getId(), true, false));
		commitAndStartNewTransaction();

		// check notifications
		Notification buyerNotification = assertNotificationCreated(order.getId(), order.getBuyer().getId(), OrderExpertisePassedPartlyNotification.class, false, false, 3, false);
		Notification sellerNotification = assertNotificationCreated(order.getId(), order.getSellerUser().getId(), SaleExpertisePassedPartlyNotification.class, false, false, 3, true);

		notificationRepository.delete(buyerNotification);
		notificationRepository.delete(sellerNotification);
	}

	@Test
	@Transactional
	@Rollback(value = false)
	@WithMockUser(authorities = {"MASTER_USER"})
	public void _93_1_order_picking_up_from_office_logistics_notification_created_successful() {
		order = getOrder(order.getId());
		order.setDeliveryState(null);
		orderRepository.save(order);
		commitAndStartNewTransaction();

		setOrderState(order.getId(), OrderState.HOLD_COMPLETED);
		commitAndStartNewTransaction();

		order = getOrder(order.getId());
		LogisticStateDeliveryDTO test = new LogisticStateDeliveryDTO()
				.setWaybillId(order.getLastWaybill().getId())
				.setDeliveryState(DeliveryState.FROM_OFFICE_TO_BUYER)
				.setWaybillInnerDeliveryStatusId("-1");
		commonLogisticService.saveLogisticStateDeliveries(Collections.singletonList(test));
		commitAndStartNewTransaction();
		order = getOrder(order.getId());
		Notification notification = assertNotificationCreated(order.getId(), order.getBuyer().getId(), OrderPickingUpFromOfficeNotification.class, false, false, 3, false);
		notificationRepository.delete(notification);
	}

	@Test
	@Transactional
	@Rollback(value = false)
	public void _94_1_delivering_from_office_to_buyer_ourselves_notification_created_successful() {
		order = getOrder(order.getId());
		orderService.saveOrderDeliveryState(new SaveDeliveryStateDTO(order, DeliveryState.OURSELVES_FROM_OFFICE_TO_BUYER, false));
		commitAndStartNewTransaction();
		Notification notification = assertNotificationCreated(order.getId(), order.getBuyer().getId(), OrderDeliveringFromOfficeToBuyerNotification.class, false, false, 3, false);
		Notification notification2 = assertNotificationCreated(order.getId(), order.getSellerUser().getId(), SaleDeliveringFromOfficeToBuyerNotification.class, false, false, 3, true);

		notificationRepository.delete(notification);
		notificationRepository.delete(notification2);
	}

	@Test
	@Transactional
	@Rollback(value = false)
	@WithMockUser(authorities = {"MASTER_USER"})
	public void _94_2_delivering_from_office_to_buyer_logistics_notification_created_successful() {
		setOrderState(order.getId(), OrderState.HOLD_COMPLETED);
		//orderService.saveOrderDeliveryState(order, DeliveryState.JUST_CREATED_TO_BUYER);
		commitAndStartNewTransaction();

		order = getOrder(order.getId());
		LogisticStateDeliveryDTO test = new LogisticStateDeliveryDTO()
				.setWaybillId(order.getLastWaybill().getId())
				.setDeliveryState(DeliveryState.DELIVERY_TODAY_TO_BUYER)
				.setWaybillInnerDeliveryStatusId("-1");
		commonLogisticService.saveLogisticStateDeliveries(Collections.singletonList(test));
		commitAndStartNewTransaction();
		order = getOrder(order.getId());
		Notification notification = assertNotificationCreated(order.getId(), order.getBuyer().getId(), OrderDeliveringFromOfficeToBuyerNotification.class, false, false, 3, false);
		Notification notification2 = assertNotificationCreated(order.getId(), order.getSellerUser().getId(), SaleDeliveringFromOfficeToBuyerNotification.class, false, false, 3, true);

		notificationRepository.delete(notification);
		notificationRepository.delete(notification2);
	}

	private void _95_0_removeOrderReceipt(Order order, FiscalReceiptRequestType fiscalReceiptRequestType) {
		fiscalReceiptRequestService.deleteReceiptRequest(order, fiscalReceiptRequestType);
	}

	@Test
	@Transactional
	@Rollback(value = false)
	public void _95_1_delivered_from_office_to_buyer_ourselves_notification_created_successful() {
		User seller = order.getSellerUser();
		seller.setProStatusTime(null);
		userService.save(seller);
		commitAndStartNewTransaction();

		order = getOrder(order.getId());


		orderService.saveOrderDeliveryState(new SaveDeliveryStateDTO(order, null, false));
		orderService.saveOrderDeliveryState(new SaveDeliveryStateDTO(order, DeliveryState.DELIVERED_TO_BUYER, false));

		commitAndStartNewTransaction();

		Notification notification = assertNotificationCreated(order.getId(), order.getBuyer().getId(), getOrderDeliveredToBuyerNotificationType(), getOrderDeliveredToBuyerNotificationNeedsAction(), false, 3, false);
		//Заодно если требуется проверяем что уведомление прилинковано к DTO заказа
		if(getOrderDeliveredToBuyerNotificationShouldBeLinkedToOrder()){
			NotificationDTO notificationDTO = assertNotificationCreatedAndLinkedToOrderDTO(buyerClient, order.getId(), order.getBuyer().getId(), getOrderDeliveredToBuyerNotificationType(), getOrderDeliveredToBuyerNotificationNeedsAction(), false, 3, false, false);
		}
		//т.к. продавец частник это уведомление не должно создаваться
		assertNotificationNOTCreated(order.getId(), order.getSellerUser().getId(), OrderDeliveredToBuyerWaitingForAgentReportNotification.class, true);

		notificationRepository.delete(notification);
	}

	@Test
	@Transactional
	@Rollback(value = false)
	@WithMockUser(authorities = {"MASTER_USER"})
	public void _95_2_delivered_from_office_to_buyer_logistics_notification_created_successful() {
		order = getOrder(order.getId());
		order.setDeliveryState(null);
		orderRepository.save(order);
		commitAndStartNewTransaction();

		order = getOrder(order.getId());
		setOrderState(order.getId(), OrderState.HOLD_COMPLETED);
		orderService.saveOrderDeliveryState(new SaveDeliveryStateDTO(order, null, false));
		_95_0_removeOrderReceipt(order, FiscalReceiptRequestType.DELIVERY_PAYMENT);
		commitAndStartNewTransaction();

		order = getOrder(order.getId());
		LogisticStateDeliveryDTO test = new LogisticStateDeliveryDTO()
				.setWaybillId(order.getLastWaybill().getId())
				.setDeliveryState(DeliveryState.DELIVERED_TO_BUYER)
				.setWaybillInnerDeliveryStatusId("-1");
		commonLogisticService.saveLogisticStateDeliveries(Collections.singletonList(test));
		commitAndStartNewTransaction();
		order = getOrder(order.getId());
		Notification notification = assertNotificationCreated(order.getId(), order.getBuyer().getId(), getOrderDeliveredToBuyerNotificationType(), getOrderDeliveredToBuyerNotificationNeedsAction(), false, 3, false);
		//Заодно если требуется проверяем что уведомление прилинковано к DTO заказа
		if(getOrderDeliveredToBuyerNotificationShouldBeLinkedToOrder()){
			NotificationDTO notificationDTO = assertNotificationCreatedAndLinkedToOrderDTO(buyerClient, order.getId(), order.getBuyer().getId(), getOrderDeliveredToBuyerNotificationType(), getOrderDeliveredToBuyerNotificationNeedsAction(), false, 3, false, false);
		}
		notificationRepository.delete(notification);
		//т.к. продавец частник это уведомление не должно создаваться
		assertNotificationNOTCreated(order.getId(), order.getSellerUser().getId(), OrderDeliveredToBuyerWaitingForAgentReportNotification.class, true);
	}

	@Test
	@Transactional
	@Rollback(value = false)
	public void _95_3_delivered_from_office_to_buyer_ourselves_PRO_SELLER_notification_created_successful() {
		order = getOrder(order.getId());
		User seller = order.getSellerUser();
		seller.setProStatusTime(LocalDateTime.now());
		userService.save(seller);
		order.setDeliveryState(null);
		orderRepository.save(order);
		commitAndStartNewTransaction();
		order = getOrder(order.getId());

		orderService.saveOrderDeliveryState(new SaveDeliveryStateDTO(order, null, false));
		_95_0_removeOrderReceipt(order, FiscalReceiptRequestType.DELIVERY_PAYMENT);
		orderService.saveOrderDeliveryState(new SaveDeliveryStateDTO(order, DeliveryState.DELIVERED_TO_BUYER, false));
		commitAndStartNewTransaction();
		Notification notification = assertNotificationCreated(order.getId(), order.getBuyer().getId(), getOrderDeliveredToBuyerNotificationType(), getOrderDeliveredToBuyerNotificationNeedsAction(), false, 3, false);
		//Заодно если требуется проверяем что уведомление прилинковано к DTO заказа
		if(getOrderDeliveredToBuyerNotificationShouldBeLinkedToOrder()){
			NotificationDTO notificationDTO = assertNotificationCreatedAndLinkedToOrderDTO(buyerClient, order.getId(), order.getBuyer().getId(), getOrderDeliveredToBuyerNotificationType(), getOrderDeliveredToBuyerNotificationNeedsAction(), false, 3, false, false);
		}
		//уведомления для бутика
		Notification notification2 = assertNotificationCreated(order.getId(), order.getSellerUser().getId(), OrderDeliveredToBuyerWaitingForAgentReportNotification.class, false, false, 3, true);

		notificationRepository.delete(notification);
		notificationRepository.delete(notification2);
	}

	@Test
	@Transactional
	@Rollback(value = false)
	@WithMockUser(authorities = {"MASTER_USER"})
	public void _95_4_delivered_from_office_to_buyer_logistics_PRO_SELLER_notification_created_successful() {
		order = getOrder(order.getId());
		order.setDeliveryState(null);
		orderRepository.save(order);
		commitAndStartNewTransaction();

		order = getOrder(order.getId());
		setOrderState(order.getId(), OrderState.HOLD_COMPLETED);
		orderService.saveOrderDeliveryState(new SaveDeliveryStateDTO(order, null, false));
		_95_0_removeOrderReceipt(order, FiscalReceiptRequestType.DELIVERY_PAYMENT);
		commitAndStartNewTransaction();

		order = getOrder(order.getId());
		LogisticStateDeliveryDTO test = new LogisticStateDeliveryDTO()
				.setWaybillId(order.getLastWaybill().getId())
				.setDeliveryState(DeliveryState.DELIVERED_TO_BUYER)
				.setWaybillInnerDeliveryStatusId("-1");
		commonLogisticService.saveLogisticStateDeliveries(Collections.singletonList(test));
		commitAndStartNewTransaction();

		order = getOrder(order.getId());
		Notification notification = assertNotificationCreated(order.getId(), order.getBuyer().getId(), getOrderDeliveredToBuyerNotificationType(), getOrderDeliveredToBuyerNotificationNeedsAction(), false, 3, false);
		//Заодно если требуется проверяем что уведомление прилинковано к DTO заказа
		if(getOrderDeliveredToBuyerNotificationShouldBeLinkedToOrder()){
			NotificationDTO notificationDTO = assertNotificationCreatedAndLinkedToOrderDTO(buyerClient, order.getId(), order.getBuyer().getId(), getOrderDeliveredToBuyerNotificationType(), getOrderDeliveredToBuyerNotificationNeedsAction(), false, 3, false, false);
		}
		//уведомления для бутика
		Notification notification2 = assertNotificationCreated(order.getId(), order.getSellerUser().getId(), OrderDeliveredToBuyerWaitingForAgentReportNotification.class, false, false, 3, true);

		notificationRepository.delete(notification);
		notificationRepository.delete(notification2);

		userService.save(order.getSellerUser().setProStatusTime(null));
	}

	////////////////////// Возвраты


	//Попытка оформить возврат к чужому и несуществующему заказу
	@Test
	@Transactional
	@Rollback(value = false)
	public void _97_1_0_createReturnInfoWrongOrder_failed() {
		//Находим чужой заказ в статусе COMPLETED
		SqlRowSet sqlRowSet = jdbcTemplate.queryForRowSet("SELECT id FROM public.order WHERE state = 'COMPLETED' AND buyer_id != " + buyerId + " ORDER BY id DESC LIMIT 1;");
		assertTrue(sqlRowSet.next());
		long someonesOrderId = sqlRowSet.getLong("id");

		ReturnInfoDTO request = createFullReturnCorrectRequest(someonesOrderId);

		//Сначала чужой заказ
		ResponseEntity<String> responseEntity = buyerClient.request(getReturnInfoServiceUrl(), null, HttpMethod.POST, request, String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("У вас нет прав на создание возврата по данному заказу"));

		//Несуществующий заказ
		request.setOrderId(0L);
		responseEntity = buyerClient.request(getReturnInfoServiceUrl(), null, HttpMethod.POST, request, String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Заказ не найден"));
	}

	//Попытка оформить возврат к своему заказу в некорректном статусе
	@Test
	@Transactional
	@Rollback(value = false)
	public void _97_1_1_createReturnInfoWrongOrderState_failed() {
		ReturnInfoDTO request = createFullReturnCorrectRequest(order.getId());
		commitTransaction();

		//Все стейты кроме COMPLETED, finishedForBuyer = false
		for (OrderState orderState : OrderState.values()) {
			startNewTransaction();
			order = getOrder(order.getId());
			order.setState(orderState);
			//Даже для COMPLETED заказа с флагом isFinishedForBuyer = false нельзя оформить возврат
			order.setFinishedForBuyer(orderState != OrderState.COMPLETED);
			orderService.saveOrder(order);
			commitTransaction();

			ResponseEntity<String> responseEntity = buyerClient.request(getReturnInfoServiceUrl(), null, HttpMethod.POST, request, String.class, true);
			assertTrue(responseEntity.getStatusCode().is4xxClientError());
			//Для удаленных заказов имеем исключение, заказ не найден.
			String expectedMessage = orderState == OrderState.DELETED ? "Заказ не найден" : "Статус заказа не позволяет оформить возврат";
			assertTrue(responseEntity.getBody().contains(expectedMessage));
		}

		//Возвращаем статус заказа к допустимому для возврата
		startNewTransaction();
		order = getOrder(order.getId());
		order.setState(OrderState.COMPLETED);
		order.setFinishedForBuyer(true);
		orderService.saveOrder(order);
		commitTransaction();

	}

	//Попытка оформить возврат к своему заказу с некорректным фото паспорта
	@Test
	@Transactional
	@Rollback(value = false)
	public void _97_1_2_createReturnInfoNoPassportPhoto_failed() {
		ReturnInfoDTO request = createFullReturnCorrectRequest(order.getId());
		commitAndStartNewTransaction();

		ResponseEntity<String> responseEntity;

		//Фото паспорта пустое
		request.setPassportPhotoId(null);
		responseEntity = buyerClient.request(getReturnInfoServiceUrl(), null, HttpMethod.POST, request, String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Не передано фото паспорта"));

		//Фото паспорта не найдено
		request.setPassportPhotoId(0L);
		responseEntity = buyerClient.request(getReturnInfoServiceUrl(), null, HttpMethod.POST, request, String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Файл не найден"));

		//Фото паспорта не чужое
		request.setPassportPhotoId(getPassportPhotoFileId(getSeller()));

		//Коммитим, чтобы файл был виден в базе
		commitAndStartNewTransaction();

		responseEntity = buyerClient.request(getReturnInfoServiceUrl(), null, HttpMethod.POST, request, String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Вы не имеете прав на данный файл"));
	}

	//Попытка оформить возврат к своему заказу с некорректной адресной точкой
	@Test
	@Transactional
	@Rollback(value = false)
	public void _97_1_3_createReturnInfoWrongAddressEndpoint_failed() {
		ReturnInfoDTO request = createFullReturnCorrectRequest(order.getId());
		commitTransaction();

		ResponseEntity<String> responseEntity;

		//Адресная точка не установлена
		request.setBuyerAddressEndpointId(null);
		responseEntity = buyerClient.request(getReturnInfoServiceUrl(), null, HttpMethod.POST, request, String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Не передана адресная точка"));

		//Адресная точка не существует
		request.setBuyerAddressEndpointId(0L);
		responseEntity = buyerClient.request(getReturnInfoServiceUrl(), null, HttpMethod.POST, request, String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Не найдена адресная точка"));

		//Находим чужую адресную точку
		SqlRowSet sqlRowSet = jdbcTemplate.queryForRowSet("SELECT id FROM address_endpoint WHERE user_id != " + buyerId + " ORDER BY id DESC LIMIT 1;");
		assertTrue(sqlRowSet.next());
		long someonesAddressEndpointId = sqlRowSet.getLong("id");


		request.setBuyerAddressEndpointId(someonesAddressEndpointId);
		responseEntity = buyerClient.request(getReturnInfoServiceUrl(), null, HttpMethod.POST, request, String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Передана некорректная адресная точка"));
	}

	//Попытка оформить возврат к своему заказу с некорректным контрагентом
	@Test
	@Transactional
	@Rollback(value = false)
	public void _97_1_4_createReturnInfoWrongCounterparty_failed() {
		ReturnInfoDTO request = createFullReturnCorrectRequest(order.getId());
		commitTransaction();

		ResponseEntity<String> responseEntity;

		//Контрагент не установлен
		request.setBuyerCounterpartyId(null);
		responseEntity = buyerClient.request(getReturnInfoServiceUrl(), null, HttpMethod.POST, request, String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Не передан контрагент"));

		//Контрагент не существует
		request.setBuyerCounterpartyId(0L);
		responseEntity = buyerClient.request(getReturnInfoServiceUrl(), null, HttpMethod.POST, request, String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Не найден контрагент"));

		//Находим чужого контрагента
		SqlRowSet sqlRowSet = jdbcTemplate.queryForRowSet("SELECT id FROM counterparty WHERE user_id != " + buyerId + " ORDER BY id DESC LIMIT 1;");
		assertTrue(sqlRowSet.next());
		long someonesCounterpartyId = sqlRowSet.getLong("id");


		request.setBuyerCounterpartyId(someonesCounterpartyId);
		responseEntity = buyerClient.request(getReturnInfoServiceUrl(), null, HttpMethod.POST, request, String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Передан некорректный контрагент"));
	}

	//Попытка оформить возврат к своему заказу без позиций или с чужими позициями
	@Test
	@Transactional
	@Rollback(value = false)
	public void _97_1_5_createReturnInfoWrongOrderPositions_failed() {
		ReturnInfoDTO request = createFullReturnCorrectRequest(order.getId());
		commitAndStartNewTransaction();

		//пустой список вместо списка позиций
		request.setReturnPositions(Collections.emptyList());
		ResponseEntity<String> responseEntity = buyerClient.request(getReturnInfoServiceUrl(), null, HttpMethod.POST, request, String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Не переданы позиции возврата"));

		//Левые позиции (из другого заказа)
		//Находим чужой заказ в статусе COMPLETED
		SqlRowSet sqlRowSet = jdbcTemplate.queryForRowSet("SELECT o.id FROM public.order o INNER JOIN public.order_position op on o.id = op.order_id WHERE o.state = 'COMPLETED' AND buyer_id != ? ORDER BY o.id DESC LIMIT 1", buyerId);
		assertTrue(sqlRowSet.next());
		long someonesOrderId = sqlRowSet.getLong("id");
		request = createFullReturnCorrectRequest(someonesOrderId);
		//По сути, запрос по другому заказу, но id заказа наш
		request.setOrderId(order.getId());
		responseEntity = buyerClient.request(getReturnInfoServiceUrl(), null, HttpMethod.POST, request, String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Позиция не найдена в заказе"));
	}

	//Попытка оформить возврат к своему заказу с некорректными причинами возврата
	@Test
	@Transactional
	@Rollback(value = false)
	public void _97_1_6_createReturnInfoWrongReturnReasons_failed() {
		ReturnInfoDTO request = createFullReturnCorrectRequest(order.getId());
		commitTransaction();

		ResponseEntity<String> responseEntity = null;

		//Пустой список причин (null)
		request.getReturnPositions().get(0).setReturnReasonIds(null);
		responseEntity = buyerClient.request(getReturnInfoServiceUrl(), null, HttpMethod.POST, request, String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Не указана причина возврата"));

		//Пустой список причин ([])
		request.getReturnPositions().get(0).setReturnReasonIds(Collections.emptyList());
		responseEntity = buyerClient.request(getReturnInfoServiceUrl(), null, HttpMethod.POST, request, String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Не указана причина возврата"));

		//Несуществующая причина
		long wrongReturnReasonId = 0L;
		request.getReturnPositions().get(0).setReturnReasonIds(Arrays.asList(wrongReturnReasonId));
		responseEntity = buyerClient.request(getReturnInfoServiceUrl(), null, HttpMethod.POST, request, String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Причина возврата не найдена: " + wrongReturnReasonId));
	}

	//Попытка оформить возврат с некорректным фото бирки
	@Test
	@Transactional
	@Rollback(value = false)
	public void _97_1_7_createReturnInfoWrongTagPhoto_failed() {
		ReturnInfoDTO request = createFullReturnCorrectRequest(order.getId());
		commitAndStartNewTransaction();

		ResponseEntity<String> responseEntity = null;

		//Не передан ID фото бирки
		request.getReturnPositions().get(0).setTagPhotoId(null);
		responseEntity = buyerClient.request(getReturnInfoServiceUrl(), null, HttpMethod.POST, request, String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Не передано фото бирки"));

		//Передан ID несуществующего фото бирки
		request.getReturnPositions().get(0).setTagPhotoId(0L);
		responseEntity = buyerClient.request(getReturnInfoServiceUrl(), null, HttpMethod.POST, request, String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Файл не найден"));

		//Передан ID чужого файла вместо своего фото бирки
		request.getReturnPositions().get(0).setTagPhotoId(getTagPhotoFileId(getSeller()));

		//Коммитим, чтобы файл был виден в базе
		commitAndStartNewTransaction();

		responseEntity = buyerClient.request(getReturnInfoServiceUrl(), null, HttpMethod.POST, request, String.class, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		assertTrue(responseEntity.getBody().contains("Вы не имеете прав на данный файл"));
	}

	//Успешное оформление возврата
	@Test
	@Transactional
	@Rollback(value = false)
	public void _97_2_1_createReturnInfo_success() {
		ReturnInfoDTO request = createFullReturnCorrectRequest(order.getId());
		commitTransaction();

		ResponseEntity<Api2Response<Long>> responseEntity = buyerClient.request(getReturnInfoServiceUrl(), null, HttpMethod.POST, request, new ParameterizedTypeReference<Api2Response<Long>>() {}, true);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody());

		returnInfoId = responseEntity.getBody().getData();
		assertNotNull(returnInfoId);
	}

	//Покупатель и продавец видит этот возврат в своих списках в зависимости от роли
	//Покупатель видит покупательские возвраты, продавец - продавцовые возвраты.
	//Но не наоборот
	@Test
	@Transactional
	@Rollback(value = false)
	public void _97_2_2_usersSeeReturnInfoInList_success() {
		//Покупатель видит возврат в покупательском списке
		assertTrue(userSeesReturnInfoInHisList(buyerClient, true, returnInfoId));

		//Продавец видит возврат в продавцовом списке
		assertTrue(userSeesReturnInfoInHisList(sellerClient, false, returnInfoId));

		//Покупатель не видит возврат в продавцовом списке
		assertFalse(userSeesReturnInfoInHisList(buyerClient, false, returnInfoId));

		//Продавец не видит возврат в покупательном списке
		assertFalse(userSeesReturnInfoInHisList(sellerClient, true, returnInfoId));

	}

	//покупатель видит детальный возврат и в нем есть все необходимое, активен первый шаг статуса
	@Test
	@Transactional
	@Rollback(value = false)
	public void _97_2_3_buyerSeesDetailedReturnInfo_success() {
		ReturnInfoDTO returnInfoDTO = getDetailedReturnInfoFromUsersPoint(buyerClient, returnInfoId);
		ReturnStepChain stepChain = returnInfoDTO.getReturnStepChain();
		assertNotNull(stepChain);
		assertNotNull(stepChain.getSteps());
		assertEquals(3, stepChain.getSteps().size());

		ReturnStepDTO step1 = stepChain.getSteps().get(0);
		assertEquals("Ожидает подтверждения", step1.getTitle());
		assertSame(WAITING, step1.getType());

		for(int i = 1; i < stepChain.getSteps().size(); i++){
			ReturnStepDTO otherStep = stepChain.getSteps().get(i);
			assertSame(DISABLED, otherStep.getType());
		}
	}

	/**
	 * Проверка работы метода, возвращающего завершенные заказы продавца
	 */
	@Test
	@Transactional
	@Rollback(value = false)
	public void _98_getSellersFinishedOrders() {
		// пока только один завершенный заказ
		assertFinishedSellerOrderResult(null, 1);

		// переводим все заказы в рамках теста в завершенные
		// + по флоу после оформления заказа у них должен проставиться продавец
		cleanUpOrdersIDs.stream().map(orderService::getOrder)
						.peek(order -> order.setFinishedForSeller(true))
						.peek(order -> order.setSeller(getSeller()))
						.forEach(orderRepository::saveAndFlush);

		commitAndStartNewTransaction();

		assertFinishedSellerOrderResult(null, cleanUpOrdersIDs.size());


		// создаем новый заказ с другими товарами
		List<Product> otherProducts = createOtherProducts();
		Order otherOrder = createOrder(otherProducts);
		otherOrder.setFinishedForSeller(true);
		otherOrder.setSeller(getSeller());
		otherOrder.setState(OrderState.MONEY_TRANSFERRED);
		orderRepository.save(otherOrder);

		commitAndStartNewTransaction();

		// тянем заказы по артикулу бутика
		OrderRequest orderRequest = new OrderRequest();
		orderRequest.setStoreCodeContains("oreco");

		// у одного заказа есть продукт с подходящим артикулом бутика
		assertFinishedSellerOrderResult(orderRequest, 1);

		orderRequest = new OrderRequest();
		orderRequest.setStoreCodeContains("StoreCode1");

		// у одного заказа есть продукт с подходящим артикулом бутика
		assertFinishedSellerOrderResult(orderRequest, 1);


		orderRequest = new OrderRequest();
		orderRequest.setStoreCodeContains("StoreCode11111");

		// не найден такой артикул, поэтому пусто
		assertFinishedSellerOrderResult(orderRequest, 0);


		orderRequest = new OrderRequest();
		orderRequest.setOrderId(otherOrder.getId());

		// нашелся заказ по идентификатору
		assertFinishedSellerOrderResult(orderRequest, 1);

		orderRequest = new OrderRequest();
		orderRequest.setOrderId(otherOrder.getId() + 1);

		// а такой уже не нашелся
		assertFinishedSellerOrderResult(orderRequest, 0);
	}

	/**
	 * Запрос завершенных заказов продавца и проверка ответа
	 *
	 * @param request параметры запроса
	 * @param expectedSize ожидаемое количество результатов
	 */
	private void assertFinishedSellerOrderResult(OrderRequest request, int expectedSize) {
		Map<String, String> paramsMap = new HashMap<>();
		if (request != null) {
			if (request.getOrderId() != null) {
				paramsMap.put("orderId", request.getOrderId().toString());
			}
			if (!Strings.isNullOrEmpty(request.getStoreCodeContains())) {
				paramsMap.put("storeCodeContains", request.getStoreCodeContains());
			}
		}
		ResponseEntity<Api2Response<List<OrderDTO>>> responseEntity = sellerClient.request(getSellerOrdersFinishedUrl(), paramsMap, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<OrderDTO>>>() {
		}, false);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody());

		if (expectedSize == 0) {
			assertNull(responseEntity.getBody().getData());
		} else {
			assertNotNull(responseEntity.getBody().getData());
			assertEquals(expectedSize, responseEntity.getBody().getData().size());
		}
	}

	/**
	 * Получение доп товаров для тестирования завершенных заказов
	 */
	private List<Product> createOtherProducts() {
		List<Product> products = productRepository.findProductsBySellerIdAndProductState(realSellerId, ProductState.PUBLISHED);
		List<Product> newProducts = new ArrayList<>();
		for (int i = 0; i < 2; i++) {
			Product product = products.get(i);
			product.setSeller(getSeller());
			product.setStoreCode("StoreCode" + i);
			productRepository.saveAndFlush(product);
			product.getProductItems().forEach(pi -> {
				pi.setHidden(false);
				pi.setCount(10);
				pi.setDeleteTime(null);
				productItemRepository.saveAndFlush(pi);
			});
			productsForOrders.add(product);
			newProducts.add(product);
		}

		return newProducts;
	}

	@Test
	@Transactional
	@Rollback(value = false)
	public void _99_cleanup() {
		if (productsForOrders != null) {
			for (Product product : productsForOrders) {
				product.setSeller(userService.getUserById(realSellerId).orElse(null));
				product.setStoreCode(null);
				productRepository.saveAndFlush(product);
			}
		}
		orderStateChangeRepository.deleteAll();
		for (Long cleanOrderId: cleanUpOrdersIDs) {
			Order cleanOrder = orderRepository.getOne(cleanOrderId);
			List<AdminAlert> alerts = adminAlertRepository.findAllAlertsByObjectId(cleanOrder.getId(), "AdminOrderAlert");
			adminAlertRepository.deleteAll(alerts);
			List<OrderWaybills> orderWaybills = orderWaybillsRepository.findAllByOrder(order);
			orderWaybillsRepository.deleteAll(orderWaybills);
			orderRepository.delete(order);
		}

		if (pickupAddressEndpoint != null) addressEndpointRepository.delete(pickupAddressEndpoint);
		if (deliveryAddressEndpoint != null) addressEndpointRepository.delete(deliveryAddressEndpoint);
		if (sellerCounterparty != null) counterpartyRepository.delete(sellerCounterparty);
		if (buyerCounterparty != null) counterpartyRepository.delete(buyerCounterparty);

		if (pickupAddress != null) addressRepository.delete(pickupAddress);
		if (deliveryAddress != null) addressRepository.delete(deliveryAddress);
	}

	//Пользователь видит детальную информацию о возврате
	private ReturnInfoDTO getDetailedReturnInfoFromUsersPoint(ApiV2Client client, long returnInfoId) {
		ResponseEntity<Api2Response<ReturnInfoDTO>> responseEntity = client.request(getReturnInfoDetailedUrl(returnInfoId), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<ReturnInfoDTO>>() {}, true);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody());
		assertReturnInfoDTOValid(responseEntity.getBody().getData());
		return responseEntity.getBody().getData();
	}

	//DTO возврата содержит минимальный набр полей
	private void assertReturnInfoDTOValid(ReturnInfoDTO returnInfoDTO) {
		assertNotNull(returnInfoDTO);
		assertNotNull(returnInfoDTO.getOrderId());
		assertNotNull(returnInfoDTO.getReturnState());
		assertNotNull(returnInfoDTO.getReturnState().getIcon());
		assertNotNull(returnInfoDTO.getReturnPositions());
		assertFalse(returnInfoDTO.getReturnPositions().isEmpty());
	}

	//Видит ли пользователь возврат в своем списке возвратов как продавец или как покупатель
	private boolean userSeesReturnInfoInHisList(ApiV2Client client, boolean isBuyer, long returnInfoId) {
		String pageUrl = isBuyer ? getReturnInfoBuyerPageUrl() : getReturnInfoSellerPageUrl();

		ResponseEntity<Api2Response<Page<ReturnInfoDTO>>> responseEntity = client.request(pageUrl, null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<Page<ReturnInfoDTO>>>() {}, true);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody());
		Page<ReturnInfoDTO> returnsPage = responseEntity.getBody().getData();
		assertNotNull(returnsPage);
		for(ReturnInfoDTO returnInfo : returnsPage.getItems()){
			//Заодно проверим DTO возвратов на наличие минимальных данных
			assertReturnInfoDTOValid(returnInfo);
			if(returnInfo.getId().equals(returnInfoId)) return true;
		}
		return false;
	}


	private ReturnInfoDTO createFullReturnCorrectRequest(long orderId){
		Order order = orderRepository.getOne(orderId);
		User buyer = getBuyer();
		ReturnInfoDTO result = new ReturnInfoDTO().setOrderId(orderId)
			.setBuyerAddressEndpointId(getAddressEndpointId(buyer))
			.setBuyerCounterpartyId(getCounterpartyId(buyer))
			.setReturnPositions(getOrderReturnPositions(order, getCorrectReturnReasonIds()))
			.setPassportPhotoId(getPassportPhotoFileId(buyer));
		return result;
	}

	//Возвращает ID файла с фото паспорта
	private Long getPassportPhotoFileId(User user){
		return getUserfileId(user, "Фото паспорта", "img/return/passport-photo.jpg");
	}

	//Возвращает ID файла с фото бирки
	private Long getTagPhotoFileId(User user){
		return getUserfileId(user, "Фото фирки", "img/return/tag-photo.jpeg");
	}

	//Возвращает ID пользовательского файла по заголовку. При необходимости, загружает новый файл.
	private Long getUserfileId(User user, String title, String pathToUpload){
		org.springframework.data.domain.Page<Userfile> userfilesPage = userfileRepository.findAllByUserId(user.getId(), PageRequest.of(0, 10));
		if(!userfilesPage.isEmpty()){
			//Ищем файл по заголовку
			for(Userfile userfile : userfilesPage.getContent()){
				if(userfile.getTitle().equals(title)) return userfile.getId();
			}
		}
		//Загружаем фото паспорта
		return accountService.uploadUserfile(user, TestUtils.getFileAsMultipartFile(pathToUpload), title).getId();
	}

	private List<ReturnPositionDTO> getOrderReturnPositions(Order order, List<Long> returnReasonIds){
		return order.getEffectiveOrderPositions().stream().map(op ->
				new ReturnPositionDTO()
					.setOrderPositionId(op.getId())
					.setReturnReasonIds(returnReasonIds)
					.setReasonDescription("Причина возврата по позиции #" + op.getId())
					.setTagPhotoId(getTagPhotoFileId(order.getBuyer()))
		).collect(Collectors.toList());
	}

	private List<Long> getCorrectReturnReasonIds(){
		return Arrays.asList(1L, 2L);
	}

	private long getAddressEndpointId(User user){
		return user.getAddressEndpoints().get(0).getId();
	}

	private long getCounterpartyId(User user){
		return user.getCounterparties().get(0).getId();
	}


	//Уведомление создано и выдается вместе с DTO заказа (например просьба подтвердить заказ)
	private NotificationDTO assertNotificationCreatedAndLinkedToOrderDTO(ApiV2Client client, Long orderId, Long userId, Class<? extends OrderNotification> clazz, boolean needAction, boolean actionCompleted, int minutesPeriod, boolean forSeller, boolean withAuthorizeParams){
		OrderNotification notification = assertNotificationCreated(orderId, userId, clazz, needAction, actionCompleted, minutesPeriod, forSeller);
		OrderDTO orderDTO = loadOrderSuccessfull(orderId, client, withAuthorizeParams);
		NotificationDTO notificationDTO = orderDTO.getLinkedNotification();
		assertNotNull(notificationDTO);
		assertEquals(notification.getId(), notificationDTO.getId());
		assertEquals(notification.getDtype(), notificationDTO.getType());
		return notificationDTO;
	}

	private OrderNotification assertNotificationCreated(Long orderId, Long userId, Class<? extends OrderNotification> clazz, boolean needAction, boolean actionCompleted, int minutesPeriod, boolean forSeller){
		Order ord = orderRepository.getOne(orderId);
		TestUtils.sleep(3);//Создание уведомления занимает некоторое время
		List<Notification> notifications = notificationService.getRawNotifications(100, forSeller? ord.getSellerUser() : ord.getBuyer());
		OrderNotification notification = null;
		for(Notification n : notifications){
			if(n.getClass() != clazz) continue;

			OrderNotification on = (OrderNotification) n;
			Order o = on.getOrder();
			User u = n.getUser();
			User targetUser = n.getTargetUser().orElse(null);
			if (o == null || targetUser == null) continue;
			if (o.getId().equals(ord.getId()) && u.getId().equals(userId) && targetUser.getId().equals(userId)) {
				//found!
				notification = on;
				break;
			}
		}
		assertNotNull(notification);
		//Если действие выполнено, то уведомление становится прочитанным
		if(needAction && actionCompleted)
			assertTrue(notification.isRead());
		else
			assertFalse(notification.isRead());
		assertTrue(needAction == notification.isNeedAction());
		assertTrue(actionCompleted == notification.isActionCompleted());
		assertTrue(ZonedDateTime.now().minusMinutes(minutesPeriod).isBefore(notification.getCreateTime()));
		return notification;
	}

	private void assertNotificationNOTCreated(Long orderId, Long userId, Class<? extends OrderNotification> clazz, boolean forSeller){
		Order ord = orderRepository.getOne(orderId);
		TestUtils.sleep(3);//Создание уведомления занимает некоторое время
		List<Notification> notifications = notificationService.getRawNotifications(100, forSeller ? ord.getSellerUser() : ord.getBuyer());
		OrderNotification notification = null;
		for(Notification n : notifications){
			if(n.getClass() != clazz) continue;

			OrderNotification on = (OrderNotification) n;
			Order o = on.getOrder();
			User u = n.getUser();
			User targetUser = n.getTargetUser().orElse(null);
			if (o == null || targetUser == null) continue;
			if (o.getId().equals(ord.getId()) && u.getId().equals(userId) && targetUser.getId().equals(userId)) {
				//found!
				notification = on;
				break;
			}
		}
		assertNull(notification);

	}

	private static void assertOrderIsValid(OrderDTO order){
		assertNotNull(order.getId());
		assertNotNull(order.getState());
		if(OrderState.getNotPayedOrderStates().contains(order.getState())){ //Заказ еще не оплачен
			assertNull(order.getOrderStepChain());
		}
		else{ //Заказ оплачен
			assertNotNull(order.getOrderStepChain());
		}
	}

	private Class<? extends OrderNotification> getOrderDeliveredToBuyerNotificationType(){
		return requestDeliveryConfirmationFromBuyer ? OrderDeliveredToBuyerRequestConfirmationNotification.class : OrderDeliveredToBuyerNotification.class;
	}

	private boolean getOrderDeliveredToBuyerNotificationNeedsAction(){
		return requestDeliveryConfirmationFromBuyer;
	}

	private boolean getOrderDeliveredToBuyerNotificationShouldBeLinkedToOrder(){
		return requestDeliveryConfirmationFromBuyer;
	}
}
