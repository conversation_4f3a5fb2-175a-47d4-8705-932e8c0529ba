package ru.oskelly.tests.pr.suite3.presentation.api.v2;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.pr.common.bonuses.BonusesServiceTestConfiguration;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.component.TestApiConfiguration;
import su.reddot.domain.dao.product.ProductItemRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.size.Size;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.order.GroupedCart;
import su.reddot.domain.service.dto.order.OrderPositionDTO;
import su.reddot.domain.service.loyalty.LoyaltyService;
import su.reddot.domain.service.loyalty.LoyaltyServiceProperties;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.mindbox.MindboxApplicationTriggerEventSender;
import su.reddot.infrastructure.util.CallInTransaction;
import su.reddot.presentation.api.v2.Api2Response;

import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@Slf4j
@SpringBootTest(classes = {OskellyApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT, properties = "app.integration.mindbox.triggers.enabled=true")
@ContextConfiguration(classes = {BonusesServiceTestConfiguration.class})
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_03)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class CartControllerV2ExclusiveLotsTest extends AbstractSpringTest {

    @Value("${test.api.user-email}")
    private String email;
    @Value("${test.api.user-password}")
    private String password;
    @Value("${test.api.user-id}")
    private Long userId;

    private Long sellerId = 23L;

    @Autowired
    private TestApiConfiguration testApiConfiguration;
    @Autowired
    private ProductRepository productRepository;
    @Autowired
    private ProductItemRepository productItemRepository;
    @Autowired
    private UserService userService;
    @Autowired
    private CartTestSupport cartTestSupport;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private MindboxApplicationTriggerEventSender mindboxApplicationTriggerEventSender;

    @Autowired
    private CallInTransaction callInTransaction;

    @MockBean
    private LoyaltyService loyaltyServiceMock;

    @MockBean
    private LoyaltyServiceProperties loyaltyServicePropertiesMock;

    static ApiV2Client apiV2Client;

    //id продавца -> список id товаров
    static Map<Long, List<Long>> productsToBuy = new HashMap<>();

    private String getServiceUrl() {
        return testApiConfiguration.getServerUrl() + "/api/v2/cart";
    }

    private String getGroupedCartUrl() {
        return getServiceUrl();
    }

    private User getUser() {
        return userService.getUserById(userId).orElse(null);
    }

    private ResponseEntity<String> getGroupedCartResponse() {
        return apiV2Client.request(getGroupedCartUrl(), null, HttpMethod.GET, null, String.class, false);
    }

    @SneakyThrows
    private GroupedCart getGroupedCartSuccessful() {
        ResponseEntity<String> response = getGroupedCartResponse();
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        Api2Response<GroupedCart> body = objectMapper.readValue(
                response.getBody(),
                new TypeReference<Api2Response<GroupedCart>>() {});
        return body.getData();
    }

    private List<Product> getProductsToBuy(Long sellerId) {
        return productRepository.findAllById(productsToBuy.get(sellerId));
    }

    private List<Product> getProductsToBuy() {
        return getProductsToBuy(sellerId);
    }

    @BeforeAll
    public void beforeAll() {

        apiV2Client = new ApiV2Client(email, password);

        cartTestSupport.setUserId(userId);
        cartTestSupport.setApiV2Client(apiV2Client);

        cleanup();

        List<Product> products = productRepository.findProductsBySellerIdAndProductState(
                sellerId,
                ProductState.PUBLISHED).stream().limit(10).collect(Collectors.toList());
        for (int i = 0; i < 10; i++) {
            Product product = products.get(i);
            List<ProductItem> productItems = productItemRepository.findAllByProduct(product);
            for (ProductItem productItem1 : productItems) {
                productItem1.setCount(100);
                productItem1.setHidden(false);
                productItem1.setDeleteTime(null);
            }
        }

        productsToBuy.put(
                sellerId,
                products.stream().map(Product::getId).collect(Collectors.toList()));
    }

    @AfterAll
    public void afterAll() {
        cleanup();
    }

    @Test
    public void testGetGroupedCartWithExclusiveLot() {

        when(loyaltyServicePropertiesMock.isExclusiveLotsPrivilegeEnabled()).thenReturn(true);

        cartTestSupport.cleanup();

        AtomicReference<Product> notExclusiveLotHolder = new AtomicReference<>();
        AtomicReference<Size> notExclusiveSizeHolder = new AtomicReference<>();
        callInTransaction.runInAnyTransaction(() -> {
            Product p = getProductsToBuy().get(0);
            unsetProductExclusiveLot(p);
            notExclusiveLotHolder.set(p);
            notExclusiveSizeHolder.set(p.getAvailableProductItems().get(0).getSize());
        });

        cartTestSupport.addToCartSuccessful(notExclusiveLotHolder.get().getId(), notExclusiveSizeHolder.get().getId(), 1, true);

        AtomicReference<Product> exclusiveLotHolder = new AtomicReference<>();
        AtomicReference<Size> exclusiveSizeHolder = new AtomicReference<>();
        callInTransaction.runInAnyTransaction(() -> {
            Product p = getProductsToBuy().get(1);
            setProductExclusiveLot(p);
            exclusiveLotHolder.set(p);
            exclusiveSizeHolder.set(p.getAvailableProductItems().get(0).getSize());
        });

        cartTestSupport.addToCartSuccessful(exclusiveLotHolder.get().getId(), exclusiveSizeHolder.get().getId(), 1, true);

        // неблэк, привилегия включена

        when(loyaltyServiceMock.hasUserBlackLoyaltyStatus(anyLong())).thenReturn(false);

        GroupedCart groupedCart = getGroupedCartSuccessful();

        OrderPositionDTO notExclusiveOrderPos = groupedCart.getGroups().stream()
                .flatMap(o -> o.getItems().stream())
                .filter(o -> Objects.equals(o.getProductId(), notExclusiveLotHolder.get().getId()))
                .findAny()
                .orElse(null);
        assertNotNull(notExclusiveOrderPos);
        assertTrue(notExclusiveOrderPos.isAvailable());

        OrderPositionDTO exclusiveOrderPos = groupedCart.getGroups().stream()
                .flatMap(o -> o.getItems().stream())
                .filter(o -> Objects.equals(o.getProductId(), exclusiveLotHolder.get().getId()))
                .findAny()
                .orElse(null);
        assertNotNull(exclusiveOrderPos);
        assertFalse(exclusiveOrderPos.isAvailable());

        // блэк, привилегия включена

        when(loyaltyServiceMock.hasUserBlackLoyaltyStatus(anyLong())).thenReturn(true);

        groupedCart = getGroupedCartSuccessful();

        notExclusiveOrderPos = groupedCart.getGroups().stream()
                .flatMap(o -> o.getItems().stream())
                .filter(o -> Objects.equals(o.getProductId(), notExclusiveLotHolder.get().getId()))
                .findAny()
                .orElse(null);
        assertNotNull(notExclusiveOrderPos);
        assertTrue(notExclusiveOrderPos.isAvailable());

        exclusiveOrderPos = groupedCart.getGroups().stream()
                .flatMap(o -> o.getItems().stream())
                .filter(o -> Objects.equals(o.getProductId(), exclusiveLotHolder.get().getId()))
                .findAny()
                .orElse(null);
        assertNotNull(exclusiveOrderPos);
        assertTrue(exclusiveOrderPos.isAvailable());

        // неблэк, привилегия отлючена

        when(loyaltyServicePropertiesMock.isExclusiveLotsPrivilegeEnabled()).thenReturn(false);

        when(loyaltyServiceMock.hasUserBlackLoyaltyStatus(anyLong())).thenReturn(false);

        groupedCart = getGroupedCartSuccessful();

        notExclusiveOrderPos = groupedCart.getGroups().stream()
                .flatMap(o -> o.getItems().stream())
                .filter(o -> Objects.equals(o.getProductId(), notExclusiveLotHolder.get().getId()))
                .findAny()
                .orElse(null);
        assertNotNull(notExclusiveOrderPos);
        assertTrue(notExclusiveOrderPos.isAvailable());

        exclusiveOrderPos = groupedCart.getGroups().stream()
                .flatMap(o -> o.getItems().stream())
                .filter(o -> Objects.equals(o.getProductId(), exclusiveLotHolder.get().getId()))
                .findAny()
                .orElse(null);
        assertNotNull(exclusiveOrderPos);
        assertTrue(exclusiveOrderPos.isAvailable());

        // блэк, привилегия отключена

        when(loyaltyServiceMock.hasUserBlackLoyaltyStatus(anyLong())).thenReturn(true);

        groupedCart = getGroupedCartSuccessful();

        notExclusiveOrderPos = groupedCart.getGroups().stream()
                .flatMap(o -> o.getItems().stream())
                .filter(o -> Objects.equals(o.getProductId(), notExclusiveLotHolder.get().getId()))
                .findAny()
                .orElse(null);
        assertNotNull(notExclusiveOrderPos);
        assertTrue(notExclusiveOrderPos.isAvailable());

        exclusiveOrderPos = groupedCart.getGroups().stream()
                .flatMap(o -> o.getItems().stream())
                .filter(o -> Objects.equals(o.getProductId(), exclusiveLotHolder.get().getId()))
                .findAny()
                .orElse(null);
        assertNotNull(exclusiveOrderPos);
        assertTrue(exclusiveOrderPos.isAvailable());
    }

    private void setProductExclusiveLot(Product product) {
        product.setExclusiveLotTime(ZonedDateTime.now());
        productRepository.saveAndFlush(product);
    }

    private void unsetProductExclusiveLot(Product product) {
        product.setExclusiveLotTime(null);
        productRepository.saveAndFlush(product);
    }

    private void cleanup() {

        cartTestSupport.cleanup();

        productsToBuy.values().forEach(productIds ->
                productIds.forEach(productId -> unsetProductExclusiveLot(productRepository.findById(productId).orElse(null))));
    }
}
