package ru.oskelly.tests.pr.suite3.presentation.api.v2;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.google.common.collect.Sets;
import com.querydsl.core.BooleanBuilder;
import lombok.NonNull;
import org.assertj.core.api.Assertions;
import org.assertj.core.util.BigDecimalComparator;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.pr.common.bonuses.BonusesServiceTestConfiguration;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.component.TestApiConfiguration;
import su.reddot.domain.dao.device.DeviceRepository;
import su.reddot.domain.dao.discount.PromoCodeRepository;
import su.reddot.domain.dao.product.ProductItemRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.Brand;
import su.reddot.domain.model.applyRule.ApplyRuleFilterType;
import su.reddot.domain.model.category.Category;
import su.reddot.domain.model.device.Device;
import su.reddot.domain.model.device.DeviceDtype;
import su.reddot.domain.model.discount.AbsolutePromoCode;
import su.reddot.domain.model.discount.FractionalPromoCode;
import su.reddot.domain.model.discount.PromoCode;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.product.QProduct;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.device.DeviceService;
import su.reddot.domain.service.dto.order.GroupedCart;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.dto.order.OrderPositionDTO;
import su.reddot.domain.service.order.Discount;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.promocode.PromoCodeService;
import su.reddot.domain.service.user.UserService;
import su.reddot.presentation.api.v2.Api2Response;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.Mockito.lenient;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@ContextConfiguration(classes = {BonusesServiceTestConfiguration.class})
@DevSuite(value = TestSuiteName.TEST_SUITE_03)
public class CartControllerApiV2PromocodesTest extends AbstractSpringTest {

    @Autowired
    private UserService userService;
    @Autowired
    private ProductRepository productRepository;
    @Autowired
    private ProductItemRepository productItemRepository;
    @Autowired
    private PromoCodeRepository promoCodeRepository;
    @Autowired
    private PromoCodeService promoCodeService;
    @Autowired
    private OrderService orderService;

    @MockBean
    DeviceService deviceService;
    @Autowired
    private CartTestSupport cartTestSupport;

    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user-password}")
    private String password;
    @Value("${test.api.user2-email}")
    private String sellerEmail;

    private Boolean saveOrderToFile = Boolean.FALSE; // in case of debug / detailed view / compare use this to save orders to JSON

    @Autowired
    private TestApiConfiguration testApiConfiguration;

    private static ApiV2Client apiV2Client;

    private ObjectMapper objectMapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setSerializationInclusion(JsonInclude.Include.NON_NULL)
            .enable(SerializationFeature.INDENT_OUTPUT);

    private User getBuyer() {
        return userService.getUserByEmail(buyerEmail);
    }

    private Long sellerId = 19553L;

    private List<Product> getProductsForOrders() {
        List<Product> products = productRepository.findProductsBySellerIdAndProductState(sellerId, ProductState.PUBLISHED).stream()
                .filter(p -> Objects.isNull(p.getCurrentPriceCurrencyId()))
                .collect(Collectors.toList());
        List<Product> productsForOrders = new ArrayList<>();
        for (int i = 0; i < 32; i++) {
            Product product = products.get(i);
            product.setCurrentPrice(new BigDecimal((i + 2) * 5000));
            productRepository.saveAndFlush(product);
            product.getProductItems().forEach(pi -> {
                pi.setHidden(false);
                pi.setCount(100);
                pi.setDeleteTime(null);
                productItemRepository.saveAndFlush(pi);
            });
            productsForOrders.add(product);
        }
        return productsForOrders;
    }

    private String getOrderUrl(@NonNull Long orderId) {
        return testApiConfiguration.getServerUrl() + "/api/v2/orders/" + orderId;
    }

    private OrderDTO loadOrderSuccessfull(Long orderId, ApiV2Client client, boolean withAuthoriseParams) {
        ResponseEntity<Api2Response<OrderDTO>> responseEntity = client.request(getOrderUrl(orderId), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<OrderDTO>>() {}, withAuthoriseParams);
        assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
        assertNotNull(responseEntity.getBody());
        assertNotNull(responseEntity.getBody().getData());
        //assertOrderIsValid(responseEntity.getBody().getData());
        return responseEntity.getBody().getData();
    }

    private String saveOrderToFile(OrderDTO orderDto, String promoCode) {
        if (!Boolean.TRUE.equals(saveOrderToFile)) {
            return null;
        }
        String fileName = String.format("/home/<USER>/test/%s.json", promoCode);
        try {
            objectMapper.writeValue(new File(fileName), orderDto);
            return fileName;
        } catch (Exception e) {
            throw new RuntimeException("Unable to save order to file");
        }
    }

    private PromoCode findOrCreatePromocode(Class<? extends PromoCode> promoCodeKind, String codeName) {
        try {
            return promoCodeRepository.findFirstByCode(codeName).stream().findFirst().orElse(promoCodeKind.newInstance());
        } catch (Exception e) {
            throw new RuntimeException("Unable to create promocode instance");
        }
    }

    private PromoCode findOrCreatePromocodeForTest(Class<? extends PromoCode> promoCodeKind, String codeName, BigDecimal value) {
        PromoCode promoCode = findOrCreatePromocode(promoCodeKind, codeName);
        if (promoCode instanceof AbsolutePromoCode) { // TODO: Think of single .setValue method to avoid "instanceOf"
            ((AbsolutePromoCode) promoCode).setValue(value);
        }
        if (promoCode instanceof FractionalPromoCode) {
            ((FractionalPromoCode) promoCode).setValue(value);
        }
        promoCode.setCode(codeName).setBeginPrice(new BigDecimal(1)).setCreatedAt(ZonedDateTime.now())
                .setExpiresAt(ZonedDateTime.now().plusYears(100))
                .setStartsAt(ZonedDateTime.now())
                .setDeleted(false);
        return promoCodeRepository.saveAndFlush(promoCode);
    }

    private GroupedCart fillCart(List<Product> productList) {
        GroupedCart cartInfo = null;
        cartTestSupport.cleanCart(true);
        for (int i = 0; i < productList.size(); i++) {
            cartInfo = cartTestSupport.addToCartSuccessful(productList.get(i).getId(), productList.get(i).getAvailableProductItems().get(0).getSize().getId(), 1, true);
            assertEquals(i + 1, cartInfo.getSize());
        }
        //
        BooleanBuilder filterExpression = new BooleanBuilder().and(QProduct.product.productState.eq(ProductState.PUBLISHED))
                                                              .and(QProduct.product.seller.id.ne(productList.get(0).getSeller().getId()));
        List<Long> randomItems = productRepository.findProductIds(filterExpression, false, Collections.emptyList(), false)
                                                  .stream().limit(3).collect(Collectors.toList());
        Assertions.assertThat(randomItems).isNotEmpty(); // We need to keep it >0 to be able to test order split by sellers
        for (Long id : randomItems) {
            Product p = productRepository.getOne(id);
            cartInfo = cartTestSupport.addToCartSuccessful(p.getId(), p.getAvailableProductItems().get(0).getSize().getId(), 1, true);
        }
        //
        return cartInfo;
    }

    private OrderDTO checkCartPromoCode(String promoCode) {
        OrderDTO promoCodeOrderDTO = cartTestSupport.checkPromoCode(sellerId, promoCode);
        Discount discount = promoCodeOrderDTO.getDiscount();
        assertNotNull(discount);
        return promoCodeOrderDTO;
    }

    private OrderDTO holdOrderWithPromoCode(String promoCode) {
        OrderService.InitOrderResult holdResult = cartTestSupport.holdWithSetAddressEndpoint(CartTestSupport.HOLD_V2_ENDPOINT, sellerId, promoCode);
        Long orderId = holdResult.getOrderId();
        return loadOrderSuccessfull(orderId, apiV2Client, true);
    }

    private boolean OrderDtoCompareVerifyOrder(OrderDTO orderDto1Verify1, OrderDTO orderDto2OnHold1) {
        try {
            OrderDTO orderDto1VerifyCopy = objectMapper.readValue(objectMapper.writeValueAsString(orderDto1Verify1), OrderDTO.class);
            OrderDTO orderDto2OnHoldCopy = objectMapper.readValue(objectMapper.writeValueAsString(orderDto2OnHold1), OrderDTO.class);
            //
            BigDecimal verifyHoldFinalAmount = orderDto2OnHoldCopy.getItems().stream().map(OrderPositionDTO::getFinalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            orderDto2OnHoldCopy.setFinalAmount(verifyHoldFinalAmount.add(orderDto2OnHoldCopy.getDeliveryCost()));
            orderDto2OnHoldCopy.setFinalAmountWithoutDeliveryCost(verifyHoldFinalAmount);
            //
            Assertions.assertThat(orderDto1VerifyCopy)
                    .usingComparatorForType(BigDecimalComparator.BIG_DECIMAL_COMPARATOR, BigDecimal.class)
                    .usingRecursiveComparison()
                    .ignoringFields("id", "isMadeByNewUser", "legalEntity", "payment", "numbers", "state", "stateTime", "deliveryAddressEndpoint", "deletable", "deliveryDescription")
                    .isEqualTo(orderDto2OnHoldCopy);
            //
            return true;
        } catch (Exception anyException) {
            return false;
        }
    }

    private void compareObjectsWithJson(Object objectOne, Object objectTwo) {
        try {
            String objectOneJsonText = objectMapper.writeValueAsString(objectOne);
            String objectTwoJsonText = objectMapper.writeValueAsString(objectTwo);
            //
            JsonNode objectOneJsonTree = objectMapper.readTree(objectOneJsonText);
            JsonNode objectTwoJsonTree = objectMapper.readTree(objectTwoJsonText);
            //
            assertEquals(objectOneJsonTree, objectTwoJsonTree);
        } catch (Exception e) {
            fail(String.format("compareObjectsWithJson: caught exception %s", e.getMessage()));
        }
    }

    private void validateOrderDto(OrderDTO orderDTO) {
        BigDecimal totalPromoCodeAmount = orderDTO.getItems().stream().map(OrderPositionDTO::getPromocodeAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        assertEquals(orderDTO.getDiscount().savingsValue.longValue(), totalPromoCodeAmount.longValue());
    }

    private OrderDTO fillCartCheckPromoAndHoldOrder(String promoCode, List<Product> productList) {
        fillCart(productList.subList(0, 2));
        //
        GroupedCart cartBefValidate = cartTestSupport.getCart(true, null);
        //
        OrderDTO orderOnPromoVerify = checkCartPromoCode(promoCode);
        validateOrderDto(orderOnPromoVerify);
        //
        GroupedCart cartAftValidate = cartTestSupport.getCart(true, null);
        //
        compareObjectsWithJson(cartBefValidate, cartAftValidate);
        //
        OrderDTO orderWithPromoCode = holdOrderWithPromoCode(promoCode);
        validateOrderDto(orderWithPromoCode);
        //
        assertTrue(OrderDtoCompareVerifyOrder(orderOnPromoVerify, orderWithPromoCode));
        //
        Order order = orderService.getOrder(orderWithPromoCode.getId());
        assertEquals(order.getAmount().setScale(0, RoundingMode.HALF_UP).longValue(), orderOnPromoVerify.getDiscount().resultAmountWithDeliveryCost.longValue());
        //
        saveOrderToFile(orderWithPromoCode, promoCode);
        //
        return orderWithPromoCode;
    }

    private void checkPromocodeFailsWithException(String promoCode, String errorText) {
        Pattern pattern = Pattern.compile(errorText);
        try {
            checkCartPromoCode(promoCode);
            fail("Unreachable code: checkPromoCode must throw exception");
        } catch (ApiV2ParseException e) {
            Api2Response<String> errorRsp = getRawApi2Response(e.getRawData());
            assertTrue(pattern.matcher(errorRsp.getMessage()).matches());
            assertTrue(pattern.matcher(errorRsp.getHumanMessage()).matches());
        }
    }

    private void holdOrderWithPromocodeFailsException(String promoCode, String errorText) {
        Pattern pattern = Pattern.compile(errorText);
        try {
            holdOrderWithPromoCode(promoCode);
            fail("Unreachable code: holdOrderPromoCode must throw exception");
        } catch (ApiV2ParseException e) {
            Api2Response<String> errorRsp = getRawApi2Response(e.getRawData());
            assertTrue(pattern.matcher(errorRsp.getMessage()).matches());
            assertTrue(pattern.matcher(errorRsp.getHumanMessage()).matches());
        }
    }

    @PostConstruct
    private void init() {
        User buyer = userService.getUserByEmail(buyerEmail);

        lenient().when(deviceService.getCurrentDeviceInfo())
            .thenReturn(new DeviceService.DeviceInfo()
                .setDeviceDtype(DeviceDtype.OtherDevice));

        apiV2Client = new ApiV2Client(buyerEmail, password);
        cartTestSupport.setUserId(buyer.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
    }

    @Test
    @Transactional
    public void _01_01_createOrderNoPromocode() {
        String promoCode = null;
        //
        List<Product> products = getProductsForOrders();
        commitAndStartNewTransaction();
        //
        fillCart(products.subList(0, 2));

        OrderDTO order = holdOrderWithPromoCode(promoCode);
        //
        assertEquals(2, order.getItems().size());
        OrderPositionDTO op1 = order.getItems().get(0);
        assertEquals(op1.getAmount(), op1.getFinalAmount());
        assertEquals(BigDecimal.ZERO, op1.getPromocodeAmount());
        OrderPositionDTO op2 = order.getItems().get(0);
        assertEquals(op2.getAmount(), op2.getFinalAmount());
        assertEquals(BigDecimal.ZERO, op2.getPromocodeAmount());
    }

    @Test
    @Transactional
    public void _01_02_createOrderPercentPromocode() {
        String promoCode = "_01_02_percent_code";
        BigDecimal value = new BigDecimal("0.10");
        //
        List<Product> products = getProductsForOrders();
        findOrCreatePromocodeForTest(FractionalPromoCode.class, promoCode, value);
        commitAndStartNewTransaction();
        //
        OrderDTO order = fillCartCheckPromoAndHoldOrder(promoCode, products);
        //
        assertEquals(2, order.getItems().size());
        //
        OrderPositionDTO op1 = order.getItems().get(0);
        BigDecimal calcFinal1 = op1.getAmount().multiply(BigDecimal.valueOf(1).subtract(value));
        assertEquals(0, op1.getPromocodeAmount().compareTo(op1.getAmount().subtract(calcFinal1)));
        //assertEquals(0, calcFinal1.compareTo(op1.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount
        //
        OrderPositionDTO op2 = order.getItems().get(0);
        BigDecimal calcFinal2 = op2.getAmount().multiply(BigDecimal.valueOf(1).subtract(value));
        assertEquals(0, op2.getPromocodeAmount().compareTo(op2.getAmount().subtract(calcFinal2)));
        //assertEquals(0, calcFinal2.compareTo(op2.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount
    }

    @Test
    @Transactional
    public void _01_03_createOrderAmountPromocode() {
        String promoCode = "_01_03_amount_code";
        BigDecimal value = new BigDecimal(1000);
        //
        List<Product> products = getProductsForOrders();
        findOrCreatePromocodeForTest(AbsolutePromoCode.class, promoCode, value);
        commitAndStartNewTransaction();
        //
        OrderDTO order = fillCartCheckPromoAndHoldOrder(promoCode, products);
        //
        assertEquals(2, order.getItems().size());
        //
        OrderPositionDTO op1 = order.getItems().get(0);
        BigDecimal calcFinal1 = op1.getAmount().subtract( op1.getAmount().multiply(value).divide(order.getClearAmount(), RoundingMode.HALF_UP) );
        assertEquals(0, op1.getPromocodeAmount().compareTo(op1.getAmount().subtract(calcFinal1)));
        //assertEquals(0, calcFinal1.compareTo(op1.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount
        //
        OrderPositionDTO op2 = order.getItems().get(0);
        BigDecimal calcFinal2 = op2.getAmount().subtract( op2.getAmount().multiply(value).divide(order.getClearAmount(), RoundingMode.HALF_UP) );
        assertEquals(0, op2.getPromocodeAmount().compareTo(op2.getAmount().subtract(calcFinal2)));
        //assertEquals(0, calcFinal2.compareTo(op2.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount
    }

    Api2Response<String> getRawApi2Response(String rawData) {
        try {
            return objectMapper.readValue(rawData, new TypeReference<Api2Response<String>>() {});
        } catch (IOException e) {
            return null;
        }
    }

    @Test
    @Transactional
    public void _01_04_createOrderPromocodeNotExists() {
        String promoCode = "_01_04_no_promo_exists";
        String errorText = "Скидка c кодом: .* не найдена";
        //
        List<Product> products = getProductsForOrders();
        commitAndStartNewTransaction();
        //
        fillCart(products.subList(0, 2));
        //
        checkPromocodeFailsWithException(promoCode, errorText);
        //
        holdOrderWithPromocodeFailsException(promoCode, errorText);
    }

    @Test
    @Transactional
    public void _01_05_createOrderPromocodeIsExpired() {
        String promoCode = "_01_05_expired_code";
        BigDecimal value = new BigDecimal(1000);
        String errorText = "Скидка c кодом: .* не найдена";
        //
        PromoCode promoCode1 = findOrCreatePromocodeForTest(AbsolutePromoCode.class, promoCode, value);
        promoCode1.setExpiresAt(ZonedDateTime.now().minusDays(100));
        promoCodeRepository.save(promoCode1);
        commitAndStartNewTransaction();
        //
        List<Product> products = getProductsForOrders();
        commitAndStartNewTransaction();
        //
        fillCart(products.subList(0, 2));
        checkPromocodeFailsWithException(promoCode, errorText);
        //
        holdOrderWithPromocodeFailsException(promoCode, errorText);
    }

    @Test
    @Transactional
    public void _01_06_createOrderPromocodeInFuture() {
        String promoCode = "_01_06_future_code";
        BigDecimal value = new BigDecimal(1000);
        String errorText = "Скидка c кодом: .* не найдена";
        //
        PromoCode promoCode1 = findOrCreatePromocodeForTest(AbsolutePromoCode.class, promoCode, value);
        promoCode1.setStartsAt(ZonedDateTime.now().plusDays(10));
        promoCodeRepository.save(promoCode1);
        commitAndStartNewTransaction();
        //
        List<Product> products = getProductsForOrders();
        commitAndStartNewTransaction();
        //
        fillCart(products.subList(0, 2));
        //
        checkPromocodeFailsWithException(promoCode, errorText);
        //
        holdOrderWithPromocodeFailsException(promoCode, errorText);
    }

    @Test
    @Transactional
    public void _01_07_createOrderPromocodeTooBigAmountFrom() {
        String promoCode = "_01_06_future_code";
        BigDecimal value = new BigDecimal(1000);
        String errorText = "Скидка c кодом: .* не найдена";
        //
        PromoCode promoCode1 = findOrCreatePromocodeForTest(AbsolutePromoCode.class, promoCode, value);
        promoCode1.setBeginPrice(BigDecimal.valueOf(1000000));
        promoCodeRepository.save(promoCode1);
        commitAndStartNewTransaction();
        //
        List<Product> products = getProductsForOrders();
        commitAndStartNewTransaction();
        //
        fillCart(products.subList(0, 2));
        //
        checkPromocodeFailsWithException(promoCode, errorText);
        //
        holdOrderWithPromocodeFailsException(promoCode, errorText);
    }

    @Test
    @Transactional
    public void _02_BrandPromoCode_PercentHits0of2() {
        String promoCode = "_02_01_percent_brand_code";
        BigDecimal value = new BigDecimal("0.10");
        String errorText = "Скидка не найдена";
        //
        List<Product> products = getProductsForOrders();
        Map<Brand, List<Product>> brandMap = products.stream().collect(groupingBy(Product::getBrand));
        assertTrue(brandMap.size() > 2);
        //
        List<Brand> brandsList = new ArrayList<>(brandMap.keySet());
        Product item1 = brandMap.get(brandsList.get(0)).get(0);
        Product item2 = brandMap.get(brandsList.get(1)).get(0);
        Product item3 = brandMap.get(brandsList.get(2)).get(0);
        assertNotEquals(item1.getBrand().getId(), item2.getBrand().getId());
        assertNotEquals(item3.getBrand().getId(), item1.getBrand().getId());
        assertNotEquals(item3.getBrand().getId(), item2.getBrand().getId());
        //
        PromoCode promoCodeItem = findOrCreatePromocodeForTest(FractionalPromoCode.class, promoCode, value);
        promoCodeService.saveFilterValues(promoCodeItem, ApplyRuleFilterType.BRAND, Collections.singleton(item3.getBrand().getId()));
        commitAndStartNewTransaction();
        //
        fillCart(Arrays.asList(item1, item2));
        //
        checkPromocodeFailsWithException(promoCode, errorText);
        //
        holdOrderWithPromocodeFailsException(promoCode, errorText);
    }

    @Test
    @Transactional
    public void _02_BrandPromoCode_PercentHits1of2() {
        String promoCode = "_02_01_percent_brand_code";
        BigDecimal value = new BigDecimal("0.10");
        //
        List<Product> products = getProductsForOrders();
        Map<Brand, List<Product>> brandMap = products.stream().collect(groupingBy(Product::getBrand));
        assertTrue(brandMap.size() > 2);
        //
        List<Brand> brandsList = new ArrayList<>(brandMap.keySet());
        Product item1 = brandMap.get(brandsList.get(0)).get(0);
        Product item2 = brandMap.get(brandsList.get(1)).get(0);
        Product item3 = brandMap.get(brandsList.get(2)).get(0);
        assertNotEquals(item1.getBrand().getId(), item2.getBrand().getId());
        assertNotEquals(item3.getBrand().getId(), item1.getBrand().getId());
        assertNotEquals(item3.getBrand().getId(), item2.getBrand().getId());
        //
        PromoCode promoCodeItem = findOrCreatePromocodeForTest(FractionalPromoCode.class, promoCode, value);
        promoCodeService.saveFilterValues(promoCodeItem, ApplyRuleFilterType.BRAND, Collections.singleton(item1.getBrand().getId()));
        commitAndStartNewTransaction();
        //
        OrderDTO order = fillCartCheckPromoAndHoldOrder(promoCode, Arrays.asList(item1, item2));
        //
        assertEquals(2, order.getItems().size());
        //
        OrderPositionDTO op1 = order.getItems().stream().filter(op -> item1.getId().equals(op.getProductId())).findFirst().orElseThrow(AssertionError::new);
        BigDecimal calcFinal1 = op1.getAmount().multiply(BigDecimal.valueOf(1).subtract(value));
        assertEquals(0, op1.getPromocodeAmount().compareTo(op1.getAmount().subtract(calcFinal1)));
        //assertEquals(0, calcFinal1.compareTo(op1.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount
        //
        OrderPositionDTO op2 = order.getItems().stream().filter(op -> item2.getId().equals(op.getProductId())).findFirst().orElseThrow(AssertionError::new);
        BigDecimal calcFinal2 = op2.getAmount();
        assertEquals(BigDecimal.ZERO, op2.getPromocodeAmount());
        //assertEquals(0, calcFinal2.compareTo(op2.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount
    }

    @Test
    @Transactional
    public void _02_BrandPromoCode_PercentHits1of2_HiddenItem() {
        String promoCode = "_02_01_percent_brand_code";
        BigDecimal discountFactor = new BigDecimal("0.10");
        //
        Map<User, List<Product>> bySeller = getProductsForOrders().stream().collect(groupingBy(Product::getSeller));
        Map.Entry<User, List<Product>> sellerProducts = bySeller.entrySet().stream()
                .filter(key -> key.getValue().size() > 1)
                .findFirst()
                .orElseThrow(IllegalStateException::new);
        User seller = sellerProducts.getKey();
        List<Product> products = sellerProducts.getValue();

        Map<Long, List<Product>> brandMap = products.stream()
                .collect(groupingBy((Product product) -> product.getBrand().getId()));
        assertTrue(brandMap.size() > 2);

        Iterator<List<Product>> iterator = brandMap.values().iterator();
        Product item1 = iterator.next().get(0);
        Product item2 = iterator.next().get(0);

        PromoCode promoCodeItem = findOrCreatePromocodeForTest(FractionalPromoCode.class, promoCode, discountFactor);
        promoCodeService.saveFilterValues(promoCodeItem, ApplyRuleFilterType.BRAND, Arrays.asList(item1.getBrand().getId() ,item2.getBrand().getId()));
        commitAndStartNewTransaction();

        cartTestSupport.cleanCart(true);
        cartTestSupport.addToCartSuccessful(item1.getId(), item1.getAvailableProductItems().get(0).getSize().getId(), 1, true);
        cartTestSupport.addToCartSuccessful(item2.getId(), item2.getAvailableProductItems().get(0).getSize().getId(), 1, true);

        GroupedCart cartBeforeProductHidden = cartTestSupport.getCart(true, null);
        //

        Assertions.assertThat(cartBeforeProductHidden.getGroup(seller.getId()).getClearAmount())
                .isEqualByComparingTo(item1.getCurrentPrice().add(item2.getCurrentPrice()));

        commitAndStartNewTransaction();

        item1.setProductState(ProductState.HIDDEN);
        productRepository.saveAndFlush(item1);
        commitAndStartNewTransaction();

        GroupedCart cartAfterProductHidden = cartTestSupport.getCart(true, null);

        Assertions.assertThat(cartAfterProductHidden.getGroup(seller.getId()).getClearAmount())
                .isEqualByComparingTo(item2.getCurrentPrice());

        commitAndStartNewTransaction();

        OrderDTO orderOnPromoVerify = cartTestSupport.checkPromoCode(sellerId, promoCode);

        Discount discountVerified = orderOnPromoVerify.getDiscount();
        Assertions.assertThat(discountVerified.baseAmount)
                .isEqualTo(item2.getCurrentPrice().longValue());
        Assertions.assertThat(discountVerified.discountValue.longValue())
                .isEqualTo(item2.getCurrentPrice().multiply(discountFactor).longValue());

        OrderDTO orderHeldWithPromoCode = holdOrderWithPromoCode(promoCode);
        BigDecimal totalPromoCodeAmount = orderHeldWithPromoCode.getItems().stream()
                .map(OrderPositionDTO::getPromocodeAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        assertEquals(orderHeldWithPromoCode.getDiscount().savingsValue.longValue(), totalPromoCodeAmount.longValue());

        Discount discountHeld = orderHeldWithPromoCode.getDiscount();
        Assertions.assertThat(discountHeld.baseAmount)
                .isEqualTo(item2.getCurrentPrice().longValue());
        Assertions.assertThat(discountHeld.discountValue.longValue())
                .isEqualTo(item2.getCurrentPrice().multiply(discountFactor).longValue());
        Assertions.assertThat(orderHeldWithPromoCode.getClearAmount())
                .isEqualByComparingTo(item2.getCurrentPrice());
        Assertions.assertThat(orderHeldWithPromoCode.getFinalAmount())
                .isEqualByComparingTo(item2.getCurrentPrice().subtract(discountHeld.discountValue).add(orderHeldWithPromoCode.getDeliveryCost()));
    }

    @Test
    @Transactional
    public void _02_BrandPromoCode_PercentHits2of2() {
        String promoCode = "_02_01_percent_brand_code";
        BigDecimal value = new BigDecimal("0.10");
        //
        List<Product> products = getProductsForOrders();
        Map<Brand, List<Product>> brandMap = products.stream().collect(groupingBy(Product::getBrand));
        assertTrue(brandMap.size() > 2);
        //
        List<Brand> brandsList = new ArrayList<>(brandMap.keySet());
        Product item1 = brandMap.get(brandsList.get(0)).get(0);
        Product item2 = brandMap.get(brandsList.get(1)).get(0);
        Product item3 = brandMap.get(brandsList.get(2)).get(0);
        assertNotEquals(item1.getBrand().getId(), item2.getBrand().getId());
        assertNotEquals(item3.getBrand().getId(), item1.getBrand().getId());
        assertNotEquals(item3.getBrand().getId(), item2.getBrand().getId());
        //
        PromoCode promoCodeItem = findOrCreatePromocodeForTest(FractionalPromoCode.class, promoCode, value);
        promoCodeService.saveFilterValues(promoCodeItem, ApplyRuleFilterType.BRAND, Sets.newHashSet(item1.getBrand().getId(), item2.getBrand().getId()));
        commitAndStartNewTransaction();
        //
        OrderDTO order = fillCartCheckPromoAndHoldOrder(promoCode, Arrays.asList(item1, item2));
        //
        assertEquals(2, order.getItems().size());
        //
        OrderPositionDTO op1 = order.getItems().stream().filter(op -> item1.getId().equals(op.getProductId())).findFirst().orElseThrow(AssertionError::new);
        BigDecimal calcFinal1 = op1.getAmount().multiply(BigDecimal.valueOf(1).subtract(value));
        assertEquals(0, op1.getPromocodeAmount().compareTo(op1.getAmount().subtract(calcFinal1)));
        //assertEquals(0, calcFinal1.compareTo(op1.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount
        //
        OrderPositionDTO op2 = order.getItems().stream().filter(op -> item2.getId().equals(op.getProductId())).findFirst().orElseThrow(AssertionError::new);
        BigDecimal calcFinal2 = op2.getAmount().multiply(BigDecimal.valueOf(1).subtract(value));
        assertEquals(0, op2.getPromocodeAmount().compareTo(op2.getAmount().subtract(calcFinal2)));
        //assertEquals(0, calcFinal2.compareTo(op2.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount
    }

    @Test
    @Transactional
    public void _02_BrandPromoCode_04_AmountHits0of2() {
        String promoCode = "_03_01_amount_brand_code";
        BigDecimal value = BigDecimal.valueOf(2500);
        String errorText = "Скидка не найдена";
        //
        List<Product> products = getProductsForOrders();
        Map<Brand, List<Product>> brandMap = products.stream().collect(groupingBy(Product::getBrand));
        assertTrue(brandMap.size() > 2);
        //
        List<Brand> brandsList = new ArrayList<>(brandMap.keySet());
        Product item1 = brandMap.get(brandsList.get(0)).get(0);
        Product item2 = brandMap.get(brandsList.get(1)).get(0);
        Product item3 = brandMap.get(brandsList.get(2)).get(0);
        assertNotEquals(item1.getBrand().getId(), item2.getBrand().getId());
        assertNotEquals(item3.getBrand().getId(), item1.getBrand().getId());
        assertNotEquals(item3.getBrand().getId(), item2.getBrand().getId());
        //
        PromoCode promoCodeItem = findOrCreatePromocodeForTest(AbsolutePromoCode.class, promoCode, value);
        promoCodeService.saveFilterValues(promoCodeItem, ApplyRuleFilterType.BRAND, Collections.singleton(item3.getBrand().getId()));
        commitAndStartNewTransaction();
        //
        fillCart(Arrays.asList(item1, item2));
        //
        checkPromocodeFailsWithException(promoCode, errorText);
        //
        holdOrderWithPromocodeFailsException(promoCode, errorText);
    }

    @Test
    @Transactional
    public void _02_BrandPromoCode_05_AmountHits1of2() {
        String promoCode = "_03_01_amount_brand_code";
        BigDecimal value = BigDecimal.valueOf(2500);
        //
        List<Product> products = getProductsForOrders();
        Map<Brand, List<Product>> brandMap = products.stream().collect(groupingBy(Product::getBrand));
        assertTrue(brandMap.size() > 2);
        //
        List<Brand> brandsList = new ArrayList<>(brandMap.keySet());
        Product item1 = brandMap.get(brandsList.get(0)).get(0);
        Product item2 = brandMap.get(brandsList.get(1)).get(0);
        Product item3 = brandMap.get(brandsList.get(2)).get(0);
        assertNotEquals(item1.getBrand().getId(), item2.getBrand().getId());
        assertNotEquals(item3.getBrand().getId(), item1.getBrand().getId());
        assertNotEquals(item3.getBrand().getId(), item2.getBrand().getId());
        //
        PromoCode promoCodeItem = findOrCreatePromocodeForTest(AbsolutePromoCode.class, promoCode, value);
        promoCodeService.saveFilterValues(promoCodeItem, ApplyRuleFilterType.BRAND, Collections.singleton(item1.getBrand().getId()));
        commitAndStartNewTransaction();
        //
        OrderDTO order = fillCartCheckPromoAndHoldOrder(promoCode, Arrays.asList(item1, item2));
        //
        assertEquals(2, order.getItems().size());
        //
        OrderPositionDTO op1 = order.getItems().stream().filter(op -> item1.getId().equals(op.getProductId())).findFirst().orElseThrow(AssertionError::new);
        BigDecimal calcFinal1 = op1.getAmount().subtract(value);
        assertEquals(0, op1.getPromocodeAmount().compareTo(op1.getAmount().subtract(calcFinal1)));
        //assertEquals(0, calcFinal1.compareTo(op1.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount
        //
        OrderPositionDTO op2 = order.getItems().stream().filter(op -> item2.getId().equals(op.getProductId())).findFirst().orElseThrow(AssertionError::new);
        BigDecimal calcFinal2 = op2.getAmount();
        assertEquals(BigDecimal.ZERO, op2.getPromocodeAmount());
        //assertEquals(0, calcFinal2.compareTo(op2.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount
    }

    @Test
    @Transactional
    public void _02_BrandPromoCode_06_AmountHits2of2() {
        String promoCode = "_03_01_amount_brand_code";
        BigDecimal value = BigDecimal.valueOf(2500);
        //
        List<Product> products = getProductsForOrders();
        Map<Brand, List<Product>> brandMap = products.stream().collect(groupingBy(Product::getBrand));
        assertTrue(brandMap.size() > 2);
        //
        List<Brand> brandsList = new ArrayList<>(brandMap.keySet());
        Product item1 = brandMap.get(brandsList.get(0)).get(0);
        Product item2 = brandMap.get(brandsList.get(1)).get(0);
        Product item3 = brandMap.get(brandsList.get(2)).get(0);
        assertNotEquals(item1.getBrand().getId(), item2.getBrand().getId());
        assertNotEquals(item3.getBrand().getId(), item1.getBrand().getId());
        assertNotEquals(item3.getBrand().getId(), item2.getBrand().getId());
        //
        PromoCode promoCodeItem = findOrCreatePromocodeForTest(AbsolutePromoCode.class, promoCode, value);
        promoCodeService.saveFilterValues(promoCodeItem, ApplyRuleFilterType.BRAND, Sets.newHashSet(item1.getBrand().getId(), item2.getBrand().getId()));
        commitAndStartNewTransaction();
        //
        OrderDTO order = fillCartCheckPromoAndHoldOrder(promoCode, Arrays.asList(item1, item2));
        //
        assertEquals(2, order.getItems().size());
        //
        OrderPositionDTO op1 = order.getItems().stream().filter(op -> item1.getId().equals(op.getProductId())).findFirst().orElseThrow(AssertionError::new);
        BigDecimal calcPromo1 = op1.getAmount().multiply(value).divide(order.getClearAmount(), 2, RoundingMode.HALF_UP);
        assertEquals(0, op1.getPromocodeAmount().compareTo(calcPromo1));
        //assertEquals(0, calcFinal1.compareTo(op1.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount
        //
        OrderPositionDTO op2 = order.getItems().stream().filter(op -> item2.getId().equals(op.getProductId())).findFirst().orElseThrow(AssertionError::new);
        BigDecimal calcPromo2 = op2.getAmount().multiply(value).divide(order.getClearAmount(), 2, RoundingMode.HALF_UP);
        assertEquals(0, op2.getPromocodeAmount().compareTo(calcPromo2));
        //assertEquals(0, calcFinal2.compareTo(op2.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount
        //
        assertEquals(BigDecimal.ZERO.longValue(), value.subtract(op1.getPromocodeAmount()).subtract(op2.getPromocodeAmount()).longValue());
    }

    @Test
    @Transactional
    public void _03_CategoryPromoCode_01_PercentHits0of2() {
        String promoCode = "_03_01_percent_category_code";
        BigDecimal value = new BigDecimal("0.10");
        String errorText = "Скидка не найдена";
        //
        List<Product> products = getProductsForOrders();
        Map<Category, List<Product>> categoryMap = products.stream().collect(groupingBy(Product::getCategory));
        assertTrue(categoryMap.size() > 2);
        //
        List<Category> categoryList = new ArrayList<>(categoryMap.keySet());
        Product item1 = categoryMap.get(categoryList.get(0)).get(0);
        Product item2 = categoryMap.get(categoryList.get(1)).get(0);
        Product item3 = categoryMap.get(categoryList.get(2)).get(0);
        assertNotEquals(item1.getCategoryId(), item2.getCategoryId());
        assertNotEquals(item3.getCategoryId(), item1.getCategoryId());
        assertNotEquals(item3.getCategoryId(), item2.getCategoryId());
        //
        PromoCode promoCodeItem = findOrCreatePromocodeForTest(FractionalPromoCode.class, promoCode, value);
        promoCodeService.saveFilterValues(promoCodeItem, ApplyRuleFilterType.CATEGORY, Collections.singleton(item3.getCategory().getId()));
        commitAndStartNewTransaction();
        //
        fillCart(Arrays.asList(item1, item2));
        //
        checkPromocodeFailsWithException(promoCode, errorText);
        //
        holdOrderWithPromocodeFailsException(promoCode, errorText);
    }

    @Test
    @Transactional
    public void _03_CategoryPromoCode_02_PercentHits1of2() {
        String promoCode = "_03_01_percent_category_code";
        BigDecimal value = new BigDecimal("0.10");
        //
        List<Product> products = getProductsForOrders();
        Map<Category, List<Product>> categoryMap = products.stream().collect(groupingBy(Product::getCategory));
        assertTrue(categoryMap.size() > 2);
        //
        List<Category> categoryList = new ArrayList<>(categoryMap.keySet());
        Product item1 = categoryMap.get(categoryList.get(0)).get(0);
        Product item2 = categoryMap.get(categoryList.get(1)).get(0);
        Product item3 = categoryMap.get(categoryList.get(2)).get(0);
        assertNotEquals(item1.getCategoryId(), item2.getCategoryId());
        assertNotEquals(item3.getCategoryId(), item1.getCategoryId());
        assertNotEquals(item3.getCategoryId(), item2.getCategoryId());
        //
        PromoCode promoCodeItem = findOrCreatePromocodeForTest(FractionalPromoCode.class, promoCode, value);
        promoCodeService.saveFilterValues(promoCodeItem, ApplyRuleFilterType.CATEGORY, Collections.singleton(item1.getCategory().getId()));
        commitAndStartNewTransaction();
        //
        OrderDTO order = fillCartCheckPromoAndHoldOrder(promoCode, Arrays.asList(item1, item2));
        //
        assertEquals(2, order.getItems().size());
        //
        OrderPositionDTO op1 = order.getItems().stream().filter(op -> item1.getId().equals(op.getProductId())).findFirst().orElseThrow(AssertionError::new);
        BigDecimal calcFinal1 = op1.getAmount().multiply(BigDecimal.valueOf(1).subtract(value));
        assertEquals(0, op1.getPromocodeAmount().compareTo(op1.getAmount().subtract(calcFinal1)));
        //assertEquals(0, calcFinal1.compareTo(op1.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount
        //
        OrderPositionDTO op2 = order.getItems().stream().filter(op -> item2.getId().equals(op.getProductId())).findFirst().orElseThrow(AssertionError::new);
        BigDecimal calcFinal2 = op2.getAmount();
        assertEquals(BigDecimal.ZERO, op2.getPromocodeAmount());
        //assertEquals(0, calcFinal2.compareTo(op2.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount
    }

    @Test
    @Transactional
    public void _03_CategoryPromoCode_03_PercentHits2of2() {
        String promoCode = "_03_01_percent_category_code";
        BigDecimal value = new BigDecimal("0.10");
        //
        List<Product> products = getProductsForOrders();
        Map<Category, List<Product>> categoryMap = products.stream().collect(groupingBy(Product::getCategory));
        assertTrue(categoryMap.size() > 2);
        //
        List<Category> categoryList = new ArrayList<>(categoryMap.keySet());
        Product item1 = categoryMap.get(categoryList.get(0)).get(0);
        Product item2 = categoryMap.get(categoryList.get(1)).get(0);
        Product item3 = categoryMap.get(categoryList.get(2)).get(0);
        assertNotEquals(item1.getCategoryId(), item2.getCategoryId());
        assertNotEquals(item3.getCategoryId(), item1.getCategoryId());
        assertNotEquals(item3.getCategoryId(), item2.getCategoryId());
        //
        PromoCode promoCodeItem = findOrCreatePromocodeForTest(FractionalPromoCode.class, promoCode, value);
        promoCodeService.saveFilterValues(promoCodeItem, ApplyRuleFilterType.CATEGORY, Sets.newHashSet(item1.getCategory().getId(), item2.getCategory().getId()));
        commitAndStartNewTransaction();
        //
        OrderDTO order = fillCartCheckPromoAndHoldOrder(promoCode, Arrays.asList(item1, item2));
        //
        assertEquals(2, order.getItems().size());
        //
        OrderPositionDTO op1 = order.getItems().stream().filter(op -> item1.getId().equals(op.getProductId())).findFirst().orElseThrow(AssertionError::new);
        BigDecimal calcFinal1 = op1.getAmount().multiply(BigDecimal.valueOf(1).subtract(value));
        assertEquals(0, op1.getPromocodeAmount().compareTo(op1.getAmount().subtract(calcFinal1)));
        //assertEquals(0, calcFinal1.compareTo(op1.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount
        //
        OrderPositionDTO op2 = order.getItems().stream().filter(op -> item2.getId().equals(op.getProductId())).findFirst().orElseThrow(AssertionError::new);
        BigDecimal calcFinal2 = op2.getAmount().multiply(BigDecimal.valueOf(1).subtract(value));
        assertEquals(0, op2.getPromocodeAmount().compareTo(op2.getAmount().subtract(calcFinal2)));
        //assertEquals(0, calcFinal2.compareTo(op2.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount
    }

    @Test
    @Transactional
    public void _03_CategoryPromoCode_04_AmountHits0of2() {
        String promoCode = "_03_01_amount_category_code";
        BigDecimal value = BigDecimal.valueOf(2500);
        String errorText = "Скидка не найдена";
        //
        List<Product> products = getProductsForOrders();
        Map<Category, List<Product>> categoryMap = products.stream().collect(groupingBy(Product::getCategory));
        assertTrue(categoryMap.size() > 2);
        //
        List<Category> categoryList = new ArrayList<>(categoryMap.keySet());
        Product item1 = categoryMap.get(categoryList.get(0)).get(0);
        Product item2 = categoryMap.get(categoryList.get(1)).get(0);
        Product item3 = categoryMap.get(categoryList.get(2)).get(0);
        assertNotEquals(item1.getCategoryId(), item2.getCategoryId());
        assertNotEquals(item3.getCategoryId(), item1.getCategoryId());
        assertNotEquals(item3.getCategoryId(), item2.getCategoryId());
        //
        PromoCode promoCodeItem = findOrCreatePromocodeForTest(AbsolutePromoCode.class, promoCode, value);
        promoCodeService.saveFilterValues(promoCodeItem, ApplyRuleFilterType.CATEGORY, Collections.singleton(item3.getCategory().getId()));
        commitAndStartNewTransaction();
        //
        fillCart(Arrays.asList(item1, item2));
        //
        checkPromocodeFailsWithException(promoCode, errorText);
        //
        holdOrderWithPromocodeFailsException(promoCode, errorText);
    }

    @Test
    @Transactional
    public void _03_CategoryPromoCode_04_AmountHits1of2() {
        String promoCode = "_03_01_amount_category_code";
        BigDecimal value = BigDecimal.valueOf(2500);
        //
        List<Product> products = getProductsForOrders();
        Map<Category, List<Product>> categoryMap = products.stream().collect(groupingBy(Product::getCategory));
        assertTrue(categoryMap.size() > 2);
        //
        List<Category> categoryList = new ArrayList<>(categoryMap.keySet());
        Product item1 = categoryMap.get(categoryList.get(0)).get(0);
        Product item2 = categoryMap.get(categoryList.get(1)).get(0);
        Product item3 = categoryMap.get(categoryList.get(2)).get(0);
        assertNotEquals(item1.getCategoryId(), item2.getCategoryId());
        assertNotEquals(item3.getCategoryId(), item1.getCategoryId());
        assertNotEquals(item3.getCategoryId(), item2.getCategoryId());
        //
        PromoCode promoCodeItem = findOrCreatePromocodeForTest(AbsolutePromoCode.class, promoCode, value);
        promoCodeService.saveFilterValues(promoCodeItem, ApplyRuleFilterType.CATEGORY, Collections.singleton(item1.getCategory().getId()));
        commitAndStartNewTransaction();
        //
        OrderDTO order = fillCartCheckPromoAndHoldOrder(promoCode, Arrays.asList(item1, item2));
        //
        assertEquals(2, order.getItems().size());
        //
        OrderPositionDTO op1 = order.getItems().stream().filter(op -> item1.getId().equals(op.getProductId())).findFirst().orElseThrow(AssertionError::new);
        BigDecimal calcFinal1 = op1.getAmount().subtract(value);
        assertEquals(0, op1.getPromocodeAmount().compareTo(op1.getAmount().subtract(calcFinal1)));
        //assertEquals(0, calcFinal1.compareTo(op1.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount
        //
        OrderPositionDTO op2 = order.getItems().stream().filter(op -> item2.getId().equals(op.getProductId())).findFirst().orElseThrow(AssertionError::new);
        BigDecimal calcFinal2 = op2.getAmount();
        assertEquals(BigDecimal.ZERO, op2.getPromocodeAmount());
        //assertEquals(0, calcFinal2.compareTo(op2.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount
    }

    @Test
    @Transactional
    public void _03_CategoryPromoCode_04_AmountHits2of2() {
        String promoCode = "_03_01_amount_category_code";
        BigDecimal value = BigDecimal.valueOf(2500);
        //
        List<Product> products = getProductsForOrders();
        Map<Category, List<Product>> categoryMap = products.stream().collect(groupingBy(Product::getCategory));
        assertTrue(categoryMap.size() > 2);
        //
        List<Category> categoryList = new ArrayList<>(categoryMap.keySet());
        Product item1 = categoryMap.get(categoryList.get(0)).get(0);
        Product item2 = categoryMap.get(categoryList.get(1)).get(0);
        Product item3 = categoryMap.get(categoryList.get(2)).get(0);
        assertNotEquals(item1.getCategoryId(), item2.getCategoryId());
        assertNotEquals(item3.getCategoryId(), item1.getCategoryId());
        assertNotEquals(item3.getCategoryId(), item2.getCategoryId());
        //
        PromoCode promoCodeItem = findOrCreatePromocodeForTest(AbsolutePromoCode.class, promoCode, value);
        promoCodeService.saveFilterValues(promoCodeItem, ApplyRuleFilterType.CATEGORY, Sets.newHashSet(item1.getCategory().getId(), item2.getCategory().getId()));
        commitAndStartNewTransaction();
        //
        OrderDTO order = fillCartCheckPromoAndHoldOrder(promoCode, Arrays.asList(item1, item2));
        //
        assertEquals(2, order.getItems().size());
        //
        OrderPositionDTO op1 = order.getItems().stream().filter(op -> item1.getId().equals(op.getProductId())).findFirst().orElseThrow(AssertionError::new);
        BigDecimal calcPromo1 = op1.getAmount().multiply(value).divide(order.getClearAmount(), 2, RoundingMode.HALF_UP);
        assertEquals(0, op1.getPromocodeAmount().compareTo(calcPromo1));
        //assertEquals(0, calcFinal1.compareTo(op1.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount
        //
        OrderPositionDTO op2 = order.getItems().stream().filter(op -> item2.getId().equals(op.getProductId())).findFirst().orElseThrow(AssertionError::new);
        BigDecimal calcPromo2 = op2.getAmount().multiply(value).divide(order.getClearAmount(), 2, RoundingMode.HALF_UP);
        assertEquals(0, op2.getPromocodeAmount().compareTo(calcPromo2));
        //assertEquals(0, calcFinal2.compareTo(op2.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount
        //
        assertEquals(BigDecimal.ZERO.longValue(), value.subtract(op1.getPromocodeAmount()).subtract(op2.getPromocodeAmount()).longValue());
    }

    @Test
    @Transactional
    public void _04_SellerPromoCode_01_NotFoundForOtherSeller() {
        String promoCode = "_04_01_percent_seller_code";
        BigDecimal value = new BigDecimal("0.10");
        String errorText = "Скидка не найдена";
        List<Product> products = getProductsForOrders();
        PromoCode promoCodeItem = findOrCreatePromocodeForTest(FractionalPromoCode.class, promoCode, value);

        promoCodeService.saveFilterValues(promoCodeItem, ApplyRuleFilterType.SELLER, Collections.singleton(1L));
        commitAndStartNewTransaction();

        fillCart(Arrays.asList(products.get(0), products.get(1)));
        checkPromocodeFailsWithException(promoCode, errorText);
        holdOrderWithPromocodeFailsException(promoCode, errorText);
    }

    @Test
    @Transactional
    public void _04_SellerPromoCode_02_PercentHits2of2() {
        String promoCode = "_04_02_percent_seller_code";
        BigDecimal value = new BigDecimal("0.10");
        List<Product> products = getProductsForOrders().subList(0, 2);
        PromoCode promoCodeItem = findOrCreatePromocodeForTest(FractionalPromoCode.class, promoCode, value);

        promoCodeService.saveFilterValues(
                promoCodeItem,
                ApplyRuleFilterType.SELLER,
                Collections.singleton(sellerId)
        );
        commitAndStartNewTransaction();

        OrderDTO order = fillCartCheckPromoAndHoldOrder(promoCode, products);

        assertEquals(2, order.getItems().size());

        Product product1 = products.get(0);
        Product product2 = products.get(1);

        OrderPositionDTO op1 = order.getItems().stream()
                .filter(op -> product1.getId().equals(op.getProductId()))
                .findFirst()
                .orElseThrow(AssertionError::new);
        BigDecimal calcFinal1 = op1.getAmount().multiply(BigDecimal.valueOf(1).subtract(value));

        assertEquals(0, op1.getPromocodeAmount().compareTo(op1.getAmount().subtract(calcFinal1)));
        //assertEquals(0, calcFinal1.compareTo(op1.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount

        OrderPositionDTO op2 = order.getItems().stream()
                .filter(op -> product2.getId().equals(op.getProductId()))
                .findFirst()
                .orElseThrow(AssertionError::new);
        BigDecimal calcFinal2 = op2.getAmount().multiply(BigDecimal.valueOf(1).subtract(value));

        assertEquals(0, op2.getPromocodeAmount().compareTo(op2.getAmount().subtract(calcFinal2)));
        //assertEquals(0, calcFinal2.compareTo(op2.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount
    }

    @Test
    @Transactional
    public void _04_SellerPromoCode_03_AmountHits2of2() {
        String promoCode = "_04_01_amount_seller_code";
        BigDecimal value = BigDecimal.valueOf(2000);
        List<Product> products = getProductsForOrders().subList(0, 2);
        PromoCode promoCodeItem = findOrCreatePromocodeForTest(AbsolutePromoCode.class, promoCode, value);

        promoCodeService.saveFilterValues(
                promoCodeItem,
                ApplyRuleFilterType.SELLER,
                Collections.singleton(sellerId)
        );
        commitAndStartNewTransaction();

        OrderDTO order = fillCartCheckPromoAndHoldOrder(promoCode, products);

        assertEquals(2, order.getItems().size());

        Product product1 = products.get(0);
        Product product2 = products.get(1);

        OrderPositionDTO op1 = order.getItems().stream()
                .filter(op -> product1.getId().equals(op.getProductId()))
                .findFirst()
                .orElseThrow(AssertionError::new);
        BigDecimal calcPromo1 = op1.getAmount().multiply(value).divide(order.getClearAmount(), 2, RoundingMode.HALF_UP);

        assertEquals(0, op1.getPromocodeAmount().compareTo(calcPromo1));
        //assertEquals(0, calcFinal1.compareTo(op1.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount

        OrderPositionDTO op2 = order.getItems().stream()
                .filter(op -> product2.getId().equals(op.getProductId()))
                .findFirst()
                .orElseThrow(AssertionError::new);
        BigDecimal calcPromo2 = op2.getAmount().multiply(value).divide(order.getClearAmount(), 2, RoundingMode.HALF_UP);

        assertEquals(0, op2.getPromocodeAmount().compareTo(calcPromo2));
        //assertEquals(0, calcFinal2.compareTo(op2.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount
        assertEquals(
                BigDecimal.ZERO.longValue(),
                value.subtract(op1.getPromocodeAmount()).subtract(op2.getPromocodeAmount()).longValue()
        );
    }

    @Test
    @Transactional
    public void _05_ExceptSellerPromoCode_01_NotFoundForSeller() {
        String promoCode = "_04_ExceptSellerPromoCode_01_NotFoundForSeller";
        BigDecimal value = new BigDecimal("0.10");
        String errorText = "Скидка не найдена";
        List<Product> products = getProductsForOrders();
        PromoCode promoCodeItem = findOrCreatePromocodeForTest(FractionalPromoCode.class, promoCode, value);

        promoCodeService.saveFilterValues(promoCodeItem, ApplyRuleFilterType.EXCEPT_SELLER, Collections.singleton(sellerId));
        commitAndStartNewTransaction();

        fillCart(Arrays.asList(products.get(0), products.get(1)));
        checkPromocodeFailsWithException(promoCode, errorText);
        holdOrderWithPromocodeFailsException(promoCode, errorText);
    }

    @Test
    @Transactional
    public void _05_ExceptSellerPromoCode_02_PercentHits2of2() {
        String promoCode = "_05_ExceptSellerPromoCode_02_PercentHits2of2";
        BigDecimal value = new BigDecimal("0.10");
        List<Product> products = getProductsForOrders().subList(0, 2);
        PromoCode promoCodeItem = findOrCreatePromocodeForTest(FractionalPromoCode.class, promoCode, value);

        promoCodeService.saveFilterValues(
                promoCodeItem,
                ApplyRuleFilterType.EXCEPT_SELLER,
                Collections.singleton(1L)
        );
        commitAndStartNewTransaction();

        OrderDTO order = fillCartCheckPromoAndHoldOrder(promoCode, products);

        assertEquals(2, order.getItems().size());

        Product product1 = products.get(0);
        Product product2 = products.get(1);

        OrderPositionDTO op1 = order.getItems().stream()
                                    .filter(op -> product1.getId().equals(op.getProductId()))
                                    .findFirst()
                                    .orElseThrow(AssertionError::new);
        BigDecimal calcFinal1 = op1.getAmount().multiply(BigDecimal.valueOf(1).subtract(value));

        assertEquals(0, op1.getPromocodeAmount().compareTo(op1.getAmount().subtract(calcFinal1)));
        //assertEquals(0, calcFinal1.compareTo(op1.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount

        OrderPositionDTO op2 = order.getItems().stream()
                                    .filter(op -> product2.getId().equals(op.getProductId()))
                                    .findFirst()
                                    .orElseThrow(AssertionError::new);
        BigDecimal calcFinal2 = op2.getAmount().multiply(BigDecimal.valueOf(1).subtract(value));

        assertEquals(0, op2.getPromocodeAmount().compareTo(op2.getAmount().subtract(calcFinal2)));
        //assertEquals(0, calcFinal2.compareTo(op2.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount
    }

    @Test
    @Transactional
    public void _05_ExceptSellerPromoCode_03_AmountHits2of2() {
        String promoCode = "_05_ExceptSellerPromoCode_03_AmountHits2of2";
        BigDecimal value = BigDecimal.valueOf(2000);
        List<Product> products = getProductsForOrders().subList(0, 2);
        PromoCode promoCodeItem = findOrCreatePromocodeForTest(AbsolutePromoCode.class, promoCode, value);

        promoCodeService.saveFilterValues(
                promoCodeItem,
                ApplyRuleFilterType.EXCEPT_SELLER,
                Collections.singleton(1L)
        );
        commitAndStartNewTransaction();

        OrderDTO order = fillCartCheckPromoAndHoldOrder(promoCode, products);

        assertEquals(2, order.getItems().size());

        Product product1 = products.get(0);
        Product product2 = products.get(1);

        OrderPositionDTO op1 = order.getItems().stream()
                                    .filter(op -> product1.getId().equals(op.getProductId()))
                                    .findFirst()
                                    .orElseThrow(AssertionError::new);
        BigDecimal calcPromo1 = op1.getAmount().multiply(value).divide(order.getClearAmount(), 2, RoundingMode.HALF_UP);

        assertEquals(0, op1.getPromocodeAmount().compareTo(calcPromo1));
        //assertEquals(0, calcFinal1.compareTo(op1.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount

        OrderPositionDTO op2 = order.getItems().stream()
                                    .filter(op -> product2.getId().equals(op.getProductId()))
                                    .findFirst()
                                    .orElseThrow(AssertionError::new);
        BigDecimal calcPromo2 = op2.getAmount().multiply(value).divide(order.getClearAmount(), 2, RoundingMode.HALF_UP);

        assertEquals(0, op2.getPromocodeAmount().compareTo(calcPromo2));
        //assertEquals(0, calcFinal2.compareTo(op2.getFinalAmount())); // TODO: use it when we`ll fix FinalAmount
        assertEquals(
                BigDecimal.ZERO.longValue(),
                value.subtract(op1.getPromocodeAmount()).subtract(op2.getPromocodeAmount()).longValue()
        );
    }
}
