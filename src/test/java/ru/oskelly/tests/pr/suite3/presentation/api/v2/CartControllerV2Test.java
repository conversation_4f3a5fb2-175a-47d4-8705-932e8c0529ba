package ru.oskelly.tests.pr.suite3.presentation.api.v2;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.assertj.core.data.Offset;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.MultiValueMap;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.pr.common.bonuses.BonusesServiceTestConfiguration;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.component.TestApiConfiguration;
import su.reddot.domain.dao.JpaHibernateUtil;
import su.reddot.domain.dao.activity.ActivityTestRepository;
import su.reddot.domain.dao.address.AddressRepository;
import su.reddot.domain.dao.counterparty.CounterpartyRepository;
import su.reddot.domain.dao.discount.PromoCodeRepository;
import su.reddot.domain.dao.notification.NotificationRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.product.ProductItemRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.exception.OrderCreationException;
import su.reddot.domain.model.activity.Activity;
import su.reddot.domain.model.address.Address;
import su.reddot.domain.model.addressendpoint.AddressEndpoint;
import su.reddot.domain.model.counterparty.Counterparty;
import su.reddot.domain.model.counterparty.PhysCounterparty;
import su.reddot.domain.model.discount.AbsolutePromoCode;
import su.reddot.domain.model.discount.FractionalPromoCode;
import su.reddot.domain.model.discount.PromoCode;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.notification.order.LostCartNewbiesNotification;
import su.reddot.domain.model.notification.order.LostCartNotification;
import su.reddot.domain.model.notification.order.LostPaymentNotification;
import su.reddot.domain.model.order.*;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.size.Size;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.activity.ActivityService;
import su.reddot.domain.service.address.AddressService;
import su.reddot.domain.service.cart.ChangeCartEvent;
import su.reddot.domain.service.counterparty.CounterpartyService;
import su.reddot.domain.service.delivery.DeliveryCostService;
import su.reddot.domain.service.dto.AddressDTO;
import su.reddot.domain.service.dto.AddressEndpointDTO;
import su.reddot.domain.service.dto.CounterpartyDTO;
import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.dto.order.GroupedCart;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.dto.order.OrderPositionDTO;
import su.reddot.domain.service.notification.NotificationService;
import su.reddot.domain.service.task.ScheduledNotificationRunner;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.product.ProductService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.cashregister.Checkable;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.mindbox.MindboxApplicationTriggerEventSender;
import su.reddot.presentation.api.v2.Api2Response;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@Slf4j
@TestMethodOrder(MethodOrderer.MethodName.class)
@SpringBootTest(classes = {OskellyApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT, properties = "app.integration.mindbox.triggers.enabled=true")
@ContextConfiguration(classes = {BonusesServiceTestConfiguration.class})
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_03)
public class CartControllerV2Test extends AbstractSpringTest {
    @Autowired
    private TestApiConfiguration testApiConfiguration;
    @Value("${test.api.user-email}")
    private String email;
    @Value("${test.api.user-password}")
    private String password;
	@Value("${test.api.user-id}")
	private Long userId;
	@Value("${test.api.promo-code-absolute}")
	private String promoCodeAbsolute;
	@Value("${test.api.promo-code-absolute-value}")
	private Integer promoCodeAbsoluteValue;

    private Long seller1Id = 23L;
    private Long seller2Id = 26L;
	private Long seller3Id = 21L;

	private String deliveryAddressEndpointAddress = "Цветной бульвар, д.1";
	private String deliveryAddressEndpointPhone = "+79202341740";
	private String deliveryAddressEndpointaLastName = "Белых";

	private static String cntPassport = "3648 436261";
	private static String cntInnPhys = "*********900";
	private static String cntInnIp = "*********900";
	private static String cntInnJur = "*********9";
	private static String cntOGRN = "*********9011";
	private static String cntOGRNIP = "*********901234";
	private static String cntKPP = "*********";
	private static String cntCompanyForm = "ПАО";
	private static String cntCompanyName = "Оскелли Груп";
	private static String cntBik = "*********";
	private static String cntCorrespondingAccount = "*********90123456789";
	private static String cntPaymentAccount = "98765432109876543211";
	private static String cntFirstName = "Дмитрий";
	private static String cntPatronymicName = "Иванович";
	private static String cntLastName = "Белых";

	String deliveryComment = "Привезите быстрее!";

	private static int userCartSize = 0;
	private static int userCartItemsCount = 0;
	private static Long userCartId;

	private static Long orderId;

	private static Long hiddenProductId;

	private static Counterparty counterparty;

    @Autowired
    private ProductRepository productRepository;
	@Autowired
	private OrderRepository orderRepository;
	@Autowired
	private AddressRepository addressRepository;
	@Autowired
	ProductItemRepository productItemRepository;
	@Autowired
	NotificationRepository<Notification> notificationRepository;
	@Autowired
	ActivityTestRepository<Activity> activityTestRepository;
	//Выключаем старые офферы
	//@Autowired
	//OfferRepository offerRepository;
	@Autowired
	CounterpartyRepository counterpartyRepository;
	@Autowired
	PromoCodeRepository promoCodeRepository;

	@Autowired
	private AddressService addressService;
	@Autowired
	private UserService userService;
	@Autowired
	private CounterpartyService counterpartyService;
	//Выключаем старые офферы
	//@Autowired
	//private OfferService offerService;
	@Autowired
	private NotificationService notificationService;
	@Autowired
	private ActivityService activityService;
	@Autowired
	private ProductService productService;
	@Autowired
	private OrderService orderService;
	@Autowired
	private DeliveryCostService deliveryCostService;

	@Autowired
	private ScheduledNotificationRunner scheduledNotificationRunner;

	@Autowired
	private CartTestSupport cartTestSupport;

	@Autowired
	private ObjectMapper objectMapper;

	@MockBean
	MindboxApplicationTriggerEventSender mindboxApplicationTriggerEventSender;

	@Autowired
	private MessageSourceAccessor messageSourceAccessor;

	/**
	 * Категории, для которых устанавливается минимальный лимит суммы корзины по одному продавцу
	 */
	@Value("${app.cart.amount-limit-category-ids}")
	private List<Long> amountLimitCategoryIds;

	/**
	 * Конечная категория, для которой устанавливается минимальный лимит суммы корзины по одному продавцу
	 */
	@Value("${test.cart.amount-limit-category-example-id}")
	private Long amountLimitCategoryExampleId;

	@Value("${test.debug}")
	private boolean debug;

	/**
	 * Минимальный лимит суммы корзины по одному продавцу. Устанавливается только для категорий amountLimitCategoryIds
	 * Для остальных категорий лимитов нет
	 */
	@Value("${app.cart.amount-limit}")
	private Integer amountLimit;

	@Captor
	ArgumentCaptor<ChangeCartEvent> changeCartEventArgumentCaptor;

    static ApiV2Client apiV2Client;

    //id продавца -> список id товаров
    static Map<Long, List<Long>> productsToBuy = new HashMap<>();

    private static boolean initialized;

    private String getServiceUrl(){
        return testApiConfiguration.getServerUrl() + "/api/v2/cart";
    }

	private String getGroupedCartUrl(){
		return getServiceUrl();
	}

	private String getSimpleCartUrl(){
		return getServiceUrl() + "/simple";
	}

	private String getCartItemsUrl(){
		return getServiceUrl() + "/items";
	}

	protected String getHoldProcedureName() {
		return CartTestSupport.HOLD_V1_ENDPOINT;
	}

	private String getHoldUrl(){
		return getServiceUrl() + getHoldProcedureName();
	}

	private String getDeliveryAddressEndpointUrl(){ return getServiceUrl() + "/deliveryAddressEndpoint"; }

	private String getBuyerCounterpartyUrl() { return getServiceUrl() + "/buyerCounterparty"; }

	private String getDeliveryCommentUrl() { return getServiceUrl() + "/deliveryComment"; }

	private String getCheckPromoCodeUrl() { return getServiceUrl() + "/checkPromoCode"; }

	private String getUnfinishedOrdersUrl() { return testApiConfiguration.getServerUrl() + "/api/v2/orders/unfinished"; }

	private User getUser(){
    	return userService.getUserById(userId).orElse(null);
	}

	private Counterparty getCounterparty(){
    	if(counterparty == null) counterparty = counterpartyService.save(new PhysCounterparty().setPassport(cntPassport).setBik(cntBik)
				.setInn(cntInnPhys).setCorrespondentAccount(cntCorrespondingAccount).setPaymentAccount(cntPaymentAccount)
				.setPhysAddress(cartTestSupport.getDeliveryAddressEndpoint().getAddress()).setFirstName(cntFirstName).setLastName(cntLastName).setPatronymicName(cntPatronymicName)
    	        .setUser(getUser()));
		return counterparty;
	}

    private ResponseEntity<Api2Response<OrderDTO>> getSimpleCartResponse(boolean withAuthorizeParams){
        return apiV2Client.request(getSimpleCartUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<OrderDTO>>() {}, withAuthorizeParams);
    }
	private ResponseEntity<String> getGroupedCartResponse(){
		return apiV2Client.request(getGroupedCartUrl(), null, HttpMethod.GET, null, String.class, false);
	}
	private ResponseEntity<Api2Response<AddressEndpointDTO>> getDelivery(boolean withAuthorizeParams){
		return apiV2Client.request(getDeliveryAddressEndpointUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<AddressEndpointDTO>>() {}, withAuthorizeParams);
	}
	private ResponseEntity<Api2Response<CounterpartyDTO>> getBuyerCounterparty(boolean withAuthorizeParams){
		return apiV2Client.request(getBuyerCounterpartyUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<CounterpartyDTO>>() {}, withAuthorizeParams);
	}
	private ResponseEntity<Api2Response<String>> getDeliveryComment(boolean withAuthorizeParams){
		return apiV2Client.request(getDeliveryCommentUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<String>>() {}, withAuthorizeParams);
	}
	private AddressEndpointDTO getDeliverySuccessfull(boolean withAuthorizeParams){
		ResponseEntity<Api2Response<AddressEndpointDTO>> response = getDelivery(withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		return response.getBody().getData();
	}
	private CounterpartyDTO getBuyerCounterpartySuccessfull(boolean withAuthorizeParams){
		ResponseEntity<Api2Response<CounterpartyDTO>> response = getBuyerCounterparty(withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		return response.getBody().getData();
	}
	private String getDeliveryCommentSuccessful(boolean withAuthorizeParams){
		ResponseEntity<Api2Response<String>> response = getDeliveryComment(withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		return response.getBody().getData();
	}
    private OrderDTO getSimpleCartSuccessfull(boolean withAuthorizeParams){
        ResponseEntity<Api2Response<OrderDTO>> response = getSimpleCartResponse(withAuthorizeParams);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        return response.getBody().getData();
    }
	@SneakyThrows
	private GroupedCart getGroupedCartSuccessful() {
		ResponseEntity<String> response = getGroupedCartResponse();
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		//Проверяем наполнение корзины на наличие обязательных полей
		Api2Response<GroupedCart> body = objectMapper.readValue(response.getBody(), new TypeReference<Api2Response<GroupedCart>>(){});
		if(body.getData() != null) validateGroupedCart(body.getData());
		return body.getData();
	}

	//Проверка корзины на наличие обязательных полей
	private void validateGroupedCart(@NonNull GroupedCart groupedCart){
		for(OrderDTO group : groupedCart.getGroups()){
			validateGroup(group);
		}
	}

	//Проверка группы корзины на наличие обязательных полей
	private void validateGroup(@NonNull OrderDTO group){
		for(OrderPositionDTO item : group.getItems()){
			validateItem(item);
		}
	}

	//Проверка айтема корзины на наличие обязательных полей
	private void validateItem(@NonNull OrderPositionDTO item){
		assertNotNull(item.getCategoryId());
		assertNotNull(item.getCategoryName());
		assertNotNull(item.getBrandId());
		assertNotNull(item.getBrandName());
	}

	/*private OrderDTO checkPromoCodeSuccessful(Long sellerId, String promoCode){
		Map<String, String> params = TestUtils.getOneParamAsMap("sellerId", sellerId);
		params.put("promoCode", promoCode);
		ResponseEntity<Api2Response<OrderDTO>> response = apiV2Client.request(getCheckPromoCodeUrl(), params, HttpMethod.GET, null, null, new ParameterizedTypeReference<Api2Response<OrderDTO>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		return response.getBody().getData();
	}*/

	private boolean simpleCartContainsProduct(long productId){
		OrderDTO cart = getSimpleCartSuccessfull(false);
		if(cart == null || cart.getCount() == 0) return false;
		for(OrderPositionDTO item : cart.getItems()){
			if(item.getProductId() == productId) return true;
		}
		return false;
	}

	private boolean groupedCartContainsProduct(long productId){
		GroupedCart cart = getGroupedCartSuccessful();
		if(cart == null || cart.getCount() == 0) return false;
		for(OrderDTO group : cart.getGroups()){
			for(OrderPositionDTO item : group.getItems()){
				if(item.getProductId() == productId) return true;
			}
		}
		return false;
	}

    //Подсчет колиества товарной позиции в корзине
    private int getItemsCount(OrderDTO orderDTO, @NonNull Long productId, @NonNull Long sizeId){
    	if(orderDTO == null) return 0;
		int result = 0;
		List<OrderPositionDTO> items = orderDTO.getItems();
		for(OrderPositionDTO orderPositionDTO : items){
			if(orderPositionDTO.getProductId() == productId.longValue() && Objects.equals(sizeId, orderPositionDTO.getSize().getId())){
				//result += orderPositionDTO.getCount();
				result ++;
			}
		}
		return result;
    }
	/*GroupedCart addToCartSuccessfull(@NonNull Long productId, @NonNull Long sizeId, Integer count, boolean withAuthorizeParams){
		CartAddRequest request = new CartAddRequest().setProductId(productId).setSizeId(sizeId).setCount(count);
		MultiValueMap<String, Object> params = TestUtils.getMultivalueMapWithObjectFields(request);
		ResponseEntity<Api2Response<GroupedCart>> response = apiV2Client.request(getCartItemsUrl(), null, HttpMethod.PUT, MediaType.APPLICATION_FORM_URLENCODED, params, new ParameterizedTypeReference<Api2Response<GroupedCart>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		assertNotNull(response.getBody().getData());
		return response.getBody().getData();
	}*/
	void assertAddToCartFailed(@NonNull Long productId, @NonNull Long sizeId, Integer count, String expectedMessage, boolean withAuthorizeParams){
		CartAddRequest request = new CartAddRequest().setProductId(productId).setSizeId(sizeId).setCount(count);
		MultiValueMap<String, Object> params = TestUtils.getMultivalueMapWithObjectFields(request);
		ResponseEntity<String> response = apiV2Client.request(getCartItemsUrl(), null, HttpMethod.PUT, MediaType.APPLICATION_FORM_URLENCODED, params, new ParameterizedTypeReference<String>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is4xxClientError());
		assertTrue(response.getBody().contains(expectedMessage));
	}
    /*private GroupedCart addToCartSuccessfull(@NonNull Long productId, @NonNull Long sizeId, Integer count){
	    return addToCartSuccessfull(productId, sizeId, count, false);
    }*/
    /*private OrderService.InitOrderResult holdSuccessful(Long sellerId, boolean withAuthorizeParams){
	    ResponseEntity<Api2Response<OrderService.InitOrderResult>> response = apiV2Client.request(getHoldUrl(), TestUtils.getOneParamAsMap("sellerId", sellerId), HttpMethod.POST, MediaType.APPLICATION_FORM_URLENCODED, null, new ParameterizedTypeReference<Api2Response<OrderService.InitOrderResult>>() {}, withAuthorizeParams);
	    assertTrue(response.getStatusCode().is2xxSuccessful());
	    assertNotNull(response.getBody());
	    assertNotNull(response.getBody().getData());
	    return response.getBody().getData();
    }
	private OrderService.InitOrderResult holdWithPromoCodeSuccessful(Long sellerId, String promoCode, boolean withAuthorizeParams){
    	Map<String, String> params = TestUtils.getOneParamAsMap("sellerId", sellerId);
    	params.put("promoCode", promoCode);
		ResponseEntity<Api2Response<OrderService.InitOrderResult>> response = apiV2Client.request(getHoldUrl(), params, HttpMethod.POST, MediaType.APPLICATION_FORM_URLENCODED, null, new ParameterizedTypeReference<Api2Response<OrderService.InitOrderResult>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		assertNotNull(response.getBody().getData());
		return response.getBody().getData();
	}*/
	private void holdFailed(Long sellerId, String[] expectedSubstrings){
		ResponseEntity<String> response = apiV2Client.request(getHoldUrl(), TestUtils.getOneParamAsMap("sellerId", sellerId), HttpMethod.POST, MediaType.APPLICATION_FORM_URLENCODED, null, new ParameterizedTypeReference<String>() {}, false);
		assertTrue(response.getStatusCode().is4xxClientError());
		assertNotNull(response.getBody());
		for(String expectedSubstring : expectedSubstrings){
			assertTrue(response.getBody().contains(expectedSubstring));
		}
	}
    private List<Product> getProducts1ToBuy(Long sellerId){
    	return productRepository.findAllById(productsToBuy.get(sellerId));
    	/*if(productsToBuy.get(sellerId) != null) return productRepository.findAllById(productsToBuy.get(sellerId));
	    List<Product> products = productRepository.findProductsBySellerIdAndProductState(sellerId, ProductState.PUBLISHED).stream().filter(Product::hasAvailableProductItems).collect(Collectors.toList());
	    List<Long> productIds = products.stream().map(p -> p.getId()).collect(Collectors.toList());
	    productsToBuy.put(sellerId, productIds);
	    return products;
	    */
    }
    private List<Product> getProducts1ToBuy(){
    	return getProducts1ToBuy(seller1Id);
    }
	private List<Product> getProducts2ToBuy(){
		return getProducts1ToBuy(seller2Id);
	}
	private List<Product> getProducts3ToBuy(){
		return getProducts1ToBuy(seller3Id);
	}
    private GroupedCart cleanCartSuccessfull(){
        ResponseEntity<Api2Response<GroupedCart>> response = apiV2Client.request(getServiceUrl(), null, HttpMethod.DELETE, null, new ParameterizedTypeReference<Api2Response<GroupedCart>>() {}, false);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        assertNull(response.getBody().getData());
        return response.getBody().getData();
    }

    //Количество строк в корзине
    private int getSimpleCartSize(OrderDTO orderDTO){
    	return orderDTO == null ? 0 : orderDTO.getSize();
    }

    //Количество товаров в корзине с учетом количества
	private int getSimpleCartItemsCount(OrderDTO orderDTO){
		return orderDTO == null ? 0 : orderDTO.getCount();
		//return orderDTO == null ? 0 : 1;
	}

	//Количество строк в сгруппированной корзине
	private int getGroupedCartSize(GroupedCart groupedCart){
		return groupedCart == null ? 0 : groupedCart.getSize();
	}

	//Количество товаров в сгруппированной корзине с учетом количества
	private int getGroupedCartItemsCount(GroupedCart groupedCart){
		return groupedCart == null ? 0 : groupedCart.getCount();
		//return groupedCart == null ? 0 : 1;
	}

	/*private MultiValueMap<String, String> getDeliveryAddressEndpointParam(){
    	AddressEndpoint addressEndpoint = getDeliveryAddressEndpoint();
		return TestUtils.getOneParamAsMultiValueMap("deliveryAddressEndpointId", addressEndpoint.getId());
	}*/

	private MultiValueMap<String, String> getBuyerCounterpartyParam(){
		Counterparty counterparty = getCounterparty();
		return TestUtils.getOneParamAsMultiValueMap("buyerCounterpartyId", counterparty.getId());
	}

	private Order getLastDBOrder(){
    	return orderRepository.findTop10ByStateOrderByIdDesc(OrderState.CREATED).get(0);
	}

	private void assertLastOrderHasNoBuyer(){
		assertNull(getLastDBOrder().getBuyer());
	}

	public void assertLastOrderHasBuyerAndItsMe(){
		assertNotNull(getLastDBOrder().getBuyer());
		assertEquals(getUser().getId(), getLastDBOrder().getBuyer().getId());
	}

	private void setResidues(ProductItem productItem, int count){
    	productItem.setCount(count);
    	//productItemRepository.saveAndFlush(productItem);
	}

	private void checkLostCartNotification(LostCartNotification notification, Order order, int count){
		order = orderRepository.findById(order.getId()).orElse(null);
    	assertEquals(count, notification.getCounter().intValue());
    	List<OrderPosition> positions = order.getAvailableOrderPositions();
    	String expectedImageHint = positions.size() > 1 ? "+" + (positions.size() - 1) : null;
    	assertEquals(expectedImageHint, notification.getTargetObjectImageHint());
    	assertEquals("/cart", notification.getTargetObjectUrl());
		assertEquals("/imgs/notification/<EMAIL>", notification.getMainIcon());
		assertEquals("/imgs/notification/<EMAIL>", notification.getTinyIcon());
		assertNotNull(notification.getTargetObjectImage());
		if(count == 1){
			assertTrue(notification.getTitle().contains("Мы сохранили ваш выбор"));
			assertNull(notification.getSubTitle());
			assertTrue(notification.getBaseMessage().contains("Напоминаем о товарах в корзине. Успейте сделать заказ, пока товар есть в наличии."));
		}
		else{
			assertTrue(notification.getTitle().contains("Остался всего"));
			assertNull(notification.getSubTitle());
			assertTrue(notification.getBaseMessage().contains("Не откладывайте на завтра то, что можно купить со скидкой сегодня"));
		}
	}

	private void checkLostCartNewbyNotification(LostCartNewbiesNotification notification, Order order, int count){
		order = orderRepository.findById(order.getId()).orElse(null);
		assertEquals(count, notification.getCounter().intValue());
		List<OrderPosition> positions = order.getAvailableOrderPositions();
		String expectedImageHint = positions.size() > 1 ? "+" + (positions.size() - 1) : null;
		assertEquals(expectedImageHint, notification.getTargetObjectImageHint());
		assertEquals("/cart", notification.getTargetObjectUrl());
		assertEquals("/imgs/notification/<EMAIL>", notification.getMainIcon());
		assertEquals("/imgs/notification/<EMAIL>", notification.getTinyIcon());
		assertNotNull(notification.getTargetObjectImage());
		assertTrue(notification.getTitle().contains("Мы сохранили ваш выбор"));
		assertNull(notification.getSubTitle());
		assertTrue(notification.getBaseMessage().contains("Напоминаем о товарах в корзине. Успейте сделать заказ, пока товар есть в наличии."));
	}

	private void hideOrUnhideOrderPositions(Long orderId, boolean hide){
		Order order = orderRepository.findById(orderId).orElse(null);
		List<ProductItem> productItems = order.getOrderPositions().stream().map(op -> op.getProductItem()).distinct().collect(Collectors.toList());
		if(hide) {
			//Делаем позиции корзины недоступными
			int i = 0;
			for (ProductItem pi : productItems) {
				switch (i) {
					case 0:
						pi.setHidden(true);
						break;
					case 1:
						pi.setDeleteTime(LocalDateTime.now());
						break;
					case 2:
						pi.setCount(0);
						break;
					default:
						pi.getProduct().setProductState(ProductState.HIDDEN);
						break;
				}
				i++;
			}
		}
		else{
			for (ProductItem pi : productItems) {
				pi.setHidden(false);
				pi.setDeleteTime(null);
				pi.setCount(100);
				pi.getProduct().setProductState(ProductState.PUBLISHED);
			}
		}
	}

	@BeforeAll
	public static void setUp() {
		initialized = false;
		counterparty = null;
	}

	private void removeAgentCps(long userId) {
		List<Counterparty> allCps = userService.getUserById(userId).map(it -> it.getCounterparties())
				.orElseThrow(() -> new IllegalArgumentException("Unable to find CP list"));
		List<Counterparty> agtCps = allCps.stream()
				.filter(Counterparty::isAgent)
				.filter(it -> Objects.isNull(it.getDeleteTime()))
				.collect(Collectors.toList());
		agtCps.forEach(it -> {
			it.setDeleteTime(ZonedDateTime.now());
			counterpartyService.save(it);
		});
	}

	@Transactional
	@Test
	public void _00_0_init(){
    	if(initialized) return;

		apiV2Client = new ApiV2Client(email, password);

		cartTestSupport.setUserId(userId);
		cartTestSupport.setApiV2Client(apiV2Client);

    	cleanup();

		List<Product> products1 =  productRepository.findProductsBySellerIdAndProductState(seller1Id, ProductState.PUBLISHED).stream().limit(10).collect(Collectors.toList());
		List<Product> products2 =  productRepository.findProductsBySellerIdAndProductState(seller2Id, ProductState.PUBLISHED).stream().limit(10).collect(Collectors.toList());
		List<Product> products3 =  productRepository.findProductsBySellerIdAndProductState(seller3Id, ProductState.PUBLISHED).stream().limit(10).collect(Collectors.toList());
    	for(int i = 0; i < 10; i++) {
		    Product product1 = products1.get(i);
		    Product product2 = products2.get(i);
		    Product product3 = products3.get(i);
		    List<ProductItem> productItems1 = productItemRepository.findAllByProduct(product1);
		    List<ProductItem> productItems2 = productItemRepository.findAllByProduct(product2);
		    List<ProductItem> productItems3 = productItemRepository.findAllByProduct(product3);
		    for(ProductItem productItem1 : productItems1) {
		    	productItem1.setCount(100);
			    productItem1.setHidden(false);
			    productItem1.setDeleteTime(null);
		    }
		    for(ProductItem productItem2 : productItems2) {
			    productItem2.setCount(100);
			    productItem2.setHidden(false);
			    productItem2.setDeleteTime(null);
		    }
		    for(ProductItem productItem3 : productItems3) {
			    productItem3.setCount(100);
			    productItem3.setHidden(false);
			    productItem3.setDeleteTime(null);
		    }
	    }

		removeAgentCps(seller1Id);
		removeAgentCps(seller2Id);
		removeAgentCps(seller3Id);

	    commitTransaction();

	    List<Long> productIds1 = products1.stream().map(p -> p.getId()).collect(Collectors.toList());
		List<Long> productIds2 = products2.stream().map(p -> p.getId()).collect(Collectors.toList());
		List<Long> productIds3 = products3.stream().map(p -> p.getId()).collect(Collectors.toList());

		productsToBuy.put(seller1Id, productIds1);
		productsToBuy.put(seller2Id, productIds2);
		productsToBuy.put(seller3Id, productIds3);

    	initialized = true;
	}

	//Первый запрос без куки. На такие запросы приходит NULL
    @Test
    public void _00_1_unauthorizedNoCookieReturnsNull(){
	    ResponseEntity<Api2Response<OrderDTO>> response = getSimpleCartResponse(false);
	    assertTrue(response.getStatusCode().is2xxSuccessful());
	    assertNotNull(response.getBody());
	    assertNull(response.getBody().getData());
    }

    //Теперь куки есть, корзина должна возвращаться
    @Test
    @Transactional
    public void _02_getCartSuccessfull(){
        getSimpleCartSuccessfull(false);
    }

    @Test
    @Transactional
    public void _03_getClearCartSuccessfull() {
        GroupedCart cart = cleanCartSuccessfull();
        assertNull(cart);
    }

	@Test
	@Transactional
	public void _04_addToCartNullCountSuccessfull(){
		OrderDTO initialOrderDTO = getSimpleCartSuccessfull(false);
		assertTrue(deliveryCostService.getDefaultCost().compareTo(initialOrderDTO.getDeliveryCost()) == 0);
		assertNull(initialOrderDTO.getFinalAmount());
		assertNull(initialOrderDTO.getFinalAmountWithoutDeliveryCost());
		int initialCartSize = getSimpleCartSize(initialOrderDTO);
		Product product = getProducts1ToBuy().get(0);
		Size size = product.getAvailableProductItems().get(0).getSize();
		cartTestSupport.addToCartSuccessful(product.getId(), size.getId(), null);
		OrderDTO updatedOrderDTO = getSimpleCartSuccessfull(false);
		assertSame(initialCartSize + 1, getSimpleCartSize(updatedOrderDTO));
		assertLastOrderHasNoBuyer();

		verify(mindboxApplicationTriggerEventSender, times(1)).onChangeCartEvent(changeCartEventArgumentCaptor.capture());
		ChangeCartEvent changeCartEvent = changeCartEventArgumentCaptor.getValue();
		Assertions.assertThat(changeCartEvent.getOrderPositionDtos()).hasSize(1);
		ChangeCartEvent.OrderPositionDto orderPositionDto = changeCartEvent.getOrderPositionDtos().get(0);
		ProductItem addedItem = product.getProductItems().stream().filter(pi -> pi.getSize().getId().equals(size.getId())).findFirst().orElse(new ProductItem());
		Assertions.assertThat(orderPositionDto.getProductItemId()).isEqualTo(addedItem.getId());
		Assertions.assertThat(orderPositionDto.getItemPrice()).isCloseTo(
				updatedOrderDTO.getItems().get(0).getAmount().doubleValue(), Offset.offset(0.01D));
		Assertions.assertThat(orderPositionDto.getCount()).isEqualByComparingTo(1);
	}

	@Test
	@Transactional
	public void _05_addToCartSameProductAndSizeSuccessfull(){
		OrderDTO initialOrderDTO = getSimpleCartSuccessfull(false);
		int initialCartSize = getSimpleCartSize(initialOrderDTO);
		int initialCartItemsCount = getSimpleCartItemsCount(initialOrderDTO);
		Product product = getProducts1ToBuy().get(0);
		ProductItem productItem = product.getAvailableProductItems().get(0);
		//setResidues(productItem, 10);
		Size size = productItem.getSize();
		int count = 3;
		cartTestSupport.addToCartSuccessful(product.getId(), size.getId(), count);
		OrderDTO updatedOrderDTO = getSimpleCartSuccessfull(false);
		// При первом добавление позиции для OrderDTO.finalAmount так же должна учитываться сумма доставки
		BigDecimal deliveryCost = deliveryCostService.getDefaultCost();
		assertEquals(deliveryCost, updatedOrderDTO.getDeliveryCost());
		Assertions.assertThat(updatedOrderDTO.getClearAmount().add(deliveryCost)).isEqualByComparingTo(updatedOrderDTO.getFinalAmount());
		//Количество строк в простой корзине изменяется
		assertSame(initialCartSize + count, getSimpleCartSize(updatedOrderDTO));
		//Но изменяется общее количество айтемов в корзине
		assertSame(initialCartItemsCount + count, getSimpleCartItemsCount(updatedOrderDTO));
		assertLastOrderHasNoBuyer();

		verify(mindboxApplicationTriggerEventSender, times(1)).onChangeCartEvent(changeCartEventArgumentCaptor.capture());
		ChangeCartEvent changeCartEvent = changeCartEventArgumentCaptor.getValue();
		Assertions.assertThat(changeCartEvent.getOrderPositionDtos()).hasSize(1);
		ChangeCartEvent.OrderPositionDto orderPositionDto = changeCartEvent.getOrderPositionDtos().get(0);
		ProductItem addedItem = product.getProductItems().stream().filter(pi -> pi.getSize().getId().equals(size.getId())).findFirst().orElse(new ProductItem());
		Assertions.assertThat(orderPositionDto.getProductItemId()).isEqualTo(addedItem.getId());
		Assertions.assertThat(orderPositionDto.getItemPrice()).isCloseTo(
				updatedOrderDTO.getItems().get(0).getAmount().doubleValue(), Offset.offset(0.01D));
		Assertions.assertThat(orderPositionDto.getCount()).isEqualByComparingTo(initialCartSize + count);
	}

	@Test
	@Transactional
	public void _06_add5ItemsToCartNotNullCountSuccessfull(){
		OrderDTO initialOrderDTO = getSimpleCartSuccessfull(false);
		int initialCartSize = getSimpleCartSize(initialOrderDTO);
		int initialCartItemsCount = getSimpleCartItemsCount(initialOrderDTO);
		Product product = getProducts1ToBuy().get(1);
		Size size = product.getAvailableProductItems().get(0).getSize();
		int count = 5;
		cartTestSupport.addToCartSuccessful(product.getId(), size.getId(), count);
		OrderDTO updatedOrderDTO = getSimpleCartSuccessfull(false);
		// При последующих добавлениях позиций OrderDTO.finalAmount не должно повторно увеличиваться на сумму доставки
		BigDecimal deliveryCost = deliveryCostService.getDefaultCost();
		assertTrue(deliveryCost.compareTo(updatedOrderDTO.getDeliveryCost()) == 0);
		assertTrue(updatedOrderDTO.getClearAmount().add(deliveryCost).compareTo(updatedOrderDTO.getFinalAmount()) == 0);

		assertSame(initialCartSize + 5, getSimpleCartSize(updatedOrderDTO));
		assertSame(initialCartItemsCount + count, getSimpleCartItemsCount(updatedOrderDTO));
		assertLastOrderHasNoBuyer();
	}

	//На этом этапе в корзине должны быть только товары от одного продавца, т.е. должна фигурировать только одна группа
	@Test
	@Transactional
	public void _10_getGroupedCartWithOneSellerSuccessfull(){
		OrderDTO simpleOrderDTO = getSimpleCartSuccessfull(false);
		GroupedCart groupedCart = getGroupedCartSuccessful();
		assertSame(1, groupedCart.getGroups().size());
		assertLastOrderHasNoBuyer();
	}

	//После добавления товара от другого продавца у нас должно приходить в ответе 2 группы
	@Test
	@Transactional
	public void _11_addProductOfAnotherSellerSuccessfull(){
		Product product = getProducts2ToBuy().get(0);
		Size size = product.getAvailableProductItems().get(0).getSize();
		int count = 1;
		GroupedCart groupedCart = cartTestSupport.addToCartSuccessful(product.getId(), size.getId(), count);

		OrderDTO simpleOrderDTO = getSimpleCartSuccessfull(false);
		assertSame(2, groupedCart.getGroups().size());
		assertLastOrderHasNoBuyer();
	}

	@Test
	@Transactional
	public void _11_1_addPositionToOldCartDoesntChangeZeroDelivery() {
		Order lastOrder = getLastDBOrder();
		BigDecimal deliveryCost = lastOrder.getDeliveryCost();
		lastOrder.setDeliveryCost(null);
		orderRepository.saveAndFlush(lastOrder);

		commitTransaction();

		startNewTransaction();

		Product product = getProducts1ToBuy().get(0);
		Size size = product.getAvailableProductItems().get(0).getSize();
		int count = 1;
		cartTestSupport.addToCartSuccessful(product.getId(), size.getId(), count);
		OrderDTO simpleOrderDTO = getSimpleCartSuccessfull(false);
		Assertions.assertThat(simpleOrderDTO.getDeliveryCost()).isEqualTo(BigDecimal.ZERO);
		lastOrder = getLastDBOrder();
		lastOrder.setDeliveryCost(deliveryCost);
		orderRepository.saveAndFlush(lastOrder);

		commitTransaction();

		startNewTransaction();
	}


	/**
	 * Если в корзине нет доступных позиций, то уведомление LostCartNotification не создается
	 * Проверяем для неавторизованного пользователя (гостя) спустя 3 часа отсутствия активности
	 */
	@Test
	@Transactional
	@Rollback(false)
	public void _12_0_lostCartNotification_unauthorized_noAvailablePositions_not_send(){
		String guestToken = apiV2Client.getOskCookie();

		//Удалим все уведомления гостя из базы
		notificationRepository.deleteAll(notificationService.getRawNotifications(10000000, null, guestToken));

		long hoursLeft = 4;
		Order lastOrder = getLastDBOrder();
		lastOrder.setChangeTime(ZonedDateTime.now().minusHours(hoursLeft));
		orderRepository.saveAndFlush(lastOrder);

		//Делаем позиции корзины недоступными
		hideOrUnhideOrderPositions(lastOrder.getId(), true);

		commitTransaction();

		TestUtils.oldActivitiesUpdateTime(null, guestToken, (int) hoursLeft, activityTestRepository);

		activityService.saveAllFromQueue();

		//Проверяем брошенные корзины с рассылкой уведомлений
		scheduledNotificationRunner.checkLostOrders(180);

		startNewTransaction();

		lastOrder = getLastDBOrder();

		//Уведомление не появилось
		assertEquals(0, TestUtils.getNotificationsCount(null, guestToken, LostCartNotification.class, false, false, notificationService));

		//Возвращаем как было
		hideOrUnhideOrderPositions(lastOrder.getId(), false);

		commitTransaction();
	}

	/**
	 * Проверяем создание уведомления LostCartNotification для неавторизованного пользователя (гостя) спустя 3 часа отсутствия активности
	 */
	@Test
	@Transactional
	@Rollback(false)
	public void _12_1_lostCartNotification_unauthorized_OK(){
		String guestToken = apiV2Client.getOskCookie();

		//Проверяем брошенные корзины с рассылкой уведомлений
		scheduledNotificationRunner.checkLostOrders(180);

		commitAndStartNewTransaction();

		Order lastOrder = getLastDBOrder();
		//Проверяем уведомление по брошенной корзине
		LostCartNewbiesNotification notification = (LostCartNewbiesNotification) TestUtils.getLastNotification(guestToken, LostCartNewbiesNotification.class, false, false, notificationService);
		checkLostCartNewbyNotification(notification, lastOrder, 1);
		notificationRepository.deleteAll(notificationService.getRawNotifications(10000000, null, guestToken));
	}


	/*
	//TODO add test scenarios by state and transition test design for auth users and unAuth users
	@Test
	@Transactional
	public void _12_2_lostCartNotificationIsReadWhenProductWasSold(){
		checkLostCartNotification(ProductState.SOLD, 1, 11, false, false, false, false);
	}

	@Test
	@Transactional
	public void _12_2_lostCartNotificationIsNotReadWhenProductWasSold(){
		checkLostCartNotification(ProductState.SOLD, 1, 8, false, false, false, false);
	}

	@Test
	@Transactional
	public void _12_2_lostCartNotificationIsReadWhenProductWasDeleted(){
		checkLostCartNotification(ProductState.DELETED, 1, 11, false, false, false, false);
	}

	@Test
	@Transactional
	public void _12_2_lostCartNotificationIsNotReadWhenProductWasDeleted(){
		checkLostCartNotification(ProductState.DELETED, 1, 8, false, false, false, false);
	}

	@Test
	@Transactional
	public void _12_2_lostCartNotificationIsReadWhenProductWasHidden(){
		checkLostCartNotification(ProductState.HIDDEN, 1, 11, false, false, false, false);
	}

	@Test
	@Transactional
	public void _12_2_lostCartNotificationIsNotReadWhenProductWasHidden(){
		checkLostCartNotification(ProductState.HIDDEN, 1, 8, false, false, false, false);
	}

	@Test
	@Transactional
	public void _12_2_lostCartNotificationIsReadWhenProductWasSecondEdition(){
		checkLostCartNotification(ProductState.SECOND_EDITION, 1, 11, false, false, false, false);
	}

	@Test
	@Transactional
	public void _12_2_lostCartNotificationIsNotReadWhenProductWasSecondEdition(){
		checkLostCartNotification(ProductState.SECOND_EDITION, 1, 8, false, false, false, false);
	}

	@Test
	@Transactional
	public void _12_2_lostCartNotificationIsReadWhenProductWasNeedModeration(){
		checkLostCartNotification(ProductState.NEED_MODERATION, 1, 11, false, false, false, false);
	}

	@Test
	@Transactional
	public void _12_2_lostCartNotificationIsNotReadWhenProductWasNeedModeration(){
		checkLostCartNotification(ProductState.NEED_MODERATION, 1, 8, false, false, false, false);
	}

	@Test
	@Transactional
	public void _12_2_lostCartNotificationIsReadWhenProductWasNeedRetouch(){
		checkLostCartNotification(ProductState.NEED_RETOUCH, 1, 11, false, false, false, false);
	}

	@Test
	@Transactional
	public void _12_2_lostCartNotificationIsNotReadWhenProductWasNeedRetouch(){
		checkLostCartNotification(ProductState.NEED_RETOUCH, 1, 8, false, false, false, false);
	}

	@Test
	@Transactional
	public void _12_2_lostCartNotificationIsReadWhenProductWasRetouchDone(){
		checkLostCartNotification(ProductState.RETOUCH_DONE, 1, 11, false, false, false, false);
	}

	@Test
	@Transactional
	public void _12_2_lostCartNotificationIsNotReadWhenProductWasRetouchDone(){
		checkLostCartNotification(ProductState.RETOUCH_DONE, 1, 8, false, false, false, false);
	}

	@Test
	@Transactional
	public void _12_2_lostCartNotificationIsReadWhenProductWasRejected(){
		checkLostCartNotification(ProductState.REJECTED, 1, 11, false, false, false, false);
	}

	@Test
	@Transactional
	public void _12_2_lostCartNotificationIsNotReadWhenProductWasRejected(){
		checkLostCartNotification(ProductState.REJECTED, 1, 8, false, false, false, false);
	}

	@Test
	@Transactional
	public void _12_2_lostCartNotificationIsReadWhenProductWasDraft(){
		checkLostCartNotification(ProductState.DRAFT, 1, 11, false, false, false, false);
	}

	@Test
	@Transactional
	public void _12_2_lostCartNotificationIsNotReadWhenProductWasDraft(){
		checkLostCartNotification(ProductState.DRAFT, 1, 8, false, false, false, false);
	}

	@Test
	@Transactional
	public void _12_2_lostCartNotificationIsReadWhenProductSizeWasDeleted(){
		checkLostCartNotification(ProductState.PUBLISHED, 1, 11, true, false, false, false);
	}

	@Test
	@Transactional
	public void _12_2_lostCartNotificationIsNotReadWhenProductSizeWasDeleted(){
		checkLostCartNotification(ProductState.PUBLISHED, 1, 8, true, false, false, false);
	}

	@Test
	@Transactional
	public void _12_2_lostCartNotificationIsReadWhenProductSizeWasHidden(){
		checkLostCartNotification(ProductState.PUBLISHED, 1, 11, false, true, false, false);
	}

	@Test
	@Transactional
	public void _12_2_lostCartNotificationIsNotReadWhenProductSizeWasHidden(){
		checkLostCartNotification(ProductState.PUBLISHED, 1, 8, false, true, false, false);
	}

	@Test
	@Transactional
	public void _12_2_lostCartNotificationIsReadWhenProductSizeWasSold(){
		checkLostCartNotification(ProductState.PUBLISHED, 1, 11, false, false, true, false);
	}

	@Test
	@Transactional
	public void _12_2_lostCartNotificationIsNotReadWhenProductSizeWasSold(){
		checkLostCartNotification(ProductState.PUBLISHED, 1, 8, false, false, true, false);
	}

	@Test
	@Transactional
	public void _12_3_lostCartNotificationIsReadWhenProductWasDeleted(){
		checkLostCartNotificationStatusWhenAllProductsWasChanged(11, true);
}

	@Test
	@Transactional
	public void _12_3_lostCartNotificationIsNotReadWhenProductWasDeleted(){
		checkLostCartNotificationStatusWhenAllProductsWasChanged(8, false);
	}
	*/

	private void checkLostCartNotificationStatusWhenAllProductsWasChanged(int minutes, Boolean expectedReadCondition) {
		Order order = getLastDBOrder();
		User buyer = order.getBuyer();
		assert order != null;
		List<OrderPosition> positionsInCart = order.getOrderPositions();
		int cartSize = positionsInCart.size();
		System.out.println(cartSize);

		for (int i = 0; i < cartSize; i++) {
			ProductItem productItem = order.getOrderPositions().get(i).getProductItem();
			long prodItemId = productItem.getId();
			System.out.println("productItem = " + prodItemId);
			Product product = productItem.getProduct();
			long productId = product.getId();
			System.out.println("product = " + productId);
		}
		List<Long> productItemIds = positionsInCart.stream().map(p -> p.getProductItem().getId()).distinct().collect(Collectors.toList());
		List<Long> productIds = positionsInCart.stream().map(p -> p.getProductItem().getProduct().getId()).distinct().collect(Collectors.toList());
		List<ProductState> hiddenProductStates = Arrays.stream(ProductState.values()).filter(ps -> ps != ProductState.PUBLISHED).collect(Collectors.toList());
		for (int i = 0; i < productIds.size(); i++) {
			int productStateIndex = (int) (hiddenProductStates.size() * Math.random());
			Product product = productRepository.getOne(productIds.get(i));
			product.setProductState(hiddenProductStates.get(productStateIndex));
			product.setChangeTime(ZonedDateTime.now().minusMinutes(minutes));
			productRepository.save(product);
		}
		// закрываем транзакцию и открываем новую
		commitTransaction();
		scheduledNotificationRunner.closeNotifications(200);
		//На работу таска требуется время
		TestUtils.sleep(1);
		// открываем новую
		startNewTransaction();
		List<Notification> notifications = null;
		if (buyer == null) {
			notifications = notificationRepository.findAllByGuestTokenAndDtype(order.getGuestToken(),"LostCartNotification");
		} else {
			notificationRepository.findAllByUserAndDtype(buyer.getId(), "LostCartNotification");
		}
		LostCartNotification lostCartNotification = (LostCartNotification) notifications.get(0);
		boolean readComplete = lostCartNotification.isRead();
		System.out.println(readComplete);
		for (int i = 0; i < productIds.size(); i++) {
			Product product = productRepository.getOne(productIds.get(i));
			product.setProductState(ProductState.PUBLISHED);
			productRepository.save(product);
			commitAndStartNewTransaction();
		}
		for (Notification notification: getLostCartNotifications(order.getId())) {
			// возвращаем уведомлению непрочитанность.
			notification.setReadTime(null);
			//сохраняем уведомлениям непрочитанность
			notificationRepository.save(notification);
		}
		org.junit.jupiter.api.Assertions.assertEquals(expectedReadCondition, readComplete);
	}

	private void checkLostCartNotification(ProductState productState, int numberOfProductInCart, int minutes, boolean productItemDeleteTime, boolean productItemHidden, boolean productIteZeroCount,
										Boolean expectedRead) {
		Order order = getLastDBOrder();
		assert order != null;
		User buyer = order.getBuyer();
		List<Notification> notifications = null;
		if (buyer == null) {
			notifications = notificationRepository.findAllByGuestTokenAndDtype(order.getGuestToken(),"LostCartNotification");
		} else {
			notificationRepository.findAllByUserAndDtype(buyer.getId(), "LostCartNotification");
		}
		LostCartNotification lostCartNotification = (LostCartNotification) notifications.get(0);

		changeProductProperties(order, numberOfProductInCart, productState, minutes, productItemDeleteTime, productItemHidden, productIteZeroCount);
		commitTransaction();
		// запускаем раннер уведомлений, в котором закрываем уведомление (аргументом передаем уведомление как лист)
		scheduledNotificationRunner.closeNotifications(200);
		// На выполнение таска требуется время
		TestUtils.sleep(1);
		// закрываем транзакцию и открываем новую
		startNewTransaction();
		// получаем уведомление и передаем в него аргументом оффер
		ProductItem productItem = order.getOrderPositions().get(0).getProductItem();
		boolean readComplete = lostCartNotification.isRead();

		if (productItemDeleteTime || productItemHidden || productIteZeroCount) {
			revertOrderProductItem(order.getId(), productItem.getProduct().getId(), productItem.getId(), "LostCartNotification");
		} else {
			revertOrderProduct(order.getId(), productItem.getProduct().getId(), "LostCartNotification");
		}

		commitAndStartNewTransaction();
		org.junit.jupiter.api.Assertions.assertEquals(expectedRead, readComplete);
	}

	private List<Notification> getLostCartNotifications(long orderId) {
		List<Notification> resultList = new ArrayList<>();
		Order order = orderRepository.getOne(orderId);
		resultList.addAll(notificationRepository.findAllByGuestTokenAndDtype(order.getGuestToken(),"LostCartNotification"));
		return resultList;
	}

	//Если товар перестает быть доступным для покупки, то его больше не видно в корзине
	@Test
	@Transactional
	public void _13_unpublishedProductsAreHiddenInCart(){
		Long productId = getGroupedCartSuccessful().getGroups().get(0).getItems().get(0).getProductId();
		for(ProductState productState : ProductState.values()){
			if(productState == ProductState.PUBLISHED) continue;
			TestUtils.setProductState(productId, productState, productRepository);

			commitAndStartNewTransaction();

			assertFalse(simpleCartContainsProduct(productId));
			assertFalse(groupedCartContainsProduct(productId));
		}

		//Опубликованный товар вновь становится видимым
		TestUtils.setProductState(productId, ProductState.PUBLISHED, productRepository);

		commitAndStartNewTransaction();

		assertTrue(simpleCartContainsProduct(productId));
		assertTrue(groupedCartContainsProduct(productId));
	}

	//Предварительно создадим адресную точку вне основных тестов, чтобы она была доступна вне тестовых транзакций
	@Test
	public void _20_createAddressEndpointSuccessfull(){
		AddressEndpoint addressEndpoint = cartTestSupport.getDeliveryAddressEndpoint();
		assertNotNull(addressEndpoint);
		assertNotNull(addressEndpoint.getUser());
		assertNotNull(addressEndpoint.getUser().getId().equals(userId));
	}

	//Мы до сих пор неавторизованы. Запрос информации о пункте доставки недоступен
	@Test
	@Transactional
	public void _21_getAddressEndpointUnauthorizedUnSuccessfull(){
		ResponseEntity<String> response = apiV2Client.request(getDeliveryAddressEndpointUrl(), null, HttpMethod.GET, null, String.class, false);
		assertSame(HttpStatus.FORBIDDEN, response.getStatusCode());
		assertLastOrderHasNoBuyer();
	}

	//Так же недоступна возможность установить пункт доставки
	@Test
	@Transactional
	public void _22_setAddressEndpointUnauthorizedUnSuccessfull(){
		ResponseEntity<String> response = apiV2Client.request(getDeliveryAddressEndpointUrl(), null, HttpMethod.PUT, cartTestSupport.getDeliveryAddressEndpointParam(), String.class, false);
		assertSame(HttpStatus.FORBIDDEN, response.getStatusCode());
		assertLastOrderHasNoBuyer();
	}

	//Авторизуемся и сразу пытаемся получить точку доставки
	@Test
	@Transactional
	public void _23_getAddressEndpointAuthorizedSuccessfull(){
		AddressEndpointDTO deliveryAddressEndpointDTO = getDeliverySuccessfull(true);
		assertNull(deliveryAddressEndpointDTO);
		assertLastOrderHasBuyerAndItsMe();
	}

	private void setAddressEndpointAuthorizedSuccessful(){
		ResponseEntity<String> response = apiV2Client.request(getDeliveryAddressEndpointUrl(), null, HttpMethod.PUT, cartTestSupport.getDeliveryAddressEndpointParam(), String.class, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		TestUtils.assertStringContainsAllWords(response.getBody(), deliveryAddressEndpointaLastName, deliveryAddressEndpointAddress, deliveryAddressEndpointPhone);
		assertLastOrderHasBuyerAndItsMe();
	}

	//Теперь мы авторизованы и легко можем задать точку доставки
	@Test
	@Transactional
	public void _24_setAddressEndpointAuthorizedSuccessful(){
		AddressEndpoint addressEndpoint = cartTestSupport.getPricedDeliveryAddress();
		MultiValueMap<String, String> endpointParam = cartTestSupport.getDeliveryAddressEndpointParam(addressEndpoint);

		commitAndStartNewTransaction();

		ResponseEntity<Api2Response<AddressEndpointDTO>> response = apiV2Client.request(getDeliveryAddressEndpointUrl(), null, HttpMethod.PUT, endpointParam, new ParameterizedTypeReference<Api2Response<AddressEndpointDTO>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		Assertions.assertThat(response.getBody()).isNotNull();
		Assertions.assertThat(response.getBody().getData()).isNotNull();
		AddressDTO address = response.getBody().getData().getAddress();
		Assertions.assertThat(address).isNotNull();
		Assertions.assertThat(address.getCountry()).isEqualTo(addressEndpoint.getAddress().getCountryData().getName());
		Assertions.assertThat(address.getCity()).isEqualTo(addressEndpoint.getAddress().getCityData().getName());
		Assertions.assertThat(address.getAddress()).isEqualTo(addressEndpoint.getAddress().getAddress());

		Order lastOrderAfter = getLastDBOrder();
		Address addressAfter = lastOrderAfter.getDeliveryAddressEndpoint().getAddress();
		Assertions.assertThat(addressAfter).isNotNull();
		Assertions.assertThat(addressAfter.getCountry()).isEqualTo(addressEndpoint.getAddress().getCountryData().getName());
		Assertions.assertThat(addressAfter.getCity()).isEqualTo(addressEndpoint.getAddress().getCityData().getName());
		Assertions.assertThat(addressAfter.getAddress()).isEqualTo(addressEndpoint.getAddress().getAddress());

		//Еще раз убеждаемся, что теперь мы авторизованы и можем получить точку доставки
		AddressEndpointDTO deliveryAddressEndpointDTO = getDeliverySuccessfull(false);
		assertNotNull(deliveryAddressEndpointDTO);
		assertEquals(addressEndpoint.getAddress().getAddress(), deliveryAddressEndpointDTO.getAddress().getAddress());
		assertEquals(addressEndpoint.getAddress().getZipCode(), deliveryAddressEndpointDTO.getAddress().getZipCode());
		assertLastOrderHasBuyerAndItsMe();
	}

	//Устанавливаем комментарий к доставке
	@Test
	@Transactional
	public void _26_setDeliveryCommentSuccessful(){
		ResponseEntity<String> response = apiV2Client.request(getDeliveryCommentUrl(), null, HttpMethod.PUT, TestUtils.getOneParamAsMultiValueMap("comment", deliveryComment), String.class, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertTrue(response.getBody().contains(deliveryComment));
		String commentFromServer = getDeliveryCommentSuccessful(false);
		assertEquals(deliveryComment, commentFromServer);
		assertLastOrderHasBuyerAndItsMe();
	}


	//Бросаем этот заказ, затираем куки и получаем чистую гостевую корзину
	//При этом сохраняем размер брошенной корзины для последующих тестов
	@Test
	@Transactional
	public void _30_forgetCookieAndGetNewClearCart(){
		GroupedCart groupedCart = getGroupedCartSuccessful();
		userCartSize = getGroupedCartSize(groupedCart);
		userCartItemsCount = getGroupedCartItemsCount(groupedCart);
		Order userOrder = getLastDBOrder();
		userCartId = userOrder.getId();

		apiV2Client.clearCookies();

		//Сначала нам надо получить куку osk
		_00_1_unauthorizedNoCookieReturnsNull();

		//Теперь получаем новую пустую гостевую корзину
		OrderDTO updatedOrderDTO = getSimpleCartSuccessfull(false);
		assertNull(updatedOrderDTO);
	}

	//Снова добавляем товары в гостевую корзину
	@Test
	@Transactional
	public void _31_addToCartNotNullCountSuccessfull(){
		_06_add5ItemsToCartNotNullCountSuccessfull();
		assertLastOrderHasNoBuyer();
	}

	//Авторизуемся, добавляем еще один товар в корзину и убеждаемся в том, что
	//merge прошел успешно
	@Test
	@Transactional
	public void _32_addToCartAuthorizedMergeSuccessfull(){
		GroupedCart guestCart = getGroupedCartSuccessful();
		int guestCartSize = getGroupedCartSize(guestCart);
		int guestCartItemsCount = getGroupedCartItemsCount(guestCart);
		Order guestOrder = getLastDBOrder();

		Product product = getProducts2ToBuy().get(2);
		Size size = product.getAvailableProductItems().get(0).getSize();
		int count = 3;
		GroupedCart groupedCart = cartTestSupport.addToCartSuccessful(product.getId(), size.getId(), count, true);

		commitAndStartNewTransaction();

		Order lastOrder = getLastDBOrder();
		//Заказ уже другой, старый пользовательский
		assertNotEquals(guestOrder.getId(), lastOrder.getId());
		assertEquals(userCartId, lastOrder.getId());
		//Размер корзины увеличился на 1, т.к. добавили новый товар в гостевую корзину, а одинаковые товары при мердже слились
		assertSame(userCartSize + 1, getGroupedCartSize(groupedCart));
		//Количество айтемов в корзине увеличилось на 3 (добавленные 5 айтемов ничего не изменили: было 5, осталось 5)
		//debug
		if(debug && userCartItemsCount + count != getGroupedCartItemsCount(groupedCart)){
			System.out.println("Order id: " + lastOrder.getId());
			System.out.println(userCartItemsCount + count + " <> " + getGroupedCartItemsCount(groupedCart));
			System.exit(0);
		}
		assertSame(userCartItemsCount + count, getGroupedCartItemsCount(groupedCart));
	}

	//Выходим из акканута, но сохраняем куку osk
	//Содержимое корзины должно сохраниться и сама корзина остается той же
	@Test
	@Transactional
	public void _33_logoutAndGetNewClearCart(){
		GroupedCart userCart = getGroupedCartSuccessful();
		userCartSize = getGroupedCartSize(userCart);
		userCartItemsCount = getGroupedCartItemsCount(userCart);

		Order userOrder = getLastDBOrder();
		userCartId = userOrder.getId();

		apiV2Client.logout();

		//Теперь после выхода гость больше не видит корзины привязанной к пользователю.
		//Теперь корзина будет пустой
		ResponseEntity<Api2Response<OrderDTO>> response = getSimpleCartResponse(false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		assertNull(response.getBody().getData());
	}

	//Снова авторизуемся, добавляем еще один товар в корзину и убеждаемся в том, что мы работаем с тем же заказом
	@Test
	@Transactional
	public void _34_addToCartAuthorizedMergeSuccessfull(){
		Product product = orderRepository.findById(userCartId).orElse(null).getOrderPositions().get(0).getProductItem().getProduct();
		Size size = product.getAvailableProductItems().get(0).getSize();
		int count = 2;
		GroupedCart groupedCart = cartTestSupport.addToCartSuccessful(product.getId(), size.getId(), count, true);

		commitAndStartNewTransaction();

		Order lastOrder = getLastDBOrder();
		//Заказ тот же, пользовательский и гостевой одновременно
		assertEquals(userCartId, lastOrder.getId());
		//Размер корзины не увеличился, т.к. количество строк оталось прежним
		assertSame(userCartSize, getGroupedCartSize(groupedCart));
		//Количество айтемов в корзине увеличилось на count (т.к. count айтемов мы только что добавили)
		assertSame(userCartItemsCount + count, getGroupedCartItemsCount(groupedCart));
	}

	/**
	 * Проверяем создание уведомления LostCartNotification для авторизованного пользователя спустя 3 часа отсутствия активности
	 */
	@Test
	@Transactional
	@Rollback(false)
	public void _35_lostCartNotification_authorized_OK(){
		User buyer = getUser();

		//Удалим все уведомления покупателя из базы
		notificationRepository.deleteAll(notificationService.getRawNotifications(10000000, buyer));

		long hoursLeft = 4;
		Order lastOrder = getLastDBOrder();
		lastOrder.setChangeTime(ZonedDateTime.now(lastOrder.getCreateTime().getZone()).minusHours(hoursLeft));
		lastOrder.setLostOrderLastRemindTime(null);
		orderRepository.saveAndFlush(lastOrder);
		buyer.setLastAccessTime(LocalDateTime.now().minusHours(hoursLeft));
		userService.save(buyer);

		commitTransaction();

		//Проверяем брошенные корзины с рассылкой уведомлений
		scheduledNotificationRunner.checkLostOrders(180);

		startNewTransaction();

		//Проверяем уведомление по брошенной корзине
		LostCartNewbiesNotification notification = (LostCartNewbiesNotification) TestUtils.getLastNotification(buyer, LostCartNewbiesNotification.class, false, false, notificationService);
		checkLostCartNewbyNotification(notification, lastOrder, 1);
	}

	/**
	 * Снова запускаем поиск заброшенных корзин и на этот раз уведомления не будет, т.к. первое уведомление мы уже получили, а до второго время еще не дошло
	 */
	@Test
	@Transactional
	@Rollback(false)
	public void _36_lostCart_NO_Notification_authorized(){
		User buyer = getUser();

		//Удалим все уведомления покупателя из базы
		notificationRepository.deleteAll(notificationService.getRawNotifications(10000000, buyer));

		//Проверяем брошенные корзины с рассылкой уведомлений
		scheduledNotificationRunner.checkLostOrders(180);

		commitTransaction();

		//Проверяем уведомление по брошенной корзине
		assertEquals(0, TestUtils.getNotificationsCount(buyer, null, LostCartNotification.class, false, false, notificationService));
	}

	/**
	 * Проверяем создание уведомления LostCartNotification второй раз для авторизованного пользователя спустя 25 часов отсутствия активности
	 */
	@Test
	@Transactional
	@Rollback(false)
	public void _37_lostCartNotification_2_authorized_OK(){
		User buyer = getUser();

		//Удалим все уведомления покупателя из базы
		notificationRepository.deleteAll(notificationService.getRawNotifications(10000000, buyer));

		long hoursLeft = 25;
		Order lastOrder = getLastDBOrder();
		lastOrder.setChangeTime(ZonedDateTime.now().minusHours(hoursLeft));
		lastOrder.setLostOrderLastRemindTime(LocalDateTime.now().minusHours(hoursLeft - 3));
		lastOrder.setLostOrderRemindCounter(0);
		orderRepository.saveAndFlush(lastOrder);
		buyer.setLastAccessTime(LocalDateTime.now().minusHours(hoursLeft));
		userService.save(buyer);

		commitTransaction();

		//Проверяем брошенные корзины с рассылкой уведомлений
		scheduledNotificationRunner.checkLostOrders(180);

		startNewTransaction();

		//Проверяем уведомление по брошенной корзине
		LostCartNewbiesNotification notification = (LostCartNewbiesNotification) TestUtils.getLastNotification(buyer, LostCartNewbiesNotification.class, false, false, notificationService);
		checkLostCartNewbyNotification(notification, lastOrder, 1);
	}

	/**
	 * Прошло еще 4 часа. Снова запускаем поиск заброшенных корзин и на этот раз уведомления не будет, т.к. второе уведомление мы уже получили, а третье уведомление не предусмотрено не дошло, даже если была пользовательская активность
	 */
	@Test
	@Transactional
	@Rollback(false)
	public void _38_lostCart_NO_Notification_authorized(){
		User buyer = getUser();

		//Удалим все уведомления покупателя из базы
		notificationRepository.deleteAll(notificationService.getRawNotifications(10000000, buyer));

		long hoursLeft = 29;
		Order lastOrder = getLastDBOrder();
		lastOrder.setChangeTime(ZonedDateTime.now().minusHours(hoursLeft));
		lastOrder.setLostOrderLastRemindTime(LocalDateTime.now().minusHours(hoursLeft - 24));
		orderRepository.saveAndFlush(lastOrder);
		buyer.setLastAccessTime(LocalDateTime.now().minusHours(hoursLeft));
		userService.save(buyer);

		commitTransaction();

		//Проверяем брошенные корзины с рассылкой уведомлений
		scheduledNotificationRunner.checkLostOrders(60 * 24);

		//Проверяем уведомление по брошенной корзине
		assertEquals(0, TestUtils.getNotificationsCount(buyer, null, LostCartNotification.class, false, false, notificationService));
	}

	/**
	 * Но если добавить товар в корзину, то спустя 3 часа втова придет 1-е уведомление
	 */
	@Test
	@Transactional
	@Rollback(false)
	public void _39_lostCartNotification_1_authorized_OK(){
		User buyer = getUser();

		//Удалим все уведомления покупателя из базы
		notificationRepository.deleteAll(notificationService.getRawNotifications(10000000, buyer));

		//Добавляем еще одну позицию в корзину
		Product product = getProducts2ToBuy().get(3);
		Size size = product.getAvailableProductItems().get(0).getSize();
		int count = 1;
		GroupedCart groupedCart = cartTestSupport.addToCartSuccessful(product.getId(), size.getId(), count, true);

		long hoursLeft = 4;
		Order lastOrder = getLastDBOrder();
		lastOrder.setChangeTime(ZonedDateTime.now().minusHours(hoursLeft));
		orderRepository.saveAndFlush(lastOrder);
		buyer.setLastAccessTime(LocalDateTime.now().minusHours(hoursLeft));
		userService.save(buyer);

		commitTransaction();

		//Проверяем брошенные корзины с рассылкой уведомлений
		scheduledNotificationRunner.checkLostOrders(180);

		startNewTransaction();

		//Проверяем уведомление по брошенной корзине
		LostCartNewbiesNotification notification = (LostCartNewbiesNotification) TestUtils.getLastNotification(buyer, LostCartNewbiesNotification.class, false, false, notificationService);
		checkLostCartNewbyNotification(notification, lastOrder, 1);
	}



	//Предварительно создадим контрагента покупателя вне основных тестов, чтобы она был доступна вне тестовых транзакций
	@Test
	public void _40_createBuyerCounterpartySuccessfull(){
		Counterparty counterparty = getCounterparty();
		assertNotNull(counterparty);
		assertNotNull(counterparty.getUser());
		assertEquals(counterparty.getUser().getId(), userId);
	}

	//Снова выходим из аккаунта
	//Теперь мы неавторизованы. Запрос информации о контрагенте покупателя недоступен
	@Test
	@Transactional
	public void _41_getBuyerCounterpartyUnauthorizedUnSuccessfull(){
		apiV2Client.logout();
		ResponseEntity<String> response = apiV2Client.request(getBuyerCounterpartyUrl(), null, HttpMethod.GET, null, String.class, false);
		assertSame(HttpStatus.FORBIDDEN, response.getStatusCode());
	}

	//Так же недоступна возможность установить пункт доставки
	@Test
	@Transactional
	public void _42_setBuyerCounterpartyUnauthorizedUnSuccessfull(){
		ResponseEntity<String> response = apiV2Client.request(getBuyerCounterpartyUrl(), null, HttpMethod.PUT, getBuyerCounterpartyParam(), String.class, false);
		assertSame(HttpStatus.FORBIDDEN, response.getStatusCode());
	}

	//Авторизуемся и получаем контрагента, который само собой пока пустой
	@Test
	@Transactional
	public void _43_getBuyerCounterpartyAuthorizedSuccessfull(){
		CounterpartyDTO buyerCounterpartyDTO = getBuyerCounterpartySuccessfull(true);
		assertNull(buyerCounterpartyDTO);
		assertLastOrderHasBuyerAndItsMe();
	}

	//Теперь мы авторизованы и легко можем задать контрагента покупателя
	@Test
	@Transactional
	public void _44_setBuyerCounterpartyAuthorizedSuccessfull(){
    	MultiValueMap<String, String> buyerCounterpartyParam = getBuyerCounterpartyParam();
		ResponseEntity<String> response = apiV2Client.request(getBuyerCounterpartyUrl(), null, HttpMethod.PUT, buyerCounterpartyParam, String.class, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		TestUtils.assertStringContainsAllWords(response.getBody(), cntPassport, cntFirstName, cntInnPhys);
		assertLastOrderHasBuyerAndItsMe();
	}

	//Еще раз убеждаемся, что теперь мы авторизованы и можем получить контрагента покупателя
	@Test
	@Transactional
	public void _45_getBuyerCounterpartyAuthorizedSuccessfull(){
		CounterpartyDTO buyerCounterpartyDTO = getBuyerCounterpartySuccessfull(false);
		assertNotNull(buyerCounterpartyDTO);
		assertEquals(deliveryAddressEndpointAddress, buyerCounterpartyDTO.getPhysAddress().getAddress());
		assertEquals(cntPaymentAccount, buyerCounterpartyDTO.getPaymentAccount());
		assertLastOrderHasBuyerAndItsMe();
	}

	//Подтверждаем оффер на первую позицию в корзине, установив цену 0.9 от первоначальной
	//Затем убеждаемся, что стоимость данной позиции в корзине поменялась
	@Transactional
	@Test
	//Выключаем старые офферы
	//public void _50_confirmedOfferChangesPriceInTheCart(){
	public void _50_makeAnOrder(){
		commitTransaction();
    	double priceKoef = 0.9;

    	OrderDTO cartBeforeOffer = getSimpleCartSuccessfull(false);
		GroupedCart groupedCartBeforeOffer = getGroupedCartSuccessful();
    	OrderPositionDTO orderPositionBeforeOffer = cartBeforeOffer.getItems().get(0);
		Long productIdBeforeOffer = orderPositionBeforeOffer.getProductId();
		Long sizeIdBeforeOffer = orderPositionBeforeOffer.getSize().getId();

    	Long orderPositionSellerIdBeforeOffer = null;

    	for(OrderDTO orderDTO : groupedCartBeforeOffer.getGroups()){
    		OrderPositionDTO op = orderDTO.findItemById(productIdBeforeOffer, sizeIdBeforeOffer);
    		if(op != null){
			    orderPositionSellerIdBeforeOffer = orderDTO.getSeller().getId();
			    break;
		    }
	    }

		//Оформляем заказ по продавцу первой позиции
		OrderService.InitOrderResult holdResult = cartTestSupport.holdWithSetAddressEndpoint(getHoldProcedureName(), orderPositionSellerIdBeforeOffer, null);
		orderId = holdResult.getOrderId();

		verify(mindboxApplicationTriggerEventSender, times(1)).onChangeCartEvent(changeCartEventArgumentCaptor.capture());
		ChangeCartEvent changeCartEvent = changeCartEventArgumentCaptor.getValue();
		Assertions.assertThat(changeCartEvent.getOrderPositionDtos()).hasSize(2);
		Assertions.assertThat(changeCartEvent.getOrderPositionDtos().get(0).getCount()).isEqualTo(5);
		Assertions.assertThat(changeCartEvent.getOrderPositionDtos().get(1).getCount()).isEqualTo(5);
	}

	/**
	 * Если добавить товар в корзину, то спустя 3 часа придет 1-е уведомление
	 */
	@Test
	@Transactional
	@Rollback(false)
	public void _51_1_lostPaymentNotification_OK(){
		User buyer = getUser();

		//Проверяем незавершенные оплаты с рассылкой уведомлений
		scheduledNotificationRunner.checkLostPayments(30, 60);

		//Уведомления нет, т.к. не прошло достаточно времени
		assertEquals(0, TestUtils.getNotificationsCount(buyer, null, LostPaymentNotification.class, false, false, notificationService));

		//Удалим все уведомления покупателя из базы
		notificationRepository.deleteAll(notificationService.getRawNotifications(10000000, buyer));

		Order order = orderRepository.findById(orderId).orElse(null);

		//Меняем дату заказа на 35 минут назад
		order.setChangeTime(ZonedDateTime.now().minusMinutes(35));
		orderRepository.saveAndFlush(order);

		//Делаем позиции корзины недоступными
		hideOrUnhideOrderPositions(order.getId(), true);

		commitAndStartNewTransaction();

		//Проверяем незавершенные оплаты с рассылкой уведомлений
		scheduledNotificationRunner.checkLostPayments(30, 60);

		commitAndStartNewTransaction();

		//Уведомления нет, т.к. позиции недоступны для покупки
		assertEquals(0, TestUtils.getNotificationsCount(buyer, null, LostPaymentNotification.class, false, false, notificationService));

		//Делаем позиции корзины доступными
		hideOrUnhideOrderPositions(order.getId(), false);

		commitAndStartNewTransaction();

		//Проверяем незавершенные оплаты с рассылкой уведомлений
		scheduledNotificationRunner.checkLostPayments(30, 60);

		//Проверяем уведомление по незавершенной оплате
		LostPaymentNotification notification = (LostPaymentNotification) TestUtils.getLastNotification(buyer, LostPaymentNotification.class, false, false, notificationService);

		//Проверяем незавершенные оплаты с рассылкой уведомлений
		scheduledNotificationRunner.checkLostPayments(30, 60);

		//Больше уведомления не отправляются. На каждый заказ по одному.
		assertEquals(1, TestUtils.getNotificationsCount(buyer, null, LostPaymentNotification.class, false, false, notificationService));
	}

	/*
	@Transactional
	@Test
	public void _52_lostPaymentNotificationIsNotReadWhenProductWasSold() {
		checkOrderNotification(ProductState.SOLD, 1,8, false, false, false,
				"LostPaymentNotification", false);
	}

	@Transactional
	@Test
	public void _52_lostPaymentNotificationIsReadWhenProductWasSold() {
		checkOrderNotification(ProductState.SOLD, 1,11, false, false, false,
				"LostPaymentNotification",false);
	}


	@Transactional
	@Test
	public void _52_lostPaymentNotificationIsNotReadWhenProductWasDraft() {
		checkOrderNotification(ProductState.DRAFT, 1,8, false, false, false,
				"LostPaymentNotification", false);
	}

	@Transactional
	@Test
	public void _52_lostPaymentNotificationIsReadWhenProductWasDraft() {
		checkOrderNotification(ProductState.DRAFT, 1,11, false, false, false,
				"LostPaymentNotification", false);
	}

	@Transactional
	@Test
	public void _52_lostPaymentNotificationIsNotReadWhenProductWasDeleted() {
		checkOrderNotification(ProductState.DELETED, 1,8, false, false, false,
				"LostPaymentNotification", false);
	}

	@Transactional
	@Test
	public void _52_lostPaymentNotificationIsReadWhenProductWasDeleted() {
		checkOrderNotification(ProductState.DELETED, 1,11, false, false, false,
				"LostPaymentNotification", false);
	}

	@Transactional
	@Test
	public void _52_lostPaymentNotificationIsNotReadWhenProductWasNeedModeration() {
		checkOrderNotification(ProductState.NEED_MODERATION, 1,8, false, false, false,
				"LostPaymentNotification", false);
	}

	@Transactional
	@Test
	public void _52_lostPaymentNotificationIsReadWhenProductWasNeedModeration() {
		checkOrderNotification(ProductState.NEED_MODERATION,1, 11, false, false, false,
				"LostPaymentNotification", false);
	}

	@Transactional
	@Test
	public void _52_lostPaymentNotificationIsNotReadWhenProductWasNeedRetouch() {
		checkOrderNotification(ProductState.NEED_RETOUCH, 1,8, false, false, false,
				"LostPaymentNotification", false);
	}

	@Transactional
	@Test
	public void _52_lostPaymentNotificationIsReadWhenProductWasNeedRetouch() {
		checkOrderNotification(ProductState.NEED_RETOUCH, 1,11, false, false, false,
				"LostPaymentNotification", false);
	}

	@Transactional
	@Test
	public void _52_lostPaymentNotificationIsNotReadWhenProductWasRetouchDone() {
		checkOrderNotification(ProductState.RETOUCH_DONE,1, 8, false, false, false,
				"LostPaymentNotification", false);
	}

	@Transactional
	@Test
	public void _52_lostPaymentNotificationIsReadWhenProductWasRetouchDone() {
		checkOrderNotification(ProductState.RETOUCH_DONE, 1,11, false, false, false,
				"LostPaymentNotification", false);
	}

	@Transactional
	@Test
	public void _52_lostPaymentNotificationIsNotReadWhenProductWasSecondEdition() {
		checkOrderNotification(ProductState.SECOND_EDITION, 1,8, false, false, false,
				"LostPaymentNotification", false);
	}

	@Transactional
	@Test
	public void _52_lostPaymentNotificationIsReadWhenProductWasSecondEdition() {
		checkOrderNotification(ProductState.SECOND_EDITION, 1,11, false, false, false,
				"LostPaymentNotification", false);
	}

	@Transactional
	@Test
	public void _52_lostPaymentNotificationIsNotReadWhenProductWasRejected() {
		checkOrderNotification(ProductState.REJECTED, 1,8, false, false, false,
				"LostPaymentNotification", false);
	}

	@Transactional
	@Test
	public void _52_lostPaymentNotificationIsReadWhenProductWasRejected() {
		checkOrderNotification(ProductState.REJECTED, 1,11, false, false, false,
				"LostPaymentNotification", false);
	}

	@Transactional
	@Test
	public void _52_lostPaymentNotificationIsNotReadWhenProductWasHidden() {
		checkOrderNotification(ProductState.HIDDEN, 1,8, false, false, false,
				"LostPaymentNotification", false);
	}

	@Transactional
	@Test
	public void _52_lostPaymentNotificationIsReadWhenProductWasHidden() {
		checkOrderNotification(ProductState.HIDDEN, 1,11, false, false, false,
				"LostPaymentNotification", false);
	}



	@Transactional
	@Test
	public void _52_lostPaymentNotificationIsNotReadWhenProductSizeWasSold() {
		checkOrderNotification(ProductState.PUBLISHED, 1,8, false, false, true,
				"LostPaymentNotification", false);
	}

	@Transactional
	@Test
	public void _52_lostPaymentNotificationIsReadWhenProductSizeWasSold() {
		checkOrderNotification(ProductState.PUBLISHED, 1,11, false, false, true,
				"LostPaymentNotification", false);
	}

	@Transactional
	@Test
	public void _52_lostPaymentNotificationIsNotReadWhenProductSizeWasDeleted() {
		checkOrderNotification(ProductState.PUBLISHED, 1,8, true, false, false,
				"LostPaymentNotification", false);
	}

	@Transactional
	@Test
	public void _52_lostPaymentNotificationIsReadWhenProductSizeWasDeleted() {
		checkOrderNotification(ProductState.PUBLISHED, 1,11, true, false, false,
				"LostPaymentNotification",false);
	}

	@Transactional
	@Test
	public void _52_lostPaymentNotificationIsNotReadWhenProductSizeWasHidden() {
		checkOrderNotification(ProductState.PUBLISHED, 1,8, false, true, false,
				"LostPaymentNotification", false);
	}

	@Transactional
	@Test
	public void _52_lostPaymentNotificationIsReadWhenProductSizeWasHidden() {
		checkOrderNotification(ProductState.PUBLISHED, 1,11, false, true, false,
				"LostPaymentNotification", false);
	}

	@Transactional
	@Test
	public void _53_lostPaymentNotificationIsReadWhenAllProductsWasChanged() {
		checkLostPaymentNotificationStatus(11, true);
	}

	@Transactional
	@Test
	public void _53_lostPaymentNotificationIsNotReadWhenAllProductsWasChanged() {
		checkLostPaymentNotificationStatus(8, false);
	}
	*/

	private void checkLostPaymentNotificationStatus(int minutes, Boolean expectedStatus) {
		Order order = orderRepository.findById(orderId).orElse(null);
		User buyer = order.getBuyer();
		assert order != null;
		List<OrderPosition> positionsInCart = order.getOrderPositions();
		int cartSize = positionsInCart.size();
		System.out.println(cartSize);

		for (int i = 0; i < cartSize; i++) {
			ProductItem productItem = order.getOrderPositions().get(i).getProductItem();
			long prodItemId = productItem.getId();
			System.out.println("productItem = " + prodItemId);
			Product product = productItem.getProduct();
			long productId = product.getId();
			System.out.println("product = " + productId);
		}
		List<Long> productItemIds = positionsInCart.stream().map(p -> p.getProductItem().getId()).distinct().collect(Collectors.toList());
		List<Long> productIds = positionsInCart.stream().map(p -> p.getProductItem().getProduct().getId()).distinct().collect(Collectors.toList());
		List<ProductState> hiddenProductStates = Arrays.stream(ProductState.values()).filter(ps -> ps != ProductState.PUBLISHED).collect(Collectors.toList());
		for (int i = 0; i < productIds.size(); i++) {
			int productStateIndex = (int) (hiddenProductStates.size() * Math.random());
			Product product = productRepository.getOne(productIds.get(i));
			product.setProductState(hiddenProductStates.get(productStateIndex));
			product.setChangeTime(ZonedDateTime.now().minusMinutes(minutes));
			productRepository.save(product);
		}
		// Закрываем транзакцию
		commitTransaction();
		scheduledNotificationRunner.closeNotifications(200);
		// На выполнение таска требуется время
		TestUtils.sleep(1);
		// открываем новую транзакцию
		startNewTransaction();
		List<Notification> notifications = notificationRepository.findAllByUserAndDtype(buyer.getId(), "LostPaymentNotification");
		LostPaymentNotification lostPaymentNotification = (LostPaymentNotification) notifications.get(0);
		boolean readComplete = lostPaymentNotification.isRead();
		System.out.println(readComplete);
		for (int i = 0; i < productIds.size(); i++) {
			Product product = productRepository.getOne(productIds.get(i));
			product.setProductState(ProductState.PUBLISHED);
			productRepository.save(product);
			commitAndStartNewTransaction();
		}
		for (Notification notification: getOrderNotifications(orderId, "LostPaymentNotification")) {
			// возвращаем уведомлению непрочитанность.
			notification.setReadTime(null);
			//сохраняем уведомлениям непрочитанность
			notificationRepository.save(notification);
		}
		org.junit.jupiter.api.Assertions.assertEquals(expectedStatus, readComplete);
	}

	private void changeProductProperties(Order order, int numberOfProductInCart, ProductState productState, int changeTimeAgeMinute, boolean productItemDeleteTime, boolean productItemHidden, boolean productItemZeroCount) {
		ProductItem productItem = order.getOrderPositions().get(numberOfProductInCart - 1).getProductItem();
		Product product = productItem.getProduct();
		product.setProductState(productState);
		product.setChangeTime(ZonedDateTime.now().minusMinutes(changeTimeAgeMinute));
		productRepository.save(product);
		if (!productItemDeleteTime && !productItemHidden && !productItemZeroCount) {
			return;
		}
		if (productItemDeleteTime) {
			productItem.setDeleteTime(LocalDateTime.now().minusMinutes(changeTimeAgeMinute));
		}
		if (productItemHidden) {
			productItem.setHidden(true);
		}
		if (productItemZeroCount) {
			productItem.setCount(0);
		}
		productItem.setChangeTime(ZonedDateTime.now().minusMinutes(changeTimeAgeMinute));
		productItemRepository.save(productItem);
	}

	private void checkOrderNotification(ProductState productState, int numberOfProductInCart, int minutes, boolean productItemDeleteTime, boolean productItemHidden, boolean productIteZeroCount,
										String notificationType, Boolean expectedRead) {
		Order order = orderRepository.findById(orderId).orElse(null);
		User buyer = order.getBuyer();

		List<Notification> notifications = notificationRepository.findAllByUserAndDtype(buyer.getId(), "LostPaymentNotification");
		LostPaymentNotification lostPaymentNotification = (LostPaymentNotification) notifications.get(0);

		changeProductProperties(order, numberOfProductInCart, productState, minutes, productItemDeleteTime, productItemHidden, productIteZeroCount);
		commitTransaction();
		// запускаем раннер уведомлений, в котором закрываем уведомление (аргументом передаем уведомление как лист)
		scheduledNotificationRunner.closeNotifications(200);
		// На выполнение таска требуется время
		TestUtils.sleep(1);
		// закрываем транзакцию и открываем новую
		startNewTransaction();
		// получаем уведомление и передаем в него аргументом оффер
	//	List<Notification> notifications = notificationRepository.findAllByUserAndDtype(buyer.getId(), "LostPaymentNotification");
	//	LostPaymentNotification lostPaymentNotification = (LostPaymentNotification) notifications.get(0);


		ProductItem productItem = order.getOrderPositions().get(0).getProductItem();
		boolean readComplete = lostPaymentNotification.isRead();

		if (productItemDeleteTime || productItemHidden || productIteZeroCount) {
			revertOrderProductItem(order.getId(), productItem.getProduct().getId(), productItem.getId(), notificationType);
		} else {
			revertOrderProduct(order.getId(), productItem.getProduct().getId(), notificationType);
		}

		commitAndStartNewTransaction();
		org.junit.jupiter.api.Assertions.assertEquals(expectedRead, readComplete);
	}

	private void revertOrderProduct(long orderId, long productId, String notificationType) {
		Product product = productRepository.getOne(productId);
		for (Notification notification: getOrderNotifications(orderId, notificationType)) {
			// возвращаем уведомлению непрочитанность и невыполненность.
			notification.setReadTime(null);
			notification.setActionCompletedTime(null);
			//сохраняем уведомлениям непрочитанность и невыполненность
			notificationRepository.save(notification);
		}
		product.setProductState(ProductState.PUBLISHED);
		productRepository.save(product);
	}

	private void revertOrderProductItem(long orderId, long productId, long productItemId, String notificationType) {
		ProductItem productItem = productItemRepository.getOne(productItemId);
		productItem.setDeleteTime(null);
		productItem.setHidden(false);
		productItem.setCount(15);
		productItemRepository.save(productItem);
		revertOrderProduct(orderId, productId, notificationType);
	}

	private List<Notification> getOrderNotifications(long orderId, String notificationType) {
		List<Notification> resultList = new ArrayList<>();
		Order order = orderRepository.getOne(orderId);
		if (notificationType.equals("LostPaymentNotification")) {
			resultList.addAll(notificationRepository.findAllByUserAndDtype(order.getBuyer().getId(), "LostPaymentNotification"));
		} else {
			resultList.addAll(notificationRepository.findAllByGuestTokenAndDtype(order.getGuestToken(),"LostCartNotification"));
		}
		return resultList;
	}

	//Добавляем несколько товаров от 3-го продавца и снова получаем 2 группы
	@Test
	@Transactional
	public void _60_addProductOf3dSellerSuccessfull(){
		GroupedCart groupedCart = null;
		for(int i = 0; i < 3; i ++){
			Product product = getProducts3ToBuy().get(i);
			Size size = product.getAvailableProductItems().get(0).getSize();
			groupedCart = cartTestSupport.addToCartSuccessful(product.getId(), size.getId(), 1);
		}
		assertSame(2, groupedCart.getGroups().size());
	}

	//Скрываем с продажи 2-й товар 3-го продавца и оформляем заказ. Данный товар не попадает в заказ, а остается скрытым в корзине.
	@Test
	@Transactional
	public void _61_hideProductAndCreateOrder(){
		Product hiddenProduct = getProducts3ToBuy().get(1);
		hiddenProductId = hiddenProduct.getId();
		TestUtils.setProductState(hiddenProductId, ProductState.HIDDEN, productRepository);

		commitTransaction();

		//Устанавливаем комментарий доставки
		apiV2Client.request(getDeliveryCommentUrl(), null, HttpMethod.PUT,
				TestUtils.getOneParamAsMultiValueMap("comment", deliveryComment), String.class, false);

		//Оформляем заказ по 3-му продавцу
		OrderService.InitOrderResult holdResult = cartTestSupport.holdWithSetAddressEndpoint(getHoldProcedureName(), seller3Id, null);
		Long subOrderId = holdResult.getOrderId();

		startNewTransaction();

		Order subOrder = orderRepository.findById(subOrderId).orElse(null);

		//Комментарий к доставке сохранился в подзаказе
		assertEquals(deliveryComment, subOrder.getDeliveryComment());

		//Убеждаемся, что скрытый товар не попал в подзаказ
		assertEquals(2, subOrder.getOrderPositions().size());
		for(OrderPosition orderPosition : subOrder.getOrderPositions()){
			assertNotEquals(hiddenProductId, orderPosition.getProductItem().getProduct().getId());
		}

		//Проверяем с какого устройства был инициирован hold
		//activityService.saveAllFromQueue();
		//Class<? extends Device> holdDevice = orderService.getInitHoldActivityDevice(subOrder.getId());
		//assertSame(OskellyApiTestDevice.class, holdDevice);
	}

	//Оформляем заказ по последней оставшейся группе и убеждаемся, что туда не попадает скрытый товар 3-го продавца.
	//Корзина при этом становится пустой.
	@Transactional
	@Test
	public void _62_0_createFinalOrderAndCheckHiddenPhoduct(){
		commitTransaction();
		GroupedCart cart = getGroupedCartSuccessful();
		assertEquals(1, cart.getGroups().size());

		Long sellerId = cart.getGroups().get(0).getSeller().getId();
		//Оформляем заказ по последнему продавцу
		OrderService.InitOrderResult holdResult = cartTestSupport.holdWithSetAddressEndpoint(getHoldProcedureName(), sellerId, null);
		Long subOrderId = holdResult.getOrderId();

		startNewTransaction();

		Order subOrder = orderRepository.findById(subOrderId).orElse(null);

		//Убеждаемся, что скрытый товар не попал в подзаказ
		for(OrderPosition orderPosition : subOrder.getOrderPositions()){
			assertNotEquals(hiddenProductId, orderPosition.getProductItem().getProduct().getId());
		}
		commitTransaction();

		//Убеждаемся, что теперь корзина пуста
		GroupedCart groupedCart = getGroupedCartSuccessful();
		OrderDTO simpleCart = getSimpleCartSuccessfull(false);
		assertNull(groupedCart);
		assertNull(simpleCart);
	}

	//Получаем незавершеные (неоплаченные) заказы.
	@Test
	public void _62_1_getUnfinishedOrders(){
		List<OrderDTO> unfinishedOrders = OrderControllerApiV2Test.loadListSuccessfull(getUnfinishedOrdersUrl(), apiV2Client, false);
		//Оформляли 3 заказа
		assertEquals(3, unfinishedOrders.size());
		for(OrderDTO order : unfinishedOrders){
			assertUnfinishedOrderIsValid(order);
		}
	}

	private void assertUnfinishedOrderIsValid(OrderDTO order){
		//Незавершенный заказ не должен содержать цепочку статусов orderStepChain
		assertNull(order.getOrderStepChain());
	}

	@Transactional
	@Test
	public void _63_addToCartTooManyPositions(){
		//Добавление слишком большого количества позиций в корзину приводит к ошибке
		int cartLimit = 50;
		String errorMessage = "Превышен максимальный размер корзины: " + cartLimit;

		Product product = getProducts1ToBuy().get(0);
		ProductItem productItem = product.getAvailableProductItems().get(0);
		Size size = productItem.getSize();

		commitTransaction();

		//Сейчас корзина пуста, можно добавить cartLimit позиций
		cartTestSupport.addToCartSuccessful(product.getId(), size.getId(), cartLimit, true);
		//Добавление еще хотя бы одной позиции вернет ошибку
		assertAddToCartFailed(product.getId(), size.getId(), 1, errorMessage, false);

		//Чистим корзину
		cleanCartSuccessfull();

		//Теперь с первой попытки пытаемся положить слишком много в пустую корзину и сразу получаем ошибку
		assertAddToCartFailed(product.getId(), size.getId(), cartLimit + 1, errorMessage, false);

		//Чистим корзину, дабы не оставлять в базе
		cleanCartSuccessfull();
	}

	private void setProductPrice(Product product, int price){
		BigDecimal currentPrice = new BigDecimal(price);
		BigDecimal currentPriceWithoutCommission = currentPrice.multiply(new BigDecimal(0.7));
		product.setCurrentPrice(currentPrice);
		product.setCurrentPriceWithoutCommission(currentPriceWithoutCommission);
		productRepository.save(product);
	}

	private void setProductCategory(Product product, long categoryId){
		product.setCategoryId(categoryId);
		productRepository.save(product);
	}

	/**
	 * Нельзя создать заказ по лимитированным категориям, если общая сумма ниже лимита
	 */
	@Transactional
	@Test
	public void _64_0_createOrderWithLimitedAmountFailed(){
		Product smallPriceProduct;

		//Найдем товары из лимитированных категорий (по которым нельзя оформлять заказ менее 5000р)
		ProductService.FilterSpecification spec = new ProductService.FilterSpecification().categoriesIds(amountLimitCategoryIds);
		Page<Product> productsPage = productService.getRawProducts(spec, 1, ProductService.SortAttribute.CHANGE_TIME_DESC, null, ProductService.UserType.HUMAN);

		if(productsPage.getItems().isEmpty()){
			log.info("Нет товаров для проведения теста. Возьмем из другой категории.");
			productsPage = productService.getRawProducts(new ProductService.FilterSpecification(), 1, ProductService.SortAttribute.CHANGE_TIME_DESC, null, ProductService.UserType.HUMAN);
			smallPriceProduct = productsPage.getItems().get(0);
			//Назначаем товару категорию с ограничением по корзине
			setProductCategory(smallPriceProduct, amountLimitCategoryExampleId);
		}
		else {
			smallPriceProduct = productsPage.getItems().get(0);
		}
		setProductPrice(smallPriceProduct, 100);

		commitAndStartNewTransaction();

		smallPriceProduct = productRepository.findById(smallPriceProduct.getId()).orElse(null);

		Long smallPriceSellerId = smallPriceProduct.getSeller().getId();

		//Добавляем дешевый товар в корзину
		GroupedCart groupedCart = cartTestSupport.addToCartSuccessful(smallPriceProduct.getId(), smallPriceProduct.getAvailableProductItems().get(0).getSize().getId(), 1, true);

		//Получаем группу по продавцу дешевого товара
		OrderDTO smallPriceGroup = groupedCart.getGroup(smallPriceSellerId);

		//Имеются предупреждения о невозможности создать заказ, т.к. суммы недостаточно
		assertNotNull(smallPriceGroup.getOrderCreationProblems());
		assertFalse(smallPriceGroup.getOrderCreationProblems().isEmpty());
		//Тип проблемы - недостаточная сумма
		assertEquals(OrderCreationProblem.NOT_ENOUGH_AMOUNT, smallPriceGroup.getOrderCreationProblems().get(0).getType());
		//В поле data идет минимальная сумма заказа
		assertEquals("" + amountLimit, "" + smallPriceGroup.getOrderCreationProblems().get(0).getData());

		//Пытаемся оформить заказ по совершенно левому продавцу
		holdFailed(-1L, new String[] {OrderCreationException.class.getSimpleName(), "Позиции по указанному продавцу не найдены: -1"});

		//Пытаемся оформить заказ по продавцу дешевого товара
		holdFailed(smallPriceSellerId, new String[] {OrderCreationException.class.getSimpleName(), "Заказ не может быть оформлен", OrderCreationProblem.NOT_ENOUGH_AMOUNT.name(), "" + amountLimit});

		//Чистим корзину, дабы не оставлять в базе
		cleanCartSuccessfull();
	}

	/**
	 * Заказ по лимитированным категориям можно оформить, если сумма до применения промокода превышает минимальную сумму,
	 * даже если с промокодом сумма ниже порога
	 */
	@Transactional
	@Test
	public void _64_1_createOrderWithLimitedAmountPromocodeSuccessful(){

		//Найдем товары из лимитированных категорий
		ProductService.FilterSpecification spec = new ProductService.FilterSpecification().categoriesIds(amountLimitCategoryIds);
		Page<Product> productsPage = productService.getRawProducts(spec, 1, ProductService.SortAttribute.CHANGE_TIME_DESC, null, ProductService.UserType.HUMAN);

		if(productsPage.getItems().isEmpty()){
			log.info("Нет товаров для проведения теста.");
			return;
		}
		Product product = productsPage.getItems().get(0);

		setProductPrice(product, amountLimit + amountLimit / 100); //Сумма на 1% больше лимита

		commitAndStartNewTransaction();

		product = productRepository.findById(product.getId()).orElse(null);

		Long sellerId = product.getSeller().getId();

		//Добавляем товар в корзину
		GroupedCart groupedCart = cartTestSupport.addToCartSuccessful(product.getId(), product.getAvailableProductItems().get(0).getSize().getId(), 1, true);

		//Получаем группу по продавцу
		OrderDTO group = groupedCart.getGroup(sellerId);

		//Отсутствует предупреждение о невозможности создать заказ, т.к. суммы достаточно (можно оформить заказ)
		assertNull(group.getOrderCreationProblems());

		//При необъодимости создаем промокод
		AbsolutePromoCode promoCode = (AbsolutePromoCode) promoCodeRepository.findFirstByCode(promoCodeAbsolute).stream().findFirst().orElse(null);
		if(promoCode == null) {
			promoCode = new AbsolutePromoCode().setValue(new BigDecimal(promoCodeAbsoluteValue));
			promoCode.setCode(promoCodeAbsolute).setBeginPrice(new BigDecimal(1)).setCreatedAt(ZonedDateTime.now())
				.setExpiresAt(ZonedDateTime.now().plusYears(100))
				.setStartsAt(ZonedDateTime.now());
			promoCodeRepository.save(promoCode);
			commitAndStartNewTransaction();
		}

		//Проверяем промокод
		OrderDTO checkPromoCodeResult = cartTestSupport.checkPromoCode(sellerId, promoCodeAbsolute);
		//Базовая сумма выше предела по корзине
		assertTrue(checkPromoCodeResult.getClearAmount().longValue() > amountLimit);

		//В группе идет информация о скидке
		assertNotNull(checkPromoCodeResult.getDiscount());
		//Итоговая сумма со скидкой ниже предела по корзине
		assertTrue(checkPromoCodeResult.getDiscount().resultAmount < amountLimit);
		//Проблем с оформлением заказа нет (заказ можно оформить)
		assertNull(checkPromoCodeResult.getOrderCreationProblems());

		//Устанавливаем адресную точку для возможности оформления заказа
		setAddressEndpointAuthorizedSuccessful();

		//Успешно оформляем заказ с промокодом
		OrderService.InitOrderResult initOrderResult = cartTestSupport.holdWithPromoCodeSuccessful(getHoldProcedureName(), sellerId, promoCodeAbsolute, false);

		commitAndStartNewTransaction();

		//Находим этот заказ в базе
		Order order = orderRepository.findById(initOrderResult.getOrderId()).orElse(null);
		//Заказ имеет сумму ниже лимита корзины
		assertTrue(order.getAmount().longValue() < amountLimit);

		//Чистим корзину, дабы не оставлять в базе
		cleanCartSuccessfull();
	}

	/**
	 * Заказ по лимитированным категориям можно оформить, если сумма до оффера превышает минимальную сумму,
	 * даже если с оффером сумма ниже порога
	 */
	@Transactional
	//Выключаем старые офферы
	//@Test
	public void _64_2_createOrderWithLimitedAmountOfferSuccessful(){

		//Найдем товары из лимитированных категорий
		ProductService.FilterSpecification spec = new ProductService.FilterSpecification().categoriesIds(amountLimitCategoryIds);
		Page<Product> productsPage = productService.getRawProducts(spec, 1, ProductService.SortAttribute.CHANGE_TIME_DESC, null, ProductService.UserType.HUMAN);

		if(productsPage.getItems().isEmpty()){
			log.info("Нет товаров для проведения теста.");
			return;
		}
		Product product = productsPage.getItems().get(0);

		setProductPrice(product, amountLimit + amountLimit / 100); //Сумма на 1% больше лимита

		commitAndStartNewTransaction();

		product = productRepository.findById(product.getId()).orElse(null);

		Long sellerId = product.getSeller().getId();

		//Добавляем товар в корзину
		GroupedCart groupedCart = cartTestSupport.addToCartSuccessful(product.getId(), product.getAvailableProductItems().get(0).getSize().getId(), 1, true);

		//Получаем группу по продавцу
		OrderDTO group = groupedCart.getGroup(sellerId);

		//Отсутствует предупреждение о невозможности создать заказ, т.к. суммы достаточно (можно оформить заказ)
		assertNull(group.getOrderCreationProblems());

		BigDecimal newOfferedPrice = product.getCurrentPrice().multiply(new BigDecimal(0.8));

		//Оформляем оффер по данной позиции так, чтобы сумма снизилась ниже порога
		//Выключаем старые офферы
		/*
		OfferDetails offerDetails = offerService.makeAnOffer(getUser(), product.getId(), product.getAvailableProductItems().get(0).getSize().getId(), newOfferedPrice);
		User seller = userService.getUserById(offerDetails.getSeller().getId()).orElse(null);
		Long lastOfferId = offerDetails.getHistory().get(0).getOfferId();
		offerService.makeDecisionOnOffer(seller, lastOfferId, true);*/

		commitAndStartNewTransaction();

		//Получаем текущую корзину и убеждаемся, что проблем с оформлением заказа нет
		GroupedCart croupedCartWithOffer = getGroupedCartSuccessful();
		OrderDTO groupWithOffer = croupedCartWithOffer.getGroup(sellerId);

		//Базовая сумма по заказу осталась выше порога
		assertTrue(groupWithOffer.getClearAmount().longValue() > amountLimit);

		//Итоговая сумма по заказу снизилась ниже порога
		assertTrue(groupWithOffer.getFinalAmount().longValue() < amountLimit);

		//Проблемы по оформлению заказа отсутствуют
		assertNull(groupWithOffer.getOrderCreationProblems());

		//Устанавливаем адресную точку для возможности оформления заказа
		setAddressEndpointAuthorizedSuccessful();

		//Успешно оформляем заказ с оффером
		OrderService.InitOrderResult initOrderResult = cartTestSupport.holdWithSetAddressEndpoint(getHoldProcedureName(), sellerId, null);

		commitAndStartNewTransaction();

		//Находим этот заказ в базе
		Order order = orderRepository.findById(initOrderResult.getOrderId()).orElse(null);
		//Заказ имеет сумму ниже лимита корзины
		assertTrue(order.getAmount().longValue() < amountLimit);

		//Чистим корзину, дабы не оставлять в базе
		cleanCartSuccessfull();
	}

	/**
	 * Согласно AbsolutePromocode осуществляются проверки:
	 * - сохранение промокода в protected_code
	 * - корректной разбивки промокода для order_positions
	 * - пересохранение промокода в момент использования заказом, где суммы должны оставаться прежними
	 * - сумма заказа совпадала с суммой по позициям
	 */
	@Transactional
	@Test
	public void _65_0_checkHoldWithProtectedAbsolutePromocode(){

		//Найдем товары из категории одежды
		ProductService.FilterSpecification spec = new ProductService.FilterSpecification().categoriesIds(Arrays.asList(139L));
		Page<Product> productsPage = productService.getRawProducts(spec, 1, ProductService.SortAttribute.CHANGE_TIME_DESC, null, ProductService.UserType.HUMAN);

		if(productsPage.getItems().isEmpty()){
			log.info("Нет товаров для проведения теста.");
			return;
		}
		Product product1 = productsPage.getItems().get(0);
		Product product2 = productsPage.getItems().get(1);
		Product product3 = productsPage.getItems().get(2);

		Long sellerId = product1.getSeller().getId();
		product2.setSeller(userService.getOne(sellerId));
		product3.setSeller(userService.getOne(sellerId));

		BigDecimal productAmount1 = new BigDecimal(122033);
		BigDecimal productAmount2 = new BigDecimal(812132);
		BigDecimal productAmount3 = new BigDecimal(3);

		product1.setCurrentPrice(productAmount1);
		product2.setCurrentPrice(productAmount2);
		product3.setCurrentPrice(productAmount3);

		productRepository.saveAndFlush(product1);
		productRepository.saveAndFlush(product2);
		productRepository.saveAndFlush(product3);

		commitAndStartNewTransaction();

		product1 = productRepository.findById(product1.getId()).orElse(null);
		product2 = productRepository.findById(product2.getId()).orElse(null);
		product3 = productRepository.findById(product3.getId()).orElse(null);

		//Добавляем товар в корзину
		List<ProductItem> availableProductItemsOfProduct1 = product1.getAvailableProductItems();
		List<ProductItem> availableProductItemsOfProduct2 = product2.getAvailableProductItems();
		List<ProductItem> availableProductItemsOfProduct3 = product3.getAvailableProductItems();
		Size sizeOfProduct1 = JpaHibernateUtil.unproxy(productItemRepository.findById(availableProductItemsOfProduct1.get(0).getId()).get().getSize(), Size.class);
		Size sizeOfProduct2 = JpaHibernateUtil.unproxy(productItemRepository.findById(availableProductItemsOfProduct2.get(0).getId()).get().getSize(), Size.class);
		Size sizeOfProduct3 = JpaHibernateUtil.unproxy(productItemRepository.findById(availableProductItemsOfProduct3.get(0).getId()).get().getSize(), Size.class);

		cartTestSupport.addToCartSuccessful(product1.getId(), sizeOfProduct1.getId(), 1, true);
		cartTestSupport.addToCartSuccessful(product2.getId(), sizeOfProduct2.getId(), 1, true);
		cartTestSupport.addToCartSuccessful(product3.getId(), sizeOfProduct3.getId(), 1, true);

		//Cоздаем Absolute промокод
		int promoCodeAbsoluteAmount = 5000;
		String promoCodeName = "HOLDWITHTESTABSOLUTEPROMO" + 5000;
		AbsolutePromoCode promoCode = (AbsolutePromoCode) promoCodeRepository.findFirstByCode(promoCodeName).stream().findFirst().orElse(null);
		if(promoCode == null) {
			promoCode = new AbsolutePromoCode();
		}
		promoCode.setValue(new BigDecimal(promoCodeAbsoluteAmount));
		promoCode.setCode(promoCodeName).setBeginPrice(new BigDecimal(1)).setCreatedAt(ZonedDateTime.now())
				.setExpiresAt(ZonedDateTime.now().plusYears(100))
				.setStartsAt(ZonedDateTime.now())
				.setDeleted(false);
		promoCodeRepository.saveAndFlush(promoCode);
		commitAndStartNewTransaction();

		promoCode = (AbsolutePromoCode) promoCodeRepository.findFirstByCode(promoCodeName).stream().findFirst().orElse(null);

		//Проверяем промокод
		OrderDTO checkPromoCodeResult = cartTestSupport.checkPromoCode(sellerId, promoCodeName);

		//В группе идет информация о скидке
		assertNotNull(checkPromoCodeResult.getDiscount());
		//Проблем с оформлением заказа нет (заказ можно оформить)
		assertNull(checkPromoCodeResult.getOrderCreationProblems());

		//Устанавливаем адресную точку для возможности оформления заказа
		setAddressEndpointAuthorizedSuccessful();

		//Успешно оформляем заказ с промокодом
		OrderService.InitOrderResult initOrderResult = cartTestSupport.holdWithPromoCodeSuccessful(getHoldProcedureName(), sellerId, promoCodeName, false);

		commitAndStartNewTransaction();

		//Находим этот заказ в базе
		Order order = orderRepository.findById(initOrderResult.getOrderId()).orElse(null);

		//Проверка состояния сохранение промокода в protected promocode
		PromoCode originalPromocode = JpaHibernateUtil.unproxy(order.getPromoCode(), PromoCode.class);
		PromoCode protectedPromocode = order.getProtectedPromoCode().getProtectedPromoCode();
		assertNotNull(originalPromocode);
		assertNotNull(protectedPromocode);
		assertTrue(originalPromocode.getCode().equals(protectedPromocode.getCode()));
		assertTrue(compareDates(originalPromocode.getCreatedAt(), protectedPromocode.getCreatedAt()));
		assertTrue(compareDates(originalPromocode.getExpiresAt(), protectedPromocode.getExpiresAt()));
		assertTrue(originalPromocode.getDtype().equals(protectedPromocode.getDtype()));
		assertTrue(originalPromocode.getBeginPrice().compareTo(protectedPromocode.getBeginPrice()) == 0);
		assertTrue(originalPromocode.isDeleted() == protectedPromocode.isDeleted());
		assertTrue(originalPromocode.getValue().compareTo(protectedPromocode.getValue()) == 0);

		//Проверка корректной разбивки промокода для order_positions
		BigDecimal accSumOrderPositionPromoAmount = BigDecimal.ZERO;
		assertTrue(order.getParticipatedInPaymentOrderPositions().size() == 3);
		for (OrderPosition orderPosition : order.getParticipatedInPaymentOrderPositions()) {
			//Проверка корректного вычисления скидки для позиции
			assertTrue(orderPosition.getAmount().doubleValue() == productAmount1.doubleValue() ? orderPosition.getPromocodeAmount().doubleValue() == 653.16 :
					orderPosition.getAmount().doubleValue() == productAmount2.doubleValue() ?  orderPosition.getPromocodeAmount().doubleValue() == 4346.82 :
					orderPosition.getAmount().doubleValue() == productAmount3.doubleValue() ? orderPosition.getPromocodeAmount().doubleValue() == 0.02 : false);
			accSumOrderPositionPromoAmount = accSumOrderPositionPromoAmount.add(orderPosition.getPromocodeAmount());
		}

		assertTrue(accSumOrderPositionPromoAmount.compareTo(new BigDecimal(promoCodeAbsoluteAmount)) == 0);

		//Пересохранение промокода в момент использования заказом
		promoCode.setValue(new BigDecimal(20000));
		promoCode.setDeleted(true);
		promoCode.setExpiresAt(promoCode.getExpiresAt().minusYears(200));
		promoCode.setBeginPrice(new BigDecimal(1000000000));
		promoCodeRepository.saveAndFlush(promoCode);
		commitAndStartNewTransaction();

		//Проверка , сумма заказа чтобы совпадала с суммой по позициям (используется для checkonline при запросе проставление buyerCheck, если суммы не совпадают будет ошибка)
		order = orderRepository.findById(initOrderResult.getOrderId()).orElse(null);
		order.setConfirmedTime(ZonedDateTime.now());
		order.getParticipatedInPaymentOrderPositions().forEach(orderPosition -> orderPosition.setState(OrderPositionState.VERIFICATION_OK));
		orderRepository.saveAndFlush(order);
		commitAndStartNewTransaction();

		order = orderRepository.findById(initOrderResult.getOrderId()).orElse(null);

		BigDecimal actualAmountToPay = orderService.getCompletionReadiness(order).getActualAmountToPay();
		BigDecimal linesSumAmount = order.getLines(messageSourceAccessor).stream()
				.map(Checkable.Line::getPrice)
				.reduce(BigDecimal.ZERO, BigDecimal::add);
		assertTrue(actualAmountToPay.compareTo(linesSumAmount) == 0);

		//Чистим корзину, дабы не оставлять в базе
		cleanCartSuccessfull();
	}

	/**
	 * Согласно FractionalPromocode осуществляются проверки:
	 * - сохранение промокода в protected_code
	 * - корректной разбивки промокода для order_positions
	 * - пересохранение промокода в момент использования заказом, где суммы должны оставаться прежними
	 * - сумма заказа совпадала с суммой по позициям
	 */
	@Transactional
	@Test
	public void _65_1_checkHoldWithProtectedFractionalPromocode(){

		//Найдем товары из категории одежды
		ProductService.FilterSpecification spec = new ProductService.FilterSpecification().categoriesIds(Arrays.asList(139L));
		Page<Product> productsPage = productService.getRawProducts(spec, 1, ProductService.SortAttribute.CHANGE_TIME_DESC, null, ProductService.UserType.HUMAN);

		if(productsPage.getItems().isEmpty()){
			log.info("Нет товаров для проведения теста.");
			return;
		}
		Product product1 = productsPage.getItems().get(0);
		Product product2 = productsPage.getItems().get(1);

		Long sellerId = product1.getSeller().getId();
		product2.setSeller(userService.getOne(sellerId));
		productRepository.save(product2);

		commitAndStartNewTransaction();

		product1 = productRepository.findById(product1.getId()).orElse(null);
		product2 = productRepository.findById(product2.getId()).orElse(null);

		//Добавляем товар в корзину
		List<ProductItem> availableProductItemsOfProduct1 = product1.getAvailableProductItems();
		List<ProductItem> availableProductItemsOfProduct2 = product2.getAvailableProductItems();
		Size sizeOfProduct1 = JpaHibernateUtil.unproxy(productItemRepository.findById(availableProductItemsOfProduct1.get(0).getId()).get().getSize(), Size.class);
		Size sizeOfProduct2 = JpaHibernateUtil.unproxy(productItemRepository.findById(availableProductItemsOfProduct2.get(0).getId()).get().getSize(), Size.class);

		cartTestSupport.addToCartSuccessful(product1.getId(), sizeOfProduct1.getId(), 1, true);
		cartTestSupport.addToCartSuccessful(product2.getId(), sizeOfProduct2.getId(), 1, true);

		//Cоздаем Fractional промокод
		double promoCodeFractionalAmount = 0.10;
		String promoCodeName = "HOLDWITHTESTFRACTIONALPROMO" + 0.10;
		FractionalPromoCode promoCode = (FractionalPromoCode) promoCodeRepository.findFirstByCode(promoCodeName).stream().findFirst().orElse(null);
		if(promoCode == null) {
			promoCode = new FractionalPromoCode();
		}
		promoCode.setValue(new BigDecimal(promoCodeFractionalAmount));
		promoCode.setCode(promoCodeName).setBeginPrice(new BigDecimal(1)).setCreatedAt(ZonedDateTime.now())
				.setExpiresAt(ZonedDateTime.now().plusYears(100))
				.setStartsAt(ZonedDateTime.now())
				.setDeleted(false);
		promoCodeRepository.saveAndFlush(promoCode);
		commitAndStartNewTransaction();

		promoCode = (FractionalPromoCode) promoCodeRepository.findFirstByCode(promoCodeName).stream().findFirst().orElse(null);

		//Проверяем промокод
		OrderDTO checkPromoCodeResult = cartTestSupport.checkPromoCode(sellerId, promoCodeName);

		//В группе идет информация о скидке
		assertNotNull(checkPromoCodeResult.getDiscount());
		//Проблем с оформлением заказа нет (заказ можно оформить)
		assertNull(checkPromoCodeResult.getOrderCreationProblems());

		//Устанавливаем адресную точку для возможности оформления заказа
		setAddressEndpointAuthorizedSuccessful();

		//Успешно оформляем заказ с промокодом
		OrderService.InitOrderResult initOrderResult = cartTestSupport.holdWithPromoCodeSuccessful(getHoldProcedureName(), sellerId, promoCodeName, false);

		commitAndStartNewTransaction();

		//Находим этот заказ в базе
		Order order = orderRepository.findById(initOrderResult.getOrderId()).orElse(null);

		//Проверка состояния сохранение промокода в protected promocode
		PromoCode originalPromocode = JpaHibernateUtil.unproxy(order.getPromoCode(), PromoCode.class);
		PromoCode protectedPromocode = order.getProtectedPromoCode().getProtectedPromoCode();
		assertNotNull(originalPromocode);
		assertNotNull(protectedPromocode);
		assertTrue(originalPromocode.getCode().equals(protectedPromocode.getCode()));
		assertTrue(compareDates(originalPromocode.getCreatedAt(), protectedPromocode.getCreatedAt()));
		assertTrue(compareDates(originalPromocode.getExpiresAt(), protectedPromocode.getExpiresAt()));
		assertTrue(originalPromocode.getDtype().equals(protectedPromocode.getDtype()));
		assertTrue(originalPromocode.getBeginPrice().compareTo(protectedPromocode.getBeginPrice()) == 0);
		assertTrue(originalPromocode.isDeleted() == protectedPromocode.isDeleted());
		assertTrue(originalPromocode.getValue().compareTo(protectedPromocode.getValue()) == 0);

		//Проверка корректной разбивки промокода для order_positions
		BigDecimal accSumOrderPositionPromoAmount = BigDecimal.ZERO;
		for (OrderPosition orderPosition : order.getParticipatedInPaymentOrderPositions()) {
			accSumOrderPositionPromoAmount = accSumOrderPositionPromoAmount.add(orderPosition.getPromocodeAmount());
		}

		BigDecimal discountByAmountOrder = order.getParticipatedInPaymentOrderPositions().stream()
				.map(OrderPosition::getAmount)
				.reduce(BigDecimal.ZERO, BigDecimal::add);
		discountByAmountOrder = discountByAmountOrder.multiply(promoCode.getValue()).divide(new BigDecimal(100));
		assertTrue(accSumOrderPositionPromoAmount.compareTo(discountByAmountOrder) == 0);

		//Пересохранение промокода в момент использования заказом
		promoCode.setValue(new BigDecimal(0.05));
		promoCode.setDeleted(true);
		promoCode.setExpiresAt(promoCode.getExpiresAt().minusYears(200));
		promoCode.setBeginPrice(new BigDecimal(1000000000));
		promoCodeRepository.saveAndFlush(promoCode);
		commitAndStartNewTransaction();

		//Проверка , сумма заказа чтобы совпадала с суммой по позициям (используется для checkonline при запросе проставление buyerCheck, если суммы не совпадают будет ошибка)
		order = orderRepository.findById(initOrderResult.getOrderId()).orElse(null);
		order.setConfirmedTime(ZonedDateTime.now());
		order.getParticipatedInPaymentOrderPositions().forEach(orderPosition -> orderPosition.setState(OrderPositionState.VERIFICATION_OK));
		orderRepository.saveAndFlush(order);
		commitAndStartNewTransaction();

		order = orderRepository.findById(initOrderResult.getOrderId()).orElse(null);

		BigDecimal actualAmountToPay = orderService.getCompletionReadiness(order).getActualAmountToPay();
		BigDecimal linesSumAmount = order.getLines(messageSourceAccessor).stream()
				.map(Checkable.Line::getPrice)
				.reduce(BigDecimal.ZERO, BigDecimal::add);
		assertTrue(actualAmountToPay.compareTo(linesSumAmount) == 0);

		//Чистим корзину, дабы не оставлять в базе
		cleanCartSuccessfull();
	}


	private boolean compareDates(ZonedDateTime firstDate, ZonedDateTime secondDate) {
		return firstDate.getZone().equals(secondDate.getZone())
				&& firstDate.getDayOfMonth() == firstDate.getDayOfMonth()
				&& firstDate.getMonthValue() == secondDate.getMonthValue()
				&& firstDate.getYear() == secondDate.getYear()
				&& firstDate.getHour() == secondDate.getHour()
				&& firstDate.getMinute() == secondDate.getMinute()
				&& firstDate.getSecond() == secondDate.getSecond();
	}


	@Transactional
	@Test
	public void _90_finalize(){
		cleanup();
	}

	private void cleanup(){
		cartTestSupport.cleanup();

    	User buyer = getUser();

		List<Counterparty> counterparties = counterpartyService.findAllUserCounterparties(buyer);
		counterpartyRepository.deleteAll(counterparties);

		List<Address> addresses = addressService.findAllUserAddresses(buyer);
		addressRepository.deleteAll(addresses);

		//Выключаем старые офферы
		//List<Offer> offers = offerRepository.findByOfferor(buyer);
		//offerRepository.deleteAll(offers);

		//Возвращаем прежний статус скрытому товару
		if(hiddenProductId != null){
			TestUtils.setProductState(hiddenProductId, ProductState.PUBLISHED, productRepository);
		}
	}

	@NoArgsConstructor @Getter @Setter @Accessors(chain = true)
	public static class CartAddRequest{
		private Long productId;
		private Long sizeId;
		private Integer count;
		private String currencyCode;
	}
}
