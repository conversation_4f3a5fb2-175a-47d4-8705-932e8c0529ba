package ru.oskelly.tests.pr.suite9;

import lombok.experimental.UtilityClass;
import su.reddot.domain.model.Brand;
import su.reddot.domain.model.attribute.Attribute;
import su.reddot.domain.model.attribute.AttributeValue;
import su.reddot.domain.model.category.Category;
import su.reddot.domain.model.product.Image;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductAttributeValueBinding;
import su.reddot.domain.model.product.ProductModel;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.size.SizeType;
import su.reddot.domain.model.user.User;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@UtilityClass
public class OpenCvTestUtils {

    public static Product createOpenCvProduct(long index) {
        Brand brand = new Brand();
        brand.setId(index);

        ProductModel productModel = new ProductModel();
        productModel.setId(index);

        AttributeValue colorAttribute = new AttributeValue();
        colorAttribute.setAttributeId(Attribute.COLOR_ID);
        colorAttribute.setId(index);

        ProductAttributeValueBinding attribute = new ProductAttributeValueBinding();
        attribute.setAttributeValue(colorAttribute);

        List<ProductAttributeValueBinding> attributes = new ArrayList<>();
        attributes.add(attribute);

        Product product = new Product();
        product.setCurrentPrice(BigDecimal.valueOf(index));
        product.setProductState(ProductState.PUBLISHED);
        product.setId(index);
        product.setCategoryId(index);
        product.setBrand(brand);
        product.setProductModel(productModel);
        product.setAttributeValues(attributes);

        return product;
    }

    public static Image createImage(int index) {
        Image image1 = new Image();
        image1.setId((long) index);
        image1.setMain(false);
        image1.setPhotoOrder(index);
        image1.setImagePath("http://localhost/img/path/image.png");
        return image1;
    }

    public static Product createProductToExternalCatalog(long index) {
        Product product = createOpenCvProduct(index);

        User seller = new User();
        seller.setId(index);

        product.setSeller(seller);
        product.setSizeType(SizeType.RU);
        product.setNewCollection(true);
        product.setAtOffice(true);
        product.setStartPrice(BigDecimal.valueOf(1L));
        product.setRrpPrice(BigDecimal.valueOf(1L));
        product.setVendorCode("VENDOR_CODE");
        product.setStoreCode("STORE_CODE");
        product.setDescription("DESCRIPTION");
        product.setCategoryId(index);

        return product;
    }

    public static void initCategory(long index) {
        Category parent = new Category();
        parent.setLeftOrder(1);

        Category category = new Category();
        category.setId(index);
        category.setParent(parent);

        Map<Long, Category> categoryMap = new HashMap<>();
        categoryMap.put(index, category);
        Category.setCategoryCache(categoryMap);
    }
}
