package ru.oskelly.tests.pr.suite9;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.ActiveProfiles;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.product.Image;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.service.catalog.CategoryService;
import su.reddot.domain.service.integration.opencv.ProductToOpenCvMapper;
import su.reddot.domain.service.staticresource.StaticResourceBalancer;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static ru.oskelly.tests.pr.suite9.OpenCvTestUtils.createImage;
import static ru.oskelly.tests.pr.suite9.OpenCvTestUtils.createOpenCvProduct;

@ExtendWith(MockitoExtension.class)
@DevSuite(value = TestSuiteName.TEST_SUITE_09)
public class ProductToOpenCvMapperTest {
    @Mock
    private ProductRepository productRepository;
    @Mock
    private StaticResourceBalancer staticResourceBalancer;
    @Mock
    private CategoryService categoryService;

    @InjectMocks
    private ProductToOpenCvMapper openCvMapper;

    @Test
    public void testGetListInfoWithImg() {
        Map<Long, List<Image>> images = new HashMap<>();
        images.put(1L, Collections.singletonList(createImage(1)));
        images.put(2L, Collections.emptyList());
        images.put(3L, null);
        when(productRepository.getProductImages(any())).thenReturn(images);
        when(categoryService.getAllParentCategoriesCached(any())).thenReturn(Collections.emptyList());

        List<Product> products = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            products.add(createOpenCvProduct(i));
        }

        ProductToOpenCvMapper.OpenCvListInfo listInfo = openCvMapper.prepareListInfo(products, true);

        // Если создается ListInfo с изображениями, то в OpenCvListInfo.images не должно быть значений null
        // Это нужно, когда ProductToOpenCvTopicDTO будет считываться из kafka и отправляться в OpenCV,
        // то значение null или массив будет влиять на выбор метода POST или PATCH
        assertEquals(1, listInfo.getImages().get(1L).size());
        assertNotNull(listInfo.getImages().get(2L));
        assertNotNull(listInfo.getImages().get(3L));
    }
}
