package ru.oskelly.tests.pr.suite9;

import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import io.micrometer.core.instrument.Counter;
import org.apache.kafka.common.KafkaException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.ActiveProfiles;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.QProduct;
import su.reddot.domain.service.catalog.CategoryService;
import su.reddot.domain.service.dto.opencv.OpenCvProductImageDTO;
import su.reddot.domain.service.externalcatalog.AbstractProductToKafkaCompletelySender;
import su.reddot.domain.service.externalcatalog.ProductFiltersToExternalCatalogManager;
import su.reddot.domain.service.externalcatalog.ProductsToExternalCatalogExecutionIdGenerator;
import su.reddot.domain.service.externalcatalog.ProductsToExternalCatalogRevisionGenerator;
import su.reddot.domain.service.integration.opencv.ProductToOpenCvMapper;
import su.reddot.domain.service.kafka.KafkaSenderService;
import su.reddot.domain.service.metric.MicrometerService;
import su.reddot.domain.service.opencv.ProductsNoImgCompletelySender;
import su.reddot.domain.service.opencv.ProductsWithImgCompletelySender;
import su.reddot.domain.service.staticresource.StaticResourceBalancer;
import su.reddot.infrastructure.util.CallInTransaction;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static ru.oskelly.tests.pr.suite9.OpenCvTestUtils.createOpenCvProduct;

@ExtendWith(MockitoExtension.class)
@DevSuite(value = TestSuiteName.TEST_SUITE_09)
public class ProductsToOpenCvCompletelySenderTest {
    private ProductsWithImgCompletelySender withImgCompletelySender;
    private ProductsNoImgCompletelySender noImgCompletelySender;

    @Mock
    private CategoryService categoryService;
    @Mock
    private Counter counter;
    @Mock
    private JPAQuery jpaQuery;
    @Mock
    private JPAQueryFactory jpaQueryFactory;
    @Mock
    private KafkaSenderService kafkaSenderService;
    @Mock
    private MicrometerService micrometerService;
    @Mock
    private ProductRepository productRepository;
    @Mock
    private StaticResourceBalancer resourceBalancer;

    @Spy
    @InjectMocks
    private ProductToOpenCvMapper productMapper;
    @InjectMocks
    private CallInTransaction callInTransaction;
    @InjectMocks
    private ProductFiltersToExternalCatalogManager filtersManager;
    @InjectMocks
    private ProductsToExternalCatalogRevisionGenerator revisionGenerator;
    @InjectMocks
    private ProductsToExternalCatalogExecutionIdGenerator executionIdGenerator;

    @BeforeEach
    public void setProductsCompletelySender() {
        withImgCompletelySender = new ProductsWithImgCompletelySender(
                productMapper,
                micrometerService,
                callInTransaction,
                kafkaSenderService,
                filtersManager,
                revisionGenerator,
                executionIdGenerator,
                jpaQueryFactory,
                "OPENCV-UPDATING-TOPIC",
                5,
                10,
                10
        );

        noImgCompletelySender = new ProductsNoImgCompletelySender(
                productMapper,
                micrometerService,
                callInTransaction,
                kafkaSenderService,
                filtersManager,
                revisionGenerator,
                executionIdGenerator,
                jpaQueryFactory,
                "OPENCV-UPDATING-TOPIC",
                5,
                10,
                10
        );

        doReturn(jpaQuery).when(jpaQueryFactory).from(eq(QProduct.product));

        when(jpaQuery.where((Predicate) any())).thenReturn(jpaQuery);
        when(jpaQuery.fetchJoin()).thenReturn(jpaQuery);
        when(jpaQuery.innerJoin((EntityPath) any())).thenReturn(jpaQuery);
        when(jpaQuery.leftJoin((EntityPath) any())).thenReturn(jpaQuery);
        when(jpaQuery.offset(anyLong())).thenReturn(jpaQuery);
        when(jpaQuery.limit(anyLong())).thenReturn(jpaQuery);
        when(jpaQuery.orderBy((OrderSpecifier<?>) any())).thenReturn(jpaQuery);
        when(jpaQuery.select((Expression<?>) any())).thenReturn(jpaQuery);
        when(jpaQuery.distinct()).thenReturn(jpaQuery);

        when(micrometerService.getCounterByName(any())).thenReturn(counter);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    public void testSendProductsSuccess(boolean includeImages) throws ReflectiveOperationException {
        int productsCount = 3;
        when(jpaQuery.fetch()).thenReturn(createProducts(productsCount));
        when(jpaQuery.fetchCount()).thenReturn(Long.valueOf(productsCount));
        doReturn(createListInfo(productsCount, includeImages)).when(productMapper).prepareListInfo(any(), anyBoolean());

        callSendProductsWithImages(includeImages);

        // успешная отправка dto в кафка
        verify(kafkaSenderService, times(productsCount)).sendProductToExternalCatalogMessageSilent(any(), any(), any());
        verify(micrometerService, times(productsCount))
                .getCounterByName(getCounterPrefix(includeImages) + ".sendProducts.saveCommandsCount");

        // errorsCount извлекается в sendProductsCompletely и увеличивается в тесте на 0
        verify(micrometerService, times(1))
                .getCounterByName(getCounterPrefix(includeImages) + ".sendProducts.errorsCount");
        verify(counter, times(1)).increment(0);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    public void testSendProductsFailed(boolean includeImages) throws ReflectiveOperationException {
        int productsCount = 3;
        when(jpaQuery.fetch()).thenReturn(createProducts(productsCount));
        when(jpaQuery.fetchCount()).thenReturn(Long.valueOf(productsCount));
        doReturn(createListInfo(productsCount, includeImages)).when(productMapper).prepareListInfo(any(), anyBoolean());
        doThrow(new KafkaException("Error")).when(kafkaSenderService).sendProductToExternalCatalogMessageSilent(any(), any(), any());

        callSendProductsWithImages(includeImages);

        // ошибка при отправке dto в кафка
        verify(kafkaSenderService, times(productsCount)).sendProductToExternalCatalogMessageSilent(any(), any(), any());
        verify(micrometerService, times(0))
                .getCounterByName(getCounterPrefix(includeImages) + ".sendProducts.saveCommandsCount");

        // errorsCount извлекается в sendProductsCompletely и увеличивается в тесте на 3
        verify(counter, times(1)).increment(3);
        verify(micrometerService, times(1))
                .getCounterByName(getCounterPrefix(includeImages) + ".sendProducts.errorsCount");
    }


    private void callSendProductsWithImages(boolean includeImages) throws ReflectiveOperationException {
        Method sendMethod = AbstractProductToKafkaCompletelySender.class.getDeclaredMethod("sendProductsCompletely", List.class);
        sendMethod.setAccessible(true);
        if (includeImages) {
            sendMethod.invoke(withImgCompletelySender, new Object[]{null});
        } else {
            sendMethod.invoke(noImgCompletelySender, new Object[]{null});
        }
    }

    private String getCounterPrefix(boolean includeImages) {
        return includeImages ? "ProductsWithImgCompletelySender" : "ProductsNoImgCompletelySender";
    }

    private List<Product> createProducts(int count) {
        List<Product> products = new ArrayList<>();

        for (int i = 0; i < count; i++) {
            products.add(createOpenCvProduct(i));
        }

        return products;
    }

    private ProductToOpenCvMapper.OpenCvListInfo createListInfo(int count, boolean includeImages) {
        ProductToOpenCvMapper.OpenCvListInfo listInfo = new ProductToOpenCvMapper.OpenCvListInfo();

        Map<Long, Long> attributes = new HashMap<>();
        for (long i = 0; i < count; i++) {
            attributes.put(i, i);
        }
        listInfo.setAttributes(attributes);

        if (includeImages) {
            Map<Long, List<OpenCvProductImageDTO>> imagesMap = new HashMap<>();
            for (long i = 0; i < count; i++) {
                imagesMap.put(i, createImageDtoList());
            }
            listInfo.setImages(imagesMap);
        }

        return listInfo;
    }

    private List<OpenCvProductImageDTO> createImageDtoList() {
        OpenCvProductImageDTO imageDTO = new OpenCvProductImageDTO();
        imageDTO.setPhotoOrder(1);
        imageDTO.setMain(true);
        imageDTO.setPath("http://localhost/img/path");

        List<OpenCvProductImageDTO> imageDTOList = new ArrayList<>();
        imageDTOList.add(imageDTO);

        return imageDTOList;
    }
}
