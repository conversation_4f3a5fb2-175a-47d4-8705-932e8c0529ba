package ru.oskelly.tests.pr.suite9;

import org.junit.jupiter.api.Test;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static su.reddot.infrastructure.configuration.web.CustomOSIVRegistry.isOsivEnabled;

@DevSuite(value = TestSuiteName.TEST_SUITE_09)
public class CustomOSIVRegistryTest {

    @Test
    void testContainsLogic() {

        // fixed urls
        assertTrue(isOsivEnabled("/api/v2/cart", "get"));
        assertTrue(isOsivEnabled("/api/v2/cart", "GET"));
        assertFalse(isOsivEnabled("/api/v2/cart", "patch"));
        assertFalse(isOsivEnabled("/api/v2/cart/fake", "get"));

        // /api/v2/orders/{orderId}/documents/{documentName}.{fmt}
        assertTrue(isOsivEnabled("/api/v2/orders/123/documents/qwe-rty.pdf", "get"));
        assertFalse(isOsivEnabled("/api/v2/orders/123/documents/qwe-rty", "get"));
        assertFalse(isOsivEnabled("/api/v2/orders/qwe/rty/documents/qwe-rty.pdf", "get"));
        assertFalse(isOsivEnabled("/api/v2/orders/123/documents/qwerty.pdf/fake", "get"));

        // /catalog/{id:\d+}
        assertTrue(isOsivEnabled("/catalog/123", "get"));
        assertTrue(isOsivEnabled("/catalog/qwerty", "get"));// TODO: !!!

        // /products/*-{id:\d+}
        assertTrue(isOsivEnabled("/products/qwe-123", "get"));
        assertTrue(isOsivEnabled("/products/123-456", "get"));
        assertFalse(isOsivEnabled("/products/qwe-rty", "get"));
        assertFalse(isOsivEnabled("/products/qwe123/", "get"));
        assertFalse(isOsivEnabled("/products/qwe-123/rty", "get"));

        // /api/v2/callback/log-raw-callback/**
        assertTrue(isOsivEnabled("/api/v2/callback/log-raw-callback/qwe", "post"));
        assertTrue(isOsivEnabled("/api/v2/callback/log-raw-callback/qwe/rty", "post"));
        assertTrue(isOsivEnabled("/api/v2/callback/log-raw-callback/", "post"));
        assertTrue(isOsivEnabled("/api/v2/callback/log-raw-callback", "post"));
        assertFalse(isOsivEnabled("/api/v2/callback/log-raw", "post"));

        // наличие маппинга /catalog/** не даст отключить open-in-view для более специфичных маппингов,
        // начинающихся с /catalog, но и убирать его тоже пока нельзя
        // /api/v2/catalog/**
        assertTrue(isOsivEnabled("/catalog/qwe", "get"));
        assertTrue(isOsivEnabled("/catalog/qwe/rty", "get"));
        assertTrue(isOsivEnabled("/catalog/banner/123", "get"));

        // /stream/*
        assertTrue(isOsivEnabled("/stream/qwe", "get"));
        assertFalse(isOsivEnabled("/stream/qwe/rty", "get"));

        // /api/v2/productPublication/categoryTree
        assertTrue(isOsivEnabled("/api/v2/productPublication/categoryTree", "GET"));
        assertTrue(isOsivEnabled("/api/v2/productpublication/categoryTree", "get"));
    }
}