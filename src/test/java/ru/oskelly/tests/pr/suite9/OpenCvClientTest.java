package ru.oskelly.tests.pr.suite9;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import su.reddot.domain.exception.OpenCvException;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.service.dto.opencv.OpenCvProductImageDTO;
import su.reddot.domain.service.dto.opencv.ProductToOpenCvTopicDTO;
import su.reddot.domain.service.metric.MicrometerService;
import su.reddot.infrastructure.opencv.OpenCvClient;
import su.reddot.infrastructure.opencv.view.OpenCvStatus;
import su.reddot.infrastructure.opencv.view.SearchResultResponse;
import su.reddot.infrastructure.opencv.view.TaskIdResponse;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ActiveProfiles(profiles = AbstractSpringTest.testProfiles)
@ExtendWith(SpringExtension.class)
@DevSuite(value = TestSuiteName.TEST_SUITE_09)
public class OpenCvClientTest {
    private static ObjectMapper objectMapper;

    @Mock
    private RestTemplate restTemplate;
    @Mock
    private MicrometerService micrometerService;
    @Mock
    private MessageSourceAccessor messageSourceAccessor;

    private OpenCvClient openCvClient;

    private final String PRODUCT_URL = "http://localhost/product";

    @BeforeAll
    public static void setUp() {
        objectMapper = new ObjectMapper();
    }

    @BeforeEach
    public void setOpenCvClient() {
        openCvClient = new OpenCvClient(micrometerService, restTemplate, objectMapper, messageSourceAccessor);
        openCvClient.baseUrl = "http://localhost";
    }

    @Test
    void testSearchOk() {
        String taskId = UUID.randomUUID().toString();
        String responseDetail = "{\"detail\": \"" + taskId + "\"}";
        HttpClientErrorException responseBody = new HttpClientErrorException(
                HttpStatus.REQUEST_TIMEOUT,
                "Request timeout",
                null,
                responseDetail.getBytes(StandardCharsets.UTF_8),
                null
        );

        when(restTemplate.postForEntity(anyString(), any(), any()))
                .thenThrow(responseBody);

        String result = openCvClient.search("image_link");
        assertEquals(taskId, result);
        verify(micrometerService, times(1)).increment("OpenCvClient.search.success");
    }

    @Test
    void testSearchOpenCvError() {
        HttpClientErrorException responseBody = new HttpClientErrorException(HttpStatus.INTERNAL_SERVER_ERROR);
        when(restTemplate.postForEntity(anyString(), any(), any())).thenThrow(responseBody);

        OpenCvException exception = assertThrows(OpenCvException.class, () -> openCvClient.search("image_link"));
        assertEquals("An error occurred while sending request to OpenCV: 500 INTERNAL_SERVER_ERROR",
                exception.getLogMessage());
        verify(micrometerService, times(1)).increment("OpenCvClient.search.failed");
    }

    @Test
    void testSearchOpenCvUnexpectedResponse() {
        when(restTemplate.postForEntity(anyString(), any(), eq(TaskIdResponse.class)))
                .thenReturn(ResponseEntity.ok().build());
        OpenCvException exception = assertThrows(OpenCvException.class, () -> openCvClient.search("image_link"));
        assertEquals("Unexpected response from OpenCV API.", exception.getLogMessage());
        verify(micrometerService, times(1)).increment("OpenCvClient.search.failed");
    }

    @Test
    void testGetTaskOk() {
        SearchResultResponse responseBody = new SearchResultResponse();
        responseBody.setStatus("finished");

        when(restTemplate.getForObject(anyString(), eq(SearchResultResponse.class))).thenReturn(responseBody);

        SearchResultResponse resultResponse = openCvClient.getTask("task_id");
        assertTrue(OpenCvStatus.FINISHED.isSame(resultResponse.getStatus()));
        verify(micrometerService, times(1)).increment("OpenCvClient.getTask.success");
    }

    @ParameterizedTest
    @MethodSource("getTaskParameters")
    void testGetTaskErrors(String errMsg, HttpStatus status, String taskId) {
        HttpClientErrorException openCvException = new HttpClientErrorException(status);
        when(restTemplate.getForObject(anyString(), eq(SearchResultResponse.class))).thenThrow(openCvException);

        OpenCvException exception = assertThrows(OpenCvException.class, () -> openCvClient.getTask(taskId));
        assertEquals(errMsg, exception.getLogMessage());
        verify(micrometerService, times(1)).increment("OpenCvClient.getTask.failed");
    }

    @Test
    void testPostProductOk() {
        ProductToOpenCvTopicDTO product = createProductDto(true);
        openCvClient.postProduct(product);
        verify(restTemplate, times(1))
                .postForObject(eq(PRODUCT_URL), any(), eq(Object.class));
        verify(micrometerService, times(1)).increment("OpenCvClient.postProduct.success");
    }

    @Test
    void testPostProductHasNull() {
        // если есть поля с null значениями, то перед выполнением POST запроса, null значения заменяются на -1
        ProductToOpenCvTopicDTO product = createProductDto(false);
        openCvClient.postProduct(product);
        verify(restTemplate, times(1))
                .postForObject(eq(PRODUCT_URL), any(), eq(Object.class));
        verify(micrometerService, times(1)).increment("OpenCvClient.postProduct.success");
        verify(micrometerService, times(0)).increment("OpenCvClient.postProduct.failed");
    }

    @Test
    void testPostProductHttpException() {
        ProductToOpenCvTopicDTO product = createProductDto(true);
        when(restTemplate.postForObject(eq(PRODUCT_URL), any(), eq(Object.class)))
                .thenThrow(new HttpClientErrorException(HttpStatus.BAD_REQUEST));
        OpenCvException exception = assertThrows(OpenCvException.class, () -> openCvClient.postProduct(product));
        assertEquals("400 BAD_REQUEST", exception.getMessage());
        assertEquals("An error occurred while posting product in OpenCV.", exception.getLogMessage());
        verify(micrometerService, times(0)).increment("OpenCvClient.postProduct.success");
        verify(micrometerService, times(1)).increment("OpenCvClient.postProduct.failed");
    }

    @Test
    void testPatchProductOk() {
        ProductToOpenCvTopicDTO product = createProductDto(false);
        String url = PRODUCT_URL + "/" + product.getId();
        openCvClient.patchProduct(product.getId(), product);
        verify(restTemplate, times(1)).patchForObject(eq(url), any(), eq(Object.class));
        verify(micrometerService, times(1)).increment("OpenCvClient.patchProduct.success");
    }

    @Test
    void testPatchProductHttpException() {
        ProductToOpenCvTopicDTO product = createProductDto(false);
        String url = PRODUCT_URL + "/" + product.getId();

        when(restTemplate.patchForObject(eq(url), any(), eq(Object.class)))
                .thenThrow(new HttpClientErrorException(HttpStatus.BAD_REQUEST));

        OpenCvException exception = assertThrows(OpenCvException.class,
                () -> openCvClient.patchProduct(product.getId(), product));

        assertEquals("400 BAD_REQUEST", exception.getMessage());
        assertEquals("An error occurred while posting product in OpenCV.", exception.getLogMessage());
        verify(micrometerService, times(0)).increment("OpenCvClient.patchProduct.success");
        verify(micrometerService, times(1)).increment("OpenCvClient.patchProduct.failed");
    }

    private ProductToOpenCvTopicDTO createProductDto(boolean includeImages) {
        ProductToOpenCvTopicDTO product = new ProductToOpenCvTopicDTO();
        product.setId(1L);
        product.setBrandId(1L);
        product.setBaseCategoryId(1L);
        product.setCategoryId(1L);
        product.setColorId(1L);
        product.setCurrentPrice(BigDecimal.valueOf(1L));
        product.setModelId(1L);
        product.setRevisionId(1L);
        product.setState(ProductState.PUBLISHED);

        if (includeImages) {
            List<OpenCvProductImageDTO> images = new ArrayList<>();
            OpenCvProductImageDTO imageDTO = new OpenCvProductImageDTO();

            images.add(imageDTO);
            product.setImages(images);
        }

        return product;
    }

    private static Stream<Arguments> getTaskParameters() {
        return Stream.of(
                Arguments.of("Task not found by id: task_id", HttpStatus.NOT_FOUND, "task_id"),
                Arguments.of(
                        "An 5XX error occurred while sending a request to the OpenCV API.",
                        HttpStatus.INTERNAL_SERVER_ERROR, "task_id"
                ),
                Arguments.of("400 BAD_REQUEST", HttpStatus.BAD_REQUEST, "task_id")
        );
    }
}
