package ru.oskelly.tests.pr.suite9;

import io.micrometer.core.instrument.Counter;
import org.apache.kafka.common.KafkaException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.ActiveProfiles;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.product.Image;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.service.adminpanel.product.domain.ProductHistoryDTOV3;
import su.reddot.domain.service.catalog.CategoryService;
import su.reddot.domain.service.externalcatalog.AbstractProductsToExternalStorageEventuallySender;
import su.reddot.domain.service.externalcatalog.ProductFiltersToExternalCatalogManager;
import su.reddot.domain.service.externalcatalog.ProductsToExternalCatalogExecutionIdGenerator;
import su.reddot.domain.service.externalcatalog.ProductsToExternalCatalogRevisionGenerator;
import su.reddot.domain.service.externalcatalog.ProductsToExternalCatalogSendingSynchronization.ChangedObject;
import su.reddot.domain.service.integration.opencv.ProductToOpenCvMapper;
import su.reddot.domain.service.kafka.KafkaSenderService;
import su.reddot.domain.service.metric.MicrometerService;
import su.reddot.domain.service.opencv.ProductsToOpenCvStorageEventuallySender;
import su.reddot.domain.service.staticresource.StaticResourceBalancer;
import su.reddot.infrastructure.util.CallInTransaction;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static ru.oskelly.tests.pr.suite9.OpenCvTestUtils.createImage;
import static ru.oskelly.tests.pr.suite9.OpenCvTestUtils.createOpenCvProduct;

@ExtendWith(MockitoExtension.class)
@DevSuite(value = TestSuiteName.TEST_SUITE_09)
public class ProductsToOpenCvStorageEventuallySenderTest {
    private ProductsToOpenCvStorageEventuallySender productsToOpenCvStorageEventuallySender;
    private ProductToOpenCvMapper productToOpenCvMapper;

    @Mock
    private CategoryService categoryService;
    @Mock
    private Counter changedObjectCounter;
    @Mock
    private Counter saveCounter;
    @Mock
    private Counter deleteCounter;
    @Mock
    private Counter errorCounter;
    @Mock
    private KafkaSenderService kafkaSenderService;
    @Mock
    private MicrometerService micrometerService;
    @Mock
    private ProductRepository productRepository;
    @Mock
    private StaticResourceBalancer staticResourceBalancer;

    @InjectMocks
    private ProductFiltersToExternalCatalogManager filtersManager;
    @InjectMocks
    private CallInTransaction callInTransaction;
    @InjectMocks
    private ProductsToExternalCatalogRevisionGenerator revisionGenerator;
    @InjectMocks
    private ProductsToExternalCatalogExecutionIdGenerator executionIdGenerator;

    @BeforeEach
    public void init() {
        productToOpenCvMapper = Mockito.spy(new ProductToOpenCvMapper(
                productRepository,
                staticResourceBalancer,
                categoryService
        ));

        productsToOpenCvStorageEventuallySender = new ProductsToOpenCvStorageEventuallySender(
                productToOpenCvMapper,
                kafkaSenderService,
                filtersManager,
                callInTransaction,
                productRepository,
                micrometerService,
                executionIdGenerator,
                revisionGenerator
        );

        filtersManager.init();

        when(micrometerService.getCounterByName(getCounter("changedObjectsCount"))).thenReturn(changedObjectCounter);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    public void testSendProductSuccess(boolean includeImages) throws ReflectiveOperationException {
        when(micrometerService.getCounterByName(getCounter("saveCommandsCount"))).thenReturn(saveCounter);

        Product product = createOpenCvProduct(1L);
        List<ChangedObject> changedObjects = Collections.singletonList(new ChangedObject(product, false));

        // Изображения отправляются в том случае, если у продукта статус сменился на PUBLISHED
        if (includeImages) {
            Image productImage = createImage(1);
            List<Image> images = Collections.singletonList(productImage);
            Map<Long, List<Image>> imagesMap = Collections.singletonMap(1L, images);

            ProductHistoryDTOV3 productHistory = new ProductHistoryDTOV3();
            productHistory.setProductState(ProductState.DRAFT);
            product.setSavedState(productHistory);

            when(productRepository.getProductImages(any())).thenReturn(imagesMap);
        }

        Product cloneProduct = createOpenCvProduct(1L);
        when(productRepository.findById(product.getId())).thenReturn(Optional.of(cloneProduct));

        callRunProcessingChangedObjects(changedObjects);

        verify(productToOpenCvMapper, times(1)).prepareListInfo(any(), eq(includeImages));

        verify(micrometerService, times(1)).getCounterByName(getCounter("changedObjectsCount"));
        verify(changedObjectCounter, times(1)).increment(changedObjects.size());

        verify(micrometerService, times(1)).getCounterByName(getCounter("saveCommandsCount"));
        verify(saveCounter, times(1)).increment();

        verify(micrometerService, times(0)).getCounterByName(getCounter("deleteCommandsCount"));
        verify(micrometerService, times(0)).getCounterByName(getCounter("errorsCount"));
    }

    @Test
    public void testSendProductIgnored() throws ReflectiveOperationException {
        // сообщение в kafka не отправляется, если статус не равен PUBLISHED, и не был PUBLISHED
        Product product = createOpenCvProduct(1L);
        List<ChangedObject> changedObjects = Collections.singletonList(new ChangedObject(product, false));

        ProductHistoryDTOV3 productHistory = new ProductHistoryDTOV3();
        productHistory.setProductState(ProductState.DRAFT);
        product.setSavedState(productHistory);

        Product cloneProduct = createOpenCvProduct(1L);
        cloneProduct.setProductState(ProductState.DRAFT);
        when(productRepository.findById(product.getId())).thenReturn(Optional.of(cloneProduct));

        callRunProcessingChangedObjects(changedObjects);

        verify(productToOpenCvMapper, times(0)).prepareListInfo(any(), anyBoolean());

        verify(micrometerService, times(1)).getCounterByName(getCounter("changedObjectsCount"));
        verify(changedObjectCounter, times(1)).increment(changedObjects.size());

        verify(micrometerService, times(0)).getCounterByName(getCounter("saveCommandsCount"));
        verify(micrometerService, times(0)).getCounterByName(getCounter("deleteCommandsCount"));
        verify(micrometerService, times(0)).getCounterByName(getCounter("errorsCount"));
    }

    @Test
    public void testSendProductKafkaError() throws ReflectiveOperationException {
        when(micrometerService.getCounterByName(getCounter("errorsCount"))).thenReturn(errorCounter);

        Product product = createOpenCvProduct(1L);
        List<ChangedObject> changedObjects = Collections.singletonList(new ChangedObject(product, false));

        when(productRepository.findById(product.getId())).thenReturn(Optional.of(product));
        doThrow(new KafkaException("Error")).when(kafkaSenderService).sendProductToExternalCatalogMessageSilent(any(), any(), any());

        callRunProcessingChangedObjects(changedObjects);

        verify(productToOpenCvMapper, times(1)).prepareListInfo(any(), eq(false));

        verify(micrometerService, times(1)).getCounterByName(getCounter("changedObjectsCount"));
        verify(changedObjectCounter, times(1)).increment(changedObjects.size());

        verify(micrometerService, times(0)).getCounterByName(getCounter("saveCommandsCount"));

        verify(micrometerService, times(0)).getCounterByName(getCounter("deleteCommandsCount"));

        verify(micrometerService, times(1)).getCounterByName(getCounter("errorsCount"));
        verify(errorCounter, times(1)).increment();
    }

    @Test
    public void testSendDeleteCommand() throws ReflectiveOperationException {
        when(micrometerService.getCounterByName(getCounter("deleteCommandsCount"))).thenReturn(deleteCounter);

        Product product = createOpenCvProduct(1L);
        List<ChangedObject> changedObjects = Collections.singletonList(new ChangedObject(product, false));

        // Отправляется команда на удаление, потому что статус перестал быть PUBLISHED
        ProductHistoryDTOV3 productHistory = new ProductHistoryDTOV3();
        productHistory.setProductState(ProductState.PUBLISHED);
        product.setSavedState(productHistory);

        Product cloneProduct = createOpenCvProduct(1L);
        cloneProduct.setProductState(ProductState.HIDDEN);
        when(productRepository.findById(product.getId())).thenReturn(Optional.of(cloneProduct));

        callRunProcessingChangedObjects(changedObjects);

        verify(productToOpenCvMapper, times(0)).prepareListInfo(any(), anyBoolean());

        verify(micrometerService, times(1)).getCounterByName(getCounter("changedObjectsCount"));
        verify(changedObjectCounter, times(1)).increment(changedObjects.size());

        verify(micrometerService, times(0)).getCounterByName(getCounter("saveCommandsCount"));

        verify(micrometerService, times(1)).getCounterByName(getCounter("deleteCommandsCount"));
        verify(deleteCounter, times(1)).increment();

        verify(micrometerService, times(0)).getCounterByName(getCounter("errorsCount"));
    }

    @Test
    public void testSendDeleteCommand2() throws ReflectiveOperationException {
        when(micrometerService.getCounterByName(getCounter("deleteCommandsCount"))).thenReturn(deleteCounter);

        Product product = createOpenCvProduct(1L);
        List<ChangedObject> changedObjects = Collections.singletonList(new ChangedObject(product, false));

        // productRepository ничего не возвращает, отправляется команда удаления
        callRunProcessingChangedObjects(changedObjects);

        verify(productToOpenCvMapper, times(0)).prepareListInfo(any(), anyBoolean());

        verify(micrometerService, times(1)).getCounterByName(getCounter("changedObjectsCount"));
        verify(changedObjectCounter, times(1)).increment(changedObjects.size());

        verify(micrometerService, times(0)).getCounterByName(getCounter("saveCommandsCount"));

        verify(micrometerService, times(1)).getCounterByName(getCounter("deleteCommandsCount"));
        verify(deleteCounter, times(1)).increment();

        verify(micrometerService, times(0)).getCounterByName(getCounter("errorsCount"));
    }

    private void callRunProcessingChangedObjects(List<ChangedObject> changedObjects) throws ReflectiveOperationException {
        Method method = AbstractProductsToExternalStorageEventuallySender.class
                .getDeclaredMethod("runProcessingChangedObjects", List.class);
        method.setAccessible(true);
        method.invoke(productsToOpenCvStorageEventuallySender, changedObjects);
    }

    private static String getCounterPrefix() {
        return "ProductsToOpenCvStorageEventuallySender";
    }

    private static String getCounter(String name) {
        return getCounterPrefix() + ".processChangedObjects." + name;
    }
}
