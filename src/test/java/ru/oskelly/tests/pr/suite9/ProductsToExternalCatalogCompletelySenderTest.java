package ru.oskelly.tests.pr.suite9;

import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import io.micrometer.core.instrument.Counter;
import org.apache.kafka.common.KafkaException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import ru.oskelly.common.messaging.messages.DeleteOutdatedProductsCommand;
import ru.oskelly.common.messaging.messages.SaveProductCommand;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import su.reddot.domain.dao.UserTagRepository;
import su.reddot.domain.dao.product.ProductAttributeValueBindingRepository;
import su.reddot.domain.dao.product.ProductItemRepository;
import su.reddot.domain.model.QBrand;
import su.reddot.domain.model.category.Category;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.QProduct;
import su.reddot.domain.model.user.QUser;
import su.reddot.domain.service.adminpanel.tag.UserCommonTagService;
import su.reddot.domain.service.attribute.AttributeService;
import su.reddot.domain.service.estimation.EstimatedDateCalculator;
import su.reddot.domain.service.externalcatalog.ProductFiltersToExternalCatalogManager;
import su.reddot.domain.service.externalcatalog.ProductsToExternalCatalogCompletelySender;
import su.reddot.domain.service.externalcatalog.ProductsToExternalCatalogExecutionIdGenerator;
import su.reddot.domain.service.externalcatalog.ProductsToExternalCatalogRevisionGenerator;
import su.reddot.domain.service.filter.ProductMapper;
import su.reddot.domain.service.kafka.KafkaSenderService;
import su.reddot.domain.service.metric.MicrometerService;
import su.reddot.domain.service.product.ProductExclusiveSelectionService;
import su.reddot.domain.service.product.ProductTagService;
import su.reddot.infrastructure.util.CallInTransaction;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static ru.oskelly.tests.pr.suite9.OpenCvTestUtils.createProductToExternalCatalog;

@ExtendWith(MockitoExtension.class)
@DevSuite(value = TestSuiteName.TEST_SUITE_09)
public class ProductsToExternalCatalogCompletelySenderTest {
    private ProductsToExternalCatalogCompletelySender productsToExternalCatalogCompletelySender;

    @Mock
    private Counter counter;
    @Mock
    private JPAQuery jpaQuery;
    @Mock
    private JPAQueryFactory jpaQueryFactory;
    @Mock
    private KafkaSenderService kafkaSenderService;
    @Mock
    private MicrometerService micrometerService;
    @Mock
    private AttributeService attributeService;
    @Mock
    private ProductItemRepository productItemRepository;
    @Mock
    private ProductTagService productTagService;
    @Mock
    private ProductAttributeValueBindingRepository productAttributeValueBindingRepository;
    @Mock
    private ProductExclusiveSelectionService productExclusiveSelectionService;
    @Mock
    private UserTagRepository userTagRepository;
    @Mock
    private UserCommonTagService userCommonTagService;
    @Mock
    private EstimatedDateCalculator estimatedDateCalculator;

    @InjectMocks
    private ProductMapper productMapper;
    @InjectMocks
    private CallInTransaction callInTransaction;
    @InjectMocks
    private ProductFiltersToExternalCatalogManager filtersManager;
    @InjectMocks
    private ProductsToExternalCatalogRevisionGenerator revisionGenerator;
    @InjectMocks
    private ProductsToExternalCatalogExecutionIdGenerator executionIdGenerator;

    @BeforeEach
    public void init() {
        productsToExternalCatalogCompletelySender = new ProductsToExternalCatalogCompletelySender(
                productMapper,
                micrometerService,
                callInTransaction,
                kafkaSenderService,
                filtersManager,
                revisionGenerator,
                executionIdGenerator,
                jpaQueryFactory,
                "CATALOG-COMPLETELY-TOPIC",
                5,
                0,
                10
        );

        doReturn(jpaQuery).when(jpaQueryFactory).from(eq(QProduct.product));

        when(jpaQuery.where((Predicate) any())).thenReturn(jpaQuery);
        when(jpaQuery.fetchJoin()).thenReturn(jpaQuery);
        when(jpaQuery.innerJoin((EntityPath<QBrand>) any())).thenReturn(jpaQuery);
        when(jpaQuery.innerJoin((EntityPath<QUser>) any(), any())).thenReturn(jpaQuery);
        when(jpaQuery.leftJoin((EntityPath) any())).thenReturn(jpaQuery);
        when(jpaQuery.offset(anyLong())).thenReturn(jpaQuery);
        when(jpaQuery.limit(anyLong())).thenReturn(jpaQuery);
        when(jpaQuery.orderBy((OrderSpecifier<?>) any())).thenReturn(jpaQuery);
        when(jpaQuery.select((Expression<?>) any())).thenReturn(jpaQuery);
        when(jpaQuery.distinct()).thenReturn(jpaQuery);

        when(micrometerService.getCounterByName(any())).thenReturn(counter);

        when(attributeService.getAttributeWithValueDTOs(any())).thenReturn(Collections.emptyList());
        when(productAttributeValueBindingRepository.getAttributeValueIdsMap(any())).thenReturn(Collections.emptyMap());
        when(productExclusiveSelectionService.getExclusiveSelectionSignAsBoolean(any())).thenReturn(false);
        when(productItemRepository.getProductItemsForSendingToExternalCatalog(any())).thenReturn(Collections.emptyList());
        when(productTagService.getProductTagsForProducts(any())).thenReturn(Collections.emptyMap());
    }

    @Test
    public void testSendProductSuccess() throws ReflectiveOperationException {
        int productsCount = 1;
        when(jpaQuery.fetch()).thenReturn(createProducts(productsCount));
        when(jpaQuery.fetchCount()).thenReturn(Long.valueOf(productsCount));
        initCategory(1L);

        callSendProducts();

        // успешная отправка dto в кафка
        verify(kafkaSenderService, times(productsCount * 2)).sendProductToExternalCatalogMessageSilent(any(), any(), any());
        verify(micrometerService, times(productsCount))
                .getCounterByName(getCounterPrefix() + ".sendProducts.saveCommandsCount");

        // errorsCount извлекается в sendProductsCompletely и увеличивается в тесте на 0
        verify(micrometerService, times(1))
                .getCounterByName(getCounterPrefix() + ".sendProducts.errorsCount");
        verify(counter, times(1)).increment(0);
    }

    @Test
    public void testSendProductKafkaError() throws ReflectiveOperationException {
        int productsCount = 1;
        when(jpaQuery.fetch()).thenReturn(createProducts(productsCount));
        when(jpaQuery.fetchCount()).thenReturn(Long.valueOf(productsCount));
        doThrow(new KafkaException("Error"))
                .when(kafkaSenderService)
                .sendProductToExternalCatalogMessageSilent(any(), any(), ArgumentMatchers.any(SaveProductCommand.class));
        initCategory(1L);

        callSendProducts();

        // успешная отправка dto в кафка
        verify(kafkaSenderService, times(1))
                .sendProductToExternalCatalogMessageSilent(any(), any(), ArgumentMatchers.any(SaveProductCommand.class));
        verify(micrometerService, times(0))
                .getCounterByName(getCounterPrefix() + ".sendProducts.saveCommandsCount");

        verify(micrometerService, times(1))
                .getCounterByName(getCounterPrefix() + ".sendProducts.errorsCount");
        verify(counter, times(1)).increment(1);
        verify(kafkaSenderService, times(0))
                .sendProductToExternalCatalogMessageSilent(any(), any(), ArgumentMatchers.any(DeleteOutdatedProductsCommand.class));
    }

    private void callSendProducts() throws ReflectiveOperationException {
        //Из-за aop через javaagent не работает sendProductsCompletelyAsync
        // потому что AnnotationAsyncExecutionAspect аспект все равно подключается, даже в юнит-тесте.
        Method sendMethod = ProductsToExternalCatalogCompletelySender.class.getSuperclass()
                .getDeclaredMethod("sendProductsCompletely", List.class);
        sendMethod.setAccessible(true);
        sendMethod.invoke(productsToExternalCatalogCompletelySender, new Object[]{null});
    }

    private String getCounterPrefix() {
        return "ProductsToExternalCatalogCompletelySender";
    }

    private List<Product> createProducts(int count) {
        List<Product> products = new ArrayList<>();

        for (int i = 0; i < count; i++) {
            products.add(createProductToExternalCatalog(i + 1));
        }

        return products;
    }

    private void initCategory(long index) {
        Category parent = new Category();
        parent.setLeftOrder(1);

        Category category = new Category();
        category.setId(index);
        category.setParent(parent);

        Map<Long, Category> categoryMap = new HashMap<>();
        categoryMap.put(index, category);
        Category.setCategoryCache(categoryMap);
    }

}
