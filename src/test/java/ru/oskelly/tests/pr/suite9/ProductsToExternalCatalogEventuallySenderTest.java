package ru.oskelly.tests.pr.suite9;

import io.micrometer.core.instrument.Counter;
import org.apache.kafka.common.KafkaException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import su.reddot.domain.dao.UserTagRepository;
import su.reddot.domain.dao.product.ProductAttributeValueBindingRepository;
import su.reddot.domain.dao.product.ProductItemRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.service.adminpanel.product.domain.ProductHistoryDTOV3;
import su.reddot.domain.service.adminpanel.tag.UserCommonTagService;
import su.reddot.domain.service.attribute.AttributeService;
import su.reddot.domain.service.catalog.CategoryService;
import su.reddot.domain.service.estimation.EstimatedDateCalculator;
import su.reddot.domain.service.externalcatalog.AbstractProductsToExternalStorageEventuallySender;
import su.reddot.domain.service.externalcatalog.ProductFiltersToExternalCatalogManager;
import su.reddot.domain.service.externalcatalog.ProductsToExternalCatalogEventuallySender;
import su.reddot.domain.service.externalcatalog.ProductsToExternalCatalogExecutionIdGenerator;
import su.reddot.domain.service.externalcatalog.ProductsToExternalCatalogRevisionGenerator;
import su.reddot.domain.service.externalcatalog.ProductsToExternalCatalogSendingSynchronization.ChangedObject;
import su.reddot.domain.service.filter.ProductMapper;
import su.reddot.domain.service.kafka.KafkaSenderService;
import su.reddot.domain.service.metric.MicrometerService;
import su.reddot.domain.service.product.ProductExclusiveSelectionService;
import su.reddot.domain.service.product.ProductTagService;
import su.reddot.domain.service.staticresource.StaticResourceBalancer;
import su.reddot.infrastructure.util.CallInTransaction;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static ru.oskelly.tests.pr.suite9.OpenCvTestUtils.createProductToExternalCatalog;
import static ru.oskelly.tests.pr.suite9.OpenCvTestUtils.initCategory;

@ExtendWith(MockitoExtension.class)
@DevSuite(value = TestSuiteName.TEST_SUITE_09)
public class ProductsToExternalCatalogEventuallySenderTest {
    private ProductsToExternalCatalogEventuallySender productsToExternalCatalogEventuallySender;

    @Mock
    private CategoryService categoryService;
    @Mock
    private Counter changedObjectCounter;
    @Mock
    private Counter saveCounter;
    @Mock
    private Counter deleteCounter;
    @Mock
    private Counter errorCounter;
    @Mock
    private KafkaSenderService kafkaSenderService;
    @Mock
    private MicrometerService micrometerService;
    @Mock
    private AttributeService attributeService;
    @Mock
    private ProductExclusiveSelectionService productExclusiveSelectionService;
    @Mock
    private ProductRepository productRepository;
    @Mock
    private ProductItemRepository productItemRepository;
    @Mock
    private ProductTagService productTagService;
    @Mock
    private ProductAttributeValueBindingRepository productAttributeValueBindingRepository;
    @Mock
    private UserTagRepository userTagRepository;
    @Mock
    private StaticResourceBalancer staticResourceBalancer;
    @Mock
    private UserCommonTagService userCommonTagService;
    @Mock
    private EstimatedDateCalculator estimatedDateCalculator;

    @InjectMocks
    private ProductMapper productMapper;
    @InjectMocks
    private ProductFiltersToExternalCatalogManager filtersManager;
    @InjectMocks
    private CallInTransaction callInTransaction;
    @InjectMocks
    private ProductsToExternalCatalogRevisionGenerator revisionGenerator;
    @InjectMocks
    private ProductsToExternalCatalogExecutionIdGenerator executionIdGenerator;

    @BeforeEach
    public void init() {
        productsToExternalCatalogEventuallySender = new ProductsToExternalCatalogEventuallySender(
                productMapper,
                kafkaSenderService,
                filtersManager,
                callInTransaction,
                productRepository,
                micrometerService,
                executionIdGenerator,
                revisionGenerator
        );

        filtersManager.init();

        when(micrometerService.getCounterByName(getCounter("changedObjectsCount"))).thenReturn(changedObjectCounter);
    }

    @Test
    public void testSendProductSuccess() throws ReflectiveOperationException {
        mockServices();
        when(micrometerService.getCounterByName(getCounter("saveCommandsCount"))).thenReturn(saveCounter);

        Product product = createProductToExternalCatalog(1L);
        List<ChangedObject> changedObjects = Collections.singletonList(new ChangedObject(product, false));
        initCategory(1L);

        Product cloneProduct = createProductToExternalCatalog(1L);
        when(productRepository.findById(product.getId())).thenReturn(Optional.of(cloneProduct));

        callRunProcessingChangedObjects(changedObjects);

        verify(micrometerService, times(1)).getCounterByName(getCounter("changedObjectsCount"));
        verify(changedObjectCounter, times(1)).increment(changedObjects.size());

        verify(micrometerService, times(1)).getCounterByName(getCounter("saveCommandsCount"));
        verify(saveCounter, times(1)).increment();

        verify(micrometerService, times(0)).getCounterByName(getCounter("deleteCommandsCount"));
        verify(micrometerService, times(0)).getCounterByName(getCounter("errorsCount"));
    }

    @Test
    public void testSendProductIgnored() throws ReflectiveOperationException {
        // сообщение в kafka не отправляется, если статус не равен PUBLISHED, и не был PUBLISHED
        Product product = createProductToExternalCatalog(1L);
        List<ChangedObject> changedObjects = Collections.singletonList(new ChangedObject(product, false));

        ProductHistoryDTOV3 productHistory = new ProductHistoryDTOV3();
        productHistory.setProductState(ProductState.DRAFT);
        product.setSavedState(productHistory);

        Product cloneProduct = createProductToExternalCatalog(1L);
        cloneProduct.setProductState(ProductState.DRAFT);
        when(productRepository.findById(product.getId())).thenReturn(Optional.of(cloneProduct));

        callRunProcessingChangedObjects(changedObjects);

        verify(micrometerService, times(1)).getCounterByName(getCounter("changedObjectsCount"));
        verify(changedObjectCounter, times(1)).increment(changedObjects.size());

        verify(micrometerService, times(0)).getCounterByName(getCounter("saveCommandsCount"));
        verify(micrometerService, times(0)).getCounterByName(getCounter("deleteCommandsCount"));
        verify(micrometerService, times(0)).getCounterByName(getCounter("errorsCount"));
    }

    @Test
    public void testSendProductKafkaError() throws ReflectiveOperationException {
        when(micrometerService.getCounterByName(getCounter("errorsCount"))).thenReturn(errorCounter);

        Product product = createProductToExternalCatalog(1L);
        List<ChangedObject> changedObjects = Collections.singletonList(new ChangedObject(product, false));

        when(productRepository.findById(product.getId())).thenReturn(Optional.of(product));
        doThrow(new KafkaException("Error")).when(kafkaSenderService).sendProductToExternalCatalogMessageSilent(any(), any(), any());

        callRunProcessingChangedObjects(changedObjects);

        verify(micrometerService, times(1)).getCounterByName(getCounter("changedObjectsCount"));
        verify(changedObjectCounter, times(1)).increment(changedObjects.size());

        verify(micrometerService, times(0)).getCounterByName(getCounter("saveCommandsCount"));

        verify(micrometerService, times(0)).getCounterByName(getCounter("deleteCommandsCount"));

        verify(micrometerService, times(1)).getCounterByName(getCounter("errorsCount"));
        verify(errorCounter, times(1)).increment();
    }

    @Test
    public void testSendDeleteCommand() throws ReflectiveOperationException {
        when(micrometerService.getCounterByName(getCounter("deleteCommandsCount"))).thenReturn(deleteCounter);

        Product product = createProductToExternalCatalog(1L);
        List<ChangedObject> changedObjects = Collections.singletonList(new ChangedObject(product, false));

        // Отправляется команда на удаление, потому что статус перестал быть PUBLISHED
        ProductHistoryDTOV3 productHistory = new ProductHistoryDTOV3();
        productHistory.setProductState(ProductState.PUBLISHED);
        product.setSavedState(productHistory);

        Product cloneProduct = createProductToExternalCatalog(1L);
        cloneProduct.setProductState(ProductState.HIDDEN);
        when(productRepository.findById(product.getId())).thenReturn(Optional.of(cloneProduct));

        callRunProcessingChangedObjects(changedObjects);

        verify(micrometerService, times(1)).getCounterByName(getCounter("changedObjectsCount"));
        verify(changedObjectCounter, times(1)).increment(changedObjects.size());

        verify(micrometerService, times(0)).getCounterByName(getCounter("saveCommandsCount"));

        verify(micrometerService, times(1)).getCounterByName(getCounter("deleteCommandsCount"));
        verify(deleteCounter, times(1)).increment();

        verify(micrometerService, times(0)).getCounterByName(getCounter("errorsCount"));
    }

    @Test
    public void testSendDeleteCommand2() throws ReflectiveOperationException {
        when(micrometerService.getCounterByName(getCounter("deleteCommandsCount"))).thenReturn(deleteCounter);

        Product product = createProductToExternalCatalog(1L);
        List<ChangedObject> changedObjects = Collections.singletonList(new ChangedObject(product, false));

        // productRepository ничего не возвращает, отправляется команда удаления
        callRunProcessingChangedObjects(changedObjects);

        verify(micrometerService, times(1)).getCounterByName(getCounter("changedObjectsCount"));
        verify(changedObjectCounter, times(1)).increment(changedObjects.size());

        verify(micrometerService, times(0)).getCounterByName(getCounter("saveCommandsCount"));

        verify(micrometerService, times(1)).getCounterByName(getCounter("deleteCommandsCount"));
        verify(deleteCounter, times(1)).increment();

        verify(micrometerService, times(0)).getCounterByName(getCounter("errorsCount"));
    }

    private void callRunProcessingChangedObjects(List<ChangedObject> changedObjects) throws ReflectiveOperationException {
        Method method = AbstractProductsToExternalStorageEventuallySender.class
                .getDeclaredMethod("runProcessingChangedObjects", List.class);
        method.setAccessible(true);
        method.invoke(productsToExternalCatalogEventuallySender, changedObjects);
    }

    private static String getCounterPrefix() {
        return "ProductsToExternalCatalogEventuallySender";
    }

    private static String getCounter(String name) {
        return getCounterPrefix() + ".processChangedObjects." + name;
    }

    private void mockServices() {
        when(attributeService.getAttributeWithValueDTOs(any())).thenReturn(Collections.emptyList());
        when(productAttributeValueBindingRepository.getAttributeValueIdsMap(any())).thenReturn(Collections.emptyMap());
        when(productExclusiveSelectionService.getExclusiveSelectionSignAsBoolean(any())).thenReturn(false);
        when(productItemRepository.getProductItemsForSendingToExternalCatalog(any())).thenReturn(Collections.emptyList());
        when(productTagService.getProductTagsForProducts(any())).thenReturn(Collections.emptyMap());
    }
}
