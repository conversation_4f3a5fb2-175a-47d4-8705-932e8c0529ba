package ru.oskelly.tests.pr.suite9;

import io.micrometer.core.instrument.Timer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.util.ReflectionTestUtils;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import su.reddot.domain.exception.OpenCvException;
import su.reddot.domain.service.dto.photosearch.LocationDTO;
import su.reddot.domain.service.dto.photosearch.OneSearchResultDTO;
import su.reddot.domain.service.integration.opencv.OpenCvService;
import su.reddot.domain.service.metric.MicrometerService;
import su.reddot.infrastructure.opencv.OpenCvClient;
import su.reddot.infrastructure.opencv.view.OpenCvStatus;
import su.reddot.infrastructure.opencv.view.PostProductSearchResult;
import su.reddot.infrastructure.opencv.view.SearchResultResponse;
import su.reddot.presentation.api.v2.filter.SearchProductPhotoResultsResponse;

import java.util.LinkedList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ActiveProfiles(profiles = AbstractSpringTest.testProfiles)
@ExtendWith(MockitoExtension.class)
@DevSuite(value = TestSuiteName.TEST_SUITE_09)
public class OpenCvServiceTest {
    @Mock
    private OpenCvClient openCvClient;
    @Mock
    private MessageSourceAccessor messageSourceAccessor;
    @Mock
    private MicrometerService micrometerService;
    @Mock
    private Timer timer;

    private OpenCvService openCvService;

    @BeforeEach
    public void setOpenCvClient() {
        openCvService = new OpenCvService(openCvClient, messageSourceAccessor, micrometerService);
        ReflectionTestUtils.setField(openCvService, "productsInGroupLimit", 5);
        ReflectionTestUtils.setField(openCvService, "catalogSyncEnabled", true);
    }

    @Test
    void testStartSearch() {
        when(openCvClient.search(anyString())).thenReturn("task_id");
        assertEquals("task_id", openCvService.startSearch("task_id"));
    }

    @ParameterizedTest
    @CsvSource({
            "STARTED",
            "QUEUED"
    })
    void testCheckTaskDelayed(OpenCvStatus status) {
        SearchResultResponse resultResponse = new SearchResultResponse();
        resultResponse.setStatus(status.getStatus());
        when(openCvClient.getTask(anyString())).thenReturn(resultResponse);

        SearchProductPhotoResultsResponse expectedResponse = new SearchProductPhotoResultsResponse();
        expectedResponse.setRetryDelay(OpenCvService.RETRY_DELAY_MILLIS);

        SearchProductPhotoResultsResponse response = openCvService.checkTask("task_id", 1L);
        assertEquals(expectedResponse.getRetryDelay(), response.getRetryDelay());
        assertNull(response.getObjects());
        assertNull(response.getProductIds());
        verify(micrometerService, never()).getTimerByName(anyString());
    }

    @Test
    void testCheckTaskSuccess() {
        Double[][] centers = new Double[][]{{0.1, 0.2}, {0.3, 0.4}};
        Long[][] productIds = new Long[][]{{1L, 2L}, {4L, 5L}};
        SearchResultResponse resultResponse = createOpenCvClientResponse(centers, productIds);

        when(openCvClient.getTask(anyString())).thenReturn(resultResponse);
        when(micrometerService.getTimerByName(anyString())).thenReturn(timer);

        // expected result
        Long[] productIdsByRelevant = new Long[]{1L, 4L, 2L, 5L};
        SearchProductPhotoResultsResponse expectedResponse = createOpenCvServiceResponse(
                productIdsByRelevant, createMapOfObjects(productIds, centers)
        );

        SearchProductPhotoResultsResponse realResponse = openCvService.checkTask("task_id", 1L);

        assertNull(realResponse.getRetryDelay());
        assertArrayEquals(expectedResponse.getProductIds(), realResponse.getProductIds());
        compareObjects(expectedResponse.getObjects(), realResponse.getObjects());
    }

    @Test
    void testCheckTaskSuccessWithProductLimitation() {
        Double[][] centers = new Double[][]{{0.1, 0.2}, {0.3, 0.4}};
        Long[][] productIds = new Long[][]{{1L, 2L, 3L, 4L, 5L, 6L, 7L}, {4L, 5L}};
        SearchResultResponse resultResponse = createOpenCvClientResponse(centers, productIds);

        when(openCvClient.getTask(anyString())).thenReturn(resultResponse);
        when(micrometerService.getTimerByName(anyString())).thenReturn(timer);

        // Кол-во productId сокращается до ограничения (в тестах 5 шт.)
        Long[] productIdsByRelevant = new Long[]{1L, 4L, 2L, 5L, 3L};
        Long[][] expectedProductIds = new Long[][]{{1L, 2L, 3L, 4L, 5L}, {4L, 5L}};
        SearchProductPhotoResultsResponse expectedResponse = createOpenCvServiceResponse(
                productIdsByRelevant, createMapOfObjects(expectedProductIds, centers)
        );

        SearchProductPhotoResultsResponse realResponse = openCvService.checkTask("task_id", 1L);

        assertNull(realResponse.getRetryDelay());
        assertArrayEquals(expectedResponse.getProductIds(), realResponse.getProductIds());
        compareObjects(expectedResponse.getObjects(), realResponse.getObjects());
    }

    @Test
    void testCheckTaskWrongCentersProductIdsDimension() {
        SearchResultResponse resultResponse = createOpenCvClientResponse(
                new Double[][]{{0.1, 0.2}}, new Long[][]{{1L, 2L, 3L}, {4L, 5L, 6L}}
        );

        when(openCvClient.getTask(anyString())).thenReturn(resultResponse);

        OpenCvException exception = assertThrows(OpenCvException.class, () -> openCvService.checkTask("task_id", 1L));
        assertEquals(
                "An error occurred while parsing result from OpenCV. The size of the centers array is not " +
                        "equal to the size of the of product_ids array.",
                exception.getLogMessage()
        );
    }

    @Test
    void testCheckTaskWrongCentersLength() {
        SearchResultResponse resultResponse = createOpenCvClientResponse(
                new Double[][]{{0.1}}, new Long[][]{{1L, 2L, 3L}}
        );

        when(openCvClient.getTask(anyString())).thenReturn(resultResponse);

        OpenCvException exception = assertThrows(OpenCvException.class, () -> openCvService.checkTask("task_id", 1L));
        assertEquals(
                "An error occurred while parsing result from OpenCV. The array of centers less than 2.",
                exception.getLogMessage()
        );
    }

    @Test
    void testCheckTaskFailed() {
        SearchResultResponse resultResponse = new SearchResultResponse();
        resultResponse.setStatus(OpenCvStatus.FAILED.getStatus());
        when(openCvClient.getTask(anyString())).thenReturn(resultResponse);
        when(micrometerService.getTimerByName(anyString())).thenReturn(timer);

        OpenCvException exception = assertThrows(OpenCvException.class, () -> openCvService.checkTask("task_id", 1L));
        assertEquals(
                "OpenCV have not found products by image. OpenCV job status: failed",
                exception.getLogMessage()
        );
    }

    private SearchResultResponse createOpenCvClientResponse(Double[][] centers,
                                                            Long[][] productIds) {
        PostProductSearchResult postProduct = new PostProductSearchResult();
        postProduct.setCenters(centers);
        postProduct.setProductIds(productIds);

        SearchResultResponse resultResponse = new SearchResultResponse();
        resultResponse.setStatus(OpenCvStatus.FINISHED.getStatus());
        resultResponse.setResult(postProduct);

        return resultResponse;
    }

    private OneSearchResultDTO createOneSearchResultDTO(Long[] productIds, Double[] centers) {
        return new OneSearchResultDTO(
                productIds.length,
                productIds,
                new LocationDTO(centers[0], centers[1])
        );
    }

    private List<OneSearchResultDTO> createMapOfObjects(Long[][] productIds, Double[][] centers) {
        List<OneSearchResultDTO> result = new LinkedList<>();
        for (int i = 0; i < productIds.length; i++) {
            result.add(createOneSearchResultDTO(productIds[i], centers[i]));
        }
        return result;
    }

    private SearchProductPhotoResultsResponse createOpenCvServiceResponse(Long[] productIds,
                                                                          List<OneSearchResultDTO> objects) {
        SearchProductPhotoResultsResponse response = new SearchProductPhotoResultsResponse();
        response.setRetryDelay(0);
        response.setProductIds(productIds);
        response.setObjects(objects);
        return response;
    }

    private void compareObjects(List<OneSearchResultDTO> left, List<OneSearchResultDTO> right) {
        assertEquals(left.size(), right.size());

        for (int i = 0; i < left.size(); i++) {
            OneSearchResultDTO leftResult = left.get(i);
            OneSearchResultDTO rightResult = right.get(i);
            assertEquals(leftResult.getCount(), rightResult.getCount());
            assertEquals(leftResult.getLocation(), rightResult.getLocation());
            assertArrayEquals(leftResult.getProductIds(), rightResult.getProductIds());
        }
    }
}