package ru.oskelly.tests.pr;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.util.UriComponentsBuilder;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import su.reddot.component.TestApiConfiguration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public abstract class AbstractServiceTest extends AbstractSpringTest {

    @Autowired
    private TestApiConfiguration testApiConfiguration;

    private static List<String> cookie = new ArrayList<>();

    @Autowired
    protected ObjectMapper objectMapper;
    protected TestRestTemplate restTemplate = TestUtils.getTestRestTemplate();

    protected void sleep(Long mills) {
        try {
            Thread.sleep(mills);
        } catch (Throwable t) {
            //ignored
        }
    }

    protected HttpEntity getHttpEntity(Object body) {
        return body == null ? new HttpEntity<String>(getHeaders(null)) : new HttpEntity<Object>(body, getHeaders(null));
    }

    protected HttpEntity getHttpEntity(Object body, MediaType mediaType) {
        return body == null ? new HttpEntity<String>(getHeaders(mediaType)) : new HttpEntity<Object>(body, getHeaders(mediaType));
    }

    private HttpHeaders getHeaders(MediaType mediaType) {
        HttpHeaders result = new HttpHeaders();
        if (mediaType != null) result.setContentType(mediaType);
        ApiV2Client.defaultHeaders.forEach((k, v) -> result.set(k, v));
        cookie.forEach(s -> {
            result.add(HttpHeaders.COOKIE, s);
        });
        return result;
    }

    protected String getUriWithParams(String url, Map<String, String> params) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(url);
        params.forEach(builder::queryParam);
        return builder.toUriString();
    }

    protected Map<String, String> getAuthorizeParams() {
        Map<String, String> result = new HashMap<>();
        result.put("email", getEmail());
        result.put("password", TestUtils.getApiHashedPassword(getPassword(), getEmail()));
        return result;
    }

    protected abstract String getEmail();

    protected abstract String getPassword();
}
