package ru.oskelly.tests.pr.suite4.presentation.api.v2.admin;

/*
 * Created by <PERSON> on 2/12/2021
 */

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.notification.NotificationRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.dao.userban.UserBanRepository;
import su.reddot.domain.exception.userban.CommentBanException;
import su.reddot.domain.exception.userban.PublishBanException;
import su.reddot.domain.model.notification.userban.BanNotification;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.model.user.userban.BanType;
import su.reddot.domain.model.user.userban.UserBan;
import su.reddot.domain.service.comment.product.ProductCommentService;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.domain.service.dto.userban.AdminPanelUserBanDTO;
import su.reddot.domain.service.dto.userban.UserBanActionDTO;
import su.reddot.domain.service.productpublication.DefaultProductPublicationService;
import su.reddot.domain.service.user.UserService;
import su.reddot.domain.service.user.userban.interfaces.UserBanService;
import su.reddot.infrastructure.security.SecurityService;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


/**
 * В работе с реальной базе на разрешается запускать данный тест
 */
@EnableAsync
@AutoConfigureMockMvc
@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_04)
public class AdminBarginsControllerTest extends AbstractSpringTest {

    @Value("${test.api.ban-user-email}")
    private String email;
    @Value("${test.api.ban-user-password}")
    private String password;
    @Value("${test.api.ban-user-id}")
    private Long userId;

    ObjectMapper mapper;
    private static User seller;

    @Autowired
    private WebApplicationContext context;

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private UserBanService userBanService;

    @Autowired
    private UserBanRepository userBanRepository;

    @Autowired
    private SecurityService securityService;

    @Autowired
    private UserService userService;

    @Autowired
    private ProductRepository productRepository;

    @Autowired
    private ProductCommentService commentService;

    @Autowired
    private DefaultProductPublicationService publicationService;

    @Autowired
    private NotificationRepository<BanNotification> notificationRepository;

    @Autowired
    private OpenEntityManagerInViewFilter openEntityManagerInViewFilter;

    @BeforeEach
    public void init() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.context).addFilters(openEntityManagerInViewFilter).build();
        //логин модератором администратором
        securityService.authenticateByEmailAndRawPassword(email, password);
        mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
    }

    @Test
    public void _01_1_0_saveTest() throws Exception {

        //удаляем все данные из таблицы user_ban
        List<UserBan> bans = userBanRepository.findAll();

        if (!bans.isEmpty()) {
            userBanRepository.deleteAllInBatch();
        }

        //удаляем все данные из таблицы notification для userId
        List<BanNotification> notifications = notificationRepository.findAllByUser(userId);

        if (!notifications.isEmpty()) {
            notificationRepository.deleteInBatch(notifications);
        }

        String json = "";

        //поиск пользователя с опубликованным товаром
        int count = 0;
        for (long i = 1;;i++) {
            seller = getUser(i);
            count = productRepository.countProductBySellerAndProductState(seller, ProductState.PUBLISHED);
            if (count > 5) {
                break;
            }
        }

        //удаляем все данные из таблицы notification для seller
        notifications = notificationRepository.findAllByUser(seller.getId());

        if (!notifications.isEmpty()) {
            notificationRepository.deleteInBatch(notifications);
        }

        //запрет на публикацию
        List<Product> productList = productRepository.findProductsBySellerIdAndProductState(seller.getId(), ProductState.PUBLISHED);

        if (!productList.isEmpty()) {

            UserBanActionDTO publishBanDTO = new UserBanActionDTO(seller.getId(), BanType.PUBLISH_BAN,
                    ZonedDateTime.now().plusDays(7L), "Test", null);

            json = mapper.writeValueAsString(publishBanDTO);

            mockMvc.perform(post("/adminpanel/bans")
                    .contentType(MediaType.APPLICATION_JSON_VALUE)
                    .content(json))
                    .andExpect(status().isOk());
            productList = productRepository.getAllBySeller(seller);
            boolean checkState = productList.stream().anyMatch(a -> a.getProductState().equals(ProductState.BANED));

            assertTrue(checkState);
        }

        int bannedCount = productRepository.countProductBySellerAndProductState(seller, ProductState.BANED);

        assertEquals(count, bannedCount);
        productList = productRepository.findProductsBySellerIdAndProductState(seller.getId(), ProductState.BANED);
        productList.forEach(a -> a.setProductState(ProductState.PUBLISHED));
        productRepository.saveAll(productList);


        //запрет на комментарий
        UserBanActionDTO commentBanDTO = new UserBanActionDTO(userId, BanType.COMMENT_BAN,
                ZonedDateTime.now().plusDays(7L), "Test", null);

        json = mapper.writeValueAsString(commentBanDTO);

        mockMvc.perform(post("/adminpanel/bans")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(json))
                .andExpect(status().isOk());

        //запрет на публикацию сторис
        UserBanActionDTO storiesPublishBanDTO = new UserBanActionDTO(userId, BanType.STORIES_BAN,
                ZonedDateTime.now().plusDays(7L), "Test", null);

        json = mapper.writeValueAsString(storiesPublishBanDTO);

        mockMvc.perform(post("/adminpanel/bans")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(json))
                .andExpect(status().isOk());

        //предупреждение
        UserBanActionDTO warningBanDTO = new UserBanActionDTO(userId, BanType.WARNING,
                ZonedDateTime.now().plusDays(7L), "Test", null);

        json = mapper.writeValueAsString(warningBanDTO);

        mockMvc.perform(post("/adminpanel/bans")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(json))
                .andExpect(status().isOk());

        List<AdminPanelUserBanDTO> list = userBanService.getAllUserBansByUserId(userId, 0, 4);

        assertEquals(3, list.size());

        //проверка блокировки аккаунта пользователя и обновления состояние товара пользователя на BANED

        productList = productRepository.findProductsBySellerIdAndProductState(seller.getId(), ProductState.PUBLISHED);
        if (!productList.isEmpty()) {

            UserBanActionDTO banDTO = new UserBanActionDTO(seller.getId(), BanType.USER_BAN,
                    ZonedDateTime.now().plusDays(7L), "Test", null);

            json = mapper.writeValueAsString(banDTO);

            mockMvc.perform(post("/adminpanel/bans")
                    .contentType(MediaType.APPLICATION_JSON_VALUE)
                    .content(json))
                    .andExpect(status().isOk());
            productList = productRepository.getAllBySeller(seller);
            boolean checkState = productList.stream().anyMatch(a -> a.getProductState().equals(ProductState.BANED));

            assertTrue(checkState);
        }
        bannedCount = productRepository.countProductBySellerAndProductState(seller, ProductState.BANED);

        assertEquals(count, bannedCount);
    }

    @Test
    public void _01_1_1_commentBanTest() {
        try {
            commentService.publishComment(userId, null, null, null, null);
            fail("Должно было выброситься CommentBanException");
        } catch (CommentBanException e){
        }

    }

    @Test
    public void _01_2_publishBanTest() throws Exception {

        UserBanActionDTO publishBanDTO = new UserBanActionDTO(userId, BanType.PUBLISH_BAN,
                ZonedDateTime.now().plusDays(7L), "Test", null);

        String json = mapper.writeValueAsString(publishBanDTO);

        mockMvc.perform(post("/adminpanel/bans")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(json))
                .andExpect(status().isOk());

        try {
            publicationService.publishProduct(new ProductDTO());
            fail("Должно было выброситься PublishBanException");
        } catch (PublishBanException e){
        }

    }

    @Test
    public void _02_cancelTest() throws Exception {
        List<UserBan> list = userBanRepository.findUserActiveAllBansOnAction(userId);

        assertEquals(3, list.size());

        List<Long> banIdList = new ArrayList<>();

        for (UserBan b : list) {
            mockMvc.perform(patch("/adminpanel/bans/" + b.getId())
                    .contentType(MediaType.APPLICATION_JSON_VALUE))
                    .andExpect(status().isOk());
            banIdList.add(b.getId());
        }

        for (Long l : banIdList) {
            Optional<UserBan> u = userBanRepository.findById(l);
            u.ifPresent(a -> assertFalse(a.isBaned()));
        }

        list = userBanRepository.findUserActiveAllBansOnAction(userId);
        assertEquals(0, list.size());
    }

    @Test
    public void _03_deleteUserBanTest() throws Exception {

        List<UserBan> list = userBanRepository.findAllByUserId(seller.getId(), PageRequest.of(0, 2))
                .stream()
                .filter(a -> a.getBanType().equals(BanType.USER_BAN))
                .collect(Collectors.toList());


        mockMvc.perform(post("/adminpanel/bans/" + list.get(0).getId())
                .contentType(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(status().isOk());

        Optional<UserBan> u = userBanRepository.findById(userId);
        u.ifPresent(a -> assertTrue(a.isDeleted()));

        if (seller.getId() != null) {

            List<Product> productList = productRepository.getAllBySeller(seller);
            boolean checkState = productList.stream().anyMatch(a -> a.getProductState().equals(ProductState.PUBLISHED));

            assertEquals(true, checkState);
        }

        list = userBanRepository.findAllByUserId(seller.getId(), PageRequest.of(0, 2))
                .stream()
                .filter(a -> a.getBanType().equals(BanType.PUBLISH_BAN))
                .collect(Collectors.toList());

        mockMvc.perform(post("/adminpanel/bans/" + list.get(0).getId())
                .contentType(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(status().isOk());
    }

    @Test
    public void _04_getAllUsersBansTest() throws Exception {

        MvcResult result = mockMvc.perform(get("/adminpanel/bans/all?pageNumber=0&pageSize=5")
                .contentType(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(status().isOk())
                .andReturn();

        List<AdminPanelUserBanDTO> list = mapper.readValue(result.getResponse().getContentAsString(),
                new TypeReference<List<AdminPanelUserBanDTO>>() {
                });

        assertEquals(5, list.size());
    }

    @Test
    public void _05_BanNotificationTest() {

        try {
            Thread.sleep(3000);
        } catch (InterruptedException ignored) {
        }

        Optional<User> one = userService.getUserById(seller.getId());
        Optional<User> two = userService.getUserById(userId);

        one.ifPresent(a -> {
            List<BanNotification> list = notificationRepository.findAllByUser(a.getId());
            assertEquals(4, list.size());
            assertTrue(list.stream().noneMatch(n -> n.getDtype().equals("CommentBanNotification")));
            assertTrue(list.stream().anyMatch(n -> n.getDtype().equals("PublishBanNotification")));
            assertTrue(list.stream().noneMatch(n -> n.getDtype().equals("PublishStoriesBanNotification")));
            assertTrue(list.stream().noneMatch(n -> n.getDtype().equals("WarningBanNotification")));
            assertTrue(list.stream().noneMatch(n -> n.getDtype().equals("CancelCommentBanNotification")));
            assertTrue(list.stream().anyMatch(n -> n.getDtype().equals("CancelPublishBanNotification")));
            assertTrue(list.stream().noneMatch(n -> n.getDtype().equals("CancelPublishStoriesBanNotification")));
            assertTrue(list.stream().anyMatch(n -> n.getDtype().equals("UserBanNotification")));
            assertTrue(list.stream().anyMatch(n -> n.getDtype().equals("CancelUserBanNotification")));
        });

        two.ifPresent(a -> {
                List<BanNotification> list = notificationRepository.findAllByUser(a.getId());
                assertEquals(7, list.size());
                assertTrue(list.stream().anyMatch(n -> n.getDtype().equals("CommentBanNotification")));
                assertTrue(list.stream().anyMatch(n -> n.getDtype().equals("PublishBanNotification")));
                assertTrue(list.stream().anyMatch(n -> n.getDtype().equals("PublishStoriesBanNotification")));
                assertTrue(list.stream().anyMatch(n -> n.getDtype().equals("WarningBanNotification")));
                assertTrue(list.stream().anyMatch(n -> n.getDtype().equals("CancelCommentBanNotification")));
                assertTrue(list.stream().anyMatch(n -> n.getDtype().equals("CancelPublishBanNotification")));
                assertTrue(list.stream().anyMatch(n -> n.getDtype().equals("CancelPublishStoriesBanNotification")));
                assertTrue(list.stream().noneMatch(n -> n.getDtype().equals("UserBanNotification")));
                assertTrue(list.stream().noneMatch(n -> n.getDtype().equals("CancelUserBanNotification")));
        });
        //всегда вызывать в последнем методе
        clearDBAfterTests();

    }

    private void clearDBAfterTests() {
        List<UserBan> banList = userBanRepository.findAllByUserId(userId, PageRequest.of(0, 1000)).getContent();

        List<UserBan> deleteBans = banList.stream().filter(a -> a.getUserId().equals(userId) && a.getStatusChangedUserId().equals(userId)).collect(Collectors.toList());

        if (!deleteBans.isEmpty()) {
            userBanRepository.deleteInBatch(deleteBans);
        }
        banList = userBanRepository.findAllByUserId(seller.getId(), PageRequest.of(0, 1000)).getContent();

        deleteBans = banList.stream().filter(a -> a.getUserId().equals(seller.getId()) && a.getStatusChangedUserId().equals(userId)).collect(Collectors.toList());

        if (!deleteBans.isEmpty()) {
            userBanRepository.deleteInBatch(deleteBans);
        }
    }

    private User getUser(Long userId) {
        return userService.getUserById(userId).orElse(null);
    }
}