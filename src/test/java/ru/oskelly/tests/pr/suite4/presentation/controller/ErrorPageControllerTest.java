package ru.oskelly.tests.pr.suite4.presentation.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.TestApiConfiguration;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_04)
public class ErrorPageControllerTest extends AbstractSpringTest {
	@Autowired
	private TestApiConfiguration testApiConfiguration;

	static ApiV2Client apiV2Client;

    @BeforeEach
    public void initialize() {
        if(apiV2Client == null) apiV2Client = new ApiV2Client(null, null);
    }

	private String getUrl(String url){
		return testApiConfiguration.getServerUrl() + url;
	}

	private void assert404page(String url, boolean withAuthorizeParams){
		ResponseEntity<String> response = apiV2Client.request(getUrl(url), null, HttpMethod.GET, null, String.class, withAuthorizeParams);
		assertTrue(response.getStatusCode() == HttpStatus.NOT_FOUND);
		assertNotNull(response.getBody());
		assertTrue(response.getBody().contains("Извините, у нас нет такой страницы") ||
				response.getBody().replace(" : ", ":").contains("\"error\":\"Not Found\""));
	}

	private void assert404page(String url){
		assert404page(url, false);
	}


	@Test
	public void getWrongUrlsTest(){
		assert404page("/asfgdgd");
		assert404page("/asfgdgd.html");
		assert404page("/asfgdgd.js");
		assert404page("/img/dfddggfg.jpg");
		assert404page("/imgs/dfddggfg.jpg");
		assert404page("/api/v2/dfddggfg");
		assert404page("/catalog/dfgdfghdg");
		assert404page("/brands/dfgdfghdg");
		assert404page("/products/ddfff/sss");
		assert404page("/products/9999999999999999");
	}

}
