package ru.oskelly.tests.pr.suite4.presentation.api.v2;

import lombok.NonNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.AccountTestSupport;
import su.reddot.component.TestApiConfiguration;
import su.reddot.domain.dao.activity.ActivityTestRepository;
import su.reddot.domain.dao.device.DeviceRepository;
import su.reddot.domain.model.activity.Activity;
import su.reddot.domain.model.activity.GetSettingsActivity;
import su.reddot.domain.model.device.Device;
import su.reddot.domain.model.device.DeviceDtype;
import su.reddot.domain.service.activity.ActivityService;
import su.reddot.domain.service.setting.SettingService;
import su.reddot.presentation.api.v2.Api2Response;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Тест, проверяющий корректность работы DeviceService.
 * Информация о клиенте получается сервером из заголовков запроса, анализируется и сохраняется в таблице device.
 * Важно, чтобы для пары (устройство + пользователь) записи в этой таблице не дублировались.
 * Уникальность устройства может быть определена по ряду признаков: токены, userAgent, ip и.д.
 * Так же данный тест частично проверяет функционал сохранения активностей.
 */
@AutoConfigureMockMvc
@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_04)
public class DeviceHeadersSupportAndActivitiesApiV2Test extends AbstractSpringTest {
 	@Autowired
 	private TestApiConfiguration testApiConfiguration;
	@Value("${test.api.user-id}")
	private Long userId;
	@Value("${test.api.user-email}")
	private String email;
	@Value("${test.api.user-password}")
	private String password;
	@Value("${test.api.user2-id}")
	private Long userId2;
	@Value("${test.api.user2-email}")
	private String email2;
	@Value("${test.api.user2-password}")
	private String password2;

	static ApiV2Client client;

	@Autowired
	private ActivityService activityService;

	@Autowired
	private ActivityTestRepository activityTestRepository;

	@Autowired
	private DeviceRepository deviceRepository;

	@Autowired
	private JdbcTemplate jdbcTemplate;

	@Autowired
	private AccountTestSupport accountTestSupport;

	private static Long deviceId;
	private static Long activityId = 0L;

	private static Long guestDeviceId;
	private static Long user1DeviceId;
	private static Long user2DeviceId;

	private static boolean initialized = false;

	private String getServiceUrl() {
		return testApiConfiguration.getServerUrl() + "/api/v2";
	}

	protected String getSettingsUrl(){
		return getServiceUrl() + "/settings";
	}

	/**
	 * Инициализация
	 */
	@BeforeEach
	public void init(){
		if(initialized) return;
		client = new ApiV2Client();
		cleanup();
		initialized = true;
	}

	/**
	 * Проверка форматирования цены в сервисе
	 */
	@Test
	public void _00_getSettingsRequestCreatesDevice_guest() {
		//Грузим настройки АПИ
		loadSettings(client);

		//Грузин еще раз, т..к в первый раз у нас не было гостевого токена в куках и наша активность не была сохранена
		loadSettings(client);
		//Сохраняем активности на сервере (чтобы не ждать)
		flushActivities();

		//Получаем последнюю ожидаемую активность и проверяем ее
		Activity lastActivity = getLastActivity(GetSettingsActivity.class, 0L, client.getOskCookie());
		assertNotNull(lastActivity);
		assertEquals(client.getOskCookie(), lastActivity.getGuestToken());
		assertNull(lastActivity.getUserId());

		//Сохраняем activityId для последующих тестов
		activityId = lastActivity.getId();

		//Получаем устройство, привязанное к активности и проверяем его
		Device device = getDevice(lastActivity.getDevice().getId());
		assertNotNull(device);
		assertNull(device.userId);
		assertClientCorrespondsToDevice(client, device, true);
		//Тип устройства ожидаемый
		assertSame(DeviceDtype.OskellyApiTestDevice, device.getDtype());

		//Сохраняем deviceId для последующих тестов
		deviceId = device.getId();
	}

	/**
	 * Обнуляем гостевой токен и все header'ы устройства, сохраняя сессию (кука)
	 * Запрашиваем настройки и эта активность привязывается к существующему девайсу, т.к. он сохранился в сессии
	 * При этом гостевой токен в записи device заменяется на новый
	 */
	@Test
	public void _01_getSettingsWithoutDeviceHeadersRequestUsesExistedDevice_guest() {
		String newGuestToken = "new_guest_token";

		client.setSetDefaultDeviceHeaders(false);
		client.replaceOskCookie(newGuestToken);

		//Грузим настройки АПИ
		loadSettings(client);

		//Возвращаем как было
		client.setSetDefaultDeviceHeaders(true);

		//Сохраняем активности на сервере (чтобы не ждать)
		flushActivities();

		//Получаем последнюю ожидаемую активность и проверяем ее
		Activity lastActivity = getLastActivity(GetSettingsActivity.class, 0L, client.getOskCookie());
		assertNotNull(lastActivity);
		//Это новая активность
		assertTrue(activityId < lastActivity.getId());
		assertEquals(client.getOskCookie(), lastActivity.getGuestToken());
		//И это точно наше новое значение
		assertEquals(newGuestToken, lastActivity.getGuestToken());
		assertNull(lastActivity.getUserId());

		//Сохраняем activityId для последующих тестов
		activityId = lastActivity.getId();

		//Получаем устройство, привязанное к активности и проверяем его
		Device device = getDevice(lastActivity.getDevice().getId());
		assertNotNull(device);
		//Это прежнее устройство
		assertEquals(deviceId, device.getId());
		assertNull(device.userId);
		assertClientCorrespondsToDevice(client, device, false);
		//Тип устройства изменился
		assertSame(DeviceDtype.OtherDevice, device.getDtype());
	}

	/**
	 * Обнуляем все, включая сессию (кука)
	 * Запрашиваем настройки и эта активность привязывается к новому пустому девайсу, т.к. нет данных для связывания старого устройства с новым и в сессии ничего нет
	 */
	@Test
	public void _02_getSettingsWithoutDeviceHeadersAndNoSessionRequestCreatesNewDevice_guest() {
		String newGuestToken = "new_guest_token1";

		client.setSetDefaultDeviceHeaders(false);
		client.replaceOskCookie(newGuestToken);
		client.removeCookie("SESSION");

		//Грузим настройки АПИ
		loadSettings(client);

		//Сохраняем активности на сервере (чтобы не ждать)
		flushActivities();

		//Получаем последнюю ожидаемую активность и проверяем ее
		Activity lastActivity = getLastActivity(GetSettingsActivity.class, 0L, client.getOskCookie());
		assertNotNull(lastActivity);
		//Это новая активность
		assertTrue(activityId < lastActivity.getId());
		assertEquals(client.getOskCookie(), lastActivity.getGuestToken());
		//И это точно наше новое значение
		assertEquals(newGuestToken, lastActivity.getGuestToken());
		assertNull(lastActivity.getUserId());

		//Сохраняем activityId для последующих тестов
		activityId = lastActivity.getId();

		//Получаем устройство, привязанное к активности и проверяем его
		Device device = getDevice(lastActivity.getDevice().getId());
		assertNotNull(device);
		//Это новое устройство
		assertTrue(deviceId < device.getId());
		assertNull(device.userId);
		//Не проверяем userAgent, т.к. мы его не шлем явно, а что там будет послано restTemplate'ом мы не знаем
		assertClientCorrespondsToDevice(client, device, false);
		//Тип устройства OtherDevice, т.к. мы даже user-agent не шлем
		assertSame(DeviceDtype.OtherDevice, device.getDtype());

		//Сохраняем deviceId для последующих тестов
		deviceId = device.getId();

		//Возвращаем как было
		client.setSetDefaultDeviceHeaders(true);

	}

	/**
	 * Возвращаем заголовки в запрос и они попадают в существующий девайс
	 */
	@Test
	public void _03_getSettingsWithDeviceHeadersRequestUpdatesOldDevice_guest() {
		//Грузим настройки АПИ
		loadSettings(client);

		//Сохраняем активности на сервере (чтобы не ждать)
		flushActivities();

		//Получаем последнюю ожидаемую активность и проверяем ее
		Activity lastActivity = getLastActivity(GetSettingsActivity.class, 0L, client.getOskCookie());
		assertNotNull(lastActivity);
		//Это новая активность
		assertTrue(activityId < lastActivity.getId());
		assertEquals(client.getOskCookie(), lastActivity.getGuestToken());
		assertNull(lastActivity.getUserId());

		//Сохраняем activityId для последующих тестов
		activityId = lastActivity.getId();

		//Получаем устройство, привязанное к активности и проверяем его
		Device device = getDevice(lastActivity.getDevice().getId());
		assertNotNull(device);
		//Это старое устройство
		assertEquals(deviceId, device.getId());
		assertNull(device.userId);
		assertClientCorrespondsToDevice(client, device, true);
		//Тип устройства OskellyApiTestDevice, т.к. мы вернули заголовки и тип устройства должен поменяться на новый в записи device
		assertSame(DeviceDtype.OskellyApiTestDevice, device.getDtype());

	}

	/**
	 * Пользователь авторизуется и привязывается к старому устройству
	 */
	@Test
	public void _10_getSettingsWithDeviceHeadersRequestUpdatesOldDevice_user() {
		//Авторизуемся
		accountTestSupport.rawAuthSuccessful(client, email, password);

		//Грузим настройки АПИ
		loadSettings(client);

		//Сохраняем активности на сервере (чтобы не ждать)
		flushActivities();

		//Получаем последнюю ожидаемую активность и проверяем ее
		Activity lastActivity = getLastActivity(GetSettingsActivity.class, 0L, client.getOskCookie());
		assertNotNull(lastActivity);
		//Это новая активность
		assertTrue(activityId < lastActivity.getId());
		assertEquals(client.getOskCookie(), lastActivity.getGuestToken());
		//Наш пользователь привязан к активности
		assertEquals(userId, lastActivity.getUserId());

		//Сохраняем activityId для последующих тестов
		activityId = lastActivity.getId();

		//Получаем устройство, привязанное к активности и проверяем его
		Device device = getDevice(lastActivity.getDevice().getId());
		assertNotNull(device);
		//Это старое устройство
		assertEquals(deviceId, device.getId());
		//Наш пользователь теперь привязан к этому устройству
		assertEquals(userId, device.userId);
		assertClientCorrespondsToDevice(client, device, true);

	}

	/**
	 * Меняем заголовки токенов (допустим, система их обновила), включая гостевой токен (типа кука устарела)
	 * Запрашиваем настройки и активность привязывается к тому же, но обновленному устройству
	 */
	@Test
	public void _11_resetTokensAndGetSettingsUpdatesOldDevice_user() {
		String newGuestToken = "_11_resetTokensAndGetSettingsUpdatesOldDevice_user";

		//Обновляем гостевой токен
		client.replaceOskCookie(newGuestToken);

		String newValueSuffix = "_test_value";

		//Получаем хедеры по умолчанию
		Map<String, String> headers = new HashMap<>(ApiV2Client.defaultHeaders);
		//Подменяем все значения, кроме userAgent, а в UserAgent отдаем значение для AppleDevice
		for(String key : headers.keySet()){
			if(key.equals(ApiV2Client.USER_AGENT_HEADER_KEY)) continue;
			String newValue = key + newValueSuffix;
			headers.put(key, newValue);
		}
		headers.put(ApiV2Client.USER_AGENT_HEADER_KEY, "ru.oskelly...iOS");

		headers.forEach((k, v) -> client.setHeader(k, v));

		//Грузим настройки АПИ
		loadSettings(client);

		//Сохраняем активности на сервере (чтобы не ждать)
		flushActivities();

		//Получаем последнюю ожидаемую активность и проверяем ее
		Activity lastActivity = getLastActivity(GetSettingsActivity.class, 0L, client.getOskCookie());
		assertNotNull(lastActivity);
		//Это новая активность
		assertTrue(activityId < lastActivity.getId());
		assertEquals(client.getOskCookie(), lastActivity.getGuestToken());
		//И это наше значение
		assertEquals(newGuestToken, lastActivity.getGuestToken());
		//Наш пользователь привязан к активности
		assertEquals(userId, lastActivity.getUserId());

		//Сохраняем activityId для последующих тестов
		activityId = lastActivity.getId();

		//Получаем устройство, привязанное к активности и проверяем его
		Device device = getDevice(lastActivity.getDevice().getId());
		assertNotNull(device);
		//Это старое устройство
		assertEquals(deviceId, device.getId());
		//Наш пользователь привязан к этому устройству
		assertEquals(userId, device.userId);
		assertClientCorrespondsToDevice(client, device, true);
		//Новый тип устройства AppleDevice
		assertSame(DeviceDtype.AppleDevice, device.getDtype());

		//Сохраняем для будущих тестов
		user1DeviceId = device.getId();
	}

	/**
	 * Логаутимся, но сохраняем гостевой токен. Снова запрашиваем настройки. Создается новое устройство.
	 */
	@Test
	public void _12_logoutAndGetSettingsCreatesNewDevice_guest() {
		//logout
		client.removeCookie("SESSION");

		//Грузим настройки АПИ
		loadSettings(client);

		//Сохраняем активности на сервере (чтобы не ждать)
		flushActivities();

		//Получаем последнюю ожидаемую активность и проверяем ее
		Activity lastActivity = getLastActivity(GetSettingsActivity.class, 0L, client.getOskCookie());
		assertNotNull(lastActivity);
		//Это новая активность
		assertTrue(activityId < lastActivity.getId());
		assertEquals(client.getOskCookie(), lastActivity.getGuestToken());
		//Пользователь не привязан к активности
		assertNull(lastActivity.getUserId());

		//Сохраняем activityId для последующих тестов
		activityId = lastActivity.getId();

		//Получаем устройство, привязанное к активности и проверяем его
		Device device = getDevice(lastActivity.getDevice().getId());
		assertNotNull(device);
		//Это новое устройство
		assertTrue(deviceId < device.getId());
		//Пользователь не привязан к этому устройству
		assertNull(device.userId);
		assertClientCorrespondsToDevice(client, device, true);
		//Тип устройства AppleDevice
		assertSame(DeviceDtype.AppleDevice, device.getDtype());

		//Сохраняем для будущих тестов
		guestDeviceId = device.getId();
		deviceId = device.getId();

	}

	/**
	 * Логинимся под другим пользователем с теми же хедерами. Устройство сохраняется и передается второму пользователю..
	 */
	@Test
	public void _13_loginWithAnotherUserAndGetSettingsCreatesNewDevice_user() {
		//Авторизуемся под вторым пользователем
		accountTestSupport.rawAuthSuccessful(client, email2, password2);

		//Грузим настройки АПИ
		loadSettings(client);

		//Сохраняем активности на сервере (чтобы не ждать)
		flushActivities();

		//Получаем последнюю ожидаемую активность и проверяем ее
		Activity lastActivity = getLastActivity(GetSettingsActivity.class, 0L, client.getOskCookie());
		assertNotNull(lastActivity);
		//Это новая активность
		assertTrue(activityId < lastActivity.getId());
		assertEquals(client.getOskCookie(), lastActivity.getGuestToken());
		//Второй пользователь привязан к активности
		assertEquals(userId2, lastActivity.getUserId());

		//Сохраняем activityId для последующих тестов
		activityId = lastActivity.getId();

		//Получаем устройство, привязанное к активности и проверяем его
		Device device = getDevice(lastActivity.getDevice().getId());
		assertNotNull(device);
		//Это прежнее устройство
		assertEquals(deviceId, device.getId());
		//Второй пользователь привязан к этому устройству
		assertEquals(userId2, device.userId);
		assertClientCorrespondsToDevice(client, device, true);
		//Тип устройства AppleDevice
		assertSame(DeviceDtype.AppleDevice, device.getDtype());

		//Сохраняем для будущих тестов
		user2DeviceId = device.getId();
	}

	/**
	 * Снова логаутимся, ничего больше не меняя. Создается новое гостевое устройство, т.к. других подходящих нет
	 */
	@Test
	public void _14_logoutAndGetSettingsCreatesNesGuestDeviceDevice_guest() {
		//logout
		client.removeCookie("SESSION");

		//Грузим настройки АПИ
		loadSettings(client);

		//Сохраняем активности на сервере (чтобы не ждать)
		flushActivities();

		//Получаем последнюю ожидаемую активность и проверяем ее
		Activity lastActivity = getLastActivity(GetSettingsActivity.class, 0L, client.getOskCookie());
		assertNotNull(lastActivity);
		//Это новая активность
		assertTrue(activityId < lastActivity.getId());
		assertEquals(client.getOskCookie(), lastActivity.getGuestToken());
		//Пользователь не привязан к активности
		assertNull(lastActivity.getUserId());

		//Сохраняем activityId для последующих тестов
		activityId = lastActivity.getId();

		//Получаем устройство, привязанное к активности и проверяем его
		Device device = getDevice(lastActivity.getDevice().getId());
		assertNotNull(device);
		//Это новое устройство
		assertTrue(deviceId < device.getId());
		//Пользователь не привязан к этому устройству
		assertNull(device.userId);
		assertClientCorrespondsToDevice(client, device, true);
		//Тип устройства AppleDevice
		assertSame(DeviceDtype.AppleDevice, device.getDtype());

		//Сохраняем для будущих тестов
		deviceId = device.getId();
		guestDeviceId = deviceId;

	}

	/**
	 * Еще раз авторизуемся под первым пользователем и попадаем на старое устройство
	 */
	@Test
	public void _15_getSettingsWithDeviceHeadersRequestUsesOldDevice_user() {
		//Авторизуемся
		accountTestSupport.rawAuthSuccessful(client, email, password);

		//Грузим настройки АПИ
		loadSettings(client);

		//Сохраняем активности на сервере (чтобы не ждать)
		flushActivities();

		//Получаем последнюю ожидаемую активность и проверяем ее
		Activity lastActivity = getLastActivity(GetSettingsActivity.class, 0L, client.getOskCookie());
		assertNotNull(lastActivity);
		//Это новая активность
		assertTrue(activityId < lastActivity.getId());
		assertEquals(client.getOskCookie(), lastActivity.getGuestToken());
		//Наш пользователь привязан к активности
		assertEquals(userId, lastActivity.getUserId());

		//Сохраняем activityId для последующих тестов
		activityId = lastActivity.getId();

		//Получаем устройство, привязанное к активности и проверяем его
		Device device = getDevice(lastActivity.getDevice().getId());
		assertNotNull(device);
		//Это старое устройство
		assertEquals(user1DeviceId, device.getId());
		//Наш пользователь теперь привязан к этому устройству
		assertEquals(userId, device.userId);
		assertClientCorrespondsToDevice(client, device, true);

	}

	/**
	 * Еще раз снова логаутимся, и привязываемся к старому гостевому устройству
	 */
	@Test
	public void _16_logoutAndGetSettingsUsesOldGuestDeviceDevice_guest() {
		//logout
		client.removeCookie("SESSION");

		//Грузим настройки АПИ
		loadSettings(client);

		//Сохраняем активности на сервере (чтобы не ждать)
		flushActivities();

		//Получаем последнюю ожидаемую активность и проверяем ее
		Activity lastActivity = getLastActivity(GetSettingsActivity.class, 0L, client.getOskCookie());
		assertNotNull(lastActivity);
		//Это новая активность
		assertTrue(activityId < lastActivity.getId());
		assertEquals(client.getOskCookie(), lastActivity.getGuestToken());
		//Пользователь не привязан к активности
		assertNull(lastActivity.getUserId());

		//Сохраняем activityId для последующих тестов
		activityId = lastActivity.getId();

		//Получаем устройство, привязанное к активности и проверяем его
		Device device = getDevice(lastActivity.getDevice().getId());
		assertNotNull(device);
		//Это старое гостевое устройство
		assertEquals(guestDeviceId, device.getId());
		//Пользователь не привязан к этому устройству
		assertNull(device.userId);
		assertClientCorrespondsToDevice(client, device, true);
		//Тип устройства AppleDevice
		assertSame(DeviceDtype.AppleDevice, device.getDtype());

		//Сохраняем для будущих тестов
		deviceId = device.getId();
		guestDeviceId = deviceId;

	}

	/**
	 * Еще раз логинимся под другим пользователем с теми же хедерами. Используется старое устройство для 2-го пользователя.
	 */
	@Test
	public void _17_loginWithAnotherUserAndGetSettingsUsesOldUserDevice_user() {
		//Авторизуемся под вторым пользователем
		accountTestSupport.rawAuthSuccessful(client, email2, password2);

		//Грузим настройки АПИ
		loadSettings(client);

		//Сохраняем активности на сервере (чтобы не ждать)
		flushActivities();

		//Получаем последнюю ожидаемую активность и проверяем ее
		Activity lastActivity = getLastActivity(GetSettingsActivity.class, 0L, client.getOskCookie());
		assertNotNull(lastActivity);
		//Это новая активность
		assertTrue(activityId < lastActivity.getId());
		assertEquals(client.getOskCookie(), lastActivity.getGuestToken());
		//Второй пользователь привязан к активности
		assertEquals(userId2, lastActivity.getUserId());

		//Сохраняем activityId для последующих тестов
		activityId = lastActivity.getId();

		//Получаем устройство, привязанное к активности и проверяем его
		Device device = getDevice(lastActivity.getDevice().getId());
		assertNotNull(device);
		//Это прежнее устройство 2-го пользователя
		assertEquals(user2DeviceId, device.getId());
		//Второй пользователь привязан к этому устройству
		assertEquals(userId2, device.userId);
		assertClientCorrespondsToDevice(client, device, true);
		//Тип устройства AppleDevice
		assertSame(DeviceDtype.AppleDevice, device.getDtype());

		//Сохраняем для будущих тестов
		user2DeviceId = device.getId();
	}

	//Все, я устал. Тут еще миллионы разных кейсов, которые надо бы проверить, но это большой объем, надо пахать.
	//Играйтесь с токенами, меняйте IP, userAgent, гостевой токен в разных компановках. Самое сложное - это понять, как оно должно работать правильно.
	//Как только поймете - дело пойдет)

	//Устройство соответствует клиенту по заголовкам
	private void assertClientCorrespondsToDevice(@NonNull ApiV2Client client, @NonNull Device device, boolean checkUserAgent){
		assertEquals(client.getOskCookie(), device.guestToken);
		if(checkUserAgent)
			assertEquals(client.getEffectiveUserAgent(), device.getUserAgent());
		assertEquals(client.getEffectiveHeader("X-Osk-PushToken"), device.getToken());
		assertEquals(client.getEffectiveHeader("X-Osk-Idfa"), device.getIdfa());
		assertEquals(client.getEffectiveHeader("X-Osk-Idfv"), device.getIdfv());
		assertEquals(client.getEffectiveHeader("X-Osk-AndroidId"), device.getAndroidId());
		assertEquals(client.getEffectiveHeader("X-Osk-AdvertisingId"), device.getAdvertisingId());
		assertEquals(client.getEffectiveHeader("X-Osk-Mac"), device.getMac());
		assertEquals(client.getEffectiveHeader("X-Osk-MindboxClientUuid"), device.getMindboxClientUuid());
	}

	protected Activity getLastActivity(@NonNull Class<? extends Activity> type, Long userId, String guestToken){
		List<Activity> activities = activityTestRepository.findLastActivities(type.getSimpleName(), userId, guestToken, 1);
		return activities.isEmpty() ? null : activities.get(0);
	}

	protected Device getDevice(long deviceId){
		return deviceRepository.findById(deviceId).orElse(null);
	}

	//Сохраняет активности в базу
	protected void flushActivities(){
		activityService.saveAllFromQueue();
	}

	protected SettingService.Settings loadSettings(@NonNull ApiV2Client client){
		ResponseEntity<Api2Response<SettingService.Settings>> responseEntity = client.request(getSettingsUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<SettingService.Settings>>() {}, false);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody());
		assertNotNull(responseEntity.getBody().getData());
		return responseEntity.getBody().getData();
	}

	//Очистка базы
	private void cleanup(){
		jdbcTemplate.execute("DELETE FROM activity");
		jdbcTemplate.execute("DELETE FROM device");
	}

}