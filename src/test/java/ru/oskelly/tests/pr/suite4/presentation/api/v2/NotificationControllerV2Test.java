package ru.oskelly.tests.pr.suite4.presentation.api.v2;

import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.TestApiConfiguration;
import su.reddot.domain.dao.comment.CommentRepository;
import su.reddot.domain.dao.following.FollowingRepository;
import su.reddot.domain.dao.notification.NotificationRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.Comment;
import su.reddot.domain.model.Following;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.notification.comment.NewCommentNotification;
import su.reddot.domain.model.notification.following.NewFollowingNotification;
import su.reddot.domain.model.notification.profile.ProfileBirthdateNotification;
import su.reddot.domain.model.notification.profile.RegisterNotification;
import su.reddot.domain.model.notification.profile.welcome.HowItWorksNotification;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.activity.ActivityService;
import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.dto.notification.ModificationMetadata;
import su.reddot.domain.service.dto.notification.NotificationDTO;
import su.reddot.domain.service.notification.NotificationService;
import su.reddot.domain.service.task.ScheduledNotificationRunner;
import su.reddot.domain.service.user.UserService;
import su.reddot.presentation.api.v2.Api2Response;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_04)
public class NotificationControllerV2Test extends AbstractSpringTest {
	@Autowired
	private TestApiConfiguration testApiConfiguration;
	@Value("${test.api.user-id}")
	private Long userId;
	@Value("${test.api.user2-id}")
	private Long user2Id;
    @Value("${test.api.user-email}")
    private String email;
    @Value("${test.api.user-password}")
    private String password;

	@Value("${test.temp-dir}")
	private String tmpDirPath;

	private static int notificationsCount = 50;
	private static String guestToken = "dsfgrtregrehrthethtrhrthrt";
	private static int birthDateDayOfMonth = 16;
	private static int birthDateMonth = 6;
	private static int birthDateYear = 1982;

    private static User user;
	private static User user2;

	private static Long registerNotificationId;

    private static ApiV2Client apiV2Client;

    @Autowired
    private UserService userService;

	@Autowired
	private NotificationService notificationService;

	@Autowired
	private ActivityService activityService;

	@Autowired
	private ScheduledNotificationRunner scheduledNotificationRunner;

	@Autowired
    private NotificationRepository<Notification> notificationRepository;

	@Autowired
	private FollowingRepository followingRepository;

	@Autowired
	private CommentRepository commentRepository;

	@Autowired
	private ProductRepository productRepository;

	private String getServiceUrl(){
		return testApiConfiguration.getServerUrl() + "/api/v2/notifications";
	}

	private String getNotificationUrl(long notificationId){
		return getServiceUrl() + "/" + notificationId;
	}

	private String getReadNotificationUrl(long notificationId){
		return getServiceUrl() + "/" + notificationId + "/read";
	}

	private String getCommentNotificationsUrl(){
		return getServiceUrl() + "/comments";
	}

	private String getNoCommentNotificationsUrl(){
		return getServiceUrl() + "/nocomments";
	}

	private String getBubblesUrl(){
		return getServiceUrl() + "/bubbles";
	}

	private String getSettingsUrl(){
		return testApiConfiguration.getServerUrl() + "/api/v2/settings";
	}


	@Test
    public void _00_initialize() {
		cleanup();
        if(apiV2Client == null) apiV2Client = new ApiV2Client(email, password);
    }

	/////////// Guest //////////////

	/**
	 * Сначала уведомленй нет. Мы получим пустой список.
	 */
    @Test
    public void _01_1_0_getNotifications_guest_empty(){
	    Page<NotificationDTO> page = getNotificationsSuccessfull(null, false);
		assertNotNull(page.getItems());
	    assertTrue(page.getItems().isEmpty());
	    assertTrue(page.getTotalAmount() == 0);
    }

	/**
	 * Получили куку, теперь нам выдадут уведомление "Зарегистрируйтеся".
	 */
	@Test
	public void _01_1_1_getNotifications_RegisterNotification_exists(){
		//Для того, чтобы уведомение появилось, необходимо отметиться в системе (н.п. запросить настройки)
		getSettingsSuccessful();
		activityService.saveAllFromQueue();
		//Регистрация активности происходит не мгновенно
		TestUtils.sleep(1);

		//Чтобы не создавались уведомления по другим пользователям, чистим активности всех, кроме нашего гостя
		jdbcTemplate.execute("delete from activity where guest_token <> '" + apiV2Client.getOskCookie() + "' ");

		// и выполнить соотв. таск
		scheduledNotificationRunner.createDefaultNotifications(50);
		//Это может занять некоторое время, поэтому подождем
		TestUtils.sleep(1);


		Page<NotificationDTO> page = getNotificationsSuccessfull(null, false);
		assertNotNull(page.getItems());
		assertFalse(page.getItems().isEmpty());
		assertTrue(page.getTotalAmount() == 1);
		NotificationDTO registerNotification = getFirstByType(page.getItems(), HowItWorksNotification.class.getSimpleName());
		assertNotNull(registerNotification);
		registerNotificationId = registerNotification.getId();
	}

	/**
	 * Нельзя получить и отметить прочитанным чужое уведомление по id
	 */
	@Test
	public void _01_2_getSomeonesNotification_guest_failed(){
		String guestToken = apiV2Client.getOskCookie();
		//Скрываем уведомления нашего гостя, меняя гостевой токен
		jdbcTemplate.execute("UPDATE notification SET guest_token = 'tmp' WHERE guest_token = '" + guestToken + "'");
		Notification someonesNotification = findFirstSomeonesNotification();
		getNotificationForbidden(someonesNotification.getId(), false);
		readNotificationForbidden(someonesNotification.getId(), false);
		jdbcTemplate.execute("UPDATE notification SET guest_token = '" + guestToken + "' WHERE guest_token = 'tmp'");
	}

	/**
	 * Создание гостевых уведомлений.
	 */
	@Test
	public void _02_1_createNotifications_guest(){
		String guestToken = apiV2Client.getOskCookie();
		assertNotNull(guestToken);
		apiV2Client.replaceOskCookie(guestToken);

		Following following = new Following().setFollowing(getUser()).setFollower(getUser2());
		followingRepository.saveAndFlush(following);

		Product product = productRepository.findTop10ByProductState(ProductState.PUBLISHED).get(0);

		Comment comment = new Comment().setText("Hi!").setPublisher(getUser2()).setPublishTime(ZonedDateTime.now()).setProduct(product);
		commentRepository.saveAndFlush(comment);

		for(int i = 0; i < notificationsCount; i++){
			Notification n;
			if(i % 2 == 0){
				NewFollowingNotification fn = new NewFollowingNotification();
				fn.setFollowing(following);
				n = fn;
			}
			else{
				NewCommentNotification cn = new NewCommentNotification();
				cn.setComment(comment);
				n = cn;
			}

			n.setGuestToken(guestToken);
			n.setCreateTime(ZonedDateTime.now());
			if(i % 3 == 0) {
				n.setReadTime(ZonedDateTime.now());
			}
			if(i % 4 == 0) {
				n.setNeedAction(true);
			}
			if(i % 8 == 0) {
				n.setActionCompletedTime(ZonedDateTime.now());
			}
			if(i % 10 == 0) {
				if(n.isNeedAction() && n.getReadTime() != null)
					n.setActionCompletedTime(ZonedDateTime.now());
			}
			notificationRepository.saveAndFlush(n);
		}
	}

	/**
	 * Гость видит свои уведомления по куки osk.
	 */
	@Test
	public void _02_2_getNotifications_guest_OK(){

		//First page
		Page<NotificationDTO> page = getNotificationsSuccessfull(null, false);
		assertNotNull(page.getItems());
		assertFalse(page.getItems().isEmpty());
		assertTrue(page.getTotalPages() == 1 + notificationsCount / NotificationService.NotificationRequest.DEFAULT_PAGE_SIZE);
		assertTrue(page.getTotalAmount() == notificationsCount + 1);
		assertEquals(NotificationService.NotificationRequest.DEFAULT_PAGE_SIZE, page.getItems().size());

		assertEquals(page.getTotalAmount(), getBubblessSuccessfull().getTotal());

		//Sorting check
		NotificationDTO firstItem = page.getItems().get(0);
		assertTrue(firstItem.isNeedAction());
		//Раньше непрочитанные уведомления выводились наверху, теперь выводятся наравне с прочитанными
		//assertNull(firstItem.getReadTime());
		assertNull(firstItem.getActionCompletedTime());

		//Last page
		Page<NotificationDTO> page2 = getNotificationsSuccessfull(TestUtils.getOneParamAsMap("page", page.getTotalPages()), false);
		assertNotNull(page2.getItems());
		assertFalse(page2.getItems().isEmpty());
		assertEquals(page.getTotalAmount() % NotificationService.NotificationRequest.DEFAULT_PAGE_SIZE, page2.getItems().size());

		//Sorting check
		NotificationDTO lastItem = page2.getItems().get(page2.getItems().size() - 1);
		//debug!!
		if(!((lastItem.isNeedAction() && lastItem.getActionCompletedTime() != null)
				|| (!lastItem.isNeedAction() && lastItem.getActionCompletedTime() == null))){
			System.out.println("lastItem.id: " + lastItem.getId());
			System.exit(0);
		}
		assertTrue((lastItem.isNeedAction() && lastItem.getActionCompletedTime() != null)
			|| (!lastItem.isNeedAction() && lastItem.getActionCompletedTime() == null));
		//Правило сортировки было изменено, так что теперь прочитанные сообщения наравне с непрочитанными и сортируются только по дате
		//assertNotNull(lastItem.getReadTime());
	}

	/**
	 * Получение уведомлений по комментариям. Видны только уведомления, имеющие отношение к комментариям.
	 */
	@Test
	public void _02_3_getCommentNotifications_guest_OK(){
		Page<NotificationDTO> page = getCommentNotificationsSuccessfull(null, false);
		assertNotNull(page.getItems());
		assertFalse(page.getItems().isEmpty());
		assertTrue(page.getTotalPages() == 1 + notificationsCount / 2 / NotificationService.NotificationRequest.DEFAULT_PAGE_SIZE);
		assertTrue(page.getTotalAmount() == notificationsCount / 2);
		assertEquals(NotificationService.NotificationRequest.DEFAULT_PAGE_SIZE, page.getItems().size());
		for(NotificationDTO nd : page.getItems()){
			//Проверка доступности каждого отдельного уведомления с иконками
			getNotificationSuccessful(nd.getId(), false, true);
		}

		assertEquals(page.getTotalAmount(), getBubblessSuccessfull().getCommentsTotal());
	}

	/**
	 * Получение уведомлений, не относящихся к комментариям.
	 */
	@Test
	public void _02_4_getNoCommentNotifications_guest_OK(){
		Page<NotificationDTO> page = getNoCommentNotificationsSuccessfull(null, false);
		assertNotNull(page.getItems());
		assertFalse(page.getItems().isEmpty());
		assertTrue(page.getTotalPages() == 1 + notificationsCount / 2 / NotificationService.NotificationRequest.DEFAULT_PAGE_SIZE);
		assertTrue(page.getTotalAmount() == 1 + notificationsCount / 2);
		assertEquals(NotificationService.NotificationRequest.DEFAULT_PAGE_SIZE, page.getItems().size());
		for(NotificationDTO nd : page.getItems()){
			if(nd.getSubTitle() != null) assertFalse(nd.getSubTitle().contains("прокомментировал"));
			if(nd.getMessage() != null) assertFalse(nd.getMessage().contains("прокомментировал"));
		}

		assertEquals(page.getTotalAmount(), getBubblessSuccessfull().getNoCommentsTotal());
	}

	/**
	 * Получение уведомлений, не относящихся к комментариям, требующих действия.
	 */
	@Test
	public void _02_5_getNoCommentNotifications_need_action_guest_OK(){
		Page<NotificationDTO> page = getNoCommentNotificationsSuccessfull(TestUtils.getOneParamAsMap("needAction", Boolean.TRUE), false);
		assertNotNull(page.getItems());
		assertFalse(page.getItems().isEmpty());
		for(NotificationDTO nd : page.getItems()){
			if(nd.getSubTitle() != null) assertFalse(nd.getSubTitle().contains("прокомментировал"));
			if(nd.getMessage() != null) assertFalse(nd.getMessage().contains("прокомментировал"));
			assertTrue(nd.isNeedAction());
		}
	}

	/**
	 * Получение уведомлений, не относящихся к комментариям, не требующих действия.
	 */
	@Test
	public void _02_6_getNoCommentNotifications_need_no_action_guest_OK(){
		Page<NotificationDTO> page = getNoCommentNotificationsSuccessfull(TestUtils.getOneParamAsMap("needAction", Boolean.FALSE), false);
		assertNotNull(page.getItems());
		assertFalse(page.getItems().isEmpty());
		for(NotificationDTO nd : page.getItems()){
			assertFalse(nd.isNeedAction());
		}
	}

	/**
	 * Получение уведомлений, не относящихся к комментариям, прочитанных.
	 */
	@Test
	public void _02_7_1_getNoCommentNotifications_read_guest_OK(){
		Page<NotificationDTO> page = getNoCommentNotificationsSuccessfull(TestUtils.getOneParamAsMap("isRead", Boolean.TRUE), false);
		assertNotNull(page.getItems());
		assertFalse(page.getItems().isEmpty());
		for(NotificationDTO nd : page.getItems()){
			assertTrue(nd.isRead());
		}
	}

	/**
	 * Получение уведомлений, не относящихся к комментариям, не прочитанных.
	 */
	@Test
	public void _02_7_2_getNoCommentNotifications_not_read_guest_OK(){
		Page<NotificationDTO> page = getNoCommentNotificationsSuccessfull(TestUtils.getOneParamAsMap("isRead", Boolean.FALSE), false);
		assertNotNull(page.getItems());
		assertFalse(page.getItems().isEmpty());
		for(NotificationDTO nd : page.getItems()){
			assertFalse(nd.isRead());
		}

		assertEquals(page.getTotalAmount(), getBubblessSuccessfull().getNoCommentsNotRead());
	}

	/**
	 * Получение уведомлений о комментариях, не прочитанных.
	 */
	@Test
	public void _02_7_3_getCommentNotifications_not_read_guest_OK(){
		Page<NotificationDTO> page = getCommentNotificationsSuccessfull(TestUtils.getOneParamAsMap("isRead", Boolean.FALSE), false);
		assertNotNull(page.getItems());
		assertFalse(page.getItems().isEmpty());
		for(NotificationDTO nd : page.getItems()){
			assertFalse(nd.isRead());
		}

		assertEquals(page.getTotalAmount(), getBubblessSuccessfull().getCommentsNotRead());
	}

	/**
	 * Получение уведомлений, с выполненной операцией.
	 */
	@Test
	public void _02_8_1_getNotifications_action_completed_guest_OK(){
		Page<NotificationDTO> page = getNotificationsSuccessfull(TestUtils.getOneParamAsMap("isActionCompleted", Boolean.TRUE), false);
		assertNotNull(page.getItems());
		assertFalse(page.getItems().isEmpty());
		for(NotificationDTO nd : page.getItems()){
			assertTrue(nd.isActionCompleted());
		}
	}

	/**
	 * Получение уведомлений, с невыполненной операцией.
	 */
	@Test
	public void _02_8_2_getNotifications_action_not_completed_guest_OK(){
		Page<NotificationDTO> page = getNotificationsSuccessfull(TestUtils.getOneParamAsMap("isActionCompleted", Boolean.FALSE), false);
		assertNotNull(page.getItems());
		assertFalse(page.getItems().isEmpty());
		for(NotificationDTO nd : page.getItems()){
			assertFalse(nd.isActionCompleted());
		}
	}

	/**
	 * Получаем одиночное непрочтенное уведомление. Потом делаем отметку о прочтении и убеждаемся, что теперь оно возвращается как прочтенное.
	 */
	@Test
	public void _02_9_1_getNotifications_setRead_guest_OK(){
		Page<NotificationDTO> page = getNotificationsSuccessfull(TestUtils.getOneParamAsMap("isRead", Boolean.FALSE), false);
		assertNotNull(page.getItems());
		assertFalse(page.getItems().isEmpty());
		Long notificationId = page.getItems().get(0).getId();

		//До отметки о прочтении
		NotificationDTO notification = getNotificationSuccessful(notificationId, false, false);
		assertFalse(notification.isRead());

		//Отметка о прочтении
		notification = readNotificationSuccessful(notificationId, false);
		assertTrue(notification.isRead());
	}

	/**
	 * После авторизации уведомления прикрепляются к пользователю.
	 */
	@Transactional
	@Test
	public void _03_1_getNotifications_authorize_OK(){
		Page<NotificationDTO> page = getNotificationsSuccessfull(TestUtils.getOneParamAsMap("pageSize", "1000"), true);
		//Уведомления "Зарегистрирутесь" больше нет
		//Решили, что больше уведомление не удаялем, а отмечаем как выполненное
		//NotificationDTO registerNotification = getFirstByType(page.getItems(), RegisterNotification.class.getSimpleName());
		//assertNull(registerNotification);
		//getNotificationNotFound(registerNotificationId, false);

		NotificationDTO registerNotification = getFirstByType(page.getItems(), HowItWorksNotification.class.getSimpleName());
		//Уведомление осталось
		assertNotNull(registerNotification);

		assertNoticationsNotEmptyRelatedToUser(page, getUser());
	}

	/**
	 * Теперь уже данные авторизации не нужны, т.к. передается идентификатор сессии.
	 */
	@Transactional
	@Test
	public void _03_2_getNotifications_authorized_OK(){
		Page<NotificationDTO> page = getNotificationsSuccessfull(null, false);
		assertNoticationsNotEmptyRelatedToUser(page, getUser());
	}

	@Test
	public void _04_getNotificationBubbles_authorized_OK(){
		NotificationService.NotificationBubbles bubbles = getBubblessSuccessfull();
		assertTrue(bubbles.getCommentsTotal() > 0);
		assertTrue(bubbles.getNoCommentsTotal() > 0);
		assertTrue(bubbles.getCommentsTotal() + bubbles.getNoCommentsTotal() == bubbles.getTotal());
		assertTrue(bubbles.getCommentsNotRead() > 0);
		assertTrue(bubbles.getNoCommentsNotRead() > 0);
		assertTrue(bubbles.getNoCommentsNeedActionNotCompleted() > 0);

	}

	//Просьба установить дату рождения. Установить ее можно только единожды.
	@Test
	public void _05_1_birthDateNotification_setBirthDate_authorized_OK(){
		String notificationType = ProfileBirthdateNotification.class.getSimpleName();
		//Сначала подчистим дату рождения для нашего пользователя
		userService.setUserBirthdate(userId, null);
		//Создадим соотв. уведомление
		notificationService.create(new ProfileBirthdateNotification().setNeedAction(true).setUser(user));
		//Создание уведомлений происходит ассинхронно, так что надо подождать
		TestUtils.sleep(3);

		Page<NotificationDTO> page = getNotificationsSuccessfull(TestUtils.getOneParamAsMap("needAction", Boolean.TRUE), false);
		//Это уведомление должно быть первым, т.к. оно самое свежее
		NotificationDTO birthDateNotificationDTO = page.getItems().get(0);
		assertEquals(notificationType, birthDateNotificationDTO.getType());
		assertFalse(birthDateNotificationDTO.isActionCompleted());
		assertFalse(birthDateNotificationDTO.isRead());

		ModificationMetadata modificationMetadata = birthDateNotificationDTO.getModificationMetadata();
		assertNotNull(modificationMetadata);
		assertTrue(modificationMetadata.isModifiable());
		assertNull(modificationMetadata.getCurrentValue());

		String acceptUrl = modificationMetadata.getAcceptUrl();
		HttpMethod acceptMethod = modificationMetadata.getAcceptMethod();
		String acceptParamName = modificationMetadata.getAcceptParamName();
		MediaType acceptMediaType = modificationMetadata.getAcceptMediaType();
		String acceptParamFormat = modificationMetadata.getAcceptParamFormat();

		assertNotNull(acceptUrl);
		assertSame(acceptMethod, HttpMethod.PATCH);
		assertNotNull(acceptParamName);
		assertNull(acceptMediaType);
		assertEquals("Long", acceptParamFormat);

		ZonedDateTime birthDate = ZonedDateTime.of(birthDateYear, birthDateMonth, birthDateDayOfMonth, 12, 0, 0, 0, ZoneId.systemDefault());
		String birthDateParamValue = "" + birthDate.toEpochSecond();

		Map<String, String> updateParamsMap = TestUtils.getOneParamAsMap(acceptParamName, birthDateParamValue);

		//Успешная установка дня рождения

		ResponseEntity<Api2Response<NotificationDTO>> response = apiV2Client.request(testApiConfiguration.getServerUrl() + acceptUrl, updateParamsMap, acceptMethod, null, new ParameterizedTypeReference<Api2Response<NotificationDTO>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());

		NotificationDTO updatedBirthDateNotificationDTO = response.getBody().getData();
		assertNotNull(updatedBirthDateNotificationDTO);
		assertTrue(updatedBirthDateNotificationDTO.isActionCompleted());
		assertTrue(updatedBirthDateNotificationDTO.isRead());

		ModificationMetadata updatedModificationMetadata = updatedBirthDateNotificationDTO.getModificationMetadata();
		assertNotNull(updatedModificationMetadata);
		assertFalse(updatedModificationMetadata.isModifiable());
		assertNotNull(updatedModificationMetadata.getCurrentValue());

		ZonedDateTime updatedBirthDate = ZonedDateTime.ofInstant(Instant.ofEpochSecond(Long.parseLong(updatedModificationMetadata.getCurrentValue().toString())), ZoneId.systemDefault());
		assertEquals(birthDateYear, updatedBirthDate.getYear());
		assertEquals(birthDateMonth, updatedBirthDate.getMonthValue());
		assertEquals(birthDateDayOfMonth, updatedBirthDate.getDayOfMonth());

		//Неуспешная установка дня рождения (уже установлен)

		ResponseEntity<String> failedResponse = apiV2Client.request(testApiConfiguration.getServerUrl() + acceptUrl, updateParamsMap, acceptMethod, null, String.class, false);
		assertTrue(failedResponse.getStatusCode().is4xxClientError());
		assertTrue(failedResponse.getBody().contains("NotificationException"));
		assertTrue(failedResponse.getBody().contains("Дата рождения уже установлена"));

	}

	@Test
	public void _06_1_notificationProcessorWorks(){
		//Закроем все уведомления с типом FollowingNotification.
		String type = NewFollowingNotification.class.getSimpleName();

		Map<String, String> actionUncompletedParamMap = TestUtils.getOneParamAsMap("isActionCompleted", Boolean.FALSE);
		Map<String, String> typedActionUncompletedParamMap = TestUtils.getOneParamAsMap("isActionCompleted", Boolean.FALSE);
		typedActionUncompletedParamMap.put("onlyType", type);

		Page<NotificationDTO> allUncompletedPage = getNotificationsSuccessfull(actionUncompletedParamMap, false);
		//Изначально у нас столько уведомлений, по которым действие не выполнено.
		long totalUncompletedActionAmount = allUncompletedPage.getTotalAmount();

		Page<NotificationDTO> typedUncompletedPage = getNotificationsSuccessfull(typedActionUncompletedParamMap, false);
		//Изначально у нас столько уведомлений того самого типа, которые мы будем закрывать.
		long typedUncompletedActionAmount = typedUncompletedPage.getTotalAmount();

		NotificationService.NotificationProcessor notificationProcessor = new NotificationService.NotificationProcessor() {
			@Override
			public ZonedDateTime getActionCompletedTime(Notification notification) {
				return ZonedDateTime.now();
			}

			@Override
			public Class<? extends Notification>[] getSupportedTypes() {
				return new Class[] {NewFollowingNotification.class};
			}
		};
		notificationService.addNotificationProcessor(notificationProcessor);

		Page<NotificationDTO> updatedAllUncompletedPage = getNotificationsSuccessfull(actionUncompletedParamMap, false);
		//Теперь у нас столько уведомлений, по которым действие не выполнено.
		long updatedTotalUncompletedActionAmount = updatedAllUncompletedPage.getTotalAmount();

		Page<NotificationDTO> updatedTypedUncompletedPage = getNotificationsSuccessfull(typedActionUncompletedParamMap, false);
		//Теперь у нас столько уведомлений того самого типа, которые мы будем закрывать.
		long updatedTypedUncompletedActionAmount = updatedTypedUncompletedPage.getTotalAmount();

		assertTrue(totalUncompletedActionAmount > 0);
		assertTrue(typedUncompletedActionAmount > 0);
		assertTrue(updatedTotalUncompletedActionAmount > 0);
		assertTrue(updatedTypedUncompletedActionAmount > 0);
		assertTrue(totalUncompletedActionAmount > updatedTotalUncompletedActionAmount);
		assertTrue(typedUncompletedActionAmount > updatedTypedUncompletedActionAmount);
		assertTrue(totalUncompletedActionAmount - updatedTotalUncompletedActionAmount == typedUncompletedActionAmount - updatedTypedUncompletedActionAmount);

		notificationService.removeNotificationProcessor(notificationProcessor);
	}

	/**
	 * Выходим, становимся гостем и теперь не видим уведомлений, т.к. они привязались к пользователю.
	 */
	@Transactional
	@Test
	public void _07_getNotifications_unauthorized_empty(){
		apiV2Client.logout();
		Page<NotificationDTO> page = getNotificationsSuccessfull(null, false);
		assertEquals(0, page.getItemsCount());
		assertTrue(page.getItems().isEmpty());
	}

	@Test
	@Transactional
	public void _99_finalize() {
		cleanup();
	}


    private void cleanup(){
		notificationRepository.deleteAll(notificationRepository.findAllByGuestToken(guestToken));
	    notificationRepository.deleteAll(notificationRepository.findAllByUser(getUser().getId()));

		Following following = followingRepository.findFirstByFollowerAndFollowing(getUser2(), getUser());
		if(following != null) followingRepository.delete(following);

		List<Comment> comments = commentRepository.findAllByPublisherId(user2Id);
		commentRepository.deleteAll(comments);
    }

	private NotificationDTO getFirstByType(List<NotificationDTO> notificationDTOS, String type){
		for(NotificationDTO n : notificationDTOS){
			if(type.equals(n.getType())) return n;
		}
		return null;
	}

	private NotificationService.NotificationBubbles getBubblessSuccessfull(){
		ResponseEntity<Api2Response<NotificationService.NotificationBubbles>> response = apiV2Client.request(getBubblesUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<NotificationService.NotificationBubbles>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		assertNotNull(response.getBody().getData());
		return response.getBody().getData();
	}


	private void assertNoticationsNotEmptyRelatedToUser(Page<NotificationDTO> page, User user){
		assertNotNull(page.getItems());
		assertFalse(page.getItems().isEmpty());
		for(NotificationDTO nd : page.getItems()){
			Notification n = notificationRepository.getOne(nd.getId());
			assertEquals(user.getId(), n.getUser().getId());
		}
	}

	private User getUser(){
		if(user == null) user = userService.getUserById(userId).orElse(null);
		return user;
	}

	private User getUser2(){
		if(user2 == null) user2 = userService.getUserById(user2Id).orElse(null);
		return user2;
	}

	private Page<NotificationDTO> getNotificationsSuccessfull(Map<String, String> getParams, boolean withAuthorizeParams){
		return getNotificationsSuccessfull(getServiceUrl(), getParams, withAuthorizeParams);
	}

	private Page<NotificationDTO> getCommentNotificationsSuccessfull(Map<String, String> getParams, boolean withAuthorizeParams){
		return getNotificationsSuccessfull(getCommentNotificationsUrl(), getParams, withAuthorizeParams);
	}

	private Page<NotificationDTO> getNoCommentNotificationsSuccessfull(Map<String, String> getParams, boolean withAuthorizeParams){
		return getNotificationsSuccessfull(getNoCommentNotificationsUrl(), getParams, withAuthorizeParams);
	}

	private Page<NotificationDTO> getNotificationsSuccessfull(String url, Map<String, String> getParams, boolean withAuthorizeParams){
		ResponseEntity<Api2Response<Page<NotificationDTO>>> response = apiV2Client.request(url, getParams, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<Page<NotificationDTO>>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		assertNotNull(response.getBody().getData());
		return response.getBody().getData();
	}

	private String getSettingsSuccessful(){
		ResponseEntity<String> response = apiV2Client.request(getSettingsUrl(), null, HttpMethod.GET, null, String.class, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		return response.getBody();
	}

	private NotificationDTO getNotificationSuccessful(long id, boolean withAuthorizeParams, boolean checkIcons){
		ResponseEntity<Api2Response<NotificationDTO>> response = apiV2Client.request(getNotificationUrl(id), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<NotificationDTO>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		NotificationDTO notificationDTO = response.getBody().getData();
		assertNotNull(notificationDTO);
		if(checkIcons){
			String mainIcon = notificationDTO.getMainIcon();
			String tinyIcon = notificationDTO.getTinyIcon();
			if(mainIcon != null) TestUtils.assertImageAvailable(testApiConfiguration.getServerUrl() + mainIcon, tmpDirPath);
			TestUtils.assertImageAvailable(TestUtils.getUrlPath(testApiConfiguration.getServerUrl(), tinyIcon), tmpDirPath);
		}
		if(notificationDTO.getTargetObjectType().equals("Comment")){
			assertNotNull(notificationDTO.getTargetObject());
			Map<String, Object> targetObjectDTO = (Map<String, Object>) notificationDTO.getTargetObject();
			assertNotNull(targetObjectDTO.get("id"));
			assertNotNull(targetObjectDTO.get("text"));
			assertNotNull(targetObjectDTO.get("publisher"));
		}
		return notificationDTO;
	}

	private void getNotificationNotFound(long id, boolean withAuthorizeParams){
		ResponseEntity<String> response = apiV2Client.request(getNotificationUrl(id), null, HttpMethod.GET, null, String.class, withAuthorizeParams);
		assertTrue(response.getStatusCode().is4xxClientError());
		assertTrue(response.getBody().contains("NotFoundException"));
	}

	private void getNotificationForbidden(long id, boolean withAuthorizeParams){
		ResponseEntity<String> response = apiV2Client.request(getNotificationUrl(id), null, HttpMethod.GET, null, String.class, withAuthorizeParams);
		assertTrue(response.getStatusCode().is4xxClientError());
		assertTrue(response.getBody().contains("ForbiddenException"));
	}

	private void readNotificationForbidden(long id, boolean withAuthorizeParams){
		ResponseEntity<String> response = apiV2Client.request(getReadNotificationUrl(id), null, HttpMethod.PATCH, null, String.class, withAuthorizeParams);
		assertTrue(response.getStatusCode().is4xxClientError());
		assertTrue(response.getBody().contains("ForbiddenException"));
	}

	private NotificationDTO readNotificationSuccessful(long id, boolean withAuthorizeParams){
		ResponseEntity<Api2Response<NotificationDTO>> response = apiV2Client.request(getReadNotificationUrl(id), null, HttpMethod.PATCH, null, new ParameterizedTypeReference<Api2Response<NotificationDTO>>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		assertNotNull(response.getBody().getData());
		assertTrue(response.getBody().getData().isRead());

		NotificationDTO result = getNotificationSuccessful(id, withAuthorizeParams, false);
		assertTrue(result.isRead());
		return result;
	}

	private Notification findFirstSomeonesNotification() {
		return notificationRepository.findAll(PageRequest.of(1, 1)).getContent().get(0);
	}

}
