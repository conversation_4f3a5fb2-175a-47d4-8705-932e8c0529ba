package ru.oskelly.tests.pr.suite4.presentation.api.v2;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import org.apache.commons.lang3.RandomStringUtils;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.test.context.TestPropertySource;
import ru.oskelly.common.messaging.messages.llmproxy.CommandType;
import ru.oskelly.common.messaging.messages.llmproxy.PromptProcessedEvent;
import ru.oskelly.common.messaging.messages.llmproxy.PromptProcessingCommand;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.TestApiConfiguration;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.comment.CommentRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.dao.userban.UserBanRepository;
import su.reddot.domain.model.Brand;
import su.reddot.domain.model.Comment;
import su.reddot.domain.model.notification.comment.NewCommentNotification;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderSource;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.model.user.userban.UserBan;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.domain.service.dto.CommentDTO;
import su.reddot.domain.service.kafka.KafkaSenderService;
import su.reddot.domain.service.llm.CommentHarmfulnessScoreCalculatedHandler.ResponseObject;
import su.reddot.domain.service.llm.PromptProcessedEventsHandler;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.configparam.ConfigParamService;
import su.reddot.infrastructure.util.AsyncExecutor;
import su.reddot.infrastructure.util.Utils;
import su.reddot.presentation.api.v2.comments.ProductCommentData;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.awaitility.Awaitility.await;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doAnswer;
import static su.reddot.domain.model.order.OrderState.CREATED;
import static su.reddot.domain.model.user.User.UserType.SIMPLE_USER;
import static su.reddot.domain.model.user.userban.BanType.COMMENT_SHADOW_BAN;
import static su.reddot.infrastructure.configparam.ConfigParamService.CONFIG_PARAM_COMMENT_HARMFULNESS_SCORE_CALC_ENABLED;
import static su.reddot.infrastructure.configparam.ConfigParamService.CONFIG_PARAM_COMMENT_HARMFULNESS_SCORE_CALC_LLM_CONTEXT_TEMPLATE;
import static su.reddot.infrastructure.configparam.ConfigParamService.CONFIG_PARAM_COMMENT_HARMFULNESS_SCORE_CALC_LLM_PROMPT_TEMPLATE;
import static su.reddot.infrastructure.configparam.ConfigParamService.CONFIG_PARAM_COMMENT_HARMFULNESS_SCORE_CALC_THRESHOLD;


@EnableAsync
@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_04)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestPropertySource(properties = {
        "app.kafka.llm-proxy-commands-topic.sender.enabled: true",
        "app.user-ban.shadow-ban.enabled: true",
        "app.user-ban.shadow-ban.addition.enabled: true",
        "app.user-ban.shadow-ban.cancellation.enabled: true"
})
public class ProductCommentShadowBansTest extends AbstractSpringTest {

    @Autowired
    private UserService userService;

    @Autowired
    private ProductRepository productRepository;

    @Autowired
    private CommentRepository commentRepository;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ConfigParamService configParamService;

    @Autowired
    private UserBanRepository userBanRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private CommissionGridService commissionGridService;

    @MockBean
    private KafkaSenderService kafkaSenderService;

    @Autowired
    private PromptProcessedEventsHandler promptProcessedEventsHandler;

    @Autowired
    private AsyncExecutor asyncExecutor;

    @Autowired
    private TestApiConfiguration testApiConfiguration;

    @Value("${app.user-ban.shadow-ban.new-user-max-time-from-registration}")
    private Duration maxTimeFromRegistrationForNewUser;

    @Value("${app.user-ban.shadow-ban.addition.comments-in-a-row.harmful-count}")
    public int harmfulCount;

    @Value("${app.master.user-id}")
    private long masterUserId;

    private static Long userId;

    private static Product product;

    private static ApiV2Client userClient;

    private static ApiV2Client sellerClient;

    private static String createCommentsUrl;

    private static String getCommentsUrlPrefix;

    @BeforeAll
    public void initialize() {

        createCommentsUrl = testApiConfiguration.getServerUrl() + "/api/v2/comments";

        getCommentsUrlPrefix = testApiConfiguration.getServerUrl() + "/api/v2/comments/product/";

        String userEmail = RandomStringUtils.randomAlphabetic(3).toLowerCase() + "@test.mail";
        String userPassword = RandomStringUtils.randomAlphabetic(8);

        userId = userRepository.save(prepareUser(userEmail, userPassword)).getId();

        userClient = new ApiV2Client(userEmail, userPassword);

        String sellerEmail = RandomStringUtils.randomAlphabetic(3).toLowerCase() + "@test.mail";
        String sellerPassword = RandomStringUtils.randomAlphabetic(8);

        User productSeller = userRepository.save(prepareUser(sellerEmail, sellerPassword));

        sellerClient = new ApiV2Client(sellerEmail, sellerPassword);

        product = createProduct(ProductState.PUBLISHED, productSeller);

        configParamService.setValueAsString(CONFIG_PARAM_COMMENT_HARMFULNESS_SCORE_CALC_ENABLED, "true");
        configParamService.setValueAsString(CONFIG_PARAM_COMMENT_HARMFULNESS_SCORE_CALC_LLM_CONTEXT_TEMPLATE, "Mock context");
        configParamService.setValueAsString(CONFIG_PARAM_COMMENT_HARMFULNESS_SCORE_CALC_LLM_PROMPT_TEMPLATE, "Mock prompt");
        configParamService.setValueAsString(CONFIG_PARAM_COMMENT_HARMFULNESS_SCORE_CALC_THRESHOLD, "0.8");
    }

    @BeforeEach
    public void beforeEach() {
        deleteShadowBans(userId);
        deleteCommentsByPublisher(userId);
        deleteNotificationsByUser(userId);
    }

    @AfterAll
    public void afterAll() {
        deleteShadowBans(userId);
        deleteCommentsByPublisher(userId);
        deleteNotificationsByUser(userId);
    }

    @Test
    public void _01_publishCommentFromNotBanedUser() {

        // бан у пользователя отсутствует,
        // hiddenAtTime при создании НЕ заполненяется,
        // нотификаций пользователям добавляются
        // обработка события от llm-proxy отсутствует

        Long commentId = createCommentByUserWithAssertions();

        Comment comment = commentRepository.findById(commentId).get();
        assertNull(comment.getHiddenAtTime());

        awaitNotification(
                product.getSeller().getId(),
                NewCommentNotification.class.getSimpleName(),
                commentId);
    }

    @NotNull
    private Long createCommentByUserWithAssertions() {
        return createCommentWithAssertions(userClient);
    }

    @NotNull
    private Long createCommentBySellerWithAssertions() {
        return createCommentWithAssertions(sellerClient);
    }

    @NotNull
    private Long createCommentWithAssertions(ApiV2Client client) {

        ResponseEntity<CommentDTO> response = client.request(
                createCommentsUrl,
                null,
                HttpMethod.POST,
                MediaType.MULTIPART_FORM_DATA,
                TestUtils.getMultivalueMapWithObjectFields(getSimpleProductCommentData("Тест")),
                CommentDTO.class,
                true);

        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getId());

        return response.getBody().getId();
    }

    @NotNull
    private List<CommentDTO> getComments(ApiV2Client client, Long productId) {

        ResponseEntity<List<CommentDTO>> response = client.request(
                getCommentsUrlPrefix + productId,
                null,
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<List<CommentDTO>>() {},
                true);

        return response.getBody();
    }

    @Test
    public void _02_publishCommentFromBanedUserWithoutLlmEvent() {

        // бан у пользователя имеется,
        // hiddenAtTime при создании заполненяется,
        // нотификаций пользователям НЕ добавляются,
        // обработка события от llm-proxy отсутствует

        createShadowBan(userId, masterUserId);

        User userRef = userRepository.getOne(userId);
        for (int i = 0; i < harmfulCount - 1; i++) {
            createComment(userRef, product, true);
        }

        Long commentId = createCommentByUserWithAssertions();

        Comment comment = commentRepository.findById(commentId).get();
        assertNotNull(comment.getHiddenAtTime());

        awaitNoNotification(
                product.getSeller().getId(),
                NewCommentNotification.class.getSimpleName(),
                commentId);
    }

    @Test
    public void _03_publishCommentFromBanedUserWithLlmEventHarmful() {

        // бан у пользователя имеется,
        // hiddenAtTime при создании заполненяется,
        // нотификаций пользователям НЕ добавляются,
        // обработка события от llm-proxy имеется, коммент подозрительный, т.о. hiddenAtTime остается без изменений

        createShadowBan(userId, masterUserId);

        User userRef = userRepository.getOne(userId);
        for (int i = 0; i < harmfulCount - 1; i++) {
            createComment(userRef, product, true);
        }

        configLlmEventHandlingEmulation(true);

        Long commentId = createCommentByUserWithAssertions();

        awaitScoreCalculation(commentId);

        Comment comment = commentRepository.findById(commentId).get();
        assertNotNull(comment.getHiddenAtTime());

        awaitNoNotification(
                product.getSeller().getId(),
                NewCommentNotification.class.getSimpleName(),
                commentId);
    }

    private Optional<BigDecimal> awaitScoreCalculation(Long commentId) {
        return await()
                .atMost(5000, TimeUnit.MILLISECONDS)
                .pollInterval(200, TimeUnit.MILLISECONDS)
                .until(() -> getHarmfulnessScore(commentId), Optional::isPresent);
    }

    private void awaitNotification(Long userId, String dtype, Long commentId) {
        await()
                .atMost(5000, TimeUnit.MILLISECONDS)
                .pollInterval(200, TimeUnit.MILLISECONDS)
                .until(() -> getCommentNotification(userId, dtype, commentId), Optional::isPresent);
    }

    private void awaitNoNotification(Long userId, String dtype, Long commentId) {
        await()
                .during(5000, TimeUnit.MILLISECONDS)
                .pollInterval(200, TimeUnit.MILLISECONDS)
                .until(() -> !getCommentNotification(userId, dtype, commentId).isPresent());
    }

    private void configLlmEventHandlingEmulation(boolean harmful) {
        doAnswer(inv -> {
            PromptProcessingCommand command = inv.getArgument(2);
            emulateLlmEventHandling(
                    new PromptProcessedEvent(
                            command.getId(),
                            CommandType.CALC_COMMENT_HARMFULNESS_SCORE,
                            objectMapper.writeValueAsString(
                                    new ResponseObject(
                                            ImmutableList.of("OTHER"),
                                            harmful ? BigDecimal.ONE : BigDecimal.ZERO,
                                            "")),
                            null,
                            "any",
                            "any"));
            return null;
        }).when(kafkaSenderService).sendObjectMessageSilentAsync(anyString(), anyString(), any());
    }

    @Test
    public void _04_publishCommentFromBanedUserWithLlmEventHarmless() {

        // бан у пользователя имеется,
        // hiddenAtTime при создании заполняется,
        // нотификаций пользователям НЕ добавляются,
        // обработка события от llm-proxy имеется, коммент НЕ подозрительный, но т.к. юзер в бане, hiddenAtTime не сбрасывается
        // и нотификаций пользователям НЕ добавляются

        createShadowBan(userId, masterUserId);

        User userRef = userRepository.getOne(userId);
        for (int i = 0; i < harmfulCount - 1; i++) {
            createComment(userRef, product, true);
        }

        configLlmEventHandlingEmulation(false);

        Long commentId = createCommentByUserWithAssertions();

        awaitScoreCalculation(commentId);

        Comment comment = commentRepository.findById(commentId).get();
        assertNotNull(comment.getHiddenAtTime());

        awaitNoNotification(
                product.getSeller().getId(),
                NewCommentNotification.class.getSimpleName(),
                commentId);
    }

    @Test
    public void _05_addUserBan() {

        // бан у пользователя отсутствует,
        // выполняются все условия добавления бана

        setProductsState(userId, ProductState.DELETED);

        setOrdersState(userId, OrderState.DELETED);

        updateUserRegistrationTime(ZonedDateTime.now().minus(maxTimeFromRegistrationForNewUser.minusHours(1)));

        User userRef = userRepository.getOne(userId);
        for (int i = 0; i < harmfulCount - 1; i++) {
            createComment(userRef, product, true);
        }

        configLlmEventHandlingEmulation(true);

        awaitScoreCalculation(createCommentByUserWithAssertions());

        assertEquals(1L, getActiveBansCount(userId));

        // теперь бан имеется,
        // поэтому даже если все условия бана выполняются, новый бан не создается

        awaitScoreCalculation(createCommentByUserWithAssertions());

        assertEquals(1L, getActiveBansCount(userId));

        // эмулируем отмену бана живым админом;
        // даже если теперь все условия бана выполняются, т.к. бан отменен НЕ мастер юзером, новый бан не создается.
        // фактически отменяем любым пользователем, отличным от мастер юзера (считается системой как админ)

        updateShadowBans(userId, false, product.getSeller().getId());

        awaitScoreCalculation(createCommentByUserWithAssertions());

        assertEquals(0L, getActiveBansCount(userId));

        // эмулируем отмену бана мастер юзером;
        // если теперь все условия бана выполняются, т.к. бан отменен мастер юзером, новый бан создается.

        updateShadowBans(userId, false, masterUserId);

        awaitScoreCalculation(createCommentByUserWithAssertions());

        assertEquals(1L, getActiveBansCount(userId));

        // удаляем все баны и проверяем каждое условие создания бана в отдельности

        deleteShadowBans(userId);

        // условие по недавней дате регистрации

        updateUserRegistrationTime(ZonedDateTime.now().minus(maxTimeFromRegistrationForNewUser.plusHours(1)));

        awaitScoreCalculation(createCommentByUserWithAssertions());

        assertEquals(0L, getActiveBansCount(userId));

        // условие по отсутствию продуктов

        updateUserRegistrationTime(ZonedDateTime.now().minus(maxTimeFromRegistrationForNewUser.minusHours(1)));
        createProduct(ProductState.PUBLISHED, userRef);

        awaitScoreCalculation(createCommentByUserWithAssertions());

        assertEquals(0L, getActiveBansCount(userId));

        // условие по отсутствию заказов

        setProductsState(userId, ProductState.DELETED);
        createOrder(OrderState.HOLD, userRef);

        awaitScoreCalculation(createCommentByUserWithAssertions());

        assertEquals(0L, getActiveBansCount(userId));

        // условие по наличию достаточного количества подозрительных комментов

        setOrdersState(userId, OrderState.DELETED);
        updateHarmfulnessScore(userId, BigDecimal.ZERO);

        awaitScoreCalculation(createCommentByUserWithAssertions());

        assertEquals(0L, getActiveBansCount(userId));

        // проверим в финале, что бан создается при соблюдении всех условий

        updateHarmfulnessScore(userId, BigDecimal.ONE);

        awaitScoreCalculation(createCommentByUserWithAssertions());

        assertEquals(1L, getActiveBansCount(userId));
    }

    private long getActiveBansCount(Long userId) {
        return userBanRepository.findAllByUserIdAndBanType(userId, COMMENT_SHADOW_BAN, Pageable.unpaged())
                .stream()
                .filter(UserBan::isBaned)
                .count();
    }

    @Test
    public void _06_cancelUserBan() {

        // у пользователя имеется бан, созданный живым админом,
        // условия для отмены бана выполняются, но т.к. бан создан живым админом, бан не отменяется.

        createShadowBan(userId, product.getSeller().getId());

        updateUserRegistrationTime(ZonedDateTime.now().minus(maxTimeFromRegistrationForNewUser.plusHours(1)));

        // 100% нормальных комментов
        User userRef = userRepository.getOne(userId);
        for (int i = 0; i < 5; i++) {
            createComment(userRef, product, false);
        }

        // убеждаемся, что активный бан имеется
        assertEquals(1L, getActiveBansCount(userId));

        configLlmEventHandlingEmulation(false);

        awaitScoreCalculation(createCommentByUserWithAssertions());

        assertEquals(1L, getActiveBansCount(userId));

        // меняем автора бана на мастер юзера,
        // теперь бан отменяется мастер юзером.
        updateShadowBans(userId, true, masterUserId);

        awaitScoreCalculation(createCommentByUserWithAssertions());

        assertEquals(0L, getActiveBansCount(userId));
    }

    @Test
    public void _07_getComments() {

        // бан у первого пользователя отсутствует, у второго пользователя - имеется,
        // коммент от второго пользователя скрывается,
        // обработка события от llm-proxy отсутствует,
        // в ответе на запрос комментов первого пользователя коммент второго пользователя отсутствует,
        // в ответе на запрос комментов второго пользователя оба коммента имеются.

        createShadowBan(userId, product.getSeller().getId());

        Long userCommentId = createCommentByUserWithAssertions();
        Comment userComment = commentRepository.findById(userCommentId).get();
        assertNotNull(userComment.getHiddenAtTime());

        Long sellerCommentId = createCommentBySellerWithAssertions();
        Comment sellerComment = commentRepository.findById(sellerCommentId).get();
        assertNull(sellerComment.getHiddenAtTime());

        List<CommentDTO> comments = getComments(userClient, product.getId());
        assertEquals(
                comments.stream()
                        .map(c -> c.getId())
                        .collect(Collectors.toSet()),
                ImmutableSet.of(userCommentId, sellerCommentId));

        comments = getComments(sellerClient, product.getId());
        assertEquals(
                comments.stream()
                        .map(c -> c.getId())
                        .collect(Collectors.toSet()),
                ImmutableSet.of(sellerCommentId));
    }

    private void updateUserRegistrationTime(ZonedDateTime time) {
        User user = userRepository.findById(userId).get();
        user.setRegistrationTime(time);
        userRepository.save(user);
    }

    private void createShadowBan(Long userId, Long authorId) {
        userBanRepository.save(prepareShadowBan(userId, authorId));
    }

    private UserBan prepareShadowBan(Long userId, Long authorId) {
        return new UserBan()
                .setBaned(true)
                .setUserId(userId)
                .setBanType(COMMENT_SHADOW_BAN)
                .setEndDate(ZonedDateTime.now().plusYears(1))
                .setDescription("Description")
                .setCreateDate(ZonedDateTime.now())
                .setUid(UUID.randomUUID())
                .setTitle("title")
                .setStartDate(ZonedDateTime.now())
                .setStatusChangedUserId(authorId);
    }

    private Comment createComment(User publisher, Product product, boolean harmful) {
        return commentRepository.save(prepareComment(publisher, product, harmful));
    }

    private Comment prepareComment(User publisher, Product product, boolean harmful) {
        return new Comment()
                .setText("Тест")
                .setPublishTime(ZonedDateTime.now())
                .setPublisher(publisher)
                .setProduct(product)
                .setHarmfulnessScore(harmful ? BigDecimal.ONE : BigDecimal.ZERO);
    }

    private ProductCommentData getSimpleProductCommentData(String text) {
        ProductCommentData result = new ProductCommentData();
        result.setProductId(product.getId());
        result.setText(text);
        return result;
    }

    private void deleteCommentsByPublisher(Long publisherId) {
        jdbcTemplate.execute("delete from comment where publisher_id = " + publisherId);
    }

    private Optional<BigDecimal> getHarmfulnessScore(long commentId) {
        try {
            return Optional.ofNullable(jdbcTemplate.queryForObject(
                    "select harmfulness_score from comment where id = " + commentId,
                    BigDecimal.class));
        } catch (EmptyResultDataAccessException e) {
            return Optional.empty();
        }
    }

    private Optional<Long> getCommentNotification(long userId, String dtype, long commentId) {
        try {
            return Optional.ofNullable(jdbcTemplate.queryForObject(
                    "select id from notification where user_id = " + userId + " and dtype = '" + dtype + "' and comment_id = " + commentId, Long.class));
        } catch (EmptyResultDataAccessException e) {
            return Optional.empty();
        }
    }

    private void updateHarmfulnessScore(Long publisherId, BigDecimal score) {
        jdbcTemplate.execute("update comment set harmfulness_score = " + score + " where publisher_id = " + publisherId);
    }

    private void deleteShadowBans(Long userId) {
        jdbcTemplate.execute("delete from user_ban where user_id = " + userId + " and ban_type = '" + COMMENT_SHADOW_BAN.name() + "'");
    }

    private void updateShadowBans(Long userId, boolean baned, Long changeUserId) {
        jdbcTemplate.execute("update user_ban set is_baned = " + baned + ", status_changed_by_user_id = " + changeUserId + " where user_id = " + userId + " and ban_type = '" + COMMENT_SHADOW_BAN.name() + "'");
    }

    private void setProductsState(Long sellerId, ProductState state) {
        jdbcTemplate.execute("update product set product_state = '" + state.name() + "' where seller_id = " + sellerId);
    }

    private void setOrdersState(Long buyerId, OrderState state) {
        jdbcTemplate.execute("update \"order\" set state = '" + state.name() + "' where buyer_id = " + buyerId);
    }

    private void deleteNotificationsByUser(Long userId) {
        jdbcTemplate.execute("delete from notification where user_id = " + userId);
    }

    private void emulateLlmEventHandling(PromptProcessedEvent event) {
        asyncExecutor.run(() -> {
            promptProcessedEventsHandler.handle(event);
        });
    }

    private User prepareUser(String email, String password) {
        return new User()
                .setChangeTime(Utils.nowAtUTC())
                .setEmail(email)
                .setNickname(RandomStringUtils.randomAlphabetic(5))
                .setHashedPassword(userService.getHashedPassword(password))
                .setRegistrationTime(ZonedDateTime.now())
                .setApiHashedPassword(userService.getApiHashedPassword(password, email))
                .setUserType(SIMPLE_USER)
                .setChatToken(UUID.randomUUID().toString())
                .setPhone(RandomStringUtils.randomNumeric(10))
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
    }

    private Product createProduct(ProductState state, User seller) {
        return productRepository.save(prepareProduct(state, seller));
    }

    private Product prepareProduct(ProductState state, User seller) {
        Product product = new Product();
        product.setProductState(state);
        product.setBrand(brand());
        product.setCategoryId(categoryId());
        product.setSeller(seller);
        product.setCurrentPrice(BigDecimal.TEN);
        return product;
    }

    private Brand brand() {
        Brand brand = new Brand();
        brand.setId(1L);
        brand.setName("Brand");
        return brand;
    }

    private long categoryId() {
        return 4;
    }

    private Order createOrder(OrderState state, User buyer) {
        return orderRepository.save(prepareOrder(state, buyer));
    }

    private Order prepareOrder(OrderState state, User buyer) {
        Order order = new Order();
        order.setUuid(UUID.randomUUID());
        order.setState(CREATED);
        order.setState(state);
        order.setOrderSource(OrderSource.WEB);
        order.setGuestToken(UUID.randomUUID().toString());
        order.setBuyer(buyer);
        return order;
    }
}
