package ru.oskelly.tests.pr.suite4.presentation.api.v2;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableList;
import lombok.Setter;
import lombok.SneakyThrows;
import org.jsoup.Connection;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.TestApiConfiguration;
import su.reddot.domain.model.Brand;
import su.reddot.domain.model.category.Category;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.catalog.CatalogCategory;
import su.reddot.domain.service.catalog.CategoryService;
import su.reddot.domain.service.deeplink.DeeplinkResolveResult;
import su.reddot.domain.service.deeplink.DeeplinkService;
import su.reddot.domain.service.dto.*;
import su.reddot.domain.service.dto.attribute.AttributeDTO;
import su.reddot.domain.service.like.LikeService;
import su.reddot.domain.service.product.ProductService;
import su.reddot.domain.service.user.UserService;
import su.reddot.presentation.api.v2.Api2Response;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_04)
public class DeeplinkControllerV2Test extends AbstractSpringTest {
    @Autowired
    private TestApiConfiguration testApiConfiguration;

	@Value("${test.api.user-email}")
	private String email;
	@Value("${test.api.user-password}")
	private String password;
	@Value("${test.api.user-id}")
	private Long userId;

	@Autowired
	@Setter
    private UserService userService;

	@Autowired
	@Setter
	private ProductService productService;

	@Autowired
	@Setter
	private CategoryService categoryService;

	@Autowired
	@Setter
	private LikeService likeService;

	@Autowired
	private ObjectMapper objectMapper;


	//Клиент для работы с API диплинков и каталога
    static ApiV2Client apiV2Client;

    private static final int PRODUCTS_COUNT = 10;

    private final static int JSOUP_TIMEOUT = 100000;

    @BeforeEach
    public void initialize() {
        if(apiV2Client == null) apiV2Client = new ApiV2Client(email, password);
    }

	private String getDeeplinkServiceUrl(){
        return testApiConfiguration.getServerUrl() + "/api/v2/deeplink";
    }
	private String getCatalogProductsUrl(){
		return testApiConfiguration.getServerUrl() + "/api/v2/catalog/products";
	}
	private String getProductUrl(Long productId){
		return testApiConfiguration.getServerUrl() + "/api/v2/catalog/products/" + productId.toString();
	}
	private String getSetSubUrl(String hash){
		return "/catalog/set/" + hash;
	}
	private String getAccountFavoritesUrl(){
		return "/account/favorites";
	}
	private String getProductPath(Long productId){
		return "/products/" + productId.toString();
	}
	private String getProfilePath(Long profileId){
		return "/profile/" + profileId.toString();
	}
	private String getBrandPath(String sexUrl,  String baseUrl){
		return "/brands/"+ sexUrl + "/" + baseUrl  ;
	}
	private String getCatalogPath(String baseUrl, Map<String,String> filterPrams){
		String ret = "/catalog";
		if(baseUrl != null) ret += "/" + baseUrl;
		if(filterPrams != null && !filterPrams.isEmpty()) {
			ret += "?";
			List<String> listKeys = new ArrayList<>(filterPrams.keySet());
			List<String> valueList = new ArrayList<String>(filterPrams.values());
			for (int i = 0; i < filterPrams.size(); i++) {
				ret += listKeys.get(i) + "=" + valueList.get(i) + "&";
			}
		}
		return ret;
	}
	private String getYouWillLikeItePath(){
    	return "/catalog/you-will-like-it";
	}
	private String getNewArrivalsPath(){
		return "/catalog/new";
	}
	private String getOurChoicePath(){
		return "/catalog/ourchoice";
	}
	private String getMyLikedBrandsWithSexPath(){
		return "/catalog?userLikedBrands=true&useUserSex=true";
	}
	private String getHermesBagsPath(){
		return "/catalog/hermesbags";
	}
	private String getFemaleStreetwearPath(){
		return "/catalog/zhenskoe/streetwear";
	}
	private String getMaleStreetwearPath(){
		return "/catalog/muzhskoe/streetwear";
	}
	private String getKidsStreetwearPath(){
		return "/catalog/detskoe/streetwear";
	}

	private String getSiteLinkUrl(String path){
		return testApiConfiguration.getServerUrl() + path;
	}

	//Проверка соответствия выдачи ссылок каталога сайта с различными параметрами фильтрации (CategoryController.CategoryRequest)
	//с выдачей фильтра диплинков.
	//Сравниваем только первые PRODUCTS_COUNT товаров
	@Test
	public void testCatalogLinks() throws IOException {
		// These are predefined with full fit (attrs / products), replace when fail (on database data change)
		List<Long> categoryFit = ImmutableList.of(39L, 50L, 51L, 52L, 54L, 57L, 58L, 59L, 79L, 86L, 93L, 94L, 96L, 98L,
				99L, 121L, 131L, 141L, 161L, 162L, 163L, 164L, 165L, 166L, 525L, 559L, 567L, 572L, 575L, 578L);
		List<Long> categoryIds = categoryService.getLeafCategoryIds(categoryService.ROOT_CATEGORY_ID);
		List<Long> categoryFlt = categoryIds.stream().filter(categoryFit::contains).collect(Collectors.toList());
		//
		List<Category> categoryList = categoryService.getCategories(categoryFlt);
		List<Category> fullTestDone = new ArrayList<>();
		for (Category category : categoryList) {
			Map<String,String> filterParam = new HashMap<>();
			assertCatalogLinkCorrect(
					getCatalogPath(category.getUrlName(), filterParam),
					category.getDisplayName());

			List<AttributeDTO> attrDTOList = categoryService.getAttributes(category.getId());
			if (attrDTOList.isEmpty()) {
				continue;
			}
			filterParam.put("filter", attrDTOList.get(0).getAttributeValues().get(0).getId().toString());
			assertCatalogLinkCorrect(
					getCatalogPath(category.getUrlName(), filterParam),
					category.getDisplayName());

			List<ProductDTO> productDTOS = getProductDTOsFromUrl(getCatalogPath(category.getUrlName(), filterParam));
			if (productDTOS.isEmpty()) {
				continue; //Пустые разделы нас не интересуют
			}

			filterParam.put("size", productDTOS.get(0).getSizes().get(0).getId().toString());
			assertCatalogLinkCorrect(
					getCatalogPath(category.getUrlName(), filterParam),
					category.getDisplayName());

			productDTOS = getProductDTOsFromUrl(getCatalogPath(category.getUrlName(), filterParam));
			if (productDTOS.size() < 2) {
				continue;
			}
			BigDecimal price1 = productDTOS.get(0).getPrice();
			BigDecimal price2 = productDTOS.get(1).getPrice();
			filterParam.put("startPrice", price1.longValue() < price2.longValue() ? "" + price1.longValue() : "" + price2.longValue());
			filterParam.put("endPrice", price1.longValue() > price2.longValue() ? "" + price1.longValue() : "" + price2.longValue());
			assertCatalogLinkCorrect(
				getCatalogPath(category.getUrlName(), filterParam),
				category.getDisplayName());
			//
			fullTestDone.add(category);
			if (fullTestDone.size() >= 10) {
				break;
			}
		}
		assertThat(fullTestDone).hasSizeGreaterThanOrEqualTo(10);	// Full test at least 10 categories
	}

	//Проверка соответствия контента профиля на сайте, выдаче диплнка
	@Test
	public void testProfileLinks() throws IOException{
		Map<Long,Integer> topSellers = productService.getTopSellerProductCountsCached(5);
		List<Long> listIds = new ArrayList<>( topSellers.keySet() );
    	for(int i = 0; i < topSellers.size() && i < 5; i++){
			assertProfileLinkCorrect(getProfilePath(listIds.get(i)));
		}
	}

	//Проверка соответствия контента бренда на сайте, выдаче диплнка
	@Test
	public void testBrandLinks() throws IOException{
		//Проверить сам бренд и товары бренда
		List<Long> productCategories = new ArrayList<>();
		List<Brand> brands = productService.getActualBrands(productCategories);
		for(int i = 0; i < 3; i++){
			assertBrandLinkCorrect(getBrandPath("zhenskoe" ,brands.get(i).getUrl()));
			assertBrandLinkCorrect(getBrandPath("muzhskoe" ,brands.get(i).getUrl()));
			assertBrandLinkCorrect(getBrandPath("detskoe" ,brands.get(i).getUrl()));
		}
	}

	//Проверка соответствия контента карточки товара на сайте, выдаче диплнка
	@Test
	public void testProductLinks() throws IOException{
		List<ProductDTO> products = getProductDTOsByCatalogFilter(new HashMap<String, String>());
		for(int i = 0; i < products.size() && i < 5; i++){
			assertProductLinkCorrect(getProductPath(products.get(i).getProductId()));
		}
	}

	//Проверка соответствия контента избронного на сайте, выдаче диплнка для неавторизованного
	@Test
	public void testAccountFavoritesLinksForUnauthorizedUser() throws IOException{
		apiV2Client.logout();
		assertAccountFavoritesLinkUnauthorizedUserCorrect(getAccountFavoritesUrl());
	}

	//Проверка соответствия контента избронного на сайте, выдаче диплнка для авторизованного
	@Test
	public void testAccountFavoritesLinksForAuthorizedUser() throws IOException{
    	apiV2Client.login(testApiConfiguration.getServerUrl());
		assertAccountFavoritesLinkAuthorizedUserCorrect(getAccountFavoritesUrl());
		apiV2Client.logout();
	}

	//Проверка наличия контента подборки "Вам это понравится"
	@Test
	public void testYouWillLikeIt() throws IOException{
		//Проверить соответствие товаров в разделе "Вам это понравится"
		assertCatalogLinkCorrect(getYouWillLikeItePath(), null);
	}

	//Проверка наличия контента подборки "Новые поступления"
	@Test
	public void testNewArrivals() throws IOException{
		//Проверить соответствие товаров в разделе "Новые поступления"
		assertCatalogLinkCorrect(getNewArrivalsPath(), null);
	}

	//Проверка наличия контента подборки "Выбор OSKELLY"
	@Test
	public void testOurChoice() throws IOException{
		//Проверить соответствие товаров в разделе "Выбор OSKELLY"
		assertCatalogLinkCorrect(getOurChoicePath(), null);
	}

	//Проверка наличия контента в каталоге с фильтром "Мои любимые бренды" с применением пола
	@Test
	public void testMyLikedBrandsWithSex() throws IOException{
		//Проверить соответствие товаров в разделе каталога с применением фильтра "Мои любимые бренды", "По моему полу"
		assertCatalogLinkCorrect(getMyLikedBrandsWithSexPath(), null);
	}

	//Проверка соответствия результата выдачи диплинка /catalog/hermesbags с содержимым веба
	@Test
	public void testHermesBags() throws IOException{
		assertCatalogLinkCorrect(getHermesBagsPath(), "CУМКИ HERMES");
	}

	//Проверка соответствия результата выдачи диплинка /catalog/zhenskoe/streetwear с содержимым веба
	@Test
	public void testFemaleStreetwear() throws IOException{
		assertCatalogLinkCorrect(getFemaleStreetwearPath(), "STREETWEAR ДЛЯ НЕЕ");
	}

	//Проверка соответствия результата выдачи диплинка /catalog/muzhskoe/streetwear с содержимым веба
	@Test
	public void testMaleStreetwear() throws IOException{
		assertCatalogLinkCorrect(getMaleStreetwearPath(), "STREETWEAR ДЛЯ НЕГО");
	}

	//Проверка соответствия результата выдачи диплинка /catalog/detskoe/streetwear с содержимым веба
	@Test
	public void testKidsStreetwear() throws IOException{
		assertCatalogLinkCorrect(getKidsStreetwearPath(), "STREETWEAR");
	}

	private String getDataAtUrl(String url) {
		ResponseEntity<String> responseOne = apiV2Client.request(url, null, HttpMethod.GET, null, String.class, true);
		if (responseOne.getStatusCode() != HttpStatus.FOUND) {	// TODO: Dunno if we should use "Follow redirects", leaving RAW to be able to test RAW scenarios
			return responseOne.getBody();
		}
		String url302 = Objects.toString(responseOne.getHeaders().getLocation(), null);
		ResponseEntity<String> responseTwo = apiV2Client.request(url302, null, HttpMethod.GET, null, String.class, true);
		return responseTwo.getBody();
	}

	private void assertCatalogLinkCorrect(String path, String expectedTitle) throws IOException {
		String url = getSiteLinkUrl(path);

		//Получаем товары из веба и собираем первые PRODUCTS_COUNT id товаров
		List<Long> webProductIds = new ArrayList<>();
		Document doc = Jsoup.parse(getDataAtUrl(url));
		Elements webProducts = doc.select("div.product");
		for(int i = 0; i < webProducts.size() && i < PRODUCTS_COUNT; i++){
			Element webProduct = webProducts.get(i);
			String productId = webProduct.attr("data-product-id");
			webProductIds.add(Long.parseLong(productId));
		}

		//Получаем товары из диплинк и собираем первые PRODUCTS_COUNT id товаров
		DeeplinkResolveResult<List<CategoryDTO>> deeplinkResult = getDeeplinkResolveResult(url, new ParameterizedTypeReference<Api2Response<DeeplinkResolveResult<List<CategoryDTO>>>>() {});
		assertEquals("Catalog", deeplinkResult.getType());
		assertEquals(expectedTitle, deeplinkResult.getTitle());
		List<Long> deeplinkProductIds = getProductIdsFromDeeplinkResultFilter(deeplinkResult);

		//Списки идентичны
		assertEquals(webProductIds, deeplinkProductIds);
	}

	@Test
	public void testNeitherBrandNorCategoryFoundReturnNotFoundError() {
		String catalogPath = getCatalogPath("test/not-found", Collections.emptyMap());
		String url = getSiteLinkUrl(catalogPath);

		assertDeeplinkCatalogItemNotFoundError(url);
	}

	@Test
	void testBrandInPathReturnSuccess() {
		Brand brand = productService.getActualBrands(Collections.emptyList()).iterator().next();

		String catalogPath = getCatalogPath(brand.getUrl(), Collections.emptyMap());
		String url = getSiteLinkUrl(catalogPath);

		assertDeepLinkResultSuccess(url);
	}

	@Test
	void testCategoryInPathReturnSuccess() {
		CatalogCategory category = categoryService.getLeafCategories(categoryService.ROOT_CATEGORY_ID).iterator().next();

		String catalogPath = getCatalogPath(category.getUrlName(), Collections.emptyMap());
		String url = getSiteLinkUrl(catalogPath);

		assertDeepLinkResultSuccess(url);
	}

	private void assertProductLinkCorrect(String path) throws IOException {
    	//получаем веб товар и берем данные
		String url = getSiteLinkUrl(path);
		Document doc = getJSoupConnection(url).get();
		Long productId = Long.parseLong(doc.select("body").attr("data-product-id"));
		String brandName = doc.select("body .name-block-brand .header_brand_block").text();
		String categoryName = doc.select("body .info-block_product_name").text();

		//получаем товары из диплинк
		DeeplinkResolveResult<ProductDTO> deeplinkResult = getDeeplinkResolveResultForProduct(url);
		ProductDTO product = deeplinkResult.getObject();

		//сравниваем
		assertEquals("Product", deeplinkResult.getType());
		assertEquals(productId, product.getProductId());
		assertEquals(brandName, product.getBrand().getName());
		assertEquals(categoryName, product.getCategory().getSingularName());
	}

	private void assertProfileLinkCorrect(String path) throws IOException {
		//получаем веб профиль и товары, и берем их данные
		String url = getSiteLinkUrl(path);
		Document doc = Jsoup.parse(getDataAtUrl(url));
		Long profileId = Long.parseLong(doc.select("body .user_info").attr("data-profile-id"));
		String profileName = doc.select("body .user_info .name").text();

		List<Long> webProfileProductIds = new ArrayList<>();
		Elements webProducts = doc.select(".all_catalog_items div.product");
		for(int i = 0; i < webProducts.size() && i < PRODUCTS_COUNT; i++){
			Element webProduct = webProducts.get(i);
			String productId = webProduct.attr("data-product-id");
			webProfileProductIds.add(Long.parseLong(productId));
		}

		//получаем товары и профиль из диплинк
		DeeplinkResolveResult<PublicProfileDTO> deeplinkResult = getDeeplinkResolveResult(url, new ParameterizedTypeReference<Api2Response<DeeplinkResolveResult<PublicProfileDTO>>>() {});
		assertEquals("Profile", deeplinkResult.getType());
		PublicProfileDTO profile = deeplinkResult.getObject();
		List<Long> deeplinkProductIds = getProductIdsFromDeeplinkResultFilter(deeplinkResult);

		//сравниваем
		assertEquals(profileId, profile.getId());
		assertEquals(profileName, profile.getName());
		assertEquals(webProfileProductIds, deeplinkProductIds);
	}

	private void assertBrandLinkCorrect(String path) throws IOException {
		//получаем веб бренд и товары, и берем их данные
		String url = getSiteLinkUrl(path);
		Document doc = getJSoupConnection(url).get();
		Elements webProducts = doc.select(".all_catalog_items div.product");
		String brandName = doc.select("body .title_page").text();
		List<Long> webBrandProductIds = new ArrayList<>();
		for(int i = 0; i < webProducts.size() && i < PRODUCTS_COUNT; i++){
			Element webProduct = webProducts.get(i);
			String productId = webProduct.attr("data-product-id");
			webBrandProductIds.add(Long.parseLong(productId));
		}

		//получаем бренд и товары из диплинк
		DeeplinkResolveResult<BrandDTO> deeplinkResult = getDeeplinkResolveResult(url, new ParameterizedTypeReference<Api2Response<DeeplinkResolveResult<BrandDTO>>>() {});
		assertEquals("Brand", deeplinkResult.getType());
		BrandDTO brand = deeplinkResult.getObject();
		List<Long> deeplinkProductIds = getProductIdsFromDeeplinkResultFilter(deeplinkResult);

		//сравниваем
		if (path.contains("zhenskoe")) {
			assertEquals(brandName, "Женское " + brand.getName());
		} else if (path.contains("muzhskoe")) {
			assertEquals(brandName, "Мужское " + brand.getName());
		} else if (path.contains("detskoe")) {
			assertEquals(brandName, "Детское " + brand.getName());
		}
		assertEquals(webBrandProductIds, deeplinkProductIds);
	}

	private void assertSetLinkCorrect(String path) throws IOException {
		//получаем веб подборки и товары, и берем их данные
		String url = getSiteLinkUrl(path);
		Document doc = getJSoupConnection(url).get();
		String setName = doc.select("body .title_page").text();
		List<Long> webSetProductIds = new ArrayList<>();
		Elements webProducts = doc.select(".all_catalog_items div.product");
		for(int i = 0; i < webProducts.size() && i < PRODUCTS_COUNT; i++){
			Element webProduct = webProducts.get(i);
			String productId = webProduct.attr("data-product-id");
			webSetProductIds.add(Long.parseLong(productId));
		}

		//получаем товары и подборку из диплинк
		DeeplinkResolveResult<DeeplinkService.Set> deeplinkResult = getDeeplinkResolveResult(url, new ParameterizedTypeReference<Api2Response<DeeplinkResolveResult<DeeplinkService.Set>>>() {});
		assertEquals("Set", deeplinkResult.getType());
		DeeplinkService.Set set = deeplinkResult.getObject();
		List<Long> deeplinkProductIds = getProductIdsFromDeeplinkResultFilter(deeplinkResult);

		//сравниваем
		assertEquals(setName, set.getTitle());
		assertEquals(webSetProductIds, deeplinkProductIds);

	}

	private void assertAccountFavoritesLinkUnauthorizedUserCorrect(String path) throws IOException {
		//получаем товары из диплинка
		String url = getSiteLinkUrl(path);
		DeeplinkResolveResult<DeeplinkService.Set> deeplinkResult = getDeeplinkResolveResult(url, new ParameterizedTypeReference<Api2Response<DeeplinkResolveResult<DeeplinkService.Set>>>() {});
		assertEquals("Set", deeplinkResult.getType());
		DeeplinkService.Set set = deeplinkResult.getObject();

		//В выдаче должен прийти только ID несуществующего товара -1
		assertEquals(1, set.getProductIds().size());
		assertEquals(new Long(-1), set.getProductIds().get(0));

		List<Long> deeplinkProductIds = getProductIdsFromDeeplinkResultFilter(deeplinkResult);
		//Выборка по каталогу будет пустой
		assertEquals("Избранное", set.getTitle());
		assertTrue(deeplinkProductIds.isEmpty());

	}

	private void assertAccountFavoritesLinkAuthorizedUserCorrect(String path) throws IOException {
		String url = getSiteLinkUrl(path);

		//Для начала удалим все избранное у пользователя
		jdbcTemplate.execute("DELETE FROM public.like WHERE dtype='ProductLike' and user_id=" + userId);

		//получаем избранное пользователя
		List<Long> serviceUserLikedProductIds = likeService.getLikeableIdsWhichUserLiked(userService.getOne(userId), Product.class);

		//Оно пустое, т.к. мы сами все удалили
		assertTrue(serviceUserLikedProductIds.isEmpty());

		//получаем товары из диплинка
		DeeplinkResolveResult<DeeplinkService.Set> deeplinkResult = getDeeplinkResolveResult(url, new ParameterizedTypeReference<Api2Response<DeeplinkResolveResult<DeeplinkService.Set>>>() {});
		assertEquals("Set", deeplinkResult.getType());
		DeeplinkService.Set set = deeplinkResult.getObject();
		//В результате должен быть только один несуществующий ID товара -1
		assertEquals(1, set.getProductIds().size());
		assertEquals(new Long(-1), set.getProductIds().get(0));
		List<Long> deeplinkProductIds = getProductIdsFromDeeplinkResultFilter(deeplinkResult);
		//Результат выборки по каталогу будет пустым
		assertTrue(deeplinkProductIds.isEmpty());

		//Добавляем товары в избранное пользователя
		List<Product> availableProducts = productService.getRawProductsByIds(productService.getAvailableProductIdsCached().subList(0, 5));
		User user = userService.getOne(userId);
		for(Product product : availableProducts) {
			likeService.like(product, user);
		}
		//Снова получаем избранное пользователя
		serviceUserLikedProductIds = likeService.getLikeableIdsWhichUserLiked(userService.getOne(userId), Product.class);

		//Теперь оно не пустое, т.к. мы добавили товаров в избранное
		assertFalse(serviceUserLikedProductIds.isEmpty());

		//получаем товары из диплинка
		deeplinkResult = getDeeplinkResolveResult(url, new ParameterizedTypeReference<Api2Response<DeeplinkResolveResult<DeeplinkService.Set>>>() {});
		assertEquals("Set", deeplinkResult.getType());
		set = deeplinkResult.getObject();
		deeplinkProductIds = getProductIdsFromDeeplinkResultFilter(deeplinkResult);

		//сравниваем
		assertEquals("Избранное", set.getTitle());
		assertEquals(new HashSet(serviceUserLikedProductIds), new HashSet(deeplinkProductIds));
		assertEquals(serviceUserLikedProductIds.size(), deeplinkProductIds.size());
	}

	private List<Long> getProductIdsFromDeeplinkResultFilter(DeeplinkResolveResult<?> deeplinkResult){
		Map<String, String> catalogFilter = deeplinkResult.getCatalogFilter();
		if(catalogFilter == null) return null;
		List<ProductDTO> productsFromDeeplink = getProductDTOsByCatalogFilter(catalogFilter);
		List<Long> deeplinkProductIds = productsFromDeeplink.stream().map(p -> p.getProductId()).collect(Collectors.toList());
		return deeplinkProductIds;
	}

	private List<ProductDTO> getProductDTOsByCatalogFilter(Map<String, String> catalogFilter){
		return getProductDTOsPageByCatalogFilter(catalogFilter).getItems();
	}


	@SneakyThrows
	private Page<ProductDTO> getProductDTOsPageByCatalogFilter(Map<String, String> catalogFilter){
    	Map<String, String> filterParams = new HashMap<>(catalogFilter);
		filterParams.put("pageLength", "" + PRODUCTS_COUNT);
		ResponseEntity<String> response = apiV2Client.request(getCatalogProductsUrl(), filterParams, HttpMethod.GET, null, String.class, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		Api2Response<Page<ProductDTO>> body = objectMapper.readValue(response.getBody(), new TypeReference<Api2Response<Page<ProductDTO>>>() {});
		assertNotNull(body.getData());
		return body.getData();
	}

	private List<ProductDTO> getProductDTOsFromUrl(String path){
		String url = getSiteLinkUrl(path);
		DeeplinkResolveResult<List<CategoryDTO>> deeplinkResult = getDeeplinkResolveResult(url, new ParameterizedTypeReference<Api2Response<DeeplinkResolveResult<List<CategoryDTO>>>>() {});
		Map<String, String> catalogFilter = deeplinkResult.getCatalogFilter();
		if(catalogFilter == null) return null;
		List<ProductDTO> productsFromDeeplink = getProductDTOsByCatalogFilter(catalogFilter);
    	return  productsFromDeeplink;
	}

	private void assertDeepLinkResultSuccess(String url) {
		getDeeplinkResolveResult(url, genericDeeplinkResolveResult());
	}

	private static <T> ParameterizedTypeReference<Api2Response<DeeplinkResolveResult<T>>> genericDeeplinkResolveResult() {
		// @formatter:off
		return new ParameterizedTypeReference<Api2Response<DeeplinkResolveResult<T>>>() {};
		// @formatter:on
	}

	private <T> DeeplinkResolveResult<T> getDeeplinkResolveResult(String url, ParameterizedTypeReference api2responseType){
    	if(api2responseType == null) api2responseType = new ParameterizedTypeReference<Api2Response<DeeplinkResolveResult<LinkedHashMap<String,Object>>>>() {};
		ResponseEntity<Api2Response<DeeplinkResolveResult<T>>> response = apiV2Client.request(getDeeplinkServiceUrl(), null, HttpMethod.POST, TestUtils.getOneParamAsMultiValueMap("link", url), api2responseType, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		assertNotNull(response.getBody().getData());
		return response.getBody().getData();
	}

	@SneakyThrows
	private DeeplinkResolveResult<ProductDTO> getDeeplinkResolveResultForProduct(String url){
		ResponseEntity<String> response = apiV2Client.request(getDeeplinkServiceUrl(), null, HttpMethod.POST, TestUtils.getOneParamAsMultiValueMap("link", url), String.class, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		Api2Response<DeeplinkResolveResult<ProductDTO>> body = objectMapper.readValue(response.getBody(), new TypeReference<Api2Response<DeeplinkResolveResult<ProductDTO>>>(){});
		assertNotNull(body.getData());
		return body.getData();
	}

	private void assertDeeplinkUnsupportedError(String url) {
		assertDeeplinkError(url, 400, "Данный вид ссылки не поддерживается");
	}

	private void assertDeeplinkCatalogItemNotFoundError(String url) {
		assertDeeplinkError(url, 404, "Товар в каталоге не найден");
	}

	private void assertDeeplinkError(String url, int code, String message) {
		ResponseEntity<String> response = apiV2Client.request(getDeeplinkServiceUrl(), null, HttpMethod.POST, TestUtils.getOneParamAsMultiValueMap("link", url), String.class, false);
		assertThat(response.getStatusCode().value()).isEqualTo(code);
		assertThat(response.getBody()).isNotNull();
		assertThat(response.getBody()).contains(message);
	}

	private Connection getJSoupConnection(String url){
		return Jsoup.connect(url).timeout(JSOUP_TIMEOUT);
	}

	private Connection getJSoupConnection(String url, Map<String, String> cookies){
		return Jsoup.connect(url).cookies(cookies).timeout(JSOUP_TIMEOUT);
	}

}
