package ru.oskelly.tests.pr.suite4.presentation.api.v2;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.NonNull;
import org.assertj.core.util.Arrays;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.pr.common.bonuses.BonusesServiceTestConfiguration;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.AccountTestSupport;
import su.reddot.component.CartTestSupport;
import su.reddot.component.CatalogTestSupport;
import su.reddot.component.TestApiConfiguration;
import su.reddot.domain.dao.bargain.BargainRepository;
import su.reddot.domain.dao.discount.PromoCodeRepository;
import su.reddot.domain.dao.notification.NotificationRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.product.ProductItemRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.exception.OskellyTestException;
import su.reddot.domain.exception.userban.BargainBanException;
import su.reddot.domain.model.bargain.Bargain;
import su.reddot.domain.model.bargain.BargainRecord;
import su.reddot.domain.model.bargain.BargainRecordType;
import su.reddot.domain.model.bargain.BargainState;
import su.reddot.domain.model.discount.AbsolutePromoCode;
import su.reddot.domain.model.discount.PromoCode;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.notification.bargain.*;
import su.reddot.domain.model.notification.userban.BargainBanNotification;
import su.reddot.domain.model.notification.userban.CancelBargainBanNotification;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.product.OfferStatus;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.product.SalesChannel;
import su.reddot.domain.model.size.Size;
import su.reddot.domain.model.user.User;
import su.reddot.domain.model.user.userban.BanType;
import su.reddot.domain.service.adminpanel.tag.UserCommonTagCode;
import su.reddot.domain.service.adminpanel.tag.UserCommonTagService;
import su.reddot.domain.service.bargain.BargainService;
import su.reddot.domain.service.delivery.DeliveryCostService;
import su.reddot.domain.service.dto.*;
import su.reddot.domain.service.dto.bargain.*;
import su.reddot.domain.service.dto.order.GroupedCart;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.dto.order.OrderPositionDTO;
import su.reddot.domain.service.notification.NotificationService;
import su.reddot.domain.service.order.Discount;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.product.item.ProductItemService;
import su.reddot.domain.service.task.ScheduledBargainRunner;
import su.reddot.domain.service.user.UserService;
import su.reddot.domain.service.user.userban.interfaces.UserBanService;
import su.reddot.infrastructure.util.Utils;
import su.reddot.presentation.api.v2.Api2Response;

import javax.annotation.Nonnull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@AutoConfigureMockMvc
@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@ContextConfiguration(classes = {BonusesServiceTestConfiguration.class})
@DevSuite(value = TestSuiteName.TEST_SUITE_04)
public class BargainControllerApiV2Test extends AbstractSpringTest {
 	@Autowired
 	private TestApiConfiguration testApiConfiguration;
	@Value("${test.api.user-id}")
	private Long buyerId;
	@Value("${test.api.user-email}")
	private String buyerEmail;
	@Value("${test.api.user-password}")
	private String buyerPassword;
	@Value("${test.api.user2-id}")
	private Long sellerId;
	@Value("${test.api.user2-email}")
	private String sellerEmail;
	@Value("${test.api.user2-password}")
	private String sellerPassword;

	static ApiV2Client buyerClient;
	static ApiV2Client sellerClient;

	@Autowired
	private UserService userService;

	@Autowired
	private UserCommonTagService userCommonTagService;

	@Autowired
	private NotificationService notificationService;

	@Autowired
	private BargainService bargainService;

	@Autowired
	private ProductRepository productRepository;

	@Autowired
	private ProductItemRepository productItemRepository;

	@Autowired
	private NotificationRepository<BargainNotification> notificationRepository;

	@Autowired
	private BargainRepository bargainRepository;

	@Autowired
	private JdbcTemplate jdbcTemplate;

	@Autowired
	private PromoCodeRepository promoCodeRepository;

    @Autowired
    private OrderRepository orderRepository;

	@Autowired
	private UserBanService userBanService;

	@Autowired
	private DeliveryCostService deliveryCostService;

	@Autowired
	private ScheduledBargainRunner scheduledBargainRunner;

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private CartTestSupport cartTestSupport;

	@Autowired
	private CatalogTestSupport catalogTestSupport;

	@Autowired
	private AccountTestSupport accountTestSupport;

	@Autowired
	private ProductItemService productItemService;

	private static final ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

	private static Long productId;
	private static Long productId2;
	private static Long sizeId;
	private static Long sizeId2;
	private static Long bargainId;
	private static Long bargainId2;
	private static Integer initialBargainOfferedPrice;
	//Последняя предложенная цена
	private static Integer lastBargainPrice;

	//Оригинальные продавцы товаров (productId -> userId)
	private static final Map<Long, Long> originalProductSellerIdsMap = new HashMap<>();

	private static final int initialProductPrice = 100000;

	private User getBuyer() {
		return userService.getUserByEmail(buyerEmail);
	}
	private User getSeller() {
		return userService.getUserByEmail(sellerEmail);
	}
	private String getServiceUrl() {
		return testApiConfiguration.getServerUrl() + "/api/v2/bargains";
	}
	private String getOutgoingBargainsUrl() {
		return getServiceUrl() + "/outgoing";
	}
	private String getOutgoingBargainsUrl(List<BargainStateDTO.Enum> states) {
		return getOutgoingBargainsUrl() + getStatesQueryString(states);
	}
	private String getOutgoingActiveBargainsUrl() {
		return getOutgoingBargainsUrl() + "/active";
	}
	private String getOutgoingFinishedBargainsUrl() {
		return getOutgoingBargainsUrl() + "/finished";
	}
	private String getIncomingBargainsUrl() {
		return getServiceUrl() + "/incoming";
	}
	private String getIncomingBargainsUrl(List<BargainStateDTO.Enum> states) {
		return getIncomingBargainsUrl() + getStatesQueryString(states);
	}
	private String getIncomingActiveBargainsUrl() {
		return getIncomingBargainsUrl() + "/active";
	}
	private String getIncomingFinishedBargainsUrl() {
		return getIncomingBargainsUrl() + "/finished";
	}
	private String getStatesQueryString(List<BargainStateDTO.Enum> states){
		if(states == null || states.isEmpty()) return "";
		return "?states=" + states.stream().map(BargainStateDTO.Enum::name).collect(Collectors.joining(","));
	}
	private String getBargainSettinsUrl() {
		return getServiceUrl() + "/settings";
	}
	private String getBargainDetailedUrl(Long bargainId) {
		return getServiceUrl() + "/" + bargainId;
	}
	private String getBargainCheckUrl(Long productId, Long sizeId) {
		return getServiceUrl() + "/" + productId + "/" + sizeId;
	}
	private String getBargainOrTemplateUrl(Long productId, Long sizeId) {
		return getServiceUrl() + "/bargain-or-template/" + productId + "/" + sizeId;
	}
    private String getBargainBubblesUrl() {
        return getServiceUrl() + "/bubbles";
    }

	private String getAdminBargainsServiceUrl() {
		return "/api/v2/admin/bargains";
	}

	private String getAdminBargainsDetailedBargainUrl(long bargainId) {
		return getAdminBargainsServiceUrl() + "/" + bargainId;
	}

	private String getAdminBargainsBargainRestoreExpiredUrl(long bargainId) {
		return getAdminBargainsServiceUrl() + "/" + bargainId + "/restore-expired";
	}


	private static boolean initialized = false;

	/**
	 * Инициализация
	 */
	@BeforeEach
	public void init(){
		if(initialized) return;

		buyerClient = new ApiV2Client(buyerEmail, buyerPassword);
		sellerClient = new ApiV2Client(sellerEmail, sellerPassword);

		cartTestSupport.setUserId(buyerId);
		cartTestSupport.setApiV2Client(buyerClient);

		catalogTestSupport.setApiV2Client(buyerClient);

		cleanup();

		initialized = true;
	}

	/**
	 * Проверка форматирования цены в сервисе
	 */
	@Test
	public void _00_01_formatPrice_service() {
		assertEquals("10 000 ₽", bargainService.formatPrice(10000, null));
		assertEquals("10 000 000 ₽", bargainService.formatPrice(10000000, null));
		assertEquals("100 ₽", bargainService.formatPrice(100, null));
	}


	/**
	 * Без авторизации настройки недоступены
	 */
	@Test
	public void _01_01_getSettings_unauthorised_failed() {
		ResponseEntity<String> responseEntity = buyerClient.request(getBargainSettinsUrl(), null, HttpMethod.GET, null, String.class, false);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		TestUtils.assertStringContains(responseEntity.getBody(), "Доступ запрещен");
	}

	/**
	 * С авторизацией настройки доступны
	 */
	@Test
	public void _01_02_01_getSettings_authorised_OK() {
		ResponseEntity<Api2Response<BargainSettingsDTO>> responseEntity = buyerClient.request(getBargainSettinsUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<BargainSettingsDTO>>() {}, true);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody());
		BargainSettingsDTO settings = responseEntity.getBody().getData();
		assertNotNull(settings);
		assertNotNull(settings.getWelcomePage());
		assertNotNull(settings.getWelcomePage().getTitle());
		assertFalse(settings.getWelcomePage().getItems().isEmpty());
		//...
		assertNotNull(settings.getConstants());
		//...
	}

	/**
	 * Если пользователь забанен, то ему сервис недоступен
	 */
	@Transactional
	@Test
	public void _01_02_02_getSettings_banned_failed() {
		commitTransaction();

		//Банним покупателя
		long userBanId = userBanService.saveUserBan(buyerId, BanType.BARGAIN_BAN, ZonedDateTime.now().plusDays(30),
		"Нечего торговаться!", userService.getUserById(sellerId).get());

		//На создание уведомлений уходит время
		TestUtils.sleep(3);

		startNewTransaction();

		//Покупателю пришло уведомление о банне
		assertNotificationCreated(getBuyer(), BargainBanNotification.class, false, false, "Нечего торговаться!");

		commitTransaction();

		ResponseEntity<String> responseEntity = buyerClient.request(getBargainSettinsUrl(), null, HttpMethod.GET, null, String.class, false);
		assertEquals(HttpStatus.FORBIDDEN, responseEntity.getStatusCode());
		TestUtils.assertStringContains(responseEntity.getBody(), BargainBanException.class.getName());
		TestUtils.assertStringContains(responseEntity.getBody(), "Вы нарушили правила сервиса и больше не можете пользоваться сервисом торгов");

		//Разбанним покупателя
		userBanService.deleteUserBan(userBanId, sellerId);

		//На создание уведомлений уходит время
		TestUtils.sleep(3);

		startNewTransaction();

		//Покупателю пришло уведомление о разбанне
		assertNotificationCreated(getBuyer(), CancelBargainBanNotification.class, false, false, "Не нарушайте правила сервиса и пользуйтесь всеми возможностями нашей платформы");

		commitAndStartNewTransaction();

		//Сервис вновь доступен
		ResponseEntity<Api2Response<BargainSettingsDTO>> responseEntityOK = buyerClient.request(getBargainSettinsUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<BargainSettingsDTO>>() {}, true);
		assertTrue(responseEntityOK.getStatusCode().is2xxSuccessful());
	}

	/**
	 * Покупатель получает шаблон для торга
	 */
	@Transactional
	@Test
	public void _01_03_checkBargainFailed_getTemplate_buyer_authorised_OK() {
		//В случае, если отвалился предыдущий тест с банном, чистим баны
		deleteUserBans(buyerId);
		commitAndStartNewTransaction();

		Product product = getAnyPublishedProduct();
		Size size = product.getAvailableProductItems().get(0).getSize();

		//Пробуем проверить существование такого контрторга
		ResponseEntity<String> responseEntity = buyerClient.request(getBargainCheckUrl(product.getId(), size.getId()), null, HttpMethod.GET, null, new ParameterizedTypeReference<String>() {}, true);
		//Получаем 404 и соотв текст ошибки
		assertEquals(HttpStatus.NOT_FOUND, responseEntity.getStatusCode());

		//Получаем шаблон для контрторга
		ResponseEntity<Api2Response<BargainDetailedDTO>> responseEntityBuyerTempl = buyerClient.request(getBargainOrTemplateUrl(product.getId(), size.getId()), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<BargainDetailedDTO>>() {}, true);
		assertTrue(responseEntityBuyerTempl.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntityBuyerTempl.getBody());

		BargainDetailedDTO bargainBuyerTempl = responseEntityBuyerTempl.getBody().getData();
		assertNotNull(bargainBuyerTempl);
		assertSame(BargainStateDTO.Enum.INITIAL, bargainBuyerTempl.getState().getName());

		//Проверка прав покупателя после встречного торга
		assertUserRightsCorrect(bargainBuyerTempl, BargainRecordTypeDTO.Enum.OFFER);

		//Проверка кол-ва оставшихся попыток
		assertEquals(3, bargainBuyerTempl.getAttemptsLeft().intValue());

		//Проверка сообщения последней записи
		checkLastRecordBargainMessage(bargainBuyerTempl, "Привет, какую цену хотите предложить? \uD83D\uDE00");

		assertEquals(1, bargainBuyerTempl.getRecords().size());
		BargainRecordDTO record0 = bargainBuyerTempl.getRecords().get(0);
		assertSame(BargainRecordFromTypeDTO.Enum.SELLER, record0.getFrom().getName());
		assertNull(record0.getPrice());
	}

	/**
	 * Продавец не может получить шаблон торга по своему товару
	 */
	@Transactional
	@Test
	public void _01_04_checkTemplate_seller_authorised_failed() {
		Product product = getAnyPublishedProduct();
		Size size = product.getAvailableProductItems().get(0).getSize();
		//Подменяем продавца для товара на своего, сохраняя оригинал для восстановления
		setProductSellerAndSaveOriginal(product, sellerId);
		commitAndStartNewTransaction();

		getDetailedBargainFailed(sellerClient, getBargainOrTemplateUrl(product.getId(), size.getId()), "Нельзя торговаться по своему товару");
	}

	/**
	 * Попытка создания контрторга по неопубликованному товару
	 */
	@Transactional
	@Test
	public void _02_01_createBargainSoldProduct_authorized_failed() {
		//Берем любой проданный товар
		Product product = getAnyProduct(ProductState.SOLD, 0);
		Size size = product.getProductItems().get(0).getSize();
		int offeredPrice = product.getCurrentPrice().multiply(new BigDecimal("0.8")).intValue();
		createBargainFailed(product.getId(), size.getId(), offeredPrice, "Товар недоступен для торга");
	}

	/**
	 * Попытка создания контрторга по несуществующему товару
	 */
	@Transactional
	@Test
	public void _02_02_createBargainUnexistedProduct_authorized_failed() {
		createBargainFailed(-1L, 1L, 6000, "Товар не найден");
	}

	/**
	 * Попытка создания контрторга с очень низкой ценой
	 */
	@Transactional
	@Test
	public void _02_03_01_createBargainSmallPrice_authorized_failed() {
		Product p = getAnyPublishedProduct();
		createBargainFailed(p.getId(), p.getAvailableProductItems().get(0).getSize().getId(), 500, "Минимальная цена по контрторгам");
	}

	/**
	 * Продавец пытается создать контрторг со слишком высокой ценой
	 */
	@Transactional
	@Test
	public void _02_03_02_createBargainHighPrice_buyer_failed() {
		Product p = getAnyPublishedProduct();
		createBargainFailed(p.getId(), p.getAvailableProductItems().get(0).getSize().getId(), p.getCurrentPrice().multiply(new BigDecimal("1.2")).intValue(), "Цена не может превышать стоимость товара");
	}

	/**
	 * Попытка создания контрторга с пустой ценой
	 */
	@Transactional
	@Test
	public void _02_04_01_createBargainNoPrice_authorized_failed() {
		createBargainFailed(-1L, 1L, null, "Required Integer parameter 'price' is not present");
	}

	/**
	 * Попытка создания контрторга с пустым параметром productId
	 */
	@Transactional
	@Test
	public void _02_04_02_createBargainNoProductId_authorized_failed() {
		createBargainFailed(null, 1L, (int) (initialProductPrice * 0.81), "Required Long parameter 'productId' is not present");
	}

	/**
	 * Попытка создания контрторга с пустым параметром sizeId
	 */
	@Transactional
	@Test
	public void _02_04_03_createBargainNoSizeId_authorized_failed() {
		createBargainFailed(2L, null, (int) (initialProductPrice * 0.81), "Required Long parameter 'sizeId' is not present");
	}

	/**
	 * Попытка создания контрторга по опубликованному товару с некорректным размером
	 */
	@Transactional
	@Test
	public void _02_05_createBargainPublishedProductWrongSize_authorized_failed() {
		//Берем любой опубликованный товар
		Product product = getAnyPublishedProduct();
		long sizeId;
		//Ищем размер, который не представлен в данном товаре
		for(sizeId = 1; product.getCountBySize(sizeId) > 0; sizeId++){ }
		int offeredPrice = product.getCurrentPrice().multiply(new BigDecimal("0.8")).intValue();
		createBargainFailed(product.getId(), sizeId, offeredPrice, "Размер недоступен для покупки");
	}

	/**
	 * Попытка создания контрторга по недавно опубликованному товару
	 */
	@Transactional
	@Test
	public void _02_06_createBargainPublishedProductWrongPublishTime_authorized_failed() {
		//Берем любой опубликованный товар
		Product product = getAnyPublishedProduct();
		//Размер
		Size size = product.getAvailableProductItems().get(0).getSize();

		//Перед тем, как мы сдвинем дату публикации товара, контроллер каталога выдает товар с флагом isReadyForBargain=true
		ProductDTO productDTO = catalogTestSupport.getProductSuccessful(product.getId(), false);
		assertTrue(productDTO.getIsReadyForBargain());
		//Проверяем дату открытия товара для торгов
		assertEquals(Utils.localDateTimeToZonedDateTime(product.getPublishTime().plusDays(7)).toEpochSecond(), productDTO.getAvailabilityForBargainDate().toEpochSecond());

		//Сохраняем старую дату публикации, чтобы потом вернуть
		LocalDateTime oldPublishTime = product.getPublishTime();

		//Освежаем дату публикации товара неа вчерашнюю
		product.setPublishTime(LocalDateTime.now().minusHours(24L));
		productRepository.save(product);
		commitAndStartNewTransaction();

		//Теперь, после сдвига даты публикации контроллер каталога выдает товар с флагом isReadyForBargain=false
		productDTO = catalogTestSupport.getProductSuccessful(product.getId(), false);
		assertFalse(productDTO.getIsReadyForBargain());

		int offeredPrice = product.getCurrentPrice().multiply(new BigDecimal("0.8")).intValue();
		createBargainFailed(product.getId(), size.getId(), offeredPrice, "Для создания торга должно пройти 7 дней с момента публикации товара");

		//Возвращаем дату публикации товара
		product = productRepository.getOne(product.getId());
		product.setPublishTime(oldPublishTime);
		productRepository.save(product);
		commitAndStartNewTransaction();
	}


	/**
	 * Попытка создания контрторга по товару в бутике
	 */
	@Transactional
	@Test
	public void _02_07_createBargainInBoutique_failed() {
		//Берем любой опубликованный товар и у одной из позиций проставляем метку В бутике
		Product product = getAnyPublishedProduct();
		ProductItem productItem = product.getAvailableProductItems().get(0);
		productItem = productItemService.setProductItemInBoutique(productItem, LocalDateTime.now());
		commitAndStartNewTransaction();
		int offeredPrice = product.getCurrentPrice().multiply(new BigDecimal("0.8")).intValue();
		createBargainFailed(product.getId(), productItem.getSize().getId(), offeredPrice, "Товар находится в бутике, поэтому недоступен для торга");

		//Возвращаем метку обратно
		productItem = productItemRepository.findById(productItem.getId()).get();
		productItem = productItemService.setProductItemInBoutique(productItem, null);
		commitAndStartNewTransaction();
	}

	/**
	 * Удачное создание торга
	 */
	@Transactional
	@Test
	public void _03_09_01_createBargain_OK() {
		createBargainWithChecks_OK(0);
	}

	/**
	 * Еще одно удачное создание торга
	 */
	@Transactional
	@Test
	public void _03_09_02_createBargain_OK() {
		createBargainWithChecks_OK(1);
	}

	/**
	 * Увеличение цены товара (если выше базовой цены по торгу) закрывает торг в стейте OFFER
	 * Работаем со вторым контрторгом!
	 */
	@Transactional
	@Test
	public void _03_09_03_highPriceBargainCloseTask_COUNTER_OFFER_OK() {
		//Делаем товар дороже на 1 рубль
		setProductPrice(productId2, initialProductPrice + 1);

		//Запускаем таск закрытия контрторгов
		scheduledBargainRunner.closeBargainsWithWrongPriceProducts(100);

		commitAndStartNewTransaction();

		//Берем контрторг из базы
		Bargain bargain = bargainRepository.findById(bargainId2).orElse(null);

		//Проверяем статус контрторга в базе
		assertSame(BargainState.CANCELLED, bargain.getState());

		//Проверка прав покупателя для торга в статусе EXPIRED (может предложить торг повторно)
		assertUserRightsCorrect(buyerClient, bargainId2, BargainRecordTypeDTO.Enum.OFFER);

		//Проверка прав продавца для торга в статусе CANCELLED (ничего не может)
		assertUserRightsCorrect(sellerClient, bargainId2, null);
	}

	/**
	 * Выдача страницы каталога с нашим товаром содержит информацию о нашем контрторге
	 */
	@Test
	public void _03_10_01_catalogListContainsBargainInfo(){
		//Запрашиваем страницу товаров (в которой должен быть только один наш товар)
		Page<ProductDTO> productsPage = catalogTestSupport.getProductsPageSuccessful(TestUtils.getOneParamAsMap("productsIds", productId), false);
		//В результате только один товар
		assertEquals(1, productsPage.getItems().size());

		ProductDTO productDTO = productsPage.getItems().get(0);
		//По которому мы торговались
		assertEquals(productId, productDTO.getProductId());

		//DTO товара содержит информацию о контрторге
		assertProductDTOContainsBargain(productDTO, sizeId, OfferStatus.PENDING, lastBargainPrice, false);
	}

	/**
	 * Выдача развернутого товара в каталоге содержит информацию о нашем контрторге
	 */
	@Test
	public void _03_10_02_catalogProductContainsBargainInfo(){
		//Товар в каталоге содержит этот торг
		assertCatalogProductContainsBargainInfo(OfferStatus.PENDING, false);
	}

	/**
	 * Попытка создания еще одного контрторга по тому же товару/размеру тем же покупателем
	 */
	@Transactional
	@Test
	public void _04_01_create2ndBargain_buyer_failed() {
		createBargainFailed(productId, sizeId, (int) (initialProductPrice * 0.81), "Вы исчерпали возможности создать торг по данному товару/размеру");
	}

	/**
	 * Попытка отправки еще одного предложения покупателем по существующему контрторгу (не дожидаясь ответа продавца)
	 */
	@Transactional
	@Test
	public void _04_02_01_sendAnotherOffer_buyer_failed() {
		sendAnotherOffer_buyer_failed();
	}

	/**
	 * Проверяется запрет на добавление других записей покупателем (подтверждение, отклонение, встречное предложение, отметка о покупке, создание)
	 */
	@Transactional
	@Test
	public void _04_02_02_sendWrongRecord_buyer_failed() {
		addWrongRecordAfterBuyersRecord_buyer_failed();
	}

	/**
	 * Продавец пытается отправить "создающую" запись и факт покупки к торгу
	 */
	@Transactional
	@Test
	public void _04_03_01_addCreationRecord_seller_failed() {
		sendOfferFailed(sellerClient, bargainId, BargainRecordTypeDTO.Enum.CREATION, (int) (initialProductPrice * 0.81), "У вас нет прав на выполнение данного действия");
	}

	/**
	 * Продавец пытается отправить встречное предложение без указания цены
	 */
	@Transactional
	@Test
	public void _04_03_02_01_addCounterOfferNoPrice_seller_failed() {
		sendOfferFailed(sellerClient, bargainId, BargainRecordTypeDTO.Enum.OFFER, null, "Параметр цены обязателен для данного действия");
	}

	/**
	 * Продавец пытается отправить встречное предложение со слишком высокой ценой (дороже стоимости товара)
	 */
	@Transactional
	@Test
	public void _04_03_02_02_addCounterOfferHighPrice_seller_failed() {
		sendOfferFailed(sellerClient, bargainId, BargainRecordTypeDTO.Enum.OFFER, initialProductPrice * 2, "Цена не может превышать стоимость товара");
	}

	//Проверки цен продавца и покупателя относительно друг друга исключены
	//Продавец пытается отправить встречное предложение со слишком низкой ценой (ниже предложения покупателя). Должно быть ошибся.
	//@Transactional
	//@Test
	//public void _04_03_02_03_addCounterOfferLowPrice_seller_failed() {
	//	sendOfferFailed(sellerClient, bargainId, BargainRecordTypeDTO.Enum.OFFER, (int) (initialProductPrice * 0.75), "Слишком низкая цена");
	//}

	/**
	 * Продавец пытается отклонить торг с указанием цены (которая не нужна)
	 */
	@Transactional
	@Test
	public void _04_03_03_declineBargainWithPrice_seller_failed() {
		sendOfferFailed(sellerClient, bargainId, BargainRecordTypeDTO.Enum.DECLINE, (int) (initialProductPrice * 0.71), "Параметр цены недопустим для данного действия");
	}

	/**
	 * Продавец пытается подтвердить торг с указанием цены (которая не нужна)
	 */
	@Transactional
	@Test
	public void _04_03_04_acceptBargainWithPrice_seller_failed() {
		sendOfferFailed(sellerClient, bargainId, BargainRecordTypeDTO.Enum.ACCEPT, (int) (initialProductPrice * 0.71), "Параметр цены недопустим для данного действия");
	}

	/**
	 * Продавец пытается предложить в ответ слишком высокую (удвоенную) цену
	 */
	@Transactional
	@Test
	public void _04_03_05_addCounterOfferHighPrice_seller_failed() {
		sendOfferFailed(sellerClient, bargainId, BargainRecordTypeDTO.Enum.OFFER, initialBargainOfferedPrice * 2, "Цена не может превышать стоимость товара");
	}

	/**
	 * Продавец успешно добавляет встречный торг
	 */
	@Transactional
	@Test
	public void _04_04_addCounterOffer_seller_OK() {
		int counterOfferPrice = (int) (initialBargainOfferedPrice * 1.1);
		BargainDetailedDTO bargain = addBargainRecordOK(sellerClient, bargainId, BargainRecordTypeDTO.Enum.OFFER, counterOfferPrice);
		assertEquals(counterOfferPrice, bargain.getLastPrice().intValue());
		assertEquals(BargainStateDTO.Enum.COUNTER_OFFER, bargain.getState().getName());

		//Проверка наличия сумма и сообщения о сумме для продавца в последней записи
		checkLastRecordSellerReceivesSumAndMessage(bargain, true);

		//Проверка кол-ва оставшихся попыток
		assertEquals(2, bargain.getAttemptsLeft().intValue());

		//Проверка сообщения для продавца
		assertNotNull(bargain.getMessage());
		TestUtils.assertStringContains(bargain.getMessage().getMessage(), "У покупателя есть {timeLeft}, чтобы купить товар по новой цене");

		//Проверка сообщения для покупателя
		BargainDetailedDTO bargainForBuyer = getDetailedBargainOK(buyerClient, getBargainDetailedUrl(bargain.getId()));
		assertNotNull(bargainForBuyer.getMessage());
		TestUtils.assertStringContains(bargainForBuyer.getMessage().getMessage(), "Цена действительна: {timeLeft}");

		//Проверка наличия сумма и сообщения о сумме для продавца в последней записи
		checkLastRecordSellerReceivesSumAndMessage(bargainForBuyer, false);

		//Проверка прав продавца после встречного торга (ничего не может)
		assertUserRightsCorrect(sellerClient, bargainId, null);

		//Проверка прав покупателя после встречного торга
		assertUserRightsCorrect(buyerClient, bargainId, BargainRecordTypeDTO.Enum.OFFER, BargainRecordTypeDTO.Enum.ACCEPT);

		//Товар в каталоге содержит этот торг
		assertCatalogProductContainsBargainInfo(OfferStatus.ACCEPTED, true);

		//Покупателю пришло уведомление о контр предложении
		assertNotificationCreatedAndLinkedToBargain(getBuyer(), BargainBuyerCounterOfferNotification.class, bargain.getId(), true, false, true);

	}

	/**
	 * Покупатель не успевает ответить на предложение и контрторг закрывается при получении развернутого объекта покупателем
	 */
	@Transactional
	@Test
	public void _04_05_01_expiredBargain_buyer_OK() {
		//Старим контрторг на 30 часов (дата обновления), устанавливаем активный стейт
		setBargainState(bargainId, BargainState.COUNTER_OFFER, LocalDateTime.now().minusHours(30));

		//Проверяем что для продавца торг закрывается при запросе единичного торга продавцом
		BargainDetailedDTO bargainDetailedForSeller = getDetailedBargainOK(buyerClient, getBargainDetailedUrl(bargainId));
		assertEquals(BargainStateDTO.Enum.EXPIRED, bargainDetailedForSeller.getState().getName());
		//Торг содержит сообщение
		assertNotNull(bargainDetailedForSeller.getMessage());
		//Сообщение негативное
		assertSame(BargainDecorationDTO.NEGATIVE, bargainDetailedForSeller.getMessage().getDecoration());
		//Заголовок и текст
		TestUtils.assertStringContains(bargainDetailedForSeller.getMessage().getTitle(), "Срок действия предложения истек");
		TestUtils.assertStringContains(bargainDetailedForSeller.getMessage().getMessage(), "В следующий раз быстрее принимайте предложение продавца");

		//Убедимся, что в базе та же сиуация (state=EXPIRED)
		commitAndStartNewTransaction();
		Bargain bargain = bargainRepository.findById(bargainId).orElse(null);
		assertEquals(BargainState.EXPIRED, bargain.getState());

		//Проверка прав покупателя для тогра в статусе EXPIRED (может предложить торг повторно)
		assertUserRightsCorrect(buyerClient, bargainId, BargainRecordTypeDTO.Enum.OFFER);

		//Проверка прав продавца для торга в статусе EXPIRED (ничего не может)
		assertUserRightsCorrect(sellerClient, bargainId, null);

		//Товар в каталоге содержит этот торг
		assertCatalogProductContainsBargainInfo(OfferStatus.PENDING, false);

		//Убеждаемся, что уведомление более не требует действия (действие выполнено)
		assertNotificationCreatedAndLinkedToBargain(getBuyer(), BargainBuyerCounterOfferNotification.class, bargain.getId(), true, true, true);

		//Покупателю пришло уведомление о просроченном им (покупателем) торге
//		assertNotificationCreatedAndLinkedToBargain(getBuyer(), BargainBuyerExpiredByBuyerNotification.class, bargain.getId(), false, false, false);

		//Возвращаем активный стейт контрторгу
		setBargainState(bargainId, BargainState.COUNTER_OFFER, null);
	}

	/**
	 * Покупатель не успевает ответить на предложение и контрторг закрывается при получении списка торгов покупателем
	 */
	@Transactional
	@Test
	public void _04_05_02_expiredBargainList_buyer_OK() {
		//Чистим все уведомления для покупателя
		TestUtils.deleteUserNotifications(buyerId, notificationRepository);

		//Старим контрторг на 30 часов (дата обновления), устанавливаем активный стейт
		setBargainState(bargainId, BargainState.COUNTER_OFFER, LocalDateTime.now().minusHours(30));

		//Проверяем что для продавца торг закрывается при запросе списка торгов
		List<BargainLiteDTO> finishedBargains = getBargainPageOK(buyerClient, getOutgoingFinishedBargainsUrl(), null, null).getItems();
		List<BargainLiteDTO> activeBargains = getBargainPageOK(buyerClient, getOutgoingActiveBargainsUrl(), null, null).getItems();

		List<Long> finishedBargainIds = getBargainIds(finishedBargains);
		List<Long> activeBargainIds = getBargainIds(activeBargains);

		//Проверяем наличие торга в списках
		TestUtils.assertListContains(finishedBargainIds, bargainId);
		TestUtils.assertListDoesntContain(activeBargainIds, bargainId);

		//Покупателю пришло уведомление о просроченном им (покупателем) торге
//		assertNotificationCreatedAndLinkedToBargain(getBuyer(), BargainBuyerExpiredByBuyerNotification.class, bargainId, false, false, false);

		//Возвращаем активный стейт контрторгу
		setBargainState(bargainId, BargainState.COUNTER_OFFER, null);
	}

	/**
	 * Покупатель не успевает ответить на предложение и контрторг закрывается при получении развернутого объекта продавцом
	 */
	@Transactional
	@Test
	public void _04_05_03_expiredBargain_seller_OK() {
		//Старим контрторг на 30 часов (дата обновления), устанавливаем активный стейт
		setBargainState(bargainId, BargainState.COUNTER_OFFER, LocalDateTime.now().minusHours(30));

		//Проверяем что для продавца торг закрывается при запросе единичного торга продавцом
		BargainDetailedDTO bargainDetailedForSeller = getDetailedBargainOK(sellerClient, getBargainDetailedUrl(bargainId));
		assertEquals(BargainStateDTO.Enum.EXPIRED, bargainDetailedForSeller.getState().getName());
		//Торг содержит сообщение
		assertNotNull(bargainDetailedForSeller.getMessage());
		//Сообщение негативное
		//assertSame(BargainDecorationDTO.NEGATIVE, bargainDetailedForSeller.getMessage().getDecoration());
		//Заголовок и текст
		TestUtils.assertStringContains(bargainDetailedForSeller.getMessage().getMessage(), "К сожалению, покупатель не успел купить товар по новой цене");

		//Убедимся, что в базе та же сиуация (state=EXPIRED)
		commitAndStartNewTransaction();
		Bargain bargain = bargainRepository.findById(bargainId).orElse(null);
		assertEquals(BargainState.EXPIRED, bargain.getState());

		//Проверка прав покупателя для тогра в статусе EXPIRED (может предложить торг повторно)
		assertUserRightsCorrect(buyerClient, bargainId, BargainRecordTypeDTO.Enum.OFFER);

		//Проверка прав продавца для тогра в статусе EXPIRED (ничего не может)
		assertUserRightsCorrect(sellerClient, bargainId, null);

		//Возвращаем активный стейт контрторгу
		setBargainState(bargainId, BargainState.COUNTER_OFFER, null);
	}

	/**
	 * Покупатель не успевает ответить на предложение и контрторг закрывается при получении списка торгов продавцом
	 */
	@Transactional
	@Test
	public void _04_05_04_expiredBargainList_seller_OK() {
		//Старим контрторг на 30 часов (дата обновления), устанавливаем активный стейт
		setBargainState(bargainId, BargainState.COUNTER_OFFER, LocalDateTime.now().minusHours(30));

		//Проверяем что для продавца торг закрывается при запросе списка торгов
		List<BargainLiteDTO> finishedBargains = getBargainPageOK(sellerClient, getIncomingFinishedBargainsUrl(), null, null).getItems();
		List<BargainLiteDTO> activeBargains = getBargainPageOK(sellerClient, getIncomingActiveBargainsUrl(), null, null).getItems();

		List<Long> finishedBargainIds = getBargainIds(finishedBargains);
		List<Long> activeBargainIds = getBargainIds(activeBargains);

		//Проверяем наличие торга в списках
		TestUtils.assertListContains(finishedBargainIds, bargainId);
		TestUtils.assertListDoesntContain(activeBargainIds, bargainId);

		//Возвращаем активный стейт контрторгу
		setBargainState(bargainId, BargainState.COUNTER_OFFER, null);
	}

	/**
	 * Покупатель не успевает ответить на предложение и контрторг закрывается таском
	 */
	@Transactional
	@Test
	public void _04_05_05_01_expiredBargainCloseTask_OK() {
		//Старим контрторг на 30 часов (дата обновления), устанавливаем активный стейт
		setBargainState(bargainId, BargainState.COUNTER_OFFER, LocalDateTime.now().minusHours(30));

		//Запускаем таск закрытия контрторгов
		scheduledBargainRunner.closeExpiredBargains(100);

		//Берем контрторг из базы
		Bargain bargain = bargainRepository.findById(bargainId).orElse(null);

		//Проверяем статус контрторга в базе
		assertSame(BargainState.EXPIRED, bargain.getState());

		//Покупателю пришло уведомление о просроченном им (покупателем) торге
//		assertNotificationCreatedAndLinkedToBargain(getBuyer(), BargainBuyerExpiredByBuyerNotification.class, bargain.getId(), false, false, false);

		//Возвращаем активный стейт контрторгу
		setBargainState(bargainId, BargainState.COUNTER_OFFER, null);
	}

	//Старый тест. Сейчас любое изменение цены приводит к закрытию торга.
	/**
	 * Незначенильное уменьшение цены товара (если не ниже последнего предложения по торгу) не закрывает торг
	 */
	/*@Transactional
	@Test
	public void _04_05_05_02_littleLowPriceBargainCloseTask_OK() {
		//Возвращаем активный стейт контрторгу
		setBargainState(bargainId, BargainState.COUNTER_OFFER, null);

		//Делаем товар дешевле на 2 рубля
		setProductPrice(productId, initialProductPrice - 2);

		//Запускаем таск закрытия контрторгов
		scheduledBargainRunner.closeBargainsWithWrongPriceProducts(100);

		//Берем контрторг из базы
		Bargain bargain = bargainRepository.findById(bargainId).orElse(null);

		//Проверяем статус контрторга в базе
		assertSame(BargainState.COUNTER_OFFER, bargain.getState());
	}*/

	/**
	 * Значенильное уменьшение цены товара (если ниже последнего предложения по торгу) закрывает торг
	 */
	@Transactional
	@Test
	public void _04_05_05_03_01_tooLowPriceBargainCloseTask_OK() {
		//Возвращаем активный стейт контрторгу
		setBargainState(bargainId, BargainState.COUNTER_OFFER, null);

		//Делаем товар дешевле в 2 раза
		setProductPrice(productId, initialProductPrice / 2);

		//Запускаем таск закрытия контрторгов
		scheduledBargainRunner.closeBargainsWithWrongPriceProducts(100);

		//Берем контрторг из базы
		Bargain bargain = bargainRepository.findById(bargainId).orElse(null);

		//Проверяем статус контрторга в базе
		assertSame(BargainState.CANCELLED, bargain.getState());

		//Проверка прав покупателя для торга в статусе CANCELLED (может предложить торг повторно)
		assertUserRightsCorrect(buyerClient, bargainId, BargainRecordTypeDTO.Enum.OFFER);

		//Проверка прав продавца для торга в статусе CANCELLED (ничего не может)
		assertUserRightsCorrect(sellerClient, bargainId, null);

		//Проверяем что для покупателя в торге выдается соотв. сообщение
		BargainDetailedDTO bargainDetailedForBuyer = getDetailedBargainOK(buyerClient, getBargainDetailedUrl(bargainId));
		assertEquals(BargainStateDTO.Enum.CANCELLED, bargainDetailedForBuyer.getState().getName());
		//Торг содержит сообщение
		assertNotNull(bargainDetailedForBuyer.getMessage());
		//Сообщение негативное
		assertSame(BargainDecorationDTO.NEGATIVE, bargainDetailedForBuyer.getMessage().getDecoration());
		//Заголовок и текст
		TestUtils.assertStringContains(bargainDetailedForBuyer.getMessage().getTitle(), "Цена товара изменилась");
		TestUtils.assertStringContains(bargainDetailedForBuyer.getMessage().getMessage(), "Продавец поменял цену товара, поэтому предложение торга было отменено");

		//Проверяем что для продавца в торге выдается соотв. сообщение
		BargainDetailedDTO bargainDetailedForSeller = getDetailedBargainOK(sellerClient, getBargainDetailedUrl(bargainId));
		assertEquals(BargainStateDTO.Enum.CANCELLED, bargainDetailedForSeller.getState().getName());
		//Торг содержит сообщение
		assertNotNull(bargainDetailedForSeller.getMessage());
		//Заголовок и текст
		TestUtils.assertStringContains(bargainDetailedForSeller.getMessage().getMessage(), "Вы поменяли цену товара, поэтому предложение торга было отменено");
	}

	//Цена на товар нормализовалась. Контрторг возвращается в активный стейт с использованием аудита
	@Transactional
	@Test
	public void _04_05_05_03_02_productNormalPriceBargainOpenTask_COUNTER_OFFER_aud_OK() {
		//Цена на товар поднялась до преждего уровня
		jdbcTemplate.execute("UPDATE product set current_price = " + initialProductPrice + " WHERE id = " + productId);
		commitAndStartNewTransaction();

		//Запускаем таск открытия контрторгов
		scheduledBargainRunner.openBargainsWithCorrectPriceProducts(100);

		//Коммитимся, чтобы изменения были доступны для АПИ
		commitAndStartNewTransaction();

		//Берем контрторг из базы
		Bargain bargain = bargainRepository.findById(bargainId).orElse(null);

		//Проверяем статус контрторга в базе
		assertSame(BargainState.COUNTER_OFFER, bargain.getState());
	}

	/**
	 * Любое увеличение цены товара (если выше базовой цены по торгу) закрывает торг в стейте COUNTER_OFFER
	 */
	@Transactional
	@Test
	public void _04_05_05_04_01_highPriceBargainCloseTask_COUNTER_OFFER_OK() {
		//Возвращаем активный стейт контрторгу
		setBargainState(bargainId, BargainState.COUNTER_OFFER, null);

		//Делаем товар дороже на 1 рубль
		setProductPrice(productId, initialProductPrice + 1);

		//Запускаем таск закрытия контрторгов
		scheduledBargainRunner.closeBargainsWithWrongPriceProducts(100);

		commitAndStartNewTransaction();

		//Берем контрторг из базы
		Bargain bargain = bargainRepository.findById(bargainId).orElse(null);

		//Проверяем статус контрторга в базе
		assertSame(BargainState.CANCELLED, bargain.getState());

		//Проверка прав покупателя для торга в статусе EXPIRED (может предложить торг повторно)
		assertUserRightsCorrect(buyerClient, bargainId, BargainRecordTypeDTO.Enum.OFFER);

		//Проверка прав продавца для торга в статусе CANCELLED (ничего не может)
		assertUserRightsCorrect(sellerClient, bargainId, null);
	}

	//Цена на товар нормализовалась. Контрторг возвращается в активный стейт с использованием аудита
	@Transactional
	@Test
	public void _04_05_05_04_02_productNormalPriceBargainOpenTask_COUNTER_OFFER_aud_OK() {
		//Стейт контрторга восстанавлиается, как в одном из предыдущих тестов
		_04_05_05_03_02_productNormalPriceBargainOpenTask_COUNTER_OFFER_aud_OK();
	}


	//Товар более недоступен. Торг закрывается при получении развернутого торга.
	@Transactional
	@Test
	public void _04_05_06_01_soldProductDetailedBargain_buyer_OK() {
		//Чистим все уведомления для покупателя
		TestUtils.deleteUserNotifications(buyerId, notificationRepository);

		//Торг в статусе контрпредложения от продавца
		setBargainState(bargainId, BargainState.COUNTER_OFFER, LocalDateTime.now());

		//Товар недоступен
		TestUtils.setProductState(productId, ProductState.HIDDEN, productRepository);
		commitAndStartNewTransaction();

		//Проверяем что для покупателя торг закрывается при запросе единичного торга покупателем
		BargainDetailedDTO bargainDetailedForBuyer = getDetailedBargainOK(buyerClient, getBargainDetailedUrl(bargainId));
		assertEquals(BargainStateDTO.Enum.SOLD, bargainDetailedForBuyer.getState().getName());
		//Торг содержит сообщение
		assertNotNull(bargainDetailedForBuyer.getMessage());
		//Сообщение негативное
		assertSame(BargainDecorationDTO.NEGATIVE, bargainDetailedForBuyer.getMessage().getDecoration());
		//Заголовок и текст
		TestUtils.assertStringContains(bargainDetailedForBuyer.getMessage().getTitle(), "Товар уже купил кто-то другой");
		TestUtils.assertStringContains(bargainDetailedForBuyer.getMessage().getMessage(), "В следующий раз покупайте быстрее");

		//Убедимся, что в базе та же сиуация (state=SOLD)
		commitAndStartNewTransaction();
		Bargain bargain = bargainRepository.findById(bargainId).orElse(null);
		assertEquals(BargainState.SOLD, bargain.getState());

		//Проверка прав покупателя для торга в статусе SOLD (ничего не может)
		assertUserRightsCorrect(buyerClient, bargainId, null);

		//Проверка прав продавца для торга в статусе SOLD (ничего не может)
		assertUserRightsCorrect(sellerClient, bargainId, null);

		//Товар в каталоге содержит этот торг
		assertCatalogProductContainsBargainInfo(OfferStatus.PENDING, false);

		//Покупателю пришло уведомление о том, что товар продан
		assertNotificationCreatedAndLinkedToBargain(getBuyer(), BargainBuyerProductSoldNotification.class, bargain.getId(), false, false, false);

		//Возвращаем активный стейт товару
		TestUtils.setProductState(productId, ProductState.PUBLISHED, productRepository);

		//Возвращаем активный стейт контрторгу
		setBargainState(bargainId, BargainState.COUNTER_OFFER, null);
	}

	//Товар более недоступен. Торг закрывается при получении списка.
	@Transactional
	@Test
	public void _04_05_06_02_soldProductBargainList_buyer_OK() {
		//Чистим все уведомления для покупателя
		TestUtils.deleteUserNotifications(buyerId, notificationRepository);

		//Торг в статусе контрпредложения от продавца
		setBargainState(bargainId, BargainState.COUNTER_OFFER, LocalDateTime.now());

		//Товар недоступен
		TestUtils.setProductState(productId, ProductState.HIDDEN, productRepository);
		commitAndStartNewTransaction();

		//Проверяем что для продавца торг закрывается при запросе списка торгов
		List<BargainLiteDTO> finishedBargains = getBargainPageOK(buyerClient, getOutgoingFinishedBargainsUrl(), null, null).getItems();
		List<BargainLiteDTO> activeBargains = getBargainPageOK(buyerClient, getOutgoingActiveBargainsUrl(), null, null).getItems();

		List<Long> finishedBargainIds = getBargainIds(finishedBargains);
		List<Long> activeBargainIds = getBargainIds(activeBargains);

		//Проверяем наличие торга в списках
		TestUtils.assertListContains(finishedBargainIds, bargainId);
		TestUtils.assertListDoesntContain(activeBargainIds, bargainId);

		//Покупателю пришло уведомление о том, что товар продан
		assertNotificationCreatedAndLinkedToBargain(getBuyer(), BargainBuyerProductSoldNotification.class, bargainId, false, false, false);

		//Возвращаем активный стейт товару
		TestUtils.setProductState(productId, ProductState.PUBLISHED, productRepository);

		//Возвращаем активный стейт контрторгу
		setBargainState(bargainId, BargainState.COUNTER_OFFER, null);
	}

	//Товар более недоступен. Торг закрывается при выполнении таска.
	@Transactional
	@Test
	public void _04_05_06_03_soldProductBargainCloseTask_OK() {
		//Чистим все уведомления для покупателя
		TestUtils.deleteUserNotifications(buyerId, notificationRepository);

		//Торг в статусе контрпредложения от продавца
		setBargainState(bargainId, BargainState.COUNTER_OFFER, LocalDateTime.now());

		//Товар недоступен
		TestUtils.setProductState(productId, ProductState.HIDDEN, productRepository);
		commitAndStartNewTransaction();

		//Запускаем таск закрытия контрторгов
		scheduledBargainRunner.closeBargainsWithUnavailableProducts(100);

		//Даем немного времени все выполнить (уведомления расходятся асинхронно)
		TestUtils.sleep(2);

		//Коммитимся, чтобы изменения были доступны для АПИ
		commitAndStartNewTransaction();

		//Берем контрторг из базы
		Bargain bargain = bargainRepository.findById(bargainId).orElse(null);

		//Проверяем статус контрторга в базе
		assertSame(BargainState.SOLD, bargain.getState());

		//Покупателю пришло уведомление о проданном товаре
		assertNotificationCreatedAndLinkedToBargain(getBuyer(), BargainBuyerProductSoldNotification.class, bargain.getId(), false, false, false);

		//Возвращаем активный стейт товару
		TestUtils.setProductState(productId, ProductState.PUBLISHED, productRepository);
	}

	//Товар снова доступен. Торг возвращается в прежний стейт COUNTER_OFFER при выполнении таска с использованием bargain_aud.
	@Transactional
	@Test
	public void _04_05_07_01_publishedProductBargainOpenTask_COUNTER_OFFER_aud_OK() {
		//Торг в статусе SOLD
		setBargainState(bargainId, BargainState.SOLD, LocalDateTime.now());

		//Возвращаем активный стейт товару
		TestUtils.setProductState(productId, ProductState.PUBLISHED, productRepository);

		//Запускаем таск открытия контрторгов
		scheduledBargainRunner.openBargainsWithAvailableProducts(100);

		//Коммитимся, чтобы изменения были доступны для АПИ
		commitAndStartNewTransaction();

		//Берем контрторг из базы
		Bargain bargain = bargainRepository.findById(bargainId).orElse(null);

		//Проверяем статус контрторга в базе
		assertSame(BargainState.COUNTER_OFFER, bargain.getState());
	}

	//Товар снова доступен. Торг возвращается в прежний стейт EXPIRED при выполнении таска с использованием bargain_aud.
	@Transactional
	@Test
	public void _04_05_07_02_publishedProductBargainOpenTask_EXPIRED_aud_OK() {
		//Делаем контрторг просроченным для проверки возврана в этот же стейт после повторного открытия
		setBargainState(bargainId, BargainState.EXPIRED, null);

		//Торг в статусе SOLD
		setBargainState(bargainId, BargainState.SOLD, LocalDateTime.now());

		//Запускаем таск открытия контрторгов
		scheduledBargainRunner.openBargainsWithAvailableProducts(100);

		//Коммитимся, чтобы изменения были доступны для АПИ
		commitAndStartNewTransaction();

		//Берем контрторг из базы
		Bargain bargain = bargainRepository.findById(bargainId).orElse(null);

		//Проверяем статус контрторга в базе
		assertSame(BargainState.EXPIRED, bargain.getState());
	}

	//Товар снова доступен. Торг возвращается в прежний стейт COUNTER_OFFER при выполнении таска без использования bargain_aud.
	@Transactional
	@Test
	public void _04_05_07_03_publishedProductBargainOpenTask_COUNTER_OFFER_noAud_OK() {
		//Торг в статусе SOLD
		setBargainState(bargainId, BargainState.SOLD, LocalDateTime.now());

		//Чистим аудит, чтобы убедиться, что и без него все сработает
		jdbcTemplate.execute("DELETE FROM bargain_aud WHERE id=" + bargainId);
		commitAndStartNewTransaction();

		//Запускаем таск открытия контрторгов
		scheduledBargainRunner.openBargainsWithAvailableProducts(100);

		//Коммитимся, чтобы изменения были доступны для АПИ
		commitAndStartNewTransaction();

		//Берем контрторг из базы
		Bargain bargain = bargainRepository.findById(bargainId).orElse(null);

		//Проверяем статус контрторга в базе
		assertSame(BargainState.COUNTER_OFFER, bargain.getState());
	}

	//Товар снова доступен. Торг возвращается в дред-прежний стейт COUNTER_OFFER (минуя EXPIRED) при выполнении таска без использования bargain_aud.
	//Данные аудита отсутствуют, поэтому информации о прежнем статусе контрторга нет. Стейт вычисляется на основе шагов
	@Transactional
	@Test
	public void _04_05_07_04_publishedProductBargainOpenTask_COUNTER_OFFER_noAud_OK() {
		//Торг в статусе SOLD
		setBargainState(bargainId, BargainState.SOLD, LocalDateTime.now());

		//Чистим аудит, чтобы убедиться, что и без него все сработает
		jdbcTemplate.execute("DELETE FROM bargain_aud WHERE id=" + bargainId);
		commitAndStartNewTransaction();

		//Запускаем таск открытия контрторгов
		scheduledBargainRunner.openBargainsWithAvailableProducts(100);

		//Коммитимся, чтобы изменения были доступны для АПИ
		commitAndStartNewTransaction();

		//Берем контрторг из базы
		Bargain bargain = bargainRepository.findById(bargainId).orElse(null);

		//Проверяем статус контрторга в базе (не EXPIRED, т.к. аудита нет)
		assertSame(BargainState.COUNTER_OFFER, bargain.getState());
	}

	//Размер более недоступен. Торг закрывается при выполнении таска.
	@Transactional
	@Test
	public void _04_06_01_unavailableSizeBargainCloseTask_OK() {
		//Чистим все уведомления для покупателя
		TestUtils.deleteUserNotifications(buyerId, notificationRepository);

		//Торг в статусе контрпредложения от продавца
		setBargainState(bargainId, BargainState.COUNTER_OFFER, LocalDateTime.now());

		//Товар доступен
		TestUtils.setProductState(productId, ProductState.PUBLISHED, productRepository);
		//Размер недоступен
		jdbcTemplate.execute("UPDATE product_item set is_hidden = TRUE WHERE product_id = " + productId + " AND size_id = " + sizeId );
		commitAndStartNewTransaction();

		//Запускаем таск закрытия контрторгов
		scheduledBargainRunner.closeBargainsWithUnavailableSizes(100);

		//Даем немного времени все выполнить (уведомления расходятся асинхронно)
		TestUtils.sleep(2);

		//Коммитимся, чтобы изменения были доступны для АПИ
		commitAndStartNewTransaction();

		//Берем контрторг из базы
		Bargain bargain = bargainRepository.findById(bargainId).orElse(null);

		//Проверяем статус контрторга в базе
		assertSame(BargainState.SOLD, bargain.getState());

		//Покупателю пришло уведомление о проданном товаре
		assertNotificationCreatedAndLinkedToBargain(getBuyer(), BargainBuyerProductSoldNotification.class, bargain.getId(), false, false, false);
	}

	//Размер снова доступен. Торг возвращается в прежний стейт COUNTER_OFFER при выполнении таска с использованием bargain_aud.
	@Transactional
	@Test
	public void _04_06_02_availableSizeBargainOpenTask_COUNTER_OFFER_aud_OK() {
		//Размер снова доступен
		jdbcTemplate.execute("UPDATE product_item set is_hidden = FALSE WHERE product_id = " + productId + " AND size_id = " + sizeId );

		//Запускаем таск открытия контрторгов
		scheduledBargainRunner.openBargainsWithAvailableSizes(100);

		//Коммитимся, чтобы изменения были доступны для АПИ
		commitAndStartNewTransaction();

		//Берем контрторг из базы
		Bargain bargain = bargainRepository.findById(bargainId).orElse(null);

		//Проверяем статус контрторга в базе
		assertSame(BargainState.COUNTER_OFFER, bargain.getState());
	}

	/**
	 * Проверяется запрет на добавление других записей покупателем (подтверждение, отклонение, встречное предложение, отметка о покупке, создание)
	 */
	@Transactional
	@Test
	public void _05_01_02_sendWrongRecord_buyer_failed() {
		addWrongRecordAfterSellersRecord_buyer_failed();
	}

	/**
	 * Это когда продавец предложил свою цену, а покупатель не ответил за час до завершения торгов
	 */
	@Transactional
	@Test
	public void _05_01_03_bargainAlmostExpiredOfferNotification_seller_OK() {
		setBargainState(bargainId, BargainState.COUNTER_OFFER, LocalDateTime.now().minusHours(23));

		//Выполняем таск по созданию уведомлений
		scheduledBargainRunner.create1hBargainAlmostExpiredNotifications(100);

		commitAndStartNewTransaction();

		TestUtils.sleep(2);

		//23 часа прошло с момента создания заказа
		assertNotificationCreatedAndLinkedToBargain(getBuyer(), BargainBuyerAlmostExpiredByBuyerNotification.class, bargainId, false, false, true);
	}

	/**
	 * Покупатель успешно добавляет еще один встречный торг
	 */
	@Transactional
	@Test
	public void _05_02_addCounterOffer_buyer_OK() {
		//Товар незначенильно дешевеет (для проверки изменения базовой цены)
		int newProductPrice = (int) (initialProductPrice * 0.95);
		setProductPrice(productId, newProductPrice);

		int counterOfferPrice = (int) (initialBargainOfferedPrice * 0.9);
		BargainDetailedDTO bargain = addBargainRecordOK(buyerClient, bargainId, BargainRecordTypeDTO.Enum.OFFER, counterOfferPrice);
		assertEquals(counterOfferPrice, bargain.getLastPrice().intValue());
		assertEquals(BargainStateDTO.Enum.OFFER, bargain.getState().getName());

		//Проверка изменения базовой цены торга
		assertEquals(newProductPrice, bargain.getBasePrice().intValue());

		//Проверка наличия сумма и сообщения о сумме для продавца в последней записи
		checkLastRecordSellerReceivesSumAndMessage(bargain, false);

		//Проверка кол-ва оставшихся попыток
		assertEquals(1, bargain.getAttemptsLeft().intValue());

		//Проверка сообщения для покупателя
		assertNotNull(bargain.getMessage());
		TestUtils.assertStringContains(bargain.getMessage().getMessage(), "У продавца есть {timeLeft}, чтобы вам ответить");

		//Проверка сообщения для продавца
		BargainDetailedDTO bargainForSeller = getDetailedBargainOK(sellerClient, getBargainDetailedUrl(bargain.getId()));
		assertNotNull(bargainForSeller.getMessage());
		TestUtils.assertStringContains(bargainForSeller.getMessage().getMessage(), "Предложение действительно: {timeLeft}");

		//Проверка наличия сумма и сообщения о сумме для продавца в последней записи
		checkLastRecordSellerReceivesSumAndMessage(bargainForSeller, true);

		//Проверка прав покупателя после встречного торга покупателя (ничего не может)
		assertUserRightsCorrect(buyerClient, bargainId, null);

		//Проверка прав продавца после встречного торга покупателя
		assertUserRightsCorrect(sellerClient, bargainId, BargainRecordTypeDTO.Enum.OFFER, BargainRecordTypeDTO.Enum.ACCEPT, BargainRecordTypeDTO.Enum.DECLINE);

		//Продавцу пришло уведомление о повторном предложении цены от покупателя
		assertNotificationCreatedAndLinkedToBargain(getSeller(), BargainSellerNewOfferNotification.class, bargain.getId(), true, false, true);

		//Товар в каталоге содержит этот торг
		assertCatalogProductContainsBargainInfo(OfferStatus.PENDING, false);
	}

	/**
	 * Проверка создания BargainSellerNewOffer12hNotification за 12 часов до окончания времени принятия решения по торгу
	 */
	@Transactional
	@Test
	public void _05_03_bargainSellerNewOffer12hNotification_seller_OK() {
		//Старим контрторг на 13 часов
		setBargainState(bargainId, BargainState.OFFER, LocalDateTime.now().minusHours(13));

		//Выполняем таск по созданию уведомлений
		scheduledBargainRunner.create12hNotifications(100);

		commitAndStartNewTransaction();

		TestUtils.sleep(2);

		//Продавцу пришло уведомление о повторном предложении цены от покупателя
		assertNotificationCreatedAndLinkedToBargain(getSeller(), BargainSellerNewOffer12hNotification.class, bargainId, false, false, true);
	}

	@Transactional
	@Test
	public void _05_04_bargainSellerIgnoreNewOffer24hNotification_seller_OK() {
		setBargainState(bargainId, BargainState.OFFER, LocalDateTime.now().minusHours(22));

		//Выполняем таск по созданию уведомлений
		scheduledBargainRunner.create24hNewSellerOfferNotifications(100);

		commitAndStartNewTransaction();

		TestUtils.sleep(2);

		//22 часа прошло с момента создания заказа
		assertNotificationCreatedAndLinkedToBargain(getSeller(), BargainSellerNewOffer24hNotification.class, bargainId, false, false, true);
	}

	/**
	 * Породавец не успевает ответить на предложение и контрторг закрывается при получении развернутого объекта продавцом
	 */
	@Transactional
	@Test
	public void _06_01_01_expiredBargain_seller_OK() {
		//Старим контрторг на 30 часов (дата обновления), устанавливаем активный стейт
		setBargainState(bargainId, BargainState.OFFER, LocalDateTime.now().minusHours(30));

		//Проверяем что для продавца торг закрывается при запросе единичного торга продавцом
		BargainDetailedDTO bargainDetailedForSeller = getDetailedBargainOK(sellerClient, getBargainDetailedUrl(bargainId));
		assertEquals(BargainStateDTO.Enum.EXPIRED, bargainDetailedForSeller.getState().getName());
		//Торг содержит сообщение
		assertNotNull(bargainDetailedForSeller.getMessage());
		//Сообщение негативное
		assertSame(BargainDecorationDTO.NEGATIVE, bargainDetailedForSeller.getMessage().getDecoration());
		//Заголовок и текст
		TestUtils.assertStringContains(bargainDetailedForSeller.getMessage().getTitle(), "Срок действия предложения истек");
		TestUtils.assertStringContains(bargainDetailedForSeller.getMessage().getMessage(), "В следующий раз быстрее принимайте предложение покупателя");

		//Убедимся, что в базе та же сиуация (state=EXPIRED)
		commitAndStartNewTransaction();
		Bargain bargain = bargainRepository.findById(bargainId).orElse(null);
		assertEquals(BargainState.EXPIRED, bargain.getState());

		//Проверка прав покупателя для тогра в статусе EXPIRED (может предлагать)
		assertUserRightsCorrect(buyerClient, bargainId, BargainRecordTypeDTO.Enum.OFFER);

		//Проверка прав продавца для тогра в статусе EXPIRED (ничего не может)
		assertUserRightsCorrect(sellerClient, bargainId, null);

		//Товар в каталоге содержит этот торг
		assertCatalogProductContainsBargainInfo(OfferStatus.PENDING, false);

		//Возвращаем активный стейт контрторгу
		setBargainState(bargainId, BargainState.OFFER, null);
	}

	/**
	 * Породавец не успевает ответить на предложение и контрторг закрывается при получении списка торгов продавцом
	 */
	@Transactional
	@Test
	public void _06_01_02_expiredBargainList_seller_OK() {
		//Старим контрторг на 30 часов (дата обновления), устанавливаем активный стейт
		setBargainState(bargainId, BargainState.OFFER, LocalDateTime.now().minusHours(30));

		//Проверяем что для продавца торг закрывается при запросе списка торгов
		List<BargainLiteDTO> finishedBargains = getBargainPageOK(sellerClient, getIncomingFinishedBargainsUrl(), null, null).getItems();
		List<BargainLiteDTO> activeBargains = getBargainPageOK(sellerClient, getIncomingActiveBargainsUrl(), null, null).getItems();

		List<Long> finishedBargainIds = getBargainIds(finishedBargains);
		List<Long> activeBargainIds = getBargainIds(activeBargains);

		//Проверяем наличие торга в списках
		TestUtils.assertListContains(finishedBargainIds, bargainId);
		TestUtils.assertListDoesntContain(activeBargainIds, bargainId);

		//Возвращаем активный стейт контрторгу
		setBargainState(bargainId, BargainState.OFFER, null);
	}

	/**
	 * Породавец не успевает ответить на предложение, но покупатель по прежнему может добавить предложение, если у него есть попытки
	 */
	@Transactional
	@Test
	public void _06_01_03_expiredBargain_buyer_OK() {
		//Старим контрторг на 30 часов (дата обновления), устанавливаем активный стейт
		setBargainState(bargainId, BargainState.OFFER, LocalDateTime.now().minusHours(30));

		//Проверяем что для покупателя торг закрывается при запросе единичного торга покупателем
		BargainDetailedDTO bargainDetailedForBuyer = getDetailedBargainOK(buyerClient, getBargainDetailedUrl(bargainId));
		assertEquals(BargainStateDTO.Enum.EXPIRED, bargainDetailedForBuyer.getState().getName());
		//Торг содержит сообщение
		assertNotNull(bargainDetailedForBuyer.getMessage());
		//Сообщение негативное
		assertSame(BargainDecorationDTO.NEGATIVE, bargainDetailedForBuyer.getMessage().getDecoration());
		//Заголовок и текст
		TestUtils.assertStringContains(bargainDetailedForBuyer.getMessage().getTitle(), "К сожалению, продавец не ответил на ваш торг");
		TestUtils.assertStringContains(bargainDetailedForBuyer.getMessage().getMessage(), "Не расстраивайтесь, торгуйтесь с продавцами и покупайте по самым выгодным ценам");

		//Убедимся, что в базе та же сиуация (state=EXPIRED)
		commitAndStartNewTransaction();
		Bargain bargain = bargainRepository.findById(bargainId).orElse(null);
		assertEquals(BargainState.EXPIRED, bargain.getState());

		//Проверка прав покупателя для торга в статусе EXPIRED (может предложить свою цену повторно)
		assertUserRightsCorrect(buyerClient, bargainId, BargainRecordTypeDTO.Enum.OFFER);

		//Проверка прав продавца для тогра в статусе EXPIRED (ничего не может)
		assertUserRightsCorrect(sellerClient, bargainId, null);

		//Товар в каталоге содержит этот торг
		assertCatalogProductContainsBargainInfo(OfferStatus.PENDING, false);

		//Возвращаем активный стейт контрторгу
		setBargainState(bargainId, BargainState.OFFER, null);
	}

	/**
	 * Породавец не успевает ответить на предложение и контрторг попалает в завершенные при получении списка торгов покупателем
	 */
	@Transactional
	@Test
	public void _06_01_04_expiredBargainList_buyer_OK() {
		//Старим контрторг на 30 часов (дата обновления), устанавливаем активный стейт
		setBargainState(bargainId, BargainState.OFFER, LocalDateTime.now().minusHours(30));

		//Проверяем что для продавца торг закрывается при запросе списка торгов
		List<BargainLiteDTO> finishedBargains = getBargainPageOK(buyerClient, getOutgoingFinishedBargainsUrl(), null, null).getItems();
		List<BargainLiteDTO> activeBargains = getBargainPageOK(buyerClient, getOutgoingActiveBargainsUrl(), null, null).getItems();

		List<Long> finishedBargainIds = getBargainIds(finishedBargains);
		List<Long> activeBargainIds = getBargainIds(activeBargains);

		//Проверяем наличие торга в списках
		TestUtils.assertListContains(finishedBargainIds, bargainId);
		TestUtils.assertListDoesntContain(activeBargainIds, bargainId);

		//Возвращаем активный стейт контрторгу
		setBargainState(bargainId, BargainState.OFFER, null);
	}

	/**
	 * Продавец отклоняет торг
	 */
	@Transactional
	@Test
	public void _07_01_sellerDeclinesOffer_OK() {
		//Возвращяем текущую дату обновления, устанавливаем активный стейт
		setBargainState(bargainId, BargainState.OFFER, LocalDateTime.now());

		BargainDetailedDTO bargain = addBargainRecordOK(sellerClient, bargainId, BargainRecordTypeDTO.Enum.DECLINE, null);

		assertEquals(BargainStateDTO.Enum.DECLINED, bargain.getState().getName());

		//Проверка наличия сумма и сообщения о сумме для продавца в последней записи
		checkLastRecordSellerReceivesSumAndMessage(bargain, false);

		//Проверка кол-ва оставшихся попыток
		assertEquals(1, bargain.getAttemptsLeft().intValue());

		//Проверка сообщения для продавца
		assertNotNull(bargain.getMessage());
		TestUtils.assertStringContains(bargain.getMessage().getMessage(), "Соглашайтесь на предложения покупателей или предлагайте свою цену. Так вы быстрее продадите ваш товар");

		//Проверка сообщения для покупателя
		BargainDetailedDTO bargainForBuyer = getDetailedBargainOK(buyerClient, getBargainDetailedUrl(bargain.getId()));

		//Проверка наличия сумма и сообщения о сумме для продавца в последней записи
		checkLastRecordSellerReceivesSumAndMessage(bargainForBuyer, false);

		//Если торг отклонен, то timeLeft не приходит (ограничение по времени не требуется)
		assertNull(bargainForBuyer.getTimeLeft());

		assertNotNull(bargainForBuyer.getMessage());
		assertSame(BargainDecorationDTO.NEGATIVE, bargainForBuyer.getMessage().getDecoration());
		TestUtils.assertStringContains(bargainForBuyer.getMessage().getTitle(), "К сожалению, продавец отклонил ваш торг");
		TestUtils.assertStringContains(bargainForBuyer.getMessage().getMessage(), "Не расстраивайтесь, торгуйтесь с продавцами и покупайте по самым выгодным ценам");

		//Проверка прав покупателя после отклонения торга покупателя (может еще раз предложить свою цену)
		assertUserRightsCorrect(buyerClient, bargainId, BargainRecordTypeDTO.Enum.OFFER);

		//Проверка прав продавца после отклонения торга покупателя
		assertUserRightsCorrect(sellerClient, bargainId, null);

		//Старим торг, чтобы убедиться, что у покупателя сохраняется возможность еще раз возобновить торг через длительное время
		setBargainState(bargainId, BargainState.DECLINED, LocalDateTime.now().minusDays(10));
		bargainForBuyer = getDetailedBargainOK(buyerClient, getBargainDetailedUrl(bargain.getId()));
		//Если торг отклонен, то timeLeft не приходит. Покупатель может вновь возобновить торг, если у него остались попытки.
		assertNull(bargainForBuyer.getTimeLeft());

		//Товар в каталоге содержит этот торг
		assertCatalogProductContainsBargainInfo(OfferStatus.REJECTED, false);

		//Покупателю пришло уведомление об отклонении торга продавцом
		assertNotificationCreatedAndLinkedToBargain(getBuyer(), BargainBuyerDeclinedNotification.class, bargain.getId(), false, false, true);
	}

	/**
	 * После отклонения покупатель еще раз успешно добавляет еще один встречный торг
	 * Это его последняя попытка
	 */
	@Transactional
	@Test
	public void _07_02_addCounterOffer_buyer_OK() {
		int counterOfferPrice = (int) (initialBargainOfferedPrice * 0.95);
		lastBargainPrice = counterOfferPrice;
		BargainDetailedDTO bargain = addBargainRecordOK(buyerClient, bargainId, BargainRecordTypeDTO.Enum.OFFER, counterOfferPrice);
		assertEquals(counterOfferPrice, bargain.getLastPrice().intValue());
		assertEquals(BargainStateDTO.Enum.OFFER, bargain.getState().getName());

		//Проверка наличия сумма и сообщения о сумме для продавца в последней записи
		checkLastRecordSellerReceivesSumAndMessage(bargain, false);

		//Проверка кол-ва оставшихся попыток
		assertEquals(0, bargain.getAttemptsLeft().intValue());

		//Проверка сообщения для покупателя
		assertNotNull(bargain.getMessage());
		TestUtils.assertStringContains(bargain.getMessage().getMessage(), "У продавца есть {timeLeft}, чтобы вам ответить");

		//Проверка сообщения для продавца
		BargainDetailedDTO bargainForSeller = getDetailedBargainOK(sellerClient, getBargainDetailedUrl(bargain.getId()));

		//Проверка наличия сумма и сообщения о сумме для продавца в последней записи
		checkLastRecordSellerReceivesSumAndMessage(bargainForSeller, true);

		assertNotNull(bargainForSeller.getMessage());
		TestUtils.assertStringContains(bargainForSeller.getMessage().getMessage(), "Предложение действительно: {timeLeft}");

		//Проверка прав покупателя после встречного торга покупателя (ничего не может)
		assertUserRightsCorrect(buyerClient, bargainId, null);

		//Проверка прав продавца после встречного торга покупателя
		assertUserRightsCorrect(sellerClient, bargainId, BargainRecordTypeDTO.Enum.OFFER, BargainRecordTypeDTO.Enum.ACCEPT, BargainRecordTypeDTO.Enum.DECLINE);

		//Товар в каталоге содержит этот торг
		assertCatalogProductContainsBargainInfo(OfferStatus.PENDING, false);
	}

	/**
	 * Продавец одобряет торг
	 */
	@Transactional
	@Test
	public void _07_03_sellerAcceptsOffer_OK() {
		//Возвращяем текущую дату обновления, устанавливаем активный стейт
		setBargainState(bargainId, BargainState.OFFER, LocalDateTime.now());

		BargainDetailedDTO bargain = addBargainRecordOK(sellerClient, bargainId, BargainRecordTypeDTO.Enum.ACCEPT, null);

		assertEquals(BargainStateDTO.Enum.CONFIRMED, bargain.getState().getName());

		//Проверка наличия сумма и сообщения о сумме для продавца в последней записи
		checkLastRecordSellerReceivesSumAndMessage(bargain, false);

		//Проверка кол-ва оставшихся попыток
		assertEquals(0, bargain.getAttemptsLeft().intValue());

		//Проверка сообщения для продавца
		assertNotNull(bargain.getMessage());
		TestUtils.assertStringContains(bargain.getMessage().getMessage(), "У покупателя есть {timeLeft}, чтобы купить товар по новой цене");

		//Проверка сообщения для покупателя
		BargainDetailedDTO bargainForBuyer = getDetailedBargainOK(buyerClient, getBargainDetailedUrl(bargain.getId()));

		//Проверка наличия сумма и сообщения о сумме для продавца в последней записи
		checkLastRecordSellerReceivesSumAndMessage(bargainForBuyer, false);

		assertNotNull(bargainForBuyer.getMessage());
		TestUtils.assertStringContains(bargainForBuyer.getMessage().getMessage(), "Цена действительна: {timeLeft}");

		//Проверка прав покупателя после подтверждения торга покупателя (может только купить)
		assertUserRightsCorrect(buyerClient, bargainId, BargainRecordTypeDTO.Enum.ACCEPT);

		//Проверка прав продавца после отклонения торга покупателя (ничего не может)
		assertUserRightsCorrect(sellerClient, bargainId, null);

		//Товар в каталоге содержит этот торг
		assertCatalogProductContainsBargainInfo(OfferStatus.ACCEPTED, true);

		//Покупателю пришло уведомление, что торг подвержден
		assertNotificationCreatedAndLinkedToBargain(getBuyer(), BargainBuyerConfirmedNotification.class, bargain.getId(), true, false, true);
	}

	@Transactional
	@Test
	public void _07_04_BargainBuyerAlreadyConfirmed24hNotification_seller_OK() {
		setBargainState(bargainId, BargainState.CONFIRMED, LocalDateTime.now().minusHours(22));

		//Выполняем таск по созданию уведомлений
		scheduledBargainRunner.create24hNewBuyerOfferNotifications(100);

		commitAndStartNewTransaction();

		TestUtils.sleep(2);

		//22 часа прошло с момента создания заказа
		assertNotificationCreatedAndLinkedToBargain(getBuyer(), BargainBuyerAlreadyConfirmed24hNotification.class, bargainId, false, false, true);
	}

	/**
	 * Попытка отправки еще одного предложения покупателем по существующему контрторгу (после истечения всех попыток)
	 */
	@Transactional
	@Test
	public void _07_04_sendAnotherOffer_buyer_failed() {
		sendAnotherOffer_buyer_failed();
	}

	/**
	 * Проверка заполненности глобальны бабблов аккаунта покупателя данными о контрторгах
	 */
	@Transactional
	@Test
	public void _07_05_01_checkGlobalBubbles_buyer_OK() {
		//Получаем бабблы для покупателя
		BubblesDTO bubblesDTO = accountTestSupport.getBubblesSuccessful(buyerClient, false);
		//Бабблы контрторгов пришли
		assertNotNull(bubblesDTO.getBargainBubbles());
		//Бабблы по контрторгам покупателя не пустые
		assertFalse(bubblesDTO.getBargainBubbles().getBuyerBargainBubbles().isEmpty());
	}

	/**
	 * Проверка заполненности глобальны бабблов аккаунта продавца данными о контрторгах
	 */
	@Transactional
	@Test
	public void _07_05_02_checkGlobalBubbles_seller_OK() {
		//Получаем бабблы для покупателя
		BubblesDTO bubblesDTO = accountTestSupport.getBubblesSuccessful(sellerClient, false);
		//Бабблы контрторгов пришли
		assertNotNull(bubblesDTO.getBargainBubbles());
		//Бабблы по контрторгам продавца не пустые
		assertFalse(bubblesDTO.getBargainBubbles().getSellerBargainBubbles().isEmpty());
	}

	/******** API корзины *******************/

	/**
	 * Покупатель добавляет товар в корзину и видит новую цену
	 */
	@Transactional
	@Test
	public void _08_01_buyerAddsToCart_OK() {
		//Добавляем товар в корзину
		GroupedCart groupedCart = cartTestSupport.addToCartSuccessful(productId, sizeId, 1);
		//Убеждаемся, что у нас одна группа
		assertEquals(1, groupedCart.getGroups().size());
		OrderDTO group = groupedCart.getGroups().get(0);
		//В группе одна позиция
		assertEquals(1, group.getItems().size());
		OrderPositionDTO orderPositionDTO = group.getItems().get(0);
		//Товар и размер совпадают
		assertEquals(productId.longValue(), orderPositionDTO.getProductId());
		assertEquals(sizeId.longValue(), orderPositionDTO.getSize().getId());
		//Цена позиции соответствует последней предложенной по торгу
		assertEquals(lastBargainPrice.intValue(), orderPositionDTO.getFinalAmount().intValue());
		//Цена в группе соответствует подтвержденной цене по торгу
		assertEquals(lastBargainPrice.intValue(), group.getFinalAmountWithoutDeliveryCost().intValue());
	}

    //Попробуем купить товар по новой цене и убедимся, что при офромлении товара уведомление станет "выполненным".
    @Transactional
    @Test
    public void _08_02_buyerCreatesOrderWithBargainAndPromocode_OK() {
        //Сначала очистим корзину.
        cartTestSupport.cleanCart(false);
        //Теперь пробуем купить то, что выторговали
        GroupedCart groupedCart = cartTestSupport.addToCartSuccessful(productId, sizeId, 1);
        OrderDTO group = groupedCart.getGroup(sellerId);
        BigDecimal finalAmount = group.getFinalAmount();
        //Цена в корзине соответствует подтвержденной цене по офферу
        assertEquals(lastBargainPrice.intValue(), finalAmount.subtract(deliveryCostService.getDefaultCost()).intValue());

        commitAndStartNewTransaction();

        //Создаем промокод
        int promoCodeAbsoluteAmount = 1000;
        String promoCodeName = "TESTABSOLUTE" + 1000;
		jdbcTemplate.execute("DELETE FROM protected_promo_code WHERE order_id IN(SELECT id FROM public.order WHERE promo_code_id IN (SELECT id FROM promo_code WHERE code='" + promoCodeName + "'))");
		jdbcTemplate.execute("DELETE FROM public.order WHERE promo_code_id IN (SELECT id FROM promo_code WHERE code='" + promoCodeName + "')");
        jdbcTemplate.execute("DELETE FROM promo_code WHERE code='" + promoCodeName + "'");
        PromoCode promoCode = new AbsolutePromoCode().setValue(new BigDecimal(promoCodeAbsoluteAmount)).setCode(promoCodeName).setBeginPrice(new BigDecimal(1000))
                .setCreatedAt(ZonedDateTime.now())
                .setStartsAt(ZonedDateTime.now())
                .setExpiresAt(ZonedDateTime.now().plusYears(1));
        promoCodeRepository.saveAndFlush(promoCode);
        commitTransaction();


        //Проверяем действие промокода (должно отразиться на поле discount)
        OrderDTO promoCodeOrderDTO = cartTestSupport.checkPromoCode(sellerId, promoCodeName);

        //Должна прийти информация по скидке
        Discount discount = promoCodeOrderDTO.getDiscount();
        assertNotNull(discount);

        //resultAmount должна учитывать и оффер и промокод
        int expectedCostWithOfferAndPromoCode = lastBargainPrice - promoCodeAbsoluteAmount;

        //Оффер и промокод учтены
        assertEquals(expectedCostWithOfferAndPromoCode, discount.resultAmount.intValue());

        //Ожидаемая полная стоимость заказа с доставкой
        int expectedCostWithOfferAndPromoCodeAndDelivery = expectedCostWithOfferAndPromoCode + deliveryCostService.getDefaultCost().intValue();

        //Холдируем
        OrderService.InitOrderResult holdResult = cartTestSupport.holdWithSetAddressEndpoint(CartTestSupport.HOLD_V2_ENDPOINT, sellerId, promoCodeName);
        Long orderId = holdResult.getOrderId();

        startNewTransaction();

        Order order = orderRepository.findById(orderId).orElse(null);

        //Проверяем, что оффер и промокод применен (amount меньше на величину оффера и промокода)
        BigDecimal orderFinalAmount = order.getFinalAmount();
        assertEquals(expectedCostWithOfferAndPromoCodeAndDelivery, (int) Math.round(orderFinalAmount.doubleValue()));

        //Убеждаемся, что позиция заказа содержит ссылку на контрторг
        OrderPosition orderPosition = order.getOrderPositions().get(0);
        assertEquals(productId, orderPosition.getProductItem().getProduct().getId());
        assertNotNull(orderPosition.getBargain());
        assertEquals(bargainId, orderPosition.getBargain().getId());

        //Убеждаемся, что теперь контрторг потреблен
        BargainDetailedDTO bargainForBuyer = getDetailedBargainOK(buyerClient, getBargainDetailedUrl(bargainId));
        assertSame(BargainStateDTO.Enum.CONSUMED, bargainForBuyer.getState().getName());
        commitAndStartNewTransaction();

        //Убеждаемся, что уведомление более не требует действия (действие выполнено)
		assertNotificationCreatedAndLinkedToBargain(getBuyer(), BargainBuyerConfirmedNotification.class, bargainId, true, true, true);
    }

	/******** API АДМИНКИ *****************/

	/**
	 * Попытка получить страницу контрторгов через админское АПИ без авторити
	 */
	@Test
	public void _10_01_getBargainsPageWithoutAuthorities_admin_failed() throws Exception{
		mockMvc.perform(get(getAdminBargainsServiceUrl())
				.contentType(MediaType.APPLICATION_JSON_VALUE))
				.andExpect(status().is(HttpStatus.FORBIDDEN.value()))
				;
	}

	/**
	 * Админ успешно получает развернутый контрторг
	 */
	@Test
	@WithMockUser(authorities = {"OFFER_MODERATION"})
	public void _10_02_getBargainsPageWithAuthorities_admin_OK() throws Exception{
		MvcResult result = mockMvc.perform(get(getAdminBargainsDetailedBargainUrl(bargainId))
				.contentType(MediaType.APPLICATION_JSON_VALUE))
				.andExpect(status().isOk())
				.andReturn()
		;
		Api2Response<AdminBargainDetailedDTO> apiV2Response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2Response<AdminBargainDetailedDTO>>(){});
		AdminBargainDetailedDTO bargain = apiV2Response.getData();
		assertNotNull(bargain);
		//Проверим доп. поля
		assertNotNull(bargain.getSellerReceivesSum());
		assertNotNull(bargain.getDiscount());
		assertNotNull(bargain.getDiscountProc());
		assertNotNull(bargain.getSeller().getEmail());
		assertNotNull(bargain.getBuyer().getEmail());
		assertNotNull(bargain.getProduct().getBrand());
		assertNotNull(bargain.getProduct().getColor());
	}

	/**
	 * Админ успешно получает страницу контрторгов
	 */
	@Test
	@WithMockUser(authorities = {"OFFER_MODERATION"})
	public void _10_03_getBargainsPageWithAuthorities_admin_OK() throws Exception{
		MvcResult result = mockMvc.perform(get(getAdminBargainsServiceUrl() +"?buyerIds=" + buyerId)
				.contentType(MediaType.APPLICATION_JSON_VALUE))
				.andExpect(status().isOk())
				.andReturn()
				;
		Api2Response<Page<AdminBargainLiteDTO>> apiV2Response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2Response<Page<AdminBargainLiteDTO>>>(){});
		Page<AdminBargainLiteDTO> bargainsPage = apiV2Response.getData();
		//Контрторги имеются и в нужном количестве
		assertEquals(2, bargainsPage.getItems().size());
		//Проверим доп. поля по контрторгам
		for(AdminBargainLiteDTO bargain : bargainsPage.getItems()){
			assertNotNull(bargain.getSellerReceivesSum());
			assertNotNull(bargain.getDiscount());
			assertNotNull(bargain.getDiscountProc());
			assertNotNull(bargain.getSeller().getEmail());
			assertNotNull(bargain.getBuyer().getEmail());
			assertNotNull(bargain.getProduct().getBrand());
			assertNotNull(bargain.getProduct().getColor());
		}
	}

	/**
	 * Проверка фильтра по дате
	 * Админ успешно получает страницу контрторгов с фильтром по датам (час назад - полчаса назад) и не видит торгов.
	 */
	@Test
	@WithMockUser(authorities = {"OFFER_MODERATION"})
	public void _10_04_getBargainsPageWithAuthoritiesWrongDates_admin_OK() throws Exception{
		LocalDateTime startTime = Utils.nowAtUTC().minusHours(1);
		LocalDateTime endTime = startTime.plusMinutes(30);
		String timeParamsQuery = String.format("&startTime=%s&endTime=%s", Utils.formatLocalDateTime(startTime), Utils.formatLocalDateTime(endTime));
		MvcResult result = mockMvc.perform(get(getAdminBargainsServiceUrl() +"?buyerIds=" + buyerId + timeParamsQuery)
				.contentType(MediaType.APPLICATION_JSON_VALUE))
				.andExpect(status().isOk())
				.andReturn()
				;
		Api2Response<Page<AdminBargainLiteDTO>> apiV2Response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2Response<Page<AdminBargainLiteDTO>>>(){});
		Page<AdminBargainLiteDTO> bargainsPage = apiV2Response.getData();
		//Получили пустой список
		assertEquals(0, bargainsPage.getItems().size());
	}

	/**
	 * Проверка фильтра по дате
	 * Админ успешно получает страницу контрторгов с фильтром по датам (10 минут назад - сейчас) и видит торги.
	 */
	@Test
	@WithMockUser(authorities = {"OFFER_MODERATION"})
	public void _10_05_getBargainsPageWithAuthoritiesRightDates_admin_OK() throws Exception{
		LocalDateTime startTime = Utils.nowAtUTC().minusMinutes(10);
		LocalDateTime endTime = Utils.nowAtUTC();
		String timeParamsQuery = String.format("&startTime=%s&endTime=%s", Utils.formatLocalDateTime(startTime), Utils.formatLocalDateTime(endTime));
		MvcResult result = mockMvc.perform(get(getAdminBargainsServiceUrl() +"?buyerIds=" + buyerId + timeParamsQuery)
				.contentType(MediaType.APPLICATION_JSON_VALUE))
				.andExpect(status().isOk())
				.andReturn()
				;
		Api2Response<Page<AdminBargainLiteDTO>> apiV2Response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2Response<Page<AdminBargainLiteDTO>>>(){});
		Page<AdminBargainLiteDTO> bargainsPage = apiV2Response.getData();
		//Торги видны в списке
		assertEquals(2, bargainsPage.getItems().size());
	}


	/**
	 *
	 */
	@Test
	@Transactional
	@WithMockUser(authorities = {"OFFER_MODERATION"})
	public void _10_06_getBargainsPageWithFilter_OK() throws Exception {

		// по товарам beegz пока нет торгов
		MvcResult result = mockMvc.perform(get(getAdminBargainsServiceUrl() +"?buyerIds=" + buyerId + "&filterType=BEEGZ")
										  .contentType(MediaType.APPLICATION_JSON_VALUE))
								  .andExpect(status().isOk())
								  .andReturn()
				;
		Api2Response<Page<AdminBargainLiteDTO>> apiV2Response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2Response<Page<AdminBargainLiteDTO>>>(){});
		Page<AdminBargainLiteDTO> bargainsPage = apiV2Response.getData();
		assertEquals(0, bargainsPage.getItems().size());


		// теперь у одного из товаров установим метку beegz и убедимся, что вернется только одна записсь торгов
		productRepository.findById(productId).ifPresent(product -> {
			product.setBeegzStatusTime(LocalDateTime.now());
			productRepository.save(product);
			commitAndStartNewTransaction();
		});

		result = mockMvc.perform(get(getAdminBargainsServiceUrl() +"?buyerIds=" + buyerId + "&filterType=BEEGZ")
										  .contentType(MediaType.APPLICATION_JSON_VALUE))
								  .andExpect(status().isOk())
								  .andReturn()
				;
		apiV2Response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2Response<Page<AdminBargainLiteDTO>>>(){});
		bargainsPage = apiV2Response.getData();
		assertEquals(1, bargainsPage.getItems().size());

		// теперь то же самое с фильтром CONCIERGE
		productRepository.findById(productId).ifPresent(product -> {
			product.setSelectedConciergeTime(LocalDateTime.now());
			productRepository.save(product);
			commitAndStartNewTransaction();
		});
		result = mockMvc.perform(get(getAdminBargainsServiceUrl() +"?buyerIds=" + buyerId + "&filterType=CONCIERGE")
								.contentType(MediaType.APPLICATION_JSON_VALUE))
						.andExpect(status().isOk())
						.andReturn()
		;
		apiV2Response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2Response<Page<AdminBargainLiteDTO>>>(){});
		bargainsPage = apiV2Response.getData();
		assertEquals(1, bargainsPage.getItems().size());


		// теперь отфильтруем по типу продавца VIP. Пока должно быть пусто
		result = mockMvc.perform(get(getAdminBargainsServiceUrl() +"?buyerIds=" + buyerId + "&filterType=VIP")
								.contentType(MediaType.APPLICATION_JSON_VALUE))
						.andExpect(status().isOk())
						.andReturn()
		;
		apiV2Response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2Response<Page<AdminBargainLiteDTO>>>(){});
		bargainsPage = apiV2Response.getData();
		assertEquals(0, bargainsPage.getItems().size());

		User seller = userService.getUserByEmail(sellerEmail);
		userCommonTagService.bindTagToUser(seller, UserCommonTagCode.VIP_STATUS_VIP_STATUS.name());
		userService.save(seller);
		commitAndStartNewTransaction();

		// теперь должно найтить оба торга
		result = mockMvc.perform(get(getAdminBargainsServiceUrl() +"?buyerIds=" + buyerId + "&filterType=VIP")
								.contentType(MediaType.APPLICATION_JSON_VALUE))
						.andExpect(status().isOk())
						.andReturn()
		;
		apiV2Response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2Response<Page<AdminBargainLiteDTO>>>(){});
		bargainsPage = apiV2Response.getData();
		assertEquals(2, bargainsPage.getItems().size());

		// а теперь и для INFLUENCER
		seller = userService.getUserByEmail(sellerEmail);
		userCommonTagService.bindTagToUser(seller, UserCommonTagCode.USER_STATUS_INFLUENCER.name());
		userService.save(seller);
		commitAndStartNewTransaction();

		result = mockMvc.perform(get(getAdminBargainsServiceUrl() +"?buyerIds=" + buyerId + "&filterType=INFLUENCER")
								.contentType(MediaType.APPLICATION_JSON_VALUE))
						.andExpect(status().isOk())
						.andReturn()
		;
		apiV2Response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2Response<Page<AdminBargainLiteDTO>>>(){});
		bargainsPage = apiV2Response.getData();
		assertEquals(2, bargainsPage.getItems().size());
	}


	/**
	 * Попытка возобновить контрторг, который не является просроченным
	 */
	@Test
	@WithMockUser(authorities = {"OFFER_MODERATION"})
	@Transactional
	public void _11_01_restoreBargainNotExpired_admin_failed() throws Exception{
		setBargainState(bargainId, BargainState.CONFIRMED, null);
		MvcResult result = mockMvc.perform(patch(getAdminBargainsBargainRestoreExpiredUrl(bargainId))
				.contentType(MediaType.APPLICATION_JSON_VALUE))
				.andExpect(status().is(HttpStatus.BAD_REQUEST.value()))
				.andReturn()
		;
		//Респонс содержит соотв ошибку
		TestUtils.assertStringContains(result.getResponse().getContentAsString(), "Торг не является просроченным и не может быть возобновлен");
	}

	/**
	 * Админ возобновляет просроченный со стороны покупателя подтвержденный торг
	 */
	@Test
	@WithMockUser(authorities = {"OFFER_MODERATION"})
	@Transactional
	public void _11_02_restoreBargainExpiredByBuyer_CONFIRMED_admin_OK() throws Exception{
		Bargain bargain = bargainRepository.findById(bargainId).orElse(null);
		//Убеждаемся, что последним ходаком был продавец
		assertFalse(bargain.buyerWasLast());

		//Делаем торг просроченным со стороны покупателя (старим дату обновления)
		setBargainState(bargainId, BargainState.CONFIRMED, Utils.nowAtUTC().minusDays(5));
		//Закрываем торг
		bargainService.checkBargainAndCloseAndSaveIfNeeded(bargainRepository.findById(bargainId).orElse(null));
		commitAndStartNewTransaction();

		//Проверяем, что товар в статусе EXPIRED и дата старая
		bargain = bargainRepository.findById(bargainId).orElse(null);
		//Статус просроченный
		assertSame(bargain.getState(), BargainState.EXPIRED);

		//При смене статсуса на EXPIRED дата поменялась на свежую
		//Поэтому, дополнительно состарим дату, чтобы убедиться, что она потом обновится на более свежую
		bargain.setChangeTime(Utils.nowAtUTC().minusDays(5));
		commitAndStartNewTransaction();

		//Реторим торг через админку
		MvcResult result = mockMvc.perform(patch(getAdminBargainsBargainRestoreExpiredUrl(bargainId))
				.contentType(MediaType.APPLICATION_JSON_VALUE))
				.andExpect(status().isOk())
				.andReturn()
				;
		//Респонс поменял статус на подтвердженный
		Api2Response<AdminBargainDetailedDTO> apiV2Response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2Response<AdminBargainDetailedDTO>>(){});
		AdminBargainDetailedDTO bargainDTO = apiV2Response.getData();
		assertNotNull(bargainDTO);
		assertSame(BargainStateDTO.Enum.CONFIRMED, bargainDTO.getState().getName());

		//Убеждаемся, что в базе то же самое
		bargain = bargainRepository.findById(bargainId).orElse(null);
		//Статус подтвержденный
		assertSame(bargain.getState(), BargainState.CONFIRMED);
		//Дата обновления свежая
		TestUtils.dateIsFreshAndInCorrectOffset(bargain.getChangeTime());
	}

	/**
	 * Админ возобновляет просроченный со стороны покупателя встречный торг от продавца
	 */
	@Test
	@WithMockUser(authorities = {"OFFER_MODERATION"})
	@Transactional
	public void _11_02_restoreBargainExpiredByBuyer_COUNTER_OFFER_admin_OK() throws Exception{
		Bargain bargain = bargainRepository.findById(bargainId).orElse(null);
		//Убеждаемся, что последним ходаком был продавец
		assertFalse(bargain.buyerWasLast());

		//Меняем тип последней записи с ACCEPT на OFFER
		BargainRecord lastRecord = bargain.getLastRecord();
		lastRecord.setType(BargainRecordType.OFFER);
		//Продавец предлагает цену на 1 рубль больше, чем предлагад покупатель
		lastRecord.setPrice(bargain.getRecords().get(bargain.getRecords().size() - 2).getPrice() + 1);
		//Меняем статус торга на COUNTER_OFFER
		bargain.setState(BargainState.COUNTER_OFFER);
		//Старим дату
		bargain.setChangeTime(Utils.nowAtUTC().minusDays(5));
		commitAndStartNewTransaction();

		//Обновляем торг из базы и проверяем, что все сохранилось
		bargain = bargainRepository.findById(bargainId).orElse(null);
		//Стейт обновился (контр-оффер)
		assertSame(BargainState.COUNTER_OFFER, bargain.getState());
		//Последняя запись имеет тип OFFER (контр-предложение от продавца)
		assertSame(BargainRecordType.OFFER, bargain.getLastRecord().getType());

		//Закрываем торг
		bargainService.checkBargainAndCloseAndSaveIfNeeded(bargain);
		commitAndStartNewTransaction();

		//Проверяем, что товар в статусе EXPIRED и дата старая
		bargain = bargainRepository.findById(bargainId).orElse(null);
		//Статус просроченный
		assertSame(bargain.getState(), BargainState.EXPIRED);

		//При смене статсуса на EXPIRED дата поменялась на свежую
		//Поэтому, дополнительно состарим дату, чтобы убедиться, что она потом обновится на более свежую
		bargain.setChangeTime(Utils.nowAtUTC().minusDays(5));
		commitAndStartNewTransaction();

		MvcResult result = mockMvc.perform(patch(getAdminBargainsBargainRestoreExpiredUrl(bargainId))
				.contentType(MediaType.APPLICATION_JSON_VALUE))
				.andExpect(status().isOk())
				.andReturn()
				;
		//Респонс поменял статус на подтвердженный
		Api2Response<AdminBargainDetailedDTO> apiV2Response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2Response<AdminBargainDetailedDTO>>(){});
		AdminBargainDetailedDTO bargainDTO = apiV2Response.getData();
		assertNotNull(bargainDTO);
		assertSame(BargainStateDTO.Enum.COUNTER_OFFER, bargainDTO.getState().getName());

		//Убеждаемся, что в базе то же самое
		bargain = bargainRepository.findById(bargainId).orElse(null);
		//Статус подтвержденный
		assertSame(bargain.getState(), BargainState.COUNTER_OFFER);
		//Дата обновления свежая
		TestUtils.dateIsFreshAndInCorrectOffset(bargain.getChangeTime());
	}

	/**
	 * Админ возобновляет просроченный со стороны покупателя торг
	 */
	@Test
	@WithMockUser(authorities = {"OFFER_MODERATION"})
	@Transactional
	public void _11_04_restoreBargainExpiredBySeller_admin_OK() throws Exception{
		//Удаляем последнюю запись торга, делая покупателя последним ходаком
		Bargain bargain = bargainRepository.findById(bargainId).orElse(null);
		BargainRecord lastRecord = bargain.getLastRecord();
		bargain.getRecords().remove(lastRecord);
		bargainRepository.save(bargain);
		commitAndStartNewTransaction();

		//Обновляем торг из базы
		bargain = bargainRepository.findById(bargainId).orElse(null);
		//Убеждаемся, что теперь последним ходаком был покупатель
		assertTrue(bargain.buyerWasLast());

		//Делаем торг просроченным со стороны продавца (старим дату обновления)
		setBargainState(bargainId, BargainState.CONFIRMED, Utils.nowAtUTC().minusDays(5));
		//Закрываем торг
		bargainService.checkBargainAndCloseAndSaveIfNeeded(bargainRepository.findById(bargainId).orElse(null));
		commitAndStartNewTransaction();

		//Проверяем, что товар в статусе EXPIRED и дата старая
		bargain = bargainRepository.findById(bargainId).orElse(null);
		//Статус просроченный
		assertSame(bargain.getState(), BargainState.EXPIRED);

		//При смене статсуса на EXPIRED дата поменялась на свежую
		//Поэтому, дополнительно состарим дату, чтобы убедиться, что она потом обновится на более свежую
		bargain.setChangeTime(Utils.nowAtUTC().minusDays(5));
		commitAndStartNewTransaction();

		MvcResult result = mockMvc.perform(patch(getAdminBargainsBargainRestoreExpiredUrl(bargainId))
				.contentType(MediaType.APPLICATION_JSON_VALUE))
				.andExpect(status().isOk())
				.andReturn()
				;
		//Респонс поменял статус на подтвердженный
		Api2Response<AdminBargainDetailedDTO> apiV2Response = mapper.readValue(result.getResponse().getContentAsString(), new TypeReference<Api2Response<AdminBargainDetailedDTO>>(){});
		AdminBargainDetailedDTO bargainDTO = apiV2Response.getData();
		assertNotNull(bargainDTO);
		assertSame(BargainStateDTO.Enum.OFFER, bargainDTO.getState().getName());

		//Убеждаемся, что в базе то же самое
		bargain = bargainRepository.findById(bargainId).orElse(null);
		//Статус подтвержденный
		assertSame(bargain.getState(), BargainState.OFFER);
		//Дата обновления свежая
		TestUtils.dateIsFreshAndInCorrectOffset(bargain.getChangeTime());
	}

	/* ТЕСТИРОВАНИЕ ТОРГОВ ДЛЯ ТОВАРОВ С ПРОИЗВОЛЬНОЙ КОМИССИЕЙ */

	/**
	 * Цена не может быть ниже 70% для товара с произвольной комиссией.
	 */
	@Test
	@Transactional
	public void _12_01_lowBargainPriceForProductWithCustomCommissionShouldFail() {
		Product product = productRepository.getOne(productId);

		setProductCustomCommission(
				product.getId(),
				BigDecimal.valueOf(15000L),
				BigDecimal.valueOf(10000L)
		);

		Bargain bargain = bargainRepository.findById(bargainId).orElse(null);

		if (bargain == null) {
			fail("Торг должен был быть найден");
		}

		bargain.setChangeTime(LocalDateTime.now());
		BargainRecord lastRecord = bargain.getLastRecord();
		bargain.getRecords().remove(lastRecord);
		bargain.setBasePrice(product.getCurrentPrice().intValue());
		bargain.setLastPrice(BigDecimal.valueOf(bargain.getBasePrice()).multiply(BigDecimal.valueOf(0.9)).intValue());

		bargainRepository.save(bargain);
		commitAndStartNewTransaction();

		int counterOfferPrice = 10499; //ниже 70% от цены товара

		sendOfferFailed(
				buyerClient,
				bargainId,
				BargainRecordTypeDTO.Enum.OFFER,
				counterOfferPrice,
				"Цена не может быть ниже 70% стоимости товара"
		);
	}

	/**
	 * Добавление товара с произвольной комиссией в корзину и его последуюшая покупка.
	 */
	@Test
	@Transactional
	public void _12_02_bargainPriceForProductWithCustomCommission() {
		BigDecimal priceWithCommission = BigDecimal.valueOf(20000L);
		BigDecimal priceWithoutCommission = BigDecimal.valueOf(10000L);

		setProductCustomCommission(productId2, priceWithCommission, priceWithoutCommission);

		int bargainPrice = priceWithCommission.multiply(new BigDecimal("0.9")).intValue();
		int sellerReceivesSum = priceWithoutCommission.multiply(new BigDecimal("0.9")).intValue();

		BargainDetailedDTO dto = addBargainRecordOK(
				buyerClient,
				bargainId2,
				BargainRecordTypeDTO.Enum.OFFER,
				bargainPrice
		);
		addBargainRecordOK(sellerClient, bargainId2, BargainRecordTypeDTO.Enum.ACCEPT, null);
		Bargain bargain = bargainRepository.findById(dto.getId()).orElse(null);
		Product product = productRepository.findById(productId2).orElse(null);

		if (bargain == null) {
			fail("Торг должен был быть найден");
		}
		if (product == null) {
			fail("Товар должен был быть найден");
		}

		//Добавляем товар в корзину
		GroupedCart groupedCart = cartTestSupport.addToCartSuccessful(productId2, sizeId2, 1);

		assertEquals(1, groupedCart.getGroups().size());

		OrderDTO group = groupedCart.getGroups().get(0);

		//В группе одна позиция
		assertEquals(1, group.getItems().size());
		OrderPositionDTO orderPositionDTO = group.getItems().get(0);
		//Товар и размер совпадают
		assertEquals(productId2.longValue(), orderPositionDTO.getProductId());
		assertEquals(sizeId2.longValue(), orderPositionDTO.getSize().getId());
		//Цена позиции соответствует последней предложенной по торгу
		assertEquals(bargain.getLastPrice().intValue(), orderPositionDTO.getFinalAmount().intValue());
		//Цена в группе соответствует подтвержденной цене по торгу
		assertEquals(bargain.getLastPrice().intValue(), group.getFinalAmountWithoutDeliveryCost().intValue());

		BigDecimal finalAmount = group.getFinalAmount();

		//Цена в корзине соответствует подтвержденной цене по офферу
		assertEquals(
				bargain.getLastPrice().intValue(),
				finalAmount.subtract(deliveryCostService.getDefaultCost()).intValue()
		);

		commitTransaction();

		OrderService.InitOrderResult holdResult = cartTestSupport.holdWithSetAddressEndpoint(
				CartTestSupport.HOLD_V2_ENDPOINT,
				getSeller().getId(),
				null
		);
		Long orderId = holdResult.getOrderId();

		startNewTransaction();

		Order order = orderRepository.findById(orderId).orElse(null);

		if (order == null) {
			fail("Заказ должен был быть найден");
		}

		BigDecimal orderFinalAmount = order.getFinalAmount();
		int expectedCostWithOfferAndDelivery = bargain.getLastPrice() + deliveryCostService.getDefaultCost().intValue();

		assertEquals(expectedCostWithOfferAndDelivery, orderFinalAmount.intValue());

		//Убеждаемся, что позиция заказа содержит ссылку на контрторг
		OrderPosition orderPosition = order.getOrderPositions().get(0);
		assertEquals(productId2, orderPosition.getProductItem().getProduct().getId());
		assertNotNull(orderPosition.getBargain());
		assertEquals(bargain.getId(), orderPosition.getBargain().getId());

		//Убеждаемся, что теперь контрторг потреблен
		BargainDetailedDTO bargainForBuyer = getDetailedBargainOK(buyerClient, getBargainDetailedUrl(bargain.getId()));
		assertSame(BargainStateDTO.Enum.CONSUMED, bargainForBuyer.getState().getName());
		commitAndStartNewTransaction();

		//Убеждаемся, что уведомление более не требует действия (действие выполнено)
		assertNotificationCreatedAndLinkedToBargain(
				getBuyer(),
				BargainBuyerConfirmedNotification.class,
				bargain.getId(),
				true,
				true,
				true
		);

		assertEquals(sellerReceivesSum, bargainService.getSellerReceivesSum(bargain).intValue());
	}

	/**
	 * Добавление товара с каналом продажи "Бутик и сайт" в корзину и его последуюшая покупка.
	 */
	@Test
	@Transactional
	public void _12_03_bargainPriceForProductWithBoutiqueAndWebsiteSalesChannel() {
		BigDecimal priceWithCommission = BigDecimal.valueOf(20000L);
		Product product = productRepository.findById(productId2).orElse(null);

		if (product == null) {
			fail("Товар должен был быть найден");
		}

		product.setSalesChannel(SalesChannel.BOUTIQUE_AND_WEBSITE);
		product.setCurrentPrice(priceWithCommission);
		product.setCurrentPriceWithoutCommission(product.getCurrentPrice().multiply(new BigDecimal("0.6")));

		productRepository.save(product);
		commitAndStartNewTransaction();

		int bargainPrice = priceWithCommission.multiply(new BigDecimal("0.9")).intValue();

		setBargainState(bargainId2, BargainState.INITIAL, LocalDateTime.now());
		BargainDetailedDTO dto = addBargainRecordOK(
				buyerClient,
				bargainId2,
				BargainRecordTypeDTO.Enum.OFFER,
				bargainPrice
		);
		addBargainRecordOK(sellerClient, bargainId2, BargainRecordTypeDTO.Enum.ACCEPT, null);

		Bargain bargain = bargainRepository.findById(dto.getId()).orElse(null);

		if (bargain == null) {
			fail("Торг должен был быть найден");
		}

		BigDecimal priceWithoutCommission = product.getCurrentPriceWithoutCommission();
		int sellerReceivesSum = priceWithoutCommission.multiply(new BigDecimal("0.9")).intValue();

		//Добавляем товар в корзину
		GroupedCart groupedCart = cartTestSupport.addToCartSuccessful(productId2, sizeId2, 1);

		assertEquals(1, groupedCart.getGroups().size());

		OrderDTO group = groupedCart.getGroups().get(0);

		//В группе одна позиция
		assertEquals(1, group.getItems().size());
		OrderPositionDTO orderPositionDTO = group.getItems().get(0);
		//Товар и размер совпадают
		assertEquals(productId2.longValue(), orderPositionDTO.getProductId());
		assertEquals(sizeId2.longValue(), orderPositionDTO.getSize().getId());
		//Цена позиции соответствует последней предложенной по торгу
		assertEquals(bargain.getLastPrice().intValue(), orderPositionDTO.getFinalAmount().intValue());
		//Цена в группе соответствует подтвержденной цене по торгу
		assertEquals(bargain.getLastPrice().intValue(), group.getFinalAmountWithoutDeliveryCost().intValue());

		BigDecimal finalAmount = group.getFinalAmount();

		//Цена в корзине соответствует подтвержденной цене по офферу
		assertEquals(
				bargain.getLastPrice().intValue(),
				finalAmount.subtract(deliveryCostService.getDefaultCost()).intValue()
		);

		commitTransaction();

		OrderService.InitOrderResult holdResult = cartTestSupport.holdWithSetAddressEndpoint(
				CartTestSupport.HOLD_V2_ENDPOINT,
				getSeller().getId(),
				null
		);
		Long orderId = holdResult.getOrderId();

		startNewTransaction();

		Order order = orderRepository.findById(orderId).orElse(null);

		if (order == null) {
			fail("Заказ должен был быть найден");
		}

		BigDecimal orderFinalAmount = order.getFinalAmount();
		int expectedCostWithOfferAndDelivery = bargain.getLastPrice() + deliveryCostService.getDefaultCost().intValue();

		assertEquals(expectedCostWithOfferAndDelivery, orderFinalAmount.intValue());

		//Убеждаемся, что позиция заказа содержит ссылку на контрторг
		OrderPosition orderPosition = order.getOrderPositions().get(0);
		assertEquals(productId2, orderPosition.getProductItem().getProduct().getId());
		assertNotNull(orderPosition.getBargain());
		assertEquals(bargain.getId(), orderPosition.getBargain().getId());

		//Убеждаемся, что теперь контрторг потреблен
		BargainDetailedDTO bargainForBuyer = getDetailedBargainOK(buyerClient, getBargainDetailedUrl(bargain.getId()));
		assertSame(BargainStateDTO.Enum.CONSUMED, bargainForBuyer.getState().getName());
		commitAndStartNewTransaction();

		//Убеждаемся, что уведомление более не требует действия (действие выполнено)
		assertNotificationCreatedAndLinkedToBargain(
				getBuyer(),
				BargainBuyerConfirmedNotification.class,
				bargain.getId(),
				true,
				true,
				true
		);

		assertEquals(sellerReceivesSum, bargainService.getSellerReceivesSum(bargain).intValue());
	}

	/**
	 * Очистка данных после теста
	 */
	@Test
	@Transactional
	public void _99_finalize() {
		restoreOriginalProductSellers();
		restoreOriginalProductBeegzConciergeStates();
		restoreOriginalSellerType();
		cleanup();
	}

	//Проверка наличия информации о торге в детализированной выдаче о товаре в каталоге
	private void assertCatalogProductContainsBargainInfo(@NonNull OfferStatus expectedOfferStatus, boolean negotiated){
		//Запрашиваем развернутую карточку товара из каталога
		ProductDTO productDTO = catalogTestSupport.getProductSuccessful(productId, false);
		//DTO товара содержит информацию о контрторге
		assertProductDTOContainsBargain(productDTO, sizeId, expectedOfferStatus, lastBargainPrice, negotiated);
	}

	//Товар содержит инфу о торге
	private void assertProductDTOContainsBargain(@NonNull ProductDTO productDTO, @NonNull Long sizeId, @NonNull OfferStatus expectedStatus, @NonNull Integer expectedPrice, boolean negotiated){
		SizeValueDTO size = productDTO.getSizes().stream().filter(s -> s.getId().equals(sizeId)).findFirst().orElseThrow(() -> new OskellyTestException("Размер не найден"));
		//Размер в выдаче содержит инфу о торге
		assertNotNull(size.getOffer());
		OfferDTO offerDTO = size.getOffer();
		//Статус корректный
		assertSame(expectedStatus, offerDTO.getOfferStatus());
		//Цена корректная
		assertEquals(expectedPrice.intValue(), offerDTO.getPrice().intValue());
		//Согласованность цены
		if(negotiated) {
			assertNotNull(offerDTO.getNegotiatedPrice());
			assertEquals(expectedPrice.intValue(), offerDTO.getNegotiatedPrice().intValue());
		}
		else{
			assertNull(offerDTO.getNegotiatedPrice());
		}
	}

	//Проверяет содержимое сообщения последней записи
	private void checkLastRecordBargainMessage(BargainDetailedDTO bargain, String expectedMessage){
		//Выражение "%,d" разделяет тысячи символом NBSP, отличным от пробела
		//Для удобства тест-кейсов заменяем на пробел
		expectedMessage = expectedMessage.replace(" ", " ");

		BargainRecordDTO lastRecord = bargain.getRecords().get(bargain.getRecords().size() - 1);
		assertEquals(expectedMessage, lastRecord.getMessage());
	}

	private void setBargainState(@Nonnull Long bargainId, @Nonnull BargainState state, LocalDateTime time){
		Bargain b = bargainRepository.findById(bargainId).orElse(null);
		b.setState(state);
		if(time != null) b.setChangeTime(time);
		bargainRepository.save(b);
		commitAndStartNewTransaction();
	}

	private List<Long> getBargainIds(List<BargainLiteDTO> bargains){
		return bargains.stream().map(b -> b.getId()).collect(Collectors.toList());
	}

	public void sendAnotherOffer_buyer_failed() {
		sendOfferFailed(buyerClient, bargainId, BargainRecordTypeDTO.Enum.OFFER, (int) (initialProductPrice * 0.81), "У вас нет прав на выполнение данного действия");
	}

	public void addWrongRecordAfterBuyersRecord_buyer_failed() {
		sendOfferFailed(buyerClient, bargainId, BargainRecordTypeDTO.Enum.CREATION, (int) (initialProductPrice * 0.81), "У вас нет прав на выполнение данного действия");
		sendOfferFailed(buyerClient, bargainId, BargainRecordTypeDTO.Enum.ACCEPT, null, "У вас нет прав на выполнение данного действия");
		sendOfferFailed(buyerClient, bargainId, BargainRecordTypeDTO.Enum.DECLINE, null, "У вас нет прав на выполнение данного действия");
	}

	public void addWrongRecordAfterSellersRecord_buyer_failed() {
		sendOfferFailed(buyerClient, bargainId, BargainRecordTypeDTO.Enum.CREATION, (int) (initialProductPrice * 0.81), "У вас нет прав на выполнение данного действия");
		sendOfferFailed(buyerClient, bargainId, BargainRecordTypeDTO.Enum.ACCEPT, null, "Для проставления отметки о покупке добавьте товар в корзину и оформите заказ");
		sendOfferFailed(buyerClient, bargainId, BargainRecordTypeDTO.Enum.DECLINE, null, "У вас нет прав на выполнение данного действия");
	}

	/**
	 * Удачное создание торга с доп проверками
	 * index - порядковый номер товара в выборке
	 */
	private long createBargainWithChecks_OK(int index) {
		Product product = getAnyPublishedProduct(index);
		//Подменяем продавца для товара на своего, сохраняя оригинал для восстановления
		setProductSellerAndSaveOriginal(product, sellerId);
		Size size = product.getAvailableProductItems().get(0).getSize();
		int basePrice = product.getCurrentPrice().multiply(new BigDecimal(0.8)).intValue();
		commitAndStartNewTransaction();

		BargainDetailedDTO bargain = createBargainOK(product, size, basePrice);

		//Проверяем корректность дат
		checkBargainDatesAreFresh(bargain);

		//Проверки для покупателя //////////////////////////////////////////////////////////

		//Проверка кол-ва оставшихся попыток
		assertEquals(2, bargain.getAttemptsLeft().intValue());

		//Проверка сообщения последней записи
		checkLastRecordBargainMessage(bargain, String.format("Я предлагаю %,d ₽ \uD83D\uDE00", basePrice));

		//Проверка наличия сумма и сообщения о сумме для продавца в последней записи
		checkLastRecordSellerReceivesSumAndMessage(bargain, false);

		//Проверка сообщения для покупателя
		assertNotNull(bargain.getMessage());
		TestUtils.assertStringContains(bargain.getMessage().getMessage(), "У продавца есть {timeLeft}, чтобы вам ответить");

		//Проверка прав покупателя по вновь созданному торгу (прав нет)
		assertUserRightsCorrect(buyerClient, bargain.getId(), null);

		//Проверяем доступность контрторга по id контрторга для покупателя
		//Результат тот же
		assertEquals(bargain, getDetailedBargainOK(buyerClient, getBargainDetailedUrl(bargain.getId())).setTimeLeft(bargain.getTimeLeft()));

		//Проверяем доступность контрторга по id товара и размера для покупателя
		//Результат тот же
		assertEquals(bargain, getDetailedBargainOK(buyerClient, getBargainCheckUrl(product.getId(), size.getId())).setTimeLeft(bargain.getTimeLeft()));

		//Проверяем доступность контрторга по id товара и размера через роут шаблона для покупателя
		//Результат тот же
		assertEquals(bargain, getDetailedBargainOK(buyerClient, getBargainOrTemplateUrl(product.getId(), size.getId())).setTimeLeft(bargain.getTimeLeft()));

		//Проверяем доступность контрторга в полном списке исходящих контрторгов для покупателя
		assertTrue(outgoingPageContainsBargain(buyerClient, null, bargain.getId()));

		if(index > 0) { //Добавлено более одного контрторга
			//Проверяем сортировку в списке

			//По нарастающей дате создания (последняя запись внизу)
			assertTrue(pageContainsBargain(buyerClient, getOutgoingBargainsUrl() + "?sort=" + BargainSortDTO.Enum.CREATE_TIME, bargain.getId(), index));
			assertFalse(pageContainsBargain(buyerClient, getOutgoingBargainsUrl() + "?sort=" + BargainSortDTO.Enum.CREATE_TIME, bargain.getId(), 0));

			//По убывающей дате создания (последняя запись наверху)
			assertTrue(pageContainsBargain(buyerClient, getOutgoingBargainsUrl() + "?sort=" + BargainSortDTO.Enum.CREATE_TIME_DESC, bargain.getId(), 0));
			assertFalse(pageContainsBargain(buyerClient, getOutgoingBargainsUrl() + "?sort=" + BargainSortDTO.Enum.CREATE_TIME_DESC, bargain.getId(), index));
		}

		//Проверяем доступность контрторга в списке исходящих контрторгов для покупателя по статусу OFFER
		assertTrue(outgoingPageContainsBargain(buyerClient, getStateList(BargainStateDTO.Enum.OFFER), bargain.getId()));

		//Проверяем доступность контрторга в списке исходящих активных контрторгов для покупателя
		assertTrue(outgoingActivePageContainsBargain(buyerClient, bargain.getId()));

		//Проверяем недоступность контрторга в списке исходящих контрторгов для покупателя по статусам, отличным от OFFER
		for(BargainStateDTO.Enum state : BargainStateDTO.Enum.values()){
			if(state == BargainStateDTO.Enum.OFFER) continue;
			assertFalse(outgoingPageContainsBargain(buyerClient, getStateList(state), bargain.getId()));
		}

		//Проверяем недоступность контрторга в списке завершенных исходящих контрторгов для покупателя
		assertFalse(outgoingFinishedPageContainsBargain(buyerClient, bargain.getId()));

		//Проверяем недоступность контрторга в полном списке входящих контрторгов для покупателя
		assertFalse(incomingPageContainsBargain(buyerClient, null, bargain.getId()));

		//Проверяем недоступность контрторга в полном списке входящих контрторгов для покупателя по всем статусам
		for(BargainStateDTO.Enum state : BargainStateDTO.Enum.values()) {
			assertFalse(incomingPageContainsBargain(buyerClient, getStateList(state), bargain.getId()));
		}

		//Проверяем счетчики
		assertBubblesCorrect(buyerClient, new BargainBubblesDTO().setOutgoingActive(1 + index).setOutgoingTotal(1 + index).setTotal(1 + index));

		/////////////////////Проверки для продавца //////////////////////////////////////////////////////////

		BargainDetailedDTO bargainForSeller = getDetailedBargainOK(sellerClient, getBargainDetailedUrl(bargain.getId()));

		//Проверка наличия сумма и сообщения о сумме для продавца в последней записи
		checkLastRecordSellerReceivesSumAndMessage(bargainForSeller, true);

		//Проверка кол-ва оставшихся попыток
		assertEquals(2, bargainForSeller.getAttemptsLeft().intValue());

		//Проверка сообщения для продавца
		assertNotNull(bargainForSeller.getMessage());
		TestUtils.assertStringContains(bargainForSeller.getMessage().getMessage(), "Предложение действительно: {timeLeft}");

		//Проверка прав продавца по вновь созданному торгу
		assertUserRightsCorrect(sellerClient, bargain.getId(), BargainRecordTypeDTO.Enum.ACCEPT, BargainRecordTypeDTO.Enum.DECLINE, BargainRecordTypeDTO.Enum.OFFER);

		//Проверяем доступность контрторга по id контрторга для продавца
		//ID контрторга совпадает
		assertEquals(bargain.getId(), bargainForSeller.getId());

		//Проверяем недоступность контрторга по id товара и размера для продавца
		getDetailedBargainFailed(sellerClient, getBargainCheckUrl(product.getId(), size.getId()), "Контрторг не найден");

		//Проверяем недоступность контрторга по id товара и размера через роут шаблона для продавца
		getDetailedBargainFailed(sellerClient, getBargainOrTemplateUrl(product.getId(), size.getId()), "Нельзя торговаться по своему товару");

		//Проверяем недоступность контрторга в списке исходящих контрторгов для продавца
		assertFalse(outgoingPageContainsBargain(sellerClient, null, bargain.getId()));

		//Проверяем недоступность контрторга в списке исходящих контрторгов для продавца по всем статусам
		for(BargainStateDTO.Enum state : BargainStateDTO.Enum.values()) {
			assertFalse(outgoingPageContainsBargain(sellerClient, getStateList(state), bargain.getId()));
		}

		//Проверяем доступность контрторга в списке входящих контрторгов для продавца
		assertTrue(incomingPageContainsBargain(sellerClient, null, bargain.getId()));

		//Проверяем доступность контрторга в списке входящих контрторгов для продавца по статусу OFFER
		assertTrue(incomingPageContainsBargain(sellerClient, getStateList(BargainStateDTO.Enum.OFFER), bargain.getId()));

		//Проверяем доступность контрторга в списке активных входящих контрторгов для продавца
		assertTrue(incomingActivePageContainsBargain(sellerClient, bargain.getId()));

		//Проверяем недоступность контрторга в списке входящих контрторгов для продавца по статусам, отличным от OFFER
		for(BargainStateDTO.Enum state : BargainStateDTO.Enum.values()) {
			if (state == BargainStateDTO.Enum.OFFER) continue;
			assertFalse(incomingPageContainsBargain(sellerClient, getStateList(state), bargain.getId()));
		}

		//Проверяем недоступность контрторга в списке завершенных входящих контрторгов для продавца
		assertFalse(incomingFinishedPageContainsBargain(sellerClient, bargain.getId()));

		//Проверяем счетчики
		assertBubblesCorrect(sellerClient, new BargainBubblesDTO().setIncomingActive(1 + index).setIncomingTotal(1 + index).setTotal(1 + index));

		//Продавцу пришло уведомление о предложении цены от покупателя
		assertNotificationCreatedAndLinkedToBargain(getSeller(), BargainSellerNewOfferNotification.class, bargain.getId(), true, false, true);

		///////////////////////////////////

		//Если это первый торг, то сохраняем ID товара, размера, контрторга для последующих тестов
		if(index == 0) {
			productId = product.getId();
			sizeId = size.getId();
			bargainId = bargain.getId();
			initialBargainOfferedPrice = basePrice;
			lastBargainPrice = basePrice;
		}
		else if(index == 1) {
			productId2 = product.getId();
			sizeId2 = size.getId();
			bargainId2 = bargain.getId();
		}

		return bargain.getId();
	}

	//Очистка базы
	private void cleanup(){
		//Чистим все торги для покупателя
		deleteBuyerBargains(buyerId);
		//Чистим все торги для продавца
		deleteSellerBargains(sellerId);

		//Чистим все уведомления для покупателя
		TestUtils.deleteUserNotifications(buyerId, notificationRepository);
		//Чистим все уведомления для продавца
		TestUtils.deleteUserNotifications(sellerId, notificationRepository);

		//Чистим бан для покупателя
		deleteUserBans(buyerId);

		//Чистим корзину
		cartTestSupport.cleanup();
	}

	//Удаление банов покупателя
	public void deleteUserBans(Long buyerId) {
		jdbcTemplate.execute("delete from user_ban where user_id = " + buyerId);
	}

	//Удаление торгов покупателя
	public void deleteBuyerBargains(Long buyerId) {
		jdbcTemplate.execute("delete from bargain where buyer_id = " + buyerId);
	}

	//Удаление торгов продавца
	public void deleteSellerBargains(Long sellerId) {
		jdbcTemplate.execute("delete from bargain where product_id in (select id from product where seller_id = " + sellerId + ")");
	}

	//Восстановление оригинальных продавцов товаров
	private void restoreOriginalProductSellers(){
		originalProductSellerIdsMap.forEach(this::setProductSeller);
	}

	private void restoreOriginalProductBeegzConciergeStates(){
		Product product = productRepository.getOne(productId);
		product.setBeegzStatusTime(null);
		product.setSelectedConciergeTime(null);
		productRepository.save(product);
		commitAndStartNewTransaction();
	}

	private void restoreOriginalSellerType(){
		User seller = userService.getUserByEmail(sellerEmail);
		seller.setSellerType(null);
		userService.save(seller);
		commitAndStartNewTransaction();
	}

	//Подмена продавца для товара
	private void setProductSeller(Long productId, Long sellerId){
		jdbcTemplate.execute("update product set seller_id=" + sellerId + " where id = " + productId);
	}

	//Смена цены товара
	private void setProductPrice(long productId, int newPrice){
		Product product = productRepository.getOne(productId);
		product.setCurrentPrice(new BigDecimal(newPrice));
		productRepository.save(product);
		commitAndStartNewTransaction();
	}

	private void setProductCustomCommission(long productId, BigDecimal newPriceForBuyer, BigDecimal newPriceForSeller) {
		Product product = productRepository.getOne(productId);

		product.setCustomCommission(true);
		product.setCurrentPrice(newPriceForBuyer);
		product.setCurrentPriceWithoutCommission(newPriceForSeller);

		productRepository.save(product);
		commitAndStartNewTransaction();
	}

	//Подмена продавца для товара с сохранением оригинала
	private void setProductSellerAndSaveOriginal(Product product, Long sellerId){
		//Для начала удалим торги старого продавца товара, чтобы они не перешли к нашему новому продавцу и не испортили тест
		//Если, конечно, он отличается от нового
		if(!product.getSeller().getId().equals(sellerId))
			deleteSellerBargains(product.getSeller().getId());
		//Сохраняем инфу о прежнем продавце для восстановления
		originalProductSellerIdsMap.put(product.getId(), productRepository.getOne(product.getId()).getSeller().getId());
		//Устанавливаем нового продавца
		setProductSeller(product.getId(), sellerId);
	}

	private Product getAnyPublishedProduct(int index){
		return getAnyProduct(ProductState.PUBLISHED, index);
	}

	private Product getAnyPublishedProduct(){
		return getAnyPublishedProduct(0);
	}

	//Получить какой-нибудь товар в интересующем статусе
	private Product getAnyProduct(ProductState state, int index){
		Product result = null;
		List<Product> products = productRepository.findTop10ByProductState(state);
		//Предпочтительно получить товары посвежее, чтобы исключить первые тестовые записи.
		products.sort((p1, p2) -> p2.getId().compareTo(p1.getId()));
		//Берем товар по переданному индексу
		//Если продукт-айтемов нет (такое бывает), то берем следующий
		for(int i = index; i < 10; i++){
			result = products.get(index);
			if(!result.getProductItems().isEmpty()){
				break;
			}
		}

		//Принудительно задаем цену, которая нам нужна для теста
		result.setCurrentPrice(new BigDecimal(initialProductPrice));
		Product product = productRepository.saveAndFlush(result);
		commitAndStartNewTransaction();
		return product;
	}

	//Ошибка создания контрторга
	private void createBargainFailed(Long productId, Long sizeId, Integer price, String expectedSubstring) {
		ResponseEntity<String> responseEntity = buyerClient.request(getServiceUrl(), null, HttpMethod.PUT, getBargainCreationParams(productId, sizeId, price), new ParameterizedTypeReference<String>() {}, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError() || responseEntity.getStatusCode().is5xxServerError());
		TestUtils.assertStringContains(responseEntity.getBody(), expectedSubstring);
	}

	//Ошибка отправки предложения по контрторгу покупателем или продавцом
	private void sendOfferFailed(ApiV2Client client, Long bargainId, BargainRecordTypeDTO.Enum type, Integer price, String expectedSubstring) {
		ResponseEntity<String> responseEntity = client.request(getBargainDetailedUrl(bargainId), null, HttpMethod.PUT, getBargainNewRecordParams(type, price), new ParameterizedTypeReference<String>() {}, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError() || responseEntity.getStatusCode().is5xxServerError());
		TestUtils.assertStringContains(responseEntity.getBody(), expectedSubstring);
	}


	//Удачное создание контрторга
	public BargainDetailedDTO createBargainOK(Product product, Size size, int price) {
		ResponseEntity<Api2Response<BargainDetailedDTO>> responseEntity = buyerClient.request(getServiceUrl(), null, HttpMethod.PUT,
				getBargainCreationParams(product.getId(), size.getId(), price),
				new ParameterizedTypeReference<Api2Response<BargainDetailedDTO>>() {}, true);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody());
		BargainDetailedDTO bargain = responseEntity.getBody().getData();
		assertNotNull(bargain);

		assertSame(BargainStateDTO.Enum.OFFER, bargain.getState().getName());
		assertEquals(price, bargain.getLastPrice().intValue());
		assertEquals(product.getCurrentPrice().longValue(), bargain.getBasePrice().intValue());

		assertEquals(2, bargain.getRecords().size());
		BargainRecordDTO record0 = bargain.getRecords().get(0);
		BargainRecordDTO record1 = bargain.getRecords().get(1);
		assertSame(BargainRecordFromTypeDTO.Enum.SELLER, record0.getFrom().getName());
		assertSame(BargainRecordFromTypeDTO.Enum.BUYER, record1.getFrom().getName());
		assertEquals(price, record1.getPrice().intValue());
		assertEquals(bargain.getLastPrice(), record1.getPrice());

		return bargain;
	}

	private void checkBargainDatesAreFresh(BargainLiteDTO bargain){
		//Проверка дат (должны попадать в диапазон от -3 минуты, до текущего момента
		assertTrue(TestUtils.dateIsFreshAndInCorrectOffset(bargain.getCreateTime()));
		assertTrue(TestUtils.dateIsFreshAndInCorrectOffset(bargain.getChangeTime()));
		if(bargain instanceof BargainDetailedDTO){
			for(BargainRecordDTO record : ((BargainDetailedDTO) bargain).getRecords()){
				assertTrue(TestUtils.dateIsFreshAndInCorrectOffset(record.getTime()));
			}
		}
	}

	//Успешное добавление новой записи к контрторгу покупателем или продавцом
	private BargainDetailedDTO addBargainRecordOK(ApiV2Client client, Long bargainId, BargainRecordTypeDTO.Enum type, Integer price){
		ResponseEntity<Api2Response<BargainDetailedDTO>> responseEntity = client.request(getBargainDetailedUrl(bargainId), null, HttpMethod.PUT,
				getBargainNewRecordParams(type, price),
				new ParameterizedTypeReference<Api2Response<BargainDetailedDTO>>() {}, true);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody());
		BargainDetailedDTO bargain = responseEntity.getBody().getData();
		assertNotNull(bargain);
		if(price != null) lastBargainPrice = price;
		return bargain;
	}

	//Удачное получение страницы контрторгов
	private Page<BargainLiteDTO> getBargainPageOK(ApiV2Client client, String url, Integer page, Integer pageSize) {
		if(page == null) page = 1;
		if(pageSize == null) pageSize = 20;
		ResponseEntity<Api2Response<Page<BargainLiteDTO>>> responseEntity = client.request(url, null, HttpMethod.GET, null,
				new ParameterizedTypeReference<Api2Response<Page<BargainLiteDTO>>>() {}, true);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody());
		return responseEntity.getBody().getData();
	}

	//Ошибка получения детализированного контрторга
	private void getDetailedBargainFailed(ApiV2Client client, String url, String expectedSubstring) {
		ResponseEntity<String> responseEntity = client.request(url, null, HttpMethod.GET, null, new ParameterizedTypeReference<String>() {}, true);
		assertTrue(responseEntity.getStatusCode().is4xxClientError());
		TestUtils.assertStringContains(responseEntity.getBody(), expectedSubstring);
	}

	//Удачное получение детализированного контрторга
	private BargainDetailedDTO getDetailedBargainOK(ApiV2Client client, String url) {
		ResponseEntity<Api2Response<BargainDetailedDTO>> responseEntity = client.request(url, null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<BargainDetailedDTO>>() {}, true);
		assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
		assertNotNull(responseEntity.getBody().getData());
		return responseEntity.getBody().getData();
	}

    //Удачное получение счетчиков по контрторгам
    private BargainBubblesDTO getBargainBubblesOK(ApiV2Client client) {
        ResponseEntity<Api2Response<BargainBubblesDTO>> responseEntity = client.request(getBargainBubblesUrl(), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<BargainBubblesDTO>>() {}, true);
        assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
        assertNotNull(responseEntity.getBody().getData());
        return responseEntity.getBody().getData();
    }

    //Проверяет корректность счетчиков для покупателя или продавца
    private void assertBubblesCorrect(ApiV2Client client, BargainBubblesDTO expectedBubbles){
	    assertEquals(expectedBubbles, getBargainBubblesOK(client));
    }

	//Проверка прав действий (добавления новых записей) для покупателя или продавца по существующему контрторгу
	private void assertUserRightsCorrect(ApiV2Client client, Long bargainId, BargainRecordTypeDTO.Enum... rights){
		BargainDetailedDTO bargain = getDetailedBargainOK(client, getBargainDetailedUrl(bargainId));
		assertUserRightsCorrect(bargain, rights);
	}

	//Проверка прав действий (добавления новых записей) для покупателя или продавца по существующему контрторгу
	private void assertUserRightsCorrect(BargainDetailedDTO bargain, BargainRecordTypeDTO.Enum... rights){
		Set<BargainRecordTypeDTO.Enum> rightsSet = rights == null
				? Collections.emptySet()
				: new HashSet(Arrays.asList(rights));
		assertEquals(rightsSet, new HashSet(bargain.getUserCan()));
	}

	//Список содержит торг с указанным id
	private boolean listContainsBargain(List<BargainLiteDTO> bargains, long bargainId){
		return bargains.stream().map(BargainLiteDTO::getId).collect(Collectors.toList()).contains(bargainId);
	}

	//Список содержит торг с указанным id в конкретном индексе
	private boolean listContainsBargainAtIndex(List<BargainLiteDTO> bargains, long bargainId, int index){
		return bargains.get(index).getId().equals(bargainId);
	}

	//Страница содержит торг
	private boolean pageContainsBargain(ApiV2Client client, String url, long bargainId){
		return listContainsBargain(getBargainPageOK(client, url, null, null).getItems(), bargainId);
	}

	//Страница содержит торг по конкретному индексу
	private boolean pageContainsBargain(ApiV2Client client, String url, long bargainId, int index){
		return listContainsBargainAtIndex(getBargainPageOK(client, url, null, null).getItems(), bargainId, index);
	}

	//Страаница входящих содержит торг
	private boolean incomingPageContainsBargain(ApiV2Client client, List<BargainStateDTO.Enum> states, long bargainId){
		return pageContainsBargain(client, getIncomingBargainsUrl(states), bargainId);
	}

	//Страаница активных входящих содержит торг
	private boolean incomingActivePageContainsBargain(ApiV2Client client, long bargainId){
		return pageContainsBargain(client, getIncomingActiveBargainsUrl(), bargainId);
	}

	//Страаница завершенных входящих содержит торг
	private boolean incomingFinishedPageContainsBargain(ApiV2Client client, long bargainId){
		return pageContainsBargain(client, getIncomingFinishedBargainsUrl(), bargainId);
	}

	//Страаница исходящих содержит торг
	private boolean outgoingPageContainsBargain(ApiV2Client client, List<BargainStateDTO.Enum> states, long bargainId){
		return pageContainsBargain(client, getOutgoingBargainsUrl(states), bargainId);
	}

	//Страаница активных исходящих содержит торг
	private boolean outgoingActivePageContainsBargain(ApiV2Client client, long bargainId){
		return pageContainsBargain(client, getOutgoingActiveBargainsUrl(), bargainId);
	}

	//Страаница завершенных исходящих содержит торг
	private boolean outgoingFinishedPageContainsBargain(ApiV2Client client, long bargainId){
		return pageContainsBargain(client, getOutgoingFinishedBargainsUrl(), bargainId);
	}

	//Проверка наличия сумма и сообщения о сумме для продавца в последней записи
	public void checkLastRecordSellerReceivesSumAndMessage(BargainDetailedDTO bargain, boolean shouldContain){
		BargainRecordDTO lastRecord = bargain.getRecords().get(bargain.getRecords().size() - 1);
		assertEquals(shouldContain, lastRecord.getSellerReceivesSum() != null);
		assertEquals(shouldContain, lastRecord.getSellerReceivesSumMessage() != null);
	}

	//Подготовить параметры для создания контрторга
	private MultiValueMap<String, Object> getBargainCreationParams(Long productId, Long sizeId, Integer price){
		return new LinkedMultiValueMap<String, Object>(){{
			if(productId != null) add("productId", "" + productId);
			if(sizeId != null) add("sizeId", "" + sizeId);
			if(price != null) add("price", "" + price);
		}};
	}

	//Подготовить параметры для добавления записи в контрторг
	private MultiValueMap<String, Object> getBargainNewRecordParams(BargainRecordTypeDTO.Enum type, Integer price){
		return new LinkedMultiValueMap<String, Object>(){{
			if(type != null) add("type", "" + type);
			if(price != null) add("price", "" + price);
		}};
	}

	private List<BargainStateDTO.Enum> getStateList(BargainStateDTO.Enum state){
		List<BargainStateDTO.Enum> lst = new ArrayList<>();
		lst.add(state);
		return lst;
	}

	//Проверка существования уведомления по торгу и его соответствия заданным параметрам
	private void assertNotificationCreatedAndLinkedToBargain(User user, Class<? extends BargainNotification> type, Long bargainId, boolean needAction, boolean actionCompleted, boolean hasLastBargainRecord){
		Bargain bargain = bargainRepository.findById(bargainId).orElseThrow(() -> new OskellyTestException("Контрторг не найден: " + bargainId));
		BargainNotification notification = (BargainNotification) TestUtils.getLastNotification(user, type, needAction, actionCompleted, notificationService);
		//Уведомление существует
		assertNotNull(notification);
		//Уведомление связано с нашим торгом
		assertEquals(bargain.getId(), notification.getBargain().getId());
		//Уведомление содержит последний шаг торга
		if(hasLastBargainRecord)
			assertEquals(bargain.getLastRecord().getId(), notification.getBargainRecord().getId());
		else
			assertNull(notification.getBargainRecord());
	}

	//Проверка существования уведомления и его соответствия заданным параметрам
	private void assertNotificationCreated(User user, Class<? extends Notification> type, boolean needAction, boolean actionCompleted, String expectedMessageSubstr){
		Notification notification = TestUtils.getLastNotification(user, type, needAction, actionCompleted, notificationService);
		//Уведомление существует
		assertNotNull(notification);
		//Уведомление содержит сообщение
		assertTrue(notification.getFullMessageText().contains(expectedMessageSubstr));
	}

}

