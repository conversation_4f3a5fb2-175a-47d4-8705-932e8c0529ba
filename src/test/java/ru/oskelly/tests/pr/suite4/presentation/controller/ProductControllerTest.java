package ru.oskelly.tests.pr.suite4.presentation.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.web.servlet.view.RedirectView;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.service.brand.BrandService;
import su.reddot.domain.service.comment.product.ProductCommentCountService;
import su.reddot.domain.service.deeplink.DeeplinkService;
import su.reddot.domain.service.filter.ProductMapper;
import su.reddot.domain.service.product.ProductPermissionsHandler;
import su.reddot.domain.service.product.ProductService;
import su.reddot.domain.service.staticresource.StaticResourceBalancer;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.security.token.UserIdAuthenticationToken;
import su.reddot.presentation.controller.ProductController;
import su.reddot.presentation.view.product.ProductView;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

@ExtendWith(MockitoExtension.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_04)
public class ProductControllerTest {

    @Mock
    private StaticResourceBalancer staticResourceBalancer;
    @Mock
    private ProductService productService;
    @Mock
    private ProductPermissionsHandler productPermissionsHandler;
    @Mock
    private ProductMapper productMapper;
    @Mock
    private BrandService brandService;
    @Mock
    private UserService userService;
    @Mock
    private ProductCommentCountService commentCountService;
    @Mock
    private DeeplinkService deeplinkService;
    @Mock
    private ApplicationEventPublisher pub;

    private MessageSourceAccessor messageSourceAccessor;

    private ProductController productController;

    @BeforeEach
    public void init() {
        productController = new ProductController(
                staticResourceBalancer,
                productService,
                productMapper,
                brandService,
                userService,
                commentCountService,
                deeplinkService,
                pub,
                messageSourceAccessor,
                productPermissionsHandler
        );
    }

    @Test
    public void getProductByIdRedirectsToUrlFromProduct() {
        Product product = mock(Product.class);
        String testUrl = "products/type-brand-1234";
        doReturn(testUrl).when(product).getUrl();
        doReturn(Optional.of(product)).when(productService).getRawProduct(any(), any());

        ProductView productView = mock(ProductView.class);
        Optional<ProductView> productViewOptional = Optional.of(productView);
        doReturn(productViewOptional).when(productService).getProductView(any(), any(), any());

        UserIdAuthenticationToken tokenMock = mock(UserIdAuthenticationToken.class);
        doReturn(123L).when(tokenMock).getUserId();
        RedirectView redirectView = productController.getProductById(0L, tokenMock);
        assertThat(redirectView.getUrl()).isEqualTo(testUrl);
    }
}
