package ru.oskelly.tests.pr.suite4.presentation.api.v2.admin;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.currency.CurrencyRateRepository;
import su.reddot.domain.model.currency.CurrencyRate;
import su.reddot.domain.service.currency.CurrencyService;
import su.reddot.domain.service.dto.currency.CurrencyRateDTO;
import su.reddot.domain.service.dto.currency.CurrencyRatesDTO;
import su.reddot.domain.service.dto.currency.UpdateCurrencyRateRequest;
import su.reddot.infrastructure.security.SecurityService;
import su.reddot.presentation.api.v2.Api2Response;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@AutoConfigureMockMvc
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_04)
public class AdminCurrencyControllerApiV2Test extends AbstractSpringTest {

    private final ObjectMapper mapper = new ObjectMapper();

    @Value("${test.api.ban-user-email}")
    private String email;
    @Value("${test.api.ban-user-password}")
    private String password;

    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private SecurityService securityService;

    @Autowired
    private CurrencyRateRepository currencyRateRepository;
    @Autowired
    private CurrencyService currencyService;

    @BeforeEach
    public void setUp() {
        securityService.authenticateByEmailAndRawPassword(email, password);
    }

    @Test
    public void getRatesShouldExcludeBaseCurrencyTest() throws Exception {
        List<CurrencyRate> existingRates = currencyRateRepository.findAll().stream()
                .filter(rate -> rate.getCurrency().getIsBase() == null || !rate.getCurrency().getIsBase())
                .collect(Collectors.toList());

        String responseJson = mockMvc.perform(get("/api/v2/admin/currency/rate"))
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();
        Api2Response<CurrencyRatesDTO> result = mapper.readValue(responseJson, new TypeReference<Api2Response<CurrencyRatesDTO>>() {
        });
        CurrencyRatesDTO data = result.getData();
        Assertions.assertThat(data).isNotNull();
        Assertions.assertThat(data.getRates())
                .isNotNull()
                .hasSameSizeAs(existingRates);
        Assertions.assertThat(data.getRates())
                .usingRecursiveComparison()
                .isEqualTo(existingRates);
    }

    @Test
    public void updateRateSuccessfulTest() throws Exception {
        int baseCurrencyIsoNumber = currencyService.getBaseCurrency().getIsoNumber();

        System.out.println("---------------- ALL RATES " + currencyRateRepository.findAll());

        CurrencyRate nonBaseCurrencyRate = currencyRateRepository.findAll().stream()
                .filter(rate -> (rate.getCurrency().getIsBase() == null || !rate.getCurrency().getIsBase())
                && rate.getCurrencyTo().getIsoNumber() == baseCurrencyIsoNumber)
                .findAny().orElseThrow(() -> new IllegalStateException("Unable to find rate"));

        BigDecimal oldCommission = nonBaseCurrencyRate.getCommission();
        BigDecimal newCommission = oldCommission.longValue() > 50
                ? oldCommission.subtract(BigDecimal.TEN)
                : oldCommission.add(BigDecimal.TEN);
        BigDecimal newRate = nonBaseCurrencyRate.getRateValue().add(BigDecimal.TEN);
        UpdateCurrencyRateRequest request = new UpdateCurrencyRateRequest()
                .setRateValue(newRate)
                .setCommission(newCommission);

        System.out.println("-------------------- nonBaseCurrencyRate " + nonBaseCurrencyRate);
        System.out.println("-------------------- request " + mapper.writeValueAsString(request));

        String responseJson = mockMvc.perform(post("/api/v2/admin/currency/rate/update")
                        .param("rateId", String.valueOf(nonBaseCurrencyRate.getId()))
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
                        .content(mapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();
        Api2Response<CurrencyRateDTO> result = mapper.readValue(responseJson, new TypeReference<Api2Response<CurrencyRateDTO>>() {
        });
        CurrencyRateDTO rate = result.getData();
        Assertions.assertThat(rate).isNotNull();
        Assertions.assertThat(rate.getRateValue()).isEqualTo(newRate);
        Assertions.assertThat(rate.getCommission()).isEqualTo(newCommission);

        Assertions.assertThat(rate)
                .usingRecursiveComparison()
                .ignoringFields("rateValue", "commission")
                .isEqualTo(nonBaseCurrencyRate);

        CurrencyRate afterUpdate = currencyRateRepository.findById(nonBaseCurrencyRate.getId())
                .orElseThrow(IllegalStateException::new);

        Assertions.assertThat(afterUpdate).isNotNull();
        Assertions.assertThat(afterUpdate.getRateValue()).isEqualTo(newRate);
        Assertions.assertThat(afterUpdate.getCommission()).isEqualTo(newCommission);
        Assertions.assertThat(afterUpdate.getLastRateUpdateTime()).isEqualToIgnoringMinutes(ZonedDateTime.now());


        Assertions.assertThat(afterUpdate)
                .usingRecursiveComparison()
                .ignoringFields("rateValue", "commission", "lastRateUpdateTime")
                .isEqualTo(nonBaseCurrencyRate);
    }

    @Test
    public void updateBaseCurrencyRateFailedTest() throws Exception {
        CurrencyRate baseCurrencyRate = currencyRateRepository.findAll().stream()
                .filter(rate -> rate.getCurrency().getIsBase())
                .findAny().orElseThrow(() -> new IllegalStateException("Unable to find base rate"));

        UpdateCurrencyRateRequest request = new UpdateCurrencyRateRequest()
                .setRateValue(baseCurrencyRate.getRateValue().add(BigDecimal.TEN))
                .setCommission(baseCurrencyRate.getCommission());

        String responseJson = mockMvc.perform(post("/api/v2/admin/currency/rate/update")
                        .param("rateId", String.valueOf(baseCurrencyRate.getId()))
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
                        .content(mapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andReturn().getResponse().getContentAsString();
        Api2Response<?> errorResponse = mapper.readValue(responseJson, Api2Response.class);
        Assertions.assertThat(errorResponse.getMessage())
                .isEqualTo("Cant update base currency rate " + baseCurrencyRate.getId());

        CurrencyRate after = currencyRateRepository.findById(baseCurrencyRate.getId())
                .orElseThrow(IllegalStateException::new);
        Assertions.assertThat(after)
                .usingRecursiveComparison()
                .isEqualTo(baseCurrencyRate);
    }

    @Test
    public void updateRequestValidationFailedTest() throws Exception {
        CurrencyRate rate = currencyRateRepository.findAll().stream()
                .filter(r -> r.getCurrency().getIsBase() == null || !r.getCurrency().getIsBase())
                .findAny().orElseThrow(() -> new IllegalStateException("Unable to find base rate"));

        UpdateCurrencyRateRequest request = new UpdateCurrencyRateRequest()
                .setRateValue(rate.getRateValue())
                .setCommission(BigDecimal.valueOf(100));

        String responseJson = mockMvc.perform(post("/api/v2/admin/currency/rate/update")
                        .param("rateId", String.valueOf(rate.getId()))
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
                        .content(mapper.writeValueAsString(request)))
                .andExpect(status().isUnprocessableEntity())
                .andReturn().getResponse().getContentAsString();
        Api2Response<?> errorResponse = mapper.readValue(responseJson, Api2Response.class);
        Assertions.assertThat(errorResponse.getValidationMessages().values().iterator().next())
                .contains("должно быть меньше или равно 99");

        CurrencyRate after = currencyRateRepository.findById(rate.getId())
                .orElseThrow(IllegalStateException::new);
        Assertions.assertThat(after)
                .usingRecursiveComparison()
                .isEqualTo(rate);
    }
}
