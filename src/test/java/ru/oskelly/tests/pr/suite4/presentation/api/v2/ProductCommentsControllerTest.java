package ru.oskelly.tests.pr.suite4.presentation.api.v2;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.amazonaws.services.s3.AmazonS3;
import lombok.Data;
import lombok.NonNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.transaction.TestTransaction;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.TestApiConfiguration;
import su.reddot.domain.dao.comment.CommentRepository;
import su.reddot.domain.dao.configparam.ConfigParamRepository;
import su.reddot.domain.dao.notification.NotificationRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.Comment;
import su.reddot.domain.model.configparam.ConfigParam;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.notification.comment.AnotherCommentNotification;
import su.reddot.domain.model.notification.comment.CommentNotification;
import su.reddot.domain.model.notification.comment.NewCommentNotification;
import su.reddot.domain.model.notification.comment.ReplyCommentNotification;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.comment.CommentView;
import su.reddot.domain.service.dto.CommentDTO;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.domain.service.dto.notification.NotificationDTO;
import su.reddot.domain.service.notification.NotificationService;
import su.reddot.domain.service.product.ProductInfoRequest;
import su.reddot.domain.service.product.ProductService;
import su.reddot.domain.service.setting.FeatureFlagsSettingService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.configparam.ConfigParamService;
import su.reddot.infrastructure.util.FileUtils;
import su.reddot.presentation.api.v2.comments.ProductCommentBase64ImagesData;
import su.reddot.presentation.api.v2.comments.ProductCommentData;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_04)
public class ProductCommentsControllerTest extends AbstractSpringTest {
    @Autowired
    private TestApiConfiguration testApiConfiguration;
    @Value("${test.api.user-email}")
    private String email;
    @Value("${test.api.user-password}")
    private String password;
	@Value("${test.api.user-id}")
	private Long userId;
	@Value("${test.api.user2-email}")
	private String email2;
	@Value("${test.api.user2-password}")
	private String password2;
	@Value("${test.api.user2-id}")
	private Long user2Id;
	@Value("${test.api.user3-id}")
	private Long user3Id;
	private String email3;
	private String password3;

	@Value("${test.temp-dir}")
	private String tmpDirPath;

	@Value("${resources.images.pathToDir}")
	private String imagesDirPath;

	@Value("${comments.maxImagesCount}")
	private int maxCommentsCount;

	@Value("${comments.imageMaxWidth}")
	private int imageMaxWidth;

	@Value("${comments.imageMaxHeight}")
	private int imageMaxHeight;

    private static String simpleCommentText = "Клевый костюм!";
	private static String oneImageCommentText = "Вот держите фотку:";
	private static String fewImageCommentText = "Вот еще несколько фоток:";
	private static String[] emptyTextVariants = {"", /*one space*/ " ", null, /*2 spaces*/ "  ", /*tab*/ " "};

	private static String imagesPath = "img/comment/";

	@Autowired
	private UserService userService;

	@Autowired
	private ProductService productService;

	@Autowired
	private NotificationService notificationService;

    @Autowired
    private ProductRepository productRepository;

    @Autowired
    private CommentRepository commentRepository;

    @Autowired
    private JdbcTemplate jdbcTemplate;

	@Autowired
	private NotificationRepository<Notification> notificationRepository;

	@Autowired
	private ObjectMapper objectMapper;

	@Autowired
	private AmazonS3 amazonS3client;

	@Autowired
	private ConfigParamRepository configParamRepository;

    private static Product product;

    private static ApiV2Client client;

	private static ApiV2Client sellerClient;

	private static ApiV2Client anotherUserClient;

    private String getServiceURL(){
        return testApiConfiguration.getServerUrl() + "/api/v2/comments";
    }

    public String getDeleteCommentURL(Long commentId) {
		return getServiceURL() + "/" + commentId;
    }

    private String getProductCommentsURL(@NonNull Long productId) { return getServiceURL() + "/product/" + productId; }

	private String getProductCommentsTreeURL(@NonNull Long productId) { return getServiceURL() + "/product/" + productId + "/tree"; }

	@BeforeEach
    public void initialize() {
        if(client == null){
        	//Обновляем пароль 3-го пользователя, который нам пригодится в тестах
        	User user3 = userService.getUserById(user3Id).orElse(null);
        	email3 = user3.getEmail();
        	password3 = "asdsdfd";
        	userService.setPassword(user3, password3);
        	userService.save(user3);

            client = new ApiV2Client(email, password);
	        sellerClient = new ApiV2Client(email2, password2);
	        anotherUserClient = new ApiV2Client(email3, password3);
            product = productRepository.findTop10ByProductState(ProductState.PUBLISHED).get(0);
            product.setSeller(userService.getUserById(user2Id).orElse(null));
            productRepository.saveAndFlush(product);
			amazonS3client.createBucket("oskelly");
			String configParamName = FeatureFlagsSettingService.ConfigFlag.ENABLE_PUBLISH_COMMENT_WITH_IMAGE.getUserListConfigParam();
			ConfigParam configParam = configParamRepository.findByName(configParamName);
			if (configParam == null) {
				configParam = new ConfigParam().setName(configParamName);
			}
			configParam
					.setValue("ALL")
					.setChangeTime(ZonedDateTime.now());
			configParamRepository.save(configParam);


			configParam = configParamRepository.findByName(ConfigParamService.CONFIG_PARAM_TEXT_CHECKER_SIMPLE_LIST);
			if (configParam == null) {
				configParam = new ConfigParam().setName(ConfigParamService.CONFIG_PARAM_TEXT_CHECKER_SIMPLE_LIST);
			}
			configParam
					.setValue("usachev,усачев")
					.setChangeTime(ZonedDateTime.now());
			configParamRepository.save(configParam);

			cleanup();
        }
    }

	@Test
	public void _00_noCommentsBeforeTests(){
		List<CommentDTO> comments = getProductCommentsSuccessful(client, product.getId());
		assertFalse(containsAnyStr(comments, simpleCommentText, oneImageCommentText, fewImageCommentText));
	}

	@Test
	public void _01_unauthorized_publishSimpleProductComment_accessDenied(){
		postCommentUnSuccessful(client, getSimpleProductCommentData(), "Доступ запрещен", false);
	}

	@Test
	public void _02_1_authorized_publishSimpleProductComment_success(){
		ProductCommentData request = getSimpleProductCommentData();
		CommentDTO commentDTO = postCommentSuccessful(client, request, true, new ExpectedNotificationDescription(NewCommentNotification.class, userId, user2Id));

		List<CommentDTO> comments = getProductCommentsSuccessful(client, product.getId());
		assertTrue(containsAnyStr(comments, simpleCommentText));
	}

	@Transactional
	@Test
	public void _02_3_anotherUserAddsComment(){
		User user = userService.getUserById(userId).orElse(null);
		TestTransaction.end();
		TestTransaction.start();

		NotificationService.NotificationBubbles bubblesBefore = notificationService.getBubbles(user, null);

		ProductCommentData anotherUserCommentRequest = getSimpleProductCommentData();

		anotherUserCommentRequest.setText("Расскажите побольше о товаре!");

		CommentDTO commentDTO = postCommentSuccessful(anotherUserClient, anotherUserCommentRequest, true, Arrays.asList(
				new ExpectedNotificationDescription(NewCommentNotification.class, user3Id, user2Id),
				new ExpectedNotificationDescription(AnotherCommentNotification.class, user3Id, userId)
		));
		TestTransaction.flagForCommit();
		TestTransaction.end();

		List<CommentDTO> comments = getProductCommentsSuccessful(client, product.getId());
		assertTrue(containsAnyStr(comments, anotherUserCommentRequest.getText()));

		getNotificationSuccessful(AnotherCommentNotification.class, product, commentDTO.getId(), user3Id, userId, false, false);

		NotificationService.NotificationBubbles bubblesAfter = notificationService.getBubbles(user, null);

		//Убеждаемся, что данное действие не повлияло на бабблы, не связанные с комментариями
		assertEquals(bubblesBefore.getNoCommentsNotRead(), bubblesAfter.getNoCommentsNotRead());
		assertEquals(bubblesBefore.getNoCommentsTotal(), bubblesAfter.getNoCommentsTotal());
	}

	@Test
	public void _03_publishEmptyComment_failed(){
		ProductCommentData request = getSimpleProductCommentData();
		for(String text : emptyTextVariants){
			request.setText(text);
			postCommentUnSuccessful(client, request, "Текст комментария не должен быть пустым!", false);
		}
	}

	@Test
	public void _04_publishOneImageProductComment_success() throws IOException{
		CommentDTO commentDTO = postCommentSuccessful(client, getOneImageProductCommentData(), false, Arrays.asList(
				new ExpectedNotificationDescription(NewCommentNotification.class, userId, user2Id),
				new ExpectedNotificationDescription(AnotherCommentNotification.class, userId, user3Id)
		));
		assertTrue(containsAnyStr(getProductCommentsSuccessful(client, product.getId()), oneImageCommentText));

		assertNotNull(commentDTO.getImages());
		assertSame(1, commentDTO.getImages().size());
		assertCommentImagesExist(commentDTO.getImages());
	}

	@Test
	public void _05_publishValidImagesProductComment_success() throws IOException{
		int imagesCount = maxCommentsCount;
		ProductCommentData request = getFewImagesProductCommentData(imagesCount);
		request.setText(request.getText() + imagesCount);
		CommentDTO commentDTO = postCommentSuccessful(client, request, false, Arrays.asList(
				new ExpectedNotificationDescription(NewCommentNotification.class, userId, user2Id),
				new ExpectedNotificationDescription(AnotherCommentNotification.class, userId, user3Id)
		));
		assertTrue(containsAnyStr(getProductCommentsSuccessful(client, product.getId()), fewImageCommentText + imagesCount));

		assertNotNull(commentDTO.getImages());
		assertSame(imagesCount, commentDTO.getImages().size());
		assertCommentImagesExist(commentDTO.getImages());
	}

	@Test
	public void _06_publishInvalidImagesProductComment_failed(){
		postCommentUnSuccessful(client, getFewImagesProductCommentData(maxCommentsCount + 1), "Нельзя прикрепить более " + maxCommentsCount + "-х изображений!", false);
	}

	@Transactional
	@Test
	public void _07_publishValidImagesProductComment_emptyText_success() throws IOException{
		int imagesCount = maxCommentsCount;
		ProductCommentData request = getFewImagesProductCommentData(imagesCount);

		for(String text : emptyTextVariants){
			request.setText(text);
			CommentDTO commentDTO = postCommentSuccessful(client, request, false, Arrays.asList(
					new ExpectedNotificationDescription(NewCommentNotification.class, userId, user2Id),
					new ExpectedNotificationDescription(AnotherCommentNotification.class, userId, user3Id)
			));
			assertNotNull(commentDTO.getImages());
			assertSame(imagesCount, commentDTO.getImages().size());
			assertCommentImagesExist(commentDTO.getImages());

			commitAndStartNewTransaction();

			NewCommentNotification notification = (NewCommentNotification) getNotificationSuccessful(NewCommentNotification.class, product, commentDTO.getId(), userId, user2Id, false, false);
			NotificationDTO notificationDTO = notificationService.getNotificationDTO(notification);
			assertNotNull(notificationDTO.getImages());
			assertSame(imagesCount, notificationDTO.getImages().size());
			assertCommentImagesExist(notificationDTO.getImages());
		}
	}

	//Удаление комментария приводит к удалению соотв. уведомлений
	@Test
	public void _08_deleteCommentsRemovesNotification(){
		//Получаем комментарии пользователя
		List<Comment> comments = commentRepository.findAllByPublisherId(userId);
		//Идентификаторы комментариев
		List<Long> commentIds = comments.stream().map(c -> c.getId()).collect(Collectors.toList());
		//Они есть
		assertFalse(comments.isEmpty());
		//Получаем соотв уведомления пользователя
		List<Notification> notifications = notificationRepository.findAllByUserAndDtype(user2Id, NewCommentNotification.class.getSimpleName());
		//Фильтруем уведомления, связанные с нашими комментариями
		List<NewCommentNotification> commentNotifications = notifications.stream().filter(n ->
			commentIds.contains(((NewCommentNotification) n).getComment().getId())
		).map(n -> (NewCommentNotification) n).collect(Collectors.toList());
		//Идентификаторы уведомлений, связанных с комментариями
		List<Long> commentNotificationIds = commentNotifications.stream().map(n -> n.getId()).collect(Collectors.toList());

		//Колиество комментариев и соотв. уведомлений совпадает
		assertEquals(commentIds.size(), commentNotificationIds.size());

		//Тянем комментарии по товару из АПИ
		List<CommentDTO> productComments = getProductCommentsSuccessful(client, product.getId());
		//Идентификаторы комментариев по товару
		List<Long> productCommentIds = productComments.stream().map(c -> c.getId()).collect(Collectors.toList());
		//Эти идентификаторы содержат комментарии нашего пользователя
		assertTrue(productCommentIds.containsAll(commentIds));

		//Пробуем удалять комментарии от имени продавца (не отправителя)
		for(Comment comment : comments){
			//Другой пользователь не может удалить комментарии
			deleteCommentForbidden(sellerClient, comment.getId(), true);
		}

		//Удаляем комментарии от имени автора
		for(Comment comment : comments){
			deleteCommentSuccessful(client, comment.getId(), true);
		}

		//Тянем комментарии по товару после удаления наших комментариев
		List<CommentDTO> productCommentsAfterRemovement = getProductCommentsSuccessful(client, product.getId());
		//Идентификаторы комментариев по товару
		List<Long> productCommentIdsAfterRemovement = productCommentsAfterRemovement.stream().map(c -> c.getId()).collect(Collectors.toList());

		//Комментарии по товару боле не содержат комментариев, которые были удалены
		for(Long commentId : commentIds) {
			assertFalse(productCommentIdsAfterRemovement.contains(commentId));
		}

		//Пытаемся выдернуть уведомления по удаленным уведомлениям
		//Получаем соотв уведомления пользователя
		List<Notification> notificationsAfterRemovement = notificationRepository.findAllByUserAndDtype(user2Id, NewCommentNotification.class.getSimpleName());
		//Фильтруем уведомления, связанные с нашими комментариями
		List<NewCommentNotification> commentNotificationsAfterRemovement = notificationsAfterRemovement.stream().filter(n ->
				commentIds.contains(((NewCommentNotification) n).getComment().getId())
		).map(n -> (NewCommentNotification) n).collect(Collectors.toList());

		//Уведомлений больше нет. Они были удалены вместе с комментариями
		assertTrue(commentNotificationsAfterRemovement.isEmpty());
	}

	//Проверка иерархичной выдачи уведомлений

	/**
	 * Тест на отображение комментариев в карточке товара. В диалоге участвуют 2 пользователя: A (продавец), B и С
	 *
	 * Структура комментариев в базе:
	 *
	 * B: Комментарий 1
	 * 	A: @B Комментарий 3
	 * 		B: @A Комментарий 4
	 * 			C: @B Комментарий 12
	 * 	B: @B Комментарий 11
	 * B: Комментарий 2
	 * 	A: @B Комментарий 6
	 * A: Комментарий 5
	 * B: Комментарий 7
	 * 	A: @B Комментарий 8
	 * 		A: @A Комментарий 9
	 * 			B: @A Комментарий 10
	 *
	 * Отображение последний 3-х комментариев до раскрытия:
	 *
	 * C: @B Комментарий 12
	 * B: @B Комментарий 11
	 * B: @A Комментарий 10
	 *
	 * Отображение комментариев в ленте:
	 *
	 * B: Комментарий 1
	 * 	A: @B Комментарий 3
	 * 	B: @A Комментарий 4
	 * 	B: @B Комментарий 11
	 * 	C: @B Комментарий 12   (12-й опускается под 11-й)
	 * B: Комментарий 2
	 * 	A: @B Комментарий 6
	 * A: Комментарий 5
	 * B: Комментарий 7
	 * 	A: @B Комментарий 8
	 * 	A: @A Комментарий 9
	 * 	B: @A Комментарий 10
	 *
	 *
	 * При удалении некорневого комментария 9, нижестоящая ветка сохраняется:
	 *
	 * B: Комментарий 1
	 * 	A: @B Комментарий 3
	 * 	B: @A Комментарий 4
	 * 	B: @B Комментарий 11
	 * 	C: @B Комментарий 12
	 * B: Комментарий 2
	 * 	A: @B Комментарий 6
	 * A: Комментарий 5
	 * B: Комментарий 7
	 * 	A: @B Комментарий 8
	 * 	B: @A Комментарий 10
	 *
	 * При удалении корневого комментария 1, нижестоящая ветка удаляется:
	 *
	 * B: Комментарий 2
	 * 	A: @B Комментарий 6
	 * A: Комментарий 5
	 * B: Комментарий 7
	 * 	A: @B Комментарий 8
	 * 	A: @A Комментарий 9
	 * 	B: @A Комментарий 10
	 */
	@Transactional
	@Test
	public void _09_01_commentsHierarchyTest(){
		commitTransaction();
		cleanup();

		Long aUserId = user2Id;
		Long bUserId = userId;
		Long cUserId = user3Id;

		ApiV2Client aClient = sellerClient;
		ApiV2Client bClient = client;
		ApiV2Client cClient = anotherUserClient;

		//Создаем иерархию комментариев
		CommentDTO comment1 = postCommentSuccessful(bClient, getSimpleProductCommentData(getCommentTxt(1), null), true, Arrays.asList(
				//Уведомление NewCommentNotification получит только продавец (aUser)
				new ExpectedNotificationDescription(NewCommentNotification.class, bUserId, aUserId)
		));
		CommentDTO comment2 = postCommentSuccessful(bClient, getSimpleProductCommentData(getCommentTxt(2), null), true, Arrays.asList(
				//Уведомление NewCommentNotification получит только продавец (aUser)
				new ExpectedNotificationDescription(NewCommentNotification.class, bUserId, aUserId)
		));
		CommentDTO comment3 = postCommentSuccessful(aClient, getSimpleProductCommentData(getCommentTxt(3), comment1.getId()), true, Arrays.asList(
				//Уведомление ReplyCommentNotification получит только bUser
				new ExpectedNotificationDescription(ReplyCommentNotification.class, aUserId, bUserId)
		));
		CommentDTO comment4 = postCommentSuccessful(bClient, getSimpleProductCommentData(getCommentTxt(4), comment3.getId()), true, Arrays.asList(
				//Уведомление ReplyCommentNotification получит только aUser (продавец)
				new ExpectedNotificationDescription(ReplyCommentNotification.class, bUserId, aUserId)
		));
		CommentDTO comment5 = postCommentSuccessful(aClient, getSimpleProductCommentData(getCommentTxt(5), null), true, Arrays.asList(
				//Уведомление AnotherCommentNotification получит только bUser
				new ExpectedNotificationDescription(AnotherCommentNotification.class, aUserId, bUserId)
		));
		CommentDTO comment6 = postCommentSuccessful(aClient, getSimpleProductCommentData(getCommentTxt(6),  comment2.getId()), true, Arrays.asList(
				//Уведомление ReplyCommentNotification получит только bUser
				new ExpectedNotificationDescription(ReplyCommentNotification.class, aUserId, bUserId)
		));
		CommentDTO comment7 = postCommentSuccessful(bClient, getSimpleProductCommentData(getCommentTxt(7),  null), true, Arrays.asList(
				//Уведомление NewCommentNotification получит только продавец (aUser)
				new ExpectedNotificationDescription(NewCommentNotification.class, bUserId, aUserId)
		));
		CommentDTO comment8 = postCommentSuccessful(aClient, getSimpleProductCommentData(getCommentTxt(8),  comment7.getId()), true, Arrays.asList(
				//Уведомление ReplyCommentNotification получит только bUser
				new ExpectedNotificationDescription(ReplyCommentNotification.class, aUserId, bUserId)
		));
		CommentDTO comment9 = postCommentSuccessful(aClient, getSimpleProductCommentData(getCommentTxt(9),  comment8.getId()), true, Arrays.asList(
				//Уведомление AnotherCommentNotification получит только bUser
				new ExpectedNotificationDescription(AnotherCommentNotification.class, aUserId, bUserId)
		));
		CommentDTO comment10 = postCommentSuccessful(bClient, getSimpleProductCommentData(getCommentTxt(10),  comment9.getId()), true, Arrays.asList(
				//Уведомление ReplyCommentNotification получит только продавец (aUser)
				new ExpectedNotificationDescription(ReplyCommentNotification.class, bUserId, aUserId)
		));
		CommentDTO comment11 = postCommentSuccessful(bClient, getSimpleProductCommentData(getCommentTxt(11),  comment1.getId()), true, Arrays.asList(
				//Уведомление NewCommentNotification получит только продавец (aUser)
				new ExpectedNotificationDescription(NewCommentNotification.class, bUserId, aUserId)
		));
		CommentDTO comment12 = postCommentSuccessful(cClient, getSimpleProductCommentData(getCommentTxt(12),  comment4.getId()), true, Arrays.asList(
				//Уведомление ReplyCommentNotification получит bUser
				new ExpectedNotificationDescription(ReplyCommentNotification.class, aUserId, bUserId),
				//Уведомление NewCommentNotification получит продавец (aUser)
				new ExpectedNotificationDescription(NewCommentNotification.class, bUserId, aUserId)
		));

		//Получаем карточку товара с последними 3-мя комментариями
		startNewTransaction();
		ProductDTO detailedProductDTO = productService.getDetailedProductDTO(product.getId(), ProductService.UserType.HUMAN, new ProductInfoRequest());
		List<CommentView> lastProductComments = detailedProductDTO.getComments();
		assertEquals(3, lastProductComments.size());
		assertEquals(comment12.getId(), lastProductComments.get(0).getId());
		assertEquals(comment11.getId(), lastProductComments.get(1).getId());
		assertEquals(comment10.getId(), lastProductComments.get(2).getId());
		commitTransaction();

		//Получаем дерево комментариев по API
		List<CommentDTO> commentsTree = getProductCommentsTreeSuccessful(client, product.getId());

		//Дерево содержит 4 ветки
		assertEquals(4, commentsTree.size());
		//Проверяем первый уровень
		for(int i = 0; i < 4; i++){
			CommentDTO firstLevelComment = commentsTree.get(i);
			assertNull(firstLevelComment.getParentCommentId()); //Родительского комментария нет
			assertNull(firstLevelComment.getReplyTo()); //replyTo пустое
		}
		CommentDTO treeComment1 = commentsTree.get(0);
		CommentDTO treeComment2 = commentsTree.get(1);
		CommentDTO treeComment5 = commentsTree.get(2);
		CommentDTO treeComment7 = commentsTree.get(3);
		assertEquals(comment1.getText(), treeComment1.getText());
		assertEquals(comment2.getText(), treeComment2.getText());
		assertEquals(comment5.getText(), treeComment5.getText());
		assertEquals(comment7.getText(), treeComment7.getText());

		//Сверяем содержимое первой ветки
		List<CommentDTO> branch1SubComments = commentsTree.get(0).getSubComments();
		assertEquals(4, branch1SubComments.size());
		for(CommentDTO subComment : branch1SubComments){
			assertNull(subComment.getSubComments()); //Иерархия заканчивается на 1-м уровне
		}
		CommentDTO treeComment3 = branch1SubComments.get(0);
		CommentDTO treeComment4 = branch1SubComments.get(1);
		CommentDTO treeComment11 = branch1SubComments.get(2);
		CommentDTO treeComment12 = branch1SubComments.get(3);
		assertEquals(comment3.getText(), treeComment3.getText());
		assertEquals(comment1.getPublisher().getNickname(), comment3.getReplyTo());
		assertEquals(comment4.getText(), treeComment4.getText());
		assertEquals(comment3.getPublisher().getNickname(), treeComment4.getReplyTo());
		assertEquals(comment11.getText(), treeComment11.getText());
		assertEquals(comment1.getPublisher().getNickname(), treeComment11.getReplyTo());
		assertEquals(comment12.getText(), treeComment12.getText());
		assertEquals(comment4.getPublisher().getNickname(), treeComment12.getReplyTo());

		//Сверяем содержимое второй ветки
		List<CommentDTO> branch2SubComments = commentsTree.get(1).getSubComments();
		assertEquals(1, branch2SubComments.size());
		for(CommentDTO subComment : branch2SubComments){
			assertNull(subComment.getSubComments()); //Иерархия заканчивается на 1-м уровне
		}
		CommentDTO treeComment6 = branch2SubComments.get(0);
		assertEquals(comment6.getText(), treeComment6.getText());
		assertEquals(comment2.getPublisher().getNickname(), treeComment6.getReplyTo());

		//Сверяем содержимое 5-й ветки (commentId = 5)
		//Эта ветка пустая
		List<CommentDTO> branch5SubComments = commentsTree.get(2).getSubComments();
		assertNull(branch5SubComments);

		//Сверяем содержимое 7-й ветки (commentId = 7)
		List<CommentDTO> branch7SubComments = commentsTree.get(3).getSubComments();
		assertEquals(3, branch7SubComments.size());
		for(CommentDTO subComment : branch7SubComments){
			assertNull(subComment.getSubComments()); //Иерархия заканчивается на 1-м уровне
		}
		CommentDTO treeComment8 = branch7SubComments.get(0);
		CommentDTO treeComment9 = branch7SubComments.get(1);
		CommentDTO treeComment10 = branch7SubComments.get(2);
		assertEquals(comment8.getText(), treeComment8.getText());
		assertEquals(comment7.getPublisher().getNickname(), treeComment8.getReplyTo());
		assertEquals(comment9.getText(), treeComment9.getText());
		assertEquals(comment8.getPublisher().getNickname(), treeComment9.getReplyTo());
		assertEquals(comment10.getText(), treeComment10.getText());
		assertEquals(comment9.getPublisher().getNickname(), treeComment10.getReplyTo());

		//Удаляем 9-й комментарий, не являющийся корневым, но имеющим один ответ.
		deleteCommentSuccessful(aClient, comment9.getId(), true);
		//Получаем дерево комментариев по API
		List<CommentDTO> commentsTreeAfterDeleted9comment = getProductCommentsTreeSuccessful(client, product.getId());
		//Дерево по прежнему содержит 4 ветки
		assertEquals(4, commentsTreeAfterDeleted9comment.size());
		//Но теперь в 7-й ветке у нас только 2 комментария - 8 и 10
		List<CommentDTO> branch7SubCommentsAfterDeleted9comment = commentsTreeAfterDeleted9comment.get(3).getSubComments();
		assertEquals(2, branch7SubCommentsAfterDeleted9comment.size());
		CommentDTO treeComment8AfterDeleted9comment = branch7SubCommentsAfterDeleted9comment.get(0);
		CommentDTO treeComment10AfterDeleted9comment = branch7SubCommentsAfterDeleted9comment.get(1);
		assertEquals(comment8.getText(), treeComment8AfterDeleted9comment.getText());
		assertEquals(comment7.getPublisher().getNickname(), treeComment8AfterDeleted9comment.getReplyTo());
		assertEquals(comment10.getText(), treeComment10.getText());
		assertEquals(comment9.getPublisher().getNickname(), treeComment10AfterDeleted9comment.getReplyTo());

		//Удаляем 1-й комментарий, являющийся корневым. Должна удалиться вся ветка.
		deleteCommentSuccessful(bClient, comment1.getId(), true);
		//Получаем дерево комментариев по API
		List<CommentDTO> commentsTreeAfterDeleted1comment = getProductCommentsTreeSuccessful(client, product.getId());
		//Дерево теперь содержит 3 ветки
		assertEquals(3, commentsTreeAfterDeleted1comment.size());

		/*Если запросить комментарии в виде списка (по старой схеме, без иерархии), то там мы увидим теперь только 6 комментариев:
		B: Комментарий 2
		A: Комментарий 5
		A: @B Комментарий 6
		B: Комментарий 7
		A: @B Комментарий 8
		B: @A Комментарий 10*/

		List<CommentDTO> flatProductCommentsAfterRemovement9and1 = getProductCommentsSuccessful(client, product.getId());
		assertEquals(6, flatProductCommentsAfterRemovement9and1.size());

		//Пытаемся добавить ответ к удаленному комментарию
		postCommentUnSuccessful(cClient, getSimpleProductCommentData(getCommentTxt(13),  comment1.getId()), "Комментарий, к которому вы оставляли комментарий был удален пользователем. Попробуйте оставить комментарий под товаром.",true);
	}

	private String getCommentTxt(int commentNumber){
		return "Комментарий " + commentNumber;
	}


	@Test
	public void _10_publishCommentWithEncodedImage() throws IOException {
		try {
			String data = "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7";
			cleanup();

			ProductCommentData c = getOneImageProductCommentData();
			ProductCommentBase64ImagesData comment = new ProductCommentBase64ImagesData();

			comment.setParentCommentId(c.getParentCommentId());
			comment.setText(c.getText());
			comment.setProductId(c.getProductId());
			comment.setImagesBase64(Arrays.asList(data));

			CommentDTO commentDTO = client
					.request(
						getServiceURL() + "/base64-images",
						null,
						HttpMethod.POST,
						MediaType.APPLICATION_JSON,
						objectMapper.writeValueAsString(comment),
						new ParameterizedTypeReference<CommentDTO>() {},
						true)
					.getBody();
			assertTrue(containsAnyStr(getProductCommentsSuccessful(client, product.getId()), oneImageCommentText));

			assertNotNull(commentDTO.getImages());
			assertSame(1, commentDTO.getImages().size());
			assertCommentImagesExist(commentDTO.getImages());
		}
		finally {
			cleanup();
		}
	}

	@Test
	public void _11_publishCommentWithXSS() {
		String badText =
				"<p><a href='http://oskelly.com' onclick='stealCookies()' onfocus='stealCookies()'>Click me</a></p>";
		String cleanText = "<p><a href=\"http://oskelly.com\">Click me</a></p>";
		ProductCommentData request = getSimpleProductCommentData(badText, null);
		CommentDTO commentDTO =
				postCommentSuccessful(client, request, true,
						Collections.singletonList(
								new ExpectedNotificationDescription(NewCommentNotification.class, userId, user2Id)),
						cleanText);
		assertThat(commentDTO.getText()).isEqualTo(cleanText);

		List<CommentDTO> comments = getProductCommentsSuccessful(client, product.getId());
		assertTrue(containsAnyStr(comments, cleanText));
	}

	@Test
	public void _12_publishCommentWithForbiddenWords() {
		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Напишите мне на whаtsарр +7 8888", null),
				"Текст комментария содержит запрещенные слова!", false);

		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Напишите мне на WНаts..арр +7 8888", null),
				"Текст комментария содержит запрещенные слова!", false);

		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Напишите мне на te1egra.mm +7 8888", null),
				"Текст комментария содержит запрещенные слова!", false);

		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Напишите мне на INstagram +7 8888", null),
				"Текст комментария содержит запрещенные слова!", false);

		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Здравствуйте, я хочу купить, но перед покупкой хотелось бы увидеть видео с состоянием, напишите мне на Whаtsарр", null),
				"Текст комментария содержит запрещенные слова!", false);

		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Здравствуйте, очень забрать хочу) напишите пожалуйста инст или тг, или мне Sofi_Portman", null),
				"Текст комментария содержит запрещенные слова!", false);

		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Здравствуйте, очень забрать хочу) напишите пожалуйста инст", null),
				"Текст комментария содержит запрещенные слова!", false);

		postCommentUnSuccessful(client,
				getSimpleProductCommentData("видимо не туда написали моя тeлeгa @kymmag", null),
				"Текст комментария содержит запрещенные слова!", false);

		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Спасибо ,не могу определиться ,так как нашла случайно новые шлёпанцы в приложении collect(ЦУМ) ,но там цвет рыжеватый)", null),
				"Текст комментария содержит запрещенные слова!", false);

		postCommentUnSuccessful(client,
				getSimpleProductCommentData("На цум коллект он не выставлен?", null),
				"Текст комментария содержит запрещенные слова!", false);

		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Здравствуйте, я хочу купить, но перед покупкой хотелось бы увидеть видео с состоянием, напишите мне в Tele gram: @kymmag\u202C Или WhatsApp \u202A \u202A******.215", null),
				"Текст комментария содержит запрещенные слова!", false);

		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Здравствуйте, меня заинтересовал ваш товар, хотела бы купить, но перед этим посмотреть состояние поближе, свяжитесь со мной \n"
						+ "\uD83C\uDD66\uD83C\uDD57\uD83C\uDD50\uD83C\uDD63\uD83C\uDD62 \uD83C\uDD50\uD83C\uDD5F\uD83C\uDD5F 7.932.010.\u202C\n"
						+ "\uD83C\uDD63\uD83C\uDD54\uD83C\uDD5B\uD83C\uDD54\uD83C\uDD56\uD83C\uDD61\uD83C\uDD50\uD83C\uDD5C @lebedev", null),
				"Текст комментария содержит запрещенные слова!", false);

		// ссылки должны деклайниться
		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Здравствуйте, вот ссылка https://www.rerer.ru/product/5239108-bryuki-pryamogo-kroya-s-lampasami-i-shtripkami-gucci-chernyi/", null),
				"Текст комментария не должен содержать ссылок!", false);

		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Здравствуйте, вот ссылка rerer.ru/product/5239108-bryuki-pryamogo-kroya-s-lampasami-i-shtripkami-gucci-chernyi/", null),
				"Текст комментария не должен содержать ссылок!", false);

		postCommentUnSuccessful(client,
				getSimpleProductCommentData("https://oskelly.id952.ru/order60247138\n"
						+ "я случайно отменил зазаз извините. Пожалуйста переоформите его ;)", null),
				"Текст комментария не должен содержать ссылок!", false);

		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Здравствуйте, я хочу купить но перед покупкой хотелось бы увидеть видео с состоянием, "
						+ "напишите мне в \uD835\uDC13\uD835\uDC1E\uD835\uDC25\uD835\uDC1E\uD835\uDC20\uD835\uDC2B\uD835\uDC1A\uD835\uDC26: "
						+ "@\uD835\uDC24\uD835\uDC32\uD835\uDC26\uD835\uDC20\uD835\uDC22\uD835\uDC2B\uD835\uDC25 "
						+ "или \uD835\uDC16\uD835\uDC21\uD835\uDC1A\uD835\uDC2D\uD835\uDC2C\uD835\uDC1A\uD835\uDC29\uD835\uDC29: "
						+ "+\uD835\uDFD5\uD835\uDFD7\uD835\uDFD6\uD835\uDFD0\uD835\uDFD1\uD835\uDFCF\uD835\uDFD5\uD835\uDFD3\uD835\uDFD2\uD835\uDFCE\uD835\uDFD6", null),
				"Текст комментария содержит запрещенные слова!", false);

		//Здравствуйте, меня заинтересовал ваш товар, хотела бы купить, но перед этим посмотреть состояние поближе, свяжитесь со мной 𝙏𝙚𝙡𝙚𝙜𝙧𝙖𝙢 @𝙡𝙚𝙗𝙚𝙙𝙚𝙫𝙖958
		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Здравствуйте, меня заинтересовал ваш товар, хотела бы купить, "
						+ "но перед этим посмотреть состояние поближе, свяжитесь со мной "
						+ "\uD835\uDE4F\uD835\uDE5A\uD835\uDE61\uD835\uDE5A\uD835\uDE5C\uD835\uDE67\uD835\uDE56\uD835\uDE62 "
						+ "@\uD835\uDE61\uD835\uDE5A\uD835\uDE57\uD835\uDE5A\uD835\uDE59\uD835\uDE5A\uD835\uDE6B\uD835\uDE56958", null),
				"Текст комментария содержит запрещенные слова!", false);

		//Здравствуйте, меня заинтересовал ваш товар, хотела бы купить, но перед этим посмотреть состояние поближе, свяжитесь со мной
		//T͙e͙l͙e͙g͙r͙a͙m͙ ͙@͙l͙e͙b͙e͙d͙e͙v͙a͙9͙5͙8͙
		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Здравствуйте, меня заинтересовал ваш товар, хотела бы купить, но перед этим посмотреть состояние поближе, свяжитесь со мной \n"
						+ "T͙e͙l͙e͙g͙r͙a͙m͙ ͙@͙l͙e͙b͙e͙d͙e͙v͙a͙9͙5͙8͙", null),
				"Текст комментария содержит запрещенные слова!", false);

		//Здравствуйте, меня заинтересовал ваш товар, хотела бы купить, но перед этим посмотреть состояние поближе, свяжитесь со мной
		//ᴛᴇʟᴇɢʀᴀᴍ ʟᴇʙᴇᴅᴇᴠᴀ958
		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Здравствуйте, меня заинтересовал ваш товар, хотела бы купить, но перед этим посмотреть состояние поближе, свяжитесь со мной \n"
						+ "ᴛᴇʟᴇɢʀᴀᴍ ʟᴇʙᴇᴅᴇᴠᴀ958", null),
				"Текст комментария содержит запрещенные слова!", false);

		//Здравствуйте, я хочу купить но перед покупкой хотелось бы увидеть видео с состоянием, напишите мне в 🅃🄴🄻🄴🄶🅁🄰🄼: @kymwom
		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Здравствуйте, я хочу купить но перед покупкой хотелось бы увидеть видео с состоянием, напишите мне в \uD83C\uDD43\uD83C\uDD34\uD83C\uDD3B\uD83C\uDD34\uD83C\uDD36\uD83C\uDD41\uD83C\uDD30\uD83C\uDD3C: @kymwom", null),
				"Текст комментария содержит запрещенные слова!", false);

		postCommentUnSuccessful(client,
				getSimpleProductCommentData("8.999.977.48.25 \n"
						+ "Девушки , готова конечно за 15000 р продать ", null),
				"Текст комментария не должен содержать телефонных номеров!", false);

		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Здравствуйте, я хочу купить, но перед покупкой хотелось бы увидеть видео с состоянием, напишите мне на Whаtsарр \u202A+7.991.937.43.37\u202C", null),
				"Текст комментария не должен содержать телефонных номеров!", false);

		// без запрещенных слов комментарий прошел
		postCommentSuccessful(client,
				getSimpleProductCommentData("Напишите мне в ответе размер подошвы", null), false);

		// без запрещенных слов комментарий прошел
		postCommentSuccessful(client,
				getSimpleProductCommentData("Давайте запилим коллективную жалобу!", null), false);

		// ссылки на домен oskelly должны пропускаться
		postCommentSuccessful(client,
				getSimpleProductCommentData("У нас все в наличии , размер S осталась последняя футболка ,можно оплатить https://oskelly.ru/products/futbolka-jil-sander-new-966108", null), false);

		postCommentSuccessful(client,
				getSimpleProductCommentData("Есть доп.фото? если нет.не надо", null), false);

		// мат
		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Пох", null),
				"Текст комментария содержит запрещенные слова!", false);

		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Да мне пох", null),
				"Текст комментария содержит запрещенные слова!", false);

		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Да мне пох.", null),
				"Текст комментария содержит запрещенные слова!", false);

		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Да мне пох ваще", null),
				"Текст комментария содержит запрещенные слова!", false);

		// мат слово внутри хорошего
		postCommentSuccessful(client,
				getSimpleProductCommentData("Поход", null), false);

		// ряд проверок на конфигурируемые стоп-слолва
		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Владислав, вы делаете великое дело на usachev.store. Благодарим!\n\n", null),
				"Текст комментария содержит запрещенные слова!", false);
		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Владислав,usachev! store.!", null),
				"Текст комментария содержит запрещенные слова!", false);
		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Владислав,usachev! store.!", null),
				"Текст комментария содержит запрещенные слова!", false);
		postCommentUnSuccessful(client,
				getSimpleProductCommentData("UsachEv! store.!", null),
				"Текст комментария содержит запрещенные слова!", false);
		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Владислав, вы делаете великое дело на usachev", null),
				"Текст комментария содержит запрещенные слова!", false);
		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Владислав, вы делаете великое дело на Усачев Стор. Благодарим!\n\n", null),
				"Текст комментария содержит запрещенные слова!", false);
		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Владислав, вы делаете великое дело на Усачев. Благодарим!\n\n", null),
				"Текст комментария содержит запрещенные слова!", false);
		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Владислав, вы делаете великое дело на Усачев!\n\n", null),
				"Текст комментария содержит запрещенные слова!", false);
		postCommentUnSuccessful(client,
				getSimpleProductCommentData("Владислав, вы делаете великое дело на усаЧев\n", null),
				"Текст комментария содержит запрещенные слова!", false);
		// должны отбриваться только отдельные слова, если слово не отделено, то должно пропускаться
		postCommentSuccessful(client,
				getSimpleProductCommentData("Владислав, вы делаете великое дело на usachevstore. Благодарим", null), false);
		postCommentSuccessful(client,
				getSimpleProductCommentData("Владислав, вы делаете великое дело на usache", null), false);
		postCommentSuccessful(client,
				getSimpleProductCommentData("Владислав, вы делаете великое дело на усачеву", null), false);
	}


	@Test
	public void _90_cleanup(){
		cleanup();
	}


	private ProductCommentData getSimpleProductCommentData(){
		return getSimpleProductCommentData(simpleCommentText, null);
	}

	private ProductCommentData getSimpleProductCommentData(String text, Long parentCommentId){
		ProductCommentData result = new ProductCommentData();
		result.setProductId(product.getId());
		result.setText(text);
		result.setParentCommentId(parentCommentId);
		return result;
	}

	private ProductCommentData getFewImagesProductCommentData(int count){
		ProductCommentData result = new ProductCommentData();
		result.setProductId(product.getId());
		result.setText(fewImageCommentText);
		String[] images = new String[count];
		for(int i = 0; i < count; i++){
			images[i] = imagesPath + (i + 1) + ".jpg";
		}
		result.setImages(TestUtils.getFilesAsMultipartFilesList(images));
		return result;
	}

	private ProductCommentData getOneImageProductCommentData(){
		ProductCommentData result = getFewImagesProductCommentData(1);
		result.setText(oneImageCommentText);
		return result;
	}

	private boolean containsAnyStr(@NonNull List<CommentDTO> commentsList, @NonNull String... strs){
		for(CommentDTO comment : commentsList){
			for(String str : strs){
				if(comment.getText().contains(str)) return true;
			}
		}
		return false;
	}

	private List<CommentDTO> getProductCommentsSuccessful(ApiV2Client apiV2Client, @NonNull Long productId){
		ResponseEntity<List<CommentDTO>> response = apiV2Client.request(getProductCommentsURL(productId), null, HttpMethod.GET, null, new ParameterizedTypeReference<List<CommentDTO>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		return response.getBody();
	}

	private List<CommentDTO> getProductCommentsTreeSuccessful(ApiV2Client apiV2Client, @NonNull Long productId){
		ResponseEntity<List<CommentDTO>> response = apiV2Client.request(getProductCommentsTreeURL(productId), null, HttpMethod.GET, null, new ParameterizedTypeReference<List<CommentDTO>>() {}, false);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		return response.getBody();
	}

	//Старые тесты проверяют только уведомление NewCommentNotification (user -> user2)
	private CommentDTO postCommentSuccessful(ApiV2Client apiV2Client, ProductCommentData request, boolean withAuthorizeParams){
		return postCommentSuccessful(apiV2Client, request, withAuthorizeParams, new ExpectedNotificationDescription(NewCommentNotification.class, userId, user2Id));
	}

	private CommentDTO postCommentSuccessful(ApiV2Client apiV2Client, ProductCommentData request, boolean withAuthorizeParams, ExpectedNotificationDescription expectedNotification){
		List<ExpectedNotificationDescription> expectedNotifications = expectedNotification != null ? Arrays.asList(expectedNotification) : null;
		return postCommentSuccessful(apiV2Client, request, withAuthorizeParams, expectedNotifications);
	}

	private CommentDTO postCommentSuccessful(ApiV2Client apiV2Client, ProductCommentData request, boolean withAuthorizeParams, List<ExpectedNotificationDescription> expectedNotifications){
		String expectedText = request.getText() == null || request.getText().trim().isEmpty() ? "" : request.getText();
		return postCommentSuccessful(apiV2Client, request, withAuthorizeParams, expectedNotifications, expectedText);
	}

	private CommentDTO postCommentSuccessful(ApiV2Client apiV2Client, ProductCommentData request,
											 boolean withAuthorizeParams,
											 List<ExpectedNotificationDescription> expectedNotifications,
											 String expectedCommetnText){
		ResponseEntity<CommentDTO> response = apiV2Client.request(getServiceURL(), null, HttpMethod.POST, MediaType.MULTIPART_FORM_DATA, TestUtils.getMultivalueMapWithObjectFields(request), new ParameterizedTypeReference<CommentDTO>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		assertEquals(expectedCommetnText, response.getBody().getText());
		if(expectedNotifications != null && !expectedNotifications.isEmpty()) {
			TestUtils.sleep(1); //Рассылка уведомлений ассинхронна и может ззанимать определенное время
			//Проверяем кол-во уведомлений, связанных с данным комментарием
			List<Long> allCommentNotificationIds = notificationRepository.getAllCommentNotificationIds(response.getBody().getId());
			assertEquals(expectedNotifications.size(), allCommentNotificationIds.size());

			//Проверяем соответствие комментариев ожидаемым типам и получателям
			for(ExpectedNotificationDescription expectedNotificationDescription : expectedNotifications) {
				CommentNotification notification = (CommentNotification) getNotificationSuccessful(expectedNotificationDescription.type, product, response.getBody().getId(), expectedNotificationDescription.initiatorUserId, expectedNotificationDescription.targetUserId, false, false);
			}
		}
		return response.getBody();
	}


	private ResponseEntity<String> postCommentUnSuccessful(ApiV2Client apiV2Client, ProductCommentData request, String expectedErrorMessage, boolean withAuthorizeParams){
		ResponseEntity<String> response = apiV2Client.request(getServiceURL(), null, HttpMethod.POST, MediaType.MULTIPART_FORM_DATA, TestUtils.getMultivalueMapWithObjectFields(request), String.class, withAuthorizeParams);
		assertTrue(response.getStatusCode().is4xxClientError());
		assertNotNull(response.getBody());
		assertTrue(response.getBody().contains(expectedErrorMessage));
		return response;
	}

	private Long deleteCommentSuccessful(ApiV2Client apiV2Client, Long commentId, boolean withAuthorizeParams){
		ResponseEntity<Long> response = apiV2Client.request(getDeleteCommentURL(commentId), null, HttpMethod.DELETE, MediaType.APPLICATION_FORM_URLENCODED, null, new ParameterizedTypeReference<Long>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode().is2xxSuccessful());
		assertNotNull(response.getBody());
		assertEquals(commentId, response.getBody());
		return response.getBody();
	}

	private void deleteCommentForbidden(ApiV2Client apiV2Client, Long commentId, boolean withAuthorizeParams){
		ResponseEntity<String> response = apiV2Client.request(getDeleteCommentURL(commentId), null, HttpMethod.DELETE, MediaType.APPLICATION_FORM_URLENCODED, null, new ParameterizedTypeReference<String>() {}, withAuthorizeParams);
		assertTrue(response.getStatusCode() == HttpStatus.FORBIDDEN);
	}

	private void assertImageExists(@NonNull String path) throws IOException{
		if(!path.startsWith("http")) path = testApiConfiguration.getServerUrl() + path;
		URL url = new URL(path);
		BufferedImage image = TestUtils.loadImageFromUrl(url, tmpDirPath);
		assertNotNull(image);
		// так как ресайз в отдельном сервисе, то надо просто проверить, что загрузилось исходное изображение
		System.out.println("Image downloaded: " + path + "; width: " + image.getWidth() + "; height: " + image.getHeight());
		assertTrue(image.getWidth() > 0);
		assertTrue(image.getHeight() > 0);
	}

	private void assertCommentImagesExist(@NonNull List<String> images) throws IOException{
		for(String image : images) {
			assertImageExists(image);
		}
	}

	private void cleanup(){
		deleteCommentsByProduct(product.getId());
		deleteCommentsByUser(userId);
		deleteCommentsByUser(user2Id);
		deleteCommentsByUser(user3Id);
		FileUtils.cleanDirectory(tmpDirPath);
	}

	private void deleteCommentsByProduct(Long productId){
		//Удаляем все комментарии товара
		jdbcTemplate.execute("DELETE FROM comment WHERE product_id=" + productId);
	}

	private void deleteCommentsByUser(Long userId){
		//Удаляем все комментарии пользователя
		jdbcTemplate.execute("DELETE FROM comment WHERE publisher_id = " + userId);
	}

	private void deleteComment(Comment comment){
		String imagesPath = imagesDirPath + "comment/" + comment.getId();
		File imagesDir = new File(imagesPath);
		if(imagesDir.exists()) {
			try {
				org.apache.tomcat.util.http.fileupload.FileUtils.forceDelete(imagesDir);
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		commentRepository.delete(comment);
	}

	private CommentNotification getNotificationSuccessful(Class<? extends CommentNotification> clazz, Product product, Long commentId, Long initiatorUserId, Long targetUserId, boolean needAction, boolean actionCompleted){
		User user = userService.getUserById(targetUserId).orElse(null);
		Notification lastNotification = TestUtils.getLastNotification(user, clazz, needAction, actionCompleted, notificationService);
		assertNotNull(lastNotification);
		CommentNotification commentNotification = (CommentNotification) lastNotification;
		Comment comment = commentNotification.getComment();
		assertEquals(commentId, comment.getId());
		return commentNotification;
	}

	@Data
	public static class ExpectedNotificationDescription{
		private final Class<? extends CommentNotification> type;
		private final Long initiatorUserId;
		private final Long targetUserId;
	}

}
