package ru.oskelly.tests.pr.suite1_1.domain.service.brand;

import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.test.util.ReflectionTestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.BrandRepository;
import su.reddot.domain.dao.category.CategoryRepository;
import su.reddot.domain.dao.product.ProductAttributeValueBindingRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.Brand;
import su.reddot.domain.model.category.Category;
import su.reddot.domain.service.attribute.AttributeService;
import su.reddot.domain.service.brand.BrandDescriptionService;
import su.reddot.domain.service.brand.BrandService;
import su.reddot.domain.service.brand.DefaultBrandService;
import su.reddot.domain.service.dto.BrandDTO;
import su.reddot.domain.service.dto.BrandDescriptionDto;
import su.reddot.domain.service.dto.CategoryDisplayNameDTO;
import su.reddot.domain.service.like.LikeService;
import su.reddot.domain.service.product.ProductService;
import su.reddot.domain.service.setting.FeatureFlagsSettingService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.configparam.ConfigParamService;
import su.reddot.infrastructure.security.SecurityService;
import su.reddot.presentation.DeeplinkUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class DefaultBrandServiceTest {
    @Mock
    BrandRepository brandRepository;
    @Mock
    ProductRepository productRepository;
    @Mock
    CategoryRepository categoryRepository;
    @Mock
    ProductAttributeValueBindingRepository productAttributeValueBindingRepository;
    @Mock
    LikeService likeService;
    @Mock
    UserService userService;
    @Mock
    SecurityService securityService;
    @Mock
    AttributeService attributeService;
    @Mock
    BrandDescriptionService brandDescriptionService;
    @Mock
    ProductService productService;
    @Mock
    ConfigParamService configParamService;
    @Mock
    FeatureFlagsSettingService featureFlagsSettingService;
    @Mock
    MessageSourceAccessor messageSourceAccessor;
    @Mock
    DeeplinkUtils  deeplinkUtils;

    BrandService brandService;

    @BeforeEach
    public void init() throws NoSuchFieldException {
        Brand brand = getBrand();
        when(brandRepository.saveAndFlush(any())).thenReturn(brand);
        when(brandRepository.findById(any())).thenReturn(Optional.of(brand));

        brandService = new DefaultBrandService(
                brandRepository,
                productRepository,
                categoryRepository,
                productAttributeValueBindingRepository,
                likeService,
                userService,
                securityService,
                attributeService,
                brandDescriptionService,
                configParamService,
                featureFlagsSettingService,
                messageSourceAccessor,
                deeplinkUtils
        );

        ReflectionTestUtils.setField(brandService, "productService", productService);
    }

    @Test
    public void getBrandDTOLiteReturnsDtoWithCategorySpecificDescription() {
        BrandDescriptionDto descriptionDto = new BrandDescriptionDto(
                new CategoryDisplayNameDTO( 1L,"Мужское"),
                "Лучший бренд"
        );

        when(brandDescriptionService.getDescriptionsForBrand(any())).thenReturn(Collections.singletonList(descriptionDto));

        BrandDTO brandDTOLite = brandService.getBrandDTOLite(getBrand(), true);
        assertThat(brandDTOLite.getBrandDescriptions()).isNotEmpty();
        assertThat(brandDTOLite.getBrandDescriptions()).hasSize(1);
        assertThat(brandDTOLite.getBrandDescriptions().get(0)).isEqualTo(descriptionDto);
    }

    @Test
    public void saveBrandSavesCategorySpecificDescription() {
        BrandDTO brandDTO = new BrandDTO();
        brandDTO.setName("Бренд");
        List<BrandDescriptionDto> brandDescriptions = getBrandDescriptionDtos();
        brandDTO.setBrandDescriptions(brandDescriptions);

        brandService.saveBrand(brandDTO);

        Brand brand = getBrand();
        verify(brandDescriptionService, times(1)).saveDescriptions(eq(brand), eq(brandDescriptions));
    }

    @Test
    public void updateBrandSavesCategorySpecificDescription() {
        List<BrandDescriptionDto> brandDescriptions = getBrandDescriptionDtos();

        BrandDTO brandDTO = new BrandDTO();
        brandDTO.setName("Бренд");
        brandDTO.setBrandDescriptions(brandDescriptions);
        Brand brand = getBrand();

        brandService.updateBrand(brandDTO);

        verify(brandDescriptionService, times(1)).saveDescriptions(eq(brand), eq(brandDescriptions));
    }

    @Test
    public void getBrandsInGroupsByCategoriesIdsForUserWithHiddenBrands() {
        when(configParamService.getValueAsList(any())).thenReturn(Lists.newArrayList("222"));
        when(brandRepository.getDistinctCharsOfBrandsInCategories(any())).thenReturn(Collections.singletonList("A"));
        when(featureFlagsSettingService.isEnableForUserWithPercent(any(FeatureFlagsSettingService.ConfigFlag.class)))
                .thenReturn(true);

        List<Long> categoryIds = categoryRepository.findAll().stream()
                .map(Category::getId)
                .collect(Collectors.toList());

        brandService.getBrandsInGroupsByCategoriesIds(categoryIds);

        verify(brandRepository).findAllByFirstCharInCategories(any(), any(), argThat(list -> list.size() != 0));
    }

    @Test
    public void getBrandsInGroupsByCategoriesIdsForUser() {
        when(brandRepository.getDistinctCharsOfBrandsInCategories(any())).thenReturn(Collections.singletonList("B"));
        when(featureFlagsSettingService.isEnableForUserWithPercent(any(FeatureFlagsSettingService.ConfigFlag.class)))
                .thenReturn(false);

        List<Long> categoryIds = categoryRepository.findAll().stream()
                .map(Category::getId)
                .collect(Collectors.toList());

        brandService.getBrandsInGroupsByCategoriesIds(categoryIds);

        verify(brandRepository).findAllByFirstCharInCategories(any(), any(), argThat(list -> list.size() == 0));
        verify(configParamService, never()).getValueAsList(any());
    }

    private List<BrandDescriptionDto> getBrandDescriptionDtos() {
        BrandDescriptionDto descriptionDto = new BrandDescriptionDto(
                new CategoryDisplayNameDTO( 1L,"Мужское"),
                "Лучший бренд"
        );
        return Collections.singletonList(descriptionDto);
    }

    private Brand getBrand() {
        Brand brand = new Brand();
        brand.setId(2L);
        brand.setName("Бренд");
        return brand;
    }
}
