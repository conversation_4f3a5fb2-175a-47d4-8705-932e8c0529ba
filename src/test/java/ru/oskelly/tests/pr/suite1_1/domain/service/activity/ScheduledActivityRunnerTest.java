package ru.oskelly.tests.pr.suite1_1.domain.service.activity;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.model.activity.Activity;
import su.reddot.domain.model.appsflyer.AppsflyerResultStatus;
import su.reddot.domain.service.activity.ScheduledActivityRunner;
import su.reddot.domain.service.appsflyer.AppsflyerService;
import su.reddot.domain.service.dto.AppsflyerResultDTO;
import su.reddot.domain.service.master.MasterServiceRequest;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;

@ExtendWith(MockitoExtension.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class ScheduledActivityRunnerTest {
    @Mock
    ApplicationEventPublisher applicationEventPublisher;
    @Mock
    AppsflyerService appsflyerService;

    ScheduledActivityRunner runner;

    @BeforeEach
    public void init() {
        runner = new ScheduledActivityRunner(appsflyerService, applicationEventPublisher);
    }

    @Test
    public void sendActivitiesToAppsflyerSendsSuccessfulSentId() {
        List<Activity> activities = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            activities.add(getActivityMock(i));
        }

        List<AppsflyerResultDTO> appsflyerResultDTOS = Arrays.asList(
                new AppsflyerResultDTO(1L, AppsflyerResultStatus.SUCCESS),
                new AppsflyerResultDTO(3L, AppsflyerResultStatus.SUCCESS),
                new AppsflyerResultDTO(5L, AppsflyerResultStatus.SUCCESS),
                new AppsflyerResultDTO(6L, AppsflyerResultStatus.SUCCESS),
                new AppsflyerResultDTO(8L, AppsflyerResultStatus.SUCCESS)
        );


        given(appsflyerService.sendToAppsflyer(any(List.class))).willReturn(appsflyerResultDTOS);

        runner.sendActivitiesToAppsflyer(activities);

        ArgumentCaptor<Object> captor = ArgumentCaptor.forClass(Object.class);

        then(applicationEventPublisher)
                .should(times(1))
                .publishEvent(captor.capture());

        Object event = captor.getValue();
        assertThat(event).isInstanceOf(MasterServiceRequest.class);
        MasterServiceRequest masterServiceRequest = (MasterServiceRequest) event;
        assertThat(masterServiceRequest.getUrl()).isEqualTo("/api/v2/master/activity/markAsSentToAppsflyer");
        assertThat(masterServiceRequest.getRequestEntityObject()).isNotNull();
        assertThat(masterServiceRequest.getRequestEntityObject()).isInstanceOf(List.class);
        List<AppsflyerResultDTO> requestBody = (List<AppsflyerResultDTO>) masterServiceRequest.getRequestEntityObject();
        assertThat(requestBody).containsAll(appsflyerResultDTOS);
    }

    private Activity getActivityMock(long id) {
        Activity activity = mock(Activity.class);
        given(activity.getId()).willReturn(id);
        return activity;
    }
}
