package ru.oskelly.tests.pr.suite1_1.domain.service.address;

import static org.assertj.core.api.Assertions.assertThat;

import java.nio.charset.StandardCharsets;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.NonNull;
import lombok.SneakyThrows;
import su.reddot.domain.service.address.DadataSuggestionConverter;
import su.reddot.domain.service.dadata.DadataService.DadataSuggestion;
import su.reddot.domain.service.dto.AddressDTO;

import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

/**
 * Unit tests for {@link DadataSuggestionConverter}.
 */
public class DadataSuggestionConverterTest {

    private static final ObjectMapper mapper = new ObjectMapper();

    @Test
    public void testConvert_Berezhkovskaya16A() {
        final AddressDTO addressDto = convert("/json/dadata/Suggestion_Berezhkovskaya16A.json");

        assertThat(addressDto.getId()).isNull();
        assertThat(addressDto.getZipCode()).isEqualTo("121059");
        assertThat(addressDto.getCountry()).isEqualTo("Россия");
        assertThat(addressDto.getRegion()).isEqualTo("Москва");
        assertThat(addressDto.getCity()).isEqualTo("Москва");
        assertThat(addressDto.getAddress()).isEqualTo("Бережковская наб, д 16А");
        assertThat(addressDto.getAddress2()).isNull();
        assertThat(addressDto.getAddress3()).isNull();
        assertThat(addressDto.getFiasId()).isEqualTo("7090885c-fbff-4b8d-af1f-fbbd0ce38660");
        assertThat(addressDto.getRegionFiasId()).isEqualTo("0c5b2444-70a0-4932-980c-b4dc0d3f02b5");
        assertThat(addressDto.getCityFiasId()).isEqualTo("0c5b2444-70a0-4932-980c-b4dc0d3f02b5");
        assertThat(addressDto.getSettlementFiasId()).isNull();
        assertThat(addressDto.getDadataFullAddress()).isEqualTo("121059, г Москва, р-н Дорогомилово, Бережковская наб, д 16А");
        assertThat(addressDto.getFullAddress()).isEqualTo("г Москва, Бережковская наб, д 16А");
    }

    @Test
    public void testConvert_SosnovyiBor12() {
        final AddressDTO addressDto = convert("/json/dadata/Suggestion_SosnovyiBor12.json");

        assertThat(addressDto.getId()).isNull();
        assertThat(addressDto.getZipCode()).isEqualTo("140125");
        assertThat(addressDto.getCountry()).isEqualTo("Россия");
        assertThat(addressDto.getRegion()).isEqualTo("Московская область");
        assertThat(addressDto.getCity()).isEqualTo("Раменское");
        assertThat(addressDto.getAddress()).isEqualTo("тер ДНТ Сосновый бор, ул Северная, д 12");
        assertThat(addressDto.getAddress2()).isNull();
        assertThat(addressDto.getAddress3()).isNull();
        assertThat(addressDto.getFiasId()).isEqualTo("4ecad58c-cd83-41d1-addb-7f47afc3696a");
        assertThat(addressDto.getRegionFiasId()).isEqualTo("29251dcf-00a1-4e34-98d4-5c47484a36d4");
        assertThat(addressDto.getCityFiasId()).isEqualTo("a54506e0-5641-4d8c-b543-7bcce2c9887f");
        assertThat(addressDto.getSettlementFiasId()).isNull();
        assertThat(addressDto.getDadataFullAddress()).isEqualTo("140125, Московская обл, г Раменское, тер ДНТ Сосновый бор, ул Северная, д 12");
        assertThat(addressDto.getFullAddress()).isEqualTo("Московская обл, г Раменское, тер ДНТ Сосновый бор, ул Северная, д 12");
    }

    @NonNull
    @SneakyThrows
    private static AddressDTO convert(@NonNull final String jsonFile) {
        final Resource resource = new ClassPathResource(jsonFile);
        final String json = IOUtils.toString(resource.getInputStream(), StandardCharsets.UTF_8);
        final DadataSuggestion dadataSuggestion = mapper.readValue(json, new TypeReference<DadataSuggestion>() {});
        final DadataSuggestionConverter converter = new DadataSuggestionConverter();

        return converter.convertToAddress(dadataSuggestion);
    }
}
