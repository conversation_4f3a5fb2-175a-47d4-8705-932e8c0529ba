package ru.oskelly.tests.pr.suite1_1.domain.service.account;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.counterparty.CounterpartyRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.model.counterparty.CardCounterparty;
import su.reddot.domain.model.counterparty.Counterparty;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderSource;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.account.AccountService;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.domain.service.dto.CounterpartyDTO;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.security.SecurityService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;

@ExtendWith(SpringExtension.class)
@ActiveProfiles(AbstractSpringTest.testProfiles)
@SpringBootTest(classes = OskellyApplication.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class DefaultAccountServiceTest {

    @Autowired
    UserRepository userRepository;
    @Autowired
    CommissionGridService commissionGridService;
    @Autowired
    CounterpartyRepository<Counterparty> counterpartyRepository;
    @Autowired
    OrderRepository orderRepository;
    @Autowired
    AccountService defaultAccountService;
    @MockBean
    SecurityService securityService;
    private User currentUser;
    private Counterparty currentUserCounterparty;
    private Order boutiqueOrder;
    private User currentBuyer;
    private Order webOrder;

    @BeforeEach
    public void init() {
        User user = new User()
                .setNickname(RandomStringUtils.randomAlphabetic(5))
                .setUserType(User.UserType.SIMPLE_USER)
                .setChangeTime(LocalDateTime.now())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        currentUser = userRepository.save(user);

        User buyer = new User()
                .setNickname(RandomStringUtils.randomAlphabetic(5))
                .setUserType(User.UserType.SIMPLE_USER)
                .setChangeTime(LocalDateTime.now())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        currentBuyer = userRepository.save(buyer);

        Counterparty counterparty = new CardCounterparty()
                .setCardBrand("MIR")
                .setUser(currentUser)
                .setCreateTime(ZonedDateTime.now())
                .setChangeTime(ZonedDateTime.now());
        currentUserCounterparty = counterpartyRepository.save(counterparty);
        user.setCounterparties(Collections.emptyList());
        user.setAddressEndpoints(Collections.emptyList());

        Order boutiqueOrder = new Order();
        boutiqueOrder.setUuid(UUID.randomUUID());
        boutiqueOrder.setState(OrderState.CREATED);
        boutiqueOrder.setSellerCounterparty(counterparty);
        boutiqueOrder.setAmount(BigDecimal.ONE);
        boutiqueOrder.setEffectiveAmount(BigDecimal.valueOf(100));
        boutiqueOrder.setPaymentVersion("tcb");
        boutiqueOrder.setBuyer(currentBuyer);
        boutiqueOrder.setOrderSource(OrderSource.BOUTIQUE);
        this.boutiqueOrder = orderRepository.save(boutiqueOrder);

        Order webOrder = new Order();
        webOrder.setUuid(UUID.randomUUID());
        webOrder.setState(OrderState.CREATED);
        webOrder.setSellerCounterparty(counterparty);
        webOrder.setAmount(BigDecimal.ONE);
        webOrder.setEffectiveAmount(BigDecimal.valueOf(100));
        webOrder.setPaymentVersion("tcb");
        webOrder.setBuyer(currentBuyer);
        webOrder.setOrderSource(OrderSource.WEB);
        this.webOrder = orderRepository.save(webOrder);

        given(securityService.getCurrentAuthorizedUser()).willReturn(currentUser);
    }

    @AfterEach
    public void cleanup() {
        orderRepository.delete(boutiqueOrder);
        orderRepository.delete(webOrder);
        counterpartyRepository.delete(currentUserCounterparty);
        userRepository.delete(currentUser);
        userRepository.delete(currentBuyer);
    }


    @Test
    public void getAccountReturnsZeroCounterpartiesIfUserHasNoCounterparies() {
        List<CounterpartyDTO> counterpartyDTOS = defaultAccountService.getPayoutCounterparties(webOrder.getId(), false);
        assertThat(counterpartyDTOS).isEmpty();
    }

    @Test
    @Transactional
    public void getAccountReturns12StoreezCounterpartyAtSecondPositionIfUserNotPro() {
        currentUser.setCounterparties(Collections.singletonList(currentUserCounterparty));
        List<CounterpartyDTO> counterpartyDTOS = defaultAccountService.getPayoutCounterparties(webOrder.getId(), false);
        assertThat(counterpartyDTOS).hasSize(2);
        CounterpartyDTO counterpartyDTO = counterpartyDTOS.get(1);
        assertThat(counterpartyDTO.getType()).isEqualByComparingTo(CounterpartyDTO.CounterpartyType.BONUS_12_STOREEZ);
        assertThat(counterpartyDTO.getCounterpartyImage()).isEqualTo("http://localhost:4566/oskelly/cards/12STOREEZ");
    }

    @Test
    @Transactional
    public void getAccountNotReturns12StoreezCounterpartyForBoutiqueOrder() {
        currentUser.setCounterparties(Collections.singletonList(currentUserCounterparty));
        List<CounterpartyDTO> counterpartyDTOS = defaultAccountService.getPayoutCounterparties(boutiqueOrder.getId(), false);
        assertThat(counterpartyDTOS).hasSize(1);
        CounterpartyDTO counterpartyDTO = counterpartyDTOS.get(0);
        assertThat(counterpartyDTO.getType()).isNotEqualByComparingTo(CounterpartyDTO.CounterpartyType.BONUS_12_STOREEZ);
    }
}
