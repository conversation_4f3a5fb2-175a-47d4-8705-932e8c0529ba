package ru.oskelly.tests.pr.suite1_1.domain.service.brand;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.BrandCategoryDescriptionRepository;
import su.reddot.domain.dao.BrandRepository;
import su.reddot.domain.dao.category.CategoryRepository;
import su.reddot.domain.model.Brand;
import su.reddot.domain.model.BrandCategoryDescription;
import su.reddot.domain.model.category.Category;
import su.reddot.domain.service.brand.BrandDescriptionService;
import su.reddot.domain.service.catalog.CatalogCategory;
import su.reddot.domain.service.dto.BrandDescriptionDto;
import su.reddot.domain.service.dto.CategoryDisplayNameDTO;
import su.reddot.infrastructure.configuration.OskellyApplication;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {OskellyApplication.class},
        properties = {
                "app.brands.categoriesWithSpecificDescriptionIds[0] = 10",
                "app.brands.categoriesWithSpecificDescriptionIds[1] = 11"
        }
)
@ActiveProfiles(profiles = AbstractSpringTest.testProfiles)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class BrandDescriptionServiceTest {
    public static final String DESCRIPTION = "Test description for test category";
    @Autowired
    private BrandDescriptionService brandDescriptionService;
    @Autowired
    private BrandCategoryDescriptionRepository brandCategoryDescriptionRepository;
    @Autowired
    private CategoryRepository categoryRepository;
    @Autowired
    private BrandRepository brandRepository;

    private Brand testBrand;
    private Category testCategory;
    private BrandCategoryDescription testBrandDescription;

    @BeforeEach
    public void init() {

        testBrand = brandRepository.findById(10L).get();
        testCategory = categoryRepository.findById(10L).get();

        BrandCategoryDescription brandCategoryDescription = new BrandCategoryDescription();
        brandCategoryDescription.setBrand(testBrand);
        brandCategoryDescription.setCategory(testCategory);
        brandCategoryDescription.setDescription(DESCRIPTION);
        testBrandDescription = brandCategoryDescriptionRepository.save(brandCategoryDescription);
    }

    @AfterEach
    public void cleanup() {
        brandCategoryDescriptionRepository.delete(testBrandDescription);
    }

    @Test
    public void getBrandDescriptionForCategoryReturnsDescription() {
        CatalogCategory category = new CatalogCategory(testCategory.getId(), testCategory.getDisplayName(), testCategory.hasChildren());
        Optional<String> descriptionForCategory = brandDescriptionService.getBrandDescriptionForCategory(testBrand, category);
        assertThat(descriptionForCategory).isPresent();
        assertThat(descriptionForCategory.get()).isEqualTo("Test description for test category");
    }

    @Test
    public void getDescriptionsForBrandsReturnsAllDescriptions() {

        Category category = categoryRepository.findById(11L).get();
        BrandCategoryDescription brandCategoryDescription = new BrandCategoryDescription();
        brandCategoryDescription.setBrand(testBrand);
        brandCategoryDescription.setCategory(category);
        brandCategoryDescription.setDescription("Test description for another category");
        BrandCategoryDescription description = brandCategoryDescriptionRepository.save(brandCategoryDescription);

        List<BrandDescriptionDto> descriptionsForBrand = brandDescriptionService.getDescriptionsForBrand(testBrand);

        List<BrandDescriptionDto> expected = new ArrayList<>();
        expected.add(new BrandDescriptionDto(
                new CategoryDisplayNameDTO(11L, category.getDisplayName()),
                "Test description for another category"
        ));
        expected.add(new BrandDescriptionDto(
                new CategoryDisplayNameDTO(10L, testCategory.getDisplayName()),
                DESCRIPTION
        ));

        assertThat(descriptionsForBrand).hasSize(2);

        assertThat(descriptionsForBrand).containsAll(expected);

        brandCategoryDescriptionRepository.delete(description);
    }

    @Test
    public void getAvailableSpecificDescriptionCategoriesReturnCategoriesFromProperties() {
        List<CategoryDisplayNameDTO> availableSpecificDescriptionCategories = brandDescriptionService.getAvailableSpecificDescriptionCategories();
        assertThat(availableSpecificDescriptionCategories).hasSize(2);
        assertThat(availableSpecificDescriptionCategories).contains(new CategoryDisplayNameDTO(10L, "Обувь"));
        assertThat(availableSpecificDescriptionCategories).contains(new CategoryDisplayNameDTO(11L, "Ботильоны и полусапоги"));
    }
}
