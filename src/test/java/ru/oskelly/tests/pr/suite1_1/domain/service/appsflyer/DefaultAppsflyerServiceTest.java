package ru.oskelly.tests.pr.suite1_1.domain.service.appsflyer;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.client.AutoConfigureMockRestServiceServer;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.web.client.RestTemplate;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.BrandRepository;
import su.reddot.domain.dao.SizeRepository;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.activity.ActivityRepository;
import su.reddot.domain.dao.device.DeviceRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.product.ProductItemRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.activity.Activity;
import su.reddot.domain.model.activity.order.AddToCartActivity;
import su.reddot.domain.model.activity.order.InitHoldActivity;
import su.reddot.domain.model.activity.order.OrderAnotherPaidActivity;
import su.reddot.domain.model.activity.order.OrderFirstPaidActivity;
import su.reddot.domain.model.activity.product.publication.ProductPublishedActivity;
import su.reddot.domain.model.activity.product.publication.SendToModerationActivity;
import su.reddot.domain.model.activity.profile.RegisterActivity;
import su.reddot.domain.model.appsflyer.AppsflyerResultStatus;
import su.reddot.domain.model.device.Device;
import su.reddot.domain.model.device.DeviceDtype;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.appsflyer.DefaultAppsflyerService;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.domain.service.dto.AppsflyerResultDTO;
import su.reddot.infrastructure.configuration.OskellyApplication;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.content;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.method;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.requestTo;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withStatus;

@SpringBootTest(classes = OskellyApplication.class)
@ActiveProfiles(AbstractSpringTest.testProfiles)
@ExtendWith(SpringExtension.class)
@AutoConfigureMockRestServiceServer
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class DefaultAppsflyerServiceTest {
    public static final long USER_ID = -1L;
    public static final String NICKNAME = "IamTestUser";
    public static final String GUEST_TOKEN = "test-guest-token";
    public static final String TEST_APPSFLYER_ID = "test-appsflyer-id";

    @Autowired
    DefaultAppsflyerService defaultAppsflyerService;
    @Autowired
    OrderRepository orderRepository;
    @Autowired
    ActivityRepository<Activity> activityRepository;
    @Autowired
    ProductRepository productRepository;
    @Autowired
    DeviceRepository deviceRepository;
    @Autowired
    ProductItemRepository productItemRepository;
    @Autowired
    SizeRepository sizeRepository;
    @Autowired
    BrandRepository brandRepository;
    @Autowired
    UserRepository userRepository;
    @Autowired
    RestTemplate restTemplate;
    @Autowired
    CommissionGridService commissionGridService;

    private Product currentProduct;
    private Order currentOrder;
    private Device currentDevice;
    private ProductItem currentProductItem;
    private User currentSeller;

    private MockRestServiceServer mockServer;

    @BeforeEach
    public void init() {
        mockServer = MockRestServiceServer.createServer(restTemplate);

        currentSeller = new User()
                .setId(USER_ID)
                .setNickname(NICKNAME)
                .setUserType(User.UserType.SIMPLE_USER)
                .setChangeTime(LocalDateTime.now())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        currentSeller = userRepository.saveAndFlush(currentSeller);

        Product product = new Product();
        product.setBrand(brandRepository.getOne(1L));
        product.setCategoryId(2L);
        product.setSeller(currentSeller);
        product.setCurrentPrice(BigDecimal.valueOf(1000));
        product.setProductState(ProductState.PUBLISHED);
        currentProduct = productRepository.saveAndFlush(product);

        ProductItem productItem = new ProductItem();
        productItem.setProduct(product);
        productItem.setSize(sizeRepository.getOne(1L));
        currentProductItem = productItemRepository.saveAndFlush(productItem);

        Order order = new Order();
        order.setUuid(UUID.randomUUID());
        order.setState(OrderState.CREATED);
        order.setGuestToken(GUEST_TOKEN);
        order.setAmount(BigDecimal.valueOf(1000));
        order.setEffectiveAmount(BigDecimal.valueOf(1010));
        currentOrder = orderRepository.saveAndFlush(order);

        Device device = new Device();
        device.setAppsflyerId(TEST_APPSFLYER_ID);
        device.setUserAgent("test-agent");
        device.setCreateTime(ZonedDateTime.now());
        device.setDtype(DeviceDtype.AndroidDevice);
        currentDevice = deviceRepository.saveAndFlush(device);
    }

    @AfterEach
    public void cleanup() {
        mockServer.reset();
        productItemRepository.delete(currentProductItem);
        deviceRepository.delete(currentDevice);
        productRepository.delete(currentProduct);
        orderRepository.delete(currentOrder);
        userRepository.delete(currentSeller);
    }

    @Test
    public void sendToAppsflyerReturnsSuccessful() {

        Activity activity = new AddToCartActivity(currentOrder.getId(), currentProductItem.getId());
        activity.setDevice(currentDevice);
        activity = activityRepository.save(activity);

        mockServer.expect(ExpectedCount.once(), requestTo("https://api2.appsflyer.com/inappevent/ru.oskelly.app"))
                .andRespond(withStatus(HttpStatus.OK));

        AppsflyerResultDTO appsflyerResultDTO = defaultAppsflyerService.sendToAppsflyer(activity);
        assertThat(appsflyerResultDTO.getAppsflyerResultStatus()).isEqualByComparingTo(AppsflyerResultStatus.SUCCESS);
        assertThat(appsflyerResultDTO.getActivityId()).isEqualTo(activity.getId());

        activityRepository.delete(activity);
    }

    @Test
    public void sendToAppsflyerReturnsNoDeviceIfNoDeviceFoundForActivity() {
        Activity activity = new AddToCartActivity(currentOrder.getId(), currentProductItem.getId());
        activity.setDevice(null);
        activity = activityRepository.save(activity);

        mockServer.expect(ExpectedCount.never(), requestTo("https://api2.appsflyer.com/inappevent/ru.oskelly.app"));


        AppsflyerResultDTO appsflyerResultDTO = defaultAppsflyerService.sendToAppsflyer(activity);
        assertThat(appsflyerResultDTO.getAppsflyerResultStatus()).isEqualByComparingTo(AppsflyerResultStatus.DEVICE_NOT_FOUND);
        assertThat(appsflyerResultDTO.getActivityId()).isEqualTo(activity.getId());

        activityRepository.delete(activity);

    }

    @Test
    public void sendToAppsflyerUseDeviceFromActivity() {

        Activity activity = new AddToCartActivity(currentOrder.getId(), currentProductItem.getId());
        activity.setDevice(currentDevice);
        activity = activityRepository.save(activity);

        mockServer.expect(ExpectedCount.once(), requestTo("https://api2.appsflyer.com/inappevent/ru.oskelly.app"))
                .andExpect(method(HttpMethod.POST))
                .andExpect(content().json("{\"appsflyer_id\":  \"test-appsflyer-id\"}"))
                .andRespond(withStatus(HttpStatus.OK));

        defaultAppsflyerService.sendToAppsflyer(activity);


        mockServer.verify();
        activityRepository.delete(activity);
    }

    @Test
    public void sendToAppsflyerUseDeviceFromInitHoldActivityForFirstPaidOrderActivity() {

        InitHoldActivity initHoldActivity = new InitHoldActivity(currentOrder.getId());
        initHoldActivity.setDevice(currentDevice);
        initHoldActivity.setGuestToken(GUEST_TOKEN);
        initHoldActivity = activityRepository.save(initHoldActivity);

        OrderFirstPaidActivity activity = new OrderFirstPaidActivity(currentOrder.getId());
        activityRepository.save(activity);

        mockServer.expect(ExpectedCount.once(), requestTo("https://api2.appsflyer.com/inappevent/ru.oskelly.app"))
                .andExpect(method(HttpMethod.POST))
                .andExpect(content().json("{\"appsflyer_id\":  \"test-appsflyer-id\"}"))
                .andRespond(withStatus(HttpStatus.OK));

        defaultAppsflyerService.sendToAppsflyer(activity);


        mockServer.verify();
        activityRepository.delete(activity);
        activityRepository.delete(initHoldActivity);
    }


    @Test
    public void sendToAppsflyerUseDeviceFromInitHoldActivityForOrderAnotherPaidActivity() {

        InitHoldActivity initHoldActivity = new InitHoldActivity(currentOrder.getId());
        initHoldActivity.setDevice(currentDevice);
        initHoldActivity.setGuestToken(GUEST_TOKEN);
        initHoldActivity = activityRepository.save(initHoldActivity);

        OrderAnotherPaidActivity activity = new OrderAnotherPaidActivity(currentOrder.getId());
        activityRepository.save(activity);

        mockServer.expect(ExpectedCount.once(), requestTo("https://api2.appsflyer.com/inappevent/ru.oskelly.app"))
                .andExpect(method(HttpMethod.POST))
                .andExpect(content().json("{\"appsflyer_id\":  \"test-appsflyer-id\"}"))
                .andRespond(withStatus(HttpStatus.OK));

        defaultAppsflyerService.sendToAppsflyer(activity);


        mockServer.verify();
        activityRepository.delete(activity);
        activityRepository.delete(initHoldActivity);
    }

    @Test
    public void getActivitiesToSendToAppsflyerReturnsActivitiesWithoutAppsflyerStatus() {
        RegisterActivity registerActivity = new RegisterActivity();
        registerActivity.setDevice(currentDevice);
        registerActivity.setGuestToken(GUEST_TOKEN);
        registerActivity.setAppsflyerResult(AppsflyerResultStatus.SUCCESS.name());
        registerActivity = activityRepository.saveAndFlush(registerActivity);

        RegisterActivity secondRegisterActivity = new RegisterActivity();
        secondRegisterActivity.setDevice(currentDevice);
        secondRegisterActivity.setGuestToken(GUEST_TOKEN);
        secondRegisterActivity = activityRepository.saveAndFlush(secondRegisterActivity);

        List<Activity> activitiesToSendToAppsflyer = defaultAppsflyerService.getActivitiesToSendToAppsflyer(100);

        assertThat(activitiesToSendToAppsflyer).hasSize(1);
        assertThat(activitiesToSendToAppsflyer.get(0).getId()).isEqualTo(secondRegisterActivity.getId());

        activityRepository.delete(registerActivity);
        activityRepository.delete(secondRegisterActivity);
    }


    @Test
    public void sendToAppsflyerUseDeviceFromSendToModerationForProductPublishedActivity() {

        SendToModerationActivity sendToModerationActivity = new SendToModerationActivity(currentProduct.getId());
        sendToModerationActivity.setDevice(currentDevice);
        sendToModerationActivity.setUserId(currentSeller.getId());
        sendToModerationActivity = activityRepository.save(sendToModerationActivity);

        ProductPublishedActivity activity = new ProductPublishedActivity(currentProduct.getId());
        activityRepository.save(activity);

        mockServer.expect(ExpectedCount.once(), requestTo("https://api2.appsflyer.com/inappevent/ru.oskelly.app"))
                .andExpect(method(HttpMethod.POST))
                .andExpect(content().json("{\"appsflyer_id\":  \"test-appsflyer-id\"}"))
                .andRespond(withStatus(HttpStatus.OK));

        defaultAppsflyerService.sendToAppsflyer(activity);


        mockServer.verify();
        activityRepository.delete(activity);
        activityRepository.delete(sendToModerationActivity);
    }

}
