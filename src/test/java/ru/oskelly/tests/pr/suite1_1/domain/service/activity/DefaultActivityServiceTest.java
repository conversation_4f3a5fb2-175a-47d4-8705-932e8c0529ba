package ru.oskelly.tests.pr.suite1_1.domain.service.activity;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.activity.ActivityRepository;
import su.reddot.domain.dao.device.DeviceRepository;
import su.reddot.domain.model.activity.Activity;
import su.reddot.domain.model.activity.profile.RegisterActivity;
import su.reddot.domain.model.appsflyer.AppsflyerResultStatus;
import su.reddot.domain.model.device.Device;
import su.reddot.domain.model.device.DeviceDtype;
import su.reddot.domain.service.activity.DefaultActivityService;
import su.reddot.domain.service.dto.AppsflyerResultDTO;
import su.reddot.infrastructure.configuration.OskellyApplication;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(SpringExtension.class)
@ActiveProfiles(AbstractSpringTest.testProfiles)
@SpringBootTest(classes = OskellyApplication.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class DefaultActivityServiceTest {
    public static final String GUEST_TOKEN = "test-guest-token";
    public static final String TEST_APPSFLYER_ID = "test-appsflyer-id";

    @Autowired
    DeviceRepository deviceRepository;
    @Autowired
    ActivityRepository<Activity> activityRepository;

    @Autowired
    DefaultActivityService defaultActivityService;

    private Device currentDevice;

    @BeforeEach
    public void init() {
        Device device = new Device();
        device.setAppsflyerId(TEST_APPSFLYER_ID);
        device.setUserAgent("test-agent");
        device.setCreateTime(ZonedDateTime.now());
        device.setDtype(DeviceDtype.AndroidDevice);
        currentDevice = deviceRepository.save(device);
    }


    @Test
    public void markAsSentToAppsflyerSavesActivityWithStatus() {
        RegisterActivity registerActivity = new RegisterActivity();
        registerActivity.setDevice(currentDevice);
        registerActivity.setGuestToken(GUEST_TOKEN);
        registerActivity = activityRepository.saveAndFlush(registerActivity);

        RegisterActivity secondRegisterActivity = new RegisterActivity();
        secondRegisterActivity.setGuestToken(GUEST_TOKEN);
        secondRegisterActivity = activityRepository.saveAndFlush(secondRegisterActivity);

        List<AppsflyerResultDTO> results = new ArrayList<>();
        results.add(new AppsflyerResultDTO(registerActivity.getId(), AppsflyerResultStatus.SUCCESS));
        results.add(new AppsflyerResultDTO(secondRegisterActivity.getId(), AppsflyerResultStatus.DEVICE_NOT_FOUND));

        defaultActivityService.markAsSentToAppsflyer(results);

        Optional<Activity> registerActivityOptional = activityRepository.findById(registerActivity.getId());
        assertThat(registerActivityOptional).isPresent();
        assertThat(registerActivityOptional.get().getAppsflyerResult()).isEqualTo(AppsflyerResultStatus.SUCCESS.name());


        Optional<Activity> secondRegisterActivityOptional = activityRepository.findById(secondRegisterActivity.getId());
        assertThat(secondRegisterActivityOptional).isPresent();
        assertThat(secondRegisterActivityOptional.get().getAppsflyerResult()).isEqualTo(AppsflyerResultStatus.DEVICE_NOT_FOUND.name());

        activityRepository.delete(registerActivity);
        activityRepository.delete(secondRegisterActivity);
    }
}
