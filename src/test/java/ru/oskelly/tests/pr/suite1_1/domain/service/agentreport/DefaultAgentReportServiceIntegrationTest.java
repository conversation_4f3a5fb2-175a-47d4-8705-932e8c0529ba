package ru.oskelly.tests.pr.suite1_1.domain.service.agentreport;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.MockPublisherConfiguration;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.BrandRepository;
import su.reddot.domain.dao.SizeRepository;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.agentreport.AgentReportRepository;
import su.reddot.domain.dao.agentreportpayments.AgentReportPaymentsRepository;
import su.reddot.domain.dao.bankaccount.BankPaymentTestRepository;
import su.reddot.domain.dao.counterparty.CounterpartyRepository;
import su.reddot.domain.dao.order.OrderPositionRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.product.ProductItemRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.agentreport.AgentReport;
import su.reddot.domain.model.agentreport.AgentReportPayments;
import su.reddot.domain.model.banktransaction.BankPayment;
import su.reddot.domain.model.counterparty.CardCounterparty;
import su.reddot.domain.model.counterparty.Counterparty;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.order.OrderPositionState;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.agentreport.AgentReportService;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.domain.service.dto.BonusDto;
import su.reddot.domain.service.dto.order.AgentReportParams;
import su.reddot.infrastructure.configuration.OskellyApplication;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {OskellyApplication.class, MockPublisherConfiguration.class}, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@ActiveProfiles(profiles = AbstractSpringTest.testProfiles)
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class DefaultAgentReportServiceIntegrationTest {

    public static final int INITIAL_PAYMENT_AMOUNT = 9000;
    public static final int BONUS_AMOUNT = 5000;
    @Autowired
    AgentReportRepository agentReportRepository;
    @Autowired
    AgentReportPaymentsRepository agentReportPaymentsRepository;
    @Autowired
    BankPaymentTestRepository bankPaymentRepository;
    @Autowired
    CounterpartyRepository<Counterparty> counterpartyRepository;
    @Autowired
    ProductRepository productRepository;
    @Autowired
    OrderRepository orderRepository;
    @Autowired
    OrderPositionRepository orderPositionRepository;
    @Autowired
    CommissionGridService commissionGridService;
    @Autowired
    SizeRepository sizeRepository;
    @Autowired
    ProductItemRepository productItemRepository;
    @Autowired
    BrandRepository brandRepository;
    @Autowired
    UserRepository userRepository;
    @Autowired
    AgentReportService agentReportService;
    private User currentSeller;
    private Product currentProduct;
    private ProductItem currentProductItem;
    private Order currentOrder;
    private User currentBuyer;
    private AgentReport currentAgentReport;
    private Counterparty currentSellerCounterparty;

    @BeforeEach
    @Transactional
    public void init() {
        User user = new User()
                .setNickname(RandomStringUtils.randomAlphabetic(5))
                .setUserType(User.UserType.SIMPLE_USER)
                .setChangeTime(LocalDateTime.now())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        currentSeller = userRepository.saveAndFlush(user);

        User buyer = new User()
                .setNickname(RandomStringUtils.randomAlphabetic(5))
                .setUserType(User.UserType.SIMPLE_USER)
                .setChangeTime(LocalDateTime.now())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        currentBuyer = userRepository.saveAndFlush(buyer);


        Product product = new Product();
        product.setBrand(brandRepository.getOne(1L));
        product.setCategoryId(2L);
        product.setSeller(currentSeller);
        product.setStartPrice(BigDecimal.valueOf(10000));
        product.setCurrentPrice(BigDecimal.valueOf(INITIAL_PAYMENT_AMOUNT));
        product.setProductState(ProductState.PUBLISHED);
        product.setPublishTime(LocalDateTime.now().minusMonths(3).minusDays(1));
        currentProduct = productRepository.saveAndFlush(product);

        Counterparty counterparty = new CardCounterparty()
                .setUser(currentSeller)
                .setPaymentAccount(RandomStringUtils.randomNumeric(20))
                .setCreateTime(ZonedDateTime.now())
                .setChangeTime(ZonedDateTime.now());
        currentSellerCounterparty = counterpartyRepository.saveAndFlush(counterparty);

        Order order = new Order();
        order.setUuid(UUID.randomUUID());
        order.setState(OrderState.CREATED);
        order.setBuyer(buyer);
        order.setSellerCounterparty(counterparty);
        order.setAmount(BigDecimal.ONE);
        order.setEffectiveAmount(BigDecimal.valueOf(100));
        order.setPaymentVersion("tcb");

        ProductItem productItem = new ProductItem();
        productItem.setProduct(product);
        productItem.setSize(sizeRepository.getOne(1L));
        currentProductItem = productItemRepository.saveAndFlush(productItem);

        OrderPosition orderPosition = new OrderPosition();
        orderPosition.setState(OrderPositionState.VERIFICATION_OK);
        orderPosition.setIsEffective(true);
        orderPosition.setParticipatesInPayment(true);
        orderPosition.setStateTime(LocalDateTime.now());
        orderPosition.setProductItem(productItem);
        orderPosition.setAmount(BigDecimal.valueOf(INITIAL_PAYMENT_AMOUNT));
        orderPosition.setItemSaleAmount(orderPosition.getAmount());
        BigDecimal amount = product.getCurrentPrice();
        orderPosition.setAmount(amount);
        orderPosition.setItemSaleAmount(orderPosition.getAmount());
        orderPosition.setCommission(BigDecimal.valueOf(15.0));

        order.setOrderPosition(orderPosition);

        currentOrder = orderRepository.saveAndFlush(order);

        AgentReport agentReport = new AgentReport();
        agentReport.setState(null);
        agentReport.setOrder(currentOrder);
        agentReport.setPaymentAmount(BigDecimal.valueOf(INITIAL_PAYMENT_AMOUNT));
        agentReport.setIsConfirmed(false);
        agentReport.setUserType(User.UserType.SIMPLE_USER);
        agentReport.setCleaningAmount(BigDecimal.valueOf(500));
        agentReport.setDefectsDiscountAmount(BigDecimal.ZERO);
        agentReport.setCreateTime(ZonedDateTime.now());
        agentReport.setDateContract(ZonedDateTime.now());
        currentAgentReport = agentReportRepository.saveAndFlush(agentReport);
    }

    @AfterEach
    public void cleanup() {
        bankPaymentRepository.deleteAllInBatch();
        agentReportPaymentsRepository.deleteAllInBatch();
        List<AgentReport> agentReports = agentReportRepository.findConfirmedBySeller(currentSeller.getId());
        agentReports.forEach(agentReport -> agentReport.setAgentReportPayments(Collections.emptyList()));
        agentReportRepository.deleteInBatch(agentReports);
        orderRepository.delete(currentOrder);
        counterpartyRepository.delete(currentSellerCounterparty);
        productItemRepository.delete(currentProductItem);
        productRepository.delete(currentProduct);
        userRepository.delete(currentSeller);
        userRepository.delete(currentBuyer);
    }

    @Test
    public void confirmAgentReportSavesAgentReportPayments() {
        agentReportService.confirm(currentAgentReport,
                Collections.singletonList(new BonusDto(BigDecimal.valueOf(BONUS_AMOUNT), "***********", "12Storeez")), new AgentReportParams(currentAgentReport.getOrder().getId(), Boolean.FALSE));

        List<AgentReportPayments> all = agentReportPaymentsRepository.findAll().stream()
                .filter(it -> it.getAgentReport().getId().equals(currentAgentReport.getId()))
                .collect(Collectors.toList());
        assertThat(all).hasSize(2);
        assertThat(all.stream().mapToDouble(arp -> arp.getAmount().doubleValue()).sum()).isEqualByComparingTo(currentAgentReport.getPaymentAmount().doubleValue());
        assertThat(all).anyMatch(arp -> arp.getPhone().equals("***********") && arp.getAgentReportPaymentsId().getBank().equals("12Storeez"));
    }

    @Test
    public void preparePaymentSubtractBonusesFromPayment() {
        agentReportService.confirm(currentAgentReport,
                Collections.singletonList(new BonusDto(BigDecimal.valueOf(BONUS_AMOUNT), "***********", "12Storeez")), new AgentReportParams(currentAgentReport.getOrder().getId(), Boolean.FALSE));

        agentReportService.preparePayment(currentAgentReport.getId());

        List<BankPayment> bankPayments = bankPaymentRepository.findAllByAgentReportId(currentAgentReport.getId());
        assertThat(bankPayments).hasSize(1);
        BankPayment bankPayment = bankPayments.get(0);
        assertThat(bankPayment.getAmount().doubleValue()).isEqualTo(INITIAL_PAYMENT_AMOUNT - BONUS_AMOUNT);
    }

    @Test
    public void confirmAgentReportNotSavesZeroSumAgentReportPayment() {
        agentReportService.confirm(currentAgentReport,
                Collections.singletonList(new BonusDto(BigDecimal.valueOf(INITIAL_PAYMENT_AMOUNT), "***********", "12Storeez")), new AgentReportParams(currentAgentReport.getOrder().getId(), Boolean.FALSE));
        List<AgentReportPayments> agentReportPayments = agentReportPaymentsRepository.findAll();
        assertThat(agentReportPayments).hasSize(1);
        assertThat(agentReportPayments.get(0).getAmount().doubleValue()).isEqualByComparingTo(currentAgentReport.getPaymentAmount().doubleValue());
        assertThat(agentReportPayments.get(0).getPhone()).isEqualTo("***********");
        assertThat(agentReportPayments.get(0).getAgentReportPaymentsId().getBank()).isEqualTo("12Storeez");
    }

}
