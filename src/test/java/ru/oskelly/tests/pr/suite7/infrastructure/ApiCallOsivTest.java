package ru.oskelly.tests.pr.suite7.infrastructure;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.TestApiConfiguration;
import su.reddot.domain.service.dto.CounterpartyDTO;
import su.reddot.presentation.api.v2.Api2Response;

import java.util.Collection;

@Slf4j
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_07)
@TestMethodOrder(MethodOrderer.MethodName.class)
public class ApiCallOsivTest extends AbstractSpringTest {

    @Autowired
    private TestApiConfiguration testApiConfiguration;

    @Value("${test.api.user-email}")
    private String userEmail;
    @Value("${test.api.user-password}")
    private String userPassword;

    private static ApiV2Client apiV2Client;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    public void init() {
        apiV2Client = new ApiV2Client(userEmail, userPassword);
    }

    private String getApiv2CpPath(String lastCall) {
        return testApiConfiguration.getServerUrl() + "/api/v2/counterparty/" + lastCall;
    }

    @SneakyThrows
    private void validateResponseCallOkay(ResponseEntity<String> response) {
        Assertions.assertThat(response.getStatusCode().is2xxSuccessful()).isTrue();
        //
        Api2Response<Collection<CounterpartyDTO.CounterpartyType>> apiv2data = objectMapper.readValue(response.getBody(),
                new TypeReference<Api2Response<Collection<CounterpartyDTO.CounterpartyType>>>() {});
        //
        Assertions.assertThat(apiv2data.getData())
                .containsExactlyInAnyOrder(CounterpartyDTO.CounterpartyType.PHYS, CounterpartyDTO.CounterpartyType.CARD, CounterpartyDTO.CounterpartyType.JUR, CounterpartyDTO.CounterpartyType.IP);
    }

    @SneakyThrows
    private void validateResponseLazyFail(ResponseEntity<String> response) {
        Assertions.assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        //
        Api2Response<String> apiv2data = objectMapper.readValue(response.getBody(), new TypeReference<Api2Response<String>>() {});
        //
        Assertions.assertThat(apiv2data.getMessage()).matches("could not initialize proxy \\[.*\\] - no Session");
        Assertions.assertThat(apiv2data.getData()).isEqualTo("org.hibernate.LazyInitializationException");
    }

    @Test
    public void _01_callMethodWithOsiv() {  // Fail with no OSIV, okay with OSIV, okay in Monolith with CustomOSIV
        ResponseEntity<String> response = apiV2Client.request(getApiv2CpPath("availableTypes"),
                null, HttpMethod.GET, null, String.class, true);
        validateResponseCallOkay(response);
    }

    @Test
    public void _02_callMethodSkipOsiv() {  // Fail with no OSIV, okay with OSIV, fail in Monolith with CustomOSIV
        ResponseEntity<String> response = apiV2Client.request(getApiv2CpPath("availableTypesSkipOsiv"),
                null, HttpMethod.GET, null, String.class, true);
        validateResponseLazyFail(response);
    }

    @Test
    public void _03_callMethodAnnotate() {  // Okay in any case (using @Transactional)
        ResponseEntity<String> response = apiV2Client.request(getApiv2CpPath("availableTypesAnnotate"),
                null, HttpMethod.GET, null, String.class, true);
        validateResponseCallOkay(response);
    }

    @Test
    public void _04_callMethodTemplate() {  // Okay in any case (using transactionTemplate)
        ResponseEntity<String> response = apiV2Client.request(getApiv2CpPath("availableTypesTemplate"),
                null, HttpMethod.GET, null, String.class, true);
        validateResponseCallOkay(response);
    }

    @Test
    public void _05_callMethodAnnotateThenDirect() {  // Fail with no OSIV, okay with OSIV, fail in Monolith with CustomOSIV
        ResponseEntity<String> response = apiV2Client.request(getApiv2CpPath("availableTypesAnnotateThenDirect"),
                null, HttpMethod.GET, null, String.class, true);
        validateResponseLazyFail(response);
    }

    @Test
    public void _06_callMethodTemplateThenDirect() {  // Fail with no OSIV, okay with OSIV, fail in Monolith with CustomOSIV
        ResponseEntity<String> response = apiV2Client.request(getApiv2CpPath("availableTypesTemplateThenDirect"),
                null, HttpMethod.GET, null, String.class, true);
        validateResponseLazyFail(response);
    }

}
