package ru.oskelly.tests.pr.suite7.service;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2HomeCall;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.model.adminpanel.v2.PrimaryPageType;
import su.reddot.domain.service.dto.primary.AdditionalBannerDTO;
import su.reddot.domain.service.dto.primary.BannerDTO;
import su.reddot.domain.service.dto.primary.PrimaryContentDTO;
import su.reddot.presentation.api.v2_1.Api2_1Response;

import java.util.List;
import java.util.Objects;

@Layer
@Disabled("This is an E2E test with Moon: manual mode only")
@DevSuite(value = TestSuiteName.TEST_SUITE_07)
@TestMethodOrder(MethodOrderer.MethodName.class)
public class UserSegmentServiceE2ETest {

    private static ApiV2Client apiV2AnonClient;
    private static ApiV2Client apiV2BuysClient;
    private static ApiV2Client apiV2NoobClient;

    private static final Long ANY_COUNTRY_ID = null;
    private static final Long ARE_COUNTRY_ID = 2L;
    private static final Long QAT_COUNTRY_ID = 187L;
    private static final Long KWT_COUNTRY_ID = 193L;

    private static final String LOCALHOST_URL = "http://localhost:8080";
    private static final String MOONSTAND_URL = "https://moon.oskelly.tech";
    private static final String ACTIVE_APIURL = MOONSTAND_URL; // LOCALHOST_URL / MOONSTAND_URL

    @BeforeEach
    public void initialize() {
        if (Objects.isNull(apiV2AnonClient)) apiV2AnonClient = new ApiV2Client(null, null);
        if (Objects.isNull(apiV2BuysClient)) apiV2BuysClient = new ApiV2Client("<EMAIL>", "111");
        if (Objects.isNull(apiV2NoobClient)) apiV2NoobClient = new ApiV2Client("<EMAIL>", "n00bn00b");
    }

    @Test
    public void promoBannerAnonTest() {
        Api2_1Response<PrimaryContentDTO<AdditionalBannerDTO>> countryAny = ApiV2HomeCall.promoBanner(apiV2AnonClient, ACTIVE_APIURL, ANY_COUNTRY_ID);
        Assertions.assertThat(countryAny.getData().getContent().getTitle()).isEqualTo("ALL+ANY");
        //
        Api2_1Response<PrimaryContentDTO<AdditionalBannerDTO>> countryUae = ApiV2HomeCall.promoBanner(apiV2AnonClient, ACTIVE_APIURL, ARE_COUNTRY_ID);
        Assertions.assertThat(countryUae.getData().getContent().getTitle()).isEqualTo("ALL+UAE");
        //
        Api2_1Response<PrimaryContentDTO<AdditionalBannerDTO>> countryQat = ApiV2HomeCall.promoBanner(apiV2AnonClient, ACTIVE_APIURL, QAT_COUNTRY_ID);
        Assertions.assertThat(countryQat.getData().getContent().getTitle()).isEqualTo("ALL+ANY");
        //
        Api2_1Response<PrimaryContentDTO<AdditionalBannerDTO>> countryKwt = ApiV2HomeCall.promoBanner(apiV2AnonClient, ACTIVE_APIURL, KWT_COUNTRY_ID);
        Assertions.assertThat(countryKwt.getData().getContent().getTitle()).isEqualTo("ALL+ANY");
    }

    @Test
    public void promoBannerBuysTest() {
        Api2_1Response<PrimaryContentDTO<AdditionalBannerDTO>> countryAny = ApiV2HomeCall.promoBanner(apiV2BuysClient, ACTIVE_APIURL, ANY_COUNTRY_ID);
        Assertions.assertThat(countryAny.getData().getContent().getTitle()).isEqualTo("BUY+ANY");
        //
        Api2_1Response<PrimaryContentDTO<AdditionalBannerDTO>> countryUae = ApiV2HomeCall.promoBanner(apiV2BuysClient, ACTIVE_APIURL, ARE_COUNTRY_ID);
        Assertions.assertThat(countryUae.getData().getContent().getTitle()).isEqualTo("BUY+UAE");
        //
        Api2_1Response<PrimaryContentDTO<AdditionalBannerDTO>> countryQat = ApiV2HomeCall.promoBanner(apiV2BuysClient, ACTIVE_APIURL, QAT_COUNTRY_ID);
        Assertions.assertThat(countryQat.getData().getContent().getTitle()).isEqualTo("BUY+QAT");
        //
        Api2_1Response<PrimaryContentDTO<AdditionalBannerDTO>> countryKwt = ApiV2HomeCall.promoBanner(apiV2BuysClient, ACTIVE_APIURL, KWT_COUNTRY_ID);
        Assertions.assertThat(countryKwt.getData().getContent().getTitle()).isEqualTo("BUY+ANY");
    }

    @Test
    public void promoBannerNoobTest() {
        Api2_1Response<PrimaryContentDTO<AdditionalBannerDTO>> countryAny = ApiV2HomeCall.promoBanner(apiV2NoobClient, ACTIVE_APIURL, ANY_COUNTRY_ID);
        Assertions.assertThat(countryAny.getData().getContent().getTitle()).isEqualTo("ALL+ANY");
        //
        Api2_1Response<PrimaryContentDTO<AdditionalBannerDTO>> countryUae = ApiV2HomeCall.promoBanner(apiV2NoobClient, ACTIVE_APIURL, ARE_COUNTRY_ID);
        Assertions.assertThat(countryUae.getData().getContent().getTitle()).isEqualTo("ALL+UAE");
        //
        Api2_1Response<PrimaryContentDTO<AdditionalBannerDTO>> countryQat = ApiV2HomeCall.promoBanner(apiV2NoobClient, ACTIVE_APIURL, QAT_COUNTRY_ID);
        Assertions.assertThat(countryQat.getData().getContent().getTitle()).isEqualTo("ALL+ANY");
        //
        Api2_1Response<PrimaryContentDTO<AdditionalBannerDTO>> countryKwt = ApiV2HomeCall.promoBanner(apiV2NoobClient, ACTIVE_APIURL, KWT_COUNTRY_ID);
        Assertions.assertThat(countryKwt.getData().getContent().getTitle()).isEqualTo("ALL+ANY");
    }

    @Test
    public void primaryBannersAnonTest() {
        Api2_1Response<List<BannerDTO>> countryAny = ApiV2HomeCall.primaryBanners(apiV2AnonClient, ACTIVE_APIURL, PrimaryPageType.MALE, ANY_COUNTRY_ID);
        Assertions.assertThat(countryAny.getData()).anyMatch(it -> Objects.equals(it.getDescription(), "ALL+ANY"));
        //
        Api2_1Response<List<BannerDTO>> countryUae = ApiV2HomeCall.primaryBanners(apiV2AnonClient, ACTIVE_APIURL, PrimaryPageType.MALE, ARE_COUNTRY_ID);
        Assertions.assertThat(countryUae.getData()).anyMatch(it -> Objects.equals(it.getDescription(), "ALL+UAE"));
        //
        Api2_1Response<List<BannerDTO>> countryQat = ApiV2HomeCall.primaryBanners(apiV2AnonClient, ACTIVE_APIURL, PrimaryPageType.MALE, QAT_COUNTRY_ID);
        Assertions.assertThat(countryQat.getData()).anyMatch(it -> Objects.equals(it.getDescription(), "ALL+ANY"));
        //
        Api2_1Response<List<BannerDTO>> countryKwt = ApiV2HomeCall.primaryBanners(apiV2AnonClient, ACTIVE_APIURL, PrimaryPageType.MALE, KWT_COUNTRY_ID);
        Assertions.assertThat(countryKwt.getData()).anyMatch(it -> Objects.equals(it.getDescription(), "ALL+ANY"));
    }

    @Test
    public void primaryBannersBuysTest() {
        Api2_1Response<List<BannerDTO>> countryAny = ApiV2HomeCall.primaryBanners(apiV2BuysClient, ACTIVE_APIURL, PrimaryPageType.MALE, ANY_COUNTRY_ID);
        Assertions.assertThat(countryAny.getData()).anyMatch(it -> Objects.equals(it.getDescription(), "BUY+ANY"));
        //
        Api2_1Response<List<BannerDTO>> countryUae = ApiV2HomeCall.primaryBanners(apiV2BuysClient, ACTIVE_APIURL, PrimaryPageType.MALE, ARE_COUNTRY_ID);
        Assertions.assertThat(countryUae.getData()).anyMatch(it -> Objects.equals(it.getDescription(), "BUY+UAE"));
        //
        Api2_1Response<List<BannerDTO>> countryQat = ApiV2HomeCall.primaryBanners(apiV2BuysClient, ACTIVE_APIURL, PrimaryPageType.MALE, QAT_COUNTRY_ID);
        Assertions.assertThat(countryQat.getData()).anyMatch(it -> Objects.equals(it.getDescription(), "BUY+QAT"));
        //
        Api2_1Response<List<BannerDTO>> countryKwt = ApiV2HomeCall.primaryBanners(apiV2BuysClient, ACTIVE_APIURL, PrimaryPageType.MALE, KWT_COUNTRY_ID);
        Assertions.assertThat(countryKwt.getData()).anyMatch(it -> Objects.equals(it.getDescription(), "BUY+ANY"));
    }

    @Test
    public void primaryBannersNoobTest() {
        Api2_1Response<List<BannerDTO>> countryAny = ApiV2HomeCall.primaryBanners(apiV2NoobClient, ACTIVE_APIURL, PrimaryPageType.MALE, ANY_COUNTRY_ID);
        Assertions.assertThat(countryAny.getData()).anyMatch(it -> Objects.equals(it.getDescription(), "ALL+ANY"));
        //
        Api2_1Response<List<BannerDTO>> countryUae = ApiV2HomeCall.primaryBanners(apiV2NoobClient, ACTIVE_APIURL, PrimaryPageType.MALE, ARE_COUNTRY_ID);
        Assertions.assertThat(countryUae.getData()).anyMatch(it -> Objects.equals(it.getDescription(), "ALL+UAE"));
        //
        Api2_1Response<List<BannerDTO>> countryQat = ApiV2HomeCall.primaryBanners(apiV2NoobClient, ACTIVE_APIURL, PrimaryPageType.MALE, QAT_COUNTRY_ID);
        Assertions.assertThat(countryQat.getData()).anyMatch(it -> Objects.equals(it.getDescription(), "ALL+ANY"));
        //
        Api2_1Response<List<BannerDTO>> countryKwt = ApiV2HomeCall.primaryBanners(apiV2NoobClient, ACTIVE_APIURL, PrimaryPageType.MALE, KWT_COUNTRY_ID);
        Assertions.assertThat(countryKwt.getData()).anyMatch(it -> Objects.equals(it.getDescription(), "ALL+ANY"));
    }

}
