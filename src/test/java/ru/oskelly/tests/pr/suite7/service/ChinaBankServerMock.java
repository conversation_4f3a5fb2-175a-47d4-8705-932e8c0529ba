package ru.oskelly.tests.pr.suite7.service;

import lombok.SneakyThrows;
import org.apache.http.entity.ContentType;
import org.mockserver.integration.ClientAndServer;
import org.mockserver.mock.action.ExpectationResponseCallback;
import org.mockserver.model.Header;
import org.mockserver.model.HttpRequest;
import org.mockserver.model.HttpResponse;
import su.reddot.infrastructure.currency.ratesrequestservice.CurrencyRatesRequestAPIChinaConfig;

import static org.mockserver.model.HttpRequest.request;
import static org.mockserver.model.HttpResponse.response;
import static org.mockserver.model.HttpStatusCode.NOT_FOUND_404;
import static org.mockserver.model.HttpStatusCode.OK_200;

public class ChinaBankServerMock {

    private static final String MOCK_AUTH = "{\n" +
            "  \"access_token\":\"b9090b1d-28a5-4bfa-a4e9-ffbb5945d0fc\",\n" +
            "  \"token_type\":\"Bearer\",\n" +
            "  \"expires_in\":600,\n" +
            "  \"scope\":\"oob\"\n" +
            "}";
    private static final String MOCK_RATES = "{\"ProductType\": \"T/T exchange rates against USD\",\n" +
            "    \"products\": [\n" +
            "        {\n" +
            "            \"Language\": \"en-US\",\n" +
            "            \"BankSell\": \"1.3451100\",\n" +
            "            \"LastUpdateTime\": \"2025/02/27 18:04:02\",\n" +
            "            \"Currency\": \"BND\",\n" +
            "            \"ExchangeCode\": \"USD/BND\",\n" +
            "            \"BankBuy\": \"1.3355000\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"Language\": \"en-US\",\n" +
            "            \"BankSell\": \"1.4408500\",\n" +
            "            \"LastUpdateTime\": \"2025/02/27 18:04:02\",\n" +
            "            \"Currency\": \"CAD\",\n" +
            "            \"ExchangeCode\": \"USD/CAD\",\n" +
            "            \"BankBuy\": \"1.4239600\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"Language\": \"en-US\",\n" +
            "            \"BankSell\": \"0.9029500\",\n" +
            "            \"LastUpdateTime\": \"2025/02/27 18:04:02\",\n" +
            "            \"Currency\": \"CHF\",\n" +
            "            \"ExchangeCode\": \"USD/CHF\",\n" +
            "            \"BankBuy\": \"0.8922600\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"Language\": \"en-US\",\n" +
            "            \"BankSell\": \"7.3138300\",\n" +
            "            \"LastUpdateTime\": \"2025/02/27 18:04:02\",\n" +
            "            \"Currency\": \"CNH\",\n" +
            "            \"ExchangeCode\": \"USD/CNH\",\n" +
            "            \"BankBuy\": \"7.2332800\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"Language\": \"en-US\",\n" +
            "            \"BankSell\": \"7.1611900\",\n" +
            "            \"LastUpdateTime\": \"2025/02/27 18:04:02\",\n" +
            "            \"Currency\": \"DKK\",\n" +
            "            \"ExchangeCode\": \"USD/DKK\",\n" +
            "            \"BankBuy\": \"7.0682700\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"Language\": \"en-US\",\n" +
            "            \"BankSell\": \"7.8006000\",\n" +
            "            \"LastUpdateTime\": \"2025/02/27 18:04:02\",\n" +
            "            \"Currency\": \"HKD\",\n" +
            "            \"ExchangeCode\": \"USD/HKD\",\n" +
            "            \"BankBuy\": \"7.7493000\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"Language\": \"en-US\",\n" +
            "            \"BankSell\": \"150.8623600\",\n" +
            "            \"LastUpdateTime\": \"2025/02/27 18:04:02\",\n" +
            "            \"Currency\": \"JPY\",\n" +
            "            \"ExchangeCode\": \"USD/JPY\",\n" +
            "            \"BankBuy\": \"148.8687200\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"Language\": \"en-US\",\n" +
            "            \"BankSell\": \"11.2714100\",\n" +
            "            \"LastUpdateTime\": \"2025/02/27 18:04:02\",\n" +
            "            \"Currency\": \"NOK\",\n" +
            "            \"ExchangeCode\": \"USD/NOK\",\n" +
            "            \"BankBuy\": \"11.0621500\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"Language\": \"en-US\",\n" +
            "            \"BankSell\": \"10.7713800\",\n" +
            "            \"LastUpdateTime\": \"2025/02/27 18:04:02\",\n" +
            "            \"Currency\": \"SEK\",\n" +
            "            \"ExchangeCode\": \"USD/SEK\",\n" +
            "            \"BankBuy\": \"10.5588000\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"Language\": \"en-US\",\n" +
            "            \"BankSell\": \"1.3451100\",\n" +
            "            \"LastUpdateTime\": \"2025/02/27 18:04:02\",\n" +
            "            \"Currency\": \"SGD\",\n" +
            "            \"ExchangeCode\": \"USD/SGD\",\n" +
            "            \"BankBuy\": \"1.3355000\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"Language\": \"en-US\",\n" +
            "            \"BankSell\": \"34.4224700\",\n" +
            "            \"LastUpdateTime\": \"2025/02/27 18:04:02\",\n" +
            "            \"Currency\": \"THB\",\n" +
            "            \"ExchangeCode\": \"USD/THB\",\n" +
            "            \"BankBuy\": \"33.2110800\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"Language\": \"en-US\",\n" +
            "            \"BankSell\": \"18.7397300\",\n" +
            "            \"LastUpdateTime\": \"2025/02/27 18:04:02\",\n" +
            "            \"Currency\": \"ZAR\",\n" +
            "            \"ExchangeCode\": \"USD/ZAR\",\n" +
            "            \"BankBuy\": \"18.1021200\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"Language\": \"en-US\",\n" +
            "            \"BankSell\": \"1.0546800\",\n" +
            "            \"LastUpdateTime\": \"2025/02/27 18:04:02\",\n" +
            "            \"Currency\": \"EUR\",\n" +
            "            \"ExchangeCode\": \"EUR/USD\",\n" +
            "            \"BankBuy\": \"1.0419300\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"Language\": \"en-US\",\n" +
            "            \"BankSell\": \"0.6353400\",\n" +
            "            \"LastUpdateTime\": \"2025/02/27 18:04:02\",\n" +
            "            \"Currency\": \"AUD\",\n" +
            "            \"ExchangeCode\": \"AUD/USD\",\n" +
            "            \"BankBuy\": \"0.6264700\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"Language\": \"en-US\",\n" +
            "            \"BankSell\": \"1.2750000\",\n" +
            "            \"LastUpdateTime\": \"2025/02/27 18:04:02\",\n" +
            "            \"Currency\": \"GBP\",\n" +
            "            \"ExchangeCode\": \"GBP/USD\",\n" +
            "            \"BankBuy\": \"1.2602100\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"Language\": \"en-US\",\n" +
            "            \"BankSell\": \"0.5733700\",\n" +
            "            \"LastUpdateTime\": \"2025/02/27 18:04:02\",\n" +
            "            \"Currency\": \"NZD\",\n" +
            "            \"ExchangeCode\": \"NZD/USD\",\n" +
            "            \"BankBuy\": \"0.5641900\"\n" +
            "        }\n" +
            "    ]\n" +
            "}";

    private final ClientAndServer clientAndServer;

    public ChinaBankServerMock(CurrencyRatesRequestAPIChinaConfig chinaConfig) {
        clientAndServer = ClientAndServer.startClientAndServer(chinaConfig.getApiPort());

        clientAndServer.when(request().withMethod("POST")).respond(new MockB2PServer(chinaConfig));
    }

    public void stop() {
        clientAndServer.stop();
    }

    public static class MockB2PServer implements ExpectationResponseCallback {

        private final CurrencyRatesRequestAPIChinaConfig chinaConfig;

        public MockB2PServer(CurrencyRatesRequestAPIChinaConfig chinaConfig) {
            this.chinaConfig = chinaConfig;
        }

        @Override
        @SneakyThrows
        public HttpResponse handle(HttpRequest httpRequest) {
            HttpResponse httpResponse = response().withStatusCode(OK_200.code())
                    .withHeaders(new Header("Content-Type", ContentType.APPLICATION_JSON.getMimeType()));

            if (chinaConfig.getAuthUrl().equals(httpRequest.getPath().getValue())) {
                return httpResponse.withBody(MOCK_AUTH);
            } else if (chinaConfig.getRatesUrl().equals(httpRequest.getPath().getValue())) {
                return httpResponse.withBody(MOCK_RATES);
            }
            return response().withStatusCode(NOT_FOUND_404.code());
        }
    }

}
