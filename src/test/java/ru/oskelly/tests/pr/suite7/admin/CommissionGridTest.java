package ru.oskelly.tests.pr.suite7.admin;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.pr.AbstractServiceTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.TestApiConfiguration;
import su.reddot.domain.dao.AuthorityRepository;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.UserTradeStatRepository;
import su.reddot.domain.dao.commission.CommissionGridRepository;
import su.reddot.domain.dao.commission.CommissionRepository;
import su.reddot.domain.dao.commissiongridhistory.UserCommissionGridHistoryRepository;
import su.reddot.domain.dao.device.DeviceRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.commission.Commission;
import su.reddot.domain.model.commission.CommissionGrid;
import su.reddot.domain.model.commissiongridhistory.UserCommissionGridHistory;
import su.reddot.domain.model.commissiongridhistory.UserCommissionGridHistoryStatus;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.product.SalesChannel;
import su.reddot.domain.model.user.User;
import su.reddot.domain.model.user.UserAuthorityBinding;
import su.reddot.domain.service.adminpanel.product.domain.CommissionDTOV3;
import su.reddot.domain.service.adminpanel.product.domain.CommissionGridDTOV3;
import su.reddot.domain.service.adminpanel.user.commissiongridhistory.UserCommissionGridHistoryProcessorService;
import su.reddot.domain.service.config.modelmapper.ObjectMapperFactoryUtil;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.domain.service.user.UserService;
import su.reddot.presentation.api.v2.Api2Response;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_07)
public class CommissionGridTest extends AbstractServiceTest {

    @Autowired
    private TestApiConfiguration testApiConfiguration;

    private String newUserEmail = "<EMAIL>";
    private String newUserNickname = "commission";
    private String newUserPassword = "commissionGridTest";
    private String newUserPhone = "+71234567890";
    private List<Long> publishedProductIds = new ArrayList<>();
    private List<Long> commissionGridIds = new ArrayList<>();
    private User user;

    private final ObjectMapper objectMapper = ObjectMapperFactoryUtil.objectMapper();

    @Autowired
    private CommissionGridRepository commissionGridRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private CommissionRepository commissionRepository;
    @Autowired
    private ProductRepository productRepository;
    @Autowired
    private UserCommissionGridHistoryRepository commissionGridHistoryRepository;
    @Autowired
    private UserService userService;
    @Autowired
    private AuthorityRepository authorityRepository;
    @Autowired
    private DeviceRepository deviceRepository;
    @Autowired
    private UserCommissionGridHistoryProcessorService processorService;
    @Autowired
    private UserTradeStatRepository userTradeStatRepository;

    @BeforeEach
    @Transactional
    public void saveUserCommissionGrid() {
        user = registerUser();

        clean();

        commitAndStartNewTransaction();
    }

    @Test
    @Transactional
    public void testEditGrid() {
        CommissionGridDTOV3 newGrid = new CommissionGridDTOV3();
        newGrid.setName("New grid 1");
        newGrid.setFixedAmount(BigDecimal.ZERO);

        CommissionDTOV3 commission1 = new CommissionDTOV3(null, null, new BigDecimal("5000.00"),
                new BigDecimal("0.10"), new BigDecimal("0.15"),
                new BigDecimal("10"), new BigDecimal("15"));
        CommissionDTOV3 commission2 = new CommissionDTOV3(null, null, new BigDecimal("55000.00"),
                new BigDecimal("0.20"), new BigDecimal("0.25"),
                new BigDecimal("20"), new BigDecimal("25"));
        CommissionDTOV3 commission3 = new CommissionDTOV3(null, null, new BigDecimal("555000.00"),
                new BigDecimal("0.30"), new BigDecimal("0.35"),
                new BigDecimal("30"), new BigDecimal("35"));
        newGrid.setCommissions(Arrays.asList(commission1, commission2, commission3));

        //создание новой сетки
        CommissionGridDTOV3 createdGrid = editGrid(newGrid);
        commissionGridIds.add(createdGrid.getId());

        CommissionGrid commissionGrid = commissionGridRepository.findById(createdGrid.getId()).get();

        assertEquals(newGrid.getName(), commissionGrid.getName());
        assertEquals(CommissionGrid.Type.CUSTOM, commissionGrid.getType());
        assertEquals(3, commissionGrid.getCommissions().size());

        Commission c1 = commissionGrid.getCommissions().stream().filter(
                c -> c.getPublicPrice().compareTo(commission1.getPublicPrice()) == 0).findFirst().get();

        assertEquals(0, commission1.getValue().compareTo(c1.getValue()));
        assertEquals(0, commission1.getBoutiqueValue().compareTo(c1.getBoutiqueValue()));

        Commission c2 = commissionGrid.getCommissions().stream().filter(
                c -> c.getPublicPrice().compareTo(commission2.getPublicPrice()) == 0).findFirst().get();

        assertEquals(0, commission2.getValue().compareTo(c2.getValue()));
        assertEquals(0, commission2.getBoutiqueValue().compareTo(c2.getBoutiqueValue()));

        Commission c3 = commissionGrid.getCommissions().stream().filter(
                c -> c.getPublicPrice().compareTo(commission3.getPublicPrice()) == 0).findFirst().get();

        assertEquals(0, commission3.getValue().compareTo(c3.getValue()));
        assertEquals(0, commission3.getBoutiqueValue().compareTo(c3.getBoutiqueValue()));

        //Редактирование сетки. Изменение параметров одной из комиссий
        createdGrid.getCommissions().stream().filter(c ->
                c.getPublicPrice().compareTo(commission1.getPublicPrice()) == 0).findFirst().ifPresent(
                c -> c.setPublicPrice(new BigDecimal("6666.00"))
        );

        createdGrid = editGrid(createdGrid);
        commitAndStartNewTransaction();

        Commission editedCommission1 = commissionRepository.findById(c1.getId()).get();

        assertEquals(0, (new BigDecimal("6666.00")).compareTo(editedCommission1.getPublicPrice()));

        //Редактирование сетки. Удаление комиссии
        createdGrid.getCommissions().remove(0);

        setCommissionGrid(createdGrid.getId());

        CommissionGridDTOV3 newCreatedCommission = editGrid(createdGrid);
        commitAndStartNewTransaction();

        commissionGridIds.add(newCreatedCommission.getId());

        //должна создасться новая сетка
        assertNotEquals(newCreatedCommission.getId(), createdGrid.getId());
        //старая комиссия должна быть отмечена, как удаленная
        commissionGrid = commissionGridRepository.findById(createdGrid.getId()).get();
        assertNotNull(commissionGrid.getDeleteTime());

        //у пользователей со "старой" комиссией должна установиться новая
        user = userRepository.findById(user.getId()).get();

        assertEquals(newCreatedCommission.getId(), user.getCommissionGrid().getId());

        //должна появиться запись в истории
        List<UserCommissionGridHistory> history =
                commissionGridHistoryRepository.findAllByCommissionGridId(newCreatedCommission.getId());

        assertTrue(history.stream().anyMatch(h -> h.getCommissionGridId().longValue() ==
                newCreatedCommission.getId().longValue()));
    }

    @Test
    @Transactional
    public void testChangeProductPrice() {

        Long prod1Id = publishSimpleProduct(new BigDecimal("10000.00"));
        Long prod2Id = publishSimpleProduct(new BigDecimal("20000.00"));
        Long prod3Id = publishSimpleProduct(new BigDecimal("70000.00"));
        Long prod4Id = publishSimpleProduct(new BigDecimal("150000.00"));
        Long prod5Id = publishSimpleProduct(new BigDecimal("1000000.00"));
        Long prod6Id = publishSimpleProduct(new BigDecimal("1000000.00"));

        //товар в статусе SOLD не должен будет обрабатываться в пересчете цен
        Product p6 = productRepository.findById(prod6Id).get();
        p6.setProductState(ProductState.SOLD);
        productRepository.save(p6);

        commitAndStartNewTransaction();

        /*
            Цены с сеткой по умолчанию. Если дефолтная сетка изменится, тест перестанет проходить.
            Предусмотреть ли это?
         */

        List<Product> products = productRepository.findProductsBySellerId(user.getId());

        Product p1 = productRepository.findById(prod1Id).get();
        Product p2 = productRepository.findById(prod2Id).get();
        Product p3 = productRepository.findById(prod3Id).get();
        Product p4 = productRepository.findById(prod4Id).get();
        Product p5 = productRepository.findById(prod5Id).get();

        //цены без комиссии
        //комиссия 1	60000.00	0.25	1	0.10
        assertEquals(0, new BigDecimal("7500.00").compareTo(p1.getCurrentPriceWithoutCommission()));
        //комиссия 1	60000.00	0.25	1	0.10
        assertEquals(0, new BigDecimal("15000.00").compareTo(p2.getCurrentPriceWithoutCommission()));
        //комиссия 2	150000.00	0.20	1	0.15
        assertEquals(0, new BigDecimal("56000.00").compareTo(p3.getCurrentPriceWithoutCommission()));
        //комиссия 3	9999999999.00	0.16	1	0.19
        assertEquals(0, new BigDecimal("126000.00").compareTo(p4.getCurrentPriceWithoutCommission()));
        //комиссия 3	9999999999.00	0.16	1	0.19
        assertEquals(0, new BigDecimal("840000.00").compareTo(p5.getCurrentPriceWithoutCommission()));

        //меняем комиссионную сетку пользователя
        CommissionGridDTOV3 newGrid = new CommissionGridDTOV3();

        newGrid.setName("New grid 1");
        newGrid.setFixedAmount(BigDecimal.ZERO);
        CommissionDTOV3 commission1 = new CommissionDTOV3(null, null, new BigDecimal("20000.00"),
                new BigDecimal("0.10"), new BigDecimal("0.15"),
                new BigDecimal("10"), new BigDecimal("15"));
        CommissionDTOV3 commission2 = new CommissionDTOV3(null, null, new BigDecimal("70000.00"),
                new BigDecimal("0.30"), new BigDecimal("0.25"),
                new BigDecimal("30"), new BigDecimal("25"));
        CommissionDTOV3 commission3 = new CommissionDTOV3(null, null, new BigDecimal("999999999.00"),
                new BigDecimal("0.40"), new BigDecimal("0.35"),
                new BigDecimal("40"), new BigDecimal("35"));
        newGrid.setCommissions(Arrays.asList(commission1, commission2, commission3));

        //создание новой сетки
        CommissionGridDTOV3 createdGrid = editGrid(newGrid);
        commissionGridIds.add(createdGrid.getId());
        user.setCommissionGrid(commissionGridRepository.findById(createdGrid.getId()).get());
        userRepository.save(user);
        commitAndStartNewTransaction();

        //в истории должна появиться запись о смене сетки у пользователя
        List<UserCommissionGridHistory> history =
                commissionGridHistoryRepository.findAllByCommissionGridId(createdGrid.getId());

        /*
        Если на тесте запущен планировщик, он может тут же подобрать эту запись, поэтому желательно на тесте
        держать планировщик выключенным или поставить ему большой период запуска.
         */
        assertEquals(1, history.size());

        UserCommissionGridHistory historyEntity = history.get(0);

        assertEquals(user.getId(), historyEntity.getUserId());

        //эмуляция цикла работы планировщика
        processorService.commitInProgressStatus(historyEntity);
        processorService.processProductsForHistoryRecord(historyEntity.getId());
        commitAndStartNewTransaction();

        /*
            размер пачки в тестовых настройках - 3 товара, т.е. у первых трех товаров должна была измениться
            currentPrice, а currentPriceWithoutCommission остаться прежней.
            Для каждого товара сравниваем дату пересчета цены и сами цены
         */
        Product updatedP1 = productRepository.findById(prod1Id).get();

        assertTrue(p1.getLastPriceConvertTime() == null || p1.getLastPriceConvertTime().isBefore(updatedP1.getLastPriceConvertTime()));
        assertEquals(0, p1.getCurrentPriceWithoutCommission()
                .compareTo(updatedP1.getCurrentPriceWithoutCommission()));
        //продукт 1 попадает в "первую" комиссию в сетке (0.10). 8333 - 833.3 = 7500
        assertEquals(0, (new BigDecimal("8333.00").compareTo(
                updatedP1.getCurrentPrice())));

        Product updatedP2 = productRepository.findById(prod2Id).get();

        assertTrue(p2.getLastPriceConvertTime() == null || p2.getLastPriceConvertTime().isBefore(updatedP2.getLastPriceConvertTime()));
        assertEquals(0, p2.getCurrentPriceWithoutCommission()
                .compareTo(updatedP2.getCurrentPriceWithoutCommission()));
        //продукт 2 попадает в "первую" комиссию в сетке (0.10) 16667 - 1666.7 = 15000
        assertEquals(0, (new BigDecimal("16667.00").compareTo(
                updatedP2.getCurrentPrice())));

        Product updatedP3 = productRepository.findById(prod3Id).get();

        assertTrue(p3.getLastPriceConvertTime() == null || p3.getLastPriceConvertTime().isBefore(updatedP3.getLastPriceConvertTime()));
        assertEquals(0, p3.getCurrentPriceWithoutCommission()
                .compareTo(updatedP3.getCurrentPriceWithoutCommission()));
        //продукт 3 попадает в "третью" комиссию в сетке (0.40) 93333 - 37333 = 56000
        assertEquals(0, (new BigDecimal("93333.00").compareTo(
                updatedP3.getCurrentPrice())));

        //продукты 4, 5 не затрагиваются
        Product updatedP4 = productRepository.findById(prod4Id).get();
        assertEquals(p4.getLastPriceConvertTime(), updatedP4.getLastPriceConvertTime());
        assertEquals(0, p4.getCurrentPriceWithoutCommission()
                .compareTo(updatedP4.getCurrentPriceWithoutCommission()));
        assertEquals(0, p4.getCurrentPrice()
                .compareTo(updatedP4.getCurrentPrice()));

        Product updatedP5 = productRepository.findById(prod5Id).get();
        assertEquals(p5.getLastPriceConvertTime(), updatedP5.getLastPriceConvertTime());
        assertEquals(0, p5.getCurrentPriceWithoutCommission()
                .compareTo(updatedP5.getCurrentPriceWithoutCommission()));
        assertEquals(0, p5.getCurrentPrice()
                .compareTo(updatedP5.getCurrentPrice()));

        commitAndStartNewTransaction();

        history = commissionGridHistoryRepository.findAllByCommissionGridId(createdGrid.getId());
        historyEntity = history.get(0);

        //запись в истории должна была вернуться в статус QUEUED и должен был обновиться maxId
        assertEquals(user.getId(), historyEntity.getUserId());
        assertEquals(UserCommissionGridHistoryStatus.QUEUED, historyEntity.getStatus());
        assertEquals(p3.getId(), historyEntity.getLastUpdatedProductId());

        //следующий проход планировщика
        //должны обработаться продукты 4,5
        processorService.commitInProgressStatus(historyEntity);
        processorService.processProductsForHistoryRecord(historyEntity.getId());
        commitAndStartNewTransaction();

        updatedP4 = productRepository.findById(prod4Id).get();
        assertEquals(p4.getLastPriceConvertTime(), updatedP4.getLastPriceConvertTime());
        assertEquals(0, p4.getCurrentPriceWithoutCommission()
                .compareTo(updatedP4.getCurrentPriceWithoutCommission()));
        //продукт 4 попадает в "третью" комиссию в сетке (0.40) 210000 - 84000 = 126000
        assertEquals(0, (new BigDecimal("210000.00").compareTo(
                updatedP4.getCurrentPrice())));

        updatedP5 = productRepository.findById(prod5Id).get();
        assertEquals(p5.getLastPriceConvertTime(), updatedP5.getLastPriceConvertTime());
        assertEquals(0, p5.getCurrentPriceWithoutCommission()
                .compareTo(updatedP5.getCurrentPriceWithoutCommission()));
        //продукт 4 попадает в "третью" комиссию в сетке (0.40) 1400000 - 560000 = 840000
        assertEquals(0, (new BigDecimal("1400000.00").compareTo(
                updatedP5.getCurrentPrice())));

        //продукт 6 не должен попасть в выборку из-за статуса => он не должен измениться
        Product updatedP6 = productRepository.findById(prod6Id).get();
        assertEquals(p6.getLastPriceConvertTime(), updatedP6.getLastPriceConvertTime());
        assertEquals(0, p6.getCurrentPriceWithoutCommission()
                .compareTo(updatedP6.getCurrentPriceWithoutCommission()));
        assertEquals(0, p6.getCurrentPrice()
                .compareTo(updatedP6.getCurrentPrice()));

        commitAndStartNewTransaction();

        history = commissionGridHistoryRepository.findAllByCommissionGridId(createdGrid.getId());
        historyEntity = history.get(0);

        //запись в истории должна была вернуться в статус QUEUED и должен был обновиться maxId
        assertEquals(user.getId(), historyEntity.getUserId());
        assertEquals(UserCommissionGridHistoryStatus.QUEUED, historyEntity.getStatus());
        assertEquals(p5.getId(), historyEntity.getLastUpdatedProductId());

        //следующий проход планировщика
        //больше продуктов нет
        processorService.commitInProgressStatus(historyEntity);
        processorService.processProductsForHistoryRecord(historyEntity.getId());
        commitAndStartNewTransaction();

        history = commissionGridHistoryRepository.findAllByCommissionGridId(createdGrid.getId());
        historyEntity = history.get(0);

        //запись в истории должна была в статусе COMPLETED
        assertEquals(user.getId(), historyEntity.getUserId());
        assertEquals(UserCommissionGridHistoryStatus.COMPLETED, historyEntity.getStatus());
        assertEquals(p5.getId(), historyEntity.getLastUpdatedProductId());
    }

    private Product filterById(Long prodId, List<Product> products) {
        return products.stream().filter(p -> p.getId().longValue() == prodId.longValue()).findFirst().get();
    }

    private User registerUser() {
        if (user != null) {
            return user;
        }

        user = userRepository.findByEmail(newUserEmail);

        if (user != null) {
            return user;
        }

        UserService.EmailRegistrationRequest emailRegistrationRequest = new UserService.EmailRegistrationRequest();
        emailRegistrationRequest.setRegisterEmail(newUserEmail);
        emailRegistrationRequest.setRegisterPassword(newUserPassword);
        emailRegistrationRequest.setRegisterConfirmPassword(newUserPassword);
        emailRegistrationRequest.setRegisterNickname(newUserNickname);
        emailRegistrationRequest.setRegisterPhone(newUserPhone);
        user = userService.registerUser(emailRegistrationRequest,
                false, false, true, true, false);
        assertNotNull(user);
        assertNotNull(user.getId());

        if (!user.hasAuthority(AuthorityName.ADMIN)) {
            UserAuthorityBinding adminBinding = new UserAuthorityBinding(user,
                    authorityRepository.findByName(AuthorityName.ADMIN));
            user.setUserAuthorityBindings(Arrays.asList(adminBinding));
        }

        commitAndStartNewTransaction();
        return user;
    }

    private void setCommissionGrid(Long commissionGridId) {
        user = userRepository.findById(user.getId()).get();
        user.setCommissionGrid(commissionGridRepository.findById(commissionGridId).get());
        userRepository.save(user);
        commitAndStartNewTransaction();
    }

    private Long publishSimpleProduct(BigDecimal price) {
        ProductDTO productDTO = new ProductDTO();
        productDTO.setBrandId(3L);
        productDTO.setCategoryId(154L);
        productDTO.setSalesChannel(SalesChannel.WEBSITE);
        productDTO.setPrice(price);

        return publishProduct(productDTO);
    }

    private Long publishProduct(ProductDTO productDTO) {
        ResponseEntity<Api2Response<Object>> response = restTemplate.exchange(getUriWithParams(getPublishUrl(), getAuthorizeParams()),
                HttpMethod.POST, getHttpEntity(productDTO), new ParameterizedTypeReference<Api2Response<Object>>() {
                });

        Long productId = Long.parseLong(response.getBody().getData().toString());//response.getBody().getData();

        publishedProductIds.add(productId);

        return productId;
    }

    private CommissionGridDTOV3 editGrid(CommissionGridDTOV3 dto) {
        ResponseEntity<Api2Response<CommissionGridDTOV3>> response = restTemplate.exchange(getUriWithParams(getGridEditUrl(), getAuthorizeParams()),
                HttpMethod.POST, getHttpEntity(dto), new ParameterizedTypeReference<Api2Response<CommissionGridDTOV3>>() {
                });

        return response.getBody().getData();
    }

    @AfterEach
    @Transactional
    public void afterEach() {
        clean();
        commitTransaction();
    }

    private void clean() {
        user = userRepository.findById(user.getId()).get();

        user.setCommissionGrid(commissionGridRepository.getDefaultCommissionGrid());
        commitAndStartNewTransaction();

        //удаление всех созданных продуктов
        productRepository.deleteAll(productRepository.findProductsBySellerId(user.getId()));

        //Удаление всех записей из истории смены сеток
        commissionGridHistoryRepository.deleteAll(
                commissionGridHistoryRepository.findAllByCommissionGridIdIn(commissionGridIds));
        commissionGridHistoryRepository.deleteAll(
                commissionGridHistoryRepository.findAllByUserId(user.getId()));

        //удаление всех созданных сеток
        List<CommissionGrid> grids = commissionGridRepository.findAllById(commissionGridIds);
        grids.forEach(grid -> {
            commissionRepository.deleteAll(grid.getCommissions());
            grid.setCommissions(null);
        });

        //удаление всех тестовых сеток
        commissionGridRepository.deleteAll(grids);
    }

    private String getPublishUrl() {
        return testApiConfiguration.getServerUrl() + "/api/v2/productpublication/publish";
    }

    private String getGridEditUrl() {
        return testApiConfiguration.getServerUrl() + "/api/v3/admin/commissionGrid";
    }

    @Override
    protected String getEmail() {
        return newUserEmail;
    }

    @Override
    protected String getPassword() {
        return newUserPassword;
    }
}
