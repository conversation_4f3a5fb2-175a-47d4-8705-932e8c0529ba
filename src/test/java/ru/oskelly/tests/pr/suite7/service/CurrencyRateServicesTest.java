package ru.oskelly.tests.pr.suite7.service;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestUtils;
import su.reddot.domain.exception.CurrencyException;
import su.reddot.domain.model.currency.CurrencyRate;
import su.reddot.domain.service.currency.CurrencyRateService;
import su.reddot.domain.service.currency.CurrencyService;
import su.reddot.infrastructure.currency.ratesrequestservice.CurrencyRatesRequestAPIChinaConfig;
import su.reddot.infrastructure.currency.ratesrequestservice.CurrencyRatesRequestAPIChinaService;
import su.reddot.infrastructure.currency.ratesrequestservice.CurrencyRatesRequestCbrService;
import su.reddot.infrastructure.currency.ratesrequestservice.CurrencyRatesRequestMoexService;
import su.reddot.infrastructure.currency.service.CurrencyRatesImportScheduler;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.Comparator;
import java.util.Currency;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Optional;

@ContextConfiguration(classes = {OrderFlowTestUtils.class})
public class CurrencyRateServicesTest extends AbstractSpringTest {

    @Autowired
    private CurrencyRatesImportScheduler currencyRatesImportScheduler;
    @Autowired
    private CurrencyRateService currencyRateService;
    @Autowired
    private CurrencyService currencyService;

    @Autowired
    private CurrencyRatesRequestCbrService currencyRateImportCbrService;
    @Autowired
    private CurrencyRatesRequestMoexService currencyRateImportMoexService;
    @Autowired
    private CurrencyRatesRequestAPIChinaService currencyRatesRequestAPIChinaService;
    @Autowired
    private CurrencyRatesRequestAPIChinaConfig currencyRatesRequestAPIChinaConfig;

    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;

    private static ChinaBankServerMock chinaBankServerMock;

    private static final Currency USD = Currency.getInstance("USD");
    private static final int USD_CODE = USD.getNumericCode();
    private static final int RUB_CODE = Currency.getInstance("RUB").getNumericCode();
    private static final int EUR_CODE = Currency.getInstance("EUR").getNumericCode();
    private static final int ZWL_CODE = Currency.getInstance("ZWL").getNumericCode();
    private static final int TMT_CODE = Currency.getInstance("TMT").getNumericCode();
    private static final int KGS_CODE = Currency.getInstance("KGS").getNumericCode();
    private static final int CNY_CODE = Currency.getInstance("CNY").getNumericCode();

    @PostConstruct
    private void init() {
        chinaBankServerMock = Objects.isNull(chinaBankServerMock) ?
                new ChinaBankServerMock(currencyRatesRequestAPIChinaConfig) : chinaBankServerMock;
    }

    @AfterAll
    public static void done() {
        chinaBankServerMock.stop();
    }

    @Test
    @Transactional
    public void ratePercentThresholdTest() {
        int baseCurrencyIsoNumber = currencyService.getBaseCurrency().getIsoNumber();

        CurrencyRate usdRate = currencyRateService.getAllRates().stream()
                .filter(it -> (it.getCurrency().getIsoNumber() == USD_CODE
                        && it.getCurrencyTo().getIsoNumber() == baseCurrencyIsoNumber))
                .findFirst()
                .orElse(null);

        //
        BigDecimal minValue = usdRate.getRateValue().divide(BigDecimal.TEN, 2, RoundingMode.HALF_UP);
        Throwable upLimitException = Assertions.catchThrowable(() ->
                currencyRateService.updateRateValueByCurrencyId(usdRate.getCurrency().getId(),
                        usdRate.getCurrencyTo().getId(), minValue, null));
        Assertions.assertThat(upLimitException)
                .isInstanceOf(CurrencyException.class)
                .hasMessageMatching("Unable to update currency rate .* percent threshold limit .*");
        //
        BigDecimal maxValue = usdRate.getRateValue().multiply(BigDecimal.TEN);
        Throwable dnLimitException = Assertions.catchThrowable(() ->
                currencyRateService.updateRateValueByCurrencyId(usdRate.getCurrency().getId(),
                        usdRate.getCurrencyTo().getId(), maxValue, null));
        Assertions.assertThat(dnLimitException)
                .isInstanceOf(CurrencyException.class)
                .hasMessageMatching("Unable to update currency rate .* percent threshold limit .*");
    }

    @Test
    public void getRatesWithCbr2Day() {
        BigDecimal usdRateToRub = currencyRateImportCbrService.getRateValue(USD_CODE, RUB_CODE, LocalDate.now());
        Assertions.assertThat(usdRateToRub).isGreaterThan(BigDecimal.valueOf(75_00, 2)); // SAD_BUT
        Assertions.assertThat(usdRateToRub).isLessThan(BigDecimal.valueOf(150_00, 2));   // HOPE_SO
        //
        Assertions.assertThatThrownBy(() -> currencyRateImportCbrService.getRateValue(USD_CODE, EUR_CODE, LocalDate.now()))
                .isInstanceOf(IllegalArgumentException.class);
        //
        Assertions.assertThatThrownBy(() -> currencyRateImportCbrService.getRateValue(ZWL_CODE, RUB_CODE, LocalDate.now()))
                .isInstanceOf(NoSuchElementException.class);
    }

    @Test
    public void getRatesWithCbr0104() {
        BigDecimal usdRateToRubAt20240401 = currencyRateImportCbrService.getRateValue(USD_CODE, RUB_CODE, LocalDate.of(2020, 4, 1));
        Assertions.assertThat(usdRateToRubAt20240401).isEqualByComparingTo(BigDecimal.valueOf(77_7325, 4));
        //
        BigDecimal usdRateToRubAt20120401 = currencyRateImportCbrService.getRateValue(USD_CODE, RUB_CODE, LocalDate.of(2012, 4, 1));
        Assertions.assertThat(usdRateToRubAt20120401).isEqualByComparingTo(BigDecimal.valueOf(29_3282, 4));
    }

    @Test
    public void getRatesWithMoex2Day() {
        BigDecimal kgsRateToRub = currencyRateImportMoexService.getRateValue(KGS_CODE, RUB_CODE, LocalDate.now());
        Assertions.assertThat(kgsRateToRub).isGreaterThan(BigDecimal.valueOf(60, 2));
        Assertions.assertThat(kgsRateToRub).isLessThan(BigDecimal.valueOf(2_00, 2));
        //
        Assertions.assertThatThrownBy(() -> currencyRateImportMoexService.getRateValue(USD_CODE, EUR_CODE, LocalDate.now()))
                .isInstanceOf(IllegalArgumentException.class);
        //
        Assertions.assertThatThrownBy(() -> currencyRateImportMoexService.getRateValue(ZWL_CODE, RUB_CODE, LocalDate.now()))
                .isInstanceOf(NoSuchElementException.class);
    }

    @Test
    public void getRatesWithAPIChina2Day() {
        BigDecimal rate = currencyRatesRequestAPIChinaService.getRateValue(CNY_CODE, USD_CODE, LocalDate.now());
        Assertions.assertThat(rate).isGreaterThan(BigDecimal.valueOf(9, 2));
        Assertions.assertThat(rate).isLessThan(BigDecimal.valueOf(50, 2));
        //
        Assertions.assertThatThrownBy(() -> currencyRatesRequestAPIChinaService.getRateValue(USD_CODE, EUR_CODE, LocalDate.now()))
                .isInstanceOf(IllegalArgumentException.class);
        //
        Assertions.assertThatThrownBy(() -> currencyRatesRequestAPIChinaService.getRateValue(ZWL_CODE, USD_CODE, LocalDate.now()))
                .isInstanceOf(NoSuchElementException.class);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void updateRatesE2ETest() {
        su.reddot.domain.model.address.Currency baseCurrency = currencyService.getBaseCurrency();
        //
        BigDecimal deltaSteps = BigDecimal.valueOf(1, 8);
        orderFlowTestUtils.setCurrencyRate("USD", baseCurrency.getIsoCode(), BigDecimal.valueOf(100_00, 2).add(deltaSteps));
        orderFlowTestUtils.setCurrencyRate("EUR", baseCurrency.getIsoCode(), BigDecimal.valueOf(100_00, 2).add(deltaSteps));
        orderFlowTestUtils.setCurrencyRate("KGS", baseCurrency.getIsoCode(), BigDecimal.valueOf(100_00, 4).add(deltaSteps));
        orderFlowTestUtils.setCurrencyRate("CNY", USD.getCurrencyCode(), BigDecimal.valueOf(13, 2).add(deltaSteps));

        commitAndStartNewTransaction();
        //
        ZonedDateTime initAt = ZonedDateTime.now();
        ZonedDateTime nullAt = initAt.minusDays(1);
        //
        List<CurrencyRate> ratesOnInit = currencyRateService.getAllRates();
        ZonedDateTime maxTimeOnInit = ratesOnInit.stream().map(CurrencyRate::getLastRateUpdateTime)
                .filter(Objects::nonNull).max(Comparator.naturalOrder())
                .orElse(nullAt);
        Assertions.assertThat(maxTimeOnInit).isBeforeOrEqualTo(initAt);
        //
        currencyRatesImportScheduler.updateCurrencyRate();
        commitAndStartNewTransaction();
        //
        List<CurrencyRate> ratesUpdate = currencyRateService.getAllRates();
        Optional<CurrencyRate> usdRateUpdate = ratesUpdate.stream()
                .filter(it -> Objects.equals(it.getCurrency().getIsoNumber(), USD_CODE)
                        && Objects.equals(it.getCurrencyTo().getIsoNumber(), baseCurrency.getIsoNumber())).findFirst();
        Assertions.assertThat(usdRateUpdate).hasValueSatisfying(it -> {  // Update my USD (with CBR)!
            Assertions.assertThat(it.getLastRateUpdateTime()).isAfter(initAt);
            Assertions.assertThat(it.getLastRateUpdateSource()).isEqualTo(CurrencyRatesRequestCbrService.SOURCE_RATE_NAME);
        });
        Optional<CurrencyRate> eurRateUpdate = ratesUpdate.stream()
                .filter(it -> Objects.equals(it.getCurrency().getIsoNumber(), EUR_CODE)
                        && Objects.equals(it.getCurrencyTo().getIsoNumber(), baseCurrency.getIsoNumber())).findFirst();
        Assertions.assertThat(eurRateUpdate).hasValueSatisfying(it -> { // Update my EUR (with CBR)
            Assertions.assertThat(it.getLastRateUpdateTime()).isAfter(initAt);
            Assertions.assertThat(it.getLastRateUpdateSource()).isEqualTo(CurrencyRatesRequestCbrService.SOURCE_RATE_NAME);
        });
        Optional<CurrencyRate> kgsRateUpdate = ratesUpdate.stream()
                .filter(it -> Objects.equals(it.getCurrency().getIsoNumber(), KGS_CODE)
                        && Objects.equals(it.getCurrencyTo().getIsoNumber(), baseCurrency.getIsoNumber())).findFirst();
        Assertions.assertThat(kgsRateUpdate).hasValueSatisfying(it -> {  // Update my KGS (with MICEX)
            Assertions.assertThat(it.getLastRateUpdateTime()).isAfter(initAt);
            Assertions.assertThat(it.getLastRateUpdateSource()).isEqualTo(CurrencyRatesRequestMoexService.SOURCE_RATE_NAME);
        });
        Optional<CurrencyRate> tmtRateUpdate = ratesUpdate.stream()
                .filter(it -> Objects.equals(it.getCurrency().getIsoNumber(), TMT_CODE)
                        && Objects.equals(it.getCurrencyTo().getIsoNumber(), baseCurrency.getIsoNumber())).findFirst();
        Assertions.assertThat(tmtRateUpdate).hasValueSatisfying(it -> {  // Don`t touch my TMT! :)
            Assertions.assertThat(Optional.ofNullable(it.getLastRateUpdateTime()).orElse(nullAt)).isBefore(initAt);
            Assertions.assertThat(it.getLastRateUpdateSource()).isNull();
        });
        Optional<CurrencyRate> cnyRateUpdate = ratesUpdate.stream()
                .filter(it -> Objects.equals(it.getCurrency().getIsoNumber(), CNY_CODE)
                        && Objects.equals(it.getCurrencyTo().getIsoNumber(), USD.getNumericCode())).findFirst();
        Assertions.assertThat(cnyRateUpdate).hasValueSatisfying(it -> {  // Update my CNY (with APICHINA)
            Assertions.assertThat(it.getLastRateUpdateTime()).isAfter(initAt);
            Assertions.assertThat(it.getLastRateUpdateSource()).isEqualTo(CurrencyRatesRequestAPIChinaService.SOURCE_RATE_NAME);
        });
    }

}
