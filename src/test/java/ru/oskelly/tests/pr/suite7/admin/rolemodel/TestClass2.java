package ru.oskelly.tests.pr.suite7.admin.rolemodel;

import com.fasterxml.jackson.annotation.JsonFilter;
import lombok.Data;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.infrastructure.configuration.jackson.RenderAction;
import su.reddot.infrastructure.configuration.jackson.Visibility;

import static su.reddot.infrastructure.configuration.JacksonConfiguration.ROLES_FILTER;

@Data
@JsonFilter(ROLES_FILTER)
public class TestClass2 {

    private String class2FieldToSkip = "class2FieldToSkip";

    @Visibility(
            roles = {AuthorityName.CONTENT_DELETE, AuthorityName.BOUTIQUE_SALES},
            action = RenderAction.HIDE
    )
    private String class2FieldHideForAny = "class2FieldHideForAny";
}
