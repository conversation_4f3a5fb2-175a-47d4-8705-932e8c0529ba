package ru.oskelly.tests.pr.suite7.admin.rolemodel;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Component;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.infrastructure.security.token.UserIdAuthenticationToken;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class AuthInjector implements AuthInjectorIF {
    private Authentication authentication;

    @Override
    public void initCurrentAuthentication(List<AuthorityName> authorityNames) {
        UserIdAuthenticationToken userIdAuthenticationToken =
                new UserIdAuthenticationToken(1L,
                        authorityNames.stream().map(authority ->
                                new SimpleGrantedAuthority(authority.name())).collect(Collectors.toList()));
        userIdAuthenticationToken.setAuthenticated(true);

        authentication = userIdAuthenticationToken;
    }

    @Override
    public Authentication getCurrentAuthentication() {
        return authentication;
    }
}
