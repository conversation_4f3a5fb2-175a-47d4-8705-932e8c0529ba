package ru.oskelly.tests.pr.suite7.service;

import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.session.FindByIndexNameSessionRepository;
import org.springframework.session.Session;
import ru.oskelly.tests.AbstractSpringTest;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.infrastructure.security.token.UserIdAuthenticationToken;

import java.util.Map;
import java.util.Objects;

@Slf4j
public class SessionsServicesTest extends AbstractSpringTest {

    @Autowired
    private FindByIndexNameSessionRepository persistentSessionRepository;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private static final long USER_ID = 999;

    private void initSessionsData() {
        String sessionData0Sql = "INSERT INTO public.spring_session (primary_id, session_id, creation_time, last_access_time, max_inactive_interval, expiry_time, principal_name)\n" +
                "VALUES('43f10ea5-b1e0-42eb-b526-39b1ab0745e5', '0b2c8a05-537a-486e-8f99-830febc01e22', 1739958490126, 1739958492904, 172800, 1740131292904, '999')\n" +
                "ON CONFLICT DO NOTHING";
        jdbcTemplate.execute(sessionData0Sql);
        String sessionAttr1Sql = "INSERT INTO public.spring_session_attributes(session_primary_id, attribute_name, attribute_bytes)\n" +
                "VALUES('43f10ea5-b1e0-42eb-b526-39b1ab0745e5', 'SPRING_SECURITY_CONTEXT', decode('ACED00057372003D6F72672E737072696E676672616D65776F726B2E73656375726974792E636F72652E636F6E746578742E5365637572697479436F6E74657874496D706C00000000000001FE0200014C000E61757468656E7469636174696F6E7400324C6F72672F737072696E676672616D65776F726B2F73656375726974792F636F72652F41757468656E7469636174696F6E3B78707372004173752E726564646F742E696E6672617374727563747572652E73656375726974792E746F6B656E2E55736572496441757468656E7469636174696F6E546F6B656EAC82434E2E53B9700200014C00067573657249647400104C6A6176612F6C616E672F4C6F6E673B787200476F72672E737072696E676672616D65776F726B2E73656375726974792E61757468656E7469636174696F6E2E416273747261637441757468656E7469636174696F6E546F6B656ED3AA287E6E47640E0200035A000D61757468656E746963617465644C000B617574686F7269746965737400164C6A6176612F7574696C2F436F6C6C656374696F6E3B4C000764657461696C737400124C6A6176612F6C616E672F4F626A6563743B787001737200266A6176612E7574696C2E436F6C6C656374696F6E7324556E6D6F6469666961626C654C697374FC0F2531B5EC8E100200014C00046C6973747400104C6A6176612F7574696C2F4C6973743B7872002C6A6176612E7574696C2E436F6C6C656374696F6E7324556E6D6F6469666961626C65436F6C6C656374696F6E19420080CB5EF71E0200014C00016371007E00067870737200136A6176612E7574696C2E41727261794C6973747881D21D99C7619D03000149000473697A6578700000000D77040000000D7372002073752E726564646F742E646F6D61696E2E6D6F64656C2E417574686F726974794372D3E64563D6430200034C0002696471007E00044C00046E616D6574002C4C73752F726564646F742F646F6D61696E2F6D6F64656C2F656E756D732F417574686F726974794E616D653B4C0004747970657400304C73752F726564646F742F646F6D61696E2F6D6F64656C2F417574686F7269747924417574686F72697479547970653B78707372000E6A6176612E6C616E672E4C6F6E673B8BE490CC8F23DF0200014A000576616C7565787200106A6176612E6C616E672E4E756D62657286AC951D0B94E08B020000787000000000000000017E72002A73752E726564646F742E646F6D61696E2E6D6F64656C2E656E756D732E417574686F726974794E616D6500000000000000001200007872000E6A6176612E6C616E672E456E756D0000000000000000120000787074000541444D494E7E72002E73752E726564646F742E646F6D61696E2E6D6F64656C2E417574686F7269747924417574686F726974795479706500000000000000001200007871007E001774000541444D494E7371007E000F7371007E001300000000000000027E71007E001674001250524F445543545F4D4F4445524154494F4E7E71007E001A7400094D4F44455241544F527371007E000F7371007E001300000000000000037E71007E0016740014415554484F524954595F4D4F4445524154494F4E71007E00217371007E000F7371007E001300000000000000067E71007E00167400104F524445525F4D4F4445524154494F4E71007E00217371007E000F7371007E001300000000000000047E71007E001674000E434F4E54454E545F43524541544571007E00217371007E000F7371007E001300000000000000057E71007E001674000E434F4E54454E545F44454C45544571007E00217371007E000F7371007E001300000000000000077E71007E001674000F555345525F4D4F4445524154494F4E71007E00217371007E000F7371007E001300000000000000087E71007E001674001543414E5F564945575F414C4C5F50524F445543545371007E00217371007E000F7371007E001300000000000000097E71007E00167400104F464645525F4D4F4445524154494F4E71007E00217371007E000F7371007E0013000000000000000A7E71007E00167400155245544F554348494E475F4D4F4445524154494F4E71007E00217371007E000F7371007E001300000000000000127E71007E001674000D4F524445525F5041594F55545371007E00217371007E000F7371007E0013000000000000002C7E71007E00167400224F524445525F4D414E55414C5F4348414E47455F44454C49564552595F535441544571007E00217371007E000F7371007E0013000000000000002D7E71007E00167400134F524445525F4D414E55414C5F43414E43454C71007E00217871007E000E707371007E00130000000000000346','hex'))" +
                "ON CONFLICT DO NOTHING";
        jdbcTemplate.execute(sessionAttr1Sql);
        String sessionAttr2Sql = "INSERT INTO public.spring_session_attributes(session_primary_id, attribute_name, attribute_bytes)\n" +
                "VALUES('43f10ea5-b1e0-42eb-b526-39b1ab0745e5', 'device_id', decode('ACED00057372000E6A6176612E6C616E672E4C6F6E673B8BE490CC8F23DF0200014A000576616C7565787200106A6176612E6C616E672E4E756D62657286AC951D0B94E08B02000078700000000000025FEE','hex'))\n" +
                "ON CONFLICT DO NOTHING";
        jdbcTemplate.execute(sessionAttr2Sql);
    }

    @Test
    public void sessionsReadTest() {
        initSessionsData();
        //
        Map<String, Session> sessions = persistentSessionRepository.findByIndexNameAndIndexValue(FindByIndexNameSessionRepository.PRINCIPAL_NAME_INDEX_NAME, String.valueOf(USER_ID));
        Assertions.assertThat(sessions).hasSize(1);
        //
        Session session = sessions.get("0b2c8a05-537a-486e-8f99-830febc01e22");
        Assertions.assertThat(session.getAttributeNames()).hasSize(2);
        //
        Object attr1Value = session.getAttribute("device_id");
        Assertions.assertThat(attr1Value).isEqualTo(155630L);
        //
        Object attr2Value = session.getAttribute("SPRING_SECURITY_CONTEXT");
        Assertions.assertThat(attr2Value).isInstanceOf(SecurityContext.class);
        SecurityContext secContext = (SecurityContext) attr2Value;
        Assertions.assertThat(secContext.getAuthentication()).isInstanceOf(UserIdAuthenticationToken.class);
        UserIdAuthenticationToken userIdToken = (UserIdAuthenticationToken) secContext.getAuthentication();
        Assertions.assertThat(userIdToken.getUserId()).isEqualTo(838L);
        Assertions.assertThat(userIdToken.getAuthorities()).anyMatch(it ->
                Objects.equals(it.getAuthority(), AuthorityName.PRODUCT_MODERATION.toString())
        );
    }

}
