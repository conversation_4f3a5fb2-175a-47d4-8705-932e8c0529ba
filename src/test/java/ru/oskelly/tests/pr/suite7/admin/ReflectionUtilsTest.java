package ru.oskelly.tests.pr.suite7.admin;

import lombok.EqualsAndHashCode;
import org.junit.jupiter.api.Test;
import org.modelmapper.internal.Pair;
import su.reddot.domain.service.util.ReflectionsUtil;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;


public class ReflectionUtilsTest {
    @EqualsAndHashCode
    class A {
        int fieldInA = 5;
    }

    @EqualsAndHashCode
    class B {
        A fieldAInB = new A();
        A fieldAInBNull;
    }

    @EqualsAndHashCode
    class C {
        private B fieldBInC = new B();
        private B fieldBInCNull;
        A fieldAInC;
    }

    @Test
    public void testExtractFieldsFromClass() {
        List<Field> fieldsOfC = ReflectionsUtil.extractFieldsFromClass(C.class);
        assertTrue(fieldsOfC.size() >= 3);

        assertTrue(fieldsOfC.stream().filter(f -> "fieldBInC".equalsIgnoreCase(f.getName())).findFirst().isPresent());
        assertTrue(fieldsOfC.stream().filter(f -> "fieldAInC".equalsIgnoreCase(f.getName())).findFirst().isPresent());
        assertTrue(fieldsOfC.stream().filter(f -> "fieldBInCNull".equalsIgnoreCase(f.getName())).findFirst().isPresent());
    }

    @Test
    public void testFindFieldAccessible() {
        Field fieldBInC = ReflectionsUtil.findFieldAccessible(
                ReflectionsUtil.extractFieldsFromClass(C.class),
                "fieldBInC"
        );
        assertNotNull(fieldBInC);
        assertEquals("fieldBInC", fieldBInC.getName());
    }

    @Test
    public void testGetObjectFieldValue() throws IllegalAccessException {
        A aObj = new A();
        Integer i = (Integer) ReflectionsUtil.getObjectFieldValue(aObj, "fieldInA");

        assertNotNull(i);
        assertTrue(5 == i.intValue());

        B bObj = new B();

        A fieldAInBNull = (A) ReflectionsUtil.getObjectFieldValue(bObj, "fieldAInBNull");
        assertNull(fieldAInBNull);
    }

    @Test
    public void testFindFieldByNamesPath() throws IllegalAccessException {
        C cObj = new C();
        Pair<Object, Field> result = ReflectionsUtil.findFieldByNamesPath(cObj, Arrays.asList("fieldInA"));

        assertNull(result);

        //"обычное" поле класса
        result = ReflectionsUtil.findFieldByNamesPath(cObj, Arrays.asList("fieldAInC"));

        assertEquals(cObj, result.getLeft());
        assertEquals("fieldAInC", result.getRight().getName());

        //"глубокое" поле класса
        //значение первого поля в пути - null
        result = ReflectionsUtil.findFieldByNamesPath(cObj, Arrays.asList("fieldBInCNull", "fieldAInBNull", "fieldInA"));
        assertNull(result);

        //значение второго первого поля в пути - null
        result = ReflectionsUtil.findFieldByNamesPath(cObj, Arrays.asList("fieldBInC", "fieldAInBNull", "fieldAInB"));
        assertNull(result);

        //"нормальное" поле объект
        result = ReflectionsUtil.findFieldByNamesPath(cObj, Arrays.asList("fieldBInC", "fieldAInBNull"));

        assertEquals(cObj.fieldBInC, result.getLeft());
        assertEquals("fieldAInBNull", result.getRight().getName());

        //"нормальное" поле примитив
        result = ReflectionsUtil.findFieldByNamesPath(cObj, Arrays.asList("fieldBInC", "fieldAInB", "fieldInA"));

        assertEquals(cObj.fieldBInC.fieldAInB, result.getLeft());
        assertEquals("fieldInA", result.getRight().getName());
        assertTrue(5 == ((Integer)result.getRight().get(result.getLeft())));
    }
}
