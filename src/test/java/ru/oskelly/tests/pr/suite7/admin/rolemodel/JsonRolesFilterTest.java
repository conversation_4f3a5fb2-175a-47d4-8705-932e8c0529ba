package ru.oskelly.tests.pr.suite7.admin.rolemodel;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.TestApiConfiguration;
import su.reddot.domain.model.enums.AuthorityName;

import java.util.ArrayList;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class JsonRolesFilterTest extends AbstractSpringTest {

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    private TestApiConfiguration testApiConfiguration;

    @Autowired
    AuthInjectorIF authInjector;

    private TestRestTemplate restTemplate = TestUtils.getTestRestTemplate();

    //@Test
    public void testRoles() throws JsonProcessingException {
        authInjector.initCurrentAuthentication(Arrays.asList(AuthorityName.BOUTIQUE_SALES));

        String response = restTemplate.exchange(
                testApiConfiguration.getServerUrl() + "/testRolesDto/dto",
                HttpMethod.GET, new HttpEntity<String>(new HttpHeaders()), String.class).getBody();

        System.out.println(response);

        assertTrue(response.contains("fieldSkip"));
        assertFalse(response.contains("hideForAny"));
        assertTrue(response.contains("showForAny"));
        assertTrue(response.contains("hideForAll"));
        assertFalse(response.contains("showForAll"));
        assertTrue(response.contains("objectFieldToSkip"));
        assertFalse(response.contains("classFieldToHideForAny"));
        assertTrue(response.contains("classFieldToShowForAny"));
        assertTrue(response.contains("classFieldToHideForAll"));
        assertFalse(response.contains("classFieldToShowForAll"));
        assertTrue(response.contains("methodToSkip"));
        assertFalse(response.contains("methodToHideForAny"));
        assertFalse(response.contains("methodToShowForAny"));
        assertTrue(response.contains("methodToHideForAll"));
        assertFalse(response.contains("methodToShowForAll"));
        assertTrue(response.contains("class2FieldToSkip"));
        assertFalse(response.contains("class2FieldHideForAny"));
        assertTrue(response.contains("classHideForAllField"));
        assertFalse(response.contains("classHideForAnyField"));
        assertTrue(response.contains("classShowForAnyField"));
        assertFalse(response.contains("classShowForAllField"));
        assertTrue(response.contains("brokenField"));
        assertTrue(response.contains("justField"));

        authInjector.initCurrentAuthentication(Arrays.asList(AuthorityName.BOUTIQUE_SALES,
                AuthorityName.ADMIN));

        response = restTemplate.exchange(
                testApiConfiguration.getServerUrl() + "/testRolesDto/dto",
                HttpMethod.GET, new HttpEntity<String>(new HttpHeaders()), String.class).getBody();

        System.out.println(response);

        assertTrue(response.contains("fieldSkip"));
        assertFalse(response.contains("hideForAny"));
        assertTrue(response.contains("showForAny"));
        assertTrue(response.contains("hideForAll"));
        assertTrue(response.contains("showForAll"));
        assertTrue(response.contains("objectFieldToSkip"));
        assertFalse(response.contains("classFieldToHideForAny"));
        assertTrue(response.contains("classFieldToShowForAny"));
        assertTrue(response.contains("classFieldToHideForAll"));
        assertTrue(response.contains("classFieldToShowForAll"));
        assertTrue(response.contains("methodToSkip"));
        assertFalse(response.contains("methodToHideForAny"));
        assertTrue(response.contains("methodToShowForAny"));
        assertFalse(response.contains("methodToHideForAll"));
        assertFalse(response.contains("methodToShowForAll"));
        assertTrue(response.contains("class2FieldToSkip"));
        assertFalse(response.contains("class2FieldHideForAny"));
        assertFalse(response.contains("classHideForAllField"));
        assertFalse(response.contains("classHideForAnyField"));
        assertTrue(response.contains("classShowForAnyField"));
        assertTrue(response.contains("classShowForAllField"));

        authInjector.initCurrentAuthentication(Arrays.asList(AuthorityName.MASTER_USER));

        response = restTemplate.exchange(
                testApiConfiguration.getServerUrl() + "/testRolesDto/dto",
                HttpMethod.GET, new HttpEntity<String>(new HttpHeaders()), String.class).getBody();

        System.out.println(response);

        assertTrue(response.contains("fieldSkip"));
        assertTrue(response.contains("hideForAny"));
        assertFalse(response.contains("showForAny"));
        assertTrue(response.contains("hideForAll"));
        assertFalse(response.contains("showForAll"));
        assertTrue(response.contains("objectFieldToSkip"));
        assertTrue(response.contains("classFieldToHideForAny"));
        assertFalse(response.contains("classFieldToShowForAny"));
        assertTrue(response.contains("classFieldToHideForAll"));
        assertFalse(response.contains("classFieldToShowForAll"));
        assertTrue(response.contains("methodToSkip"));
        assertTrue(response.contains("methodToHideForAny"));
        assertFalse(response.contains("methodToShowForAny"));
        assertTrue(response.contains("methodToHideForAll"));
        assertFalse(response.contains("methodToShowForAll"));
        assertTrue(response.contains("class2FieldToSkip"));
        assertTrue(response.contains("class2FieldHideForAny"));
        assertTrue(response.contains("classHideForAllField"));
        assertTrue(response.contains("classHideForAnyField"));
        assertFalse(response.contains("classShowForAnyField"));
        assertFalse(response.contains("classShowForAllField"));

        authInjector.initCurrentAuthentication(new ArrayList<>());

        response = restTemplate.exchange(
                testApiConfiguration.getServerUrl() + "/testRolesDto/dto",
                HttpMethod.GET, new HttpEntity<String>(new HttpHeaders()), String.class).getBody();

        System.out.println(response);

        assertTrue(response.contains("fieldSkip"));
        assertTrue(response.contains("hideForAny"));
        assertFalse(response.contains("showForAny"));
        assertTrue(response.contains("hideForAll"));
        assertFalse(response.contains("showForAll"));
        assertTrue(response.contains("objectFieldToSkip"));
        assertTrue(response.contains("classFieldToHideForAny"));
        assertFalse(response.contains("classFieldToShowForAny"));
        assertTrue(response.contains("classFieldToHideForAll"));
        assertFalse(response.contains("classFieldToShowForAll"));
        assertTrue(response.contains("methodToSkip"));
        assertTrue(response.contains("methodToHideForAny"));
        assertFalse(response.contains("methodToShowForAny"));
        assertTrue(response.contains("methodToHideForAll"));
        assertFalse(response.contains("methodToShowForAll"));
        assertTrue(response.contains("class2FieldToSkip"));
        assertTrue(response.contains("class2FieldHideForAny"));
        assertTrue(response.contains("classHideForAllField"));
        assertTrue(response.contains("classHideForAnyField"));
        assertFalse(response.contains("classShowForAnyField"));
        assertFalse(response.contains("classShowForAllField"));
    }

}
