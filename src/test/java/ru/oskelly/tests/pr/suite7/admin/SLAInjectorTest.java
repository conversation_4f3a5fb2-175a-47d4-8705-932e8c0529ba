package ru.oskelly.tests.pr.suite7.admin;

import org.junit.jupiter.api.Test;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.service.adminpanel.product.domain.ProductDTOV3;
import su.reddot.domain.service.adminpanel.product.domain.injector.ProductInfoInjectorContext;
import su.reddot.domain.service.adminpanel.product.domain.injector.SLAInjector;

import java.time.OffsetDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class SLAInjectorTest {
    @Test
    void testInjector() {
        OffsetDateTime now = OffsetDateTime.now();

        //Продут с непросроченным статусом
        ProductDTOV3 productDTOV3_1 = new ProductDTOV3();
        productDTOV3_1.setProductState(ProductState.PUBLISHED);
        productDTOV3_1.setProductStateTime(now.minusMinutes(10));

        //Продут с просроченным статусом
        ProductDTOV3 productDTOV3_2 = new ProductDTOV3();
        productDTOV3_2.setProductState(ProductState.SOLD);
        productDTOV3_2.setProductStateTime(now.minusMinutes(300));

        //Продут с просроченным статусом, которого нет в настройках
        ProductDTOV3 productDTOV3_3 = new ProductDTOV3();
        productDTOV3_3.setProductState(ProductState.NEED_MODERATION);
        productDTOV3_3.setProductStateTime(now.minusMinutes(300));

        Map<ProductState, Integer> slaMap = new HashMap<>();

        slaMap.put(ProductState.PUBLISHED, 30);
        slaMap.put(ProductState.SOLD, 100);

        ProductInfoInjectorContext injectorContext = new ProductInfoInjectorContext(OffsetDateTime.now());

        SLAInjector slaInjector = new SLAInjector(slaMap);
        slaInjector.injectInfo(productDTOV3_1, injectorContext);
        slaInjector.injectInfo(productDTOV3_2, injectorContext);
        slaInjector.injectInfo(productDTOV3_3, injectorContext);

        assertFalse(productDTOV3_1.getSlaFlag());
        assertTrue(productDTOV3_2.getSlaFlag());
        assertFalse(productDTOV3_3.getSlaFlag());
    }
}
