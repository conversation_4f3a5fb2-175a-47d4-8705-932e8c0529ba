package ru.oskelly.tests.pr.suite7.admin.rolemodel;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import java.io.IOException;

@Component
@Order(100)
public class AuthFilter implements Filter {
    @Autowired
    private AuthInjectorIF authInjector;

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        SecurityContext context = SecurityContextHolder.getContextHolderStrategy().getContext();

        context.setAuthentication(authInjector.getCurrentAuthentication());

        filterChain.doFilter(servletRequest, servletResponse);
    }
}
