package ru.oskelly.tests.pr.suite7.admin.rolemodel;

import com.fasterxml.jackson.annotation.JsonFilter;
import lombok.Data;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.infrastructure.configuration.jackson.RenderAction;
import su.reddot.infrastructure.configuration.jackson.RolesInclusion;
import su.reddot.infrastructure.configuration.jackson.Visibility;

import static su.reddot.infrastructure.configuration.JacksonConfiguration.ROLES_FILTER;

@Data
@JsonFilter(ROLES_FILTER)
public class TestClass {
    private String fieldSkip = "fieldSkip";
    @Visibility(
            roles = {AuthorityName.COMMENT_MODERATION, AuthorityName.BOUTIQUE_SALES},
            action = RenderAction.HIDE)
    private String hideForAny = "hideForAny";

    @Visibility(
            roles = {AuthorityName.ADMIN, AuthorityName.BOUTIQUE_SALES},
            action = RenderAction.SHOW)
    private String showForAny = "showForAny";

    @Visibility(
            roles = {AuthorityName.COMMENT_MODERATION, AuthorityName.BOUTIQUE_SALES},
            action = RenderAction.HIDE,
            inclusion = RolesInclusion.ALL
    )
    private String hideForAll = "hideForAll";

    @Visibility(
            roles = {AuthorityName.ADMIN, AuthorityName.BOUTIQUE_SALES},
            action = RenderAction.SHOW,
            inclusion = RolesInclusion.ALL
    )
    private String showForAll = "showForAll";

    private TestClass2 objectFieldToSkip = new TestClass2();

    @Visibility(
            roles = {AuthorityName.ADMIN, AuthorityName.BOUTIQUE_SALES},
            action = RenderAction.HIDE
    )
    private TestClass2 classFieldToHideForAny = new TestClass2();

    @Visibility(
            roles = {AuthorityName.ADMIN, AuthorityName.BOUTIQUE_SALES},
            action = RenderAction.SHOW
    )
    private TestClass2 classFieldToShowForAny = new TestClass2();

    @Visibility(
            roles = {AuthorityName.COMMENT_MODERATION, AuthorityName.BOUTIQUE_SALES},
            action = RenderAction.HIDE,
            inclusion = RolesInclusion.ALL
    )
    private TestClass2 classFieldToHideForAll = new TestClass2();

    @Visibility(
            roles = {AuthorityName.ADMIN, AuthorityName.BOUTIQUE_SALES},
            action = RenderAction.SHOW,
            inclusion = RolesInclusion.ALL
    )
    private TestClass2 classFieldToShowForAll = new TestClass2();

    public String getMethodToSkip() {
        return "methodToSkip";
    }

    @Visibility(
            roles = {AuthorityName.ADMIN, AuthorityName.BOUTIQUE_SALES},
            action = RenderAction.HIDE
    )
    public String getMethodToHideForAny() {
        return "methodToHideForAny";
    }

    @Visibility(
            roles = {AuthorityName.ADMIN},
            action = RenderAction.SHOW
    )
    public String getMethodToShowForAny() {
        return "methodToShowForAny";
    }

    @Visibility(
            roles = {AuthorityName.ADMIN, AuthorityName.BOUTIQUE_SALES},
            action = RenderAction.HIDE,
            inclusion = RolesInclusion.ALL
    )
    public String getMethodToHideForAll() {
        return "methodToHideForAll";
    }

    @Visibility(
            roles = {AuthorityName.COMMENT_MODERATION, AuthorityName.ADMIN},
            action = RenderAction.SHOW,
            inclusion = RolesInclusion.ALL

    )
    public String getMethodToShowForAll() {
        return "methodToShowForAll";
    }

    private TestClassHideForAll testClassHideForAll = new TestClassHideForAll();
    private TestClassHideForAny testClassHideForAny = new TestClassHideForAny();

    private TestClassShowForAll testClassShowForAll = new TestClassShowForAll();
    private TestClassShowForAny testClassShowForAny = new TestClassShowForAny();

    @Visibility(
            roles = {},
            action = RenderAction.SHOW,
            inclusion = RolesInclusion.ALL

    )
    public String getBrokenField() {
        return "brokenField";
    }

    public TestClass3 testClass3 = new TestClass3();

    @Visibility(
            roles = {AuthorityName.COMMENT_MODERATION, AuthorityName.BOUTIQUE_SALES},
            action = RenderAction.HIDE,
            skipFor = @Visibility.Skip(
                roles = {AuthorityName.ADMIN, AuthorityName.PRODUCT_MODERATION},
                inclusion = RolesInclusion.ANY))
    private String hideWithSkip = "hideWithSkip";
}
