package ru.oskelly.tests.pr.suite7.admin;

import com.querydsl.core.BooleanBuilder;
import org.junit.jupiter.api.Test;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.product.QProduct;
import su.reddot.domain.model.product.SalesChannel;
import su.reddot.domain.model.size.SizeType;
import su.reddot.domain.model.user.QUser;
import su.reddot.domain.service.adminpanel.domain.PredicateType;
import su.reddot.domain.service.adminpanel.product.domain.ProductsRequestV3;
import su.reddot.domain.service.adminpanel.product.domain.filter.CustomFilterIF;
import su.reddot.domain.service.adminpanel.product.domain.filter.InBoutiqueCustomFilter;
import su.reddot.domain.service.adminpanel.product.domain.filter.SLACustomFilter;
import su.reddot.domain.service.adminpanel.product.domain.filter.UserAuthorityBooleanBuilder;
import su.reddot.domain.service.adminpanel.user.domain.AdminV2UsersRequest;
import su.reddot.domain.service.adminpanel.user.domain.UsersRequest;
import su.reddot.domain.service.product.DefaultProductTagService;
import su.reddot.domain.service.util.QuerydslUtil;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class QdslUtilTest {
    @Test
    public void simplePredicateTest() throws IllegalAccessException {
        ZonedDateTime changeTimeSince = ZonedDateTime.of(2000, 1, 1,
                0, 0, 0, 0, ZoneId.of("UTC+1"));
        ZonedDateTime stateTimeTill = ZonedDateTime.of(2001, 1, 1,
                0, 0, 0, 0, ZoneId.of("UTC+1"));

        ProductsRequestV3 productsRequestV3 = new ProductsRequestV3();
        productsRequestV3.setId(10L);
        productsRequestV3.setChangeTimeSince(changeTimeSince);
        productsRequestV3.setProductStateTimeTill(stateTimeTill);
        productsRequestV3.getProductStates().add(ProductState.RETOUCH_DONE);
        productsRequestV3.getProductStates().add(ProductState.DRAFT);
        productsRequestV3.setName("someName");
        productsRequestV3.setPurchasePriceFrom(new BigDecimal("1.0"));
        productsRequestV3.setPurchasePriceTo(new BigDecimal("2.0"));
        productsRequestV3.setIsAtOffice(true);
        productsRequestV3.setCustomFilter("slaFilter");

        productsRequestV3.setBrandIds(Arrays.asList(11L, 12L, 13L));
        productsRequestV3.setBrandIdsExclude(Arrays.asList(14L, 15L, 16L));

        productsRequestV3.setProductModelIds(Arrays.asList(21L, 22L, 23L));
        productsRequestV3.setProductModelIdsExclude(Arrays.asList(24L, 25L, 26L));

        productsRequestV3.setCategoryIds(Arrays.asList(31L, 32L, 33L));
        productsRequestV3.setCategoryIdsExclude(Arrays.asList(34L, 35L, 36L));

        productsRequestV3.setSellerIds(Arrays.asList(41L, 42L, 43L));
        productsRequestV3.setSellerIdsExclude(Arrays.asList(44L, 45L, 46L));

        productsRequestV3.setRetoucherIds(Arrays.asList(51L, 52L, 53L));
        productsRequestV3.setRetoucherIdsExclude(Arrays.asList(54L, 55L, 56L));

        productsRequestV3.setChangeUserIds(Arrays.asList(61L, 62L, 63L));
        productsRequestV3.setChangeUserIdsExclude(Arrays.asList(64L, 65L, 66L));

        productsRequestV3.setProductConditionIds(Arrays.asList(71L, 72L, 73L));
        productsRequestV3.setProductConditionIdsExclude(Arrays.asList(74L, 75L, 76L));

        productsRequestV3.setProductStatesExclude(Arrays.asList(ProductState.BANED, ProductState.HIDDEN));

        productsRequestV3.setSizeTypes(Arrays.asList(SizeType.AU, SizeType.AGE));
        productsRequestV3.setSizeTypesExclude(Arrays.asList(SizeType.BUST, SizeType.CENTIMETERS));

        productsRequestV3.setSalesChannels(Arrays.asList(SalesChannel.WEBSITE));
        productsRequestV3.setSalesChannelsExclude(Arrays.asList(SalesChannel.BOUTIQUE_AND_WEBSITE));
        productsRequestV3.setSellerProStatusTimeIsNotNull(true);
        productsRequestV3.setPublishTimeIsNotNull(true);
        productsRequestV3.setRrpPriceIsNotNull(false);

        //кастомный фильтр SLA

        Map<ProductState, Integer> slaMap = new HashMap<>();
        slaMap.put(ProductState.PUBLISHED, 100);
        slaMap.put(ProductState.DRAFT, 200);

        SLACustomFilter slaCustomFilter = new SLACustomFilter(slaMap);

        Map<String, CustomFilterIF> filtersMap = new HashMap<>();
        filtersMap.put("slaFilter", slaCustomFilter);

        InBoutiqueCustomFilter inBoutiqueCustomFilter = new InBoutiqueCustomFilter(new DefaultProductTagService(null, null));
        filtersMap.put("inBoutiqueFilter", inBoutiqueCustomFilter);

        BooleanBuilder booleanBuilder = QuerydslUtil.combinePredicate(QProduct.product, productsRequestV3,
                filtersMap);

        String queryString = booleanBuilder.toString();

        System.out.println(queryString);

        //NumericAppender number eq
        assertTrue(queryString.contains("product.id = 10"));
        //StringAppender string contains
        assertTrue(queryString.contains("containsIc(product.name,someName)"));
        //EnumAppender enum in
        assertTrue(queryString.contains("product.productState in [RETOUCH_DONE, DRAFT]"));
        //DateAppender date >=
        assertTrue(queryString.contains("product.changeTime >= 2000-01-01T00:00+01:00[UTC+01:00]"));
        //DateAppender date <=
        assertTrue(queryString.contains("product.productStateTime <= 2001-01-01T00:00+01:00[UTC+01:00]"));
        //NumericAppender number >=
        assertTrue(queryString.contains("product.purchasePrice >= 1.0"));
        //NumericAppender number <=
        assertTrue(queryString.contains("product.purchasePrice <= 2.0"));
        //BooleanAppender boolean eq
        assertTrue(queryString.contains("product.isAtOffice = true"));

        //brandId in
        assertTrue(queryString.contains("product.brand.id in [11, 12, 13]"));
        //brandId not in
        assertTrue(queryString.contains("product.brand.id not in [14, 15, 16]"));

        //productModelId in
        assertTrue(queryString.contains("product.productModel.id in [21, 22, 23]"));
        //productModelId not in
        assertTrue(queryString.contains("product.productModel.id not in [24, 25, 26]"));

        //categoryId in
        assertTrue(queryString.contains("product.categoryId in [31, 32, 33]"));
        //categoryId not in
        assertTrue(queryString.contains("product.categoryId not in [34, 35, 36]"));

        //sellerId in
        assertTrue(queryString.contains("product.seller.id in [41, 42, 43]"));
        //sellerId not in
        assertTrue(queryString.contains("product.seller.id not in [44, 45, 46]"));

        //retoucherId
        assertTrue(queryString.contains("product.retoucher.id in [51, 52, 53]"));
        //retoucherId not in
        assertTrue(queryString.contains("product.retoucher.id not in [54, 55, 56]"));

        //changeUserId in
        assertTrue(queryString.contains("product.changeUser.id in [61, 62, 63]"));
        //changeUserId not in
        assertTrue(queryString.contains("product.changeUser.id not in [64, 65, 66]"));

        //productConditionId in
        assertTrue(queryString.contains("product.productConditionId in [71, 72, 73]"));
        //productConditionId not in
        assertTrue(queryString.contains("product.productConditionId not in [74, 75, 76]"));

        //states exclude
        assertTrue(queryString.contains("product.productState not in [BANED, HIDDEN]"));

        //sizeType in
        assertTrue(queryString.contains("product.sizeType in [AU, AGE]"));
        //sizeType not in
        assertTrue(queryString.contains("product.sizeType not in [BUST, CENTIMETERS]"));

        //sla
        assertTrue(queryString.contains("product.productState = DRAFT && product.productStateTime <"));
        assertTrue(queryString.contains("product.productState = PUBLISHED && product.productStateTime <"));

        //pro status time
        assertTrue(queryString.contains("product.seller.proStatusTime is not null"));

        //publishTime
        assertTrue(queryString.contains("product.publishTime is not null"));

        //rrp
        assertTrue(queryString.contains("product.rrpPrice is null"));

        //inBoutiqueFilter
        /*
        productsRequestV3.setCustomFilter("inBoutiqueFilter");
        booleanBuilder = QuerydslUtil.booleanBuilder(QProduct.product, productsRequestV3,
                filtersMap);
        queryString = booleanBuilder.toString();
        System.out.println(queryString);
         */

        UsersRequest usersRequest = new UsersRequest();
        usersRequest.setCountryId(1l);
        usersRequest.getAuthorities().add(AuthorityName.EXPERTISE);
        usersRequest.getAuthorities().add(AuthorityName.ADMIN);

        BooleanBuilder booleanBuilderUsers = QuerydslUtil.combinePredicate(QUser.user, usersRequest);

        booleanBuilderUsers.and(UserAuthorityBooleanBuilder.build(usersRequest.getAuthorities()));

        String queryStringUsers = booleanBuilderUsers.toString();

        System.out.println(queryStringUsers);

        AdminV2UsersRequest adminV2UsersRequest = new AdminV2UsersRequest();

        AdminV2UsersRequest subRequest = new AdminV2UsersRequest();
        adminV2UsersRequest.setIdsInclude(Arrays.asList(3L));
        adminV2UsersRequest.getSubPredicates().add(subRequest);
        subRequest.setIdsInclude(Arrays.asList(1L,2L));
        subRequest.setNotNullSellerTypeOR(false);
        subRequest.setConciergeBuyer(true);

        booleanBuilderUsers = QuerydslUtil.combinePredicate(QUser.user, adminV2UsersRequest);

        assertEquals("user.id = 3 && (user.id in [1, 2] && user.conciergeBuyerTime is not null || user.sellerType is null)",
                booleanBuilderUsers.toString());
    }

    @Test
    public void complexPredicateTest() throws IllegalAccessException {

        //запрос, отбирающий "все" записи
        ProductsRequestV3 rootPredicate = new ProductsRequestV3();
        rootPredicate.getSalesChannels().add(SalesChannel.WEBSITE);
        rootPredicate.getSalesChannels().add(SalesChannel.BOUTIQUE_AND_WEBSITE);

        //таб "на модерации"
        ProductsRequestV3 subPredicateOnModeration = new ProductsRequestV3();
        subPredicateOnModeration.setProductStates(Arrays.asList(ProductState.NEED_MODERATION,
                ProductState.RETOUCH_DONE, ProductState.NEED_RETOUCH));
        rootPredicate.getSubPredicates().add(subPredicateOnModeration);

        //галочки "модерация админом", "требуется ретушь", "ретушь выполнена"
        ProductsRequestV3 needModerationCheckBox = new ProductsRequestV3();
        //пусть это будет "модерация админом", другого подходящего статуса я не нашел
        needModerationCheckBox.setProductStates(Arrays.asList(ProductState.NEED_MODERATION));
        needModerationCheckBox.setSelfPredicateType(PredicateType.OR);
        subPredicateOnModeration.getSubPredicates().add(needModerationCheckBox);

        ProductsRequestV3 retouchDoneCheckBox = new ProductsRequestV3();
        retouchDoneCheckBox.setProductStates(Arrays.asList(ProductState.RETOUCH_DONE));
        retouchDoneCheckBox.setSelfPredicateType(PredicateType.OR);
        //subPredicateOnModeration.getSubPredicates().add(retouchDoneCheckBox);

        ProductsRequestV3 needRetouchCheckBox = new ProductsRequestV3();
        needRetouchCheckBox.setProductStates(Arrays.asList(ProductState.NEED_RETOUCH));
        needRetouchCheckBox.setSelfPredicateType(PredicateType.OR);
        //subPredicateOnModeration.getSubPredicates().add(needRetouchCheckBox);

        //выбрана 1 галочка - "модерация админом"
        BooleanBuilder combinedPredicate = QuerydslUtil.combinePredicate(QProduct.product, rootPredicate);

        String queryString = combinedPredicate.toString();

        assertEquals("product.salesChannel in [WEBSITE, BOUTIQUE_AND_WEBSITE] && " +
                        "product.productState in [NEED_MODERATION, RETOUCH_DONE, NEED_RETOUCH] && " +
                        "product.productState = NEED_MODERATION",
                queryString);

        //выбрано 2 галочки
        subPredicateOnModeration.getSubPredicates().add(retouchDoneCheckBox);

        combinedPredicate = QuerydslUtil.combinePredicate(QProduct.product, rootPredicate);

        queryString = combinedPredicate.toString();

        assertEquals("product.salesChannel in [WEBSITE, BOUTIQUE_AND_WEBSITE] && " +
                        "product.productState in [NEED_MODERATION, RETOUCH_DONE, NEED_RETOUCH] && " +
                        "(product.productState = NEED_MODERATION || product.productState = RETOUCH_DONE)",
                queryString);
    }
}
