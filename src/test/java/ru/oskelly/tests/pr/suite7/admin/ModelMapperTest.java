package ru.oskelly.tests.pr.suite7.admin;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.modelmapper.ModelMapper;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.service.adminpanel.product.domain.ProductEditRequestV3;
import su.reddot.domain.service.config.modelmapper.ModelMapperFactoryUtil;
import su.reddot.infrastructure.security.SocialAuthProvider;
import su.reddot.infrastructure.security.jwt.dto.SocialUserCredentialsDTO;
import su.reddot.infrastructure.util.ObjectMapperUtils;

import java.time.ZonedDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class ModelMapperTest {
    @Test
    void testModelMapper() {
        ModelMapper modelMapper = ModelMapperFactoryUtil.modelMapper();

        Product p = new Product();
        p.setId(1L);
        p.setProductState(ProductState.PUBLISHED);

        ZonedDateTime stateTimeBefore = p.getProductStateTime();

        p.setCategoryId(10L);

        ProductEditRequestV3 productEditDTOV3 = new ProductEditRequestV3();
        productEditDTOV3.setProductState(ProductState.SOLD);
        productEditDTOV3.setAtOffice(true);

        modelMapper.map(productEditDTOV3, p);

        assertEquals(1L, p.getId());
        assertEquals(10L, p.getCategoryId());
        assertTrue(p.isAtOffice());
    }

    @Test
    public void testCopy() {
        SocialUserCredentialsDTO credentialsDTO = new SocialUserCredentialsDTO();
        credentialsDTO.setUserId(1L);
        credentialsDTO.setUid(UUID.randomUUID());
        credentialsDTO.setEmail("mail");
        credentialsDTO.setSocialType(SocialAuthProvider.APPLE);
        credentialsDTO.setGuestToken("token");
        credentialsDTO.setSocialAccountId("accountId");

        SocialUserCredentialsDTO copy = ObjectMapperUtils.clone(credentialsDTO, SocialUserCredentialsDTO.class,
                new ObjectMapper());

        assertEquals(credentialsDTO, copy);
    }
}
