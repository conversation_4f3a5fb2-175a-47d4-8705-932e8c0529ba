package ru.oskelly.tests.pr.suite7.admin;

import org.junit.jupiter.api.Test;
import su.reddot.domain.service.util.Transliterator;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class TransliteratorTest {
    @Test
    public void testTransliterator() {
        assertEquals("PRIVET", Transliterator.transliterateToUpperCase("Привет"));
        assertEquals("PRIVET_1_POKA", Transliterator.transliterateToUpperCase("*Привет #  ,`1 Пока   "));
        assertEquals("7_PRIVET_DA_NET_3", Transliterator.transliterateToUpperCase(" % 7 Привет ; da net 3' "));
    }
}
