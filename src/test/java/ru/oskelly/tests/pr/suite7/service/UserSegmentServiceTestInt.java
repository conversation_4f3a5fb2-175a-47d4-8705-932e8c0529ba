package ru.oskelly.tests.pr.suite7.service;

import org.assertj.core.api.Assertions;
import org.assertj.core.data.Index;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.model.segment.UserListMode;
import su.reddot.domain.service.dto.segment.SegmentDTO;

import java.util.List;
import java.util.UUID;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_07)
@TestMethodOrder(MethodOrderer.MethodName.class)
public class UserSegmentServiceTestInt extends UserSegmentServiceTest {

    @BeforeEach
    public void init() {
        jdbcTemplate.execute("UPDATE user_segment SET delete_time = null WHERE country_id IS NOT NULL");
    }

    @AfterEach
    public void done() {
        jdbcTemplate.execute("UPDATE user_segment SET delete_time = now() WHERE country_id IS NOT NULL");
    }

    @Test
    public void segmentsDictTest() {
        super.segmentsDictTestCommon();
    }

    @Test
    public void countrySegmentFindAllTestAny() {
        super.countrySegmentFindAllTestAnyCommon();
    }

    @Test
    public void countrySegmentFindAllTestSAU() {
        List<SegmentDTO> nullOrdersIsGuest = userSegmentService.findAllByUserIdOrGuestToken(null, UUID.randomUUID().toString(), SAU_COUNTRY_ID);
        Assertions.assertThat(nullOrdersIsGuest).hasSize(4)
                .satisfies(it -> {
                    Assertions.assertThat(it.getUserListMode()).isEqualTo(UserListMode.ALL_USERS);
                    Assertions.assertThat(it.getCountry()).isNull();
                }, Index.atIndex(0))
                .satisfies(it -> {
                    Assertions.assertThat(it.getUserListMode()).isEqualTo(UserListMode.NOOB_USERS);
                    Assertions.assertThat(it.getCountry()).isNull();
                }, Index.atIndex(1))
                .satisfies(it -> {
                    Assertions.assertThat(it.getUserListMode()).isEqualTo(UserListMode.ALL_USERS);
                    Assertions.assertThat(it.getCountry()).isNotNull().satisfies(countryDto ->
                            Assertions.assertThat(countryDto.getId()).isEqualTo(SAU_COUNTRY_ID)
                    );
                }, Index.atIndex(2))
                .satisfies(it -> {
                    Assertions.assertThat(it.getUserListMode()).isEqualTo(UserListMode.NOOB_USERS);
                    Assertions.assertThat(it.getCountry()).isNotNull().satisfies(countryDto ->
                            Assertions.assertThat(countryDto.getId()).isEqualTo(SAU_COUNTRY_ID)
                    );
                }, Index.atIndex(3));
        //
        List<SegmentDTO> zeroOrdersIsGuest = userSegmentService.findAllByUserIdOrGuestToken(USER_WITH_ZERO_ORDERS, UUID.randomUUID().toString(), SAU_COUNTRY_ID);
        Assertions.assertThat(zeroOrdersIsGuest).hasSize(4)
                .satisfies(it -> {
                    Assertions.assertThat(it.getUserListMode()).isEqualTo(UserListMode.ALL_USERS);
                    Assertions.assertThat(it.getCountry()).isNull();
                }, Index.atIndex(0))
                .satisfies(it -> {
                    Assertions.assertThat(it.getUserListMode()).isEqualTo(UserListMode.NOOB_USERS);
                    Assertions.assertThat(it.getCountry()).isNull();
                }, Index.atIndex(1))
                .satisfies(it -> {
                    Assertions.assertThat(it.getUserListMode()).isEqualTo(UserListMode.ALL_USERS);
                    Assertions.assertThat(it.getCountry()).isNotNull().satisfies(countryDto ->
                            Assertions.assertThat(countryDto.getId()).isEqualTo(SAU_COUNTRY_ID)
                    );
                }, Index.atIndex(2))
                .satisfies(it -> {
                    Assertions.assertThat(it.getUserListMode()).isEqualTo(UserListMode.NOOB_USERS);
                    Assertions.assertThat(it.getCountry()).isNotNull().satisfies(countryDto ->
                            Assertions.assertThat(countryDto.getId()).isEqualTo(SAU_COUNTRY_ID)
                    );
                }, Index.atIndex(3));
        //
        List<SegmentDTO> someOrdersNoGuest = userSegmentService.findAllByUserIdOrGuestToken(USER_WITH_SOME_ORDERS, null, SAU_COUNTRY_ID);
        Assertions.assertThat(someOrdersNoGuest).hasSize(4)
                .satisfies(it -> {
                    Assertions.assertThat(it.getUserListMode()).isEqualTo(UserListMode.ALL_USERS);
                    Assertions.assertThat(it.getCountry()).isNull();
                }, Index.atIndex(0))
                .satisfies(it -> {
                    Assertions.assertThat(it.getUserListMode()).isEqualTo(UserListMode.USERS_WITH_ORDERS);
                    Assertions.assertThat(it.getCountry()).isNull();
                }, Index.atIndex(1))
                .satisfies(it -> {
                    Assertions.assertThat(it.getUserListMode()).isEqualTo(UserListMode.ALL_USERS);
                    Assertions.assertThat(it.getCountry()).isNotNull().satisfies(countryDto ->
                            Assertions.assertThat(countryDto.getId()).isEqualTo(SAU_COUNTRY_ID)
                    );
                }, Index.atIndex(2))
                .satisfies(it -> {
                    Assertions.assertThat(it.getUserListMode()).isEqualTo(UserListMode.USERS_WITH_ORDERS);
                    Assertions.assertThat(it.getCountry()).isNotNull().satisfies(countryDto ->
                            Assertions.assertThat(countryDto.getId()).isEqualTo(SAU_COUNTRY_ID)
                    );
                }, Index.atIndex(3));
        //
        List<SegmentDTO> someOrdersIsGuest = userSegmentService.findAllByUserIdOrGuestToken(USER_WITH_SOME_ORDERS, UUID.randomUUID().toString(), SAU_COUNTRY_ID);
        Assertions.assertThat(someOrdersIsGuest).hasSize(4)
                .satisfies(it -> {
                    Assertions.assertThat(it.getUserListMode()).isEqualTo(UserListMode.ALL_USERS);
                    Assertions.assertThat(it.getCountry()).isNull();
                }, Index.atIndex(0))
                .satisfies(it -> {
                    Assertions.assertThat(it.getUserListMode()).isEqualTo(UserListMode.USERS_WITH_ORDERS);
                    Assertions.assertThat(it.getCountry()).isNull();
                }, Index.atIndex(1))
                .satisfies(it -> {
                    Assertions.assertThat(it.getUserListMode()).isEqualTo(UserListMode.ALL_USERS);
                    Assertions.assertThat(it.getCountry()).isNotNull().satisfies(countryDto ->
                            Assertions.assertThat(countryDto.getId()).isEqualTo(SAU_COUNTRY_ID)
                    );
                }, Index.atIndex(2))
                .satisfies(it -> {
                    Assertions.assertThat(it.getUserListMode()).isEqualTo(UserListMode.USERS_WITH_ORDERS);
                    Assertions.assertThat(it.getCountry()).isNotNull().satisfies(countryDto ->
                            Assertions.assertThat(countryDto.getId()).isEqualTo(SAU_COUNTRY_ID)
                    );
                }, Index.atIndex(3));
    }

}
