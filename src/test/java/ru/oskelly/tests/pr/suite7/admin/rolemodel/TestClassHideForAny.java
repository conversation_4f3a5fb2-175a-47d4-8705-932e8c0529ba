package ru.oskelly.tests.pr.suite7.admin.rolemodel;

import com.fasterxml.jackson.annotation.JsonFilter;
import lombok.Data;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.infrastructure.configuration.jackson.RenderAction;
import su.reddot.infrastructure.configuration.jackson.Visibility;

import static su.reddot.infrastructure.configuration.JacksonConfiguration.ROLES_FILTER;

@Data
@JsonFilter(ROLES_FILTER)
@Visibility(
        roles = {AuthorityName.BOUTIQUE_SALES, AuthorityName.ADMIN},
        action = RenderAction.HIDE
)
public class TestClassHideForAny {
    private String classHideForAnyField = "classHideForAnyField";
}
