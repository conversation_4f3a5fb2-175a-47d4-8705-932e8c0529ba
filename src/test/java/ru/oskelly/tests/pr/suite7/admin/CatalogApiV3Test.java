package ru.oskelly.tests.pr.suite7.admin;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.BrandRepository;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.attribute.AttributeValueRepository;
import su.reddot.domain.dao.category.CategoryRepository;
import su.reddot.domain.dao.product.ProductAttributeValueBindingRepository;
import su.reddot.domain.dao.product.ProductConditionRepository;
import su.reddot.domain.dao.product.ProductItemAdditionalSizeValueRepository;
import su.reddot.domain.dao.product.ProductModelRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.dao.user.SaleShipmentRouteRepository;
import su.reddot.domain.model.Brand;
import su.reddot.domain.model.attribute.AttributeValue;
import su.reddot.domain.model.category.Category;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductAttributeValueBinding;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.ProductItemAdditionalSizeValue;
import su.reddot.domain.model.product.ProductModel;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.product.SalesChannel;
import su.reddot.domain.model.user.SaleShipmentRoute;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.additionalsize.AdditionalSizeService;
import su.reddot.domain.service.adminpanel.brand.BrandServiceAPI;
import su.reddot.domain.service.adminpanel.brand.domain.BrandsRequest;
import su.reddot.domain.service.adminpanel.exception.AdministrationException;
import su.reddot.domain.service.adminpanel.exception.BrandNotFoundAdministrationException;
import su.reddot.domain.service.adminpanel.product.AdminProductService;
import su.reddot.domain.service.adminpanel.product.DictHelperService;
import su.reddot.domain.service.adminpanel.product.ProductServiceAPI;
import su.reddot.domain.service.adminpanel.product.domain.ProductDTOV3;
import su.reddot.domain.service.adminpanel.product.domain.ProductEditRequestV3;
import su.reddot.domain.service.adminpanel.product.domain.ProductPriceDTOV3;
import su.reddot.domain.service.adminpanel.product.domain.ProductsRequestV3;
import su.reddot.domain.service.adminpanel.productmodel.ProductModelServiceAPI;
import su.reddot.domain.service.adminpanel.productmodel.domain.ProductModelsRequest;
import su.reddot.domain.service.adminpanel.user.UserServiceAPI;
import su.reddot.domain.service.adminpanel.user.domain.UsersRequest;
import su.reddot.domain.service.currency.CurrencyService;
import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.dto.order.adminpanel.EnumLabelPairDTO;
import su.reddot.domain.service.dto.order.adminpanel.ProductConditionResponseDTO;
import su.reddot.domain.service.dto.order.adminpanel.ProductStateResponseDTO;
import su.reddot.domain.service.dto.order.adminpanel.SellerTypeDTO;
import su.reddot.domain.service.dto.order.adminpanel.SizeTypeResponseDTO;
import su.reddot.domain.service.productpublication.exception.ProductEditException;
import su.reddot.infrastructure.security.SecurityService;
import su.reddot.presentation.api.v3.domain.BrandDTOV3;
import su.reddot.presentation.api.v3.domain.ProductModelDTOV3;
import su.reddot.presentation.api.v3.domain.UserDTO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Slf4j
public class CatalogApiV3Test extends AbstractSpringTest {

    @Autowired
    private DictHelperService dictHelperService;
    @Autowired
    private ProductRepository productRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private CategoryRepository categoryRepository;
    @Autowired
    private BrandRepository brandRepository;
    @Autowired
    private ProductConditionRepository productConditionRepository;
    @Autowired
    private ProductModelServiceAPI productModelServiceAPI;
    @Autowired
    private BrandServiceAPI brandServiceAPI;
    @Autowired
    private UserServiceAPI userServiceAPI;
    @Autowired
    private ProductServiceAPI productServiceAPI;
    @Autowired
    private ProductModelRepository productModelRepository;
    @Autowired
    private AttributeValueRepository attributeValueRepository;
    @Autowired
    private ProductAttributeValueBindingRepository productAttributeValueBindingRepository;
    @Autowired
    private AdminProductService adminProductService;
    @Autowired
    private AdditionalSizeService additionalSizeService;
    @Autowired
    private ProductItemAdditionalSizeValueRepository productItemAdditionalSizeValueRepository;
    @Autowired
    private SecurityService securityService;
    @Autowired
    private CurrencyService currencyService;
    @Autowired
    private SaleShipmentRouteRepository saleShipmentRouteRepository;

    private User user;
    private Category category;
    private Brand brand;
    private Long tmpProductId;

    @Value("${test.api.user-email}")
    private String email;
    @Value("${test.api.user-password}")
    private String password;
    @Value("${test.api.user-id}")
    private Long userId;

    @Transactional
    @BeforeEach
    public void init() {
        securityService.authenticateByEmailAndRawPassword(email, password);
        category = category();
        brand = brand();
        user = userRepository.findById(userId).get();

        commitAndStartNewTransaction();
    }

    /**
     * Недоступность анонимного вывоза метода
     */
    //@Test
    @Transactional
    @Order(2)
    public void getProductsCount() {
        List<ProductStateResponseDTO> countBefore =
                dictHelperService.getProductsCountGroupedByState();

        createProducts(ProductState.SOLD, 10, user, category, brand);
        createProducts(ProductState.PUBLISHED, 15, user, category, brand);
        createProducts(ProductState.NEED_MODERATION, 20, user, category, brand);
        commitAndStartNewTransaction();

        List<ProductStateResponseDTO> countAfter =
                dictHelperService.getProductsCountGroupedByState();

        assertEquals(10, countByState(ProductState.SOLD, countAfter) -
                countByState(ProductState.SOLD, countBefore));
        assertEquals(15, countByState(ProductState.PUBLISHED, countAfter) -
                countByState(ProductState.PUBLISHED, countBefore));
        assertEquals(20, countByState(ProductState.NEED_MODERATION, countAfter) -
                countByState(ProductState.NEED_MODERATION, countBefore));
    }

    //@Test
    @Order(3)
    public void getSellerTypes() {
        List<EnumLabelPairDTO> sellerTypeResponseDTOS =
                dictHelperService.getAllSellerTypes();

        assertEquals(SellerTypeDTO.values().length, sellerTypeResponseDTOS.size());

        for (SellerTypeDTO sellerTypeDTO : SellerTypeDTO.values()) {
            assertTrue(isListContainsSellerTypeDTO(sellerTypeDTO, sellerTypeResponseDTOS));
        }
    }

    //@Test
    @Order(4)
    public void getBrands() {

        BrandsRequest brandsRequest = new BrandsRequest();
        brandsRequest.setName("alex");
        brandsRequest.setSortBy("name");
        brandsRequest.setDescending(false);
        brandsRequest.setPage(1);
        brandsRequest.setRowsPerPage(100);

        Page<BrandDTOV3> brands = brandServiceAPI.getBrandsCached(brandsRequest);

        //проверяем наличие нескольких брендов в ответе. Надеюсь, их не удалят из БД :)
        assertEquals(25, brands.getItemsCount());

        //проверка сортировки по name
        List<BrandDTOV3> brandsList = brands.getItems();
        List<BrandDTOV3> sortTestList = new ArrayList<>(brands.getItems());

        sortTestList.sort(Comparator.comparing(BrandDTOV3::getName));

        for (int i = 0; i < brandsList.size(); i++) {
            assertEquals(brandsList.get(i).getId(), sortTestList.get(i).getId());
        }

        brandsRequest = new BrandsRequest();
        brandsRequest.setIds(Arrays.asList(1L, 2L, 3L));

        brands = brandServiceAPI.getBrandsCached(brandsRequest);

        assertEquals(3, brands.getItemsCount());
    }

    //@Test
    @Order(5)
    public void getModelsByBrand() {
        //971 - "Испанская компания LOEWE"
        ProductModelsRequest productModelsRequest = new ProductModelsRequest();
        productModelsRequest.setBrandId(971L);
        productModelsRequest.setPage(1);
        productModelsRequest.setRowsPerPage(100);
        productModelsRequest.setDescending(false);
        productModelsRequest.setSortBy("name");
        Page<ProductModelDTOV3> models =
                productModelServiceAPI.getProductModelsCached(productModelsRequest);

        //для этой компании есть 15 каких-то моделей
        assertEquals(15, models.getItemsCount());

        //проверка сортировки по name
        List<ProductModelDTOV3> brandsList = models.getItems();
        List<ProductModelDTOV3> sortTestList = new ArrayList<>(models.getItems());

        sortTestList.sort(Comparator.comparing(ProductModelDTOV3::getName));

        for (int i = 0; i < brandsList.size(); i++) {
            assertEquals(brandsList.get(i).getId(), sortTestList.get(i).getId());
        }

        productModelsRequest = new ProductModelsRequest();
        productModelsRequest.setIds(Arrays.asList(1L, 2L, 3L));

        models = productModelServiceAPI.getProductModelsCached(productModelsRequest);

        assertEquals(3, models.getItemsCount());
    }

    //@Test
    @Order(6)
    public void getSalesChannels() {
        List<EnumLabelPairDTO> channels =
                dictHelperService.getSalesChannels();

        assertEquals(SalesChannel.values().length, channels.size());

        for (SalesChannel salesChannel : SalesChannel.values()) {
            assertTrue(isListContainsSellerTypeDTO(salesChannel, channels));
        }
    }

    //@Test
    @Order(7)
    public void getProductStates() {
        List<EnumLabelPairDTO> states =
                dictHelperService.getProductStates();

        assertEquals(ProductState.values().length, states.size());

        for (ProductState ps : ProductState.values()) {
            assertTrue(isListContainsSellerTypeDTO(ps, states));
        }
    }

    //@Test
    @Order(8)
    public void getProductConditions() {
        List<ProductConditionResponseDTO> conditions =
                dictHelperService.getProductConditions();

        assertEquals(productConditionRepository.findAll().size(), conditions.size());
    }

    //@Test
    @Order(9)
    @Transactional
    public void getUsers() {
        UsersRequest usersRequest = new UsersRequest();

        usersRequest.setNickname("admin");

        Page<UserDTO> users =
                userServiceAPI.getUsersCached(usersRequest);

        //5 пользователей с ником, содержащим "admin"
        assertEquals(5, users.getItemsCount());

        usersRequest.setNickname(null);
        usersRequest.setPhone("345");
        usersRequest.setDescending(false);
        usersRequest.setSortBy("birthDate");

        users = userServiceAPI.getUsersCached(usersRequest);
        assertEquals(24, users.getItemsCount());

        //проверка сортировки birthDate
        List<UserDTO> usersList = users.getItems();
        List<UserDTO> sortTestList = new ArrayList<>(users.getItems());

        sortTestList.sort(Comparator.comparing(UserDTO::getBirthDate,
                Comparator.nullsLast(Comparator.naturalOrder())));

        for (int i = 0; i < usersList.size(); i++) {
            assertEquals(usersList.get(i).getId(), sortTestList.get(i).getId());
        }

        //поиск по countryId
        usersRequest = new UsersRequest();
        usersRequest.setCountryId(1L);

        users = userServiceAPI.getUsersCached(usersRequest);
        assertEquals(2, users.getItemsCount());

        //поиск по pickupCountryId
        usersRequest.setCountryId(null);
        usersRequest.setPickupCountryId(2L);

        users = userServiceAPI.getUsersCached(usersRequest);
        assertEquals(2, users.getItemsCount());

        //даты
        usersRequest = new UsersRequest();

        usersRequest.setBirthDateSince(LocalDateTime.of(1989, Month.JANUARY, 1, 0, 0));
        usersRequest.setBirthDateTill(LocalDateTime.of(1989, Month.FEBRUARY, 1, 0, 0, 0));

        users = userServiceAPI.getUsersCached(usersRequest);
        assertEquals(32, users.getItemsCount());

        usersRequest = new UsersRequest();
        usersRequest.setRegistrationTimeSince(ZonedDateTime.of(2020, 1, 1,
                0, 0, 0, 0, ZoneId.of("+04")));
        usersRequest.setRegistrationTimeTill(ZonedDateTime.of(2020, 1, 2,
                0, 0, 0, 0, ZoneId.of("+04")));

        users = userServiceAPI.getUsersCached(usersRequest);
        assertEquals(13, users.getItemsCount());

        //ids
        usersRequest = new UsersRequest();
        usersRequest.setIds(Arrays.asList(1L, 3L, 9L));
        users = userServiceAPI.getUsersCached(usersRequest);
        assertEquals(3, users.getItemsCount());
    }

    //временно отключил
    //@Test
    @Transactional
    @Order(10)
    public void getProducts() {
        BigDecimal purchasePriceFrom = new BigDecimal("20000");
        List<ProductState> productStatesExclude = Arrays.asList(ProductState.REJECTED, ProductState.DRAFT);

        ProductsRequestV3 productsRequestV3 = new ProductsRequestV3();
        productsRequestV3.setPage(1);
        productsRequestV3.setDescending(false);
        productsRequestV3.setSortBy("purchasePrice");

        productsRequestV3.setCategoryIds(Arrays.asList(50L, 101L));
        productsRequestV3.setSellerIds(Arrays.asList(3234L, 9207L));
        productsRequestV3.setRowsPerPage(200);

        productsRequestV3.setPurchasePriceFrom(purchasePriceFrom);
        productsRequestV3.setProductStatesExclude(productStatesExclude);

        Page<ProductDTOV3> products =
                productServiceAPI.getProducts(productsRequestV3);

        //в БД таких записей 5
        assertEquals(5, products.getItemsCount());

        List<ProductDTOV3> productsOList = products.getItems();

        //проверка сортировки по purchasePrice
        List<ProductDTOV3> sortProductsOList = new ArrayList<>(products.getItems());

        sortProductsOList.sort(Comparator.comparing(ProductDTOV3::getPurchasePrice));

        for (int i = 0; i < productsOList.size(); i++) {
            ProductDTOV3 productDTOV3 = productsOList.get(i);
            //проверка отсутствия лишних записей в ответе
            assertTrue(productDTOV3.getCategoryId().longValue() == 50L ||
                    productDTOV3.getCategoryId().longValue() == 100L);
            assertTrue(productDTOV3.getSeller().getId().longValue() == 3234L ||
                    productDTOV3.getSeller().getId().longValue() == 9207L);

            assertTrue(productDTOV3.getPurchasePrice().compareTo(purchasePriceFrom) >= 0);
            assertFalse(productStatesExclude.contains(productDTOV3.getProductState()));

            //проверка сортировки по purchasePrice
            assertEquals(productDTOV3.getId(), sortProductsOList.get(i).getId());
        }
    }

    //@Test
    @Order(14)
    @Transactional
    public void getSizes() {

        //категория 10
        List<SizeTypeResponseDTO> sizes = dictHelperService.getSizes(10L);

        assertEquals(8, sizes.size());
    }

    @Test
    @Order(12)
    @Transactional
    public void editProductPATCH() {
        Product product = new Product();

        product.setProductConditionId(1L);
        product.setProductState(ProductState.DRAFT);

        User retoucher = userRepository.findById(20L).get();
        product.setRetoucher(retoucher);

        //этот "простой" парметр одолжен остаться без изменения
        product.setStoreCode("storeCode1");
        //этот обновим
        product.setName("name1");

        //7, 8
        Brand b = brandRepository.findById(7L).get();
        product.setBrand(b);

        //5, 6
        product.setCategoryId(5L);

        product.setDeliveryDescription("Delivery description");
        product.setDescription("Description");
        product.setModel("Model");
        product.setOrigin("Origin");
        product.setPaymentDescription("Payment description");
        product.setSeller(userRepository.findById(30L).get());

        product.getSeller().setSaleShipmentRoute(null);
        userRepository.save(product.getSeller());

        product = productRepository.save(product);
        commitTransaction();
        tmpProductId = product.getId();

        ProductEditRequestV3 productEditRequestV3 = new ProductEditRequestV3();
        productEditRequestV3.setId(tmpProductId);
        productEditRequestV3.setName("name2");
        productEditRequestV3.setRetoucherId(30L);
        productEditRequestV3.setBrandId(8L);
        productEditRequestV3.setCategoryId(6L);
        productEditRequestV3.setProductConditionId(2L);

        productServiceAPI.editProductPATCH(productEditRequestV3);
        product = productRepository.findById(tmpProductId).get();

        assertEquals(ProductState.DRAFT, product.getProductState());
        assertEquals("storeCode1", product.getStoreCode());
        assertEquals("name2", product.getName());
        assertEquals(30L, product.getRetoucher().getId());
        assertEquals(8L, product.getBrand().getId());
        assertEquals(6L, product.getCategoryId());
        assertEquals(2L, product.getProductConditionId());

        //несуществующий ретушер
        productEditRequestV3.setRetoucherId(-1L);
        try {
            productServiceAPI.editProductPATCH(productEditRequestV3);
            fail();
        } catch (ProductEditException ex) {
            assertEquals("Retoucher not found for id -1", ex.getMessage());
        }
        productEditRequestV3.setRetoucherId(null);

        //несуществующий бренд
        productEditRequestV3.setBrandId(-1L);
        try {
            productServiceAPI.editProductPATCH(productEditRequestV3);
            fail();
        } catch (BrandNotFoundAdministrationException ex) {

        }
        productEditRequestV3.setBrandId(null);

        //несуществующая категория
        productEditRequestV3.setCategoryId(-1L);
        try {
            productServiceAPI.editProductPATCH(productEditRequestV3);
            fail();
        } catch (AdministrationException ex) {
            assertEquals("Категория не найдена: -1", ex.getMessage());
        }
        productEditRequestV3.setCategoryId(null);

        //несуществующая productModel
        productEditRequestV3.setProductModelId(-1L);
        try {
            productServiceAPI.editProductPATCH(productEditRequestV3);
            fail();
        } catch (ProductEditException ex) {
            assertEquals("Wrong product model id -1", ex.getMessage());
        }
        productEditRequestV3.setProductModelId(null);

        ProductPriceDTOV3 productPriceDTOV3 = new ProductPriceDTOV3();
        productPriceDTOV3.setCurrentPrice(BigDecimal.ZERO);
        productEditRequestV3.setProductPriceContainer(productPriceDTOV3);

        try {
            productServiceAPI.editProductPATCH(productEditRequestV3);
            fail();
        } catch (ProductEditException ex) {
            assertTrue(ex.getMessage().contains("Inconsistent price params") ||
                    ex.getMessage().contains("Несогласованные значения цен"));
        }

        productPriceDTOV3.setCurrentPrice(new BigDecimal("100.00"));
        productPriceDTOV3.setSellerPrice(new BigDecimal("50.00"));
        productPriceDTOV3.setCurrentPriceCurrencyId(currencyService.getBaseCurrencyCached().getId());
        productPriceDTOV3.setCustomCommission(false);
        productPriceDTOV3.setSalesChannel(SalesChannel.WEBSITE);
        productPriceDTOV3.setCurrentPriceInCurrency(new BigDecimal("101.00"));
        //смена цены
        productServiceAPI.editProductPATCH(productEditRequestV3);

        product = productRepository.findById(tmpProductId).get();

        assertTrue((new BigDecimal("100.00")).compareTo(product.getCurrentPrice()) == 0);
        assertTrue((new BigDecimal("75.00")).compareTo(product.getCurrentPriceWithoutCommission()) == 0);
        assertTrue((new BigDecimal("101.00")).compareTo(product.getCurrentPriceInCurrency()) == 0);
        assertEquals(currencyService.getBaseCurrencyCached().getId(), product.getCurrentPriceCurrencyId());

        //кросс бордер
        SaleShipmentRoute saleShipmentRoute = saleShipmentRouteRepository.getBySystemName("EuropeDubaiMoscowGBS")
                .orElseThrow(IllegalArgumentException::new);
        product.getSeller().setSaleShipmentRoute(saleShipmentRoute);
        userRepository.save(product.getSeller());

        productPriceDTOV3.setCurrentPriceInCurrency(new BigDecimal("102.00"));

        try {
            productServiceAPI.editProductPATCH(productEditRequestV3);
            fail();
        } catch (ProductEditException ex) {
            assertTrue(ex.getMessage().contains("Price in currency change for cross border products is not supported"));
        }

        productPriceDTOV3.setCurrentPriceInCurrency(new BigDecimal("101.00"));
        productPriceDTOV3.setCurrentPriceCurrencyId(2L);

        try {
            productServiceAPI.editProductPATCH(productEditRequestV3);
            fail();
        } catch (ProductEditException ex) {
            assertTrue(ex.getMessage().contains("Price in currency change for cross border products is not supported"));
        }

        productPriceDTOV3.setCurrentPriceInCurrency(new BigDecimal("101.00"));
        productPriceDTOV3.setCurrentPriceCurrencyId(1L);
        productPriceDTOV3.setCurrentPrice(new BigDecimal("110.00"));

        productServiceAPI.editProductPATCH(productEditRequestV3);
        product = productRepository.findById(tmpProductId).get();
        assertTrue((new BigDecimal("110.00")).compareTo(product.getCurrentPrice()) == 0);
    }

    //@Test
    @Order(13)
    @Transactional
    @WithMockUser(username = "user", authorities = {"RETOUCHING_MODERATION"})
    public void editProductPUT() {
        Product product = productRepository.findById(tmpProductId).get();
        ProductModel productModel = productModelRepository.findById(200l).get();
        product.setProductModel(productModel);
        product.setCategoryId(36L);

        product = productRepository.save(product);

        //-------------------------------------- атрибуты BEGIN --------------------------------------------
        //attributeId = 2, value = Бархат
        AttributeValue attributeValue1_1 = attributeValueRepository.findById(24L).get();

        //attributeId = 2, value = Кожа
        AttributeValue attributeValue1_2 = attributeValueRepository.findById(26L).get();

        ProductAttributeValueBinding productAttributeValueBinding1_1 = new ProductAttributeValueBinding(product, attributeValue1_1);
        productAttributeValueBinding1_1 = productAttributeValueBindingRepository.save(productAttributeValueBinding1_1);

        ProductAttributeValueBinding productAttributeValueBinding1_2 = new ProductAttributeValueBinding(product, attributeValue1_2);
        productAttributeValueBinding1_2 = productAttributeValueBindingRepository.save(productAttributeValueBinding1_2);
        //-----------------------

        //"материал одежды" ----------------
        AttributeValue attributeValue2_1 = attributeValueRepository.findById(36L).get();

        AttributeValue attributeValue2_2 = attributeValueRepository.findById(37L).get();

        ProductAttributeValueBinding productAttributeValueBinding2_1 = new ProductAttributeValueBinding(product, attributeValue2_1);
        productAttributeValueBinding2_1 = productAttributeValueBindingRepository.save(productAttributeValueBinding2_1);

        ProductAttributeValueBinding productAttributeValueBinding2_2 = new ProductAttributeValueBinding(product, attributeValue2_2);
        productAttributeValueBinding2_2 = productAttributeValueBindingRepository.save(productAttributeValueBinding2_2);

        product = productRepository.findById(tmpProductId).get();

        //-------------------------------------- атрибуты END --------------------------------------------

        //-------------------------------------- размеры BEGIN --------------------------------------------
        //какие-то существующие в БД размеры
        adminProductService.setSizeCount(product, 2L, 20);
        adminProductService.setSizeCount(product, 3L, 20);

        product = productRepository.findById(tmpProductId).get();

        //проверка наличия размеров в БД ----------------
        assertEquals(2, product.getAvailableProductItems().size());

        Optional<ProductItem> size1Opt = product.getAvailableProductItems().stream().filter(
                productItem -> productItem.getSize().getId().equals(2L)).findFirst();
        assertTrue(size1Opt.isPresent());

        Optional<ProductItem> size2Opt = product.getAvailableProductItems().stream().filter(
                productItem -> productItem.getSize().getId().equals(3L)).findFirst();
        assertTrue(size2Opt.isPresent());
        //-----------------------------------------------

        Map<Long, Map<Long, Integer>> additionalSizes = new HashMap<>();

        additionalSizes.put(2L, new HashMap<>());
        additionalSizes.put(3L, new HashMap<>());
        //какие-то существующие в БД AdditionalSize
        additionalSizes.get(2L).put(3L, 33);
        additionalSizes.get(2L).put(4L, 44);

        additionalSizes.get(3L).put(5L, 55);
        additionalSizes.get(3L).put(6L, 66);

        additionalSizeService.updateAdditionalSizeValuesForProductItem(product, additionalSizes);

        product = productRepository.findById(tmpProductId).get();
        //--------------------------------
        //проверка наличия additionalValues в БД

        ProductItem size1 = product.getAvailableProductItems().stream().filter(
                pi -> pi.getSize().getId().equals(2L)).findFirst().get();

        List<ProductItemAdditionalSizeValue> values1 =
                productItemAdditionalSizeValueRepository.findAdditionalSizeValuesByProductItem(size1);

        assertEquals(2, values1.size());

        ProductItemAdditionalSizeValue val1_1 = values1.stream().filter(
                val -> val.getAdditionalSize().getId().equals(3L)).findFirst().get();
        assertEquals(33, val1_1.getValue());
        ProductItemAdditionalSizeValue val1_2 = values1.stream().filter(
                val -> val.getAdditionalSize().getId().equals(4L)).findFirst().get();
        assertEquals(44, val1_2.getValue());

        //-----

        ProductItem size2 = product.getAvailableProductItems().stream().filter(
                pi -> pi.getSize().getId().equals(3L)).findFirst().get();

        List<ProductItemAdditionalSizeValue> values2 =
                productItemAdditionalSizeValueRepository.findAdditionalSizeValuesByProductItem(size2);

        assertEquals(2, values2.size());

        ProductItemAdditionalSizeValue val2_1 = values2.stream().filter(
                val -> val.getAdditionalSize().getId().equals(5L)).findFirst().get();
        assertEquals(55, val2_1.getValue());
        ProductItemAdditionalSizeValue val2_2 = values2.stream().filter(
                val -> val.getAdditionalSize().getId().equals(6L)).findFirst().get();
        assertEquals(66, val2_2.getValue());

        product = productRepository.findById(tmpProductId).get();
        //-------------------------------------- размеры END --------------------------------------------

        ProductEditRequestV3 productEditRequestV3 = new ProductEditRequestV3();
        productEditRequestV3.setId(product.getId());
        productEditRequestV3.setProductModelId(product.getProductModel().getId());
        productEditRequestV3.setProductState(product.getProductState());
        productEditRequestV3.setProductConditionId(product.getProductConditionId());
        productEditRequestV3.setDescription(product.getDescription());
        productEditRequestV3.setModel(product.getModel());
        productEditRequestV3.setName(product.getName());
        productEditRequestV3.setRetoucherId(product.getRetoucher().getId());
        productEditRequestV3.setBrandId(product.getBrand().getId());
        productEditRequestV3.setCategoryId(product.getCategoryId());
        productEditRequestV3.setProductConditionId(product.getProductConditionId());
        productEditRequestV3.setDeliveryDescription(product.getDeliveryDescription());
        productEditRequestV3.setNewCollection(product.isNewCollection());
        productEditRequestV3.setOrigin(product.getOrigin());
        productEditRequestV3.setPaymentDescription(product.getPaymentDescription());
        productEditRequestV3.setRrpPrice(product.getRrpPrice());
        productEditRequestV3.setAtOffice(product.isAtOffice());
        productEditRequestV3.setSellerId(product.getSeller().getId());
        productEditRequestV3.setPurchaseYear(product.getPurchaseYear());
        productEditRequestV3.setSizeType(product.getSizeType());
        productEditRequestV3.setSlidesCount(product.getSlidesCount());
        productEditRequestV3.setSerialNumber(product.getSerialNumber());
        productEditRequestV3.setSourceLink(product.getSourceLink());
        productEditRequestV3.setStartPrice(product.getStartPrice());
        productEditRequestV3.setStoreCode(product.getStoreCode());
        productEditRequestV3.setTurbo(product.isTurbo());
        productEditRequestV3.setVendorCode(product.getVendorCode());
        productEditRequestV3.setVintage(product.isVintage());

        //атрибуты
        product.getAttributeValues().forEach(binding -> productEditRequestV3.getAttributes().add(
                new AdminProductService.ProductAttributeValue(
                        binding.getAttributeValue().getAttributeId(),
                        binding.getAttributeValue().getId())));

        //размеры
        product.getProductItems().forEach(item -> {
            productEditRequestV3.getSizeCounts().put(item.getSize().getId(), item.getCount());

            Map<Long, Integer> additionalSizesMap = new HashMap<>();
            item.getAdditionalSizeValuesAsMap().forEach((k, v) -> additionalSizesMap.put(k, v));

            productEditRequestV3.getAdditionalSizeValues().put(item.getSize().getId(), additionalSizesMap);
        });

        productEditRequestV3.setDeliveryDescription(null);
        productEditRequestV3.setPaymentDescription("New payment desc");

        log.error("------ REQUEST " + productEditRequestV3);

        String beforeStr = product.toString();

        log.error("------ BEFORE " + product);

        assertNotNull(product.getPaymentDescription());
        assertNotNull(product.getDeliveryDescription());

        productServiceAPI.editProductPUT(productEditRequestV3);
        product = productRepository.findById(tmpProductId).get();

        assertNull(product.getDeliveryDescription());
        assertEquals("New payment desc", product.getPaymentDescription());

        String afterStr = product.toString();
        log.error("------ AFTER " + product);

        assertEquals(beforeStr, afterStr);
    }

    private int countByState(ProductState productState, List<ProductStateResponseDTO> productStateResponseDTOList) {
        for (ProductStateResponseDTO responseDTO : productStateResponseDTOList) {
            if (productState == responseDTO.getProductState()) {
                return responseDTO.getProductsCount();
            }
        }

        return 0;
    }

    private void createProducts(ProductState productState, int count,
                                User user, Category category, Brand brand) {
        for (int i = 0; i < count; i++) {
            createProduct(
                    productState,
                    "Prod_" + productState + "_" + i,
                    user,
                    category,
                    brand);
        }
    }

    private Product createProduct(ProductState productState, String name,
                                  User user, Category category, Brand brand) {
        Product p = new Product();
        p.setCategoryId(category.getId());
        p.setBrand(brand);
        p.setDeliveryDescription("Delivery description");
        p.setDescription("Description");
        p.setModel("Model");
        p.setName(name);
        p.setOrigin("Origin");
        p.setPaymentDescription("Payment description");
        p.setSeller(user);
        p.setProductState(productState);

        return productRepository.save(p);
    }

    private Brand brand() {
        return brandRepository.findById(2L).get();
    }

    private Category category() {
        return categoryRepository.findById(2L).get();
    }

    private boolean isListContainsSellerTypeDTO(Enum e, List<EnumLabelPairDTO>
            sellerTypeResponseDTOS) {
        for (EnumLabelPairDTO sellerTypeResponseDTO : sellerTypeResponseDTOS) {
            if (e.equals(sellerTypeResponseDTO.getValue())) {
                return true;
            }
        }

        return false;
    }
}
