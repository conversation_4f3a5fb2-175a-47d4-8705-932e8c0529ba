package ru.oskelly.tests.pr.suite7.admin.rolemodel;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/testRolesDto")
public class TestRolesController {

    @GetMapping("/dto")
    public ResponseEntity<TestClass> getDto() {
        return ResponseEntity.ok(new TestClass());
    }
}
