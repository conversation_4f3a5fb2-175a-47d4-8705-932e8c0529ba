package ru.oskelly.tests.pr.suite7.admin;

import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserAuthorityBindingRepository;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.commission.CommissionGridRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.dao.userban.UserBanRepository;
import su.reddot.domain.model.commission.CommissionGrid;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.user.SellerType;
import su.reddot.domain.model.user.User;
import su.reddot.domain.model.user.UserAuthorityBinding;
import su.reddot.domain.model.user.userban.BanType;
import su.reddot.domain.model.user.userban.UserBan;
import su.reddot.domain.service.adminpanel.user.AdminV2UserService;
import su.reddot.domain.service.adminpanel.user.domain.AdminV2UsersRequest;
import su.reddot.domain.service.dto.Page;
import su.reddot.presentation.api.v2.adminpanel.users.dto.AdminPanelUserDTO;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.UUID;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Slf4j
@Disabled
public class UsersApiV3Test extends AbstractSpringTest {
    private static final Random random = new Random();
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private AdminV2UserService adminV2UserService;
    @Autowired
    private ProductRepository productRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private UserBanRepository userBanRepository;
    @Autowired
    private CommissionGridRepository commissionGridRepository;
    @Autowired
    private UserAuthorityBindingRepository userAuthorityBindingRepository;

    private User user;
    private User anonymUser;
    private UserAuthorityBinding userAuthorityBinding;
    private String userNickNamePrefix = "usersApiV3TestNickname";
    private int newUserCount = 30;

    private AdminV2UsersRequest basicUserRequestDto() {
        AdminV2UsersRequest usersRequestDTO = new AdminV2UsersRequest();
        usersRequestDTO.setIdsExclude(Collections.singletonList(-1L));
        return usersRequestDTO;
    }

    @BeforeAll
    public void init() {
        for (int i = 0; i < newUserCount; i++) {
            String nickName = userNickNamePrefix + i;
            removeUsersByNickName(nickName);
            User user = createUser(sellerTypeByNumber(i), nickName);
            if (i % 3 == 0) {
                createUserBan(user, i);
            }
        }
    }

    @AfterAll
    public void cleanup() {
        clearProducts();
        cleanUserAuthorityBinding();
        cleanUser();
        cleanAnonymUser();
        for (int i = 0; i < newUserCount; i++) {
            removeUsersByNickName(userNickNamePrefix + i);
        }
    }

    @Test
    public void shouldReturnDifferentUsersInDifferentPages() {
        AdminV2UsersRequest usersRequest1 = basicUserRequestDto();
        usersRequest1.setPage(1);
        usersRequest1.setRowsPerPage(6);
        Page<AdminPanelUserDTO> userPage1 = adminV2UserService.getUsers(usersRequest1);
        assertEquals(6, userPage1.getItemsCount());

        AdminV2UsersRequest usersRequest2 = basicUserRequestDto();
        usersRequest2.setPage(1);
        usersRequest2.setRowsPerPage(3);
        Page<AdminPanelUserDTO> userPage2 = adminV2UserService.getUsers(usersRequest2);
        assertEquals(3, userPage2.getItemsCount());

        AdminV2UsersRequest usersRequest3 = basicUserRequestDto();
        usersRequest3.setPage(2);
        usersRequest3.setRowsPerPage(3);
        Page<AdminPanelUserDTO> userPage3 = adminV2UserService.getUsers(usersRequest3);
        assertEquals(3, userPage3.getItemsCount());

        AdminPanelUserDTO userFromPage2 = userPage2.getItems().get(0);
        AdminPanelUserDTO userFromPage3 = userPage3.getItems().get(0);

        Assertions.assertThat(userFromPage2.getId()).isNotEqualTo(userFromPage3.getId());
    }

    @Test
    public void shouldFilterUsersByUserType() {
        AdminV2UsersRequest usersRequest = basicUserRequestDto();
        usersRequest.setPage(1);
        usersRequest.setRowsPerPage(6);
        usersRequest.setNickname("name");
        usersRequest.setSellerTypes(Collections.singletonList(SellerType.INDIVIDUAL));
        Page<AdminPanelUserDTO> userPage = adminV2UserService.getUsers(usersRequest);
        assertEquals(4, userPage.getItemsCount());
        List<Object> individualUserIds = userPage.getItems().stream()
            .map(AdminPanelUserDTO::getId).collect(Collectors.toList());

        usersRequest = basicUserRequestDto();
        usersRequest.setPage(1);
        usersRequest.setRowsPerPage(6);
        usersRequest.setNickname("name");
        usersRequest.setSellerTypes(Collections.singletonList(SellerType.BOUTIQUE));
        userPage = adminV2UserService.getUsers(usersRequest);
        assertEquals(4, userPage.getItemsCount());
        List<Object> boutiqueUsersIds = userPage.getItems().stream()
            .map(AdminPanelUserDTO::getId).collect(Collectors.toList());

        individualUserIds.forEach(individualUserId -> {
            Assertions.assertThat(boutiqueUsersIds).doesNotContain(individualUserId);
        });
    }

    @Test
    public void shouldFilterUsersByName() {
        AdminV2UsersRequest usersRequest = basicUserRequestDto();
        usersRequest.setNickname("name");
        usersRequest.setDescending(false);
        usersRequest.setPage(3);
        usersRequest.setRowsPerPage(5);

        Page<AdminPanelUserDTO> userPage = adminV2UserService.getUsers(usersRequest);

        assertEquals(5, userPage.getItemsCount());
        userPage.getItems().forEach(item -> {
            Assertions.assertThat(item.getInfo().getNickname().toLowerCase()).contains("name");
        });
    }

    @Test
    public void shouldFilterUsersByIds() {
        AdminV2UsersRequest usersRequest = basicUserRequestDto();
        usersRequest.setNickname("name");
        usersRequest.setDescending(false);
        usersRequest.setPage(1);
        usersRequest.setRowsPerPage(20);

        Page<AdminPanelUserDTO> userPage = adminV2UserService.getUsers(usersRequest);

        List<Long> userIds = userPage.getItems().stream().map(AdminPanelUserDTO::getId).collect(Collectors.toList());

        List<Long> toBeIncludedIds = new ArrayList<>();
        List<Long> toBeExcludedIds = new ArrayList<>();

        for (int i = 0; i < userIds.size(); i++) {
            if (i % 2 == 0) {
                toBeIncludedIds.add(userIds.get(i));
            } else {
                toBeExcludedIds.add(userIds.get(i));
            }
        }

        usersRequest.setIdsInclude(toBeIncludedIds);
        usersRequest.setIdsExclude(toBeExcludedIds);
        userPage = adminV2UserService.getUsers(usersRequest);

        userPage.getItems().forEach(user -> {
            Assertions.assertThat(toBeIncludedIds).contains(user.getId());
            Assertions.assertThat(toBeExcludedIds).doesNotContain(user.getId());
        });
    }

    @Test
    public void shouldFilterUsersByUserBans() {
        List<BanType> banTypes = new ArrayList<>();
        banTypes.add(BanType.COMMENT_BAN);
        banTypes.add(BanType.USER_BAN);
        banTypes.add(BanType.BARGAIN_BAN);
        banTypes.add(BanType.PUBLISH_BAN);
        banTypes.add(BanType.STORIES_BAN);
        banTypes.add(BanType.STREAM_BAN);
        banTypes.add(BanType.WARNING);
        AdminV2UsersRequest usersRequest = basicUserRequestDto();
        usersRequest.setDescending(false);
        usersRequest.setPage(1);
        usersRequest.setRowsPerPage(20);
        usersRequest.setIsActiveBan(true);
        usersRequest.setBanTypes(banTypes);

        Page<AdminPanelUserDTO> userPage = adminV2UserService.getUsers(usersRequest);

        List<AdminPanelUserDTO> items = userPage.getItems();
        Assertions.assertThat(items).hasSize(5);
    }

    private void cleanUser() {
        if (user != null) {
            Optional<User> userOptional = userRepository.findById(user.getId());

            if (userOptional.isPresent()) {
                List<Product> products = productRepository.getAllBySeller(userOptional.get());
                productRepository.deleteAll(products);
            }

            userRepository.deleteById(user.getId());
            user = null;
        }
    }

    private void removeUsersByNickName(String nickName) {
        User user = userRepository.findByNickname(nickName);
        if (user != null) {
            String removeUserBanAud = String.format("delete from user_ban_aud where user_id = %d", user.getId());
            String removeUserBan = String.format("delete from user_ban where user_id = %d", user.getId());
            jdbcTemplate.execute(removeUserBanAud);
            jdbcTemplate.execute(removeUserBan);
            user.setUserBans(null);
            userRepository.deleteById(user.getId());
        }
    }

    private void cleanAnonymUser() {
        if (anonymUser != null) {
            userRepository.deleteById(anonymUser.getId());
            anonymUser = null;
        }
    }

    private void cleanUserAuthorityBinding() {
        if (userAuthorityBinding != null) {
            userAuthorityBindingRepository.deleteById(userAuthorityBinding.getId());
        }

        userAuthorityBinding = null;
    }

    private void clearProducts() {
        productRepository.deleteAll(productRepository.getAllBySeller(user));
    }

    private User createUser(SellerType sellerType, String nickname) {
        CommissionGrid commissionGrid = commissionGridRepository.findById(1L).get();

        User user = new User();
        user.setEmail(UUID.randomUUID() + "@acme.com");
        user.setNickname(nickname);
        user.setUserType(User.UserType.SIMPLE_USER);
        user.setRegistrationTime(ZonedDateTime.now());
        user.setChangeTime(LocalDateTime.now());
        user.setSellerType(sellerType);
        user.setCommissionGrid(commissionGrid);

        return userRepository.save(user);
    }

    private UserBan createUserBan(User user, int i) {
        BanType banType = banTypeByNumber(i);
        UserBan userBan = new UserBan();
        userBan.setUserId(user.getId());
        boolean isBaned = ((i % 6) == 0);
        if (!isBaned && random.nextBoolean()) {
            userBan.setBaned(isBaned);
        }
        if (isBaned) {
            userBan.setBaned(true);
        }
        userBan.setBanType(banType);
        userBan.setDescription(user.getNickname() + "Descr");
        userBan.setTitle(user.getNickname() + "Title");
        ZonedDateTime creationDate = ZonedDateTime.now().minusDays(20);
        userBan.setCreateDate(creationDate);
        userBan.setStartDate(creationDate);
        userBan.setEndDate(
            isBaned ? ZonedDateTime.now().plusDays(3) : ZonedDateTime.now().minusDays(3)
        );
        userBan.setDeleted(!userBan.isBaned());
        return userBanRepository.saveAndFlush(userBan);
    }

    private SellerType sellerTypeByNumber(int i) {
        if (i < 2) {
            return null;
        }
        int sellerTypesCount = SellerType.values().length;
        return SellerType.values()[(i - 2) % sellerTypesCount];
    }

    private BanType banTypeByNumber(int i) {
        int banTypesCount = BanType.values().length;
        return BanType.values()[i % banTypesCount];
    }
}
