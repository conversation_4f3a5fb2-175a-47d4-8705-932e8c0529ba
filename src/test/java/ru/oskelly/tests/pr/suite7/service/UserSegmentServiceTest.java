package ru.oskelly.tests.pr.suite7.service;

import com.fasterxml.jackson.core.type.TypeReference;
import org.assertj.core.api.Assertions;
import org.assertj.core.data.Index;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestJsonDataUtils;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.segment.UserListMode;
import su.reddot.domain.model.segment.UserSegment;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.segment.SegmentDTO;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.segment.UserSegmentService;
import su.reddot.domain.service.task.UserVectorRunner;
import su.reddot.domain.service.user.UserService;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

public class UserSegmentServiceTest extends AbstractSpringTest {

    @Autowired
    protected UserSegmentService userSegmentService;

    @Autowired
    protected OrderService orderService;

    @Autowired
    protected UserService userService;

    @Autowired
    protected UserVectorRunner userVectorRunner;

    @Autowired
    protected JdbcTemplate jdbcTemplate;

    protected final static Long USER_WITH_SOME_ORDERS = 16644L; // TODO: Change in case of fail, better lookup by DB
    protected final static Long USER_WITH_ZERO_ORDERS = 12L;    // TODO: Change in case of fail, better create newOne

    protected final static Long SAU_COUNTRY_ID = 193L;

    @BeforeEach
    public void init() {
        long lookDays = Duration.between(LocalDate.of(2020, 1, 1).atStartOfDay(), LocalDateTime.now()).toDays();
        userVectorRunner.recalculateUserOrders(Long.valueOf(lookDays).intValue());
        //
        User zeroOrdersUser = userService.getOne(USER_WITH_ZERO_ORDERS);
        List<Order> zeroOrdersList = orderService.getFinishedOrdersForBuyer(zeroOrdersUser);
        Assertions.assertThat(zeroOrdersList).as("We need user with zero orders: change ID in case of fail").isEmpty();
        //
        User someOrdersUser = userService.getOne(USER_WITH_SOME_ORDERS);
        List<Order> someOrdersList = orderService.getFinishedOrdersForBuyer(someOrdersUser);
        Assertions.assertThat(someOrdersList).as("We need user with some orders: change ID in case of fail").isNotEmpty();
    }

    public void segmentsDictTestCommon() {
        List<SegmentDTO> baseSegmentsList = userSegmentService.findAll().stream()
                .filter(it -> it.getId() < UserSegment.MAX_PREDEFINED_SEGMENT_ID)
                .sorted(Comparator.comparing(SegmentDTO::getId))
                .collect(Collectors.toList());
        String baseSegmentsJson = TestJsonDataUtils.objectToJson(baseSegmentsList);
        Assertions.assertThat(baseSegmentsJson).as("Dumb case: just to view json").isNotEmpty();
        //
        List<SegmentDTO> jsonSegmentsList = TestJsonDataUtils.readJsonTestData(this, null, new TypeReference<List<SegmentDTO>>(){});
        Assertions.assertThat(jsonSegmentsList)
                .usingRecursiveComparison()
                    .ignoringFields() // Fukken strange but "country.id" comparison won`t work without this
                    .comparingOnlyFields("id", "isEditable", "userListMode", "country.id")
                .isEqualTo(baseSegmentsList);
        //
        baseSegmentsList.forEach(it ->
                Assertions.assertThat(userSegmentService.findById(it.getId()).orElse(null))
                        .usingRecursiveComparison()
                        .isEqualTo(it)
        );
    }

    public void countrySegmentFindAllTestAnyCommon() {
        List<SegmentDTO> nullOrdersIsGuest = userSegmentService.findAllByUserIdOrGuestToken(null, UUID.randomUUID().toString(), null);
        Assertions.assertThat(nullOrdersIsGuest).hasSize(2)
                .satisfies(it -> {
                    Assertions.assertThat(it.getUserListMode()).isEqualTo(UserListMode.ALL_USERS);
                    Assertions.assertThat(it.getCountry()).isNull();
                }, Index.atIndex(0))
                .satisfies(it -> {
                    Assertions.assertThat(it.getUserListMode()).isEqualTo(UserListMode.NOOB_USERS);
                    Assertions.assertThat(it.getCountry()).isNull();
                }, Index.atIndex(1));
        //
        List<SegmentDTO> zeroOrdersIsGuest = userSegmentService.findAllByUserIdOrGuestToken(USER_WITH_ZERO_ORDERS, UUID.randomUUID().toString(), null);
        Assertions.assertThat(zeroOrdersIsGuest).hasSize(2)
                .satisfies(it -> {
                    Assertions.assertThat(it.getUserListMode()).isEqualTo(UserListMode.ALL_USERS);
                    Assertions.assertThat(it.getCountry()).isNull();
                }, Index.atIndex(0))
                .satisfies(it -> {
                    Assertions.assertThat(it.getUserListMode()).isEqualTo(UserListMode.NOOB_USERS);
                    Assertions.assertThat(it.getCountry()).isNull();
                }, Index.atIndex(1));
        //
        List<SegmentDTO> someOrdersNoGuest = userSegmentService.findAllByUserIdOrGuestToken(USER_WITH_SOME_ORDERS, null, null);
        Assertions.assertThat(someOrdersNoGuest).hasSize(2)
                .satisfies(it -> {
                    Assertions.assertThat(it.getUserListMode()).isEqualTo(UserListMode.ALL_USERS);
                    Assertions.assertThat(it.getCountry()).isNull();
                }, Index.atIndex(0))
                .satisfies(it -> {
                    Assertions.assertThat(it.getUserListMode()).isEqualTo(UserListMode.USERS_WITH_ORDERS);
                    Assertions.assertThat(it.getCountry()).isNull();
                }, Index.atIndex(1));
        //
        List<SegmentDTO> someOrdersIsGuest = userSegmentService.findAllByUserIdOrGuestToken(USER_WITH_SOME_ORDERS, UUID.randomUUID().toString(), null);
        Assertions.assertThat(someOrdersIsGuest).hasSize(2)
                .satisfies(it -> {
                    Assertions.assertThat(it.getUserListMode()).isEqualTo(UserListMode.ALL_USERS);
                    Assertions.assertThat(it.getCountry()).isNull();
                }, Index.atIndex(0))
                .satisfies(it -> {
                    Assertions.assertThat(it.getUserListMode()).isEqualTo(UserListMode.USERS_WITH_ORDERS);
                    Assertions.assertThat(it.getCountry()).isNull();
                }, Index.atIndex(1));
    }

}
