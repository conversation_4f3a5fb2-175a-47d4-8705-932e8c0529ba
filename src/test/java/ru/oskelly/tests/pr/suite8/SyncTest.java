package ru.oskelly.tests.pr.suite8;

import org.apache.kafka.clients.producer.ProducerRecord;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.data.domain.PageRequest;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.ProducerListener;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.common.messaging.messages.Message;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.ResendDTO;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.UserSyncQueueRepository;
import su.reddot.domain.dao.notification.NotificationGroupUserBindingRepository;
import su.reddot.domain.model.notification.NotificationGroupUserBinding;
import su.reddot.domain.model.user.SellerType;
import su.reddot.domain.model.user.User;
import su.reddot.domain.model.user.UserSyncEvent;
import su.reddot.domain.model.user.UserSyncTransactionEventListener;
import su.reddot.domain.service.config.modelmapper.ModelMapperFactoryUtil;
import su.reddot.domain.service.kafka.KafkaSenderService;
import su.reddot.domain.service.usersync.dto.UserSyncDataDTO;
import su.reddot.domain.service.usersync.dto.UserSyncMessageDTO;
import su.reddot.domain.service.usersync.mapper.UserSyncMapper;
import su.reddot.domain.service.usersync.service.UserForSyncIsDeletedException;
import su.reddot.domain.service.usersync.service.UserSyncQueueService;
import su.reddot.domain.service.usersync.service.UserWithSameEmailExistsException;
import su.reddot.domain.service.usersync.service.UserWithSamePhoneExistsException;
import su.reddot.domain.service.usersync.service.impl.UserSyncServiceConsumerImpl;
import su.reddot.domain.service.usersync.service.impl.UserSyncServiceSenderImpl;
import su.reddot.infrastructure.util.ProductionEnvironment;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Random;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_08)
@TestPropertySource(properties = {
    "app.kafka.enabled=true",
    "user-sync-service.enabled.send=true",
    "user-sync-service.enabled.consume=false",
    "internationalVersion=false",
    "user-sync-service.kafka.topic=qweasd"
})
public class SyncTest extends AbstractSyncTest {

    @Autowired
    private UserRepository userRepository;
    @SpyBean
    private UserSyncTransactionEventListener listener;
    @SpyBean
    private su.reddot.domain.service.usersync.service.UserSyncServiceSender userSyncService;
    @SpyBean
    private UserSyncServiceSenderImpl defaultUserSyncService;
    @SpyBean
    private UserSyncServiceConsumerImpl userSyncServiceConsumer;
    @SpyBean
    private UserSyncQueueService userSyncQueueService;
    @Autowired
    private NotificationGroupUserBindingRepository notificationGroupUserBindingRepository;
    @SpyBean
    private KafkaSenderService kafkaSender;
    @MockBean(name = "kafkaTemplate")
    private KafkaTemplate<String, String> kafkaTemplate;
    @MockBean(name = "objectKafkaTemplate")
    private KafkaTemplate<String, Message> objectKafkaTemplate;
    @Autowired
    private UserSyncQueueRepository syncQueueRepository;
    private final ModelMapper modelMapper = ModelMapperFactoryUtil.modelMapper();
    @Autowired
    private UserSyncMapper userSyncMapper;

    private static final Long TEST_USER_ID = 10L;

    @BeforeEach
    @Transactional
    public void init() {
        initTestUser(TEST_USER_ID);
        when(kafkaTemplate.getProducerFactory()).thenCallRealMethod();
        when(objectKafkaTemplate.getProducerFactory()).thenCallRealMethod();
    }

    /*
    1. Отправка "нормального" сообщения
    2. Прием нормального (не ухудшающего) сообщения
    3. Ошибка при отправке (запись в очередь на переотправку)
    4. Прием ухудшающего сообщения
    5. Прием из того же окружения
    6. Прием протухшего
     */

    //У пользователя не изменились никакие синхронизируемые данные - отправки сообщения быть не должно
    @Test
    @Transactional
    public void testNoMessage() {
        User userToChange = userRepository.findById(TEST_USER_ID).get();
        userToChange.setSystemTime(ZonedDateTime.now());
        userRepository.save(userToChange);

        commitTransaction();

        /*
            т.к. поменялось свойство пользователя, не входящее в синхронизацию,
            не должен быть вызван метод sendUpdateData
         */
        Awaitility.await().atMost(30, TimeUnit.SECONDS).untilAsserted(() -> {
            verify(userSyncService, never()).sendUpdate(any(UserSyncDataDTO.class));
        });
    }

    //проверка того, что в UserSyncTransactionEventListener попало правильное сообщение
    @Test
    @Transactional
    public void testVerifySyncMessageInSpringListener() {
        User userToChange = userRepository.findById(TEST_USER_ID).get();
        userToChange.setPhone(randomizePhone(userToChange.getPhone()));

        userRepository.save(userToChange);

        UserSyncEvent[] dto = new UserSyncEvent[1];

        doAnswer(invocation -> {
            dto[0] = invocation.getArgument(0);
            return null;
        }).when(listener).catchEvent(any(UserSyncEvent.class));

        commitAndStartNewTransaction();

        Awaitility.await().atMost(30, TimeUnit.SECONDS).untilAsserted(() -> {
            verify(listener).catchEvent(any(UserSyncEvent.class));

            verifyUserAndSyncDataExceptPhone(dto[0].getUser(), userSyncMapper.toDTO(
                dto[0].getUser().getOnloadState()));
        });
    }

    /*
        проверка того, что в результате обработки сообщения внутри UserSyncTransactionEventListener
        будет вызван userSyncService.sendUpdateData с правильным dto
     */
    @Test
    @Transactional
    public void testSendGoodMessageWithGoodKafka() {
        //мок кафки - успешная отправка
        doNothing().when(kafkaSender).sendMessage(any(), any(), any(), any());

        User userToChange = userRepository.findById(TEST_USER_ID).get();
        //очистка очереди на отправку
        syncQueueRepository.deleteAllItemsForUser(userToChange.getUid());
        assertTrue(syncQueueRepository.getPendingUserUids(PageRequest.of(0, 100)).isEmpty());

        userToChange.setPhone(randomizePhone(userToChange.getPhone()));
        userRepository.save(userToChange);
        commitAndStartNewTransaction();

        Awaitility.await().atMost(30, TimeUnit.SECONDS).untilAsserted(() -> {
            //т.к. телефон изменился, должен быть вызван sendUpdateData
            ArgumentCaptor<UserSyncDataDTO> captor = ArgumentCaptor.forClass(UserSyncDataDTO.class);
            verify(userSyncService, atLeastOnce()).sendUpdate(captor.capture());

            List<UserSyncDataDTO> allCapturedDTOs = captor.getAllValues();

            UserSyncDataDTO capturedDTO = allCapturedDTOs.get(allCapturedDTOs.size() - 1); // Последний вызов

            verifyUserAndSyncDataExceptPhone(userToChange, capturedDTO);
            assertEquals(userToChange.getPhone(), capturedDTO.getPhone());
        });

        //после "успешной" отправки не должно быть записи в очереди на переотправку
        assertTrue(syncQueueRepository.getPendingUserUids(PageRequest.of(0, 100)).isEmpty());
    }

    //проверка того, что при неработающей кафке сообщение запишется в очередь на переотправку
    @Test
    @Transactional
    public void testGoodMessageWithBadKafka() {
        mockKafkaSenderWithExceptionOnSend();


        User userToChange = userRepository.findById(TEST_USER_ID).get();
        //очистка очереди на отправку
        syncQueueRepository.deleteAllItemsForUser(userToChange.getUid());
        assertTrue(syncQueueRepository.getPendingUserUids(PageRequest.of(0, 100)).isEmpty());

        userToChange.setPhone(randomizePhone(userToChange.getPhone()));
        userRepository.save(userToChange);
        commitAndStartNewTransaction();

        Awaitility.await().atMost(30, TimeUnit.SECONDS).untilAsserted(() -> {
            verify(userSyncQueueService, atLeastOnce()).saveMessageToRetryQueue(any(), any(), any());
        });

        //после "ошибочной" отправки должна быть запись в очереди на переотправку
        List<ResendDTO> resendDTOS = syncQueueRepository.getPendingUserUids(PageRequest.of(0, 100));

        assertFalse(resendDTOS.isEmpty());
        assertTrue(resendDTOS.stream().anyMatch(item -> item.getUserUid().toString()
            .equalsIgnoreCase(userToChange.getUid().toString())));
    }

    private void mockKafkaSenderWithExceptionOnSend() {
        doAnswer(inv -> {
            ProducerListener<String, String> producerListener = inv.getArgument(3, ProducerListener.class);
            producerListener.onError(new ProducerRecord<>(
                    inv.getArgument(0, String.class),
                    inv.getArgument(1, String.class),
                    inv.getArgument(2, String.class)),
                new RuntimeException("testGoodMessageWithBadKafka exception"));
            return null;
        }).when(kafkaSender).sendMessage(anyString(), anyString(), anyString(), any(ProducerListener.class));
    }

    private void consumeUpdate(UserSyncMessageDTO messageDTO) {
        enableConsume();
        userSyncServiceConsumer.consumeUpdate(messageDTO);
        disableConsume();
    }

    //проверка защиты от обработки сообщения с той же зоны
    @Test
    @Transactional
    public void testReceiveSameZoneMessage() {
        //сообщение из той же zone
        UserSyncMessageDTO syncMessageDTO = new UserSyncMessageDTO();
        syncMessageDTO.setSourceUserZone(ProductionEnvironment.RU);

        //Если не пройдет проверка на "ту же зону", в этом методе вылетит NullPointer, т.к. syncMessageDTO.getData() == null
        consumeUpdate(syncMessageDTO);
    }

    //проверка защиты от обработки сообщения с той же зоны
    @Test
    @Transactional
    public void testReceiveOldMessage() {
        UserSyncMessageDTO syncMessageDTO = new UserSyncMessageDTO();
        syncMessageDTO.setSourceUserZone(ProductionEnvironment.INT);

        UserSyncDataDTO dto = new UserSyncDataDTO();
        User user = userRepository.findById(TEST_USER_ID).get();

        //время обновления пользователя "до"
        LocalDateTime changeTimeBefore = user.getChangeTime();

        dto.setUid(user.getUid());

        String mockPhone = "111111111";
        dto.setPhone(mockPhone);

        syncMessageDTO.setData(dto);
        //"старые" изменения
        syncMessageDTO.setCreatedAt(user.getChangeTime().minusHours(1).atOffset(
            OffsetDateTime.now().getOffset()));

        consumeUpdate(syncMessageDTO);

        commitAndStartNewTransaction();

        user = userRepository.findById(TEST_USER_ID).get();
        LocalDateTime changeTimeAfter = user.getChangeTime();

        //никаких изменений у пользователя не должно быть, т.к. изменения "старые"
        assertFalse(user.getPhone().equalsIgnoreCase(mockPhone));
        assertEquals(changeTimeBefore, changeTimeAfter);
    }

    //прием "нормального" "свежего" сообщения. Не первая синхронизация (уже есть пользователь с таким uuid)
    @Test
    @Transactional
    public void testReceiveGoodMessageNotFirstSyncUpdate() {
        UserSyncMessageDTO syncMessageDTO = new UserSyncMessageDTO();
        syncMessageDTO.setSourceUserZone(ProductionEnvironment.INT);

        User user = userRepository.findById(TEST_USER_ID).get();
        user.setUserType(User.UserType.IP);
        user.setSellerType(SellerType.BOUTIQUE);
        user.setYandexIdToken("some yandex token " + UUID.randomUUID());
        user.setEnvironment(ProductionEnvironment.INT);
        userRepository.save(user);

        //чистим notification groups
        notificationGroupUserBindingRepository.deleteAllByUserId(user.getId());
        commitAndStartNewTransaction();

        //добавляем тестовые связи
        Set<Long> notificationGroups = new HashSet<>();
        notificationGroups.add(1L);
        notificationGroups.add(3L);
        notificationGroups.add(5L);
        notificationGroupUserBindingRepository.insertNewBindings(user.getId(), notificationGroups);
        commitAndStartNewTransaction();

        //время обновления пользователя "до"
        LocalDateTime changeTimeBefore = user.getChangeTime();

        //заполняем dto
        UserSyncDataDTO dto = userSyncMapper.toDTO(user);

        // Заполнение строковых полей с модификацией
        dto.setUid(user.getUid());  // UUID оставляем как есть
        dto.setEmail(user.getEmail() + "_modified email");
        dto.setIsTrusted(user.getIsTrusted() == null || !user.getIsTrusted());
        dto.setNickname(user.getNickname() + "_modified nickname");
        dto.setPhone(randomizePhone(user.getPhone()));
        dto.setApiHashedPassword(user.getApiHashedPassword() + "_modified apiHashedPassword");
        dto.setHashedPassword(user.getHashedPassword() + "_modified hashedPassword");
        dto.setActivationToken(user.getActivationToken() + "_modified activationToken");
        dto.setRestFacebookId(user.getRestFacebookId() + "_modified restFacebookId");
        dto.setRestVkId(user.getRestVkId() + "_modified restVkId");
        dto.setRestAppleId(user.getRestAppleId() + "_modified restAppleId");
        dto.setGoogleAccountId(user.getGoogleAccountId() + "_modified googleAccountId");
        dto.setYandexAccountId(user.getYandexAccountId() + "_modified yandexAccountId");
        dto.setGoogleIdToken(user.getGoogleIdToken() + "_modified googleIdToken");
        dto.setYandexIdToken(null);
        dto.setEnvironment(ProductionEnvironment.RU);

        // Увеличение временных полей
        dto.setPhoneVerifiedTime(ZonedDateTime.now().plusHours(1));
        dto.setBirthDate(LocalDateTime.now().plusHours(2));
        dto.setActivationTime(LocalDateTime.now().plusHours(3));
        dto.setRegistrationTime(ZonedDateTime.now().plusHours(4));
        dto.setNotificationGroupsChangeTime(ZonedDateTime.now().plusHours(7));
        dto.setAddBrandLikeNotificationTime(ZonedDateTime.now().plusHours(8));
        dto.setDeleteTime(ZonedDateTime.now().plusHours(9));

        //группы нотификаций
        Set<Long> modifiedNotificationGroups = new HashSet<>();
        modifiedNotificationGroups.add(2L);
        modifiedNotificationGroups.add(4L);
        modifiedNotificationGroups.add(5L);
        dto.setNotificationGroupIds(modifiedNotificationGroups);

        syncMessageDTO.setData(dto);

        syncMessageDTO.setCreatedAt(user.getChangeTime().plusHours(1).atOffset(
            OffsetDateTime.now().getOffset()));

        consumeUpdate(syncMessageDTO);

        commitAndStartNewTransaction();

        user = userRepository.findById(TEST_USER_ID).get();

        assertEquals(dto.getUid(), user.getUid());
        assertEquals(dto.getEmail(), user.getEmail());
        assertEquals(dto.getIsTrusted(), user.getIsTrusted());
        assertEquals(dto.getNickname(), user.getNickname());
        assertEquals(dto.getPhone(), user.getPhone());
        assertEquals(dto.getApiHashedPassword(), user.getApiHashedPassword());
        assertEquals(dto.getPhoneVerifiedTime(), user.getPhoneVerifiedTime());
        assertEquals(dto.getHashedPassword(), user.getHashedPassword());
        assertEquals(dto.getBirthDate(), user.getBirthDate());
        assertEquals(dto.getSex(), user.getSex());
        assertEquals(dto.getActivationTime(), user.getActivationTime());
        assertEquals(dto.getRegistrationTime(), user.getRegistrationTime());
        assertEquals(dto.getUserAppleUuid(), user.getUserAppleUuid());
        assertEquals(dto.getUserVkUuid(), user.getUserVkUuid());
        assertEquals(dto.getUserFbUuid(), user.getUserFbUuid());
        assertEquals(dto.getGoogleAccountId(), user.getGoogleAccountId());
        assertEquals(dto.getYandexAccountId(), user.getYandexAccountId());
        assertEquals(dto.getActivationToken(), user.getActivationToken());
        assertEquals(dto.getRestFacebookId(), user.getRestFacebookId());
        assertEquals(dto.getRestVkId(), user.getRestVkId());
        assertEquals(dto.getNotificationGroupsChangeTime(), user.getNotificationGroupsChangeTime());
        assertEquals(dto.getAddBrandLikeNotificationTime(), user.getAddBrandLikeNotificationTime());
        assertEquals(dto.getRestAppleId(), user.getRestAppleId());
        assertEquals(dto.getDeleteTime(), user.getDeleteTime());
        assertEquals(dto.getGoogleIdToken(), user.getGoogleIdToken());
        assertEquals(dto.getYandexIdToken(), user.getYandexIdToken());
        assertEquals(dto.getEnvironment(), user.getEnvironment());

        assertTrue(changeTimeBefore.isBefore(user.getChangeTime()));

        assertEquals(modifiedNotificationGroups,
            notificationGroupUserBindingRepository.findAllGroupIdsByUser(user.getId()));
    }

    @Test
    @Transactional
    public void testReceiveGoodMessageNotFirstSyncUpdate2() {
        UserSyncMessageDTO syncMessageDTO = new UserSyncMessageDTO();
        syncMessageDTO.setSourceUserZone(ProductionEnvironment.INT);

        User user = userRepository.findById(TEST_USER_ID).get();
        user.setUserType(User.UserType.IP);
        user.setSellerType(SellerType.BOUTIQUE);
        user.setYandexIdToken("some yandex token " + UUID.randomUUID());
        user.setEnvironment(ProductionEnvironment.RU);
        userRepository.save(user);

        //чистим notification groups
        notificationGroupUserBindingRepository.deleteAllByUserId(user.getId());
        commitAndStartNewTransaction();

        //добавляем тестовые связи
        Set<Long> notificationGroups = new HashSet<>();
        notificationGroups.add(1L);
        notificationGroups.add(3L);
        notificationGroups.add(5L);
        notificationGroupUserBindingRepository.insertNewBindings(user.getId(), notificationGroups);
        commitAndStartNewTransaction();

        //время обновления пользователя "до"
        LocalDateTime changeTimeBefore = user.getChangeTime();

        //заполняем dto
        UserSyncDataDTO dto = userSyncMapper.toDTO(user);

        // Заполнение строковых полей с модификацией
        dto.setUid(user.getUid());  // UUID оставляем как есть
        dto.setEmail(user.getEmail() + "_modified email");
        dto.setIsTrusted(user.getIsTrusted() == null || !user.getIsTrusted());
        dto.setNickname(user.getNickname() + "_modified nickname");
        dto.setPhone(randomizePhone(user.getPhone()));
        dto.setApiHashedPassword(user.getApiHashedPassword() + "_modified apiHashedPassword");
        dto.setHashedPassword(user.getHashedPassword() + "_modified hashedPassword");
        dto.setActivationToken(user.getActivationToken() + "_modified activationToken");
        dto.setRestFacebookId(user.getRestFacebookId() + "_modified restFacebookId");
        dto.setRestVkId(user.getRestVkId() + "_modified restVkId");
        dto.setRestAppleId(user.getRestAppleId() + "_modified restAppleId");
        dto.setGoogleAccountId(user.getGoogleAccountId() + "_modified googleAccountId");
        dto.setYandexAccountId(user.getYandexAccountId() + "_modified yandexAccountId");
        dto.setGoogleIdToken(user.getGoogleIdToken() + "_modified googleIdToken");
        dto.setYandexIdToken(null);
        dto.setEnvironment(ProductionEnvironment.INT);

        // Увеличение временных полей
        dto.setPhoneVerifiedTime(ZonedDateTime.now().plusHours(1));
        dto.setBirthDate(LocalDateTime.now().plusHours(2));
        dto.setActivationTime(LocalDateTime.now().plusHours(3));
        dto.setRegistrationTime(ZonedDateTime.now().plusHours(4));
        dto.setNotificationGroupsChangeTime(ZonedDateTime.now().plusHours(7));
        dto.setAddBrandLikeNotificationTime(ZonedDateTime.now().plusHours(8));
        dto.setDeleteTime(ZonedDateTime.now().plusHours(9));

        //группы нотификаций
        Set<Long> modifiedNotificationGroups = new HashSet<>();
        modifiedNotificationGroups.add(2L);
        modifiedNotificationGroups.add(4L);
        modifiedNotificationGroups.add(5L);
        dto.setNotificationGroupIds(modifiedNotificationGroups);

        syncMessageDTO.setData(dto);

        syncMessageDTO.setCreatedAt(user.getChangeTime().plusHours(1).atOffset(
            OffsetDateTime.now().getOffset()));

        consumeUpdate(syncMessageDTO);

        commitAndStartNewTransaction();

        user = userRepository.findById(TEST_USER_ID).get();

        assertEquals(dto.getUid(), user.getUid());
        assertEquals(dto.getEmail(), user.getEmail());
        assertEquals(dto.getIsTrusted(), user.getIsTrusted());
        assertEquals(dto.getNickname(), user.getNickname());
        assertEquals(dto.getPhone(), user.getPhone());
        assertEquals(dto.getApiHashedPassword(), user.getApiHashedPassword());
        assertEquals(dto.getPhoneVerifiedTime(), user.getPhoneVerifiedTime());
        assertEquals(dto.getHashedPassword(), user.getHashedPassword());
        assertEquals(dto.getBirthDate(), user.getBirthDate());
        assertEquals(dto.getSex(), user.getSex());
        assertEquals(dto.getActivationTime(), user.getActivationTime());
        assertEquals(dto.getRegistrationTime(), user.getRegistrationTime());
        assertEquals(dto.getUserAppleUuid(), user.getUserAppleUuid());
        assertEquals(dto.getUserVkUuid(), user.getUserVkUuid());
        assertEquals(dto.getUserFbUuid(), user.getUserFbUuid());
        assertEquals(dto.getGoogleAccountId(), user.getGoogleAccountId());
        assertEquals(dto.getYandexAccountId(), user.getYandexAccountId());
        assertEquals(dto.getActivationToken(), user.getActivationToken());
        assertEquals(dto.getRestFacebookId(), user.getRestFacebookId());
        assertEquals(dto.getRestVkId(), user.getRestVkId());
        assertEquals(dto.getNotificationGroupsChangeTime(), user.getNotificationGroupsChangeTime());
        assertEquals(dto.getAddBrandLikeNotificationTime(), user.getAddBrandLikeNotificationTime());
        assertEquals(dto.getRestAppleId(), user.getRestAppleId());
        assertEquals(dto.getDeleteTime(), user.getDeleteTime());
        assertEquals(dto.getGoogleIdToken(), user.getGoogleIdToken());
        assertEquals(dto.getYandexIdToken(), user.getYandexIdToken());
        assertEquals(ProductionEnvironment.RU, user.getEnvironment());

        assertTrue(changeTimeBefore.isBefore(user.getChangeTime()));

        assertEquals(modifiedNotificationGroups,
            notificationGroupUserBindingRepository.findAllGroupIdsByUser(user.getId()));
    }

    @Test
    @Transactional
    public void testReceiveGoodMessageNotFirstSyncUpdate3() {
        UserSyncMessageDTO syncMessageDTO = new UserSyncMessageDTO();
        syncMessageDTO.setSourceUserZone(ProductionEnvironment.INT);

        User user = userRepository.findById(TEST_USER_ID).get();
        user.setUserType(User.UserType.IP);
        user.setSellerType(SellerType.BOUTIQUE);
        user.setYandexIdToken("some yandex token " + UUID.randomUUID());
        user.setEnvironment(ProductionEnvironment.INT);
        userRepository.save(user);

        //чистим notification groups
        notificationGroupUserBindingRepository.deleteAllByUserId(user.getId());
        commitAndStartNewTransaction();

        //добавляем тестовые связи
        Set<Long> notificationGroups = new HashSet<>();
        notificationGroups.add(1L);
        notificationGroups.add(3L);
        notificationGroups.add(5L);
        notificationGroupUserBindingRepository.insertNewBindings(user.getId(), notificationGroups);
        commitAndStartNewTransaction();

        //время обновления пользователя "до"
        LocalDateTime changeTimeBefore = user.getChangeTime();

        //заполняем dto
        UserSyncDataDTO dto = userSyncMapper.toDTO(user);

        // Заполнение строковых полей с модификацией
        dto.setUid(user.getUid());  // UUID оставляем как есть
        dto.setEmail(user.getEmail() + "_modified email");
        dto.setIsTrusted(user.getIsTrusted() == null || !user.getIsTrusted());
        dto.setNickname(user.getNickname() + "_modified nickname");
        dto.setPhone(randomizePhone(user.getPhone()));
        dto.setApiHashedPassword(user.getApiHashedPassword() + "_modified apiHashedPassword");
        dto.setHashedPassword(user.getHashedPassword() + "_modified hashedPassword");
        dto.setActivationToken(user.getActivationToken() + "_modified activationToken");
        dto.setRestFacebookId(user.getRestFacebookId() + "_modified restFacebookId");
        dto.setRestVkId(user.getRestVkId() + "_modified restVkId");
        dto.setRestAppleId(user.getRestAppleId() + "_modified restAppleId");
        dto.setGoogleAccountId(user.getGoogleAccountId() + "_modified googleAccountId");
        dto.setYandexAccountId(user.getYandexAccountId() + "_modified yandexAccountId");
        dto.setGoogleIdToken(user.getGoogleIdToken() + "_modified googleIdToken");
        dto.setYandexIdToken(null);

        // Увеличение временных полей
        dto.setPhoneVerifiedTime(ZonedDateTime.now().plusHours(1));
        dto.setBirthDate(LocalDateTime.now().plusHours(2));
        dto.setActivationTime(LocalDateTime.now().plusHours(3));
        dto.setRegistrationTime(ZonedDateTime.now().plusHours(4));
        dto.setNotificationGroupsChangeTime(ZonedDateTime.now().plusHours(7));
        dto.setAddBrandLikeNotificationTime(ZonedDateTime.now().plusHours(8));
        dto.setDeleteTime(ZonedDateTime.now().plusHours(9));

        //группы нотификаций
        Set<Long> modifiedNotificationGroups = new HashSet<>();
        modifiedNotificationGroups.add(2L);
        modifiedNotificationGroups.add(4L);
        modifiedNotificationGroups.add(5L);
        dto.setNotificationGroupIds(modifiedNotificationGroups);

        syncMessageDTO.setData(dto);

        syncMessageDTO.setCreatedAt(user.getChangeTime().plusHours(1).atOffset(
            OffsetDateTime.now().getOffset()));

        consumeUpdate(syncMessageDTO);

        commitAndStartNewTransaction();

        user = userRepository.findById(TEST_USER_ID).get();

        assertEquals(dto.getUid(), user.getUid());
        assertEquals(dto.getEmail(), user.getEmail());
        assertEquals(dto.getIsTrusted(), user.getIsTrusted());
        assertEquals(dto.getNickname(), user.getNickname());
        assertEquals(dto.getPhone(), user.getPhone());
        assertEquals(dto.getApiHashedPassword(), user.getApiHashedPassword());
        assertEquals(dto.getPhoneVerifiedTime(), user.getPhoneVerifiedTime());
        assertEquals(dto.getHashedPassword(), user.getHashedPassword());
        assertEquals(dto.getBirthDate(), user.getBirthDate());
        assertEquals(dto.getSex(), user.getSex());
        assertEquals(dto.getActivationTime(), user.getActivationTime());
        assertEquals(dto.getRegistrationTime(), user.getRegistrationTime());
        assertEquals(dto.getUserAppleUuid(), user.getUserAppleUuid());
        assertEquals(dto.getUserVkUuid(), user.getUserVkUuid());
        assertEquals(dto.getUserFbUuid(), user.getUserFbUuid());
        assertEquals(dto.getGoogleAccountId(), user.getGoogleAccountId());
        assertEquals(dto.getYandexAccountId(), user.getYandexAccountId());
        assertEquals(dto.getActivationToken(), user.getActivationToken());
        assertEquals(dto.getRestFacebookId(), user.getRestFacebookId());
        assertEquals(dto.getRestVkId(), user.getRestVkId());
        assertEquals(dto.getNotificationGroupsChangeTime(), user.getNotificationGroupsChangeTime());
        assertEquals(dto.getAddBrandLikeNotificationTime(), user.getAddBrandLikeNotificationTime());
        assertEquals(dto.getRestAppleId(), user.getRestAppleId());
        assertEquals(dto.getDeleteTime(), user.getDeleteTime());
        assertEquals(dto.getGoogleIdToken(), user.getGoogleIdToken());
        assertEquals(dto.getYandexIdToken(), user.getYandexIdToken());
        assertEquals(ProductionEnvironment.INT, user.getEnvironment());

        assertTrue(changeTimeBefore.isBefore(user.getChangeTime()));

        assertEquals(modifiedNotificationGroups,
            notificationGroupUserBindingRepository.findAllGroupIdsByUser(user.getId()));
    }

    //прием "нормального" "свежего" сообщения. Создание нового пользователя
    @Test
    @Transactional
    public void testReceiveGoodMessagePersist() {
        //сообщение из той же zone
        UserSyncMessageDTO syncMessageDTO = new UserSyncMessageDTO();
        syncMessageDTO.setSourceUserZone(ProductionEnvironment.INT);

        //заполняем dto
        UserSyncDataDTO dto = new UserSyncDataDTO();

        // Заполнение строковых полей с модификацией
        dto.setUid(UUID.randomUUID());
        dto.setEmail("_modified_persist email" + dto.getUid().toString());
        dto.setIsTrusted(true);
        dto.setNickname("_modified_persist nickname " + dto.getUid().toString());
        dto.setPhone(randomizePhone("79171000000"));
        dto.setApiHashedPassword("_modified_persist apiHashedPassword");
        dto.setHashedPassword("_modified_persist hashedPassword");
        dto.setActivationToken("_modified_persist activationToken" + dto.getUid().toString());
        dto.setRestFacebookId("_modified_persist restFacebookId" + dto.getUid().toString());
        dto.setRestVkId("_modified_persist restVkId" + dto.getUid().toString());
        dto.setRestAppleId("_modified_persist restAppleId" + dto.getUid().toString());
        dto.setGoogleAccountId("_modified_persist googleAccountId" + dto.getUid().toString());
        dto.setYandexAccountId("_modified_persist yandexAccountId" + dto.getUid().toString());
        dto.setGoogleIdToken("_modified_persist googleIdToken" + dto.getUid().toString());
        dto.setYandexIdToken("_modified_persist yandexIdToken" + dto.getUid().toString());
        dto.setEnvironment(ProductionEnvironment.INT);

        // Увеличение временных полей
        dto.setPhoneVerifiedTime(ZonedDateTime.now().plusHours(1));
        dto.setBirthDate(LocalDateTime.now().plusHours(2));
        dto.setActivationTime(LocalDateTime.now().plusHours(3));
        dto.setRegistrationTime(ZonedDateTime.now().plusHours(4));
        dto.setNotificationGroupsChangeTime(ZonedDateTime.now().plusHours(7));
        dto.setAddBrandLikeNotificationTime(ZonedDateTime.now().plusHours(8));
//        dto.setDeleteTime(ZonedDateTime.now().plusHours(9));

        //группы нотификаций
        Set<Long> modifiedNotificationGroups = new HashSet<>();
        modifiedNotificationGroups.add(2L);
        modifiedNotificationGroups.add(4L);
        modifiedNotificationGroups.add(5L);
        dto.setNotificationGroupIds(modifiedNotificationGroups);

        syncMessageDTO.setData(dto);

        syncMessageDTO.setCreatedAt(OffsetDateTime.now());

        consumeUpdate(syncMessageDTO);

        commitAndStartNewTransaction();

        User user = userRepository.findByUid(dto.getUid());

        assertEquals(dto.getUid(), user.getUid());
        assertEquals(dto.getEmail(), user.getEmail());
        assertEquals(dto.getIsTrusted(), user.getIsTrusted());
        assertEquals(dto.getNickname(), user.getNickname());
        assertEquals(dto.getPhone(), user.getPhone());
        assertEquals(dto.getApiHashedPassword(), user.getApiHashedPassword());
        assertEquals(dto.getPhoneVerifiedTime(), user.getPhoneVerifiedTime());
        assertEquals(dto.getHashedPassword(), user.getHashedPassword());
        assertEquals(dto.getBirthDate(), user.getBirthDate());
        assertEquals(dto.getSex(), user.getSex());
        assertEquals(dto.getActivationTime(), user.getActivationTime());
        assertEquals(dto.getRegistrationTime(), user.getRegistrationTime());
        assertEquals(dto.getUserAppleUuid(), user.getUserAppleUuid());
        assertEquals(dto.getUserVkUuid(), user.getUserVkUuid());
        assertEquals(dto.getUserFbUuid(), user.getUserFbUuid());
        assertEquals(dto.getGoogleAccountId(), user.getGoogleAccountId());
        assertEquals(dto.getYandexAccountId(), user.getYandexAccountId());
        assertEquals(dto.getActivationToken(), user.getActivationToken());
        assertEquals(dto.getRestFacebookId(), user.getRestFacebookId());
        assertEquals(dto.getRestVkId(), user.getRestVkId());
        assertEquals(dto.getNotificationGroupsChangeTime(), user.getNotificationGroupsChangeTime());
        assertEquals(dto.getAddBrandLikeNotificationTime(), user.getAddBrandLikeNotificationTime());
        assertEquals(dto.getRestAppleId(), user.getRestAppleId());
        assertEquals(dto.getDeleteTime(), user.getDeleteTime());
        assertEquals(dto.getGoogleIdToken(), user.getGoogleIdToken());
        assertEquals(dto.getYandexIdToken(), user.getYandexIdToken());
        assertEquals(dto.getEnvironment(), user.getEnvironment());

        assertEquals(modifiedNotificationGroups,
            notificationGroupUserBindingRepository.findAllGroupIdsByUser(user.getId()));
    }

    //прием "нормального" "свежего" сообщения. Первая синхронизация - несовпадение uid
    @Test
    @Transactional
    public void testReceiveGoodMessageFirstSyncUpdate1() {
        UserSyncMessageDTO syncMessageDTO = new UserSyncMessageDTO();
        syncMessageDTO.setSourceUserZone(ProductionEnvironment.INT);

        User user = userRepository.findById(TEST_USER_ID).get();

        //в dto эти поля будут null
        user.setActivationTime(LocalDateTime.now());
        user.setActivationToken("some token");
        user.setAddBrandLikeNotificationTime(ZonedDateTime.now());
        user.setDeleteTime(null);
        user.setIsTrusted(true);
        user.setHashedPassword("somePass" + UUID.randomUUID());
        user.setPhoneVerifiedTime(ZonedDateTime.now());
        user.setUserType(User.UserType.SIMPLE_USER);
        user.setSellerType(SellerType.BOUTIQUE);

        userRepository.save(user);

        //чистим notification groups
        notificationGroupUserBindingRepository.deleteAllByUserId(user.getId());
        commitAndStartNewTransaction();

        //добавляем тестовые связи
        Set<Long> notificationGroups = new HashSet<>();
        notificationGroups.add(1L);
        notificationGroups.add(3L);
        notificationGroups.add(5L);
        notificationGroupUserBindingRepository.insertNewBindings(user.getId(), notificationGroups);
        commitAndStartNewTransaction();

        //заполняем dto
        UserSyncDataDTO dto = userSyncMapper.toDTO(user);

        // Заполнение строковых полей с модификацией
        //uuid должен отличаться от пользовательского
        dto.setUid(UUID.randomUUID());

        dto.setActivationToken(null);
        dto.setActivationTime(null);
        dto.setAddBrandLikeNotificationTime(null);
        dto.setDeleteTime(ZonedDateTime.now());
        dto.setIsTrusted(false);
        dto.setPhoneVerifiedTime(ZonedDateTime.now());

        //группы нотификаций
        Set<Long> modifiedNotificationGroups = new HashSet<>();
        modifiedNotificationGroups.add(2L);
        modifiedNotificationGroups.add(4L);
        modifiedNotificationGroups.add(5L);
        dto.setNotificationGroupIds(modifiedNotificationGroups);

        syncMessageDTO.setData(dto);

        syncMessageDTO.setCreatedAt(user.getChangeTime().plusHours(1).atOffset(
            OffsetDateTime.now().getOffset()));

        consumeUpdate(syncMessageDTO);

        commitAndStartNewTransaction();

        user = userRepository.findById(TEST_USER_ID).get();

        assertNotNull(user.getActivationTime());
        assertNotNull(user.getActivationToken());
        assertNotNull(user.getAddBrandLikeNotificationTime());
        assertNull(user.getDeleteTime());
        assertTrue(user.getIsTrusted());
        assertNotNull(user.getPhoneVerifiedTime());

        assertEquals(dto.getHashedPassword(), user.getHashedPassword());
        assertEquals(dto.getUid(), user.getUid());
        assertEquals(dto.getEmail(), user.getEmail());
        assertEquals(dto.getNickname(), user.getNickname());
        assertEquals(dto.getPhone(), user.getPhone());
        assertEquals(dto.getApiHashedPassword(), user.getApiHashedPassword());
        assertEquals(dto.getBirthDate(), user.getBirthDate());
        assertEquals(dto.getSex(), user.getSex());
        assertEquals(dto.getRegistrationTime(), user.getRegistrationTime());
        assertEquals(dto.getUserAppleUuid(), user.getUserAppleUuid());
        assertEquals(dto.getUserVkUuid(), user.getUserVkUuid());
        assertEquals(dto.getUserFbUuid(), user.getUserFbUuid());
        assertEquals(dto.getGoogleAccountId(), user.getGoogleAccountId());
        assertEquals(dto.getYandexAccountId(), user.getYandexAccountId());
        assertEquals(dto.getRestFacebookId(), user.getRestFacebookId());
        assertEquals(dto.getRestVkId(), user.getRestVkId());
        assertEquals(dto.getNotificationGroupsChangeTime(), user.getNotificationGroupsChangeTime());
        assertEquals(dto.getRestAppleId(), user.getRestAppleId());
        assertEquals(dto.getGoogleIdToken(), user.getGoogleIdToken());
        assertEquals(dto.getYandexIdToken(), user.getYandexIdToken());

        //группы нотификаций должны доплниться
        modifiedNotificationGroups.addAll(notificationGroups);

        assertEquals(modifiedNotificationGroups,
            notificationGroupUserBindingRepository.findAllGroupIdsByUser(user.getId()));
    }

    @Test
    @Transactional
    public void testReceiveGoodMessageFirstSyncUpdate2() {
        UserSyncMessageDTO syncMessageDTO = new UserSyncMessageDTO();
        syncMessageDTO.setSourceUserZone(ProductionEnvironment.INT);

        User user = userRepository.findById(TEST_USER_ID).get();

        //в dto эти поля будут null
        user.setActivationTime(LocalDateTime.now());
        user.setActivationToken("some token");
        user.setAddBrandLikeNotificationTime(ZonedDateTime.now());
        user.setDeleteTime(null);
        user.setIsTrusted(true);
        user.setHashedPassword("somePass" + UUID.randomUUID());
        user.setPhoneVerifiedTime(ZonedDateTime.now());
        user.setUserType(User.UserType.SIMPLE_USER);
        user.setSellerType(SellerType.BOUTIQUE);

        userRepository.save(user);

        //чистим notification groups
        notificationGroupUserBindingRepository.deleteAllByUserId(user.getId());
        commitAndStartNewTransaction();

        //добавляем тестовые связи
        Set<Long> notificationGroups = new HashSet<>();
        notificationGroups.add(1L);
        notificationGroups.add(3L);
        notificationGroups.add(5L);
        notificationGroupUserBindingRepository.insertNewBindings(user.getId(), notificationGroups);
        commitAndStartNewTransaction();

        //заполняем dto
        UserSyncDataDTO dto = userSyncMapper.toDTO(user);

        // Заполнение строковых полей с модификацией
        //uuid должен отличаться от пользовательского
        dto.setUid(UUID.randomUUID());

        dto.setActivationToken(null);
        dto.setActivationTime(null);
        dto.setAddBrandLikeNotificationTime(null);
        dto.setDeleteTime(ZonedDateTime.now());
        dto.setIsTrusted(false);
        dto.setPhoneVerifiedTime(ZonedDateTime.now());

        // В этом тесте проверяем, что если совпал подтверждённый номер телефона, но
        // не совпал пароль – тогда всё равно связываем пользователей, т. к. телефон важнее
        dto.setApiHashedPassword("somePass" + UUID.randomUUID());

        //группы нотификаций
        Set<Long> modifiedNotificationGroups = new HashSet<>();
        modifiedNotificationGroups.add(2L);
        modifiedNotificationGroups.add(4L);
        modifiedNotificationGroups.add(5L);
        dto.setNotificationGroupIds(modifiedNotificationGroups);

        syncMessageDTO.setData(dto);

        syncMessageDTO.setCreatedAt(user.getChangeTime().plusHours(1).atOffset(
            OffsetDateTime.now().getOffset()));

        consumeUpdate(syncMessageDTO);

        commitAndStartNewTransaction();

        user = userRepository.findById(TEST_USER_ID).get();

        assertNotNull(user.getActivationTime());
        assertNotNull(user.getActivationToken());
        assertNotNull(user.getAddBrandLikeNotificationTime());
        assertNull(user.getDeleteTime());
        assertTrue(user.getIsTrusted());
        assertNotNull(user.getPhoneVerifiedTime());

        assertEquals(dto.getHashedPassword(), user.getHashedPassword());
        assertEquals(dto.getUid(), user.getUid());
        assertEquals(dto.getEmail(), user.getEmail());
        assertEquals(dto.getNickname(), user.getNickname());
        assertEquals(dto.getPhone(), user.getPhone());
        assertEquals(dto.getApiHashedPassword(), user.getApiHashedPassword());
        assertEquals(dto.getBirthDate(), user.getBirthDate());
        assertEquals(dto.getSex(), user.getSex());
        assertEquals(dto.getRegistrationTime(), user.getRegistrationTime());
        assertEquals(dto.getUserAppleUuid(), user.getUserAppleUuid());
        assertEquals(dto.getUserVkUuid(), user.getUserVkUuid());
        assertEquals(dto.getUserFbUuid(), user.getUserFbUuid());
        assertEquals(dto.getGoogleAccountId(), user.getGoogleAccountId());
        assertEquals(dto.getYandexAccountId(), user.getYandexAccountId());
        assertEquals(dto.getRestFacebookId(), user.getRestFacebookId());
        assertEquals(dto.getRestVkId(), user.getRestVkId());
        assertEquals(dto.getNotificationGroupsChangeTime(), user.getNotificationGroupsChangeTime());
        assertEquals(dto.getRestAppleId(), user.getRestAppleId());
        assertEquals(dto.getGoogleIdToken(), user.getGoogleIdToken());
        assertEquals(dto.getYandexIdToken(), user.getYandexIdToken());

        //группы нотификаций должны доплниться
        modifiedNotificationGroups.addAll(notificationGroups);

        assertEquals(modifiedNotificationGroups,
            notificationGroupUserBindingRepository.findAllGroupIdsByUser(user.getId()));
    }

    @Test
    @Transactional
    public void testReceiveGoodMessageFirstSyncUpdate3() {
        UserSyncMessageDTO syncMessageDTO = new UserSyncMessageDTO();
        syncMessageDTO.setSourceUserZone(ProductionEnvironment.INT);

        User user = userRepository.findById(TEST_USER_ID).get();

        //в dto эти поля будут null
        user.setActivationTime(LocalDateTime.now());
        user.setActivationToken("some token");
        user.setAddBrandLikeNotificationTime(ZonedDateTime.now());
        user.setDeleteTime(null);
        user.setIsTrusted(true);
        user.setHashedPassword("somePass" + UUID.randomUUID());
        user.setPhoneVerifiedTime(ZonedDateTime.now());
        user.setUserType(User.UserType.SIMPLE_USER);
        user.setSellerType(SellerType.BOUTIQUE);

        userRepository.save(user);

        //чистим notification groups
        notificationGroupUserBindingRepository.deleteAllByUserId(user.getId());
        commitAndStartNewTransaction();

        //добавляем тестовые связи
        Set<Long> notificationGroups = new HashSet<>();
        notificationGroups.add(1L);
        notificationGroups.add(3L);
        notificationGroups.add(5L);
        notificationGroupUserBindingRepository.insertNewBindings(user.getId(), notificationGroups);
        commitAndStartNewTransaction();

        //заполняем dto
        UserSyncDataDTO dto = userSyncMapper.toDTO(user);

        // Заполнение строковых полей с модификацией
        //uuid должен отличаться от пользовательского
        dto.setUid(UUID.randomUUID());

        dto.setActivationToken(null);
        dto.setActivationTime(null);
        dto.setAddBrandLikeNotificationTime(null);
        dto.setDeleteTime(ZonedDateTime.now());
        dto.setIsTrusted(false);
        dto.setPhoneVerifiedTime(ZonedDateTime.now());

        // В этом тесте проверяем, что если совпал подтверждённый номер телефона, но
        // не совпал apple id – тогда связи произойти не должно
        dto.setRestAppleId("somePass" + UUID.randomUUID());

        //группы нотификаций
        Set<Long> modifiedNotificationGroups = new HashSet<>();
        modifiedNotificationGroups.add(2L);
        modifiedNotificationGroups.add(4L);
        modifiedNotificationGroups.add(5L);
        dto.setNotificationGroupIds(modifiedNotificationGroups);

        syncMessageDTO.setData(dto);

        syncMessageDTO.setCreatedAt(user.getChangeTime().plusHours(1).atOffset(
            OffsetDateTime.now().getOffset()));

        assertThrows(UserWithSameEmailExistsException.class, () -> consumeUpdate(syncMessageDTO));
    }

    @Test
    @Transactional
    public void testReceiveGoodMessageFirstSyncUpdate4() {
        UserSyncMessageDTO syncMessageDTO = new UserSyncMessageDTO();
        syncMessageDTO.setSourceUserZone(ProductionEnvironment.INT);

        User user = userRepository.findById(TEST_USER_ID).get();

        //в dto эти поля будут null
        user.setActivationTime(LocalDateTime.now());
        user.setActivationToken("some token");
        user.setAddBrandLikeNotificationTime(ZonedDateTime.now());
        user.setDeleteTime(null);
        user.setIsTrusted(true);
        user.setHashedPassword("somePass" + UUID.randomUUID());
        user.setPhoneVerifiedTime(ZonedDateTime.now());
        user.setUserType(User.UserType.SIMPLE_USER);
        user.setSellerType(SellerType.BOUTIQUE);

        userRepository.save(user);

        //чистим notification groups
        notificationGroupUserBindingRepository.deleteAllByUserId(user.getId());
        commitAndStartNewTransaction();

        //добавляем тестовые связи
        Set<Long> notificationGroups = new HashSet<>();
        notificationGroups.add(1L);
        notificationGroups.add(3L);
        notificationGroups.add(5L);
        notificationGroupUserBindingRepository.insertNewBindings(user.getId(), notificationGroups);
        commitAndStartNewTransaction();

        //заполняем dto
        UserSyncDataDTO dto = userSyncMapper.toDTO(user);

        // Заполнение строковых полей с модификацией
        //uuid должен отличаться от пользовательского
        dto.setUid(UUID.randomUUID());

        dto.setActivationToken(null);
        dto.setActivationTime(null);
        dto.setAddBrandLikeNotificationTime(null);
        dto.setDeleteTime(ZonedDateTime.now());
        dto.setIsTrusted(false);

        // В этом тесте проверяем, что если у пользователя подтверждён номер телефона, а dto – нет,
        // тогда ничего не выйдет
        dto.setPhoneVerifiedTime(null);

        //группы нотификаций
        Set<Long> modifiedNotificationGroups = new HashSet<>();
        modifiedNotificationGroups.add(2L);
        modifiedNotificationGroups.add(4L);
        modifiedNotificationGroups.add(5L);
        dto.setNotificationGroupIds(modifiedNotificationGroups);

        syncMessageDTO.setData(dto);

        syncMessageDTO.setCreatedAt(user.getChangeTime().plusHours(1).atOffset(
            OffsetDateTime.now().getOffset()));

        assertThrows(UserWithSameEmailExistsException.class, () -> consumeUpdate(syncMessageDTO));
    }

    @Test
    @Transactional
    public void testReceiveGoodMessageFirstSyncUpdate5() {
        UserSyncMessageDTO syncMessageDTO = new UserSyncMessageDTO();
        syncMessageDTO.setSourceUserZone(ProductionEnvironment.INT);

        User user = userRepository.findById(TEST_USER_ID).get();

        //в dto эти поля будут null
        user.setActivationTime(LocalDateTime.now());
        user.setActivationToken("some token");
        user.setAddBrandLikeNotificationTime(ZonedDateTime.now());
        user.setIsTrusted(true);
        user.setHashedPassword("somePass" + UUID.randomUUID());
        user.setPhoneVerifiedTime(null);
        user.setUserType(User.UserType.SIMPLE_USER);
        user.setSellerType(SellerType.BOUTIQUE);

        userRepository.save(user);

        //чистим notification groups
        notificationGroupUserBindingRepository.deleteAllByUserId(user.getId());
        commitAndStartNewTransaction();

        //добавляем тестовые связи
        Set<Long> notificationGroups = new HashSet<>();
        notificationGroups.add(1L);
        notificationGroups.add(3L);
        notificationGroups.add(5L);
        notificationGroupUserBindingRepository.insertNewBindings(user.getId(), notificationGroups);
        commitAndStartNewTransaction();

        //заполняем dto
        UserSyncDataDTO dto = userSyncMapper.toDTO(user);

        // Заполнение строковых полей с модификацией
        //uuid должен отличаться от пользовательского
        dto.setUid(UUID.randomUUID());

        dto.setActivationToken(null);
        dto.setActivationTime(null);
        dto.setAddBrandLikeNotificationTime(null);
        dto.setIsTrusted(false);
        dto.setPhoneVerifiedTime(null);

        //группы нотификаций
        Set<Long> modifiedNotificationGroups = new HashSet<>();
        modifiedNotificationGroups.add(2L);
        modifiedNotificationGroups.add(4L);
        modifiedNotificationGroups.add(5L);
        dto.setNotificationGroupIds(modifiedNotificationGroups);

        syncMessageDTO.setData(dto);

        syncMessageDTO.setCreatedAt(user.getChangeTime().plusHours(1).atOffset(
            OffsetDateTime.now().getOffset()));

        consumeUpdate(syncMessageDTO);

        commitAndStartNewTransaction();

        user = userRepository.findById(TEST_USER_ID).get();

        assertNotNull(user.getActivationTime());
        assertNotNull(user.getActivationToken());
        assertNotNull(user.getAddBrandLikeNotificationTime());
        assertNull(user.getDeleteTime());
        assertTrue(user.getIsTrusted());
        assertNull(user.getPhoneVerifiedTime());

        assertEquals(dto.getHashedPassword(), user.getHashedPassword());
        assertEquals(dto.getUid(), user.getUid());
        assertEquals(dto.getEmail(), user.getEmail());
        assertEquals(dto.getNickname(), user.getNickname());
        assertEquals(dto.getPhone(), user.getPhone());
        assertEquals(dto.getApiHashedPassword(), user.getApiHashedPassword());
        assertEquals(dto.getBirthDate(), user.getBirthDate());
        assertEquals(dto.getSex(), user.getSex());
        assertEquals(dto.getRegistrationTime(), user.getRegistrationTime());
        assertEquals(dto.getUserAppleUuid(), user.getUserAppleUuid());
        assertEquals(dto.getUserVkUuid(), user.getUserVkUuid());
        assertEquals(dto.getUserFbUuid(), user.getUserFbUuid());
        assertEquals(dto.getGoogleAccountId(), user.getGoogleAccountId());
        assertEquals(dto.getYandexAccountId(), user.getYandexAccountId());
        assertEquals(dto.getRestFacebookId(), user.getRestFacebookId());
        assertEquals(dto.getRestVkId(), user.getRestVkId());
        assertEquals(dto.getNotificationGroupsChangeTime(), user.getNotificationGroupsChangeTime());
        assertEquals(dto.getRestAppleId(), user.getRestAppleId());
        assertEquals(dto.getGoogleIdToken(), user.getGoogleIdToken());
        assertEquals(dto.getYandexIdToken(), user.getYandexIdToken());

        //группы нотификаций должны доплниться
        modifiedNotificationGroups.addAll(notificationGroups);

        assertEquals(modifiedNotificationGroups,
            notificationGroupUserBindingRepository.findAllGroupIdsByUser(user.getId()));
    }

    @Test
    @Transactional
    public void testReceiveGoodMessageFirstSyncUpdate6() {
        UserSyncMessageDTO syncMessageDTO = new UserSyncMessageDTO();
        syncMessageDTO.setSourceUserZone(ProductionEnvironment.INT);

        User user = userRepository.findById(TEST_USER_ID).get();

        //в dto эти поля будут null
        user.setActivationTime(LocalDateTime.now());
        user.setActivationToken("some token");
        user.setAddBrandLikeNotificationTime(ZonedDateTime.now());
        user.setDeleteTime(ZonedDateTime.now());
        user.setIsTrusted(true);
        user.setHashedPassword("somePass" + UUID.randomUUID());
        user.setPhoneVerifiedTime(ZonedDateTime.now());
        user.setUserType(User.UserType.SIMPLE_USER);
        user.setSellerType(SellerType.BOUTIQUE);

        userRepository.save(user);

        //чистим notification groups
        notificationGroupUserBindingRepository.deleteAllByUserId(user.getId());
        commitAndStartNewTransaction();

        //добавляем тестовые связи
        Set<Long> notificationGroups = new HashSet<>();
        notificationGroups.add(1L);
        notificationGroups.add(3L);
        notificationGroups.add(5L);
        notificationGroupUserBindingRepository.insertNewBindings(user.getId(), notificationGroups);
        commitAndStartNewTransaction();

        //заполняем dto
        UserSyncDataDTO dto = userSyncMapper.toDTO(user);

        // Заполнение строковых полей с модификацией
        //uuid должен отличаться от пользовательского
        dto.setUid(UUID.randomUUID());

        dto.setActivationToken(null);
        dto.setActivationTime(null);
        dto.setAddBrandLikeNotificationTime(null);
        dto.setDeleteTime(null);
        dto.setIsTrusted(false);
        dto.setPhoneVerifiedTime(ZonedDateTime.now());

        //группы нотификаций
        Set<Long> modifiedNotificationGroups = new HashSet<>();
        modifiedNotificationGroups.add(2L);
        modifiedNotificationGroups.add(4L);
        modifiedNotificationGroups.add(5L);
        dto.setNotificationGroupIds(modifiedNotificationGroups);

        syncMessageDTO.setData(dto);

        syncMessageDTO.setCreatedAt(user.getChangeTime().plusHours(1).atOffset(
            OffsetDateTime.now().getOffset()));

        assertThrows(UserForSyncIsDeletedException.class, () -> consumeUpdate(syncMessageDTO));
    }

    @Test
    @Transactional
    public void directSyncTest() {
        User user = userRepository.findById(TEST_USER_ID).get();
        user.setStateForSyncSent(true);
        user.setNoSyncNickTime(null);

        UserSyncMessageDTO syncMessageDTO = new UserSyncMessageDTO();
        syncMessageDTO.setSourceUserZone(ProductionEnvironment.INT);

        UserSyncDataDTO dto = userSyncMapper.toDTO(user);

        syncMessageDTO.setData(dto);
        syncMessageDTO.setDirect(true);
        syncMessageDTO.setCreatedAt(OffsetDateTime.now());

        String newPhone = randomizePhone(user.getPhone());
        dto.setPhone(newPhone);

        //"Нормальный" пользователь - синхронизация должна пройти без ошибок
        consumeUpdate(syncMessageDTO);
        commitAndStartNewTransaction();

        user = userRepository.findById(TEST_USER_ID).get();
        assertEquals(newPhone, user.getPhone());

        //пользователь, ник которого существует на принимающей стороне
        User userWithSameNick = userRepository.findById(11L).get();
        dto.setNickname(userWithSameNick.getNickname());
        syncMessageDTO.setCreatedAt(OffsetDateTime.now());

        String oldNick = user.getNickname();

        consumeUpdate(syncMessageDTO);
        commitAndStartNewTransaction();

        //ник не должен измениться и должна поставиться галочка "не синкаем ник"
        user = userRepository.findById(TEST_USER_ID).get();
        assertEquals(oldNick, user.getNickname());
        assertNotNull(user.getNoSyncNickTime());

        //новый пользователь
        UserSyncDataDTO dtoNewUser = new UserSyncDataDTO();

        //Заполнение строковых полей с модификацией
        dtoNewUser.setUid(UUID.randomUUID());
        dtoNewUser.setEmail("_modified_persist email" + dto.getUid().toString());
        dtoNewUser.setIsTrusted(true);
        dtoNewUser.setNickname(oldNick);
        dtoNewUser.setPhone(randomizePhone("79172000000"));
        dtoNewUser.setApiHashedPassword("_modified_persist apiHashedPassword" + dto.getUid().toString());
        dtoNewUser.setHashedPassword("_modified_persist hashedPassword" + dto.getUid().toString());
        dtoNewUser.setActivationToken("_modified_persist activationToken" + dto.getUid().toString());
        dtoNewUser.setRestFacebookId("_modified_persist restFacebookId" + dto.getUid().toString());
        dtoNewUser.setRestVkId("_modified_persist restVkId" + dto.getUid().toString());
        dtoNewUser.setRestAppleId("_modified_persist restAppleId" + dto.getUid().toString());
        dtoNewUser.setGoogleAccountId("_modified_persist googleAccountId" + dto.getUid().toString());
        dtoNewUser.setYandexAccountId("_modified_persist yandexAccountId" + dto.getUid().toString());
        dtoNewUser.setGoogleIdToken("_modified_persist googleIdToken" + dto.getUid().toString());
        dtoNewUser.setYandexIdToken("_modified_persist yandexIdToken" + dto.getUid().toString());
        syncMessageDTO.setCreatedAt(OffsetDateTime.now());
        syncMessageDTO.setData(dtoNewUser);

        consumeUpdate(syncMessageDTO);
        commitAndStartNewTransaction();

        //ник не должен измениться и должна поставиться галочка "не синкаем ник"
        User newUser = userRepository.findByUid(dtoNewUser.getUid());
        String suffix = newUser.getNickname().substring(newUser.getNickname().length() - 3);

        assertTrue(suffix.startsWith("_"));
        Integer randInt = Integer.parseInt(suffix.substring(1));
        assertTrue(oldNick.startsWith(newUser.getNickname().substring(0, newUser.getNickname().length() - 3)));

        //пользователь с таким же email, но не совпадающими идентификационными данными
        syncMessageDTO.setCreatedAt(OffsetDateTime.now());
        syncMessageDTO.setData(dto);

        dto = userSyncMapper.toDTO(user);
        syncMessageDTO.setData(dto);

        dto.setUid(UUID.randomUUID());
        dto.setYandexIdToken("1111");

        try {
            consumeUpdate(syncMessageDTO);
            fail();
        } catch (Exception e) {
            assertInstanceOf(UserWithSameEmailExistsException.class, e);
        }

        //пользователь с отличающимся подтвержденным телефоном, но не совпадающими идентификационными данными
        user = userRepository.findById(TEST_USER_ID).get();
        user.setStateForSyncSent(true);

        user.setPhoneVerifiedTime(ZonedDateTime.now());
        user = userRepository.save(user);

        dto.setEmail("<EMAIL>");

        try {
            consumeUpdate(syncMessageDTO);
            fail();
        } catch (Exception e) {
            assertInstanceOf(UserWithSamePhoneExistsException.class, e);
        }
    }

    private String randomizePhone(String phone) {
        return String.valueOf(Long.parseLong(phone) + getRandomPlusOrMinusOne());
    }

    private int getRandomPlusOrMinusOne() {
        Random random = new Random();
        return random.nextInt(9999);
    }

    private void verifyUserAndSyncDataExceptPhone(User user, UserSyncDataDTO syncData) {
        //если в UserSyncDataDTO добавятся новые поля, тест должен падать
        assertEquals(31, UserSyncDataDTO.class.getDeclaredFields().length);

        assertEquals(user.getUid(), syncData.getUid());
        assertEquals(user.getEmail(), syncData.getEmail());
        assertEquals(user.getIsTrusted(), syncData.getIsTrusted());
        assertEquals(user.getNickname(), syncData.getNickname());
        assertEquals(user.getApiHashedPassword(), syncData.getApiHashedPassword());
        assertEquals(user.getPhoneVerifiedTime(), syncData.getPhoneVerifiedTime());
        assertEquals(user.getHashedPassword(), syncData.getHashedPassword());
        assertEquals(user.getBirthDate(), syncData.getBirthDate());
        assertEquals(user.getSex(), syncData.getSex());
        assertEquals(user.getActivationTime(), syncData.getActivationTime());
        assertEquals(user.getRegistrationTime(), syncData.getRegistrationTime());
        assertEquals(user.getUserAppleUuid(), syncData.getUserAppleUuid());
        assertEquals(user.getUserVkUuid(), syncData.getUserVkUuid());
        assertEquals(user.getUserFbUuid(), syncData.getUserFbUuid());
        assertEquals(user.getGoogleAccountId(), syncData.getGoogleAccountId());
        assertEquals(user.getYandexAccountId(), syncData.getYandexAccountId());
        assertEquals(user.getActivationToken(), syncData.getActivationToken());
        assertEquals(user.getRestFacebookId(), syncData.getRestFacebookId());
        assertEquals(user.getRestVkId(), syncData.getRestVkId());
        assertEquals(user.getNotificationGroupsChangeTime(), syncData.getNotificationGroupsChangeTime());
        assertEquals(user.getAddBrandLikeNotificationTime(), syncData.getAddBrandLikeNotificationTime());
        assertEquals(user.getRestAppleId(), syncData.getRestAppleId());
        assertEquals(user.getDeleteTime(), syncData.getDeleteTime());
        assertEquals(user.getGoogleIdToken(), syncData.getGoogleIdToken());

        Set<Long> notificationGroupIds = notificationGroupUserBindingRepository.findAllByUserId(user.getId()).
            stream()
            .map(NotificationGroupUserBinding::getNotificationGroupId)
            .collect(Collectors.toSet());

        if (notificationGroupIds.isEmpty()) {
            assertNull(syncData.getNotificationGroupIds());
        } else {
            assertEquals(notificationGroupIds, syncData.getNotificationGroupIds());
        }

        //phone
        assertNotNull(syncData.getPhone());
    }
}
