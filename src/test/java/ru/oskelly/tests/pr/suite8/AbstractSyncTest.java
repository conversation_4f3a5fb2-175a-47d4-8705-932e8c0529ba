package ru.oskelly.tests.pr.suite8;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;
import org.springframework.core.env.MutablePropertySources;
import ru.oskelly.tests.AbstractSpringTest;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.model.user.User;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public abstract class AbstractSyncTest extends AbstractSpringTest  {
    @Autowired
    protected UserRepository userRepository;
    @Autowired
    private ConfigurableEnvironment environment;

    protected void enableConsume() {
        changeProperty("user-sync-service.enabled.consume", "true");
    }

    protected void disableConsume() {
        changeProperty("user-sync-service.enabled.consume", "false");
    }

    protected void changeProperty(String key, String value) {
        MutablePropertySources propertySources = environment.getPropertySources();
        Map<String, Object> map = new HashMap<>();
        map.put(key, value);
        propertySources.addFirst(new MapPropertySource("dynamicProperties", map));
    }

    protected void initTestUser(Long userId) {
        User userToChange = userRepository.findById(userId).get();
        if (userToChange.getUid() == null) {
            userToChange.setUid(UUID.randomUUID());
        }

        userToChange.setDeleteTime(null);

        commitAndStartNewTransaction();
    }
}
