package ru.oskelly.tests.pr.suite8;

import org.junit.jupiter.api.Test;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.presentation.NicknameUtils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_08)
public class NicknameUtilTest {
    @Test
    public void nicknameForSyncTest() {
        int randomDigitsCount = 2;
        //пустой ник
        String existedNick = null;

        String nickForSync = NicknameUtils.generateNickForSync(existedNick, randomDigitsCount);

        assertEquals(randomDigitsCount + 1, nickForSync.length());
        assertTrue(nickForSync.startsWith("_"));

        Integer randNum = Integer.parseInt(nickForSync.substring(1));
        assertTrue(randNum.compareTo(0) >= 0);

        //"короткий" ник
        existedNick = "someNick";

        nickForSync = NicknameUtils.generateNickForSync(existedNick, randomDigitsCount);

        assertEquals(existedNick.length() + randomDigitsCount + 1, nickForSync.length());
        String suffix = nickForSync.substring(nickForSync.length() - randomDigitsCount - 1);
        assertTrue(suffix.startsWith("_"));

        randNum = Integer.parseInt(suffix.substring(1));
        assertTrue(randNum.compareTo(0) >= 0);

        //"длинный ник" ник
        existedNick = "someNicksomeNicksomeNicksomeNicksomeNicksomeNick";

        nickForSync = NicknameUtils.generateNickForSync(existedNick, randomDigitsCount);

        assertEquals(15, nickForSync.length());
        assertTrue(existedNick.startsWith(nickForSync.substring(0, nickForSync.length() - randomDigitsCount - 1)));
        suffix = nickForSync.substring(nickForSync.length() - randomDigitsCount - 1);
        assertTrue(suffix.startsWith("_"));

        randNum = Integer.parseInt(suffix.substring(1));
        assertTrue(randNum.compareTo(0) >= 0);
    }
}
