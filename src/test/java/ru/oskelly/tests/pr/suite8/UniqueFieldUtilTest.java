package ru.oskelly.tests.pr.suite8;

import org.junit.jupiter.api.Test;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.service.usersync.UniqueFieldChecker;
import su.reddot.domain.service.usersync.dto.UserSyncDataDTO;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_08)
public class UniqueFieldUtilTest {
    @Test
    public void uniqueFieldTest() {
        UserSyncDataDTO dto1 = new UserSyncDataDTO();
        UserSyncDataDTO dto2 = new UserSyncDataDTO();

        //уникальное поле не меняется
        dto1.setNickname("nick1");
        dto2.setNickname("nick2");

        assertFalse(UniqueFieldChecker.isUniqueFieldChanged(dto1, dto2));

        //уникальное поле меняется
        dto1.setEmail("email1");
        dto2.setNickname("email2");

        assertTrue(UniqueFieldChecker.isUniqueFieldChanged(dto1, dto2));
    }
}
