package ru.oskelly.tests.pr.suite8;

import org.junit.jupiter.api.Test;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.model.address.Country;
import su.reddot.domain.model.device.Device;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.usersync.service.DetermineCountryUtil;
import su.reddot.infrastructure.util.ProductionEnvironment;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_08)
public class DetermineCountryUtilTest {
    @Test
    public void testBestEnv() {
        assertEquals(ProductionEnvironment.RU,
                DetermineCountryUtil.bestEnvironment(null, null, false));

        assertEquals(ProductionEnvironment.INT,
                DetermineCountryUtil.bestEnvironment(null, null, true));

        assertEquals(ProductionEnvironment.RU,
                DetermineCountryUtil.bestEnvironment(ProductionEnvironment.RU, null, true));
        assertEquals(ProductionEnvironment.RU,
                DetermineCountryUtil.bestEnvironment(ProductionEnvironment.RU, null, false));

        assertEquals(ProductionEnvironment.RU,
                DetermineCountryUtil.bestEnvironment(null, ProductionEnvironment.RU, true));
        assertEquals(ProductionEnvironment.RU,
                DetermineCountryUtil.bestEnvironment(null, ProductionEnvironment.RU, false));

        assertEquals(ProductionEnvironment.RU,
                DetermineCountryUtil.bestEnvironment(ProductionEnvironment.RU, ProductionEnvironment.INT, true));
        assertEquals(ProductionEnvironment.RU,
                DetermineCountryUtil.bestEnvironment(ProductionEnvironment.RU, ProductionEnvironment.INT, false));
        assertEquals(ProductionEnvironment.RU,
                DetermineCountryUtil.bestEnvironment(ProductionEnvironment.INT, ProductionEnvironment.RU, true));
        assertEquals(ProductionEnvironment.RU,
                DetermineCountryUtil.bestEnvironment(ProductionEnvironment.INT, ProductionEnvironment.RU, false));

        assertEquals(ProductionEnvironment.INT,
                DetermineCountryUtil.bestEnvironment(ProductionEnvironment.INT, null, true));
        assertEquals(ProductionEnvironment.INT,
                DetermineCountryUtil.bestEnvironment(ProductionEnvironment.INT, null, false));
        assertEquals(ProductionEnvironment.INT,
                DetermineCountryUtil.bestEnvironment(null, ProductionEnvironment.INT, true));
        assertEquals(ProductionEnvironment.INT,
                DetermineCountryUtil.bestEnvironment(null, ProductionEnvironment.INT, false));
    }

    @Test
    public void determineCountryForNewVersionTest() {

        Country defaultCountryForServer = new Country();
        defaultCountryForServer.setId(100L);

        Optional<Country> countryByIpOptional = Optional.empty();

        Device device = new Device();
        User authorizedUser = new User();

        Map<ProductionEnvironment, Optional<Country>> defaultCountriesForEnv = new HashMap<>();
        defaultCountriesForEnv.put(ProductionEnvironment.RU, Optional.empty());
        defaultCountriesForEnv.put(ProductionEnvironment.INT, Optional.empty());

        Country defaultCountryForRU = new Country();
        defaultCountryForRU.setId(1L);

        Country defaultCountryForINT = new Country();
        defaultCountryForINT.setId(2L);

        /*
        Cases tree:

        Level 1: final env
         1.1: RU
         1.2: INT
        Level 2: countryByIp
         2.1: empty
         2.2: env=null
         2.3: env=RU
         2.4: env=INT
        Level 3: defaultCountryForEnv
         3.1: empty
         3.2: env=null
         3.3: env=RU
         3.4: env=INT
         */

        /////////1.1

        /////1.1 - 2.1

        // 1.1 - 2.1 - 3.1
        assertEquals(defaultCountryForServer,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.1 - 3.2
        defaultCountriesForEnv.put(ProductionEnvironment.RU, Optional.of(defaultCountryForRU));

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.1 - 3.3
        defaultCountryForRU.setEnvironment(ProductionEnvironment.RU);
        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.1 - 3.4
        defaultCountryForRU.setEnvironment(ProductionEnvironment.INT);
        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        /////1.1 - 2.2 -----------------------------------------------

        Country countryByIp = new Country();
        countryByIp.setId(111L);
        countryByIpOptional = Optional.of(countryByIp);

        // 1.1 - 2.2 - 3.1
        defaultCountriesForEnv.put(ProductionEnvironment.RU, Optional.empty());

        assertEquals(defaultCountryForServer,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.2 - 3.2
        defaultCountriesForEnv.put(ProductionEnvironment.RU, Optional.of(defaultCountryForRU));
        defaultCountryForRU.setEnvironment(null);

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.2 - 3.3
        defaultCountryForRU.setEnvironment(ProductionEnvironment.RU);

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.2 - 3.4
        defaultCountryForRU.setEnvironment(ProductionEnvironment.INT);

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        /////1.1 - 2.3 -----------------------------------------
        countryByIp.setEnvironment(ProductionEnvironment.RU);

        // 1.1 - 2.3 - 3.1
        defaultCountriesForEnv.put(ProductionEnvironment.RU, Optional.empty());

        assertEquals(countryByIp,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.3 - 3.2
        defaultCountriesForEnv.put(ProductionEnvironment.RU, Optional.of(defaultCountryForRU));
        defaultCountryForRU.setEnvironment(null);

        assertEquals(countryByIp,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.3 - 3.3
        defaultCountryForRU.setEnvironment(ProductionEnvironment.RU);
        assertEquals(countryByIp,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.3 - 3.4
        defaultCountryForRU.setEnvironment(ProductionEnvironment.INT);
        assertEquals(countryByIp,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        /////1.1 - 2.4 -------------------------------------
        countryByIp.setEnvironment(ProductionEnvironment.INT);


        // 1.1 - 2.4 - 3.1
        defaultCountriesForEnv.put(ProductionEnvironment.RU, Optional.empty());

        assertEquals(defaultCountryForServer,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.4 - 3.2
        defaultCountriesForEnv.put(ProductionEnvironment.RU, Optional.of(defaultCountryForRU));
        defaultCountryForRU.setEnvironment(null);

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.4 - 3.3
        defaultCountriesForEnv.put(ProductionEnvironment.RU, Optional.of(defaultCountryForRU));
        defaultCountryForRU.setEnvironment(ProductionEnvironment.RU);

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.4 - 3.4
        defaultCountriesForEnv.put(ProductionEnvironment.RU, Optional.of(defaultCountryForRU));
        defaultCountryForRU.setEnvironment(ProductionEnvironment.INT);

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        //------------------------------------
        /////// 1.2
        //------------------------------------

        /////1.2 - 2.1

        countryByIpOptional = Optional.empty();

        // 1.2 - 2.1 - 3.1
        defaultCountriesForEnv.put(ProductionEnvironment.INT, Optional.empty());

        assertEquals(defaultCountryForServer,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.1 - 3.2
        defaultCountriesForEnv.put(ProductionEnvironment.INT, Optional.of(defaultCountryForINT));
        defaultCountryForINT.setEnvironment(null);

        assertEquals(defaultCountryForServer,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.1 - 3.3
        defaultCountryForINT.setEnvironment(ProductionEnvironment.RU);

        assertEquals(defaultCountryForServer,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.1 - 3.4
        defaultCountryForINT.setEnvironment(ProductionEnvironment.INT);

        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        /////1.2 - 2.2 -----------------------------------------------

        countryByIpOptional = Optional.of(countryByIp);
        countryByIp.setEnvironment(null);

        // 1.2 - 2.2 - 3.1
        defaultCountriesForEnv.put(ProductionEnvironment.INT, Optional.empty());

        assertEquals(defaultCountryForServer,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.2 - 3.2
        defaultCountriesForEnv.put(ProductionEnvironment.INT, Optional.of(defaultCountryForINT));
        defaultCountryForINT.setEnvironment(null);

        assertEquals(defaultCountryForServer,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.2 - 3.3
        defaultCountryForINT.setEnvironment(ProductionEnvironment.RU);

        assertEquals(defaultCountryForServer,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.2 - 3.4
        defaultCountryForINT.setEnvironment(ProductionEnvironment.INT);

        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        /////1.2 - 2.3 -----------------------------------------
        countryByIp.setEnvironment(ProductionEnvironment.RU);

        // 1.2 - 2.3 - 3.1
        defaultCountriesForEnv.put(ProductionEnvironment.INT, Optional.empty());

        assertEquals(defaultCountryForServer,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.3 - 3.2
        defaultCountriesForEnv.put(ProductionEnvironment.INT, Optional.of(defaultCountryForINT));
        defaultCountryForINT.setEnvironment(null);

        assertEquals(defaultCountryForServer,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.3 - 3.3
        defaultCountryForINT.setEnvironment(ProductionEnvironment.RU);
        assertEquals(defaultCountryForServer,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.3 - 3.4
        defaultCountryForINT.setEnvironment(ProductionEnvironment.INT);
        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        /////1.2 - 2.4 -------------------------------------

        countryByIp.setEnvironment(ProductionEnvironment.INT);

        // 1.2 - 2.4 - 3.1
        defaultCountriesForEnv.put(ProductionEnvironment.INT, Optional.empty());

        assertEquals(countryByIp,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.4 - 3.2
        defaultCountriesForEnv.put(ProductionEnvironment.INT, Optional.of(defaultCountryForINT));
        defaultCountryForINT.setEnvironment(null);

        assertEquals(countryByIp,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.4 - 3.3
        defaultCountryForINT.setEnvironment(ProductionEnvironment.RU);

        assertEquals(countryByIp,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.4 - 3.4
        defaultCountryForINT.setEnvironment(ProductionEnvironment.INT);

        assertEquals(countryByIp,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        null,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));
    }

    @Test
    public void determineCountryForNewVersionWithStoreEnvironmentINTTest() {

        Country defaultCountryForServer = new Country();
        defaultCountryForServer.setId(100L);

        Optional<Country> countryByIpOptional = Optional.empty();

        Device device = new Device();
        User authorizedUser = new User();

        Country defaultCountryForRU = new Country();
        defaultCountryForRU.setId(1L);
        defaultCountryForRU.setEnvironment(ProductionEnvironment.RU);

        Country defaultCountryForINT = new Country();
        defaultCountryForINT.setId(2L);
        defaultCountryForINT.setEnvironment(ProductionEnvironment.INT);

        Map<ProductionEnvironment, Optional<Country>> defaultCountriesForEnv = new HashMap<>();
        defaultCountriesForEnv.put(ProductionEnvironment.RU, Optional.of(defaultCountryForRU));
        defaultCountriesForEnv.put(ProductionEnvironment.INT, Optional.of(defaultCountryForINT));

        /*
        Cases tree:

        Level 1: final env
         1.1: RU
         1.2: INT
        Level 2: countryByIp
         2.1: empty
         2.2: env=null
         2.3: env=RU
         2.4: env=INT
        Level 3: defaultCountryForEnv
         3.1: empty
         3.2: env=null
         3.3: env=RU
         3.4: env=INT
         */

        /////////1.1

        /////1.1 - 2.1

        // 1.1 - 2.1 - 3.1
        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.1 - 3.2
        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.1 - 3.3
        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.1 - 3.4
        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        /////1.1 - 2.2 -----------------------------------------------

        Country countryByIp = new Country();
        countryByIp.setId(111L);
        countryByIpOptional = Optional.of(countryByIp);

        // 1.1 - 2.2 - 3.1

        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.2 - 3.2

        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.2 - 3.3

        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.2 - 3.4

        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        /////1.1 - 2.3 -----------------------------------------
        countryByIp.setEnvironment(ProductionEnvironment.RU);

        // 1.1 - 2.3 - 3.1

        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.3 - 3.2

        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.3 - 3.3
        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.3 - 3.4
        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        /////1.1 - 2.4 -------------------------------------
        countryByIp.setEnvironment(ProductionEnvironment.INT);


        // 1.1 - 2.4 - 3.1

        assertEquals(countryByIp,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.4 - 3.2

        assertEquals(countryByIp,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.4 - 3.3

        assertEquals(countryByIp,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.4 - 3.4

        assertEquals(countryByIp,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        //------------------------------------
        /////// 1.2
        //------------------------------------

        /////1.2 - 2.1

        countryByIpOptional = Optional.empty();

        // 1.2 - 2.1 - 3.1

        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.1 - 3.2

        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.1 - 3.3

        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.1 - 3.4

        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        /////1.2 - 2.2 -----------------------------------------------

        countryByIpOptional = Optional.of(countryByIp);
        countryByIp.setEnvironment(null);

        // 1.2 - 2.2 - 3.1

        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.2 - 3.2

        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.2 - 3.3

        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.2 - 3.4

        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        /////1.2 - 2.3 -----------------------------------------
        countryByIp.setEnvironment(ProductionEnvironment.RU);

        // 1.2 - 2.3 - 3.1

        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.3 - 3.2

        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.3 - 3.3
        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.3 - 3.4
        assertEquals(defaultCountryForINT,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        /////1.2 - 2.4 -------------------------------------

        countryByIp.setEnvironment(ProductionEnvironment.INT);

        // 1.2 - 2.4 - 3.1

        assertEquals(countryByIp,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.4 - 3.2

        assertEquals(countryByIp,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.4 - 3.3

        assertEquals(countryByIp,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.4 - 3.4

        assertEquals(countryByIp,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.INT,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));
    }

    @Test
    public void determineCountryForNewVersionWithStoreEnvironmentRUTest() {

        Country defaultCountryForServer = new Country();
        defaultCountryForServer.setId(100L);

        Optional<Country> countryByIpOptional = Optional.empty();

        Device device = new Device();
        User authorizedUser = new User();

        Country defaultCountryForRU = new Country();
        defaultCountryForRU.setId(1L);
        defaultCountryForRU.setEnvironment(ProductionEnvironment.RU);

        Country defaultCountryForINT = new Country();
        defaultCountryForINT.setId(2L);
        defaultCountryForINT.setEnvironment(ProductionEnvironment.INT);

        Map<ProductionEnvironment, Optional<Country>> defaultCountriesForEnv = new HashMap<>();
        defaultCountriesForEnv.put(ProductionEnvironment.RU, Optional.of(defaultCountryForRU));
        defaultCountriesForEnv.put(ProductionEnvironment.INT, Optional.of(defaultCountryForINT));

        /*
        Cases tree:

        Level 1: final env
         1.1: RU
         1.2: INT
        Level 2: countryByIp
         2.1: empty
         2.2: env=null
         2.3: env=RU
         2.4: env=INT
        Level 3: defaultCountryForEnv
         3.1: empty
         3.2: env=null
         3.3: env=RU
         3.4: env=INT
         */

        /////////1.1

        /////1.1 - 2.1

        // 1.1 - 2.1 - 3.1
        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.1 - 3.2
        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.1 - 3.3
        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.1 - 3.4
        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        /////1.1 - 2.2 -----------------------------------------------

        Country countryByIp = new Country();
        countryByIp.setId(111L);
        countryByIpOptional = Optional.of(countryByIp);

        // 1.1 - 2.2 - 3.1

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.2 - 3.2

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.2 - 3.3

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.2 - 3.4

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        /////1.1 - 2.3 -----------------------------------------
        countryByIp.setEnvironment(ProductionEnvironment.RU);

        // 1.1 - 2.3 - 3.1

        assertEquals(countryByIp,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.3 - 3.2

        assertEquals(countryByIp,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.3 - 3.3
        assertEquals(countryByIp,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.3 - 3.4
        assertEquals(countryByIp,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        /////1.1 - 2.4 -------------------------------------
        countryByIp.setEnvironment(ProductionEnvironment.INT);


        // 1.1 - 2.4 - 3.1

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.4 - 3.2

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.4 - 3.3

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.1 - 2.4 - 3.4

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        false,
                        defaultCountriesForEnv, defaultCountryForServer));

        //------------------------------------
        /////// 1.2
        //------------------------------------

        /////1.2 - 2.1

        countryByIpOptional = Optional.empty();

        // 1.2 - 2.1 - 3.1

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.1 - 3.2

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.1 - 3.3

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.1 - 3.4

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        /////1.2 - 2.2 -----------------------------------------------

        countryByIpOptional = Optional.of(countryByIp);
        countryByIp.setEnvironment(null);

        // 1.2 - 2.2 - 3.1

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.2 - 3.2

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.2 - 3.3

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.2 - 3.4

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        /////1.2 - 2.3 -----------------------------------------
        countryByIp.setEnvironment(ProductionEnvironment.RU);

        // 1.2 - 2.3 - 3.1

        assertEquals(countryByIp,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.3 - 3.2

        assertEquals(countryByIp,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.3 - 3.3
        assertEquals(countryByIp,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.3 - 3.4
        assertEquals(countryByIp,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        /////1.2 - 2.4 -------------------------------------

        countryByIp.setEnvironment(ProductionEnvironment.INT);

        // 1.2 - 2.4 - 3.1

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.4 - 3.2

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.4 - 3.3

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));

        // 1.2 - 2.4 - 3.4

        assertEquals(defaultCountryForRU,
                DetermineCountryUtil.determineCountryForNewVersion(countryByIpOptional,
                        ProductionEnvironment.RU,
                        device,
                        authorizedUser,
                        true,
                        defaultCountriesForEnv, defaultCountryForServer));
    }

    @Test
    public void determineCountryForOldVersionTest() {
 /*
			Если окружение INT, проверяем env страны.
			Если env страны = INT, возвращаем страну. Если нет, то default
		 */

        Country defaultCountryForServer = new Country();
        defaultCountryForServer.setId(100L);

        Optional<Country> countryByIpOptional = Optional.empty();

        assertEquals(defaultCountryForServer,
                DetermineCountryUtil.determineCountryForOldVersion(countryByIpOptional,
                        false, defaultCountryForServer));

        assertEquals(defaultCountryForServer,
                DetermineCountryUtil.determineCountryForOldVersion(countryByIpOptional,
                        true, defaultCountryForServer));

        Country countryByIp = new Country();
        countryByIp.setId(200L);
        countryByIp.setEnvironment(ProductionEnvironment.RU);

        countryByIpOptional = Optional.of(countryByIp);

        assertEquals(defaultCountryForServer,
                DetermineCountryUtil.determineCountryForOldVersion(countryByIpOptional,
                        false, defaultCountryForServer));
        assertEquals(defaultCountryForServer,
                DetermineCountryUtil.determineCountryForOldVersion(countryByIpOptional,
                        true, defaultCountryForServer));

        countryByIp.setEnvironment(ProductionEnvironment.INT);

        assertEquals(defaultCountryForServer,
                DetermineCountryUtil.determineCountryForOldVersion(countryByIpOptional,
                        false, defaultCountryForServer));
        assertEquals(countryByIp,
                DetermineCountryUtil.determineCountryForOldVersion(countryByIpOptional,
                        true, defaultCountryForServer));
    }
}
