package ru.oskelly.tests.pr.suite6_3.orderflow;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.OrderFlowTest;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestTcbMock;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.domain.model.banktransaction.OperationType;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.fiscalreceiptrequest.FiscalReceiptRequestType;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPaymentState;
import su.reddot.domain.model.order.OrderRefundReasonType;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.user.User;
import su.reddot.domain.model.userbalance.UserBalanceChangeMode;
import su.reddot.domain.model.userbalance.UserBalanceChangeType;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.bank.jobs.AgentPaymentJobs;
import su.reddot.infrastructure.logistic.DeliveryState;
import su.reddot.infrastructure.util.CallInTransaction;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
@TestMethodOrder(MethodOrderer.MethodName.class)
public class OrderFlowConciergeOrderTest extends OrderFlowTest {

    @Autowired
    private UserService userService;
    @Autowired
    private AgentPaymentJobs agentPaymentJobs;

    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private CartTestSupport cartTestSupport;
    @Autowired
    private CallInTransaction callInTransaction;

    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user-password}")
    private String password;
    @Value("${test-prepayments.usual-seller-id}")
    private Long usualSellerId;
    @Value("${test-prepayments.usual-seller-counterparty-id}")
    private Long usualSellerCounterpartyId;
    @Value("${test-prepayments.pickup-id}")
    private Long pickupId;
    @Value("${test-prepayments.delivery-id}")
    private Long deliveryId;

    private static OrderFlowTestTcbMock orderFlowTestTcbMock;

    @Value("${test.receipts.mock-server-host}")
    private String mockServerHost;
    @Value("${test.receipts.mock-server-tcb-bank-port}")
    private Integer mockTcbServerPort;

    private static final String EXCEPTION_MASK_EXPERTISE_DEFECTS = "Order .*: unable to complete expertise step / refund, concierge order with order positions state VERIFICATION_BAD_STATE";
    private static final String EXCEPTION_MASK_EXPERTISE_DOCLEAN = "Order .*: unable to complete expertise step / refund, concierge order with order positions state VERIFICATION_NEED_CLEANING";


    private Long prepareAdminUser() {
        User adminUser = userService.getUserByEmail(buyerEmail);
        orderFlowTestUtils.enableUserAuthority(adminUser.getId(), AuthorityName.ORDER_CONCIERGE_PAYOUTS, true);
        orderFlowTestUtils.enableUserAuthority(adminUser.getId(), AuthorityName.ORDER_PAYOUTS, true);
        orderFlowTestUtils.enableUserAuthority(adminUser.getId(), AuthorityName.ORDER_PREPAYMENTS, true);
        orderFlowTestUtils.enableUserAuthority(adminUser.getId(), AuthorityName.ORDER_MANUAL_CHANGE_DELIVERY_STATE, true);
        return adminUser.getId();
    }

    @PostConstruct
    private void init() {
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(TcbBankService.SCHEMA));
        User buyer = userService.getUserByEmail(buyerEmail);
        ApiV2Client apiV2Client = new ApiV2Client(buyerEmail, password);
        orderFlowTestUtils.init(buyerEmail, password);
        cartTestSupport.setUserId(buyer.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
        orderFlowTestTcbMock = Objects.isNull(orderFlowTestTcbMock) ? new OrderFlowTestTcbMock(mockServerHost, mockTcbServerPort) : orderFlowTestTcbMock;
        callInTransaction.runInNewTransaction(this::prepareAdminUser);
    }

    @AfterAll
    public static void done() {
        orderFlowTestTcbMock.stop();
    }

    private List<Product> conciergeOrderTest_getConciergeProducts(int itemsCount) {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                        .sellerId(usualSellerId)
                        .forConcierge(true)
                        .maxItems(itemsCount)
                .build()
        );
        for (Product product : products) {
            product.setSelectedConciergeTime(LocalDateTime.now());
        }
        return products;
    }

    private OrderDTO conciergeOrderTest_makeOrderAndAuthorizePayment(List<Product> products) {
        orderFlowTestUtils.fillCart(products);
        //
        OrderService.InitOrderResult testOrder = orderFlowTestUtils.holdOrderWithPromoCodePS(products.get(0).getSeller(), null, TcbBankService.SCHEMA);
        orderFlowTestUtils.callOrderHoldCallback(testOrder.getOrderId(), testOrder.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        OrderDTO orderInfo = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getOrderId(), true);
        Assertions.assertThat(orderInfo.getId()).isNotZero();
        //
        return orderInfo;
    }

    private boolean isConciergeDebt(OrderFlowTestUtils.TestConfig testConfig) {
        return CollectionUtils.isNotEmpty(testConfig.getExpertiseFailPositions());
    }

    private void conciergeOrderTest_executeRefundStep(OrderDTO testOrder, OrderFlowTestUtils.TestConfig testConfig) {
        boolean isRefundAsReverse = testConfig.isExpectingRefundOnDoConfirm() && BooleanUtils.isNotTrue(testConfig.isPrepayBefOrderConfirm());
        //
        ResponseEntity<String> refundResponse = orderFlowTestUtils.adminPanel_refund(testOrder.getId(), false);
        Assertions.assertThat(refundResponse.getStatusCode().series()).isEqualTo(HttpStatus.Series.SUCCESSFUL);
        rollbackAndStartNewTransaction();
        //
        if (!isRefundAsReverse) {
            orderFlowTestUtils.processBankOperation(testOrder.getId(), OperationType.REFUND);
            rollbackAndStartNewTransaction();
        }
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.REFUND);
        orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, isRefundAsReverse
                ? OrderPaymentState.REVERSE_DONE
                : OrderPaymentState.REFUND_DONE);
        //
        List<OperationType> validateOperations = Lists.newArrayList(Lists.newArrayList(OperationType.HOLD));
        if (isRefundAsReverse) {
            validateOperations.add(OperationType.HOLD_REVERSE);
        } else {
            if (testConfig.isRefusalsMoneyReturnAsReverse()) {
                validateOperations.add(OperationType.HOLD_REVERSE);
            }
            validateOperations.add(OperationType.HOLD_COMPLETE);
            if (testConfig.isExpectingRefundOnExpertise()) {
                validateOperations.add(OperationType.SELLER_PAYOUT);
            }
            validateOperations.add(OperationType.REFUND);
        }
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(), validateOperations);
        //
        List<FiscalReceiptRequestType> validateReceiptsList = Lists.newArrayList();
        if (!isRefundAsReverse) {
            validateReceiptsList.add(FiscalReceiptRequestType.DELIVERY_ADVANCE);
            validateReceiptsList.add(FiscalReceiptRequestType.DELIVERY_REFUND);
        }
        orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), validateReceiptsList);
        //
        Assertions.assertThat(testConfig.getFailText()).isNull();
        //
        if (isConciergeDebt(testConfig)) {
            BigDecimal balanceValue = orderFlowTestUtils.adjustUserBalanceToValue(testOrder.getSeller().getId(), null);
            Assertions.assertThat(balanceValue).isNegative();
        }
    }

    private void conciergeOrderSkipPayoutTest_executeRefundStep(OrderDTO testOrder, OrderFlowTestUtils.TestConfig testConfig) {
        boolean isRefundAsReverse = BooleanUtils.isNotTrue(testConfig.isPrepayBefOrderConfirm()) || BooleanUtils.isNotTrue(testConfig.isPrepayAftOrderConfirm());
        //
        ResponseEntity<String> refundResponse = orderFlowTestUtils.adminPanel_refund(testOrder.getId(), false);
        Assertions.assertThat(refundResponse.getStatusCode().series()).isEqualTo(HttpStatus.Series.SUCCESSFUL);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.REFUND);
        orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, isRefundAsReverse
                ? OrderPaymentState.REVERSE_DONE
                : OrderPaymentState.REFUND_DONE);
        //
        List<OperationType> validateOperations = Lists.newArrayList(Lists.newArrayList(OperationType.HOLD));
        if (isRefundAsReverse) {
            validateOperations.add(OperationType.HOLD_REVERSE);
        } else {
            if (testConfig.isRefusalsMoneyReturnAsReverse()) {
                validateOperations.add(OperationType.HOLD_REVERSE);
            }
            validateOperations.add(OperationType.HOLD_COMPLETE);
            if (testConfig.isExpectingRefundOnExpertise()) {
                validateOperations.add(OperationType.SELLER_PAYOUT);
            }
            validateOperations.add(OperationType.REFUND);
        }
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(), validateOperations);
        //
        List<FiscalReceiptRequestType> validateReceiptsList = Lists.newArrayList();
        if (!isRefundAsReverse) {
            validateReceiptsList.add(FiscalReceiptRequestType.DELIVERY_ADVANCE);
            validateReceiptsList.add(FiscalReceiptRequestType.DELIVERY_REFUND);
        }
        orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), validateReceiptsList);
        //
        Assertions.assertThat(testConfig.getFailText()).isNull();
        //
        if (isConciergeDebt(testConfig)) {
            BigDecimal balanceValue = orderFlowTestUtils.adjustUserBalanceToValue(testOrder.getSeller().getId(), null);
            if (BigDecimal.ZERO.compareTo(balanceValue) != 0) {
                Assertions.assertThat(balanceValue).isNegative();
            }
        }
    }

    private void conciergeOrderTest_executePrepayStep(OrderDTO testOrder) {
        ResponseEntity<String> responseOnConvertToPrepayment = orderFlowTestUtils.convertHoldToPrepayment(testOrder.getId(), HttpStatus.Series.SUCCESSFUL);
        Assertions.assertThat(responseOnConvertToPrepayment.getStatusCode().series()).isEqualTo(HttpStatus.Series.SUCCESSFUL);
        orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_INPROGRESS);
        orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE));
        orderFlowTestUtils.processHoldComplete(testOrder);
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_DONE);
    }

    private OrderDTO conciergeOrderSkipPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig conciergeTestConfig) {
        List<Product> products = conciergeOrderTest_getConciergeProducts(conciergeTestConfig.getItemsCount());
        commitAndStartNewTransaction();
        //
        OrderDTO orderInfo = conciergeOrderTest_makeOrderAndAuthorizePayment(products);
        //
        orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), BigDecimal.ZERO);
        commitAndStartNewTransaction();
        //
        if (conciergeTestConfig.isPrepayBefOrderConfirm()) {
            conciergeOrderTest_executePrepayStep(orderInfo);
        }
        //
        int confirmedCounter = orderFlowTestUtils.confirmRefuseOrderPositions(orderInfo, products, conciergeTestConfig);
        if (confirmedCounter == 0) {
            conciergeOrderSkipPayoutTest_executeRefundStep(orderInfo, conciergeTestConfig);
            return orderInfo;
        }
        //
        orderFlowTestUtils.changeAddressEndpoint(orderInfo.getId(), pickupId, deliveryId);
        orderFlowTestUtils.adminPanel_sendWithDeliveryCompany(orderInfo.getId(), HttpStatus.Series.SUCCESSFUL);
        //
        if (conciergeTestConfig.isPrepayAftOrderConfirm()) {
            conciergeOrderTest_executePrepayStep(orderInfo);
        }
        //
        orderFlowTestUtils.changeSellerCounterparty(orderInfo.getId(), usualSellerCounterpartyId, HttpStatus.Series.SUCCESSFUL);
        //
        List<OperationType> operationBefSellerPayout = new ArrayList<>();
        operationBefSellerPayout.add(OperationType.HOLD);
        if (conciergeTestConfig.isPrepayAftOrderConfirm()) {
            if (conciergeTestConfig.getRefusePositions().size() > 0) {
                operationBefSellerPayout.add(OperationType.HOLD_REVERSE);
            }
            operationBefSellerPayout.add(OperationType.HOLD_COMPLETE);
        }
        if (conciergeTestConfig.isPrepayBefOrderConfirm()) {
            operationBefSellerPayout.add(OperationType.HOLD_COMPLETE);
        }
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), operationBefSellerPayout);
        orderFlowTestUtils.validateOrderDeliveryState(orderInfo.getId(), DeliveryState.FROM_SELLER_TO_OFFICE);
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE, true);
        //
        int itemsLeftExpertiseStep = orderFlowTestUtils.processExpertiseSteps(orderInfo, products, conciergeTestConfig);
        if (itemsLeftExpertiseStep == 0) {
            conciergeOrderSkipPayoutTest_executeRefundStep(orderInfo, conciergeTestConfig);
            return orderInfo;
        }
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.HOLD);
        //
        if (!conciergeTestConfig.isPrepayAftOrderConfirm() || !conciergeTestConfig.isPrepayBefOrderConfirm()) {
            orderFlowTestUtils.adminPanel_Charge(orderInfo.getId());
            rollbackAndStartNewTransaction();
        }
        //
        orderFlowTestUtils.adminPanel_paymentToConcierge(orderInfo.getId(), true);
        rollbackAndStartNewTransaction();

        orderFlowTestUtils.changeSellerCounterpartyFailPayoutActiveOrDone(orderInfo.getId(), usualSellerCounterpartyId, OrderFlowTestUtils.FAIL_TEXT_PAYOUT_ACTIVE_OR_DONE);

        if (!conciergeTestConfig.isAnyPrepayModeSet()) {
            orderFlowTestUtils.processHoldComplete(orderInfo);
            commitAndStartNewTransaction();
        }

        List<OperationType> operationAftSellerPayout = new ArrayList<>();
        operationAftSellerPayout.add(OperationType.HOLD);
        if (conciergeTestConfig.getItemsCount() > conciergeTestConfig.getExpertisePassPositions().size()) {
            operationAftSellerPayout.add(OperationType.HOLD_REVERSE);
        }
        if (conciergeTestConfig.getConfirmPositions().size() > 0) {
            operationAftSellerPayout.add(OperationType.HOLD_COMPLETE);
        }
        if (conciergeTestConfig.isExpectingRefundOnDoConfirm() && conciergeTestConfig.isRefusalsMoneyReturnAsRefunds()) {
            operationAftSellerPayout.add(OperationType.REFUND);
        }
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), operationAftSellerPayout);

        orderFlowTestUtils.prepareSellerPayout(orderInfo.getId(), true);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.changeSellerCounterpartyFailPayoutActiveOrDone(orderInfo.getId(), usualSellerCounterpartyId, OrderFlowTestUtils.FAIL_TEXT_PAYOUT_ACTIVE_OR_DONE);
        //
        orderFlowTestUtils.transferMoneyToSellers(orderInfo.getId());
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.changeSellerCounterpartyFailPayoutActiveOrDone(orderInfo.getId(), usualSellerCounterpartyId, OrderFlowTestUtils.FAIL_TEXT_PAYOUT_ACTIVE_OR_DONE);
        //
        orderFlowTestUtils.validateMoneyToSellers(orderInfo.getId());
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.changeSellerCounterpartyFailPayoutActiveOrDone(orderInfo.getId(), usualSellerCounterpartyId, OrderFlowTestUtils.FAIL_TEXT_PAYOUT_ACTIVE_OR_DONE);
        //
        agentPaymentJobs.transferMoneyToSeller();
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.changeSellerCounterpartyFailPayoutActiveOrDone(orderInfo.getId(), usualSellerCounterpartyId, OrderFlowTestUtils.FAIL_TEXT_PAYOUT_ACTIVE_OR_DONE);
        //
        ResponseEntity<String> responseOnSendO2B = orderFlowTestUtils.sendOurselves(orderInfo.getId(), null);
        rollbackAndStartNewTransaction();
        //
        List<OperationType> operationAftOrdExpertise = new ArrayList<>();
        operationAftOrdExpertise.add(OperationType.HOLD);
        if (conciergeTestConfig.getItemsCount() > conciergeTestConfig.getExpertisePassPositions().size()) {
            operationAftOrdExpertise.add(OperationType.HOLD_REVERSE);
        }
        operationAftOrdExpertise.add(OperationType.HOLD_COMPLETE);
        operationAftOrdExpertise.add(OperationType.SELLER_PAYOUT);
        if (conciergeTestConfig.isRefusalsMoneyReturnAsRefunds() && CollectionUtils.isNotEmpty(conciergeTestConfig.getExpertiseFailPositions())) {
            operationAftOrdExpertise.add(OperationType.REFUND);
        }
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), operationAftOrdExpertise);
        //
        int expertiseUncleanSize = conciergeTestConfig.getDefectsByPositions().size() + conciergeTestConfig.getCleaningsByPositions().size();
        if (expertiseUncleanSize > 0) {
            Exception failOnSend = orderFlowTestUtils.readExceptionFromText(responseOnSendO2B.getBody());
            Assertions.assertThat(failOnSend.getMessage()).matches(conciergeTestConfig.getFailText());
            orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.HOLD);
            return orderInfo;
        }
        //
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.MONEY_TRANSFERRED);
        if (conciergeTestConfig.getStopPosition() == OrderFlowTestUtils.TestStopPosition.EXPERTISE_CALC_DONE) {
            return orderInfo;
        }
        //
        orderFlowTestUtils.changeSellerCounterpartyFailPayoutActiveOrDone(orderInfo.getId(), usualSellerCounterpartyId, OrderFlowTestUtils.FAIL_TEXT_PAYOUT_ACTIVE_OR_DONE);
        //
        Assertions.assertThat(responseOnSendO2B.getStatusCode().series()).isEqualTo(HttpStatus.Series.SUCCESSFUL);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.OURSELVES_FROM_OFFICE_TO_BUYER, true);
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.DELIVERED_TO_BUYER, true);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.COMPLETED);
        //
        BigDecimal balanceOnDelivered = orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null);
        //
        BigDecimal balanceOnCompleted = orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null);
        Assertions.assertThat(balanceOnCompleted).isEqualByComparingTo(balanceOnDelivered);
        //
        orderFlowTestUtils.changeSellerCounterpartyFailPayoutActiveOrDone(orderInfo.getId(), usualSellerCounterpartyId, OrderFlowTestUtils.FAIL_TEXT_ORDER_STATUS_COMPLETE);
        //
        Assertions.assertThat(conciergeTestConfig.getFailText()).isNull();
        //
        return orderInfo;
    }

    private OrderDTO conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig testConfig) {
        OrderFlowTestUtils.TestConfig conciergeTestConfig = testConfig
                .toBuilder()
                    .payoutAftOrderConfirm(true)
                .build();
        //
        List<Product> products = conciergeOrderTest_getConciergeProducts(conciergeTestConfig.getItemsCount());
        commitAndStartNewTransaction();
        //
        OrderDTO orderInfo = conciergeOrderTest_makeOrderAndAuthorizePayment(products);
        //
        orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), BigDecimal.ZERO);
        commitAndStartNewTransaction();
        //
        if (conciergeTestConfig.isPrepayBefOrderConfirm()) {
            conciergeOrderTest_executePrepayStep(orderInfo);
        }
        //
        int confirmedCounter = orderFlowTestUtils.confirmRefuseOrderPositions(orderInfo, products, conciergeTestConfig);
        if (confirmedCounter == 0) {
            conciergeOrderTest_executeRefundStep(orderInfo, conciergeTestConfig);
            return orderInfo;
        }
        //
        orderFlowTestUtils.changeAddressEndpoint(orderInfo.getId(), pickupId, deliveryId);
        orderFlowTestUtils.adminPanel_sendWithDeliveryCompany(orderInfo.getId(), HttpStatus.Series.SUCCESSFUL);
        //
        if (conciergeTestConfig.isPrepayAftOrderConfirm()) {
            conciergeOrderTest_executePrepayStep(orderInfo);
        }
        //
        orderFlowTestUtils.changeSellerCounterparty(orderInfo.getId(), usualSellerCounterpartyId, HttpStatus.Series.SUCCESSFUL);
        //
        List<OperationType> operationBefSellerPayout = new ArrayList<>();
        operationBefSellerPayout.add(OperationType.HOLD);
        if (conciergeTestConfig.isPrepayAftOrderConfirm()) {
            if (conciergeTestConfig.getRefusePositions().size() > 0) {
                operationBefSellerPayout.add(OperationType.HOLD_REVERSE);
            }
            operationBefSellerPayout.add(OperationType.HOLD_COMPLETE);
        }
        if (conciergeTestConfig.isPrepayBefOrderConfirm()) {
            operationBefSellerPayout.add(OperationType.HOLD_COMPLETE);
        }
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), operationBefSellerPayout);
        //
        orderFlowTestUtils.adminPanel_paymentToConcierge(orderInfo.getId(), true);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.changeSellerCounterpartyFailPayoutActiveOrDone(orderInfo.getId(), usualSellerCounterpartyId, OrderFlowTestUtils.FAIL_TEXT_PAYOUT_ACTIVE_OR_DONE);
        //
        if (!conciergeTestConfig.isAnyPrepayModeSet()) {
            orderFlowTestUtils.processHoldComplete(orderInfo);
            commitAndStartNewTransaction();
        }
        //
        List<OperationType> operationAftSellerPayout = new ArrayList<>();
        operationAftSellerPayout.add(OperationType.HOLD);
        if (conciergeTestConfig.isRefusalsMoneyReturnAsReverse()) {
            operationAftSellerPayout.add(OperationType.HOLD_REVERSE);
        }
        if (conciergeTestConfig.getConfirmPositions().size() > 0) {
            operationAftSellerPayout.add(OperationType.HOLD_COMPLETE);
        }
        if (conciergeTestConfig.isExpectingRefundOnDoConfirm() && conciergeTestConfig.isRefusalsMoneyReturnAsRefunds()) {
            operationAftSellerPayout.add(OperationType.REFUND);
        }
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), operationAftSellerPayout);
        //
        orderFlowTestUtils.prepareSellerPayout(orderInfo.getId(), true);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.changeSellerCounterpartyFailPayoutActiveOrDone(orderInfo.getId(), usualSellerCounterpartyId, OrderFlowTestUtils.FAIL_TEXT_PAYOUT_ACTIVE_OR_DONE);
        //
        orderFlowTestUtils.transferMoneyToSellers(orderInfo.getId());
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.changeSellerCounterpartyFailPayoutActiveOrDone(orderInfo.getId(), usualSellerCounterpartyId, OrderFlowTestUtils.FAIL_TEXT_PAYOUT_ACTIVE_OR_DONE);
        //
        orderFlowTestUtils.validateMoneyToSellers(orderInfo.getId());
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.changeSellerCounterpartyFailPayoutActiveOrDone(orderInfo.getId(), usualSellerCounterpartyId, OrderFlowTestUtils.FAIL_TEXT_PAYOUT_ACTIVE_OR_DONE);
        //
        agentPaymentJobs.transferMoneyToSeller();
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.changeSellerCounterpartyFailPayoutActiveOrDone(orderInfo.getId(), usualSellerCounterpartyId, OrderFlowTestUtils.FAIL_TEXT_PAYOUT_ACTIVE_OR_DONE);
        //
        orderFlowTestUtils.validateOrderDeliveryState(orderInfo.getId(), DeliveryState.FROM_SELLER_TO_OFFICE);
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE, true);
        //
        int itemsLeftExpertiseStep = orderFlowTestUtils.processExpertiseSteps(orderInfo, products, conciergeTestConfig);
        if (itemsLeftExpertiseStep == 0) {
            conciergeOrderTest_executeRefundStep(orderInfo, conciergeTestConfig);
            return orderInfo;
        }
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.HOLD);
        //
        ResponseEntity<String> responseOnSendO2B = orderFlowTestUtils.sendOurselves(orderInfo.getId(), null);
        rollbackAndStartNewTransaction();
        //
        if (conciergeTestConfig.isRefusalsMoneyReturnAsRefunds() || CollectionUtils.isNotEmpty(conciergeTestConfig.getExpertiseFailPositions())) {
            orderFlowTestUtils.processBankOperation(orderInfo.getId(), OperationType.REFUND);
            rollbackAndStartNewTransaction();
        }
        //
        List<OperationType> operationAftOrdExpertise = new ArrayList<>();
        operationAftOrdExpertise.add(OperationType.HOLD);
        if (conciergeTestConfig.isRefusalsMoneyReturnAsReverse()) {
            operationAftOrdExpertise.add(OperationType.HOLD_REVERSE);
        }
        operationAftOrdExpertise.add(OperationType.HOLD_COMPLETE);
        operationAftOrdExpertise.add(OperationType.SELLER_PAYOUT);
        if (conciergeTestConfig.isRefusalsMoneyReturnAsRefunds() || conciergeTestConfig.isDefectsMoneyReturnAsRefunds()) {
            operationAftOrdExpertise.add(OperationType.REFUND);
        }
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), operationAftOrdExpertise);
        //
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.MONEY_TRANSFERRED);
        if (conciergeTestConfig.getStopPosition() == OrderFlowTestUtils.TestStopPosition.EXPERTISE_CALC_DONE) {
            return orderInfo;
        }
        //
        orderFlowTestUtils.changeSellerCounterpartyFailPayoutActiveOrDone(orderInfo.getId(), usualSellerCounterpartyId, OrderFlowTestUtils.FAIL_TEXT_PAYOUT_ACTIVE_OR_DONE);
        //
        Assertions.assertThat(responseOnSendO2B.getStatusCode().series()).isEqualTo(HttpStatus.Series.SUCCESSFUL);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.OURSELVES_FROM_OFFICE_TO_BUYER, true);
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.DELIVERED_TO_BUYER, true);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.COMPLETED);
        //
        BigDecimal balanceOnDelivered = orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null);
        //
        BigDecimal balanceOnCompleted = orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null);
        Assertions.assertThat(balanceOnCompleted).isEqualByComparingTo(balanceOnDelivered);
        //
        orderFlowTestUtils.changeSellerCounterpartyFailPayoutActiveOrDone(orderInfo.getId(), usualSellerCounterpartyId, OrderFlowTestUtils.FAIL_TEXT_ORDER_STATUS_COMPLETE);
        //
        Assertions.assertThat(conciergeTestConfig.getFailText()).isNull();
        //
        orderFlowTestUtils.returnCompletedOrSoldOrder(orderInfo.getId(), true, true, null);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.RETURN);
        //
        return orderInfo;
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _05_01_regular_captureOnPayout_confirms_0of1_expertise_0of1() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(1)
                .confirmPositions(Collections.emptyList())
                .refusePositions(ImmutableList.of(1))

                .expertiseFailPositions(Collections.emptyList())
                .expertisePassPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.REFUND);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 10_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), Collections.emptyList());
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.ZERO);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_01_regular_captureOnPayout_confirms_1of1_expertise_1of1() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(1)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.RETURN);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 7_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-7_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(1)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _89_01_regular_captureOnPayout_confirms_0of2_expertise_0of2() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(2)
                .confirmPositions(Collections.emptyList())
                .refusePositions(ImmutableList.of(1, 2))

                .expertiseFailPositions(Collections.emptyList())
                .expertisePassPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.REFUND);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 30_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), Collections.emptyList());
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.ZERO);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _96_01_regular_captureOnPayout_confirms_1of2_expertise_1of2() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(2)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(ImmutableList.of(2))

                .expertisePassPositions(ImmutableList.of(1))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.RETURN);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 20_000_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 7_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-7_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(1)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _10_01_regular_captureOnPayout_confirms_2of2_expertise_2of2() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(2)
                .confirmPositions(ImmutableList.of(1, 2))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1, 2))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.RETURN);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 22_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-22_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(1)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(22_500_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _10_02_prepays_befOrderConfirm_confirms_2of2_expertise_2of2() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(2)

                .prepayBefOrderConfirm(true)
                .confirmPositions(ImmutableList.of(1, 2))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1, 2))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.RETURN);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 22_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-22_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(1)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(22_500_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _10_03_prepays_aftOrderConfirm_confirms_2of2_expertise_2of2() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(2)

                .confirmPositions(ImmutableList.of(1, 2))
                .prepayAftOrderConfirm(true)
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1, 2))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.RETURN);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 22_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-22_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(1)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(22_500_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _03_01_regular_captureOnPayout_confirms_1of1_failOnExpertiseDefect_1of1() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(1)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(ImmutableList.of(100L))
                .cleaningsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.RETURN);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 7_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 100_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-7_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(2)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(7_400_00, 2));
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(100_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _92_01_regular_captureOnPayout_confirms_1of2_failOnExpertiseDefect_1of2() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(2)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(ImmutableList.of(2))

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Lists.newArrayList(100L, null))
                .cleaningsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.RETURN);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 20_000_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 7_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 100_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-7_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(2)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(7_400_00, 2));
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(100_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _04_01_regular_captureOnPayout_confirms_1of1_failOnExpertiseCleaning_1of1() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(1)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(ImmutableList.of(200L))

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.RETURN);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 7_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-7_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(2)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(7_300_00, 2));
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(200_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _91_01_regular_captureOnPayout_confirms_1of2_failOnExpertiseCleaning_1of2() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(2)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(ImmutableList.of(2))

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Lists.newArrayList(100L, null))

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.RETURN);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 20_000_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 7_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-7_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(2)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(7_400_00, 2));
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(100_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _02_01_regular_captureOnPayout_confirms_1of1_expertise_0of1() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(1)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(1))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.REFUND);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 7_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 10_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_REFUND));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_REFUND);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-7_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(1)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _90_01_regular_captureOnPayout_confirms_1of2_expertise_0of2() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(2)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(ImmutableList.of(2))

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(1))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.REFUND);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 20_000_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 7_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 10_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_REFUND));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_REFUND);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-7_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(1)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _05_03_prepays_aftOrderConfirm_confirms_0of1_expertise_0of1() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(1)
                .prepayAftOrderConfirm(true)
                .confirmPositions(Collections.emptyList())
                .refusePositions(ImmutableList.of(1))

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.REFUND);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 10_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), Collections.emptyList());
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.ZERO);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_03_prepays_aftOrderConfirm_confirms_1of1_expertise_1of1() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(1)
                .prepayAftOrderConfirm(true)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.RETURN);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 7_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-7_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(1)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _96_03_prepays_aftOrderConfirm_confirms_1of2_expertise_1of2() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(2)
                .prepayAftOrderConfirm(true)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(ImmutableList.of(2))

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.RETURN);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 20_000_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 7_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-7_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(1)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _05_02_prepays_befOrderConfirm_confirms_0of1_expertise_0of1() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(1)
                .prepayBefOrderConfirm(true)
                .confirmPositions(Collections.emptyList())
                .refusePositions(ImmutableList.of(1))

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.REFUND);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 10_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_REFUND));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_REFUND);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.ZERO);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_02_prepays_befOrderConfirm_confirms_1of1_expertise_1of1() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(1)
                .prepayBefOrderConfirm(true)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.RETURN);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 7_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-7_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(1)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _96_02_prepays_befOrderConfirm_confirms_1of2_expertise_1of2() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(2)
                .prepayBefOrderConfirm(true)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(ImmutableList.of(2))

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.RETURN);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 7_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 20_000_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-7_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(1)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _03_03_prepays_aftOrderConfirm_confirms_1of1_failOnExpertiseDefect_1of1() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(1)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())
                .prepayAftOrderConfirm(true)

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(ImmutableList.of(100L))
                .cleaningsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.RETURN);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 7_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 100_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-7_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(2)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(7_400_00, 2));
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(100_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _92_03_prepays_aftOrderConfirm_confirms_2of2_failOnExpertiseDefect_1of2() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(2)
                .confirmPositions(ImmutableList.of(1, 2))
                .refusePositions(Collections.emptyList())
                .prepayAftOrderConfirm(true)

                .expertisePassPositions(ImmutableList.of(2))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Lists.newArrayList(100L, null))
                .cleaningsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.RETURN);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 22_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 100_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-22_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(2)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(100_00, 2));
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(22_400_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _03_02_prepays_befOrderConfirm_confirms_1of1_failOnExpertiseDefect_1of1() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(1)
                .prepayBefOrderConfirm(true)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(ImmutableList.of(100L))
                .cleaningsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.RETURN);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 7_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 100_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-7_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(2)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(7_400_00, 2));
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(100_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _92_02_prepays_befOrderConfirm_confirms_2of2_failOnExpertiseDefect_1of2() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(2)
                .prepayBefOrderConfirm(true)
                .confirmPositions(ImmutableList.of(1, 2))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(2))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Lists.newArrayList(100L, null))
                .cleaningsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.RETURN);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 22_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 100_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-22_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(2)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(100_00, 2));
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(22_400_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _04_03_prepays_aftOrderConfirm_confirms_1of1_failOnExpertiseCleaning_1of1() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(1)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())
                .prepayAftOrderConfirm(true)

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(ImmutableList.of(200L))

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.RETURN);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 7_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-7_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(2)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(7_300_00, 2));
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(200_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _91_03_prepays_aftOrderConfirm_confirms_2of2_failOnExpertiseCleaning_1of2() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(2)
                .confirmPositions(ImmutableList.of(1, 2))
                .refusePositions(Collections.emptyList())
                .prepayAftOrderConfirm(true)

                .expertisePassPositions(ImmutableList.of(2))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Lists.newArrayList(100L, null))

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.RETURN);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 22_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-22_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(2)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(100_00, 2));
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(22_400_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _04_02_prepays_befOrderConfirm_confirms_1of1_failOnExpertiseCleaning_1of1() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(1)
                .prepayBefOrderConfirm(true)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .cleaningsByPositions(ImmutableList.of(200L))
                .defectsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.RETURN);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 7_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-7_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(2)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(7_300_00, 2));
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(200_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _91_02_prepays_befOrderConfirm_confirms_2of2_failOnExpertiseCleaning_1of2() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(2)
                .prepayBefOrderConfirm(true)
                .confirmPositions(ImmutableList.of(1, 2))
                .refusePositions(Collections.emptyList())

                .cleaningsByPositions(Lists.newArrayList(100L, null))
                .expertisePassPositions(ImmutableList.of(2))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.RETURN);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 22_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-22_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(2)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(100_00, 2));
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(22_400_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _02_03_prepays_aftOrderConfirm_confirms_1of1_expertise_0of1() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(1)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())
                .prepayAftOrderConfirm(true)

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(1))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.REFUND);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 7_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 10_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_REFUND));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_REFUND);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-7_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(1)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _11_03_prepays_aftOrderConfirm_confirms_2of2_expertise_1of2() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(2)
                .confirmPositions(ImmutableList.of(1, 2))
                .refusePositions(Collections.emptyList())
                .prepayAftOrderConfirm(true)

                .expertisePassPositions(ImmutableList.of(2))
                .expertiseFailPositions(ImmutableList.of(1))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.RETURN);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 22_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 10_000_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-22_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(2)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(15_000_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _02_02_prepays_befOrderConfirm_confirms_1of1_expertise_0of1() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(1)
                .prepayBefOrderConfirm(true)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(1))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.REFUND);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 7_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 10_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_REFUND));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_REFUND);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-7_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(1)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _11_02_prepays_befOrderConfirm_confirms_2of2_expertise_1of2() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(2)
                .prepayBefOrderConfirm(true)
                .confirmPositions(ImmutableList.of(1, 2))
                .refusePositions(Collections.emptyList())

                .expertiseFailPositions(ImmutableList.of(1))
                .expertisePassPositions(ImmutableList.of(2))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.RETURN);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 22_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 10_000_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-22_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(2)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(15_000_00, 2));
                });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _11_01_regular_captureOnPayout_confirms_2of2_expertise_1of2() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(2)

                .confirmPositions(ImmutableList.of(1, 2))
                .refusePositions(Collections.emptyList())

                .expertiseFailPositions(ImmutableList.of(1))
                .expertisePassPositions(ImmutableList.of(2))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.RETURN);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 22_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 10_000_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.valueOf(-22_500_00, 2));
        Assertions.assertThat(orderFlowTestUtils.loadBalanceChanges(orderInfo.getId().toString(), orderInfo.getSeller().getId()))
                .hasSize(2)
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getOperationMode()).isEqualTo(UserBalanceChangeMode.AUTO);
                    Assertions.assertThat(it.getOperationType()).isEqualTo(UserBalanceChangeType.DEBIT);
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(15_000_00, 2));
                });
    }

    private void _13_00_refundOnReturnExecuteAndValidate(OrderDTO orderInfo) {
        orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), BigDecimal.ZERO);
        //
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.MONEY_TRANSFERRED);
        //
        orderFlowTestUtils.refundOnReturnAuto(orderInfo.getId());
        rollbackAndStartNewTransaction();
        //
        Order ordersRefund = orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.REFUND);
        Assertions.assertThat(ordersRefund.getRefundReason()).isNotNull().satisfies(it -> Assertions.assertThat(it.getName()).isEqualTo(OrderRefundReasonType.EXCLUDED_FROM_AGENT_REPORT));
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 7_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_ADVANCE, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_ADVANCE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE));
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null)).isEqualByComparingTo(BigDecimal.valueOf(-7_500_00, 2));
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _13_01_regular_captureOnPayout_confirms_1of1_expertise_1of1_refundOnReturn() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(1)

                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertiseFailPositions(Collections.emptyList())
                .expertisePassPositions(ImmutableList.of(1))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .stopPosition(OrderFlowTestUtils.TestStopPosition.EXPERTISE_CALC_DONE)

                .build()
        );
        _13_00_refundOnReturnExecuteAndValidate(orderInfo);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _13_02_prepays_befOrderConfirm_confirms_1of1_expertise_1of1_refundOnReturn() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(1)
                .prepayBefOrderConfirm(true)

                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertiseFailPositions(Collections.emptyList())
                .expertisePassPositions(ImmutableList.of(1))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .stopPosition(OrderFlowTestUtils.TestStopPosition.EXPERTISE_CALC_DONE)

                .build()
        );
        _13_00_refundOnReturnExecuteAndValidate(orderInfo);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _13_03_prepays_aftOrderConfirm_confirms_1of1_expertise_1of1_refundOnReturn() {
        OrderDTO orderInfo = conciergeOrderTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(1)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())
                .prepayAftOrderConfirm(true)

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .stopPosition(OrderFlowTestUtils.TestStopPosition.EXPERTISE_CALC_DONE)

                .build()
        );
        _13_00_refundOnReturnExecuteAndValidate(orderInfo);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _14_01_regular_confirms_1of1_expertise_1of1_payoutAfterExpertise() {
        OrderDTO orderInfo = conciergeOrderSkipPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(1)
                .prepayBefOrderConfirm(false)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.COMPLETED);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 7_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.ZERO);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _14_02_regular_confirms_2of2_expertise_1of2_payoutAfterExpertise() {
        OrderDTO orderInfo = conciergeOrderSkipPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(2)
                .prepayBefOrderConfirm(false)
                .confirmPositions(ImmutableList.of(1, 2))
                .refusePositions(Collections.emptyList())

                .expertiseFailPositions(ImmutableList.of(1))
                .expertisePassPositions(ImmutableList.of(2))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.COMPLETED);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 20_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 15_000_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 10_000_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.ZERO);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _14_03_regular_confirms_1of2_expertise_1of2_payoutAfterExpertise() {
        OrderDTO orderInfo = conciergeOrderSkipPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(2)
                .prepayBefOrderConfirm(false)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(ImmutableList.of(2))

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.COMPLETED);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 20_000_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 7_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.ZERO);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _14_04_regular_confirms_1of1_expertise_0of1_payoutAfterExpertise() {
        OrderDTO orderInfo = conciergeOrderSkipPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(1)
                .prepayBefOrderConfirm(false)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(1))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.REFUND);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 10_500_00);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.ZERO);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _14_05_prepays_aftOrderConfirm_confirms_1of1_expertise_1of1() {
        OrderDTO orderInfo = conciergeOrderSkipPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(1)
                .prepayAftOrderConfirm(true)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.COMPLETED);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 7_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.ZERO);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _14_06_prepays_aftOrderConfirm_confirms_1of2_expertise_1of2() {
        OrderDTO orderInfo = conciergeOrderSkipPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(2)
                .prepayAftOrderConfirm(true)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(ImmutableList.of(2))

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build()
        );
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.COMPLETED);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 30_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 20_000_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_500_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 7_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_ADVANCE);
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo, FiscalReceiptRequestType.DELIVERY_PAYMENT);
        //
        Assertions.assertThat(orderFlowTestUtils.adjustUserBalanceToValue(orderInfo.getSeller().getId(), null))
                .isEqualByComparingTo(BigDecimal.ZERO);
    }

}
