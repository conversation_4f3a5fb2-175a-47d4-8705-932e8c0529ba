package ru.oskelly.tests.pr.suite6_3.orderflow;

import com.google.common.collect.Lists;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.OrderFlowTest;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestTcbMock;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.domain.model.attribute.Attribute;
import su.reddot.domain.model.attribute.AttributeKind;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.attribute.AttributeService;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.dto.order.OrderPositionDTO;
import su.reddot.domain.service.markingcode.MarkingCodeConfigService;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.bank.jobs.AgentPaymentJobs;
import su.reddot.infrastructure.logistic.DeliveryState;
import su.reddot.infrastructure.util.CallInTransaction;
import su.reddot.oskelly.orderprocessing.internal.web.dto.IntegrationMobileOrderExpertiseDTO;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
@TestMethodOrder(MethodOrderer.MethodName.class)
public class OrderFlowMarkingCodesTest extends OrderFlowTest {

    @Autowired
    private UserService userService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private AgentPaymentJobs agentPaymentJobs;

    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private CartTestSupport cartTestSupport;
    @Autowired
    protected CallInTransaction callInTransaction;

    @Autowired
    private MarkingCodeConfigService markingCodeConfigService;
    @Autowired
    private AttributeService attributeService;

    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user-password}")
    private String password;

    @Value("${test-prepayments.agent-seller-id}")
    private Long agentSellerId;
    @Value("${test-prepayments.agent-seller-counterparty-id}")
    private Long agentSellerCounterpartyId;


    @Value("${test-prepayments.usual-seller-id}")
    private Long usualSellerId;
    @Value("${test-prepayments.usual-seller-counterparty-id}")
    private Long usualSellerCounterpartyId;

    @Value("${test-prepayments.pickup-id}")
    private Long pickupId;
    @Value("${test-prepayments.delivery-id}")
    private Long deliveryId;

    private static OrderFlowTestTcbMock orderFlowTestTcbMock;

    private static final String SELLER_PASSWORD = "SellerPassword";

    private ApiV2Client sellerAgentApiV2Client;
    private ApiV2Client sellerUsualApiV2Client;

    @Value("${test.receipts.mock-server-host}")
    private String mockServerHost;
    @Value("${test.receipts.mock-server-tcb-bank-port}")
    private Integer mockTcbServerPort;


    final static String codeWithGsUpperRawInput = "010461004683731521'Xdt+WtLaM;sc<GS>91003E<GS>92____MarkingCodesTest_WithGsUpper____zMNFSIAiP3KAGDxH65q/vOHzse/ra5bGkV3fGXoDG1evfU07zA==";
    final static String codeWithGsCamelRawInput = "010461004683731521'Xdt+WtLaM;sc<gS>91003E<Gs>92____MarkingCodesTest_WithGsUpper____zMNFSIAiP3KAGDxH65q/vOHzse/ra5bGkV3fGXoDG1evfU07zA==";
    final static String codeWithGsCamelValidate = "010461004683731521'Xdt+WtLaM;sc<GS>91003E<GS>92____MarkingCodesTest_WithGsUpper____zMNFSIAiP3KAGDxH65q/vOHzse/ra5bGkV3fGXoDG1evfU07zA==";
    final static String codeWithGsLowerRawInput = "010461004683731521'Xdt+WtLaM;sc<gs>91003E<gs>92____MarkingCodesTest_WithGsLower____zMNFSIAiP3KAGDxH65q/vOHzse/ra5bGkV3fGXoDG1evfU07zA==";
    final static String codeWithGsLowerValidate = "010461004683731521'Xdt+WtLaM;sc<GS>91003E<GS>92____MarkingCodesTest_WithGsLower____zMNFSIAiP3KAGDxH65q/vOHzse/ra5bGkV3fGXoDG1evfU07zA==";
    final static String codeWithGsEmptyRawInput = "010461004683731521'Xdt+WtLaM;sc"+ "91003E"+ "92____MarkingCodesTest_WithGsEmpty____zMNFSIAiP3KAGDxH65q/vOHzse/ra5bGkV3fGXoDG1evfU07zA==";
    final static String codeWithGsEmptyValidate = "010461004683731521'Xdt+WtLaM;sc<GS>91003E<GS>92____MarkingCodesTest_WithGsEmpty____zMNFSIAiP3KAGDxH65q/vOHzse/ra5bGkV3fGXoDG1evfU07zA==";


    private Long prepareAdminsUser() {
        User adminsUser = userService.getUserByEmail(buyerEmail);
        orderFlowTestUtils.enableUserAuthority(adminsUser.getId(), AuthorityName.ORDER_PAYOUTS, true);
        orderFlowTestUtils.enableUserAuthority(adminsUser.getId(), AuthorityName.ORDER_MARKING_CODES, true);
        return adminsUser.getId();
    }

    private String prepareSellerUser(long sellerId) {
        User sellerUser = userService.getUserById(sellerId).orElse(null);
        userService.setPassword(sellerUser, SELLER_PASSWORD);
        return sellerUser.getEmail();
    }

    @PostConstruct
    private void init() {
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(TcbBankService.SCHEMA));
        User buyer = userService.getUserByEmail(buyerEmail);
        ApiV2Client apiV2Client = new ApiV2Client(buyerEmail, password);
        orderFlowTestUtils.init(buyerEmail, password);
        cartTestSupport.setUserId(buyer.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
        orderFlowTestTcbMock = Objects.isNull(orderFlowTestTcbMock) ? new OrderFlowTestTcbMock(mockServerHost, mockTcbServerPort) : orderFlowTestTcbMock;
        callInTransaction.runInNewTransaction(this::prepareAdminsUser);
        String agentSellersEmail = callInTransaction.runInNewTransaction(() -> prepareSellerUser(agentSellerId));
        sellerAgentApiV2Client = new ApiV2Client(agentSellersEmail, SELLER_PASSWORD);
        String usualSellersEmail = callInTransaction.runInNewTransaction(() -> prepareSellerUser(usualSellerId));
        sellerUsualApiV2Client = new ApiV2Client(usualSellersEmail, SELLER_PASSWORD);
        callInTransaction.runInNewTransaction(() -> orderFlowTestUtils.prepareAgentSellerData(agentSellerCounterpartyId));
        callInTransaction.runInNewTransaction(() -> orderFlowTestUtils.prepareUsualSellerData(usualSellerCounterpartyId));
        Mockito.when(orderMobileApi.getMobileOrderExpertise(Mockito.any()))
                .thenReturn(new IntegrationMobileOrderExpertiseDTO().items(Collections.emptyList()));
    }

    @AfterAll
    public static void done() {
        orderFlowTestTcbMock.stop();
        orderFlowTestTcbMock = null;
    }

    @BeforeEach
    public void eachInit() {
        orderFlowTestUtils.switchMarkingCode4AllSellers(true);
    }

    @AfterEach
    public void eachDone() {
        orderFlowTestUtils.switchMarkingCode4AllSellers(false);
        markingCodeConfigService.clearMarkingCodesConfig();
    }

    public void _XX_OrderFlow_ItemExpertise(OrderPositionDTO item,
                                            OrderFlowTestUtils.ExpertiseAction expertiseAction,
                                            Long decreaseAmount,
                                            String validateMarkingCode) {
        boolean waitOkay = Objects.isNull(validateMarkingCode) || (expertiseAction == OrderFlowTestUtils.ExpertiseAction.EXPERTISE_REJECT_IT);
        ResponseEntity<String> expertiseRsp1st =  orderFlowTestUtils.adminsApi1expertise(item.getId(), waitOkay, expertiseAction, decreaseAmount);
        if (waitOkay) {
            return;
        }
        Assertions.assertThat(expertiseRsp1st.getStatusCode().is4xxClientError()).isTrue();
        // Fail on formats -> change code
        Exception codeFormatsException = orderFlowTestUtils.readExceptionFromText(expertiseRsp1st.getBody());
        Assertions.assertThat(codeFormatsException.getMessage()).matches("Код маркировки .* не соответствует формату");
        //
        orderFlowTestUtils.adminsApi1SetPositionMarkingCode(item.getId(), "0104610139185736215WeC59UkuTnYK<GS>918093<GS>921jeDY98l9iBKT/jQ4oLrShlzGM/K+xewxlLJFDV0qiBHo9bHGSyBDnvwABBX+COk2+2R6pYvuUra3Gd2wFoZpA==", true);
        //
        // UnConfirmed -> Confirm
        ResponseEntity<String> expertiseRsp2nd =  orderFlowTestUtils.adminsApi1expertise(item.getId(), false, expertiseAction, decreaseAmount);
        Assertions.assertThat(expertiseRsp2nd.getStatusCode().is4xxClientError()).isTrue();
        Exception codeConfirmException = orderFlowTestUtils.readExceptionFromText(expertiseRsp2nd.getBody());
        Assertions.assertThat(codeConfirmException.getMessage()).matches("Заказ .* \\(позиция .*\\): код маркировки не подтвержден");
        //
        orderFlowTestUtils.adminsApi1ApproveMarkingCode(item.getId(), true);
        // Success
        orderFlowTestUtils.adminsApi1expertise(item.getId(), true, expertiseAction, decreaseAmount);
    }

    public List<Product> _XX_OrderFlow_DefaultProductList(long sellerId) {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(sellerId)
                .maxItems(5)
                .build()
        );
        commitAndStartNewTransaction();
        return products;
    }

    public void _XX_OrderFlow_DefaultMarkingCodeConfig() {
        markingCodeConfigService.clearMarkingCodesConfig();
        markingCodeConfigService.makeMarkingCodesConfig(null, null, "RU-SHOES");
        commitAndStartNewTransaction();
    }

    private OrderPositionDTO getOpDto(OrderDTO orderDTO, long productId) {
        return orderDTO.getItems().stream().filter(it -> it.getProductId() == productId).findFirst().orElse(null);
    }

    public long _XX_OrderFlow_ProcessOrder(OrderFlowTestUtils.TestConfig testConfig,
                                           ApiV2Client sellerApiV2Client,
                                           List<Product> products,
                                           Map<Integer, String> markingCodes,
                                           Map<Integer, String> validateCodes) {
        orderFlowTestUtils.fillCart(products);
        //
        OrderService.InitOrderResult testOrder = orderFlowTestUtils.holdOrderWithPromoCodePS(products.get(0).getSeller(), null, TcbBankService.SCHEMA);
        orderFlowTestUtils.callOrderHoldCallback(testOrder.getOrderId(), testOrder.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        OrderDTO orderInfo = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getOrderId(), true);
        Assertions.assertThat(orderInfo.getId()).isNotEqualTo(0);
        //
        Order order = orderService.getOrder(orderInfo.getId());
        Assertions.assertThat(order.getId()).isEqualTo(orderInfo.getId());
        //
        orderFlowTestUtils.changeSellerCounterparty(orderInfo.getId(), testConfig.getSellerCounterpartyId(), HttpStatus.Series.SUCCESSFUL);
        //
        long rejectPositionId = getOpDto(orderInfo, products.get(0).getId()).getId();
        for (int i = 0; i < products.size(); i++) {
            OrderPositionDTO op = getOpDto(orderInfo, products.get(i).getId());
            orderFlowTestUtils.sellerApiConfirmPosition(sellerApiV2Client,
                    true,
                    orderInfo.getId(),
                    op.getId(),
                    op.getId() != rejectPositionId,
                    markingCodes.get(i));
        }
        orderFlowTestUtils.changeAddressEndpoint(orderInfo.getId(), pickupId, deliveryId);
        if (testConfig.getStopPosition() == OrderFlowTestUtils.TestStopPosition.ALL_POSITIONS_CONFIRMATION_DONE) {
            return orderInfo.getId();
        }
        //
        orderFlowTestUtils.takeOurselves(orderInfo.getId(), null);
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.OURSELVES_FROM_SELLER_TO_OFFICE, true);
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE, true);
        //
        OrderPositionDTO orderPosition1 = getOpDto(orderInfo, products.get(0).getId());
        orderFlowTestUtils.adminsApi1expertise(orderPosition1.getId(), true, OrderFlowTestUtils.ExpertiseAction.EXPERTISE_NO_ACTION, null);
        OrderPositionDTO orderPosition2 = getOpDto(orderInfo, products.get(1).getId());
        _XX_OrderFlow_ItemExpertise(orderPosition2, OrderFlowTestUtils.ExpertiseAction.EXPERTISE_PASSED_OK, null, validateCodes.get(1));
        OrderPositionDTO orderPosition3 = getOpDto(orderInfo, products.get(2).getId());
        _XX_OrderFlow_ItemExpertise(orderPosition3, OrderFlowTestUtils.ExpertiseAction.EXPERTISE_REJECT_IT, null, validateCodes.get(2));
        OrderPositionDTO orderPosition4 = getOpDto(orderInfo, products.get(3).getId());
        _XX_OrderFlow_ItemExpertise(orderPosition4, OrderFlowTestUtils.ExpertiseAction.EXPERTISE_DEFECT_IT, 1234L, validateCodes.get(3));
        OrderPositionDTO orderPosition5 = getOpDto(orderInfo, products.get(4).getId());
        _XX_OrderFlow_ItemExpertise(orderPosition5, OrderFlowTestUtils.ExpertiseAction.EXPERTISE_CLEANS_IT, 4321L, validateCodes.get(4));
        if (testConfig.getStopPosition() == OrderFlowTestUtils.TestStopPosition.EXPERTISE_JUST_DONE) {
            return orderInfo.getId();
        }
        //
        ResponseEntity<String> responseBody =  orderFlowTestUtils.adminPanel_Charge(orderInfo.getId());
        Assertions.assertThat(responseBody.getStatusCode()).isEqualTo(HttpStatus.OK);
        //
        orderFlowTestUtils.processHoldComplete(orderInfo);
        //
        orderFlowTestUtils.sendOurselves(orderInfo.getId(), null);
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.OURSELVES_FROM_OFFICE_TO_BUYER, true);
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.DELIVERED_TO_BUYER, true);
        //
        orderFlowTestUtils.sendAgentReport(orderInfo.getId(), true);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.confirmAgentReport(orderInfo.getId(), true);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.prepareSellerPayout(orderInfo.getId(), true);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.loadAgentReport(orderInfo.getId());
        //
        orderFlowTestUtils.transferMoneyToSellers(orderInfo.getId());
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.MONEY_PAYMENT_WAIT);
        //
        orderFlowTestUtils.validateMoneyToSellers(orderInfo.getId());
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.COMPLETED);
        //
        agentPaymentJobs.transferMoneyToSeller();
        //
        return orderInfo.getId();
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_01_OrderFlow_MarkingCodesFlowNullCode_AgentSeller() {
        _XX_OrderFlow_DefaultMarkingCodeConfig();
        List<Product> products = _XX_OrderFlow_DefaultProductList(agentSellerId);
        long ordersId = _XX_OrderFlow_ProcessOrder(
                OrderFlowTestUtils.TestConfig.builder()
                        .sellerCounterpartyId(agentSellerCounterpartyId)
                        .stopPosition(OrderFlowTestUtils.TestStopPosition.EXPERTISE_JUST_DONE)
                .build(),
                sellerAgentApiV2Client,
                products,
                Collections.emptyMap(),
                Collections.emptyMap()
        );
        //
        ResponseEntity<String> responseBody =  orderFlowTestUtils.adminPanel_Charge(ordersId);
        Assertions.assertThat(responseBody.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        Exception chargesException = orderFlowTestUtils.readExceptionFromText(responseBody.getBody());
        Assertions.assertThat(chargesException.getMessage()).matches("Заказ .*: код маркировки не подтвержден");
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_02_OrderFlow_MarkingCodesFlowNullCode_UsualSeller() {
        _XX_OrderFlow_DefaultMarkingCodeConfig();
        List<Product> products = _XX_OrderFlow_DefaultProductList(usualSellerId);
        _XX_OrderFlow_ProcessOrder(
                OrderFlowTestUtils.TestConfig.builder()
                        .sellerCounterpartyId(usualSellerCounterpartyId)
                .build(),
                sellerUsualApiV2Client,
                products,
                Collections.emptyMap(),
                Collections.emptyMap()
        );
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _02_01_OrderFlow_MarkingCodesFlowWithCode_AgentSeller() {
        _XX_OrderFlow_DefaultMarkingCodeConfig();
        List<Product> products = _XX_OrderFlow_DefaultProductList(agentSellerId);
        Map<Integer, String> markingCodes  = new HashMap<Integer, String>() {{
            put(0, "A");
            put(1, "B");
            put(2, "C");
            put(3, "D");
            put(4, "E");
        }};
        _XX_OrderFlow_ProcessOrder(
                OrderFlowTestUtils.TestConfig.builder()
                        .sellerCounterpartyId(agentSellerCounterpartyId)
                .build(),
                sellerAgentApiV2Client,
                products,
                markingCodes,
                markingCodes
        );
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _02_02_OrderFlow_MarkingCodesFlowWithCode_UsualSeller() {
        _XX_OrderFlow_DefaultMarkingCodeConfig();
        List<Product> products = _XX_OrderFlow_DefaultProductList(usualSellerId);
        Map<Integer, String> markingCodes  = new HashMap<Integer, String>() {{
            put(0, "A");
            put(1, "B");
            put(2, "C");
            put(3, "D");
            put(4, "E");
        }};
        _XX_OrderFlow_ProcessOrder(
                OrderFlowTestUtils.TestConfig.builder()
                        .sellerCounterpartyId(usualSellerCounterpartyId)
                .build(),
                sellerUsualApiV2Client,
                products,
                markingCodes,
                Collections.emptyMap()
        );
    }

    private long getMaterialAttributeValueId(Product product) {
        List<Long> materialAttributeIds = attributeService.getAllAttributes().stream()
                .filter(it -> it.getKind() == AttributeKind.MATERIAL)
                .map(Attribute::getId)
                .collect(Collectors.toList());
        Long materialAttrsValueId = product.getAttributeValues().stream()
                .filter(it -> materialAttributeIds.contains(it.getAttributeValue().getAttributeId()))
                .limit(1)
                .findFirst()
                .map(it -> it.getAttributeValue().getId())
                .orElse(null);
        return materialAttrsValueId;
    }

    private List<Product> _03_OrderFlow_ConfigProductList(long sellerId) {
        markingCodeConfigService.clearMarkingCodesConfig();
        //
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(sellerId)
                .maxItems(256)
                .build()
        );
        // Getting arbitrary category value, make config for marking code and remove other products with that category
        Product product3 = products.get(0);
        long categoryId3 = product3.getCategoryId();
        markingCodeConfigService.makeMarkingCodesConfig(categoryId3, null, "RU-SHOES");
        products.removeIf(it -> it.getCategoryId().equals(categoryId3));
        // Getting arbitrary material attribute value, make config for marking code and remove other products with that material
        Product product4 = products.get(0);
        long materialAttrsValueId4 = getMaterialAttributeValueId(product4);
        markingCodeConfigService.makeMarkingCodesConfig(null, materialAttrsValueId4, "RU-SHOES");
        products.removeIf(it -> it.getAttributeValues().stream().anyMatch(attrsValue -> attrsValue.getAttributeValue().getId().equals(materialAttrsValueId4)));
        // Get arbitrary category n material and remove all items with that category n material from list
        Product product2 = products.get(0);
        long categoryId2 = product2.getCategoryId();
        long materialAttrsValueId2 = getMaterialAttributeValueId(product2);
        markingCodeConfigService.makeMarkingCodesConfig(categoryId2, materialAttrsValueId2, "RU-SHOES");
        products.removeIf(it -> it.getCategoryId().equals(categoryId2) && it.getAttributeValues().stream().anyMatch(attrsValue -> attrsValue.getAttributeValue().getId().equals(materialAttrsValueId2)));
        //
        Product product1 = products.get(0);
        Product product5 = products.get(1);
        //
        commitAndStartNewTransaction();
        return Arrays.asList(
                product1, // No marking codes
                product2, // Marking required by category and attr id
                product3, // Marking required by category
                product4, // Marking required by attribute
                product5  // No marking required
        );
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _03_01_OrderFlow_MarkingCodesFlowWithConfig_AgentSeller() {
        List<Product> products = _03_OrderFlow_ConfigProductList(agentSellerId);
        Map<Integer, String> markingCodes  = new HashMap<Integer, String>() {{
            put(0, "A");
            put(1, "B");
            put(2, "C");
            put(3, "D");
            put(4, "E");
        }};
        Map<Integer, String> validateCodes  = new HashMap<Integer, String>() {{
            // 0
            put(1, "B");
            put(2, "C");
            put(3, "D");
            // 4
        }};
        _XX_OrderFlow_ProcessOrder(
                OrderFlowTestUtils.TestConfig.builder()
                        .sellerCounterpartyId(agentSellerCounterpartyId)
                .build(),
                sellerAgentApiV2Client,
                products,
                markingCodes,
                validateCodes
        );
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _03_02_OrderFlow_MarkingCodesFlowWithConfig_UsualSeller() {
        List<Product> products = _03_OrderFlow_ConfigProductList(usualSellerId);
        Map<Integer, String> markingCodes  = new HashMap<Integer, String>() {{
            put(0, "A");
            put(1, "B");
            put(2, "C");
            put(3, "D");
            put(4, "E");
        }};
        _XX_OrderFlow_ProcessOrder(
                OrderFlowTestUtils.TestConfig.builder()
                        .sellerCounterpartyId(usualSellerCounterpartyId)
                .build(),
                sellerUsualApiV2Client,
                products,
                markingCodes,
                Collections.emptyMap()
        );
    }

    private List<Product> _04_OrderFlow_ConfigProductList(long sellerId) {
        markingCodeConfigService.clearMarkingCodesConfig();
        //
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(sellerId)
                .maxItems(256)
                .build()
        );
        // Getting arbitrary category value, make config for marking code and remove other products with that category
        Product product2 = products.get(0);
        long categoryId2 = product2.getCategoryId();
        markingCodeConfigService.makeMarkingCodesConfig(categoryId2, null, "RU-SHOES");
        products.removeIf(it -> it.getCategoryId().equals(categoryId2));
        // Getting arbitrary material attribute value, make config for marking code and remove other products with that material
        Product product3 = products.get(0);
        long materialAttrsValueId3 = getMaterialAttributeValueId(product3);
        markingCodeConfigService.makeMarkingCodesConfig(null, materialAttrsValueId3, "RU-SHOES");
        products.removeIf(it -> it.getAttributeValues().stream().anyMatch(attrsValue -> attrsValue.getAttributeValue().getId().equals(materialAttrsValueId3)));
        // Get arbitrary category n material and remove all items with that category n material from list
        Product product1 = products.get(0);
        long categoryId1 = product1.getCategoryId();
        long materialAttrsValueId1 = getMaterialAttributeValueId(product1);
        markingCodeConfigService.makeMarkingCodesConfig(categoryId1, materialAttrsValueId1, "RU-SHOES");
        products.removeIf(it -> it.getCategoryId().equals(categoryId1) && it.getAttributeValues().stream().anyMatch(attrsValue -> attrsValue.getAttributeValue().getId().equals(materialAttrsValueId1)));
        //
        Product product4 = products.get(0);
        long categoryId4 = product4.getCategoryId();
        markingCodeConfigService.makeMarkingCodesConfig(categoryId4, materialAttrsValueId3, "RU-SHOES");
        products.removeIf(it -> it.getCategoryId().equals(categoryId4));
        //
        Product product5 = products.get(1);
        long materialAttrsValueId5 = getMaterialAttributeValueId(product5);
        markingCodeConfigService.makeMarkingCodesConfig(categoryId2, materialAttrsValueId5, "RU-SHOES");
        products.removeIf(it -> it.getCategoryId().equals(categoryId2));
        products.removeIf(it -> it.getAttributeValues().stream().anyMatch(attrsValue -> attrsValue.getAttributeValue().getId().equals(materialAttrsValueId5)));
        //
        commitAndStartNewTransaction();
        return Arrays.asList(
                product1, // Marking required by category and attr id
                product2, // Marking required by category
                product3, // Marking required by attribute
                product4, // --> Category [+], attr [-]: no marking code
                product5  // --> Category [-], attr [+]: no marking code
        );
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _04_01_OrderFlow_MarkingCodesFlowWithConfig_AgentSeller() {
        List<Product> products = _04_OrderFlow_ConfigProductList(agentSellerId);
        Map<Integer, String> markingCodes  = new HashMap<Integer, String>() {{
            put(0, "A");
            put(1, "B");
            put(2, "C");
            put(3, "D");
            put(4, "E");
        }};
        Map<Integer, String> validateCodes  = new HashMap<Integer, String>() {{
            put(0, "A");
            put(1, "B");
            put(2, "C");
            // 3
            // 4
        }};
        _XX_OrderFlow_ProcessOrder(
                OrderFlowTestUtils.TestConfig.builder()
                        .sellerCounterpartyId(agentSellerCounterpartyId)
                .build(),
                sellerAgentApiV2Client,
                products,
                markingCodes,
                validateCodes
        );
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _04_02_OrderFlow_MarkingCodesFlowWithConfig_UsualSeller() {
        List<Product> products = _04_OrderFlow_ConfigProductList(usualSellerId);
        Map<Integer, String> markingCodes  = new HashMap<Integer, String>() {{
            put(0, "A");
            put(1, "B");
            put(2, "C");
            put(3, "D");
            put(4, "E");
        }};
        _XX_OrderFlow_ProcessOrder(
                OrderFlowTestUtils.TestConfig.builder()
                        .sellerCounterpartyId(usualSellerCounterpartyId)
                .build(),
                sellerUsualApiV2Client,
                products,
                markingCodes,
                Collections.emptyMap()
        );
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _05_01_orderFlow_markingCodesFlow_sellerMarkingCodeReformatOkay() {
        _XX_OrderFlow_DefaultMarkingCodeConfig();
        List<Product> products = _XX_OrderFlow_DefaultProductList(agentSellerId);
        commitAndStartNewTransaction();
        //
        Map<Integer, String> markingCodes  = new HashMap<Integer, String>() {{
            put(0, codeWithGsCamelRawInput); // This Code won`t be saved
            put(1, codeWithGsUpperRawInput);
            put(2, codeWithGsCamelRawInput);
            put(3, codeWithGsLowerRawInput);
            put(4, codeWithGsEmptyRawInput);
        }};
        //
        long orderId = _XX_OrderFlow_ProcessOrder(
                OrderFlowTestUtils.TestConfig.builder()
                        .sellerCounterpartyId(agentSellerCounterpartyId)
                        .stopPosition(OrderFlowTestUtils.TestStopPosition.ALL_POSITIONS_CONFIRMATION_DONE)
                .build(),
                sellerAgentApiV2Client,
                products,
                markingCodes,
                Collections.emptyMap()
        );
        //
        OrderDTO orderInfo = orderFlowTestUtils.loadOrderSuccessfullWithCustomClient(sellerAgentApiV2Client, orderId, true);
        //
        OrderPositionDTO orderPosition0 = getOpDto(orderInfo, products.get(0).getId());
        Assertions.assertThat(orderPosition0.getDatamatrix()).isNull();
        OrderPositionDTO orderPosition1 = getOpDto(orderInfo, products.get(1).getId());
        Assertions.assertThat(orderPosition1.getDatamatrix()).isEqualTo(codeWithGsUpperRawInput);
        OrderPositionDTO orderPosition2 = getOpDto(orderInfo, products.get(2).getId());
        Assertions.assertThat(orderPosition2.getDatamatrix()).isEqualTo(codeWithGsCamelValidate);
        OrderPositionDTO orderPosition3 = getOpDto(orderInfo, products.get(3).getId());
        Assertions.assertThat(orderPosition3.getDatamatrix()).isEqualTo(codeWithGsLowerValidate);
        OrderPositionDTO orderPosition4 = getOpDto(orderInfo, products.get(4).getId());
        Assertions.assertThat(orderPosition4.getDatamatrix()).isEqualTo(codeWithGsEmptyValidate);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _05_02_orderFlow_markingCodesFlow_sellerMarkingCodeRawInputOkay() {
        _XX_OrderFlow_DefaultMarkingCodeConfig();
        List<Product> products = _XX_OrderFlow_DefaultProductList(agentSellerId);
        commitAndStartNewTransaction();
        //
        final String position0CodeWontSave = "010461004683731521'Xdt+WtLaM;sc<GS>91003E<GS>92/W3ktzM2NmvKf5kI6w8gmyZ2RAmvfWUip8iszMNFSIAiP3KAGDxH65q/vOHzse/ra5bGkV3fGXoDG1evfU07zA==";
        final String position1stCode4mUUID = UUID.randomUUID().toString();
        final String position2ndCode4mUUID = UUID.randomUUID().toString();
        final String position3rdCode4mUUID = UUID.randomUUID().toString();
        final String position4thCode4mUUID = UUID.randomUUID().toString();
        //
        Map<Integer, String> markingCodes  = new HashMap<Integer, String>() {{
            put(0, position0CodeWontSave); // This Code won`t be saved
            put(1, position1stCode4mUUID);
            put(2, position2ndCode4mUUID);
            put(3, position3rdCode4mUUID);
            put(4, position4thCode4mUUID);
        }};
        //
        long orderId = _XX_OrderFlow_ProcessOrder(
                OrderFlowTestUtils.TestConfig.builder()
                        .sellerCounterpartyId(agentSellerCounterpartyId)
                        .stopPosition(OrderFlowTestUtils.TestStopPosition.ALL_POSITIONS_CONFIRMATION_DONE)
                .build(),
                sellerAgentApiV2Client,
                products,
                markingCodes,
                Collections.emptyMap()
        );
        //
        OrderDTO orderInfo = orderFlowTestUtils.loadOrderSuccessfullWithCustomClient(sellerAgentApiV2Client, orderId, true);
        //
        OrderPositionDTO orderPosition0 = getOpDto(orderInfo, products.get(0).getId());
        Assertions.assertThat(orderPosition0.getDatamatrix()).isNull();
        OrderPositionDTO orderPosition1 = getOpDto(orderInfo, products.get(1).getId());
        Assertions.assertThat(orderPosition1.getDatamatrix()).isEqualTo(position1stCode4mUUID);
        OrderPositionDTO orderPosition2 = getOpDto(orderInfo, products.get(2).getId());
        Assertions.assertThat(orderPosition2.getDatamatrix()).isEqualTo(position2ndCode4mUUID);
        OrderPositionDTO orderPosition3 = getOpDto(orderInfo, products.get(3).getId());
        Assertions.assertThat(orderPosition3.getDatamatrix()).isEqualTo(position3rdCode4mUUID);
        OrderPositionDTO orderPosition4 = getOpDto(orderInfo, products.get(4).getId());
        Assertions.assertThat(orderPosition4.getDatamatrix()).isEqualTo(position4thCode4mUUID);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _06_01_orderFlow_markingCodesFlow_adminsMarkingCodeReFormatOkay() {
        _XX_OrderFlow_DefaultMarkingCodeConfig();
        List<Product> products = _XX_OrderFlow_DefaultProductList(agentSellerId);
        commitAndStartNewTransaction();
        //
        final String position0CodeWontSave = "010461004683731521'Xdt+WtLaM;sc<GS>91003E<GS>92/W3ktzM2NmvKf5kI6w8gmyZ2RAmvfWUip8iszMNFSIAiP3KAGDxH65q/vOHzse/ra5bGkV3fGXoDG1evfU07zA==";
        final String position1stCode4mUUID = UUID.randomUUID().toString();
        final String position2ndCode4mUUID = UUID.randomUUID().toString();
        final String position3rdCode4mUUID = UUID.randomUUID().toString();
        final String position4thCode4mUUID = UUID.randomUUID().toString();
        //
        Map<Integer, String> markingCodes  = new HashMap<Integer, String>() {{
            put(0, position0CodeWontSave); // This Code won`t be saved
            put(1, position1stCode4mUUID);
            put(2, position2ndCode4mUUID);
            put(3, position3rdCode4mUUID);
            put(4, position4thCode4mUUID);
        }};
        //
        long orderId = _XX_OrderFlow_ProcessOrder(
                OrderFlowTestUtils.TestConfig.builder()
                        .sellerCounterpartyId(agentSellerCounterpartyId)
                        .stopPosition(OrderFlowTestUtils.TestStopPosition.ALL_POSITIONS_CONFIRMATION_DONE)
                .build(),
                sellerAgentApiV2Client,
                products,
                markingCodes,
                Collections.emptyMap()
        );
        //
        OrderDTO orderInfoCodesAsIs = orderFlowTestUtils.loadOrderSuccessfullWithCustomClient(sellerAgentApiV2Client, orderId, true);
        List<OrderPositionDTO> opsCodesAsIs = products.stream().map(it -> getOpDto(orderInfoCodesAsIs, it.getId())).collect(Collectors.toList());
        //
        Assertions.assertThat(opsCodesAsIs.get(0).getDatamatrix()).isNull();
        Assertions.assertThat(opsCodesAsIs.get(1).getDatamatrix()).isEqualTo(position1stCode4mUUID);
        Assertions.assertThat(opsCodesAsIs.get(2).getDatamatrix()).isEqualTo(position2ndCode4mUUID);
        Assertions.assertThat(opsCodesAsIs.get(3).getDatamatrix()).isEqualTo(position3rdCode4mUUID);
        Assertions.assertThat(opsCodesAsIs.get(4).getDatamatrix()).isEqualTo(position4thCode4mUUID);
        //
        orderFlowTestUtils.adminPanel_setOrderPositionMarkingCode(opsCodesAsIs.get(1).getId(), codeWithGsUpperRawInput, HttpStatus.Series.SUCCESSFUL);
        orderFlowTestUtils.adminPanel_setOrderPositionMarkingCode(opsCodesAsIs.get(2).getId(), codeWithGsCamelRawInput, HttpStatus.Series.SUCCESSFUL);
        orderFlowTestUtils.adminPanel_setOrderPositionMarkingCode(opsCodesAsIs.get(3).getId(), codeWithGsLowerRawInput, HttpStatus.Series.SUCCESSFUL);
        orderFlowTestUtils.adminPanel_setOrderPositionMarkingCode(opsCodesAsIs.get(4).getId(), codeWithGsEmptyRawInput, HttpStatus.Series.SUCCESSFUL);
        //
        OrderDTO orderInfoCodesDone = orderFlowTestUtils.loadOrderSuccessfullWithCustomClient(sellerAgentApiV2Client, orderId, true);
        List<OrderPositionDTO> opsCodesDone = products.stream().map(it -> getOpDto(orderInfoCodesDone, it.getId())).collect(Collectors.toList());
        //
        Assertions.assertThat(opsCodesDone.get(0).getDatamatrix()).isNull();
        Assertions.assertThat(opsCodesDone.get(1).getDatamatrix()).isEqualTo(codeWithGsUpperRawInput);
        Assertions.assertThat(opsCodesDone.get(2).getDatamatrix()).isEqualTo(codeWithGsCamelValidate);
        Assertions.assertThat(opsCodesDone.get(3).getDatamatrix()).isEqualTo(codeWithGsLowerValidate);
        Assertions.assertThat(opsCodesDone.get(4).getDatamatrix()).isEqualTo(codeWithGsEmptyValidate);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _06_01_orderFlow_markingCodesFlow_adminsMarkingCodeRawInputFail() {
        _XX_OrderFlow_DefaultMarkingCodeConfig();
        List<Product> products = _XX_OrderFlow_DefaultProductList(agentSellerId);
        commitAndStartNewTransaction();
        //
        final String position0CodeWontSave = UUID.randomUUID().toString();
        final String position1stCode4mUUID = UUID.randomUUID().toString();
        final String position2ndCode4mUUID = UUID.randomUUID().toString();
        final String position3rdCode4mUUID = UUID.randomUUID().toString();
        final String position4thCode4mUUID = UUID.randomUUID().toString();
        //
        final String position1stChangeText = "";
        final String position2ndChangeText = "SomeValueThanFailsToMatch";
        //
        Map<Integer, String> markingCodes  = new HashMap<Integer, String>() {{
            put(0, position0CodeWontSave); // This Code won`t be saved
            put(1, position1stCode4mUUID);
            put(2, position2ndCode4mUUID);
            put(3, position3rdCode4mUUID);
            put(4, position4thCode4mUUID);
        }};
        //
        long orderId = _XX_OrderFlow_ProcessOrder(
                OrderFlowTestUtils.TestConfig.builder()
                        .sellerCounterpartyId(agentSellerCounterpartyId)
                        .stopPosition(OrderFlowTestUtils.TestStopPosition.ALL_POSITIONS_CONFIRMATION_DONE)
                .build(),
                sellerAgentApiV2Client,
                products,
                markingCodes,
                Collections.emptyMap()
        );
        //
        OrderDTO orderInfoCodesAsIs = orderFlowTestUtils.loadOrderSuccessfullWithCustomClient(sellerAgentApiV2Client, orderId, true);
        List<OrderPositionDTO> opsCodesAsIs = products.stream().map(it -> getOpDto(orderInfoCodesAsIs, it.getId())).collect(Collectors.toList());
        //
        Assertions.assertThat(opsCodesAsIs.get(0).getDatamatrix()).isNull();
        Assertions.assertThat(opsCodesAsIs.get(1).getDatamatrix()).isEqualTo(position1stCode4mUUID);
        Assertions.assertThat(opsCodesAsIs.get(2).getDatamatrix()).isEqualTo(position2ndCode4mUUID);
        Assertions.assertThat(opsCodesAsIs.get(3).getDatamatrix()).isEqualTo(position3rdCode4mUUID);
        Assertions.assertThat(opsCodesAsIs.get(4).getDatamatrix()).isEqualTo(position4thCode4mUUID);
        //
        orderFlowTestUtils.adminPanel_setOrderPositionMarkingCode(opsCodesAsIs.get(1).getId(), position1stChangeText, HttpStatus.Series.SUCCESSFUL);
        //
        ResponseEntity<String> op2ndResponseFail = orderFlowTestUtils.adminPanel_setOrderPositionMarkingCode(orderInfoCodesAsIs.getItems().get(2).getId(), position2ndChangeText, HttpStatus.Series.CLIENT_ERROR);
        Exception op2ndResponseException = orderFlowTestUtils.readExceptionFromText(op2ndResponseFail.getBody());
        Assertions.assertThat(op2ndResponseException.getMessage()).matches("Код маркировки .* не соответствует формату");
        //
        OrderDTO orderInfoCodesDone = orderFlowTestUtils.loadOrderSuccessfullWithCustomClient(sellerAgentApiV2Client, orderId, true);
        List<OrderPositionDTO> opsCodesDone = products.stream().map(it -> getOpDto(orderInfoCodesDone, it.getId())).collect(Collectors.toList());
        //
        Assertions.assertThat(opsCodesDone.get(0).getDatamatrix()).isNull();
        Assertions.assertThat(opsCodesDone.get(1).getDatamatrix()).isNull();                            // We`re able to set code to null as an administrator
        Assertions.assertThat(opsCodesDone.get(2).getDatamatrix()).isEqualTo(position2ndCode4mUUID);    // But we can`t change it to wrong code (exception validation before)
        Assertions.assertThat(opsCodesDone.get(3).getDatamatrix()).isEqualTo(position3rdCode4mUUID);    // Old codes are free for experiments
        Assertions.assertThat(opsCodesDone.get(4).getDatamatrix()).isEqualTo(position4thCode4mUUID);    // And stays same
    }

}
