package ru.oskelly.tests.pr.suite6_3.orderflow;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.OrderFlowTest;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestFixtures;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestTcbMock;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.order.OrderSourceInfo;
import su.reddot.domain.model.stock.Stock;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.ordersourceinfo.OrderSourceInfoService;
import su.reddot.domain.service.stock.StockService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.BoutiqueBankService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.util.CallInTransaction;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
@TestMethodOrder(MethodOrderer.MethodName.class)
public class OrderFlowAgentReportTest extends OrderFlowTest {

    @Autowired
    private UserService userService;

    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private CartTestSupport cartTestSupport;
    @Autowired
    private OrderFlowTestFixtures orderFlowTestFixtures;
    @Autowired
    private CallInTransaction callInTransaction;
    @Autowired
    private StockService stockService;
    @Autowired
    private OrderSourceInfoService orderSourceInfoService;

    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user-password}")
    private String password;
    @Value("${test-prepayments.usual-seller-id}")
    private Long usualSellerId;
    @Value("${test-prepayments.usual-seller-counterparty-id}")
    private Long usualSellerCounterpartyId;
    @Value("${test-prepayments.agent-seller-counterparty-id}")
    private Long agentSellerCounterpartyId;
    @Value("${test-prepayments.pickup-id}")
    private Long pickupId;
    @Value("${test-prepayments.delivery-id}")
    private Long deliveryId;
    @Value("${test.receipts.mock-server-host}")
    private String mockServerHost;
    @Value("${test.receipts.mock-server-tcb-bank-port}")
    private Integer mockTcbServerPort;
    @Value("${test-boutique.buyer-id}")
    private Long boutiqueBuyerId;

    private final String boutiqueBuyerPassword = "password4boutique";
    private ApiV2Client apiV2Client;
    private User boutiqueUser;
    private static OrderFlowTestTcbMock orderFlowTestTcbMock;

    private Long prepareAdminUser() {
        User adminUser = userService.getUserByEmail(buyerEmail);
        orderFlowTestUtils.enableUserAuthority(adminUser.getId(), AuthorityName.ORDER_PREPAYMENTS, true);
        orderFlowTestUtils.enableUserAuthority(adminUser.getId(), AuthorityName.ORDER_MANUAL_CHANGE_DELIVERY_STATE, true);
        return adminUser.getId();
    }

    @PostConstruct
    private void init() {
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(TcbBankService.SCHEMA));
        orderFlowTestUtils.init(buyerEmail, password);
        User buyer = userService.getUserByEmail(buyerEmail);
        ApiV2Client apiV2Client = new ApiV2Client(buyerEmail, password);
        cartTestSupport.setUserId(buyer.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
        orderFlowTestTcbMock = Objects.isNull(orderFlowTestTcbMock) ? new OrderFlowTestTcbMock(mockServerHost, mockTcbServerPort) : orderFlowTestTcbMock;
        callInTransaction.runInNewTransaction(this::prepareAdminUser);
        callInTransaction.runInNewTransaction(() -> orderFlowTestUtils.prepareAgentSellerData(agentSellerCounterpartyId));
        callInTransaction.runInNewTransaction(() -> orderFlowTestUtils.prepareUsualSellerData(usualSellerCounterpartyId));
    }

    @AfterAll
    public static void done() {
        orderFlowTestTcbMock.stop();
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _00_checkUsualSellerAgentReportConfirmIfHasDisput() {
        List<Long> productIds = orderFlowTestUtils.createProducts(usualSellerId, 10000, 1);
        commitAndStartNewTransaction();
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)
                .productIdsList(productIds)

                .itemsCount(1)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())
                        .stopPosition(OrderFlowTestUtils.TestStopPosition.CONFIRM_AGENT_REPORT)

                .build());

        orderFlowTestUtils.setHasDispute(orderInfo.getId(), true, true);
        ResponseEntity<String> confirmFail =  orderFlowTestUtils.confirmAgentReport(orderInfo.getId(), false);

        Exception confirmResponseException = orderFlowTestUtils.readExceptionFromText(confirmFail.getBody());
        Assertions.assertThat(confirmResponseException.getMessage()).matches("Не удается подтвердить отчет агента для заказа .*: открыт спор");
        //
        orderFlowTestUtils.setHasDispute(orderInfo.getId(), false, true);
        orderFlowTestUtils.confirmAgentReport(orderInfo.getId(), true);
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _02_checkUsualSellerAgentReportConfirmSuccess() {
        List<Long> productIds = orderFlowTestUtils.createProducts(usualSellerId, 10000, 1);
        commitAndStartNewTransaction();
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)
                .productIdsList(productIds)

                .itemsCount(1)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())
                .stopPosition(OrderFlowTestUtils.TestStopPosition.CONFIRM_AGENT_REPORT)

                .build());

        orderFlowTestUtils.confirmAgentReport(orderInfo.getId(), true);
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _03_checkBotiqueSellerAgentReportConfirmIfHasDisput() {
        boutiqueUser = callInTransaction.runInNewTransaction(() -> orderFlowTestFixtures.prepareBoutiqueUser(boutiqueBuyerId, boutiqueBuyerPassword));
        apiV2Client = new ApiV2Client(boutiqueUser.getEmail(), boutiqueBuyerPassword);
        orderFlowTestUtils.init(boutiqueUser.getEmail(), boutiqueBuyerPassword);
        cartTestSupport.setUserId(boutiqueUser.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
        List<Long> productIds = orderFlowTestUtils.createProducts(usualSellerId, 10000, 1);
        commitAndStartNewTransaction();
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)
                .productIdsList(productIds)
                        .noReceiptsMode(true)

                .itemsCount(1)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())
                        .paymentsSchema(BoutiqueBankService.BOUTIQUE_SCHEMA)

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())
                .stopPosition(OrderFlowTestUtils.TestStopPosition.SEND_AGENT_REPORT)

                .build());

        OrderSourceInfo orderSourceInfo = orderSourceInfoService.findOrderSourceInfoByName(OrderSourceInfo.ORDER_SOURCE_INFO_OSKELLY_STOCK_NAME).orElse(null);
        Stock stock = stockService.findStocksByOrderSourceInfo(orderSourceInfo).stream().findFirst().orElse(null);
        orderFlowTestUtils.sellOrderInBoutique(orderInfo.getId(), true, stock.getOnecUuid());
        //
        orderFlowTestUtils.sendAgentReport(orderInfo.getId(), true);
        //
        orderFlowTestUtils.setHasDispute(orderInfo.getId(), true, true);
        //
        ResponseEntity<String> confirmFail =  orderFlowTestUtils.confirmAgentReport(orderInfo.getId(), false);

        Exception confirmResponseException = orderFlowTestUtils.readExceptionFromText(confirmFail.getBody());
        Assertions.assertThat(confirmResponseException.getMessage()).matches("Не удается подтвердить отчет агента для заказа .*: открыт спор");
    }
}