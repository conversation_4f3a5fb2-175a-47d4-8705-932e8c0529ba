package ru.oskelly.tests.pr.suite6_3.orderflow;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import org.assertj.core.api.Assertions;
import org.assertj.core.data.MapEntry;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.OrderFlowTest;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestTcbMock;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.domain.model.counterparty.Counterparty;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.logistic.Waybill;
import su.reddot.domain.model.logistic.WaybillItem;
import su.reddot.domain.model.logistic.WaybillOrder;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.readerdevicemaskconfig.ReaderDeviceObjectType;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.counterparty.CounterpartyService;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.dto.order.OrderSourceDTO;
import su.reddot.domain.service.dto.order.adminpanel.OrderStateDTO;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.product.item.ProductItemService;
import su.reddot.domain.service.readerdevice.ReaderDeviceMaskConfigService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.util.CallInTransaction;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
@TestMethodOrder(MethodOrderer.MethodName.class)
public class OrderFlowLookupOrderBySomeDataTest extends OrderFlowTest {

    @Autowired
    private UserService userService;
    @Autowired
    private ReaderDeviceMaskConfigService readerDeviceMaskConfigService;

    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private CartTestSupport cartTestSupport;

    @Autowired
    private CallInTransaction callInTransaction;

    @Autowired
    private OrderService orderService;
    @Autowired
    private CounterpartyService counterpartyService;
    @Autowired
    private ProductItemService productItemService;

    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user-password}")
    private String password;
    @Value("${test-prepayments.usual-seller-id}")
    private Long usualSellerId;
    @Value("${test-prepayments.agent-seller-id}")
    private Long agentSellerId;
    @Value("${test-prepayments.usual-seller-counterparty-id}")
    private Long usualSellerCounterpartyId;
    @Value("${test-prepayments.agent-seller-counterparty-id}")
    private Long agentSellerCounterpartyId;
    @Value("${test-prepayments.pickup-id}")
    private Long pickupId;
    @Value("${test-prepayments.delivery-id}")
    private Long deliveryId;
    @Value("${test.receipts.mock-server-host}")
    private String mockServerHost;
    @Value("${test.receipts.mock-server-tcb-bank-port}")
    private Integer mockTcbServerPort;

    private static OrderFlowTestTcbMock orderFlowTestTcbMock;

    private Long prepareAdminUser() {
        User adminUser = userService.getUserByEmail(buyerEmail);
        orderFlowTestUtils.enableUserAuthority(adminUser.getId(), AuthorityName.ORDER_PREPAYMENTS, true);
        orderFlowTestUtils.enableUserAuthority(adminUser.getId(), AuthorityName.ORDER_MANUAL_CHANGE_DELIVERY_STATE, true);
        return adminUser.getId();
    }

    @PostConstruct
    private void init() {
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(TcbBankService.SCHEMA));
        orderFlowTestUtils.init(buyerEmail, password);
        User buyer = userService.getUserByEmail(buyerEmail);
        ApiV2Client apiV2Client = new ApiV2Client(buyerEmail, password);
        cartTestSupport.setUserId(buyer.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
        orderFlowTestTcbMock = Objects.isNull(orderFlowTestTcbMock) ? new OrderFlowTestTcbMock(mockServerHost, mockTcbServerPort) : orderFlowTestTcbMock;
        callInTransaction.runInNewTransaction(this::prepareAdminUser);
        callInTransaction.runInNewTransaction(() -> orderFlowTestUtils.prepareAgentSellerData(agentSellerCounterpartyId));
        callInTransaction.runInNewTransaction(() -> orderFlowTestUtils.prepareUsualSellerData(usualSellerCounterpartyId));
    }

    @AfterAll
    public static void done() {
        orderFlowTestTcbMock.stop();
    }

    @Test
    @Transactional
    public void _01_01_readerDeviceMaskConfigServiceTest_VAT_ID() {
        Map<ReaderDeviceObjectType, String> vatIdRu10Data = readerDeviceMaskConfigService.getReaderDeviceObjects("6658485429");
        Assertions.assertThat(vatIdRu10Data).containsExactly(MapEntry.entry(ReaderDeviceObjectType.VAT_ID, "6658485429"), MapEntry.entry(ReaderDeviceObjectType.PRODUCT_ITEM_ID, "6658485429"));
        Map<ReaderDeviceObjectType, String> vatIdRu12Data = readerDeviceMaskConfigService.getReaderDeviceObjects("056207267540");
        Assertions.assertThat(vatIdRu12Data).containsExactly(MapEntry.entry(ReaderDeviceObjectType.VAT_ID, "056207267540"));
    }

    @Test
    @Transactional
    public void _01_02_readerDeviceMaskConfigServiceTest_ORDER_LOGISTIC() {
        Map<ReaderDeviceObjectType, String> dalliWaybillS20Data = readerDeviceMaskConfigService.getReaderDeviceObjects("1379172-OSKELLY-O2B");
        Assertions.assertThat(dalliWaybillS20Data).containsExactly(MapEntry.entry(ReaderDeviceObjectType.ORDER_LOGISTIC, "1379172-OSKELLY-O2B"));
        Map<ReaderDeviceObjectType, String> dalliWaybillO2BData = readerDeviceMaskConfigService.getReaderDeviceObjects("1390054-OSKELLY-S2O");
        Assertions.assertThat(dalliWaybillO2BData).containsExactly(MapEntry.entry(ReaderDeviceObjectType.ORDER_LOGISTIC, "1390054-OSKELLY-S2O"));
    }

    @Test
    @Transactional
    public void _01_04_readerDeviceMaskConfigServiceTest_PRODUCT_ITEM_BARCODE() {
        Map<ReaderDeviceObjectType, String> productItemBarcode13OneData = readerDeviceMaskConfigService.getReaderDeviceObjects("2000000404110");
        Assertions.assertThat(productItemBarcode13OneData).containsExactly(
                MapEntry.entry(ReaderDeviceObjectType.PRODUCT_ITEM_BARCODE, "2000000404110"),
                MapEntry.entry(ReaderDeviceObjectType.PRODUCT_ITEM_ID, "2000000404110")
        );
        Map<ReaderDeviceObjectType, String> productItemBarcode13TwoData = readerDeviceMaskConfigService.getReaderDeviceObjects("2000000404110");
        Assertions.assertThat(productItemBarcode13TwoData).containsExactly(
                MapEntry.entry(ReaderDeviceObjectType.PRODUCT_ITEM_BARCODE, "2000000404110"),
                MapEntry.entry(ReaderDeviceObjectType.PRODUCT_ITEM_ID, "2000000404110")
        );
    }

    @Test
    @Transactional
    public void _01_05_readerDeviceMaskConfigServiceTest_MARKING_CODE_RU_SHOES() {
        final String codeWithGsUpperNoPref = "010461004683731521'Xdt+WtLaM;sc<GS>91003E<GS>92/W3ktzM2NmvKf5kI6w8gmyZ2RAmvfWUip8iszMNFSIAiP3KAGDxH65q/vOHzse/ra5bGkV3fGXoDG1evfU07zA==";
        final String codeWithGsUpperGsPref = "<GS>" + codeWithGsUpperNoPref;
        final String codeWithGsCamelNoPref = "010461004683731521'Xdt+WtLaM;sc<gS>91003E<Gs>92/W3ktzM2NmvKf5kI6w8gmyZ2RAmvfWUip8iszMNFSIAiP3KAGDxH65q/vOHzse/ra5bGkV3fGXoDG1evfU07zA==";
        final String codeWithGsCamelGsPref = "<Gs>" + codeWithGsCamelNoPref;
        final String codeWithGsLowerNoPref = "010461004683731521'Xdt+WtLaM;sc<gs>91003E<gs>92/W3ktzM2NmvKf5kI6w8gmyZ2RAmvfWUip8iszMNFSIAiP3KAGDxH65q/vOHzse/ra5bGkV3fGXoDG1evfU07zA==";
        final String codeWithGsLowerGsPref = "<gS>" + codeWithGsLowerNoPref;
        final String codeWithGsEmptyNoPref = "010461004683731521'Xdt+WtLaM;sc"+ "91003E"+ "92/W3ktzM2NmvKf5kI6w8gmyZ2RAmvfWUip8iszMNFSIAiP3KAGDxH65q/vOHzse/ra5bGkV3fGXoDG1evfU07zA==";
        final String codeFailWithExtraPref = "_" + codeWithGsUpperNoPref;
        final String codeFailWithExtraPost = codeWithGsUpperNoPref + "_";
        // GS -> GS
        Map<ReaderDeviceObjectType, String> markingCodeWithGsUpperNoPref = readerDeviceMaskConfigService.getReaderDeviceObjects(codeWithGsUpperNoPref);
        Assertions.assertThat(markingCodeWithGsUpperNoPref).contains(MapEntry.entry(ReaderDeviceObjectType.MARKING_CODE, codeWithGsUpperNoPref));
        Map<ReaderDeviceObjectType, String> markingCodeWithGsUpperGsPref = readerDeviceMaskConfigService.getReaderDeviceObjects(codeWithGsUpperGsPref);
        Assertions.assertThat(markingCodeWithGsUpperGsPref).contains(MapEntry.entry(ReaderDeviceObjectType.MARKING_CODE, codeWithGsUpperNoPref));
        // gS -> GS
        Map<ReaderDeviceObjectType, String> markingCodeWithGsCamelNoPref = readerDeviceMaskConfigService.getReaderDeviceObjects(codeWithGsCamelNoPref);
        Assertions.assertThat(markingCodeWithGsCamelNoPref).contains(MapEntry.entry(ReaderDeviceObjectType.MARKING_CODE, codeWithGsUpperNoPref));
        Map<ReaderDeviceObjectType, String> markingCodeWithGsCamelGsPref = readerDeviceMaskConfigService.getReaderDeviceObjects(codeWithGsCamelGsPref);
        Assertions.assertThat(markingCodeWithGsCamelGsPref).contains(MapEntry.entry(ReaderDeviceObjectType.MARKING_CODE, codeWithGsUpperNoPref));
        // gs -> GS
        Map<ReaderDeviceObjectType, String> markingCodeWithGsLowerNoPref = readerDeviceMaskConfigService.getReaderDeviceObjects(codeWithGsLowerNoPref);
        Assertions.assertThat(markingCodeWithGsLowerNoPref).contains(MapEntry.entry(ReaderDeviceObjectType.MARKING_CODE, codeWithGsUpperNoPref));
        Map<ReaderDeviceObjectType, String> markingCodeWithGsLowerGsPref = readerDeviceMaskConfigService.getReaderDeviceObjects(codeWithGsLowerGsPref);
        Assertions.assertThat(markingCodeWithGsLowerGsPref).contains(MapEntry.entry(ReaderDeviceObjectType.MARKING_CODE, codeWithGsUpperNoPref));
        // "" -> GS
        Map<ReaderDeviceObjectType, String> markingCodeWithGsEmptyNoPref = readerDeviceMaskConfigService.getReaderDeviceObjects(codeWithGsEmptyNoPref);
        Assertions.assertThat(markingCodeWithGsEmptyNoPref).contains(MapEntry.entry(ReaderDeviceObjectType.MARKING_CODE, codeWithGsUpperNoPref));
        //
        Map<ReaderDeviceObjectType, String> markingCodeFailWithExtraPref = readerDeviceMaskConfigService.getReaderDeviceObjects(codeFailWithExtraPref);
        Assertions.assertThat(markingCodeFailWithExtraPref.keySet()).doesNotContain(ReaderDeviceObjectType.MARKING_CODE);
        Map<ReaderDeviceObjectType, String> markingCodeFailWithExtraPost = readerDeviceMaskConfigService.getReaderDeviceObjects(codeFailWithExtraPost);
        Assertions.assertThat(markingCodeFailWithExtraPost.keySet()).doesNotContain(ReaderDeviceObjectType.MARKING_CODE);
    }

    @Test
    @Transactional
    public void _01_06_readerDeviceMaskConfigServiceTest_MARKING_CODE_RU_LIGHT_INDUSTRY() {
        final String codeWithGsUpperNoPref = "0104630242416138215oMuqIoMn9TaY<GS>91EE09<GS>92xgaXymySeHTlP+eMZ0WW1GWVrfOLelohGAhEpHPUKR0=";
        final String codeWithGsUpperGsPref = "<GS>" + codeWithGsUpperNoPref;
        final String codeWithGsCamelNoPref = "0104630242416138215oMuqIoMn9TaY<gS>91EE09<Gs>92xgaXymySeHTlP+eMZ0WW1GWVrfOLelohGAhEpHPUKR0=";
        final String codeWithGsCamelGsPref = "<Gs>" + codeWithGsCamelNoPref;
        final String codeWithGsLowerNoPref = "0104630242416138215oMuqIoMn9TaY<gs>91EE09<gs>92xgaXymySeHTlP+eMZ0WW1GWVrfOLelohGAhEpHPUKR0=";
        final String codeWithGsLowerGsPref = "<gS>" + codeWithGsLowerNoPref;
        final String codeWithGsEmptyNoPref = "0104630242416138215oMuqIoMn9TaY"+ "91EE09"+ "92xgaXymySeHTlP+eMZ0WW1GWVrfOLelohGAhEpHPUKR0=";
        final String codeFailWithExtraPref = "_" + codeWithGsUpperNoPref;
        final String codeFailWithExtraPost = codeWithGsUpperNoPref + "_";
        // GS -> GS
        Map<ReaderDeviceObjectType, String> markingCodeWithGsUpperNoPref = readerDeviceMaskConfigService.getReaderDeviceObjects(codeWithGsUpperNoPref);
        Assertions.assertThat(markingCodeWithGsUpperNoPref).contains(MapEntry.entry(ReaderDeviceObjectType.MARKING_CODE, codeWithGsUpperNoPref));
        Map<ReaderDeviceObjectType, String> markingCodeWithGsUpperGsPref = readerDeviceMaskConfigService.getReaderDeviceObjects(codeWithGsUpperGsPref);
        Assertions.assertThat(markingCodeWithGsUpperGsPref).contains(MapEntry.entry(ReaderDeviceObjectType.MARKING_CODE, codeWithGsUpperNoPref));
        // gS -> GS
        Map<ReaderDeviceObjectType, String> markingCodeWithGsCamelNoPref = readerDeviceMaskConfigService.getReaderDeviceObjects(codeWithGsCamelNoPref);
        Assertions.assertThat(markingCodeWithGsCamelNoPref).contains(MapEntry.entry(ReaderDeviceObjectType.MARKING_CODE, codeWithGsUpperNoPref));
        Map<ReaderDeviceObjectType, String> markingCodeWithGsCamelGsPref = readerDeviceMaskConfigService.getReaderDeviceObjects(codeWithGsCamelGsPref);
        Assertions.assertThat(markingCodeWithGsCamelGsPref).contains(MapEntry.entry(ReaderDeviceObjectType.MARKING_CODE, codeWithGsUpperNoPref));
        // gs -> GS
        Map<ReaderDeviceObjectType, String> markingCodeWithGsLowerNoPref = readerDeviceMaskConfigService.getReaderDeviceObjects(codeWithGsLowerNoPref);
        Assertions.assertThat(markingCodeWithGsLowerNoPref).contains(MapEntry.entry(ReaderDeviceObjectType.MARKING_CODE, codeWithGsUpperNoPref));
        Map<ReaderDeviceObjectType, String> markingCodeWithGsLowerGsPref = readerDeviceMaskConfigService.getReaderDeviceObjects(codeWithGsLowerGsPref);
        Assertions.assertThat(markingCodeWithGsLowerGsPref).contains(MapEntry.entry(ReaderDeviceObjectType.MARKING_CODE, codeWithGsUpperNoPref));
        // "" -> GS
        Map<ReaderDeviceObjectType, String> markingCodeWithGsEmptyNoPref = readerDeviceMaskConfigService.getReaderDeviceObjects(codeWithGsEmptyNoPref);
        Assertions.assertThat(markingCodeWithGsEmptyNoPref).contains(MapEntry.entry(ReaderDeviceObjectType.MARKING_CODE, codeWithGsUpperNoPref));
        //
        Map<ReaderDeviceObjectType, String> markingCodeFailWithExtraPref = readerDeviceMaskConfigService.getReaderDeviceObjects(codeFailWithExtraPref);
        Assertions.assertThat(markingCodeFailWithExtraPref.keySet()).doesNotContain(ReaderDeviceObjectType.MARKING_CODE);
        Map<ReaderDeviceObjectType, String> markingCodeFailWithExtraPost = readerDeviceMaskConfigService.getReaderDeviceObjects(codeFailWithExtraPost);
        Assertions.assertThat(markingCodeFailWithExtraPost.keySet()).doesNotContain(ReaderDeviceObjectType.MARKING_CODE);
    }

    @Test
    @Transactional
    public void _01_05_readerDeviceMaskConfigServiceTest_MARKING_CODE_LOOKUP_PART() {
        Map<ReaderDeviceObjectType, String> markingCodesPart1stLookupIds = readerDeviceMaskConfigService.getReaderDeviceObjects("010461004683731521");
        Assertions.assertThat(markingCodesPart1stLookupIds).containsExactly(MapEntry.entry(ReaderDeviceObjectType.MARKING_CODE_LOOKUP_PART, "010461004683731521.*"));
        //
        Map<ReaderDeviceObjectType, String> markingCodesPart2ndLookupIds = readerDeviceMaskConfigService.getReaderDeviceObjects("010461004683731521CodePart");
        Assertions.assertThat(markingCodesPart2ndLookupIds).containsExactly(MapEntry.entry(ReaderDeviceObjectType.MARKING_CODE_LOOKUP_PART, "010461004683731521CodePart.*"));
        //
        Map<ReaderDeviceObjectType, String> markingCodesPart3rdLookupIds = readerDeviceMaskConfigService.getReaderDeviceObjects("010461004683731521CodePart*()");
        Assertions.assertThat(markingCodesPart3rdLookupIds).containsExactly(MapEntry.entry(ReaderDeviceObjectType.MARKING_CODE_LOOKUP_PART, "010461004683731521CodePart\\*\\(\\).*"));
        //
        Map<ReaderDeviceObjectType, String> markingCodesPart4thLookupIds = readerDeviceMaskConfigService.getReaderDeviceObjects("010461004683731522CodePart");
        Assertions.assertThat(markingCodesPart4thLookupIds).isNullOrEmpty();
        //
        Map<ReaderDeviceObjectType, String> markingCodesPart5thLookupIds = readerDeviceMaskConfigService.getReaderDeviceObjects("____010461004683731521Null");
        Assertions.assertThat(markingCodesPart5thLookupIds).isNullOrEmpty();
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _02_orderLookup_byProductItemBarcode() {
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(1)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(1))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build());
        //
        String itemsBarcode = "2000000932774";
        //
        Order order = orderService.getOrder(orderInfo.getId());
        ProductItem productItem = productItemService.findById(order.getOrderPositions().get(0).getProductItem().getId());
        productItem.setBarcode(itemsBarcode);
        productItemService.save(productItem);
        commitTransaction();
        //
        List<Long> lookupOrderIds = orderFlowTestUtils.getApiV2AdminOrdersIds(null, null, itemsBarcode).getData();
        Assertions.assertThat(lookupOrderIds).contains(orderInfo.getId());
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _02_orderLookup_byLogisticsWaybill() {
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(1)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(1))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build());
        //
        String lookupCode = UUID.randomUUID().toString();
        //
        Order order = orderService.getOrder(orderInfo.getId());
        Waybill waybillS2O = order.getLastWaybillFromSeller(true);
        waybillS2O.setExternalSystemId(lookupCode);
        orderService.saveOrder(order);
        commitAndStartNewTransaction();
        //
        List<Long> lookupOrderIds = orderFlowTestUtils.getApiV2AdminOrdersIds(null, null, lookupCode).getData();
        Assertions.assertThat(lookupOrderIds)
                .hasSize(1)
                .contains(orderInfo.getId());
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _02_orderLookup_byLogisticsWaybillOrder() {
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(1)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(1))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build());
        //
        String lookupCode = UUID.randomUUID().toString();
        //
        Order order = orderService.getOrder(orderInfo.getId());
        WaybillOrder waybillOrderS2O = order.getLastWaybillFromSeller(true).getWaybillOrder();
        waybillOrderS2O.setExternalSystemId(lookupCode);
        orderService.saveOrder(order);
        commitAndStartNewTransaction();
        //
        List<Long> lookupOrderIds = orderFlowTestUtils.getApiV2AdminOrdersIds(null, null, lookupCode).getData();
        Assertions.assertThat(lookupOrderIds)
                .hasSize(1)
                .contains(orderInfo.getId());
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _02_orderLookup_byLogisticsWaybillItem() {
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(1)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(1))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build());
        //
        String lookupCode = UUID.randomUUID().toString();
        //
        Order order = orderService.getOrder(orderInfo.getId());
        Waybill waybillS2O = order.getLastWaybillFromSeller(true);
        waybillS2O.getWaybillItems().add(new WaybillItem().setWaybill(waybillS2O).setExternalSystemId(lookupCode));
        orderService.saveOrder(order);
        commitAndStartNewTransaction();
        //
        List<Long> lookupOrderIds = orderFlowTestUtils.getApiV2AdminOrdersIds(null, null, lookupCode).getData();
        Assertions.assertThat(lookupOrderIds)
                .hasSize(1)
                .contains(orderInfo.getId());
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _02_orderLookup_byMarkingCodeFull() {
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(1)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(1))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build());
        //
        String lookupCode = "010461004683731521'Xdt+WtLaM;sc<GS>91003E<GS>92_____" + UUID.randomUUID() + "____" + UUID.randomUUID() + "_____==";
        //
        Order order = orderService.getOrder(orderInfo.getId());
        order.getOrderPositions().get(0).setDatamatrix(lookupCode);
        orderService.saveOrder(order);
        commitAndStartNewTransaction();
        //
        List<Long> lookupOrderIds = orderFlowTestUtils.getApiV2AdminOrdersIds(null, null, lookupCode).getData();
        Assertions.assertThat(lookupOrderIds)
                .hasSize(1)
                .contains(orderInfo.getId());
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _02_orderLookup_byMarkingCodePart() {
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(1)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(1))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build());
        //
        String markingsCode = "010461004683731521CodeData*()ts<GS>91003E<GS>92_____" + UUID.randomUUID() + "____" + UUID.randomUUID() + "_____==";
        //
        Order order = orderService.getOrder(orderInfo.getId());
        order.getOrderPositions().get(0).setDatamatrix(markingsCode);
        orderService.saveOrder(order);
        commitAndStartNewTransaction();
        //
        List<Long> lookupOrdersPart1stIds = orderFlowTestUtils.getApiV2AdminOrdersIds(null, null, "010461004683731521").getData();
        Assertions.assertThat(lookupOrdersPart1stIds).contains(orderInfo.getId());
        //
        List<Long> lookupOrdersPart2ndIds = orderFlowTestUtils.getApiV2AdminOrdersIds(null, null, "010461004683731521CodeData").getData();
        Assertions.assertThat(lookupOrdersPart2ndIds).contains(orderInfo.getId());
        //
        List<Long> lookupOrdersPart3rdIds = orderFlowTestUtils.getApiV2AdminOrdersIds(null, null, "010461004683731521CodeData*").getData();
        Assertions.assertThat(lookupOrdersPart3rdIds).contains(orderInfo.getId());
        //
        List<Long> lookupOrdersPart4thIds = orderFlowTestUtils.getApiV2AdminOrdersIds(null, null, "010461004683731521NullData").getData();
        Assertions.assertThat(lookupOrdersPart4thIds).isNullOrEmpty();
        //
        List<Long> lookupOrdersPart5thIds = orderFlowTestUtils.getApiV2AdminOrdersIds(null, null, "____010461004683731521Null").getData();
        Assertions.assertThat(lookupOrdersPart5thIds).isNullOrEmpty();
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _02_orderLookup_byLogisticsWaybillThatFitsVatId() {
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .sellerCounterpartyId(null)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(1)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(1))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build());
        //
        String lookupCode = "1742099953";
        //
        Order order = orderService.getOrder(orderInfo.getId());
        Waybill waybillS2O = order.getLastWaybillFromSeller(true);
        waybillS2O.setExternalSystemId(lookupCode);
        orderService.saveOrder(order);
        commitAndStartNewTransaction();
        //
        _02_orderLookup_validate(orderInfo, lookupCode);
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _02_orderLookup_byVatId() {
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isAgentSeller(true)
                .agentSellerId(agentSellerId)
                .sellerCounterpartyId(agentSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(1)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(1))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build());
        //
        Counterparty counterparty = counterpartyService.findById(agentSellerCounterpartyId);
        String lookupCode = counterparty.getInn();
        //
        _02_orderLookup_validate(orderInfo, lookupCode);
    }

    public void _02_orderLookup_validate(OrderDTO orderInfo, String lookupCode) {
        OrderDTO actualOrders = orderService.getOrderDTO(orderService.getOrder(orderInfo.getId()));
        //
        List<Long> lookupOrder1st = orderFlowTestUtils.getApiV2AdminOrdersIds(null, null, lookupCode).getData();
        Assertions.assertThat(lookupOrder1st).contains(orderInfo.getId());
        //
        OrderSourceDTO orderSource2nd = actualOrders.getOrderSource();
        List<Long> lookupOrder2nd = orderFlowTestUtils.getApiV2AdminOrdersIds(orderSource2nd, null, lookupCode).getData();
        Assertions.assertThat(lookupOrder2nd).contains(orderInfo.getId());
        //
        List<OrderStateDTO> orderStates3rd = ImmutableList.of(OrderStateDTO.valueOf(actualOrders.getState().name()));
        List<Long> lookupOrder3rd = orderFlowTestUtils.getApiV2AdminOrdersIds(null, orderStates3rd, lookupCode).getData();
        Assertions.assertThat(lookupOrder3rd).contains(orderInfo.getId());
    }

    @Test
    public void _03_orderLookup_variousStates() {
        List<OrderSourceDTO> orderSourcesList = new ArrayList<>(Arrays.asList(OrderSourceDTO.values()));
        orderSourcesList.add(null);
        List<List<OrderStateDTO>> ordersStatesList = Arrays.stream(OrderStateDTO.values()).map(it -> ImmutableList.of(it)).collect(Collectors.toList());
        ordersStatesList.add(null);
        for (OrderSourceDTO orderSource : orderSourcesList) {
            for (List<OrderStateDTO> orderState : ordersStatesList) {
                orderFlowTestUtils.getApiV2AdminOrdersIds(orderSource, orderState, null);
                Assertions.assertThat(true).isTrue();
            }
        }
    }

    @Test
    @Transactional
    public void _04_orderLookup_byProductItemId() {
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(1)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(1))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .build());
        //
        Order order = orderService.getOrder(orderInfo.getId());
        ProductItem productItem = productItemService.findById(order.getOrderPositions().get(0).getProductItem().getId());
        //
        List<Long> lookupOrderIds = orderFlowTestUtils.getApiV2AdminOrdersIds(null, null, productItem.getId().toString()).getData();
        Assertions.assertThat(lookupOrderIds).contains(orderInfo.getId());
    }

}