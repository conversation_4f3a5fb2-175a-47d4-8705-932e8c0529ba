package ru.oskelly.tests.pr.suite6_3.orderflow;

import org.junit.jupiter.api.Disabled;
import org.springframework.beans.factory.annotation.Autowired;
import ru.oskelly.tests.OrderFlowTest;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;

import javax.annotation.PostConstruct;

@Layer
@Disabled("Use this test to return orders")
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
public class OrderFlowOrderReturnTest extends OrderFlowTest {

    private static final String USERNAME = "";
    private static final String PASSWORD = "";

    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;

    @PostConstruct
    private void init() {
        orderFlowTestUtils.init(USERNAME, PASSWORD);
    }

    //@Test
    public void _00_OrderFlow_ReturnOrderListFromCsv() {
        final String ordersCsv = "1338570:170878556,1338777:170917743,1338779:170918040,1338778:170917809,1338739:170913046,1346413:178262451,1338814:170929617,1338815:170929724,1338816:170929785,1338818:170929911,1346584:178281613,1346585:178281673,1346587:178281869,1346593:178282426,1346595:178282645,1338769:170916777,1338775:170917397,1338767:170916517,1338774:170917361,1338772:170917096,1338776:170917604,1338770:170916916,1338773:170917229,1338768:170916631,1338771:170916954,1338757:170915444,1338759:170915635,1338758:170915601,1346409:178262092,1338913:170937560,1338736:170912642,1338813:170929566,1338791:170919224,1338792:170919264,1338793:170919344,1338789:170918947,1338790:170919090,1338795:170919586,1338794:170919541,1355815:186991385,1338867:170934551,1346574:178280815,1346534:178277734,1338696:170907180,1338688:170906276,1338689:170906415,1338684:170905808,1338692:170906632,1338691:170906515,1338675:170904946,1338911:170937299,1338912:170937528,1338787:170918781,1338786:170918643,1338664:170903728,1338666:170903962,1338668:170904187,1338734:170912362,1346515:178270639,1346512:178270537,1338900:170936051,1355836:186993387,1338894:170935681,1338893:170935628,1338892:170935591,1346550:178279006,1358081:188593705,1351900:183458874,1346492:178268874,1344486:176551786,1344472:176549893,1346557:178279490,1346558:178279520,1346559:178279599,1346564:178279962,1346568:178280238,1346569:178280327,1351903:183460538,1351890:183452296,1351904:183461337,1346588:178281954,1346589:178282121,1346591:178282218,1338854:170934076,1338857:170934164,1361519:191337217,1346293:178252768,1344360:176540230,1346516:178270775,1346518:178270858,1358391:188837997,1358392:188838837,1338855:170934107,1338568:170878291,1338538:170875741,1338535:170875451,1338541:170876028,1338534:170875378,1346286:178251763,1338721:170909529,1346578:178281043,1346521:178271049,1346522:178271122,1346577:178280995,1346546:178278698,1346545:178278563,1346544:178278523,1346547:178278752,1346523:178277281,1346529:178277458,1341797:173953016,1344462:176548767,1338981:171008227,1339000:171008270,1339001:171008297,1338917:170945949,1341707:173942831,1338781:170918165,1338782:170918192,1338783:170918331,1346537:178277947,1346541:178278295,1346538:178278087,1346539:178278127,1346530:178277495,1338717:170909339,1338716:170909299,1346533:178277647,1346506:178269941,1346508:178270153,1346509:178270303,1346511:178270392,1338710:170908769,1338711:170908800,1346289:178252461,1338626:170887888,1338491:170861552,1346485:178268200,1338624:170887597,1338623:170887523,1338850:170932735,1338845:170932274,1341697:173942082,1339225:171157529,1344346:176539165,1338600:170881785,1338592:170881003,1338888:170935384,1338916:170945523,1338882:170935139,1338597:170881620,1341784:173951707,1344335:176538258,1344357:176540053,1338708:170908592,1344522:176556054,1344338:176538512,1344455:176548235,1338581:170879561,1338638:170889227,1341749:173947679,1348222:179967421,1346495:178269085,1338760:170915872,1344430:176545984,1344436:176546481,1344505:176554255,1346273:178250801,1346275:178250909,1344420:176545037,1344421:176545204,1344528:176556871,1344525:176556459,1344529:176557012,1344524:176556331,1341800:173953463,1344410:176544328,1344403:176543726,1344396:176543232,1344390:176542879,1344385:176542315,1341626:173927105,1341636:173928023,1341637:173928097,1341640:173928395,1341642:173928598,1344494:176552942,1344513:176554782,1346285:178251670,1346282:178251429,1346283:178251494,1346284:178251613,1346279:178251190,1346280:178251229,1346281:178251386,1346337:178256274,1346375:178259345,1346348:178257241,1346320:178254994,1346388:178260295,1346361:178258276,1346363:178258385,1344517:176555295,1344518:176555533,1344519:176555662,1341737:173946353,1344520:176555790,1341738:173946562,1341816:173955450,1344334:176538109,1341813:173955041,1341729:173945417,1341691:173941440,1341676:173940180,1341686:173940956,1341704:173942586,1342065:174197117,1342095:174200209,1342096:174200238,1342097:174200384,1342099:174200531,1342102:174200888,1342106:174201283,1342124:174203099,1342162:174206574,1342178:174208411,1342187:174209097,1342198:174210012,1342225:174215208,1342226:174215238,1342229:174215528,1342230:174215588,1342250:174217351,1342251:174217499,1342254:174217799,1342256:174217850,1342262:174218433,1342268:174219013,1342271:174219328,1342284:174220516,1342291:174221121,1342302:174222052,1342311:174222714,1342317:174223125,1342321:174223347,1342323:174223462,1342330:174223962,1342336:174224367,1342339:174224661,1342350:174225435,1342357:174226005,1342358:174226093,1342359:174226191,1342361:174226348,1342362:174226394,1342368:174226828,1344799:176602029,1344838:176608474,1344620:176582564,1344712:176593855,1344704:176593046,1344779:176600338,1344806:176603004,1344600:176580796,1344636:176583957,1344767:176599308,1344583:176579368,1344724:176595224,1344725:176595277,1344681:176590562,1344790:176601256,1343860:176037593,1344731:176595918,1344770:176599551,1344638:176584163,1344639:176584192,1344602:176580981,1344603:176581012,1344604:176581174,1344660:176588762,1344858:176608807,1344860:176608845,1344861:176608874,1344714:176594055,1344608:176581538,1344609:176581573,1344634:176583700,1344692:176591651,1344619:176582525,1344622:176582615,1344690:176591430,1344809:176603095,1344576:176578678,1344769:176599393,1344695:176591887,1344590:176580002,1344623:176582742,1344823:176604372,1344824:176604545,1344825:176604583,1344826:176604733,1344710:176593636,1344686:176591021,1344693:176591680,1344597:176580531,1344713:176593983,1344807:176603040,1344626:176582955,1344607:176581438,1344739:176596807,1345240:177155078,1344530:176557251,1346458:178265941,1346435:178264341,1346301:178253380,1346438:178264546,1346294:178252868,1346295:178252901,1346290:178252565,1347413:179057015,1347421:179057370,1346477:178267457,1347590:179246432,1347593:179246670,1347573:179244370,1347575:179245010,1347985:179722081,1348256:179970681,1357054:187885872,1351874:183436283,1351825:183401171,1351817:183399337,1354157:185617379,1357666:188305077,1358144:188619750,1357569:188237740,1361667:191397187";
        for (final String idPairString : ordersCsv.split(",")) {
            final String[] idPair = idPairString.split(":");
            final long orderId = Long.parseLong(idPair[0]);
            final long orderPositionId = Long.parseLong(idPair[1]);
            orderFlowTestUtils.loadAdminOrderSuccessful(null, orderId, false);
            orderFlowTestUtils.excludeFromAgentReport(orderPositionId, true);
            orderFlowTestUtils.refundOnReturn(orderId);
        }
    }
}
