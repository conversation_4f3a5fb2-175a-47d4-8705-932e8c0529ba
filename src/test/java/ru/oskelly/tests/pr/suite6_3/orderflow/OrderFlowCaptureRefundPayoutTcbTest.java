package ru.oskelly.tests.pr.suite6_3.orderflow;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.OrderFlowTest;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestTcbMock;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.domain.model.banktransaction.BankOperation;
import su.reddot.domain.model.banktransaction.OperationType;
import su.reddot.domain.model.banktransaction.TransactionState;
import su.reddot.domain.model.counterparty.Counterparty;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.fiscalreceiptrequest.FiscalReceiptRequestType;
import su.reddot.domain.model.order.OrderPaymentState;
import su.reddot.domain.model.order.OrderPositionState;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.counterparty.CounterpartyService;
import su.reddot.domain.service.dto.BankOperationDTO;
import su.reddot.domain.service.dto.CounterpartyDTO;
import su.reddot.domain.service.dto.order.GroupedCart;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.order.OrderPaymentService;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.BankAccountService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.cashregister.Checkable;
import su.reddot.infrastructure.logistic.DeliveryState;
import su.reddot.infrastructure.util.CallInTransaction;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.UUID;


@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
@TestMethodOrder(MethodOrderer.MethodName.class)
public class OrderFlowCaptureRefundPayoutTcbTest extends OrderFlowTest {

    @Autowired
    private UserService userService;
    @Autowired
    private CounterpartyService counterpartyService;
    @Autowired
    private OrderPaymentService orderPaymentService;

    @Autowired
    private CallInTransaction callInTransaction;
    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private CartTestSupport cartTestSupport;

    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user-password}")
    private String password;
    @Value("${test-prepayments.agent-seller-id}")
    private Long agentSellerId;
    @Value("${test-prepayments.usual-seller-id}")
    private Long usualSellerId;
    @Value("${test-prepayments.agent-seller-counterparty-id}")
    private Long agentSellerCounterpartyId;
    @Value("${test-prepayments.pickup-id}")
    private Long pickupId;
    @Value("${test-prepayments.delivery-id}")
    private Long deliveryId;

    private static OrderFlowTestTcbMock orderFlowTestTcbMock;

    private ApiV2Client apiV2Client;

    @Value("${test-prepayments.usual-seller-counterparty-id}")
    private Long usualSellerCounterpartyId;

    private ObjectMapper objectMapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setSerializationInclusion(JsonInclude.Include.NON_NULL)
            .enable(SerializationFeature.INDENT_OUTPUT);

    @Value("${test.receipts.mock-server-host}")
    private String mockServerHost;
    @Value("${test.receipts.mock-server-tcb-bank-port}")
    private Integer mockTcbServerPort;

    private void prepareUserSecondAgent(long userId) {
        User user = userService.getOne(userId);
        if (user.getCounterparties().stream().anyMatch(it -> Objects.equals(it.getContractNumber(), "CONTRACT-02"))) {
            return;
        }
        CounterpartyDTO secondAgents = counterpartyService.getCounterpartyDTO(counterpartyService.findById(agentSellerCounterpartyId));
        secondAgents.setId(null);
        secondAgents.setContractDate(ZonedDateTime.now());
        secondAgents.setPaymentAccount("00000000000000000000");
        secondAgents.setContractNumber("CONTRACT-02");
        secondAgents.setVatRateIndex(1);
        counterpartyService.save(user, secondAgents);
    }

    private Long prepareAgentSellerData() {
        Counterparty counterparty = counterpartyService.findById(agentSellerCounterpartyId);
        counterparty.setContractTime(LocalDateTime.now());
        counterparty.setContractNumber("CONTRACT-ID");
        counterparty.setVatRateIndex(2);
        counterpartyService.save(counterparty);
        prepareUserSecondAgent(counterparty.getUser().getId());
        return counterparty.getId();
    }

    private Long prepareUsualSellerData() {
        Counterparty counterparty = counterpartyService.findById(usualSellerCounterpartyId);
        counterpartyService.save(counterparty);
        return counterparty.getId();
    }

    private Long prepareAdminUser() {
        User adminUser = userService.getUserByEmail(buyerEmail);
        orderFlowTestUtils.enableUserAuthority(adminUser.getId(), AuthorityName.ORDER_PREPAYMENTS, true);
        orderFlowTestUtils.enableUserAuthority(adminUser.getId(), AuthorityName.ORDER_MANUAL_CHANGE_DELIVERY_STATE, true);
        return adminUser.getId();
    }

    @PostConstruct
    private void init() {
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(TcbBankService.SCHEMA));
        orderFlowTestUtils.init(buyerEmail, password);
        User buyer = userService.getUserByEmail(buyerEmail);
        apiV2Client = new ApiV2Client(buyerEmail, password);
        cartTestSupport.setUserId(buyer.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
        orderFlowTestTcbMock = Objects.isNull(orderFlowTestTcbMock) ? new OrderFlowTestTcbMock(mockServerHost, mockTcbServerPort) : orderFlowTestTcbMock;
        agentSellerCounterpartyId = callInTransaction.runInNewTransaction(this::prepareAgentSellerData);
        usualSellerCounterpartyId = callInTransaction.runInNewTransaction(this::prepareUsualSellerData);
        callInTransaction.runInNewTransaction(this::prepareAdminUser);
    }

    @BeforeEach
    public void before() {
        orderFlowTestUtils.adjustUserBalanceToValue(usualSellerId, BigDecimal.ZERO);
        orderFlowTestUtils.adjustUserBalanceToValue(agentSellerId, BigDecimal.ZERO);
    }

    @AfterAll
    public static void done() {
        orderFlowTestTcbMock.stop();
    }
    
    private static final long ITEM_KIND_ADVANCE = Checkable.Line.LINE_ATTRIBUTE_ADVANCE;
    private static final long ITEM_KIND_COMMODITY = Checkable.Line.LINE_ATTRIBUTE_COMMODITY;
    private static final long ITEM_KIND_SERVICE = Checkable.Line.LINE_ATTRIBUTE_SERVICE;

    private static final long ITEM_PAY_KIND_ADVANCE = Checkable.Line.LINE_PAYMENT_ADVANCE;
    private static final long ITEM_PAY_KIND_FULLPAY = Checkable.Line.LINE_PAYMENT_FULL_PAYMENT;


    private OrderDTO captureRefundPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig testConfig) {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(testConfig.isAgentSeller() ? agentSellerId : usualSellerId)
                .maxItems(testConfig.getItemsCount())
                .build()
        );
        commitAndStartNewTransaction();
        //
        GroupedCart cartInfo = orderFlowTestUtils.fillCart(products.subList(0, testConfig.getItemsCount()));
        OrderDTO cartOrder = cartInfo.getGroup(products.get(0).getSeller().getId());
        //
        OrderService.InitOrderResult testOrder1 = orderFlowTestUtils.holdOrderWithPromoCodePS(products.get(0).getSeller(), null, TcbBankService.SCHEMA);
        rollbackAndStartNewTransaction();
        //
        OrderDTO testOrder = orderFlowTestUtils.loadOrderSuccessfull(testOrder1.getOrderId(), true);
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.HOLD_PROCESSING);
        orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_INPROGRESS);
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(), Lists.newArrayList(OperationType.HOLD));
        //
        orderFlowTestUtils.callOrderHoldCallback(testOrder1.getOrderId(), testOrder1.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.HOLD);
        orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_DONE);
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(), Lists.newArrayList(OperationType.HOLD));
        //
        Long sellerCounterPartyId = testConfig.isAgentSeller() ? agentSellerCounterpartyId : usualSellerCounterpartyId;
        //
        if (testConfig.isPrepayBefOrderConfirm()) {
            captureRefundPayoutTest_executePrepayStep(testOrder, testConfig, FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT);
            captureRefundPayoutTest_validateRefundFail(testOrder);
            orderFlowTestUtils.processHoldComplete(testOrder);
            rollbackAndStartNewTransaction();
        }
        //
        int confirmedPositionsLeft = orderFlowTestUtils.confirmRefuseOrderPositions(testOrder, products, testConfig);
        if (confirmedPositionsLeft == 0) {
            captureRefundPayoutTest_executeRefundStep(testOrder, testConfig);
            return testOrder;
        }
        //
        orderFlowTestUtils.changeAddressEndpoint(testOrder.getId(), pickupId, deliveryId);
        //
        if (testConfig.isAgentSeller()) {
            ResponseEntity<String> takeOurselvesResp = orderFlowTestUtils.takeOurselves(testOrder.getId(), orderFlowTestUtils.getDefaultOskellyDeliveryInfoS2O().toBuilder().callWillFail(true).build());
            Exception takeOurselvesFailInfo = orderFlowTestUtils.readExceptionFromText(takeOurselvesResp.getBody());
            Assertions.assertThat(takeOurselvesFailInfo.getMessage()).matches("Юридическому лицу необходимо выбрать реквизиты для выплаты средств\\.");
        }
        //
        orderFlowTestUtils.changeSellerCounterparty(testOrder.getId(), sellerCounterPartyId, HttpStatus.Series.SUCCESSFUL);
        orderFlowTestUtils.takeOurselves(testOrder.getId(), null);
        //
        if (testConfig.isPrepayAftOrderConfirm()) {
            captureRefundPayoutTest_executePrepayStep(testOrder, testConfig, FiscalReceiptRequestType.AGENT_PREPAYMENT);
            captureRefundPayoutTest_validateRefundFail(testOrder);
            orderFlowTestUtils.processHoldComplete(testOrder);
            rollbackAndStartNewTransaction();
        }
        //
        orderFlowTestUtils.changeDeliveryState(testOrder.getId(), DeliveryState.OURSELVES_FROM_SELLER_TO_OFFICE, true);
        orderFlowTestUtils.changeDeliveryState(testOrder.getId(), DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE, true);
        //
        ResponseEntity<String> refundAmountFailText = orderFlowTestUtils.adminPanel_refundAmount(testOrder.getId(), BigDecimal.valueOf(1_00L, 2), "refundAmountFail", false);
        Assertions.assertThat(refundAmountFailText.getStatusCode().is4xxClientError()).isTrue();
        Exception refundAmountFailInfo = orderFlowTestUtils.readExceptionFromText(refundAmountFailText.getBody());
        Assertions.assertThat(refundAmountFailInfo.getMessage()).matches("Order .*: unable to call refundAmount with states \\[HOLD\\]");
        //
        int itemsLeftExpertiseStep = orderFlowTestUtils.processExpertiseSteps(testOrder, products, testConfig);
        if (itemsLeftExpertiseStep == 0) {
            captureRefundPayoutTest_executeRefundStep(testOrder, testConfig);
            return testOrder;
        }
        rollbackAndStartNewTransaction();
        //
        if (testConfig.isPrepayAfterExpertises()) {
            captureRefundPayoutTest_executePrepayStep(testOrder, testConfig, FiscalReceiptRequestType.AGENT_PREPAYMENT);
            captureRefundPayoutTest_validateRefundFail(testOrder);
            orderFlowTestUtils.processHoldComplete(testOrder);
            rollbackAndStartNewTransaction();
        }
        //
        orderFlowTestUtils.sendOurselves(testOrder.getId(), null);
        rollbackAndStartNewTransaction();
        //
        OrderDTO orderAfterCharge = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getId(), true);
        long defectsCount = orderAfterCharge.getItems().stream() // TODO: use testConfig params to calc this
                .filter(it -> it.getState() == OrderPositionState.VERIFICATION_BAD_STATE).count();
        long expertiseRefundItems = defectsCount + testConfig.getExpertiseFailPositions().size();
        if (expertiseRefundItems > 0) {
            orderFlowTestUtils.processBankOperation(testOrder.getId(), OperationType.REFUND);
            rollbackAndStartNewTransaction();
        }
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.MONEY_TRANSFERRED);
        orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, expertiseRefundItems > 0
                ? OrderPaymentState.REFUND_DONE
                : OrderPaymentState.CAPTURE_DONE);
        //
        List<OperationType> validateOperationsList = Lists.newArrayList(OperationType.HOLD);
        boolean reverseExist = (testConfig.isPrepayAftOrderConfirm() || testConfig.isPrepayAfterExpertises()) && testConfig.getRefusePositions().size() > 0;
        if (reverseExist) {
            validateOperationsList.add(OperationType.HOLD_REVERSE);
        }
        validateOperationsList.add(OperationType.HOLD_COMPLETE);
        if (expertiseRefundItems > 0) {
            validateOperationsList.add(OperationType.REFUND);
        }
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(), validateOperationsList);
        //
        BankAccountService bankAccountService = orderPaymentService.getPaymentService(testOrder.getPayment().getPaymentVersion());
        List<BankOperationDTO> bankOperationsDTOs =  bankAccountService.checkBankOperationsInProgress(Long.MAX_VALUE);
        List<BankOperationDTO> processedDTOList = bankAccountService.handleBankOperations(bankOperationsDTOs);
        commitAndStartNewTransaction();
        //
        List<FiscalReceiptRequestType> validateReceiptsList = Lists.newArrayList(testConfig.isAgentSeller()
                ? (testConfig.isPrepayBefOrderConfirm() ? FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT : FiscalReceiptRequestType.AGENT_PREPAYMENT)
                : FiscalReceiptRequestType.DELIVERY_ADVANCE);
        if (expertiseRefundItems > 0) {
            if (testConfig.isAgentSeller()) {
                validateReceiptsList.add(testConfig.isPrepayBefOrderConfirm() ? FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT_ADJUST : FiscalReceiptRequestType.AGENT_PREPAYMENT_ADJUST);
            }
        }
        orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), validateReceiptsList);
        //
        orderFlowTestUtils.changeDeliveryState(testOrder.getId(), DeliveryState.OURSELVES_FROM_OFFICE_TO_BUYER, true);
        orderFlowTestUtils.changeDeliveryState(testOrder.getId(), DeliveryState.DELIVERED_TO_BUYER, true);
        //
        validateReceiptsList.add(testConfig.isAgentSeller()
                ? FiscalReceiptRequestType.AGENT_PAYMENT
                : FiscalReceiptRequestType.DELIVERY_PAYMENT);
        orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), validateReceiptsList);
                //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.MONEY_TRANSFERRED);
        orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, expertiseRefundItems > 0
                ? OrderPaymentState.REFUND_DONE
                : OrderPaymentState.CAPTURE_DONE);
        //
        orderFlowTestUtils.sendAgentReport(testOrder.getId(), true);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.confirmAgentReport(testOrder.getId(), true);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.prepareSellerPayout(testOrder.getId(), true);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.validateSellerPayout(testOrder.getId(), testConfig.getSellerPayoutAmount(), null, TcbBankService.SCHEMA, TransactionState.PREPARED);
        //
        orderFlowTestUtils.transferMoneyToSellers(testOrder.getId());
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.MONEY_PAYMENT_WAIT);
        //
        orderFlowTestUtils.validateMoneyToSellers(testOrder.getId());
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.transferAgentPaymentsMoneyToSellers();
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        //
        orderFlowTestUtils.validateSellerPayout(testOrder.getId(), testConfig.getSellerPayoutAmount(), null, TcbBankService.SCHEMA, TransactionState.DONE);
        //
        if (!Objects.isNull(testConfig.getRefundAfterCompleteAmount())) {
            String refundComments = this.getClass().getSimpleName() + "-" + UUID.randomUUID();
            //
            orderFlowTestUtils.adminPanel_refundAmount(testOrder.getId(), BigDecimal.valueOf(testConfig.getRefundAfterCompleteAmount(), 2), refundComments, true);
            rollbackAndStartNewTransaction();
            //
            orderFlowTestUtils.processBankOperation(testOrder.getId(), OperationType.REFUND);
            rollbackAndStartNewTransaction();
            //
            BankOperation refundOp = orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.REFUND, testConfig.getRefundAfterCompleteAmount());
            Assertions.assertThat(refundOp.getComment()).isEqualTo(refundComments + " (о)");
            Assertions.assertThat(refundOp.getApiSentAmount()).isEqualTo(testConfig.getRefundAfterCompleteAmount());
        }
        //
        orderFlowTestUtils.returnCompletedOrSoldOrder(testOrder.getId(), true, true, null);
        //
        return testOrder;
    }

    private void captureRefundPayoutTest_executePrepayStep(OrderDTO orderInfo, OrderFlowTestUtils.TestConfig testConfig, FiscalReceiptRequestType agentReceiptWaitType) {
        orderFlowTestUtils.convertHoldToPrepayment(orderInfo.getId(), HttpStatus.Series.SUCCESSFUL);
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.HOLD);
        orderFlowTestUtils.validateOrderPayment(orderInfo.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_INPROGRESS);
        //
        List<OperationType> validateOperations = Lists.newArrayList(OperationType.HOLD, OperationType.HOLD_COMPLETE);
        boolean reverseExist = (testConfig.isPrepayAftOrderConfirm() || testConfig.isPrepayAfterExpertises()) && testConfig.getRefusePositions().size() > 0;
        if (reverseExist) {
            validateOperations = Lists.newArrayList(OperationType.HOLD, OperationType.HOLD_REVERSE, OperationType.HOLD_COMPLETE);
        }
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), validateOperations);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), testConfig.isAgentSeller()
                ? Lists.newArrayList(agentReceiptWaitType)
                : Lists.newArrayList(FiscalReceiptRequestType.DELIVERY_ADVANCE));
    }

    private void captureRefundPayoutTest_validateRefundFail(OrderDTO testOrder) {
        ResponseEntity<String> refundFail = orderFlowTestUtils.adminPanel_refund(testOrder.getId(), false);
        Exception thrownRefund = orderFlowTestUtils.readExceptionFromText(refundFail.getBody());
        Assertions.assertThat(thrownRefund.getMessage()).matches("Order .*: unable to find orderPayment with stage \\[CAPTURE_DONE, REFUND_FAIL, REFUND_DONE\\]");
        rollbackAndStartNewTransaction();
    }

    private void captureRefundPayoutTest_executeRefundStep(OrderDTO testOrder, OrderFlowTestUtils.TestConfig testConfig) {
        orderFlowTestUtils.adminPanel_refund(testOrder.getId(), true);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.processBankOperation(testOrder.getId(), OperationType.REFUND);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.REFUND);
        orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.REFUND_DONE);
        List<OperationType> operations = Lists.newArrayList(OperationType.HOLD);
        if (testConfig.isPrepayAftOrderConfirm() && !testConfig.getRefusePositions().isEmpty()) {
            operations.add(OperationType.HOLD_REVERSE);
        }
        operations.add(OperationType.HOLD_COMPLETE);
        operations.add(OperationType.REFUND);
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(), operations);
        if (testConfig.isAgentSeller()) {
            if (testConfig.isPrepayBefOrderConfirm()) {
                orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), Lists.newArrayList(FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT, FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT_REFUND));
            } else {
                orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), Lists.newArrayList(FiscalReceiptRequestType.AGENT_PREPAYMENT, FiscalReceiptRequestType.AGENT_PREPAYMENT_REFUND));
            }
        } else {
            orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), Lists.newArrayList(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_REFUND));
        }
    }

    @Test
    @Transactional
    public void _01_happyPath_prepayBefConfirm_5of5_usualSeller() {
        _01_happyPath_prepayBefConfirm_5of5_usualSellerCallTest(
                ImmutableList.of(1, 2, 3, 4, 5),
                Collections.emptyList(),
                Collections.emptyList()
        );
    }

    public void _01_happyPath_prepayBefConfirm_5of5_usualSellerCallTest(List<Integer> expertisePassPositions,
                                                                        List<Long> defectsByPositions,
                                                                        List<Long> serviceByPositions) {
        OrderDTO orderInfo = captureRefundPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)

                .itemsCount(5)
                .prepayBefOrderConfirm(true)
                .confirmPositions(ImmutableList.of(1, 2, 3, 4, 5))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(expertisePassPositions)
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(defectsByPositions)
                .cleaningsByPositions(serviceByPositions)

                .sellerPayoutAmount(112_500_00L)

                .build());
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), Lists.newArrayList(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 112_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), Lists.newArrayList(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_ADVANCE, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_PAYMENT, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_SERVICE),
                Lists.newArrayList(ITEM_PAY_KIND_FULLPAY));
    }

    @Test
    @Transactional
    public void _01_happyPath_prepayBefConfirm_5of5_agentSeller() {
       OrderDTO orderInfo = captureRefundPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
               .isAgentSeller(true)

               .itemsCount(5)
               .prepayBefOrderConfirm(true)
               .confirmPositions(ImmutableList.of(1, 2, 3, 4, 5))
               .refusePositions(Collections.emptyList())

               .expertisePassPositions(ImmutableList.of(1, 2, 3, 4, 5))
               .expertiseFailPositions(Collections.emptyList())
               .defectsByPositions(Collections.emptyList())
               .cleaningsByPositions(Collections.emptyList())

               .sellerPayoutAmount(112_500_00L)

               .build());
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), Lists.newArrayList(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 112_500_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), Lists.newArrayList(FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT, FiscalReceiptRequestType.AGENT_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT, 0L,
                Lists.newArrayList(50_000_00L, 40_000_00L, 30_000_00L, 20_000_00L, 10_000_00L, orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_PAYMENT, 0L,
                Lists.newArrayList(50_000_00L, 40_000_00L, 30_000_00L, 20_000_00L, 10_000_00L, orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_COMMODITY, ITEM_KIND_COMMODITY, ITEM_KIND_COMMODITY, ITEM_KIND_COMMODITY, ITEM_KIND_COMMODITY, ITEM_KIND_SERVICE),
                Lists.newArrayList(ITEM_PAY_KIND_FULLPAY, ITEM_PAY_KIND_FULLPAY, ITEM_PAY_KIND_FULLPAY, ITEM_PAY_KIND_FULLPAY, ITEM_PAY_KIND_FULLPAY, ITEM_PAY_KIND_FULLPAY));

    }

    @Test
    @Transactional
    public void _02_refundPath_prepayBefConfirm_0of5_agentSeller() {
        OrderDTO orderInfo = captureRefundPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .isAgentSeller(true)

                .itemsCount(5)
                .prepayBefOrderConfirm(true)
                .confirmPositions(Collections.emptyList())
                .refusePositions(ImmutableList.of(1, 2, 3, 4, 5))

                .build());
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), Lists.newArrayList(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), ImmutableList.of(FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT, FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT_REFUND));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT, 0L,
                Lists.newArrayList(50_000_00L, 40_000_00L, 30_000_00L, 20_000_00L, 10_000_00L, orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT_REFUND, 2L,
                Lists.newArrayList(50_000_00L, 40_000_00L, 30_000_00L, 20_000_00L, 10_000_00L, orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE));
    }

    @Test
    @Transactional
    public void _02_refundPath_prepayBefConfirm_0of5_usualSeller() {
        OrderDTO orderInfo = captureRefundPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)

                .itemsCount(5)
                .prepayBefOrderConfirm(true)
                .confirmPositions(Collections.emptyList())
                .refusePositions(ImmutableList.of(1, 2, 3, 4, 5))

                .build());
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), Lists.newArrayList(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), Lists.newArrayList(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_REFUND));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_ADVANCE, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_REFUND, 2L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE));
    }

    @Test
    @Transactional
    public void _03_refundPath_prepayBefConfirm_5of5_expertiseReject_5of5_agentSeller() {
        OrderDTO orderInfo = captureRefundPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .isAgentSeller(true)

                .itemsCount(5)
                .prepayBefOrderConfirm(true)
                .confirmPositions(ImmutableList.of(1, 2, 3, 4, 5))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(1, 2, 3, 4, 5))
                .cleaningsByPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())

                .build());
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), Lists.newArrayList(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), Lists.newArrayList(FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT, FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT_REFUND));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT, 0L,
                Lists.newArrayList(50_000_00L, 40_000_00L, 30_000_00L, 20_000_00L, 10_000_00L, orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT_REFUND, 2L,
                Lists.newArrayList(50_000_00L, 40_000_00L, 30_000_00L, 20_000_00L, 10_000_00L, orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE));
    }

    @Test
    @Transactional
    public void _03_refundPath_prepayAftConfirm_4of5_expertiseReject_4of5_agentSeller() {
        OrderDTO orderInfo = captureRefundPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .isAgentSeller(true)

                .itemsCount(5)
                .confirmPositions(ImmutableList.of(2, 3, 4, 5))
                .refusePositions(ImmutableList.of(1))
                .prepayAftOrderConfirm(true)

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(2, 3, 4, 5))
                .cleaningsByPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())

                .build());
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), Lists.newArrayList(OperationType.HOLD, OperationType.HOLD_REVERSE, OperationType.HOLD_COMPLETE, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 10_000_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 140_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 140_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), Lists.newArrayList(FiscalReceiptRequestType.AGENT_PREPAYMENT, FiscalReceiptRequestType.AGENT_PREPAYMENT_REFUND));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_PREPAYMENT, 0L,
                Lists.newArrayList(50_000_00L, 40_000_00L, 30_000_00L, 20_000_00L, orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_PREPAYMENT_REFUND, 2L,
                Lists.newArrayList(50_000_00L, 40_000_00L, 30_000_00L, 20_000_00L, orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE));
    }

    @Test
    @Transactional
    public void _03_refundPath_prepayAftConfirm_4of5_expertiseReject_4of5_usualSeller() {
        OrderDTO orderInfo = captureRefundPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)

                .itemsCount(5)
                .confirmPositions(ImmutableList.of(2, 3, 4, 5))
                .refusePositions(ImmutableList.of(1))
                .prepayAftOrderConfirm(true)

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(2, 3, 4, 5))
                .cleaningsByPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())

                .build());
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), Lists.newArrayList(OperationType.HOLD, OperationType.HOLD_REVERSE, OperationType.HOLD_COMPLETE, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 10_000_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 140_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 140_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), Lists.newArrayList(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_REFUND));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_ADVANCE, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE));

        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_REFUND, 2L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE));
    }

    @Test
    @Transactional
    public void _03_refundPath_prepayBefConfirm_4of5_expertiseReject_4of5_agentSeller() {
        OrderDTO orderInfo = captureRefundPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .isAgentSeller(true)

                .itemsCount(5)
                .prepayBefOrderConfirm(true)
                .confirmPositions(ImmutableList.of(2, 3, 4, 5))
                .refusePositions(ImmutableList.of(1))

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(2, 3, 4, 5))
                .cleaningsByPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())

                .build());
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), Lists.newArrayList(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), ImmutableList.of(FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT, FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT_REFUND));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT, 0L,
                Lists.newArrayList(50_000_00L, 40_000_00L, 30_000_00L, 20_000_00L, 10_000_00L, orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT_REFUND, 2L,
                Lists.newArrayList(50_000_00L, 40_000_00L, 30_000_00L, 20_000_00L, 10_000_00L, orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE));
    }

    @Test
    @Transactional
    public void _03_refundPath_prepayBefConfirm_4of5_expertiseReject_4of5_usualSeller() {
        OrderDTO orderInfo = captureRefundPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)

                .itemsCount(5)
                .prepayBefOrderConfirm(true)
                .confirmPositions(ImmutableList.of(2, 3, 4, 5))
                .refusePositions(ImmutableList.of(1))

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(2, 3, 4, 5))
                .cleaningsByPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())

                .build());
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), Lists.newArrayList(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), Lists.newArrayList(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_REFUND));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_ADVANCE, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE));

        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_REFUND, 2L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE));
    }

    @Test
    @Transactional
    public void _03_refundPath_prepayAftConfirm_5of5_expertiseReject_5of5_agentSeller() {
        OrderDTO orderInfo = captureRefundPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .isAgentSeller(true)

                .itemsCount(5)
                .confirmPositions(ImmutableList.of(1, 2, 3, 4, 5))
                .prepayAftOrderConfirm(true)
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(1, 2, 3, 4, 5))
                .cleaningsByPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())

                .build());
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), Lists.newArrayList(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), Lists.newArrayList(FiscalReceiptRequestType.AGENT_PREPAYMENT, FiscalReceiptRequestType.AGENT_PREPAYMENT_REFUND));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_PREPAYMENT, 0L,
                Lists.newArrayList(50_000_00L, 40_000_00L, 30_000_00L, 20_000_00L, 10_000_00L, orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_PREPAYMENT_REFUND, 2L,
                Lists.newArrayList(50_000_00L, 40_000_00L, 30_000_00L, 20_000_00L, 10_000_00L, orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE));
    }

    @Test
    @Transactional
    public void _03_refundPath_prepayBefConfirm_5of5_expertiseReject_5of5_usualSeller() {
        OrderDTO orderInfo = captureRefundPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)

                .itemsCount(5)
                .prepayBefOrderConfirm(true)
                .confirmPositions(ImmutableList.of(1, 2, 3, 4, 5))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(1, 2, 3, 4, 5))
                .cleaningsByPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())

                .build());
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), Lists.newArrayList(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), Lists.newArrayList(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_REFUND));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_ADVANCE, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE));

        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_REFUND, 2L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE));
    }

    @Test
    @Transactional
    public void _03_refundPath_prepayAftConfirm_5of5_expertiseReject_5of5_usualSeller() {
        OrderDTO orderInfo = captureRefundPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)

                .itemsCount(5)
                .confirmPositions(ImmutableList.of(1, 2, 3, 4, 5))
                .prepayAftOrderConfirm(true)
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(1, 2, 3, 4, 5))
                .cleaningsByPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())

                .build());
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), Lists.newArrayList(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), Lists.newArrayList(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_REFUND));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_ADVANCE, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE));

        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_REFUND, 2L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE));
    }

    @Test
    @Transactional
    public void _04_comboItems_prepayBefConfirm_4of5_agentSeller() {
        OrderDTO orderInfo = captureRefundPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(5)
                .isAgentSeller(true)
                .prepayBefOrderConfirm(true)
                .confirmPositions(ImmutableList.of(1, 2, 3, 4))
                .refusePositions(ImmutableList.of(5))

                .expertisePassPositions(ImmutableList.of(4))
                .expertiseFailPositions(ImmutableList.of(3))
                .defectsByPositions(Lists.newArrayList(null, 1234L, null, null, null))
                .cleaningsByPositions(Lists.newArrayList(4321L, null, null, null, null))

                .sellerPayoutAmount(46_945_00L)

                .refundAfterCompleteAmount(1_234_56L)

                .build());
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.REFUND, OperationType.SELLER_PAYOUT, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 50_000_00 + 30_000_00 + 1_234_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 46_945_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 1_234_56);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), ImmutableList.of(FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT, FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT_ADJUST, FiscalReceiptRequestType.AGENT_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT, 0L,
                Lists.newArrayList(50_000_00L, 40_000_00L, 30_000_00L, 20_000_00L, 10_000_00L, orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT_ADJUST, 2L,
                Lists.newArrayList(50_000_00L, 30_000_00L, 1_234_00L),
                Lists.newArrayList(ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_PAYMENT, 0L,
                Lists.newArrayList(40_000_00L, 18_766_00L, 10_000_00L, orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_COMMODITY, ITEM_KIND_COMMODITY, ITEM_KIND_COMMODITY, ITEM_KIND_SERVICE),
                Lists.newArrayList(ITEM_PAY_KIND_FULLPAY, ITEM_PAY_KIND_FULLPAY, ITEM_PAY_KIND_FULLPAY, ITEM_PAY_KIND_FULLPAY));
    }

    @Test
    @Transactional
    public void _04_comboItems_prepayBefConfirm_4of5_usualSeller() {
        OrderDTO orderInfo = captureRefundPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(5)
                .isUsualSeller(true)
                .prepayBefOrderConfirm(true)
                .confirmPositions(ImmutableList.of(1, 2, 3, 4))
                .refusePositions(ImmutableList.of(5))

                .expertisePassPositions(ImmutableList.of(4))
                .expertiseFailPositions(ImmutableList.of(3))
                .defectsByPositions(Lists.newArrayList(null, 1234L, null, null, null))
                .cleaningsByPositions(Lists.newArrayList(4321L, null, null, null, null))

                .sellerPayoutAmount(46_945_00L)

                .refundAfterCompleteAmount(1_234_56L)

                .build());
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.REFUND, OperationType.SELLER_PAYOUT, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 50_000_00 + 30_000_00 + 1_234_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 46_945_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 1_234_56);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), Lists.newArrayList(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_ADVANCE, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_PAYMENT, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_SERVICE),
                Lists.newArrayList(ITEM_PAY_KIND_FULLPAY));
    }

    @Test
    @Transactional
    public void _04_comboItems_prepayAftConfirm_4of5_agentSeller() {
        OrderDTO orderInfo = captureRefundPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(5)
                .isAgentSeller(true)
                .confirmPositions(ImmutableList.of(1, 2, 3, 4))
                .refusePositions(ImmutableList.of(5))
                .prepayAftOrderConfirm(true)

                .expertisePassPositions(ImmutableList.of(4))
                .expertiseFailPositions(ImmutableList.of(3))
                .defectsByPositions(Lists.newArrayList(null, 1234L, null, null, null))
                .cleaningsByPositions(Lists.newArrayList(4321L, null, null, null, null))

                .sellerPayoutAmount(46_945_00L)

                .refundAfterCompleteAmount(1_234_56L)

                .build());
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE, OperationType.HOLD_COMPLETE, OperationType.REFUND, OperationType.SELLER_PAYOUT, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 50_000_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 100_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 30_000_00 + 1_234_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 46_945_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 1_234_56);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.AGENT_PREPAYMENT, FiscalReceiptRequestType.AGENT_PREPAYMENT_ADJUST, FiscalReceiptRequestType.AGENT_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_PREPAYMENT, 0L,
                Lists.newArrayList(40_000_00L, 30_000_00L, 20_000_00L, 10_000_00L, orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_PREPAYMENT_ADJUST, 2L,
                Lists.newArrayList(30_000_00L, 1_234_00L),
                Lists.newArrayList(ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_PAYMENT, 0L,
                Lists.newArrayList(40_000_00L, 18_766_00L, 10_000_00L, orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_COMMODITY, ITEM_KIND_COMMODITY, ITEM_KIND_COMMODITY, ITEM_KIND_SERVICE),
                Lists.newArrayList(ITEM_PAY_KIND_FULLPAY, ITEM_PAY_KIND_FULLPAY, ITEM_PAY_KIND_FULLPAY, ITEM_PAY_KIND_FULLPAY));
    }

    @Test
    @Transactional
    public void _04_comboItems_prepayAftConfirm_4of5_usualSeller() {
        OrderDTO orderInfo = captureRefundPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(5)
                .isUsualSeller(true)
                .confirmPositions(ImmutableList.of(1, 2, 3, 4))
                .refusePositions(ImmutableList.of(5))
                .prepayAftOrderConfirm(true)

                .expertisePassPositions(ImmutableList.of(4))
                .expertiseFailPositions(ImmutableList.of(3))
                .defectsByPositions(Lists.newArrayList(null, 1234L, null, null, null))
                .cleaningsByPositions(Lists.newArrayList(4321L, null, null, null, null))

                .sellerPayoutAmount(46_945_00L)

                .refundAfterCompleteAmount(1_234_56L)

                .build());
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE, OperationType.HOLD_COMPLETE, OperationType.REFUND, OperationType.SELLER_PAYOUT, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 50_000_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 100_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 30_000_00 + 1_234_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 46_945_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 1_234_56);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_ADVANCE, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_PAYMENT, 0L,
            Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
            Lists.newArrayList(ITEM_KIND_SERVICE),
            Lists.newArrayList(ITEM_PAY_KIND_FULLPAY));
    }

    @Test
    @Transactional
    public void _04_comboItems_prepayBefCharges_4of5_agentSeller() {
        OrderDTO orderInfo = captureRefundPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(5)
                .isAgentSeller(true)
                .confirmPositions(ImmutableList.of(1, 2, 3, 4))
                .refusePositions(ImmutableList.of(5))

                .prepayAfterExpertises(true)

                .expertisePassPositions(ImmutableList.of(4))
                .expertiseFailPositions(ImmutableList.of(3))
                .defectsByPositions(Lists.newArrayList(null, 1234L, null, null, null))
                .cleaningsByPositions(Lists.newArrayList(4321L, null, null, null, null))

                .sellerPayoutAmount(46_945_00L)

                .refundAfterCompleteAmount(1_234_56L)

                .build());
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE, OperationType.HOLD_COMPLETE, OperationType.REFUND, OperationType.SELLER_PAYOUT, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 50_000_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 100_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 30_000_00 + 1_234_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 46_945_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 1_234_56);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.AGENT_PREPAYMENT, FiscalReceiptRequestType.AGENT_PREPAYMENT_ADJUST, FiscalReceiptRequestType.AGENT_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_PREPAYMENT, 0L,
                Lists.newArrayList(40_000_00L, 30_000_00L, 20_000_00L, 10_000_00L, orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_PREPAYMENT_ADJUST, 2L,
                Lists.newArrayList(30_000_00L, 1_234_00L),
                Lists.newArrayList(ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_PAYMENT, 0L,
                Lists.newArrayList(40_000_00L, 18_766_00L, 10_000_00L, orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_COMMODITY, ITEM_KIND_COMMODITY, ITEM_KIND_COMMODITY, ITEM_KIND_SERVICE),
                Lists.newArrayList(ITEM_PAY_KIND_FULLPAY, ITEM_PAY_KIND_FULLPAY, ITEM_PAY_KIND_FULLPAY, ITEM_PAY_KIND_FULLPAY));
    }

    @Test
    @Transactional
    public void _04_comboItems_prepayBefCharges_4of5_usualSeller() {
        OrderDTO orderInfo = captureRefundPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .itemsCount(5)
                .isUsualSeller(true)
                .confirmPositions(ImmutableList.of(1, 2, 3, 4))
                .refusePositions(ImmutableList.of(5))

                .prepayAfterExpertises(true)

                .expertisePassPositions(ImmutableList.of(4))
                .expertiseFailPositions(ImmutableList.of(3))
                .defectsByPositions(Lists.newArrayList(null, 1234L, null, null, null))
                .cleaningsByPositions(Lists.newArrayList(4321L, null, null, null, null))

                .sellerPayoutAmount(46_945_00L)

                .refundAfterCompleteAmount(1_234_56L)

                .build());
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE, OperationType.HOLD_COMPLETE, OperationType.REFUND, OperationType.SELLER_PAYOUT, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 50_000_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 100_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 30_000_00 + 1_234_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 46_945_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 1_234_56);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_ADVANCE, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_PAYMENT, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_SERVICE),
                Lists.newArrayList(ITEM_PAY_KIND_FULLPAY));
    }

    @Test
    @Transactional
    public void _05_singleItemDefect_prepayBefConfirm_1of1_agentSeller() {
        OrderDTO orderInfo = captureRefundPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .isAgentSeller(true)

                .itemsCount(1)
                .prepayBefOrderConfirm(true)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(ImmutableList.of(1647L))
                .cleaningsByPositions(Collections.emptyList())

                .sellerPayoutAmount(5_853_00L)

                .build());
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.REFUND, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 10_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 1_647_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 5_853_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), Lists.newArrayList(FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT, FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT_ADJUST, FiscalReceiptRequestType.AGENT_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT, 0L,
                Lists.newArrayList(10_000_00L, orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT_ADJUST, 2L,
                Lists.newArrayList( 1_647_00L),
                Lists.newArrayList(ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_PAYMENT, 0L,
                    Lists.newArrayList(8_353_00L, orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                    Lists.newArrayList(ITEM_KIND_COMMODITY, ITEM_KIND_SERVICE),
                    Lists.newArrayList(ITEM_PAY_KIND_FULLPAY, ITEM_PAY_KIND_FULLPAY));
        //
        orderFlowTestUtils.validateSellerPayout(orderInfo.getId(), 5_853_00, null, TcbBankService.SCHEMA, TransactionState.DONE);
    }

    @Test
    @Transactional
    public void _05_singleItemDefect_prepayBefConfirm_1of1_usualSeller() {
        OrderDTO orderInfo = captureRefundPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)

                .itemsCount(1)
                .prepayBefOrderConfirm(true)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(ImmutableList.of(1647L))
                .cleaningsByPositions(Collections.emptyList())

                .sellerPayoutAmount(5_853_00L)

                .build());
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.REFUND, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 10_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.REFUND, 1_647_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 5_853_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), Lists.newArrayList(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_ADVANCE, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_PAYMENT, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_SERVICE),
                Lists.newArrayList(ITEM_PAY_KIND_FULLPAY));
        //
        orderFlowTestUtils.validateSellerPayout(orderInfo.getId(), 5_853_00, null, TcbBankService.SCHEMA, TransactionState.DONE);
    }

    @Test
    @Transactional
    public void _06_singleItemCleans_prepayBefConfirm_1of1_agentSeller() {
        OrderDTO orderInfo = captureRefundPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .isAgentSeller(true)

                .itemsCount(1)
                .prepayBefOrderConfirm(true)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(ImmutableList.of(1647L))

                .sellerPayoutAmount(5_853_00L)

                .build());
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 10_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 5_853_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), Lists.newArrayList(FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT, FiscalReceiptRequestType.AGENT_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT, 0L,
                Lists.newArrayList(10_000_00L, orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_PAYMENT, 0L,
                Lists.newArrayList(10_000_00L, orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_COMMODITY, ITEM_KIND_SERVICE),
                Lists.newArrayList(ITEM_PAY_KIND_FULLPAY, ITEM_PAY_KIND_FULLPAY));
        //
        orderFlowTestUtils.validateSellerPayout(orderInfo.getId(), 5_853_00, null, TcbBankService.SCHEMA, TransactionState.DONE);
    }

    @Test
    @Transactional
    public void _06_singleItemCleans_prepayBefConfirm_1of1_usualSeller() {
        OrderDTO orderInfo = captureRefundPayoutTest_executeTestWithConfig(OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)

                .itemsCount(1)
                .prepayBefOrderConfirm(true)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(ImmutableList.of(1647L))

                .sellerPayoutAmount(5_853_00L)

                .build());
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 10_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 10_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.SELLER_PAYOUT, 5_853_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), Lists.newArrayList(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_ADVANCE, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_ADVANCE),
                Lists.newArrayList(ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_PAYMENT, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(ITEM_KIND_SERVICE),
                Lists.newArrayList(ITEM_PAY_KIND_FULLPAY));
        //
        orderFlowTestUtils.validateSellerPayout(orderInfo.getId(), 5_853_00, null, TcbBankService.SCHEMA, TransactionState.DONE);
    }

    @Test
    @Transactional
    public void _07_happyPath_defectsThenOkay_prepayBefConfirm_5of5_usualSeller() {
        _01_happyPath_prepayBefConfirm_5of5_usualSellerCallTest(
                ImmutableList.of(1, 2, 3, 4, 5),
                Lists.newArrayList(null, 100L, 1000L, null, null),
                Collections.emptyList()
        );
    }

    @Test
    @Transactional
    public void _07_happyPath_serviceThenOkay_prepayBefConfirm_5of5_usualSeller() {
        _01_happyPath_prepayBefConfirm_5of5_usualSellerCallTest(
                ImmutableList.of(1, 2, 3, 4, 5),
                Collections.emptyList(),
                Lists.newArrayList(null, 100L, 1000L, null, null)
        );
    }

    private void _08_OrderFlow_ValidateHoldCompleted(OrderDTO orderDto, boolean isAgentSeller) {
        orderFlowTestUtils.validateOrderState(orderDto.getId(), OrderState.MONEY_TRANSFERRED);
        orderFlowTestUtils.validateOrderPayment(orderDto.getId(), TcbBankService.SCHEMA, OrderPaymentState.REFUND_DONE);
        if (isAgentSeller) {
            orderFlowTestUtils.validateFiscalReceiptsTypeList(orderDto.getId(), Lists.newArrayList(FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT, FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT_ADJUST));
            orderFlowTestUtils.validateOrderFiscalReceipt(orderDto.getId(), FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT_ADJUST, 2L,
                    Lists.newArrayList( 1_647_00L),
                    Lists.newArrayList(ITEM_KIND_ADVANCE),
                    Lists.newArrayList(ITEM_PAY_KIND_ADVANCE));

        } else {
            orderFlowTestUtils.validateFiscalReceiptsTypeList(orderDto.getId(), Lists.newArrayList(FiscalReceiptRequestType.DELIVERY_ADVANCE));
        }
        //
        orderFlowTestUtils.validateBankOperation(orderDto.getId(), OperationType.REFUND, 1647_00);
        orderFlowTestUtils.validateBankOperationTypeList(orderDto.getId(), Lists.newArrayList(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.REFUND));
    }

    @SneakyThrows
    private void _08_OrderFlow_ChargeMoneyOkay(boolean isAgentSeller) {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(isAgentSeller ? agentSellerId : usualSellerId)
                .maxItems(1)
                .build()
        );
        commitAndStartNewTransaction();
        //
        GroupedCart cartInfo = orderFlowTestUtils.fillCart(products);
        OrderDTO cartOrder = cartInfo.getGroup(products.get(0).getSeller().getId());
        //
        OrderService.InitOrderResult testOrder1 = orderFlowTestUtils.holdOrderWithPromoCodePS(products.get(0).getSeller(), null, TcbBankService.SCHEMA);
        rollbackAndStartNewTransaction();
        //
        OrderDTO testOrder = orderFlowTestUtils.loadOrderSuccessfull(testOrder1.getOrderId(), true);
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.HOLD_PROCESSING);
        orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_INPROGRESS);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, 10_000_00 + testOrder.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(), Lists.newArrayList(OperationType.HOLD));
        //
        orderFlowTestUtils.callOrderHoldCallback(testOrder1.getOrderId(), testOrder1.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.HOLD);
        orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_DONE);
        //
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, 10_000_00 + testOrder.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(), Lists.newArrayList(OperationType.HOLD));
        //
        Long sellerCounterPartyId = isAgentSeller ? agentSellerCounterpartyId : usualSellerCounterpartyId;
        orderFlowTestUtils.convertHoldToPrepayment(testOrder.getId(), HttpStatus.Series.SUCCESSFUL);
        orderFlowTestUtils.changeSellerCounterparty(testOrder.getId(), sellerCounterPartyId, HttpStatus.Series.SUCCESSFUL);
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.HOLD);
        orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_INPROGRESS);
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(), Lists.newArrayList(OperationType.HOLD, OperationType.HOLD_COMPLETE));
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_COMPLETE, 10_000_00 + testOrder.getDeliveryCost().movePointRight(2).longValue());
        if (isAgentSeller) {
            orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), Lists.newArrayList(FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT));
            orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT, 0L,
                    Lists.newArrayList(10_000_00L, testOrder.getDeliveryCost().movePointRight(2).longValue()),
                    Lists.newArrayList(ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE),
                    Lists.newArrayList(ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE));
        } else {
            orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), Lists.newArrayList(FiscalReceiptRequestType.DELIVERY_ADVANCE));
            orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.DELIVERY_ADVANCE, 0L,
                    Lists.newArrayList(testOrder.getDeliveryCost().movePointRight(2).longValue()),
                    Lists.newArrayList(ITEM_KIND_ADVANCE),
                    Lists.newArrayList(ITEM_PAY_KIND_ADVANCE));
        }
        //
        orderFlowTestUtils.rejectOrApprovePosition(testOrder.getItems().get(0).getId(), true);
        //
        ResponseEntity<String> chargeResponseFail = orderFlowTestUtils.adminPanel_Charge(testOrder.getId());
        Assertions.assertThat(chargeResponseFail.getStatusCode().is4xxClientError()).isTrue();
        Exception thrownCharge = objectMapper.readValue(chargeResponseFail.getBody(), Exception.class);
        Assertions.assertThat(thrownCharge.getMessage()).matches("Заказ .*, подтверждение списания ДС невозможно: некорректное состояние заказа");
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.HOLD);
        orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_INPROGRESS);
        //
        orderFlowTestUtils.changeAddressEndpoint(testOrder.getId(), pickupId, deliveryId);
        OrderFlowTestUtils.OskellyDeliveryInfo deliveryInfo = OrderFlowTestUtils.OskellyDeliveryInfo.builder()
                .courierName("S2O-Courier").courierPhone("**********").courierDate(LocalDate.now()).callWillFail(true)
                .build();
        ResponseEntity<String> takeResponseFail = orderFlowTestUtils.takeOurselves(testOrder.getId(), deliveryInfo);
        Assertions.assertThat(takeResponseFail.getStatusCode().is4xxClientError()).isTrue();
        Exception thrownTakeMe = objectMapper.readValue(takeResponseFail.getBody(), Exception.class);
        Assertions.assertThat(thrownTakeMe.getMessage()).matches("Заказ .*: не удается подтвердить заказ, отсутствует информация об оплате");
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.HOLD);
        orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_INPROGRESS);
        //
        orderFlowTestUtils.processHoldComplete(testOrder);
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.HOLD);
        orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_DONE);
        //
        orderFlowTestUtils.takeOurselves(testOrder.getId(), null);
        orderFlowTestUtils.changeDeliveryState(testOrder.getId(), DeliveryState.OURSELVES_FROM_SELLER_TO_OFFICE, true);
        orderFlowTestUtils.changeDeliveryState(testOrder.getId(), DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE, true);
        //
        ResponseEntity<String> sendItResponseFail = orderFlowTestUtils.sendOurselves(testOrder.getId(), null);
        Assertions.assertThat(sendItResponseFail.getStatusCode().is4xxClientError()).isTrue();
        Exception thrownSendIt = objectMapper.readValue(sendItResponseFail.getBody(), Exception.class);
        Assertions.assertThat(thrownSendIt.getMessage())
                .matches("Заказ .*, подтверждение списания ДС невозможно: некорректное состояние заказа");
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.HOLD);
        orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_DONE);
        //
        orderFlowTestUtils.adminsApi1expertise(testOrder.getItems().get(0).getId(), true, OrderFlowTestUtils.ExpertiseAction.EXPERTISE_DEFECT_IT, 1647L);
        //
        ResponseEntity<String> chargeRsp2nd = orderFlowTestUtils.adminPanel_Charge(testOrder.getId());
        Assertions.assertThat(chargeRsp2nd.getStatusCode().is2xxSuccessful()).isTrue();
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.processBankOperation(testOrder.getId(), OperationType.REFUND);
        rollbackAndStartNewTransaction();
        //
        _08_OrderFlow_ValidateHoldCompleted(testOrder, isAgentSeller);
        //
        ResponseEntity<String> chargeRsp3rd = orderFlowTestUtils.adminPanel_Charge(testOrder.getId());
        Assertions.assertThat(chargeRsp3rd.getStatusCode().is4xxClientError()).isTrue();
        Exception thrown = objectMapper.readValue(chargeRsp3rd.getBody(), Exception.class);
        Assertions.assertThat(thrown.getMessage()).matches("Не валидное состояние заказа Деньги на банковском счете для .* при списании денег");
        rollbackAndStartNewTransaction();
        //
        _08_OrderFlow_ValidateHoldCompleted(testOrder, isAgentSeller); // Nothing changed when we called charge again
        //
        orderFlowTestUtils.sendOurselves(testOrder.getId(), null);
        rollbackAndStartNewTransaction();
        //
        _08_OrderFlow_ValidateHoldCompleted(testOrder, isAgentSeller); // Nothing changed when we called delivery company (and moneycharging inside)
        //
        orderFlowTestUtils.changeDeliveryState(testOrder.getId(), DeliveryState.OURSELVES_FROM_OFFICE_TO_BUYER, true);
        orderFlowTestUtils.changeDeliveryState(testOrder.getId(), DeliveryState.DELIVERED_TO_BUYER, true);
        if (isAgentSeller) {
            orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.AGENT_PAYMENT, 0L,
                    Lists.newArrayList(8_353_00L, testOrder.getDeliveryCost().movePointRight(2).longValue()),
                    Lists.newArrayList(ITEM_KIND_COMMODITY, ITEM_KIND_SERVICE),
                    Lists.newArrayList(ITEM_PAY_KIND_FULLPAY, ITEM_PAY_KIND_FULLPAY));
        } else {
            orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.DELIVERY_PAYMENT, 0L,
                    Lists.newArrayList(testOrder.getDeliveryCost().movePointRight(2).longValue()),
                    Lists.newArrayList(ITEM_KIND_SERVICE),
                    Lists.newArrayList(ITEM_PAY_KIND_FULLPAY));
        }
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.MONEY_TRANSFERRED);
        orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.REFUND_DONE);
        //
        orderFlowTestUtils.sendAgentReport(testOrder.getId(), true);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.confirmAgentReport(testOrder.getId(), true);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.prepareSellerPayout(testOrder.getId(), true);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.validateSellerPayout(testOrder.getId(), 5_853_00, null, TcbBankService.SCHEMA, TransactionState.PREPARED);
        if (isAgentSeller) {
            orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), Lists.newArrayList(FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT, FiscalReceiptRequestType.AGENT_COMMON_PREPAYMENT_ADJUST, FiscalReceiptRequestType.AGENT_PAYMENT));
        } else {
            orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), Lists.newArrayList(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        }
    }

    @Test
    @Transactional
    public void _AgentSeller_08_ChargeMoneyOkay() {
        _08_OrderFlow_ChargeMoneyOkay(true);
    }

    @Test
    @Transactional
    public void _UsualSeller_08_ChargeMoneyOkay() {
        _08_OrderFlow_ChargeMoneyOkay(false);
    }

}
