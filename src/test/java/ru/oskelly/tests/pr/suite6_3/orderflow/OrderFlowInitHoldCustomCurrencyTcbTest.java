package ru.oskelly.tests.pr.suite6_3.orderflow;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.OrderFlowTest;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2ClientException;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2ParseException;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestB2PMock;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestNoonMock;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestTcbMock;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.component.HoldRequest;
import su.reddot.domain.model.banktransaction.OperationType;
import su.reddot.domain.model.currency.CurrencyRate;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.currency.CurrencyRateService;
import su.reddot.domain.service.currency.CurrencyService;
import su.reddot.domain.service.dto.order.GroupedCart;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.Best2payAndTcbBankService;
import su.reddot.infrastructure.bank.BoutiqueBankService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.bank.payments.noon.NoonBankService;
import su.reddot.presentation.api.v2.Api2Response;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.fail;


@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
public class OrderFlowInitHoldCustomCurrencyTcbTest extends OrderFlowTest {

    @Autowired
    private UserService userService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private CurrencyRateService currencyRateService;
    @Autowired
    private CurrencyService currencyService;

    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private CartTestSupport cartTestSupport;

    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user-password}")
    private String password;
    @Value("${test-prepayments.usual-seller-id}")
    private Long usualSellerId;

    private static OrderFlowTestTcbMock orderFlowTestTcbMock;

    private static OrderFlowTestB2PMock orderFlowTestB2PMock;
    private static OrderFlowTestNoonMock orderFlowTestNonMock;

    private ApiV2Client apiV2Client;

    @Value("${test.receipts.mock-server-host}")
    private String mockServerHost;
    @Value("${test.receipts.mock-server-tcb-bank-port}")
    private Integer mockTcbServerPort;
    @Value("${test.receipts.mock-server-b2p-bank-port}")
    private Integer mockB2PServerPort;
    @Value("${test.receipts.mock-server-non-bank-port}")
    private Integer mockNonServerPort;

    @PostConstruct
    private void init() {
        List<String> allowSchemas = Lists.newArrayList(TcbBankService.SCHEMA, NoonBankService.NOON_SCHEMA, Best2payAndTcbBankService.SCHEMA, BoutiqueBankService.BOUTIQUE_SCHEMA);
        orderFlowTestUtils.setAllowPaymentSystemChoose(allowSchemas);
        User buyer = userService.getUserByEmail(buyerEmail);
        apiV2Client = new ApiV2Client(buyerEmail, password);
        orderFlowTestUtils.init(buyerEmail, password);
        cartTestSupport.setUserId(buyer.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
        orderFlowTestTcbMock = Objects.isNull(orderFlowTestTcbMock) ? new OrderFlowTestTcbMock(mockServerHost, mockTcbServerPort) : orderFlowTestTcbMock;
        orderFlowTestB2PMock = Objects.isNull(orderFlowTestB2PMock) ? new OrderFlowTestB2PMock(mockServerHost, mockB2PServerPort) : orderFlowTestB2PMock;
        orderFlowTestNonMock = Objects.isNull(orderFlowTestNonMock) ? new OrderFlowTestNoonMock(mockServerHost, mockNonServerPort) : orderFlowTestNonMock;
    }

    @AfterAll
    public static void done() {
        orderFlowTestTcbMock.stop();
        orderFlowTestB2PMock.stop();
        orderFlowTestNonMock.stop();
    }

    void validateOrderState(long orderId, OrderState state) {
        Order order = orderService.getOrder(orderId);
        assertEquals(state, order.getState());
    }

    OrderService.InitOrderResult holdOrderWithCurrency(List<Product> products, String currencyCode) {
        cartTestSupport.setCartAddressEndpoint();
        //
        return cartTestSupport.holdCartWithParams(products.get(0).getSeller().getId(),
                HoldRequest.builder()
                        .currencyCode(currencyCode)
                        .paymentSystem(TcbBankService.SCHEMA)
                .build());
    }

    @Test
    @Transactional
    public void _01_OrderFlow_CartHoldWithNullEqualsCartHoldWithBase() {
        final String baseCurrencyCode = "RUB";
        final String nullCurrencyCode = null;
        //
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(5)
                .build()
        );
        List<Long> productsIds = products.stream().map(Product::getId).collect(Collectors.toList());
        commitAndStartNewTransaction();
        //
        GroupedCart cartWithNull = orderFlowTestUtils.fillCartWithCurrencyCode(productsIds, nullCurrencyCode);
        OrderService.InitOrderResult holdWithNull = holdOrderWithCurrency(products, nullCurrencyCode);
        GroupedCart cartWithBase = orderFlowTestUtils.fillCartWithCurrencyCode(productsIds, baseCurrencyCode);
        OrderService.InitOrderResult holdWithBase = holdOrderWithCurrency(products, baseCurrencyCode);
        rollbackAndStartNewTransaction();
        //
        Assertions.assertThat(cartWithNull)
                .usingRecursiveComparison()
                .ignoringFields("groups.items.id")
                .isEqualTo(cartWithBase);
        //
        OrderDTO orderWithNull = orderFlowTestUtils.loadOrderSuccessfull(holdWithNull.getOrderId(), true);
        validateOrderState(orderWithNull.getId(), OrderState.HOLD_PROCESSING);
        orderFlowTestUtils.validateBankOperation(orderWithNull.getId(), OperationType.HOLD, 150_000_00 + orderWithNull.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperationTypeList(orderWithNull.getId(), Lists.newArrayList(OperationType.HOLD));
        //
        OrderDTO orderWithBase = orderFlowTestUtils.loadOrderSuccessfull(holdWithBase.getOrderId(), true);
        validateOrderState(orderWithBase.getId(), OrderState.HOLD_PROCESSING);
        orderFlowTestUtils.validateBankOperation(orderWithBase.getId(), OperationType.HOLD, 150_000_00 + orderWithBase.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperationTypeList(orderWithBase.getId(), Lists.newArrayList(OperationType.HOLD));
        //
        Assertions.assertThat(orderWithNull)
                .usingRecursiveComparison()
                .ignoringFields("id", "stateTime", "items.id", "payment.id")
                .isEqualTo(orderWithBase);
        //
        orderFlowTestUtils.callOrderHoldCallback(holdWithNull.getOrderId(), holdWithNull.getBank_url().replace("https://", "http://"));
        orderFlowTestUtils.callOrderHoldCallback(holdWithBase.getOrderId(), holdWithBase.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        validateOrderState(orderWithNull.getId(), OrderState.HOLD);
        validateOrderState(orderWithBase.getId(), OrderState.HOLD);
        //
        orderFlowTestUtils.validateBankOperation(orderWithNull.getId(), OperationType.HOLD, 150_000_00 + orderWithNull.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperationTypeList(orderWithNull.getId(), Lists.newArrayList(OperationType.HOLD));
        orderFlowTestUtils.validateBankOperation(orderWithBase.getId(), OperationType.HOLD, 150_000_00 + orderWithBase.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperationTypeList(orderWithBase.getId(), Lists.newArrayList(OperationType.HOLD));
    }

    @Test
    @Transactional
    public void _02_OrderFlow_CartHoldWithFakeCurrencyFail() {
        final String fakeCurrencyCode = "ZUZ";
        //
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(5)
                .build()
        );
        commitAndStartNewTransaction();
        //
        try {
            orderFlowTestUtils.fillCartWithFakeCurrencyCode(products, fakeCurrencyCode);
            fail("Unreachable code: fillCart must throw exception");
        } catch (ApiV2ClientException e) {
            Api2Response<String> errorRsp = orderFlowTestUtils.getRawApi2Response(e.getRawData());
            Assertions.assertThat(errorRsp.getMessage()).isEqualTo("Unable to find currency with code ZUZ");
            Assertions.assertThat(errorRsp.getHumanMessage()).isEqualTo("Unable to find currency with code ZUZ");
        }
        try {
            OrderService.InitOrderResult holdWithFake = holdOrderWithCurrency(products, fakeCurrencyCode); // TODO It will fail with multiple currencies support in hold request
            fail("Unreachable code: holdCart must throw exception");
        } catch (ApiV2ParseException e) {
            Api2Response<String> errorRsp = orderFlowTestUtils.getRawApi2Response(e.getRawData());
            Assertions.assertThat(errorRsp.getMessage()).isEqualTo("Unable to find currency with code ZUZ");
            Assertions.assertThat(errorRsp.getHumanMessage()).isEqualTo("Unable to find currency with code ZUZ");
        }
    }

    @Test
    @Disabled("Test will go back with Checkout, now we can`t call hold with different currencies, no bank service supports more that one currency")
    @Transactional
    public void _03_OrderFlow_CartHoldWithDifferentCurrenciesDiffers() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(5)
                .build()
        );
        commitAndStartNewTransaction();
        //
        List<CurrencyRate> allRates = currencyRateService.getAllRates();
        List<String> allCurrencies = allRates.stream()
                .map(rate -> rate.getCurrency().getIsoCode())
                .collect(Collectors.toList());
        allCurrencies.add("RUB");
        //
        Map<String, GroupedCart> cartByCurrency = new HashMap<>();
        Map<String, OrderDTO> orderByCurrency = new HashMap<>();
        for (String currencyCode : allCurrencies) {
            List<Long> productsIds = products.stream().map(Product::getId).collect(Collectors.toList());
            GroupedCart currencyCart = orderFlowTestUtils.fillCartWithCurrencyCode(productsIds, currencyCode);
            cartByCurrency.put(currencyCode, currencyCart);
            OrderService.InitOrderResult currencyHold = holdOrderWithCurrency(products, currencyCode);
            OrderDTO orderHold = orderFlowTestUtils.loadOrderSuccessfull(currencyHold.getOrderId(), true);
            orderByCurrency.put(currencyCode, orderHold);
            orderFlowTestUtils.validateBankOperation(orderHold.getId(), OperationType.HOLD, 150_000_00 + orderHold.getDeliveryCost().movePointRight(2).longValue());
            orderFlowTestUtils.validateBankOperationTypeList(orderHold.getId(), Lists.newArrayList(OperationType.HOLD));
        }

        for (String currencyCode : allCurrencies) {
            for (String currencyWith : allCurrencies) {
                if (currencyCode.equals(currencyWith)) {
                    break;
                }
                Assertions.assertThat(cartByCurrency.get(currencyCode))
                        .usingRecursiveComparison()
                        .ignoringFields("groups.items.id",
                                "groups.clearAmount", "groups.deliveryCost", "groups.finalAmount", "groups.finalAmountWithoutDeliveryCost",
                                "groups.items.amount", "groups.items.finalAmount", "groups.items.rrp")
                        .isEqualTo(cartByCurrency.get(currencyWith));
                Assertions.assertThat(orderByCurrency.get(currencyCode))
                        .usingRecursiveComparison()
                        .ignoringFields("id", "stateTime", "items.id")
                        .isEqualTo(orderByCurrency.get(currencyWith));
            }
        }
    }

    private Set<String> getCurrencyCodesInDb() {
        return currencyService.getAllCurrenciesAvailableToPayment().stream()
                .map(currency -> currency.getIsoCode())
                .collect(Collectors.toSet());
    }

    private void _04_OrderFlow_holdFail(List<Product> products, String paymentSystem, String serviceName, String currencyCode) {
        Set<String> currenciesInDb = getCurrencyCodesInDb();
        //
        orderFlowTestUtils.fillCart(products);
        //
        cartTestSupport.setCartAddressEndpoint();
        //
        HoldRequest holdRequest = HoldRequest.builder().paymentSystem(paymentSystem).currencyCode(currencyCode).build();
        try {
            cartTestSupport.holdCartWithParams(products.get(0).getSeller().getId(), holdRequest);
        } catch(ApiV2ParseException apiV2ParseException) {
            Api2Response<String> errorRsp = orderFlowTestUtils.getRawApi2Response(apiV2ParseException.getRawData());
            if (currenciesInDb.contains(currencyCode)) {
                String bsNoSupportMsg = "Ошибка при оплате заказа .* \\(" + Pattern.quote(paymentSystem) + "\\), пожалуйста, повторите попытку позже"; // TODO String.format("Currency %s is not supported in bank service %s (.*)", currencyCode, serviceName);
                Assertions.assertThat(errorRsp.getMessage()).matches(bsNoSupportMsg);
                Assertions.assertThat(errorRsp.getHumanMessage()).matches(bsNoSupportMsg);
            } else {
                String noCurrencyMesg = "Unable to find currency with code " + currencyCode;
                Assertions.assertThat(errorRsp.getMessage()).isEqualTo(noCurrencyMesg);
                Assertions.assertThat(errorRsp.getHumanMessage()).isEqualTo(noCurrencyMesg);
            }
        }
    }

    private void _04_OrderFlow_holdOkay(List<Product> products, String paymentSystem, String serviceName, String currencyCode) {
        orderFlowTestUtils.fillCart(products);
        //
        cartTestSupport.setCartAddressEndpoint();
        //
        HoldRequest holdRequest = HoldRequest.builder().paymentSystem(paymentSystem).currencyCode(currencyCode).build();
        //
        OrderService.InitOrderResult orderInfo = cartTestSupport.holdCartWithParams(products.get(0).getSeller().getId(), holdRequest);
        //
        Assertions.assertThat(orderInfo.getOrderId()).isNotNull();
    }

    private void _04_orderFlow_validateHoldWithCurrencyPS(String paymentSystem, String serviceName, Set<String> okayCurrencies) {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(5)
                .build()
        );
        commitAndStartNewTransaction();
        //
        Set<String> failCurrencies = Sets.newHashSet("MXN", "VES", "GTQ", "RUB", "AED", "USD", "HNL", "ZAR", "EUR");
        Assertions.assertThat(failCurrencies).containsAll(okayCurrencies);
        failCurrencies.removeAll(okayCurrencies);
        Assertions.assertThat(failCurrencies).isNotEmpty();
        //
        for (String failCode : failCurrencies) {
            _04_OrderFlow_holdFail(products, paymentSystem, serviceName, failCode);
        }
        //
        for (String okayCode : okayCurrencies) {
            _04_OrderFlow_holdOkay(products, paymentSystem, serviceName, okayCode);
        }
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _04_orderFlow_validateHoldWithCurrency() {
        _04_orderFlow_validateHoldWithCurrencyPS(TcbBankService.SCHEMA, TcbBankService.class.getSimpleName(), Sets.newHashSet("RUB"));
        //
        _04_orderFlow_validateHoldWithCurrencyPS(NoonBankService.NOON_SCHEMA, NoonBankService.class.getSimpleName(), Sets.newHashSet("AED"));
        //
        _04_orderFlow_validateHoldWithCurrencyPS(Best2payAndTcbBankService.SCHEMA, Best2payAndTcbBankService.class.getSimpleName(), Sets.newHashSet("RUB"));
        //
        _04_orderFlow_validateHoldWithCurrencyPS(BoutiqueBankService.BOUTIQUE_SCHEMA, BoutiqueBankService.class.getSimpleName(), Sets.newHashSet("RUB"));
    }

}
