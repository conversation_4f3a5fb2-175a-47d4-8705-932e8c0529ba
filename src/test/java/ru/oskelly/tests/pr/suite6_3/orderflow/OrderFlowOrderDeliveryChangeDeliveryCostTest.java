package ru.oskelly.tests.pr.suite6_3.orderflow;

import com.google.common.collect.Lists;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.OrderFlowTest;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestTcbMock;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.domain.dao.delivery.DeliveryInfoRepository;
import su.reddot.domain.model.delivery.DeliveryInfo;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.currency.CurrencyService;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.bank.jobs.AgentPaymentJobs;
import su.reddot.infrastructure.util.CallInTransaction;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.Objects;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
@TestMethodOrder(MethodOrderer.MethodName.class)
public class OrderFlowOrderDeliveryChangeDeliveryCostTest extends OrderFlowTest {

    @Autowired
    private UserService userService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private CurrencyService currencyService;
    @Autowired
    private AgentPaymentJobs agentPaymentJobs;

    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private CartTestSupport cartTestSupport;
    @Autowired
    private CallInTransaction callInTransaction;

    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user-password}")
    private String password;
    @Value("${test-prepayments.usual-seller-id}")
    private Long usualSellerId;
    @Value("${test-prepayments.usual-seller-counterparty-id}")
    private Long usualSellerCounterpartyId;
    @Value("${test-prepayments.pickup-id}")
    private Long pickupId;
    @Value("${test-prepayments.delivery-id}")
    private Long deliveryId;

    private static OrderFlowTestTcbMock orderFlowTestTcbMock;

    @Value("${test.receipts.mock-server-host}")
    private String mockServerHost;
    @Value("${test.receipts.mock-server-tcb-bank-port}")
    private Integer mockTcbServerPort;

    @Autowired
    private DeliveryInfoRepository deliveryInfoRepository;

    private DeliveryInfo defaultDeliveryCost;

    private Long prepareAdminUser() {
        User adminUser = userService.getUserByEmail(buyerEmail);
        orderFlowTestUtils.enableUserAuthority(adminUser.getId(), AuthorityName.ORDER_PAYOUTS, true);
        orderFlowTestUtils.enableUserAuthority(adminUser.getId(), AuthorityName.ORDER_MANUAL_CHANGE_DELIVERY_STATE, true);
        return adminUser.getId();
    }

    @PostConstruct
    private void init() {
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(TcbBankService.SCHEMA));
        User buyer = userService.getUserByEmail(buyerEmail);
        ApiV2Client apiV2Client = new ApiV2Client(buyerEmail, password);
        orderFlowTestUtils.init(buyerEmail, password);
        cartTestSupport.setUserId(buyer.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
        orderFlowTestTcbMock = Objects.isNull(orderFlowTestTcbMock) ? new OrderFlowTestTcbMock(mockServerHost, mockTcbServerPort) : orderFlowTestTcbMock;
        callInTransaction.runInNewTransaction(this::prepareAdminUser);}

    @BeforeEach
    public void before() {
        defaultDeliveryCost = deliveryInfoRepository.findById(1L).orElse(null);
        configDeliveryCost(BigDecimal.ONE);
    }

    @AfterEach
    public void done() {
        orderFlowTestTcbMock.stop();
        configDeliveryCost(defaultDeliveryCost.getDeliveryPrice());
    }

    private void configDeliveryCost(BigDecimal deliveryCost) {
        DeliveryInfo deliveryInfo = deliveryInfoRepository.findById(1L).orElse(null);
        deliveryInfo.setDeliveryPrice(deliveryCost);
        deliveryInfoRepository.save(deliveryInfo);
    }

    @Test
    @Transactional
    @Rollback
//    @Disabled
    public void _01_orderFlow_orderDelivery_deliveryCostFrom1ToXXX() {
        //
        OrderFlowTestUtils.TestConfig testConfig = OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(1)

                .stopPosition(OrderFlowTestUtils.TestStopPosition.HOLD)

                .build();
        //
        OrderDTO orderWithDeliveryCost001 = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, testConfig);
        //
        configDeliveryCost(BigDecimal.valueOf(500));
        commitAndStartNewTransaction();
        //
        Assertions.assertThat(orderWithDeliveryCost001.getDeliveryCost()).isEqualByComparingTo(BigDecimal.ONE);
        //
        orderFlowTestUtils.changeAddressEndpoint(orderWithDeliveryCost001.getId(), testConfig.getPickupDeliveryAepId(), testConfig.getTargetDeliveryAepId());
        //
        OrderDTO orderWithDeliveryCostXXX = orderFlowTestUtils.loadOrderSuccessfull(orderWithDeliveryCost001.getId(), true);
        //
        Assertions.assertThat(orderWithDeliveryCostXXX.getDeliveryCost()).isEqualByComparingTo(BigDecimal.ONE);
    }

}
