package ru.oskelly.tests.build;

import org.junit.jupiter.api.Test;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.infrastructure.util.Utils;

import java.time.ZoneId;
import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class TestUtilsTest {
    @Test
    public void getApiHashedPasswordFine() {
        String email = "<EMAIL>";
        String password = "111";
        String realHashedPassword = "915f2dc77f6ba996147525ce3a6e36e5894999aabf155280e594c581705184aab69830fa5dfed44e7a086876548e2aabddf6c9fd432a7f9ad48791cd1f5b796f";
        String hashedPassword = TestUtils.getApiHashedPassword(password, email);
        assertEquals(realHashedPassword, hashedPassword);
    }

    //Форматирует даты в 2021-12-30T16:44:13Z
    //В случае с :00 секундами на конце добавляет одну секунду.
    @Test
    public void formatToUTC() {
        int year = 2021;
        int month = 12;
        int dayOfMonth = 30;
        int hour = 16;
        int minute = 46;
        int second = 23;
        ZonedDateTime dateTime = ZonedDateTime.of(year, month, dayOfMonth, hour, minute, second,
                34, ZoneId.of("UTC"));

        String expectedResult = year + "-" + month + "-" + dayOfMonth + "T" + hour + ":" + minute + ":" + second + "Z";
        assertEquals(expectedResult, Utils.formatToUTC(dateTime).toString());
        assertEquals(expectedResult, Utils.formatToUTCStr(dateTime));

        //Если в дате :00 секунд, то метод, возвращающий ZonedDateTime прибавляет 1 секунду, чтобы не обрезать нули при выводе DTO
        ZonedDateTime dateTimeZeroSeconds = dateTime.withSecond(0);
        String expectedResultZonedDateTime = year + "-" + month + "-" + dayOfMonth + "T" + hour + ":" + minute + ":01Z";
        String expectedResultStr = year + "-" + month + "-" + dayOfMonth + "T" + hour + ":" + minute + ":00Z";

        assertEquals(expectedResultZonedDateTime, Utils.formatToUTC(dateTimeZeroSeconds).toString());
        assertEquals(expectedResultStr, Utils.formatToUTCStr(dateTimeZeroSeconds));
    }
}
