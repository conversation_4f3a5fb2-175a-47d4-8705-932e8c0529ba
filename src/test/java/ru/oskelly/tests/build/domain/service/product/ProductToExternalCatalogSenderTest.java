package ru.oskelly.tests.build.domain.service.product;

import lombok.Data;
import lombok.SneakyThrows;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import ru.oskelly.tests.AbstractSpringTest;
import su.reddot.domain.dao.SizeRepository;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.attribute.AttributeValueRepository;
import su.reddot.domain.dao.product.ProductAttributeValueBindingRepository;
import su.reddot.domain.dao.product.ProductItemLocationRepository;
import su.reddot.domain.dao.product.ProductItemRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.dao.tags.UserCommonTagBindingRepository;
import su.reddot.domain.model.Brand;
import su.reddot.domain.model.category.Category;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductAttributeValueBinding;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.tags.UserCommonTagBinding;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.domain.service.externalcatalog.ProductsToExternalCatalogEventuallySender;
import su.reddot.domain.service.kafka.KafkaSenderService;
import su.reddot.infrastructure.util.CallInTransaction;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static su.reddot.domain.model.product.ProductState.DELETED;
import static su.reddot.domain.model.product.ProductState.DRAFT;
import static su.reddot.domain.model.product.ProductState.PUBLISHED;

public class ProductToExternalCatalogSenderTest extends AbstractSpringTest {

	@Autowired
	private ProductRepository productRepository;

	@Autowired
	private ProductItemLocationRepository productItemLocationRepository;

	@Autowired
	private CallInTransaction callInTransaction;

	@SpyBean
	private ProductsToExternalCatalogEventuallySender productsToExternalCatalogEventuallySender;

	@Autowired
	private ProductAttributeValueBindingRepository attributeValueBindingRepository;

	@Autowired
	private AttributeValueRepository attributeValueRepository;

	@Autowired
	private ProductItemRepository productItemRepository;

	@Autowired
	private SizeRepository sizeRepository;

	@Autowired
	private UserRepository userRepository;

	@Autowired
	private UserCommonTagBindingRepository usercommonTagBindingRepository;

	@Autowired
	private CommissionGridService commissionGridService;

	@MockBean
	private KafkaSenderService kafkaSenderService;


	@SneakyThrows
	@Test
	@Disabled("Отладочный тест для запуска вручную")
	public void testCreateOneProduct() {

		long sellerId = createUser().getId();

		reset(productsToExternalCatalogEventuallySender);
		callInTransaction.runInNewTransaction(() ->
			createProduct(new ProductCreationParams(PUBLISHED, sellerId)));

		Thread.sleep(1000L);

		verify(productsToExternalCatalogEventuallySender, times(1))
				.sendSaveCommand(any(), anyLong(), any());
		verify(productsToExternalCatalogEventuallySender, never())
				.sendDeleteCommand(anyLong(), anyLong(), anyLong());
	}

	@SneakyThrows
	@Test
	@Disabled("Отладочный тест для запуска вручную")
	public void testUpdateOneProductDifferentSerialTransactions() {

		long sellerId = createUser().getId();

		ProductCreationParams productCreationParams = new ProductCreationParams(DRAFT, sellerId);

		Long productId = createProduct(productCreationParams).getId();

		// изменение характеристик сущности продукта и связанных с ним сущностей в последовательных транзакциях
		reset(productsToExternalCatalogEventuallySender);
		callInTransaction.runInNewTransaction(() -> {
			Product product = productRepository.findById(productId).get();
			product.setProductState(PUBLISHED);
			productRepository.save(product);
		});
		// атрибуты: добавление
		callInTransaction.runInNewTransaction(() -> {
			Product product = productRepository.findById(productId).get();
			ProductAttributeValueBinding pavBinding = new ProductAttributeValueBinding();
			pavBinding.setAttributeValue(attributeValueRepository.findById(24L).get());
			pavBinding.setProduct(product);
			attributeValueBindingRepository.save(pavBinding);
			pavBinding = new ProductAttributeValueBinding();
			pavBinding.setAttributeValue(attributeValueRepository.findById(26L).get());
			pavBinding.setProduct(product);
			attributeValueBindingRepository.save(pavBinding);
		});
		// атрибуты: удаление
		callInTransaction.runInNewTransaction(() -> {
			Product product = productRepository.findById(productId).get();
			attributeValueBindingRepository.delete(product.getAttributeValues().get(0));
			attributeValueBindingRepository.delete(product.getAttributeValues().get(1));
		});
		// айтемы: добавление
		callInTransaction.runInNewTransaction(() -> {
			Product product = productRepository.findById(productId).get();
			createProductItem(product, false, 1);
			createProductItem(product, false, 2);
		});
		// айтемы: изменение
		callInTransaction.runInNewTransaction(() -> {
			Product product = productRepository.findById(productId).get();
			product.getProductItems().get(0).setCount(2);
			productItemRepository.save(product.getProductItems().get(0));
			product.getProductItems().get(0).setHidden(true);
			productItemRepository.save(product.getProductItems().get(1));
		});
		// айтемы: удаление
		callInTransaction.runInNewTransaction(() -> {
			Product product = productRepository.findById(productId).get();
			productItemRepository.delete(product.getProductItems().get(0));
		});
		// теги: добавление через добавление stocks в productItem
		callInTransaction.runInNewTransaction(() -> {
			Product product = productRepository.findById(productId).get();
			product.getProductItems().get(0).addOrUpdateStock(productItemLocationRepository.findById(1L).get(), 1, ZonedDateTime.now());
			productItemRepository.save(product.getProductItems().get(0));
			product.getProductItems().get(0).addOrUpdateStock(productItemLocationRepository.findById(2L).get(), 1, ZonedDateTime.now());
			productItemRepository.save(product.getProductItems().get(0));
		});
		// теги: удаление через удаление stocks в productItem
		callInTransaction.runInNewTransaction(() -> {
			Product product = productRepository.findById(productId).get();
			product.getProductItems().get(0).removeStock(productItemLocationRepository.findById(2L).get());
			productItemRepository.save(product.getProductItems().get(0));
		});

		// продавец: изменение характеристик сущности юзера
		callInTransaction.runInNewTransaction(() -> {
			Product product = productRepository.findById(productId).get();
			product.getSeller().setEmail(UUID.randomUUID() + "@email.com");
			userRepository.save(product.getSeller());
		});
		// продавец: добавление тегов юзера
		callInTransaction.runInNewTransaction(() -> {
			Product product = productRepository.findById(productId).get();
			usercommonTagBindingRepository.save(new UserCommonTagBinding(product.getSeller().getId(), 1L, 1L));
			usercommonTagBindingRepository.save(new UserCommonTagBinding(product.getSeller().getId(), 2L, 1L));

		});
		// продавец: удаление тегов юзера
		callInTransaction.runInNewTransaction(() -> {
			Product product = productRepository.findById(productId).get();
			usercommonTagBindingRepository.deleteAllByUserId(product.getSeller().getId());
		});

		Thread.sleep(1000L);

		verify(productsToExternalCatalogEventuallySender, times(11))
				.sendSaveCommand(any(), anyLong(), any());
		verify(productsToExternalCatalogEventuallySender, never())
				.sendDeleteCommand(anyLong(), anyLong(), anyLong());
	}

	@SneakyThrows
	@Test
	@Disabled("Отладочный тест для запуска вручную")
	public void testUpdateOneProductOneTransaction() {

		long sellerId = createUser().getId();

		ProductCreationParams productCreationParams = new ProductCreationParams(DRAFT, sellerId);

		Long productId = createProduct(productCreationParams).getId();

		// изменение характеристик сущности продукта и связанных с ним сущностей в одной транзакции
		reset(productsToExternalCatalogEventuallySender);
		callInTransaction.runInNewTransaction(() -> {

			Product product = productRepository.findById(productId).get();
			product.setProductState(PUBLISHED);
			productRepository.save(product);

			// атрибуты: добавление
			ProductAttributeValueBinding pavBinding = new ProductAttributeValueBinding();
			pavBinding.setAttributeValue(attributeValueRepository.findById(24L).get());
			pavBinding.setProduct(product);
			attributeValueBindingRepository.save(pavBinding);
			pavBinding = new ProductAttributeValueBinding();
			pavBinding.setAttributeValue(attributeValueRepository.findById(26L).get());
			pavBinding.setProduct(product);
			attributeValueBindingRepository.save(pavBinding);

			// айтемы: добавление
			ProductItem productItem1 = createProductItem(product, false, 1);
			ProductItem productItem2 = createProductItem(product, false, 2);

			// теги: добавление через добавление stocks в productItem
			productItem1.addOrUpdateStock(productItemLocationRepository.findById(1L).get(), 1, ZonedDateTime.now());
			productItemRepository.save(productItem1);
			productItem2.addOrUpdateStock(productItemLocationRepository.findById(2L).get(), 1, ZonedDateTime.now());
			productItemRepository.save(productItem1);

			// продавец: изменение характеристик сущности юзера
			product.getSeller().setEmail(UUID.randomUUID() + "@email.com");
			userRepository.save(product.getSeller());

			// продавец: добавление тегов юзера
			usercommonTagBindingRepository.save(new UserCommonTagBinding(product.getSeller().getId(), 1L, 1L));
			usercommonTagBindingRepository.save(new UserCommonTagBinding(product.getSeller().getId(), 2L, 1L));
		});

		Thread.sleep(1000L);

		verify(productsToExternalCatalogEventuallySender, times(1))
				.sendSaveCommand(any(), anyLong(), any());
		verify(productsToExternalCatalogEventuallySender, never())
				.sendDeleteCommand(anyLong(), anyLong(), anyLong());
	}

	@SneakyThrows
	@Test
	@Disabled("Отладочный тест для запуска вручную")
	public void testCreateTwoProductsDifferentSerialTransactions() {

		long sellerId = createUser().getId();

		// в двух последовательных транзакциях
		reset(productsToExternalCatalogEventuallySender);

		callInTransaction.runInNewTransaction(() ->
				createProduct(new ProductCreationParams(PUBLISHED, sellerId)));
		callInTransaction.runInNewTransaction(() ->
				createProduct(new ProductCreationParams(PUBLISHED, sellerId)));

		Thread.sleep(1000L);

		verify(productsToExternalCatalogEventuallySender, times(2))
				.sendSaveCommand(any(), anyLong(), any());
		verify(productsToExternalCatalogEventuallySender, never())
				.sendDeleteCommand(anyLong(), anyLong(), anyLong());
	}

	@SneakyThrows
	@Test
	@Disabled("Отладочный тест для запуска вручную")
	public void testCreateTwoProductsOneTransaction() {

		long sellerId = createUser().getId();

		// в одной транзакции
		reset(productsToExternalCatalogEventuallySender);

		callInTransaction.runInNewTransaction(() -> {
			createProduct(new ProductCreationParams(PUBLISHED, sellerId));
			createProduct(new ProductCreationParams(PUBLISHED, sellerId));
		});

		Thread.sleep(1000L);

		verify(productsToExternalCatalogEventuallySender, times(2))
				.sendSaveCommand(any(), anyLong(), any());
		verify(productsToExternalCatalogEventuallySender, never())
				.sendDeleteCommand(anyLong(), anyLong(), anyLong());
	}

	@SneakyThrows
	@Test
	@Disabled("Отладочный тест для запуска вручную")
	public void testCreateTwoProductsDifferentParallelTransactions() {

		long sellerId = createUser().getId();

		// в двух параллельных транзакциях
		reset(productsToExternalCatalogEventuallySender);

		ExecutorService executor = Executors.newFixedThreadPool(2);
		CompletableFuture<Void> feature1 = CompletableFuture.runAsync(() ->
				callInTransaction.runInNewTransaction(() ->
						createProduct(new ProductCreationParams(PUBLISHED, sellerId))), executor);
		CompletableFuture<Void> feature2 = CompletableFuture.runAsync(() ->
				callInTransaction.runInNewTransaction(() ->
						createProduct(new ProductCreationParams(PUBLISHED, sellerId))), executor);
		feature1.join();
		feature2.join();

		Thread.sleep(1000L);

		verify(productsToExternalCatalogEventuallySender, times(2))
				.sendSaveCommand(any(), anyLong(), any());
		verify(productsToExternalCatalogEventuallySender, never())
				.sendDeleteCommand(anyLong(), anyLong(), anyLong());
	}

	@SneakyThrows
	@Test
	@Disabled("Отладочный тест для запуска вручную")
	public void testUpdateTwoProductsDifferentSerialTransactions() {

		long seller1Id = createUser().getId();
		ProductCreationParams product1CreationParams = new ProductCreationParams(DRAFT, seller1Id);
		Long product1Id = createProduct(product1CreationParams).getId();

		long seller2Id = createUser().getId();
		ProductCreationParams product2CreationParams = new ProductCreationParams(DRAFT, seller2Id);
		Long product2Id = createProduct(product2CreationParams).getId();

		// в двух последовательных транзакциях
		reset(productsToExternalCatalogEventuallySender);
		callInTransaction.runInNewTransaction(() -> {

			Product product1 = productRepository.findById(product1Id).get();
			product1.setProductState(PUBLISHED);
			productRepository.save(product1);

			// атрибуты: добавление продукту 1
			ProductAttributeValueBinding pavBinding = new ProductAttributeValueBinding();
			pavBinding.setAttributeValue(attributeValueRepository.findById(24L).get());
			pavBinding.setProduct(product1);
			attributeValueBindingRepository.save(pavBinding);
			pavBinding = new ProductAttributeValueBinding();
			pavBinding.setAttributeValue(attributeValueRepository.findById(26L).get());
			pavBinding.setProduct(product1);
			attributeValueBindingRepository.save(pavBinding);

			// продавец: изменение характеристик сущности юзера по продукту 1
			product1.getSeller().setEmail(UUID.randomUUID() + "@email.com");
			userRepository.save(product1.getSeller());
		});

		callInTransaction.runInNewTransaction(() -> {

			Product product2 = productRepository.findById(product2Id).get();
			product2.setProductState(PUBLISHED);
			productRepository.save(product2);

			// айтемы: добавление продукту 2
			ProductItem productItem1 = createProductItem(product2, false, 1);
			ProductItem productItem2 = createProductItem(product2, false, 2);

			// теги: добавление через добавление stocks в productItem
			productItem1.addOrUpdateStock(productItemLocationRepository.findById(1L).get(), 1, ZonedDateTime.now());
			productItemRepository.save(productItem1);
			productItem2.addOrUpdateStock(productItemLocationRepository.findById(2L).get(), 1, ZonedDateTime.now());
			productItemRepository.save(productItem1);

			// продавец: добавление тегов юзера по продукту 2
			usercommonTagBindingRepository.save(new UserCommonTagBinding(product2.getSeller().getId(), 1L, 1L));
			usercommonTagBindingRepository.save(new UserCommonTagBinding(product2.getSeller().getId(), 2L, 1L));
		});

		Thread.sleep(1000L);

		verify(productsToExternalCatalogEventuallySender, times(2))
				.sendSaveCommand(any(), anyLong(), any());
		verify(productsToExternalCatalogEventuallySender, never())
				.sendDeleteCommand(anyLong(), anyLong(), anyLong());
	}

	@SneakyThrows
	@Test
	@Disabled("Отладочный тест для запуска вручную")
	public void testUpdateTwoProductsDifferentParallelTransactions() {

		long seller1Id = createUser().getId();
		ProductCreationParams product1CreationParams = new ProductCreationParams(DRAFT, seller1Id);
		Long product1Id = createProduct(product1CreationParams).getId();

		long seller2Id = createUser().getId();
		ProductCreationParams product2CreationParams = new ProductCreationParams(DRAFT, seller2Id);
		Long product2Id = createProduct(product2CreationParams).getId();

		reset(productsToExternalCatalogEventuallySender);
		// в двух параллельных транзакциях
		ExecutorService executor = Executors.newFixedThreadPool(2);
		CompletableFuture<Void> feature1 = CompletableFuture.runAsync(
				() -> callInTransaction.runInNewTransaction(() -> {
					Product product1 = productRepository.findById(product1Id).get();
					product1.setProductState(PUBLISHED);
					productRepository.save(product1);

					// атрибуты: добавление продукту 1
					ProductAttributeValueBinding pavBinding = new ProductAttributeValueBinding();
					pavBinding.setAttributeValue(attributeValueRepository.findById(24L).get());
					pavBinding.setProduct(product1);
					attributeValueBindingRepository.save(pavBinding);
					pavBinding = new ProductAttributeValueBinding();
					pavBinding.setAttributeValue(attributeValueRepository.findById(26L).get());
					pavBinding.setProduct(product1);
					attributeValueBindingRepository.save(pavBinding);

					// продавец: изменение характеристик сущности юзера по продукту 1
					product1.getSeller().setEmail(UUID.randomUUID() + "@email.com");
					userRepository.save(product1.getSeller());
				}), executor);
		CompletableFuture<Void> feature2 = CompletableFuture.runAsync(() ->
				callInTransaction.runInNewTransaction(() -> {
					Product product2 = productRepository.findById(product2Id).get();
					product2.setProductState(PUBLISHED);
					productRepository.save(product2);

					// айтемы: добавление продукту 2
					ProductItem productItem1 = createProductItem(product2, false, 1);
					ProductItem productItem2 = createProductItem(product2, false, 2);

					// теги: добавление через добавление stocks в productItem
					productItem1.addOrUpdateStock(productItemLocationRepository.findById(1L).get(), 1, ZonedDateTime.now());
					productItemRepository.save(productItem1);
					productItem2.addOrUpdateStock(productItemLocationRepository.findById(2L).get(), 1, ZonedDateTime.now());
					productItemRepository.save(productItem1);

					// продавец: добавление тегов юзера по продукту 2
					usercommonTagBindingRepository.save(new UserCommonTagBinding(product2.getSeller().getId(), 1L, 1L));
					usercommonTagBindingRepository.save(new UserCommonTagBinding(product2.getSeller().getId(), 2L, 1L));

				}), executor);
		feature1.join();
		feature2.join();

		Thread.sleep(1000L);

		verify(productsToExternalCatalogEventuallySender, times(2))
				.sendSaveCommand(any(), anyLong(), any());
		verify(productsToExternalCatalogEventuallySender, never())
				.sendDeleteCommand(anyLong(), anyLong(), anyLong());
	}

	@SneakyThrows
	@Test
	@Disabled("Отладочный тест для запуска вручную")
	public void testDeleteTwoProductsDifferentParallelTransactions() {

		long seller1Id = createUser().getId();
		ProductCreationParams product1CreationParams = new ProductCreationParams(PUBLISHED, seller1Id);
		Long product1Id = createProduct(product1CreationParams).getId();

		long seller2Id = createUser().getId();
		ProductCreationParams product2CreationParams = new ProductCreationParams(DRAFT, seller2Id);
		Long product2Id = createProduct(product2CreationParams).getId();

		reset(productsToExternalCatalogEventuallySender);
		// в двух параллельных транзакциях
		ExecutorService executor = Executors.newFixedThreadPool(2);
		CompletableFuture<Void> feature1 = CompletableFuture.runAsync(
				() -> callInTransaction.runInNewTransaction(() -> {
					Product product1 = productRepository.findById(product1Id).get();
					product1.setProductState(DELETED);
					productRepository.save(product1);

					// атрибуты: добавление продукту 1
					ProductAttributeValueBinding pavBinding = new ProductAttributeValueBinding();
					pavBinding.setAttributeValue(attributeValueRepository.findById(24L).get());
					pavBinding.setProduct(product1);
					attributeValueBindingRepository.save(pavBinding);
					pavBinding = new ProductAttributeValueBinding();
					pavBinding.setAttributeValue(attributeValueRepository.findById(26L).get());
					pavBinding.setProduct(product1);
					attributeValueBindingRepository.save(pavBinding);

				}), executor);
		CompletableFuture<Void> feature2 = CompletableFuture.runAsync(() ->
				callInTransaction.runInNewTransaction(() -> {
					Product product2 = productRepository.findById(product2Id).get();
					product2.setProductState(PUBLISHED);
					productRepository.save(product2);
				}), executor);
		feature1.join();
		feature2.join();

		Thread.sleep(1000L);

		verify(productsToExternalCatalogEventuallySender, times(1))
				.sendSaveCommand(any(), anyLong(), any());
		verify(productsToExternalCatalogEventuallySender, times(1))
				.sendDeleteCommand(anyLong(), anyLong(), anyLong());
	}

	@Data
	@Accessors(chain = true)
	private static class ProductCreationParams {
		private ProductState state;
		private long sellerId;
		private String vendorCode = null;
		private String storeCode = null;
		private BigDecimal currentPrice = BigDecimal.TEN;
		private LocalDateTime selectedConciergeTime = null;

		public ProductCreationParams(ProductState state, long sellerId) {
			this.state = state;
			this.sellerId = sellerId;
		}
	}

	private Product createProduct(ProductCreationParams params) {
		Product p = new Product();
		p.setCategoryId(category().getId());
		p.setProductConditionId(productConditionId());
		p.setBrand(brand());
		p.setDeliveryDescription("Delivery description");
		p.setDescription("Description");
		p.setModel("Model");
		p.setName("Name");
		p.setOrigin("Origin");
		p.setPaymentDescription("Payment description");
		p.setSeller(seller(params.getSellerId()));
		p.setProductState(params.getState());
		p.setVendorCode(params.getVendorCode());
		p.setStoreCode(params.getStoreCode());
		p.setCurrentPrice(params.getCurrentPrice());
		p.setSelectedConciergeTime(params.getSelectedConciergeTime());
		return productRepository.save(p);
	}

	private ProductItem createProductItem(Product product, boolean hidden, long sizeId) {
		ProductItem item = new ProductItem();
		item.setProduct(product);
		item.setDeleteTime(null);
		item.setHidden(hidden);
		item.setCount(1);
		item.setSize(sizeRepository.findById(sizeId).get());
		return productItemRepository.save(item);
	}

	private Brand brand() {
		Brand brand = new Brand();
		brand.setId(1L);
		brand.setName("Brand");

		return brand;
	}

	private Category category() {
		Category category = new Category();
		category.setId(4L);
		category.setDisplayName("Category");
		return category;
	}

	private Long productConditionId() {
		return 1L;
	}

	private User seller(Long id) {
		User seller = new User();
		seller.setNickname(RandomStringUtils.randomAlphabetic(5));
		seller.setUserType(User.UserType.SIMPLE_USER);
		seller.setId(id);
		seller.setRegistrationTime(ZonedDateTime.now());
		return seller;
	}

	private User createUser() {
		User user = new User();
		user.setNickname(RandomStringUtils.randomAlphabetic(5));
		user.setUserType(User.UserType.SIMPLE_USER);
		user.setChangeTime(LocalDateTime.now());
		user.setEmail(UUID.randomUUID() + "@acme.com");
		user.setRegistrationTime(ZonedDateTime.now());
		user.setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
		return userRepository.save(user);
	}
}
