package ru.oskelly.tests.build.domain.service.counterparty;

import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.address.AddressRepository;
import su.reddot.domain.dao.counterparty.CounterpartyRepository;
import su.reddot.domain.model.address.Address;
import su.reddot.domain.model.counterparty.Counterparty;
import su.reddot.domain.model.counterparty.IpCounterparty;
import su.reddot.domain.model.counterparty.JurCounterparty;
import su.reddot.domain.model.counterparty.PhysCounterparty;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.address.AddressService;
import su.reddot.domain.service.counterparty.CounterpartyService;
import su.reddot.domain.service.user.UserService;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class CounterpartyServiceTest extends AbstractSpringTest {
    @Autowired
    private CounterpartyService counterpartyService;
	@Autowired
    private AddressService addressService;
	@Autowired
	private UserService userService;

    @Autowired
    private CounterpartyRepository counterpartyRepository;
	@Autowired
	private AddressRepository addressRepository;

	@Value("${test.api.user-id}")
	private Long userId;
	private static String country = "Россия";
	private static String region = "Тамбовская обл.";
	private static String city = "г.Рассказово";
	private static String zipCode = "795634";
	private static String address = "ул.Советская, д.23, кв.10";

	private static String cntPassport = "3543 456462";
	private static String cntInnPhys = "*********901";
	private static String cntInnIp = "*********901";
	private static String cntInnJur = "*********9";
	private static String cntOGRN = "*********9012";
	private static String cntOGRNIP = "*********901234";
	private static String cntKPP = "*********";
	private static String cntCompanyForm = "ОАО";
	private static String cntCompanyName = "Оскелли";
	private static String cntBik = "*********";
	private static String cntCorrespondingAccount = "*********9*********9";
	private static String cntPaymentAccount = "98765432109876543210";
	private static String cntFirstName = "Иван";
	private static String cntPatronymicName = "Сергеевич";
	private static String cntLastName = "Белых";

	private static List<Long> counterpartyIds = new ArrayList<>();

    private User getUser(){
		return userService.getUserById(userId).orElse(null);
    }

    private Address getAddress(){
    	Address addr = new Address().setZipCode(zipCode).setCountry(country).setRegion(region).setCity(city).setAddress(address);
    	return addressService.saveAddress(getUser(), addr);
    }


    private void cleanup(){
		Address oldTestAddress = addressRepository.findFirstByUserAndZipCodeAndCountryAndRegionAndCityAndAddressAndAddress2AndAddress3AndCityFiasIdAndSettlementFiasIdAndDeleteTimeIsNull(getUser(), zipCode, country, region, city, address, null, null, null, null);
		if(oldTestAddress != null) addressRepository.delete(oldTestAddress);
	    List<Counterparty> counterparties = counterpartyService.findAllUserCounterparties(getUser());
	    for(Counterparty counterparty : counterparties){
	    	if(counterparty.getBik() == null || counterparty.getBik().equals(cntBik) || counterparty.getBik().equals("123456789" /*another test*/ )) counterpartyService.delete(counterparty);
	    }
    }

    private Counterparty saveCounterpartySuccessfully(Counterparty counterparty){
    	Class c = counterparty.getClass();
	    Counterparty savedCounterparty = counterpartyService.save(counterparty);
	    assertNotNull(savedCounterparty);
	    assertNotNull(savedCounterparty.getId());
	    assertTrue(c.isAssignableFrom(savedCounterparty.getClass()));
	    assertNotNull(counterparty.getUser());
	    assertNotNull(counterparty.getCreateTime());
	    return savedCounterparty;
    }

    private List<Counterparty> getSavedCounterparties(){
    	return counterpartyService.findAllUserCounterparties(getUser()).stream().filter(c -> counterpartyIds.contains(c.getId())).collect(Collectors.toList());
    }

    private void assertUserHasNoMoreThanOneActiveCounterparty(){
	    User user = getUser();
	    List<Counterparty> counterparties = counterpartyService.findAllUserCounterparties(user);
	    Counterparty activeCounterparty = null;
	    int foundActiveCounterpatriesCount = 0;
	    for(Counterparty counterparty : counterparties){
		    if(counterparty.getIsActive()){
			    activeCounterparty = counterparty;
			    foundActiveCounterpatriesCount++;
		    }
	    }
	    assertTrue(foundActiveCounterpatriesCount < 2);
	    if(activeCounterparty != null)
	        assertEquals(activeCounterparty.getId(), counterpartyService.findActiveUserCounterparty(user).getId());
    }

    @Test
    public void _00_init(){
    	cleanup();
    }

    @Test
    public void _01_createPhysCounterpartySuccessfull(){
	    long initialCount = counterpartyRepository.count();
	    User user = getUser();
	    Counterparty counterparty = new PhysCounterparty().setPassport(cntPassport)
	        .setFirstName(cntFirstName).setPatronymicName(cntPatronymicName).setLastName(cntLastName).setPhysAddress(getAddress())
		    .setBik(cntBik).setPaymentAccount(cntPaymentAccount).setCorrespondentAccount(cntCorrespondingAccount).setInn(cntInnPhys)
		    .setUser(user);
	    Counterparty savedCounterparty = saveCounterpartySuccessfully(counterparty);
	    if(initialCount == 0L) assertTrue(savedCounterparty.getIsActive());
	    assertEquals(initialCount + 1, counterpartyRepository.count());
	    assertTrue(savedCounterparty instanceof PhysCounterparty);
	    counterpartyIds.add(savedCounterparty.getId());
    }

	@Test
	public void _02_createIPCounterpartySuccessfull(){
		long initialCount = counterpartyRepository.count();
		User user = getUser();
		Counterparty counterparty = new IpCounterparty()
				.setJurAddress(getAddress()).setFirstName(cntFirstName).setPatronymicName(cntPatronymicName).setLastName(cntLastName).setPhysAddress(getAddress())
				.setBik(cntBik).setPaymentAccount(cntPaymentAccount).setCorrespondentAccount(cntCorrespondingAccount).setInn(cntInnIp)
				.setUser(user);
		Counterparty savedCounterparty = saveCounterpartySuccessfully(counterparty);
		assertFalse(savedCounterparty.getIsActive());
		assertEquals(initialCount + 1, counterpartyRepository.count());
		assertTrue(savedCounterparty instanceof IpCounterparty);
		counterpartyIds.add(savedCounterparty.getId());
	}

	@Test
	public void _03_createJurCounterpartySuccessfull(){
		long initialCount = counterpartyRepository.count();
		User user = getUser();
		Counterparty counterparty = new JurCounterparty()
				.setCompanyForm(cntCompanyForm).setCompanyName(cntCompanyName)
				.setJurAddress(getAddress()).setPhysAddress(getAddress())
				.setBik(cntBik).setPaymentAccount(cntPaymentAccount).setCorrespondentAccount(cntCorrespondingAccount).setInn(cntInnJur)
				.setUser(user);
		Counterparty savedCounterparty = saveCounterpartySuccessfully(counterparty);
		assertFalse(savedCounterparty.getIsActive());
		assertEquals(initialCount + 1, counterpartyRepository.count());
		assertTrue(savedCounterparty instanceof JurCounterparty);
		counterpartyIds.add(savedCounterparty.getId());
	}

	@Test
	public void _04_getCounterpartiesSuccessfull(){
		List<Counterparty> counterparties = getSavedCounterparties();
		assertTrue(counterparties.size() == 3);
	}

	@Test
	public void _05_getCounterpartyByIdSuccessfull(){
		Counterparty c;

		c = counterpartyService.findById(counterpartyIds.get(0));
		assertNotNull(c);
		assertTrue(c instanceof PhysCounterparty);

		c = counterpartyService.findById(counterpartyIds.get(1));
		assertNotNull(c);
		assertTrue(c instanceof IpCounterparty);

		c = counterpartyService.findById(counterpartyIds.get(2));
		assertNotNull(c);
		assertTrue(c instanceof JurCounterparty);
	}

	@Test
	public void _06_userHasOnlyOneActiveCounterparty(){
		assertUserHasNoMoreThanOneActiveCounterparty();
	}

	@Test
	public void _07_changeActiveCounterpartySuccessfully(){
		Counterparty newActiveCounterparty = counterpartyService.findById(counterpartyIds.get(1));
		counterpartyService.setActive(newActiveCounterparty);
		assertTrue(newActiveCounterparty.getIsActive());
		assertEquals(newActiveCounterparty.getId(), counterpartyService.findActiveUserCounterparty(newActiveCounterparty.getUser()).getId());
		assertUserHasNoMoreThanOneActiveCounterparty();
	}

	@Test
	public void _08_deleteCounterpartySuccessfully(){
		List<Counterparty> counterparties = getSavedCounterparties();
		for(int i = 0; i < counterparties.size(); i++){
			Counterparty counterparty = counterparties.get(i);
			counterpartyService.setDeleteTime(counterparty, ZonedDateTime.now());
			//Для всех, кроме последнего проверяем сохранение активного контрагента
			if(i != counterparties.size() - 1)
				assertUserHasNoMoreThanOneActiveCounterparty();
		}
	}

	@Test
	public void _99_destroy(){
		cleanup();
	}

}
