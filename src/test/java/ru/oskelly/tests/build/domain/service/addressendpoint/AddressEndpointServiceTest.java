package ru.oskelly.tests.build.domain.service.addressendpoint;

import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import lombok.NonNull;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.address.AddressRepository;
import su.reddot.domain.dao.addressendpoint.AddressEndpointRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.model.address.Address;
import su.reddot.domain.model.addressendpoint.AddressEndpoint;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.address.AddressService;
import su.reddot.domain.service.addressendpoint.AddressEndpointService;
import su.reddot.domain.service.adminpanel.orders.dto.SaveAddressEndpointParams;
import su.reddot.domain.service.dto.AddressDTO;
import su.reddot.domain.service.dto.AddressEndpointDTO;
import su.reddot.domain.service.user.UserService;

import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class AddressEndpointServiceTest extends AbstractSpringTest {
	@Autowired
	private AddressService addressService;
    @Autowired
    private AddressEndpointService addressEndpointService;
	@Autowired
	private UserService userService;

    @Autowired
    private AddressEndpointRepository addressEndpointRepository;
    @Autowired
    private OrderRepository orderRepository;

	@Autowired
	private AddressRepository addressRepository;

	@Value("${test.api.user-id}")
	private Long userId;

	@Value("${test.api.address-endpoint-test-order-id}")
	private Long orderId;

	private static final String country = "Россия";
	private static final String region = "Тамбовская обл.";
	private static final String city = "г.Тамбов";
	private static final String zipCode = "435623";
	private static final String address = "ул.Пушкина, д.Колотушкина, кв.67";
	private static final String cityFiasId = "ea2a1270-1e19-4224-b1a0-4228b9de3c7a";
	private static final String firstName = "Иван";
	private static final String patronymicName = "Сергеевич";
	private static final String lastName = "Белых";
	private static final String phone = "+79202341740";
	private static final String phone2 = "+79204771677";

	private static Long addressEndpointId;
	private static Long addressEndpoint2Id;

    private User getUser(){
		return userService.getUserById(userId).orElse(null);
    }

	private Address getAddress(){
		return new Address()
				.setZipCode(zipCode)
				.setCountry(country)
				.setRegion(region)
				.setCity(city)
				.setCityFiasId(cityFiasId)
				.setAddress(address);
	}

	@NonNull
	private static AddressDTO getAddressDto() {
		return new AddressDTO()
				.setZipCode(zipCode)
				.setCountry(country)
				.setRegion(region)
				.setCity(city)
				.setCityFiasId(cityFiasId)
				.setAddress(address);
	}

	private AddressEndpoint getAddressEndpoint(){
		return new AddressEndpoint().setAddress(getAddress()).setPhone(phone).setFirstName(firstName).setPatronymicName(patronymicName).setLastName(lastName);
	}

    private AddressEndpoint saveAddressEndpointSuccessfull(AddressEndpoint addressEndpoint, User user){
	    AddressEndpoint savedAddressEndpoint = addressEndpointService.save(user, addressEndpoint);
	    assertNotNull(savedAddressEndpoint);
	    assertNotNull(savedAddressEndpoint.getId());
	    assertNotNull(savedAddressEndpoint.getUser());
	    return savedAddressEndpoint;
    }

    private void cleanup(){
    	User user = getUser();
    	Address addr = addressService.getAddress(user, getAddressDto());

		AddressEndpoint oldTestAddressEndpoint = addressEndpointRepository.findFirstByUserAndFirstNameAndPatronymicNameAndLastNameAndPhoneAndAddress(
				user, firstName, patronymicName, lastName, phone, addr
		);
		if(oldTestAddressEndpoint != null) addressEndpointRepository.delete(oldTestAddressEndpoint);

	    AddressEndpoint oldTestAddressEndpoint2 = addressEndpointRepository.findFirstByUserAndFirstNameAndPatronymicNameAndLastNameAndPhoneAndAddress(
			    user, firstName, patronymicName, lastName, phone2, addr
	    );
	    if(oldTestAddressEndpoint2 != null) addressEndpointRepository.delete(oldTestAddressEndpoint2);
	    addressEndpointRepository.flush();

	    if(addr != null) addressRepository.delete(addr);
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _01_addressEndpointCreationSuccessfull(){
		long initialAddressEndpointsCount = addressEndpointRepository.count();
		User user = getUser();
		AddressEndpoint addressEndpoint = getAddressEndpoint();
		AddressEndpoint savedAddressEndpoint = saveAddressEndpointSuccessfull(addressEndpoint, user);
	    addressEndpointId = savedAddressEndpoint.getId();
	    long newAddressEndpointsCount = addressEndpointRepository.count();
	    assertEquals(newAddressEndpointsCount, initialAddressEndpointsCount + 1);
    }

	@Test
	@Transactional
	@Rollback(false)
	public void _02_addressEndpointCreationSameReturnsOldAddressEndpoint(){
		long initialAddressEndpointsCount = addressEndpointRepository.count();
		User user = getUser();
		AddressEndpoint addressEndpoint = getAddressEndpoint();
		AddressEndpoint savedAddressEndpoint = saveAddressEndpointSuccessfull(addressEndpoint, user);
		long newAddressEndpointsCount = addressEndpointRepository.count();
		assertEquals(newAddressEndpointsCount, initialAddressEndpointsCount);
		assertEquals(savedAddressEndpoint.getId(), addressEndpointId);
	}

	@Test
	@Transactional
	@Rollback(false)
	public void _03_getAddressEndpointReturnsOldAddressEndpoint(){
		long initialAddressEndpointsCount = addressEndpointRepository.count();
		User user = getUser();
		AddressEndpointDTO addressEndpointDto = new AddressEndpointDTO()
				.setFirstName(firstName)
				.setPatronymicName(patronymicName)
				.setLastName(lastName)
				.setPhone(phone)
				.setAddress(new AddressDTO()
						.setZipCode(zipCode)
						.setCountry(country)
						.setRegion(region)
						.setCity(city)
						.setCityFiasId(cityFiasId)
						.setAddress(address));
		SaveAddressEndpointParams params = SaveAddressEndpointParams.builder()
				.addressEndpoint(addressEndpointDto).user(user).includedDeliveryCost(false)
				.build();
		AddressEndpointDTO returnedAddressEndpoint = addressEndpointService.save(params);
		long newAddressEndpointsCount = addressEndpointRepository.count();
		assertEquals(newAddressEndpointsCount, initialAddressEndpointsCount);
		assertEquals(returnedAddressEndpoint.getId(), addressEndpointId);
	}

	@Test
	@Transactional
	@Rollback(false)
	public void _04_addressEndpointEditChangesOldAddressEndpointWhenItIsNotUsedInOrders(){
		long initialAddressEndpointsCount = addressEndpointRepository.count();
		User user = getUser();
		AddressEndpoint addressEndpoint = addressEndpointService.findById(addressEndpointId);
		addressEndpoint.setPhone(phone2);
		AddressEndpoint savedAddressEndpoint = saveAddressEndpointSuccessfull(addressEndpoint, user);
		addressEndpoint2Id = savedAddressEndpoint.getId();
		long newAddressEndpointsCount = addressEndpointRepository.count();
		assertEquals(phone2, savedAddressEndpoint.getPhone());
		assertEquals(newAddressEndpointsCount, initialAddressEndpointsCount);
		assertEquals(savedAddressEndpoint.getId(), addressEndpointId);
	}

	@Test
	@Transactional
	@Rollback(false)
	public void _05_addressEndpoinEditCreatesNewAddressEndpointWhenItIsUsedInOrders_0_prepare(){
		long initialAddressEndpointsCount = addressEndpointRepository.count();
		User user = getUser();
		AddressEndpoint addressEndpoint = addressEndpointService.findById(addressEndpointId);

		Order order = orderRepository.findById(orderId).orElse(null);
		order.setDeliveryAddressEndpoint(addressEndpoint);
		orderRepository.saveAndFlush(order);
	}

	@Test
	public void _05_addressEndpoinEditCreatesNewAddressEndpointWhenItIsUsedInOrders_1(){
		long initialAddressEndpointsCount = addressEndpointRepository.count();
		User user = getUser();
		AddressEndpoint addressEndpoint = addressEndpointService.findById(addressEndpointId);

		addressEndpoint.setPhone(phone);
		AddressEndpoint savedAddressEndpoint = saveAddressEndpointSuccessfull(addressEndpoint, user);
		addressEndpoint2Id = savedAddressEndpoint.getId();
		long newAddressEndpointsCount = addressEndpointRepository.count();
		assertEquals(phone, savedAddressEndpoint.getPhone());
		assertEquals(newAddressEndpointsCount, initialAddressEndpointsCount + 1);
		assertNotEquals(savedAddressEndpoint.getId(), addressEndpointId);
	}

	@Test
	@Transactional
	@Rollback(false)
	public void _10_findAllUserAddressEndpointsSuccessfull(){
		User user = getUser();
		List<AddressEndpoint> userAddressEndpoints = addressEndpointService.findAllUserActiveAddressEndpoints(user);
		List<Long> userAddressEndpointIds = userAddressEndpoints.stream().map(AddressEndpoint::getId).collect(Collectors.toList());
		assertTrue(userAddressEndpointIds.contains(addressEndpointId));
		assertTrue(userAddressEndpointIds.contains(addressEndpoint2Id));
	}

	@Test
	@Transactional
	@Rollback(false)
	public void _80_cleanup(){
		Order order = orderRepository.findById(orderId).orElse(null);
		order.setDeliveryAddressEndpoint(null);
		orderRepository.saveAndFlush(order);

		if(addressEndpointId != null) addressEndpointRepository.deleteById(addressEndpointId);
		if(addressEndpoint2Id != null) addressEndpointRepository.deleteById(addressEndpoint2Id);

		cleanup();
	}
}
