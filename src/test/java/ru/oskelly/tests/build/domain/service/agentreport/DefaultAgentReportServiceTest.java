package ru.oskelly.tests.build.domain.service.agentreport;

import lombok.NonNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.support.MessageSourceAccessor;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.agentreport.AgentReportRepository;
import su.reddot.domain.dao.agentreportpayments.AgentReportPaymentsRepository;
import su.reddot.domain.dao.bankaccount.BankPaymentRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.model.agentreport.AgentReport;
import su.reddot.domain.model.counterparty.CardCounterparty;
import su.reddot.domain.model.counterparty.Counterparty;
import su.reddot.domain.model.counterparty.PhysCounterparty;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.service.agentreport.CommissionAgentReportBuilder;
import su.reddot.domain.service.agentreport.DefaultAgentReportService;
import su.reddot.domain.service.bonuses.BonusesService;
import su.reddot.domain.service.catalog.CategoryService;
import su.reddot.domain.service.community.CommunityService;
import su.reddot.domain.service.counterparty.CounterpartyService;
import su.reddot.domain.service.currency.CurrencyConverterFactory;
import su.reddot.domain.service.currency.CurrencyService;
import su.reddot.domain.service.integration.logistic.LogisticEventProcessor;
import su.reddot.domain.service.integration.orderprocessing.OPExporter;
import su.reddot.domain.service.integration.orderprocessing.OrderProcessingService;
import su.reddot.domain.service.order.OrderDocumentsService;
import su.reddot.domain.service.order.impl.OrderExtraPropsService;
import su.reddot.domain.service.payoutrequest.PayoutRequestService;
import su.reddot.domain.service.setting.FeatureFlagsSettingService;
import su.reddot.domain.service.userbalance.UserBalanceService;
import su.reddot.infrastructure.bank.jobs.AgentPaymentJobs;
import su.reddot.infrastructure.configparam.ConfigParamService;
import su.reddot.infrastructure.logistic.service.LogisticServiceProperties;
import su.reddot.infrastructure.messaging.SyncOrderService;
import su.reddot.infrastructure.security.SecurityService;
import su.reddot.presentation.PDFUtils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.mock;

/**
 * Unit tests for {@link DefaultAgentReportService}.
 */
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class DefaultAgentReportServiceTest {

    private static final String PAYMENT_ACCOUNT = "******************11";
    private static final String BIK = "*********";

    private Order order;
    private AgentReport agentReport;

    // SUT
    private DefaultAgentReportService service;

    @BeforeEach
    public void setUp() {
        order = new Order();

        agentReport = new AgentReport();
        agentReport.setOrder(order);
        agentReport.setCardRefId("0");
        agentReport.setCardNumber("****************");
        agentReport.setCardHolder("Card Holder");
        agentReport.setCardBrand("VISA");

        service = new DefaultAgentReportService(
                mock(AgentReportRepository.class),
                mock(UserBalanceService.class),
                mock(AgentPaymentJobs.class),
                mock(CategoryService.class),
                mock(SecurityService.class),
                mock(ApplicationEventPublisher.class),
                mock(BankPaymentRepository.class),
                mock(AgentReportPaymentsRepository.class),
                mock(FeatureFlagsSettingService.class),
                mock(CurrencyService.class),
                mock(SyncOrderService.class),
                mock(CounterpartyService.class),
                mock(CommunityService.class),
                mock(BonusesService.class),
                mock(CurrencyConverterFactory.class),
                mock(CommissionAgentReportBuilder.class),
                mock(MessageSourceAccessor.class),
                mock(PDFUtils.class),
                mock(OrderDocumentsService.class),
                mock(OrderRepository.class),
                mock(ConfigParamService.class),
                mock(OrderExtraPropsService.class),
                mock(LogisticEventProcessor.class),
                mock(LogisticServiceProperties.class),
                mock(PayoutRequestService.class),
                mock(OrderProcessingService.class));
    }

    /**
     * Проверяем обновление реквизитов карты отчета агента.
     */
    @Test
    public void testUpdateCard() {
        // ARRANGE
        final CardCounterparty counterparty = new CardCounterparty();
        setCounterpartyFields(counterparty);
        final String newCardRefId = "1";
        counterparty.setCardRefId(newCardRefId);
        final String newCardNumber = "*********1111111";
        counterparty.setCardNumber(newCardNumber);
        final String newCardHolder = "New Card Holder";
        counterparty.setCardHolder(newCardHolder);
        final String newCardBrand = "MASTERCARD";
        counterparty.setCardBrand(newCardBrand);
        order.setSellerCounterparty(counterparty);

        // ACT
        service.update(agentReport);

        // ASSERT
        checkAgentReport();
        assertEquals(newCardRefId, agentReport.getCardRefId());
        assertEquals(newCardNumber, agentReport.getCardNumber());
        assertEquals(newCardHolder, agentReport.getCardHolder());
        assertEquals(newCardBrand, agentReport.getCardBrand());
    }

    /**
     * Проверяем переключение отчета агента с карты на банковский аккаунт.
     */
    @Test
    public void testChangeCardToBankAccount() {
        // ARRANGE
        final Counterparty counterparty = new PhysCounterparty();
        setCounterpartyFields(counterparty);
        order.setSellerCounterparty(counterparty);

        // ACT
        service.update(agentReport);

        // ASSERT
        checkAgentReport();
        assertNull(agentReport.getCardRefId());
        assertNull(agentReport.getCardNumber());
        assertNull(agentReport.getCardHolder());
        assertNull(agentReport.getCardBrand());
    }

    private static void setCounterpartyFields(@NonNull final Counterparty counterparty) {
        counterparty.setPaymentAccount(PAYMENT_ACCOUNT);
        counterparty.setBik(BIK);
    }

    private void checkAgentReport() {
        assertEquals(PAYMENT_ACCOUNT, agentReport.getPaymentAccount());
        assertEquals(BIK, agentReport.getBik());
    }
}
