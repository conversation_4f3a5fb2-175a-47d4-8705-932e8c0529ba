package ru.oskelly.tests.build.domain.service.product;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.SizeRepository;
import su.reddot.domain.dao.order.OrderPositionRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.product.ProductItemRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.Brand;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.order.OrderPositionState;
import su.reddot.domain.model.order.OrderSource;
import su.reddot.domain.model.order.OrderSourceInfo;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.product.ProductTagType;
import su.reddot.domain.model.user.SellerType;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.domain.service.dto.order.OrderSourceInfoParams;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.ordersourceinfo.OrderSourceInfoService;
import su.reddot.domain.service.product.ProductItemStocksUpdatingStrategyImpl;
import su.reddot.domain.service.product.ProductService;
import su.reddot.domain.service.product.item.ProductItemService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;
import static su.reddot.domain.model.order.OrderSource.BOUTIQUE;
import static su.reddot.domain.model.order.OrderSourceInfo.ORDER_SOURCE_INFO_BOUTIQUE_KUZN_MOST_NAME;
import static su.reddot.domain.model.order.OrderSourceInfo.ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME;
import static su.reddot.domain.model.order.OrderSourceInfo.ORDER_SOURCE_INFO_OSKELLY_STOCK_NAME;
import static su.reddot.domain.model.order.OrderSourceInfo.ORDER_SOURCE_INFO_SOLD_IN_OSKELLY_NAME;
import static su.reddot.domain.model.order.OrderState.CREATED;
import static su.reddot.domain.model.order.OrderState.HOLD_COMPLETED;
import static su.reddot.domain.model.order.OrderState.RETURN;
import static su.reddot.domain.model.product.ProductItemLocation.LOCATION_CODE_BOUTIQUE_KUZNETSKY_BRIDGE;
import static su.reddot.domain.model.product.ProductItemLocation.LOCATION_CODE_BOUTIQUE_STOLESHNIKOV;
import static su.reddot.domain.model.product.ProductItemLocation.LOCATION_CODE_WAREHOUSE;
import static su.reddot.domain.model.product.ProductState.NEED_RETOUCH;
import static su.reddot.domain.model.product.ProductState.PUBLISHED;
import static su.reddot.domain.service.product.ProductService.UserType.SYSTEM;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class ProductItemStocksUpdatingTest extends AbstractSpringTest {
    @Autowired
    private ProductItemStocksUpdatingStrategyImpl optimizedProductItemsStocksUpdatingStrategy;
    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private ProductRepository productRepository;

    @Autowired
    private ProductItemRepository productItemRepository;

    @Autowired
    private OrderPositionRepository orderPositionRepository;

    @Autowired
    private OrderSourceInfoService orderSourceInfoService;

    @Autowired
    private SizeRepository sizeRepository;

    @Autowired
    private ProductService productService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private ProductItemService productItemService;

    private static final long SELLER_USER_ID = 19554L;

    @Test
    @Transactional
    public void testUpdate() {

        OrderSourceInfo stolOrderSourceInfo = orderSourceInfoService.findOrderSourceInfoByNameCached(
                ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME).get();
        OrderSourceInfo kuznOrderSourceInfo = orderSourceInfoService.findOrderSourceInfoByNameCached(
                ORDER_SOURCE_INFO_BOUTIQUE_KUZN_MOST_NAME).get();
        OrderSourceInfo stockOrderSourceInfo = orderSourceInfoService.findOrderSourceInfoByNameCached(
                ORDER_SOURCE_INFO_OSKELLY_STOCK_NAME).get();

        Product product1 = createProduct(PUBLISHED);
        ProductItem productItem1 = createProductItem(product1, false);

        Product product2 = createProduct(PUBLISHED);
        ProductItem productItem2 = createProductItem(product2, false);

        Product product3 = createProduct(PUBLISHED);
        ProductItem productItem3 = createProductItem(product3, false);

        // продукт и айтем, для которого stocks пересчитываться будут, несмотря на статус продукта
        Product product4 = createProduct(NEED_RETOUCH);
        ProductItem productItem4 = createProductItem(product4, false);

        // продукт и айтем, для которого stocks пересчитываться будут, несмотря на то, что айтем скрыт
        Product product5 = createProduct(PUBLISHED);
        ProductItem productItem5 = createProductItem(product5, true);

        Product product6 = createProduct(PUBLISHED);
        ProductItem productItem6 = createProductItem(product6, false);

        Order order1 = createOrder(HOLD_COMPLETED, BOUTIQUE, stolOrderSourceInfo);
        createOrderPosition(order1, productItem1);
        createOrderPosition(order1, productItem2);
        createOrderPosition(order1, productItem4);

        Order order2 = createOrder(HOLD_COMPLETED, BOUTIQUE, stockOrderSourceInfo);
        createOrderPosition(order2, productItem1);
        createOrderPosition(order2, productItem3);
        createOrderPosition(order2, productItem4);
        createOrderPosition(order2, productItem5);

        Order order3 = createOrder(HOLD_COMPLETED, BOUTIQUE, stockOrderSourceInfo);
        createOrderPosition(order3, productItem1);
        createOrderPosition(order3, productItem4);
        createOrderPosition(order3, productItem5);

        // заказ, который не влияет на stocks из-за статуса
        Order order4 = createOrder(RETURN, BOUTIQUE, stolOrderSourceInfo);
        createOrderPosition(order4, productItem1);
        createOrderPosition(order4, productItem2);

        Order order5 = createOrder(HOLD_COMPLETED, BOUTIQUE, kuznOrderSourceInfo);
        createOrderPosition(order5, productItem6);

        commitAndStartNewTransaction();

        // запускаем обновление productItem stocks, будет выполнена первоначальное заполнение stocks

        optimizedProductItemsStocksUpdatingStrategy.updateProductItemStocks();

        commitAndStartNewTransaction();

        assertProductLocationDataCorrect(
                productItem1.getId(),
                ImmutableMap.of(LOCATION_CODE_BOUTIQUE_STOLESHNIKOV, 1, LOCATION_CODE_WAREHOUSE, 2),
                ImmutableList.of(ProductTagType.BOUTIQUE_STOLESHNIKOV.name(), ProductTagType.WAREHOUSE.name()),
                true);

        assertProductLocationDataCorrect(
                productItem2.getId(),
                ImmutableMap.of(LOCATION_CODE_BOUTIQUE_STOLESHNIKOV, 1),
                ImmutableList.of(ProductTagType.BOUTIQUE_STOLESHNIKOV.name()),
                true);

        assertProductLocationDataCorrect(
                productItem3.getId(),
                ImmutableMap.of(LOCATION_CODE_WAREHOUSE, 1),
                ImmutableList.of(ProductTagType.WAREHOUSE.name()),
                false);

        assertProductLocationDataCorrect(
                productItem4.getId(),
                ImmutableMap.of(LOCATION_CODE_BOUTIQUE_STOLESHNIKOV, 1, LOCATION_CODE_WAREHOUSE, 2),
                ImmutableList.of(ProductTagType.BOUTIQUE_STOLESHNIKOV.name(), ProductTagType.WAREHOUSE.name()),
                true);

        // productItem.hidden == true, поэтому айтем не влияет на набор тегов
        assertProductLocationDataCorrect(
                productItem5.getId(),
                ImmutableMap.of(LOCATION_CODE_WAREHOUSE, 2),
                ImmutableList.of(),
                false);

        assertProductLocationDataCorrect(
                productItem6.getId(),
                ImmutableMap.of(LOCATION_CODE_BOUTIQUE_KUZNETSKY_BRIDGE, 1),
                ImmutableList.of(ProductTagType.BOUTIQUE_KUZNETSKY_BRIDGE.name()),
                true);

        // обновляем статус заказа на RETURN, тем самым отменяем его влияние на productItem stocks

        order1 = orderRepository.findById(order1.getId()).get();
        order1.setState(RETURN);
        order1 = orderRepository.save(order1);

        // запускаем обновление productItem stocks, будет выполнен пересчет всех stocks

        optimizedProductItemsStocksUpdatingStrategy.updateProductItemStocks();

        commitAndStartNewTransaction();

        assertProductLocationDataCorrect(
                productItem1.getId(),
                ImmutableMap.of(LOCATION_CODE_WAREHOUSE, 2),
                ImmutableList.of(ProductTagType.WAREHOUSE.name()),
                false);

        assertProductLocationDataCorrect(
                productItem2.getId(),
                ImmutableMap.of(),
                ImmutableList.of(),
                false);

        assertProductLocationDataCorrect(
                productItem3.getId(),
                ImmutableMap.of(LOCATION_CODE_WAREHOUSE, 1),
                ImmutableList.of(ProductTagType.WAREHOUSE.name()),
                false);

        assertProductLocationDataCorrect(
                productItem4.getId(),
                ImmutableMap.of(LOCATION_CODE_WAREHOUSE, 2),
                ImmutableList.of(ProductTagType.WAREHOUSE.name()),
                false);

        // productItem.hidden == true, поэтому айтем не влияет на набор тегов
        assertProductLocationDataCorrect(
                productItem5.getId(),
                ImmutableMap.of(LOCATION_CODE_WAREHOUSE, 2),
                ImmutableList.of(),
                false);
    }

    private void assertProductLocationDataCorrect(Long productItemId, ImmutableMap<String, Integer> productItemLocations,
            ImmutableList<String> productTagNames, boolean inBoutique) {

        ProductItem productItem = productItemRepository.findById(productItemId).get();
        assertEquals(productItemLocations.size(), productItem.getStocks().size());
        productItemLocations.forEach((locationName, count) ->
                assertTrue(productItem.getStocks().stream()
                        .anyMatch(it -> it.getLocation().getCode().equals(locationName)
                                && it.getCount() == count)));
        assertEquals(inBoutique, productItem.isInBoutique());

        Product product = productRepository.findById(productItem.getProduct().getId()).get();
        assertEquals(productTagNames.size(), product.getTags().size());
        productTagNames.forEach(tagName ->
                assertTrue(product.getTags().stream()
                        .anyMatch(it -> it.getCode().equals(tagName))));
        assertEquals(inBoutique, product.isInBoutique());

        ProductDTO productDTO = productService.getProductDTO(productItem.getProduct().getId(), SYSTEM);
        assertEquals(productTagNames.size(), productDTO.getTags().size());
        assertEquals(inBoutique, productDTO.getInBoutique());
    }

    @Test
    @DisplayName("Тест обновления order.orderSourceInfo с последующим обновлением productItem.stocks")
    @Transactional
    public void testSetOrderSourceInfo() {

        Product product1 = createProduct(PUBLISHED);
        ProductItem productItem1 = createProductItem(product1, false);

        Product product2 = createProduct(PUBLISHED);
        ProductItem productItem2 = createProductItem(product2, false);

        Product product3 = createProduct(PUBLISHED);
        ProductItem productItem3 = createProductItem(product3, false);

        Order order1 = createOrder(HOLD_COMPLETED, BOUTIQUE, null);
        createOrderPosition(order1, productItem1);
        createOrderPosition(order1, productItem2);

        Order order2 = createOrder(HOLD_COMPLETED, BOUTIQUE, null);
        createOrderPosition(order2, productItem1);
        createOrderPosition(order2, productItem3);

        commitAndStartNewTransaction();

        OrderSourceInfo stolOrderSourceInfo = orderSourceInfoService.findOrderSourceInfoByNameCached(
                ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME).get();
        OrderSourceInfo stockOrderSourceInfo = orderSourceInfoService.findOrderSourceInfoByNameCached(
                ORDER_SOURCE_INFO_OSKELLY_STOCK_NAME).get();
        OrderSourceInfo soldOrderSourceInfo = orderSourceInfoService.findOrderSourceInfoByNameCached(
                ORDER_SOURCE_INFO_SOLD_IN_OSKELLY_NAME).get();

        // перемещаем первый заказ в бутик

        orderService.setOrderSourceInfo(new OrderSourceInfoParams(order1.getId(), stolOrderSourceInfo.getId(), true, true, true));

        commitAndStartNewTransaction();

        assertProductLocationDataCorrect(
                productItem1.getId(),
                ImmutableMap.of(LOCATION_CODE_BOUTIQUE_STOLESHNIKOV, 1),
                ImmutableList.of(ProductTagType.BOUTIQUE_STOLESHNIKOV.name()),
                true);

        assertProductLocationDataCorrect(
                productItem2.getId(),
                ImmutableMap.of(LOCATION_CODE_BOUTIQUE_STOLESHNIKOV, 1),
                ImmutableList.of(ProductTagType.BOUTIQUE_STOLESHNIKOV.name()),
                true);

        assertProductLocationDataCorrect(
                productItem3.getId(),
                ImmutableMap.of(),
                ImmutableList.of(),
                false);

        // перемещаем второй заказ на склад

        orderService.setOrderSourceInfo(new OrderSourceInfoParams(order2.getId(), stockOrderSourceInfo.getId(), true, true, true));

        commitAndStartNewTransaction();

        assertProductLocationDataCorrect(
                productItem1.getId(),
                ImmutableMap.of(LOCATION_CODE_BOUTIQUE_STOLESHNIKOV, 1, LOCATION_CODE_WAREHOUSE, 1),
                ImmutableList.of(ProductTagType.BOUTIQUE_STOLESHNIKOV.name(), ProductTagType.WAREHOUSE.name()),
                true);

        assertProductLocationDataCorrect(
                productItem2.getId(),
                ImmutableMap.of(LOCATION_CODE_BOUTIQUE_STOLESHNIKOV, 1),
                ImmutableList.of(ProductTagType.BOUTIQUE_STOLESHNIKOV.name()),
                true);

        assertProductLocationDataCorrect(
                productItem3.getId(),
                ImmutableMap.of(LOCATION_CODE_WAREHOUSE, 1),
                ImmutableList.of(ProductTagType.WAREHOUSE.name()),
                false);

        // перемещаем второй заказ со склада тоже в бутик

        orderService.setOrderSourceInfo(new OrderSourceInfoParams(order2.getId(), stolOrderSourceInfo.getId(), true, true, true));

        commitAndStartNewTransaction();

        assertProductLocationDataCorrect(
                productItem1.getId(),
                ImmutableMap.of(LOCATION_CODE_BOUTIQUE_STOLESHNIKOV, 2),
                ImmutableList.of(ProductTagType.BOUTIQUE_STOLESHNIKOV.name()),
                true);

        assertProductLocationDataCorrect(
                productItem2.getId(),
                ImmutableMap.of(LOCATION_CODE_BOUTIQUE_STOLESHNIKOV, 1),
                ImmutableList.of(ProductTagType.BOUTIQUE_STOLESHNIKOV.name()),
                true);

        assertProductLocationDataCorrect(
                productItem3.getId(),
                ImmutableMap.of(LOCATION_CODE_BOUTIQUE_STOLESHNIKOV, 1),
                ImmutableList.of(ProductTagType.BOUTIQUE_STOLESHNIKOV.name()),
                true);

        // убираем второй заказ из бутика

        orderService.setOrderSourceInfo(new OrderSourceInfoParams(order2.getId(), soldOrderSourceInfo.getId(), true, true, true));

        commitAndStartNewTransaction();

        assertProductLocationDataCorrect(
                productItem1.getId(),
                ImmutableMap.of(LOCATION_CODE_BOUTIQUE_STOLESHNIKOV, 1),
                ImmutableList.of(ProductTagType.BOUTIQUE_STOLESHNIKOV.name()),
                true);

        assertProductLocationDataCorrect(
                productItem2.getId(),
                ImmutableMap.of(LOCATION_CODE_BOUTIQUE_STOLESHNIKOV, 1),
                ImmutableList.of(ProductTagType.BOUTIQUE_STOLESHNIKOV.name()),
                true);

        assertProductLocationDataCorrect(
                productItem3.getId(),
                ImmutableMap.of(),
                ImmutableList.of(),
                false);

        // убираем первый заказ со склада

        orderService.setOrderSourceInfo(new OrderSourceInfoParams(order1.getId(), soldOrderSourceInfo.getId(), true, true, true));

        commitAndStartNewTransaction();

        assertProductLocationDataCorrect(
                productItem1.getId(),
                ImmutableMap.of(),
                ImmutableList.of(),
                false);

        assertProductLocationDataCorrect(
                productItem2.getId(),
                ImmutableMap.of(),
                ImmutableList.of(),
                false);

        assertProductLocationDataCorrect(
                productItem3.getId(),
                ImmutableMap.of(),
                ImmutableList.of(),
                false);
    }

    private Order createOrder(OrderState state, OrderSource orderSource, OrderSourceInfo orderSourceInfo) {
        Order order = new Order();
        order.setUuid(UUID.randomUUID());
        order.setState(CREATED);
        order.setState(state);
        order.setOrderSource(orderSource);
        order.setOrderSourceInfo(orderSourceInfo);
        order.setGuestToken(UUID.randomUUID().toString());
        return orderRepository.save(order);
    }

    private OrderPosition createOrderPosition(Order order, ProductItem productItem) {
        OrderPosition orderPosition = new OrderPosition();
        orderPosition.setOrder(order);
        orderPosition.setState(OrderPositionState.VERIFICATION_OK);
        orderPosition.setStateTime(LocalDateTime.now());
        orderPosition.setProductItem(productItem);
        return orderPositionRepository.save(orderPosition);
    }

    // TODO: дубль создания продуктов, айтемов, см ProductServiceTest

    private Product createProduct(ProductState state) {
        Product product = new Product();
        product.setProductState(state);
        product.setBrand(brand());
        product.setCategoryId(categoryId());
        product.setSeller(seller());
        product.setCurrentPrice(BigDecimal.TEN);
        return productRepository.save(product);
    }

    private Brand brand() {
        Brand brand = new Brand();
        brand.setId(1L);
        brand.setName("Brand");
        return brand;
    }

    private long categoryId() {
        return 4;
    }

    private User seller() {
        User seller = new User();
        seller.setId(SELLER_USER_ID);
        seller.setEmail("<EMAIL>");
        seller.setRegistrationTime(ZonedDateTime.now());
        seller.setSellerType(SellerType.BOUTIQUE);
        return seller;
    }

    private ProductItem createProductItem(Product product, boolean hidden) {
        ProductItem item = new ProductItem();
        item.setProduct(product);
        item.setDeleteTime(null);
        item.setHidden(hidden);
        item.setCount(1);
        item.setSize(sizeRepository.getOne(1L));
        return productItemRepository.save(item);
    }

    /**
     * Подстановка нужного пользователя для аутентификации
     *
     * @param userID идентификатор пользователя для подстановки
     */
    private void mockAuthenticationWithUserID(Long userID) {
        Authentication authentication = Mockito.mock(Authentication.class);
        SecurityContext securityContext = Mockito.mock(SecurityContext.class);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(securityContext);

        when(authentication.getPrincipal()).thenReturn(userID);
    }
}
