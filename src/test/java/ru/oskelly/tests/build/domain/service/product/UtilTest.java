package ru.oskelly.tests.build.domain.service.product;

import org.junit.jupiter.api.Test;
import su.reddot.presentation.Utils;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class UtilTest {
    @Test
    void testAllOrNobody() {
        assertTrue(Utils.allOrNobodyNotNull(null, null, null));
        assertFalse(Utils.allOrNobodyNotNull(new Object(), null, null));
        assertFalse(Utils.allOrNobodyNotNull(null, new Object(), null));
        assertFalse(Utils.allOrNobodyNotNull(null, null, new Object()));
        assertTrue(Utils.allOrNobodyNotNull(new Object(), new Object(), new Object()));
        assertFalse(Utils.allOrNobodyNotNull(null, new Object(), new Object()));
        assertFalse(Utils.allOrNobodyNotNull(new Object(), null, new Object()));
        assertFalse(Utils.allOrNobodyNotNull(new Object(), new Object(), null));
    }
}
