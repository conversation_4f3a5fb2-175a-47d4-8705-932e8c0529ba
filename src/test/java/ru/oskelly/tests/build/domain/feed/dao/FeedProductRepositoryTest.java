package ru.oskelly.tests.build.domain.feed.dao;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.feed.dao.FeedProductRepository;
import su.reddot.domain.feed.model.FeedProduct;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ConditionalOnProperty("app.feed-db.enabled")
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class FeedProductRepositoryTest extends AbstractSpringTest {

    @Autowired
    private FeedProductRepository feedProductRepository;

    /**
     * Удаляем все записи, создаем новую и читаем ее
     */
    @Test
    public void createRecord_OK(){
        feedProductRepository.deleteAll();
        assertTrue(feedProductRepository.findAll().isEmpty());
        FeedProduct fp = new FeedProduct(1L);
        fp.setFeedChangeTime(LocalDateTime.now());
        fp.setFeedCreateTime(LocalDateTime.now());
        fp.setGmcXml("<xml>");
        feedProductRepository.save(fp);

        List<FeedProduct> fps = feedProductRepository.findAll();
        assertEquals(1, fps.size());

        FeedProduct savedFeedProduct = fps.get(0);
        assertNotNull(savedFeedProduct);
        assertEquals("<xml>", savedFeedProduct.getGmcXml());
    }



}
