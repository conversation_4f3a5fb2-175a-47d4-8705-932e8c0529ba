package ru.oskelly.tests.build.domain.service.product;

/*
 * Created by <PERSON>
 */

import lombok.Getter;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.FanoutExchange;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.service.product.ProductService;
import su.reddot.infrastructure.configuration.AMQPConfiguration;
import su.reddot.infrastructure.configuration.OskellyApplication;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {OskellyApplication.class, ChangeProductEventTest.ChangeProductEventTestConfiguration.class})
@ActiveProfiles(profiles = AbstractSpringTest.testProfiles)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class ChangeProductEventTest {

    private static final String queueName = "product.action.event.test-queue";

    //Последнее полученное событие для проверки в тесте
    private static ChangeProductEvent lastProductChangeEvent;

    @RabbitListener(queues = queueName)
    public void changeTagStatusListener(ChangeProductEvent event) {
        lastProductChangeEvent = event;
    }

    @Autowired
    private ProductService productService;

    @Test
    public void changeProductEventTest() {
        //Создаем событие, чтобы проверить, дойдет ли оно до нас
        Product product = new Product();
        product.setId(1L);
        product.setProductState(ProductState.SOLD);
        productService.onProductChanged(product);

        TestUtils.sleep(2);

        //События приходили
        assertNotNull(lastProductChangeEvent);

        //ID товара в событии совпадает
        assertEquals(product.getId(), lastProductChangeEvent.getProductId());

        //state товара в событии совпадает
        assertEquals(product.getProductState().getName(), lastProductChangeEvent.getProductState());
    }

    @Getter
    static class ChangeProductEvent{
        private Long productId;
        private String productState;
    }

    @Configuration
    static class ChangeProductEventTestConfiguration{
        //Создаем новую тестовую очередь
        @Bean(name = queueName)
        public org.springframework.amqp.core.Queue productEventTestQueue() {
            return new org.springframework.amqp.core.Queue(queueName, false, true, true);
        }
        //Банндим очередь к эксченджу
        @Bean
        Binding productEventTestBinding(@Qualifier(AMQPConfiguration.CHANGE_PRODUCT_EVENT_FANOUT_EXCHANGE_NAME) FanoutExchange exchange,
                                        @Qualifier(queueName) org.springframework.amqp.core.Queue queue) {
            return BindingBuilder.bind(queue).to(exchange);
        }
    }
}
