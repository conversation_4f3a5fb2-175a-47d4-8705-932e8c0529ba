package ru.oskelly.tests.build.domain.service.concierge;

import org.apache.commons.lang3.RandomStringUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.TestApiConfiguration;
import su.reddot.domain.service.dto.concierge.ConciergeApplicationFormDto;
import su.reddot.presentation.api.v2.Api2Response;

import java.util.Arrays;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class DefaultConciergeServiceTest extends AbstractSpringTest {
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private TestApiConfiguration testApiConfiguration;

//    Check that clients can send requests and we immediately get applications via email
    @Test
    public void submitApplication0() throws RestClientException {
        ConciergeApplicationFormDto requestBody = new ConciergeApplicationFormDto();
        requestBody.setClientName("Tony Stark");
        requestBody.setPhoneNumber("+7965" + RandomStringUtils.randomNumeric(7));
        requestBody.setComment(RandomStringUtils.randomAlphabetic(6));
        requestBody.setReference("https://" + RandomStringUtils.randomAlphabetic(5) + ".ru");
        requestBody.setImagesReferences(Arrays.asList("link" + RandomStringUtils.randomNumeric(2), "link"+ RandomStringUtils.randomNumeric(3)));
        Api2Response<String> response = restTemplate.postForObject(testApiConfiguration.getServerUrl() + "/api/v2/concierge/submit-application", requestBody, Api2Response.class);
        Assertions.assertThat(response.getMessage()).isEqualTo("Application was submitted"); //when we add everything we need
    }

    @Test
    public void submitApplication1() throws RestClientException {
        ConciergeApplicationFormDto requestBody = new ConciergeApplicationFormDto();
        requestBody.setClientName("Tony Stark");
        requestBody.setPhoneNumber("8965" + RandomStringUtils.randomNumeric(7));
        requestBody.setComment(RandomStringUtils.randomAlphabetic(6));
        requestBody.setReference("https://" + RandomStringUtils.randomAlphabetic(5) + ".ru");
        requestBody.setImagesReferences(Arrays.asList("link" + RandomStringUtils.randomNumeric(2), "link"+ RandomStringUtils.randomNumeric(3)));
        Api2Response<String> response = restTemplate.postForObject(testApiConfiguration.getServerUrl() + "/api/v2/concierge/submit-application", requestBody, Api2Response.class);
        Assertions.assertThat(response.getMessage()).isEqualTo("Application was submitted"); //when we add everything we need
    }

    @Test
    public void submitApplication2() throws RestClientException {
        ConciergeApplicationFormDto requestBody = new ConciergeApplicationFormDto();
        requestBody.setClientName("Tony Stark");
        requestBody.setPhoneNumber("+7(965)4567890");
        requestBody.setComment(RandomStringUtils.randomAlphabetic(6));
        requestBody.setReference("https://" + RandomStringUtils.randomAlphabetic(5) + ".ru");
        requestBody.setImagesReferences(Arrays.asList("link" + RandomStringUtils.randomNumeric(2), "link"+ RandomStringUtils.randomNumeric(3)));
        Api2Response<String> response = restTemplate.postForObject(testApiConfiguration.getServerUrl() + "/api/v2/concierge/submit-application", requestBody, Api2Response.class);
        Assertions.assertThat(response.getMessage()).isEqualTo("Application was submitted"); //when we add everything we need
    }

    @Test
    public void submitApplication3() throws RestClientException {
        ConciergeApplicationFormDto requestBody = new ConciergeApplicationFormDto();
        requestBody.setClientName("Tony Stark");
        requestBody.setPhoneNumber("+7(965)456-7890");
        requestBody.setComment(RandomStringUtils.randomAlphabetic(6));
        requestBody.setReference("https://" + RandomStringUtils.randomAlphabetic(5) + ".ru");
        requestBody.setImagesReferences(Arrays.asList("link" + RandomStringUtils.randomNumeric(2), "link"+ RandomStringUtils.randomNumeric(3)));
        Api2Response<String> response = restTemplate.postForObject(testApiConfiguration.getServerUrl() + "/api/v2/concierge/submit-application", requestBody, Api2Response.class);
        Assertions.assertThat(response.getMessage()).isEqualTo("Application was submitted"); //when we add everything we need
    }

    @Test
    public void submitApplication4() throws RestClientException {
        ConciergeApplicationFormDto requestBody = new ConciergeApplicationFormDto();
        requestBody.setClientName("Tony Stark");
        requestBody.setPhoneNumber("+7(965)456-78-90");
        requestBody.setComment(RandomStringUtils.randomAlphabetic(6));
        requestBody.setReference("https://" + RandomStringUtils.randomAlphabetic(5) + ".ru");
        requestBody.setImagesReferences(Arrays.asList("link" + RandomStringUtils.randomNumeric(2), "link"+ RandomStringUtils.randomNumeric(3)));
        Api2Response<String> response = restTemplate.postForObject(testApiConfiguration.getServerUrl() + "/api/v2/concierge/submit-application", requestBody, Api2Response.class);
        Assertions.assertThat(response.getMessage()).isEqualTo("Application was submitted"); //when we add everything we need
    }

    @Test
    public void submitApplication5() throws RestClientException {
        ConciergeApplicationFormDto requestBody = new ConciergeApplicationFormDto();
        requestBody.setClientName("Tony Stark");
        requestBody.setPhoneNumber("+7965456-78-90");
        requestBody.setComment(RandomStringUtils.randomAlphabetic(6));
        requestBody.setReference("https://" + RandomStringUtils.randomAlphabetic(5) + ".ru");
        requestBody.setImagesReferences(Arrays.asList("link" + RandomStringUtils.randomNumeric(2), "link"+ RandomStringUtils.randomNumeric(3)));
        Api2Response<String> response = restTemplate.postForObject(testApiConfiguration.getServerUrl() + "/api/v2/concierge/submit-application", requestBody, Api2Response.class);
        Assertions.assertThat(response.getMessage()).isEqualTo("Application was submitted"); //when we add everything we need
    }

    @Test
    public void submitApplication6() throws RestClientException {
        ConciergeApplicationFormDto requestBody = new ConciergeApplicationFormDto();
        requestBody.setClientName("Tony Stark");
        requestBody.setPhoneNumber("+796545678-90");
        requestBody.setComment(RandomStringUtils.randomAlphabetic(6));
        requestBody.setReference("https://" + RandomStringUtils.randomAlphabetic(5) + ".ru");
        requestBody.setImagesReferences(Arrays.asList("link" + RandomStringUtils.randomNumeric(2), "link"+ RandomStringUtils.randomNumeric(3)));
        Api2Response<String> response = restTemplate.postForObject(testApiConfiguration.getServerUrl() + "/api/v2/concierge/submit-application", requestBody, Api2Response.class);
        Assertions.assertThat(response.getMessage()).isEqualTo("Application was submitted"); //when we add everything we need
    }

    @Test
    public void submitApplication7() throws RestClientException {
        ConciergeApplicationFormDto requestBody = new ConciergeApplicationFormDto();
        requestBody.setClientName("Tony Stark");
        requestBody.setPhoneNumber("+7965456-7890");
        requestBody.setComment(RandomStringUtils.randomAlphabetic(6));
        requestBody.setReference("https://" + RandomStringUtils.randomAlphabetic(5) + ".ru");
        requestBody.setImagesReferences(Arrays.asList("link" + RandomStringUtils.randomNumeric(2), "link"+ RandomStringUtils.randomNumeric(3)));
        Api2Response<String> response = restTemplate.postForObject(testApiConfiguration.getServerUrl() + "/api/v2/concierge/submit-application", requestBody, Api2Response.class);
        Assertions.assertThat(response.getMessage()).isEqualTo("Application was submitted"); //when we add everything we need
    }
}