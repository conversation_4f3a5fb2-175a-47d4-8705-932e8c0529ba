package ru.oskelly.tests.build.domain.service.commission;

import com.google.common.collect.Lists;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.data.util.Pair;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.commission.CommissionGridRepository;
import su.reddot.domain.dao.commission.CommissionRepository;
import su.reddot.domain.model.bargain.Bargain;
import su.reddot.domain.model.commission.Commission;
import su.reddot.domain.model.commission.CommissionGrid;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.SalesChannel;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.domain.service.commission.CommissionService;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertSame;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class CommissionServiceTest extends AbstractSpringTest {

	@SpyBean
    private CommissionService commissionService;

	@Autowired
	private CommissionGridService commissionGridService;

	@Autowired
	private CommissionRepository commissionRepository;

	@Autowired
	private CommissionGridRepository commissionGridRepository;

	private static final CommissionGrid DEFAULT_COMMISSION_GRID = new CommissionGrid(
			1L,
			"Комиссионная сетка для частных продавцов",
			CommissionGrid.Type.DEFAULT
	);
	private static final CommissionGrid PRO_COMMISSION_GRID = new CommissionGrid(
			2L,
			"Комиссионная сетка для бутиков",
			CommissionGrid.Type.PRO
	);

	@BeforeEach
	public void init() {
		Set<Commission> commissions = new HashSet<>(3);

		commissionGridRepository.saveAll(Lists.newArrayList(DEFAULT_COMMISSION_GRID, PRO_COMMISSION_GRID));

		commissions.add(new Commission(1L, DEFAULT_COMMISSION_GRID, BigDecimal.valueOf(30000), BigDecimal.valueOf(0.25), BigDecimal.valueOf(0.1)));
		commissions.add(new Commission(2L, DEFAULT_COMMISSION_GRID, BigDecimal.valueOf(100000), BigDecimal.valueOf(0.2), BigDecimal.valueOf(0.15)));
		commissions.add(new Commission(3L, DEFAULT_COMMISSION_GRID, BigDecimal.valueOf(9999999999L), BigDecimal.valueOf(0.16), BigDecimal.valueOf(0.19)));
		commissions.add(new Commission(4L, PRO_COMMISSION_GRID, BigDecimal.valueOf(16000), BigDecimal.valueOf(0.25), BigDecimal.valueOf(0.1)));
		commissions.add(new Commission(5L, PRO_COMMISSION_GRID, BigDecimal.valueOf(60000), BigDecimal.valueOf(0.2), BigDecimal.valueOf(0.15)));
		commissions.add(new Commission(6L, PRO_COMMISSION_GRID, BigDecimal.valueOf(9999999999L), BigDecimal.valueOf(0.15), BigDecimal.valueOf(0.2)));

		commissionRepository.saveAll(commissions);
	}

	@Test
	@Transactional
	public void testGetCommission() {
		Product product = new Product();
		User seller = new User();

		seller.setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
		product.setSeller(seller);
		product.setCurrentPrice(BigDecimal.valueOf(15000.0));
		product.setCommission(commissionRepository.getOne(1L));

		assertSame(commissionRepository.getOne(1L), commissionService.getCommissionForNewProduct(product));
	}

	@Test
	public void testCalculatePriceWithCommissionWithWebsiteSalesChannel() {
		BigDecimal currentPrice = BigDecimal.valueOf(10000);
		BigDecimal price = commissionService.calculatePriceWithCommission(
				currentPrice,
				currentPrice.subtract(BigDecimal.valueOf(2000)),
				SalesChannel.WEBSITE,
				false,
				commissionGridService.getDefaultCommissionGrid()
		).getPriceWithCommission();

		assertThat(currentPrice, Matchers.comparesEqualTo(price));
	}

	//@Test
	//метод getCommissionByPriceWithCommission поменял название и стал приватным
	public void testCalculatePriceWithoutCommissionWithWebsiteSalesChannel() {
		commissionService.calculatePriceWithoutCommission(
				BigDecimal.valueOf(10000),
				BigDecimal.valueOf(10000).subtract(BigDecimal.valueOf(2000)),
				SalesChannel.WEBSITE,
				false,
				commissionGridService.getDefaultCommissionGrid()
		);

		Mockito.verify(commissionService)
				.getCommissionByPriceWithCommission(Mockito.any(), Mockito.any(), Mockito.any());
	}

	@Test
	public void testCalculatePriceWithoutCommissionWithBoutiqueAndWebsiteSalesChannel() {
		BigDecimal currentPrice = BigDecimal.valueOf(10000);
		BigDecimal sellerPrice = currentPrice.subtract(BigDecimal.valueOf(2000));
		BigDecimal result = commissionService.calculatePriceWithoutCommission(
				currentPrice,
				sellerPrice,
				SalesChannel.BOUTIQUE_AND_WEBSITE,
				false,
				commissionGridService.getDefaultCommissionGrid()
		);

		Mockito.verify(commissionService, Mockito.never())
				.calculatePriceWithoutCommission(Mockito.any(), Mockito.any(), Mockito.any());
		assertThat(sellerPrice, Matchers.comparesEqualTo(result));
	}

	@Test
	public void testCalculateCommissionForBargainWithCustomCommission() {
		Product product = new Product();
		product.setCustomCommission(true);
		product.setCurrentPrice(BigDecimal.valueOf(10000));
		product.setCurrentPriceWithoutCommission(BigDecimal.valueOf(10000).subtract(BigDecimal.valueOf(2000)));

		Bargain bargain = new Bargain();
		bargain.setLastPrice(8500);
		bargain.setProduct(product);

		commissionService.calculateCommissionForBargain(bargain);

		Mockito.verify(commissionService)
				.calculateCustomCommission(Mockito.any(), Mockito.any());
		Mockito.verify(commissionService, Mockito.never())
				.getCommissionByPriceWithCommission(Mockito.any(), Mockito.any(), Mockito.any());
	}

	@Test
	public void testCalculateCommissionForBargainWithBoutiqueAndWebsiteSalesChannel() {
		User seller = new User();
		seller.setCommissionGrid(commissionGridService.getDefaultCommissionGrid());

		Product product = new Product();
		product.setSeller(seller);
		product.setSalesChannel(SalesChannel.BOUTIQUE_AND_WEBSITE);
		product.setCurrentPrice(BigDecimal.valueOf(10000));
		product.setCurrentPriceWithoutCommission(BigDecimal.valueOf(10000).subtract(BigDecimal.valueOf(2000)));

		Bargain bargain = new Bargain();
		bargain.setLastPrice(8500);
		bargain.setProduct(product);

		commissionService.calculateCommissionForBargain(bargain);

		Mockito.verify(commissionService, Mockito.never())
				.calculateCustomCommission(Mockito.any(), Mockito.any());
	}

	@Test
	public void testCalculateCommissionForBargainWithWebsiteSalesChannel() {
		User seller = new User();
		seller.setCommissionGrid(commissionGridService.getDefaultCommissionGrid());

		Product product = new Product();
		product.setSeller(seller);
		product.setSalesChannel(SalesChannel.WEBSITE);
		product.setCurrentPrice(BigDecimal.valueOf(10000));
		product.setCurrentPriceWithoutCommission(BigDecimal.valueOf(10000).subtract(BigDecimal.valueOf(2000)));

		Bargain bargain = new Bargain();
		bargain.setLastPrice(8500);
		bargain.setProduct(product);

		commissionService.calculateCommissionForBargain(bargain);

		Mockito.verify(commissionService)
				.getCommissionByPriceWithCommission(Mockito.any(), Mockito.any(), Mockito.any());
		Mockito.verify(commissionService, Mockito.never())
				.calculateCustomCommission(Mockito.any(), Mockito.any());
	}

	@Test
	@Transactional
	public void testSetCommission() {
		OrderPosition orderPosition = new OrderPosition();
		ProductItem productItem = new ProductItem();
		Product product = new Product();
		User seller = new User();
		CommissionGrid commissionGrid = commissionGridService.getDefaultCommissionGrid();

		orderPosition.setAmount(BigDecimal.valueOf(10000));
		orderPosition.setItemSaleAmount(orderPosition.getAmount());
		orderPosition.setProductItem(productItem);
		productItem.setProduct(product);
		product.setSeller(seller);
		seller.setCommissionGrid(commissionGrid);

		product.setCurrentPrice(BigDecimal.valueOf(10000));
		product.setCurrentPriceWithoutCommission(BigDecimal.valueOf(10000).subtract(BigDecimal.valueOf(2000)));
		product.setCustomCommission(true);

		commissionService.setCommission(orderPosition);

		Mockito.verify(commissionService).calculateCustomCommission(Mockito.any(), Mockito.any());
		Mockito.verify(commissionService, Mockito.never())
				.getCommissionByPriceWithCommission(Mockito.any(), Mockito.any(), Mockito.any());

		Mockito.reset(commissionService);

		product.setCustomCommission(false);
		product.setSalesChannel(SalesChannel.BOUTIQUE_AND_WEBSITE);

		commissionService.setCommission(orderPosition);

		Mockito.verify(commissionService, Mockito.never()).calculateCustomCommission(Mockito.any(), Mockito.any());

		Mockito.reset(commissionService);

		product.setSalesChannel(SalesChannel.WEBSITE);

		commissionService.setCommission(orderPosition);

		Mockito.verify(commissionService)
				.getCommissionByPriceWithCommission(Mockito.any(), Mockito.any(), Mockito.any());
		Mockito.verify(commissionService, Mockito.never()).calculateCustomCommission(Mockito.any(), Mockito.any());
	}

	/**
	 * Проверяет корректность вычисления размера комиссии в зависимости от цены товара на сайте
	 *
	 * Тест проверяет только простой (не бутиковый) кейс
	 */
	@Test
	public void testCalculateCommissionByPriceWithCommission() {
		Map<BigDecimal, BigDecimal> expectations = new HashMap<>();

		expectations.put(BigDecimal.valueOf(0.0), BigDecimal.valueOf(0.0));
		expectations.put(BigDecimal.valueOf(1.0), BigDecimal.valueOf(0.25));
		expectations.put(BigDecimal.valueOf(1000.0), BigDecimal.valueOf(0.25));
		expectations.put(BigDecimal.valueOf(10000.0), BigDecimal.valueOf(0.25));
		expectations.put(BigDecimal.valueOf(15000.0), BigDecimal.valueOf(0.25));
		expectations.put(BigDecimal.valueOf(15999.0), BigDecimal.valueOf(0.25));
		expectations.put(BigDecimal.valueOf(15999.99), BigDecimal.valueOf(0.25));
		expectations.put(BigDecimal.valueOf(16000.0), BigDecimal.valueOf(0.25));
		expectations.put(BigDecimal.valueOf(16001.0), BigDecimal.valueOf(0.25));
		expectations.put(BigDecimal.valueOf(29999.99), BigDecimal.valueOf(0.25));
		expectations.put(BigDecimal.valueOf(30000.0), BigDecimal.valueOf(0.2));
		expectations.put(BigDecimal.valueOf(40000.0), BigDecimal.valueOf(0.2));
		expectations.put(BigDecimal.valueOf(60000.0), BigDecimal.valueOf(0.2));
		expectations.put(BigDecimal.valueOf(80000.0), BigDecimal.valueOf(0.2));
		expectations.put(BigDecimal.valueOf(95000.0), BigDecimal.valueOf(0.2));
		expectations.put(BigDecimal.valueOf(99000.0), BigDecimal.valueOf(0.2));
		expectations.put(BigDecimal.valueOf(99999.0), BigDecimal.valueOf(0.2));
		expectations.put(BigDecimal.valueOf(99999.99), BigDecimal.valueOf(0.2));
		expectations.put(BigDecimal.valueOf(100000.0), BigDecimal.valueOf(0.16));
		expectations.put(BigDecimal.valueOf(100001.0), BigDecimal.valueOf(0.16));
		expectations.put(BigDecimal.valueOf(120000.0), BigDecimal.valueOf(0.16));
		expectations.put(BigDecimal.valueOf(500000.0), BigDecimal.valueOf(0.16));
		expectations.put(BigDecimal.valueOf(1000000.0), BigDecimal.valueOf(0.16));

		expectations.forEach((priceWithCommission, commission) -> {
			assertThat(
					commission,
					Matchers.comparesEqualTo(
							commissionService.getCommissionByPriceWithCommission(
									priceWithCommission,
									commissionGridService.getDefaultCommissionGrid(),
									//тест рассчитан на небутиковый кей
									SalesChannel.WEBSITE
							).getValueNullSafe(SalesChannel.WEBSITE)
					)
			);
		});
	}

	@Test
	public void testCalculateCommissionByPriceWithCommissionWithFixedAmount() {
		Map<BigDecimal, BigDecimal> expectations = new HashMap<>();

		BigDecimal fixedAmount = new BigDecimal("1500.0");

		expectations.put(BigDecimal.valueOf(0.0), BigDecimal.valueOf(0.0));
		expectations.put(BigDecimal.valueOf(1.0).add(fixedAmount), BigDecimal.valueOf(0.25));
		expectations.put(BigDecimal.valueOf(1000.0).add(fixedAmount), BigDecimal.valueOf(0.25));
		expectations.put(BigDecimal.valueOf(10000.0).add(fixedAmount), BigDecimal.valueOf(0.25));
		expectations.put(BigDecimal.valueOf(15000.0).add(fixedAmount), BigDecimal.valueOf(0.25));
		expectations.put(BigDecimal.valueOf(15999.0).add(fixedAmount), BigDecimal.valueOf(0.25));
		expectations.put(BigDecimal.valueOf(15999.99).add(fixedAmount), BigDecimal.valueOf(0.25));
		expectations.put(BigDecimal.valueOf(16000.0).add(fixedAmount), BigDecimal.valueOf(0.25));
		expectations.put(BigDecimal.valueOf(16001.0).add(fixedAmount), BigDecimal.valueOf(0.25));
		expectations.put(BigDecimal.valueOf(29999.99).add(fixedAmount), BigDecimal.valueOf(0.25));
		expectations.put(BigDecimal.valueOf(30000.0).add(fixedAmount), BigDecimal.valueOf(0.2));
		expectations.put(BigDecimal.valueOf(40000.0).add(fixedAmount), BigDecimal.valueOf(0.2));
		expectations.put(BigDecimal.valueOf(60000.0).add(fixedAmount), BigDecimal.valueOf(0.2));
		expectations.put(BigDecimal.valueOf(80000.0).add(fixedAmount), BigDecimal.valueOf(0.2));
		expectations.put(BigDecimal.valueOf(95000.0).add(fixedAmount), BigDecimal.valueOf(0.2));
		expectations.put(BigDecimal.valueOf(99000.0).add(fixedAmount), BigDecimal.valueOf(0.2));
		expectations.put(BigDecimal.valueOf(99999.0).add(fixedAmount), BigDecimal.valueOf(0.2));
		expectations.put(BigDecimal.valueOf(99999.99).add(fixedAmount), BigDecimal.valueOf(0.2));
		expectations.put(BigDecimal.valueOf(100000.0).add(fixedAmount), BigDecimal.valueOf(0.16));
		expectations.put(BigDecimal.valueOf(100001.0).add(fixedAmount), BigDecimal.valueOf(0.16));
		expectations.put(BigDecimal.valueOf(120000.0).add(fixedAmount), BigDecimal.valueOf(0.16));
		expectations.put(BigDecimal.valueOf(500000.0).add(fixedAmount), BigDecimal.valueOf(0.16));
		expectations.put(BigDecimal.valueOf(1000000.0).add(fixedAmount), BigDecimal.valueOf(0.16));

		CommissionGrid commissionGrid = commissionGridService.getDefaultCommissionGrid();
		commissionGrid.setFixedAmount(fixedAmount);

		expectations.forEach((priceWithCommission, commission) -> {
			assertThat(
					commission,
					Matchers.comparesEqualTo(
							commissionService.getCommissionByPriceWithCommission(
									priceWithCommission,
									commissionGridService.getDefaultCommissionGrid(),
									//тест рассчитан на небутиковый кей
									SalesChannel.WEBSITE
							).getValueNullSafe(SalesChannel.WEBSITE)
					)
			);
		});

		commissionGrid.setFixedAmount(BigDecimal.ZERO);
	}

	/**
	 * Проверяет корректность вычисления суммы, которую получит продавец исходя из суммы на сайте
	 */
	@Test
	public void testCalculatePriceWithoutCommission(){
		Map<BigDecimal, BigDecimal> expectations = new HashMap<>();

		expectations.put(BigDecimal.valueOf(0.0), BigDecimal.valueOf(0.0));
		expectations.put(BigDecimal.valueOf(1000.0), BigDecimal.valueOf(750.0));
		expectations.put(BigDecimal.valueOf(10000.0), BigDecimal.valueOf(7500.0));
		expectations.put(BigDecimal.valueOf(15500.0), BigDecimal.valueOf(11625.0));
		expectations.put(BigDecimal.valueOf(16000.0), BigDecimal.valueOf(12000.0));
		expectations.put(BigDecimal.valueOf(20000.0), BigDecimal.valueOf(15000.0));
		expectations.put(BigDecimal.valueOf(29999.0), BigDecimal.valueOf(22499.0));
		expectations.put(BigDecimal.valueOf(30000.0), BigDecimal.valueOf(24000));
		expectations.put(BigDecimal.valueOf(50000.0), BigDecimal.valueOf(40000.0));
		expectations.put(BigDecimal.valueOf(55000.0), BigDecimal.valueOf(44000.0));
		expectations.put(BigDecimal.valueOf(59500.0), BigDecimal.valueOf(47600.0));
		expectations.put(BigDecimal.valueOf(60000.0), BigDecimal.valueOf(48000.0));
		expectations.put(BigDecimal.valueOf(80000.0), BigDecimal.valueOf(64000.0));
		expectations.put(BigDecimal.valueOf(99500.0), BigDecimal.valueOf(79600.0));
		expectations.put(BigDecimal.valueOf(99999.0), BigDecimal.valueOf(79999.0));
		expectations.put(BigDecimal.valueOf(100000.0), BigDecimal.valueOf(84000.0));
		expectations.put(BigDecimal.valueOf(110000.0), BigDecimal.valueOf(92400.0));
		expectations.put(BigDecimal.valueOf(500000.0), BigDecimal.valueOf(420000.0));
		expectations.put(BigDecimal.valueOf(1000000.0), BigDecimal.valueOf(840000.0));

		expectations.forEach((priceWithCommission, priceWithoutCommission) -> {
			assertThat(
					priceWithoutCommission,
					Matchers.comparesEqualTo(
							commissionService.calculatePriceWithoutCommission(
									priceWithCommission,
									commissionGridService.getDefaultCommissionGrid(),
									//тест рассчитан на небутиковый кей
									SalesChannel.WEBSITE
							)
					)
			);
		});
	}

	/*
	Копия теста testCalculatePriceWithoutCommission с добавлением фиксированной суммы
	 */
	@Test
	public void testCalculatePriceWithoutCommissionWithFixedAmount(){
		Map<BigDecimal, BigDecimal> expectations = new HashMap<>();

		BigDecimal fixedAmount = new BigDecimal("1500.0");

		expectations.put(BigDecimal.valueOf(0.0), BigDecimal.valueOf(0.0));
		expectations.put(BigDecimal.valueOf(1000.0).add(fixedAmount), BigDecimal.valueOf(750.0));
		expectations.put(BigDecimal.valueOf(10000.0).add(fixedAmount), BigDecimal.valueOf(7500.0));
		expectations.put(BigDecimal.valueOf(15500.0).add(fixedAmount), BigDecimal.valueOf(11625.0));
		expectations.put(BigDecimal.valueOf(16000.0).add(fixedAmount), BigDecimal.valueOf(12000.0));
		expectations.put(BigDecimal.valueOf(20000.0).add(fixedAmount), BigDecimal.valueOf(15000.0));
		expectations.put(BigDecimal.valueOf(29999.0).add(fixedAmount), BigDecimal.valueOf(22499.0));
		expectations.put(BigDecimal.valueOf(30000.0).add(fixedAmount), BigDecimal.valueOf(24000));
		expectations.put(BigDecimal.valueOf(50000.0).add(fixedAmount), BigDecimal.valueOf(40000.0));
		expectations.put(BigDecimal.valueOf(55000.0).add(fixedAmount), BigDecimal.valueOf(44000.0));
		expectations.put(BigDecimal.valueOf(59500.0).add(fixedAmount), BigDecimal.valueOf(47600.0));
		expectations.put(BigDecimal.valueOf(60000.0).add(fixedAmount), BigDecimal.valueOf(48000.0));
		expectations.put(BigDecimal.valueOf(80000.0).add(fixedAmount), BigDecimal.valueOf(64000.0));
		expectations.put(BigDecimal.valueOf(99500.0).add(fixedAmount), BigDecimal.valueOf(79600.0));
		expectations.put(BigDecimal.valueOf(99999.0).add(fixedAmount), BigDecimal.valueOf(79999.0));
		expectations.put(BigDecimal.valueOf(100000.0).add(fixedAmount), BigDecimal.valueOf(84000.0));
		expectations.put(BigDecimal.valueOf(110000.0).add(fixedAmount), BigDecimal.valueOf(92400.0));
		expectations.put(BigDecimal.valueOf(500000.0).add(fixedAmount), BigDecimal.valueOf(420000.0));
		expectations.put(BigDecimal.valueOf(1000000.0).add(fixedAmount), BigDecimal.valueOf(840000.0));

		CommissionGrid commissionGrid = commissionGridService.getDefaultCommissionGrid();
		commissionGrid.setFixedAmount(fixedAmount);

		expectations.forEach((priceWithCommission, priceWithoutCommission) -> {
			assertThat(
					priceWithoutCommission,
					Matchers.comparesEqualTo(
							commissionService.calculatePriceWithoutCommission(
									priceWithCommission,
									commissionGrid,
									//тест рассчитан на небутиковый кей
									SalesChannel.WEBSITE
							)
					)
			);
		});

		commissionGrid.setFixedAmount(BigDecimal.ZERO);
	}

	/**
	 * Проверяет корректность вычисления суммы публикации на сайте исходя из суммы, которую ожидает получить продавец
	 */
	@Test
	public void testCalculatePriceWithCommission(){
		Map<BigDecimal, BigDecimal> expectations = new HashMap<>();

		expectations.put(BigDecimal.valueOf(0.0), BigDecimal.valueOf(0.0));
		expectations.put(BigDecimal.valueOf(750.0), BigDecimal.valueOf(1000.0));
		expectations.put(BigDecimal.valueOf(7500.0), BigDecimal.valueOf(10000.0));
		expectations.put(BigDecimal.valueOf(11625.0), BigDecimal.valueOf(15500.0));
		expectations.put(BigDecimal.valueOf(12000.0), BigDecimal.valueOf(16000.0));
		expectations.put(BigDecimal.valueOf(15000.0), BigDecimal.valueOf(20000.0));
		expectations.put(BigDecimal.valueOf(22499.0), BigDecimal.valueOf(29999.0));
		expectations.put(BigDecimal.valueOf(24000.0), BigDecimal.valueOf(30000.0));
		expectations.put(BigDecimal.valueOf(40000.0), BigDecimal.valueOf(50000.0));
		expectations.put(BigDecimal.valueOf(44000.0), BigDecimal.valueOf(55000.0));
		expectations.put(BigDecimal.valueOf(47600.0), BigDecimal.valueOf(59500.0));
		expectations.put(BigDecimal.valueOf(79200.0), BigDecimal.valueOf(99000.0));
		expectations.put(BigDecimal.valueOf(79999.0), BigDecimal.valueOf(99999.0));
		expectations.put(BigDecimal.valueOf(84000.0), BigDecimal.valueOf(100000.0));
		expectations.put(BigDecimal.valueOf(100800.0), BigDecimal.valueOf(120000.0));
		expectations.put(BigDecimal.valueOf(420000.0), BigDecimal.valueOf(500000.0));
		expectations.put(BigDecimal.valueOf(840000.0), BigDecimal.valueOf(1000000.0));

		expectations.forEach((priceWithoutCommission, priceWithCommission) -> {
			assertThat(
					priceWithCommission,
					Matchers.comparesEqualTo(
							commissionService.calculatePriceWithCommission(
									priceWithoutCommission,
									commissionGridService.getDefaultCommissionGrid(),
									//тест рассчитан на небутиковый кей
									SalesChannel.WEBSITE
							).getPriceWithCommission()
					)
			);
		});
	}

	/*
	Копия теста testCalculatePriceWithCommission с добавлением фиксированной суммы
	 */
	@Test
	public void testCalculatePriceWithCommissionWithFixedAmount(){
		Map<BigDecimal, BigDecimal> expectations = new HashMap<>();

		BigDecimal fixedAmount = new BigDecimal("1500.0");

		expectations.put(BigDecimal.valueOf(0.0), BigDecimal.valueOf(0.0));
		expectations.put(BigDecimal.valueOf(750.0), BigDecimal.valueOf(1000.0).add(fixedAmount));
		expectations.put(BigDecimal.valueOf(7500.0), BigDecimal.valueOf(10000.0).add(fixedAmount));
		expectations.put(BigDecimal.valueOf(11625.0), BigDecimal.valueOf(15500.0).add(fixedAmount));
		expectations.put(BigDecimal.valueOf(12000.0), BigDecimal.valueOf(16000.0).add(fixedAmount));
		expectations.put(BigDecimal.valueOf(15000.0), BigDecimal.valueOf(20000.0).add(fixedAmount));
		expectations.put(BigDecimal.valueOf(22499.0), BigDecimal.valueOf(29999.0).add(fixedAmount));
		expectations.put(BigDecimal.valueOf(24000.0), BigDecimal.valueOf(30000.0).add(fixedAmount));
		expectations.put(BigDecimal.valueOf(40000.0), BigDecimal.valueOf(50000.0).add(fixedAmount));
		expectations.put(BigDecimal.valueOf(44000.0), BigDecimal.valueOf(55000.0).add(fixedAmount));
		expectations.put(BigDecimal.valueOf(47600.0), BigDecimal.valueOf(59500.0).add(fixedAmount));
		expectations.put(BigDecimal.valueOf(79200.0), BigDecimal.valueOf(99000.0).add(fixedAmount));
		expectations.put(BigDecimal.valueOf(79999.0), BigDecimal.valueOf(99999.0).add(fixedAmount));
		expectations.put(BigDecimal.valueOf(84000.0), BigDecimal.valueOf(100000.0).add(fixedAmount));
		expectations.put(BigDecimal.valueOf(100800.0), BigDecimal.valueOf(120000.0).add(fixedAmount));
		expectations.put(BigDecimal.valueOf(420000.0), BigDecimal.valueOf(500000.0).add(fixedAmount));
		expectations.put(BigDecimal.valueOf(840000.0), BigDecimal.valueOf(1000000.0).add(fixedAmount));

		CommissionGrid commissionGrid = commissionGridService.getDefaultCommissionGrid();
		commissionGrid.setFixedAmount(fixedAmount);

		expectations.forEach((priceWithoutCommission, priceWithCommission) -> {
			assertThat(
					priceWithCommission,
					Matchers.comparesEqualTo(
							commissionService.calculatePriceWithCommission(
									priceWithoutCommission,
									commissionGrid,
									//тест рассчитан на небутиковый кей
									SalesChannel.WEBSITE
							).getPriceWithCommission()
					)
			);
		});

		commissionGrid.setFixedAmount(BigDecimal.ZERO);
	}

	@Test
	public void testCalculateCustomCommission() {
		Map<Pair<BigDecimal, BigDecimal>, BigDecimal> expectations = new HashMap<>();

		expectations.put(Pair.of(BigDecimal.valueOf(0.0), BigDecimal.valueOf(0.0)), BigDecimal.valueOf(0.0));
		expectations.put(Pair.of(BigDecimal.valueOf(16000.0), BigDecimal.valueOf(12000.0)), BigDecimal.valueOf(0.25));
		expectations.put(Pair.of(BigDecimal.valueOf(16000.0), BigDecimal.valueOf(12500.0)), BigDecimal.valueOf(0.21875));
		expectations.put(Pair.of(BigDecimal.valueOf(16000.0), BigDecimal.valueOf(12800.0)), BigDecimal.valueOf(0.2));
		expectations.put(Pair.of(BigDecimal.valueOf(60000.0), BigDecimal.valueOf(49000.0)), BigDecimal.valueOf(0.1833333333));
		expectations.put(Pair.of(BigDecimal.valueOf(60000.0), BigDecimal.valueOf(50000.0)), BigDecimal.valueOf(0.1666666667));
		expectations.put(Pair.of(BigDecimal.valueOf(60000.0), BigDecimal.valueOf(51000.0)), BigDecimal.valueOf(0.15));
		expectations.put(Pair.of(BigDecimal.valueOf(80000.0), BigDecimal.valueOf(10000.0)), BigDecimal.valueOf(0.875));
		expectations.put(Pair.of(BigDecimal.valueOf(80000.0), BigDecimal.valueOf(20000.0)), BigDecimal.valueOf(0.75));
		expectations.put(Pair.of(BigDecimal.valueOf(80000.0), BigDecimal.valueOf(30000.0)), BigDecimal.valueOf(0.625));
		expectations.put(Pair.of(BigDecimal.valueOf(80000.0), BigDecimal.valueOf(40000.0)), BigDecimal.valueOf(0.5));
		expectations.put(Pair.of(BigDecimal.valueOf(80000.0), BigDecimal.valueOf(50000.0)), BigDecimal.valueOf(0.375));
		expectations.put(Pair.of(BigDecimal.valueOf(80000.0), BigDecimal.valueOf(60000.0)), BigDecimal.valueOf(0.25));
		expectations.put(Pair.of(BigDecimal.valueOf(80000.0), BigDecimal.valueOf(70000.0)), BigDecimal.valueOf(0.125));

		expectations.forEach((key, value) -> {
			assertThat(
					value,
					Matchers.comparesEqualTo(
							commissionService.calculateCustomCommission(key.getFirst(), key.getSecond())
					)
			);
		});
	}

	@Test
	public void testCalculateCommissionForBoutiqueSalesChannelByPriceWithCommission() {
		Map<BigDecimal, BigDecimal> expectations = new HashMap<>();

		expectations.put(BigDecimal.valueOf(0.0), BigDecimal.valueOf(0.0));
		expectations.put(BigDecimal.valueOf(1000.0), BigDecimal.valueOf(0.35));
		expectations.put(BigDecimal.valueOf(5000.0), BigDecimal.valueOf(0.35));
		expectations.put(BigDecimal.valueOf(10000.0), BigDecimal.valueOf(0.35));
		expectations.put(BigDecimal.valueOf(15999.0), BigDecimal.valueOf(0.35));
		expectations.put(BigDecimal.valueOf(16000.0), BigDecimal.valueOf(0.35));
		expectations.put(BigDecimal.valueOf(20000.0), BigDecimal.valueOf(0.35));
		expectations.put(BigDecimal.valueOf(30000.0), BigDecimal.valueOf(0.35));
		expectations.put(BigDecimal.valueOf(40000.0), BigDecimal.valueOf(0.35));
		expectations.put(BigDecimal.valueOf(50000.0), BigDecimal.valueOf(0.35));
		expectations.put(BigDecimal.valueOf(59999.0), BigDecimal.valueOf(0.35));
		expectations.put(BigDecimal.valueOf(60000.0), BigDecimal.valueOf(0.35));
		expectations.put(BigDecimal.valueOf(70000.0), BigDecimal.valueOf(0.35));
		expectations.put(BigDecimal.valueOf(80000.0), BigDecimal.valueOf(0.35));
		expectations.put(BigDecimal.valueOf(90000.0), BigDecimal.valueOf(0.35));
		expectations.put(BigDecimal.valueOf(99999.0), BigDecimal.valueOf(0.35));
		expectations.put(BigDecimal.valueOf(100000.0), BigDecimal.valueOf(0.35));
		expectations.put(BigDecimal.valueOf(120000.0), BigDecimal.valueOf(0.35));
		expectations.put(BigDecimal.valueOf(500000.0), BigDecimal.valueOf(0.35));

		expectations.forEach((priceWithCommission, commission) -> {
			assertThat(
					commission,
					Matchers.comparesEqualTo(
							commissionService.getCommissionByPriceWithCommission(
									priceWithCommission,
									commissionGridService.getDefaultCommissionGrid(),
									SalesChannel.BOUTIQUE_AND_WEBSITE
							).getValueNullSafe(SalesChannel.BOUTIQUE_AND_WEBSITE)
					)
			);
		});
	}

	@Test
	public void testCalculateCommissionForStockSalesChannelByPriceWithCommission() {
		Map<BigDecimal, BigDecimal> expectations = new HashMap<>();

		//todo здесь "старый" метод возвращал ненулевую комиссию для нулевой сумму. Разве это правильно?
		//expectations.put(BigDecimal.valueOf(0.0), BigDecimal.valueOf(0.28));
		expectations.put(BigDecimal.valueOf(0.0), BigDecimal.valueOf(0.0));
		expectations.put(BigDecimal.valueOf(1000.0), BigDecimal.valueOf(0.28));
		expectations.put(BigDecimal.valueOf(5000.0), BigDecimal.valueOf(0.28));
		expectations.put(BigDecimal.valueOf(10000.0), BigDecimal.valueOf(0.28));
		expectations.put(BigDecimal.valueOf(15999.0), BigDecimal.valueOf(0.28));
		expectations.put(BigDecimal.valueOf(16000.0), BigDecimal.valueOf(0.28));
		expectations.put(BigDecimal.valueOf(20000.0), BigDecimal.valueOf(0.28));
		expectations.put(BigDecimal.valueOf(30000.0), BigDecimal.valueOf(0.28));
		expectations.put(BigDecimal.valueOf(40000.0), BigDecimal.valueOf(0.28));
		expectations.put(BigDecimal.valueOf(50000.0), BigDecimal.valueOf(0.28));
		expectations.put(BigDecimal.valueOf(59999.0), BigDecimal.valueOf(0.28));
		expectations.put(BigDecimal.valueOf(60000.0), BigDecimal.valueOf(0.23));
		expectations.put(BigDecimal.valueOf(70000.0), BigDecimal.valueOf(0.23));
		expectations.put(BigDecimal.valueOf(80000.0), BigDecimal.valueOf(0.23));
		expectations.put(BigDecimal.valueOf(90000.0), BigDecimal.valueOf(0.23));
		expectations.put(BigDecimal.valueOf(99999.0), BigDecimal.valueOf(0.23));
		expectations.put(BigDecimal.valueOf(100000.0), BigDecimal.valueOf(0.23));
		expectations.put(BigDecimal.valueOf(120000.0), BigDecimal.valueOf(0.23));
		expectations.put(BigDecimal.valueOf(149999.0), BigDecimal.valueOf(0.23));
		expectations.put(BigDecimal.valueOf(150000.0), BigDecimal.valueOf(0.19));
		expectations.put(BigDecimal.valueOf(500000.0), BigDecimal.valueOf(0.19));

		expectations.forEach((priceWithCommission, commission) -> {
			assertThat(
					commission,
					Matchers.comparesEqualTo(
							commissionService.getCommissionByPriceWithCommission(
									priceWithCommission,
									commissionGridService.getDefaultCommissionGrid(),
									SalesChannel.STOCK_AND_BOUTIQUE_AND_WEBSITE
							).getValueNullSafe(SalesChannel.STOCK_AND_BOUTIQUE_AND_WEBSITE)
					)
			);
		});
	}

	@Test
	public void testCalculatePriceWithCommissionForBoutiqueSalesChannel() {
		Map<BigDecimal, BigDecimal> expectations = new HashMap<>();

		expectations.put(BigDecimal.valueOf(0.0), BigDecimal.valueOf(0.0));
		expectations.put(BigDecimal.valueOf(600.0), BigDecimal.valueOf(923.0));
		expectations.put(BigDecimal.valueOf(3000.0), BigDecimal.valueOf(4615.0));
		expectations.put(BigDecimal.valueOf(6000.0), BigDecimal.valueOf(9231.0));
		expectations.put(BigDecimal.valueOf(9599.0), BigDecimal.valueOf(14768.0));
		expectations.put(BigDecimal.valueOf(10400.0), BigDecimal.valueOf(16000.0));
		expectations.put(BigDecimal.valueOf(13000.0), BigDecimal.valueOf(20000.0));
		expectations.put(BigDecimal.valueOf(19500.0), BigDecimal.valueOf(30000.0));
		expectations.put(BigDecimal.valueOf(26000.0), BigDecimal.valueOf(40000.0));
		expectations.put(BigDecimal.valueOf(32500.0), BigDecimal.valueOf(50000.0));
		expectations.put(BigDecimal.valueOf(38999.0), BigDecimal.valueOf(59998.0));
		expectations.put(BigDecimal.valueOf(39000.0), BigDecimal.valueOf(60000.0));
		expectations.put(BigDecimal.valueOf(45500.0), BigDecimal.valueOf(70000.0));
		expectations.put(BigDecimal.valueOf(52000.0), BigDecimal.valueOf(80000.0));
		expectations.put(BigDecimal.valueOf(58500.0), BigDecimal.valueOf(90000.0));
		expectations.put(BigDecimal.valueOf(64999.0), BigDecimal.valueOf(99998.0));
		expectations.put(BigDecimal.valueOf(69000.0), BigDecimal.valueOf(106154.0));
		expectations.put(BigDecimal.valueOf(82800.0), BigDecimal.valueOf(127385.0));
		expectations.put(BigDecimal.valueOf(345000.0), BigDecimal.valueOf(530769.0));

		expectations.forEach((priceWithoutCommission, priceWithCommission) -> {
			assertThat(
					priceWithCommission,
					Matchers.comparesEqualTo(
							commissionService.calculatePriceWithCommission(
									priceWithoutCommission,
									commissionGridService.getDefaultCommissionGrid(),
									SalesChannel.BOUTIQUE_AND_WEBSITE
							).getPriceWithCommission()
					)
			);
		});
	}

	@Test
	public void testCalculatePriceWithCommissionForStockSalesChannel() {
		Map<BigDecimal, BigDecimal> expectations = new HashMap<>();

		expectations.put(BigDecimal.valueOf(0.0), BigDecimal.valueOf(0.0));
		expectations.put(BigDecimal.valueOf(600.0), BigDecimal.valueOf(900.0));
		expectations.put(BigDecimal.valueOf(3000.0), BigDecimal.valueOf(4200.0));
		expectations.put(BigDecimal.valueOf(6000.0), BigDecimal.valueOf(8400.0));
		expectations.put(BigDecimal.valueOf(10400.0), BigDecimal.valueOf(14500.0));
		expectations.put(BigDecimal.valueOf(19500.0), BigDecimal.valueOf(27100.0));
		expectations.put(BigDecimal.valueOf(21500.0), BigDecimal.valueOf(29900.0));
		expectations.put(BigDecimal.valueOf(43100.0), BigDecimal.valueOf(59900.0));
		expectations.put(BigDecimal.valueOf(46200.0), BigDecimal.valueOf(60000.0));
		expectations.put(BigDecimal.valueOf(52000.0), BigDecimal.valueOf(67600.0));
		expectations.put(BigDecimal.valueOf(76000.0), BigDecimal.valueOf(98800.0));
		expectations.put(BigDecimal.valueOf(81000.0), BigDecimal.valueOf(105200.0));
		expectations.put(BigDecimal.valueOf(95000.0), BigDecimal.valueOf(123400.0));
		expectations.put(BigDecimal.valueOf(115400.0), BigDecimal.valueOf(149900.0));
		expectations.put(BigDecimal.valueOf(121500.0), BigDecimal.valueOf(150000.0));
		expectations.put(BigDecimal.valueOf(345000.0), BigDecimal.valueOf(426000.0));

		expectations.forEach((priceWithoutCommission, priceWithCommission) -> {
			assertThat(
					priceWithCommission,
					Matchers.comparesEqualTo(
							commissionService.calculatePriceWithCommission(
									priceWithoutCommission,
									commissionGridService.getDefaultCommissionGrid(),
									SalesChannel.STOCK_AND_BOUTIQUE_AND_WEBSITE
							).getPriceWithCommission()
					)
			);
		});
	}

	@Test
	public void testCalculatePriceWithoutCommissionForBoutiqueSalesChannel() {
		Map<BigDecimal, BigDecimal> expectations = new HashMap<>();

		expectations.put(BigDecimal.valueOf(0.0), BigDecimal.valueOf(0.0));
		expectations.put(BigDecimal.valueOf(1000.0), BigDecimal.valueOf(650.0));
		expectations.put(BigDecimal.valueOf(10000.0), BigDecimal.valueOf(6500.0));
		expectations.put(BigDecimal.valueOf(15500.0), BigDecimal.valueOf(10075.0));
		expectations.put(BigDecimal.valueOf(16000.0), BigDecimal.valueOf(10400.0));
		expectations.put(BigDecimal.valueOf(20000.0), BigDecimal.valueOf(13000.0));
		expectations.put(BigDecimal.valueOf(29999.0), BigDecimal.valueOf(19499.0));
		expectations.put(BigDecimal.valueOf(30000.0), BigDecimal.valueOf(19500.0));
		expectations.put(BigDecimal.valueOf(50000.0), BigDecimal.valueOf(32500.0));
		expectations.put(BigDecimal.valueOf(55000.0), BigDecimal.valueOf(35750.0));
		expectations.put(BigDecimal.valueOf(59500.0), BigDecimal.valueOf(38675.0));
		expectations.put(BigDecimal.valueOf(99000.0), BigDecimal.valueOf(64350.0));
		expectations.put(BigDecimal.valueOf(99999.0), BigDecimal.valueOf(64999.0));
		expectations.put(BigDecimal.valueOf(100000.0), BigDecimal.valueOf(65000.0));
		expectations.put(BigDecimal.valueOf(120000.0), BigDecimal.valueOf(78000.0));
		expectations.put(BigDecimal.valueOf(500000.0), BigDecimal.valueOf(325000.0));
		expectations.put(BigDecimal.valueOf(1000000.0), BigDecimal.valueOf(650000.0));

		expectations.forEach((priceWithCommission, priceWithoutCommission) -> {
			assertThat(
					priceWithoutCommission,
					Matchers.comparesEqualTo(
							commissionService.calculatePriceWithoutCommission(
									priceWithCommission,
									commissionGridService.getDefaultCommissionGrid(),
									SalesChannel.BOUTIQUE_AND_WEBSITE
							)
					)
			);
		});
	}

	@Test
	public void testRoundPriceWithCommissionForBoutiqueSalesChannel() {
		Map<BigDecimal, BigDecimal> expectations = new HashMap<>();

		expectations.put(BigDecimal.valueOf(0), BigDecimal.valueOf(0));
		expectations.put(BigDecimal.valueOf(99), BigDecimal.valueOf(100));
		expectations.put(BigDecimal.valueOf(999), BigDecimal.valueOf(1000));
		expectations.put(BigDecimal.valueOf(1111), BigDecimal.valueOf(1200));
		expectations.put(BigDecimal.valueOf(1234), BigDecimal.valueOf(1300));
		expectations.put(BigDecimal.valueOf(9990), BigDecimal.valueOf(10000));
		expectations.put(BigDecimal.valueOf(15313), BigDecimal.valueOf(15400));
		expectations.put(BigDecimal.valueOf(16001), BigDecimal.valueOf(16100));
		expectations.put(BigDecimal.valueOf(19998), BigDecimal.valueOf(20000));

		expectations.forEach((priceWithCommission, result) -> {
			assertThat(
					result,
					Matchers.comparesEqualTo(
							commissionService.roundPriceToHundred(priceWithCommission)
					)
			);
		});
	}

}
