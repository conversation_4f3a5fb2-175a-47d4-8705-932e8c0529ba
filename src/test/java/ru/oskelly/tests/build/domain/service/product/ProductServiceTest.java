package ru.oskelly.tests.build.domain.service.product;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.SizeRepository;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.order.OrderPositionRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.product.ProductItemLocationRepository;
import su.reddot.domain.dao.product.ProductItemRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.exception.ProductNotFoundException;
import su.reddot.domain.model.Brand;
import su.reddot.domain.model.attribute.Attribute;
import su.reddot.domain.model.attribute.AttributeValue;
import su.reddot.domain.model.category.Category;
import su.reddot.domain.model.currency.CurrencyRate;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.ProductItemLocation;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.product.ProductTag;
import su.reddot.domain.model.product.ProductTagCategory;
import su.reddot.domain.model.user.SellerType;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.domain.service.currency.CurrencyRateService;
import su.reddot.domain.service.currency.CurrencyService;
import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.dto.PageRequest;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.domain.service.dto.ProductItemDTO;
import su.reddot.domain.service.dto.ProductTagDTO;
import su.reddot.domain.service.dto.SplitInfo;
import su.reddot.domain.service.filter.ProductMapper;
import su.reddot.domain.service.filter.ProductMapper.MapProductsOptions;
import su.reddot.domain.service.like.LikeService;
import su.reddot.domain.service.loyalty.LoyaltyService;
import su.reddot.domain.service.loyalty.LoyaltyServiceProperties;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.ordersourceinfo.OrderSourceInfoService;
import su.reddot.domain.service.payment.impl.tabby.TabbySplitService;
import su.reddot.domain.service.payment.impl.yandexpay.YandexPayService;
import su.reddot.domain.service.payment.impl.yandexsplit.YandexSplitService;
import su.reddot.domain.service.product.ProductInfoRequest;
import su.reddot.domain.service.product.ProductPermissionsHandler;
import su.reddot.domain.service.product.ProductService;
import su.reddot.domain.service.product.ProductTagService;
import su.reddot.domain.service.product.item.ProductItemService;
import su.reddot.domain.service.setting.FeatureFlagsSettingService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.configparam.ConfigParamService;
import su.reddot.infrastructure.util.CallInTransaction;
import su.reddot.infrastructure.util.Utils;
import su.reddot.presentation.view.product.ProductView;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static su.reddot.domain.model.product.ProductItemLocation.LOCATION_CODE_BOUTIQUE_KUZNETSKY_BRIDGE;
import static su.reddot.domain.model.product.ProductItemLocation.LOCATION_CODE_BOUTIQUE_STOLESHNIKOV;
import static su.reddot.domain.model.product.ProductState.BANED;
import static su.reddot.domain.model.product.ProductState.DRAFT;
import static su.reddot.domain.model.product.ProductState.PUBLISHED;
import static su.reddot.domain.model.product.ProductState.SOLD;
import static su.reddot.domain.model.user.User.UserType.SIMPLE_USER;
import static su.reddot.domain.service.product.ProductService.UserType.HUMAN;
import static su.reddot.domain.service.product.ProductService.UserType.SYSTEM;
import static su.reddot.domain.service.setting.FeatureFlagsSettingService.ConfigFlag.HIDE_BRANDS_AND_SELLERS;
import static su.reddot.infrastructure.configparam.ConfigParamService.CONFIG_PARAM_BRANDS_HIDE_LIST;
import static su.reddot.infrastructure.configparam.ConfigParamService.CONFIG_PARAM_SELLERS_HIDE_LIST;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class ProductServiceTest extends AbstractSpringTest {

	/** Идентификатор продавца */
	@Value("${test.api.user-id}")
	private Long sellerId;

	@Value("${test.api.user2-id}")
	private Long user2Id;

    @Mock
    private ProductRepository productRepository;

	@Mock
	private User user;

	@Autowired
	private ProductService productService;

	@Autowired
	private ProductMapper productMapper;

	@Autowired
	private YandexSplitService yandexSplitService;

	@Autowired
	private TabbySplitService tabbySplitService;

	@Autowired
	private YandexPayService yandexPayService;

	@MockBean
	private FeatureFlagsSettingService featureFlagsSettingService;

	@MockBean
	private ConfigParamService configParamService;

	@Autowired
	private ProductItemService productItemService;

	@Autowired
	private ProductItemLocationRepository productItemLocationRepository;

	@Autowired
	private ProductTagService productTagService;

	@Autowired
	private OrderService orderService;

	@Autowired
	private OrderRepository orderRepository;

	@Autowired
	private OrderPositionRepository orderPositionRepository;

	@Autowired
	private ProductItemRepository productItemRepository;

	@Autowired
	private OrderSourceInfoService orderSourceInfoService;

	@Autowired
	private SizeRepository sizeRepository;

	@Autowired
	private ProductPermissionsHandler productPermissionsHandler;

	@Autowired
	private CurrencyService currencyService;

	@MockBean
	private CurrencyRateService currencyRateService;

	@Autowired
	LikeService likeService;

	@Autowired
	UserService userService;

	@Autowired
	private CommissionGridService commissionGridService;

	@Autowired
	private UserRepository userRepository;

	@Autowired
	private CallInTransaction callInTransaction;

	@MockBean
	private LoyaltyService loyaltyServiceMock;

	@MockBean
	private LoyaltyServiceProperties loyaltyServicePropertiesMock;

	private void canRetrieveProductsByFilterFalse(ProductService.FilterSpecification spec, User user) {
		assertFalse(productPermissionsHandler.canRetrieveProductsByFilter(HUMAN, spec, user));
	}

	private void canRetrieveProductsByStatusFalse(ProductState state, User user) {
		ProductService.FilterSpecification spec = new ProductService.FilterSpecification().state(state);
		canRetrieveProductsByFilterFalse(spec, user);
	}

	private void canRetrieveProductsByFilterTrue(ProductService.FilterSpecification spec, User user) {
		assertTrue(productPermissionsHandler.canRetrieveProductsByFilter(HUMAN, spec, user));
	}

	private void canRetrieveProductsByStatusTrue(ProductState state, User user) {
		ProductService.FilterSpecification spec = new ProductService.FilterSpecification().state(state);
		canRetrieveProductsByFilterTrue(spec, user);
	}

    /**
     * Товара с запрашиваемым идентификатором физически нет в системе
     */
    @Test
    public void getProductView_productNotFound_throwsProductNotFoundException() {
        Long nonExistingProductId = 42L;
        given(productRepository.findById(nonExistingProductId).orElse(null)).willReturn(null);
		Assertions.assertThrows(ProductNotFoundException.class, () -> {
			Optional<ProductView> productView = productService.getProductView(nonExistingProductId, null, HUMAN);
		});
    }

    /**
     * Товара есть, но недоступен для отображения
     */
    @Test
    public void getProductView_productUnavailableForViewing_throwsProductNotFoundException() {
        Long existingProductId = 42L;
        Product nonViewableProduct = mock(Product.class);
        given(nonViewableProduct.isAvailable()).willReturn(false);
        given(productRepository.getOne(existingProductId)).willReturn(nonViewableProduct);
		Assertions.assertThrows(ProductNotFoundException.class, () -> {
			Optional<ProductView> productView = productService.getProductView(existingProductId, null, HUMAN);
			assertTrue(!productView.isPresent());
		});

    }

	/**
	 * Гость может запрашивать товары без указания статуса
	 */
	@Test
	public void canRetrieveProductsByFilter_guest_noState_true() {
		canRetrieveProductsByStatusTrue(null, null);
	}

	/**
	 * Гость может запрашивать товары со статусом PUBLISHED
	 */
	@Test
	public void canRetrieveProductsByFilter_guest_PUBLISHED_true() {
		canRetrieveProductsByStatusTrue(PUBLISHED, null);
	}

	/**
	 * Гость не может запрашивать товары с остальными статусами
	 */
	@Test
	public void canRetrieveProductsByFilter_guest_false() {
		canRetrieveProductsByStatusFalse(ProductState.HIDDEN, null);
		canRetrieveProductsByStatusFalse(ProductState.SECOND_EDITION, null);
		canRetrieveProductsByStatusFalse(ProductState.REJECTED, null);
		canRetrieveProductsByStatusFalse(ProductState.NEED_MODERATION, null);
		canRetrieveProductsByStatusFalse(DRAFT, null);
		canRetrieveProductsByStatusFalse(ProductState.NEED_RETOUCH, null);
		canRetrieveProductsByStatusFalse(ProductState.RETOUCH_DONE, null);
		canRetrieveProductsByStatusFalse(ProductState.DELETED, null);
		canRetrieveProductsByStatusFalse(ProductState.SOLD, null);
	}

	/**
	 * Простой авторизованный пользователь может запрашивать товары без указания статуса
	 */
	@Test
	public void canRetrieveProductsByFilter_simpleAuthorizedUser_noState_true() {
		canRetrieveProductsByStatusTrue(null, simpleAuthorizedUser());
	}

	/**
	 * Простой авторизованный пользователь может запрашивать товары со статусом PUBLISHED
	 */
	@Test
	public void canRetrieveProductsByFilter_simpleAuthorizedUser_PUBLISHED_true() {
		canRetrieveProductsByStatusTrue(PUBLISHED, simpleAuthorizedUser());
	}

	/**
	 * Простой авторизованный пользователь может запрашивать товары со статусом SOLD
	 */
	@Test
	public void canRetrieveProductsByFilter_simpleAuthorizedUser_SOLD_true() {
		canRetrieveProductsByStatusTrue(ProductState.SOLD, simpleAuthorizedUser());
	}

	/**
	 * Простой авторизованный пользователь не может запрашивать товары с остальными статусами
	 */
	@Test
	public void canRetrieveProductsByFilter_simpleAuthorizedUser_false() {
		User user = simpleAuthorizedUser();
		canRetrieveProductsByStatusFalse(ProductState.HIDDEN, user);
		canRetrieveProductsByStatusFalse(ProductState.SECOND_EDITION, user);
		canRetrieveProductsByStatusFalse(ProductState.REJECTED, user);
		canRetrieveProductsByStatusFalse(ProductState.NEED_MODERATION, user);
		canRetrieveProductsByStatusFalse(DRAFT, user);
		canRetrieveProductsByStatusFalse(ProductState.NEED_RETOUCH, user);
		canRetrieveProductsByStatusFalse(ProductState.RETOUCH_DONE, user);
		canRetrieveProductsByStatusFalse(ProductState.DELETED, user);
	}

	/**
	 * Модератор товаров может запрашивать любые товары, кроме удаленных
	 */
	@Test
	public void canRetrieveProductsByFilter_productModerator() {
		given(user.isModerator()).willReturn(true);
		given(user.hasAuthority(AuthorityName.PRODUCT_MODERATION)).willReturn(true);

		canRetrieveProductsByStatusTrue(null, user);
		canRetrieveProductsByStatusTrue(ProductState.HIDDEN, user);
		canRetrieveProductsByStatusTrue(ProductState.SECOND_EDITION, user);
		canRetrieveProductsByStatusTrue(ProductState.REJECTED, user);
		canRetrieveProductsByStatusTrue(ProductState.NEED_MODERATION, user);
		canRetrieveProductsByStatusTrue(DRAFT, user);
		canRetrieveProductsByStatusTrue(ProductState.NEED_RETOUCH, user);
		canRetrieveProductsByStatusTrue(ProductState.RETOUCH_DONE, user);
		canRetrieveProductsByStatusTrue(ProductState.SOLD, user);

		canRetrieveProductsByStatusFalse(ProductState.DELETED, user);
	}

	/**
	 * Админ может запрашивать любые товары, кроме удаленных
	 */
	@Test
	public void canRetrieveProductsByFilter_Admin() {
		given(user.isAdmin()).willReturn(true);

		canRetrieveProductsByStatusTrue(null, user);
		canRetrieveProductsByStatusTrue(ProductState.HIDDEN, user);
		canRetrieveProductsByStatusTrue(ProductState.SECOND_EDITION, user);
		canRetrieveProductsByStatusTrue(ProductState.REJECTED, user);
		canRetrieveProductsByStatusTrue(ProductState.NEED_MODERATION, user);
		canRetrieveProductsByStatusTrue(DRAFT, user);
		canRetrieveProductsByStatusTrue(ProductState.NEED_RETOUCH, user);
		canRetrieveProductsByStatusTrue(ProductState.RETOUCH_DONE, user);
		canRetrieveProductsByStatusTrue(ProductState.SOLD, user);

		canRetrieveProductsByStatusFalse(ProductState.DELETED, user);
	}

	/**
	 * Модератор с авторити CAN_VIEW_ALL_PRODUCTS может запрашивать любые товары, кроме удаленных и черновиков
	 */
	@Test
	public void canRetrieveProductsByFilter_ProductViewer() {
		given(user.isModerator()).willReturn(true);
		given(user.hasAuthority(AuthorityName.CAN_VIEW_ALL_PRODUCTS)).willReturn(true);

		canRetrieveProductsByStatusTrue(null, user);
		canRetrieveProductsByStatusTrue(ProductState.HIDDEN, user);
		canRetrieveProductsByStatusTrue(ProductState.SECOND_EDITION, user);
		canRetrieveProductsByStatusTrue(ProductState.REJECTED, user);
		canRetrieveProductsByStatusTrue(ProductState.NEED_MODERATION, user);
		canRetrieveProductsByStatusTrue(ProductState.NEED_RETOUCH, user);
		canRetrieveProductsByStatusTrue(ProductState.RETOUCH_DONE, user);
		canRetrieveProductsByStatusTrue(ProductState.SOLD, user);

		canRetrieveProductsByStatusFalse(DRAFT, user);
		canRetrieveProductsByStatusFalse(ProductState.DELETED, user);
	}

	/**
	 * Продавец может запрашивать все свои товары, кроме удаленных
	 */
	@Test
	public void canRetrieveProductsByFilter_seller() {
		User user = simpleAuthorizedUser();
		user.setId(567L);
		ProductService.FilterSpecification spec = new ProductService.FilterSpecification().sellerId(user.getId());

		canRetrieveProductsByFilterTrue(null, user);
		canRetrieveProductsByFilterTrue(spec, user);
		spec.state(ProductState.HIDDEN);
		canRetrieveProductsByFilterTrue(spec, user);
		spec.state(ProductState.SECOND_EDITION);
		canRetrieveProductsByFilterTrue(spec, user);
		spec.state(ProductState.REJECTED);
		canRetrieveProductsByFilterTrue(spec, user);
		spec.state(ProductState.NEED_MODERATION);
		canRetrieveProductsByFilterTrue(spec, user);
		spec.state(DRAFT);
		canRetrieveProductsByFilterTrue(spec, user);
		spec.state(ProductState.NEED_RETOUCH);
		canRetrieveProductsByFilterTrue(spec, user);
		spec.state(ProductState.RETOUCH_DONE);
		canRetrieveProductsByFilterTrue(spec, user);
		spec.state(ProductState.SOLD);
		canRetrieveProductsByFilterTrue(spec, user);

		spec.state(ProductState.DELETED);
		canRetrieveProductsByFilterFalse(spec, user);
	}

	/**
	 * Проверка корректности создания/обновления и фильтрации продуктов
	 * в части полей артикулов производителя и бутика
	 */
	@Test
	@Transactional
	public void filterProductsOverVendorAndStoreCode() {

		mockAuthenticationWithUserID(sellerId);

		Product product = createProduct(
				new ProductCreationParams(DRAFT, sellerId)
						.setVendorCode("123456")
						.setStoreCode("777777"),
				null);

		long productId = product.getId();

		ProductService.FilterSpecification spec = new ProductService.FilterSpecification().state(DRAFT)
																						  .sellerId(sellerId)
																						  .vendorCodeContains("123");
		List<Product> products = productService.getRawProductsList(spec, null, null, null, null);
		assertTrue(products.stream()
				.anyMatch(p -> p.getId() == productId));

		spec = new ProductService.FilterSpecification().state(DRAFT)
													   .sellerId(sellerId)
													   .vendorCodeContains("0123");
		products = productService.getRawProductsList(spec, null, null, null, null);
		assertFalse(products.stream()
				.anyMatch(p -> p.getId() == productId));

		spec = new ProductService.FilterSpecification().state(DRAFT)
													   .sellerId(sellerId)
													   .storeCodeContains("777");
		products = productService.getRawProductsList(spec, null, null, null, null);
		assertTrue(products.stream()
				.anyMatch(p -> p.getId() == productId));

		spec = new ProductService.FilterSpecification().state(DRAFT)
													   .sellerId(sellerId)
													   .storeCodeContains("7778");
		products = productService.getRawProductsList(spec, null, null, null, null);
		assertFalse(products.stream()
				.anyMatch(p -> p.getId() == productId));

		product.setVendorCode("0123456");
		product.setStoreCode("7777778");

		productService.saveProduct(product, null);

		spec = new ProductService.FilterSpecification().state(DRAFT)
													   .sellerId(sellerId)
													   .storeCodeContains("7778");
		products = productService.getRawProductsList(spec, null, null, null, null);
		assertTrue(products.stream()
				.anyMatch(p -> p.getId() == productId));


		spec = new ProductService.FilterSpecification().state(DRAFT)
													   .sellerId(sellerId)
													   .vendorCodeContains("0123");
		products = productService.getRawProductsList(spec, null, null, null, null);
		assertTrue(products.stream()
				.anyMatch(p -> p.getId() == productId));
	}

	@Test
	@Transactional
	public void canRetrieveProductForBlog() {
		//GIVEN
		mockAuthenticationWithUserID(sellerId);

		Product soldProduct = createProduct(
				new ProductCreationParams(SOLD, sellerId),
				SYSTEM);
		Product publishedProduct = createProduct(
				new ProductCreationParams(PUBLISHED, sellerId),
				SYSTEM);
		Product banedProduct = createProduct(
				new ProductCreationParams(BANED, sellerId),
				SYSTEM);

		//WHEN
		Optional<Product> actualSoldProduct = productService.getPublishedOrSoldRowProduct(soldProduct.getId());
		Optional<Product> actualPublishedProduct = productService.getPublishedOrSoldRowProduct(publishedProduct.getId());
		Optional<Product> actualBanedProduct = productService.getPublishedOrSoldRowProduct(banedProduct.getId());

		//THEN
		assertThat(actualSoldProduct).isPresent();
		assertThat(actualPublishedProduct).isPresent();
		assertThat(actualBanedProduct).isEmpty();
	}

	@Test
	@Transactional
	public void productDTOForBlogShouldContainsSoldFlag() {
		//GIVEN
		mockAuthenticationWithUserID(sellerId);

		Product soldProduct = createProduct(
				new ProductCreationParams(SOLD, sellerId),
				SYSTEM);
		Product publishedProduct =  createProduct(
				new ProductCreationParams(PUBLISHED, sellerId),
				SYSTEM);

		//WHEN
		ProductDTO actualSoldProduct = productService.getPublishedOrSoldProductDTO(soldProduct.getId());
		ProductDTO actualPublishedProduct = productService.getPublishedOrSoldProductDTO(publishedProduct.getId());

		//THEN
		assertTrue(actualSoldProduct.getIsSold());
		assertFalse(actualPublishedProduct.getIsSold());
	}

	@Test
	@Transactional
	public void getRawProductWithHiddenBrands() {
		Long randomBrandId = randomBrandId();

		mockAuthenticationWithUserID(user2Id);
		when(featureFlagsSettingService.isEnableForUserWithPercent(HIDE_BRANDS_AND_SELLERS))
				.thenReturn(true);
		when(configParamService.getValueAsList(CONFIG_PARAM_BRANDS_HIDE_LIST))
				.thenReturn(Lists.newArrayList(randomBrandId.toString()));

		Page<ProductDTO> products = productService.getProductDTOPageForAuthorizedUserCached(
				new ProductInfoRequest()
						.setFilterSpecification(new ProductService.FilterSpecification().state(PUBLISHED))
						.setViewQualification(new ProductService.ViewQualification().pageLength(16)),
				null,
				null,
				null
		);
		List<Long> brandIds = products.getItems().stream()
				.map(product -> product.getBrand().getId())
				.collect(Collectors.toList());

		assertFalse(brandIds.contains(randomBrandId));
	}

	@Test
	@Transactional
	public void getRawProductWithHiddenSeller() {
		Long randomSellerId = randomSellerId();

		mockAuthenticationWithUserID(user2Id);
		when(featureFlagsSettingService.isEnableForUserWithPercent(HIDE_BRANDS_AND_SELLERS))
				.thenReturn(true);
		when(configParamService.getValueAsList(CONFIG_PARAM_SELLERS_HIDE_LIST))
				.thenReturn(Lists.newArrayList(randomSellerId.toString()));

		Page<ProductDTO> products = productService.getProductDTOPageForAuthorizedUserCached(
				new ProductInfoRequest()
						.setFilterSpecification(new ProductService.FilterSpecification().state(PUBLISHED))
						.setViewQualification(new ProductService.ViewQualification().pageLength(16)),
				null,
				null,
				null
		);
		List<Long> sellerIds = products.getItems().stream()
				.map(product -> product.getSeller().getId())
				.collect(Collectors.toList());

		assertFalse(sellerIds.contains(randomSellerId));
	}

	@Test
	@Transactional
	public void getRawProductWithHiddenSellerAsSeller() {
		Long randomSellerId = randomSellerId();

		when(featureFlagsSettingService.isEnableForUserWithPercent(any()))
				.thenReturn(true);
		mockAuthenticationWithUserID(randomSellerId);

		Page<ProductDTO> products = productService.getProductDTOPageForAuthorizedUserCached(
				new ProductInfoRequest()
						.setFilterSpecification(
								new ProductService.FilterSpecification()
										.state(PUBLISHED)
										.exceptSellers(Lists.newArrayList(randomSellerId))
						)
						.setViewQualification(new ProductService.ViewQualification().pageLength(16)),
				null,
				null,
				null
		);
		List<Long> sellerIds = products.getItems().stream()
				.map(product -> product.getSeller().getId())
				.collect(Collectors.toList());

		assertTrue(sellerIds.contains(randomSellerId));
	}

	@Test
	public void testGetSplitInfoTabbyInCurrency() {

		when(configParamService.getValueAsDoubleCached(configParamService.CONFIG_PARAM_SPLIT_PRODUCT_MAX_PRICE))
				.thenReturn(200000d);

		assertFalse(tabbySplitService.prepareSplitInfo(null, null).isPresent());
		assertFalse(tabbySplitService.prepareSplitInfo(new BigDecimal("200001"), null).isPresent());

		String aedCurrency = "AED";

		long currencyId = currencyService.getCurrencyDTOByCodeCached(aedCurrency).getId();
		long currencyToId = currencyService.getBaseCurrency().getId();

		CurrencyRate aedRate = new CurrencyRate();
		aedRate.setRateValue(new BigDecimal("50.00"));

		when(currencyRateService.findCurrencyRateWithCurrencyIdAndCurrencyToId(currencyId, currencyToId))
				.thenReturn(aedRate);

		assertFalse(tabbySplitService.prepareSplitInfo(new BigDecimal("200001"), aedCurrency).isPresent());

		BigDecimal initialPrice = new BigDecimal(111111.4d);
		SplitInfo splitInfo = tabbySplitService.prepareSplitInfo(initialPrice, aedCurrency).get();

		assertEquals("Сегодня", splitInfo.getParts().get(0).getLabel());
		assertEquals("1 месяц", splitInfo.getParts().get(1).getLabel());
		assertEquals("2 месяца", splitInfo.getParts().get(2).getLabel());
		assertEquals("3 месяца", splitInfo.getParts().get(3).getLabel());

		assertEquals(new BigDecimal(556), splitInfo.getParts().get(0).getValue());
		assertEquals(new BigDecimal(554), splitInfo.getParts().get(3).getValue());

		LocalDate expectedDelay = LocalDateTime.now().plus(1, ChronoUnit.MONTHS).atZone(ZoneId.of("Europe/Moscow")).toLocalDate();
		LocalDate testDelay = splitInfo.getParts().get(1).getDate().atZone(ZoneId.of("Europe/Moscow")).toLocalDate();
		assertTrue(expectedDelay.isEqual(testDelay));
	}

	@Test
	public void testGetSplitInfoYandex() {

		when(configParamService.getValueAsDoubleCached(configParamService.CONFIG_PARAM_SPLIT_PRODUCT_MAX_PRICE))
				.thenReturn(200000d);

		assertFalse(yandexSplitService.prepareSplitInfo(null).isPresent());
		assertFalse(yandexSplitService.prepareSplitInfo(new BigDecimal("200001")).isPresent());

		BigDecimal initialPrice = new BigDecimal(111111.4d);
		SplitInfo splitInfo = yandexSplitService.prepareSplitInfo(initialPrice).get();
		assertEquals(new BigDecimal(27778), splitInfo.getParts().get(0).getValue());
		assertEquals(new BigDecimal(27777), splitInfo.getParts().get(3).getValue());

		initialPrice = new BigDecimal(111109.5);
		splitInfo = yandexSplitService.prepareSplitInfo(initialPrice).get();
		assertEquals(new BigDecimal(27778), splitInfo.getParts().get(0).getValue());
		assertEquals(new BigDecimal(27776), splitInfo.getParts().get(3).getValue());

		initialPrice = new BigDecimal(111109);
		splitInfo = yandexSplitService.prepareSplitInfo(initialPrice).get();
		assertEquals(new BigDecimal(27778), splitInfo.getParts().get(0).getValue());
		assertEquals(new BigDecimal(27775), splitInfo.getParts().get(3).getValue());

		LocalDate expected2WeeksDelay = Instant.now().plus(14, ChronoUnit.DAYS).atZone(ZoneId.of("Europe/Moscow")).toLocalDate();
		LocalDate test2WeeksDelay = splitInfo.getParts().get(1).getDate().atZone(ZoneId.of("Europe/Moscow")).toLocalDate();
		assertTrue(expected2WeeksDelay.isEqual(test2WeeksDelay));
	}

	@Test
	@Transactional
	public void testYandexPlusPointCalculationSuccessful1() {

		when(configParamService.getValueAsDoubleCached(configParamService.CONFIG_PARAM_YANDEX_PLUS_POINTS_PRODUCT_MAX_AMOUNT))
				.thenReturn(1000000d);
		when(configParamService.getValueAsInteger(configParamService.CONFIG_PARAM_YANDEX_PLUS_POINTS_ORDER_MAX_POINTS_COUNT))
				.thenReturn(10000);
		when(configParamService.getValueAsDoubleCached(configParamService.CONFIG_PARAM_YANDEX_PLUS_POINTS_PERCENT))
				.thenReturn(5d);
		when(configParamService.getValueAsBoolean(eq(configParamService.CONFIG_PARAM_YANDEX_PLUS_POINTS_CALC_FOR_CONCIERGE_PRODUCTS), anyBoolean()))
				.thenReturn(true);

		User simpleAuthUser = simpleAuthorizedUser();

		// количество укладывается в максимум баллов

		Product product = createProduct(
				new ProductCreationParams(PUBLISHED, sellerId)
						.setCurrentPrice(new BigDecimal("100000"))
						.setShowAsConciergeTime(null),
				SYSTEM);

		ProductDTO productDTO = productMapper.toDTO(
				product,
				new MapProductsOptions().setForAuthorizedUser(simpleAuthUser));
		assertEquals(5000, productDTO.getYandexPlus().getPoints());

		// количество укладывается в максимум баллов, присутствуют консьержные товары (расчет баллов для консьержных товаров включен)

		product = createProduct(
				new ProductCreationParams(PUBLISHED, sellerId)
						.setCurrentPrice(new BigDecimal("100000"))
						.setShowAsConciergeTime(LocalDateTime.now()),
				SYSTEM);

		productDTO = productMapper.toDTO(
				product,
				new MapProductsOptions().setForAuthorizedUser(simpleAuthUser));
		assertEquals(5000, productDTO.getYandexPlus().getPoints());

		// количество превышает максимум баллов

		product = createProduct(
				new ProductCreationParams(PUBLISHED, sellerId)
						.setCurrentPrice(new BigDecimal("500000"))
						.setShowAsConciergeTime(null),
				SYSTEM);

		productDTO = productMapper.toDTO(
				product,
				new MapProductsOptions().setForAuthorizedUser(simpleAuthUser));
		assertEquals(10000, productDTO.getYandexPlus().getPoints());
	}

	@Test
	@Transactional
	public void testYandexPlusPointCalculationNotPerformed1() {

		double maxAmount = 100000d;

		when(configParamService.getValueAsDoubleCached(configParamService.CONFIG_PARAM_YANDEX_PLUS_POINTS_PRODUCT_MAX_AMOUNT))
				.thenReturn(maxAmount);
		when(configParamService.getValueAsBoolean(eq(configParamService.CONFIG_PARAM_YANDEX_PLUS_POINTS_CALC_FOR_CONCIERGE_PRODUCTS), anyBoolean()))
				.thenReturn(false);

		User simpleAuthUser = simpleAuthorizedUser();

		// попытка расчета с отсутствующей суммой

		Product product = createProduct(
				new ProductCreationParams(PUBLISHED, sellerId)
						.setCurrentPrice(null)
						.setShowAsConciergeTime(null),
				SYSTEM);

		ProductDTO productDTO = productMapper.toDTO(
				product,
				new MapProductsOptions().setForAuthorizedUser(simpleAuthUser));
		assertNull(productDTO.getYandexPlus());

		// попытка расчета с превышением суммы

		product = createProduct(
				new ProductCreationParams(PUBLISHED, sellerId)
						.setCurrentPrice(new BigDecimal(maxAmount + 1000))
						.setShowAsConciergeTime(null),
				SYSTEM);

		productDTO = productMapper.toDTO(
				product,
				new MapProductsOptions().setForAuthorizedUser(simpleAuthUser));
		assertNull(productDTO.getYandexPlus());

		// попытка расчета с укладывающейся суммой, но со статусом SOLD

		product = createProduct(
				new ProductCreationParams(SOLD, sellerId)
						.setCurrentPrice(new BigDecimal(maxAmount - 1000))
						.setShowAsConciergeTime(LocalDateTime.now()),
				SYSTEM);

		productDTO = productMapper.toDTO(
				product,
				new MapProductsOptions().setForAuthorizedUser(simpleAuthUser));
		assertNull(productDTO.getYandexPlus());

		// попытка расчета с укладывающейся суммой, но с наличием консьерж товаров (расчет баллов для консьержных товаров отключен)

		product = createProduct(
				new ProductCreationParams(PUBLISHED, sellerId)
						.setCurrentPrice(new BigDecimal(maxAmount - 1000))
						.setShowAsConciergeTime(LocalDateTime.now()),
				SYSTEM);

		productDTO = productMapper.toDTO(
				product,
				new MapProductsOptions().setForAuthorizedUser(simpleAuthUser));
		assertNull(productDTO.getYandexPlus());
	}

	/**
	 * Подстановка нужного пользователя для аутентификации
	 *
	 * @param userID идентификатор пользователя для подстановки
	 */
	private void mockAuthenticationWithUserID(Long userID) {
		Authentication authentication = Mockito.mock(Authentication.class);
		SecurityContext securityContext = Mockito.mock(SecurityContext.class);
		when(securityContext.getAuthentication()).thenReturn(authentication);
		SecurityContextHolder.setContext(securityContext);

		when(authentication.getPrincipal()).thenReturn(userID);
	}

    private Brand brand() {
        Brand brand = new Brand();
        brand.setId(1L);
        brand.setName("Brand");

        return brand;
    }

    private Category category() {
        Category category = new Category();
		category.setId(4L);
        category.setDisplayName("Category");
        return category;
    }

    private Long productConditionId() {
		return 1L;
    }

    private User seller(Long id) {
        User seller = new User();
		seller.setId(id);
        seller.setEmail("<EMAIL>");
        seller.setRegistrationTime(ZonedDateTime.now());
		seller.setSellerType(SellerType.INDIVIDUAL);
        return seller;
    }

	private User simpleAuthorizedUser() {
		User user = new User();
		user.setId(1L);
		user.setEmail("<EMAIL>");
		user.setRegistrationTime(ZonedDateTime.now());
		user.setSellerType(SellerType.INDIVIDUAL);
		return user;
	}

    private List<AttributeValue> attributeValues() {

        ArrayList<AttributeValue> values = new ArrayList<>();
        for (Long attrId: new long[] {1, 2}) {
            Attribute attribute = new Attribute();
            attribute.setId(attrId);
            attribute.setName("Attribute " + attrId);

            for (Long valueId: new long[] {1, 2}) {
                AttributeValue value = new AttributeValue();
                value.setId(valueId);
                value.setAttributeId(attribute.getId());
                value.setValue("Attribute " + attrId + " Value " + valueId);
            }
        }

        return values;
    }

	private Long randomBrandId() {
		mockAuthenticationWithUserID(sellerId);
		ProductService.FilterSpecification spec = new ProductService.FilterSpecification()
				.state(PUBLISHED);

		return productService.getRawProductsList(spec, null, null, null, null).stream()
				.filter(product -> !product.getSeller().getId().equals(sellerId))
				.filter(product -> !product.getSeller().getId().equals(user2Id))
				.findAny()
				.get()
				.getBrand()
				.getId();
	}

	private Long randomSellerId() {
		mockAuthenticationWithUserID(sellerId);
		ProductService.FilterSpecification spec = new ProductService.FilterSpecification()
				.state(PUBLISHED);

		return productService.getRawProductsList(spec, null, null, null, null).stream()
				.filter(product -> !product.getSeller().getId().equals(sellerId))
				.filter(product -> !product.getSeller().getId().equals(user2Id))
				.findAny()
				.get()
				.getSeller()
				.getId();
	}

	@Data
	@Accessors(chain = true)
	private class ProductCreationParams {
		private ProductState state;
		private long sellerId;
		private String vendorCode = null;
		private String storeCode = null;
		private BigDecimal currentPrice = BigDecimal.TEN;
		private LocalDateTime showAsConciergeTime = null;
		private boolean exclusiveLot = false;

		public ProductCreationParams(ProductState state, long sellerId) {
			this.state = state;
			this.sellerId = sellerId;
		}
	}

	private Product createProduct(ProductCreationParams params, ProductService.UserType userType) {
		Product p = new Product();
		p.setCategoryId(category().getId());
		p.setProductConditionId(productConditionId());
		p.setBrand(brand());
		p.setDeliveryDescription("Delivery description");
		p.setDescription("Description");
		p.setModel("Model");
		p.setName("Name");
		p.setOrigin("Origin");
		p.setPaymentDescription("Payment description");
		p.setSeller(seller(params.getSellerId()));
		p.setProductState(params.getState());
		p.setVendorCode(params.getVendorCode());
		p.setStoreCode(params.getStoreCode());
		p.setCurrentPrice(params.getCurrentPrice());
		p.setShowAsConciergeTime(params.getShowAsConciergeTime());
		p.setExclusiveLotTime(params.isExclusiveLot() ? ZonedDateTime.now() : null);
		return productService.saveProduct(p, userType);
	}

	private ProductItem createProductItem(Product product, boolean hidden) {
		ProductItem item = new ProductItem();
		item.setProduct(product);
		item.setDeleteTime(null);
		item.setHidden(hidden);
		item.setCount(1);
		item.setSize(sizeRepository.getOne(1L));
		return productItemRepository.save(item);
	}

	@Test
	@DisplayName("Тест установки и сброса флага айтема 'в бутике'")
	@Transactional
	public void testSetProductItemInBoutique() {

		mockAuthenticationWithUserID(sellerId);

		Product product = createProduct(
				new ProductCreationParams(DRAFT, sellerId)
						.setVendorCode("123456")
						.setStoreCode("777777"),
				SYSTEM);

		String firstBoutiqueLocationCode = ProductItemLocation.LOCATION_CODE_BOUTIQUE_STOLESHNIKOV;
		ProductTag firstBoutiqueProductTag = productTagService.getProductTagByLocationCode(firstBoutiqueLocationCode).get();

		commitAndStartNewTransaction();

		ProductItem productItem1 = new ProductItem();
		productItem1.setProduct(product);
		productItem1.setCount(1);
		productItem1.setSize(sizeRepository.getOne(1L));
		productItem1 = productItemService.save(productItem1);

		final ProductItem productItem1Final = productItem1;

		ProductItem productItem2 = new ProductItem();
		productItem2.setProduct(product);
		productItem2.setCount(2);
		productItem2.setSize(sizeRepository.getOne(1L));
		productItem2 = productItemService.save(productItem2);

		final ProductItem productItem2Final = productItem1;

		commitAndStartNewTransaction();

		// сразу после создания ничего не указано

		List<ProductItemDTO> productItems = productItemService.getProductItems(product.getId());
		assertEquals(2, productItems.size());
		productItems.forEach(it -> {
			assertFalse(it.isInBoutique());
			assertTrue(it.getStocks().isEmpty());
		});

		ProductDTO productDTO = productService.getProductDTO(product.getId(), HUMAN);
		assertFalse(productDTO.getInBoutique());
		assertTrue(getLocationTags(productDTO).isEmpty());

		// устанавливаем флаг для productItem1

		productItem1 = productItemService.setProductItemInBoutique(productItem1, LocalDateTime.now());

		commitAndStartNewTransaction();

		productItems = productItemService.getProductItems(product.getId());
		assertEquals(2, productItems.size());
		productItems.forEach(it -> {
			if (it.getId() == productItem1Final.getId()) {
				assertTrue(it.isInBoutique());
				assertEquals(1, it.getStocks().size());
				assertEquals(productItem1Final.getCount(), it.getStocks().get(0).getCount());
				assertEquals(firstBoutiqueLocationCode, it.getStocks().get(0).getLocation().getCode());
			} else if (it.getId() == productItem2Final.getId()) {
				assertFalse(it.isInBoutique());
				assertTrue(it.getStocks().isEmpty());
			}
		});

		productDTO = productService.getProductDTO(product.getId(), HUMAN);
		assertTrue(productDTO.getInBoutique());
		List<ProductTagDTO> locationTags = getLocationTags(productDTO);
		assertEquals(1, locationTags.size());
		assertEquals(
				firstBoutiqueProductTag.getId(),
				locationTags.get(0).getId());

		// устанавливаем флаг для productItem2

		productItem2 = productItemService.setProductItemInBoutique(productItem2, LocalDateTime.now());

		commitAndStartNewTransaction();

		productItems = productItemService.getProductItems(product.getId());
		assertEquals(2, productItems.size());
		productItems.forEach(it -> {
			if (it.getId() == productItem1Final.getId()) {
				assertTrue(it.isInBoutique());
				assertEquals(1, it.getStocks().size());
				assertEquals(productItem1Final.getCount(), it.getStocks().get(0).getCount());
				assertEquals(firstBoutiqueLocationCode, it.getStocks().get(0).getLocation().getCode());
			} else if (it.getId() == productItem2Final.getId()) {
				assertTrue(it.isInBoutique());
				assertEquals(1, it.getStocks().size());
				assertEquals(productItem2Final.getCount(), it.getStocks().get(0).getCount());
				assertEquals(firstBoutiqueLocationCode, it.getStocks().get(0).getLocation().getCode());
			}
		});

		productDTO = productService.getProductDTO(product.getId(), HUMAN);
		assertTrue(productDTO.getInBoutique());
		locationTags = getLocationTags(productDTO);
		assertEquals(1, locationTags.size());
		assertEquals(
				firstBoutiqueProductTag.getId(),
				locationTags.get(0).getId());

		// снимаем флаг для productItem2

		productItem2 = productItemService.setProductItemInBoutique(productItem2, null);

		commitAndStartNewTransaction();

		productItems = productItemService.getProductItems(product.getId());
		assertEquals(2, productItems.size());
		productItems.forEach(it -> {
			if (it.getId() == productItem1Final.getId()) {
				assertTrue(it.isInBoutique());
				assertEquals(1, it.getStocks().size());
				assertEquals(productItem1Final.getCount(), it.getStocks().get(0).getCount());
				assertEquals(firstBoutiqueLocationCode, it.getStocks().get(0).getLocation().getCode());
			} else if (it.getId() == productItem2Final.getId()) {
				assertFalse(it.isInBoutique());
				assertTrue(it.getStocks().isEmpty());
			}
		});

		productDTO = productService.getProductDTO(product.getId(), HUMAN);
		assertTrue(productDTO.getInBoutique());
		locationTags = getLocationTags(productDTO);
		assertEquals(1, locationTags.size());
		assertEquals(
				firstBoutiqueProductTag.getId(),
				locationTags.get(0).getId());

		// снимаем флаг для productItem1

		productItem1 = productItemService.setProductItemInBoutique(productItem1, null);

		commitAndStartNewTransaction();

		productItems = productItemService.getProductItems(product.getId());
		assertEquals(2, productItems.size());
		productItems.forEach(it -> {
			assertFalse(it.isInBoutique());
			assertTrue(it.getStocks().isEmpty());
		});

		productDTO = productService.getProductDTO(product.getId(), HUMAN);
		assertFalse(productDTO.getInBoutique());
		assertTrue(getLocationTags(productDTO).isEmpty());
	}

	@Test
	@DisplayName("Тест добавления и удаления остатков для айтема")
	@Transactional
	public void testSetProductItemStocks() {

		mockAuthenticationWithUserID(sellerId);

		ProductItemLocation stolLocation = productItemLocationRepository
				.getProductItemLocationByCode(LOCATION_CODE_BOUTIQUE_STOLESHNIKOV).get();
		ProductItemLocation kuzLocation = productItemLocationRepository
				.getProductItemLocationByCode(LOCATION_CODE_BOUTIQUE_KUZNETSKY_BRIDGE).get();

		Long stolTagId = productTagService.getProductTagIdByLocationCodeOrThrowCached(
				LOCATION_CODE_BOUTIQUE_STOLESHNIKOV);
		Long kuzTagId = productTagService.getProductTagIdByLocationCodeOrThrowCached(
                LOCATION_CODE_BOUTIQUE_KUZNETSKY_BRIDGE);

		Product product = createProduct(
				new ProductCreationParams(DRAFT, sellerId)
						.setVendorCode("123456")
						.setStoreCode("777777"),
				SYSTEM);

		commitAndStartNewTransaction();

		ProductItem productItem = new ProductItem();
		productItem.setProduct(product);
		productItem.setCount(1);
		productItem = productItemService.save(productItem);

		final ProductItem productItem1Final = productItem;

		commitAndStartNewTransaction();

		// устанавливаем stock в бутике Столешников

		int stolCount = 1;

		productItem = productItemService.addOrUpdateStock(productItem, stolLocation, stolCount, ZonedDateTime.now());

		commitAndStartNewTransaction();

		List<ProductItemDTO> productItems = productItemService.getProductItems(product.getId());
		assertEquals(1, productItems.size());

		ProductItemDTO productItemDto = productItems.get(0);
		assertTrue(productItemDto.isInBoutique());
		assertEquals(1, productItemDto.getStocks().size());
		assertEquals(stolCount, productItemDto.getStocks().get(0).getCount());
		assertEquals(LOCATION_CODE_BOUTIQUE_STOLESHNIKOV, productItemDto.getStocks().get(0).getLocation().getCode());

		ProductDTO productDTO = productService.getProductDTO(product.getId(), HUMAN);
		assertTrue(productDTO.getInBoutique());
		List<ProductTagDTO> locationTags = getLocationTags(productDTO);
		assertEquals(1, locationTags.size());
		assertEquals(stolTagId, locationTags.get(0).getId());

		// устанавливаем сток в бутике Охотный

		int kuzCount = 2;

		productItem = productItemService.addOrUpdateStock(productItem, kuzLocation, kuzCount, ZonedDateTime.now());

		commitAndStartNewTransaction();

		productItems = productItemService.getProductItems(product.getId());
		assertEquals(1, productItems.size());

		productItemDto = productItems.get(0);
		assertTrue(productItemDto.isInBoutique());
		assertEquals(2, productItemDto.getStocks().size());
		assertTrue(productItemDto.getStocks().stream()
				.anyMatch(stock -> LOCATION_CODE_BOUTIQUE_STOLESHNIKOV.equals(stock.getLocation().getCode()) && stock.getCount() == stolCount));
		assertTrue(productItemDto.getStocks().stream()
				.anyMatch(stock -> LOCATION_CODE_BOUTIQUE_KUZNETSKY_BRIDGE.equals(stock.getLocation().getCode()) && stock.getCount() == kuzCount));

		productDTO = productService.getProductDTO(product.getId(), HUMAN);
		assertTrue(productDTO.getInBoutique());
		locationTags = getLocationTags(productDTO);
		assertEquals(2, locationTags.size());
		assertTrue(locationTags.stream().anyMatch(it -> it.getId() == stolTagId));
		assertTrue(locationTags.stream().anyMatch(it -> it.getId() == kuzTagId));

		// убираем сток в бутике Столешников

		productItem = productItemService.removeStock(productItem, stolLocation);

		commitAndStartNewTransaction();

		productItems = productItemService.getProductItems(product.getId());
		assertEquals(1, productItems.size());

		productItemDto = productItems.get(0);
		assertTrue(productItemDto.isInBoutique());
		assertEquals(1, productItemDto.getStocks().size());
		assertEquals(kuzCount, productItemDto.getStocks().get(0).getCount());
		assertEquals(LOCATION_CODE_BOUTIQUE_KUZNETSKY_BRIDGE, productItemDto.getStocks().get(0).getLocation().getCode());

		productDTO = productService.getProductDTO(product.getId(), HUMAN);
		assertTrue(productDTO.getInBoutique());
		locationTags = getLocationTags(productDTO);
		assertEquals(1, locationTags.size());
		assertEquals(kuzTagId, locationTags.get(0).getId());

		// убираем сток в бутике Охотный

		productItem = productItemService.removeStock(productItem, kuzLocation);

		commitAndStartNewTransaction();

		productItems = productItemService.getProductItems(product.getId());
		assertEquals(1, productItems.size());

		productItemDto = productItems.get(0);
		assertFalse(productItemDto.isInBoutique());
		assertTrue(productItemDto.getStocks().isEmpty());

		productDTO = productService.getProductDTO(product.getId(), HUMAN);
		assertFalse(productDTO.getInBoutique());
		assertTrue(getLocationTags(productDTO).isEmpty());
	}

	@Test
	@Transactional
	public void testHideLiked() {
		User user1 = userService.getOne(sellerId);

		Product product1 = createProduct(new ProductCreationParams(PUBLISHED, user1.getId()), SYSTEM);
		Product product2 = createProduct(new ProductCreationParams(PUBLISHED, user1.getId()), SYSTEM);

		int expected = 2;

		likeService.like(product1, user1);
		likeService.like(product2, user1);

		//Аутентификация НЕ под лайкнувшим пользователем
		mockAuthenticationWithUserID(user2Id);

		//Проверка того, что все лайкнутые товары отображаются
		checkLikedProducts(expected, user1.getId());

		//Установка запрета отображения лайкнутых товаров
		user1.setHideLikedTime(ZonedDateTime.now());
		userService.save(user1);

		//Проверка того, что все лайкнутые товары НЕ отображаются
		checkLikedProducts(0, user1.getId());

		//Аутентификация под лайкнувшим пользователем (+ из предыдущего пункта остался запрет на отображение лайкнутых товаров)
		mockAuthenticationWithUserID(user1.getId());

		//Проверка того, что все лайкнутые товары отображаются
		checkLikedProducts(expected, user1.getId());
	}

	private void checkLikedProducts(int expectedSize, Long userId) {
		List<ProductDTO> products = productService.getLikedProducts(userId, null, true);
		assertNotNull(products);
		assertEquals(expectedSize, products.size());

		Page<ProductDTO> page = productService.getLikedProductsPage(userId, PageRequest.of(1, 20), null, true);
		assertNotNull(page);
		assertNotNull(page.getItems());
		assertEquals(expectedSize, page.getItems().size());
	}

	private List<ProductTagDTO> getLocationTags(ProductDTO productDTO) {
		return productDTO.getTags().stream()
				.filter(t -> t.getCategory() == ProductTagCategory.LOCATION)
				.collect(Collectors.toList());
	}

	@Test
	public void testGetLikedExclusiveLots() {

		when(loyaltyServicePropertiesMock.isExclusiveLotsPrivilegeEnabled()).thenReturn(true);

		User sellerUser = createUser();
		User likedUser = createUser();
		User otherUser = createUser();

		Product sellerLikedUserLot = createProduct(
				new ProductCreationParams(PUBLISHED, likedUser.getId()).setExclusiveLot(true),
				SYSTEM);
		createProductItem(sellerLikedUserLot, false);

		Product sellerOtherUserLot = createProduct(
				new ProductCreationParams(PUBLISHED, otherUser.getId()).setExclusiveLot(true),
				SYSTEM);
		createProductItem(sellerOtherUserLot, false);

		Product notLot = createProduct(
				new ProductCreationParams(PUBLISHED, sellerUser.getId()),
				SYSTEM);
		createProductItem(notLot, false);

		likeService.like(sellerLikedUserLot, likedUser);
		likeService.like(sellerOtherUserLot, likedUser);
		likeService.like(notLot, likedUser);

		// пользователь не блэк, получает свое избранное, привилегия включена

		mockAuthenticationWithUserID(likedUser.getId());
		when(loyaltyServiceMock.hasUserBlackLoyaltyStatus(anyLong())).thenReturn(false);

		Page<ProductDTO> productsPage = callInTransaction.runInAnyTransaction(() ->
				productService.getLikedProductsPage(likedUser.getId(), PageRequest.of(1, 100), "RUB", true));
		ProductDTO foundSellerLikedUserLot = productsPage.getItems()
				.stream()
				.filter(p -> Objects.equals(p.getProductId(), sellerLikedUserLot.getId()))
				.findAny()
				.orElse(null);
		assertNotNull(foundSellerLikedUserLot);
		assertTrue(foundSellerLikedUserLot.getIsAvailable());

		ProductDTO foundSellerOtherUserLot = productsPage.getItems()
				.stream()
				.filter(p -> Objects.equals(p.getProductId(), sellerOtherUserLot.getId()))
				.findAny()
				.orElse(null);
		assertNotNull(foundSellerOtherUserLot);
		assertFalse(foundSellerOtherUserLot.getIsAvailable());

		ProductDTO foundNotLot = productsPage.getItems()
				.stream()
				.filter(p -> Objects.equals(p.getProductId(), notLot.getId()))
				.findAny()
				.orElse(null);
		assertNotNull(foundNotLot);
		assertTrue(foundNotLot.getIsAvailable());

		// пользователь блэк, получает свое избранное, привилегия включена

		when(loyaltyServiceMock.hasUserBlackLoyaltyStatus(anyLong())).thenReturn(true);

		productsPage = callInTransaction.runInAnyTransaction(() ->
				productService.getLikedProductsPage(likedUser.getId(), PageRequest.of(1, 100), "RUB", true));
		foundSellerLikedUserLot = productsPage.getItems()
				.stream()
				.filter(p -> Objects.equals(p.getProductId(), sellerLikedUserLot.getId()))
				.findAny()
				.orElse(null);
		assertNotNull(foundSellerLikedUserLot);
		assertTrue(foundSellerLikedUserLot.getIsAvailable());

		foundSellerOtherUserLot = productsPage.getItems()
				.stream()
				.filter(p -> Objects.equals(p.getProductId(), sellerOtherUserLot.getId()))
				.findAny()
				.orElse(null);
		assertNotNull(foundSellerOtherUserLot);
		assertTrue(foundSellerOtherUserLot.getIsAvailable());

		foundNotLot = productsPage.getItems()
				.stream()
				.filter(p -> Objects.equals(p.getProductId(), notLot.getId()))
				.findAny()
				.orElse(null);
		assertNotNull(foundNotLot);
		assertTrue(foundNotLot.getIsAvailable());

		// пользователь не блэк, получает чужое избранное, привилегия включена

		mockAuthenticationWithUserID(otherUser.getId());
		when(loyaltyServiceMock.hasUserBlackLoyaltyStatus(anyLong())).thenReturn(false);

		productsPage = callInTransaction.runInAnyTransaction(() ->
				productService.getLikedProductsPage(likedUser.getId(), PageRequest.of(1, 100), "RUB", true));
		foundSellerLikedUserLot = productsPage.getItems()
				.stream()
				.filter(p -> Objects.equals(p.getProductId(), sellerLikedUserLot.getId()))
				.findAny()
				.orElse(null);
		assertNull(foundSellerLikedUserLot);

		foundSellerOtherUserLot = productsPage.getItems()
				.stream()
				.filter(p -> Objects.equals(p.getProductId(), sellerOtherUserLot.getId()))
				.findAny()
				.orElse(null);
		assertNotNull(foundSellerOtherUserLot);
		assertTrue(foundSellerOtherUserLot.getIsAvailable());

		foundNotLot = productsPage.getItems()
				.stream()
				.filter(p -> Objects.equals(p.getProductId(), notLot.getId()))
				.findAny()
				.orElse(null);
		assertNotNull(foundNotLot);
		assertTrue(foundNotLot.getIsAvailable());

		// пользователь блэк, получает чужое избранное, привилегия включена

		when(loyaltyServiceMock.hasUserBlackLoyaltyStatus(anyLong())).thenReturn(true);

		productsPage = callInTransaction.runInAnyTransaction(() ->
				productService.getLikedProductsPage(likedUser.getId(), PageRequest.of(1, 100), "RUB", true));
		foundSellerLikedUserLot = productsPage.getItems()
				.stream()
				.filter(p -> Objects.equals(p.getProductId(), sellerLikedUserLot.getId()))
				.findAny()
				.orElse(null);
		assertNotNull(foundSellerLikedUserLot);
		assertTrue(foundSellerLikedUserLot.getIsAvailable());

		foundSellerOtherUserLot = productsPage.getItems()
				.stream()
				.filter(p -> Objects.equals(p.getProductId(), sellerOtherUserLot.getId()))
				.findAny()
				.orElse(null);
		assertNotNull(foundSellerOtherUserLot);
		assertTrue(foundSellerOtherUserLot.getIsAvailable());

		foundNotLot = productsPage.getItems()
				.stream()
				.filter(p -> Objects.equals(p.getProductId(), notLot.getId()))
				.findAny()
				.orElse(null);
		assertNotNull(foundNotLot);
		assertTrue(foundNotLot.getIsAvailable());

		// пользователь не блэк, получает свое избранное, привилегия отключена

		when(loyaltyServicePropertiesMock.isExclusiveLotsPrivilegeEnabled()).thenReturn(false);

		mockAuthenticationWithUserID(likedUser.getId());
		when(loyaltyServiceMock.hasUserBlackLoyaltyStatus(anyLong())).thenReturn(false);

		productsPage = callInTransaction.runInAnyTransaction(() ->
				productService.getLikedProductsPage(likedUser.getId(), PageRequest.of(1, 100), "RUB", true));
		foundSellerLikedUserLot = productsPage.getItems()
				.stream()
				.filter(p -> Objects.equals(p.getProductId(), sellerLikedUserLot.getId()))
				.findAny()
				.orElse(null);
		assertNotNull(foundSellerLikedUserLot);
		assertTrue(foundSellerLikedUserLot.getIsAvailable());

		foundSellerOtherUserLot = productsPage.getItems()
				.stream()
				.filter(p -> Objects.equals(p.getProductId(), sellerOtherUserLot.getId()))
				.findAny()
				.orElse(null);
		assertNotNull(foundSellerOtherUserLot);
		assertTrue(foundSellerOtherUserLot.getIsAvailable());

		foundNotLot = productsPage.getItems()
				.stream()
				.filter(p -> Objects.equals(p.getProductId(), notLot.getId()))
				.findAny()
				.orElse(null);
		assertNotNull(foundNotLot);
		assertTrue(foundNotLot.getIsAvailable());

		// пользователь блэк, получает свое избранное, привилегия отключена

		when(loyaltyServiceMock.hasUserBlackLoyaltyStatus(anyLong())).thenReturn(true);

		productsPage = callInTransaction.runInAnyTransaction(() ->
				productService.getLikedProductsPage(likedUser.getId(), PageRequest.of(1, 100), "RUB", true));
		foundSellerLikedUserLot = productsPage.getItems()
				.stream()
				.filter(p -> Objects.equals(p.getProductId(), sellerLikedUserLot.getId()))
				.findAny()
				.orElse(null);
		assertNotNull(foundSellerLikedUserLot);
		assertTrue(foundSellerLikedUserLot.getIsAvailable());

		foundSellerOtherUserLot = productsPage.getItems()
				.stream()
				.filter(p -> Objects.equals(p.getProductId(), sellerOtherUserLot.getId()))
				.findAny()
				.orElse(null);
		assertNotNull(foundSellerOtherUserLot);
		assertTrue(foundSellerOtherUserLot.getIsAvailable());

		foundNotLot = productsPage.getItems()
				.stream()
				.filter(p -> Objects.equals(p.getProductId(), notLot.getId()))
				.findAny()
				.orElse(null);
		assertNotNull(foundNotLot);
		assertTrue(foundNotLot.getIsAvailable());

		// пользователь не блэк, получает чужое избранное, привилегия отключена

		mockAuthenticationWithUserID(otherUser.getId());
		when(loyaltyServiceMock.hasUserBlackLoyaltyStatus(anyLong())).thenReturn(false);

		productsPage = callInTransaction.runInAnyTransaction(() ->
				productService.getLikedProductsPage(likedUser.getId(), PageRequest.of(1, 100), "RUB", true));
		foundSellerLikedUserLot = productsPage.getItems()
				.stream()
				.filter(p -> Objects.equals(p.getProductId(), sellerLikedUserLot.getId()))
				.findAny()
				.orElse(null);
		assertNotNull(foundSellerLikedUserLot);
		assertTrue(foundSellerLikedUserLot.getIsAvailable());

		foundSellerOtherUserLot = productsPage.getItems()
				.stream()
				.filter(p -> Objects.equals(p.getProductId(), sellerOtherUserLot.getId()))
				.findAny()
				.orElse(null);
		assertNotNull(foundSellerOtherUserLot);
		assertTrue(foundSellerOtherUserLot.getIsAvailable());

		foundNotLot = productsPage.getItems()
				.stream()
				.filter(p -> Objects.equals(p.getProductId(), notLot.getId()))
				.findAny()
				.orElse(null);
		assertNotNull(foundNotLot);
		assertTrue(foundNotLot.getIsAvailable());

		// пользователь блэк, получает чужое избранное, привилегия отключена

		when(loyaltyServiceMock.hasUserBlackLoyaltyStatus(anyLong())).thenReturn(true);

		productsPage = callInTransaction.runInAnyTransaction(() ->
				productService.getLikedProductsPage(likedUser.getId(), PageRequest.of(1, 100), "RUB", true));
		foundSellerLikedUserLot = productsPage.getItems()
				.stream()
				.filter(p -> Objects.equals(p.getProductId(), sellerLikedUserLot.getId()))
				.findAny()
				.orElse(null);
		assertNotNull(foundSellerLikedUserLot);
		assertTrue(foundSellerLikedUserLot.getIsAvailable());

		foundSellerOtherUserLot = productsPage.getItems()
				.stream()
				.filter(p -> Objects.equals(p.getProductId(), sellerOtherUserLot.getId()))
				.findAny()
				.orElse(null);
		assertNotNull(foundSellerOtherUserLot);
		assertTrue(foundSellerOtherUserLot.getIsAvailable());

		foundNotLot = productsPage.getItems()
				.stream()
				.filter(p -> Objects.equals(p.getProductId(), notLot.getId()))
				.findAny()
				.orElse(null);
		assertNotNull(foundNotLot);
		assertTrue(foundNotLot.getIsAvailable());
	}

	private User createUser() {
		return userRepository.save(
				new User()
					.setChangeTime(Utils.nowAtUTC())
					.setEmail(RandomStringUtils.randomAlphabetic(5) + "@email.com")
					.setNickname(RandomStringUtils.randomAlphabetic(5))
					.setRegistrationTime(ZonedDateTime.now())
					.setSellerType(SellerType.INDIVIDUAL)
					.setUserType(SIMPLE_USER)
					.setChatToken(UUID.randomUUID().toString())
					.setPhone(RandomStringUtils.randomNumeric(10))
					.setCommissionGrid(commissionGridService.getDefaultCommissionGrid()));
	}
}
