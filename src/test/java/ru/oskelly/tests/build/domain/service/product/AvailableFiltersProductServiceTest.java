package ru.oskelly.tests.build.domain.service.product;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.model.attribute.Attribute;
import su.reddot.domain.model.attribute.AttributeValue;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductAttributeValueBinding;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.user.SellerType;
import su.reddot.domain.service.attribute.AttributeService;
import su.reddot.domain.service.catalog.AvailableFilters;
import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.filter.processor.filter.impl.SellerTypeFilterProcessor;
import su.reddot.domain.service.product.ProductService;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class AvailableFiltersProductServiceTest extends AbstractSpringTest {

    @Autowired
    private ProductService productService;

	@Autowired
	private AttributeService attributeService;

	private List<Long> categoryIds = Arrays.asList(4L, 150L); //Женское -> Сумки -> Рюкзаки | Мужское -> Обувь -> Ботинки -> Высокие ботинки
    private long brandId = 9L;//08SIRCUS
	private long sizeId = 3L;
	private long productConditionId = 1L;//new
	private boolean vintage = true;//yeah, very vintage
	private boolean onSale = true;//of course!
	private boolean startPriceHigher = true;//of course!
	private boolean ourChoice = true;//always
	private boolean newCollection = true;//true

	private boolean exclusiveSelection = true;//true
	private boolean isInStock = true;//true
	private BigDecimal startPrice = new BigDecimal(5000);
	private BigDecimal endPrice = new BigDecimal(10000);
	private List<SellerType> interestingSellerTypes = Arrays.asList(SellerType.INDIVIDUAL, SellerType.BUYER);

	Map<Attribute, List<AttributeValue>> attributeListMap;

	@BeforeEach
	public void init(){
		attributeListMap = attributeService.getActualAttributeValues(categoryIds, true);
	}

	@Transactional
	@Test
	public void getAvailableFiltersByCategory() {
		ProductService.FilterSpecification spec = getDefaultSpec();
		AvailableFilters availableFilters = productService.getAvailableFilters(spec, ProductService.UserType.HUMAN);
		List<Product> products = getProducts(spec);

		assertConformity(products, availableFilters, getDefaultFilterException().exceptCategories(true));
	}

	@Transactional
	@Test
	public void getAvailableFiltersBySize() {
		ProductService.FilterSpecification spec = getDefaultSpec().interestingSizes(Arrays.asList(sizeId));
		AvailableFilters availableFilters = productService.getAvailableFilters(spec, ProductService.UserType.HUMAN);
		List<Product> products = getProducts(spec);

		assertConformity(products, availableFilters, getDefaultFilterException().exceptSizes(true));
	}

	@Transactional
    @Test
    public void getAvailableFiltersByBrand() {
	    ProductService.FilterSpecification spec = getDefaultSpec().interestingBrands(Arrays.asList(brandId));
	    AvailableFilters availableFilters = productService.getAvailableFilters(spec, ProductService.UserType.HUMAN);
	    List<Product> products = getProducts(spec);

	    assertConformity(products, availableFilters, getDefaultFilterException().exceptBrands(true));
    }

	@Transactional
	@Test
	public void getAvailableFiltersByProductCondition() {
		//Из-за большого размера выборки накладываем доп. фильтр по бренду
		List<Long> brandIds = Arrays.asList(brandId);
		ProductService.FilterSpecification spec = getDefaultSpec().interestingConditions(Arrays.asList(productConditionId))
				.interestingBrands(brandIds);
		AvailableFilters availableFilters = productService.getAvailableFilters(spec, ProductService.UserType.HUMAN);
		List<Product> products = getProducts(spec);

		assertConformity(products, availableFilters, getDefaultFilterException().exceptConditions(true).exceptBrands(true));
	}

	@Transactional
	@Test
	public void getAvailableFiltersByVintage() {
		ProductService.FilterSpecification spec = getDefaultSpec().isVintage(vintage);
		AvailableFilters availableFilters = productService.getAvailableFilters(spec, ProductService.UserType.HUMAN);
		List<Product> products = getProducts(spec);

		assertConformity(products, availableFilters, getDefaultFilterException().exceptIsVintage(true));
	}

	@Transactional
	@Test
	public void getAvailableFiltersByOnSale() {
		ProductService.FilterSpecification spec = getDefaultSpec().isOnSale(onSale);
		AvailableFilters availableFilters = productService.getAvailableFilters(spec, ProductService.UserType.HUMAN);
		List<Product> products = getProducts(spec);

		assertConformity(products, availableFilters, getDefaultFilterException().exceptIsOnSale(true));
	}

	@Transactional
	@Test
	public void getAvailableFiltersByStartPriceHigher() {
		ProductService.FilterSpecification spec = getDefaultSpec().isStartPriceHigher(startPriceHigher);
		AvailableFilters availableFilters = productService.getAvailableFilters(spec, ProductService.UserType.HUMAN);
		List<Product> products = getProducts(spec);

		assertConformity(products, availableFilters, getDefaultFilterException().exceptIsStartPriceHigher(true));
	}

	@Transactional
	@Test
	public void getAvailableFiltersByOurChoice() {
		ProductService.FilterSpecification spec = getDefaultSpec().hasOurChoice(ourChoice);
		AvailableFilters availableFilters = productService.getAvailableFilters(spec, ProductService.UserType.HUMAN);
		List<Product> products = getProducts(spec);

		assertConformity(products, availableFilters, getDefaultFilterException().exceptHasOurChoice(true));
	}

	@Transactional
	@Test
	public void getAvailableFiltersByNewCollection() {
		ProductService.FilterSpecification spec = getDefaultSpec().isNewCollection(newCollection);
		AvailableFilters availableFilters = productService.getAvailableFilters(spec, ProductService.UserType.HUMAN);
		List<Product> products = getProducts(spec);

		assertConformity(products, availableFilters, getDefaultFilterException().exceptIsNewCollection(true));
	}

	@Transactional
	@Test
	public void getAvailableFiltersByExclusiveSelection() {
		ProductService.FilterSpecification spec = getDefaultSpec().isExclusiveSelection(exclusiveSelection);
		AvailableFilters availableFilters = productService.getAvailableFilters(spec, ProductService.UserType.HUMAN);
		List<Product> products = getProducts(spec);

		assertConformity(products, availableFilters, getDefaultFilterException().exceptIsExclusiveSelection(true));
	}

	@Transactional
	@Test
	public void getAvailableFiltersByIsInStock() {

		// TODO: для корректного теста нужен product с тегом WAREHOUSE

		ProductService.FilterSpecification spec = getDefaultSpec().isInStock(isInStock);
		AvailableFilters availableFilters = productService.getAvailableFilters(spec, ProductService.UserType.HUMAN);
		List<Product> products = getProducts(spec);

		assertConformity(products, availableFilters, getDefaultFilterException().exceptIsInStock(true));
	}

	@Transactional
	@Test
	public void getAvailableFiltersByStartPrice() {
		ProductService.FilterSpecification spec = getDefaultSpec().startPrice(startPrice);
		AvailableFilters availableFilters = productService.getAvailableFilters(spec, ProductService.UserType.HUMAN);
		List<Product> products = getProducts(spec);

		assertConformity(products, availableFilters, getDefaultFilterException().exceptStartPrice(true));
	}

	@Transactional
	@Test
	public void getAvailableFiltersByEndPrice() {
		ProductService.FilterSpecification spec = getDefaultSpec().endPrice(endPrice);
		AvailableFilters availableFilters = productService.getAvailableFilters(spec, ProductService.UserType.HUMAN);
		List<Product> products = getProducts(spec);

		assertConformity(products, availableFilters, getDefaultFilterException().exceptEndPrice(true));
	}

	@Transactional
	@Test
	public void getAvailableFiltersByIsPro() {
		ProductService.FilterSpecification spec = getDefaultSpec().interestingSellerTypes(interestingSellerTypes);
		AvailableFilters availableFilters = productService.getAvailableFilters(spec, ProductService.UserType.HUMAN);
		List<Product> products = getProducts(spec);

		assertConformity(products, availableFilters, getDefaultFilterException().exceptSellerTypes(true));
	}

	@Transactional
	@Test
	public void getAvailableFiltersByAttribute() {
		for(Attribute attribute : attributeListMap.keySet()){
			List<AttributeValue> values = attributeListMap.get(attribute);
			int randomIndex = (int)(Math.random() * values.size());
			AttributeValue value = values.get(randomIndex);

			ProductService.FilterSpecification spec = getDefaultSpec().interestingAttributeValues(Arrays.asList(value.getId()));
			AvailableFilters availableFilters = productService.getAvailableFilters(spec, ProductService.UserType.HUMAN);
			List<Product> products = getProducts(spec);

			assertConformity(products, availableFilters, getDefaultFilterException().exceptThisAttribute(attribute));
		}
	}

	private ProductService.FilterException getDefaultFilterException(){
		return new ProductService.FilterException().exceptCategories(true);
	}

	private ProductService.FilterSpecification getDefaultSpec(){
		return new ProductService.FilterSpecification().categoriesIds(categoryIds);
	}

	private void assertConformity(List<Product> products, AvailableFilters availableFilters, ProductService.FilterException filterException){
		BigDecimal productsStartPrice = getStartPrice(products);
		BigDecimal productsEndPrice = getEndPrice(products);

		if(!filterException.exceptCategories())
			assertCategoriesConformity(products, availableFilters);
		if(!filterException.exceptSizes())
			assertSizeConformity(products, availableFilters);
		if(!filterException.exceptBrands())
			assertBrandsConformity(products, availableFilters);
		if(!filterException.exceptConditions())
			assertProductConditionConformity(products, availableFilters);
		if(!filterException.exceptIsVintage())
			assertVintageConformity(products, availableFilters);
		if(!filterException.exceptIsOnSale())
			assertIsOnSaleConformity(products, availableFilters);
		if(!filterException.exceptIsStartPriceHigher())
			assertIsStartPriceHigherConformity(products, availableFilters);
		if(!filterException.exceptHasOurChoice())
			assertOurChoiceConformity(products, availableFilters);
		if(!filterException.exceptIsExclusiveSelection())
			assertIsExclusiveSelectionConformity(products, availableFilters);
		if(!filterException.exceptIsNewCollection())
			assertIsNewCollectionConformity(products, availableFilters);
		if(!filterException.exceptIsInStock())
			assertIsInStockConformity(products, availableFilters);
		if(!filterException.exceptSellerTypes())
			assertSellerTypesConformity(products, availableFilters);

		if(!filterException.exceptStartPrice())
			assertStartPriceConformity(productsStartPrice, availableFilters);
		else
			//Заодно проверим, попадает ли выборка у установленное ограничение
			assertTrue(isGreaterOrSame(productsStartPrice, startPrice));

		if(!filterException.exceptEndPrice())
			assertEndPriceConformity(productsEndPrice, availableFilters);
		else
			//Заодно проверим, попадает ли выборка у установленное ограничение
			assertTrue(isGreaterOrSame(endPrice, productsEndPrice));

		for(Attribute attribute : attributeListMap.keySet()){
			if(attribute != filterException.exceptThisAttribute())
				assertAttributeConformity(products, availableFilters, attribute);
		}
	}

    private void assertSizeConformity(List<Product> products, AvailableFilters availableFilters){
		List<Long> productSizeIds = new ArrayList<>();
		for(Product product : products){
			for(ProductItem pi : product.getAvailableProductItems()){
				if(pi.getSize() != null) productSizeIds.add(pi.getSize().getId());
			}
		}
		assertTrue(equalsIgnoreOrderAndCount(productSizeIds, availableFilters.getSize()));
    }

	private void assertCategoriesConformity(List<Product> products, AvailableFilters availableFilters){
		List<Long> categoryIds = products.stream().map(p -> p.getCategory().getId()).collect(Collectors.toList());
		assertTrue(equalsIgnoreOrderAndCount(categoryIds, availableFilters.getCategory()));
	}

	private void assertBrandsConformity(List<Product> products, AvailableFilters availableFilters){
		List<Long> brandIds = products.stream().map(p -> p.getBrand().getId()).collect(Collectors.toList());
		assertTrue(equalsIgnoreOrderAndCount(brandIds, availableFilters.getBrand()));
	}

	private void assertProductConditionConformity(List<Product> products, AvailableFilters availableFilters){
		List<Long> conditionIds = products.stream().map(p -> p.getProductConditionId()).collect(Collectors.toList());
		assertTrue(equalsIgnoreOrderAndCount(conditionIds, availableFilters.getProductCondition()));
	}

	private void assertVintageConformity(List<Product> products, AvailableFilters availableFilters){
		List<Boolean> vintageValues = products.stream().map(p -> p.isVintage()).collect(Collectors.toList());
		assertSame(vintageValues.contains(Boolean.TRUE), availableFilters.isVintage());
	}

	private void assertIsOnSaleConformity(List<Product> products, AvailableFilters availableFilters){
		List<Boolean> onSaleValues = products.stream()
				.map(p -> p.getCurrentPrice() != null && p.getStartPrice() != null && p.getCurrentPrice().doubleValue() < p.getStartPrice().doubleValue()
						|| p.getCurrentPrice() != null && p.getRrpPrice() != null && p.getCurrentPrice().doubleValue() < p.getRrpPrice().doubleValue())
				.collect(Collectors.toList());
		assertSame(onSaleValues.contains(Boolean.TRUE), availableFilters.isOnSale());
	}

	private void assertIsStartPriceHigherConformity(List<Product> products, AvailableFilters availableFilters){
		List<Boolean> startPriceHigherValues = products.stream().map(p -> p.getCurrentPrice() != null && p.getStartPrice() != null && p.getCurrentPrice().doubleValue() < p.getStartPrice().doubleValue()).collect(Collectors.toList());
		assertSame(startPriceHigherValues.contains(Boolean.TRUE), availableFilters.isStartPriceHigher());
	}

	private void assertOurChoiceConformity(List<Product> products, AvailableFilters availableFilters){
		List<Boolean> ourChoiceValues = products.stream().map(p -> p.isOurChoice()).collect(Collectors.toList());
		assertSame(ourChoiceValues.contains(Boolean.TRUE), availableFilters.isHasOurChoice());
	}

	private void assertIsExclusiveSelectionConformity(List<Product> products, AvailableFilters availableFilters){
		List<Boolean> exckusiveSelectionValues = products.stream().map(p -> p.getExclusiveSelectionTime() != null).collect(Collectors.toList());
		assertSame(exckusiveSelectionValues.contains(Boolean.TRUE), availableFilters.isExclusiveSelection());
	}


	private void assertIsNewCollectionConformity(List<Product> products, AvailableFilters availableFilters){
		List<Boolean> isNewCollectionValues = products.stream().map(p -> p.isNewCollection()).collect(Collectors.toList());
		assertSame(isNewCollectionValues.contains(Boolean.TRUE), availableFilters.isNewCollection());
	}

	private void assertIsInStockConformity(List<Product> products, AvailableFilters availableFilters){

		// TODO: для корректного теста нужен product с тегом WAREHOUSE

		List<Boolean> isInStockValues = products.stream().map(p -> p.isInStock()).collect(Collectors.toList());
		assertSame(isInStockValues.contains(Boolean.TRUE), availableFilters.getIsInStock());
	}

	private void assertSellerTypesConformity(List<Product> products, AvailableFilters availableFilters){
		List<Long> sellerTypes = products.stream()
				.map(p -> p.getSeller().getSellerType())
				.filter(Objects::nonNull)
				.map(SellerTypeFilterProcessor::getFilterIdBySellerType)
				.collect(Collectors.toList());
		assertTrue(equalsIgnoreOrderAndCount(sellerTypes, availableFilters.getSellerTypes()));
	}

	private void assertStartPriceConformity(BigDecimal startPrice, AvailableFilters availableFilters){
		assertTrue(isSame(availableFilters.getStartPrice(), startPrice));
	}

	private void assertEndPriceConformity(BigDecimal endPrice, AvailableFilters availableFilters){
		assertTrue(isSame(endPrice, availableFilters.getEndPrice()));
	}

	private boolean isSame(BigDecimal a, BigDecimal b){
		if(a == null || b == null) return a == b;
		return a.longValue() == b.longValue();
	}

	//a > b and 34.5 > null
	private boolean isGreaterOrSame(BigDecimal a, BigDecimal b){
		if(a == null) a = BigDecimal.ZERO;
		if(b == null) b = BigDecimal.ZERO;
		return a.compareTo(b) >= 0;
	}

	private BigDecimal getStartPrice(List<Product> products){
		return products.stream().map(p -> p.getCurrentPrice()).filter(v -> v != null).min(BigDecimal::compareTo).orElse(null);
	}

	private BigDecimal getEndPrice(List<Product> products){
		return products.stream().map(p -> p.getCurrentPrice()).filter(v -> v != null).max(BigDecimal::compareTo).orElse(null);
	}

	private void assertAttributeConformity(List<Product> products, AvailableFilters availableFilters, Attribute attribute){
		List<Long> possibleValues = attributeListMap.get(attribute).stream().map(av -> av.getId()).collect(Collectors.toList());
		List<Long> attributeValuesFromProducts = new ArrayList<>();
		List<Long> attributeValuesFromAvailableFilters = new ArrayList<>();
		for(Product product : products){
			for(ProductAttributeValueBinding pavb : product.getAttributeValues()){
				if(possibleValues.contains(pavb.getAttributeValue().getId())){
					attributeValuesFromProducts.add(pavb.getAttributeValue().getId());
				}
			}
		}
		for(Long atributeValueId : availableFilters.getFilter()){
			if(possibleValues.contains(atributeValueId)){
				attributeValuesFromAvailableFilters.add(atributeValueId);
			}
		}
		assertTrue(equalsIgnoreOrderAndCount(attributeValuesFromProducts, attributeValuesFromAvailableFilters));
	}

    private List<Product> getProducts(ProductService.FilterSpecification spec){
		int pageLength = 100;
		ProductService.ViewQualification vq = new ProductService.ViewQualification().pageLength(pageLength);
		Page<Product> productPage = productService.getRawProducts(spec, 1, null, vq, ProductService.UserType.HUMAN);
		List<Product> result = new ArrayList<>(productPage.getItems());
		//Если страниц больше 1-й, то вытягиваем остальные страницы
		if(productPage.getTotalPages() > 1){
			for(int i = 2; i <= productPage.getTotalPages(); i++){
				result.addAll(productService.getRawProducts(spec, i, null, vq, ProductService.UserType.HUMAN).getItems());
			}
		}
	    return result;
    }

	private boolean equalsIgnoreOrderAndCount(List a, List b){
		return new HashSet(a).equals(new HashSet(b));
	}

}
