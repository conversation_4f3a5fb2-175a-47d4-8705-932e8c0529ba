package ru.oskelly.tests.build.domain.model.addressendpoint;

import org.junit.jupiter.api.Test;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.model.address.Address;
import su.reddot.domain.model.addressendpoint.AddressEndpoint;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class AddressEndpointTest {

    @Test
    public void isComplete_returns_true(){
    	Address address = new Address().setAddress("some address");
    	AddressEndpoint addressEndpoint = new AddressEndpoint().setAddress(address);
	    addressEndpoint.setPhone("+79202341740").setFirstName("Иван");
	    assertTrue(addressEndpoint.isComplete());
	    addressEndpoint.setLastName("Белых").setFirstName(null);
	    assertTrue(addressEndpoint.isComplete());
	    addressEndpoint.setLastName(null).setPatronymicName("Сергеевич");
	    assertTrue(addressEndpoint.isComplete());
    }

	@Test
	public void isComplete_returns_false(){
		AddressEndpoint addressEndpoint = new AddressEndpoint();
		assertFalse(addressEndpoint.isComplete());
		addressEndpoint.setPhone("+79202341740");
		assertFalse(addressEndpoint.isComplete());
		Address address = new Address().setAddress("some address");
		addressEndpoint.setAddress(address);
		assertFalse(addressEndpoint.isComplete());
		addressEndpoint.setPhone(null).setFirstName("Иван");
		assertFalse(addressEndpoint.isComplete());
	}

}
