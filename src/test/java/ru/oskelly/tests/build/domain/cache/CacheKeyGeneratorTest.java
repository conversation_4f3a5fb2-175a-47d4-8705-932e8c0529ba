package ru.oskelly.tests.build.domain.cache;

import org.junit.jupiter.api.Test;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.product.ProductService;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class CacheKeyGeneratorTest {

	private Long[] productIds = {8L, 2L};
	private Long[] cateloryIds = {5L, 9L};
	private Long[] sizeIds = {9L, 93L};
	private ProductState state = ProductState.PUBLISHED;
	private ProductState[] states = {ProductState.PUBLISHED, ProductState.HIDDEN};
	private User.Sex sex = User.Sex.FEMALE;
	private boolean isVintage = true;

    @Test
    public void testFilterSpecificationCacheKey(){
		ProductService.FilterSpecification spec1 = createFilterSpecification();
		ProductService.FilterSpecification spec2 = createFilterSpecification();
		ProductService.FilterSpecification spec3 = spec1.clone();
		assertFilterSpecificationEquals(spec1, spec2);
		assertFilterSpecificationEquals(spec1, spec3);
		assertFilterSpecificationEquals(spec2, spec1);
		assertFilterSpecificationEquals(spec2, spec3);
    }

    private void assertFilterSpecificationEquals(ProductService.FilterSpecification spec1, ProductService.FilterSpecification spec2){
		assertEquals(spec1, spec2);
		assertEquals(spec1.toString(), spec2.toString());
		assertEquals(spec1.hashCode(), spec2.hashCode());
	}

    private ProductService.FilterSpecification createFilterSpecification(){
		return new ProductService.FilterSpecification()
				.productsIds(Arrays.asList(productIds))
				.categoriesIds(Arrays.asList(cateloryIds))
				.interestingSizes(Arrays.asList(sizeIds))
				.state(state)
				.states(Arrays.asList(states))
				.sex(sex)
				.isVintage(isVintage);
	}



}
