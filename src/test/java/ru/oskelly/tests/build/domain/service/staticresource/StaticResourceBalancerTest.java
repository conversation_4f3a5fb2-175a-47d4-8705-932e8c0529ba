package ru.oskelly.tests.build.domain.service.staticresource;

import org.junit.jupiter.api.Test;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.service.staticresource.StaticResourceBalancer;

import static org.junit.jupiter.api.Assertions.assertEquals;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class StaticResourceBalancerTest {

	private static String host = "http://static.oskelly.ru";

	@Test
	public void transform(){
		StaticResourceBalancer staticResourceBalancer = new StaticResourceBalancer();
		staticResourceBalancer.setPrefix(host);

		String url = "/abc/def";
		String transformed = staticResourceBalancer.transform(url);

		assertEquals(host + url, transformed);
	}

}
