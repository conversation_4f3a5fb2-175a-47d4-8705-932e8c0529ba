package ru.oskelly.tests.build.domain.service.product;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.google.common.collect.ImmutableList;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import ru.oskelly.tests.AbstractSpringTest;
import su.reddot.domain.model.size.SizeType;
import su.reddot.domain.service.catalog.AvailableFilters;
import su.reddot.domain.service.dto.CurrencyDTO;
import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.domain.service.dto.ProductModelDTO;
import su.reddot.domain.service.dto.experiments.ExperimentDTO;
import su.reddot.domain.service.experiments.ExperimentsService;
import su.reddot.domain.service.filter.ProductFiltrationEngine;
import su.reddot.domain.service.product.AvailableProductFilterTypes;
import su.reddot.domain.service.product.DefaultProductService;
import su.reddot.domain.service.product.ProductInfoRequest;
import su.reddot.domain.service.setting.FeatureFlagsSettingService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.configparam.dto.SlotPatternDTO;
import su.reddot.infrastructure.security.SecurityService;
import su.reddot.infrastructure.util.CallInTransaction;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static su.reddot.domain.service.experiments.ExperimentsService.FlagrKey.EXTERNAL_CATALOG;
import static su.reddot.domain.service.filter.ProductFiltrationEngine.CountProductsOptions;
import static su.reddot.domain.service.filter.ProductFiltrationEngine.GetAvailableFiltersOptions;
import static su.reddot.domain.service.filter.ProductFiltrationEngine.GetProductsOptions;
import static su.reddot.domain.service.filter.ProductFiltrationEngine.ProductFiltrationPageRequest;
import static su.reddot.domain.service.product.AvailableProductFilterTypes.CONDITIONS;
import static su.reddot.domain.service.product.ProductService.FilterSpecification;
import static su.reddot.domain.service.product.ProductService.SortAttribute;
import static su.reddot.domain.service.product.ProductService.SortAttribute.DISCOUNT;
import static su.reddot.domain.service.product.ProductService.SortAttribute.DISCOUNT_DESC;
import static su.reddot.domain.service.product.ProductService.SortAttribute.PROMOTION_TIME_DESC;
import static su.reddot.domain.service.product.ProductService.UserType.HUMAN;
import static su.reddot.domain.service.product.ProductService.ViewQualification;
import static su.reddot.domain.service.setting.FeatureFlagsSettingService.ConfigFlag.HIDE_BRANDS_AND_SELLERS;

@Slf4j
public class ProductFiltrationEngineTest extends AbstractSpringTest {

    @Autowired
    private DefaultProductService defaultProductService;

    @Autowired
    private ProductFiltrationEngine productFiltrationEngine;

    @MockBean
    private SecurityService securityService;

    @Autowired
    private CallInTransaction callInTransaction;

    @MockBean
    private ExperimentsService experimentsService;

    @MockBean
    private FeatureFlagsSettingService featureFlagsSettingService;

    @Autowired
    private UserService userService;

    private final ObjectMapper objectMapper = new ObjectMapper().enable(SerializationFeature.INDENT_OUTPUT);

    private final List<Method> methods =
            Arrays.stream(AvailableFilters.class.getDeclaredMethods())
                    .filter(m -> !m.getName().equals("getCurrencies") && (m.getName().startsWith("get") || m.getName().startsWith("is")))
                    .collect(Collectors.toList());

    @Test
    @Disabled("Отладочный тест для запуска вручную")
    public void testGetItemsOldNew() {

        // сравниваем выдачу старого и нового методов фильтрации

        AvailableFilters availableFilters = defaultProductService.getAvailableFilters(new FilterSpecification(),
                HUMAN);

        for (int i = 0; i < 100; i++) {

            log.info("Iteration {}", (i + 1));

            String currencyCode = nextCurrencyCode("RUB", "EUR");
            int pageSize = nextPageLength(10);
            Long forAuthorizedUserId = nextForAuthorizedUser();
            boolean forAdmin = forAuthorizedUserId != null && RandomUtils.nextBoolean();
            boolean includeSoldProducts = RandomUtils.nextBoolean();
            boolean fillSizes = RandomUtils.nextBoolean();
            SortAttribute sortAttribute = nextSortAttribute();
            SizeType sizeType = nextSizeType();
            FilterSpecification spec = nextFilterSpecification(availableFilters, currencyCode);
            log.info("currencyCode: {}", currencyCode);
            log.info("pageSize: {}", pageSize);
            log.info("forAuthorizedUser: {}", forAuthorizedUserId);
            log.info("forAdmin: {}", forAdmin);
            log.info("includeSoldProducts: {}", includeSoldProducts);
            log.info("fillSizes: {}", fillSizes);
            log.info("sortAttribute: {}", sortAttribute);
            log.info("sizeType: {}", sizeType);
            log.info("spec: {}", spec);

            List<Page<ProductDTO>> oldPages = getPageByOldMethod(pageSize, sortAttribute, currencyCode, spec,
                    forAuthorizedUserId, forAdmin, includeSoldProducts, fillSizes, sizeType, null);
            log.info("oldPages: {} pages, {} items {}",
                    oldPages.size(),
                    oldPages.stream().flatMap(p -> p.getItems().stream()).count(),
                    oldPages.stream().flatMap(p -> p.getItems().stream()).map(ProductDTO::getProductId).map(Object::toString).collect(
                            Collectors.joining(", ")));

            when(experimentsService.getExperimentsForAllUserDevices(any(), any()))
                    .thenReturn(ImmutableList.of());
            when(experimentsService.getExperimentsForAllGuestTokenDevices(any(), any()))
                    .thenReturn(ImmutableList.of());

            List<Page<ProductDTO>> newPages = getPageByNewMethod(pageSize, sortAttribute, currencyCode, spec,
                    forAuthorizedUserId, forAdmin, includeSoldProducts, fillSizes, sizeType, null);
            log.info("newPages: {} pages, {} items {}",
                    newPages.size(),
                    newPages.stream().flatMap(p -> p.getItems().stream()).count(),
                    newPages.stream().flatMap(p -> p.getItems().stream()).map(ProductDTO::getProductId).map(Object::toString).collect(
                            Collectors.joining(", ")));

            comparePages(oldPages, newPages);
        }
    }

    @Test
    @Disabled("Отладочный тест для запуска вручную")
    public void testGetItemsOldNewExternal() {

        // сравниваем выдачу старого и нового с внешним источником методов фильтрации

        AvailableFilters availableFilters = defaultProductService.getAvailableFilters(new FilterSpecification(),
                HUMAN);

        long oldMs = 0;
        long newMs = 0;

        for (int i = 0; i < 100; i++) {

            log.info("Iteration {}", (i + 1));

            String currencyCode = nextCurrencyCode("RUB", "EUR");
            int pageSize = nextPageLength(10);
            Long forAuthorizedUserId = nextForAuthorizedUser();
            boolean forAdmin = forAuthorizedUserId != null && RandomUtils.nextBoolean();
            boolean includeSoldProducts = RandomUtils.nextBoolean();
            boolean fillSizes = RandomUtils.nextBoolean();
            SortAttribute sortAttribute = nextSortAttribute();
            SizeType sizeType = nextSizeType();
            FilterSpecification spec = nextFilterSpecification(availableFilters, currencyCode);
            boolean hideBrandsAndSellersFeatureEnabled = RandomUtils.nextBoolean();
//            String currencyCode = "EUR";
//            int pageSize = 4;
//            boolean forAdmin = false;
//            boolean includeSoldProducts = true;
//            boolean fillSizes = false;
//            SortAttribute sortAttribute = SortAttribute.ID;
//            SizeType sizeType = SizeType.DE;
//            FilterSpecification spec = new FilterSpecification().startPrice(BigDecimal.valueOf(840D)).currencyCode("EUR").offlineOnly(false);
            log.info("currencyCode: {}", currencyCode);
            log.info("pageSize: {}", pageSize);
            log.info("forAuthorizedUser: {}", forAuthorizedUserId);
            log.info("forAdmin: {}", forAdmin);
            log.info("includeSoldProducts: {}", includeSoldProducts);
            log.info("fillSizes: {}", fillSizes);
            log.info("sortAttribute: {}", sortAttribute);
            log.info("sizeType: {}", sizeType);
            log.info("spec: {}", spec);
            log.info("hideBrandsAndSellersFeatureEnabled: {}", hideBrandsAndSellersFeatureEnabled);

            if (hideBrandsAndSellersFeatureEnabled) {
                when(featureFlagsSettingService.isEnableForUserWithPercentCached(any(), eq(HIDE_BRANDS_AND_SELLERS))).thenReturn(true);
                when(featureFlagsSettingService.isEnableForUserWithPercent(eq(HIDE_BRANDS_AND_SELLERS))).thenReturn(true);
            } else {
                when(featureFlagsSettingService.isEnableForUserWithPercentCached(any(), eq(HIDE_BRANDS_AND_SELLERS))).thenReturn(false);
                when(featureFlagsSettingService.isEnableForUserWithPercent(eq(HIDE_BRANDS_AND_SELLERS))).thenReturn(false);
            }

            long start = System.currentTimeMillis();
            List<Page<ProductDTO>> oldPages = getPageByOldMethod(pageSize, sortAttribute, currencyCode, spec,
                    forAuthorizedUserId, forAdmin, includeSoldProducts, fillSizes, sizeType, null);
            oldMs += System.currentTimeMillis() - start;
            log.info("oldPages: {} pages, {} items {}",
                    oldPages.size(),
                    oldPages.stream().flatMap(p -> p.getItems().stream()).count(),
                    oldPages.stream().flatMap(p -> p.getItems().stream()).map(ProductDTO::getProductId).map(Object::toString).collect(
                            Collectors.joining(", ")));

            when(experimentsService.getExperimentsForAllUserDevices(any(), any()))
                    .thenReturn(ImmutableList.of(
                            new ExperimentDTO()
                                    .setKey(EXTERNAL_CATALOG.name())
                                    .setValueKey(EXTERNAL_CATALOG.getValueType().name())));
            when(experimentsService.getExperimentsForAllGuestTokenDevices(any(), any()))
                    .thenReturn(ImmutableList.of(
                            new ExperimentDTO()
                                    .setKey(EXTERNAL_CATALOG.name())
                                    .setValueKey(EXTERNAL_CATALOG.getValueType().name())));

            start = System.currentTimeMillis();
            List<Page<ProductDTO>> newPages = getPageByNewMethod(pageSize, sortAttribute, currencyCode, spec,
                    forAuthorizedUserId, forAdmin, includeSoldProducts, fillSizes, sizeType, null);
            newMs += System.currentTimeMillis() - start;
            log.info("newPages: {} pages, {} items {}",
                    newPages.size(),
                    newPages.stream().flatMap(p -> p.getItems().stream()).count(),
                    newPages.stream().flatMap(p -> p.getItems().stream()).map(ProductDTO::getProductId).map(Object::toString).collect(
                            Collectors.joining(", ")));

            log.info("iterationsCount: {}, oldMs = {}, newMs = {}, old avg = {}, new avg = {}",
                    i + 1,
                    oldMs,
                    newMs,
                    ((double) oldMs) / (i + 1),
                    ((double) newMs) / (i + 1));

            comparePages(oldPages, newPages);
        }
    }

    @Test
    @Disabled("Отладочный тест для запуска вручную")
    public void testGetItemsWithSlotsOldNewExternal() {

        // сравниваем выдачу со слотами старого и нового с внешним источником методов фильтрации

        AvailableFilters availableFilters = defaultProductService.getAvailableFilters(new FilterSpecification(),
                HUMAN);

        long oldMs = 0;
        long newMs = 0;

        for (int i = 0; i < 100; i++) {

            log.info("Iteration {}", (i + 1));

            String currencyCode = nextCurrencyCode("RUB", "EUR");
            int pageSize = nextPageLength(10);
            Long forAuthorizedUserId = nextForAuthorizedUser();
            boolean forAdmin = forAuthorizedUserId != null && RandomUtils.nextBoolean();
            boolean includeSoldProducts = RandomUtils.nextBoolean();
            boolean fillSizes = RandomUtils.nextBoolean();
            SortAttribute sortAttribute = nextSortAttribute();
            SizeType sizeType = nextSizeType();
            FilterSpecification spec = nextFilterSpecification(availableFilters, currencyCode);
            boolean hideBrandsAndSellersFeatureEnabled = RandomUtils.nextBoolean();
            SlotPatternDTO slotPattern = nextSlotPattern();

            /////////////
//            String currencyCode = "EUR";
//            int pageSize = 6;
//            Long forAuthorizedUserId = null;
//            boolean forAdmin = false;
//            boolean includeSoldProducts = false;
//            boolean fillSizes = false;
//            SortAttribute sortAttribute = SortAttribute.CHANGE_TIME;
//            SizeType sizeType = SizeType.COLLAR_INCHES;
//            FilterSpecification spec = new FilterSpecification()
//                    .currencyCode(currencyCode)
//                    .interestingSizes(ImmutableList.of(287L, 203L, 208L, 322L));
//            boolean hideBrandsAndSellersFeatureEnabled = false;
//            SlotPatternDTO slotPattern = nextSlotPattern();
            /////////////

            log.info("currencyCode: {}", currencyCode);
            log.info("pageSize: {}", pageSize);
            log.info("forAuthorizedUser: {}", forAuthorizedUserId);
            log.info("forAdmin: {}", forAdmin);
            log.info("includeSoldProducts: {}", includeSoldProducts);
            log.info("fillSizes: {}", fillSizes);
            log.info("sortAttribute: {}", sortAttribute);
            log.info("sizeType: {}", sizeType);
            log.info("spec: {}", spec);
            log.info("hideBrandsAndSellersFeatureEnabled: {}", hideBrandsAndSellersFeatureEnabled);
            log.info("slotPattern: {}", slotPattern);

            if (hideBrandsAndSellersFeatureEnabled) {
                when(featureFlagsSettingService.isEnableForUserWithPercentCached(any(), eq(HIDE_BRANDS_AND_SELLERS))).thenReturn(true);
                when(featureFlagsSettingService.isEnableForUserWithPercent(eq(HIDE_BRANDS_AND_SELLERS))).thenReturn(true);
            } else {
                when(featureFlagsSettingService.isEnableForUserWithPercentCached(any(), eq(HIDE_BRANDS_AND_SELLERS))).thenReturn(false);
                when(featureFlagsSettingService.isEnableForUserWithPercent(eq(HIDE_BRANDS_AND_SELLERS))).thenReturn(false);
            }

            long start = System.currentTimeMillis();
            List<Page<ProductDTO>> oldPages = getPageByOldMethod(pageSize, sortAttribute, currencyCode, spec,
                    forAuthorizedUserId, forAdmin, includeSoldProducts, fillSizes, sizeType, slotPattern);
            oldMs += System.currentTimeMillis() - start;
            log.info("oldPages: {} pages, {} items {}",
                    oldPages.size(),
                    oldPages.stream().flatMap(p -> p.getItems().stream()).count(),
                    oldPages.stream().flatMap(p -> p.getItems().stream()).map(ProductDTO::getProductId).map(Object::toString).collect(
                            Collectors.joining(", ")));

            when(experimentsService.getExperimentsForAllUserDevices(any(), any()))
                    .thenReturn(ImmutableList.of(
                            new ExperimentDTO()
                                    .setKey(EXTERNAL_CATALOG.name())
                                    .setValueKey(EXTERNAL_CATALOG.getValueType().name())));
            when(experimentsService.getExperimentsForAllGuestTokenDevices(any(), any()))
                    .thenReturn(ImmutableList.of(
                            new ExperimentDTO()
                                    .setKey(EXTERNAL_CATALOG.name())
                                    .setValueKey(EXTERNAL_CATALOG.getValueType().name())));

            start = System.currentTimeMillis();
            List<Page<ProductDTO>> newPages = getPageByNewMethod(pageSize, sortAttribute, currencyCode, spec,
                    forAuthorizedUserId, forAdmin, includeSoldProducts, fillSizes, sizeType, slotPattern);
            newMs += System.currentTimeMillis() - start;
            log.info("newPages: {} pages, {} items {}",
                    newPages.size(),
                    newPages.stream().flatMap(p -> p.getItems().stream()).count(),
                    newPages.stream().flatMap(p -> p.getItems().stream()).map(ProductDTO::getProductId).map(Object::toString).collect(
                            Collectors.joining(", ")));

            log.info("iterationsCount: {}, oldMs = {}, newMs = {}, old avg = {}, new avg = {}",
                    i + 1,
                    oldMs,
                    newMs,
                    ((double) oldMs) / (i + 1),
                    ((double) newMs) / (i + 1));

            comparePages(oldPages, newPages);
        }
    }

    private SlotPatternDTO nextSlotPattern() {
        return new SlotPatternDTO(ImmutableList.of(1, 3, 7, 10), 10);
    }

    @SneakyThrows
    private void comparePages(List<Page<ProductDTO>> oldPages, List<Page<ProductDTO>> newPages) {

        assertEquals(oldPages.size(), newPages.size());

        int i = 1;

        for (Iterator<Page<ProductDTO>> oldPageIt = oldPages.iterator(), newPageIt = newPages.iterator(); oldPageIt.hasNext() && newPageIt.hasNext(); ) {

            Page<ProductDTO> oldPage = oldPageIt.next();
            Page<ProductDTO> newPage = newPageIt.next();

            assertEquals(oldPage.getTotalPages(), newPage.getTotalPages());
            assertEquals(oldPage.getTotalAmount(), newPage.getTotalAmount());
            assertEquals(oldPage.getItems().size(), newPage.getItems().size());

            for (Iterator<ProductDTO> oldIt = oldPage.getItems().iterator(), newIt = newPage.getItems()
                    .iterator(); oldIt.hasNext() && newIt.hasNext(); ) {

                String oldProductJson = objectMapper.writeValueAsString(oldIt.next());
                String newProductJson = objectMapper.writeValueAsString(newIt.next());

//                log.info("{}. oldProductJson: {}", i, oldProductJson);
//                log.info("{}. newProductJson: {}", i, newProductJson);

                assertEquals(oldProductJson, newProductJson);

                i++;
            }
        }
    }

    private List<Page<ProductDTO>> getPageByOldMethod(
            int pageSize,
            SortAttribute sortAttribute,
            String currencyCode,
            FilterSpecification spec,
            Long forAuthorizedUserId,
            boolean forAdmin,
            boolean includeSoldProducts,
            boolean fillSizes,
            SizeType sizeType,
            SlotPatternDTO slotPatternDTO
    ) {
        List<Page<ProductDTO>> result = new ArrayList<>();

        Integer pageNumber = 1;
        while (pageNumber != null) {

            ProductInfoRequest productInfoRequest = new ProductInfoRequest()
                    .setViewQualification(
                            new ViewQualification()
                                    .pageLength(pageSize)
                                    .withSizeChart(fillSizes)
                                    .interestingSizeType(sizeType))
                    .setCurrencyCode(currencyCode)
                    .setFilterSpecification(spec)
                    .setIncludeSoldProducts(includeSoldProducts);

            final Integer pageNumberToUse = pageNumber;
            Page<ProductDTO> page = slotPatternDTO != null ?
                    callInTransaction.runInAnyTransaction(
                            () -> {
                                // Если нужен отбор для авторизованного пользователя, мокаем вовзрат пользователя/true, если не нужен мокаем возврат null/false
                                // получаем пользователя в рамках транзакции во избежание проблем с LIE
                                if (forAuthorizedUserId != null) {
                                    when(securityService.getCurrentAuthorizedUser()).thenReturn(userService.getUserOrThrowUserNotFoundException(forAuthorizedUserId));
                                    when(securityService.getCurrentAuthorizedUserId()).thenReturn(forAuthorizedUserId);
                                    when(securityService.isAuthorized()).thenReturn(true);
                                } else {
                                    when(securityService.getCurrentAuthorizedUser()).thenReturn(null);
                                    when(securityService.getCurrentAuthorizedUserId()).thenReturn(null);
                                    when(securityService.isAuthorized()).thenReturn(false);
                                }
                                productInfoRequest.setForAdmin(forAdmin && forAuthorizedUserId != null);

                                return defaultProductService.getProductDTOPageWithSlotsCached(productInfoRequest, pageNumberToUse, sortAttribute, slotPatternDTO, HUMAN);
                            }) :
                    callInTransaction.runInAnyTransaction(
                            () -> {
                                // Если нужен отбор для авторизованного пользователя, мокаем вовзрат пользователя/true, если не нужен мокаем возврат null/false
                                // получаем пользователя в рамках транзакции во избежание проблем с LIE
                                if (forAuthorizedUserId != null) {
                                    when(securityService.getCurrentAuthorizedUser()).thenReturn(userService.getUserOrThrowUserNotFoundException(forAuthorizedUserId));
                                    when(securityService.getCurrentAuthorizedUserId()).thenReturn(forAuthorizedUserId);
                                    when(securityService.isAuthorized()).thenReturn(true);
                                } else {
                                    when(securityService.getCurrentAuthorizedUser()).thenReturn(null);
                                    when(securityService.getCurrentAuthorizedUserId()).thenReturn(null);
                                    when(securityService.isAuthorized()).thenReturn(false);
                                }
                                productInfoRequest.setForAdmin(forAdmin && forAuthorizedUserId != null);

                                return defaultProductService.getProductDTOPageCached(productInfoRequest, pageNumberToUse, sortAttribute, HUMAN);
                            });
            result.add(page);
            if (pageNumber < 3 && pageNumber < page.getTotalPages()) {
                pageNumber++;
            } else {
                pageNumber = null;
            }
        }

        return result;
    }

    private List<Page<ProductDTO>> getPageByNewMethod(
            int pageSize,
            SortAttribute sortAttribute,
            String currencyCode,
            FilterSpecification spec,
            Long forAuthorizedUserId,
            boolean forAdmin,
            boolean includeSoldProducts,
            boolean fillSizes,
            SizeType sizeType,
            SlotPatternDTO slotPatternDTO
    ) {
        List<Page<ProductDTO>> result = new ArrayList<>();

        Integer pageNumber = 1;
        while (pageNumber != null) {

            GetProductsOptions options = new GetProductsOptions()
                    .setFillSizes(fillSizes)
                    .setFillProductResponse(false)
                    .setProductResponseId(null)
                    .setFillProductResponseCount(false)
                    .setSizeType(sizeType)
                    .setWithSlots(slotPatternDTO != null)
                    .setSlotPatternDTO(slotPatternDTO)
                    .setCurrencyCode(currencyCode)
                    .setIncludeSoldProducts(includeSoldProducts);
            options
                    .setForUserType(HUMAN)
                    .setForAuthorizedUserTokenToUser(new ArrayList<>());

            final Integer pageNumberToUse = pageNumber;
            Page<ProductDTO> page = callInTransaction.runInAnyTransaction(
                            () -> {
                                // для обнаружения проблем всегда мокаем возврат null/false, т.к. в новом поиске не должно быть зависимости от пользователя в контексте
                                // получаем пользователя в рамках транзакции во избежание проблем с LIE
                                when(securityService.getCurrentAuthorizedUser()).thenReturn(null);
                                when(securityService.getCurrentAuthorizedUserId()).thenReturn(null);
                                when(securityService.isAuthorized()).thenReturn(false);

                                options
                                        .setForAuthorizedUser(forAuthorizedUserId != null ? userService.getUserOrThrowUserNotFoundException(forAuthorizedUserId) : null)
                                        .setForGuestToken(ImmutableList.of("token"))
                                        .setForAdmin(forAdmin && forAuthorizedUserId != null);

                                return productFiltrationEngine.getProductPage(spec,
                                        new ProductFiltrationPageRequest(pageNumberToUse, pageSize, sortAttribute), options);
                            });

            result.add(page);
            if (pageNumber < 3 && pageNumber < page.getTotalPages()) {
                pageNumber++;
            } else {
                pageNumber = null;
            }
        }

        return result;
    }

    private Long nextForAuthorizedUser() {
        return RandomUtils.nextBoolean() ? 50918L : null;
    }

    private SortAttribute nextSortAttribute() {
        List<SortAttribute> filtered = Arrays.stream(SortAttribute.values())
                .filter(s -> s != PROMOTION_TIME_DESC) // TODO: после реализации сортировки добавить
                .filter(s -> s != DISCOUNT)
                .filter(s -> s != DISCOUNT_DESC)
                .collect(Collectors.toList());
        return filtered.get(RandomUtils.nextInt(0, filtered.size()));
    }

    private int nextPageLength(int maxPageLength) {
        return RandomUtils.nextInt(1, maxPageLength);
    }

    private SizeType nextSizeType() {
        return SizeType.values()[RandomUtils.nextInt(0, SizeType.values().length)];
    }

    private String nextCurrencyCode(String... currencies) {
        return currencies[RandomUtils.nextInt(0, currencies.length)];
    }

    private FilterSpecification nextFilterSpecification(AvailableFilters availableFilters, String currencyCode) {

        FilterSpecification spec = new FilterSpecification();

        spec.currencyCode(currencyCode);

        ImmutablePair<Method, Object> pair = nextNotEmptyResultMethod(availableFilters);

        log.info("Next not null method: {}, value: {}", pair.getLeft().getName(), pair.getRight());

        Method method = pair.getLeft();
        Object res = pair.getRight();

        if (method.getName().equals("getFilter")) {
            spec.interestingAttributeValues(getNextNLongValues((List<Long>) res));
        }
        if (method.getName().equals("getCategory")) {
            spec.categoriesIds(getNextNLongValues((List<Long>) res));
        }
        if (method.getName().equals("getSize")) {
            spec.interestingSizes(getNextNLongValues((List<Long>) res));
        }
        if (method.getName().equals("getBrand")) {
            spec.interestingBrands(getNextNLongValues((List<Long>) res));
        }
        if (method.getName().equals("getProductModel")) {
            spec.interestingProductModels(getNextNProductModelValues((List<ProductModelDTO>) res));
        }
        if (method.getName().equals("getProductCondition")) {
            spec.interestingConditions(getNextNLongValues((List<Long>) res));
        }
        if (method.getName().equals("isVintage")) {
            spec.isVintage(true);
        }
        if (method.getName().equals("isBrandNew")) {
            spec.isBrandNew(true);
        }
        if (method.getName().equals("isOnSale")) {
            spec.isOnSale(true);
        }
        if (method.getName().equals("isStartPriceHigher")) {
            spec.isStartPriceHigher(true);
        }
        if (method.getName().equals("isHasOurChoice")) {
            spec.hasOurChoice(true);
        }
        if (method.getName().equals("isNewCollection")) {
            spec.isNewCollection(true);
        }
        if (method.getName().equals("isExclusiveSelection")) {
            spec.isExclusiveSelection(true);
        }
        if (method.getName().equals("isInStock") || method.getName().equals("getIsInStock")) {
            spec.isInStock(true);
        }
        if (method.getName().equals("isAtOffice") || method.getName().equals("getIsAtOffice")) {
            spec.isAtOffice(true);
        }
        if (method.getName().equals("isInBoutique") || method.getName().equals("getIsInBoutique")) {
            spec.isInBoutique(true);
        }
        if (method.getName().equals("getBoutiqueLocationTags")) {
            spec.boutiqueLocationTags(getNextNLongValues((List<Long>) res));
        }
        if (method.getName().equals("getStartPrice")) {
            spec.startPrice((BigDecimal) res);
        }
        if (method.getName().equals("getEndPrice")) {
            spec.endPrice((BigDecimal) res);
        }
        if (method.getName().equals("isPro")) {
            spec.isPro(true);
        }
        if (method.getName().equals("isNotPro")) {
            spec.isPro(false);
        }
        if (method.getName().equals("getLocationTags")) {
            spec.locationTags(getNextNLongValues((List<Long>) res));
        }

        return spec;
    }

    private Collection<Long> getNextNLongValues(List<Long> res) {
        List<Long> resToUse = new ArrayList<>(res);
        Collections.shuffle(resToUse);
        return resToUse.stream()
                .limit(RandomUtils.nextInt(2,6))
                .collect(Collectors.toList());
    }

    private Collection<Long> getNextNProductModelValues(List<ProductModelDTO> res) {
        List<ProductModelDTO> resToUse = new ArrayList<>(res);
        Collections.shuffle(resToUse);
        return resToUse.stream()
                .limit(RandomUtils.nextInt(2,6))
                .map(ProductModelDTO::getId)
                .collect(Collectors.toList());
    }

    @SneakyThrows
    private ImmutablePair<Method, Object> nextNotEmptyResultMethod(AvailableFilters availableFilters) {

        while (true) {
            Method method = methods.get(RandomUtils.nextInt(0, methods.size()));
            Object res = method.invoke(availableFilters);
//            if (method.getName().equals("getProductCondition")) return new ImmutablePair<>(method, ImmutableList.of(1L,2L,3L));
            if (res instanceof List) {
                if (!((List) res).isEmpty()) {
                    return new ImmutablePair<>(method, res);
                }
            } else if (res instanceof Boolean) {
                if (((Boolean) res)) {
                    return new ImmutablePair<>(method, true);
                }
            } else if (res instanceof BigDecimal) {
                return new ImmutablePair<>(method, res);
            }
        }
    }

    @Test
    @Disabled("Отладочный тест для запуска вручную")
    public void testGetAvailableFiltersOldNewExternal() {

        // сравниваем выдачу старого и нового с внешним источником методов фильтрации

        AvailableFilters availableFilters = defaultProductService.getAvailableFilters(new FilterSpecification(),
                HUMAN);

        long oldMs = 0;
        long newMs = 0;

        for (int i = 0; i < 100; i++) {

            log.info("Iteration {}", (i + 1));

            String currencyCode = nextCurrencyCode("RUB", "EUR");
            FilterSpecification spec = nextFilterSpecification(availableFilters, currencyCode);
            Set<AvailableProductFilterTypes> requiredFilterTypes = nextRequiredFilterTypes();
//            spec = new FilterSpecification();
//            spec.currencyCode("EUR");
//            spec.isNewCollection(true);
//            spec.isBrandNew(true);
//            spec.endPrice(BigDecimal.valueOf(18000000.000000000000));
//            spec.interestingAttributeValues(ImmutableList.of(295L, 56L, 323L, 322L));
//            spec.interestingBrands(ImmutableList.of(2413L, 985L, 2460L, 303L));
//            requiredFilterTypes.clear();
//            requiredFilterTypes.add(ATTRIBUTES);
//            requiredFilterTypes.add(LOCATION_TAGS);
//            requiredFilterTypes.add(CONDITIONS);
//            requiredFilterTypes.add(START_PRICE);
//            requiredFilterTypes.add(END_PRICE);
//            requiredFilterTypes.add(NEW_COLLECTION);
//            requiredFilterTypes.add(OUR_CHOICE);
//            requiredFilterTypes.add(PRO);
//            requiredFilterTypes.add(SIZES);
//            requiredFilterTypes.add(CATEGORIES);
//            requiredFilterTypes.add(BOUTIQUE_LOCATION_TAGS);
//            requiredFilterTypes.add(IN_BOUTIQUE);
//            requiredFilterTypes.add(STREETWEAR);
//            requiredFilterTypes.add(MODELS);
//            requiredFilterTypes.add(START_PRICE_HIGHER);
//            requiredFilterTypes.add(VINTAGE);
//            requiredFilterTypes.add(BRANDS);
//            requiredFilterTypes.add(BRAND_NEW);
//            requiredFilterTypes.add(ON_SALE);

            log.info("spec: {}", spec);
            log.info("requiredFilterTypes: {}", requiredFilterTypes);

            long start = System.currentTimeMillis();
            AvailableFilters oldFilters = getAvailableFiltersByOldMethod(spec, requiredFilterTypes);
            oldMs += System.currentTimeMillis() - start;
            log.info("oldFilters: {}", oldFilters);

            when(experimentsService.getExperimentsForAllUserDevices(any(), any()))
                    .thenReturn(ImmutableList.of(
                            new ExperimentDTO()
                                    .setKey(EXTERNAL_CATALOG.name())
                                    .setValueKey(EXTERNAL_CATALOG.getValueType().name())));
            when(experimentsService.getExperimentsForAllGuestTokenDevices(any(), any()))
                    .thenReturn(ImmutableList.of(
                            new ExperimentDTO()
                                    .setKey(EXTERNAL_CATALOG.name())
                                    .setValueKey(EXTERNAL_CATALOG.getValueType().name())));

            start = System.currentTimeMillis();
            AvailableFilters newFilters = getAvailableFiltersByNewMethod(spec, requiredFilterTypes);
            newMs += System.currentTimeMillis() - start;
            log.info("newFilters: {}", newFilters);

            log.info("iterationsCount: {}, oldMs = {}, newMs = {}, old avg = {}, new avg = {}",
                    i + 1,
                    oldMs,
                    newMs,
                    ((double) oldMs) / (i + 1),
                    ((double) newMs) / (i + 1));

            compareAvailableFilters(oldFilters, newFilters, requiredFilterTypes);
        }
    }

    private Set<AvailableProductFilterTypes> nextRequiredFilterTypes() {
        Set<AvailableProductFilterTypes> result = new HashSet<>();
        for (int i = 0; i < AvailableProductFilterTypes.values().length; i++) {
            if (RandomUtils.nextBoolean()) {
                result.add(AvailableProductFilterTypes.values()[i]);
            }
        }
        return result;
    }

    @SneakyThrows
    private void compareAvailableFilters(AvailableFilters oldFilters, AvailableFilters newFilters,
            Set<AvailableProductFilterTypes> requiredFilterTypes) {

        oldFilters.getFilter().sort(Comparator.naturalOrder());
        oldFilters.getCategory().sort(Comparator.naturalOrder());
        oldFilters.getSize().sort(Comparator.naturalOrder());
        oldFilters.getBrand().sort(Comparator.naturalOrder());
        oldFilters.getProductModel().sort(Comparator.comparing(ProductModelDTO::getId));
        oldFilters.getProductCondition().sort(Comparator.naturalOrder());
        oldFilters.getLocationTags().sort(Comparator.naturalOrder());
        oldFilters.getBoutiqueLocationTags().sort(Comparator.naturalOrder());
        if (oldFilters.getCurrencies()!= null) oldFilters.getCurrencies().sort(Comparator.comparing(CurrencyDTO::getId));
        if (!requiredFilterTypes.contains(CONDITIONS)) oldFilters.setProductCondition(new ArrayList<>());
        oldFilters.setCurrencies(new ArrayList<>());
        oldFilters.setAttributeTree(null);
        oldFilters.setCategoryTree(null);
        oldFilters.setSizeTree(null);
        if (oldFilters.getStartPrice() != null) oldFilters.setStartPrice(oldFilters.getStartPrice().setScale(0, RoundingMode.DOWN));
        if (oldFilters.getEndPrice() != null) oldFilters.setEndPrice(oldFilters.getEndPrice().setScale(0, RoundingMode.DOWN));

        newFilters.getFilter().sort(Comparator.naturalOrder());
        newFilters.getCategory().sort(Comparator.naturalOrder());
        newFilters.getSize().sort(Comparator.naturalOrder());
        newFilters.getBrand().sort(Comparator.naturalOrder());
        newFilters.getProductModel().sort(Comparator.comparing(ProductModelDTO::getId));
        newFilters.getProductCondition().sort(Comparator.naturalOrder());
        newFilters.getLocationTags().sort(Comparator.naturalOrder());
        newFilters.getBoutiqueLocationTags().sort(Comparator.naturalOrder());
        if (newFilters.getCurrencies() != null) newFilters.getCurrencies().sort(Comparator.comparing(CurrencyDTO::getId));
        if (!requiredFilterTypes.contains(CONDITIONS)) newFilters.setProductCount(null);
        newFilters.setCurrencies(new ArrayList<>());
        newFilters.setAttributeTree(null);
        newFilters.setCategoryTree(null);
        newFilters.setSizeTree(null);
        if (newFilters.getStartPrice() != null) newFilters.setStartPrice(newFilters.getStartPrice().setScale(0, RoundingMode.DOWN));
        if (newFilters.getEndPrice() != null) newFilters.setEndPrice(newFilters.getEndPrice().setScale(0, RoundingMode.DOWN));

        String oldJson = objectMapper.writeValueAsString(oldFilters);
        String newJson = objectMapper.writeValueAsString(newFilters);

        log.info("oldJson: {}", oldJson);
        log.info("newJson: {}", newJson);

        assertEquals(oldJson, newJson);
    }

    private AvailableFilters getAvailableFiltersByOldMethod(
            FilterSpecification spec,
            Set<AvailableProductFilterTypes> availableProductFilterTypes
    ) {
        return defaultProductService.getAvailableFilters(spec, HUMAN, availableProductFilterTypes);
    }

    private AvailableFilters getAvailableFiltersByNewMethod(
            FilterSpecification spec,
            Set<AvailableProductFilterTypes> availableProductFilterTypes
    ) {
        GetAvailableFiltersOptions options = new GetAvailableFiltersOptions()
                .setAvailableFiltersInfoTypes(availableProductFilterTypes);
        options
                .setForUserType(HUMAN)
                .setForAuthorizedUser(userService.getUserOrThrowUserNotFoundException(50918L))
                .setForAuthorizedUserTokenToUser(new ArrayList<>());

        return productFiltrationEngine.getAvailableFilters(spec, options);
    }

    @Test
    @Disabled("Отладочный тест для запуска вручную")
    public void testGetItemsCountOldNewExternal() {

        // сравниваем выдачу старого и нового с внешним источником методов фильтрации

        AvailableFilters availableFilters = defaultProductService.getAvailableFilters(new FilterSpecification(),
                HUMAN);

        for (int i = 0; i < 100; i++) {

            log.info("Iteration {}", (i + 1));

            String currencyCode = nextCurrencyCode("RUB", "EUR");
            FilterSpecification spec = nextFilterSpecification(availableFilters, currencyCode);
            log.info("spec: {}", spec);

            long oldCount = getItemsCountByOldMethod(spec);
            log.info("oldCount: {}", oldCount);

            when(experimentsService.getExperimentsForAllUserDevices(any(), any()))
                    .thenReturn(ImmutableList.of(
                            new ExperimentDTO()
                                    .setKey(EXTERNAL_CATALOG.name())
                                    .setValueKey(EXTERNAL_CATALOG.getValueType().name())));
            when(experimentsService.getExperimentsForAllGuestTokenDevices(any(), any()))
                    .thenReturn(ImmutableList.of(
                            new ExperimentDTO()
                                    .setKey(EXTERNAL_CATALOG.name())
                                    .setValueKey(EXTERNAL_CATALOG.getValueType().name())));

            long newCount = getItemsCountByNewMethod(spec);
            log.info("newCount: {}", newCount);

            assertEquals(oldCount, newCount);
        }
    }

    private long getItemsCountByOldMethod(FilterSpecification spec) {
        return defaultProductService.countProducts(spec, HUMAN);
    }

    private long getItemsCountByNewMethod(FilterSpecification spec) {

        CountProductsOptions options = new CountProductsOptions();
        options
                .setForUserType(HUMAN)
                .setForAuthorizedUser(userService.getUserOrThrowUserNotFoundException(50918L))
                .setForAuthorizedUserTokenToUser(new ArrayList<>());

        return productFiltrationEngine.countProducts(spec, options);
    }
}