package ru.oskelly.tests.build.domain.service.catalog;

import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.service.catalog.CatalogCategory;
import su.reddot.domain.service.catalog.CategoryService;
import su.reddot.domain.service.dto.CategoryDTO;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class CategoryServiceTest extends AbstractSpringTest {
    @Autowired
    private CategoryService categoryService;

	List<Long> allRootCategories = Arrays.asList(2L, 105L, 188L, 366L);

    @Test
    public void _01_getSiblings(){
	    List<CatalogCategory> allSiblings = categoryService.getSiblings(allRootCategories.get(0), false);
	    assertSame(allRootCategories.size(), allSiblings.size());
	    List<Long> allSiblingsIds = allSiblings.stream().map(c -> c.getId()).collect(Collectors.toList());
	    assertEquals(new HashSet<Long>(allRootCategories), new HashSet<Long>(allSiblingsIds));
    }

    @Test
	public void _10_getGlobalSizeTree(){
	    CategoryDTO globalSizeTree = categoryService.getGlobalSizeTree().getRootCategory();
	    assertNotNull(globalSizeTree);
	    assertNotNull(globalSizeTree.getChildren());
	    assertTrue(globalSizeTree.getChildren().size() == allRootCategories.size());
	    for(CategoryDTO secondLevelCategory : globalSizeTree.getChildren()){
	    	assertNull(secondLevelCategory.getSizeValues());
	    	if(secondLevelCategory.getChildren() == null) continue; //Имеем дело с веткой без размеров (н.п.Lifestyle)
	    	//assertNotNull(secondLevelCategory.getChildren());
	    	for(CategoryDTO thirdLevelCategory : secondLevelCategory.getChildren()){
	    		assertTrue(thirdLevelCategory.getChildren() != null || thirdLevelCategory.getSizeValues() != null);
	    		if(thirdLevelCategory.getChildren() != null){
				    for(CategoryDTO fourthLevelCategory : thirdLevelCategory.getChildren()) {
					    assertNull(fourthLevelCategory.getChildren());
					    assertNotNull(fourthLevelCategory.getSizeValues());
					    assertFalse(fourthLevelCategory.getSizeValues().isEmpty());
				    }
			    }
		    }
	    }
    }

	@Test
	public void _20_getGlobalAttributeTree(){
		CategoryDTO globalAttributeTree = categoryService.getGlobalAttributeTree().getRootCategory();
		assertNotNull(globalAttributeTree);
		//assertTrue(globalAttributeTree.getAttributes().size() == 1);
		//assertTrue(globalAttributeTree.getAttributes().get(0).getName().equals("Цвет"));
		assertNotNull(globalAttributeTree.getChildren());
		assertTrue(globalAttributeTree.getChildren().size() == allRootCategories.size());
		for(CategoryDTO secondLevelCategory : globalAttributeTree.getChildren()){
			assertNotNull(secondLevelCategory.getChildren());
		}
	}

	@Test
	public void _30_getGlobalCategoryTree(){
		CategoryDTO globalCategoryTree = categoryService.getGlobalCategoryTree().getRootCategory();
		assertNotNull(globalCategoryTree);
		assertNotNull(globalCategoryTree.getChildren());
		assertTrue(globalCategoryTree.getChildren().size() == allRootCategories.size());
		for(CategoryDTO secondLevelCategory : globalCategoryTree.getChildren()){
			assertNotNull(secondLevelCategory.getChildren());
		}
	}
}
