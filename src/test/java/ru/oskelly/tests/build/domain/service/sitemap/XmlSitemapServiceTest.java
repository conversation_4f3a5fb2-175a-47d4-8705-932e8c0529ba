package ru.oskelly.tests.build.domain.service.sitemap;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.test.util.ReflectionTestUtils;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.model.Brand;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.service.brand.BrandService;
import su.reddot.domain.service.catalog.CatalogCategory;
import su.reddot.domain.service.catalog.CategoryService;
import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.product.ProductService;
import su.reddot.domain.service.sitemap.SiteMapGenerationProperties;
import su.reddot.domain.service.sitemap.XmlSitemapService;
import su.reddot.domain.service.sitemap.impl.XmlSitemapServiceImpl;
import su.reddot.infrastructure.s3.S3Service;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyCollection;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class XmlSitemapServiceTest {
    public static final int PAGE_SIZE = 500;
    public static final String TEST_SITEMAP_XML = "sitemap.xml";
    public static final String BLOG_SITEMAP_URL = "http://test.test/sitemap_index.xml";
    public static final String S3_PATH = "/s3Folder/";

    private CategoryService categoryService = mock(CategoryService.class);

    private ProductService productService = mock(ProductService.class);

    private BrandService brandService = mock(BrandService.class);

    private SiteMapGenerationProperties siteMapGenerationProperties = mock(SiteMapGenerationProperties.class);

    private S3Service s3Service = mock(S3Service.class);

    private ArgumentCaptor<File> fileArgumentCaptor = ArgumentCaptor.forClass(File.class);


    private DocumentBuilder builder;
    private XPathFactory xPathFactory;
    private XmlSitemapService sitemapService;

    @BeforeEach
    public void init() throws Exception {
        builder = DocumentBuilderFactory.newInstance().newDocumentBuilder();
        xPathFactory = XPathFactory.newInstance();

        doReturn(S3_PATH).when(siteMapGenerationProperties).getS3Path();
        doReturn(PAGE_SIZE).when(siteMapGenerationProperties).getProductPageSize();

        sitemapService = new XmlSitemapServiceImpl(
                categoryService,
                productService,
                brandService,
                siteMapGenerationProperties,
                s3Service
        );
        doReturn(getBrands()).when(brandService).getAllBrandsForCategories(anyCollection());
        doReturn(getCategories()).when(categoryService).getEntireCatalog();
        doReturn(getProductPage()).when(productService).getAvailableProducts(anyInt(), anyInt());
        //
        ReflectionTestUtils.setField(sitemapService, "host", "http://test.test");
        ReflectionTestUtils.setField(sitemapService, "blogMapUrl", BLOG_SITEMAP_URL);
        ReflectionTestUtils.setField(sitemapService, "removeFiles", false);
    }

    @AfterEach
    public void cleanup() {

    }

    @Test
    public void createsSiteMapForProductsAndCategoriesAndBrands() {
        sitemapService.createSiteMapXml();
        verify(s3Service, times(4)).uploadFile(anyString(), fileArgumentCaptor.capture());
        List<File> files = fileArgumentCaptor.getAllValues();
        assertThat(files).hasSize(4);
        assertThat(files).anyMatch(file -> "sitemap-brands.xml".equals(file.getName()));
        assertThat(files).anyMatch(file -> "sitemap-catalog.xml".equals(file.getName()));
        assertThat(files).anyMatch(file -> "sitemap-products-1.xml".equals(file.getName()));
        assertThat(files).anyMatch(file -> "sitemap.xml".equals(file.getName()));
    }

    @Test
    public void sitemapBrandsHasValidContent() throws Exception {
        sitemapService.createSiteMapXml();
        verify(s3Service, times(4)).uploadFile(anyString(), fileArgumentCaptor.capture());
        Optional<File> fileOptional = fileArgumentCaptor.getAllValues().stream().filter(f -> f.getName().equals("sitemap-brands.xml")).findFirst();
        assertThat(fileOptional).isPresent();
        Document document = builder.parse(fileOptional.get());
        XPath xPath = xPathFactory.newXPath();

        assertThat(xPath.evaluate("urlset/url/loc", document)).isEqualTo("http://test.test/brands/testovoe/test-brand");
        assertThat(xPath.evaluate("urlset/url/changefreq", document)).isEqualTo("weekly");
        assertThat(Double.parseDouble(xPath.evaluate("urlset/url/priority", document))).isEqualTo(0.75);
    }

    @Test
    public void sitemapCategoriesHasValidContent() throws IOException, SAXException, XPathExpressionException {
        sitemapService.createSiteMapXml();
        verify(s3Service, times(4)).uploadFile(anyString(), fileArgumentCaptor.capture());
        Optional<File> fileOptional = fileArgumentCaptor.getAllValues().stream().filter(f -> f.getName().equals("sitemap-catalog.xml")).findFirst();
        assertThat(fileOptional).isPresent();
        Document document = builder.parse(fileOptional.get());
        XPath xPath = xPathFactory.newXPath();

        assertThat(xPath.evaluate("urlset/url[1]/loc", document)).isEqualTo("http://test.test/catalog/testovoe");
        assertThat(xPath.evaluate("urlset/url[1]/changefreq", document)).isEqualTo("monthly");
        assertThat(Double.parseDouble(xPath.evaluate("urlset/url[1]/priority", document))).isEqualTo(1.0);

        assertThat(xPath.evaluate("urlset/url[2]/loc", document)).isEqualTo("http://test.test/catalog/testovoe-child");
        assertThat(xPath.evaluate("urlset/url[2]/changefreq", document)).isEqualTo("monthly");
        assertThat(Double.parseDouble(xPath.evaluate("urlset/url[2]/priority", document))).isEqualTo(1.0);
    }


    @Test
    public void sitemapProductsHasValidContent() throws IOException, SAXException, XPathExpressionException {
        sitemapService.createSiteMapXml();
        verify(s3Service, times(4)).uploadFile(anyString(), fileArgumentCaptor.capture());
        Optional<File> fileOptional = fileArgumentCaptor.getAllValues().stream().filter(f -> f.getName().equals("sitemap-products-1.xml")).findFirst();
        assertThat(fileOptional).isPresent();
        Document document = builder.parse(fileOptional.get());
        XPath xPath = xPathFactory.newXPath();

        for (int i = 1; i <= 10; i++) {
            assertThat(xPath.evaluate("urlset/url[" + i + "]/loc", document)).isEqualTo("http://test.test/products/testovyy-produkt-test-pre-owned-" + (i - 1));
            assertThat(xPath.evaluate("urlset/url[" + i + "]/changefreq", document)).isEqualTo("daily");
            assertThat(Double.parseDouble(xPath.evaluate("urlset/url[" + i + "]/priority", document))).isEqualTo(0.75);
        }
    }

    @Test
    public void rootSitemapHasValidContent() throws IOException, SAXException, XPathExpressionException {
        sitemapService.createSiteMapXml();
        verify(s3Service, times(4)).uploadFile(anyString(), fileArgumentCaptor.capture());
        Optional<File> fileOptional = fileArgumentCaptor.getAllValues().stream().filter(f -> f.getName().equals("sitemap.xml")).findFirst();
        assertThat(fileOptional).isPresent();
        Document document = builder.parse(fileOptional.get());
        XPath xPath = xPathFactory.newXPath();

        assertThat(xPath.evaluate("sitemapindex/sitemap[1]/loc", document)).isEqualTo("http://test.test/sitemap-products-1.xml");
        assertThat(xPath.evaluate("sitemapindex/sitemap[2]/loc", document)).isEqualTo("http://test.test/sitemap-catalog.xml");
        assertThat(xPath.evaluate("sitemapindex/sitemap[3]/loc", document)).isEqualTo("http://test.test/sitemap-brands.xml");
        assertThat(xPath.evaluate("sitemapindex/sitemap[4]/loc", document)).isEqualTo(BLOG_SITEMAP_URL);
    }

    @Test
    public void generatesMultipleProductsFiles() {
        List<Product> items = new ArrayList<>();
        for (int i = 0; i < 20; i++) {
            Product product = spy(Product.class);
            product.setId((long) i);
            when(product.getDisplayName()).thenReturn("Тестовый продукт");
            product.setProductState(ProductState.PUBLISHED);
            Brand brand = mock(Brand.class);
            when(brand.getUrl()).thenReturn("test");
            product.setBrand(brand);
            product.setPublishTime(LocalDateTime.now());
            items.add(product);
        }
        Page<Product> firstPage = new Page<>(items.subList(0, 10), 2, 20);
        Page<Product> secondPage = new Page<>(items.subList(10, 20), 2, 20);

        doReturn(firstPage).doReturn(secondPage).when(productService).getAvailableProducts(anyInt(), anyInt());
        sitemapService.createSiteMapXml();
        verify(s3Service, times(5)).uploadFile(anyString(), fileArgumentCaptor.capture());
        assertThat(fileArgumentCaptor.getAllValues().stream().filter(f -> f.getName().equals("sitemap-products-1.xml")).findFirst()).isPresent();
        assertThat(fileArgumentCaptor.getAllValues().stream().filter(f -> f.getName().equals("sitemap-products-2.xml")).findFirst()).isPresent();
    }

    @Test
    public void shouldGenerateValidSchemaWhenProductPublishTimeHasZeroSecondAndZeroMillis() throws IOException, SAXException, XPathExpressionException {
        Product product = spy(Product.class);
        product.setId((long) 1);
        when(product.getDisplayName()).thenReturn("Тестовый продукт");
        product.setProductState(ProductState.PUBLISHED);
        Brand brand = mock(Brand.class);
        when(brand.getUrl()).thenReturn("test");
        product.setBrand(brand);
        LocalDateTime publishTime = LocalDateTime.of(2022, 3, 29, 13, 25, 0, 0);
        product.setPublishTime(publishTime);
        Page<Product> productPage = new Page<>(Collections.singletonList(product), 1, 1);


        doReturn(productPage).when(productService).getAvailableProducts(anyInt(), anyInt());

        sitemapService.createSiteMapXml();

        verify(s3Service, times(4)).uploadFile(anyString(), fileArgumentCaptor.capture());
        Optional<File> fileOptional = fileArgumentCaptor.getAllValues().stream().filter(f -> f.getName().equals("sitemap-products-1.xml")).findFirst();
        assertThat(fileOptional).isPresent();
        Document document = builder.parse(fileOptional.get());
        XPath xPath = xPathFactory.newXPath();

        DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;

        ZonedDateTime actualLastMod = ZonedDateTime.parse(xPath.evaluate("urlset/url[1]/lastmod", document), formatter);
        ZonedDateTime expectedTime = ZonedDateTime.of(publishTime, ZoneOffset.UTC);
        assertThat(actualLastMod).isEqualTo(expectedTime);
    }

    @Test
    public void allFilesAreSentToS3() {
        sitemapService.createSiteMapXml();

        verify(s3Service, times(4))
                .uploadFile(eq(S3_PATH), fileArgumentCaptor.capture());

        List<File> files = fileArgumentCaptor.getAllValues();
        assertThat(files).anyMatch(file -> "sitemap-catalog.xml".equals(file.getName()));
        assertThat(files).anyMatch(file -> TEST_SITEMAP_XML.equals(file.getName()));
        assertThat(files).anyMatch(file -> "sitemap-products-1.xml".equals(file.getName()));
        assertThat(files).anyMatch(file -> "sitemap-brands.xml".equals(file.getName()));
    }

    private Page<Product> getProductPage() {
        List<Product> items = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            Product product = spy(Product.class);
            product.setId((long) i);
            when(product.getDisplayName()).thenReturn("Тестовый продукт");
            product.setProductState(ProductState.PUBLISHED);
            Brand brand = mock(Brand.class);
            when(brand.getUrl()).thenReturn("test");
            product.setBrand(brand);
            product.setPublishTime(LocalDateTime.now());
            items.add(product);
        }
        return new Page<>(items, 1, 10);
    }

    private List<CatalogCategory> getCategories() {
        CatalogCategory catalogCategory = mock(CatalogCategory.class);
        doReturn("testovoe").when(catalogCategory).getUrlName();
        doReturn(true).when(catalogCategory).isHasChildren();
        doReturn(null).when(catalogCategory).getParentId();
        CatalogCategory childCategory = mock(CatalogCategory.class);
        doReturn("testovoe-child").when(childCategory).getUrlName();
        doReturn(false).when(childCategory).isHasChildren();

        doReturn(Collections.singletonList(childCategory)).when(catalogCategory).getChildren();
        return Collections.singletonList(catalogCategory);
    }

    private List<Brand> getBrands() {
        Brand brand = new Brand();
        brand.setUrl("test-brand");
        return Collections.singletonList(brand);
    }
}
