package ru.oskelly.tests.build.infrastructure.util;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.context.support.MessageSourceAccessor;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.dto.PageRequest;
import su.reddot.infrastructure.bank.commons.BankCommons;
import su.reddot.infrastructure.util.Utils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static su.reddot.infrastructure.util.Utils.getUrlFromString;

@Slf4j
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class UtilsTest {
	@Mock
	private MessageSourceAccessor messageSourceAccessor;

	@BeforeEach
	public void setUp() {
		messageSourceAccessor = mock(MessageSourceAccessor.class);
		when(messageSourceAccessor.getMessage("infrastructure.util.Utils.Product.One")).thenReturn("товар");
		when(messageSourceAccessor.getMessage("infrastructure.util.Utils.Product.Many")).thenReturn("товаров");
		when(messageSourceAccessor.getMessage("infrastructure.util.Utils.Product.Other")).thenReturn("товара");
		when(messageSourceAccessor.getMessage("infrastructure.util.Utils.NewItem.One")).thenReturn("новинка");
		when(messageSourceAccessor.getMessage("infrastructure.util.Utils.NewItem.Many")).thenReturn("новинок");
		when(messageSourceAccessor.getMessage("infrastructure.util.Utils.NewItem.Other")).thenReturn("новинки");
		when(messageSourceAccessor.getMessage("infrastructure.util.Utils.Day.One")).thenReturn("день");
		when(messageSourceAccessor.getMessage("infrastructure.util.Utils.Day.Many")).thenReturn("дней");
		when(messageSourceAccessor.getMessage("infrastructure.util.Utils.Day.Other")).thenReturn("дня");
		when(messageSourceAccessor.getMessage("infrastructure.util.Utils.WorkingDay.One")).thenReturn("рабочий день");
		when(messageSourceAccessor.getMessage("infrastructure.util.Utils.WorkingDay.Many")).thenReturn("рабочих дней");
		when(messageSourceAccessor.getMessage("infrastructure.util.Utils.WorkingDay.Other")).thenReturn("рабочих дня");
	}

    @Test
    public void isNumericAndConcreteLengthReturnsTrue(){
        Map<String, Integer> fineCombinations = new HashMap<>();
        fineCombinations.put("1", 1);
        fineCombinations.put("0", 1);
        fineCombinations.put("123436346", 9);
        fineCombinations.put("0123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789", 100);
        fineCombinations.forEach((s, l) ->
            assertTrue(Utils.isNumericAndConcreteLength(s, l))
        );
    }

    @Test
    public void isNumericAndConcreteLengthReturnsFalse(){
        Map<String, Integer> badCombinations = new HashMap<>();
        badCombinations.put("1", 2);
        badCombinations.put("0", 0);
        badCombinations.put("12 34", 5);
        badCombinations.put("12-34", 5);
        badCombinations.put(" 1234", 5);
        badCombinations.put(null, 0);
        badCombinations.put("", 0);
        badCombinations.forEach((s, l) ->
            assertFalse(Utils.isNumericAndConcreteLength(s, l))
        );
    }

    @Test
    public void testJoinStrings(){
        assertEquals("Hello world", Utils.joinStrings(" ", "Hello", "world"));
        assertEquals("Hello world", Utils.joinStrings(" ", "Hello", "world", " "));
        assertEquals("Hello world", Utils.joinStrings(" ", "Hello", "world", null));
        assertEquals("Hello world", Utils.joinStrings(" ", "Hello", null, "world"));
        assertEquals("Hello world", Utils.joinStrings(" ", null, "Hello", null, "world"));
    }

	public void testGetGoodsCountTitle(int hundreds){
    	int offset = 100 * hundreds;
		assertEquals((offset + 1) + " товар", Utils.getGoodsCountTitle(offset + 1, messageSourceAccessor));
		for(int i = 2; i < 5; i++){
			assertEquals((offset + i) + " товара", Utils.getGoodsCountTitle(offset + i, messageSourceAccessor));
		}
		for(int i = 5; i < 21 ; i++){
			assertEquals((offset + i) + " товаров", Utils.getGoodsCountTitle((offset + i), messageSourceAccessor));
		}
		for(int i = 22; i < 25; i++){
			assertEquals((offset + i) + " товара", Utils.getGoodsCountTitle(offset + i, messageSourceAccessor));
		}
		for(int i = 25; i < 31 ; i++){
			assertEquals((offset + i) + " товаров", Utils.getGoodsCountTitle(offset + i, messageSourceAccessor));
		}
		for(int i = 32; i < 35; i++){
			assertEquals((offset + i) + " товара", Utils.getGoodsCountTitle(offset + i, messageSourceAccessor));
		}
	}

	@Test
	public void testGetGoodsCountTitle(){
		testGetGoodsCountTitle(0);
		testGetGoodsCountTitle(1);
		testGetGoodsCountTitle(2);
		testGetGoodsCountTitle(10);
	}

	@Test
	public void testGetFirstElementFromLists(){
		ListPool listPool = new ListPool();
		String firstValue = Utils.getFirstElementFromLists(() -> listPool.getList1(),
				() -> listPool.getList2(),
				() -> listPool.getList3());
		assertEquals("first", firstValue);
	}

	@Test
	public void getPageFromListTest(){
		int pageSize = 10;
    	int totalPages = 11;
    	int totalAmount = 102;
    	/*list: 0, 1, 2, ..., 100, 101 */
    	List<Integer> list = new ArrayList<>(totalAmount);
    	for(int i = 0; i < totalAmount; i++){
    		list.add(i);
	    }

    	//Первая страница
		Page<Integer> page = Utils.getPageFromList(list, PageRequest.of(1, pageSize));
		checkPage(page, totalAmount, totalPages, pageSize, 0, 9);

		//Вторая страница
		page = Utils.getPageFromList(list, PageRequest.of(2, pageSize));
		checkPage(page, totalAmount, totalPages, pageSize, 10, 19);

		//Десятая страница
		page = Utils.getPageFromList(list, PageRequest.of(10, pageSize));
		checkPage(page, totalAmount, totalPages, pageSize, 90, 99);

		//Одиннадцатая страница
		page = Utils.getPageFromList(list, PageRequest.of(11, pageSize));
		checkPage(page, totalAmount, totalPages, 2, 100, 101);

		//Двенадцатая страница
		page = Utils.getPageFromList(list, PageRequest.of(12, pageSize));
		checkPage(page, totalAmount, totalPages, 0, 0, 0);

	}

	@Test
	public void _00_getUrlFromString() {
		Map<String, String> input_texts_and_expected_urls = new HashMap<String, String>();
		input_texts_and_expected_urls.put("oskelly", "oskelly");
		input_texts_and_expected_urls.put("THE ROW", "the-row");
		input_texts_and_expected_urls.put("...oske...lly...", "oskelly");
		input_texts_and_expected_urls.put("abcdefghijklmnopqrstuvwxyz", "abcdefghijklmnopqrstuvwxyz");
		input_texts_and_expected_urls.put(" ABCDEFGHIJKLMNOPQRSTUVWXYZ ", "abcdefghijklmnopqrstuvwxyz");
		input_texts_and_expected_urls.put("ABCDEFGHIjklmnOPQRSTuvwxyz", "abcdefghijklmnopqrstuvwxyz");
		input_texts_and_expected_urls.put("абвгдеёжзийклмнопрстуфхцчшщъыьэюя", "abvgdeyozhziyklmnoprstufkhtschshshchyeyuya");
		input_texts_and_expected_urls.put("АБВГДЕЁЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ", "abvgdeyozhziyklmnoprstufkhtschshshchyeyuya");
		input_texts_and_expected_urls.put("АБВГДЕЁЖЗИ///ЙКЛМНОПРСТУФХ.....>>>><<<<,,,,,ЦЧШЩЪЫЬЭЮЯ", "abvgdeyozhziyklmnoprstufkhtschshshchyeyuya");
		input_texts_and_expected_urls.put("£€¶©®™ºª‰π¦§°µ?!@#$%^&*()[]{}|~…‾´№` \\ \' \" ☎ Δ ← ↑ → ↓ ↔ ♠ ♣ ♥ ♦ «» „ ” “    Q", "q");
		input_texts_and_expected_urls.put("£€¶©®™ºª‰π¦§°µ?!@#$%^&*()[]{}|~…‾´№` \\ \' \" ☎ Δ ← ↑ → ↓ ↔ ♠ ♣ ♥ T ♦ «» „ ” “    Q", "t-q");


		input_texts_and_expected_urls.forEach((k,v) -> {
			assertEquals(getUrlFromString(k), v);
		});

	}

	//Форматирование XML-строки с корневым элементом
	@Test
	public void testFormatXmlWithRoot(){
		String input = "<root><xml version=\"34.565\" name=\"XML Name русский\"></xml><csv version=\"4.5\" name=\"CSV Name русский\"></csv></root>";
		String output = Utils.formatXML(input);
		assertEquals("<root>" + System.lineSeparator() +
				"    <xml version=\"34.565\" name=\"XML Name русский\"/>" + System.lineSeparator() +
				"    <csv version=\"4.5\" name=\"CSV Name русский\"/>" + System.lineSeparator() +
				"  </root>", output);
	}

	//Форматирование XML-строки без корневого элемента
	@Test
	public void testFormatXmlNoRoot(){
		String input = "<xml version=\"34.565\" name=\"XML Name русский\"></xml><csv version=\"4.5\" name=\"CSV Name русский\"></csv>";
		String output = Utils.formatXML(input);
		assertEquals("<xml version=\"34.565\" name=\"XML Name русский\"/>" + System.lineSeparator() +
				"  <csv version=\"4.5\" name=\"CSV Name русский\"/>", output);
	}

	//Форматирование XML-строки в параллельных потоках (есть подозрение, что бывают глюки при выводе)
	@Test
	public void testFormatXmlWithRootMultythread(){
		int count = 100;
		String input = "<root><xml version=\"34.565\" name=\"XML Name русский\"></xml><csv version=\"4.5\" name=\"CSV Name русский\"></csv></root>";
		String expectedResult = "<root>" + System.lineSeparator() +
				"    <xml version=\"34.565\" name=\"XML Name русский\"/>" + System.lineSeparator() +
				"    <csv version=\"4.5\" name=\"CSV Name русский\"/>" + System.lineSeparator() +
				"  </root>";
		Map<Integer, String> inputsMap = new ConcurrentHashMap<>();
		Map<Integer, String> resultsMap = new ConcurrentHashMap<>();
		for(int i = 0; i < count; i++){
			inputsMap.put(i, input);
		}
		//Многопоточно гоняем форматирование и складываем в таблицу результатов
		inputsMap.keySet().parallelStream().forEach(i -> {
			String inp = inputsMap.get(i);
			String out = Utils.formatXML(inp);
			log.info("Форматируем строку " + i + ": " + inp);
			log.info("Результат форматирования строки " + i + ": " + out);
			resultsMap.put(i, out);
		});

		//Проверяем результаты
		for(int i = 0; i < count; i++){
			String res = resultsMap.get(i);
			assertEquals(expectedResult, res);
		}
	}

	private void checkPage(Page<Integer> page, int totalAmount, int totalPages, int itemsSize, int firstItem, int lastItem){
    	assertEquals(totalAmount, page.getTotalAmount());
		assertEquals(totalPages, page.getTotalPages());
		assertEquals(itemsSize, page.getItems().size());
		if(itemsSize == 0) return;
		assertEquals(firstItem, page.getItems().get(0).intValue());
		assertEquals(lastItem, page.getItems().get(page.getItems().size() - 1).intValue());
	}

	public static class ListPool{
    	public List<String> getList1(){
    		return Collections.emptyList();
	    }
		public List<String> getList2(){
			return Arrays.asList("first", "second");
		}
		public List<String> getList3(){
			return Arrays.asList("third");
		}
	}

	@Test
	public void toAmountInCentsFine() {
		Long amountInCents;
		amountInCents = BankCommons.toAmountInCents(new BigDecimal("100.01"));
		assertEquals(10001L, amountInCents.longValue());
		amountInCents = BankCommons.toAmountInCents(new BigDecimal("100.49"));
		assertEquals(10049L, amountInCents.longValue());
		amountInCents = BankCommons.toAmountInCents(new BigDecimal("100.51"));
		assertEquals(10051L, amountInCents.longValue());
		amountInCents = BankCommons.toAmountInCents(new BigDecimal("100.001"));
		assertEquals(10000L, amountInCents.longValue());
		amountInCents = BankCommons.toAmountInCents(new BigDecimal("100.005"));
		assertEquals(10001L, amountInCents.longValue());
		amountInCents = BankCommons.toAmountInCents(new BigDecimal("100.498"));
		assertEquals(10050L, amountInCents.longValue());
		amountInCents = BankCommons.toAmountInCents(new BigDecimal("100.494"));
		assertEquals(10049L, amountInCents.longValue());
		amountInCents = BankCommons.toAmountInCents(new BigDecimal("93099.6500000000006080080883208438535802997648715972900390625"));
		assertEquals(9309965L, amountInCents.longValue());
	}

	/**
	 * Tests {@link Utils#cutLongString(String, int)} method.
	 */
	@Test
	public void testCutLongString() {
		String origString = "5 этажное здание из красного кирпича. Контактный телефон 000 000-00-00. Сказать что забираете серьги";
		for (int maxLength = 3; maxLength < 102; maxLength++) {
			String result = Utils.cutLongString(origString, maxLength);
			assertTrue(result.length() <= maxLength);
		}
	}
}
