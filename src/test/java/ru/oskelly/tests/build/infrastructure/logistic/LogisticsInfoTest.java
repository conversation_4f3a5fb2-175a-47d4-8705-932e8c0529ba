package ru.oskelly.tests.build.infrastructure.logistic;

import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.deliverycompany.DeliveryCompanyRepository;
import su.reddot.domain.dao.logistic.LogisticsInfoRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.model.logistic.DeliveryCompany;
import su.reddot.domain.model.logistic.LogisticsInfo;
import su.reddot.domain.model.logistic.LogisticsInfoState;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.task.ScheduledAutoPickupRunner;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.delivery.DeliveryService;
import su.reddot.infrastructure.logistic.LogisticsInfoService;
import su.reddot.infrastructure.logistic.ProcessOrderPickupProperties;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

/**
 * Use for development/debugging goals
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(
        classes = {OskellyApplication.class},
        properties = {
                "logistics.job.auto-pickup-runner.retry-max-count=1"
        }
)
@ActiveProfiles(profiles = AbstractSpringTest.testProfiles)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class LogisticsInfoTest {

    @Autowired
    LogisticsInfoService logisticsInfoService;

    @Autowired
    LogisticsInfoRepository logisticsInfoRepository;

    @Autowired
    OrderRepository orderRepository;

    @Autowired
    UserRepository userRepository;

    @Autowired
    DeliveryCompanyRepository deliveryCompanyRepository;

    @Autowired
    ProcessOrderPickupProperties logisticsInfoProperties;

    @Autowired
    JdbcTemplate jdbcTemplate;

    @MockBean
    DeliveryService deliveryService;

    @MockBean
    ScheduledAutoPickupRunner scheduledLogisticsRunner;

    @Test
    public void shouldProcessPendingRequestAndReturnSuccessWithoutRetries() {
        // prepare
        Order order = generateFakeOrder();

        // do action
        LogisticsInfo sellerLogisticsInfo = order.getSellerLogisticsInfo();
        logisticsInfoService.markAsPending(sellerLogisticsInfo);
        List<LogisticsInfo> result = logisticsInfoService.processOrdersPickup();

        // assert results
        assertEquals(1, result.size());
        assertEquals(LogisticsInfoState.SUCCESS, result.get(0).getState());
        assertEquals(1, result.get(0).getProcessingAttempt());
        assertNull(result.get(0).getNextProcessingTime());

        // reset state
        orderRepository.delete(order);
    }

    @SneakyThrows
    @Test
    public void shouldProcessPendingRequestAndReturnSuccessAfterRetries() {
        //prepare
        Order order = generateFakeOrder();
        Mockito.doThrow(new RuntimeException("Test error"))
                .doNothing()
                .when(deliveryService)
                .sendConfirmedOrderToPickup(Mockito.any());

        //do action
        LogisticsInfo sellerLogisticsInfo = order.getSellerLogisticsInfo();
        logisticsInfoService.markAsPending(sellerLogisticsInfo);
        List<LogisticsInfo> result = logisticsInfoService.processOrdersPickup();

        //assert results
        assertEquals(1, result.size());
        assertEquals(LogisticsInfoState.RETRY, result.get(0).getState());
        assertEquals(1, result.get(0).getProcessingAttempt());
        assertNotNull(result.get(0).getNextProcessingTime());

        int delayTime = logisticsInfoProperties.getRetryInitialStepSec() * 1000 * 2;
        Thread.sleep(delayTime);

        //do action
        result = logisticsInfoService.processOrdersPickup();

        //assert results
        assertEquals(1, result.size());
        assertEquals(LogisticsInfoState.SUCCESS, result.get(0).getState());
        assertEquals(2, result.get(0).getProcessingAttempt());
        assertNotNull(result.get(0).getNextProcessingTime());
        assertEquals("Test error", result.get(0).getErrorMessage());

        //reset state
        orderRepository.delete(order);
    }

    @SneakyThrows
    @Test
    public void shouldProcessPendingRequestAndReturnErrorAfterRetries() {
        //prepare
        Order order = generateFakeOrder();
        Mockito.doThrow(new RuntimeException("Test error attempt 1"))
                .doThrow(new RuntimeException("Test error attempt 2"))
                .doNothing()
                .when(deliveryService)
                .sendConfirmedOrderToPickup(Mockito.any());

        //do action
        LogisticsInfo sellerLogisticsInfo = order.getSellerLogisticsInfo();
        logisticsInfoService.markAsPending(sellerLogisticsInfo);
        List<LogisticsInfo> result = logisticsInfoService.processOrdersPickup();

        //assert results
        assertEquals(1, result.size());
        assertEquals(LogisticsInfoState.RETRY, result.get(0).getState());
        assertEquals(1, result.get(0).getProcessingAttempt());
        assertNotNull(result.get(0).getNextProcessingTime());
        assertEquals("Test error attempt 1", result.get(0).getErrorMessage());

        int delayTime = logisticsInfoProperties.getRetryInitialStepSec() * 1000 * 2;
        Thread.sleep(delayTime);

        //do action
        result = logisticsInfoService.processOrdersPickup();

        //assert results
        assertEquals(1, result.size());
        assertEquals(LogisticsInfoState.ERROR, result.get(0).getState());
        assertEquals(2, result.get(0).getProcessingAttempt());
        assertNotNull(result.get(0).getNextProcessingTime());
        assertEquals("Test error attempt 2", result.get(0).getErrorMessage());

        //reset state
        orderRepository.delete(order);
    }

    private Order generateFakeOrder() {
        User user = userRepository.getOne(838L);
        DeliveryCompany deliveryCompany = deliveryCompanyRepository.getOne(1L);

        LogisticsInfo logisticsInfo = logisticsInfoRepository.saveAndFlush(
                LogisticsInfo.builder()
                        .deliveryCompany(deliveryCompany)
                        .pickupDate(LocalDate.now())
                        .pickupTimeIntervalId(1)
                        .build()
        );

        Order order = new Order();
        order.setState(OrderState.CREATED);
        order.setUuid(UUID.randomUUID());
        order.setBuyer(user);
        order.setSellerLogisticsInfo(logisticsInfo);
        return orderRepository.save(order);
    }
}
