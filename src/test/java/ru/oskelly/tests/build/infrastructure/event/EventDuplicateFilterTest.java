package ru.oskelly.tests.build.infrastructure.event;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.service.product.ChangeProductEvent;
import su.reddot.domain.service.user.ChangeUserEvent;
import su.reddot.infrastructure.event.EventDuplicateFilter;

import static org.junit.jupiter.api.Assertions.*;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class EventDuplicateFilterTest extends AbstractSpringTest {

    String uniqueServiceId1 = "EventDuplicateFilterTest";
    String uniqueServiceId2 = "EventDuplicateFilterTest2";

    @Autowired
    private EventDuplicateFilter eventDuplicateFilter;

    @Test
    public void testCacheFilter_ChangeProductEvent(){
        long productId1 = 4L;
        long productId2 = 5L;


        //Создаем первое событие
        ChangeProductEvent e1 = new ChangeProductEvent().setProductId(productId1).setProductState(ProductState.NEED_MODERATION.name());
        //В первый раз не считается дубликатом
        assertFalse(eventDuplicateFilter.isDuplicate(e1, uniqueServiceId1));
        //Во второй и третий разы - дубликат
        assertTrue(eventDuplicateFilter.isDuplicate(e1, uniqueServiceId1));
        assertTrue(eventDuplicateFilter.isDuplicate(e1, uniqueServiceId1));

        //Создаем аналогичное событие
        ChangeProductEvent e2 = new ChangeProductEvent().setProductId(productId1).setProductState(ProductState.NEED_MODERATION.name());
        //Оно является дубликатом
        assertTrue(eventDuplicateFilter.isDuplicate(e2, uniqueServiceId1));

        //Создаем другое событие
        ChangeProductEvent e3 = new ChangeProductEvent().setProductId(productId1).setProductState(ProductState.PUBLISHED.name());
        //Оно не является дубликатом в первый раз
        assertFalse(eventDuplicateFilter.isDuplicate(e3, uniqueServiceId1));
        //В последующие разы оно считается дубликатом
        assertTrue(eventDuplicateFilter.isDuplicate(e3, uniqueServiceId1));
        assertTrue(eventDuplicateFilter.isDuplicate(e3, uniqueServiceId1));

        //Событие с другим ID товара не считается дубликатом в первый раз
        ChangeProductEvent e4 = new ChangeProductEvent().setProductId(productId2).setProductState(ProductState.PUBLISHED.name());
        assertFalse(eventDuplicateFilter.isDuplicate(e4, uniqueServiceId1));

        //Но считается дубликатом в последующие разы
        assertTrue(eventDuplicateFilter.isDuplicate(e4, uniqueServiceId1));
        assertTrue(eventDuplicateFilter.isDuplicate(e4, uniqueServiceId1));

        //Старое событие по прежнему является дубликатом
        assertTrue(eventDuplicateFilter.isDuplicate(e3, uniqueServiceId1));

        //Не считаются дубликатами при передаче другого uniqueServiceId
        assertFalse(eventDuplicateFilter.isDuplicate(e3, uniqueServiceId2));
        assertFalse(eventDuplicateFilter.isDuplicate(e4, uniqueServiceId2));
    }

    @Test
    public void testCacheFilter_ChangeUserEvent(){
        long userId1 = 4L;
        long userId2 = 5L;

        String nickname1 = "nickname1";
        String nickname2 = "nickname2";

        //Создаем первое событие
        ChangeUserEvent e1 = new ChangeUserEvent().setId(userId1).setNickName(nickname1);
        //В первый раз не считается дубликатом
        assertFalse(eventDuplicateFilter.isDuplicate(e1, uniqueServiceId1));
        //Во второй и третий разы - дубликат
        assertTrue(eventDuplicateFilter.isDuplicate(e1, uniqueServiceId1));
        assertTrue(eventDuplicateFilter.isDuplicate(e1, uniqueServiceId1));

        //Создаем аналогичное событие
        ChangeUserEvent e2 = new ChangeUserEvent().setId(userId1).setNickName(nickname1);
        //Оно является дубликатом
        assertTrue(eventDuplicateFilter.isDuplicate(e2, uniqueServiceId1));

        //Создаем другое событие
        ChangeUserEvent e3 = new ChangeUserEvent().setId(userId1).setNickName(nickname2);
        //Оно не является дубликатом в первый раз
        assertFalse(eventDuplicateFilter.isDuplicate(e3, uniqueServiceId1));
        //В последующие разы оно считается дубликатом
        assertTrue(eventDuplicateFilter.isDuplicate(e3, uniqueServiceId1));
        assertTrue(eventDuplicateFilter.isDuplicate(e3, uniqueServiceId1));

        //Событие с другим ID пользователя не считается дубликатом в первый раз
        ChangeUserEvent e4 = new ChangeUserEvent().setId(userId2).setNickName(nickname2);
        assertFalse(eventDuplicateFilter.isDuplicate(e4, uniqueServiceId1));

        //Но считается дубликатом в последующие разы
        assertTrue(eventDuplicateFilter.isDuplicate(e4, uniqueServiceId1));
        assertTrue(eventDuplicateFilter.isDuplicate(e4, uniqueServiceId1));

        //Старое событие по прежнему является дубликатом
        assertTrue(eventDuplicateFilter.isDuplicate(e3, uniqueServiceId1));

        //Не считаются дубликатами при передаче другого uniqueServiceId
        assertFalse(eventDuplicateFilter.isDuplicate(e3, uniqueServiceId2));
        assertFalse(eventDuplicateFilter.isDuplicate(e4, uniqueServiceId2));
    }
}
