package ru.oskelly.tests.build.presentation.pdf;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

import com.google.common.collect.ImmutableList;

import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.presentation.pdf.CommissionAgentReport;
import su.reddot.presentation.pdf.CommissionAgentReportData;
import su.reddot.presentation.pdf.CommissionAgentReportDataItem;

import org.junit.jupiter.api.Test;

/**
 * Unit tests for {@link CommissionAgentReport}.
 */
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class CommissionAgentReportTest {

    /**
     * Test report for physical seller.
     */
    @Test
    public void testReportForPhysSeller() {
        testReport(true);
    }

    /**
     * Test report for legal entity seller.
     */
    @Test
    public void testReportForJurSeller() {
        testReport(false);
    }

    private static void testReport(final boolean isPhys) {
        final ZonedDateTime now = ZonedDateTime.now();
        final CommissionAgentReportData reportData = CommissionAgentReportData.builder()
                .agentReportId(1111)
                .agentReportName("Иванов И.И.")
                .inn("623000000000000")
                .contractDateTime(now.minusDays(1))
                .paymentDateTime(now)
                .physUser(isPhys)
                .deliveryCost(BigDecimal.ZERO)
                .cleaningAmount(BigDecimal.ZERO)
                .sellerReceivesAmount(BigDecimal.valueOf(15000))
                .items(ImmutableList.of(
                        CommissionAgentReportDataItem.builder()
                                .productId(2222)
                                .category("Ремень")
                                .brand("SAINT LAURENT")
                                .confirmedTime(now.minusDays(2))
                                .amountAfterDiscounts(BigDecimal.valueOf(20000))
                                .commission(BigDecimal.valueOf(0.25))
                                .commissionAmount(BigDecimal.valueOf(5000))
                                .build()))
                .build();

        final byte[] pdfContent = new CommissionAgentReport(reportData).generate();

        PdfTestUtils.validatePdf(pdfContent);
    }
}
