package ru.oskelly.tests.build.presentation.pdf;

import java.time.ZonedDateTime;

import org.springframework.beans.factory.annotation.Autowired;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.presentation.PDFUtils;

import org.junit.jupiter.api.Test;
import su.reddot.presentation.PDFUtils;

import java.time.ZonedDateTime;
import java.util.HashMap;

/**
 * Unit tests for {@link PDFUtils}.
 */
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class PDFUtilsTest extends AbstractSpringTest {
    @Autowired
    PDFUtils pdfUtils;

    /**
     * Test authenticity certificate.
     */
    @Test
    public void testCertificate() {
        final byte[] pdfContent = pdfUtils.getCertificate(
                "Сумка",
                "Новое,  с биркой",
                540738L,
                1410834178L,
                "fekla10000",
                "Бутик",
                ZonedDateTime.now(),
                new HashMap<>()
                );

        PdfTestUtils.validatePdf(pdfContent);
    }
}
