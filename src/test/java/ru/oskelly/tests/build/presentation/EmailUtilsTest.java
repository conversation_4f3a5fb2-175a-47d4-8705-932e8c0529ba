package ru.oskelly.tests.build.presentation;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationCredentialsNotFoundException;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.exception.EmailException;
import su.reddot.presentation.EmailUtils;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class EmailUtilsTest extends AbstractSpringTest {

    @Autowired
    private EmailUtils emailUtils;

    @Test
    public void validationFine(){
        String[] fineEmails = {"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
		        "<EMAIL>", "<EMAIL>", "<EMAIL>"};
        for(int i = 0; i < fineEmails.length; i++){
            emailUtils.validateEmail(fineEmails[i]);
        }
    }

    @Test
    public void validationNoDogFails(){
        Assertions.assertThrows(EmailException.class, () -> {
            emailUtils.validateEmail("Ivan");;
        });
    }

    @Test
    public void validationRussianLettersFails(){
        Assertions.assertThrows(EmailException.class, () -> {
            emailUtils.validateEmail("Иван");;
        });
    }

    @Test
    public void validationDomainlessFails(){
        Assertions.assertThrows(EmailException.class, () -> {
            emailUtils.validateEmail("Иван");;
        });
    }

    @Test
    public void validationRussianDomainFails(){
        Assertions.assertThrows(EmailException.class, () -> {
            emailUtils.validateEmail("Иван");;
        });
    }

    @Test
    public void validationSpaceFails(){
        Assertions.assertThrows(EmailException.class, () -> {
            emailUtils.validateEmail("ivan <EMAIL>");
        });
    }

    @Test
    public void validationAposFails(){
        Assertions.assertThrows(EmailException.class, () -> {
            emailUtils.validateEmail("'<EMAIL>");
        });
    }

    @Test
    public void validationCommaFails(){
        Assertions.assertThrows(EmailException.class, () -> {
            emailUtils.validateEmail("ivan,<EMAIL>");
        });
    }

    @Test
    public void validationRussianZoneFails(){
        Assertions.assertThrows(EmailException.class, () -> {
            emailUtils.validateEmail("ivan-belykh@ya.рф");
        });
    }

    @Test
    public void validationEmptyFails(){
        Assertions.assertThrows(EmailException.class, () -> {
            emailUtils.validateEmail("");
        });
    }

	@Test
	public void validationNullFails(){
        Assertions.assertThrows(EmailException.class, () -> {
            emailUtils.validateEmail(null);
        });
	}

	@Test
	public void validationSpaceOnlyFails(){
        Assertions.assertThrows(EmailException.class, () -> {
            emailUtils.validateEmail(" ");
        });
	}

	@Test
	public void validationTabOnlyFails(){
        Assertions.assertThrows(EmailException.class, () -> {
            emailUtils.validateEmail("    ");
        });
	}

	@Test
	public void validationTooLongFails(){
        Assertions.assertThrows(EmailException.class, () -> {
            emailUtils.validateEmail("<EMAIL>");
        });
	}
}