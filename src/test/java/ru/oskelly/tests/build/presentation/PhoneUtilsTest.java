package ru.oskelly.tests.build.presentation;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.beans.factory.annotation.Autowired;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.exception.PhoneException;
import su.reddot.presentation.PhoneUtils;

import java.util.stream.Stream;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class PhoneUtilsTest extends AbstractSpringTest {

    @Autowired
    private PhoneUtils phoneUtils;

    private static Stream<Arguments> providePhonesForFineValidation() {
        return Stream.of(
                Arguments.of("+79202341740"), // Россия
                Arguments.of("+998711205800"),
                Arguments.of("+74954552626"),
                Arguments.of("+998711205800"), // Узбекистан
                Arguments.of("+375172097101"), // Беларусь
                Arguments.of("+77272919600"), // Казахстан
                Arguments.of("+37411200400"), // Армения
                Arguments.of("+996312537777"), // Кыргызстан
                Arguments.of("+994124906000") // Азербайджан
        );
    }

    @MethodSource("providePhonesForFineValidation")
    @ParameterizedTest
    public void validationFine(String finePhone) {
            phoneUtils.validatePhone(finePhone);
    }

    @Test
    public void nullAndEmptyAreFine() {
        phoneUtils.validatePhone(null);
        phoneUtils.validatePhone("");
    }

    @Test
    public void validationNoPlusFails() {
        Assertions.assertThrows(PhoneException.class, () -> phoneUtils.validatePhone("79202341740"));
    }

    @Test
    public void validationNo7Fails() {
        Assertions.assertThrows(PhoneException.class, () -> phoneUtils.validatePhone("+89202341740"));
    }

    private static Stream<Arguments> provideWrongLengthPhones() {
        return Stream.of(
                Arguments.of("+79202341740000"),
                Arguments.of("+9987112058001"),
                Arguments.of("+3751720971077"),
                Arguments.of("+997727291960"),
                Arguments.of("+374112004000")
        );
    }

    @MethodSource("provideWrongLengthPhones")
    @ParameterizedTest()
    public void validationWrongLengthFails(String phone) {
        Assertions.assertThrows(PhoneException.class, () -> phoneUtils.validatePhone(phone));
    }

    @Test
    public void validationWrongSymbolsFails() {
        Assertions.assertThrows(PhoneException.class, () -> phoneUtils.validatePhone("+7920234K740"));
    }

    @Test
    public void validationFirstSpaceFails() {
        Assertions.assertThrows(PhoneException.class, () -> phoneUtils.validatePhone(" +79202341740"));
    }

    @Test
    public void validationLastSpaceFails() {
        Assertions.assertThrows(PhoneException.class, () -> phoneUtils.validatePhone("+79202341740 "));
    }

    @Test
    public void validationUnavailableCountryFails() {
        // французский номер не должен пройти валидацию
        Assertions.assertThrows(PhoneException.class, () -> phoneUtils.validatePhone("+33971284606"));
    }

    private static Stream<Arguments> providePhonesForFix() {
        return Stream.of(
                Arguments.of("7026007549", "77026007549"),
                Arguments.of("77026007549", "77026007549"),
                Arguments.of("9026007549", "79026007549")
        );
    }

    @ParameterizedTest
    @MethodSource("providePhonesForFix")
    public void fixPhoneFormatTest(String phone, String fixedPhone) {
        Assertions.assertEquals(fixedPhone, phoneUtils.expandPhoneToRuFormat(phone));
    }
}
