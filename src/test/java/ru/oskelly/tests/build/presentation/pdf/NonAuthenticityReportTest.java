package ru.oskelly.tests.build.presentation.pdf;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;

import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.presentation.PDFUtils;
import su.reddot.presentation.pdf.Expert;
import su.reddot.presentation.pdf.NonAuthenticityReport;
import su.reddot.presentation.pdf.NonAuthenticityReportData;

import org.junit.jupiter.api.Test;

/**
 * Unit tests for {@link NonAuthenticityReport}.
 */
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class NonAuthenticityReportTest {

    /**
     * Test normal path when all report data is populated.
     */
    @Test
    public void testNormalReport() {
        NonAuthenticityReportData reportData = NonAuthenticityReportData.builder()
                .orderId(1199924)
                .orderPositionId(1199930)
                .productId(1199920)
                .expertiseDate(LocalDate.now())
                .category("Ремень")
                .brand("SAINT LAURENT")
                .color("черный")
                .sizeType("Европейский")
                .size("80-85")
                .conclusion("Установлено полное несоответствие аналогичной модели бренда " +
                        "THE CAMBRIDGE SATCHEL COMPANY")
                .rejectionReason("Желтые пятна подмышками и на воротнике с внешней стороны спереди, " +
                        "огромное желтое пятно на одной оборке сзади, порвана одна часть оборки " +
                        "(разошлись швы), пуговица одна на рукаве не родная, есть зацепки")
                .expertList(Arrays.asList(
                        new Expert("Иванов А.А.", "Младший эксперт"),
                        new Expert("Петров Б.Б.", "Младший эксперт")))
                .build();

        byte[] pdfContent = new NonAuthenticityReport(reportData).generate();

        PdfTestUtils.validatePdf(pdfContent);
    }

    /**
     * Test the case when none of the non-required report fields is populated.
     */
    @Test
    public void testReportWithNullData() {
        NonAuthenticityReportData reportData = NonAuthenticityReportData.builder()
                .expertiseDate(LocalDate.now())
                .expertList(Collections.emptyList())
                .build();

        byte[] pdfContent = new NonAuthenticityReport(reportData).generate();

        PdfTestUtils.validatePdf(pdfContent);
    }

    /**
     * Test report with some special characters in the conclusion.
     */
    @Test
    public void testReportWithSpecialCharacters() {
        String string = PDFUtils.ALLOWED_CHARS + "\n";
        NonAuthenticityReportData reportData = NonAuthenticityReportData.builder()
                .expertiseDate(LocalDate.now())
                .category(string)
                .brand(string)
                .color(string)
                .sizeType(string)
                .size(string)
                .conclusion(string)
                .rejectionReason(string)
                .expertList(Arrays.asList(
                        new Expert(string, string),
                        new Expert(string, string)))
                .build();

        byte[] pdfContent = new NonAuthenticityReport(reportData).generate();

        PdfTestUtils.validatePdf(pdfContent);
    }
}
