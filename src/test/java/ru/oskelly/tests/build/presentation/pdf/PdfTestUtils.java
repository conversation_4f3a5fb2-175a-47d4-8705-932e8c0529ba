package ru.oskelly.tests.build.presentation.pdf;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.File;

import lombok.SneakyThrows;

import org.apache.commons.io.FileUtils;

public class PdfTestUtils {

    /**
     * Check that PDF document is valid.
     *
     * @param pdfContent PDF document bytes.
     */
    public static void validatePdf(byte[] pdfContent) {
        assertNotNull(pdfContent);
        assertTrue(pdfContent.length > 10);

        // Validate header. Expected header is %PDF.
        assertEquals(0x25, pdfContent[0]);
        assertEquals(0x50, pdfContent[1]);
        assertEquals(0x44, pdfContent[2]);
        assertEquals(0x46, pdfContent[3]);

        // Validate trailer. Expected trailers are:
        //   %%EOF
        //   %%EOF\n
        //   %%EOF\r\n
        final int length = pdfContent.length;
        int trailerStartPosition = length - 5;
        if (pdfContent[length - 1] == 0x0A) { // \n
            trailerStartPosition--;
            if (pdfContent[length - 2] == 0x0D) { // \r
                trailerStartPosition--;
            }
        }
        assertEquals(0x25, pdfContent[trailerStartPosition]);     // %
        assertEquals(0x25, pdfContent[trailerStartPosition + 1]); // %
        assertEquals(0x45, pdfContent[trailerStartPosition + 2]); // E
        assertEquals(0x4f, pdfContent[trailerStartPosition + 3]); // O
        assertEquals(0x46, pdfContent[trailerStartPosition + 4]); // F
    }

    /**
     * Save PDF document to local FS.
     *
     * @param pdfContent PDF document bytes.
     */
    @SneakyThrows
    public static void savePdf(byte[] pdfContent, String path) {
        FileUtils.writeByteArrayToFile(new File(path), pdfContent);
    }
}
