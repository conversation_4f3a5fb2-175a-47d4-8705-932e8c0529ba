package ru.oskelly.tests.build.presentation;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.exception.NicknameException;
import su.reddot.presentation.NicknameUtils;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class NicknameUtilsTest extends AbstractSpringTest {

    @Autowired
    private NicknameUtils nicknameUtils;

    @Test
    public void validationFine(){
        String[] fineNicknames = {"ivan", "belykh_1982", "123", "ivan1982", "ivan_belykh", "<PERSON>", "ivan-belykh"};
        for(int i = 0; i < fineNicknames.length; i++){
            nicknameUtils.validateNickname(fineNicknames[i]);
        }
    }

    @Test
    public void validationRussianLettersFails(){
        Assertions.assertThrows(NicknameException.class, () -> {
            nicknameUtils.validateNickname("Иван");
        });
    }

    @Test
    public void validationSpaceFails(){

        Assertions.assertThrows(NicknameException.class, () -> {
            nicknameUtils.validateNickname("Ivan Belykh");
        });
    }

    @Test
    public void validationPointFails(){
        Assertions.assertThrows(NicknameException.class, () -> {
            nicknameUtils.validateNickname("ivan.belykh");
        });
    }

    @Test
    public void validationQuoteFails(){
        Assertions.assertThrows(NicknameException.class, () -> {
            nicknameUtils.validateNickname("\"ivan\"");
        });
    }

    @Test
    public void validationAposFails(){
        Assertions.assertThrows(NicknameException.class, () -> {
            nicknameUtils.validateNickname("'ivan'");
        });
    }

    @Test
    public void validationCommaFails(){
        Assertions.assertThrows(NicknameException.class, () -> {
            nicknameUtils.validateNickname("ivan,belykh");
        });
    }

    @Test
    public void validationPlusFails(){
        Assertions.assertThrows(NicknameException.class, () -> {
            nicknameUtils.validateNickname("ivan+belykh");
        });

    }

    @Test
    public void validationEqualsFails(){
        Assertions.assertThrows(NicknameException.class, () -> {
            nicknameUtils.validateNickname("ivan=belykh");
        });
    }

    @Test
    public void validationColonFails(){
        Assertions.assertThrows(NicknameException.class, () -> {
            nicknameUtils.validateNickname("ivan:belykh");
        });
    }

    @Test
    public void validationBigLengthFails(){
        Assertions.assertThrows(NicknameException.class, () -> {
            nicknameUtils.validateNickname("0123456789_0123456789_0123456789_0123456789_0123456789");
        });
    }

    @Test
    public void validationNullFails(){
        Assertions.assertThrows(NicknameException.class, () -> {
            nicknameUtils.validateNickname(null);
        });
    }

    @Test
    public void validationEmptyFails(){
        Assertions.assertThrows(NicknameException.class, () -> {
            nicknameUtils.validateNickname("");
        });
    }

}
