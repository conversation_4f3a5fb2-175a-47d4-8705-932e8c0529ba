package ru.oskelly.tests.build.presentation.pdf.logistic;

import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.function.Function;

import ru.oskelly.tests.build.presentation.pdf.PdfTestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.presentation.pdf.logistic.LogisticActReport;
import su.reddot.presentation.pdf.logistic.LogisticActReportData;
import su.reddot.presentation.pdf.logistic.LogisticActReportDataItem;
import su.reddot.presentation.pdf.logistic.CseActReport;
import su.reddot.presentation.pdf.logistic.DalliActReport;

import org.junit.jupiter.api.Test;

/**
 * Unit tests for CSE and Dalli act report generation.
 */
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class LogisticActReportTest {

    /**
     * Test CSE act report generation.
     */
    @Test
    public void testCse() {
        testGenerateAct(CseActReport::new);
    }

    /**
     * Test Dalli act report generation.
     */
    @Test
    public void testDalli() {
        testGenerateAct(DalliActReport::new);
    }

    private void testGenerateAct(Function<LogisticActReportData, LogisticActReport> reportCreator) {
        ZonedDateTime now = ZonedDateTime.now();
        LogisticActReportDataItem item = LogisticActReportDataItem.builder()
                .waybillExternalSystemId("1284399-OSKELLY-O2B")
                .waybillOrderExternalSystemId("496-A025910-00000626")
                .waybillOrderClientSystemId("1284399-OSKELLY-O2B")
                .waybillDateTime(now)
                .declaredValue(10000)
                .quantity(1)
                .build();
        for (int i = 0; i <= 5; i++) {
            LogisticActReportData reportData = LogisticActReportData.builder()
                    .actDateTime(now)
                    .items(Collections.nCopies(i * 20 + 1, item))
                    .build();

            byte[] pdfContent = reportCreator.apply(reportData).generate();

            PdfTestUtils.validatePdf(pdfContent);
        }
    }
}
