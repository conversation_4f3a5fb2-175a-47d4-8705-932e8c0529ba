package ru.oskelly.tests.build.presentation.pdf.act;

import java.time.LocalDate;

import com.google.common.collect.ImmutableList;

import ru.oskelly.tests.build.presentation.pdf.PdfTestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.presentation.PDFUtils;
import su.reddot.presentation.pdf.act.ActOfAcceptanceReport;
import su.reddot.presentation.pdf.act.ActOfReturnReport;
import su.reddot.presentation.pdf.act.ActReportData;

import org.junit.jupiter.api.Test;

/**
 * Unit tests for {@link ActOfAcceptanceReport} and {@link ActOfReturnReport}.
 */
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class ActReportTest {

    /**
     * Test normal path when all report data is populated.
     */
    @Test
    public void testNormalReport() {
        ActReportData reportData = ActReportData.builder()
                .date(LocalDate.now())
                .sellerFullName("Иванов Иван Иванович")
                .sellerAbbreviatedName("Иванов И.И.")
                .products(ImmutableList.of("Ремень SAINT LAURENT", "Сумка LOUIS VUITTON"))
                .orderIds(ImmutableList.of("1", "2"))
                .build();

        byte[] actOfAcceptance = new ActOfAcceptanceReport(reportData).generate();
        PdfTestUtils.validatePdf(actOfAcceptance);

        byte[] actOfReturn = new ActOfReturnReport(reportData).generate();
        PdfTestUtils.validatePdf(actOfReturn);
    }

    /**
     * Test report with some special characters in customer name or product description.
     */
    @Test
    public void testReportWithSpecialCharacters() {
        String string = PDFUtils.ALLOWED_CHARS + "\n";
        ActReportData reportData = ActReportData.builder()
                .date(LocalDate.now())
                .sellerFullName(string)
                .sellerAbbreviatedName(string)
                .products(ImmutableList.of(string, string))
                .orderIds(ImmutableList.of("1", "2"))
                .build();

        byte[] actOfAcceptance = new ActOfAcceptanceReport(reportData).generate();
        PdfTestUtils.validatePdf(actOfAcceptance);

        byte[] actOfReturn = new ActOfReturnReport(reportData).generate();
        PdfTestUtils.validatePdf(actOfReturn);
    }
}
