package ru.oskelly.tests;

import lombok.Setter;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.annotation.DirtiesContext;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.flagr.FlagrClient;
import su.reddot.infrastructure.flagr.response.FlagrBatchEvaluationResponse;
import su.reddot.infrastructure.sender.NotificationSender;

import java.util.Collections;


@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
@SpringBootTest(classes = {OskellyApplication.class},
		webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
public abstract class AbstractSpringTest extends SpringTestWithDb {

	@Setter
	@Autowired
	protected JdbcTemplate jdbcTemplate;

	@MockBean
	protected NotificationSender notificationSender;

	@MockBean
	protected FlagrClient flagrClient;

	@BeforeEach
	protected void initMocks() {
		Mockito.when(flagrClient.batchEvaluation(Mockito.any()))
				.thenReturn(new FlagrBatchEvaluationResponse(Collections.emptyList()));
	}

}
