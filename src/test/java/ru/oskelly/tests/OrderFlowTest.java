package ru.oskelly.tests;

import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import ru.oskelly.tests.pr.common.bonuses.BonusesServiceTestConfiguration;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestFixtures;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestUtils;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.oskelly.orderprocessing.internal.web.client.OrderMobileApi;

@ContextConfiguration(classes = {
        OrderFlowTestUtils.class,
        OrderFlowTestFixtures.class,
        BonusesServiceTestConfiguration.class
})
@SpringBootTest(classes = {OskellyApplication.class},
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
)
public class OrderFlowTest extends SpringTestWithDb {

    @MockBean
    protected OrderMobileApi orderMobileApi;

}