package ru.oskelly.tests;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;

import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

public class TestJsonDataUtils {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @SneakyThrows
    public static String readJsonTestData(AbstractSpringTest testCase, String fileName) {
        String reportResPath = "json/" + testCase.getClass().getSimpleName() + "/" + testCase.testName + ".json";
        URL reportResData = testCase.getClass().getClassLoader().getResource(reportResPath);
        return Objects.isNull(reportResData) ? null : IOUtils.toString(reportResData, StandardCharsets.UTF_8);
    }

    @SneakyThrows
    public static <T> T readJsonTestData(AbstractSpringTest testCase, String fileName, TypeReference<T> typeReference) {
        return objectMapper.readValue(readJsonTestData(testCase, fileName), typeReference);
    }

    @SneakyThrows
    public static String objectToJson(Object object) {
        return objectMapper.writeValueAsString(object);
    }

}
