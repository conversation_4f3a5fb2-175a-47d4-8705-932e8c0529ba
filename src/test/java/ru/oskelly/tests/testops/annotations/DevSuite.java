package ru.oskelly.tests.testops.annotations;

import io.qameta.allure.LabelAnnotation;
import io.qameta.allure.Stories;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Inherited
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.TYPE})
@Repeatable(DevSuites.class)
@LabelAnnotation(name = "devtestsuite")
public @interface DevSuite {

    String value();
}
