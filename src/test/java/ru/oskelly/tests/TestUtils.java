package ru.oskelly.tests;

import com.google.common.base.Strings;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.DefaultUriBuilderFactory;
import su.reddot.domain.dao.activity.ActivityTestRepository;
import su.reddot.domain.dao.notification.NotificationRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.activity.Activity;
import su.reddot.domain.model.device.DeviceDtype;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.activity.ActivityService;
import su.reddot.domain.service.notification.NotificationService;
import su.reddot.infrastructure.util.Utils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client.*;

@Slf4j
public class TestUtils {
	private static TestRestTemplate testRestTemplate;

	public static String getFieldName(Method getter){
		String name = getter.getName();
		if(name.startsWith("get")) name = name.substring(3);
		else if(name.startsWith("is")) name = name.substring(2);
		return StringUtils.uncapitalize(name);
	}
	public static final Map<String, Object> getObjectFields(Object object){
		Map<String, Object> result = new HashMap<>();
		final List<Method> methods = Arrays.asList(object.getClass().getMethods());
		//final List<Method> baseObjectMethods = Arrays.asList(Object.class.getMethods());
		final List<String> baseObjectMethodNames = Arrays.asList(Object.class.getMethods()).stream().map(m -> m.getName()).distinct().collect(Collectors.toList());
		methods.forEach(m -> {
			if(baseObjectMethodNames.contains(m.getName())) return; //continue
			if(m.getReturnType() == Void.class) return; //continue
			if(m.getParameterCount() != 0) return; //continue
			try {
				result.put(getFieldName(m), m.invoke(object));
			} catch (IllegalAccessException|InvocationTargetException e) {
				log.debug(e.getMessage());
			}
		});

		return result;
	}
	private static Object getObjectFieldValueForHttpParam(Object o){
		if(o instanceof FileSystemResource){
			return o;
		}
		else if(o instanceof MultipartFile){
			String path = ((MultipartFile) o).getName();
			FileSystemResource fsr = new FileSystemResource(path);
			return fsr;
		}
		return o.toString();
	}
	public static final MultiValueMap<String, Object> getMultivalueMapWithObjectFields(Object object){
		if(object == null) return null;
		MultiValueMap<String, Object> result = new LinkedMultiValueMap<>();
		Map<String, Object> field2value = getObjectFields(object);
		field2value.forEach((f, v) -> {
			if(v == null) return; //continue
			if(v instanceof List){
				for(Object o : (List) v){
					result.add(f, getObjectFieldValueForHttpParam(o));
				}
				return; //continue
			}
			result.add(f, getObjectFieldValueForHttpParam(v));
		});
		return result;
	}
	public static MultiValueMap<String, String> getOneParamAsMultiValueMap(@NonNull String key, @NonNull Object value){
		MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
		params.add(key, value.toString());
		return params;
	}
	public static Map<String, String> getOneParamAsMap(@NonNull String key, @NonNull Object value){
		Map<String, String> params = new HashMap<>();
		params.put(key, value.toString());
		return params;
	}
	public static final HttpEntity getHttpEntityWithObjectFields(Object object, MultiValueMap<String, String> headers){
		return new HttpEntity<>(getMultivalueMapWithObjectFields(object), headers);
	}
	public static final String getApiHashedPassword(String password, String email){
		return Utils.getApiHashedPassword(password, email.toLowerCase());
	}
	public static String getUriWithParams(String url, Map<String, String> params){
		return Utils.getUriWithParams(url, params);
	}
	public static boolean hasError(ResponseEntity<?> response){
		return !response.getStatusCode().is2xxSuccessful();
	}
	/**
	 * Проверяет наличие указанной ошибки в теле ответа
	 * @param response
	 * @param clazz
	 * @return
	 */
	public static boolean hasError(String response, Class clazz){
		return response.contains(clazz.getName());
	}
	public static void assertStringContainsAllWords(String str, String... words){
		for(String word : words){
			assertStringContains(str, word);
		}
	}
	public static void assertStringContains(String str, String word){
		boolean contains = str.contains(word);
		if(!contains) throw new AssertionError(String.format("Строка \"%s\" не содержит \"%s\"", str, word));
	}
	public static void assertListContains(List lst, Object obj){
		boolean contains = lst.contains(obj);
		if(!contains) throw new AssertionError(String.format("Список %s не содержит %s", lst, obj));
	}
	public static void assertListDoesntContain(List lst, Object obj){
		boolean contains = lst.contains(obj);
		if(contains) throw new AssertionError(String.format("Список %s содержит %s", lst, obj));
	}
	public static TestRestTemplate getTestRestTemplate(){
		if(testRestTemplate == null) {
			testRestTemplate = createTestRestTemplate();
		}
		return testRestTemplate;
	}

	/**
	 * Creates a new instance of {@code TestRestTemplate}.
	 *
	 * @return New instance of {@code TestRestTemplate}.
	 */
	public static TestRestTemplate createTestRestTemplate() {
		final TestRestTemplate testRestTemplate = new TestRestTemplate();
		testRestTemplate.getRestTemplate().setInterceptors(
				Collections.singletonList((request, body, execution) -> {
					//По умолчанию в accept прописывается как JSON, так и XML. В итоге сервер возвращает ответ в формате XML,
					//который нам не подходит. Поэтому мы трем этот заголовок и посылаем его пустым, ожидая ответ по умолчанию в формате JSON
					request.getHeaders().remove("Accept");
					return execution.execute(request, body);
				}));
		DefaultUriBuilderFactory defaultUriBuilderFactory = new DefaultUriBuilderFactory();		// To avoid double encoding with Utils.getUriWithParams (UriComponentsBuilder)
		defaultUriBuilderFactory.setEncodingMode(DefaultUriBuilderFactory.EncodingMode.NONE);	// and later with own RestTemplate encoder
		testRestTemplate.getRestTemplate().setUriTemplateHandler(defaultUriBuilderFactory);
		return testRestTemplate;
	}

	/**
	 * Проверяет поля activity на соответствие HTTP заголовкам тестового RESR-клиента
	 * @param activity
	 */
	public static void checkTestActivity(Activity activity){
		assertEquals(activity.getUserAgent(), DeviceDtype.OskellyApiTestDevice.name());
		assertEquals(activity.getDevice().getToken(), HEADER_PUSH_TOKEN);
		assertEquals(activity.getIdfa(), HEADER_IDFA);
		assertEquals(activity.getIdfv(), HEADER_IDFV);
		assertEquals(activity.getAndroidId(), HEADER_ANDROID_ID);
		assertEquals(activity.getAdvertisingId(), HEADER_ADVERTISING_ID);
		assertEquals(activity.getAppsflyerId(), HEADER_APPSFLYER_ID);
		assertEquals(activity.getMac(), HEADER_MAC);
		assertEquals(activity.getDevice().getMindboxClientUuid(), HEADER_MINDBOX_CLIENT_UUID);
	}

	public static MultipartFile getFileAsMultipartFile(@NonNull String file){
		URL url = TestUtils.class.getClassLoader().getResource(file);
		try {
			return new MockMultipartFile(url.getPath(), url.openStream());
		}
		catch (IOException e){
			e.printStackTrace();
		}
		return null;
	}
	public static List<MultipartFile> getFilesAsMultipartFilesList(@NonNull String... files){
		List<MultipartFile> result = new ArrayList<>();
		for(String file : files){
			MultipartFile multipartFile = getFileAsMultipartFile(file);
			if(multipartFile != null) result.add(multipartFile);
		}
		return result;
	}
	public static BufferedImage loadImage(@NonNull File imageFile){
		if(!imageFile.exists() || !imageFile.isFile()) return null;
		try {
			InputStream is = new FileInputStream(imageFile);
			BufferedImage image = ImageIO.read(is);
			is.close();
			return image;
		}
		catch (IOException e){
			e.printStackTrace();
		}
		return null;
	}
	public static byte[] loadFile(@NonNull File file){
		if(!file.exists() || !file.isFile()) return null;
		try {
			return Files.readAllBytes(file.toPath());
		}
		catch (IOException e){
			e.printStackTrace();
		}
		return null;
	}
	public static void download(@NonNull URL fromURL, @NonNull File toFile){
		try{
			Utils.download(fromURL, toFile);
		}
		catch (IOException e) {
			e.printStackTrace();
		}
	}
	public static BufferedImage loadImageFromUrl(@NonNull URL url, @NonNull String tmpDirPath){
		String tmpFilename = tmpDirPath + UUID.randomUUID();
		File tmpFile = new File(tmpFilename);
		download(url, tmpFile);
		return loadImage(tmpFile);
	}
	public static byte[] loadFileFromUrl(@NonNull URL url, @NonNull String tmpDirPath){
		String tmpFilename = tmpDirPath + UUID.randomUUID();
		File tmpFile = new File(tmpFilename);
		download(url, tmpFile);
		return loadFile(tmpFile);
	}
	public static BufferedImage loadImageFromUrl(@NonNull String urlPath, @NonNull String tmpDirPath){
		try {
			return loadImageFromUrl(new URL(urlPath), tmpDirPath);
		} catch (MalformedURLException e) {
			e.printStackTrace();
		}
		return null;
	}
	public static byte[] loadFileFromUrl(@NonNull String urlPath, @NonNull String tmpDirPath){
		try {
			return loadFileFromUrl(new URL(urlPath), tmpDirPath);
		} catch (MalformedURLException e) {
			e.printStackTrace();
		}
		return null;
	}
	public static void assertImageAvailable(@NonNull String urlPath, @NonNull String tmpDirPath){
		BufferedImage image = loadImageFromUrl(urlPath, tmpDirPath);
		assertTrue(image.getWidth() > 0);
		assertTrue(image.getHeight() > 0);
	}
	public static void assertFileAvailable(@NonNull String urlPath, @NonNull String tmpDirPath){
		byte[] fileContents = loadFileFromUrl(urlPath, tmpDirPath);
		assertNotNull(fileContents);
		assertTrue(fileContents.length > 0);
	}
	public static String getUrlPath(String serverUrl, String subUrl){
		if(subUrl.startsWith("http")) return subUrl;
		return serverUrl + subUrl;
	}

	public static void sleep(int seconds){
		Utils.sleep(seconds);
	}

	public static Notification getLastNotification(User user, String guestToken, Class<? extends Notification> clazz, boolean needAction, boolean actionCompleted, NotificationService notificationService){
		return getLastNotifications(1, user, guestToken, clazz, needAction, actionCompleted, notificationService).get(0);
	}

	public static Notification getLastNotification(String guestToken, Class<? extends Notification> clazz, boolean needAction, boolean actionCompleted, NotificationService notificationService){
		return getLastNotifications(1, null, guestToken, clazz, needAction, actionCompleted, notificationService).get(0);
	}

	public static Notification getLastNotification(User user, Class<? extends Notification> clazz, boolean needAction, boolean actionCompleted, NotificationService notificationService){
		return getLastNotifications(1, user, null, clazz, needAction, actionCompleted, notificationService).get(0);
	}

	public static List<Notification> getLastNotifications(int count, User user, Class<? extends Notification> clazz, boolean needAction, boolean actionCompleted, NotificationService notificationService) {
		return getLastNotifications(count, user, null, clazz, needAction, actionCompleted, notificationService);
	}

	public static List<Notification> getLastNotifications(int count, String guestToken, Class<? extends Notification> clazz, boolean needAction, boolean actionCompleted, NotificationService notificationService) {
		return getLastNotifications(count, null, guestToken, clazz, needAction, actionCompleted, notificationService);
	}

	public static List<Notification> getLastNotifications(int count, User user, String guestToken, Class<? extends Notification> clazz, boolean needAction, boolean actionCompleted, NotificationService notificationService){
		TestUtils.sleep(2);//Создание уведомления занимает некоторое время
		List<Notification> notifications = notificationService.getRawNotifications(100, user, guestToken);
		List<Notification> result = new ArrayList<>();
		int found = 0;
		for(Notification n : notifications){
			if(n.getClass() != clazz) continue;
			if(n.isNeedAction() != needAction) continue;
			if(n.isActionCompleted() != actionCompleted) continue;
			User targetUser = n.getTargetUser().orElse(null);
			if((targetUser != null && targetUser.getId().equals(user.getId()) || (guestToken != null && guestToken.equals(n.getGuestToken())))){
				//found!
				result.add(n);
				found ++;
				if(found == count) break;
			}
		}
		assertEquals(count, result.size());
		for(Notification n : result) {
			assertTrue(ZonedDateTime.now().minusMinutes(30).isBefore(n.getCreateTime()));
		}
		return result;
	}

	public static int getNotificationsCount(User user, String guestToken, Class<? extends Notification> clazz, boolean needAction, boolean actionCompleted, NotificationService notificationService){
		TestUtils.sleep(2);//Создание уведомления занимает некоторое время
		List<Notification> notifications = notificationService.getRawNotifications(100, user, guestToken);
		List<Notification> result = new ArrayList<>();
		int found = 0;
		for(Notification n : notifications){
			if(n.getClass() != clazz) continue;
			if(n.isNeedAction() != needAction) continue;
			if(n.isActionCompleted() != actionCompleted) continue;
			User targetUser = n.getTargetUser().orElse(null);
			if((targetUser != null && targetUser.getId().equals(user.getId()) || (guestToken != null && guestToken.equals(n.getGuestToken())))){
				//found!
				result.add(n);
				found ++;
			}
		}
		return found;
	}

	public static void assertNoNotificationsCount(User user, String guestToken, Class<? extends Notification> clazz, boolean needAction, boolean actionCompleted, NotificationService notificationService){
		assertEquals(0, getNotificationsCount(user, guestToken, clazz, needAction, actionCompleted, notificationService));
	}

	/**
	 * Состарить активности пользователя или гостя на несколько часов.
	 * Касается только тех активностей, которые моложе hours часов (совсем старые не трогаются)
	 * @param user
	 * @param guestToken
	 * @param hours
	 */
	public static void oldActivitiesUpdateTime(User user, String guestToken, int hours, ActivityTestRepository activityTestRepository){
		ZonedDateTime timeLimit = ZonedDateTime.now().minusHours(hours);
		List<Activity> allActivities = activityTestRepository.findAllActivities(user == null ? 0L : user.getId(),
				guestToken == null ? "" : guestToken);
		for(Activity activity : allActivities){
			if(activity.getUpdateTime().isBefore(timeLimit)) break; //Порядок от новых к старым, так что можно выходить из цикла совсем
			activity.setUpdateTime(activity.getUpdateTime().minusHours(hours));
			activityTestRepository.save(activity);
		}
	}

	/**
	 * Удалить все активности пользователя и/или гостя
	 */
	public static void deleteActivities(Long userId, String guestToken, ActivityTestRepository<Activity> activityTestRepository) {
		activityTestRepository.deleteByUserIdOrGuestToken(userId, guestToken);
	}

	/**
	 * Удалить уведомления пользователя заданного типа
	 */
	public static void deleteUserNotifications(Long userId, String dtype, NotificationRepository notificationRepository){
		notificationRepository.deleteAll(notificationRepository.findAllByUserAndDtype(userId, dtype));
	}

	/**
	 * Удалить все уведомления пользователя
	 */
	public static void deleteUserNotifications(Long userId, NotificationRepository notificationRepository){
		notificationRepository.deleteAll(notificationRepository.findAllByUser(userId));
	}

	/**
	 * Устанавливает нового продавца на наказ, меняя владельца соответствующих товаров
	 * @param order
	 * @param seller
	 */
	public static void setOrderSeller(Order order, User seller, ProductRepository productRepository){
		for(OrderPosition orderPosition : order.getOrderPositions()){
			Product product = orderPosition.getProductItem().getProduct();
			product.setSeller(seller);
			productRepository.saveAndFlush(product);
		}
	}

	/**
	 * Устанавливает статус для товара с указанным id
	 * @param productId
	 * @param productState
	 * @param productRepository
	 */
	public static void setProductState(Long productId, ProductState productState, ProductRepository productRepository){
		Product product = productRepository.getOne(productId);
		product.setProductState(productState);
		productRepository.saveAndFlush(product);
	}

	/**
	 * Удаляет все заказы покупателя
	 */
	public static void deleteBuyerOrders(User buyer, OrderRepository orderRepository){
		orderRepository.deleteAll(orderRepository.findAllByBuyerOrderByIdDesc(buyer));
	}

	/**
	 * Удаляет все заказы продавца
	 */
	public static void deleteSellerOrders(User seller, OrderState orderState, OrderRepository orderRepository){
		orderRepository.deleteAll(orderRepository.getSellerOrders(100000, orderState, seller.getId()));
	}

	/**
	 * Возвращает последнюю активность пользователя по типу и id связанного объекта
	 * @param activityService
	 * @param dtype
	 * @param userId
	 * @param guestToken
	 * @param objectId
	 * @return
	 */
	public static Activity getLastActivityChecked(ActivityService activityService, Class<? extends Activity> dtype, Long userId, String guestToken, Long objectId){
		if(dtype == null || userId == null || objectId == null) return null;
		//Сначала нужно сбросить содержимое очереди в базу
		activityService.saveAllFromQueue();
		Activity result = activityService.getLastActivity(userId, guestToken, objectId, dtype);
		//Активность существует
		assertNotNull(result);
		//И свежая
		assertTrue(ZonedDateTime.now().minusMinutes(3).isBefore(result.getUpdateTime()));
		return result;
	}

	/**
	 * Compares two images pixel by pixel.
	 *
	 * @param imgA the first image.
	 * @param imgB the second image.
	 * @return whether the images are both the same or not.
	 */
	public static void assertSameImages(BufferedImage imgA, BufferedImage imgB) {
		// The images must be the same size.
		assertTrue(imgA.getWidth() == imgB.getWidth() && imgA.getHeight() == imgB.getHeight());

		int width  = imgA.getWidth();
		int height = imgA.getHeight();

		// Loop over every pixel.
		for (int y = 0; y < height; y++) {
			for (int x = 0; x < width; x++) {
				// Compare the pixels for equality.
				assertEquals(imgA.getRGB(x, y), imgB.getRGB(x, y));
			}
		}
	}

	//Дата свежая (не более 3-х минутной давности), не выходящая в другой часовой пояс (н.п. не из будущего, на 3 часа позже)
	public static boolean dateIsFreshAndInCorrectOffset(Date date){
		LocalDateTime localDateTime = date.toInstant()
				.atZone(ZoneId.systemDefault())
				.toLocalDateTime();
		return dateIsFreshAndInCorrectOffset(localDateTime);
	}

	//Дата свежая (не более 3-х минутной давности), не выходящая в другой часовой пояс (н.п. не из будущего, на 3 часа позже)
	public static boolean dateIsFreshAndInCorrectOffset(LocalDateTime localDateTime){
		LocalDateTime end = LocalDateTime.now();
		LocalDateTime start = end.minusMinutes(3);
		return localDateTime.isAfter(start) && localDateTime.isBefore(end);
	}

	public static Set<String> getAllCurrencies() {
		Set<String> codesSet = new HashSet<>();
		for (Locale locale : Locale.getAvailableLocales()) {
			if (Strings.isNullOrEmpty(locale.getCountry())) {
				continue;
			}
			Currency currency = Currency.getInstance(locale);
			if (Objects.nonNull(currency)) {
				codesSet.add(currency.getCurrencyCode());
			}
		}
		return codesSet;
	}

}
