package ru.oskelly.tests;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.TestInfo;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import su.reddot.infrastructure.configuration.OskellyApplication;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

@SpringBootTest(classes = {OskellyApplication.class},
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
)
@ExtendWith(SpringExtension.class)
@ActiveProfiles(profiles = SpringTestWithCtxCounterIT.testProfiles)
public class SpringTestWithCtxCounterIT {

    public static final String testProfiles = "base,master,local,test,testprops,testpropslocal";

    @Autowired
    private ApplicationContext applicationContext;

    private static final Map<Integer, String> ctxMap = new HashMap<>();

    private static final AtomicInteger ctxLimit = new AtomicInteger(100);

    public String testName;

    @BeforeEach
    void setUp(TestInfo testInfo) {
        testName = testInfo.getTestMethod().map(Method::getName).orElse(null);
        ctxMap.put(applicationContext.hashCode(), applicationContext.getDisplayName());
        if (ctxMap.size() > ctxLimit.intValue()) {
            throw new IllegalStateException(String.format("Test context limit: %d > %d", ctxMap.size(), ctxLimit.intValue()));
        }
    }

    protected void requestMoreCtx() {
        ctxLimit.incrementAndGet();
    }

}