package ru.oskelly.tests.build_01.domain.service.user;

/*
 * Created by <PERSON>
 */

import lombok.Getter;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.FanoutExchange;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.exception.UserNotFoundException;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.configuration.AMQPConfiguration;
import su.reddot.infrastructure.configuration.OskellyApplication;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {OskellyApplication.class, ChangeUserEventTest.ChangeUserEventTestConfiguration.class})
@ActiveProfiles(profiles = AbstractSpringTest.testProfiles)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class ChangeUserEventTest {
    
    @Value("${test.api.user-id}")
    private Long userId;

    private static final String queueName = "user.action.event.test-queue";

    //Последнее полученное событие для проверки в тесте
    private static ChangeUserEventTest.ChangeUserEvent lastUserChangeEvent;

    @RabbitListener(queues = queueName)
    public void changeTagStatusListener(ChangeUserEventTest.ChangeUserEvent event) {
        lastUserChangeEvent = event;
    }

    @Autowired
    private UserService userService;

    @Test
    public void changeUserEventTest() {
        User user = userService.getUserById(userId).orElseThrow(() -> new UserNotFoundException(String.valueOf(userId)));
        //Создаем событие, чтобы проверить, дойдет ли оно до нас
        userService.onUserChanged(user.getId());

        TestUtils.sleep(2);

        //События приходили
        assertNotNull(lastUserChangeEvent);

        //ID пользователя в событии совпадает
        assertEquals(user.getId(), lastUserChangeEvent.getId());

        //никнейм пользователя в событии совпадает
        assertEquals(user.getNickname(), lastUserChangeEvent.getNickName());
    }

    @Getter
    static class ChangeUserEvent{
        private Long id;
        private String avatarPath;
        private String nickName;
        private Boolean isPro;
        private Boolean isTrusted;
    }

    @Configuration
    static class ChangeUserEventTestConfiguration{
        //Создаем новую тестовую очередь
        @Bean(name = queueName)
        public org.springframework.amqp.core.Queue userEventTestQueue() {
            return new org.springframework.amqp.core.Queue(queueName, false, true, true);
        }
        //Банндим очередь к эксченджу
        @Bean
        Binding productEventTestBinding(@Qualifier(AMQPConfiguration.CHANGE_USER_EVENT_FANOUT_EXCHANGE_NAME) FanoutExchange exchange,
                                        @Qualifier(queueName) org.springframework.amqp.core.Queue queue) {
            return BindingBuilder.bind(queue).to(exchange);
        }
    }
}