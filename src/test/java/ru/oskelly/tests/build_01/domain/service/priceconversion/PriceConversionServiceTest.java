package ru.oskelly.tests.build_01.domain.service.priceconversion;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.model.address.Currency;
import su.reddot.domain.model.currency.CurrencyInfo;
import su.reddot.domain.service.currency.CurrencyConverterFactory;
import su.reddot.domain.service.currency.CurrencyService;
import su.reddot.domain.service.currency.DefaultCurrencyConverter;
import su.reddot.domain.service.dto.CurrencyDTO;
import su.reddot.domain.service.priceconversion.PriceConversionRequestDTO;
import su.reddot.domain.service.priceconversion.PriceConversionResponseDTO;
import su.reddot.domain.service.priceconversion.PriceConversionService;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;


@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class PriceConversionServiceTest extends AbstractSpringTest {

    @Autowired
    private PriceConversionService priceConversionService;
    @Autowired
    private CurrencyService currencyService;
    @MockBean
    private CurrencyConverterFactory currencyConverterFactory;

    @Test
    public void _01_convertProductPrices_fromNotBaseCurrency() {
        // prepare
        Currency currency = currencyService.findByIsoNumber(CurrencyInfo.AED.getIsoNumber());
        CurrencyDTO currencyDto = currencyService.getCurrencyDTOByIDCached(currency.getId());

        BigDecimal effectiveRate = BigDecimal.valueOf(5);
        Mockito.when(currencyConverterFactory.createByCurrencyCodeToBase(Mockito.any()))
                .thenReturn(new DefaultCurrencyConverter(currencyDto, effectiveRate));

        String correlationIdOne = "correlationIdOne";
        String correlationIdTwo = "correlationIdTwo";

        PriceConversionRequestDTO input = PriceConversionRequestDTO.builder()
                .currencyId(currency.getId())
                .items(
                        Arrays.asList(
                                PriceConversionRequestDTO.PriceConversionItemDTO
                                        .builder()
                                        .correlationId(correlationIdOne)
                                        .prices(Arrays.asList(new BigDecimal("10.00"), new BigDecimal("20.1")))
                                        .build(),
                                PriceConversionRequestDTO.PriceConversionItemDTO
                                        .builder()
                                        .correlationId(correlationIdTwo)
                                        .prices(Arrays.asList(new BigDecimal("30.12"), new BigDecimal("40.12345")))
                                        .build()
                        )
                )
                .build();

        // action
        PriceConversionResponseDTO output = priceConversionService.convertProductPrices(input);

        // check
        assertEquals(input.getCurrencyId(), output.getCurrencyId());
        assertEquals(input.getItems().size(), output.getItems().size());

        output.getItems().stream()
                .filter(it -> it.getCorrelationId().equals(correlationIdOne))
                .findFirst()
                .map(item -> {
                    List<BigDecimal> expectedPrices = Arrays.asList(new BigDecimal("50.00"), new BigDecimal("100.50"));
                    Assertions.assertEquals(expectedPrices, item.getPrices());
                    return item;
                })
                .orElseGet(Assertions::fail);

        output.getItems().stream()
                .filter(it -> it.getCorrelationId().equals(correlationIdTwo))
                .findFirst()
                .map(item -> {
                    List<BigDecimal> expectedPrices = Arrays.asList(new BigDecimal("150.60"), new BigDecimal("200.62"));
                    Assertions.assertEquals(expectedPrices, item.getPrices());
                    return item;
                })
                .orElseGet(Assertions::fail);
    }

    @Test
    public void _02_convertProductPrices_fromBaseCurrency() {
        // prepare
        Currency currency = currencyService.getBaseCurrency();
        CurrencyDTO currencyDto = currencyService.getCurrencyDTOByIDCached(currency.getId());

        BigDecimal effectiveRate = BigDecimal.valueOf(5);
        Mockito.when(currencyConverterFactory.createByCurrencyCodeToBase(Mockito.any()))
                .thenReturn(new DefaultCurrencyConverter(currencyDto, effectiveRate));

        String correlationIdOne = "correlationIdOne";
        String correlationIdTwo = "correlationIdTwo";

        PriceConversionRequestDTO input = PriceConversionRequestDTO.builder()
                .currencyId(currency.getId())
                .items(
                        Arrays.asList(
                                PriceConversionRequestDTO.PriceConversionItemDTO
                                        .builder()
                                        .correlationId(correlationIdOne)
                                        .prices(Arrays.asList(new BigDecimal("10.00"), new BigDecimal("20.1")))
                                        .build(),
                                PriceConversionRequestDTO.PriceConversionItemDTO
                                        .builder()
                                        .correlationId(correlationIdTwo)
                                        .prices(Arrays.asList(new BigDecimal("30.12"), new BigDecimal("40.12345")))
                                        .build()
                        )
                )
                .build();

        // action
        PriceConversionResponseDTO output = priceConversionService.convertProductPrices(input);

        // check
        assertEquals(input.getCurrencyId(), output.getCurrencyId());
        assertEquals(input.getItems().size(), output.getItems().size());

        output.getItems().stream()
                .filter(it -> it.getCorrelationId().equals(correlationIdOne))
                .findFirst()
                .map(item -> {
                    List<BigDecimal> expectedPrices = Arrays.asList(new BigDecimal("10.00"), new BigDecimal("20.1"));
                    Assertions.assertEquals(expectedPrices, item.getPrices());
                    return item;
                })
                .orElseGet(Assertions::fail);

        output.getItems().stream()
                .filter(it -> it.getCorrelationId().equals(correlationIdTwo))
                .findFirst()
                .map(item -> {
                    List<BigDecimal> expectedPrices = Arrays.asList(new BigDecimal("30.12"), new BigDecimal("40.12345"));
                    Assertions.assertEquals(expectedPrices, item.getPrices());
                    return item;
                })
                .orElseGet(Assertions::fail);
    }

}
