package ru.oskelly.tests.build_01.domain.service.adminpanel.orders;

import lombok.SneakyThrows;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.test.util.ReflectionTestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.expertise.ExpertiseRepository;
import su.reddot.domain.exception.OrderException;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.adminpanel.orders.AdminConfirmationService;
import su.reddot.domain.service.adminpanel.orders.impl.DefaultAdminConfirmationService;
import su.reddot.domain.service.duty.DutyService;
import su.reddot.domain.service.expertise.DefaultExpertiseService;
import su.reddot.domain.service.expertise.ExpertiseService;
import su.reddot.domain.service.integration.orderprocessing.OrderProcessingService;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.orderPosition.OrderPositionService;
import su.reddot.domain.service.setting.FeatureFlagsSettingService;
import su.reddot.infrastructure.datamatrix.DatamatrixUtils;

import java.time.Clock;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static su.reddot.domain.service.setting.FeatureFlagsSettingService.ConfigFlag.ENABLE_MARKING_CODE;

/**
 * Unit test for {@link DefaultAdminConfirmationService#setExpertisePassed(Long)} method.
 */
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class DefaultAdminConfirmationServiceSetExpertisePassedTest {

    private static final long ORDER_POSITION_ID = 1;

    private static final List<String> validExamples = Arrays.asList(
            // Обувь (88 символов):
            "010466006821276821JfdaC6MeDQEMz91ffd0929ymi3p633GKY5Vv1T6olD8LdVe0g+jSquYWSP4bDZU53OIkf8Mqbnhu1guGXz8FN6IRrsWThu|DOGdoQ5D6DcA==",
            "010466006821276821ujZZGyFUgj0fM91ffd092mPsqPqIPCdnwo3fL5a4Wiag4Do4K/r5wh|oo6nrW8fCLbYTG8dp9VQzrsuUuEgn0DKtgkAHdNw28Euc0OyLtOg==",
            "010466006821276821zWyn8vjJ2rp0u91ffd092uMMKlJp9zosKgBPHZlTt0o5MO/LvWVC|g1cZZGHIJDJ5r3YiMTZbGKNWxRpyNCJL+0c8/cvWySuAEv2niJD2sg==",
            "010466006821276821JfdaC6MeDQEMz91ffd0929ymi3p633GKY5Vv1T6olD8LdVe0g+jSquYWSP4bDZU53OIkf8Mqbnhu1guGXz8FN6IRrsWThu/DOGdoQ5D6DcA==");

    private OrderPosition orderPosition;

    private ExpertiseService expertiseService;
    private ExpertiseRepository expertiseRepository;
    private FeatureFlagsSettingService featureFlagsSettingService;
    private AdminConfirmationService service;
    private DatamatrixUtils datamatrixUtils;
    private MessageSourceAccessor messageSourceAccessor;

    @BeforeEach
    public void setUp() {
        orderPosition = mock(OrderPosition.class);
        when(orderPosition.getConfirmedTime()).thenReturn(ZonedDateTime.now());

        final Order order = mock(Order.class);
        when(order.getState()).thenReturn(OrderState.HOLD);
        when(order.getSellerUser()).thenReturn(new User().setId(1L));
        when(order.getOrderPositions()).thenReturn(Collections.singletonList(orderPosition));
        when(orderPosition.getOrder()).thenReturn(order);

        OrderPositionService orderPositionService = mock(OrderPositionService.class);
        when(orderPositionService.getById(ORDER_POSITION_ID)).thenReturn(Optional.of(orderPosition));

        expertiseRepository = mock(ExpertiseRepository.class);
        featureFlagsSettingService = mock(FeatureFlagsSettingService.class);
        messageSourceAccessor = mock(MessageSourceAccessor.class);
        datamatrixUtils = new DatamatrixUtils(messageSourceAccessor);
        expertiseService = new DefaultExpertiseService(featureFlagsSettingService, expertiseRepository, messageSourceAccessor, datamatrixUtils);

        service = new DefaultAdminConfirmationService(
                mock(OrderService.class),
                orderPositionService,
                expertiseService,
                mock(OrderProcessingService.class),
                mock(DutyService.class),
                mock(MessageSourceAccessor.class));

        ReflectionTestUtils.setField(datamatrixUtils, "markingCodeMask", ".{125}==");
    }

    /**
     * Test valid datamatrix case.
     */
    @Test
    public void testValidDatamatrix() {
        validExamples.forEach(this::happyPath);
    }

    /**
     * Test invalid datamatrix case, validation enabled
     */
    @SneakyThrows
    @Test
    public void testInvalidDatamatrixValidationFailWhenEnabled() {
        when(featureFlagsSettingService.isEnableForUserCached(any(), eq(ENABLE_MARKING_CODE)))
                .thenReturn(true);

        when(orderPosition.getDatamatrix()).thenReturn("123456a");

        Assertions.assertThrows(OrderException.class, () -> {
            service.setExpertisePassed(ORDER_POSITION_ID);
        });
    }

    /**
     * Test empty datamatrix case.
     */
    @Test
    public void testEmptyDatamatrix() {
        happyPath("");
    }

    /**
     * Test null datamatrix case.
     */
    @Test
    public void testNullDatamatrix() {
        happyPath(null);
    }

    private void happyPath(String datamatrix) {
        when(orderPosition.getDatamatrix()).thenReturn(datamatrix);
        when(orderPosition.getMarkingCodeApprovalTime()).thenReturn(LocalDateTime.now(Clock.systemUTC()));

        service.setExpertisePassed(ORDER_POSITION_ID);

        verify(expertiseRepository).save(any());
        reset(expertiseRepository);
    }
}
