package ru.oskelly.tests.build_01.domain.service.feed.v2.factory.builder;

import lombok.val;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.feed.model.FeedProduct;
import su.reddot.domain.service.feed.FeedService;
import su.reddot.domain.service.feed.config.YandexProductYmlFeedExportConfiguration;
import su.reddot.domain.service.feed.v2.factory.builder.FeedXmlFileBuilder;
import su.reddot.domain.service.feed.v2.factory.builder.FeedYmlFileBuilder;

import java.io.File;
import java.io.IOException;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
class FeedYmlFileBuilderTest {
    private static final String FILE_NAME = "test/test.xml";
    private static final String YANDEX_YML_OFFER = "<offer id=\"224\" available=\"true\">\n" +
            "    <name>Вечернее платье</name>\n" +
            "    <vendor>MAXIME SIMOENS</vendor>\n" +
            "    <url>https://oskelly.ru/products/64</url>\n" +
            "    <price>63750</price>\n" +
            "    <currencyId>RUR</currencyId>\n" +
            "    <delivery>true</delivery>\n" +
            "    <categoryId>51</categoryId>\n" +
            "    <param name=\"ProductSex\">FEMALE</param>\n" +
            "    <picture>https://static.oskelly.ru/img/product/64/item-b797b99b-7ed8-46b0-8253-2e75e98f303b</picture>\n" +
            "  </offer>";
    private static final String TEMPLATE = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
            "<yml_catalog date=\"2025-01-15 13:14:00\">\n" +
            "    <shop>\n" +
            "        <name>OSKELLY.RU LUXURY RESALE STORE</name>\n" +
            "        <url>https://oskelly.ru/</url>\n" +
            "        <company>Oskelly Group, LLC</company>\n" +
            "        <currencies>\n" +
            "            <currency id=\"RUB\" rate=\"1\"/>\n" +
            "        </currencies>\n" +
            "        <categories>\n" +
            "            <category id=\"2\">Женское</category>\n" +
            "        </categories>\n" +
            "        <offers/>\n" +
            "    </shop>\n" +
            "</yml_catalog>\n";

    FeedYmlFileBuilder builder;
    @Mock
    YandexProductYmlFeedExportConfiguration config;
    @Mock
    FeedService feedService;

    @BeforeEach
    void init() throws IOException {
        when(config.isZipResult())
                .thenReturn(false);
        when(config.getFilename())
                .thenReturn(FILE_NAME);
        when(feedService.prepareFeedTemplate(anyString()))
                .thenReturn(TEMPLATE);

        builder = new FeedYmlFileBuilder(config, FeedProduct::getYandexProductFeedYml, feedService);
    }

    @AfterEach
    void tearDown() {
        new File(FILE_NAME)
                .delete();
    }

    @Test
    @DisplayName("Создание корректного YML товарного фида")
    void buildYmlFeed() throws IOException {
        //Arrange
        final FeedProduct feedProduct = new FeedProduct();
        feedProduct.setYandexProductFeedYml(YANDEX_YML_OFFER);
        feedProduct.setId(1L);

        // Act
        builder.addFeedProductItems(Collections.singletonList(feedProduct));
        final FeedXmlFileBuilder.FeedXmlFileBuildResult buildResult = builder.build();

        // Assert
        val expectedFile = new File("src/test/resources/feed/productFeedYml.xml");
        val resultFile = new File(buildResult.getLocalFileName());
        assertTrue(FileUtils.contentEquals(expectedFile, resultFile));
    }
}
