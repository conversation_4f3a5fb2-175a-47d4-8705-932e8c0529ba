package ru.oskelly.tests.build_01.domain.service.bargain;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Spy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cache.CacheManager;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.util.Assert;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.exception.BargainException;
import su.reddot.domain.model.bargain.Bargain;
import su.reddot.domain.model.commission.CommissionGrid;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.product.SalesChannel;
import su.reddot.domain.model.size.Size;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.bargain.BargainService;
import su.reddot.domain.service.product.DefaultProductService;
import su.reddot.domain.service.size.SizeService;
import su.reddot.infrastructure.configparam.ConfigParamService;
import su.reddot.infrastructure.security.SecurityService;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static su.reddot.infrastructure.configparam.ConfigParamService.CONFIG_PARAM_DISABLE_BARGAIN_CREATE_SELLER_LIST;

;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class BargainServiceTest extends AbstractSpringTest {

    @Autowired
    private BargainService bargainService;

    @MockBean
    ConfigParamService configParamServiceMock;

    @MockBean
    SecurityService securityService;

    @Autowired
    private CacheManager cacheManager;

    @MockBean
    private DefaultProductService productService;

    @MockBean
    private SizeService sizeService;

    @Autowired
    private MessageSourceAccessor messageSourceAccessor;

    @Spy
    Product productDisabled = new Product();


    @Test
    public void testSellerReceivesSumForProductWithSalesChannelWebsite() {
        // Первая пара – цена с комиссией и сколько получит продавец;
        // вторая пара – предложенная цена за торг и сколько получит продавец
        Map<Pair<BigDecimal, BigDecimal>, Pair<Integer, Integer>> values = new HashMap<>();

        values.put(Pair.of(BigDecimal.valueOf(10000), BigDecimal.valueOf(7500)), Pair.of(9000, 6750));
        values.put(Pair.of(BigDecimal.valueOf(15000), BigDecimal.valueOf(11250)), Pair.of(14000, 10500));
        values.put(Pair.of(BigDecimal.valueOf(20000), BigDecimal.valueOf(16000)), Pair.of(16000, 12000));
        values.put(Pair.of(BigDecimal.valueOf(25000), BigDecimal.valueOf(20000)), Pair.of(23000, 17250));
        values.put(Pair.of(BigDecimal.valueOf(33000), BigDecimal.valueOf(26400)), Pair.of(31500, 23625));
        values.put(Pair.of(BigDecimal.valueOf(66000), BigDecimal.valueOf(52800)), Pair.of(65580, 52464));
        values.put(Pair.of(BigDecimal.valueOf(150000), BigDecimal.valueOf(126000)), Pair.of(115000, 92000));

        Bargain bargain = new Bargain();
        Product product = new Product();
        User seller = new User();
        CommissionGrid commissionGrid = new CommissionGrid(
                1L,
                "Комиссионная сетка для частных продавцов",
                CommissionGrid.Type.DEFAULT
        );

        seller.setCommissionGrid(commissionGrid);
        product.setSeller(seller);
        product.setSalesChannel(SalesChannel.WEBSITE);
        bargain.setProduct(product);

        values.forEach((key, value) -> {
            product.setCurrentPrice(key.getLeft());
            product.setCurrentPriceWithoutCommission(key.getRight());
            bargain.setLastPrice(value.getLeft());

            Assertions.assertEquals(value.getRight().intValue(), bargainService.getSellerReceivesSum(bargain).intValue());
        });
    }

    @Test
    public void testSellerReceivesSumForProductWithCustomCommission() {
        // Первая пара – цена с комиссией и сколько получит продавец;
        // вторая пара – предложенная цена за торг и сколько получит продавец
        Map<Pair<BigDecimal, BigDecimal>, Pair<Integer, Integer>> values = new HashMap<>();

        values.put(Pair.of(BigDecimal.valueOf(10000), BigDecimal.valueOf(9900)), Pair.of(9000, 8910)); // комиссия 1%
        values.put(Pair.of(BigDecimal.valueOf(10000), BigDecimal.valueOf(9500)), Pair.of(8000, 7600)); // комиссия 5%
        values.put(Pair.of(BigDecimal.valueOf(10000), BigDecimal.valueOf(8000)), Pair.of(5000, 4000)); // комиссия 20%
        values.put(Pair.of(BigDecimal.valueOf(15000), BigDecimal.valueOf(11250)), Pair.of(14000, 10500)); // комиссия 25%
        values.put(Pair.of(BigDecimal.valueOf(10000), BigDecimal.valueOf(5000)), Pair.of(4000, 2000)); // комиссия 50%
        values.put(Pair.of(BigDecimal.valueOf(10000), BigDecimal.valueOf(2500)), Pair.of(4000, 1000)); // комиссия 75%

        Bargain bargain = new Bargain();
        Product product = new Product();

        product.setCustomCommission(true);
        bargain.setProduct(product);

        values.forEach((key, value) -> {
            product.setCurrentPrice(key.getLeft());
            product.setCurrentPriceWithoutCommission(key.getRight());
            product.setSeller(new User());
            bargain.setLastPrice(value.getLeft());

            Assertions.assertEquals(value.getRight().intValue(), bargainService.getSellerReceivesSum(bargain).intValue());
        });
    }

    @Test
    public void testSellerReceivesSumForProductWithSalesChannelBoutiqueAndWebsite() {
        // Первая пара – цена с комиссией и сколько получит продавец;
        // вторая пара – предложенная цена за торг и сколько получит продавец
        Map<Pair<BigDecimal, BigDecimal>, Pair<Integer, Integer>> values = new HashMap<>();

        values.put(Pair.of(BigDecimal.valueOf(10000), BigDecimal.valueOf(6000)), Pair.of(9000, 5850));
        values.put(Pair.of(BigDecimal.valueOf(10000), BigDecimal.valueOf(6000)), Pair.of(8000, 5200));
        values.put(Pair.of(BigDecimal.valueOf(10000), BigDecimal.valueOf(6000)), Pair.of(5000, 3250));
        values.put(Pair.of(BigDecimal.valueOf(15000), BigDecimal.valueOf(9000)), Pair.of(14000, 9100));
        values.put(Pair.of(BigDecimal.valueOf(10000), BigDecimal.valueOf(6000)), Pair.of(4000, 2600));
        values.put(Pair.of(BigDecimal.valueOf(10000), BigDecimal.valueOf(6000)), Pair.of(7000, 4550));
        values.put(Pair.of(BigDecimal.valueOf(150000), BigDecimal.valueOf(126000)), Pair.of(115000, 74750));

        Bargain bargain = new Bargain();
        Product product = new Product();
        User seller = new User();
        CommissionGrid commissionGrid = new CommissionGrid(
                1L,
                "Комиссионная сетка для частных продавцов",
                CommissionGrid.Type.DEFAULT
        );

        seller.setCommissionGrid(commissionGrid);
        product.setSeller(seller);
        product.setSalesChannel(SalesChannel.BOUTIQUE_AND_WEBSITE);
        bargain.setProduct(product);

        values.forEach((key, value) -> {
            product.setCurrentPrice(key.getLeft());
            product.setCurrentPriceWithoutCommission(key.getRight());
            bargain.setLastPrice(value.getLeft());

            Assertions.assertEquals(value.getRight().intValue(), bargainService.getSellerReceivesSum(bargain).intValue());
        });
    }

    @Test
    public void testSellerDisableBargainCached() {

        final Long productIdDisabled = 90955L;
        Product productDisabledLocal = new Product();
        productDisabledLocal.setId(productIdDisabled);
        //Будет в списке отключенных
        User userDisabled = new User();
        userDisabled.setId(41165L);
        productDisabledLocal.setSeller(userDisabled);

        final Long productIdEnabled = 52854L;
        Product productEnabled = new Product();
        productEnabled.setId(productIdEnabled);
        //Не будет в списке отключенных
        User userEnabled = new User();
        userEnabled.setId(41166L);
        productEnabled.setSeller(userEnabled);

        //Зададим жесткий ответ от configParamService
        setUpMockConfigParamService();
        clearCacheSellerDisabledBargain();
        Assert.isTrue(bargainService.isSellerDisabledBargainCached(productDisabledLocal), "disabled failed");
        Assert.isTrue(!bargainService.isSellerDisabledBargainCached(productEnabled), "enabled failed");

        //Сheck cache manager
        Optional<Boolean> cachedValue = getCachedProductDisabled(productIdDisabled);
        Assert.isTrue(cachedValue.isPresent() && cachedValue.get(), "cache failed");

        //Убедимся в том, что предыдущие 2 вызова были сделаны
        verify(configParamServiceMock, times(2)).getValueAsListCached(CONFIG_PARAM_DISABLE_BARGAIN_CREATE_SELLER_LIST);

        //Проверим, что было попадание кеша и вызов метода не выполнился
        setUpMockConfigParamService();
        bargainService.isSellerDisabledBargainCached(productDisabledLocal);
        verify(configParamServiceMock, times(0)).getValueAsListCached(CONFIG_PARAM_DISABLE_BARGAIN_CREATE_SELLER_LIST);

    }


    @Test
    @WithMockUser
    public void testCreateBargainByAuthorizedBuyer_throws_bargainSellerDisabledError() {
        reset(productDisabled);
        final Long productIdDisabled = 90955L;
        final Long SIZE_ID = 0L;
        productDisabled.setId(productIdDisabled);
        productDisabled.setProductState(ProductState.PUBLISHED);
        when(productDisabled.isSizeAvailableToBuy(SIZE_ID))
                .thenReturn(true);
        when(sizeService.fromId(SIZE_ID)).thenReturn(new Size());

        User userDisabled = new User();
        userDisabled.setId(41165L);
        productDisabled.setSeller(userDisabled);

        setUpMockConfigParamService();

        when(securityService.getCurrentAuthorizedUser())
                .thenReturn(new User().setId(-1L));

        when(productService.getRawProduct(any(), any()))
                .thenReturn(Optional.of(productDisabled));

        Exception exception = assertThrows(BargainException.class, () -> {
            bargainService.createBargainByAuthorizedBuyer(productIdDisabled, SIZE_ID, 0, "");
        });

        String expectedMessage = messageSourceAccessor.getMessage("bargain.seller.disabled.error");
        String actualMessage = exception.getMessage();

        assertTrue(actualMessage.equals(expectedMessage));

    }

    private Optional<Boolean> getCachedProductDisabled(Long productId) {
        return Optional.ofNullable(cacheManager.getCache("bargainService.isSellerDisabledBargain")).map(c -> c.get(productId, Boolean.class));
    }

    private void clearCacheSellerDisabledBargain() {
        cacheManager.getCache("bargainService.isSellerDisabledBargain").clear();
    }

    private void setUpMockConfigParamService() {
        reset(configParamServiceMock);
        when(configParamServiceMock.getValueAsListCached(eq(CONFIG_PARAM_DISABLE_BARGAIN_CREATE_SELLER_LIST)))
                .thenReturn(Arrays.asList("41165"));
    }

}
