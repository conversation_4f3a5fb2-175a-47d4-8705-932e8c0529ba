package ru.oskelly.tests.build_01.domain.service.feed;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.exception.UserNotFoundException;
import su.reddot.domain.feed.dao.FeedUserRepository;
import su.reddot.domain.feed.model.FeedUser;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.feed.FeedDbExporter;
import su.reddot.domain.service.feed.FeedDbSynchronizer;
import su.reddot.domain.service.feed.FeedService;
import su.reddot.domain.service.feed.config.MindboxCSVUserFeedExportConfiguration;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.util.FileUtils;

import java.io.File;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class FeedDbUserSynchronizerAndExporterTest extends AbstractSpringTest {
    @Autowired
    private FeedUserRepository feedUserRepository;
    @Autowired
    private FeedDbSynchronizer feedDbSynchronizer;
    @Autowired
    private FeedDbExporter feedDbExporter;
    @Autowired
    private UserService userService;
    @Autowired
    private FeedService feedService;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private MindboxCSVUserFeedExportConfiguration mindboxCSVUserFeedExportConfiguration;

    @Value("${test.api.user-id}")
    private Long userId;

    @Value("${test.api.user-phone}")
    private String userPhone;

    /**
     * Чистим все в фиде, обновляем любого пользователя и он появляется в фиде
     *  После этого обновляем пользователя снова и фид снова обновляется
     */
    @Test
    public void _00_feedUser_userChanged_OK(){
        feedUserRepository.deleteAll();
        feedUserRepository.flush();
        assertTrue(feedUserRepository.findAll().isEmpty());

        //Получаем тестового пользователя
        User user = userService.getUserById(userId).orElse(null);

        //Задаем телефон (чтобы был)
        user.setPhone(userPhone);
        userRepository.save(user);

        //Старый день рождения пользователя
        LocalDateTime oldUserBirthDate = user.getBirthDate();

        //Меняем дату рождения пользователя
        userService.setUserBirthdate(userId, LocalDateTime.now());

        //Ждем пару сек (асинхронная синхронизация)
        TestUtils.sleep(2);

        //Тянем из базы
        List<FeedUser> feedUsers = feedUserRepository.findAll();
        //Проверяем кол-во
        assertEquals(1, feedUsers.size());

        //Проверяем содержимое
        FeedUser feedUser = feedUsers.get(0);
        assertEquals(user.getId(), feedUser.getId());
        assertFeedUserIsFine(feedUser);

        //Сохраняем даты для дальнейшего сравнение
        LocalDateTime feedCreateTime = feedUser.getFeedCreateTime();
        LocalDateTime feedChangeTime = feedUser.getFeedChangeTime();
        LocalDateTime userChangeTime = feedUser.getUserChangeTime();

        //Меняем дату рождения обратно, заодно меняем isTrusted, чтобы не столкнуться с дубликатом события
        user = userService.getUserById(userId).orElse(null);
        user.setBirthDate(oldUserBirthDate);
        user.setIsTrusted(Boolean.FALSE.equals(user.getIsTrusted()));
        userService.save(user);

        //Ждем пару сек (асинхронная синхронизация)
        TestUtils.sleep(2);

        //Тянем из базы
        List<FeedUser> feedUsers2 = feedUserRepository.findAll();
        //Проверяем кол-во
        assertEquals(1, feedUsers.size());

        //Проверяем содержимое
        FeedUser feedUser2 = feedUserRepository.findById(feedUser.getId()).orElse(null);
        assertEquals(user.getId(), feedUser.getId());
        assertFeedUserIsFine(feedUser2);

        //Дата создания записи не изменилась
        assertEquals(feedCreateTime, feedUser2.getFeedCreateTime());

        //Дата обновления пользователя и записи фида обновились
        assertNotEquals(userChangeTime, feedUser2.getUserChangeTime());
        assertNotEquals(feedChangeTime, feedUser2.getFeedChangeTime());
    }

    /**
     * Удаляем все записи фидов пользователя, синхронизируем и проверяем наличие новых записей в базе
     */
    @Test
    public void _01_feedUser_sync_add_OK(){
        feedUserRepository.deleteAll();
        feedUserRepository.flush();
        assertTrue(feedUserRepository.findAll().isEmpty());

        int count = 10;

        //Обновляем телефоны у первых пользователей (в дампе они пустые)
        List<Long> userIds = userRepository.getAllUserIds().subList(0, count);
        for(Long userId: userIds){
            User user = userRepository.findById(userId).orElse(null);
            user.setPhone(userPhone);
            userRepository.save(user);
        }

        //Синхронизируем
        feedDbSynchronizer.runFeedUserSync(count);

        //Тянем из базы
        List<FeedUser> feedUsers = feedUserRepository.findAll();
        //Проверяем кол-во
        assertEquals(count, feedUsers.size());

        //Проверяем содержимое
        for(FeedUser feedUser : feedUsers){
            assertFeedUserIsFine(feedUser);
        }
    }

    /**
     * Обновляем записи фидов пользователя, синхронизируем и проверяем наличие изменения
     */
    @Test
    public void _02_feedUser_change_OK(){
        //Чтобы время после обновления отличалось
        TestUtils.sleep(1);

        //Тянем все ID товаров из фида
        List<Long> feedUserIds = feedUserRepository.getAllIds();

        //Список не пустой
        assertFalse(feedUserIds.isEmpty());

        //Сохраняем даты обновления фидов
        Map<Long, LocalDateTime> feedUpdateTimes = feedUserRepository.findAll().stream()
                .collect(Collectors.toMap(p -> p.getId(), p -> p.getFeedChangeTime()));

        //Проверяем, что список совпадает со списком id на обновление
        assertTrue(feedUpdateTimes.keySet().containsAll(feedUserIds));

        //Обновляем фиды по списку на обновление
        int updateResultCount = feedService.createOrUpdateFeedUsers(feedUserIds);

        //Обновились все
        assertEquals(feedUserIds.size(), updateResultCount);

        //Тянем из базы
        List<FeedUser> feedUsers = feedUserRepository.findAll();
        //Даты обновления изменились
        for(FeedUser feedUser : feedUsers){
            assertTrue(feedUser.getFeedChangeTime().isAfter(feedUpdateTimes.get(feedUser.getId())));
        }
    }

    /**
     * Экспортируем Mindbox CSV-фид польвателей
     */
    @Test
    public void _10_exportMindboxCsvFeed_OK(){
        String resultFilename = mindboxCSVUserFeedExportConfiguration.getFilename();
        //Перед началом теста удаляем файл выгрузки
        FileUtils.deleteFile(resultFilename);
        File resultFile = new File(resultFilename);
        //Убеждаемся, что файла не существует
        assertFalse(resultFile.exists());
        //Выполняем выгрузку
        int count = feedDbExporter.runExportUserCsvMindbox();
        //Что-то выгрузилось
        assertTrue(count > 0);
        //Проверяем наличие файла
        assertTrue(resultFile.exists());

        String resultFileContent = new String(TestUtils.loadFile(resultFile));
        int resultFileContentLinesCount = StringUtils.countMatches(resultFileContent, "\r\n");

        //Количество строк в файле на 1 больше, чем кол-во выгруженных пользователей, т.к. в файле имеется заголовок
        assertEquals(resultFileContentLinesCount, count + 1);
    }

    protected void assertFeedUserIsFine(FeedUser feedUser){
        User user = userService.getUserById(feedUser.getId()).orElseThrow(() -> new UserNotFoundException(String.valueOf(feedUser.getId())));

        assertNotNull(feedUser.getMindboxCsv());
        //Не пустая строка
        assertFalse(feedUser.getMindboxCsv().isEmpty());
        //Строка начинается с email'а пользователя
        assertTrue(feedUser.getMindboxCsv().startsWith(user.getEmail()));
        //Строка содержит телефон
        assertTrue(feedUser.getMindboxCsv().contains(";" + userPhone + ";"));
        //Содержит разделители
        assertTrue(feedUser.getMindboxCsv().contains(";"));
    }

}
