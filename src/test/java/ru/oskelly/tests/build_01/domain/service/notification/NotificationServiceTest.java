package ru.oskelly.tests.build_01.domain.service.notification;

import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.transaction.TestTransaction;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.activity.ActivityTestRepository;
import su.reddot.domain.dao.notification.NotificationGroupUserBindingRepository;
import su.reddot.domain.dao.notification.NotificationRepository;
import su.reddot.domain.dao.notificationDelivery.NotificationDeliveryRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.activity.Activity;
import su.reddot.domain.model.activity.GetSettingsActivity;
import su.reddot.domain.model.activity.profile.ChangeSexActivity;
import su.reddot.domain.model.device.DeviceDtype;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.notification.NotificationGroup;
import su.reddot.domain.model.notification.UserSubscriptionType;
import su.reddot.domain.model.notification.catalog.LastChance1000Notification;
import su.reddot.domain.model.notification.catalog.NewArrivalsNotification;
import su.reddot.domain.model.notification.catalog.Promo24HoursLeftNotification;
import su.reddot.domain.model.notification.catalog.YouWillLikeItNotification;
import su.reddot.domain.model.notification.profile.*;
import su.reddot.domain.model.notification.profile.welcome.HowItWorksNotification;
import su.reddot.domain.model.notificationDelivery.NotificationDelivery;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.activity.ActivityService;
import su.reddot.domain.service.device.DeviceService;
import su.reddot.domain.service.dto.NotificationDeliveryResult;
import su.reddot.domain.service.notification.DefaultNotificationService;
import su.reddot.domain.service.notification.NotificationService;
import su.reddot.domain.service.task.ScheduledNotificationCleanRunner;
import su.reddot.domain.service.task.ScheduledNotificationRunner;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.util.Utils;

import java.time.ZonedDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.BDDMockito.given;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class NotificationServiceTest extends AbstractSpringTest {
    @Autowired
    private NotificationService notificationService;

    @Autowired
    private UserService userService;

    @Autowired
    private ActivityService activityService;

    @Autowired
    private ScheduledNotificationRunner scheduledNotificationRunner;

	@Autowired
	private NotificationGroupUserBindingRepository notificationGroupUserBindingRepository;

	@Autowired
	private OrderRepository orderRepository;

	@Autowired
	private ProductRepository productRepository;

	@Autowired
	private NotificationRepository notificationRepository;

	@Autowired
	private ActivityTestRepository activityTestRepository;

	@Autowired
	private ScheduledNotificationCleanRunner scheduledNotificationCleanRunner;

	@Autowired
	private DefaultNotificationService defaultNotificationService;

	@Autowired
	private NotificationDeliveryRepository notificationDeliveryRepository;

	@SpyBean
	DeviceService deviceService;

	@Autowired
	private JdbcTemplate jdbcTemplate;

	@Value("${test.api.user-id}")
	private Long userId;

	@Value("${test.api.user2-id}")
	private Long user2Id;

    @Test
    public void _01_getNotificationGroups(){
    	List<NotificationGroup> notificationGroups = notificationService.getNotificationGroups();
    	assertEquals(11, notificationGroups.size());
    }

	@Test
	public void _02_notificationGroupContainsDtype(){
    	assertTrue(notificationService.notificationGroupContainsDtype(2L, "MyOfferNotification"));
		assertFalse(notificationService.notificationGroupContainsDtype(1L, "MyOfferNotification"));
		assertFalse(notificationService.notificationGroupContainsDtype(3L, "MyOfferNotification"));
		assertFalse(notificationService.notificationGroupContainsDtype(4L, "MyOfferNotification"));
		assertFalse(notificationService.notificationGroupContainsDtype(5L, "MyOfferNotification"));
		assertFalse(notificationService.notificationGroupContainsDtype(300L, "MyOfferNotification"));
	}

	//По умолчанию пользователь подписан на все группы
	@Test
	public void _03_userHasAllNotHiddenNotificationGroupsByDefault(){
		User user = userService.getUserById(userId).orElse(null);
		//Удаляем информацию о подписках пользователя
		notificationGroupUserBindingRepository.deleteAll(notificationGroupUserBindingRepository.findAllByUserId(user.getId()));
		user.setNotificationGroupsChangeTime(null);
		userService.save(user);

		Set<Long> allVisibleNotificationGroupIds = notificationService.getNotHiddenNotificationGroupsForSubscriptionType(UserSubscriptionType.MOBILE_PUSH)
				.stream().map(n -> n.getId()).collect(Collectors.toSet());

		Set<Long> userNotificationGroupIds = notificationService.getUserNotificationGroups(user, UserSubscriptionType.MOBILE_PUSH)
				.stream().map(n -> n.getId()).collect(Collectors.toSet());

		assertEquals(allVisibleNotificationGroupIds, userNotificationGroupIds);

		//Пользователю будет отправлен пуш
		assertTrue(notificationService.shouldSendNotification(user, "MyOfferNotification", UserSubscriptionType.MOBILE_PUSH));
	}

	//Подписываем пользователя на группы по своему усмотрению
	@Test
	public void _04_setUserNotificationGroups(){
		User user = userService.getUserById(userId).orElse(null);

		List<Long> groupIds = Arrays.asList(1L, 3L, 5L);

		notificationService.setUserNotificationGroups(user, groupIds, UserSubscriptionType.MOBILE_PUSH);

		List<Long> userNotificationGroupIds = notificationService.getUserNotificationGroups(user, UserSubscriptionType.MOBILE_PUSH)
				.stream().map(n -> n.getId()).collect(Collectors.toList());

		assertTrue(Utils.equalsIgnoreOrder(groupIds, userNotificationGroupIds));

		//Пользователю не будет отправлен пуш
		assertFalse(notificationService.shouldSendNotification(user, "MyOfferNotification", UserSubscriptionType.MOBILE_PUSH));
	}

	//Отписываем пользователя на группы по своему усмотрению
	@Test
	public void _05_clearUserNotificationGroups(){
		User user = userService.getUserById(userId).orElse(null);

		List<Long> groupIds = Collections.emptyList();

		for (UserSubscriptionType subscriptionType : UserSubscriptionType.values()) {
			notificationService.setUserNotificationGroups(user, groupIds, subscriptionType);
		}

		List<Long> userNotificationGroupIds = notificationService.getUserNotificationGroupIds(user);

		assertTrue(userNotificationGroupIds.isEmpty());

		//Пользователю не будет отправлен пуш
		assertFalse(notificationService.shouldSendNotification(user, "MyOfferNotification", UserSubscriptionType.MOBILE_PUSH));
	}

	//Проверка уведомления AddBrandLikeNotification
	@Test
	public void _10_scheduledNotificationRunner_createAddBrandLikeNotifications(){
		User user = userService.getUserById(userId).orElse(null);
		user.setRegistrationTime(ZonedDateTime.now());
		user.setAddBrandLikeNotificationTime(null);
		userService.save(user);

		//У пользователя нет уведомлений AddBrandLikeNotification
		TestUtils.deleteUserNotifications(user.getId(), AddBrandLikeNotification.class.getSimpleName(), notificationRepository);

		scheduledNotificationRunner.createAddBrandLikeNotifications(ZonedDateTime.now(), 10);

		//Уведомление появилось
		AddBrandLikeNotification notification = (AddBrandLikeNotification) TestUtils.getLastNotification(user, AddBrandLikeNotification.class, false, false, notificationService);
	}

	//Проверка уведомления TrustedSetNotification
	@Transactional
	@Test
	public void _11_scheduledNotificationRunner_setTrustedToSellersWithOrders(){
		User user = userService.getUserById(userId).orElse(null);
		user.setIsTrusted(false);
		userService.save(user);

		//У пользователя нет уведомлений TrustedSetNotification
		TestUtils.deleteUserNotifications(user.getId(), TrustedSetNotification.class.getSimpleName(), notificationRepository);

		//Присваиваем себе чужие выполненные заказы
		//При этом сохраняем старых продавцов, чтобы вернуть обратно
		Map<Long, User> orderSellers = new HashMap<>();
		List<Order> orders = orderRepository.findTop10ByStateOrderByIdDesc(OrderState.COMPLETED);
		for(Order order : orders){
			orderSellers.put(order.getId(), order.getSellerUser());
			TestUtils.setOrderSeller(order, user, productRepository);
		}

		TestTransaction.flagForCommit();
		TestTransaction.end();

		//Инициируем проставление статуса trusted
		scheduledNotificationRunner.setTrustedToSellersWithOrders(orders.size());

		//Уведомление появилось
		TrustedSetNotification notification = (TrustedSetNotification) TestUtils.getLastNotification(user, TrustedSetNotification.class, false, false, notificationService);

		//Проставляем прежних продавцов
		for(Order order : orders){
			TestUtils.setOrderSeller(order, orderSellers.get(order.getId()), productRepository);
		}
	}

	//Проверка уведомления NoActivityNotification
	@Test
	public void _12_scheduledNotificationRunner_createNoActivityNotifications_registered_user(){
		given(deviceService.getCurrentDeviceInfo())
				.willReturn(new DeviceService.DeviceInfo()
						.setGuestToken("guest-token")
						.setDeviceDtype(DeviceDtype.OtherDevice));

		User user = userService.getUserById(userId).orElse(null);
		String guestToken = UUID.randomUUID().toString();
		ZonedDateTime[] monthActivityDays = new ZonedDateTime[]{ZonedDateTime.now().minusDays(31), ZonedDateTime.now().minusDays(30), ZonedDateTime.now().minusDays(29), ZonedDateTime.now().minusDays(14), ZonedDateTime.now().minusDays(1)};
		ZonedDateTime[] yearActivityDays = new ZonedDateTime[]{ZonedDateTime.now().minusDays(365), ZonedDateTime.now().minusDays(366), ZonedDateTime.now().minusDays(367)};

		//Для начала удалим все активности пользователя и гостя
		TestUtils.deleteActivities(user.getId(), guestToken, activityTestRepository);

		//Активность пользователя и гостя год назад
		for(ZonedDateTime time : yearActivityDays){
			//Устанавливаем женский пол
			activityService.saveNow(new ChangeSexActivity(User.Sex.FEMALE).setUserId(user.getId()). setCreateTime(time).setUpdateTime(time));
			activityService.saveNow(new ChangeSexActivity(User.Sex.FEMALE).setGuestToken(guestToken). setCreateTime(time).setUpdateTime(time));
		}

		//У пользователя нет уведомлений об отсутствии активности
		TestUtils.deleteUserNotifications(user.getId(), NoActivityNotification.class.getSimpleName(), notificationRepository);

		//Инициируем проверку на отсутствие активности
		scheduledNotificationRunner.checkNoActivityCandidates(30);

		//Уведомление для пользователя не создано
		TestUtils.assertNoNotificationsCount(user, null, NoActivityNotification.class, false, false, notificationService);

		//Уведомление для гостя не создано
		TestUtils.assertNoNotificationsCount(null, guestToken, NoActivityNotification.class, false, false, notificationService);

		////////////////////////

		//Активность пользователя и гостя в течени месяца
		for(ZonedDateTime time : monthActivityDays){
			activityService.saveNow(new ChangeSexActivity(User.Sex.FEMALE).setUserId(user.getId()). setCreateTime(time).setUpdateTime(time));
			activityService.saveNow(new ChangeSexActivity(User.Sex.FEMALE).setGuestToken(guestToken). setCreateTime(time).setUpdateTime(time));
		}


		//У пользователя нет заказов
		TestUtils.deleteBuyerOrders(user, orderRepository);

		//У пользователя нет продаж
		TestUtils.deleteSellerOrders(user, OrderState.COMPLETED, orderRepository);

		//Инициируем проверку на отсутствие активности
		scheduledNotificationRunner.checkNoActivityCandidates(30);

		//Уведомление появилось
		NoActivityNotification notification = (NoActivityNotification) TestUtils.getLastNotification(user, NoActivityNotification.class, false, false, notificationService);

		//Снова инициируем проверку на отсутствие активности
		scheduledNotificationRunner.checkNoActivityCandidates(30);

		//Новое уведомление не появилось
		NoActivityNotification notification2 = (NoActivityNotification) TestUtils.getLastNotification(user, NoActivityNotification.class, false, false, notificationService);

		//Это то же уведомление.
		assertEquals(notification.getId(), notification2.getId());

		///////////////Гость////////

		//Уведомление появилось
		notification = (NoActivityNotification) TestUtils.getLastNotification(guestToken, NoActivityNotification.class, false, false, notificationService);

		//Снова инициируем проверку на отсутствие активности
		scheduledNotificationRunner.checkNoActivityCandidates(30);

		//Новое уведомление не появилось
		notification2 = (NoActivityNotification) TestUtils.getLastNotification(guestToken, NoActivityNotification.class, false, false, notificationService);

		//Это то же уведомление.
		assertEquals(notification.getId(), notification2.getId());
	}

	//Проверка уведомления AddBirthdateAndAvatarNotification
	@Test
	public void _13_scheduledNotificationRunner_checkAddBirthdateAndAvatarNotificationCandidates(){
		int daysCount = 5;

		User user = userService.getUserById(userId).orElse(null);
		user.setRegistrationTime(ZonedDateTime.now().minusDays(daysCount));
		user.setBirthDate(null);
		user.setAvatarPath(null);
		userService.save(user);

		//У пользователя нет уведомлений AddBirthdateAndAvatarNotification
		TestUtils.deleteUserNotifications(user.getId(), AddBirthdateAndAvatarNotification.class.getSimpleName(), notificationRepository);

		//Инициируем отправку уведомления
		scheduledNotificationRunner.checkAddBirthdateAndAvatarNotificationCandidates(daysCount);
		//На выполнение таска требуется время
		TestUtils.sleep(10);

		//Уведомление появилось
		AddBirthdateAndAvatarNotification notification = (AddBirthdateAndAvatarNotification) TestUtils.getLastNotification(user, AddBirthdateAndAvatarNotification.class, true, false, notificationService);

		//Снова инициируем отправку уведомления
		scheduledNotificationRunner.checkAddBirthdateAndAvatarNotificationCandidates(daysCount);
		//На выполнение таска требуется время
		TestUtils.sleep(1);

		//Снова получаем уведомление
		AddBirthdateAndAvatarNotification notification2 = (AddBirthdateAndAvatarNotification) TestUtils.getLastNotification(user, AddBirthdateAndAvatarNotification.class, true, false, notificationService);

		//И это то же самое уведомление. Новое не создавалось, т.к. этот тип создется только один раз
		assertEquals(notification.getId(), notification2.getId());

		//Выполняем действие
		user.setAvatarPath("img");
		userService.save(user);

		//Снова получаем уведомление
		AddBirthdateAndAvatarNotification notification3 = (AddBirthdateAndAvatarNotification) TestUtils.getLastNotification(user, AddBirthdateAndAvatarNotification.class, true, true, notificationService);

		//И это то же самое уведомление, новое не создавалось, но теперь по нему действие выполнено
		assertEquals(notification.getId(), notification3.getId());
		assertEquals(true, notification3.isActionCompleted());

	}

	/**
	 * Проверка дефолтных уведомлений для пользователя
	 */
	@Test
	public void _14_01_scheduledNotificationRunner_createDefaultNotifications_registeredUser(){
		//ID пользователя, над которым ставим эксперименты (при создании уведомлений выборка идет от меньшего к большему, поэтоме берем минимальный ID)
    	Long testUserId = 1L;
		List<Notification> notifications;
		//Экспериментыльный пользователь
		User testUser = userService.getOne(testUserId);
		//Мы ожидаем следующие типы
		List<String> expectedTypes = Arrays.asList(HowItWorksNotification.class.getSimpleName());

    	//Удаляем все уведомления пользователя
		jdbcTemplate.execute("DELETE FROM notification WHERE user_id=" + testUserId);

		//Тянем уведомления, которых не должно быть
		notifications = notificationService.getRawNotifications(10, testUser);

		//На этот момент уведомлений для пользователя нет
		assertTrue(notifications.isEmpty());

		//Выполняем таск создания тестовых уведомлений
		scheduledNotificationRunner.createDefaultNotifications(10);

		//На выполнение таска требуется время
		TestUtils.sleep(3);

		//Тянем уведомления, которые уже должны появиться
		notifications = notificationService.getRawNotifications(10, testUser);

		//Появилось expectedTypes.size() уведомления
		assertEquals(expectedTypes.size(), notifications.size());

		//Получаем типы созданных уведомлений
		List<String> types = notifications.stream().map(n -> n.getDtype()).collect(Collectors.toList());

		//Получаем ID уведомлений
		List<Long> notificationIds = notifications.stream().map(n -> n.getId()).collect(Collectors.toList());

		//Сравниваем ожидаемые и фактические типы
		assertTrue(Utils.equalsIgnoreOrder(expectedTypes, types));

		//Снова выполняем таск создания тестовых уведомлений, чтобы проверить, что не будет создано дубликатов
		scheduledNotificationRunner.createDefaultNotifications(10);

		//На выполнение таска требуется время
		TestUtils.sleep(3);

		//Тянем уведомления
		notifications = notificationService.getRawNotifications(10, testUser);

		//Количество уведомлений не изменилось.
		assertEquals(expectedTypes.size(), notifications.size());

		//Получаем ID новых уведомлений
		List<Long> newNotificationIds = notifications.stream().map(n -> n.getId()).collect(Collectors.toList());

		//Убеждаемся, что ID увеомлений не изменились (имеем дело с теми же уведомлениями)
		assertTrue(Utils.equalsIgnoreOrder(notificationIds, newNotificationIds));

	}

	/**
	 * Проверка дефолтных уведомлений для гостя
	 */
	@Test
	public void _14_02_scheduledNotificationRunner_createDefaultNotifications_guest(){
		//Гостевой токен для тестирования
		String guestToken = UUID.randomUUID().toString();
		List<Notification> notifications;
		//Мы ожидаем следующие типы
		List<String> expectedTypes = Arrays.asList(HowItWorksNotification.class.getSimpleName());

		//Удаляем старые активности за последние 2 дня
		jdbcTemplate.execute("DELETE FROM activity WHERE create_time > (NOW() - interval '2 days' ) ");

		//Создаем активность возрастом 22 часа для нашего гостя
		//Уведмления создаются от старых к новым активностям, поэтому добавляем древнее время
		Activity activity = new GetSettingsActivity().setGuestToken(guestToken).setCreateTime(ZonedDateTime.now().minusHours(22));
		activityTestRepository.save(activity);

		//Тянем уведомления, которых не должно быть
		notifications = notificationService.getRawNotifications(10, null, guestToken);

		//На этот момент уведомлений для пользователя нет
		assertTrue(notifications.isEmpty());

		//Выполняем таск создания тестовых уведомлений
		scheduledNotificationRunner.createDefaultNotifications(10);

		//На выполнение таска требуется время
		TestUtils.sleep(5);

		//Тянем уведомления, которые уже должны появиться
		notifications = notificationService.getRawNotifications(10, null, guestToken);

		//Появилось expectedTypes.size() уведомления
		assertEquals(expectedTypes.size(), notifications.size());

		//Получаем типы созданных уведомлений
		List<String> types = notifications.stream().map(n -> n.getDtype()).collect(Collectors.toList());

		//Получаем ID уведомлений
		List<Long> notificationIds = notifications.stream().map(n -> n.getId()).collect(Collectors.toList());

		//Сравниваем ожидаемые и фактические типы
		assertTrue(Utils.equalsIgnoreOrder(expectedTypes, types));

		//Снова выполняем таск создания тестовых уведомлений, чтобы проверить, что не будет создано дубликатов
		scheduledNotificationRunner.createDefaultNotifications(10);

		//На выполнение таска требуется время
		TestUtils.sleep(3);

		//Тянем уведомления
		notifications = notificationService.getRawNotifications(10, null, guestToken);

		//Количество уведомлений не изменилось.
		assertEquals(expectedTypes.size(), notifications.size());

		//Получаем ID новых уведомлений
		List<Long> newNotificationIds = notifications.stream().map(n -> n.getId()).collect(Collectors.toList());

		//Убеждаемся, что ID увеомлений не изменились (имеем дело с теми же уведомлениями)
		assertTrue(Utils.equalsIgnoreOrder(notificationIds, newNotificationIds));

	}

	/**
	 * Проверка создания уведомления SubscribeCelebritiesNotification для гостя
	 */
	/*@Test
	public void _15_01_01_scheduledNotificationRunner_createSubscribeCelebritiesNotification_guest(){
		checkActivityBasedNotificationCreation(100 * 365 * 24, 2, 20,
				SubscribeCelebritiesNotification.class, true, false,
				count -> scheduledNotificationRunner.createSubscribeCelebritiesNotificationsOnceForUserOrGuest(count));
	}*/

	/**
	 * Проверка создания уведомления SubscribeCelebritiesNotification для пользователя
	 */
	/*@Test
	public void _15_01_02_scheduledNotificationRunner_createSubscribeCelebritiesNotification_user(){
		checkActivityBasedNotificationCreation(10 * 365 * 24, 2, 20,
				SubscribeCelebritiesNotification.class, false, true,
				count -> scheduledNotificationRunner.createSubscribeCelebritiesNotificationsOnceForUserOrGuest(count));
	}*/

	/**
	 * Проверка создания уведомления YouWillLikeItNotification для гостя
	 */
	@Test
	public void _15_02_01_scheduledNotificationRunner_createYouWillLikeItNotification_guest(){
		checkActivityBasedNotificationCreation(50, 2, 24,
				YouWillLikeItNotification.class, true, false,
				count -> scheduledNotificationRunner.createYouWillLikeItNotificationsOnceForUserOrGuest(count));
	}

	/**
	 * Проверка создания уведомления YouWillLikeItNotification для пользователя
	 */
	@Test
	public void _15_02_02_scheduledNotificationRunner_createYouWillLikeItNotification_user(){
		checkActivityBasedNotificationCreation(50, 2, 24,
				YouWillLikeItNotification.class, false, true,
				count -> scheduledNotificationRunner.createYouWillLikeItNotificationsOnceForUserOrGuest(count));
	}

	/**
	 * Проверка создания уведомления NewArrivalsNotification для гостя
	 */
	@Test
	public void _15_03_01_scheduledNotificationRunner_createNewArrivalsNotification_guest(){
		checkActivityBasedNotificationCreation(70, 24, 43,
				NewArrivalsNotification.class, true, false,
				count -> scheduledNotificationRunner.createNewArrivalsNotificationsOnceForUserOrGuest(count));
	}

	/**
	 * Проверка создания уведомления NewArrivalsNotification для пользователя
	 */
	@Test
	public void _15_03_02_scheduledNotificationRunner_createNewArrivalsNotification_user(){
		checkActivityBasedNotificationCreation(70, 24, 43,
				NewArrivalsNotification.class, false, true,
				count -> scheduledNotificationRunner.createNewArrivalsNotificationsOnceForUserOrGuest(count));
	}

	/**
	 * Проверка создания уведомления SubscribeInstagramNotification для гостя
	 */
	/*@Test
	public void _15_04_01_scheduledNotificationRunner_createSubscribeInstagramNotification_guest(){
		checkActivityBasedNotificationCreation(100 * 365 * 24, 48, 70,
				SubscribeInstagramNotification.class, true, false,
				count -> scheduledNotificationRunner.createSubscribeInstagramNotificationsOnceForUserOrGuest(count));
	}*/

	/**
	 * Проверка создания уведомления SubscribeInstagramNotification для пользователя
	 */
	/*@Test
	public void _15_04_02_scheduledNotificationRunner_createSubscribeInstagramNotification_user(){
		checkActivityBasedNotificationCreation(10 * 365 * 24, 48, 70,
				SubscribeInstagramNotification.class, false, true,
				count -> scheduledNotificationRunner.createSubscribeInstagramNotificationsOnceForUserOrGuest(count));
	}*/

	/**
	 * Проверка создания уведомления Promo24HoursLeftNotification для гостя
	 */
	@Test
	public void _15_05_01_scheduledNotificationRunner_createPromo24HoursLeftNotification_guest(){
		checkActivityBasedNotificationCreation(118, 72, 91,
				Promo24HoursLeftNotification.class, true, false,
				count -> scheduledNotificationRunner.createPromo24HoursLeftNotificationsOnceForUserOrGuest(count));
	}

	/**
	 * Проверка НЕ создания уведомления Promo24HoursLeftNotification для пользователя с заказами
	 */
	@Test
	public void _15_05_02_scheduledNotificationRunner_createPromo24HoursLeftNotification_user_with_orders(){
		//Берем первый попавшийся заказ из базы и назначаем нашему пользователю
		jdbcTemplate.execute("UPDATE public.order SET buyer_id=" + userId + " WHERE id IN(SELECT id FROM public.order WHERE hold_time IS NOT NULL ORDER BY id DESC LIMIT 1)");

		checkActivityBasedNotificationCreationNegative(91,
				Promo24HoursLeftNotification.class, false, true,
				count -> scheduledNotificationRunner.createPromo24HoursLeftNotificationsOnceForUserOrGuest(count));
	}

	/**
	 * Проверка создания уведомления Promo24HoursLeftNotification для пользователя без заказов
	 */
	@Test
	public void _15_05_03_scheduledNotificationRunner_createPromo24HoursLeftNotification_user_without_orders(){
		//Назначаем заказы второму тестовому пользователю
		jdbcTemplate.execute("UPDATE public.order SET buyer_id=" + user2Id + " WHERE buyer_id=" + userId);
		checkActivityBasedNotificationCreation(118, 72, 91,
				Promo24HoursLeftNotification.class, false, true,
				count -> scheduledNotificationRunner.createPromo24HoursLeftNotificationsOnceForUserOrGuest(count));
	}

	//Проверяет создание разового уведомления, основанного на активности ползователя/гостя
	private void checkActivityBasedNotificationCreation(
			int tooEarlyActivityDelayHours, //Слишком ранний срок (старая) активности, при которой уведомление не создается
			int tooLateActivityDelayHours, //Слишком поздний срок (молодая) активности, при которой уведомление не создается
			int exactActivityDelayHours, //Подходящий срок активности, при котором уведомление должно быть создано
			Class<? extends Notification> notificationType, //Тип уведомления
			boolean forGuest, //Проверить для гостя
			boolean forUser, //Проверить для пользователя
			Consumer<Integer> notificationCreationTask
	){
		//Количество создаваемых уведомлений таском
		int taskCountParam = 10;

		//Гостевой токен для тестирования
		String guestToken = forGuest ? UUID.randomUUID().toString() : null;

		//Пользователь для тестирования
		User user = forUser ? userService.getOne(userId) : null;

		List<Notification> notifications;

		//Удаляем старые активности
		jdbcTemplate.execute("DELETE FROM activity");

		//Удаляем старые уведомления
		if(user != null) jdbcTemplate.execute("delete from notification where user_id=" + user.getId());
		if(guestToken != null) jdbcTemplate.execute("delete from notification where guest_token='" + guestToken + "'");

		//Тянем уведомления, которых не должно быть
		notifications = notificationService.getRawNotifications(10, user, guestToken);

		//На этот момент уведомлений нет
		assertTrue(notifications.isEmpty());

		//Создаем слишком раннюю активность для нашего пользователя/гостя
		Activity activity = new GetSettingsActivity().setGuestToken(guestToken).setCreateTime(ZonedDateTime.now().minusHours(tooEarlyActivityDelayHours));
		if(user != null) activity.setUserId(user.getId());
		activityTestRepository.save(activity);

		//Выполняем таск создания уведомлений
		executeTaskAndSleep(notificationCreationTask, taskCountParam, 3);

		//Тянем уведомления, которых не должно быть, т.к. активность была слишком ранней (старая)
		notifications = notificationService.getRawNotifications(10, user, guestToken);

		//На этот момент уведомлений нет
		assertTrue(notifications.isEmpty());

		//Теперь проверяем по слишком поздней активности
		activity.setCreateTime(ZonedDateTime.now().minusHours(tooLateActivityDelayHours));
		activityTestRepository.save(activity);

		//Выполняем таск создания уведомлений
		executeTaskAndSleep(notificationCreationTask, taskCountParam, 3);

		//Тянем уведомления, которых не должно быть, т.к. активность была слишком поздней (молодая)
		notifications = notificationService.getRawNotifications(10, user, guestToken);

		//На этот момент уведомлений нет
		assertTrue(notifications.isEmpty());

		//Теперь проверяем по подходящей активности
		activity.setCreateTime(ZonedDateTime.now().minusHours(exactActivityDelayHours));
		activityTestRepository.save(activity);

		//Выполняем таск создания уведомлений
		executeTaskAndSleep(notificationCreationTask, taskCountParam, 3);

		//Тянем уведомления, которые должны появитьс, т.к. активность подходящая
		notifications = notificationService.getRawNotifications(10, user, guestToken);

		//Уведомление есть!
		assertEquals(1, notifications.size());

		//Проверяем тип уведомления
		Notification notification = notifications.get(0);
		assertEquals(notificationType.getSimpleName(), notification.getDtype());

		//Снова выполняем таск создания уведомлений, чтобы проверить, что не будет создано дубликатов
		executeTaskAndSleep(notificationCreationTask, taskCountParam, 3);

		//Тянем уведомления
		notifications = notificationService.getRawNotifications(10, user, guestToken);

		//Количество уведомлений не изменилось.
		assertEquals(1, notifications.size());
	}

	/**
	 * Проверка создания уведомления LastChance1000Notification для гостя
	 */
	@Test
	public void _15_06_01_scheduledNotificationRunner_createLastChance1000Notification_guest(){
		checkActivityBasedNotificationCreation(262, 216, 235,
				LastChance1000Notification.class, true, false,
				count -> scheduledNotificationRunner.createLastChance1000NotificationsOnceForUserOrGuest(count));
	}

	/**
	 * Проверка НЕ создания уведомления LastChance1000Notification для пользователя с заказами
	 */
	@Test
	public void _15_06_02_scheduledNotificationRunner_createLastChance1000Notification_user_with_orders(){
		//Берем первый попавшийся заказ из базы и назначаем нашему пользователю
		jdbcTemplate.execute("UPDATE public.order SET buyer_id=" + userId + " WHERE id IN(SELECT id FROM public.order WHERE hold_time IS NOT NULL ORDER BY id DESC LIMIT 1)");

		checkActivityBasedNotificationCreationNegative(235,
				LastChance1000Notification.class, false, true,
				count -> scheduledNotificationRunner.createLastChance1000NotificationsOnceForUserOrGuest(count));
	}

	/**
	 * Проверка создания уведомления LastChance1000Notification для пользователя без заказов
	 */
	@Test
	public void _15_06_03_scheduledNotificationRunner_createLastChance1000Notification_user_without_orders(){
		//Назначаем заказы второму тестовому пользователю
		jdbcTemplate.execute("UPDATE public.order SET buyer_id=" + user2Id + " WHERE buyer_id=" + userId);
		checkActivityBasedNotificationCreation(262, 216, 235,
				LastChance1000Notification.class, false, true,
				count -> scheduledNotificationRunner.createLastChance1000NotificationsOnceForUserOrGuest(count));
	}

	@Test
	public void _16_notificationService_enableAllNonEmailNotifications() {
		User user = userService.getUserById(userId).orElse(null);

		List<NotificationGroup> emailGroupsBefore = notificationService.getUserNotificationGroups(user, UserSubscriptionType.EMAIL);

		notificationService.enableAllNonEmailNotifications(user);

		List<NotificationGroup> pushGroups = notificationService.getUserNotificationGroups(user, UserSubscriptionType.MOBILE_PUSH);
		List<NotificationGroup> emailGroupsAfter = notificationService.getUserNotificationGroups(user, UserSubscriptionType.EMAIL);

		assertEquals(4, pushGroups.size());
		assertEquals(emailGroupsBefore, emailGroupsAfter);
	}

	@Test
	public void _16_scheduledNotificationCleanRunner_shouldCleanOldNotifications() {
		Notification notification = new YouWillLikeItNotification()
				.setCreateTime(ZonedDateTime.now().minusDays(720));
		notification = (Notification) notificationRepository.save(notification);

		String reason = "Ok";
		NotificationDeliveryResult notificationDeliveryResult = new NotificationDeliveryResult(
				notification.getId(),
				"onesignal",
				reason,
				NotificationDeliveryResult.ResultType.SUCCESSFUL
		);

		defaultNotificationService.markNotificationDelivery(notificationDeliveryResult);
		List<NotificationDelivery> onesignalDeliveries = notificationDeliveryRepository.findDeliveries("onesignal", notification.getId());
		assertEquals(1, onesignalDeliveries.size());

		NotificationDelivery notificationDelivery = onesignalDeliveries.get(0);
		notificationDelivery.setCreateTime(ZonedDateTime.now().minusDays(720));
		notificationRepository.save(notificationDelivery);

		scheduledNotificationCleanRunner.clean();

		onesignalDeliveries = notificationDeliveryRepository.findDeliveries("onesignal", notification.getId());
		assertEquals(0, onesignalDeliveries.size());
		Optional optNotification = notificationRepository.findById(notification.getId());
		assertEquals(false, optNotification.isPresent());
	}

	//Проверяет НЕ создание разового уведомления, основанного на активности ползователя/гостя
	private void checkActivityBasedNotificationCreationNegative(
			int activityDelayHours, //Подходящий срок активности, при котором уведомление должно быть создано
			Class<? extends Notification> notificationType, //Тип уведомления
			boolean forGuest, //Проверить для гостя
			boolean forUser, //Проверить для пользователя
			Consumer<Integer> notificationCreationTask
	){
		//Количество создаваемых уведомлений таском
		int taskCountParam = 10;

		//Гостевой токен для тестирования
		String guestToken = forGuest ? UUID.randomUUID().toString() : null;

		//Пользователь для тестирования
		User user = forUser ? userService.getOne(userId) : null;

		List<Notification> notifications;

		//Удаляем старые активности
		jdbcTemplate.execute("DELETE FROM activity");

		//Удаляем старые уведомления
		if(user != null) jdbcTemplate.execute("delete from notification where user_id=" + user.getId());
		if(guestToken != null) jdbcTemplate.execute("delete from notification where guest_token='" + guestToken + "'");

		//Тянем уведомления, которых не должно быть
		notifications = notificationService.getRawNotifications(10, user, guestToken);

		//На этот момент уведомлений нет
		assertTrue(notifications.isEmpty());

		//Создаем активность для нашего пользователя/гостя
		Activity activity = new GetSettingsActivity().setGuestToken(guestToken).setCreateTime(ZonedDateTime.now().minusHours(activityDelayHours));
		if(user != null) activity.setUserId(user.getId());
		activityTestRepository.save(activity);

		//Выполняем таск создания уведомлений
		executeTaskAndSleep(notificationCreationTask, taskCountParam, 3);

		//Тянем уведомления, которых не должно быть, т.к. отрабатываем негативный сценарий
		notifications = notificationService.getRawNotifications(10, user, guestToken);

		//Уведомлений нет
		assertTrue(notifications.isEmpty());
	}

	private void executeTaskAndSleep(Consumer<Integer> task, int taskCountParam, int seconds){
		task.accept(taskCountParam);
		TestUtils.sleep(seconds);
	}

}
