package ru.oskelly.tests.build_01.domain.service.audit;

import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.order.OrderPositionRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.product.ProductItemRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.order.OrderPositionState;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.audit.AuditService;
import su.reddot.domain.service.user.UserService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class AuditServiceTest extends AbstractSpringTest {
    @Autowired
    private AuditService auditService;
    @Autowired
    private UserService userService;

    @Autowired
	OrderRepository orderRepository;
    @Autowired
	OrderPositionRepository orderPositionRepository;
    @Autowired
	ProductRepository productRepository;
    @Autowired
	ProductItemRepository productItemRepository;

	@Value("${test.api.user-id}")
	private Long userId;

	private Long sellerId = 23L;

	private static List<Order> orders = new ArrayList<>();

	private User getUser(){
		return userService.getUserById(userId).orElse(null);
	}

	@Test
	public void _01_0_orderStateNoChanges_prepare(){
		Order order = new Order();
		order.setState(OrderState.CREATED);
		order.setUuid(UUID.randomUUID());
		order.setBuyer(getUser());
		order = orderRepository.saveAndFlush(order);
		orders.add(order);
	}

    @Test
    @Transactional
    public void _01_1_orderStateNoChanges(){
		assertSame(1, orders.size());
	    //Изменений не было, сервис вернет null
	    assertNull(auditService.getOrderStateChanges(orders.get(0).getId()));
	    cleanup();
    }

	@Test
	@Transactional
	@Rollback(false)
	public void _02_0_orderStateOneChange_0_prepare(){
		createNewOrder();
	}

	@Test
	@Transactional
	@Rollback(false)
	public void _02_0_orderStateOneChange_1_prepare(){
		Order order = getLastOrder();
		order.setState(OrderState.CANCELED);
		orderRepository.save(order);
	}

	@Test
	@Transactional
	public void _02_1_orderStateOneChange(){
		assertSame(1, orders.size());
		//Было только одно изменение, поэтому сервис вернет 2 строки: первоначальная и последняя версии
		List<Order> changes = auditService.getOrderStateChanges(orders.get(0).getId());
		assertNotNull(changes);
		assertSame(2, changes.size());
		assertSame(OrderState.CREATED, changes.get(0).getState());
		assertSame(OrderState.CANCELED, changes.get(1).getState());
		cleanup();
	}

	@Test
	@Transactional
	@Rollback(false)
	public void _03_0_orderStateTwoChanges_prepare_0(){
		createNewOrder();
	}

	@Test
	@Transactional
	@Rollback(false)
	public void _03_0_orderStateTwoChanges_prepare_1(){
		Order order = getLastOrder();
		order.setState(OrderState.HOLD_PROCESSING);
		orderRepository.saveAndFlush(order);
	}

	@Test
	@Transactional
	@Rollback(false)
	public void _03_0_orderStateTwoChanges_prepare_2(){
		Order order = getLastOrder();
		order.setState(OrderState.HOLD_ERROR);
		orderRepository.saveAndFlush(order);
	}

	@Test
	@Transactional
	public void _03_1_orderStateTwoChanges(){
		assertSame(1, orders.size());
		//Было только два изменения, поэтому сервис вернет 3 строки: первоначальная, промежуточная и последняя версии
		List<Order> changes = auditService.getOrderStateChanges(getLastOrder().getId());
		assertNotNull(changes);
		assertSame(3, changes.size());
		assertSame(OrderState.CREATED, changes.get(0).getState());
		assertSame(OrderState.HOLD_PROCESSING, changes.get(1).getState());
		assertSame(OrderState.HOLD_ERROR, changes.get(2).getState());
		cleanup();
	}

	@Test
	@Transactional
	@Rollback(false)
	public void _04_0_orderStateFourStateChangesWithOtherFieldsChanges_0() {
		createNewOrder();
	}

	@Test
	@Transactional
	@Rollback(false)
	public void _04_0_orderStateFourStateChangesWithOtherFieldsChanges_1(){
		Order order = getLastOrder();

		//order1
		order.setAmount(new BigDecimal(10));
		orderRepository.saveAndFlush(order);
	}

	@Test
	@Transactional
	@Rollback(false)
	public void _04_0_orderStateFourStateChangesWithOtherFieldsChanges_2(){
		Order order = getLastOrder();

		//order1
		order.setState(OrderState.HOLD_PROCESSING);
		orderRepository.saveAndFlush(order);

	}

	@Test
	@Transactional
	@Rollback(false)
	public void _04_0_orderStateFourStateChangesWithOtherFieldsChanges_3(){
		Order order = getLastOrder();

		//order2
		order.setGuestToken("guest");
		orderRepository.saveAndFlush(order);
	}

	@Test
	@Transactional
	@Rollback(false)
	public void _04_0_orderStateFourStateChangesWithOtherFieldsChanges_4(){
		Order order = getLastOrder();

		//order2
		order.setState(OrderState.HOLD_ERROR);
		orderRepository.saveAndFlush(order);
	}

	@Test
	@Transactional
	@Rollback(false)
	public void _04_0_orderStateFourStateChangesWithOtherFieldsChanges_5(){
		Order order = getLastOrder();

		//order3
		order.setAmount(new BigDecimal(20));
		order.setGuestToken("guest2");
		orderRepository.saveAndFlush(order);
	}

	@Test
	@Transactional
	@Rollback(false)
	public void _04_0_orderStateFourStateChangesWithOtherFieldsChanges_6(){
		Order order = getLastOrder();

		//order3
		order.setAmount(new BigDecimal(30));
		order.setState(OrderState.CANCELED);
		orderRepository.saveAndFlush(order);
	}

	//Суть теста в том, что изменени, не касающиеся статуса заказа не возвращаются
	@Test
	@Transactional
	public void _04_1_orderStateFourStateChangesWithOtherFieldsChanges(){
		assertSame(1, orders.size());

		List<Order> changes = auditService.getOrderStateChanges(orders.get(0).getId());
		assertNotNull(changes);
		assertSame(4, changes.size());

		Order order0 = changes.get(0);
		Order order1 = changes.get(1);
		Order order2 = changes.get(2);
		Order order3 = changes.get(3);

		assertSame(OrderState.CREATED, order0.getState());
		assertEquals(null,order0.getAmount());
		assertEquals(null, order0.getGuestToken());

		assertSame(OrderState.HOLD_PROCESSING, order1.getState());
		assertEquals(new BigDecimal(10), order1.getAmount());
		assertEquals(null, order1.getGuestToken());

		assertSame(OrderState.HOLD_ERROR, order2.getState());
		assertEquals(new BigDecimal(10), order2.getAmount());
		assertEquals("guest", order2.getGuestToken());

		assertSame(OrderState.CANCELED, order3.getState());
		assertEquals(new BigDecimal(30), order3.getAmount());
		assertEquals("guest2", order3.getGuestToken());

		cleanup();
	}

	@Test
	@Transactional
	@Rollback(false)
	public void _05_0_oneOrderPositionAdded_prepare_0(){
		createNewOrder();
	}

	@Test
	@Transactional
	@Rollback(false)
	public void _05_0_oneOrderPositionAdded_prepare_1(){
		Order order = getLastOrder();
		OrderPosition op = new OrderPosition();
		op.setAmount(new BigDecimal(10000));
		op.setItemSaleAmount(op.getAmount());
		op.setState(OrderPositionState.INITIAL);
		op.setStateTime(LocalDateTime.now());
		Product product = getProducts1ToBuy().get(0);
		ProductItem productItem = productItemRepository.findAllByProduct(product).get(0);
		op.setProductItem(productItem);
		op.setOrder(order);
		orderPositionRepository.saveAndFlush(op);
		order.addPosition(op);
		orderRepository.saveAndFlush(order);
	}

	@Test
	@Transactional
	public void _05_1_oneOrderPositionAdded(){
		Order order = getLastOrder();

		assertSame(1, orders.size());
		//Было только одно изменение, связаное с добавлением OrderPosition
		List<Order> changes = auditService.getAllOrderVersions(orders.get(0).getId(), null);
		assertNotNull(changes);
		assertSame(2, changes.size());
		assertSame(OrderState.CREATED, changes.get(0).getState());
		assertSame(0, changes.get(0).getOrderPositions().size());
		assertSame(1, changes.get(1).getOrderPositions().size());
		cleanup();
	}

	private List<Product> getProducts1ToBuy(){
		return productRepository.findProductsBySellerIdAndProductState(sellerId, ProductState.PUBLISHED).stream().collect(Collectors.toList());
	}

	private Order getLastOrder(){
		return orderRepository.getOne(orders.get(orders.size() - 1).getId());
	}

	public Order createNewOrder(){
		Order order = new Order();
		order.setState(OrderState.CREATED);
		order.setUuid(UUID.randomUUID());
		order.setBuyer(getUser());
		order = orderRepository.save(order);
		orders.add(order);
		return order;
	}

	private void cleanup(){
		for(Order order : orders) {
			orderRepository.delete(order);
		}
		orders.clear();
	}

}
