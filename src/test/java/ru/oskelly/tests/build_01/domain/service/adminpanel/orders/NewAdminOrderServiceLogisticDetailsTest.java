package ru.oskelly.tests.build_01.domain.service.adminpanel.orders;

import com.google.common.collect.ImmutableList;
import org.junit.jupiter.api.Test;
import org.modelmapper.ModelMapper;
import org.springframework.context.support.MessageSourceAccessor;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.logistic.WaybillRepository;
import su.reddot.domain.dao.order.OrderAdminsRepository;
import su.reddot.domain.dao.order.OrderExtraPropValueRepository;
import su.reddot.domain.dao.order.OrderPositionRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.product.ProductItemRepository;
import su.reddot.domain.model.logistic.DeliveryCompany;
import su.reddot.domain.model.logistic.DestinationType;
import su.reddot.domain.model.logistic.Waybill;
import su.reddot.domain.model.logistic.WaybillOrder;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.service.adminpanel.category.AdminCategoryService;
import su.reddot.domain.service.adminpanel.orders.AdminOrdersService;
import su.reddot.domain.service.adminpanel.orders.AdminAddressService;
import su.reddot.domain.service.adminpanel.orders.NewAdminOrderService;
import su.reddot.domain.service.adminpanel.orders.impl.DalliStatusTranslator;
import su.reddot.domain.service.adminpanel.orders.impl.NewAdminOrderServiceImpl;
import su.reddot.domain.service.audit.AuditService;
import su.reddot.domain.service.brand.BrandService;
import su.reddot.domain.service.dto.order.DeliveryStatusDTO;
import su.reddot.domain.service.dto.order.LogisticDetailsDTO;
import su.reddot.domain.service.integration.orderprocessing.OrderProcessingService;
import su.reddot.domain.service.legalentity.OrderLegalEntityService;
import su.reddot.domain.service.notification.NotificationService;
import su.reddot.domain.service.order.OrderPaymentService;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.product.ProductService;
import su.reddot.domain.service.readerdevice.ReaderDeviceMaskConfigService;
import su.reddot.domain.service.refundreason.RefundReasonService;
import su.reddot.infrastructure.delivery.DeliveryService;
import su.reddot.infrastructure.logistic.CommonLogisticService;
import su.reddot.infrastructure.logistic.DeliveryOptionsService;
import su.reddot.infrastructure.security.SecurityService;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Unit tests for {@link NewAdminOrderServiceImpl#getLogisticDetails(long)} method.
 */
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class NewAdminOrderServiceLogisticDetailsTest {

    private static final long WAYBILL_FROM_SELLER_ID = 2;
    private static final long WAYBILL_TO_BUYER_ID = 3;
    private static final long WAYBILL_ORDER_FROM_SELLER_ID = 4;
    private static final long WAYBILL_ORDER_TO_BUYER_ID = 5;

    private static final long BASE_TIME = 1654070584;

    @Test
    public void testGetLogisticDetails() {
        // ARRANGE
        List<Waybill> waybills = ImmutableList.of(
                createWaybill(true),
                createWaybill(false));

        Order order = mock(Order.class);
        when(order.getWaybills()).thenReturn(waybills);
        when(order.getWaybills(anyBoolean())).thenCallRealMethod();

        long orderId = 1L;
        OrderService orderService = mock(OrderService.class);
        when(orderService.getOrder(orderId)).thenReturn(order);

        WaybillRepository waybillRepository = mock(WaybillRepository.class);

        List<Object[]> fromSellerHistory = Arrays.asList(
                new Object[]{null, null, null, BASE_TIME + 1000},
                new Object[]{"Принят", new Timestamp(BASE_TIME), "Комментарий1", BASE_TIME + 2000},
                new Object[]{"Принят", new Timestamp(BASE_TIME), "Комментарий1", BASE_TIME + 3000},
                new Object[]{"Доставлен", new Timestamp(BASE_TIME + 1000), "Комментарий2", BASE_TIME + 4000},
                new Object[]{"Доставлен", new Timestamp(BASE_TIME + 1000), "Комментарий2", BASE_TIME + 5000});
        when(waybillRepository.getWaybillHistory(WAYBILL_FROM_SELLER_ID)).thenReturn(fromSellerHistory);

        List<Object[]> toBuyerHistory = Arrays.asList(
                new Object[] {"Принят", new Timestamp(BASE_TIME), "Комментарий1", BASE_TIME + 1000},
                new Object[] {null, null, null, BASE_TIME + 2000},
                new Object[] {null, null, null, BASE_TIME + 3000},
                new Object[] {"Доставлен", new Timestamp(BASE_TIME + 3000), "Комментарий2", BASE_TIME + 4000});
        when(waybillRepository.getWaybillHistory(WAYBILL_TO_BUYER_ID)).thenReturn(toBuyerHistory);

        NewAdminOrderService service = new NewAdminOrderServiceImpl(
                mock(ProductService.class),
                mock(AdminCategoryService.class),
                mock(BrandService.class),
                orderService,
                mock(AuditService.class),
                mock(UserRepository.class),
                mock(OrderRepository.class),
                mock(OrderAdminsRepository.class),
                mock(OrderExtraPropValueRepository.class),
                waybillRepository,
                mock(ProductItemRepository.class),
                mock(OrderPositionRepository.class),
                new DalliStatusTranslator(),
                mock(AdminOrdersService.class),
                mock(ModelMapper.class),
                mock(CommonLogisticService.class),
                mock(OrderPaymentService.class),
                mock(ReaderDeviceMaskConfigService.class),
                mock(DeliveryOptionsService.class),
                mock(MessageSourceAccessor.class),
                mock(OrderLegalEntityService.class),
                mock(RefundReasonService.class),
                mock(AdminAddressService.class),
                mock(DeliveryService.class),
                mock(SecurityService.class),
                mock(NotificationService.class),
                mock(OrderProcessingService.class));

        // ACT
        LogisticDetailsDTO logisticDetails = service.getLogisticDetails(orderId);

        // ASSERT
        List<DeliveryStatusDTO> fromSellerDeliveryChanges = logisticDetails.getFromSellerDeliveryChanges();
        assertEquals(3, fromSellerDeliveryChanges.size());
        checkDeliveryStatus(fromSellerDeliveryChanges.get(0), true, null);
        checkDeliveryStatus(fromSellerDeliveryChanges.get(1), true, "Принят");
        checkDeliveryStatus(fromSellerDeliveryChanges.get(2), true, "Доставлен на склад OSKELLY");
        List<DeliveryStatusDTO> toBuyerDeliveryChanges = logisticDetails.getToBuyerDeliveryChanges();
        assertEquals(3, toBuyerDeliveryChanges.size());
        checkDeliveryStatus(toBuyerDeliveryChanges.get(0), false, "Принят");
        checkDeliveryStatus(toBuyerDeliveryChanges.get(1), false, null);
        checkDeliveryStatus(toBuyerDeliveryChanges.get(2), false, "Доставлен");
    }

    private static Waybill createWaybill(boolean isFromSeller) {
        long waybillId = getWaybillId(isFromSeller);
        Waybill waybill = new Waybill();
        waybill.setId(waybillId);
        waybill.setExternalSystemId(String.valueOf(waybillId));
        waybill.setPickupDestinationType(isFromSeller ? DestinationType.SELLER : DestinationType.OFFICE);
        DeliveryCompany deliveryCompany = new DeliveryCompany();
        deliveryCompany.setName("Dalli");
        waybill.setDeliveryCompany(deliveryCompany);
        WaybillOrder waybillOrder = new WaybillOrder();
        waybillOrder.setExternalSystemId(String.valueOf(getWaybillOrderId(isFromSeller)));
        waybill.setWaybillOrder(waybillOrder);
        return waybill;
    }

    private static void checkDeliveryStatus(
            DeliveryStatusDTO deliveryStatus,
            boolean isFromSeller,
            String status) {
        assertEquals("Dalli", deliveryStatus.getDeliveryCompany());
        assertEquals(String.valueOf(getWaybillId(isFromSeller)), deliveryStatus.getWaybillExternalId());
        assertEquals(String.valueOf(getWaybillOrderId(isFromSeller)), deliveryStatus.getWaybillOrderExternalId());
        assertEquals(status, deliveryStatus.getTrackingState());
    }

    private static long getWaybillId(boolean isFromSeller) {
        return isFromSeller ? WAYBILL_FROM_SELLER_ID : WAYBILL_TO_BUYER_ID;
    }

    private static long getWaybillOrderId(boolean isFromSeller) {
        return isFromSeller ? WAYBILL_ORDER_FROM_SELLER_ID : WAYBILL_ORDER_TO_BUYER_ID;
    }
}
