package ru.oskelly.tests.build_01.domain.service.user;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.notification.following.NewFollowingNotification;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.notification.NotificationService;
import su.reddot.domain.service.user.UserService;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class UserServiceTest extends AbstractSpringTest {

    @Autowired
    private UserService userService;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private UserRepository userRepository;

	@Value("${test.api.user-id}")
	private Long userId;

	private User user;

	private String newUserEmail = "<EMAIL>";
	private String newUserNickname = "tmptmptmp";
	private String newUserPassword = "Tmp_tmp-tmp";
	private String newUserPhone = "+79202341740";

	@BeforeEach
	public void init(){
		if(user == null){
			cleanup();
			user = userService.getUserById(userId).orElse(null);
		}
	}

    /**
     * Старое поле address в таблице user еще есть. Оно используется в админке товаров, так что удалять его нельзя.
     */
    @Test
    public void _0_getUserAddress() {
		assertEquals("Ленинский пр-кт, д 2", userService.getUserAddress(user));
    }

	/**
	 * Старое поле city в таблице user еще есть. Оно используется в админке товаров, так что удалять его нельзя.
	 */
	@Test
	public void _0_getUserCity() {
		assertEquals("г Москва", userService.getUserCity(user));
	}

	/**
	 * Старые поля first_name и last_name в таблице user еще есть. Они используются в админке товаров, так что удалять их нельзя.
	 */
	@Test
	public void _0_getUserFullName() {
		assertEquals("Иван Белых", userService.getUserFullName(user));
	}

	//old
	/*@Transactional
	@Test
	public void _1_registerUser(){
		EmailRegistrationRequest emailRegistrationRequest = new EmailRegistrationRequest();
		emailRegistrationRequest.setEmail(newUserEmail);
		emailRegistrationRequest.setPassword(newUserPassword);
		emailRegistrationRequest.setConfirmPassword(newUserPassword);
		emailRegistrationRequest.setNickname(newUserNickname);
		emailRegistrationRequest.setPhone(newUserPhone);
		ErrorNotification errorNotification = new ErrorNotification();
		Optional<User> registeredUserOpt = userService.registerUserByEmail(emailRegistrationRequest, errorNotification);
		assertTrue(registeredUserOpt.isPresent());
		User newUser = registeredUserOpt.get();
		assertNotNull(newUser.getId());

		commitAndStartNewTransaction();

		//Проверяем созданные уведомления после регистрации
		List<Notification> newFollowingNotifications = TestUtils.getLastNotifications(2, newUser, NewFollowingNotification.class, false, false, notificationService);
		NewFollowingNotification newFollowingNotification0 = (NewFollowingNotification)newFollowingNotifications.get(0);
		NewFollowingNotification newFollowingNotification1 = (NewFollowingNotification)newFollowingNotifications.get(1);
		//Уведомления добавляются фактически одновременно, поэтому порядок может меняться
		if(newFollowingNotification0.getTitle().equals("ZAIRA")){
			assertEquals(newFollowingNotification1.getTitle(), "FashionRoom");
		}
		else{
			assertEquals(newFollowingNotification0.getTitle(), "FashionRoom");
			assertEquals(newFollowingNotification1.getTitle(), "ZAIRA");
		}
	}*/

	@Transactional
	@Test
	public void _1_registerUser(){
		UserService.EmailRegistrationRequest emailRegistrationRequest = new UserService.EmailRegistrationRequest();
		emailRegistrationRequest.setRegisterEmail(newUserEmail);
		emailRegistrationRequest.setRegisterPassword(newUserPassword);
		emailRegistrationRequest.setRegisterConfirmPassword(newUserPassword);
		emailRegistrationRequest.setRegisterNickname(newUserNickname);
		emailRegistrationRequest.setRegisterPhone(newUserPhone);
		User newUser = userService.registerUser(emailRegistrationRequest, false, false, true, true, false);
		assertNotNull(newUser);
		assertNotNull(newUser.getId());

		commitAndStartNewTransaction();

		//Проверяем созданные уведомления после регистрации
		List<Notification> newFollowingNotifications = TestUtils.getLastNotifications(2, newUser, NewFollowingNotification.class, false, false, notificationService);
		NewFollowingNotification newFollowingNotification0 = (NewFollowingNotification)newFollowingNotifications.get(0);
		NewFollowingNotification newFollowingNotification1 = (NewFollowingNotification)newFollowingNotifications.get(1);
		//Уведомления добавляются фактически одновременно, поэтому порядок может меняться
		if(newFollowingNotification0.getTitle().equals("ZAIRA")){
			assertEquals(newFollowingNotification1.getTitle(), "FashionRoom");
		}
		else{
			assertEquals(newFollowingNotification0.getTitle(), "FashionRoom");
			assertEquals(newFollowingNotification1.getTitle(), "ZAIRA");
		}
	}

	@Test
	public void _9_finalize(){
		cleanup();
	}

	private void cleanup(){
		User newUser = userService.getUserByEmail(newUserEmail);
		if(newUser != null) userRepository.delete(newUser);
	}

}
