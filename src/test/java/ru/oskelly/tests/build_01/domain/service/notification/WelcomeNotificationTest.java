package ru.oskelly.tests.build_01.domain.service.notification;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.activity.ActivityRepository;
import su.reddot.domain.dao.notification.NotificationRepository;
import su.reddot.domain.model.activity.Activity;
import su.reddot.domain.model.activity.GetSettingsActivity;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.notification.NotificationService;
import su.reddot.domain.service.social.SocialService;
import su.reddot.domain.service.task.ScheduledWelcomeNotificationRunner;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.util.Utils;

import javax.annotation.PostConstruct;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
@Slf4j
public class WelcomeNotificationTest extends AbstractSpringTest {
    @Autowired
    private ScheduledWelcomeNotificationRunner scheduledWelcomeNotificationRunner;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private UserService userService;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private ActivityRepository activityRepository;
    @Autowired
    private NotificationRepository notificationRepository;
    @MockBean
    private SocialService socialService;

    private final static Long userId = 100L;

    private final Map<String, Integer> welcomeNotifications = new HashMap<String, Integer>(){{
        put("UsePromocodeSecondDayNotification", 1);
        put("UsePromocodeSixthDayNotification", 5);
        put("UsePromocodeNinthDayNotification", 8);
        put("HowToTakePhotoNotification", 6);
        put("HowToUseBargainNotification", 4);
        put("WhatIsBeegzNotification", 7);
        put("WhatIsConciergeNotification", 6);
        put("WhyNeedAuthenticationNotification", 3);
        put("WhyNeedLikeNotification", 2);
        put("SubscribeCelebritiesNotification", 2);
    }};

    @PostConstruct
    @Transactional
    public void init(){
        jdbcTemplate.execute("DELETE FROM notification WHERE user_id = " + userId);
//        Потому что в настройках если выставить меньше 10 дней, то тесты упадут, ибо не создадутся уведомления
        ReflectionTestUtils.setField(scheduledWelcomeNotificationRunner, "initialDateInString", ZonedDateTime.now().minusDays(10).toString());
    }

    @Test
    @Transactional
    public void checkWelcomeNotification_Guest(){
        welcomeNotifications.forEach(this::checkGuestWelcomeNotifications);
    }

    @Test
    @Transactional
    public void checkWelcomeNotification_User(){
        welcomeNotifications.forEach((n, d) ->{
            notificationRepository.deleteAllByUserId(userId);
            notificationRepository.flush();
            checkUserWelcomeNotifications(n, d);
        });
    }

    private void checkGuestWelcomeNotifications(String welcomeNotification, int delayDaysCount){
        log.info("Start processing: " + welcomeNotification);
        String guestToken = UUID.randomUUID().toString();
        List<String> expectedTypes = Arrays.asList(welcomeNotification);
        //Создаем активность возрастом 22 часа для нашего гостя
        //Уведмления создаются от старых к новым активностям, поэтому добавляем древнее время
        Activity activity = new GetSettingsActivity().setGuestToken(guestToken).setCreateTime(ZonedDateTime.now().minusDays(delayDaysCount));
        activityRepository.saveAndFlush(activity);

        //Тянем уведомления, которых не должно быть
        List<Notification> notifications = notificationService.getRawNotifications(10, null, guestToken);

        //На этот момент уведомлений для пользователя нет
        assertTrue(notifications.isEmpty());

        chooseWelcomeNotificationRunner(welcomeNotification);


        //На выполнение таска требуется время
        TestUtils.sleep(5);

        //Тянем уведомления, которые уже должны появиться
        notifications = notificationService.getRawNotifications(10, null, guestToken);

        //Появилось expectedTypes.size() уведомления
        assertEquals(expectedTypes.size(), notifications.size());

        //Получаем типы созданных уведомлений
        List<String> types = notifications.stream().map(Notification::getDtype).collect(Collectors.toList());

        //Получаем ID уведомлений
        List<Long> notificationIds = notifications.stream().map(Notification::getId).collect(Collectors.toList());

        //Сравниваем ожидаемые и фактические типы
        assertTrue(Utils.equalsIgnoreOrder(expectedTypes, types));

        //Снова выполняем таск создания тестовых уведомлений, чтобы проверить, что не будет создано дубликатов
        chooseWelcomeNotificationRunner(welcomeNotification);

        //На выполнение таска требуется время
        TestUtils.sleep(3);

        //Тянем уведомления
        notifications = notificationService.getRawNotifications(10, null, guestToken);

        //Количество уведомлений не изменилось.
        assertEquals(expectedTypes.size(), notifications.size());

        //Получаем ID новых уведомлений
        List<Long> newNotificationIds = notifications.stream().map(Notification::getId).collect(Collectors.toList());

        //Убеждаемся, что ID увеомлений не изменились (имеем дело с теми же уведомлениями)
        assertTrue(Utils.equalsIgnoreOrder(notificationIds, newNotificationIds));
    }

    private void checkUserWelcomeNotifications(String welcomeNotification, int delayDaysCount){
        log.info("Start processing: " + welcomeNotification);
        //ID пользователя, над которым ставим эксперименты (при создании уведомлений выборка идет от меньшего к большему, поэтоме берем минимальный ID)
        List<Notification> notifications;
        //Экспериментыльный пользователь
        User testUser = userService.getOne(userId);
        testUser.setRegistrationTime(ZonedDateTime.now().minusDays(delayDaysCount));
        userRepository.saveAndFlush(testUser);
        //Мы ожидаем следующие типы
        List<String> expectedTypes = Arrays.asList(welcomeNotification);

        //Тянем уведомления, которых не должно быть
        notifications = notificationService.getRawNotifications(10, testUser);

        //На этот момент уведомлений для пользователя нет
        assertTrue(notifications.isEmpty());

        //Выполняем таск создания тестовых уведомлений
        chooseWelcomeNotificationRunner(welcomeNotification);

        //На выполнение таска требуется время
        TestUtils.sleep(3);

        //Тянем уведомления, которые уже должны появиться
        notifications = notificationService.getRawNotifications(10, testUser);

        //Появилось expectedTypes.size() уведомления
        assertEquals(expectedTypes.size(), notifications.size());

        //Получаем типы созданных уведомлений
        List<String> types = notifications.stream().map(Notification::getDtype).collect(Collectors.toList());

        //Получаем ID уведомлений
        List<Long> notificationIds = notifications.stream().map(Notification::getId).collect(Collectors.toList());

        //Сравниваем ожидаемые и фактические типы
        assertTrue(Utils.equalsIgnoreOrder(expectedTypes, types));

        //Снова выполняем таск создания тестовых уведомлений, чтобы проверить, что не будет создано дубликатов
        chooseWelcomeNotificationRunner(welcomeNotification);

        //На выполнение таска требуется время
        TestUtils.sleep(3);

        //Тянем уведомления
        notifications = notificationService.getRawNotifications(10, testUser);

        //Количество уведомлений не изменилось.
        assertEquals(expectedTypes.size(), notifications.size());

        //Получаем ID новых уведомлений
        List<Long> newNotificationIds = notifications.stream().map(Notification::getId).collect(Collectors.toList());

        //Убеждаемся, что ID увеомлений не изменились (имеем дело с теми же уведомлениями)
        assertTrue(Utils.equalsIgnoreOrder(notificationIds, newNotificationIds));
    }

    private void chooseWelcomeNotificationRunner(String welcomeNotification){
        switch (welcomeNotification){
            case "UsePromocodeSecondDayNotification":
                scheduledWelcomeNotificationRunner.createUsePromocodeSecondDayNotification(10);
                break;
            case "UsePromocodeSixthDayNotification":
                scheduledWelcomeNotificationRunner.createUsePromocodeSixthDayNotification(10);
                break;
            case "UsePromocodeNinthDayNotification":
                scheduledWelcomeNotificationRunner.createUsePromocodeNinthDayNotification(10);
                break;
            case "HowToTakePhotoNotification":
                scheduledWelcomeNotificationRunner.createHowToTakePhotoNotification(10);
                break;
            case "HowToUseBargainNotification":
                scheduledWelcomeNotificationRunner.createHowToUseBargainNotification(10);
                break;
            case "WhatIsBeegzNotification":
                scheduledWelcomeNotificationRunner.createWhatIsBeegzNotification(10);
                break;
            case "WhatIsConciergeNotification":
                scheduledWelcomeNotificationRunner.createWhatIsConciergeNotification(10);
                break;
            case "WhyNeedAuthenticationNotification":
                scheduledWelcomeNotificationRunner.createWhyNeedAuthenticationNotification(10);
                break;
            case "WhyNeedLikeNotification":
                scheduledWelcomeNotificationRunner.createWhyNeedLikeNotification(10);
                break;
            case "SubscribeCelebritiesNotification":
                Mockito.when(socialService.isReadyForSocialNotifications()).thenReturn(Boolean.TRUE);
                scheduledWelcomeNotificationRunner.createSubscribeCelebritiesNotification(10);
                break;
        }
    }
}
