package ru.oskelly.tests.build_01.domain.service.feed;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.mock.mockito.MockBean;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.feed.dao.FeedProductRepository;
import su.reddot.domain.feed.model.FeedProduct;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.service.feed.FeedDbExporter;
import su.reddot.domain.service.feed.FeedDbSynchronizer;
import su.reddot.domain.service.feed.FeedService;
import su.reddot.domain.service.feed.config.MindboxYMLFeedExportConfiguration;
import su.reddot.domain.service.product.ProductService;
import su.reddot.infrastructure.s3.S3Service;
import su.reddot.infrastructure.util.FileUtils;
import su.reddot.infrastructure.util.Utils;

import java.io.File;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class FeedDbProductSynchronizerAndExporterTest extends AbstractSpringTest {
    @Autowired
    private FeedProductRepository feedProductRepository;
    @Autowired
    private FeedDbSynchronizer feedDbSynchronizer;
    @Autowired
    private FeedDbExporter feedDbExporter;
    @Autowired
    private ProductService productService;
    @Autowired
    private FeedService feedService;
    @Autowired
    private MindboxYMLFeedExportConfiguration mindboxYMLFeedExportConfiguration;
    @MockBean
    @Qualifier("s3Service")
    private S3Service s3Service;

    /**
     * Чистим все в фиде, обновляем любой товар и он появляется в фиде
     *  После этого обновляем товар снова и фид снова обновляется
     */
    @Test
    public void _00_feedProduct_productChanged_OK(){
        feedProductRepository.deleteAll();
        feedProductRepository.flush();
        assertTrue(feedProductRepository.findAll().isEmpty());

        //Получаем любой опубликованный товар
        Product product = productService.getRawProducts(new ProductService.FilterSpecification().state(ProductState.PUBLISHED),
                1,
                ProductService.SortAttribute.CHANGE_TIME,
                new ProductService.ViewQualification().pageLength(1),
                ProductService.UserType.SYSTEM).getItems().get(0);

        //Меняем стейт товара
        productService.updateState(product, ProductState.DRAFT, ProductService.UserType.SYSTEM);

        //Ждем пару сек (асинхронная синхронизация)
        TestUtils.sleep(2);

        //Тянем из базы
        List<FeedProduct> feedProducts = feedProductRepository.findAll();
        //Проверяем кол-во
        assertEquals(1, feedProducts.size());

        //Проверяем содержимое
        FeedProduct feedProduct = feedProducts.get(0);
        assertEquals(product.getId(), feedProduct.getId());
        assertFeedProductIsFine(feedProduct);

        //Сохраняем даты для дальнейшего сравнение
        LocalDateTime feedCreateTime = feedProduct.getFeedCreateTime();
        LocalDateTime feedChangeTime = feedProduct.getFeedChangeTime();
        LocalDateTime productChangeTime = feedProduct.getProductChangeTime();

        //Меняем стейт товара обратно
        productService.updateState(product, ProductState.PUBLISHED, ProductService.UserType.SYSTEM);

        //Ждем пару сек (асинхронная синхронизация)
        TestUtils.sleep(1);

        //Тянем из базы
        List<FeedProduct> feedProducts2 = feedProductRepository.findAll();
        //Проверяем кол-во
        assertEquals(1, feedProducts2.size());

        //Проверяем содержимое
        FeedProduct feedProduct2 = feedProductRepository.findById(feedProduct.getId()).orElse(null);
        assertEquals(product.getId(), feedProduct.getId());
        assertFeedProductIsFine(feedProduct2);

        //Дата создания записи не изменилась
        assertEquals(feedCreateTime, feedProduct2.getFeedCreateTime());

        //Дата обновления товара и записи фида обновились
        int i = 0;
        while(Objects.equals(feedChangeTime, feedProduct2.getFeedChangeTime()) && i < 5) {
            TestUtils.sleep(1);
            i++;
            feedProduct2 = feedProductRepository.findById(feedProduct.getId()).orElse(null);
        }

        assertNotEquals(productChangeTime, feedProduct2.getProductChangeTime());
        assertNotEquals(feedChangeTime, feedProduct2.getFeedChangeTime());
    }

    /**
     * Удаляем все записи фидов товара, синхронизируем и проверяем наличие новых записей в базе
     */
    @Test
    public void _01_feedProduct_add_OK(){
        feedProductRepository.deleteAll();
        feedProductRepository.flush();
        assertTrue(feedProductRepository.findAll().isEmpty());

        int count = 10;
        //Синхронизируем
        feedDbSynchronizer.runFeedProductSync(count);

        //Тянем из базы
        List<FeedProduct> feedProducts = feedProductRepository.findAll();
        //Проверяем кол-во
        assertEquals(count, feedProducts.size());

        //Проверяем содержимое
        for(FeedProduct feedProduct : feedProducts){
            assertFeedProductIsFine(feedProduct);
        }
    }

    /**
     * Обновляем записи фидов товара, синхронизируем и проверяем наличие изменения
     */
    @Test
    public void _02_feedProduct_change_OK(){
        //Чтобы врея после бновления отличалось
        TestUtils.sleep(1);

        //Тянем все ID товаров из фида
        List<Long> feedProductIds = feedProductRepository.getAllIds();

        //Список не пустой
        assertFalse(feedProductIds.isEmpty());

        //Сохраняем даты обновления фидов
        Map<Long, LocalDateTime> feedUpdateTimes = feedProductRepository.findAll().stream()
                .collect(Collectors.toMap(p -> p.getId(), p -> p.getFeedChangeTime()));

        //Проверяем, что список совпадат со списком id на обновление
        assertTrue(feedUpdateTimes.keySet().containsAll(feedProductIds));

        //Обновляем фиды по списку на обновление
        int updateResultCount = feedService.createOrUpdateFeedProducts(feedProductIds);

        //Обновились все
        assertEquals(feedProductIds.size(), updateResultCount);

        //Тянем из базы
        List<FeedProduct> feedProducts = feedProductRepository.findAll();
        //Даты обновления изменились
        for(FeedProduct feedProduct : feedProducts){
            assertTrue(feedProduct.getFeedChangeTime().isAfter(feedUpdateTimes.get(feedProduct.getId())));
        }
    }

    /**
     * Экспортируем Mindbox YML-фид
     */
    @Test
    public void _10_exportMindboxYmlFeed_OK(){
        String resultFilename = mindboxYMLFeedExportConfiguration.getFilename();
        //Перед началом теста удаляем файл выгрузки
        FileUtils.deleteFile(resultFilename);
        File resultFile = new File(resultFilename);
        //Убеждаемся, что файла не существует
        assertFalse(resultFile.exists());
        //Выполняем выгрузку
        int count = feedDbExporter.runExportProductYmlMindbox();
        //Что-то выгрузилось
        assertTrue(count > 0);
        //Проверяем наличие файла
        assertTrue(resultFile.exists());

        String resultFileContent = new String(TestUtils.loadFile(resultFile));
        int resultFileContentOffersCount = StringUtils.countMatches(resultFileContent, "</offer>");

        //Количество офферов в файле больше или равно кол-ву выгруженных офферов, т.к. офферы могут дублироваться (разные размеры)
        assertTrue(resultFileContentOffersCount >= count);

        //Заодно сверим с кол-вом ProductItem'ов по товарам
        String productIdsStr = Utils.joinValues(",", feedProductRepository.getAllIds());
        int productItemIdsCount = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM product_item WHERE product_id IN(" + productIdsStr + ")", Integer.class);
        assertEquals(productItemIdsCount, resultFileContentOffersCount);

        //Убеждаемся, что файл был опубликован на FTP
        String publishFileName = mindboxYMLFeedExportConfiguration.getPublishFilename();
        verify(s3Service, times(1)).uploadFile(eq(publishFileName), any(File.class));
    }

    protected void assertFeedProductIsFine(FeedProduct feedProduct){
        assertNotNull(feedProduct.getMindboxYml());
        assertTrue(feedProduct.getMindboxYml().startsWith("<offer"));
        assertTrue(feedProduct.getMindboxYml().endsWith("</offer>"));
        assertTrue(feedProduct.getMindboxYml().contains("ProductColor"));

        assertNotNull(feedProduct.getYandexProductFeedYml());
        assertTrue(feedProduct.getYandexProductFeedYml().startsWith("<offer"));
        assertTrue(feedProduct.getYandexProductFeedYml().endsWith("</offer>"));
        assertTrue(feedProduct.getYandexProductFeedYml().contains("ProductColor"));

        assertNotNull(feedProduct.getFeedV2GoogleXml());
    }
}
