package ru.oskelly.tests.build_01.domain.service.currencyrate;

import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.exception.CurrencyException;
import su.reddot.domain.model.address.Currency;
import su.reddot.domain.model.currency.CurrencyInfo;
import su.reddot.domain.model.currency.CurrencyRate;
import su.reddot.domain.service.currency.ConversionResult;
import su.reddot.domain.service.currency.CurrencyConverter;
import su.reddot.domain.service.currency.CurrencyConverterFactory;
import su.reddot.domain.service.currency.CurrencyRateService;
import su.reddot.domain.service.currency.CurrencyService;

import java.math.BigDecimal;
import java.math.RoundingMode;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class CurrencyServicesTest extends AbstractSpringTest {

    @Autowired
    private CurrencyRateService currencyRateService;
    @Autowired
    private CurrencyService currencyService;
    @Autowired
    private CurrencyConverterFactory currencyConverterFactory;

    private void validateCurrencyWithCode(CurrencyInfo currencyInfo) {
        Assertions.assertEquals(currencyInfo.name(), currencyInfo.getIsoCode());
        //
        java.util.Currency javaCurrency = java.util.Currency.getInstance(currencyInfo.getIsoCode());
        Assertions.assertNotNull(javaCurrency); // This currency exists
        // Validate with enum data
        Assertions.assertEquals(javaCurrency.getCurrencyCode(), currencyInfo.getIsoCode());
        Assertions.assertEquals(javaCurrency.getNumericCode(), currencyInfo.getIsoNumber());
        // Validate with base data
        Currency currency = currencyService.findByIsoNumber(javaCurrency.getNumericCode());
        Assertions.assertEquals(javaCurrency.getCurrencyCode(), currency.getIsoCode());
    }

    @Test
    @Transactional
    public void _01_currencyDefaultsListOkay() {
        for (CurrencyInfo currencyInfo : CurrencyInfo.values()) {
            validateCurrencyWithCode(currencyInfo);
        }
    }

    @Test
    @Transactional
    public void _02_currencyRateWithBaseCurrencyExists() {
        Currency currencyRUB = currencyService.findByIsoNumber(CurrencyInfo.RUB.getIsoNumber());
        long baseCurrencyId = currencyService.getBaseCurrency().getId();
        //
        CurrencyRate baseCurrencyRate = currencyRateService.findCurrencyRateWithCurrencyIdAndCurrencyToId(currencyRUB.getId(), baseCurrencyId);
        Assertions.assertEquals(BigDecimal.valueOf(100, 2), baseCurrencyRate.getRateValue().setScale(2, RoundingMode.UNNECESSARY));
    }

    @Test
    @Transactional
    public void _03_saveRateValueWith06pStepFail() {
        Currency currencyUSD = currencyService.findByIsoNumber(CurrencyInfo.USD.getIsoNumber());
        long baseCurrencyId = currencyService.getBaseCurrency().getId();
        //
        CurrencyRate usdCurrencyRate = currencyRateService.findCurrencyRateWithCurrencyIdAndCurrencyToId(currencyUSD.getId(),
                baseCurrencyId);
        BigDecimal currentRateValue = usdCurrencyRate.getRateValue();
        //
        BigDecimal up10PercentsValue = currentRateValue.multiply(BigDecimal.valueOf(106, 2));
        usdCurrencyRate.setRateValue(up10PercentsValue);
        try {
            currencyRateService.save(usdCurrencyRate);
        } catch (CurrencyException currencyException) {
            Assertions.assertEquals("Currency rates changes are allowed in 5% max steps", currencyException.getMessage());
        }
        //
        BigDecimal dn10PercentsValue = currentRateValue.multiply(BigDecimal.valueOf(94, 2));
        usdCurrencyRate.setRateValue(dn10PercentsValue);
        try {
            currencyRateService.save(usdCurrencyRate);
        } catch (CurrencyException currencyException) {
            Assertions.assertEquals("Currency rates changes are allowed in 5% max steps", currencyException.getMessage());
        }
    }

    @Test
    @Transactional
    public void _04_saveRateValueWith04pStepOkay() {
        long baseCurrencyId = currencyService.getBaseCurrency().getId();
        Currency currencyUSD = currencyService.findByIsoNumber(CurrencyInfo.USD.getIsoNumber());
        //
        CurrencyRate usdCurrencyRate = currencyRateService.findCurrencyRateWithCurrencyIdAndCurrencyToId(currencyUSD.getId(),
                baseCurrencyId);
        BigDecimal currentRateValue = usdCurrencyRate.getRateValue();
        //
        BigDecimal up04PercentsValue = currentRateValue.multiply(BigDecimal.valueOf(104, 2));
        usdCurrencyRate.setRateValue(up04PercentsValue);
        currencyRateService.save(usdCurrencyRate);
        CurrencyRate usdUpCurrencyRate =  currencyRateService.findCurrencyRateWithCurrencyIdAndCurrencyToId(currencyUSD.getId(),
                baseCurrencyId);
        Assertions.assertEquals(usdUpCurrencyRate.getRateValue(), up04PercentsValue);
        //
        usdCurrencyRate.setRateValue(currentRateValue);
        currencyRateService.save(usdCurrencyRate);
        CurrencyRate usdDnCurrencyRate =  currencyRateService.findCurrencyRateWithCurrencyIdAndCurrencyToId(currencyUSD.getId(),
                baseCurrencyId);
        Assertions.assertEquals(usdDnCurrencyRate.getRateValue(), currentRateValue);
    }

    @Test
    @Transactional
    public void _05_convertBaseAmountToCurrencyOkay() {
        long baseCurrencyId = currencyService.getBaseCurrency().getId();
        Currency currencyTMT = currencyService.findByIsoNumber(CurrencyInfo.TMT.getIsoNumber());
        Currency currencyRUB = currencyService.findByIsoNumber(CurrencyInfo.RUB.getIsoNumber());
        //
        CurrencyRate baseCurrencyRate = currencyRateService.findCurrencyRateWithCurrencyIdAndCurrencyToId(currencyRUB.getId(),
                baseCurrencyId);
        Assertions.assertEquals(BigDecimal.valueOf(100, 2), baseCurrencyRate.getRateValue().setScale(2, RoundingMode.UNNECESSARY));
        //
        CurrencyRate tmt_CurrencyRate = currencyRateService.findCurrencyRateWithCurrencyIdAndCurrencyToId(currencyTMT.getId(),
                baseCurrencyId);
        Assertions.assertEquals(0, BigDecimal.valueOf(151343, 4).compareTo(tmt_CurrencyRate.getRateValue()));
        //
        tmt_CurrencyRate.setCommission(BigDecimal.ZERO);
        currencyRateService.save(tmt_CurrencyRate);
        CurrencyConverter currencyConverterCommissionZero = currencyConverterFactory.createByCurrencyCodeToBase(currencyTMT.getIsoCode());
        // 1000 RUB / 15.1343 = 66.08 TMT
        BigDecimal amount1000RubInTmtZeroCommission = currencyConverterCommissionZero.convertFromBase(BigDecimal.valueOf(1000)); // 1000 RUB = <?> TMT
        Assertions.assertEquals(BigDecimal.valueOf(6608, 2), amount1000RubInTmtZeroCommission);
        // 2000 RUB / 15.1343 = 132.15 TMT
        BigDecimal amount2000RubInTmtZeroCommission = currencyConverterCommissionZero.convertFromBase(BigDecimal.valueOf(2000)); // 1000 RUB = <?> TMT
        Assertions.assertEquals(BigDecimal.valueOf(13215, 2), amount2000RubInTmtZeroCommission);
        //
        tmt_CurrencyRate.setCommission(BigDecimal.valueOf(115, 2));
        currencyRateService.save(tmt_CurrencyRate);
        CurrencyConverter currencyConverterCommission1_15 = currencyConverterFactory.createByCurrencyCodeToBase(currencyTMT.getIsoCode());
        // 1000 RUB / 15.1343 - 1.15% = 65.32 TMT
        BigDecimal amount1000RubInTmt1_15Commission = currencyConverterCommission1_15.convertFromBase(BigDecimal.valueOf(1000)); // 1000 RUB = <?> TMT
        Assertions.assertEquals(BigDecimal.valueOf(6532, 2), amount1000RubInTmt1_15Commission);
        // 2000 RUB / 15.1343 - 1.15% = 130.65 TMT
        BigDecimal amount2000RubInTmt1_15Commission = currencyConverterCommission1_15.convertFromBase(BigDecimal.valueOf(2000)); // 1000 RUB = <?> TMT
        Assertions.assertEquals(BigDecimal.valueOf(13065, 2), amount2000RubInTmt1_15Commission);
    }

    @Test
    @Transactional
    public void _06_convertCurrencyToBaseAmountOkay() {
        long baseCurrencyId = currencyService.getBaseCurrency().getId();
        Currency currencyTMT = currencyService.findByIsoNumber(CurrencyInfo.TMT.getIsoNumber());
        Currency currencyRUB = currencyService.findByIsoNumber(CurrencyInfo.RUB.getIsoNumber());
        //
        CurrencyRate baseCurrencyRate = currencyRateService.findCurrencyRateWithCurrencyIdAndCurrencyToId(currencyRUB.getId(),
                baseCurrencyId);
        Assertions.assertEquals(BigDecimal.valueOf(100, 2), baseCurrencyRate.getRateValue().setScale(2, RoundingMode.UNNECESSARY));
        //
        CurrencyRate tmt_CurrencyRate = currencyRateService.findCurrencyRateWithCurrencyIdAndCurrencyToId(currencyTMT.getId(),
                baseCurrencyId);
        Assertions.assertEquals(0, BigDecimal.valueOf(151343, 4).compareTo(tmt_CurrencyRate.getRateValue()));
        //
        tmt_CurrencyRate.setCommission(BigDecimal.ZERO);
        currencyRateService.save(tmt_CurrencyRate);
        CurrencyConverter currencyConverterCommissionZero = currencyConverterFactory.createByCurrencyCodeToBase(currencyTMT.getIsoCode());
        // 66.08 TMT x 15.1343 = 1000 RUB
        ConversionResult amount1000TmtInRubZeroCommission = currencyConverterCommissionZero
                .revertConversionFromBase(BigDecimal.valueOf(6608, 2));
        org.assertj.core.api.Assertions.assertThat(BigDecimal.valueOf(100007, 2)).isEqualByComparingTo(amount1000TmtInRubZeroCommission.getConvertResult());
        org.assertj.core.api.Assertions.assertThat(BigDecimal.valueOf(15_1343, 4)).isEqualByComparingTo(amount1000TmtInRubZeroCommission.getEffectiveRate());
        // 132.15 TMT x 15.1343 = 2000 RUB
        ConversionResult amount2000TmtInRubZeroCommission = currencyConverterCommissionZero
                .revertConversionFromBase(BigDecimal.valueOf(13215, 2));
        org.assertj.core.api.Assertions.assertThat(BigDecimal.valueOf(200000, 2)).isEqualByComparingTo(amount2000TmtInRubZeroCommission.getConvertResult());
        org.assertj.core.api.Assertions.assertThat(BigDecimal.valueOf(15_1343, 4)).isEqualByComparingTo(amount2000TmtInRubZeroCommission.getEffectiveRate());
        //
        tmt_CurrencyRate.setCommission(BigDecimal.valueOf(115, 2));
        currencyRateService.save(tmt_CurrencyRate);
        CurrencyConverter currencyConverterCommission1_15 = currencyConverterFactory.createByCurrencyCodeToBase(currencyTMT.getIsoCode());
        // 65.32 TMT x 15.1343 - 1.15% = ? RUB
        ConversionResult amount1000TmtInRub1_15Commission = currencyConverterCommission1_15
                .revertConversionFromBase(BigDecimal.valueOf(6532, 2)); // 1000 RUB = <?> TMT
        org.assertj.core.api.Assertions.assertThat(BigDecimal.valueOf(99994, 2)).isEqualByComparingTo(amount1000TmtInRub1_15Commission.getConvertResult());
        org.assertj.core.api.Assertions.assertThat(BigDecimal.valueOf(15_30834445, 8)).isEqualByComparingTo(amount1000TmtInRub1_15Commission.getEffectiveRate());
        // 130.65 TMT x 15.1343 - 1.15% = ? RUB
        ConversionResult amount2000TmtInRub1_15Commission = currencyConverterCommission1_15
                .revertConversionFromBase(BigDecimal.valueOf(13065, 2));
        org.assertj.core.api.Assertions.assertThat(BigDecimal.valueOf(200004, 2)).isEqualByComparingTo(amount2000TmtInRub1_15Commission.getConvertResult());
        org.assertj.core.api.Assertions.assertThat(BigDecimal.valueOf(15_30834445, 8)).isEqualByComparingTo(amount2000TmtInRub1_15Commission.getEffectiveRate());
    }

}
