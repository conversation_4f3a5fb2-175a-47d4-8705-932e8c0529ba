package ru.oskelly.tests.build_01.domain.service.adminpanel.product;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.AuthenticationCredentialsNotFoundException;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.model.device.Device;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.product.SalesChannel;
import su.reddot.domain.model.size.SizeType;
import su.reddot.domain.service.adminpanel.product.AdminProductService;
import su.reddot.domain.service.adminpanel.product.AdminProductService.SaveProductSizeData;
import su.reddot.domain.service.adminpanel.view.ProductEditorView;
import su.reddot.domain.service.adminpanel.view.SizeView;
import su.reddot.domain.service.adminpanel.view.TableFilterView;
import su.reddot.domain.service.commission.CommissionService;
import su.reddot.domain.service.device.DeviceService;
import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.domain.service.image.ImageService;
import su.reddot.domain.service.product.ProductService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.security.SecurityService;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class OPProductServiceTest extends AbstractSpringTest {

    @Autowired
    private AdminProductService adminProductService;

    @Autowired
    private ProductService productService;

    @Autowired
    private CommissionService commissionService;

    @Autowired
    private UserService userService;

    @MockBean
    private DeviceService deviceService;

    @MockBean
    private ImageService imageService;

    @MockBean
    private SecurityService securityService;

    /**
     * Недоступность сервисов для неавторизованных
     */
    @Test
    public void getCatalogProductPage_unauthorized_throws() {
        Assertions.assertThrows(AuthenticationCredentialsNotFoundException.class, () -> {
            adminProductService.getCatalogProductPage(null, 0, null);
        });
    }

    /**
     * Недоступность сервисов с Authority != PRODUCT_MODERATION, ADMIN, RETOUCHING_MODERATION
     */
    @Test
    @WithMockUser(authorities = {"USER_MODERATION", "AUTHORITY_MODERATION", "CONTENT_CREATE", "CONTENT_DELETE", "ORDER_MODERATION", "CAN_VIEW_ALL_PRODUCTS", "OFFER_MODERATION"})
    public void getCatalogProductPage_otherAuthority_throws() {
        Assertions.assertThrows(AccessDeniedException.class, () -> {
            adminProductService.getCatalogProductPage(null, 0, null);
        });
    }

    /**
     * Недоступность анонимного вывоза метода
     */
    @Test
    public void getAvailablePurchaseYearsWithMarkedActive_unauthorized_throws() {
        Assertions.assertThrows(AuthenticationCredentialsNotFoundException.class, () -> {
            adminProductService.getAvailablePurchaseYearsWithMarkedActive(0l);
        });
    }

    /**
     * Товаров по указанным критериям нет
     */
    @Test
    @WithMockUser(authorities = {"PRODUCT_MODERATION"})
    public void getCatalogProductPage_unexistedCategories_productsNotFound_returnEmptyResult() {
        ProductService.FilterSpecification filterSpecification = new ProductService.FilterSpecification().categoriesIds(Arrays.asList(555555555555l, 66666666666666l, 77777777777l));
        int pageNumber = 1;
        ProductService.SortAttribute sortAttribute = ProductService.SortAttribute.ID;
        Page<ProductDTO> productsList = adminProductService.getCatalogProductPage(filterSpecification, pageNumber, sortAttribute);
        assertTrue(productsList.getItems().size() == 0 && productsList.getTotalAmount() == 0 && productsList.getTotalPages() == 0);
    }

    /**
     * Проверка работы конструктора фильтра по продавцам-бутикам в админке модератора
     */
    @Test
    @WithMockUser(authorities = {"PRODUCT_MODERATION"})
    public void getTableFiltersProSellers() {
        ProductService.FilterSpecification filterSpecification = new ProductService.FilterSpecification();

        // проверка, что списка фильтров по продавцам не будет, если не выбран конкретный тип
        filterSpecification.isPro(false);
        Map<String, TableFilterView> tableFilters = adminProductService.getTableFilters(filterSpecification);
        TableFilterView filterSeller = tableFilters.get("filterSeller");
        assertTrue(filterSeller.getValues().isEmpty());

        // проверка, что вернется список фильтров по продавцам, если выбран тип Бутики
        filterSpecification.isPro(true);
        tableFilters = adminProductService.getTableFilters(filterSpecification);
        filterSeller = tableFilters.get("filterSeller");
        assertFalse(filterSeller.getValues().isEmpty());

        // проверка того, что несмотря на отсутствие типа продавца,
        // список фильтрации продавцов заполнен из-за наличия активной фильтрации по продавцам
        filterSpecification.isPro(false);
        filterSpecification.sellerIds(Collections.singletonList(1L));
        tableFilters = adminProductService.getTableFilters(filterSpecification);
        filterSeller = tableFilters.get("filterSeller");
        assertFalse(filterSeller.getValues().isEmpty());
    }

    /**
     * Проверка на произвольной комиссии на товар.
     */
    @Test
    @WithMockUser(authorities = {"ADMIN"})
    @Transactional
    public void productWithCustomCommissionPrices() {
        Mockito.when(securityService.hasAnyAuthority(Mockito.any()))
                .thenReturn(true);
        Mockito.when(securityService.getCurrentAuthorizedUser())
                .thenReturn(userService.getUserByEmail("<EMAIL>"));
        Mockito.when(deviceService.registerDevice(Mockito.any(Device.class)))
                .thenReturn(new Device());
        Mockito.doNothing()
                .when(imageService)
                .setProductImagesOrder(Mockito.any(Product.class), Mockito.anyList());

        ProductService.FilterSpecification filter = new ProductService.FilterSpecification()
                .states(Collections.singletonList(ProductState.NEED_MODERATION));
        Optional<Long> productIdOptional = adminProductService
                .getCatalogProductPage(filter, 1, null).getItems().stream()
                .findFirst()
                .map(ProductDTO::getProductId);

        if (!productIdOptional.isPresent()) {
            fail("Product for edit is not found");
        }

        Long productId = productIdOptional.get();

        AdminProductService.SaveProductRequest request = getRequest(productId);

        adminProductService.saveProduct(request);

        ProductEditorView view = adminProductService.getProductEditorView(productId);

        assertEquals(request.getCurrentPrice(), view.getCurrentPrice());
        assertEquals(request.getSellerPrice(), view.getSellerPrice());

        request.getSizesData().forEach((sizeId, sizeData) -> {
            Optional<SizeView> sizeViewOpt = view.getSizes().stream()
                    .filter(s -> s.getId().equals(sizeId))
                    .findAny();
            if (!sizeViewOpt.isPresent()) {
                fail();
            }
            SizeView sizeView = sizeViewOpt.get();
            assertEquals(sizeData.getCount(), sizeView.getSizeCount());
            assertEquals(sizeData.getCustomType(), sizeView.getCustomType());
            assertEquals(sizeData.getCustomValue(), sizeView.getCustomValue());
        });

        Product product = productService.getRawProduct(productId, ProductService.UserType.SYSTEM).get();
        assertThat(
                commissionService
                        .calculateCustomCommission(
                                product.getCurrentPrice(),
                                product.getCurrentPriceWithoutCommission())
                        .multiply(BigDecimal.valueOf(100)),
                Matchers.comparesEqualTo(view.getCommission()));
    }

    private AdminProductService.SaveProductRequest getRequest(Long productId) {
        AdminProductService.SaveProductRequest request = new AdminProductService.SaveProductRequest();

        request.setProductId(productId);
        request.setDescription("ОВЕРШОТ A-COLD-WALL* LASDUN SHIRT");
        request.setProductConditionId(1L);
        request.setPurchasePrice(BigDecimal.ZERO);
        request.setCurrentPrice(BigDecimal.valueOf(50000));
        request.setSellerPrice(BigDecimal.valueOf(10000));
        request.setModel("");
        request.setVendorCode("");
        request.setSerialNumber("");
        request.setStoreCode("");
        request.setVintage(false);
        AdminProductService.ProductAttributeValue attributeValue1 = new AdminProductService.ProductAttributeValue();
        attributeValue1.setAttributeId(3L);
        attributeValue1.setAttributeValueId(50L);
        AdminProductService.ProductAttributeValue attributeValue2 = new AdminProductService.ProductAttributeValue();
        attributeValue2.setAttributeId(10L);
        attributeValue2.setAttributeValueId(129L);
        request.setAttributes(Lists.newArrayList(attributeValue1, attributeValue2));
        request.setCategoryId(114L);
        request.setBrandId(2636L);
        request.setSizeType(SizeType.INT);
        AdminProductService.SaveProductSellerRequest seller = new AdminProductService.SaveProductSellerRequest();
        seller.setName("Name");
        seller.setPhone("+79991234567");
        seller.setCity("Казань");
        seller.setAddress("ул. Баумана, д. 34");
        request.setSeller(seller);
        request.setOurChoice(false);
        request.setBeegz(false);
        request.setNewCollection(false);
        request.setAtOffice(false);
        request.setProductState(ProductState.NEED_MODERATION);
        request.setImageOrderedIds(Lists.newArrayList(1L, 2L));
        request.setSizesData(
                ImmutableMap.<Long, SaveProductSizeData>builder()
                        .put(78L, new SaveProductSizeData().setCount(1))
                        .build());
        request.setCustomCommission(true);
        request.setSalesChannel(SalesChannel.WEBSITE);

        return request;
    }

}
