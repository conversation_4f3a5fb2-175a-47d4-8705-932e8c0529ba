package ru.oskelly.tests.build_01.domain.service.address;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import lombok.NonNull;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.address.AddressRepository;
import su.reddot.domain.exception.OskellyException;
import su.reddot.domain.model.address.Address;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.address.AddressService;
import su.reddot.domain.service.dto.AddressDTO;
import su.reddot.domain.service.user.UserService;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class AddressServiceTest extends AbstractSpringTest {
    @Autowired
    private AddressService addressService;
	@Autowired
	private UserService userService;

    @Autowired
    private AddressRepository addressRepository;

	@Value("${test.api.user-id}")
	private Long userId;
	private static final String country = "Россия";
	private static final String region = "Тамбовская обл.";
	private static final String city = "г.Тамбов";
	private static final String city2 = "г.Москва";
	private static final String cityFiasId = "ea2a1270-1e19-4224-b1a0-4228b9de3c7a";
	private static final String zipCode = "435623";
	private static final String address = "ул.Пушкина, д.Колотушкина, кв.67";

	private static Long addressId;
	private static Long address2Id;

    private User getUser(){
		return userService.getUserById(userId).orElse(null);
    }

    private Address getAddress(){
    	return new Address()
				.setZipCode(zipCode)
				.setCountry(country)
				.setRegion(region)
				.setCity(city)
				.setCityFiasId(cityFiasId)
				.setAddress(address);
    }

	@NonNull
	private static AddressDTO getAddressDto() {
		return new AddressDTO()
				.setZipCode(zipCode)
				.setCountry(country)
				.setRegion(region)
				.setCity(city)
				.setCityFiasId(cityFiasId)
				.setAddress(address);
	}

    private Address saveAddressSuccessfull(Address address, User user){
	    Address savedAddress = addressService.saveAddress(user, address);
	    assertNotNull(savedAddress);
	    assertNotNull(savedAddress.getId());
	    assertNotNull(savedAddress.getUser());
	    return savedAddress;
    }

    private void cleanup(){
		Address oldTestAddress = addressRepository.findFirstByUserAndZipCodeAndCountryAndRegionAndCityAndAddressAndAddress2AndAddress3AndCityFiasIdAndSettlementFiasIdAndDeleteTimeIsNull(getUser(), zipCode, country, region, city, address, null, null, null, null);
		if(oldTestAddress != null) addressRepository.delete(oldTestAddress);
	    Address oldTestAddress2 = addressRepository.findFirstByUserAndZipCodeAndCountryAndRegionAndCityAndAddressAndAddress2AndAddress3AndCityFiasIdAndSettlementFiasIdAndDeleteTimeIsNull(getUser(), zipCode, country, region, city2, address, null, null, null, null);
	    if(oldTestAddress2 != null) addressRepository.delete(oldTestAddress2);
    }

    @Test
    public void _01_addressCreationSuccessfull(){
    	cleanup();
		long initialAddressesCount = addressRepository.count();
		User user = getUser();
		Address address = getAddress();
		Address savedAddress = saveAddressSuccessfull(address, user);
	    long newAddressesCount = addressRepository.count();
	    assertEquals(newAddressesCount, initialAddressesCount + 1);
	    addressId = savedAddress.getId();
    }

	@Test
	public void _02_addressCreationSameReturnsOldAddress(){
		long initialAddressesCount = addressRepository.count();
		User user = getUser();
		Address address = getAddress();
		Address savedAddress = saveAddressSuccessfull(address, user);
		long newAddressesCount = addressRepository.count();
		assertEquals(newAddressesCount, initialAddressesCount);
		assertEquals(savedAddress.getId(), addressId);
	}

	@Test
	public void _03_getAddressReturnsOldAddress(){
		long initialAddressesCount = addressRepository.count();
		User user = getUser();
		Address returnedAddress = addressService.getAddress(user, getAddressDto());
		long newAddressesCount = addressRepository.count();
		assertEquals(newAddressesCount, initialAddressesCount);
		assertEquals(returnedAddress.getId(), addressId);
	}

	@Test
	public void _04_addressChangeCreatesNewAddress(){
		long initialAddressesCount = addressRepository.count();
		User user = getUser();
		Address address = addressService.findAddressById(addressId);
		address.setCity(city2);
		Address savedAddress = saveAddressSuccessfull(address, user);
		long newAddressesCount = addressRepository.count();
		assertEquals(newAddressesCount, initialAddressesCount + 1);
		assertNotEquals(savedAddress.getId(), addressId);
		address2Id = savedAddress.getId();
	}

	@Test
	public void _05_findAllUserAddressesSuccessfull(){
		User user = getUser();
		List<Address> userAddresses = addressService.findAllUserAddresses(user);
		List<Long> userAddressesIds = userAddresses.stream().map(Address::getId).collect(Collectors.toList());
		assertTrue(userAddressesIds.contains(addressId));
		assertTrue(userAddressesIds.contains(address2Id));
	}

	private void validateSaveFailWithFiasId(Address address, String failText) {
		try {
			addressService.saveAddress(address.getUser(), address);
			Assertions.fail("Unreachable code: holdOrderPromoCode must throw exception");
		} catch (OskellyException oex) {
			Assertions.assertThat(oex.getMessage()).matches("Адрес .*: некорректные данные ФИАС ID: " + failText);
		}
	}

	@Test
	public void _06_saveFiasIdTest(){
		Address address = addressService.findAddressById(addressId);
		address.setCity(UUID.randomUUID().toString());
		//
		address.setFiasId(UUID.randomUUID().toString());
		address.setSettlementFiasId(UUID.randomUUID().toString());
		address.setCityFiasId(UUID.randomUUID().toString());
		address.setRegionFiasId(UUID.randomUUID().toString());
		//
		address.setFiasId("setFiasId");
		validateSaveFailWithFiasId(address, "setFiasId");
		address.setFiasId(UUID.randomUUID().toString());
		//
		address.setSettlementFiasId("setSettlementFiasId");
		validateSaveFailWithFiasId(address, "setSettlementFiasId");
		address.setSettlementFiasId(UUID.randomUUID().toString());
		//
		address.setCityFiasId("setCityFiasId");
		validateSaveFailWithFiasId(address, "setCityFiasId");
		address.setCityFiasId(UUID.randomUUID().toString());
		//
		address.setRegionFiasId("setRegionFiasId");
		validateSaveFailWithFiasId(address, "setRegionFiasId");
		address.setRegionFiasId(UUID.randomUUID().toString());
		//
		Address savedAddress = saveAddressSuccessfull(address, address.getUser());
		Assertions.assertThat(savedAddress.getId()).isNotEqualTo(addressId);
		//
		Address addressWithOne = addressService.findAddressById(addressId);
		Address addressWithTwo = addressService.findAddressById(savedAddress.getId());
		//
		Assertions.assertThat(addressWithOne.getFiasId()).isNotEqualTo(addressWithTwo.getFiasId());
		Assertions.assertThat(addressWithTwo.getFiasId()).isEqualTo(address.getFiasId());
		//
		Assertions.assertThat(addressWithOne.getSettlementFiasId()).isNotEqualTo(addressWithTwo.getSettlementFiasId());
		Assertions.assertThat(addressWithTwo.getSettlementFiasId()).isEqualTo(address.getSettlementFiasId());
		//
		Assertions.assertThat(addressWithOne.getCityFiasId()).isNotEqualTo(addressWithTwo.getCityFiasId());
		Assertions.assertThat(addressWithTwo.getCityFiasId()).isEqualTo(address.getCityFiasId());
		//
		Assertions.assertThat(addressWithOne.getRegionFiasId()).isNotEqualTo(addressWithTwo.getRegionFiasId());
		Assertions.assertThat(addressWithTwo.getRegionFiasId()).isEqualTo(address.getRegionFiasId());
	}

	@Test
	public void _07_saveBlanksFiasIdTest() {
		Address address = addressService.findAddressById(addressId);
		address.setCity(UUID.randomUUID().toString());
		//
		address.setFiasId("");
		address.setSettlementFiasId("");
		address.setCityFiasId("");
		address.setRegionFiasId("");
		//
		Address savedAddress = saveAddressSuccessfull(address, address.getUser());
		Assertions.assertThat(savedAddress.getId()).isNotEqualTo(addressId);
		//
		Address addressWithBlanks = addressService.findAddressById(savedAddress.getId());
		Assertions.assertThat(addressWithBlanks.getFiasId()).isNull();
		Assertions.assertThat(addressWithBlanks.getSettlementFiasId()).isNull();
		Assertions.assertThat(addressWithBlanks.getCityFiasId()).isNull();
		Assertions.assertThat(addressWithBlanks.getRegionFiasId()).isNull();
	}

	@Test
	public void _99_cleanup(){
    	addressRepository.deleteById(addressId);
		addressRepository.deleteById(address2Id);
		cleanup();
	}
}
