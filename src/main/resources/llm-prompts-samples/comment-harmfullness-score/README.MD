Шаблон промпта разбит на две части: шаблон контекст и шаблон непосредственно промпта для указания в соответствующих полях в запросе к llm.
Актуальные версии шаблонов контекста и промпта задаются в БД oskelly в таблице config_param с соответствующими ключами:
- шаблон контекста - по ключу `comment.harmfulness.score.calc.llm.context.template`
- шаблон промпта - по ключу `comment.harmfulness.score.calc.llm.prompt.template`