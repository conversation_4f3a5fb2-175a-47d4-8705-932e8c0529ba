# Профиль запуска приложения по умолчанию
spring:
  datasource:
    #hikari:
      # moved to 1 level up
      #jdbc-url: ${DATABASELINK:****************************************}
      # moved to 1 level up
      #driver-class-name: org.postgresql.Driver
      #leak-detection-threshold: 30000
      #maximum-pool-size: 20
      #connection-timeout: 60000
      # moved to 1 level up
      #username: oskelly
      # moved to 1 level up
      #password: qwerty
    type: com.zaxxer.hikari.HikariDataSource
    jdbc-url: ${DATABASELINK:****************************************}
    username: ${DATABASEUSER:oskelly}
    password: ${DATABASEPASS:qwerty}
    maximum-pool-size: 20
    leak-detection-threshold: 30000
    connection-timeout: 60000
    driver-class-name: org.postgresql.Driver
    readonly: false

  data:
    mongodb:
      #uri:
      auto-index-creation: true


  #Для фидов при тестировании и локальной разработке можно использовать основную базу
  #На продакшене данные по фидам будут храниться в отдельной базе
  #Все таблицы по фидам будут создаваться в отдельной схеме feed
  feed-datasource:
    #hikari:
      #leak-detection-threshold: 30000
      #maximum-pool-size: 20
      #connection-timeout: 60000
    type: com.zaxxer.hikari.HikariDataSource
    #recommended: jdbc-url: ${FEEDDATABASELINK:********************************************}
    jdbc-url: ${FEEDDATABASELINK:****************************************}
    #recommended: username: oskellyfeed
    username: ${FEEDDATABASEUSER:oskelly}
    password: ${FEEDDATABASEPASS:qwerty}
    maximum-pool-size: 20
    leak-detection-threshold: 30000
    connection-timeout: 60000

    schema: feed
    driver-class-name: org.postgresql.Driver

  thymeleaf:
    cache: false
    mode: HTML
    #prefix: file:///home/<USER>/IdeaProjects/olab/oskelly-monolith/src/main/resources/templates/ # 4Debug, must point to a valid folder (e. g. same folder as resource tempalte folder)

  jpa:
    database: postgresql
    show-sql: false
    hibernate:
      ddl-auto: validate
      types:
        print:
          banner: false
    database-platform: org.hibernate.dialect.PostgreSQLDialect

  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      # При десериализации времени не преобразовывать его часовой пояс в часовой пояс системы (которое равно +0:00 по-умолчанию)
      # То есть если в json указано время 2000-01-01T10:00:00+3:00, то и в системе оно будет
      # иметь часовой пояс +3:00. Иначе jackson преобразует это время во время в часовом поясе UTC (0:00): 2000-01-01T07:00:00Z
      adjust-dates-to-context-time-zone: false

  mvc:
    #Формат для всех параметров дат в контроллерах
    date-format: "yyyy-MM-dd'T'HH:mm:ss'Z'"


  http:
    multipart:
      max-file-size: 8MB
      max-request-size: 8MB

  servlet:
    multipart:
      max-file-size: 8MB
      max-request-size: 8MB

  session:
    store-type: jdbc
    readonly: false
    jdbc:
      readonly: false

  flyway:
    placeholders:
      internationalVersion: ${internationalVersion}
    baseline-on-migrate: false
    locations: classpath:/migrations/prod
    schemas: public
    table: schema_version
    enabled: true

  feed-flyway:
    baseline-on-migrate: false
    locations: classpath:/migrations/feed
    schemas: feed
    table: schema_version_feed
    enabled: false

  rabbitmq:
    enabled: ${RABBITENABLED:true}
    virtual-host: ${RABBITMQVHOST:/}
    host: ${RABBITMQHOST:localhost}
    port: ${RABBITMQPORT:5672}
    username: oskelly
    password: qwerty
    listener:
      simple:
        default-requeue-rejected: true
        retry:
          enabled: true
          initial-interval: 3s
          max-attempts: 3
          max-interval: 10s
          multiplier: 2

  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}
    properties:
      security.protocol: ${KAFKA_SECURITY_PROTOCOL:SASL_PLAINTEXT}   # см. org.apache.kafka.common.security.auth.SecurityProtocol
      sasl.mechanism: ${KAFKA_SASL_MECHANISM:SCRAM-SHA-512}       # см. org.apache.kafka.common.config.SaslConfigs, https://www.iana.org/assignments/sasl-mechanisms/sasl-mechanisms.xhtml
      sasl.jaas.config: ${KAFKA_SASL_JAAS_CONFIG:org.apache.kafka.common.security.scram.ScramLoginModule required username="${app.kafka.username}" password="${app.kafka.password}";}
    consumer:
      group-id: ${KAFKA_GROUP_ID:core}
      auto-offset-reset: earliest
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        spring.json.trusted.packages: "su.reddot,ru.oskelly,*"
    producer:
      properties:
        max.block.ms: 2000
        retry.backoff.ms: 500
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    admin:
      properties:
        sasl.jaas.config: ${KAFKA_ADMIN_SASL_JAAS_CONFIG:org.apache.kafka.common.security.scram.ScramLoginModule required username="${app.kafka.admin.username}" password="${app.kafka.admin.password}";}


application-feature-flags:
  enable-card-binding: true
  enable-payout-to-bound-card: true
  enable-payment-from-bound-card: true
  enable-payout-to-bank-account-for-individuals: true
  enable-new-home: false
  enable-android-new-home: false
  enable-product-publication: true
  support-channel: usedesk # no helpcrunch anymore

services:
  onec:
    enable-rollback-1C-refund: false

web-feature-flags:
  support-channel: usedesk # no helpcrunch anymore

app:
  graceful-shutdown:
    enabled: false
    timeout: 30s
  language: ru
  country: RU
  default-country:
    iso-code-alpha2: RU
    default-country-id: 191

  device:
#    RULocales: hy_AM,az,az_Cyrl,az_Cyrl_AZ,az_Latn,az_Latn_AZ,be_BY,kk_Cyrl_KZ,KG,ru,ru_RU,ru_UA,TJ,uz,uz_Arab,uz_Arab_AF,uz_Cyrl,uz_Cyrl_UZ,uz_Latn,uz_Latn_UZ,uk_UA,TM
    RULocales: ru,ru_RU,ru-RU
    RUStores: AZE,ARM,UKR,TKM,BLR,KAZ,KGZ,UZB,RUS,TJK

  experiments:
    flagr:
      host: https://flagr-dev.oskelly.tech
      tag: development
      debug: true
      httpclient:
        connect-timeout: 10000
        read-timeout: 10000

  maxmind:
    db-file-path: /build/maxmind/geo.mmdb

  google-cloud:
    api-key: empty

  yandex-metrika:
    access-token: y0_AgAAAAB1kNK_AAumvgAAAAECdaA0AADI9cfvAcZEZast-Xs9gS1pqR6kkA
    counter-id: 44496388

  google-auth:
    client-id: ${GOOGLE_CLIENT_ID:}
    android-client-id: ${GOOGLE_CLIENT_ID_ANDROID:}
    client-secret: ${GOOGLE_CLIENT_SECRET:}

  yandex-auth:
    client-id: ${YANDEX_CLIENT_ID:}
    client-secret: ${YANDEX_CLIENT_SECRET:}

  httpclient:
    connect-timeout: 120000
    connection-request-timeout: 120000
    socket-timeout: 120000
    oauth:
      connect-timeout: 10000
      connection-request-timeout: 10000
      socket-timeout: 10000

  cache:
    storage-type: local # тип хранения кэша - local или redis
    life-period-seconds: 600
    long-life-period-seconds: 3600
    warm-up-at-start: false
  async:
    common:
      core-pool-size: 10
      max-pool-size: 50
      queue-capacity: 100
    products-to-external-catalog-sender:
      core-pool-size: 4
      max-pool-size: 8
      queue-capacity: 100
    osocial-sync:
      core-pool-size: 1
      max-pool-size: 2
      queue-capacity: 10

  host: http://localhost:8080

  #Для обновления залипших кэшей статик файлов CSS/JS/...
  webVersion: 2.4.4

  apiVersion: 2.2.1

  user-ban:
    auto-cancel-task:
      enabled: false
      cron: "0 0/30 * * * *"
    shadow-ban:
      enabled: false
      new-user-max-time-from-registration: 7d
      addition:
        enabled: true
        comments-in-a-row:
          harmful-count: 4
        comments-in-a-window:
          enabled: false
          window: 5m
          harmful-count: 4
      cancellation:
        enabled: false
        comments-percent:
          window: 7d
          harmless-percent: 90

  stories:
    active: true
    executorPoolSize: 5
    maxExecutorPoolSize: 20
    executorPoolCapacity: 100
    timeout: 30s
    connectTimeout: 1s
    socketTimeout: 10s
    client:
      host: localhost:8083

  queue:
    #Выключатели для рассылки событий в AMQP
    producer:
      change:
        product:
          enabled: true
        user:
          enabled: true
        order:
          enabled: true
        cart:
          enabled: true
        wishlist:
          enabled: true
        device-mindbox-id:
          enabled: true
        order-delivery-confirmed:
          enabled: true
        environment:
          enabled: true
      logistic:
        order:
          enabled: true
      new:
        image:
          enabled: true
          priority: 5
        activity:
          enabled: true
        common-fanout:
          enabled: true
        agent-report-payment:
          enabled: true
        agent-report-repayment:
          enabled: true
        user-auth:
          enabled: true
        user-register:
          enabled: true
        user-subscription:
          #Передача событий о подписке/отписке пользователя на уведомления по email/push/sms/...
          enabled: true
        bargain:
          enabled: true
        product-comment:
          enabled: true
        concierge-form:
          enabled: true
        sale-request:
          enabled: true
      #stream:
        #new:
          #viewer:
            #enabled: true
      toggle:
        product-like:
          enabled: true
        brand-like:
          enabled: true
        following:
          enabled: true
      order-paid:
        enabled: true
      view:
        product:
          enabled: true
      bitrix:
        sale-request-deal:
          command:
            enabled: false
          event:
            enabled: false
      feed:
        order-paid:
          enabled: true
      email:
        seller:
          concierge:
            payout:
              enabled: false
    #Выключатели для приема событий из AMQP
    processor:
      #По умолчанию, события из Rabbit игнорятся, если они были отправлены тем же инстансом
      #Данная опция выключается для упрощения тестирования прогона событий через очередь (отправка и обработка на едином инстансе)
      skip-events-from-the-same-instance: true
      change:
        product:
          enabled: false
        user:
          enabled: false
        order:
          enabled: false
        cart:
          enabled: false
        wishlist:
          enabled: false
        device-mindbox-id:
          enabled: false
        order-delivery-confirmed:
          enabled: false
        environment:
          enabled: false
      email:
        seller:
          concierge:
            payout:
              enabled: false
      new:
        image:
          enabled: false
          maxPriority: 10
        activity:
          enabled: false
        common-fanout:
          enabled: false
        agent-report-payment:
          enabled: false
        agent-report-repayment:
          enabled: false
        user-auth:
          enabled: false
        user-register:
          enabled: false
        user-subscription:
          #Получение событий о подписке/отписке пользователя на уведомления по email/push/sms/...
          enabled: false
        bargain:
          enabled: false
        notification-publication:
          enabled: false
        story-published:
          enabled: false
        product-comment:
          enabled: false
        concierge-form:
          enabled: false
        sale-request:
          enabled: false
      #stream:
        #new:
          #viewer:
            #enabled: false
      toggle:
        product-like:
          enabled: false
        brand-like:
          enabled: false
        following:
          enabled: false
      order-paid:
        enabled: false
      view:
        product:
          enabled: false
      bitrix:
        sale-request-deal:
          command:
            enabled: false
          event:
            enabled: false
      order-state-update:
        enabled: false
      feed:
        order-paid:
          enabled: false

  kafka:
    # по пропертям с префиксом app.kafka... и постфиксом ...topic.name топики создаются автоматически
    # с учетом соответствующих name, partitionsCount, replicationFactor
    # см. su.reddot.infrastructure.configuration.kafka.KafkaTopicsConfig
    enabled: ${KAFKA_ENABLED:false}
    username: ${KAFKA_USER:oskelly}
    password: ${KAFKA_PASSWORD:oskelly123}
    historyTopic:
      name: ${KAFKA_HISTORY_TOPIC:kafka.core.historyTopic}
      partitionsCount: 1
      replicationFactor: 1
    sales-app-salesman-topic:
      enabled: ${KAFKA_SALES_APP_SALESMAN_TOPIC_ENABLED:false}
      name: ${KAFKA_SALES_APP_SALESMAN_TOPIC:salesman-events}
      partitionsCount: 1
      replicationFactor: 1
    sales-app-phone-verification-topic:
      enabled: ${KAFKA_SALES_APP_PHONE_VERIFICATION_TOPIC_ENABLED:false}
      name: ${KAFKA_SALES_APP_PHONE_VERIFICATION_TOPIC:phone-verification-events}
      partitionsCount: 1
      replicationFactor: 1
    confirmationsTopic:
      enabled: ${KAFKA_CONFIRMATIONS_TOPIC_ENABLED:false}
      name: ${KAFKA_CONFIRMATIONS_TOPIC:kafka.core.confirmationsTopic}
      partitionsCount: 1
      replicationFactor: 1
    community-status-events-topic:
      name: ${KAFKA_COMMUNITY_STATUS_EVENTS_TOPIC:community-status-events}
      listener:
        enabled: ${KAFKA_COMMUNITY_STATUS_EVENTS_TOPIC_ENABLED:false}
      partitionsCount: 1
      replicationFactor: 1
    community-user-transactions-topic:
      enabled: ${KAFKA_COMMUNITY_USER_TRANSACTIONS_TOPIC_ENABLED:false}
      name: ${KAFKA_COMMUNITY_USER_TRANSACTIONS_TOPIC:community-user-transactions}
      partitionsCount: 1
      replicationFactor: 1
    community-user-bans-topic:
      enabled: ${KAFKA_COMMUNITY_USER_BANS_TOPIC_ENABLED:false}
      name: ${KAFKA_COMMUNITY_USER_BANS_TOPIC:community-user-bans}
      partitionsCount: 1
      replicationFactor: 1
    notification-events-topic:
      name: ${KAFKA_NOTIFICATION_EVENTS_TOPIC:notification-events}
      listener:
        enabled: ${KAFKA_NOTIFICATION_EVENTS_TOPIC_ENABLED:false}
      partitionsCount: 1
      replicationFactor: 1
    completely-updating-catalog-commands-topic:
      name: ${KAFKA_COMPLETELY_UPDATING_CATALOG_COMMANDS_TOPIC:completely-updating-catalog-commands}
      partitionsCount: 1
      replicationFactor: 1
    updating-opencv-commands-topic:
      name: ${KAFKA_UPDATING_OPENCV_COMMANDS_TOPIC:updating-opencv-commands}
      partitionsCount: 1
      replicationFactor: 1
    eventually-updating-catalog-commands-topic:
      name: ${KAFKA_EVENTUALLY_UPDATING_CATALOG_COMMANDS_TOPIC:eventually-updating-catalog-commands}
      partitionsCount: 1
      replicationFactor: 1
    filter-subscriptions-product-commands-topic:
      name: ${KAFKA_FILTER_SUBSCRIPTIONS_PRODUCT_COMMANDS_TOPIC:filter-subscriptions-product-commands}
      partitionsCount: 1
      replicationFactor: 1
    filter-subscriptions-schema-sync-batch:
      name: ${KAFKA_FILTER_SUBSCRIPTIONS_SCHEMA_SYNC_BATCH_TOPIC:filter-subscriptions-schema-sync-batch}
      listener:
        enabled: ${KAFKA_FILTER_SUBSCRIPTIONS_SCHEMA_SYNC_BATCH_TOPIC_LISTENER_ENABLED:false}
        consumer:
          properties:
            max-poll-records: ${KAFKA_FILTER_SUBSCRIPTIONS_SCHEMA_SYNC_BATCH_TOPIC_MAX_POLL_RECORDS:100}
            max-poll-interval-ms: ${KAFKA_FILTER_SUBSCRIPTIONS_SCHEMA_SYNC_BATCH_TOPIC_MAX_POLL_INTERVAL_MS:500000}
    import2orderprocessing-topic:
      name: ${KAFKA_IMPORT2ORDERPROCESSING_TOPIC:import2orderprocessing}
      partitionsCount: 1
      replicationFactor: 1
    following-events-topic:
      name: ${KAFKA_FOLLOWING_EVENTS_TOPIC:following-events}
      sender:
        enabled: ${KAFKA_FOLLOWING_EVENTS_TOPIC_SENDER_ENABLED:false}
      partitionsCount: 1
      replicationFactor: 1
    user-status-events-topic:
      name: ${KAFKA_USER_STATUS_EVENTS_TOPIC:user-status-events}
      sender:
        enabled: ${KAFKA_USER_STATUS_EVENTS_TOPIC_SENDER_ENABLED:false}
      partitionsCount: 1
      replicationFactor: 1
    user-updated-events-topic:
      name: ${KAFKA_USER_UPDATED_EVENTS_TOPIC:user-updated-events}
      sender:
        enabled: ${KAFKA_USER_UPDATED_EVENTS_TOPIC_SENDER_ENABLED:false}
      partitionsCount: 1
      replicationFactor: 1
    osocial-events-topic:
      name: ${KAFKA_OSOCIAL_EVENTS_TOPIC:osocial-events}
      listener:
        enabled: ${KAFKA_OSOCIAL_EVENTS_TOPIC_ENABLED:false}
        consumer:
          properties:
            max-poll-records: 20
            max-poll-interval-ms: 600000
            session-timeout-ms: 30000
            heartbeat-interval-ms: 10000
      partitionsCount: 1
      replicationFactor: 1
    brand-like-events-topic:
      name: ${KAFKA_BRAND_LIKE_EVENTS_TOPIC:brand-like-events}
      sender:
        enabled: ${KAFKA_BRAND_LIKE_EVENTS_TOPIC_SENDER_ENABLED:false}
      partitionsCount: 1
      replicationFactor: 1
    bonuses-events-topic:
      name: ${KAFKA_BONUSES_EVENTS_TOPIC:bonuses-events}
      listener:
        enabled: ${KAFKA_BONUSES_EVENTS_TOPIC_ENABLED:false}
      partitionsCount: 1
      replicationFactor: 1
    bitrix-cross-border-topic:
      name: ${KAFKA_BITRIX_CROSS_BORDER_TOPIC:bitrix-cross-border}
      listener:
        enabled: ${KAFKA_BITRIX_CROSS_BORDER_TOPIC_ENABLED:false}
      partitionsCount: 1
      replicationFactor: 1
    loyalty-cards-events-topic:
      name: ${KAFKA_LOYALTYCARDS_EVENTS_TOPIC:loyalty-cards-events}
      sender:
        enabled: ${KAFKA_LOYALTYCARDS_EVENTS_TOPIC_ENABLED:false}
      partitionsCount: 1
      replicationFactor: 1
    loyalty-commands-topic:
      name: ${KAFKA_LOYALTY_COMMANDS_TOPIC:loyalty-commands}
      sender:
        enabled: ${KAFKA_LOYALTY_COMMANDS_TOPIC_ENABLED:false}
      partitionsCount: 1
      replicationFactor: 1
    loyalty-events-topic:
      name: ${KAFKA_LOYALTY_EVENTS_TOPIC:loyalty-events}
      listener:
        enabled: ${KAFKA_LOYALTY_EVENTS_TOPIC_ENABLED:false}
      partitionsCount: 1
      replicationFactor: 1
    search-events-topic:
      name: ${KAFKA_SEARCH_EVENTS_TOPIC:search-events}
      sender:
        enabled: ${KAFKA_SEARCH_EVENTS_TOPIC_SENDER_ENABLED:false}
      partitionsCount: 1
      replicationFactor: 1
    llm-proxy-commands-topic:
      name: ${KAFKA_LLM_PROXY_COMMANDS_TOPIC:llm-proxy-commands}
      sender:
        enabled: false
    llm-proxy-events-topic:
      name: ${KAFKA_LLM_PROXY_EVENTS_TOPIC:llm-proxy-events}
      listener:
        enabled: false
    bitrix-deal-update-requests-topic:
      name: ${KAFKA_BITRIX_DEAL_UPDATE_REQUESTS_TOPIC:bitrix-deal-update-requests}
      sender:
        enabled: ${KAFKA_BITRIX_DEAL_UPDATE_REQUESTS_TOPIC_ENABLED:false}
      partitionsCount: 1
      replicationFactor: 1

#    Не используем prefix topic, что бы не создавать топики автоматически. Мы их просто слушаем
    filter-subscriptions-new-products-individual:
      name: ${KAFKA_FILTER_SUBSCRIPTIONS_NEW_PRODUCTS_INDIVIDUAL_TOPIC:filter-subscriptions-new-product-individual}
      listener:
        enabled: ${KAFKA_FILTER_SUBSCRIPTIONS_NEW_PRODUCTS_INDIVIDUAL_LISTENER_ENABLED:false}
        consumer:
          properties:
            max-poll-records: ${KAFKA_FILTER_SUBSCRIPTIONS_NEW_PRODUCTS_INDIVIDUAL_MAX_POLL_RECORDS:100}
            max-poll-interval-ms: ${KAFKA_FILTER_SUBSCRIPTIONS_NEW_PRODUCTS_INDIVIDUAL_MAX_POLL_INTERVAL_MS:30000}
    filter-subscriptions-new-products-batch:
      name: ${KAFKA_FILTER_SUBSCRIPTIONS_NEW_PRODUCTS_BATCH_TOPIC:filter-subscriptions-new-product-batch}
      listener:
        enabled: ${KAFKA_FILTER_SUBSCRIPTIONS_NEW_PRODUCTS_BATCH_LISTENER_ENABLED:false}
        consumer:
          properties:
            max-poll-records: ${KAFKA_FILTER_SUBSCRIPTIONS_NEW_PRODUCTS_BATCH_MAX_POLL_RECORDS:100}
            max-poll-interval-ms: ${KAFKA_FILTER_SUBSCRIPTIONS_NEW_PRODUCTS_BATCH_MAX_POLL_INTERVAL_MS:30000}
    producer:
      object-value-common-producer:
        properties:
          max.block.ms: 2000
          retry.backoff.ms: 500
        value-serializer: su.reddot.infrastructure.configuration.kafka.MessageJsonSerializer
      product-to-external-catalog-sender-producer:
        properties:
          max.block.ms: 2000
          retry.backoff.ms: 200
        value-serializer: su.reddot.infrastructure.configuration.kafka.MessageJsonSerializer
    topic-auto-creating:
      enabled: false
    admin:
      username: ${KAFKA_ADMIN_USER:}
      password: ${KAFKA_ADMIN_PASSWORD:}

  streamsale:
    enabled: false
    bambuser:
      apikey: 6DP1wsiMyAWRoxU1vq9Bxf #DEV server
    broadcast-id: #Test instance
      a53278b0-5b6b-4962-bd8b-9e5122803a69
    job:
      cron: #if need disable only one cron replace cron pattern to -
        one-hour-reminder: "-" # Usersync period, enable on tasks with "0 0/2 * * * ?"
        five-minutes-reminder: "-" # Usersync period, enable on tasks with "0 0/1 * * * ?"
        send-notification: "-" # Usersync period, enable on tasks with "*/20 * * * * ?"
        auto-deleted: "-" # Usersync period, enable on master with "0 0/10 * * * ?"
        update-user-data: "-" # Usersync period, enable on tasks with "0 0/10 * * * ?"

  #ID системного пользователя поддержки (Профиль OSKELLY)
  supportUserId: 25952

  #ID разделов, товары которых не должны отображаться в подборках на главной
  indexPageProductsSetExceptCategoryIds: 292,355

  bots:
    instagram:
      baseUrl: https://instagram.com
      userAgent: Mozilla/5.0 (iPhone; CPU iPhone OS 13_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 Instagram **********.119 (iPhone10,3; iOS 13_4_1; ru_RU; ru-RU; scale=3.00; 1125x2436; 218808672)
      login: oskitkelly
      password: kawsberry2020
      proxy-host:
      proxy-port:

  static:
    host:

  file:
    host: https://oskelly.ru

  applink:
    app-scheme: oskelly
    ios:
      name: Oskelly
      store-id: 1339623403
    android:
      name: Oskelly
      package: ru.oskelly.app

  product:
    price-in-currency:
      update:
        scheduler:
          cron: "-"
        batch-size: 1000

    condition:
      newId: 1

  bitrix-deal-import:
    enabled:  ${BITRIX_DEAL_IMPORT_ENABLED:false}
    fixedDelay: 120000
    image:
      s3-publish-dir: images/deal-import/images/

  product-item:
    stocks:
      updating:
        batch:
          size: 10000
        scheduler:
          cron: "-"

  product-item-location:
    in-boutique:
      codes: BOUTIQUE_STOLESHNIKOV,BOUTIQUE_KUZNETSKY_BRIDGE
    in-stock:
      codes: WAREHOUSE

  product-checker:
    #Выключатель механизма проверки товаров
    enabled: false
    sold-products:
      #Скрытие проданных товаров каждые 300 сек
      updateDelayMillis: 300000
    pro-published-products:
      #Каждый вечер идет рассылка о новых опубликованных бутиками товарах (в 18:00)
      notification-task-cron: "0 0 18 * * *"
    slides:
      #Обновление кол-ва слайдов каждые 600 сек
      updateDelayMillis: 600000
      #endpoint предоставляющий данные о кол-ве слайдов для товаровб н.п. https://oskelly.ru/stories/slide/product/count. Пустой URL выключает обновление
      data-url:

  scheduled-tasks:
    create-admin-order-alerts:
      cron: "-" # enable it on one instance only (task)
    create-admin-product-alerts:
      cron: "-" # enable it on one instance only (task)
    remove-admin-alerts:
      cron: "-" # enable it on one instance only (master)
    track-delivery-state:
      cron: "-" # enable it on one instance only (task)
    validate-addresses:
      cron: "-" # enable it on one instance only (task)
    update-mindbox-ids:
      cron: "-"
      limit: 100
    exclusive-selection:
      cron: "-"

  master:
    #Адрес главного сервиса (для взаимодействия доп. сервисов с основным)
    url: http://localhost:8080
    #Данные пользователя для взимодействия с мастер-сервисом
    user-id: 1
    user-email: <EMAIL>
    user-password: hLgewYvvhjdfLb3475_-32
    client-user-agent: oskelly-ext-dev
    ftp-host: ***************
    ftp-1c-user: ftptest
    ftp-1c-pass: 4EXbeUoXc9afoVxT7hFf
    ftp-1c-remote-path: /1c/
    ftp-partner-user: ftptest
    ftp-partner-pass: 4EXbeUoXc9afoVxT7hFf
    ftp-partner-remote-path: /partner/
    ftp-social-user: ftptest
    ftp-social-pass: 4EXbeUoXc9afoVxT7hFf
    ftp-social-remote-path: /social/
    ftp-marketing-user: ftptest
    ftp-marketing-pass: 4EXbeUoXc9afoVxT7hFf
    ftp-marketing-remote-path: /marketing/
    ftp-accounting-user: ftptest
    ftp-accounting-pass: 4EXbeUoXc9afoVxT7hFf
    ftp-accounting-remote-path: /accounting/

  search:
    host: http://***************:8081
    cookies: osk, osk-search
#    Продуковый поиск по вторым главным
    banner-setting:
#      Кол-во уровней вложенности вторых главных учитывающихся при продуктовом поиске
      max-nesting-depth: 20
#      Типы контентных блоков (например Вертикальная подборка) учитывающиеся при поиске
      target-content-block-types: VERTICAL_COLLECTION,HORIZONTAL_COLLECTION,SHELF,FILTERABLE_SHELF

  feed-db:
    enabled: false
    #Синхронизация базы фидов
    sync:
      enabled: false
      product:
        #Перетягивание товаров таском
        enabled: false
        task-delay-millis: 60000
        count: 100
        #Обновление фида при изменении товара (по событию)
        on-change-enabled: false
      product-item:
        #Перетягивание товаров таском
        enabled: false
        task-delay-millis: 60000
        count: 100
        #Обновление фида при изменении товара (по событию)
        on-product-change-enabled: false
      user:
        #Перетягивание пользователей таском
        enabled: false
        task-delay-millis: 60000
        count: 100
        #Обновление фида при изменении пользователя (по событию)
        on-change-enabled: false
      order-position:
        #Перетягивание заказов таском
        enabled: false
        task-delay-millis: 60000
        count: 100
        #Обновление фида при изменении заказа (по событию)
        on-change-enabled: false
      order:
        enabled: false
    #Выгрузка фидов
    export:
      enabled: false
      product:
        yml:
          mindbox:
            enabled: false
            #Кол-во записей за один запрос
            rows-per-request: 5
            #Расписание выгрузки
            cron: "0 15 17 * * *"
            #Файл выгрузки
            filename: /home/<USER>/export-files/partner/yml_mindbox.xml
            #Файл публикации на S3
            publish-filename: /feed/mindbox/yml_mindbox.xml
        v2:
          #валюта в которую будет конвертирована цена продукта в выгрузке
          target-currency-code: RUB
          #Расписание выгрузки
          cron: "-"
          #Кол-во записей за один запрос
          rows-per-request: 10
          title: "Интернет-магазин Oskelly"
          link: "https://oskelly.ru/"
          description: "Дизайнерская женская одежда, обувь и аксессуары от бутиков и частных продавцов – Интернет-магазин Oskelly"
          vk:
            static-fields:
              android-tracking-url: #
              ios-tracking-url: #
              android-appLink: #
              ios-app-store-id: #
              mobile-link-enabled: true
            enabled: false
            description: "Оригинальные коллекции от <brand>. Покупайте со скидкой 10%."
            #Файл выгрузки
            filename: /home/<USER>/export-files/partner/feed_vk.xml
            #Директория для публикации в s3. Имя файла берется из локального имени файла. Если не указана, то грузится не будет.
            publish-dirname: /feed/v2/
            zip-result: false
          yandex:
            static-fields:
              android-tracking-url: #
              ios-tracking-url: #
              android-appLink: #
              ios-app-store-id: #
              mobile-link-enabled: true
            enabled: false
            description: "Оригинальные коллекции от <brand> на OSKELLY. Скидки до 90% на сайте."
            #Файл выгрузки
            filename: /home/<USER>/export-files/partner/feed_ya.xml
            #Директория для публикации в s3. Имя файла берется из локального имени файла. Если не указана, то грузится не будет.
            publish-dirname: /feed/v2/
            zip-result: false
          yandex-feed-yml:
            international-version: false
            enabled: false
            #Файл выгрузки
            filename: /home/<USER>/export-files/partner/yandex_feed_yml.xml
            #Директория для публикации в s3. Имя файла берется из локального имени файла. Если не указана, то грузится не будет.
            publish-dirname: /feed/v2/
            zip-result: false
          google:
            static-fields:
              mobile-link-enabled: false
            #              android-tracking-url: #
            #              ios-tracking-url: #
            #              android-appLink: #
            #              ios-app-store-id: #
            enabled: true
            #Файл выгрузки
            filename: /home/<USER>/export-files/partner/google_feed.xml
            #Директория для публикации в s3. Имя файла берется из локального имени файла. Если не указана, то грузится не будет.
            publish-dirname: /feed/v2/
            zip-result: false
          facebook:
            static-fields:
              android-tracking-url: #
              ios-tracking-url: #
              android-appLink: #
              ios-app-store-id: #
              mobile-link-enabled: true
            enabled: false
            #Файл выгрузки
            filename: /home/<USER>/export-files/partner/feed_fb.xml
            #Директория для публикации в s3. Имя файла берется из локального имени файла. Если не указана, то грузится не будет.
            publish-dirname: /feed/v2/
            zip-result: false
          admitad:
            enabled: false
            static-fields:
              mobile-link-enabled: false
#              Данные поля не используются для admitad
#              android-tracking-url: #
#              ios-tracking-url: #
#              android-appLink: #
#              ios-app-store-id: #
            #Файл выгрузки
            filename: /home/<USER>/export-files/partner/feed_admitad.xml
            #Директория для публикации в s3. Имя файла берется из локального имени файла. Если не указана, то грузится не будет.
            publish-dirname: /feed/v2/
            zip-result: false
      user:
        csv:
          mindbox:
            enabled: false
            #Кол-во записей за один запрос
            rows-per-request: 5
            #Расписание выгрузки
            cron: "0 15 15 * * *"
            #Файл выгрузки
            filename: /home/<USER>/export-files/partner/users_mindbox.csv
            #Разделитель записей
            delimiter: ";"
            #Заголовок файла (колонки через разделитель)
            header: "Email;MobilePhone;LastName;FirstName;MiddleName;BirthDate;IsSubscribedByEmail;CustomFieldClientApprovedAsSeller;CustomFieldClientBestFriend;CustomFieldClientCelebrity;CustomFieldClientCity;CustomFieldClientDeviceSystem;CustomFieldClientIdBuyer;CustomFieldClientIdSeller;CustomFieldClientLastSessionTime;CustomFieldClientRegistrationDate;CustomFieldClientSellerType;CustomFieldClientTrustedSeller;IsSubscribedByMobilePush"
      product-item:
        cron: "-"
        #Часть настроек используется app.feed-db.export.product.v2
        tabby:
          enabled: false
          static-fields:
            mobile-link-enabled: false
#              Данные поля не используются для tabby
#              android-tracking-url: #
#              ios-tracking-url: #
#              android-appLink: #
#              ios-app-store-id: #
          filename: /home/<USER>/export-files/partner/feed_tabby.xml
          publish-dirname: feed/v2/
          zip-result: false

      #Заказы (со стороны покупателя). Не путать с Продажами (идут ниже)
      order-order-position:
        csv:
          #Заказ-покупка
          mindbox:
            enabled: false
            #Кол-во записей за один запрос
            rows-per-request: 5
            #Расписание выгрузки
            cron: "0 14 15 * * *"
            #Файл выгрузки
            filename: /home/<USER>/export-files/partner/order_positions_mindbox.csv
            #Разделитель записей
            delimiter: ";"
            #Заголовок файла (колонки через разделитель)
            header: "ClientIdBuyer;OrderIdOrderNumberBuyer;OrderLastUpdateDateTimeUtc;OrderLineProductIds;OrderLineQuantity;OrderLinePriceOfLine;OrderTotalPrice;OrderLineStatus;OrderCustomFieldOrderDetailOrderClientIdSeller;OrderCustomFieldOrderDetailOrderClientSellerType;OrderCustomFieldOrderDetailOrderClientBuyerType;OrderPromoCode;OrderCustomFieldOrderDetailOrderDeliveryToCity;OrderCustomFieldOrderDetailOrderDeliveryFromCity;OrderCustomFieldOrderDetailOrderComission"
      sale-order-position:
        csv:
          #Заказ-продажа
          mindbox:
            enabled: false
            #Кол-во записей за один запрос
            rows-per-request: 5
            #Расписание выгрузки
            cron: "0 45 15 * * *"
            #Файл выгрузки
            filename: /home/<USER>/export-files/partner/sales_order_positions_mindbox.csv
            #Разделитель записей
            delimiter: ";"
            #Заголовок файла (колонки через разделитель)
            header: "ClientIdSeller;OrderIdOrderNumberSeller;OrderLastUpdateDateTimeUtc;OrderLineProductIds;OrderLineQuantity;OrderLinePriceOfLine;OrderTotalPrice;OrderLineStatus;OrderCustomFieldOrderDetailOrderClientIdBuyer;OrderCustomFieldOrderDetailOrderClientSellerType;OrderCustomFieldOrderDetailOrderClientBuyerType;OrderPromoCode;OrderCustomFieldOrderDetailOrderDeliveryToCity;OrderCustomFieldOrderDetailOrderDeliveryFromCity;OrderCustomFieldOrderDetailOrderComission"
      order:
        csv:
          yandex-metrika:
            enabled: false
            rows-per-request: 100
#            Задержка между генерациями csv в часах
            delay-in-hours: 24
            cron: "-"
#            Есть возможность динамически менять имя файла {UUID} -> на значения uuid в моменте
            local-file-name-template: /home/<USER>/export-files/partner/ya_metrika_feed_order_{UUID}.csv
            is-save-to-s3: false
            s3-path-to-save: feed/order/yandex/metrika/

  feedExport:
    enabled: false #Выключатель тасков выгрузки фидов (XML, HTML, CSV, YML)
    limitExport: true #Ограничивает выборки SQL-запросов выгрузки для тестирования (на боевом сервисе выгрузки эта опция должна быть выключена)
    yml:
      defaultPathToExportFile: /home/<USER>/export/
      s3PathToSave: /feed/yml
      filename: yml_oskelly.xml
      cron: "0 0 21 * * *"
      pageSize: 500
    yml-with-model:
      defaultPathToExportFile: /home/<USER>/export/
      filename: yml_oskelly_with_model.xml
      cron: "0 10 21 * * *"
    gmc:
      defaultPathToExportFile: /home/<USER>/export/
      filename: gmc_oskelly.xml
      cron: "0 20 21 * * *"
    facebook:
      defaultPathToExportFile: /home/<USER>/export/facebook/
      filename: fb_oskelly.xml
      config: facebook-feed.conf
      cron: "0 0 22 * * SAT"
    oneass:
      defaultPathToExportFile: /home/<USER>/export/1c/
      customers-filename: Customers.xml
      boutique-sellers-filename: BoutiqueSellers.xml
      bank-accounts-filename: BankAccounts.xml
      products-filename: Products.xml
      boutique-products-filename: BoutiqueProducts.xml
      sales-filename: Sales.xml
      payments-filename: PaymentsToSellers.xml
      twelve-storeez-filename: 12StoreezPayments.xml
      products-part-size: 1000
      # Включает форматирование в XML отчетах (перенос строк и отступы)
      beautify-xml: true
      cron: "0 20 22 * * *"
    catalog-report:
      defaultPathToExportFile: /home/<USER>/export/
      filename: CatalogReport.html
      cron: "0 0 0 * * *"
    cohort:
      defaultPathToExportFile: /home/<USER>/export/cohort/
      cron: "0 0 3 * * *"
    payments:
      defaultPathToExportFile: /home/<USER>/export/payments/
      filename: payments.csv
      cron: "0 10 0 * * *"
    payments_in:
      defaultPathToExportFile: /home/<USER>/export/payments/
      filename: payments_in.csv
      cron: "0 15 0 * * *"
    orders:
      defaultPathToExportFile: /home/<USER>/export/orders/
      filename: orders.csv
      cron: "0 20 0 * * *"
    payedOrders:
      defaultPathToExportFile: /home/<USER>/export/orders/
      filename: payed_orders.csv
      cron: "0 30 0 * * *"
    orderProducts:
      defaultPathToExportFile: /home/<USER>/export/orders/
      filename: order_products.csv
      cron: "0 40 0 * * *"
    publishedProducts:
      defaultPathToExportFile: /home/<USER>/export/products/
      filename: published-products.html
      cron: "0 50 0 * * *"
    products:
      defaultPathToExportFile: /home/<USER>/export/products/
      filename: products.csv
      cron: "0 0 5 * * *"
    moderationProducts:
      defaultPathToExportFile: /home/<USER>/export/products/
      filename: moderation_products.csv
      cron: "0 10 5 * * *"
    users:
      defaultPathToExportFile: /home/<USER>/export/users/
      filename: users.csv
      cron: "0 30 5 * * *"
    notificationDeliveryReport:
      defaultPathToExportFile: /home/<USER>/export/notifications/
      filename: notification_delivery_report.csv
      cron: "0 20 5 * * *"
    social:
      thumbs:
        instagram:
          path: /home/<USER>/export/social/thumbs/instagram
          web-prefix: /social/thumbs/instagram

  publication:
    moderation-timeout-hours: 48
    images-ftp-host: ***************
    images-ftp-login: boutiques
    images-ftp-passw: a!s@d#
    images-ftp-tmp-path: ftptmp
    images-ftp-cleanup-task:
      enabled: false
      cron: "0 */24 * * * *"
    min-price: 5000 #Минимальная цена для публикации
    zero-min-price-category-ids: 292,355,367 #Категории, в которых можно публиковать товары дешевле min-price
    phys-disabled-category-ids: 292,355,415 #Категории, недоступные для публикации физикам

  cart:
    amount-limit-category-ids: 292,355,367 #Категории, для которых устанавливается лимит суммы по корзине (для одного продавца)
    amount-limit: 5000 #Ограничение вводится только для категорий amount-limit-category-ids. По остальным категориям ограничения нет
    forbidden-crossborder-category-ids: # disabled jewelry validation in devalan-531 by Bela & Timur request
    forbidden-crossborder-category-name: "Ювелирные изделия"
    check-promocode-version: 1 # Remove this when PromoCode refactoring will be done

  returnable:
    period-days: 28

  mail:
    enabled: false
    # аккаунт для отправки письма с токеном подтверждения регистрации
    registration:
      host: smtp.yandex.ru
      port: 25
      username: <EMAIL>
      password: example
      personal: Магазин брендовых вещей OSKELLY

    # аккаунт для отправки уведомлений по почте
    notification:
      host: smtp.yandex.ru
      port: 25
      username: <EMAIL>
      password: example
      personal: Магазин брендовых вещей OSKELLY

  integration:

    mindbox:
      domain: _FAKE_oskelly.ru
      web-secret-key: _FAKE_SECRET_KEY_
      web-endpoint-id: _FAKE_oskelly.ru
      ios-secret-key: _FAKE_SECRET_KEY_
      ios-endpoint-id: _FAKE_oskelly-ios
      android-secret-key: _FAKE_SECRET_KEY_
      android-endpoint-id: _FAKE_oskelly-Android
      mobile-push-secret-key: _FAKE_SECRET_KEY_
      mobile-push-endpoint-id: _FAKE_oskelly-Mobile-Push
      service-integration-secret-key: _FAKE_SECRET_KEY_
      service-integration-endpoint-id: _FAKE_oskelly-Mobile-Push
      triggers:
        enabled: false
        user-auth:
          enabled: false
        user-register:
          enabled: false
        user-subscription:
          enabled: false
        user-change:
          enabled: false
        cart-change:
          enabled: false
        order-paid:
          enabled: false
        wishlist-change:
          enabled: false
        view-product:
          enabled: false
        change-environment:
          enabled: false
      sms:
        enabled: false
        passportRequired:
          enabled: false


      twelve-storeez:
        domain: https://api.mindbox.ru/v3/operations/sync
        secretKey: #
        endpointId: Oskelly12Storeez

    major:
      #endpoint: https://ltl-ws.major-express.ru/edclients/edclients.asmx
      endpoint: https://ltl-ws.major-express.ru/test-edclients/edclients2.asmx
      login: oskely18
      password: 10122018
      tracking-url-pattern: https://major-express.ru/trace.aspx?type=1&product=1&wbnumber={wbnumber}
      requests-delay-millis: 200

      available-for-seller: true #доступность для перевозок продавец-оскелли
      available-for-buyer: true #доступность для перевозок оскелли-покупатель
      available-for-auto: true #доступность для автовыбора ТК
    cse:
      endpoint: http://lk-test.cse.ru/1c/ws/Web1C.1cws
      login: test
      password: 2016
      tracking-url-pattern: https://www.cse.ru/per/track/?numbers={wbnumber}
      requests-delay-millis: 200
      client-number-s2o-prefix: -OSKELLY-S2O
      client-number-o2b-prefix: -OSKELLY-O2B
      add-timestamp-to-client-number: false

      trace-check-enabled: false # Если включено, трекинг будет отбрасывать результаты с записью "Трейс" в истории
      trace-delay-in-hours: 4

      available-for-seller: true #доступность для перевозок продавец-оскелли
      available-for-buyer: true #доступность для перевозок оскелли-покупатель
      available-for-auto: true #доступность для автовыбора ТК
    dalli:
      endpoint: https://api.dalli-service.com/v1/
      auth-token: 608333adc72f545078ede3aad71bfe74 # This token is used for delivery from office to buyer
      auth-token-for-s2o: 608333adc72f545078ede3aad71bfe74 # This token is used for delivery from seller to office
      tracking-url-pattern: https://dalli-service.com/?number={barcode}&phone={phone}
      client-number-s2o-postfix: -OSKELLY-S2O
      client-number-o2b-postfix: -OSKELLY-O2B
      requests-delay-millis: 200
      enable-send-to-delivery-from-basket: false
      end-of-the-day-time: "18:30:00"

      available-for-seller: true #доступность для перевозок продавец-оскелли
      available-for-buyer: true #доступность для перевозок оскелли-покупатель
      available-for-auto: false #доступность для автовыбора ТК
    aramex:
      shipping-endpoint: https://ws.aramex.net/shippingapi.v2/shipping/service_1_0.svc
      tracking-endpoint: https://ws.aramex.net/shippingapi.v2/tracking/service_1_0.svc
      location-endpoint: https://ws.aramex.net/shippingapi.v2/location/service_1_0.svc
      username: <EMAIL>
      password: Ar@m3x$h1pp1ng
      account-entity: DXB
      account-number: 45796
      account-pin: 116216
      account-country-code: AE
      version: v1.0
      zone: Asia/Dubai
      #Set it to "true" to use REST API. Set it to "false" to use SOAP API.
      rest-enabled: true
      tracking-url: https://qa.aramex.com/track/results?ShipmentNumber=
    gbs:
      endpoint: https://apitest.spsr.tech/
      username: ${GBS_API_USERNAME:tst}
      password: ${GBS_API_PASSWORD:test}
    appsflyer:
      #Таск отправки ивентов (активностей) в Appsflyer
      outgoing-events:
        enabled: false
        #Количество отправляемых ивентов за 1 раз
        count: 100
        #Расписание отправки ивентов
        cron: "30 */5 * * * *"
      queue-events:
        enabled: false
        add-to-wishlist:
          enabled: false
        remove-from-wishlist:
          enabled: false
        new-bargain:
          enabled: false
        new-following:
          enabled: false
        new-brand-like:
          enabled: false
        new-product-comment:
          enabled: false
        new-concierge-form:
          enabled: false
        new-sale-request:
          enabled: false
        order-delivery-confirmed:
          enabled: false

      endpoint: https://api2.appsflyer.com/inappevent/{app_id}
      apple_id: id1339623403
      android_id: ru.oskelly.app
      apple_bundle_id: ru.oskelly
      android_bundle_id: ru.oskelly.app
      dev_key: _FAKE_key
      pull-api-token: FAKE_pull_token
      pull-api-token-v2: ""
      pull-api-download-path: /home/<USER>/appsflyer_pull_api/
      #Расписание загрузки базы с Appsflyer
      pull-conversions-cron: "-" # Unused: will remove later (DEVALAN-1293)
      conversions-filename: conversions.csv
      conversions-days-count: 60
      currency: RUB
      lib_host: appsflyersdk.com

    onesignal:
      app-id: FAKE_12176786-d1fe-41e8-b739-d2d359bc8ea3
      rest-api-key: FAKE_NTA1ODUzYWUtMTZiZC00YmZhLThkNzgtNmQwNmNjMGVkMGNh
      download-path: /home/<USER>/onesignal/

    opencv:
      baseUrl: ${OPENCV_BASE_URL:https://opencv-host/}
      timeOutInSec: 30
      productsInGroupLimit: 2000
      catalog:
        syncEnabled: ${OPENCV_CATALOG_SYNC:false}
      socketTimeout: 30s
      supportedCategoryIds: 3,4,5,7,8,9,12,13,14,15,17,18,19,20,21,22,23,24,26,27,28,30,31,33,34,37,38,39,41,42,43,44,45,46,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,77,78,79,80,82,83,84,86,87,89,90,91,93,94,96,98,100,101,104,107,108,109,110,111,112,113,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135,136,137,138,139,140,141,142,143,144,145,146,149,150,151,153,154,155,156,157,158,159,160,161,162,163,164,165,166,168,169,170,171,172,174,175,177,178,180,183,190,192,193,194,195,196,197,198,199,200,201,202,203,207,208,209,210,211,212,213,214,215,216,217,218,219,224,225,226,227,228,229,230,231,232,233,234,235,240,241,242,243,244,245,246,247,248,249,250,277,278,279,280,281,282,283,284,285,286,287,288,289,291,428,429,432,433,434,435,437,438,439,440,442,443,444,445,446,447,448,450,451,452,453,454,455,456,460,461,465,466,467,468,469,470,474,475,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,498,499,500,501,502,503,504,505,506,507,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,547,548,549,550,551,553,554,555,556,557,558,559,560,561,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,590,591,592,593,594,595,596,597,598,599,600,601,603,604,605,606,607,2,6,10,11,16,25,29,32,35,36,40,47,75,76,81,88,102,103,105,106,134,147,148,152,173,181,182,189,191,204,206,220,222,223,236,238,239,251,427,430,431,441,464,478,497,508,529,546,552,114

    orderprocessing:
      isEnabled: false
      baseUrl: http://localhost:8085
      connectTimeout: 10000
      readTimeout: 10000

    logistic:
      isEnabled: true
      baseUrl: http://localhost:8090
      connectTimeout: 3000
      readTimeout: 10000

    methodius-docapi:
      baseUrl: https://aquisition-methodius.oskelly.tech

    currencyrate:
      updateCron: "-"
      updateList:
        - currency: USD
          base: RUB
          source: CBR
        - currency: EUR
          base: RUB
          source: CBR
        - currency: KGS
          base: RUB
          source: MOEX
        - currency: CNY
          base: USD
          source: APICHINA
        - currency: CNY
          base: RUB
          source: CBR
      rateSource:
        CBR:
          apiUrl: "https://www.cbr.ru/scripts/XML_daily.asp"
        MOEX:
          apiUrl: "https://iss.moex.com/iss/engines/currency/markets/selt/boards/CETS/securities/"
          ticker:
            USD:
              ticker: USD000UTSTOM
            EUR:
              ticker: EUR_RUB__TOM
            KGS:
              ticker: KGSRUB_TOM
              faceValue: 100
              section: securities # default is "marketdata"
              column: PREVWAPRICE # default is "LAST"
        APICHINA:
          apiHost: "https://apigateway.bochk.com"
          apiPort: 443
          ratesUrl: "/fx/usdrate/v1"
          authUrl: "/auth/oauth/v2/token"
          clientId: ${BANK_OF_CHINA_API_CLIENT_ID:l72218842b80074d14b2812e3b656d7615}
          clientSecret: ${BANK_OF_CHINA_API_CLIENT_SECRET:bca6a54a32794ee7b44f6b85c7451a98}

  push:
    with-testmode: true
    test-mode-emails: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>
    # здесь нужно указать идентификаторы продовых пользователей, которым должны уходить пуши в тестовом режиме
    test-mode-mindbox-user-ids:
    apple:
      test-mode-device-tokens: 0a4d0cba071dd21046259246cc336624116de2b2f65da60769acb0b4ea103151, 8b99aaacc36a1196770605811b729f6673faa44431ffee56b5257b8fad9b01ac, 4f42cd5947888b089919e8ab2534dc2be4961d5bfb03771636b227565f25db01, 9d33ac86d31d0bd7773371cdd6bff17568b0505c8799f256741aeb15a5fe2f9f, d40b55438a1b5a1d4d346dd88f4b3f706737bbb30f5fcaee3fbb11db55acbdde, a7dbb47903c19200a9aa48aa1e97b254b56f06983b31a42b9f1c655f0716c38d, baba12924f81bbf11b058ac510c88cb76d8e310cb5a23e1903e7fcc8ccfbdb0c, 1d89dfa985e8e48c1aa7fc7e6052caebac2cc23236c7020f2036b27e20ca1681, b629e23d5c2b05ba44ac7f1dbfe99abad1e32400b028cc7f87faabfe43b0c1f8, 6f67b0852f7a00e097913dc9c1abebbeda46cc48ac7ee789a7d88e34112ac625, d35f00a1520933e1326d75daee99f6c0a108d4a75f84f99e464ba782d566c6ae
      test-mode-device-mindbox-uuids: 8C1A73A2-554C-445D-B8DB-FAB404D9E917
    android:
      test-mode-device-tokens: fa9T52k-RrKYnubiAd4RkL:APA91bHbTuIysg6LsqeLYLVHPYqohZnnB4fkGuBOunxc1l1W6TV3pivC3JEfQy29Tmobl0G6DiQuDY4W-wx0pJkFZCBGsPzbdS25zjfyjN61kvcyZ9gWoqPTCZ2I3PzDZOD6o9ResUCT, cGoqdLxfXrY:APA91bHgcLvYwVwlHXODDB9i39sJlKiRnf9z9uZP11VMf4uOzOLzUaS5GDq1Zwy61pvCMWldWOWWpRm4-yhAlqYbyhBEHDrCXw5AA6_TNmBJa2lpKxYaOB2b4M60MNx76yISeyRJQ2i-, f4AN8HNdTtOpBqbUsOqBTa:APA91bEfwaPYDEAZO-POPembAsm5ooYVb14Fb38fxvn5Qkhjz74VQS1kpYbM4DDpCiulkooNuTyUFrlF78dM1lB6BtXKV-cQmYqPxcn1SzL6zgvB8xE9JHtYP6rRpZmwT_VXrotxDcTA, fwp7mzsZTyWQRKsEKx6N7F:APA91bEiOR3dH1xyup9cAfjt3tTCMSOdqJVhmfJm1QywrDL0Rjlmbkrs7LvFEGarcAm1vPSVJ1YYBwls2ETtvLKKut5ZVlnWyTQQGB6PlV-6W6C7lbdX3cPza71QvCwSOVV9vCxCuUDa, cjfK8Am3T--rHmkD4ykTvc:APA91bF3j3W4NBo4V0H7iG_fbj04gSt4cZ5ZQjp9CGtKEiNNLK1QTLWN9ox3aD8tUdhpPrkBIPFvHsNHV77lqjFjdptT9C8goZLoE-KO4rinad9DqR5Qnej9YD7HHD9-vxbolIxI-47J, enS_WujgR6q9C60sZGg0b3:APA91bGnMR9T9siGHKcD8HqK3RfMDYBhnYDowif24xonyiL2Ae52TNrF1wJaaQxGPZCVquZ80noN0_Tn_Cx8TgoYWoquZk0cu6Rqpol4VujtUR3m3qt1b4-K4wKnVHvwFGX6DkgNuJqv, dIEjkJVvRq2v2eokOfZk-T:APA91bFl_p6CXYmrY18_cAYkbZ76Fkso8fVxbrKM134x74LIGsyIqTuYifmIbQ0S0a6cg7wB9IrpKauKNu830qpTrkSjBxliD22xEXK_hQ7BLPx6gAjXXTm_b0NfJ6LU5MRr0y3UQzJ3
      test-mode-device-mindbox-uuids:

  notificationDelivery:
    enabled: false #Доставка включена
    threadPool: 14
    maxRetry: 5
    onesignal:
      enabled: false #Доставка включена
      deliveryDelayMillis: 1000 #промежуток между запусками тасков по доставке уведомлений в миллисекундах по каналу onesignal
      limitPerTask: 500 #максимальное количество уведомлений за одно выполнение таска по каналу onesignal
      hours-limit: 25  # Рассылка уведомлений через notificationDelivery или email актуальна в течение N часов. Уведомления старше отправлять не нужно. Если мы за сутки не смогли отправить пуш или емейл, то больше не пытаемся.
    mailganer:
      enabled: false #Доставка включена
      deliveryDelayMillis: 1000 #промежуток между запусками тасков по доставке уведомлений в миллисекундах по каналу mailganer
      limitPerTask: 500 #максимальное количество уведомлений за одно выполнение таска по каналу mailganer
      hours-limit: 25  # Рассылка уведомлений через notificationDelivery или email актуальна в течение N часов. Уведомления старше отправлять не нужно. Если мы за сутки не смогли отправить пуш или емейл, то больше не пытаемся.
    usedesk-whatsapp:
      enabled: false
      deliveryDelayMillis: 1000 #промежуток между запусками тасков по доставке уведомлений в миллисекундах по каналу usedesk-whatsapp
      limitPerTask: 500 #максимальное количество уведомлений за одно выполнение таска по каналу usedesk-whatsapp
      hours-limit: 25  # Рассылка уведомлений через notificationDelivery или email актуальна в течение N часов. Уведомления старше отправлять не нужно. Если мы за сутки не смогли отправить пуш или емейл, то больше не пытаемся.
    mindbox:
      enabled: false #Доставка включена
      deliveryDelayMillis: 1000 #промежуток между запусками тасков по доставке уведомлений в миллисекундах по каналу onesignal
      limitPerTask: 500 #максимальное количество уведомлений за одно выполнение таска по каналу onesignal
      hours-limit: 25  # Рассылка уведомлений через notificationDelivery или email актуальна в течение N часов. Уведомления старше отправлять не нужно. Если мы за сутки не смогли отправить пуш или емейл, то больше не пытаемся.

  product-publication-notification-create:
    enabled: false #Доставка выключена
    create-delay-millis: #промежуток между запусками тасков по созданию нотификаций о публикации товаров в миллисекундах
    interval:
      default: 30 minutes #Интервал отсечения нотификаций (более ранние нотификации игнорируются)
      select: 15 minutes # интервал для группирования нотификаций

  userSync:
    enabled: false #Синхронизация включена
    onesignal:
      enabled: false #Синхронизация включена
      usersSyncDelayMillis: 60000 #промежуток между запусками тасков по синхронизации пользователей с Onesignal в миллисекундах
      limitPerTask: 500 #максимальное количество синхронизируемых пользователей за раз

  deeplink:
    brand:
      hermes: 2037 #ID бренда HERMES
    category:
      female:
        bags: 3 #ID категории женских сумок
    streetwear: #настройки для streetwear (id категорий и брендов, входящих в подборку streetwear)
      female-category-ids: 44,39,41,42,63,62,64,283,58,57,59,55,60,81,70,65,35,15,33,16,34,19,7,5,4,6,90,89,91,103,87,285,86,93,98,94,287,104
      male-category-ids: 143,109,110,111,112,113,288,123,125,126,124,121,119,120,116,128,129,132,147,139,149,152,156,164,161,160,163,166,169,170,174,175,191,172,168,177,178,183
      kids-category-ids: 199,192,198,193,202,196,204,215,209,214,210,218,216,213,211,212,220,221,224,225,226,227,228,229,230,231,232,234,236,237,240,241,242,243,244,245,246,247,248,251,252
      brand-ids: 39,262,2524,2531,2510,54,100,2614,236,2516,430,436,2613,518,580,602,2430,599,675,2207,2475,2375,1951,2617,837,2481,882,1210,1164,1157,1023,1025,2508,2325,1254,2973,1287,1334,1422,1447,1454,2536,1505,1554,1722,2646,2424,2539,1856,1853,2562,1886,1944,1957,1947

  user-service:
    update-last-access:
      enabled: false

  bargain:
    enabled: true
    #Количество секунд до открытия возможности создавать торг после публикации товара
    product-published-delay-seconds: 604800
    #Добавлять информацию о торге в карточках товара
    fill-product-card: true
    min-price-koef: 0.7
    max-attempts-count: 3
    seller-max-decision-time-hours: 24
    buyer-max-decision-time-hours: 24
    min-bargain-price: 5000
    default-page-size: 20
    #Таск закрытия торгов (товар продан/скрыт/на модерации, торг просрочен, цена изенилась и т.п.)
    close-task:
      enabled: false
      #Кол-во миллисекунд между выполнениями таска
      delay-millis: 60000
      #Кол-во обрабатываемых торгов за один запуск
      count: 100
    #Таск повторного открытия торгов (товар вновь доступен для продажи)
    open-task:
      enabled: false
      #Кол-во миллисекунд между выполнениями таска
      delay-millis: 600000
      #Кол-во обрабатываемых торгов за один запуск
      count: 100
    #Таск рассылки 12 часовыз уведомлений (осталось 12 часов для принятия решения по торгу)
    12h-notifications-task:
      enabled: false
      count: 100
      delay-millis: 60000
    24h-seller-notifications-task:
      enabled: false
      count: 100
      delay-millis: 60000
    24h-buyer-notifications-task:
      enabled: false
      count: 100
      delay-millis: 60000
    1h-notifications-task:
      enabled: false
      count: 100
      delay-millis: 60000

  attribute:
    color-id: 10

  security:
    enableCredsEndpoint: ${ENABLE_CREDS_ENDPOINT:false}

  brands:
    categoriesWithSpecificDescriptionIds:
      - 2     #женское
      - 105   #мужское
      - 188   #детское

  new-home:
    enableInstagramBlock: false

  catalog:
    menu:
      defaultVersion: 1
    filters:
      timeout: 100s

  datamatrix:
    markingCodeMask: ".{135}|.{91}" # Regex для проверки маркировки товара перед отправкой покупателю
    # mark-example-1: "0104660153091674219x.ORdd<2;In)<gs>910098<gs>922xrRmvGUbSZgTeHI9DZYZY7wC2IN0L3DTfPQ8ttIWUZNwvZ81fpzqVdL+P1qiOVoiTU+fckY7qA+mekmOKhWXA=="
    # mark-example-2: "010481260337237021266Ph7eZkFzXa<gs>910005<gs>92ytC8FoTLEohHZazYqKsMbEZc93DzpC87Nd2HZwnVHwhyjMB4vxWbPncigFzDEtSj78aCG2eixK5MJFirQWS88bAV"
#    markingCodeMask: "^[0-9]{6,12}$" #Use for HS-code in the international stand. Only digits, from 6 to 12 digits

  recommendation-product:
    host: oskelly-recommendations
    httpclient:
      connect-timeout: 10000
      read-timeout: 10000

  top-n-product:
    enabled: false
    host: oskelly-topn
    connect-timeout: 1000
    read-timeout: 1000

  personalized-service:
    enabled: false
    host: oskelly-personalized
    limit: 100
    score-boost: 100
    time-decay: 0.01
    httpclient:
      connect-timeout: 1000
      read-timeout: 1000

  paymentOptions:
    card:
      enabled: true
      ignoreExp: true
    yandexPay:
      enabled: false
      ignoreExp: true
      points:
        enabled: false
    yandexSplit:
      enabled: false
      ignoreExp: true
    sbp:
      enabled: false
      ignoreExp: true
    noonApplePay:
      enabled: false
      ignoreExp: true
    noonCard:
      enabled: false
      ignoreExp: true
    tabbySplit:
      enabled: false
      ignoreExp: true
      order: 40
      splitCount: 4
      delay: 1
      delayChronoUnit: MONTHS
      supportedCurrencies: AED,SAR,QAR,KWD,BHD

  custom-user-segment:
    uploading-csv:
      user-ids-batch-size: 100

  activity-table-cleanup:
    view-product-activity:
      limit-per-user: 30
      task:
        clean-by-guest-token:
          cron: "-"
          guest-token-page-size: 100
        clean-by-user-id:
          cron: "-"
          user-id-page-size: 100

  tokenForResetPasswordTTLHours: 2
  tokenForSetPasswordTTLHours: 6

  userTradeStat:
    scheduleCron: "-" #"0 0 2 * * ?"

  userCounters:
    enabled: ${USER_COUNTERS_ENABLED:false}

  commissionGridChange:
    history:
      productsBatchSize: 100
      enabled: false
      schedule: "-" #"0 0 2 * * ?"
    mainGridsEditEnabled: false

  products-to-external-catalog-sender:
    batch-size: 1000
    cancel-deletion-errors-count-threshold: 0
    abort-sending-errors-count-threshold: 10
    cron: "-"

  productPriceDiscount:
    schedule: "-" #"0 0 3 * * ?"
    period: 30
    periodChronoUnit: DAYS #MINUTES, HOURS, DAYS, WEEKS, MONTHS - для тестирования
    maxCount: 3
    percent: 10

  auth-jwt:
    phone-number:
      secret-key: EhjSc27MTe2qLBw3fYeGgWezyfh9TnJ3
      expiration-seconds: -1 #Если -1, то токен не будет просачиваться. Если 0, то сразу будет просрочен
    social:
      secret-key: EhjSc27MTe2qLBw3fYeGgWezyfSOCIAL
      expiration-seconds: -1 #Если -1, то токен не будет просачиваться. Если 0, то сразу будет просрочен
  deeplink-jwt:
    add-to-cart:
      secret-key: CfDCgxoKkrEFSWLXPuBaVCdB2aMqDBL9
      expiration-seconds: -1 #Если -1, то токен не будет просачиваться. Если 0, то сразу будет просрочен
  instagram-feed:
    enabled: false
    instagram:
      user:
        id:
      page:
        url: https://www.instagram.com/oskelly.co
    facebook:
      client-id:
      client-secret:
      redirect-uri: http://localhost:8080/api/v2/admin/instagram-feed/facebook-access-token
      graph-api:
        base-url: https://graph.facebook.com/v19.0
        user-media:
          url-part: /{mediaId}?access_token={accessToken}&fields={fields}
        user-media-list:
          url-part: /{userId}/media?access_token={accessToken}&fields={fields}&limit={limit}&after={after}
          page-size: 20
        access-token:
          url-part: /oauth/access_token?client_id={clientId}&client_secret={clientSecret}&redirect_uri={redirectUri}&code={code}
        exchange-token:
          url-part: /oauth/access_token?client_id={clientId}&client_secret={clientSecret}&fb_exchange_token={accessToken}&grant_type=fb_exchange_token
      proxy:
        host:
        port:
    path: /api/v2/home/<USER>
    media:
      proxying:
        enabled: false
        getting-file-meta-info:
          for-list:
            enabled: true
        url-part: /api/v2/home/<USER>/item/content?mediaId={mediaId}&variantType={variantType}

  text-slides:
    icon:
      max-width: 100
      max-height: 100
    default-slide-interval: 5000

  osocial:
    media:
      uploading:
        file-path-prefix: osocial/media/
        temp-file-path-prefix: osocial/media/temp/
        url:
          ttl: 1h
          content-length-enabled: false
        file:
          max-size: 100MB
        # Пример расчета таймаута:
        # средняя пропускная способность канала на отдачу - 10 Мбит/с -> 1,25 Мбайт/с -> расчетное время загрузки 1 Мб - 0.8 с -> округляем, используем коэффициент 10, выставляем коэффициент = 10 с
        # канал 100 Мбит/с -> 12,5 Мбайт/с -> 1 Мб за 0.08 с -> таймаут 1 с
        # канал 1 Гбит/с -> 125 Мбайт/с -> 1 Мб за 0.008 с -> таймаут 100 мс
        # Коэффициент можно настраивать
        timeout-per-mb: 1s
    users:
      syncing:
        batch-size: 1000
        cron: "-"
        timeout: 60s
        errors:
          max-count: 50
        following-users:
          too-many-threshold: 2000

    posts:
      text:
        checking:
          maxLength: 2200
          maxLineBreaks: 10
          performPhoneNumberCheck: true
          performEmailCheck: true
          performSocialNetworkCheck: true
          performSwearsCheck: true
          performLinkCheck: true
          performConfigurableStopListCheck: true
      product-brands:
        syncing:
          batch-size: 100
    comments:
      text:
        checking:
          maxLength: 2200
          maxLineBreaks: 10
          performPhoneNumberCheck: true
          performEmailCheck: true
          performSocialNetworkCheck: true
          performSwearsCheck: true
          performLinkCheck: true
          performConfigurableStopListCheck: true
  turnstile:
    enabled: false
    url: https://challenges.cloudflare.com/turnstile/v0/siteverify
    connect-timeout: 1s
    read-timeout: 1s
    secret-key: ${TURNSTILE_SECRET_KEY:}
    send-ip-address: true
  recaptcha:
    enabled: false
    project-id: empty
    site-key: empty
  rate-limiter:
    enabled: false

boutique:
  boutique-buyers-ids: 231441 # <EMAIL>, overridden in tests
  boutique-buyers-ids-for-online-order-notification: 231441,652049
  boutique-buyer-id-for-automation: 231441 # Used as a buyer in automated Boutique order creation
  office-address-endpoint-id: 59987 # address_endpoint.id used as seller address in Boutique orders
  split:
    cron: "-"

logging:
  level:
    root: INFO
    org.apache.http: ERROR
    su.reddot.domain.interceptor: DEBUG
    su.reddot.infrastructure.cashregister: DEBUG
    su.reddot.infrastructure.bank: DEBUG

resources:
  images:
    storageType: LOCAL # LOCAL или S3
    prefix: # url-prefix для доступа к фото. Ex. http://static.oskelly.ru/
    mainpageprefix:
    urlPath: /img/ # аналог urlPrefix, но только для фото товаров, дефектов и комментов
    urlPrefix: /img/
    pathToDir: /home/<USER>/images/
    pathToTempDir: /Users/<USER>/oskelly/temp
    productMaxImagesCount: 9
    productMaxImageFileSizeMb: 8
    productMaxDefectImagesCount: 10
    cardBrands:
      MIR: "-"
      12STOREEZ: "-"
      MASTER: "-"
      VISA: "-"
      MASTER_3X: "/images/payments/mc_3x.jpeg"
      VISA_3X: "/images/payments/visa_3x.jpeg"
      MIR_3X: "/images/payments/mir_3x.jpeg"

  userfiles:
    urlPrefix: /userfiles/
    pathToDir: /home/<USER>/userfiles/
    maxFileSizeMb: 8

  user-contracts:
    storageType: LOCAL # LOCAL или S3
    pathToDir: /home/<USER>/user-contracts/
    maxFileSizeMb: 50

  order-files:
    storageType: S3 # LOCAL или S3
    pathToDir: /tmp
    maxFileSizeMb: 50

payments:
  cb-fallback-mode: "tcb-pay/card"
  payments-service:
    api-url: https://aquisition-payments.oskelly.tech
    ret-url: http://localhost:8080 # https://oskelly.ru
    jobcron: "-"
    useAmqp: false
    apiAuth:
      username: ${PAYMENTS_SERVICE_API_USERNAME:payments}
      password: ${PAYMENTS_SERVICE_API_PASSWORD:pay-pass}
  checkout-com:
    enabled: false
    return-url: http://localhost:8080 # https://oskelly.co
    currencies:
      - "AED"
    public-key: "pk_test_a06301c2-c2a5-4b10-9055-222edcb21b10" # From settings panel in Checkout.com
    secret-key: "sk_test_95286550-15c1-49b3-bd63-eb7fef790962" # From settings panel in Checkout.com
    environment: sandbox # sandbox / production
  noon-payments:
    enabled: false
    return-url: http://localhost:8080 # https://oskelly.co
    payments-ttl: P00dT00h05m
    currencies:
      - "AED"
    api-url: https://api-test.noonpayments.com/payment/v1
    environment: Test # Test / Live
    business-id: oskelly # Getting from Noon account
    application-id: OskellyCoTest  # Getting from Noon account, application settings
    application-key: 84649f672dca4f26be5de1818014c1cc  # Getting from Noon account, application settings
  boutique:
    enabled: true
    return-url: http://localhost:8080 # https://oskelly.ru

kratos:
  kratos-service:
    enabled: false
    api-url: http://localhost:8089
    apiAuth:
      username: ${KRATOS_API_USER:oskelly}
      password: ${KRATOS_API_PASS:qwerty}

seo:
  service:
    enabled: false
    url: ${SEO_SERVICE_URL:http://localhost:9092}
    connectTimeoutInMillis: 50
    readTimeoutInMillis: 200
    debug: false

bank-account:
  payment-version: b2p+tkb
  enable-agent-sales: true
  enable-advance-receipts: true
  enable-oskelly-payout: false
  hold-expires-in-seconds: 432000

  jobs:
    enabled: false
    create-seller-payouts:
      cron: "45 * * * * ?"
    prepare-seller-payouts:
      cron: "0 0/5 * * * ?"
    check-b2p-acquirer-transfers:
      cron: "0 0 0/1 * * ?"
    check-seller-payouts:
      cron: "0 0/30 * * * ?"
    check-bank-operations:
      cron: "0 0/2 * * * ?"
    refund-expiring-orders:
      cron: "-"
    auto-capture-authorizations-expire:
      cron: "-"
    auto-sent-concierge-agent-reports:
      cron: "-"
    auto-confirm-concierge-agent-reports:
      cron: "-"
    create-payout-request:
      cron: "-"
    refuse-payout-request:
      cron: "-"

  best2pay:
    signing-key: test
    mobile-signing-key: test
    merchant-name: OSKELLY.RU
    return-url: http://localhost:8080
    api-endpoint: https://test.best2pay.net/webapi
    gateweb-endpoint: https://test.best2pay.net/gateweb
    sector-id: 1304
    mobile-sector-id: 1635
  tcb:
    api-endpoint: https://paytest.online.tkbbank.ru/api/v1
    api-business-endpoint: https://paytest.online.tkbbank.ru/api/interfaces/business
    login: T2721101360ID
    signing-key: V0fmkT6zjLzsgR2aaHMB3iZWxLC0EExvGV3q41ozjO52bHuFcXJY7uLMasYKqg3OoeVvXNeitnspm6UYq5XQ5HxmjrpMcNicUsP6W8SzTzEUbvzdGattsnSPRJkFxBgQdpE3sXcqoy6FdMkX0mfbtYUGJ8qnYmbrtYRW0EUT7df2pmMeywOkwewoE6vsGYBbxQmo3x6wAvdqT0rwxkXBGZ3oCCs2jCmiLdFJ8xkbysUbbfENP74wb8DGV7VBfem7
    account-number: PAT100000763ID
    return-url: http://localhost:8080
  tcb2:
    api-endpoint: https://paytest.online.tkbbank.ru/api/v1
    api-business-endpoint: https://paytest.online.tkbbank.ru/api/interfaces/business
    login: T2873101412ID
    signing-key: zBeOj2mGi0PEMS0uDU68pAbwXt17UqCJ0ESM6vgHBaQKhmNMPj5263K7zS0ZanV2ygOgaowhOyGpUBoMQqtLuJL53TmpErHopWkaHFMAWXpHwvn7DoiU3kHZERmqbhNw0ta6qoekO1XGtdJJFoUrhXueovEZtvyOj1QdvqSpi7gbwbFBhNTXwwpd84T4So1sCiwAwFnqFFhjRsRmYdSPLLrXNNezGXF3fXHnPfeMzDs0oevQoGfPFmQ11HWSAWTR
    account-number: PAT100000869ID # login at https://paytest.online.tkbbank.ru/ with password 'w1kuEDpQ'
    return-url: http://localhost:8080
    hold-timeout-in-seconds: 300
    oskelly-current-account: "40817810238100000000"
    oskelly-legal-name: ООО "ОСКЕЛИ ГРУПП"
    oskelly-account-BIK: "*********"
    oskelly-INN: "**********"
    hold-expires-in: P6dT23h30m # java.time.Duration.parse format
    allow-payout-to-bound-card: true
    allow-payment-from-bound-card: true
  swiftpeople:
    api-endpoint: http://************
    token: ${SWIFT_PEOPLE_TOKEN:eshwyPifUAqk5guhuksyV3yLKklBFTVX8cipUgzDf53730fc}
    organisation-id: 2
    baseCurrency: RUB
    invoice:
      billTo: TRIXTY GLOBAL LIMITED
      billToAddress: 22/F 3 LOCKHART ROAD, WAN CHAI, HONG KONG
      shipToAddress: HONG KONG
    proxy:
      host:
      port:

server:
  port: ${APP_PORT:8080}
  session:
    persistent: true
    store-dir: /tmp/
    cookie:
      max-age: 172800
    timeout: 172800 # 2 дня
  servlet:
    session:
      timeout: 172800 # 2 дня

  error:
    include-stacktrace: always

logistic:
  storageType: LOCAL # LOCAL or S3
  defaultCompany: "CSE"
  baseDir: /home/<USER>
  endpoint: http://lk-test.cse.ru/1c/ws/web1c.1cws
  login: test
  password: 2016
  min-declared-seller-profit: 40000 # Минимальная сумма продавцу, для которой отправляем в КС оценочную стоимость
  max-declared-value: 500000 #максимальная оценочная стоимость товара для ТК. Если товар выше этой стоимости, то назначаем значение из этого поля
  with-cse: true
  with-major: true
  with-dalli: true
  with-aramex: false
  with-oskelly: true
  with-gbs: true
  with-cargo-express: true

cash-register:
  starrys:
    endpoint: ${app.host}/api/v2/receiptreceive/fail500http
    # тестовый клиентский сертификат
    client-certificate-path: classpath:starrys-client.p12
    client-certificate-password: oskelly
    allow-non-localhost-endpoint: false

    clientId: noclientid
    password: 1080880801
    payAttribute: 4
    taxId: 4
    place: NO-PLACE
    fullResponse: true
    automationBuyerCheckFromDate: 2020-01-01T00:00:00-00:00
    allowCorrectionAutomation: true
    correctionFromDate: 2019-01-01T00:00:00-00:00
    correctionDocumentName: Неприменение ККТ
    correctionDocumentNumber: 1-05-12
  receipts-sender:
    cron: "-" # execute never, enable on tasks with "0 0/1 * * * ?" (one minute) for example
    sender-email: <EMAIL>
    requestid-prefix: "" # empty for production, required on test environment to distinguish receipts with same order ID`s and avoid caching
    enable-marking-info: true # pass orderPosition marking info in fiscal receipts?

facebook:
  client:
    clientId: _FAKE_
    clientSecret: _FAKE_
    accessTokenUri: https://graph.facebook.com/oauth/access_token
    userAuthorizationUri: https://www.facebook.com/dialog/oauth
    tokenName: oauth_token
    authenticationScheme: query
    clientAuthenticationScheme: form
    pre-established-redirect-uri: http://localhost:8080/login/facebook
    use-current-uri: false
  resource:
    userInfoUri: https://graph.facebook.com/me

apple:
  appleClientId: ru.oskelly
  appleTeamId: _FAKE_
  appleKeyId: _FAKE_
  certificate-path: /home/<USER>/app/_FAKE_.p8

esputnik:
  url: https://esputnik.com/api/v1
  subscribeMethod: /contact/subscribe
  subscribeGroup: Подписчики (не подтвердили)
  login: <EMAIL>
  password: OsPutn1k

mailganer:
  subscribe_url: https://mailganer.com/api/v2/emails/
  #Выключатель механизма подписки новых email'ов
  subscription-enabled: false
  api_version: 1
  trigger_url: https://mailganer.com/api/trigger/send/
  trigger_url_v2: https://mailganer.com/api/v2/triggers/{trigger_id}/send/
  site_url: https://oskelly.ru
  api_key: _FAKE_b7c6af065a4427ebe25b9c905f39a575
  # На всех профилях, кроме prod в качестве получателя будет подставляться это значение
  # Ни в коем случае не запускайте локально приложение с use_real_emails: true, т.к. вы будете спамить по реальным адресам
  use_real_emails: false
  test_email: <EMAIL>
  source_id: 48321
  user_registration_trigger_id: 2321
  seller_concierge_payout_trigger_id: 13200
  crossborder_agent_report_trigger_id: 16235
  problems_sorry_promocode: SORRY1000
  synchronizer:
    enabled: false
    #Делей между запусками таска в миллисекундах
    deliveryDelayMillis: 60000
    #Кол-во обрабатываемых контактов за один запуск таска
    contactsCount: 50
    #Максимальный номер страницы при загрузке контактов с сервера
    maxPageNumber: 5
    #Делей между запросами к серверу в миллисекундах
    requestsDelayMillis: 100
    #Расписание загрузки базы с Mailganer
    uploadBaseCron: "0 48 0 * * SUN"
    #Начальный ID обхода по пользователям. Если N, то выбираются пользователи с id > N
    userIdOffset: 0
    #Начальный ID обхода по подписчикам. Если N, то выбираются подписчики с id > N
    subscriptionContactIdOffset: 0

remind:
    # Периоды уведомлений о брошенной корзине в минутах. Перечисляются в минутах через запятую в порядке уменьшения периода
    # (сначала максимальный период, далее более короткий). 1440 - сутки, 60 - 1 час
    #order_lost_minutes: 1440,180
    order_lost_minutes: 60

#Выключаем старые офферы
#offers:
  #Таск миграции в контрторги
  #migration-task:
    #enabled: false
    #Кол-во миллисекунд между выполнениями таска
    #delay-millis: 60000
    #Кол-во обрабатываемых торгов за один запуск
    #count: 100

  #minPricePercent: 0.7
  #alertsCreateDelayMinutes: 15 # создавать алерты по оферам не чаще чем раз в x минуту
  #alertsStartPeriodHours: 24 # начинать создавать алерты по оферам через x часов
  #offerValidPeriodHours: 72 # закрывать висящие оферы через x часов
  #checkExpiredOrdersRateMillis: 1800000 # проверять валидность оферов каждые x миллисекунд
  #maxAttemptsPerUser: 3

exclusive-selection:
  durationHours: 24
  #Это верхняя граница низших статусов O!Community для эксклюзивной селекции.
  #Например, если эксклюзивная селекция доступна со статуса BESTIE, то в этом поле
  #будет содержаться ранк статуса BUDDY.
  communityLowStatusRank: 1
  ignoreExperiment: true

order:
  days_to_refund_not_confirmed_order: 5
  alertsCreateDelayMinutes: 15
  hours_when_not_full_confirmed: 24
  hours_when_not_shipped: 24
  hours_when_not_full_expertise: 24
  request_delivery_confirmation_from_buyer: true #Запрашивать подтверждение доставки заказа от покупателя
  scheduler:
    check-not-completed-order:
      days-to-delete-not-completed-order: 30
      cron: "-"

comments:
  performPhoneNumberCheck: true
  performEmailCheck: true
  performSocialNetworkCheck: true
  performSwearsCheck: true
  performLinkCheck: true
  performConfigurableStopListCheck: true
  maxImagesCount: 3
  imageMaxWidth: 1024
  imageMaxHeight: 1024
  imageQuality: 80
  publishing:
    # ограничение количества комментов работает при активном rate-limiter-е: app.rate-limiter.enabled: true
    # и при доступном redis
    limit:
      total:
        enabled: false
        value: 10
        window: 5m
        key-prefix: ""
      similar:
        enabled: false
        value: 5
        window: 5m
        key-prefix: ""

dadata:
  token: abb1e065856a2c6747958a4a21c2ce8fd27ceacc
  onlineToken: e11822b8a09ff236dfc56237e257e1a2df033f31
  addressSuggestionUrl: https://suggestions.dadata.ru/suggestions/api/4_1/rs/suggest/address

following:
  defaultFollowerIdsOnRegister: 21,10 #Пользователи с id 21 и 10 автоматически подписываются на вновь зарегистрированного пользователя
  proposals:
    user-status-rate: # рейтинг титула рекомендуемого пользователя
      weight: 6
    user-followers-count: # количество подписчиков рекомендуемого пользователя
      weight: 0.002
    user-brands-crossing: # количество пересечений по брендам между текущим и рекомендуемым пользователями
      weight: 0.1
    limit: 2000
  scheduler:
    cron: "-"

helpcrunch:
  apiUrl: https://api.helpcrunch.com/v1
  apiToken: ZjE2MDI5YTNjNDVjMGE5ZDk0YWUzZjQyrnF4Dw05POaZ4wdF8T9h9BF1DpSvYF9U5tX4wzLRM3oUsniImkG5UG5zXgQymevW2+pOnWr9/kzvjiTPOCbGHM0g8gzXM1TDN/ynIA1lTI5lHwTV82eYUzidG2ylJubT955dQnCf7Lqypdze40Fy9BK0DQY1oyt41Pl1SujdFvRkSLphQoMsmnS2STaDViITPIlEEZokFB5VoJ/Csm8L6ByJGOdLgbzfHyBSlN4eIcy5rRrF4lcqF3pAnWDdsQ1AoegX8roO0OatXvRHZFnDIO4aaBCrKxyjlA==
  widgetDomain: testoskelly
  widgetSecret: VpN/Bw6IdcucN3pKiJBJBVguwzmWl/kNmQdE5J+1Qo63Ok1Vrdpmd2INmoBQ4P0i+TwS/HwLSVt+BAhkINXiFw==
  android:
    appId: 2
    secret: IPzEo01miEtXa3JI2HPgnhJQmX/c9N9RnSZ+q8wKy0LF56w1Y325mXXaUfDz81l2mQSGS1dK670PBrM5AzQqEQ==
  ios:
    appId: 3
    secret: L4Na1IukW5tv/jyBsrIGU7g1237fASjtC7jkzPyenRS3ASOW9OpE4Hcc+/s7Z2jWj8EAJyZGVbAOd8hlipnbpw==

usedesk:
  apiUrl: https://api.usedesk.ru
  apiToken: NoValidTokenInBaseProfiles
  companyId: 161911
  mailMask: '.*@oskelly.ru' # only mails with this mask (regex) will be processed
  syncCron: "-" # Disable everywhere, enable on task, example "0 0/5 * * * ?" # Every 5 minutes
  statCron: "-" # Disable everywhere, enable on master, example "0 0 * * * ?" # Every hour
  syncSize: 0
  channelIds:
    whatsapp: 40828
  exportConfig:
    exportAepUserFullNames: true # Export fullname from user AddressEndpoints to messengers [] in Usedesk
    exportAepPhoneNAddress: true # Export phone numbers & addresses from user AddressEndpoints to addresses in Usedesk
    exportUserLinksToSites: false # Export links to user page in adminpanel to sites propertin UseDesk
  mobileConfig:
    organizationId: 0
    channelId: 0
    chatApiUrl: pubsubsec.usedesk.ru
  webConfig:
    widgetpath: https://lib.usedesk.ru/secure.usedesk.ru/widget_161911_33325.js
    profileUrl: https://secure.usedesk.ru/clients/details/{client_id}
  notificationsConfig:
    apiToken: 294d96fe873bc7ce3048915f3ac5ac0196b4b945
    userId: 201330 #userId бота в UseDesk
    dtypes:
      BargainSellerNewOfferNotification:
        triggerSubject: Уведомление продавца о необходимости ответить на торг
        triggerTag: пушпродавецотвтторг
        remindAfter: PT4H
      NewCommentNotification:
        triggerSubject: Уведомление продавца о необходимости ответить на комментарий
        triggerTag: пушпродавецотвткоммент
        remindAfter: PT4H

adminpanel:
  #lookupv2: true
  baseUrl: https://admin.oskelly.ru
  orders:
    path: /orders
  user:
    socialAccount:
      # доступные сейчас INSTAGRAM/VK/FACEBOOK
      displaySocialNetworks: INSTAGRAM
  offers:
    #Ссылка на иконке торгов в админке (старая кнопка ведет на новую админку)
    icon-link: https://admin.oskelly.ru/bargains/list/inProgress
  images:
    maxWidth: 1200
    maxHeight: 1200
  pickupDatesNumber: 10


management:
  endpoints:
    web:
      exposure:
        include: "*"
        exclude: ""
  metrics:
    distribution:
      percentiles-histogram:
        reactor:
          flow:
            duration: true

internationalVersion: false

#Переключатель промокодов
promocode:
  chance5:
    enabled: true
  oskelly5:
    enabled: true
  sorry1000:
    enabled: true
  #На данный момент на промокоде Mailganer отсылается промокод sorry1000
  order-pickup-declined-mailganer:
    enabled: true
  promocode-generator:
    attempts-to-generate-limit: 20
    attempts-to-generate-warning: 10


sitemap-generation:
  enabled: false
  cron: "-"
  s3Path: #path to sitemap on s3
  productPageSize: 10000
  host: 'https://oskelly.ru'
  blogMapUrl: 'https://oskelly.ru/blog/sitemap_index.xml'
#Уведомление продавцов о подтверждении заказа
order-confirm-notification:
  host: https://oskelly.ru
  enabled: false
  cron: 0 0/15 * * * *
  noticeAfter: PT4H
  tag: пушпродавецподтвпродажи
  adminNoticeAfter: PT12H

s3:
  serviceEndpoint: storage.yandexcloud.net
  signingRegion: ru-central1
  accessKey: #
  secretKey: #
  bucket:
    static: #
    feed: #
    images: #
    logistics: #
    userContracts: #
    saleinfo: oskelly-saleinfo-dev
    orderFiles: #

concierge:
  mail:
    smtp:
      host: smtp.gmail.com
      port: 465
      ssl-enable: true
      auth: true
    sender:
      address: <EMAIL>
      password: test_password
    receiver:
      address: <EMAIL>
    seller-application-receiver:
      address: <EMAIL>
  seller-ids:
  form-processing:
    enable: false
    bitrix:
      cron: "-"
      max-retry-count: 3
      form-butch-size:  10
      assign-by-setting:
        enable: false
  order-processing:
    bitrix:
      enable: false
      cron: "-"
      batch-size: 20
      max-retry-count: 10
      clean-product-bitrix-cron: "-"
      product-bitrix-expired-time-hours: 72
      clean-product-batch-size: 20
  order-position-processing:
    bitrix:
      enable: false
      cron: "-"
      batch-size: 20
      max-retry-count: 10

bitrix:
  webhook-url: #https://bitrix.oskelly.ru/rest/15/dfdwsaE213SasaXC/
  fields:
    lead:
      product-reference-link: "UF_CRM_1700740415579"
      image-links: "UF_CRM_1700740513840"
      image-files: "UF_CRM_1702968774"
      customer-order-link: "UF_CRM_1716559932"
#      ID Воронки
      funnel-id: "UF_CRM_1712572541"
    deal:
      similar-product-links: "UF_CRM_1700544596355"
      sale-request-id: "UF_CRM_1702471907"
      order-id: "UF_CRM_1702472068"
      customer-order-link: "UF_CRM_1706178541479"
      image-urls: "UF_CRM_1703068935"
      image-files: "UF_CRM_1699879543589"
      image-files-lower: "ufCrm_1699879543589"
      add-cart-error-message: "UF_CRM_1721302574298"
    smartProcess:
      order:
        orderId: "ufCrmCrossborderOskellyOrderId"
        orderHoldAt: "ufCrmCrossborderOskellyHoldDate"
        idFullName: "ufCrmCrossborderIdFullname"
        idDocType: "ufCrmCrossborderIdDocType"
        idNumber: "ufCrmCrossborderIdNumber"
        idIssueDate: "ufCrmCrossborderIdIssueDate"
        identityCardGiven: "ufCrmCrossborderIdentityCardGiven"
        taxNumber: "ufCrmCrossborderTaxNumber"
        phone: "ufCrmCrossborderReceiverPhone"
        email: "ufCrmCrossborderReceiverEmail"
      product:
        orderLink: "parentId1034"
        productId: "ufCrmCrossborderProductId"
        orderPositionId: "ufCrmCrossborderOrderPositionId"
        unitWeight: "ufCrmCrossborderUinitWeight"
        unitVolume: "ufCrmCrossborderUnitVolume"
        quantity: "ufCrmCrossborderQuantity"
        name: "ufCrmCrossborderProductName"
        description: "ufCrmCrossborderProductDescription"
        image: "ufCrmCrossborderProductImage"
        productUrl: "ufCrmCrossborderProductUrl"
        countryOfOrigin: "ufCrmCrossborderProductCountryOfOrigin"
        productPrice: "ufCrmCrossborderProductPrice"
        productCurrencyCode: "ufCrmCrossborderProductPriceCurrency"
        sellerNickname: "ufCrmCrossborderSellerNickname"
        senderFullName: "ufCrmCrossborderSenderFullname"
        senderCountry: "ufCrmCrossborderSenderCountry"
        senderCity: "ufCrmCrossborderSenderCity"
        senderDistrict: "ufCrmCrossborderSenderDistrict"
        senderStreet: "ufCrmCrossborderSenderStreet"
        senderBuilding: "ufCrmCrossborderSenderBuilding"
        receiverFullName: "ufCrmCrossborderReceiverFullname"
        receiverCountry: "ufCrmCrossborderReceiverCountry"
        receiverCity: "ufCrmCrossborderReceiverCity"
        receiverStreet: "ufCrmCrossborderReceiverStreet"
        receiverBuilding: "ufCrmCrossborderReceiverBuilding"
        contractNumberGBS: "ufCrmCrossborderGbsContractNumber"
        serviceCode: "ufCrmCrossborderServiceCode"
        refBarCode: "ufCrmCrossborderReceiverRefBarcode"
        trackingNumber: "ufCrmCrossborderTrackingNumber"
        boutiqueTrackingNumber: "ufCrmCrossborderBoutiqueTrackingNumber"
        confirmationStatus: "ufCrmCrossborderBitrixConfirmationStatus"
        deliveryStatus: "ufCrmCrossborderBitrixDeliveryStatus"
        sellerPayoutStatus: "ufCrmCrossborderBitrixPayoutStatus"
        cancelStatus: "ufCrmCrossborderBitrixCancelStatus"
        editStatus: "ufCrmCrossborderBitrixEditStatus"
        status:
          productConfirmed: 620
          productRejected: 621
          shipment: 622
          transit: 623
          delivered: 624
          sellerPayoutCompleted: 626
          sellerPayoutNotCompleted: 625
          canceled: 627
          editEnabled: 628
          editDisabled: 629
        stage:
          sentToOffice: 14
  smartProcesses:
    orderTypeId: 1034
    productTypeId: 1038
  stages:
    default-category-id: 0
#    Тут лежат id статусов для каждой подерживаемой воронки в Bitrix
    category-id-to-stage:
#      Oskelly-консьерж
      0:
    #   Ошибка с корзиной
        add-to-cart-fail: "UC_BZBD1X"
    #   Товар в корзине
        add-to-cart-success: "UC_TBWS0Z"
    #   Оплаченные заказы
        deal-paid: "UC_9UNSN7"
    #    Байер принял заказ
        bayer-accepted-deal: "UC_323KYA"
    #    Ожидаем доставки
        awaiting-delivery: "UC_V1P49A"
    #    На экспертизе
        under-expertise: "UC_ZGFVFC"
    #    Обнаружены несоответствия
        defect-detected: "UC_1MEPDK"
    #    Передано в отдел логистики
        transferred-to-logistics-department: "UC_SU9BWM"
    #    Клиент получил заказ
        customer-receive-order: "UC_LY9VDA"
    #    Заказчик отменил заявку
        customer-cancel-order: "LOSE"
    #    Байер отказался от договоренностей
        seller-cancel-order: "6"
    #    Не прошёл экспертизу
        expertise-fail: "4"
    #    Сделка успешна
        deal-successful: "WON"
    #    Товар не оплачен
        product-not-paid: "UC_Z0YXC3"

#      Beegz-консьерж
      1:
    #   Ошибка с корзиной
        add-to-cart-fail: "C1:UC_YCPQNP"
    #   Товар в корзине
        add-to-cart-success: "C1:UC_T7J2B0"
        #   Оплаченные заказы
        deal-paid: "C1:UC_S8E1C1"
        #    Байер принял заказ
        bayer-accepted-deal: "C1:UC_3MMDWK"
        #    Ожидаем доставки
        awaiting-delivery: "C1:UC_TKE9BE"
        #    На экспертизе
        under-expertise: "C1:UC_OSHV7H"
        #    Обнаружены несоответствия
        defect-detected: "C1:UC_NJUGZ9"
        #    Передано в отдел логистики
        transferred-to-logistics-department: "C1:UC_E49PHZ"
        #    Клиент получил заказ
        customer-receive-order: "C1:UC_1FHSAL"
        #    Заказчик отменил заявку
        customer-cancel-order: "C1:UC_2LREDW"
        #    Байер отказался от договоренностей
        seller-cancel-order: "C1:8"
        #    Не прошёл экспертизу
        expertise-fail: "C1:6"
        #    Сделка успешна
        deal-successful: "C1:WON"
        #    Товар не оплачен
        product-not-paid: "C1:UC_0NKOHY"

#      International Concierge
      5:
    #   Ошибка с корзиной
        add-to-cart-fail: "C5:UC_F3N83R"
    #   Товар в корзине
        add-to-cart-success: "C5:UC_LDC8UN"
    #   Оплаченные заказы
        deal-paid: "C5:UC_DNJBMK"
    #    Байер принял заказ
        bayer-accepted-deal: "C5:UC_IS3ECN"
    #    Ожидаем доставки
        awaiting-delivery: "C5:UC_M0ARQV"
    #    На экспертизе
        under-expertise: "C5:UC_XKJTDA"
    #    Обнаружены несоответствия
        defect-detected: "C5:UC_6CUFZR"
    #    Передано в отдел логистики
        transferred-to-logistics-department: "C5:UC_Q77XC4"
    #    Клиент получил заказ
        customer-receive-order: "C5:UC_AFGF4E"
    #    Заказчик отменил заявку
        customer-cancel-order: "C5:UC_1377ER"
    #    Байер отказался от договоренностей
        seller-cancel-order: "C5:UC_ZTD6G5"
    #    Не прошёл экспертизу
        expertise-fail: "C5:UC_IXL446"
    #    Сделка успешна
        deal-successful: "C5:WON"
    #    Товар не оплачен
        product-not-paid: "C5:UC_O4IP11"

    deal:
      concierge-for-sellers:
        new: "C2:NEW"
        in-progress: "C2:EXECUTING"
        confirmed: "C2:PREPARATION"
        declined: "C2:APOLOGY"
  categories:
    concierge-for-sellers.id: 2
    int-concierge-for-buyer:
      id: 5
  sources:
    application: "UC_91J01U"
    admin: "UC_A52CVM"
    web-oskelly: "UC_630JQG"
    comment: "UC_C4S3CN"
    concierge-showcase: "UC_YB4JZU"
  url-template:
    deal: https://bitrix.oskelly.ru/crm/deal/details/%s/
    customer-order-link: https://oskelly.ru/account/orders/%s?ctx=BuyerOrderDetails
    smart-process: https://bitrix.oskelly.ru/crm/type/%s/details/%s/
  jobs:
    cross-border:
      enabled: false
      cron: "0 0/15 * * * *"
  webhook-tokens:
    deal-update: ${WEBHOOK_TOKEN_DEAL_UPDATE:0xvr9acrissebrbmheja0fc3uxfq8ywn}

concierge-service:
  enabled: ${CONCIERGEENABLE:false}
  host: ${CONCIERGEURL:https://concierge.oskelly.ru}
  connect-timeout: 5000
  read-timeout: 5000
  ap-price-threshold-new: 150000
  ap-price-threshold-resale: 300000


manychat:
  enable: false
  host: https://api.manychat.com
  token:
  max-retry-count: 3
  limit-for-processing:  10
  processing-cron: "-"
  custom-fields:
    phone:
      id:
    concierge-info:
      name: "Concierge Info"
    order:
      id:
  flow:
    init-message:
      flow-ns:
    init-message-vtb:
      flow-ns:
    notify-buyer:

notifications:
  notification-completion:
    cron: "-"
    defaultDuration: PT24H
    pageSize: 1000
    dtypesDuration:
      NewCommentNotification: PT5H # имеет смысл проверять до тех пор, пока не уведомили в whatsapp
      BargainBuyerConfirmedNotification: PT5H # имеет смысл проверять до тех пор, пока не уведомили в whatsapp
      RegisterNotification: ${notifications.notification-completion.defaultDuration}
      SetLowerPriceNotification: ${notifications.notification-completion.defaultDuration}
      SetLowerPriceForSeveralProductsNotification: ${notifications.notification-completion.defaultDuration}
      ModerationFailedNotification: ${notifications.notification-completion.defaultDuration}
      OrderDeliveredToBuyerNeedAgentReportNotification: ${notifications.notification-completion.defaultDuration}
      BargainBuyerCounterOfferNotification: ${notifications.notification-completion.defaultDuration}
      OrderNeedConfirmationNotification: ${notifications.notification-completion.defaultDuration}
      BargainSellerNewOfferNotification: ${notifications.notification-completion.defaultDuration}
      AddBirthdateAndAvatarNotification: ${notifications.notification-completion.defaultDuration}
  setLowerPrice:
    cron: "-"
    productPublishingTimeBefore: P3M
    repeatAfter: P7D
  #old-value 0 30 20 * * *
  no-activity-notification:
    cron: "-"
  #old-value 0 * 19-23 * * *
  you-will-like-it-notification:
    cron: "-"
  #old-value 0 * 14-18 * * *
  new-arrivals-notification:
    cron: "-"
  complete-publication:
    cron: "-"
    notification-interval: PT1H
    error-interval: PT31M
  create-publication-notification:
    cron: "-"
    delivered-to-buyer-interval: "PT30M"
    delivered-order-count-condition: "2"
    error-interval: "PT31M"
    frequency-of-notifications: "PT43200M"   #30 дней в минутах
    active-publications:
      - PUBLISHED
      - DRAFT
  complete-publication-range-day:
    cron: "-"
    notification-interval: PT24H
    error-interval: PT25H
  schedule:
    FirstProductLikeNotification:
      startTime: '08:00:00'
      endTime: '23:59:00'
      timezone: 'Europe/Moscow'
    NewProductLikeNotification:
      startTime: '08:00:00'
      endTime: '23:59:00'
      timezone: 'Europe/Moscow'
    WishlistItemSoldNotification:
      startTime: '08:00:00'
      endTime: '23:59:00'
      timezone: 'Europe/Moscow'
    WishlistItemActiveBargainNotification:
      startTime: '08:00:00'
      endTime: '23:59:00'
      timezone: 'Europe/Moscow'

  welcome:
    enabled: false
    initialDate: '2023-06-21T10:15:30+01:00[Europe/Moscow]' #Дата с которой запускаются джобы, чтобы пользователь с 10 дневной регистрацией не получил 9 пушей
    HowItWorksNotification:
      cron: "-"
    HowToTakePhotoNotification:
      cron: "-" # 0 * 11-18 * * * At every minute past every hour from 11 through 18.
    HowToUseBargainNotification:
      cron: "-"
    UsePromocodeSecondDayNotification:
      cron: "-"
    UsePromocodeSixthDayNotification:
      cron: "-"
    UsePromocodeNinthDayNotification:
      cron: "-"
    WhatIsBeegzNotification:
      cron: "-"
    WhatIsConciergeNotification:
      cron: "-" # 0 * 18-23 * * * At every minute past every hour from 18 through 23.
    WhyNeedAuthenticationNotification:
      cron: "-"
    WhyNeedLikeNotification:
      cron: "-" # 0 * 18-23 * * * At every minute past every hour from 18 through 23
    SubscribeCelebritiesNotification:
      cron: "-" # * * 11-18 * * * At every minute past every hour from 11 through 18.

notification-cleaner:
  cron: "-"
  olderThanDays: 365
  olderThanDaysShortLife: 30

notification-expire:
  cron: "-"
  batchSize: 500
  dtypes:
    SetLowerPriceNotification: P1D

agent-report-payments:
  job:
    prepare-seller-payouts:
      cron: "-"

logistics:
  job:
    auto-pickup-runner:
      processing-limit: 30
      retry-max-count: 5
      retry-initial-step-sec: 180
      retry-multiplier: 2
      cron: "-"
  pickup:
    moscow:
      maxDalliOrderAmount: 50000

comment-images:
  job:
    images-resizer:
      processing-limit: 50
      cron: "-"

user-vip-status:
  job:
    vip-status-recalculation-runner:
      cron: "-"

product-badges:
  job:
    season-badges-recalculation-runner:
      processing-limit: 1000
      cron: "-"

product-delivery:
  job:
    product-delivery-calculation-runner:
      processing-limit: 100
      cron: "-"
    product-delivery-init-runner:
      processing-limit: 100
      cron: "-"

order-events:
  cron: "-"

delete-unused-addresses:
  cron: "-"

remove-expired-boutique-badge:
  cron: "-"

recalculate-estimations:
  cron: "-"

instorepickup:
  name: самовывоз
  phone: +7 (499) 501-11-62

analytics: #oskelly analytics service configuration
  url: #format -> http://localhost:8089

product-request-service:
  enabled: false
  notification:
    task-runner:
      enabled: false
      InformAboutProductRequestServiceNotification:
        cron: "-"
        duration-after-app-installation-days: #
      NeedPublishProductRequestNotification:
        cron: "-"
        batch-size: 100
        duration-between-notification-days: #
      NoResponseProductRequestNotification:
        cron: "-"
        batch-size: 100
        duration-after-publication-days: #
        duration-not-older-publication-days: #
      InformAboutCountOfUserWhoCreateProductRequestNotification:
        cron: "-"
        duration-between-notification-days: #
        batch-size: 100
      MotivateCreateProductResponseNotification:
        cron: "-"
        duration-between-notification-days: #
        batch-size: 100
  url: http://localhost:8085


sale-request-service:
  enabled: false
  url: http://localhost:8085
  connectTimeout: 1s
  httpTimeout: 10s
  executorPoolSize: 5
  maxExecutorPoolSize: 20
  executorPoolCapacity: 100

user-segmentation:
  jobs:
    user-vector-runner:
      enabled: false
      recalculate-order-count:
        cron: "-"
        order-day-duration: 100 #Right limit for hold_time. For example, 2021-10-10 > hold_time >= last_hold_time + order-day-duration

retool:
  host: https://retool-dev.oskelly.tech

keycloak:
  enabled: false
  #аналог настройки security.oauth2.resourceserver.jwt.issuer-uri
  jwtIssuerUri: http://localhost:8888/realms/OskellyRealm

customFilters:
  slaFilter:
    statusLifeTimeInMins: '{"NEED_MODERATION": 100, "NEED_RETOUCH": 200}'

integration-sellers:
  oskelly:
    event:
      queue:
        enabled: false
        name: integration-sellers.oskelly.event.queue

verification:
  phone-number:
    settings:
      attempt:
        count: 3
        timeout-sec: 60
  service:
    url: http://oskelly-verification:8080
    connectTimeout: 1s
    readTimeout: 1s

# TODO: настроить пул согласованно с другими пулами
bonuses-service:
  enabled: false
  notLowerRest: 1
  url: http://localhost:8090
  connectTimeout: 5s
  socketTimeout: 30s
  executorPoolSize: 5
  maxExecutorPoolSize: 20
  executorQueueCapacity: 100
  programs:
    #Необходимо согласовывать между собой длительность бонусов в днях (duration-in-days) и даты отправки нотификаций (days-before-end)
    #Если это не учитывать, то получим некорректную с точки зрения пользователей последовательность нотификаций. Например 2 или 3 одновременно.
    #При смене значения в параметрах days-before-end надо не забывать проверить текст соответствующего уведомления в ресурсах
    birthday:
      enabled: false
      #Данный параметр заполняется с учетом дня рождения. Т.е., если по постановке требуется что бы балы действовали 22 дня после ДР, то здесь должно быть значени 23.
      days-after-birthday: 23
      days-before-birthday: 7
      days-check-period: 355
      notifications:
        stage1:
          days-before-end: 30
          time: 9h
          order: 1
        stage2:
          days-before-end: 22
          time: 9h
          order: 2
        stage3:
          days-before-end: 3
          time: 9h
          order: 3
    welcome:
      enabled: false
      duration-in-days: 30
      sum: 3000
      notifications:
        stage1:
          days-before-end: 14
          time: 9h
          order: 1
        stage2:
          days-before-end: 3
          time: 9h
          order: 2
    default:
      duration-in-days: 365
      notifications:
        stage1:
          days-before-end: 14
          time: 9h
          order: 1
        stage2:
          days-before-end: 3
          time: 9h
          order: 2
  notification:
    expiration:
      min-amount: 1000
  scheduler:
    error-income-transactions-processing:
      portion-size: 100
      max-attempts: 5
      cron: "-"
    old-hold-transactions-processing:
      oldness-in-seconds: 3600
      cron: "-"
    offline-bonuses-transfer:
      oldness-in-days: 14
      cron: "-"
loyalty-cards-service:
  enabled: false
  url: http://localhost:8070
  connectTimeout: 5s
  socketTimeout: 60s
  executorPoolSize: 5
  maxExecutorPoolSize: 20
  executorQueueCapacity: 100

community-service:
  enabled: false
  url: http://localhost:8085
  connectTimeout: 5s
  socketTimeout: 30s
  executorPoolSize: 5
  maxExecutorPoolSize: 20
  executorQueueCapacity: 100
  scheduler:
    community-status-sync:
      cron: "-"
    birthday-promocodes-generate:
      cron: "-"
    init-task:
      status-update:
        cron: "-"
      status-manual-update:
        cron: "-"
      status-may-be-lost-notification:
        cron: "-"
      users-bans:
        cron: "-"
      users-data-grabber:
        cron: "-"
  days-for-transactions: 180
  birthday-promocodes:
    promocodesProperties:
      -
        name: oskelly
        prefix: BDAY-
        discount: 5
        begin-price: 50000
        duration-days: 14
        type-id: null
      -
        name: concierge
        prefix: CON-
        discount: 3
        begin-price: 30000
        duration-days: 14
        type-id: null

external-catalog-service:
  enabled: false
  experiments-checking:
    enabled: false
  url: http://localhost:8086
  connect-timeout: 1s
  socket-timeout: 4s
  task:
    enabled: false
    update-synonyms:
      cron: "-"

filter-subscriptions-service:
  enabled: ${FILTER_SUBSCRIPTIONS_SERVICE_ENABLED:false}
  url: ${FILTER_SUBSCRIPTIONS_SERVICE_URL:http://localhost:8082}
  connect-timeout: 1s
  socket-timeout: 30s
  getting-subscription-for-available-filters-timeout: 1s

external-spellcheck-service:
  enabled: false
  url: http://localhost:8089
  connect-timeout: 1s
  socket-timeout: 30s
  flagr-flag: spellcheck-enabled
  min-symbols-to-check: 2

external-suggesting-service:
  enabled: false
  url: http://localhost:8090
  connect-timeout: 1s
  socket-timeout: 30s

gazprom:
  user-create:
    max-attempts: 20

user-sync-service:
  test-mode:
    enabled: ${USER_SYNC_TEST_MODE:true}
    uuids: ${USER_SYNC_ENABLED_TEST_UUIDS:}
  enabled:
    send: ${USER_SYNC_SEND_ENABLED:false}
    consume: ${USER_SYNC_CONSUME_ENABLED:false}
  jobs:
    retry:
      cron: "-"
      expiration-hours: 24
      batchSizeLimit: 10
  kafka:
    topic: ${USER_SYNC_KAFKA_TOPIC:kafka.core.userSync}
    user-ban-topic: ${USER_SYNC_KAFKA_USER_BAN_TOPIC:kafka.core.userBanSync}
    consumer:
      username: ${KAFKA_USER:oskelly}
      password: ${KAFKA_PASSWORD:oskelly123}
      group-id: ${USER_SYNC_CONSUMER_GROUP_ID:coreRU}
      bootstrap-servers: ${USER_SYNC_KAFKA_CONSUMER_BOOTSTRAP_SERVER:localhost:9092}
      topic: ${USER_SYNC_KAFKA_CONSUMER_TOPIC:kafka.core.userSync}
      user-ban-topic: ${USER_SYNC_KAFKA_CONSUMER_USER_BAN_TOPIC:kafka.core.userBanSync}
      properties:
        security.protocol: ${USER_SYNC_KAFKA_SECURITY_PROTOCOL:SASL_PLAINTEXT}   # см. org.apache.kafka.common.security.auth.SecurityProtocol
        sasl.mechanism: ${USER_SYNC_KAFKA_SASL_MECHANISM:SCRAM-SHA-512}       # см. org.apache.kafka.common.config.SaslConfigs, https://www.iana.org/assignments/sasl-mechanisms/sasl-mechanisms.xhtml
        sasl.jaas.config: ${USER_SYNC_KAFKA_SASL_JAAS_CONFIG:org.apache.kafka.common.security.scram.ScramLoginModule required username="${user-sync-service.kafka.consumer.username}" password="${user-sync-service.kafka.consumer.password}";}
  updaters:
    user-updated-event-sender:
      enabled: ${USER_SYNC_USER_UPDATED_EVENT_SENDER_ENABLED:true}
  master-service:
    url: ${USER_SYNC_MASTER_SERVICE_URL:localhost}
    user-email: ${USER_SYNC_MASTER_SERVICE_EMAIL:<EMAIL>}
    user-password: ${USER_SYNC_MASTER_SERVICE_PASS:sync-master}
    attemptsCount: ${USER_SYNC_MASTER_SERVICE_ATTEMPTS_COUNT:1}
    attemptsDelayPeriodSeconds: ${USER_SYNC_MASTER_SERVICE_ATTEMPTS_DELAY_SEC:5}
    client-user-agent: oskelly-ext-dev

social-service:
  enabled: false
  url: http://localhost:8087
  connect-timeout: 2s
  socket-timeout: 30s
  executor-pool-size: 5
  max-executor-pool-size: 20
  executor-queue-capacity: 100

file-service:
  socket-timeout: 30s

hide-user-products-service:
  warning-notification-trigger-id: 16133
  final-notification-trigger-id: 16135
  send-notification-test-mode: false
  send-notification-delay-ms: 250 # пауза 250 мс => ~4 вызовов/сек => ~240 вызовов/мин
  send-notification-retry-max-count: 3
  users-inactive-since: 2024-10-01
  job:
    final-notifications-runner:
      cron: "-"
      processing-limit: 250

mongo-collections-migration-runner:
  enabled: false

springdoc:
  swagger-ui:
    enabled: ${SWAGGER_ENABLED:false}
  packages-to-scan:
    - su.reddot.presentation.api.v2.admin.concierge
    - su.reddot.presentation.api.v2.admin.concierge.mercaux
    - su.reddot.presentation.api.v2.concierge

search-history-service:
  enabled: false
  url: http://localhost:8088
  connectTimeout: 5s
  socketTimeout: 30s
  executorPoolSize: 5
  maxExecutorPoolSize: 20
  executorQueueCapacity: 100

loyalty-service:
  enabled: false
  #Настройка для отображения в settings
  bonuses-transferring-enabled: true
  url: http://localhost:8092
  connect-timeout: 2s
  socket-timeout: 30s
  executor-pool-size: 5
  max-executor-pool-size: 20
  executor-queue-capacity: 100
  noop:
    transfer-percent: 0
    transfer-bonuses-limit: 1000000
    withdraw-percent: 30
  #Начало и окончания периода для вычисления общей суммы заказов пользователя
  orders-sum:
    date-from-days-before-now: 379
    date-to-days-before-now: 14
  statuses-sync:
    #Размер страницы в кол-ве пользователей при фолбэк синхронизации статусов из сервиса лояльности в коммонтеги
    page-size: 200
  scheduler:
    #Джоб по начислению бонусов на ДР пользователям-участникам программы лояльности
    bonuses-transfer-birthday:
      #По ТЗ запуск должен быть один раз в сутки
      cron: "-"
    #Джоб по инкрементальному подсчету суммы заказов пользователя, начислению бонусов и последующему пересчету статусов лояльности
    bonuses-transfer-order-and-loyalty-statuses-recalc:
      #Частый запуск. По идее должен запускаться раз в несколько минут (параметр interval контролируется внутри джоба)
      cron: "-"
    #Джоб по массовому пересчету статусов пользователей раз в сутки
    loyalty-statuses-recalc:
      #Частый запуск. По идее должен запускаться +- раз в час. Внутри контролируется, что реальный запуск один раз в сутки при отсутствии ошибок. При наличии ошибок будет пытаться выполниться в момент запуска.
      cron: "-"
      #Время суток для старта джоба
      start-time: PT4H
    #Фолбэк джоб по синхронизации статусов пользователей в коммонтеги
    loyalty-statuses-sync:
      #По идее запуск один раз в сутки
      cron: "-"
  privileges:
    exclusive-lots:
      enabled: false

purchaser-service:
  enabled: ${SALES_APP_ENABLED:false}
  api:
    url: ${SALES_APP_HOSTNAME:https://sales-app-api.oskelly.tech}
  employee-role-interceptor:
    enabled: ${EMPLOYEE_ROLE_INTERCEPTOR_ENABLED:false}

purchasers:
  online-activity:
    days-counting: 30

feign:
  client:
    config:
      default:
        connect-timeout: 1000
        read-timeout: 30000
