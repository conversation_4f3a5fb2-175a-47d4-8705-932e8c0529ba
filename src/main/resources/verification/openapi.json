{"openapi": "3.0.1", "info": {"title": "Сервис верификации по SMS", "description": "API для генерации и верификации кодов SMS", "contact": {"name": "<PERSON><PERSON>", "email": "s<PERSON><PERSON><PERSON>@oskelly.ru"}, "version": "1.0"}, "externalDocs": {"description": "Документация к проекту", "url": "https://wiki.yandex.ru/projects/servis-verifikacii-nomera-telefona/"}, "servers": [{"url": "http://localhost:8448/v1/api", "description": "Локальный сервер"}], "tags": [{"name": "JwtVerification", "description": "API для работы с верификацией JWT токена"}, {"name": "SmsVerification", "description": "API для работы с SMS верификацией"}], "paths": {"/api/v1/verify": {"post": {"tags": ["SmsVerification"], "summary": "Проверка кода", "description": "Проверяет предоставленный код на соответствие коду, отправленному на номер телефона пользователя", "operationId": "verifyCode", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerificationPayload"}}}, "required": true}, "responses": {"429": {"description": "Превышено количество запросов", "content": {"*/*": {"schema": {"type": "object"}}}}, "500": {"description": "Внутренняя ошибка сервера", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Неверный запрос, ошибка валидации", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "Статус верификации", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyBoolean"}}}}}}}, "/api/v1/verifyAndGetJwt": {"post": {"tags": ["SmsVerification"], "summary": "Проверка кода и генерация JWT токена", "description": "Проверяет предоставленный код на соответствие коду, отправленному на номер телефона пользователя и генерирует JWT токен", "operationId": "verifyCodeAndGenerateJwtToken", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerificationPayload"}}}, "required": true}, "responses": {"429": {"description": "Превышено количество запросов", "content": {"*/*": {"schema": {"type": "object"}}}}, "500": {"description": "Внутренняя ошибка сервера", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Неверный запрос, ошибка валидации", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "Статус верификации", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyString"}}}}}}}, "/api/v1/jwt/verify": {"post": {"tags": ["JwtVerification"], "summary": "Верификация JWT токена сгенерированного после проверки номера телефона", "operationId": "verifyJWTToken", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyJwtTokenRequest"}}}, "required": true}, "responses": {"429": {"description": "Too Many Requests", "content": {"*/*": {"schema": {"type": "object"}}}}, "500": {"description": "Внутренняя ошибка сервера", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Неверный запрос, ошибка валидации", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "Проверка", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyBoolean"}}}}}}}, "/api/v1/generate": {"post": {"tags": ["SmsVerification"], "summary": "Генерация кода верификации", "description": "Генерирует новый код верификации и отправляет его на указанный номер телефона", "operationId": "generateCode", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerationPayload"}}}, "required": true}, "responses": {"429": {"description": "Превышено количество запросов", "content": {"*/*": {"schema": {"type": "object"}}}}, "500": {"description": "Внутренняя ошибка сервера", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Неверный запрос, ошибка валидации", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "Код успешно сгенерирован", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyUUID"}}}}}}}}, "components": {"schemas": {"VerificationPayload": {"required": ["code", "guestToken", "operation", "phoneNumber", "token"], "type": "object", "properties": {"guestToken": {"type": "string", "description": "То<PERSON><PERSON><PERSON>, идентифицирующий гостевую сессию"}, "userId": {"type": "string", "description": "Идентификатор пользователя, если применимо"}, "phoneNumber": {"pattern": "^\\+\\d{6,15}$", "type": "string", "description": "Номер телефона, на который будет отправлен код верификации", "example": "+79991234567"}, "operation": {"type": "string", "description": "Идентификатор операции", "enum": ["VERIFY_PHONE_NUMBER", "AUTH_OR_REGISTER", "VERIFY_PHONE_NUMBER_3RD_PARTY"]}, "context": {"type": "object", "additionalProperties": {"type": "string", "description": "Дополнительный контекст для операции"}, "description": "Дополнительный контекст для операции"}, "token": {"type": "string", "description": "То<PERSON><PERSON>н, идентифицирующий сессию подтверждения"}, "code": {"pattern": "^\\d{4}$", "type": "string", "description": "СМС для подтверждения номера"}}, "description": "Запрос на проверку и генерацию кода верификации"}, "ResponseBodyBoolean": {"type": "object", "properties": {"data": {"type": "boolean", "description": "Данные ответа, могут быть null."}, "message": {"type": "string", "description": "Сообщение ответа, может быть использовано для передачи сообщений об ошибках или других уведомлений."}}, "description": "Обобщенный класс тела ответа API."}, "ResponseBodyString": {"type": "object", "properties": {"data": {"type": "string", "description": "Данные ответа, могут быть null."}, "message": {"type": "string", "description": "Сообщение ответа, может быть использовано для передачи сообщений об ошибках или других уведомлений."}}, "description": "Обобщенный класс тела ответа API."}, "VerifyJwtTokenRequest": {"required": ["guestToken", "jwtToken", "operation", "phoneNumber"], "type": "object", "properties": {"phoneNumber": {"pattern": "^\\+\\d{6,15}$", "type": "string", "description": "Номер телефона, на который будет отправлен код верификации", "example": "+79991234567"}, "guestToken": {"type": "string", "description": "То<PERSON><PERSON><PERSON>, идентифицирующий гостевую сессию"}, "operation": {"type": "string", "description": "Идентификатор операции", "enum": ["VERIFY_PHONE_NUMBER", "AUTH_OR_REGISTER", "VERIFY_PHONE_NUMBER_3RD_PARTY"]}, "jwtToken": {"type": "string", "description": "JWT токен"}}, "description": "Запрос на проверку JWT токена"}, "GenerationPayload": {"required": ["guestToken", "operation", "path", "phoneNumber"], "type": "object", "properties": {"guestToken": {"type": "string", "description": "То<PERSON><PERSON><PERSON>, идентифицирующий гостевую сессию"}, "userId": {"type": "string", "description": "Идентификатор пользователя, если применимо"}, "phoneNumber": {"pattern": "^\\+\\d{6,15}$", "type": "string", "description": "Номер телефона, на который будет отправлен код верификации", "example": "+79991234567"}, "ip": {"type": "string", "description": "IP клиента"}, "operation": {"type": "string", "description": "Идентификатор операции", "enum": ["VERIFY_PHONE_NUMBER", "AUTH_OR_REGISTER", "VERIFY_PHONE_NUMBER_3RD_PARTY"]}, "environment": {"type": "string", "description": "Среда, к которой относится страна телефона", "enum": ["RU", "INT"]}, "context": {"type": "object", "additionalProperties": {"type": "string", "description": "Дополнительный контекст для операции"}, "description": "Дополнительный контекст для операции"}, "template": {"type": "string", "description": "Код шаблона отправляемого сообщения. Если не передан, то подставляется значение по умолчанию из конфигурационного файла.", "enum": ["DEFAULT", "BONUSES_OFFLINE"]}, "path": {"type": "string", "description": "Каким путем будет отправлен код подтверждения", "enum": ["SMS", "WA"]}}, "description": "Запрос на проверку и генерацию кода верификации"}, "ResponseBodyUUID": {"type": "object", "properties": {"data": {"type": "string", "description": "Данные ответа, могут быть null.", "format": "uuid"}, "message": {"type": "string", "description": "Сообщение ответа, может быть использовано для передачи сообщений об ошибках или других уведомлений."}}, "description": "Обобщенный класс тела ответа API."}}}}