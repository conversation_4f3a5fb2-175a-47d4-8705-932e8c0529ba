openapi: 3.0.1
info:
  title: Personalized Product Service API
  version: v1
servers:
  - url: http://oskelly-personalized
    description: Personalized Product Service
paths:
  /api/v1/products/personalized:
    get:
      operationId: getPersonalizedProductList
      summary: Получить персонализированные рекомендации товаров
      description: Возвращает рекомендованные товары для пользователя с возможностью фильтрации по бренду, категории и типу продукта
      tags:
        - personalized-controller
      parameters:
        - name: userId
          in: query
          description: Идентификатор пользователя
          required: false
          schema:
            type: integer
            format: int64
        - name: limit
          in: query
          description: Максимальное количество рекомендаций в ответе
          required: true
          schema:
            type: integer
            format: int32
        - name: brands
          in: query
          description: Фильтр по идентификаторам брендов
          required: false
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: categories
          in: query
          description: Фильтр по идентификаторам категорий
          required: false
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: type
          in: query
          description: Тип товара для фильтрации
          required: false
          schema:
            type: string
            description: Тип товара
            enum:
              - New
              - Resale
              - All
        - name: time_decay
          in: query
          description: Коэффициент временного затухания для подсчета релевантности товаров
          required: false
          schema:
            type: number
            format: double
        - name: exploration_rate
          in: query
          description: Процент добавления в выдачу свежих товаров
          required: false
          schema:
            type: number
            format: double
      responses:
        "200":
          description: Успешный ответ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductPersonalizedListDTO'
components:
  schemas:
    ProductPersonalizedListDTO:
      type: object
      properties:
        personalized:
          type: array
          items:
            $ref: '#/components/schemas/ProductPersonalizedDTO'
    ProductPersonalizedDTO:
      type: object
      properties:
        productId:
          type: integer
          format: int64
        score:
          type: number
          format: double