INSERT INTO reader_device_mask_config(rule_name,input_mask,replace_mask,object_type,"comment")
VALUES ('MARKING-CODE-RU-LIGHT-INDUSTRY-091-WITH-GS','^(<[Gg][Ss]>)?(01\d{14}21.{13})<[Gg][Ss]>(.{6})<[Gg][Ss]>(92.{44})$','$2<GS>$3<GS>$4','MARKING_CODE',
        'Код маркировки RU (легпром), с символами <GS>')
ON CONFLICT DO NOTHING;

INSERT INTO reader_device_mask_config(rule_name,input_mask,replace_mask,object_type,"comment")
VALUES ('MARKING-CODE-RU-LIGHT-INDUSTRY-091-NULL-GS','^(01\d{14}21.{13})(.{6})(92.{44})$','$1<GS>$2<GS>$3','MARKING_CODE',
        'Код маркировки RU (легпром), без символов <GS>')
ON CONFLICT DO NOTHING;

INSERT INTO reader_device_mask_config(rule_name,input_mask,replace_mask,object_type,"comment")
VALUES ('MARKING-CODE-RU-LOOKUP-PART','^(01\d{14}21.*)$','$1.*','MARKING_CODE_LOOKUP_PART',
        'Код маркировки RU (обувь): поиск по началу кода')
ON CONFLICT DO NOTHING;

UPDATE marking_code_type
SET code_mask = '^(01\d{14}21.{13})<GS>(.{6})<GS>(92.{88})$'
WHERE "name" = 'RU-SHOES';

UPDATE marking_code_type
SET code_mask = '^(01\d{14}21.{13})<GS>(.{6})<GS>(92.{44})$'
WHERE "name" = 'RU-LIGHT-INDUSTRY';