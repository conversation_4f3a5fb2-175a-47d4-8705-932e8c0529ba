INSERT INTO notification_group_dtype_binding (notification_group_id, notification_dtype)
VALUES (1, 'BargainSellerNewOffer24hNotification');
INSERT INTO notification_group_dtype_binding (notification_group_id, notification_dtype)
VALUES (1, 'BargainBuyerAlmostExpiredByBuyerNotification');
INSERT INTO notification_group_dtype_binding (notification_group_id, notification_dtype)
VALUES (1, 'BargainSellerNewOfferNotification');
INSERT INTO notification_group_dtype_binding (notification_group_id, notification_dtype)
VALUES (1, 'BargainBuyerConfirmedNotification');
INSERT INTO notification_group_dtype_binding (notification_group_id, notification_dtype)
VALUES (1, 'BargainBuyerDeclinedNotification');
INSERT INTO notification_group_dtype_binding (notification_group_id, notification_dtype)
VALUES (1, 'Bargain<PERSON>uyerCounterOfferNotification');
INSERT INTO notification_group_dtype_binding (notification_group_id, notification_dtype)
VALUES (1, 'BargainBuyerExpiredByBuyerNotification');
INSERT INTO notification_group_dtype_binding (notification_group_id, notification_dtype)
VALUES (1, 'BargainBuyerProductSoldNotification');
INSERT INTO notification_group_dtype_binding (notification_group_id, notification_dtype)
VALUES (1, 'BargainSellerNewOffer12hNotification');
