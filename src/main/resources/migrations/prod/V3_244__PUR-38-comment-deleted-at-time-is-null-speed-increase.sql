-- Этот индекс неэффективен из-за того что количество записей с is_null свыше 60%
drop index if exists comment_deleted_at_time;

-- Добавляется индекс для запроса комментариев где deleted_at_time is null.
-- Этот индекс сделан для конкретного запроса, чтобы выполнялось условие в PostgresSql на only-index search.
create index if not exists comment_deleted_at_time_key
    on public.comment (product_id, deleted_at_time) where deleted_at_time is null;