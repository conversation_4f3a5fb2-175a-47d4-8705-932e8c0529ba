ALTER TABLE IF EXISTS order_position
    ADD COLUMN IF NOT EXISTS country_of_origin_id BIGINT REFERENCES country (id);

ALTER TABLE IF EXISTS order_position_aud
    ADD COLUMN IF NOT EXISTS country_of_origin_id BIGINT;

INSERT INTO country_context (id, name) VALUES (7, 'PRODUCT_ORIGIN') ON CONFLICT DO NOTHING;
INSERT INTO country_context (id, name) VALUES (8, 'PRODUCT_ORIGIN_EXT') ON CONFLICT DO NOTHING;

INSERT INTO country_context_relation(country_id, country_context_id) SELECT id, 7 FROM country ON CONFLICT DO NOTHING