INSERT INTO reader_device_mask_config(rule_name, input_mask, replace_mask, object_type, comment)
VALUES ('MARKING-CODE-RU-SHOES-135-WITH-GS','^(01\d{14}21.{13})<[Gg][Ss]>(.{6})<[Gg][Ss]>(92.{88})$','$1<GS>$2<GS>$3','MARKING_CODE',
        'Код маркировки RU (обувь), с символами <GS>')
ON CONFLICT DO NOTHING;

INSERT INTO reader_device_mask_config(rule_name, input_mask, replace_mask, object_type, comment)
VALUES ('MARKING-CODE-RU-SHOES-135-NULL-GS','^(01\d{14}21.{13})(.{6})(92.{88})$','$1<GS>$2<GS>$3','MARKING_CODE',
        'Код маркировки RU (обувь), без символов <GS>')
ON CONFLICT DO NOTHING;