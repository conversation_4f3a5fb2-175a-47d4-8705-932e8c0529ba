-- 1st: remove dupes (2+ promocodes on one order)
DELETE FROM protected_promo_code ppc1
    USING   protected_promo_code ppc2
WHERE   ppc1.id  > ppc2.id
  AND ppc1.order_id = ppc2.order_id;

-- 2nd: add a constraint 1 order - 1 protected promocode
DO $$
BEGIN
	IF NOT EXISTS (SELECT constraint_name
		FROM information_schema.constraint_column_usage
		WHERE constraint_name = 'protected_promo_code_unique_order_id') THEN
			EXECUTE 'ALTER TABLE public.protected_promo_code ADD CONSTRAINT protected_promo_code_unique_order_id UNIQUE(order_id)';
END IF;
END
$$