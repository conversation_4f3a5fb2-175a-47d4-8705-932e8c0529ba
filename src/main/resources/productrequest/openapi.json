{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "paths": {"/api/v1/productResponse": {"get": {"tags": ["product-response-controller-v-1"], "operationId": "get", "parameters": [{"name": "params", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ProductResponseRequestParams"}}, {"name": "pageableRequestParams", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PageableRequestParams"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyPageDTOProductResponseDtoResponseFull"}}}}}}, "post": {"tags": ["product-response-controller-v-1"], "operationId": "getByIds", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyListProductResponseDtoResponseFull"}}}}}}}, "/api/v1/productResponse/users": {"post": {"tags": ["product-response-controller-v-1"], "operationId": "getUserIdsByParams", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductResponseUsersRequestParams"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyListLong"}}}}}}}, "/api/v1/productResponse/update": {"post": {"tags": ["product-response-controller-v-1"], "operationId": "update", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductResponseDtoRequestUpdate"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyProductResponseDtoResponseFull"}}}}}}}, "/api/v1/productResponse/updateState": {"post": {"tags": ["product-response-controller-v-1"], "operationId": "updateState", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductResponseDtoRequestUpdateState"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyBoolean"}}}}}}}, "/api/v1/productResponse/getByProductIds": {"post": {"tags": ["product-response-controller-v-1"], "operationId": "getByProductIds", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyListProductResponseDtoResponseFull"}}}}}}}, "/api/v1/productRequest": {"get": {"tags": ["product-request-controller-v-1"], "operationId": "get_1", "parameters": [{"name": "stateList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string", "enum": ["DRAFT", "MODERATION", "PUBLISHED", "HIDDEN", "REJECTED", "DELETED"]}}}, {"name": "enrichContext", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ProductRequestDtoRequestContext"}}, {"name": "pageableRequestParams", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PageableRequestParams"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyPageDTOProductRequestDtoResponseFull"}}}}}}, "post": {"tags": ["product-request-controller-v-1"], "operationId": "getProductRequestByIds", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyListProductRequestDtoResponseFull"}}}}}}}, "/api/v1/productRequest/update": {"post": {"tags": ["product-request-controller-v-1"], "operationId": "update_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductRequestDtoRequestUpdate"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyProductRequestDtoResponseFull"}}}}}}}, "/api/v1/productRequest/statistic/usersWithProductRequests": {"post": {"tags": ["product-request-controller-v-1"], "operationId": "getStatistic", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatisticUsersWithProductRequestsContext"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyStatisticUsersWithProductRequests"}}}}}}}, "/api/v1/productRequest/filter": {"post": {"tags": ["product-request-controller-v-1"], "operationId": "getAvailableFilters", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductRequestDtoRequestFilter"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyProductRequestDtoResponseAvailableFilter"}}}}}}}, "/api/v1/productRequest/filter/items": {"post": {"tags": ["product-request-controller-v-1"], "operationId": "getPageByFilter", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"filter": {"$ref": "#/components/schemas/ProductRequestDtoRequestFilter"}, "enrichContext": {"$ref": "#/components/schemas/ProductRequestDtoRequestContext"}, "pageableRequestParams": {"$ref": "#/components/schemas/PageableRequestParams"}}}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyPageDTOProductRequestDtoResponseFull"}}}}}}}, "/api/v1/productRequest/filter/count": {"post": {"tags": ["product-request-controller-v-1"], "operationId": "getCountByFilter", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductRequestDtoRequestFilter"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyLong"}}}}}}}, "/api/v1/productResponse/{id}": {"get": {"tags": ["product-response-controller-v-1"], "operationId": "getById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyProductResponseDtoResponseFull"}}}}}}}, "/api/v1/productResponse/getByUserId": {"get": {"tags": ["product-response-controller-v-1"], "operationId": "getByUserId", "parameters": [{"name": "params", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ProductResponseRequestParams"}}, {"name": "pageableRequestParams", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PageableRequestParams"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyPageDTOProductResponseDtoResponseFull"}}}}}}}, "/api/v1/productResponse/getByProductRequestId": {"get": {"tags": ["product-response-controller-v-1"], "operationId": "getByProductRequestId", "parameters": [{"name": "params", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ProductResponseRequestParams"}}, {"name": "pageableRequestParams", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PageableRequestParams"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyPageDTOProductResponseDtoResponseFull"}}}}}}}, "/api/v1/productRequest/{productRequestId}": {"get": {"tags": ["product-request-controller-v-1"], "operationId": "getProductRequestById", "parameters": [{"name": "productRequestId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "enrichContext", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ProductRequestDtoRequestContext"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyProductRequestDtoResponseFull"}}}}}}, "delete": {"tags": ["product-request-controller-v-1"], "operationId": "deleteProductRequestById", "parameters": [{"name": "productRequestId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyBoolean"}}}}}}}, "/api/v1/productRequest/statistic": {"get": {"tags": ["product-request-controller-v-1"], "operationId": "getStatistic_1", "parameters": [{"name": "statisticContext", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ProductRequestDtoRequestStatisticContext"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyProductRequestDtoResponseStatistic"}}}}}}}, "/api/v1/productRequest/productStateToCount": {"get": {"tags": ["product-request-controller-v-1"], "operationId": "productStateToCount", "parameters": [{"name": "userId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "stateList", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["DRAFT", "MODERATION", "PUBLISHED", "HIDDEN", "REJECTED", "DELETED"]}}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyMapProductRequestStateInteger"}}}}}}}, "/api/v1/productRequest/getByUserId": {"get": {"tags": ["product-request-controller-v-1"], "operationId": "getAllByUserIdAndStateListPage", "parameters": [{"name": "userId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "stateList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string", "enum": ["DRAFT", "MODERATION", "PUBLISHED", "HIDDEN", "REJECTED", "DELETED"]}}}, {"name": "enrichContext", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ProductRequestDtoRequestContext"}}, {"name": "pageableRequestParams", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PageableRequestParams"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyPageDTOProductRequestDtoResponseFull"}}}}}}}, "/api/v1/productRequest/getByProductId": {"get": {"tags": ["product-request-controller-v-1"], "operationId": "getAllByProductIdAndStateListPage", "parameters": [{"name": "productId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "stateList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string", "enum": ["DRAFT", "MODERATION", "PUBLISHED", "HIDDEN", "REJECTED", "DELETED"]}}}, {"name": "enrichContext", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ProductRequestDtoRequestContext"}}, {"name": "pageableRequestParams", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PageableRequestParams"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyPageDTOProductRequestDtoResponseFull"}}}}}}}, "/api/v1/productRequest/getAbandonedByDaysPassed": {"get": {"tags": ["product-request-controller-v-1"], "operationId": "getAbandonedProductRequestPage", "parameters": [{"name": "daysPassed", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "notOlderThanDays", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "pageableRequestParams", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PageableRequestParams"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyPageDTOProductRequestDtoResponseFull"}}}}}}}, "/api/v1/productRequest/countByProductId": {"get": {"tags": ["product-request-controller-v-1"], "operationId": "countByProductIdAndStateListPage", "parameters": [{"name": "productId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "stateList", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string", "enum": ["DRAFT", "MODERATION", "PUBLISHED", "HIDDEN", "REJECTED", "DELETED"]}}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyLong"}}}}}}}, "/api/v1/admin/productRequest/getByProductRequestId": {"get": {"tags": ["product-response-admin-controller-v-1"], "operationId": "getByProductRequestId_1", "parameters": [{"name": "params", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ProductResponseRequestParams"}}, {"name": "pageableRequestParams", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PageableRequestParams"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyPageDTOProductResponseDtoResponseFull"}}}}}}}, "/api/v1/productResponse/{productResponseId}": {"delete": {"tags": ["product-response-controller-v-1"], "operationId": "delete", "parameters": [{"name": "productResponseId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyBoolean"}}}}}}}, "/api/v1/productResponse/deleteByProductId/{productId}": {"delete": {"tags": ["product-response-controller-v-1"], "operationId": "deleteByProductId", "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBodyBoolean"}}}}}}}}, "components": {"schemas": {"ProductRequestDtoResponseLite": {"required": ["id", "userId"], "type": "object", "properties": {"id": {"type": "integer", "description": "Entity id", "format": "int64", "example": 123}, "userId": {"type": "integer", "description": "Product request creator user id ", "format": "int64", "example": 123}}, "description": "Product request lite"}, "ProductResponseDtoResponseFull": {"required": ["productId", "productRequest", "userId"], "type": "object", "properties": {"id": {"type": "integer", "description": "Entity id", "format": "int64", "example": 123}, "productRequest": {"$ref": "#/components/schemas/ProductRequestDtoResponseLite"}, "productId": {"type": "integer", "description": "product id", "format": "int64"}, "userId": {"type": "integer", "description": "Product request creator user id ", "format": "int64", "example": 123}, "comment": {"type": "string", "description": "product response comment"}}}, "ResponseBodyListProductResponseDtoResponseFull": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/ProductResponseDtoResponseFull"}}, "errorMessage": {"type": "string"}, "humanErrorMessage": {"type": "string"}}}, "ProductResponseUsersRequestParams": {"type": "object", "properties": {"userIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "productRequestId": {"type": "integer", "format": "int64"}}}, "ResponseBodyListLong": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "errorMessage": {"type": "string"}, "humanErrorMessage": {"type": "string"}}}, "ProductResponseDtoRequestUpdate": {"type": "object", "properties": {"id": {"type": "integer", "description": "Entity id", "format": "int64", "example": 123}, "productRequestId": {"type": "integer", "description": "product request id", "format": "int64"}, "productId": {"type": "integer", "description": "product id", "format": "int64"}, "userId": {"type": "integer", "description": "Product request creator user id ", "format": "int64", "example": 123}, "comment": {"type": "string", "description": "product response comment"}, "state": {"type": "string", "description": "product response state", "enum": ["AVAILABLE_ONLY_TO_AUTHOR", "AVAILABLE_TO_ALL", "HIDDEN", "DELETED"]}}}, "ResponseBodyProductResponseDtoResponseFull": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/ProductResponseDtoResponseFull"}, "errorMessage": {"type": "string"}, "humanErrorMessage": {"type": "string"}}}, "ProductResponseDtoRequestUpdateState": {"required": ["newState", "productId"], "type": "object", "properties": {"productId": {"type": "integer", "format": "int64"}, "newState": {"type": "string", "enum": ["AVAILABLE_ONLY_TO_AUTHOR", "AVAILABLE_TO_ALL", "HIDDEN", "DELETED"]}}}, "ResponseBodyBoolean": {"type": "object", "properties": {"data": {"type": "boolean"}, "errorMessage": {"type": "string"}, "humanErrorMessage": {"type": "string"}}}, "ImageDTO": {"required": ["extension", "fileName", "isPrimary"], "type": "object", "properties": {"fileName": {"type": "string", "description": "image UUID without extension", "example": "7918f631-20d5-41fb-b37d-6928f50d5289"}, "extension": {"type": "string", "description": "image extension without dot", "example": "png"}, "isPrimary": {"type": "boolean", "description": "primary image == first image"}}, "description": "List of images"}, "ProductRequestDtoResponseFull": {"required": ["categoryId", "createTime", "id", "state", "updateTim", "userId"], "type": "object", "properties": {"id": {"type": "integer", "description": "Entity id", "format": "int64", "example": 123}, "userId": {"type": "integer", "description": "Product request creator user id ", "format": "int64", "example": 123}, "categoryId": {"type": "integer", "description": "category id", "format": "int64"}, "title": {"type": "string", "description": "Product request title", "example": "GUCCI"}, "state": {"type": "string", "description": "Product request state", "example": "DRAFT", "enum": ["DRAFT", "MODERATION", "PUBLISHED", "HIDDEN", "REJECTED", "DELETED"]}, "stateTime": {"type": "string", "description": "Product request state last update time", "format": "date-time"}, "brandIds": {"type": "array", "description": "List of brand ids", "items": {"type": "integer", "description": "List of brand ids", "format": "int64"}}, "productModelIds": {"type": "array", "description": "List of productModel ids", "items": {"type": "integer", "description": "List of productModel ids", "format": "int64"}}, "attributeValueIds": {"type": "array", "description": "List of attributeValue ids", "items": {"type": "integer", "description": "List of attributeValue ids", "format": "int64"}}, "conditionIds": {"type": "array", "description": "List of condition ids", "items": {"type": "integer", "description": "List of condition ids", "format": "int64"}}, "sizeType": {"type": "string", "description": "size type"}, "sizeIds": {"type": "array", "description": "List of size ids", "items": {"type": "integer", "description": "List of size ids", "format": "int64"}}, "images": {"type": "array", "description": "List of images", "items": {"$ref": "#/components/schemas/ImageDTO"}}, "fromPrice": {"type": "number", "description": "Maximum of product price"}, "toPrice": {"type": "number", "description": "Maximum of product price"}, "fromPriceInCurrency": {"type": "number", "description": "Minimum of product price in currency"}, "toPriceInCurrency": {"type": "number", "description": "Maximum of product price in currency"}, "priceCurrencyId": {"type": "integer", "description": "Currency code identifier", "format": "int64"}, "description": {"type": "string", "description": "Description"}, "createTime": {"type": "string", "description": "Publication time of entity", "format": "date-time"}, "updateTim": {"type": "string", "description": "Last update time", "format": "date-time"}, "productResponseCount": {"type": "integer", "description": "Number of related product responses", "format": "int32"}, "isResponded": {"type": "boolean", "description": "User has responded to"}}}, "ResponseBodyListProductRequestDtoResponseFull": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/ProductRequestDtoResponseFull"}}, "errorMessage": {"type": "string"}, "humanErrorMessage": {"type": "string"}}}, "ProductRequestDtoRequestUpdate": {"type": "object", "properties": {"id": {"type": "integer", "description": "Entity id", "format": "int64", "example": 123}, "userId": {"type": "integer", "description": "Product request creator user id ", "format": "int64", "example": 123}, "categoryId": {"type": "integer", "description": "category id", "format": "int64"}, "title": {"type": "string", "description": "Product request title", "example": "GUCCI"}, "state": {"type": "string", "description": "Product request state", "example": "DRAFT", "enum": ["DRAFT", "MODERATION", "PUBLISHED", "HIDDEN", "REJECTED", "DELETED"]}, "brandIds": {"type": "array", "description": "List of brand ids", "items": {"type": "integer", "description": "List of brand ids", "format": "int64"}}, "productModelIds": {"type": "array", "description": "List of productModel ids", "items": {"type": "integer", "description": "List of productModel ids", "format": "int64"}}, "attributeValueIds": {"type": "array", "description": "List of attributeValue ids", "items": {"type": "integer", "description": "List of attributeValue ids", "format": "int64"}}, "conditionIds": {"type": "array", "description": "List of condition ids", "items": {"type": "integer", "description": "List of condition ids", "format": "int64"}}, "sizeType": {"type": "string", "description": "size type"}, "sizeIds": {"type": "array", "description": "List of size ids", "items": {"type": "integer", "description": "List of size ids", "format": "int64"}}, "images": {"type": "array", "description": "List of images", "items": {"$ref": "#/components/schemas/ImageDTO"}}, "fromPrice": {"type": "number", "description": "Minimum of product price"}, "toPrice": {"type": "number", "description": "Maximum of product price"}, "description": {"type": "string", "description": "Description"}, "fromPriceInCurrency": {"type": "number", "description": "Minimum of product price in currency"}, "toPriceInCurrency": {"type": "number", "description": "Maximum of product price in currency"}, "priceCurrencyId": {"type": "integer", "description": "Currency code identifier", "format": "int64"}}}, "ResponseBodyProductRequestDtoResponseFull": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/ProductRequestDtoResponseFull"}, "errorMessage": {"type": "string"}, "humanErrorMessage": {"type": "string"}}}, "StatisticUsersWithProductRequestsContext": {"required": ["userIds"], "type": "object", "properties": {"userIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "ResponseBodyStatisticUsersWithProductRequests": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/StatisticUsersWithProductRequests"}, "errorMessage": {"type": "string"}, "humanErrorMessage": {"type": "string"}}}, "StatisticUsersWithProductRequests": {"required": ["userIds"], "type": "object", "properties": {"userIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "ProductRequestDtoRequestFilter": {"type": "object", "properties": {"interestingProductIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "interestingProductRequestIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "interestingUserIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "interestingProductResponseUserIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "interestingCategoryIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "interestingStates": {"type": "array", "items": {"type": "string", "enum": ["DRAFT", "MODERATION", "PUBLISHED", "HIDDEN", "REJECTED", "DELETED"]}}, "interestingAttributeValueIds": {"type": "array", "items": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "interestingSizeIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "interestingBrandIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "interestingProductModelIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "interestingConditionIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "fromPrice": {"type": "number"}, "toPrice": {"type": "number"}, "productResponseAuthorId": {"type": "integer", "format": "int64"}}}, "ProductRequestDtoResponseAvailableFilter": {"type": "object", "properties": {"interestingProductRequestIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "interestingUserIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "interestingCategoryIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "interestingStates": {"type": "array", "items": {"type": "string", "enum": ["DRAFT", "MODERATION", "PUBLISHED", "HIDDEN", "REJECTED", "DELETED"]}}, "fromPrice": {"type": "number"}, "toPrice": {"type": "number"}, "interestingBrandIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "interestingProductModelIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "interestingAttributeValueIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "interestingConditionIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "interestingSizeIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "ResponseBodyProductRequestDtoResponseAvailableFilter": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/ProductRequestDtoResponseAvailableFilter"}, "errorMessage": {"type": "string"}, "humanErrorMessage": {"type": "string"}}}, "ProductRequestDtoRequestContext": {"type": "object", "properties": {"enrichUserId": {"type": "integer", "description": "User id of whom who did the request ", "format": "int64", "example": 123}}}, "PageableRequestParams": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int64"}, "sortCode": {"type": "string"}}}, "PageDTOProductRequestDtoResponseFull": {"required": ["items", "totalAmount", "totalPages"], "type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/ProductRequestDtoResponseFull"}}, "totalPages": {"type": "integer", "format": "int32"}, "totalAmount": {"type": "integer", "format": "int64"}}}, "ResponseBodyPageDTOProductRequestDtoResponseFull": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/PageDTOProductRequestDtoResponseFull"}, "errorMessage": {"type": "string"}, "humanErrorMessage": {"type": "string"}}}, "ResponseBodyLong": {"type": "object", "properties": {"data": {"type": "integer", "format": "int64"}, "errorMessage": {"type": "string"}, "humanErrorMessage": {"type": "string"}}}, "ProductResponseRequestParams": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int64"}, "userIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "notUserId": {"type": "integer", "format": "int64"}, "productRequestId": {"type": "integer", "format": "int64"}, "authorId": {"type": "integer", "format": "int64"}}}, "PageDTOProductResponseDtoResponseFull": {"required": ["items", "totalAmount", "totalPages"], "type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/ProductResponseDtoResponseFull"}}, "totalPages": {"type": "integer", "format": "int32"}, "totalAmount": {"type": "integer", "format": "int64"}}}, "ResponseBodyPageDTOProductResponseDtoResponseFull": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/PageDTOProductResponseDtoResponseFull"}, "errorMessage": {"type": "string"}, "humanErrorMessage": {"type": "string"}}}, "ProductRequestDtoRequestStatisticContext": {"type": "object", "properties": {"withUniqueUsersCount": {"type": "boolean"}, "stateListForUniqueUsersCount": {"type": "array", "items": {"type": "string", "enum": ["DRAFT", "MODERATION", "PUBLISHED", "HIDDEN", "REJECTED", "DELETED"]}}}}, "ProductRequestDtoResponseStatistic": {"type": "object", "properties": {"uniqueUsersByState": {"type": "integer", "format": "int64"}}}, "ResponseBodyProductRequestDtoResponseStatistic": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/ProductRequestDtoResponseStatistic"}, "errorMessage": {"type": "string"}, "humanErrorMessage": {"type": "string"}}}, "ResponseBodyMapProductRequestStateInteger": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}}, "errorMessage": {"type": "string"}, "humanErrorMessage": {"type": "string"}}}}}}