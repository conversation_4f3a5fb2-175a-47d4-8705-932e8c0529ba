openapi: 3.0.1
info:
  title: OpenAPI definition
  version: v0
servers:
  - description: Generated server url
    url: http://localhost:8088
paths:
  /api/v1/search/history:
    get:
      operationId: getSearchHistory
      parameters:
        - in: header
          name: x-user-id
          required: false
          schema:
            type: integer
            format: int64
        - in: header
          name: x-guest-token
          required: true
          schema:
            type: string
        - in: query
          name: limit
          required: false
          schema:
            type: integer
            format: int32
        - in: query
          name: type
          required: false
          schema:
            type: string
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResponseBodyApiDtoListSearchHistoryApiDto'
          description: OK
      tags:
        - search-history-controller
    post:
      operationId: createSearchHistoryItem
      parameters:
        - in: header
          name: x-user-id
          required: false
          schema:
            type: integer
            format: int64
        - in: header
          name: x-guest-token
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSearchHistoryRequestApiDto'
        required: true
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResponseBodyApiDtoUnit'
          description: OK
      tags:
        - search-history-controller
  /api/v1/search/history/delete:
    post:
      operationId: deleteSearchHistoryItem
      parameters:
        - in: header
          name: x-user-id
          required: false
          schema:
            type: integer
            format: int64
        - in: header
          name: x-guest-token
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteSearchHistoryRequestApiDto'
        required: true
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResponseBodyApiDtoUnit'
          description: OK
      tags:
        - search-history-controller
  /api/v1/search/history/delete/all:
    post:
      operationId: deleteSearchHistory
      parameters:
        - in: header
          name: x-user-id
          required: false
          schema:
            type: integer
            format: int64
        - in: header
          name: x-guest-token
          required: true
          schema:
            type: string
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResponseBodyApiDtoUnit'
          description: OK
      tags:
        - search-history-controller
  /api/v1/search/history/fallback/delete-outdated:
    post:
      operationId: deleteOutdatedSearchHistoryItems
      parameters:
        - in: query
          name: createdAtBefore
          required: true
          schema:
            type: string
            format: date-time
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResponseBodyApiDtoUnit'
          description: OK
      tags:
        - search-history-fallback-controller
components:
  schemas:
    BaseErrorDetails:
      type: object
    CreateSearchHistoryRequestApiDto:
      type: object
      properties:
        data:
          type: object
          additionalProperties:
            type: object
        query:
          type: string
        type:
          type: string
      required:
        - query
        - type
    DeleteSearchHistoryRequestApiDto:
      type: object
      properties:
        query:
          type: string
        type:
          type: string
      required:
        - type
    Error:
      type: object
      properties:
        code:
          type: string
        details:
          $ref: '#/components/schemas/BaseErrorDetails'
        message:
          type: string
        stackTrace:
          type: string
      required:
        - code
        - details
    ResponseBodyApiDtoListSearchHistoryApiDto:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/SearchHistoryApiDto'
        error:
          $ref: '#/components/schemas/Error'
    ResponseBodyApiDtoUnit:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/Unit'
        error:
          $ref: '#/components/schemas/Error'
    SearchHistoryApiDto:
      type: object
      properties:
        data:
          type: object
          additionalProperties:
            type: object
        query:
          type: string
        type:
          type: string
      required:
        - query
        - type
    Unit:
      type: object