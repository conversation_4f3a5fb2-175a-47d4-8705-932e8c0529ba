[{"relation": ["delegate_permission/common.handle_all_urls"], "target": {"namespace": "android_app", "package_name": "ru.oskelly.app", "sha256_cert_fingerprints": ["98:A0:C9:D7:BC:A2:E5:4F:10:28:D2:3F:B1:34:40:91:79:C5:8C:82:90:61:9F:3F:2C:BC:29:AD:33:DF:21:1E", "B5:07:1C:EB:FC:00:72:18:E1:E1:4A:EC:26:E8:15:30:97:AF:26:48:28:5A:B7:10:30:AD:C3:F3:66:54:9E:8D", "6B:29:18:12:57:CB:52:03:29:55:36:91:B7:A4:8C:9B:11:23:89:99:50:10:51:27:B3:DD:67:01:2C:49:A8:3E"]}}, {"relation": ["delegate_permission/common.handle_all_urls"], "target": {"namespace": "android_app", "package_name": "test.oskelly.app", "sha256_cert_fingerprints": ["98:A0:C9:D7:BC:A2:E5:4F:10:28:D2:3F:B1:34:40:91:79:C5:8C:82:90:61:9F:3F:2C:BC:29:AD:33:DF:21:1E", "B5:07:1C:EB:FC:00:72:18:E1:E1:4A:EC:26:E8:15:30:97:AF:26:48:28:5A:B7:10:30:AD:C3:F3:66:54:9E:8D", "6B:29:18:12:57:CB:52:03:29:55:36:91:B7:A4:8C:9B:11:23:89:99:50:10:51:27:B3:DD:67:01:2C:49:A8:3E"]}}]