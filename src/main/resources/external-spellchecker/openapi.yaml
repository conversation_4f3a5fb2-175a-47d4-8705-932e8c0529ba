openapi: 3.0.1
info:
  title: OpenAPI definition
  version: v0
servers:
- url: http://localhost:8089
  description: Generated server url
paths:
  /brand/synonyms/update:
    put:
      tags:
      - brand-synonym-controller
      operationId: updateIndexFromS3File
      responses:
        "200":
          description: OK
  /spellcheck:
    get:
      tags:
      - spellcheck-controller
      operationId: spellcheck
      parameters:
      - name: query
        in: query
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: string
