{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "http://localhost:9092", "description": "Generated server url"}], "paths": {"/seo-api/v1/filter": {"post": {"tags": ["seo-controller"], "summary": "Filter", "operationId": "filter", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SeoDataFilterRequest"}}}, "required": true}, "responses": {"200": {"description": "Success response with errorCode 0 or logical error with errorCode != 0", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponsePageSeoDataDTO"}}}}}}}, "/seo-api/v1/filled": {"post": {"tags": ["seo-controller"], "summary": "Filter", "operationId": "filled", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SeoDataRequest"}}}, "required": true}, "responses": {"200": {"description": "Success response with errorCode 0 or logical error with errorCode != 0", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseSeoDataDTO"}}}}}}}, "/seo-api/v1/crud/uploadCsv": {"post": {"tags": ["seo-crud-controller"], "summary": "Upload file", "operationId": "uploadFile", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "Success response with errorCode 0 or logical error with errorCode != 0", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseInteger"}}}}}}}, "/seo-api/v1/crud/remove": {"post": {"tags": ["seo-crud-controller"], "summary": "Remove seo data", "operationId": "remove", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SeoDataEditRequest"}}}, "required": true}, "responses": {"200": {"description": "Success response with errorCode 0 or logical error with errorCode != 0", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseVoid"}}}}}}}, "/seo-api/v1/crud/edit": {"post": {"tags": ["seo-crud-controller"], "summary": "Edit seo data", "operationId": "edit", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SeoDataEditRequest"}}}, "required": true}, "responses": {"200": {"description": "Success response with errorCode 0 or logical error with errorCode != 0", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseSeoDataDTO"}}}}}}}, "/seo-api/v1/crud/variables": {"get": {"tags": ["seo-crud-controller"], "summary": "Get possible variables", "operationId": "getVariables", "responses": {"200": {"description": "Success response with errorCode 0 or logical error with errorCode != 0", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseListContentVariables"}}}}}}}}, "components": {"schemas": {"SeoDataFilterRequest": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "rowsPerPage": {"type": "integer", "format": "int32"}, "sortBy": {"type": "string"}, "direction": {"type": "string", "enum": ["ASC", "DESC"]}, "categoryIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "brandIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "PageSeoDataDTO": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/SeoDataDTO"}}, "totalPages": {"type": "integer", "format": "int32"}, "totalAmount": {"type": "integer", "format": "int64"}}}, "ResponsePageSeoDataDTO": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"$ref": "#/components/schemas/PageSeoDataDTO"}, "errorCode": {"type": "string", "enum": ["OK", "INTERNAL_ERROR", "SEO_DATA_NOT_FOUND", "CSV_PARSE_ERROR"]}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}}}, "SeoDataDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "categoryId": {"type": "integer", "format": "int64"}, "brandId": {"type": "integer", "format": "int64"}, "modelId": {"type": "integer", "format": "int64"}, "textContent": {"type": "string"}, "headerContent": {"type": "string"}, "linkGroups": {"type": "array", "items": {"$ref": "#/components/schemas/SeoLinkGroupDTO"}}}}, "SeoLinkDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "linkUrl": {"type": "string"}, "linkContent": {"type": "string"}}}, "SeoLinkGroupDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "links": {"type": "array", "items": {"$ref": "#/components/schemas/SeoLinkDTO"}}}}, "SeoDataRequest": {"type": "object", "properties": {"categoryId": {"type": "integer", "format": "int64"}, "brandId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "modelId": {"type": "integer", "format": "int64"}, "variablesMap": {"type": "object", "additionalProperties": {"type": "string"}}}}, "ResponseSeoDataDTO": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"$ref": "#/components/schemas/SeoDataDTO"}, "errorCode": {"type": "string", "enum": ["OK", "INTERNAL_ERROR", "SEO_DATA_NOT_FOUND", "CSV_PARSE_ERROR"]}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}}}, "ResponseInteger": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "integer", "format": "int32"}, "errorCode": {"type": "string", "enum": ["OK", "INTERNAL_ERROR", "SEO_DATA_NOT_FOUND", "CSV_PARSE_ERROR"]}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}}}, "SeoDataEditRequest": {"type": "object", "properties": {"categoryId": {"type": "integer", "format": "int64"}, "brandId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "textContent": {"type": "string"}, "headerContent": {"type": "string"}, "linkGroups": {"type": "array", "items": {"$ref": "#/components/schemas/SeoLinkGroupDTO"}}}}, "ResponseVoid": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}, "errorCode": {"type": "string", "enum": ["OK", "INTERNAL_ERROR", "SEO_DATA_NOT_FOUND", "CSV_PARSE_ERROR"]}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}}}, "ResponseListContentVariables": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "array", "items": {"type": "string", "enum": ["CATEGORY", "BRAND", "MODEL", "GENDER", "COLOR", "MATERIAL"]}}, "errorCode": {"type": "string", "enum": ["OK", "INTERNAL_ERROR", "SEO_DATA_NOT_FOUND", "CSV_PARSE_ERROR"]}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}}}}}}