spring:
  datasource:
    jdbc-url: ${DATABASELINK:*******************************************}

app:
  language: en
  country: US
  default-country:
    iso-code-alpha2: AE
    default-country-id: 2

  google-cloud:
    api-key: "-"

  experiments:
    flagr:
      host: '-'

  integration:
    orderprocessing:
      isEnabled: true

  bargain:
    min-bargain-price: 300

  publication:
    min-price: 300

  cart:
    amount-limit: 300

internationalVersion: true

payments:
  noon-payments:
    enabled: true
    api-url: https://api-test.noonpayments.com/payment/v1
    return-url: http://localhost:8080 # https://oskelly.co
    currencies:
      - "AED"
      - "USD"
      - "EUR"

bank-account:
  payment-version: noonpayments-1.0
  jobs:
    enabled: true
    prepare-seller-payouts:
      cron: "0 0/1 * * * ?"
    check-seller-payouts:
      cron: "0 0/1 * * * ?"
    check-bank-operations:
      cron: "0 0/1 * * * ?"
    refund-expiring-orders:
      cron: "-"

logistic:
  with-cse: false
  with-dpd: false
  with-major: false
  with-dalli: false
  with-aramex: false
  with-null: true
  with-oskelly: true
  defaultCompany: "Null"

  major-office:
    city: Dubai
    cityCode: 129
    address: DUBAI COMMERCITY LLC of PO Box 491, BCB2-215
    zipCode: 00000

    name: OSKELLY TRADING L.L.C
    company: OSKELLY TRADING L.L.C
    contactNameSender: OSKELLY TRADING L.L.C
    contactPhoneSender: ***********
    contactNameReceiver: OSKELLY TRADING L.L.C
    contactPhoneReceiver: ***********

server:
  port: 8081

user-sync-service:
  enabled: true
  zone: INT
  jobs:
    retry:
      cron: "0 0/5 * * * *"
      expiration-hours: 24
      batchSizeLimit: 10
  kafka:
    topic: int-user-sync
    user-ban-topic: int-user-ban-sync
    consumer:
      topic: ru-user-sync
      user-ban-topic: ru-user-ban-sync
      group-id: coreINT
      bootstrap-servers: localhost:9093
  master-service:
    url: http://0.0.0.0:8080
    user-email: <EMAIL>
    user-password: 111111
    attemptsCount: 1
    attemptsDelayPeriodSeconds: 5