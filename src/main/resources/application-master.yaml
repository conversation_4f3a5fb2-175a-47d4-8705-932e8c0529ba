app:
  user-ban:
    auto-cancel-task:
      enabled: true
  product-checker:
    enabled: true
  bargain:
    close-task:
      enabled: true
    open-task:
      enabled: true
    12h-notifications-task:
      enabled: true
  queue:
    #Выключатели для приема событий из AMQP
    processor:
      new:
        image:
          #Чтение очереди с новыми изображениями для ресайза
          enabled: true
        activity:
          #Чтение очереди с новыми активностями
          enabled: true
        #Переходим на user-auth
        #user-access:
          #Чтение очереди об обращении пользователя к системе
          #enabled: true
        agent-report-payment:
          #Чтение очереди о начале выплаты продавцу
          enabled: true
        agent-report-repayment:
          #Чтение очереди о начале повторной выплаты продавцу
          enabled: true
        user-auth:
          enabled: true
      order-state-update:
        enabled: true
  publication:
    images-ftp-cleanup-task:
      enabled: true

notification-cleaner:
  cron: 0 0 1 * * *
order-events:
  cron: "0 * * * * *"
delete-unused-addresses:
  cron: "0 0 3 * * *"
recalculate-estimations:
  cron: "0 0 19 * * *"

logistics:  #todo: set value
  job:
    auto-pickup-runner:
      cron: "0 * * * * *"
