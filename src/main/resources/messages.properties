bargain.expired.error=Ð¢Ð¾ÑÐ³ Ð½Ðµ ÑÐ²Ð»ÑÐµÑÑÑ Ð¿ÑÐ¾ÑÑÐ¾ÑÐµÐ½Ð½ÑÐ¼ Ð¸ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð²Ð¾Ð·Ð¾Ð±Ð½Ð¾Ð²Ð»ÐµÐ½
service.DutyService.DutyDescription=Import Customs and Duties
service.PrimaryServiceImpl.getProductRequestSmallBanner.Title=ÐÑÑ ÑÐ¾Ð²Ð°Ñ
service.PrimaryServiceImpl.getProductRequestSmallBanner.Description=ÐÐµ Ð½Ð°ÑÐ»Ð¸ Ð»Ð¾Ñ Ð¼ÐµÑÑÑ? Ð¡Ð¾Ð·Ð´Ð°Ð¹ÑÐµ Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½Ð¸Ðµ Ð¾ Ð¿Ð¾Ð¸ÑÐºÐµ, ÐºÐ¾ÑÐ¾ÑÐ¾Ðµ ÑÐ²Ð¸Ð´ÑÑ Ð²ÑÐµ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÑ Oskelly
service.PrimaryServiceImpl.getProductRequestSmallBanner.ButtonTitle=Ð¡Ð¾Ð·Ð´Ð°ÑÑ Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½Ð¸Ðµ
service.PrimaryServiceImpl.getProductRequestSmallBannerOnPrimary.Title=ÐÐµ Ð½Ð°ÑÐ»Ð¸, ÑÑÐ¾ ÑÐ¾ÑÐµÐ»Ð¸?
service.PrimaryServiceImpl.getProductRequestSmallBannerOnPrimary.Description=Ð¡Ð¾Ð·Ð´Ð°Ð¹ÑÐµ Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½Ð¸Ðµ Ð¾ Ð¿Ð¾Ð¸ÑÐºÐµ, ÐºÐ¾ÑÐ¾ÑÐ¾Ðµ ÑÐ²Ð¸Ð´ÑÑ Ð²ÑÐµ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÑ Oskelly
service.PrimaryServiceImpl.getProductRequestSmallBannerOnPrimary.ButtonTitle=Ð¡Ð¾Ð·Ð´Ð°ÑÑ Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½Ð¸Ðµ
service.PrimaryServiceImpl.ProductRequestBanner.Title=ÐÑÑ ÑÐ¾Ð²Ð°Ñ
service.PrimaryServiceImpl.ProductRequestBanner.Description=Ð¡Ð¾Ð·Ð´Ð°Ð¹ÑÐµ Ð·Ð°Ð¿ÑÐ¾Ñ Ð½Ð° Ð¿Ð¾Ð¸ÑÐº ÑÐ¾Ð²Ð°ÑÐ° Ð¼ÐµÑÑÑ Ð¸Ð»Ð¸ ÑÐ¼Ð¾ÑÑÐ¸ÑÐµ, ÑÑÐ¾ Ð¸ÑÑÑ Ð´ÑÑÐ³Ð¸Ðµ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ð¸, Ð¸ Ð¿ÑÐµÐ´Ð»Ð°Ð³Ð°Ð¹ÑÐµ ÑÐ²Ð¾Ð¸ Ð»Ð¾ÑÑ
service.PrimaryServiceImpl.ProductRequestBanner.ButtonTitle=ÐÐ¾ÑÐ¼Ð¾ÑÑÐµÑÑ Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½Ð¸Ñ
presentation.api.v2.concierge.ConciergeController.NoName=ÐÐµ Ð²Ð²ÐµÐ´ÐµÐ½Ð¾ Ð¸Ð¼Ñ
presentation.api.v2.concierge.ConciergeController.NoPhone=ÐÐµ Ð²Ð²ÐµÐ´ÐµÐ½ Ð½Ð¾Ð¼ÐµÑ ÑÐµÐ»ÐµÑÐ¾Ð½Ð°
infrastructure.security.SecurityServiceImpl.Exception.UserNotFound=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½. Id: 
infrastructure.security.SecurityServiceImpl.Exception.BadToken=ÐÐµÐ²ÐµÑÐ½ÑÐ¹ ÑÐ¾ÐºÐµÐ½ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ñ Ð°Ð²ÑÐ¾ÑÐ¸Ð·Ð°ÑÐ¸Ð¸
infrastructure.security.provider.register.OAuthRegistrationProvider.NoEmailException=ÐÑÐµÐ´Ð¾ÑÑÐ°Ð²ÑÑÐµ email Ð´Ð»Ñ ÑÐµÐ³Ð¸ÑÑÑÐ°ÑÐ¸Ð¸
infrastructure.security.provider.register.OAuthRegistrationProvider.NoNicknameException=ÐÑÐµÐ´Ð¾ÑÑÐ°Ð²ÑÑÐµ Ð½Ð¸ÐºÐ½ÐµÐ¹Ð¼ Ð´Ð»Ñ ÑÐµÐ³Ð¸ÑÑÑÐ°ÑÐ¸Ð¸
bargain.previous.state.error=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¾Ð¿ÑÐµÐ´ÐµÐ»Ð¸ÑÑ Ð¿ÑÐµÐ¶Ð½Ð¸Ð¹ ÑÑÐ°ÑÑÑ ÐºÐ¾Ð½ÑÑÑÐ¾ÑÐ³Ð°
bargain.is.user.product.error=ÐÐµÐ»ÑÐ·Ñ ÑÐ¾ÑÐ³Ð¾Ð²Ð°ÑÑÑÑ Ð¿Ð¾ ÑÐ²Ð¾ÐµÐ¼Ñ ÑÐ¾Ð²Ð°ÑÑ
bargain.product.size.not.available.error=Ð Ð°Ð·Ð¼ÐµÑ Ð½ÐµÐ´Ð¾ÑÑÑÐ¿ÐµÐ½ Ð´Ð»Ñ Ð¿Ð¾ÐºÑÐ¿ÐºÐ¸
bargain.exhausted.action.error=ÐÑ Ð¸ÑÑÐµÑÐ¿Ð°Ð»Ð¸ Ð²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ÑÑÐ¸ ÑÐ¾Ð·Ð´Ð°ÑÑ ÑÐ¾ÑÐ³ Ð¿Ð¾ Ð´Ð°Ð½Ð½Ð¾Ð¼Ñ ÑÐ¾Ð²Ð°ÑÑ/ÑÐ°Ð·Ð¼ÐµÑÑ
bargain.product.not.available.error=Ð¢Ð¾Ð²Ð°Ñ Ð½ÐµÐ´Ð¾ÑÑÑÐ¿ÐµÐ½ Ð´Ð»Ñ ÑÐ¾ÑÐ³Ð°: {0}
bargain.product.in.boutique.error=Ð¢Ð¾Ð²Ð°Ñ Ð½Ð°ÑÐ¾Ð´Ð¸ÑÑÑ Ð² Ð±ÑÑÐ¸ÐºÐµ, Ð¿Ð¾ÑÑÐ¾Ð¼Ñ Ð½ÐµÐ´Ð¾ÑÑÑÐ¿ÐµÐ½ Ð´Ð»Ñ ÑÐ¾ÑÐ³Ð°
bargain.access.error=Ð£ Ð²Ð°Ñ Ð½ÐµÑ Ð¿ÑÐ°Ð² Ð½Ð° Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð¸Ðµ Ð´Ð°Ð½Ð½Ð¾Ð³Ð¾ Ð´ÐµÐ¹ÑÑÐ²Ð¸Ñ
bargain.add.product.basket.error=ÐÐ»Ñ Ð¿ÑÐ¾ÑÑÐ°Ð²Ð»ÐµÐ½Ð¸Ñ Ð¾ÑÐ¼ÐµÑÐºÐ¸ Ð¾ Ð¿Ð¾ÐºÑÐ¿ÐºÐµ Ð´Ð¾Ð±Ð°Ð²ÑÑÐµ ÑÐ¾Ð²Ð°Ñ Ð² ÐºÐ¾ÑÐ·Ð¸Ð½Ñ Ð¸ Ð¾ÑÐ¾ÑÐ¼Ð¸ÑÐµ Ð·Ð°ÐºÐ°Ð·
bargain.price.unacceptable.error=ÐÐ°ÑÐ°Ð¼ÐµÑÑ ÑÐµÐ½Ñ Ð½ÐµÐ´Ð¾Ð¿ÑÑÑÐ¸Ð¼ Ð´Ð»Ñ Ð´Ð°Ð½Ð½Ð¾Ð³Ð¾ Ð´ÐµÐ¹ÑÑÐ²Ð¸Ñ
bargain.price.mandatory.error=ÐÐ°ÑÐ°Ð¼ÐµÑÑ ÑÐµÐ½Ñ Ð¾Ð±ÑÐ·Ð°ÑÐµÐ»ÐµÐ½ Ð´Ð»Ñ Ð´Ð°Ð½Ð½Ð¾Ð³Ð¾ Ð´ÐµÐ¹ÑÑÐ²Ð¸Ñ
bargain.buyer.cannot.use.countertrade.error=ÐÐ¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð²Ð¾ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑÑÑ Ð´Ð°Ð½Ð½ÑÐ¼ ÐºÐ¾Ð½ÑÑÑÐ¾ÑÐ³Ð¾Ð¼
bargain.min.price.error=Ð¦ÐµÐ½Ð° Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð½Ð¸Ð¶Ðµ {0}% ÑÑÐ¾Ð¸Ð¼Ð¾ÑÑÐ¸ ÑÐ¾Ð²Ð°ÑÐ°
bargain.max.price.error=Ð¦ÐµÐ½Ð° Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð¿ÑÐµÐ²ÑÑÐ°ÑÑ ÑÑÐ¾Ð¸Ð¼Ð¾ÑÑÑ ÑÐ¾Ð²Ð°ÑÐ°
bargain.day.error=ÐÐ»Ñ ÑÐ¾Ð·Ð´Ð°Ð½Ð¸Ñ ÑÐ¾ÑÐ³Ð° Ð´Ð¾Ð»Ð¶Ð½Ð¾ Ð¿ÑÐ¾Ð¹ÑÐ¸ {0} Ð´Ð½ÐµÐ¹ Ñ Ð¼Ð¾Ð¼ÐµÐ½ÑÐ° Ð¿ÑÐ±Ð»Ð¸ÐºÐ°ÑÐ¸Ð¸ ÑÐ¾Ð²Ð°ÑÐ°
bargain.seller.disabled.error=ÐÐ° ÑÐ¾Ð²Ð°ÑÐ°Ñ ÑÑÐ¾Ð³Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ° ÑÐ¾ÑÐ³Ð¸ Ð½Ðµ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÑÑÑÑ
stream.can.create.error=ÐÑ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ ÑÐ¾Ð·Ð´Ð°ÑÑ ÑÑÐ¸Ñ. Ð§ÑÐ¾Ð±Ñ Ð¾ÑÐºÑÑÑÑ Ð²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ÑÑÑ Ð¿ÑÐ¾Ð²ÐµÐ´ÐµÐ½Ð¸Ñ ÑÑÐ¸ÑÐ¾Ð², Ð²Ð°Ð¼ Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°ÑÑ {0} ÑÐ¾Ð²Ð°ÑÐ¾Ð² Ð¸ ÑÐ¾Ð²ÐµÑÑÐ¸ÑÑ {1} ÑÑÐ¿ÐµÑÐ½ÑÑ Ð¿ÑÐ¾Ð´Ð°Ð¶ Ð½Ð° Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ðµ OSKELLY
stream.create.ban.error=ÐÑ Ð·Ð°Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ñ Ð¸ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ ÑÐ¾Ð·Ð´Ð°ÑÑ ÑÑÐ¸Ñ
stream.update.ban.error=ÐÑ Ð·Ð°Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ñ Ð¸ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¾Ð±Ð½Ð¾Ð²Ð¸ÑÑ ÑÑÐ¸Ñ
stream.update.access.error=Ð£ ÐÐ°Ñ Ð½ÐµÑ Ð¿ÑÐ°Ð² Ð½Ð° ÑÐµÐ´Ð°ÐºÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ ÑÑÐ¾Ð³Ð¾ ÑÑÐ¸ÑÐ°
stream.can.update.error=ÐÑ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¾Ð±Ð½Ð¾Ð²Ð¸ÑÑ ÑÑÐ¾Ñ ÑÑÐ¸Ñ
stream.delete.ban.error=ÐÑ Ð·Ð°Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ñ Ð¸ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ ÑÐ´Ð°Ð»Ð¸ÑÑ ÑÑÐ¸Ñ
stream.delete.access.error=Ð£ ÐÐ°Ñ Ð½ÐµÑ Ð¿ÑÐ°Ð² Ð½Ð° ÑÐ´Ð°Ð»ÐµÐ½Ð¸Ðµ ÑÑÐ¾Ð³Ð¾ ÑÑÐ¸ÑÐ°
stream.can.delete.error=ÐÑ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ ÑÐ´Ð°Ð»Ð¸ÑÑ ÑÑÐ¾Ñ ÑÑÐ¸Ñ
stream.live.ban.error=ÐÑ Ð·Ð°Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ñ Ð¸ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¿ÑÐ¾Ð´Ð¾Ð»Ð¶Ð¸ÑÑ ÑÑÐ¸Ñ
stream.get.deleting.logic.error=ÐÐ°Ð½Ð½ÑÐ¹ ÑÑÐ¸Ñ ÑÐ´Ð°Ð»ÐµÐ½
stream.start.ban.error=ÐÑ Ð·Ð°Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ñ Ð¸ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð½Ð°ÑÐ°ÑÑ ÑÑÐ¸Ñ
stream.start.access.error=Ð£ ÐÐ°Ñ Ð½ÐµÑ Ð¿ÑÐ°Ð² Ð½Ð° Ð·Ð°Ð¿ÑÑÐº ÑÑÐ¾Ð³Ð¾ ÑÑÐ¸ÑÐ°
stream.can.start.error=ÐÑ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð½Ð°ÑÐ°ÑÑ ÑÑÐ¾Ñ ÑÑÐ¸Ñ
stream.finish.ban.error=ÐÑ Ð·Ð°Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ñ Ð¸ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð·Ð°Ð²ÐµÑÑÐ¸ÑÑ ÑÑÐ¸Ñ
stream.finish.access.error=Ð£ ÐÐ°Ñ Ð½ÐµÑ Ð¿ÑÐ°Ð² Ð½Ð° Ð·Ð°Ð²ÐµÑÑÐµÐ½Ð¸Ðµ ÑÑÐ¾Ð³Ð¾ ÑÑÐ¸ÑÐ°
stream.subscribe.error=ÐÑ ÑÐ¶Ðµ Ð¿Ð¾Ð´Ð¿Ð¸ÑÐ°Ð½Ñ Ð½Ð° Ð¿ÑÑÐ¼Ð¾Ð¹ ÑÑÐ¸Ñ
stream.unsubscribe.error=ÐÑ ÑÐ¶Ðµ Ð¾ÑÐ¿Ð¸ÑÐ°Ð»Ð¸ÑÑ Ð¾Ñ Ð¿ÑÑÐ¼Ð¾Ð³Ð¾ ÑÑÐ¸ÑÐ°
stream.statuses.logic.error=ÐÐ¾Ð»Ðµ streamStatuses Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¿ÑÑÑÑÐ¼
stream.statuses.ban.logic.error=ÐÐ¾Ð»Ðµ streamStatuses Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑ ÑÐ¸Ð¿ BAN
stream.can.create.title=ÐÐ¾Ð·Ð´ÑÐ°Ð²Ð»ÑÐµÐ¼, ÑÐµÐ¿ÐµÑÑ Ð²Ñ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¿ÑÐ¾Ð²Ð¾Ð´Ð¸ÑÑ Ð¿ÑÑÐ¼ÑÐµ ÑÑÐ¸ÑÑ
stream.can.create.description=ÐÑ Ð½Ð°Ð±ÑÐ°Ð»Ð¸ {0} Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð½ÑÑ ÑÐ¾Ð²Ð°ÑÐ¾Ð² Ð¸ {1} ÑÑÐ¿ÐµÑÐ½ÑÑ Ð¿ÑÐ¾Ð´Ð°Ð¶, Ð¸ ÑÐµÐ¿ÐµÑÑ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¿ÑÐ¾Ð²Ð¾Ð´Ð¸ÑÑ Ð¿ÑÑÐ¼ÑÐµ ÑÑÐ¸ÑÑ Ð½Ð° Ð½Ð°ÑÐµÐ¹ Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ðµ
stream.can.not.create.title=ÐÑ Ð¿Ð¾ÐºÐ° Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¿ÑÐ¾Ð²Ð¾Ð´Ð¸ÑÑ ÑÑÐ¸ÑÑ
stream.can.not.create.description=Ð§ÑÐ¾Ð±Ñ Ð¾ÑÐºÑÑÑÑ Ð²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ÑÑÑ Ð¿ÑÐ¾Ð²ÐµÐ´ÐµÐ½Ð¸Ñ ÑÑÐ¸ÑÐ¾Ð², Ð²Ð°Ð¼ Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°ÑÑ {0} ÑÐ¾Ð²Ð°ÑÐ¾Ð² Ð¸ ÑÐ¾Ð²ÐµÑÑÐ¸ÑÑ {1} ÑÑÐ¿ÐµÑÐ½ÑÑ Ð¿ÑÐ¾Ð´Ð°Ð¶ Ð½Ð° Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ðµ OSKELLY
stream.cover.validation=ÐÑ Ð´Ð¾Ð»Ð¶Ð½Ñ Ð´Ð¾Ð±Ð°Ð²Ð¸ÑÑ Ð¾Ð±Ð»Ð¾Ð¶ÐºÑ Ð´Ð»Ñ ÑÑÐ¸ÑÐ°
stream.title.validation=ÐÑ Ð´Ð¾Ð»Ð¶Ð½Ñ Ð´Ð¾Ð±Ð°Ð²Ð¸ÑÑ Ð½Ð°Ð·Ð²Ð°Ð½Ð¸Ðµ Ð´Ð»Ñ ÑÑÐ¸ÑÐ°
stream.description.validation=ÐÑ Ð´Ð¾Ð»Ð¶Ð½Ñ Ð´Ð¾Ð±Ð°Ð²Ð¸ÑÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ðµ Ð´Ð»Ñ ÑÑÐ¸ÑÐ°
stream.min.product.count.validation=ÐÑ Ð´Ð¾Ð»Ð¶Ð½Ñ Ð´Ð¾Ð±Ð°Ð²Ð¸ÑÑ Ð¼Ð¸Ð½Ð¸Ð¼ÑÐ¼ 1 ÑÐ¾Ð²Ð°Ñ
stream.max.product.count.validation=ÐÑ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð´Ð¾Ð±Ð°Ð²Ð¸ÑÑ Ð±Ð¾Ð»ÐµÐµ {0} ÑÐ¾Ð²Ð°ÑÐ¾Ð² Ð² Ð¾Ð´Ð¸Ð½ ÑÑÐ¸Ñ
stream.start.time.validation=ÐÑ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ ÑÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ Ð´Ð°ÑÑ Ð½Ð°ÑÐ°Ð»Ð° ÑÑÐ¸ÑÐ° ÑÐ°Ð½ÐµÐµ, ÑÐµÐ¼ ÑÐµÐºÑÑÐ°Ñ Ð´Ð°ÑÐ°
stream.start.min.time.validation=ÐÑÐµÐ¼Ñ Ð´Ð¾ Ð¾ÑÐ»Ð¾Ð¶ÐµÐ½Ð½Ð¾Ð³Ð¾ ÑÑÐ¸ÑÐ° Ð´Ð¾Ð»Ð¶Ð½Ð¾ Ð±ÑÑÑ Ð±Ð¾Ð»ÑÑÐµ 1 ÑÐ°ÑÐ°
stream.start.max.time.validation=ÐÑÐµÐ¼Ñ Ð´Ð¾ Ð½Ð°ÑÐ°Ð»Ð° Ð¾ÑÐ»Ð¾Ð¶ÐµÐ½Ð½Ð¾Ð³Ð¾ ÑÑÐ¸ÑÐ° Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð±Ð¾Ð»ÑÑÐµ 30 Ð´Ð½ÐµÐ¹
stream.is.your.product.validation=ÐÑ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð´Ð¾Ð±Ð°Ð²Ð¸ÑÑ ÑÐ¾Ð²Ð°Ñ - {0} {1} Ð² ÑÑÐ¸Ñ, ÑÐ°Ðº ÐºÐ°Ðº Ð¾Ð½ Ð½Ðµ Ð½Ð°ÑÐ¾Ð´Ð¸ÑÑÑ Ð² Ð²Ð°ÑÐµÐ¼ ÑÐ¿Ð¸ÑÐºÐµ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð½ÑÑ ÑÐ¾Ð²Ð°ÑÐ¾Ð²
stream.product.state.validation=ÐÑ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð´Ð¾Ð±Ð°Ð²Ð¸ÑÑ ÑÐ¾Ð²Ð°Ñ - {0} {1} Ð² ÑÑÐ¸Ñ, ÑÐ°Ðº ÐºÐ°Ðº Ð¾Ð½ Ð½Ðµ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½
exception.photo-search.internal-error=ÐÐ¾Ð·Ð½Ð¸ÐºÐ»Ð° Ð²Ð½ÑÑÑÐµÐ½Ð½ÑÑ Ð¾ÑÐ¸Ð±ÐºÐ°, Ð¿Ð¾Ð¿ÑÐ¾Ð±ÑÐ¹ÑÐµ ÐµÑÐµ ÑÐ°Ð·
exception.photo-search.time-out=ÐÑÐµÐ¼Ñ Ð¿Ð¾Ð¸ÑÐºÐ° Ð¸ÑÑÐµÐºÐ»Ð¾, Ð¿Ð¾Ð¿ÑÐ¾Ð±ÑÐ¹ÑÐµ ÐµÑÐµ ÑÐ°Ð·
exception.product-request.not-found=ÐÐ±ÑÑÐ²Ð»ÐµÐ½Ð¸Ðµ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð¾
exception.product-request.bad-request=ÐÐµ Ð·Ð°Ð¿Ð¾Ð»Ð½ÐµÐ½Ð¾ Ð¾Ð±ÑÐ·Ð°ÑÐµÐ»ÑÐ½Ð¾Ðµ Ð¿Ð¾Ð»Ðµ
exception.product-request.create.category-id-not-found=ÐÐµ Ð·Ð°Ð¿Ð¾Ð»Ð½ÐµÐ½Ð¾ Ð¾Ð±ÑÐ·Ð°ÑÐµÐ»ÑÐ½Ð¾Ðµ Ð¿Ð¾Ð»Ðµ - ÐÐ°ÑÐµÐ³Ð¾ÑÐ¸Ñ
exception.product-request.create.not-authorized=Ð¢Ð¾Ð»ÑÐºÐ¾ Ð°Ð²ÑÐ¾Ñ Ð¼Ð¾Ð¶ÐµÑ ÑÐµÐ´Ð°ÐºÑÐ¸ÑÐ¾Ð²Ð°ÑÑ Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½Ð¸Ñ
exception.product-request.create.description-id-not-found=ÐÐµ Ð·Ð°Ð¿Ð¾Ð»Ð½ÐµÐ½Ð¾ Ð¾Ð±ÑÐ·Ð°ÑÐµÐ»ÑÐ½Ð¾Ðµ Ð¿Ð¾Ð»Ðµ - ÐÐ¿Ð¸ÑÐ°Ð½Ð¸Ðµ
exception.product-request.no-answer-from-product-request-service=ÐÑÐ¸Ð±ÐºÐ° Ð² ÑÐµÑÐ²Ð¸ÑÐµ Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½Ð¸Ð¹
exception.product-request.does-not-belong-to-user=ÐÐ±ÑÑÐ²Ð»ÐµÐ½Ð¸Ðµ Ð½Ðµ Ð¿ÑÐ¸Ð½Ð°Ð´Ð»ÐµÐ¶Ð¸Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ
exception.product-request.new-incorrect-state=ÐÐµÐ´Ð¾Ð¿ÑÑÑÐ¸Ð¼Ð¾Ðµ ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ðµ Ð´Ð»Ñ Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½Ð¸Ñ
exception.bargain.not-found=ÐÐ¾Ð½ÑÑÑÐ¾ÑÐ³ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
exception.bargain.not-found-id=ÐÐ¾Ð½ÑÑÑÐ¾ÑÐ³ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½: {0}
exception.bargain.small-price=ÐÐ¸Ð½Ð¸Ð¼Ð°Ð»ÑÐ½Ð°Ñ ÑÐµÐ½Ð° Ð¿Ð¾ ÐºÐ¾Ð½ÑÑÑÐ¾ÑÐ³Ð°Ð¼: {0}. ÐÑ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶Ð¸Ð»Ð¸: {1}
exception.bargain.access-denied=Ð£ Ð²Ð°Ñ Ð½ÐµÑ Ð¿ÑÐ°Ð² Ð´Ð¾ÑÑÑÐ¿Ð° Ðº Ð´Ð°Ð½Ð½Ð¾Ð¼Ñ ÐºÐ¾Ð½ÑÑÑÐ¾ÑÐ³Ñ
exception.bargain.need-update-application=ÐÐ»Ñ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°Ð½Ð¸Ñ ÑÐ¾ÑÐ³Ð¾Ð² Ð¾Ð±Ð½Ð¾Ð²Ð¸ÑÐµ Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ Ð´Ð¾ Ð¿Ð¾ÑÐ»ÐµÐ´Ð½ÐµÐ¹ Ð²ÐµÑÑÐ¸Ð¸
exception.bonuses.boutique-user=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ ÑÑÐ°ÑÑÐ²Ð¾Ð²Ð°ÑÑ Ð² Ð¿ÑÐ¾Ð³ÑÐ°Ð¼Ð¼Ðµ Ð»Ð¾ÑÐ»ÑÐ½Ð¾ÑÑÐ¸, Ñ.Ðº. ÑÐ²Ð»ÑÐµÑÑÑ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÐµÐ¼ Ð±ÑÑÐ¸ÐºÐ°
exception.bonuses.loyalty-cards.phone-required=ÐÐ»Ñ ÑÐµÐ³Ð¸ÑÑÑÐ°ÑÐ¸Ð¸ Ð±Ð¾Ð½ÑÑÐ½Ð¾Ð¹ ÐºÐ°ÑÑÑ Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ {0} Ð´Ð¾Ð»Ð¶ÐµÐ½ Ð±ÑÑÑ ÑÐµÐ»ÐµÑÐ¾Ð½
exception.bonuses.transaction.not-found=ÐÐ°Ð½Ð½ÑÐµ Ð¾ Ð±Ð¾Ð½ÑÑÐ½Ð¾Ð¹ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ð¸: {0} Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ñ Ð±Ð°ÑÐºÐ¾Ð´Ð¾Ð¼: {1} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ñ
exception.bonuses.transaction.must-belong-to-user=ÐÐ¾Ð½ÑÑÐ½Ð°Ñ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ: {0} Ð½Ðµ Ð¿ÑÐ¸Ð½Ð°Ð´Ð»ÐµÐ¶Ð¸Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ: {1} Ñ Ð±Ð°ÑÐºÐ¾Ð´Ð¾Ð¼: {2}
exception.bonuses.transaction.wrong-state=ÐÐ¾Ð½ÑÑÐ½Ð°Ñ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ: {0} Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ñ Ð¸Ð´ÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¾ÑÐ¾Ð¼: {1} Ð¸ Ð±Ð°ÑÐºÐ¾Ð´Ð¾Ð¼: {2} Ð´Ð¾Ð»Ð¶Ð½Ð° Ð±ÑÑÑ Ð² ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ð¸ HOLD
exception.brand.not-found=ÐÑÐµÐ½Ð´ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½: {0}.
exception.bubble-filler.not-found=ÐÐµ Ð½Ð°Ð¹Ð´ÐµÐ½ Ð¾Ð±Ð¾Ð³Ð°ÑÐ¸ÑÐµÐ»Ñ Ð¸Ð½ÑÐ¾ÑÐ¾Ð¼Ð°ÑÐ¸Ð¸ Ð´Ð»Ñ ÐºÐ°ÑÐµÐ³Ð¾ÑÐ¸Ð¸ {0}
exception.category.not-found=ÐÐ°ÑÐµÐ³Ð¾ÑÐ¸Ñ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð°: {0}
exception.residues.not-enough=ÐÐµÐ´Ð¾ÑÑÐ°ÑÐ¾ÑÐ½Ð¾ Ð¾ÑÑÐ°ÑÐºÐ¾Ð² ÑÐ¾Ð²Ð°ÑÐ°. Ð¢ÑÐµÐ±ÑÐµÑÑÑ: {0}. Ð Ð½Ð°Ð»Ð¸ÑÐ¸Ð¸: {1}
exception.residues.unavailable=ÐÐ¼ÐµÑÑÑÑ Ð½ÐµÐ´Ð¾ÑÑÑÐ¿Ð½ÑÐµ Ð¿Ð¾Ð·Ð¸ÑÐ¸Ð¸ ({0})
exception.order.promocode-expired=ÐÐ°ÐºÐ°Ð· {0}: Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´ Ð¸ÑÑÐµÐº ({1})
exception.order.promocode-discount-cannot-applied-to-seller=ÐÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´ Ð½ÐµÐ´Ð¾ÑÑÑÐ¿ÐµÐ½ Ð´Ð»Ñ ÑÐ¾Ð²Ð°ÑÐ¾Ð² Ð´Ð°Ð½Ð½Ð¾Ð³Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°
exception.order.promocode-discount-cannot-applied-to-brand=ÐÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´ Ð½ÐµÐ´Ð¾ÑÑÑÐ¿ÐµÐ½ Ð´Ð»Ñ ÑÐ¾Ð²Ð°ÑÐ¾Ð² Ð´Ð°Ð½Ð½Ð¾Ð³Ð¾ Ð±ÑÐµÐ½Ð´Ð°
exception.order.promocode-discount-cannot-applied-to-category=ÐÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´ Ð½ÐµÐ´Ð¾ÑÑÑÐ¿ÐµÐ½ Ð´Ð»Ñ ÑÐ¾Ð²Ð°ÑÐ¾Ð² Ð´Ð°Ð½Ð½Ð¾Ð¹ ÐºÐ°ÑÐµÐ³Ð¾ÑÐ¸Ð¸
exception.order.promocode-discount-cannot-applied-to-product=ÐÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´ Ð½ÐµÐ´Ð¾ÑÑÑÐ¿ÐµÐ½ Ð´Ð»Ñ Ð½ÐµÐºÐ¾ÑÐ¾ÑÑÑ Ð¸Ð· Ð²ÑÐ±ÑÐ°Ð½Ð½ÑÑ ÑÐ¾Ð²Ð°ÑÐ¾Ð²
exception.order.promocode-discount-cannot-applied=Ð ÑÑÐ¾Ð¼Ñ Ð·Ð°ÐºÐ°Ð·Ñ Ð½ÐµÐ»ÑÐ·Ñ Ð¿ÑÐ¸Ð¼ÐµÐ½Ð¸ÑÑ ÑÐºÐ¸Ð´ÐºÑ
exception.order.promocode-discount-cannot-cancel-in-proceed=ÐÐµÐ»ÑÐ·Ñ Ð¾ÑÐ¼ÐµÐ½Ð¸ÑÑ ÑÐºÐ¸Ð´ÐºÑ Ð´Ð»Ñ Ð·Ð°ÐºÐ°Ð·Ð°, ÐºÐ¾ÑÐ¾ÑÑÐ¹ ÑÐ¶Ðµ Ð¾Ð¿Ð»Ð°ÑÐµÐ½ Ð¸Ð»Ð¸ Ð½Ð°ÑÐ¾Ð´Ð¸ÑÑÑ Ð² Ð¿ÑÐ¾ÑÐµÑÑÐµ Ð¾Ð¿Ð»Ð°ÑÑ
exception.order.promocode-already-used=Ð­ÑÐ° ÑÐºÐ¸Ð´ÐºÐ° ÑÐ¶Ðµ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°Ð½Ð° Ð² Ð´ÑÑÐ³Ð¾Ð¼ Ð·Ð°ÐºÐ°Ð·Ðµ (â {0})
exception.order.request-not-found=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ ÑÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ Ð°Ð´ÑÐµÑÑÐ° Ð·Ð°Ð±Ð¾ÑÐ° Ð¸ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸. ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
exception.order.response-not-found=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ ÑÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ Ð´Ð¾ÑÑÐ°Ð²ÐºÐµ. ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
exception.order.response-information-not-full=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ ÑÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ Ð´Ð¾ÑÑÐ°Ð²ÐºÐµ. ÐÐµÑÐµÐ´Ð°Ð½Ð° Ð½Ðµ Ð²ÑÑ Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð°Ñ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ
exception.order.counteragent-order-not-found=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ ÑÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ ÐºÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½ÑÐ° Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ. ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
exception.order.delete-bucket-empty=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ ÑÐ´Ð°Ð»Ð¸ÑÑ Ð¿Ð¾Ð·Ð¸ÑÐ¸Ñ. ÐÐ¾ÑÐ·Ð¸Ð½Ð° Ð¿ÑÑÑÐ°.
exception.order.position-not-found=ÐÐ¾Ð·Ð¸ÑÐ¸Ñ {0} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð° Ð² Ð·Ð°ÐºÐ°Ð·Ðµ {1}
exception.order.point-not-set=Ð¢Ð¾ÑÐºÐ° Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸ Ð½Ðµ ÑÐºÐ°Ð·Ð°Ð½Ð°
exception.order.point-delivery-incorrect=Ð¢Ð¾ÑÐºÐ° Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸ Ð·Ð°Ð¿Ð¾Ð»Ð½ÐµÐ½Ð° Ð½ÐµÐºÐ¾ÑÑÐµÐºÑÐ½Ð¾
exception.order.seller-not-specified=ÐÑÐ¾Ð´Ð°Ð²ÐµÑ Ð½Ðµ ÑÐºÐ°Ð·Ð°Ð½
exception.order.delivery-fail-capture-in-progress=ÐÐ°ÐºÐ°Ð· {0}: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð²ÑÐ·Ð²Ð°ÑÑ ÑÐ»ÑÐ¶Ð±Ñ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸, ÑÐ°Ðº ÐºÐ°Ðº ÑÐ¿Ð¸ÑÐ°Ð½Ð¸Ðµ ÑÑÐµÐ´ÑÑÐ² Ð½Ðµ Ð·Ð°Ð²ÐµÑÑÐµÐ½Ð¾
exception.order.delivery-o2b-state-fail-no-payment=ÐÐ°ÐºÐ°Ð· {0}: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¸Ð·Ð¼ÐµÐ½Ð¸ÑÑ ÑÑÐ°ÑÑÑ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸, Ð¿Ð»Ð°ÑÐµÐ¶ Ð½Ðµ Ð·Ð°Ð²ÐµÑÑÐµÐ½
exception.order.delivery-s2o-state-fail-need-payout=ÐÐ°ÐºÐ°Ð· {0}\: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¸Ð·Ð¼ÐµÐ½Ð¸ÑÑ ÑÑÐ°ÑÑÑ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸, Ð¾Ð¶Ð¸Ð´Ð°ÐµÑÑÑ Ð²ÑÐ¿Ð»Ð°ÑÐ°
exception.order.incorrect-status=ÐÐ°ÐºÐ°Ð· {0}: Ð½ÐµÐºÐ¾ÑÑÐµÐºÑÐ½Ð¾Ðµ ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ðµ Ð·Ð°ÐºÐ°Ð·Ð° {1}
exception.order.incorrect-boutique-place=ÐÐ°ÐºÐ°Ð· {0}: Ð½Ð°ÑÐ¾Ð´Ð¸ÑÑÑ Ð² Ð±ÑÑÐ¸ÐºÐµ {1}, ÑÐµÐº Ð¸Ð· Ð±ÑÑÐ¸ÐºÐ° {2}
exception.order.empty=ÐÐ°ÐºÐ°Ð· Ð¿ÑÑÑÐ¾Ð¹
exception.order.not-found=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½ {0}
exception.order.file.not-found=Ð¤Ð°Ð¹Ð» {0} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½ Ð´Ð»Ñ Ð·Ð°ÐºÐ°Ð·Ð° {1}
exception.order.file.type.not-found=Ð¤Ð°Ð¹Ð» Ñ ÑÐºÐ°Ð·Ð°Ð½Ð½ÑÐ¼ ÑÐ¸Ð¿Ð¾Ð¼ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½ {0}
exception.order.file.size.limit=ÐÐ°Ð³ÑÑÐ¶Ð°ÐµÐ¼ÑÐ¹ ÑÐ°Ð¹Ð» Ð² Ð·Ð°ÐºÐ°Ð·Ðµ {0} Ð±Ð¾Ð»ÑÑÐµ Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼Ð¾Ð³Ð¾ Ð»Ð¸Ð¼Ð¸ÑÐ°. Ð¢ÑÐµÐ±ÑÐµÑÑÑ Ð´Ð¾ {1} Ð¼Ð±, Ð·Ð°Ð³ÑÑÐ¶Ð°ÐµÑÑÑ {2} Ð¼Ð±
exception.order.file.name=Ð¤Ð°Ð¹Ð» Ñ ÑÐ°ÐºÐ¸Ð¼ Ð¸Ð¼ÐµÐ½ÐµÐ¼ {0} Ð² Ð·Ð°ÐºÐ°Ð·Ðµ {1} ÑÐ¶Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ
exception.order.file.upload=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð·Ð°Ð³ÑÑÐ·ÐºÐ¸ ÑÐ°Ð¹Ð»Ð° {0} Ð² ÑÑÐ°Ð½Ð¸Ð»Ð¸ÑÐµ Ð² Ð·Ð°ÐºÐ°Ð·Ðµ {1}
exception.order.file.remove=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ ÑÐ´Ð°Ð»ÐµÐ½Ð¸Ð¸ ÑÐ°Ð¹Ð»Ð° {0} Ð¸Ð· ÑÑÐ°Ð½Ð¸Ð»Ð¸ÑÐ° Ð² Ð·Ð°ÐºÐ°Ð·Ðµ {1}
exception.order.file.obtain=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð¿Ð¾Ð»ÑÑÐµÐ½Ð¸Ð¸ ÑÐ°Ð¹Ð»Ð° {0} Ð¸Ð· ÑÑÐ°Ð½Ð¸Ð»Ð¸ÑÐ° Ð² Ð·Ð°ÐºÐ°Ð·Ðµ {1}
exception.order.empty-promocode-error=ÐÐµÐ»ÑÐ·Ñ Ð¿ÑÐ¸Ð¼ÐµÐ½Ð¸ÑÑ ÑÐºÐ¸Ð´ÐºÑ Ðº Ð¿ÑÑÑÐ¾Ð¼Ñ Ð·Ð°ÐºÐ°Ð·Ñ
exception.order.incorrect-point-request=ÐÐµ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð° ÑÐ¾ÑÐºÐ° Ð²ÑÐ²Ð¾Ð·Ð° Ð·Ð°ÐºÐ°Ð·Ð°
exception.order.delete-error=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ ÑÐ´Ð°Ð»Ð¸ÑÑ Ð·Ð°ÐºÐ°Ð·: {0}, Ð¿Ð¾Ð²ÑÐ¾ÑÐ¸ÑÐµ Ð¿Ð¾Ð¿ÑÑÐºÑ Ð¿Ð¾Ð·Ð¶Ðµ
exception.order.cancel-error-not-all-confirm=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÑ/Ð¾ÑÐ¼ÐµÐ½Ð¸ÑÑ Ð·Ð°ÐºÐ°Ð·, Ñ.Ðº. Ð½Ðµ Ð²ÑÐµ Ð¿Ð¾Ð·Ð¸ÑÐ¸Ð¸ Ð² Ð·Ð°ÐºÐ°Ð·Ðµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ñ/Ð¾ÑÐ¼ÐµÐ½ÐµÐ½Ñ.
exception.order.delete-alien=ÐÐµÐ»ÑÐ·Ñ ÑÐ´Ð°Ð»Ð¸ÑÑ ÑÑÐ¶Ð¾Ð¹ Ð·Ð°ÐºÐ°Ð·
exception.order.pay-order-not-found=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ Ð¸Ð½Ð¸ÑÐ¸Ð¸ÑÐ¾Ð²Ð°ÑÑ Ð¾Ð¿Ð»Ð°ÑÑ. ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½: #{0}
exception.order.scheme-not-supported=Ð¡ÑÐµÐ¼Ð° {0} Ð½Ðµ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÐµÑÑÑ Ð´Ð»Ñ Ð¾Ð±ÑÐ°Ð±Ð¾ÑÐºÐ¸ Ð·Ð°ÐºÐ°Ð·Ð¾Ð²
exception.order.not-suitable-for-placing-receipt=ÐÐ°ÐºÐ°Ð· {0} Ð½Ðµ Ð¿Ð¾Ð´ÑÐ¾Ð´Ð¸Ñ Ð´Ð»Ñ Ð¿ÑÐ¾ÑÑÐ°Ð²Ð»ÐµÐ½Ð¸Ñ ÑÐµÐºÐ°
exception.order.sync-buyer-check-dto-empty=Ð¡Ð¿Ð¸ÑÐ¾Ðº buyerCheckDTOs Ð´Ð»Ñ ÑÐ¸Ð½ÑÑÐ¾Ð½Ð¸Ð·Ð°ÑÐ¸Ð¸ Ð¿ÑÑÑ
exception.order.list-order-check-empty=Ð¡Ð¿Ð¸ÑÐ¾Ðº Ð·Ð°ÐºÐ°Ð·Ð¾Ð² Ð´Ð»Ñ Ð¿ÑÐ¾Ð±Ð¸ÑÐ¸Ñ ÑÐµÐºÐ¾Ð² Ð¿ÑÑÑ
exception.order.check-has-error=ÐÐ°ÐºÐ°Ð·: {0}, ÑÐµÐº Ð·Ð°ÐºÐ°Ð·Ð° ÑÐ¾Ð´ÐµÑÐ¶Ð¸Ñ Ð¾ÑÐ¸Ð±ÐºÐ¸, Ð¾ÑÐ¿ÑÐ°Ð²ÐºÐ° Ð½ÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð°
exception.order.delivery-point-not-found=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ ÑÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ Ð´Ð¾ÑÑÐ°Ð²ÐºÐµ. Ð¢Ð¾ÑÐºÐ° Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð°
exception.order.interval-error=ÐÐµÐ´Ð¾ÑÑÑÐ¿Ð½ÑÐ¹ ID Ð¸Ð½ÑÐµÑÐ²Ð°Ð»Ð° Ð²ÑÐµÐ¼ÐµÐ½Ð¸
exception.order.cancel-error-already-confirm=ÐÐµÐ»ÑÐ·Ñ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÑ/Ð¾ÑÐ¼ÐµÐ½Ð¸ÑÑ Ð¿Ð¾Ð·Ð¸ÑÐ¸Ñ Ð² Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð½Ð¾Ð¼ Ð·Ð°ÐºÐ°Ð·Ðµ
exception.order.cancel-error-already-confirm-order=ÐÐµÐ»ÑÐ·Ñ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÑ/Ð¾ÑÐ¼ÐµÐ½Ð¸ÑÑ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð½ÑÐ¹ Ð·Ð°ÐºÐ°Ð·
exception.order.position-not-found-simple=ÐÐ¾Ð·Ð¸ÑÐ¸Ñ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð°: {0}
exception.order.error-request-checkonline=ÐÐ°ÐºÐ°Ð· {0}, Ð¾ÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð¸Ð¸ Ð·Ð°Ð¿ÑÐ¾ÑÐ° Ðº CheckOnline: {1} ({2})
exception.order.has-not-been-transferred=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ Ð¸Ð½Ð¸ÑÐ¸Ð¸ÑÐ¾Ð²Ð°ÑÑ Ð¾Ð¿Ð»Ð°ÑÑ. ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð¿ÐµÑÐµÐ´Ð°Ð½.
exception.order.alien=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ Ð¸Ð½Ð¸ÑÐ¸Ð¸ÑÐ¾Ð²Ð°ÑÑ Ð¾Ð¿Ð»Ð°ÑÑ ÑÑÐ¶Ð¾Ð³Ð¾ Ð·Ð°ÐºÐ°Ð·Ð°
exception.order.attached-card-not-supported=ÐÐ°ÐºÐ°Ð· {0}: Ð¾Ð¿Ð»Ð°ÑÐ° Ð¿ÑÐ¸Ð²ÑÐ·Ð°Ð½Ð½Ð¾Ð¹ ÐºÐ°ÑÑÐ¾Ð¹ Ð½Ðµ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÐµÑÑÑ (ÐºÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½Ñ: {1})
exception.order.requisite-error=ÐÐ°ÐºÐ°Ð· {0}: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¾Ð¿Ð»Ð°ÑÐ¸ÑÑ Ñ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°Ð½Ð¸ÐµÐ¼ ÑÐºÐ°Ð·Ð°Ð½Ð½ÑÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÐ¾Ð² ({1})
exception.order.can-not-complete-hold-best2pay=ÐÐ»Ñ Ð·Ð°ÐºÐ°Ð·Ð° {0} Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð²ÑÐ²Ð¿Ð¾Ð»Ð½ÐµÐ½ HOLD_COMPLETED Ðº Best2Pay. ÐÑÐ¸Ð±ÐºÐ°: {1}
exception.order.can-not-complete-refund-best2pay=ÐÐ»Ñ Ð·Ð°ÐºÐ°Ð·Ð° {0} Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½ REFUND Ðº Best2Pay. ÐÑÐ¸Ð±ÐºÐ°: {1}
exception.order.payout-in-progress=ÐÐ°ÐºÐ°Ð· {0}: Ð²ÑÐ¿Ð»Ð°ÑÐ° Ð² Ð¿ÑÐ¾ÑÐµÑÑÐµ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð¸Ñ
exception.order.payout-fail=ÐÐ°ÐºÐ°Ð· {0}: Ð¾ÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð²ÑÐ¿Ð»Ð°ÑÐµ ÑÑÐµÐ´ÑÑÐ²
exception.order.payout-list-size-zero=ÐÐ°ÐºÐ°Ð· {0}: ÑÐ¿Ð¸ÑÐ¾Ðº Ð²ÑÐ¿Ð»Ð°Ñ Ð¾ÑÑÑÑÑÑÐ²ÑÐµÑ Ð¸Ð»Ð¸ Ð¿ÑÑÑ
exception.order.expertise.invalid-state=ÐÐ°ÐºÐ°Ð· {0}: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¸Ð·Ð¼ÐµÐ½Ð¸ÑÑ ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ðµ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ, Ð½ÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ ÑÑÐ°ÑÑÑ ({1})
exception.order.expertise.unconfirmed=ÐÐ°ÐºÐ°Ð· {0}: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¸Ð·Ð¼ÐµÐ½Ð¸ÑÑ ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ðµ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ, Ð¿Ð¾Ð·Ð¸ÑÐ¸Ñ Ð·Ð°ÐºÐ°Ð·Ð° {1} Ð½Ðµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð°
exception.order.paymentOptions.notOrderIdNorSellerId=ÐÑÐ¸ Ð¿Ð¾Ð»ÑÑÐµÐ½Ð¸Ð¸ ÑÐ¿Ð¾ÑÐ¾Ð±Ð¾Ð² Ð¾Ð¿Ð»Ð°ÑÑ Ð´Ð¾Ð»Ð¶ÐµÐ½ Ð±ÑÑÑ Ð¿ÐµÑÐµÐ´Ð°Ð½ Ð·Ð°ÐºÐ°Ð· Ð¸Ð»Ð¸ Ð¿ÑÐ¾Ð´Ð°Ð²ÐµÑ
exception.order.payment.auth.expire=ÐÐ°ÐºÐ°Ð· {0}: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð²ÑÐ¿Ð¾Ð»Ð½Ð¸ÑÑ ÑÐ¿Ð¸ÑÐ°Ð½Ð¸Ðµ ÐÐ¡, Ð²ÑÐµÐ¼Ñ ÑÐ´ÐµÑÐ¶Ð°Ð½Ð¸Ñ ÐÐ¡ Ð¸ÑÑÐµÐºÐ»Ð¾ ({1} > {2})
exception.order.no.payments.on.confirm=ÐÐ°ÐºÐ°Ð· {0}: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÑ Ð·Ð°ÐºÐ°Ð·, Ð¾ÑÑÑÑÑÑÐ²ÑÐµÑ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾Ð± Ð¾Ð¿Ð»Ð°ÑÐµ
exception.order.buyer-seller.orders-count.unknown=ÐÐµÐ¸Ð·Ð²ÐµÑÑÐ½Ð°Ñ Ð¾ÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð¿Ð¾Ð»ÑÑÐµÐ½Ð¸Ð¸ ÐºÐ¾Ð»-Ð²Ð° Ð¿ÑÐ¾Ð´Ð°Ð¶/Ð¿Ð¾ÐºÑÐ¿Ð¾Ðº Ð´Ð»Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ {0}
exception.order.item-in-boutique-only=Ð¢Ð¾Ð²Ð°Ñ {0} Ð´Ð¾ÑÑÑÐ¿ÐµÐ½ Ð´Ð»Ñ Ð¿Ð¾ÐºÑÐ¿ÐºÐ¸ ÑÐ¾Ð»ÑÐºÐ¾ Ð² Ð±ÑÑÐ¸ÐºÐµ OSKELLY
exception.promocode.number-of-applies-exceeded=ÐÑÑÐµÑÐ¿Ð°Ð½Ð¾ ÐºÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð¿ÑÐ¸Ð¼ÐµÐ½ÐµÐ½Ð¸Ð¹ Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´Ð°.
exception.waybill.forbidden-area=Can''t create order for forbidden area. OrderId: {0}
exception.waybill.cse.code=ÐÐ¾Ð´ Ð¾ÑÐ¸Ð±ÐºÐ¸ ÐÐ¡Ð­: {0}
exception.waybill.cse.history-notfound=ÐÑÑÐ¾ÑÐ¸Ñ Ð´Ð»Ñ Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ° Ñ Ð½Ð¾Ð¼ÐµÑÐ¾Ð¼: {0} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½ Ð² ÐÐ¡Ð­
exception.cse.already-exist=ÐÐ°ÑÐ²ÐºÐ° Ð½Ð° Ð´Ð¾ÑÑÐ°Ð²ÐºÑ Ð´Ð»Ñ ÑÑÐ¾Ð³Ð¾ Ð·Ð°ÐºÐ°Ð·Ð° ÑÐ¶Ðµ Ð·Ð°ÑÐµÐ³Ð¸ÑÑÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð° Ð² ÐÐ¡Ð­. ÐÑÐ»Ð¸ ÑÑÐ°ÑÑÑ Ð½Ðµ Ð¾Ð±Ð½Ð¾Ð²Ð¸Ð»ÑÑ, Ð´Ð¾Ð¶Ð´Ð¸ÑÐµÑÑ Ð¾Ð±Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ ÑÑÐ°ÑÑÑÐ° Ð² ÐºÐ°Ð±Ð¸Ð½ÐµÑÐµ.
exception.cse.not-found=Order with clientNumber: {0} not found in CSE
exception.cse.error-code=CSE error code: {0}
exception.cse.history-error=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð¿Ð¾Ð»ÑÑÐµÐ½Ð¸Ð¸ Ð¸ÑÑÐ¾ÑÐ¸Ð¸ Ð·Ð°ÐºÐ°Ð·Ð° Ð² ÐÐ¡Ð­ Ð´Ð»Ñ Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ°: {0}
exception.cse.history-error-document=Not found history data for documentType: {0}, historyNumber: {1}
exception.cse.waybill-not-found=Waybill with clientNumber: {0} not found in CSE
exception.streamservice.url-create-error=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ ÑÐ¾Ð·Ð´Ð°ÑÑ ÑÑÑÐ»ÐºÑ Ð´Ð»Ñ Ð·Ð°Ð¿ÑÐ¾ÑÐ° Ðº https://api.bambuser.com/broadcasts/{0}
exception.streamservice.bambuser-error=ÐÐµ Ð¿Ð¾Ð»ÑÑÐ¸Ð»Ð¸ Ð¿ÑÐ°Ð²Ð¸Ð»ÑÐ½ÑÐ¹ BambuserResponse {0}
exception.streamservice.bambuser-error-simple=ÐÐµ Ð¿Ð¾Ð»ÑÑÐ¸Ð»Ð¸ Ð¿ÑÐ°Ð²Ð¸Ð»ÑÐ½ÑÐ¹ BambuserResponse
exception.streamservice.connect-error=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¿Ð¾Ð´ÐºÐ»ÑÑÐ¸ÑÑÑÑ Ðº https://api.bambuser.com/broadcasts/{0}
exception.streamservice.user-not-found=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ñ ÑÐ°ÐºÐ¸Ð¼ Ð¿ÐµÑÐ²Ð¸ÑÐ½ÑÐ¼ ÐºÐ»ÑÑÐ¾Ð¼ Ð½Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ {0}
exception.userban.already-removed=ÐÐ°Ð½Ð½ÑÐ¹ Ð·Ð°Ð¿ÑÐµÑ ÑÐ¶Ðµ ÑÐ½ÑÑ
exception.userban.already-removed-or-deleted=ÐÐ°Ð½Ð½ÑÐ¹ Ð·Ð°Ð¿ÑÐµÑ ÑÐ¶Ðµ ÑÐ½ÑÑ Ð¸Ð»Ð¸ ÑÐ´Ð°Ð»ÐµÐ½
exception.userban.rules-violation=ÐÑ Ð½Ð°ÑÑÑÐ¸Ð»Ð¸ Ð¿ÑÐ°Ð²Ð¸Ð»Ð° ÑÐµÑÐ²Ð¸ÑÐ° Ð¸ Ð±Ð¾Ð»ÑÑÐµ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑÑÑ ÑÐµÑÐ²Ð¸ÑÐ¾Ð¼ ÑÐ¾ÑÐ³Ð¾Ð²
exception.userban.ban=ÐÑ Ð·Ð°Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ñ
exception.address.delete-no-rights=ÐÐµÐ»ÑÐ·Ñ ÑÐµÐ´Ð°ÐºÑÐ¸ÑÐ¾Ð²Ð°ÑÑ/ÑÐ´Ð°Ð»ÑÑÑ/Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ ÑÐ¾ÑÐºÑ, Ð½Ðµ Ð¿ÑÐ¸Ð½Ð°Ð´Ð»ÐµÐ¶Ð°ÑÑÑ ÑÐºÐ°Ð·Ð°Ð½Ð½Ð¾Ð¼Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ
exception.address.delete-impossible-used=ÐÐ´ÑÐµÑÐ½Ð°Ñ ÑÐ¾ÑÐºÐ° Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ ÑÐ´Ð°Ð»ÐµÐ½Ð° Ñ.Ðº. Ð¸ÑÐ¿Ð¾Ð»ÑÐ·ÑÐµÑÑÑ Ð² Ð·Ð°ÐºÐ°Ð·Ð°Ñ
exception.address.endpoint-delivery-pickup-not-found=Ð¢Ð¾ÑÐºÐ° Ð²ÑÐ²Ð¾Ð·Ð° Ð·Ð°Ð±Ð¾ÑÐ° Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð°: {0}
exception.address.endpoint-pickup-not-belong=Ð¢Ð¾ÑÐºÐ° Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸ Ð½Ðµ Ð¿ÑÐ¸Ð½Ð°Ð´Ð»ÐµÐ¶Ð¸Ñ ÑÐµÐºÑÑÐµÐ¼Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ: {0}
exception.address.endpoint-delivery-not-belong=Ð¢Ð¾ÑÐºÐ° Ð²ÑÐ²Ð¾Ð·Ð° Ð²Ð°Ð¼ Ð½Ðµ Ð¿ÑÐ¸Ð½Ð°Ð´Ð»ÐµÐ¶Ð¸Ñ: {0}
exception.address.endpoint-not-found=Ð¢Ð¾ÑÐºÐ° Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð°: {0}
exception.address.endpoint-pickup-not-found=Ð¢Ð¾ÑÐºÐ° Ð²ÑÐ²Ð¾Ð·Ð° Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð°: {0}
exception.address.endpoint-delivery-not-found=Ð¢Ð¾ÑÐºÐ° Ð²ÑÐ²Ð¾Ð·Ð° Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð°: {0}
exception.address.endpoint-delivery-not-fully-filled=Ð¢Ð¾ÑÐºÐ° Ð²ÑÐ²Ð¾Ð·Ð° Ð·Ð°Ð¿Ð¾Ð»Ð½ÐµÐ½Ð° Ð½Ðµ Ð¿Ð¾Ð»Ð½Ð¾ÑÑÑÑ: {0}
exception.address.link-not-valid=Link is not valid
exception.agent.report-in-fail-list=ÐÐ°ÐºÐ°Ð· {0}: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ ÑÑÐ¾ÑÐ¼Ð¸ÑÐ¾Ð²Ð°ÑÑ Ð¾ÑÑÐµÑ Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð¶Ðµ (ARFL)
exception.agent.invalid-state=ÐÑÑÐµÑ Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð¶Ðµ {0}: Ð½ÐµÐºÐ¾ÑÑÐµÐºÑÐ½Ð¾Ðµ ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ðµ {1}
exception.agent.cb.required-seller-link=ÐÑÑÐµÑ Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð¶Ðµ Ð´Ð»Ñ CrossBorder Ð·Ð°ÐºÐ°Ð·Ð° {0}: Ð½Ðµ ÑÐºÐ°Ð·Ð°Ð½Ð° ÑÑÑÐ»ÐºÐ° Ð½Ð° ÑÐ¾Ð²Ð°Ñ
exception.agent.cb.required-seller-price=ÐÑÑÐµÑ Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð¶Ðµ Ð´Ð»Ñ CrossBorder Ð·Ð°ÐºÐ°Ð·Ð° {0}: Ð½Ðµ ÑÐºÐ°Ð·Ð°Ð½Ð° ÑÐµÐ½Ð° Ð½Ð° ÑÑÐºÐ¸ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÑ
exception.agent.cb.required-seller-currency=ÐÑÑÐµÑ Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð¶Ðµ Ð´Ð»Ñ CrossBorder Ð·Ð°ÐºÐ°Ð·Ð° {0}: Ð½Ðµ ÑÐºÐ°Ð·Ð°Ð½Ð° Ð²Ð°Ð»ÑÑÐ° ÑÐµÐ½Ñ Ð½Ð° ÑÑÐºÐ¸ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÑ
exception.agent.cb.invalid-seller-currency=ÐÑÑÐµÑ Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð¶Ðµ Ð´Ð»Ñ CrossBorder Ð·Ð°ÐºÐ°Ð·Ð° {0}: Ð²Ð°Ð»ÑÑÐ° Ð´Ð»Ñ Ð²ÑÐµÑ ÑÐ¾Ð²Ð°ÑÐ¾Ð² Ð´Ð¾Ð»Ð¶Ð½Ð° Ð±ÑÑÑ Ð¾Ð´Ð¸Ð½Ð°ÐºÐ¾Ð²Ð°Ñ
exception.agent.repeated-payment-error=ÐÐµÐ»ÑÐ·Ñ ÑÐ¾Ð²ÐµÑÑÐ¸ÑÑ Ð¿Ð¾Ð²ÑÐ¾ÑÐ½ÑÑ Ð²ÑÐ¿Ð»Ð°ÑÑ Ð´Ð»Ñ Ð¾ÑÑÐµÑÐ° Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð¶Ðµ {0}
exception.agent.report-already-confirmed=ÐÑÑÐµÑ Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð¶Ðµ Ð´Ð»Ñ Ð·Ð°ÐºÐ°Ð·Ð° {0} ÑÐ¶Ðµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½
exception.agent.report-already-in-process=ÐÑÑÐµÑ Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð¶Ðµ Ð´Ð»Ñ Ð·Ð°ÐºÐ°Ð·Ð° {0} ÑÐ¶Ðµ Ð² Ð¿ÑÐ¾ÑÐµÑÑÐµ Ð¾Ð¿Ð»Ð°ÑÑ. ÐÐ¶Ð¸Ð´Ð°Ð¹ÑÐµ Ð·Ð°Ð²ÐµÑÑÐµÐ½Ð¸Ñ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð¸Ñ Ð¾Ð¿ÐµÑÐ°ÑÐ¸Ð¸ Ð²ÑÐ¿Ð»Ð°ÑÑ!
exception.agent.counteragent-not-installed=ÐÐ»Ñ Ð·Ð°ÐºÐ°Ð·Ð° {0} Ð½Ðµ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½ ÐºÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°
exception.agent.banking-details-not-set=ÐÐ»Ñ Ð·Ð°ÐºÐ°Ð·Ð° {0} Ð½Ðµ ÑÐºÐ°Ð·Ð°Ð½Ñ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð²ÑÐ²Ð¾Ð´Ð° Ð½Ð° ÑÑÐµÑ / ÐºÐ°ÑÑÑ (ÐºÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½Ñ: {1})
exception.agent.banking-details-not-supported=ÐÐ»Ñ Ð·Ð°ÐºÐ°Ð·Ð° {0} ÑÐºÐ°Ð·Ð°Ð½Ñ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð²ÑÐ²Ð¾Ð´Ð° Ð½Ð° ÐºÐ°ÑÑÑ ({1}): Ð½Ðµ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÑÑÑÑ Ð½Ð° Ð´Ð°Ð½Ð½ÑÐ¹ Ð¼Ð¾Ð¼ÐµÐ½Ñ
exception.agent.agent-didnt-receive-money=ÐÐ¾ Ð·Ð°ÐºÐ°Ð·Ñ {0} Ð´ÐµÐ½ÐµÐ¶Ð½ÑÐµ ÑÑÐµÐ´ÑÑÐ²Ð° Ð´Ð¾ ÑÐ¸Ñ Ð¿Ð¾Ñ Ð½Ðµ Ð¿ÐµÑÐµÑÐ¸ÑÐ»ÐµÐ½Ñ. ÐÐ¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ðµ Ð¾ÑÑÐµÑÐ° Ð´Ð¾ÑÑÑÐ¿Ð½Ð¾ ÑÐ¾Ð»ÑÐºÐ¾ Ð¿Ð¾ÑÐ»Ðµ Ð¿ÐµÑÐµÑÐ¸ÑÐ»ÐµÐ½Ð¸Ñ.
exception.agent.counteragent-not-set=ÐÐ»Ñ Ð·Ð°ÐºÐ°Ð·Ð° {0} Ð½Ðµ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½ ÐºÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°
exception.agent.confirm-someone-report=ÐÑ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´Ð°ÑÑ ÑÑÐ¶Ð¾Ð¹ Ð¾ÑÑÐµÑ
exception.agent.access-denied-report=ÐÐµÑ Ð´Ð¾ÑÑÑÐ¿Ð° Ðº Ð¾ÑÑÐµÑÑ Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð¶Ðµ
exception.agent.report-not-exist=ÐÑÑÐµÑÐ° Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð¶Ðµ Ð½Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ Ð´Ð»Ñ Ð·Ð°ÐºÐ°Ð·Ð° {0}
exception.agent.report-has-disput=ÐÐµ ÑÐ´Ð°ÐµÑÑÑ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÑ Ð¾ÑÑÐµÑ Ð°Ð³ÐµÐ½ÑÐ° Ð´Ð»Ñ Ð·Ð°ÐºÐ°Ð·Ð° {0}: Ð¾ÑÐºÑÑÑ ÑÐ¿Ð¾Ñ
exception.agent.report-not-exist-simple=ÐÑÑÐµÑÐ° Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð¶Ðµ Ð½Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ
exception.agent.agent-report-not-exist=ÐÑÑÐµÑÐ° Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð¶Ðµ {0} Ð½Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ
exception.agent.illegal-cp-kind=ÐÑÑÐµÑ Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð¶Ðµ {0}: Ð½ÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐµ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°
exception.agent.agent-report-cb-cp-kind-fail=ÐÐ°ÐºÐ°Ð· {0}: Ð½ÐµÐ´Ð¾Ð¿ÑÑÑÐ¸Ð¼ÑÐ¹ ÑÐ¸Ð¿ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÐ¾Ð² {2} (Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼ÑÐµ Ð´Ð»Ñ ÐÐ: {1})
exception.agent.agent-report-cb-ps-kind-fail=ÐÐ°ÐºÐ°Ð· {0}: Ð¿Ð»Ð°ÑÐµÐ¶Ð½Ð°Ñ ÑÐ¸ÑÑÐµÐ¼Ð° {1} Ð½Ðµ Ð¿Ð¾Ð´ÑÐ¾Ð´Ð¸Ñ Ð´Ð»Ñ Ð·Ð°ÐºÐ°Ð·Ð¾Ð² ÐÐ
exception.agent.counterparty.invalid.type=ÐÑÐ¸Ð±ÐºÐ° Ð² ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÐ°Ñ {0} Ð²ÑÐ¿Ð»Ð°ÑÑ Ð·Ð°ÐºÐ°Ð·Ð° {1}: ÐÑÐ¿Ð¾Ð»ÑÐ·ÑÐµÑÑÑ Ð½Ðµ Ð²ÐµÑÐ½ÑÐ¹ ÑÐ¸Ð¿ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÐ¾Ð², Ð²ÑÐ¿Ð»Ð°ÑÐ° Ð½Ðµ Ð´Ð¾ÑÑÑÐ¿Ð½Ð°
exception.agent.counterparty.empty.company.name=ÐÑÐ¸Ð±ÐºÐ° Ð² ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÐ°Ñ {0} Ð²ÑÐ¿Ð»Ð°ÑÑ Ð·Ð°ÐºÐ°Ð·Ð° {1}: ÐÐµ ÑÐºÐ°Ð·Ð°Ð½Ð¾ Ð½Ð°Ð¸Ð¼ÐµÐ½Ð¾Ð²Ð°Ð½Ð¸Ðµ ÐºÐ¾Ð¼Ð¿Ð°Ð½Ð¸Ð¸
exception.agent.counterparty.empty.legal.address=ÐÑÐ¸Ð±ÐºÐ° Ð² ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÐ°Ñ {0} Ð²ÑÐ¿Ð»Ð°ÑÑ Ð·Ð°ÐºÐ°Ð·Ð° {1}: ÐÐµ ÑÐºÐ°Ð·Ð°Ð½ Ð°Ð´ÑÐµÑ ÐºÐ¾Ð¼Ð¿Ð°Ð½Ð¸Ð¸
exception.agent.counterparty.empty.director.name=ÐÑÐ¸Ð±ÐºÐ° Ð² ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÐ°Ñ {0} Ð²ÑÐ¿Ð»Ð°ÑÑ Ð·Ð°ÐºÐ°Ð·Ð° {1}: ÐÐµ ÑÐºÐ°Ð·Ð°Ð½Ð¾ Ð¤ÐÐ Ð´Ð¸ÑÐµÐºÑÐ¾ÑÐ°
exception.agent.counterparty.empty.bank.name=ÐÑÐ¸Ð±ÐºÐ° Ð² ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÐ°Ñ {0} Ð²ÑÐ¿Ð»Ð°ÑÑ Ð·Ð°ÐºÐ°Ð·Ð° {1}: ÐÐµ ÑÐºÐ°Ð·Ð°Ð½Ð¾ Ð½Ð°Ð¸Ð¼ÐµÐ½Ð¾Ð²Ð°Ð½Ð¸Ðµ Ð±Ð°Ð½ÐºÐ°
exception.agent.counterparty.empty.billing.address=ÐÑÐ¸Ð±ÐºÐ° Ð² ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÐ°Ñ {0} Ð²ÑÐ¿Ð»Ð°ÑÑ Ð·Ð°ÐºÐ°Ð·Ð° {1}: ÐÐµ ÑÐºÐ°Ð·Ð°Ð½ Ð°Ð´ÑÐµÑ Ð±Ð°Ð½ÐºÐ°
exception.agent.counterparty.empty.swift.code=ÐÑÐ¸Ð±ÐºÐ° Ð² ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÐ°Ñ {0} Ð²ÑÐ¿Ð»Ð°ÑÑ Ð·Ð°ÐºÐ°Ð·Ð° {1}: ÐÐµ ÑÐºÐ°Ð·Ð°Ð½ swift code
exception.agent.counterparty.empty.iban=ÐÑÐ¸Ð±ÐºÐ° Ð² ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÐ°Ñ {0} Ð²ÑÐ¿Ð»Ð°ÑÑ Ð·Ð°ÐºÐ°Ð·Ð° {1}: ÐÐµ ÑÐºÐ°Ð·Ð°Ð½ IBAN
exception.description.unacceptable-words=Ð¢ÐµÐºÑÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ ÑÐ¾Ð´ÐµÑÐ¶Ð¸Ñ Ð·Ð°Ð¿ÑÐµÑÐµÐ½Ð½ÑÐµ ÑÐ»Ð¾Ð²Ð°!
exception.comment.phone-not-verified=ÐÐµ ÑÐ´Ð°ÐµÑÑÑ ÑÐ¾ÑÑÐ°Ð½Ð¸ÑÑ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹: Ð½Ðµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½ Ð½Ð¾Ð¼ÐµÑ ÑÐµÐ»ÐµÑÐ¾Ð½Ð° Ð² Ð¿ÑÐ¾ÑÐ¸Ð»Ðµ
exception.comment.telephone-must-not-contain=Ð¢ÐµÐºÑÑ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ñ Ð½Ðµ Ð´Ð¾Ð»Ð¶ÐµÐ½ ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑ ÑÐµÐ»ÐµÑÐ¾Ð½Ð½ÑÑ Ð½Ð¾Ð¼ÐµÑÐ¾Ð²!
exception.comment.email-must-not-contain=Ð¢ÐµÐºÑÑ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ñ Ð½Ðµ Ð´Ð¾Ð»Ð¶ÐµÐ½ ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑ email!
exception.comment.unacceptable-words=Ð¢ÐµÐºÑÑ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ñ ÑÐ¾Ð´ÐµÑÐ¶Ð¸Ñ Ð·Ð°Ð¿ÑÐµÑÐµÐ½Ð½ÑÐµ ÑÐ»Ð¾Ð²Ð°!
exception.comment.link-must-not-contain=Ð¢ÐµÐºÑÑ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ñ Ð½Ðµ Ð´Ð¾Ð»Ð¶ÐµÐ½ ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑ ÑÑÑÐ»Ð¾Ðº!
exception.comment.configurable-stop-list-word=Ð¢ÐµÐºÑÑ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ñ ÑÐ¾Ð´ÐµÑÐ¶Ð¸Ñ Ð·Ð°Ð¿ÑÐµÑÐµÐ½Ð½ÑÐµ ÑÐ»Ð¾Ð²Ð°!
exception.comment.user-not-authorized=ÐÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¸ Ð¼Ð¾Ð³ÑÑ Ð¾ÑÑÐ°Ð²Ð»ÑÑÑ ÑÐ¾Ð»ÑÐºÐ¾ Ð°Ð²ÑÐ¾ÑÐ¸Ð·Ð¾Ð²Ð°Ð½Ð½ÑÐµ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ð¸
exception.comment.order-id-not-present=ÐÐµ ÑÐºÐ°Ð·Ð°Ð½ Ð¸Ð´ÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¾Ñ ÑÐ¾Ð²Ð°ÑÐ° Ðº ÐºÐ¾ÑÐ¾ÑÐ¾Ð¼Ñ Ð¾ÑÐ½Ð¾ÑÐ¸ÑÑÑ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹
exception.comment.product-request-id-not-present=ÐÐµ ÑÐºÐ°Ð·Ð°Ð½Ð¾ Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½Ð¸Ðµ Ðº ÐºÐ¾ÑÐ¾ÑÐ¾Ð¼Ñ Ð¾ÑÐ½Ð¾ÑÐ¸ÑÑÑ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹
exception.comment.must-not-empty=Ð¢ÐµÐºÑÑ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ñ Ð½Ðµ Ð´Ð¾Ð»Ð¶ÐµÐ½ Ð±ÑÑÑ Ð¿ÑÑÑÑÐ¼!
exception.comment.image-over-number=ÐÐµÐ»ÑÐ·Ñ Ð¿ÑÐ¸ÐºÑÐµÐ¿Ð¸ÑÑ Ð±Ð¾Ð»ÐµÐµ {0}-Ñ Ð¸Ð·Ð¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ð¹!
exception.comment.product-not-found=Ð¢Ð¾Ð²Ð°Ñ Ñ Ð¸Ð´ÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¾ÑÐ¾Ð¼ {0} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½!
exception.comment.parent-comment-not-found=ÐÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹ {0} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½!
exception.comment.already-deleted=ÐÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹, Ðº ÐºÐ¾ÑÐ¾ÑÐ¾Ð¼Ñ Ð²Ñ Ð¾ÑÑÐ°Ð²Ð»ÑÐ»Ð¸ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹ Ð±ÑÐ» ÑÐ´Ð°Ð»ÐµÐ½ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÐµÐ¼. ÐÐ¾Ð¿ÑÐ¾Ð±ÑÐ¹ÑÐµ Ð¾ÑÑÐ°Ð²Ð¸ÑÑ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹ Ð¿Ð¾Ð´ ÑÐ¾Ð²Ð°ÑÐ¾Ð¼.
exception.comment.not-apply-specified-product=ÐÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹ {0} Ð½Ðµ Ð¾ÑÐ½Ð¾ÑÐ¸ÑÑÑ Ðº ÑÐºÐ°Ð·Ð°Ð½Ð½Ð¾Ð¼Ñ ÑÐ¾Ð²Ð°ÑÑ!
exception.comment.not-apply-specified-product-request=ÐÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹ {0} Ð½Ðµ Ð¾ÑÐ½Ð¾ÑÐ¸ÑÑÑ Ðº ÑÐºÐ°Ð·Ð°Ð½Ð½Ð¾Ð¼Ñ Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½Ð¸Ñ!
exception.comment.publishing.total-limit-exceeded=ÐÑÐµÐ²ÑÑÐµÐ½Ð¾ ÐºÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸ÐµÐ²
exception.comment.publishing.similar-limit-exceeded=ÐÑÐµÐ²ÑÑÐµÐ½Ð¾ ÐºÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð¿Ð¾ÑÐ¾Ð¶Ð¸Ñ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸ÐµÐ²
exception.post.configurable-stop-list-word=Ð¢ÐµÐºÑÑ ÑÐ¾Ð´ÐµÑÐ¶Ð¸Ñ Ð·Ð°Ð¿ÑÐµÑÐµÐ½Ð½ÑÐµ ÑÐ»Ð¾Ð²Ð°!
exception.bank.operation-id-empty=ÐÐµ Ð·Ð°Ð´Ð°Ð½ bankOperationId Ð´Ð»Ñ Ð¼ÐµÑÐ¾Ð´Ð° handleBindCard
exception.bank.operation-not-card-bind=ÐÐ¿ÐµÑÐ°ÑÐ¸Ñ {0} Ð½Ðµ ÑÐ²Ð»ÑÐµÑÑÑ CARD_BIND
exception.bank.operation-not-in-process=ÐÐ¿ÐµÑÐ°ÑÐ¸Ñ {0} Ð½Ðµ ÑÐ²Ð»ÑÐµÑÑÑ Ð² Ð¿ÑÐ¾ÑÐµÑÑÐµ
exception.bank.tbk-response-fail=Ð¢ÐÐ Ð½Ðµ Ð²ÐµÑÐ½ÑÐ» Ð¾ÑÐ²ÐµÑ
exception.bank.card-binding-error=ÐÐ¿ÐµÑÐ°ÑÐ¸Ñ Ð¿ÑÐ¸Ð²ÑÐ·ÐºÐ¸ ÐºÐ°ÑÑÑ Ð½Ðµ Ð²ÑÐ¿Ð¾Ð»Ð½Ð¸Ð»Ð°ÑÑ ÑÑÐ¿ÐµÑÐ½Ð¾
exception.bank.request-for-user-error=ÐÑ {0} Ð½Ðµ Ð¿Ð¾Ð»ÑÑÐµÐ½ registerCardBeginResponse Ð´Ð»Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ: {1}
exception.birthday.can-not-change=ÐÐµÐ»ÑÐ·Ñ Ð¼ÐµÐ½ÑÑÑ Ð´ÐµÐ½Ñ ÑÐ¾Ð¶Ð´ÐµÐ½Ð¸Ñ
exception.birthday.incorrect-date=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½Ð°Ñ Ð´Ð°ÑÐ° ÑÐ¾Ð¶Ð´ÐµÐ½Ð¸Ñ!
exception.card.card-ref-id-empty=ÐÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½Ñ {0} Ð½Ðµ ÑÐ¾Ð´ÐµÑÐ¶Ð¸Ñ cardRefId Ð´Ð»Ñ unbindCard
exception.card.unbound=ÐÐ¿ÐµÑÐ°ÑÐ¸Ñ Ð¾ÑÐ²ÑÐ·ÐºÐ¸ ÐºÐ°ÑÑÑ Ð½Ðµ Ð²ÑÐ¿Ð¾Ð»Ð½Ð¸Ð»Ð°ÑÑ ÑÑÐ¿ÐµÑÐ½Ð¾
exception.card.card-ref-id-unbind=ÐÐµ Ð·Ð°Ð´Ð°Ð½ cardRefId Ð´Ð»Ñ unbind ÐºÐ°ÑÑÑ
exception.card.counteragent-card-id-not-found=ÐÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½Ñ Ð¿Ð¾ cardRefId {0} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
exception.cart.not-possible-create-order-an-order=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ ÑÐ¾Ð·Ð´Ð°ÑÑ Ð¿Ð¾Ð´Ð·Ð°ÐºÐ°Ð· Ð´Ð»Ñ Ð·Ð°ÐºÐ°Ð·Ð° ÑÐ¾ ÑÑÐ°ÑÑÑÐ¾Ð¼ Ð¾ÑÐ»Ð¸ÑÐ½ÑÐ¼ Ð¾Ñ CREATED
exception.cart.user-not-found=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½: {0}
exception.cart.no-available-products-from-seller=Ð Ð·Ð°ÐºÐ°Ð·Ðµ Ð½ÐµÑ Ð´Ð¾ÑÑÑÐ¿Ð½ÑÑ ÑÐ¾Ð²Ð°ÑÐ¾Ð² Ð¾Ñ ÑÐºÐ°Ð·Ð°Ð½Ð½Ð¾Ð³Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°: {0}
exception.cart.buyer-not-set=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ ÑÐ¾Ð·Ð´Ð°ÑÑ Ð¿Ð¾Ð´Ð·Ð°ÐºÐ°Ð·, Ñ.Ðº. Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð½Ðµ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½
exception.cart.empty=ÐÐ¾ÑÐ·Ð¸Ð½Ð° Ð¿ÑÑÑÐ°. ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑ Ð¸Ð½Ð¸ÑÐ¸Ð¸ÑÐ¾Ð²Ð°Ð½.
exception.chat.add-error=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð´Ð¾Ð±Ð°Ð²Ð»ÐµÐ½Ð¸Ð¸ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð² HelpCrunch
exception.chat.update-error=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð¾Ð±Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ð¸ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÐµÐ¹ Ð² HelpCrunch
exception.chat.delete-error=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ ÑÐ´Ð°Ð»ÐµÐ½Ð¸Ð¸ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð² HelpCrunch
exception.chat.get-info-error=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð²Ð·ÑÑÐ¸Ð¸ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ð¸ Ð¾ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ðµ Ð² HelpCrunch
exception.counterparty.incorrect-format-bik=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ ÑÐ¾ÑÐ¼Ð°Ñ ÐÐÐ
exception.counterparty.incorrect-format-corr-acc=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ ÑÐ¾ÑÐ¼Ð°Ñ ÐºÐ¾ÑÑ. ÑÑÐµÑÐ°
exception.counterparty.incorrect-format-swift=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ ÑÐ¾ÑÐ¼Ð°Ñ SWIFT ÑÑÐµÑÐ°
exception.counterparty.no-iban-and-swift=ÐÑÐ¶Ð½Ð¾ ÑÐºÐ°Ð·Ð°ÑÑ SWIFT Ð¸Ð»Ð¸ IBAN ÐºÐ¾Ð´Ñ
exception.counterparty.no-country-type=ÐÑÐ¶Ð½Ð¾ ÑÐºÐ°Ð·Ð°ÑÑ ÑÑÑÐ°Ð½Ñ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÐ¾Ð²
exception.counterparty.no-code=ÐÑÐ¶Ð½Ð¾ ÑÐºÐ°Ð·Ð°ÑÑ {0} ÐºÐ¾Ð´
exception.counterparty.no-name=ÐÑÐ¶Ð½Ð¾ Ð·Ð°Ð¿Ð¾Ð»Ð½Ð¸ÑÑ Ð¤ÐÐ Ð¸Ð»Ð¸ Ð½Ð°Ð·Ð²Ð°Ð½Ð¸Ðµ ÐºÐ¾Ð¼Ð¿Ð°Ð½Ð¸Ð¸
exception.counterparty.no-account-type=ÐÐµ Ð·Ð°Ð¿Ð¾Ð»Ð½ÐµÐ½ ÑÐ°ÑÑÐµÑÐ½ÑÐ¹ ÑÑÐµÑ
exception.counterparty.no-bic-and-swift=ÐÐµ Ð·Ð°Ð¿Ð¾Ð»Ð½ÐµÐ½ ÐÐÐ Ð¸Ð»Ð¸ SWIFT
exception.counterparty.no-country-in-billing-address=ÐÐµ Ð·Ð°Ð¿Ð¾Ð»Ð½ÐµÐ½Ð° ÑÑÑÐ°Ð½Ð° Ð² Ð¿Ð»Ð°ÑÐµÐ¶Ð½Ð¾Ð¼ Ð°Ð´ÑÐµÑÐµ
exception.counterparty.no-iban-and-account-number=ÐÐµ Ð·Ð°Ð¿Ð¾Ð»Ð½ÐµÐ½ IBAN Ð¸Ð»Ð¸ ÑÐ°ÑÑÐµÑÐ½ÑÐ¹ ÑÑÐµÑ
exception.counterparty.incorrect-format-corresp-acc=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ ÑÐ¾ÑÐ¼Ð°Ñ ÑÐ°ÑÑÐµÑÐ½Ð¾Ð³Ð¾ ÑÑÐµÑÐ°
exception.counterparty.incorrect-format-inn-acc=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ ÑÐ¾ÑÐ¼Ð°Ñ ÐÐÐ ÑÑÐµÑÐ°. ÐÐ¾Ð»Ð¶ÐµÐ½ ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑ {0} ÑÐ¸ÑÑ Ð±ÐµÐ· Ð¿ÑÐ¾Ð±ÐµÐ»Ð¾Ð² Ð¸ Ð´ÑÑÐ³Ð¸Ñ ÑÐ¸Ð¼Ð²Ð¾Ð»Ð¾Ð²
exception.counterparty.access-denied-update-counteragent=ÐÐµÐ»ÑÐ·Ñ ÑÐµÐ´Ð°ÐºÑÐ¸ÑÐ¾Ð²Ð°ÑÑ ÐºÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½ÑÐ°, ÐºÐ¾ÑÐ¾ÑÑÐ¹ Ð½Ðµ Ð¿ÑÐ¸Ð½Ð°Ð´Ð»ÐµÐ¶Ð¸Ñ ÑÐµÐºÑÑÐµÐ¼Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ.
exception.counterparty.incorrect-format-ogrn=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ ÑÐ¾ÑÐ¼Ð°Ñ ÐÐÐ Ð (ÐÐÐ ÐÐÐ). ÐÐ¾Ð»Ð¶ÐµÐ½ ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑ 15 ÑÐ¸ÑÑ Ð±ÐµÐ· Ð¿ÑÐ¾Ð±ÐµÐ»Ð¾Ð² Ð¸ Ð´ÑÑÐ³Ð¸Ñ ÑÐ¸Ð¼Ð²Ð¾Ð»Ð¾Ð²
exception.counterparty.incorrect-format-ogrn-other=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ ÑÐ¾ÑÐ¼Ð°Ñ ÐÐÐ Ð. ÐÐ¾Ð»Ð¶ÐµÐ½ ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑ 13 ÑÐ¸ÑÑ Ð±ÐµÐ· Ð¿ÑÐ¾Ð±ÐµÐ»Ð¾Ð² Ð¸ Ð´ÑÑÐ³Ð¸Ñ ÑÐ¸Ð¼Ð²Ð¾Ð»Ð¾Ð²
exception.counterparty.incorrect-format-kpp=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ ÑÐ¾ÑÐ¼Ð°Ñ ÐÐÐ. ÐÐ¾Ð»Ð¶ÐµÐ½ ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑ 9 ÑÐ¸ÑÑ Ð±ÐµÐ· Ð¿ÑÐ¾Ð±ÐµÐ»Ð¾Ð² Ð¸ Ð´ÑÑÐ³Ð¸Ñ ÑÐ¸Ð¼Ð²Ð¾Ð»Ð¾Ð²
exception.counterparty.incorrect-format-iban=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ ÑÐ¾ÑÐ¼Ð°Ñ IBAN ÑÑÐµÑÐ°
exception.counterparty.access-denied-delete-counteragent=ÐÐµÐ»ÑÐ·Ñ ÑÐ´Ð°Ð»ÑÑÑ ÐºÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½ÑÐ° Ð½Ðµ Ð¿ÑÐ¸Ð½Ð°Ð´Ð»ÐµÐ¶Ð°Ð²ÑÐµÐ³Ð¾ ÑÐºÐ°Ð·Ð°Ð½Ð½Ð¾Ð¼Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ
exception.counterparty.counterparty-does-not-belong=ÐÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½Ñ Ð½Ðµ Ð¿ÑÐ¸Ð½Ð°Ð´Ð»ÐµÐ¶Ð¸Ñ ÑÐµÐºÑÑÐµÐ¼Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ: {0}
exception.counterparty.counteragent-not-found=ÐÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½Ñ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½: {0}
exception.counterparty.deleted.true=Ð ÐµÐºÐ²Ð¸Ð·Ð¸Ñ {0} Ð±ÑÐ» ÑÐ´Ð°Ð»ÐµÐ½. ÐÐµÐ´Ð¾ÑÑÑÐ¿ÐµÐ½ Ð´Ð»Ñ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°Ð½Ð¸Ñ
exception.counterparty.field-empty=ÐÑÑÑÐ¾Ðµ Ð¿Ð¾Ð»Ðµ
exception.counterparty.should-without-spaces=ÐÐ°Ð·Ð²Ð°Ð½Ð¸Ðµ Ð½Ðµ Ð´Ð¾Ð»Ð¶Ð½Ð¾ Ð½Ð°ÑÐ¸Ð½Ð°ÑÑÑÑ Ð¸ Ð·Ð°ÐºÐ°Ð½ÑÐ¸Ð²Ð°ÑÑÑÑ Ð¿ÑÐ¾Ð±ÐµÐ»Ð°Ð¼Ð¸
exception.counterparty.must-not-contain-quotation-marks=ÐÐ°Ð·Ð²Ð°Ð½Ð¸Ðµ Ð½Ðµ Ð´Ð¾Ð»Ð¶Ð½Ð¾ ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑ ÐºÐ°Ð²ÑÑÐºÐ¸
exception.counterparty.name-should-not-start=ÐÐ°Ð·Ð²Ð°Ð½Ð¸Ðµ Ð½Ðµ Ð´Ð¾Ð»Ð¶Ð½Ð¾ Ð½Ð°ÑÐ¸Ð½Ð°ÑÑÑÑ Ñ {0}
exception.counterparty.requisite-can-not-delete-order=Ð ÐµÐºÐ²Ð¸Ð·Ð¸Ñ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ ÑÐ´Ð°Ð»ÐµÐ½, Ñ.Ðº. Ð¸ÑÐ¿Ð¾Ð»ÑÐ·ÑÐµÑÑÑ Ð² Ð·Ð°ÐºÐ°Ð·Ð°Ñ
exception.counterparty.output-not-available=ÐÐ°ÐºÐ°Ð· {0}, Ð²ÑÐ¿Ð»Ð°ÑÐ° Ð½Ð° Ð±Ð°Ð½ÐºÐ¾Ð²ÑÐºÑÑ ÐºÐ°ÑÑÑ Ð½ÐµÐ´Ð¾ÑÑÑÐ¿Ð½Ð°: Ð¸ÑÐ¿Ð¾Ð»ÑÐ·ÑÐ¹ÑÐµ Ð²ÑÐ²Ð¾Ð´ ÑÑÐµÐ´ÑÑÐ² Ð¿Ð¾ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÐ°Ð¼ (ÐºÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½Ñ: {1}, {2})
exception.counterparty.output-test-not-available=ÐÐ°ÐºÐ°Ð· {0}: Ð²ÑÐ¿Ð»Ð°ÑÐ° Ð½Ð° ÐºÐ°ÑÑÑ Ð² ÑÐµÑÑÐ¾Ð²Ð¾Ð¼ ÑÐµÐ¶Ð¸Ð¼Ðµ, Ð½ÐµÐ´Ð¾ÑÑÑÐ¿Ð½Ð¾ Ð´Ð»Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ {1} (Ð²ÑÐ±ÐµÑÐ¸ÑÐµ Ð´ÑÑÐ³Ð¾Ð¹ ÑÐ¿Ð¾ÑÐ¾Ð± Ð²ÑÐ²Ð¾Ð´Ð° Ð¸Ð»Ð¸ Ð¿Ð¾Ð²ÑÐ¾ÑÐ¸ÑÐµ Ð¿Ð¾Ð¿ÑÑÐºÑ Ð¿Ð¾Ð·Ð´Ð½ÐµÐµ, ÐºÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½Ñ: {2})
exception.counterparty.output-expired-card-empty=ÐÐ°ÐºÐ°Ð· {0}: ÑÑÐ¾Ðº Ð´ÐµÐ¹ÑÑÐ²Ð¸Ñ ÐºÐ°ÑÑÑ {1} Ð½Ðµ ÑÐºÐ°Ð·Ð°Ð½ (ÐºÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½Ñ: {2})
exception.counterparty.output-expired-card=ÐÐ°ÐºÐ°Ð· {0}: ÑÑÐ¾Ðº Ð´ÐµÐ¹ÑÑÐ²Ð¸Ñ ÐºÐ°ÑÑÑ {1} Ð±Ð»Ð¸Ð·Ð¾Ðº Ðº Ð¸ÑÑÐµÑÐµÐ½Ð¸Ñ Ð¸Ð»Ð¸ ÑÐ¶Ðµ Ð¸ÑÑÐµÐº (ÐºÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½Ñ: {2})
exception.counterparty.requisite-not-change-already-confirm-order=ÐÐ°ÐºÐ°Ð· {0}: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¸Ð·Ð¼ÐµÐ½Ð¸ÑÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ, Ð·Ð°ÐºÐ°Ð· ÑÐ¶Ðµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½ Ñ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÐ°Ð¼Ð¸ Ð®Ð / ÐÐ ({1})
exception.counterparty.requisite-receipts-exists=ÐÐ°ÐºÐ°Ð· {0}\: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¸Ð·Ð¼ÐµÐ½Ð¸ÑÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ, Ð¾ÑÐ¾ÑÐ¼Ð»ÐµÐ½ ÑÐ¸ÑÐºÐ°Ð»ÑÐ½ÑÐ¹ ÑÐµÐº
exception.counterparty.requisite-not-used-already-confirm-order=ÐÐ°ÐºÐ°Ð· {0}: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð®Ð / ÐÐ ({1}), Ð·Ð°ÐºÐ°Ð· ÑÐ¶Ðµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½
exception.counterparty.requisite-not-used-need-admin-confirm=ÐÐ°ÐºÐ°Ð· {0}: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð®Ð / ÐÐ ({1}), ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð½Ðµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ñ Ð°Ð´Ð¼Ð¸Ð½Ð¸ÑÑÑÐ°ÑÐ¾ÑÐ¾Ð¼
exception.counterparty.requisite-not-used-incorrect-index-nds=ÐÐ°ÐºÐ°Ð· {0}: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð®Ð / ÐÐ ({1}), Ð½ÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ Ð¸Ð½Ð´ÐµÐºÑ ÐÐÐ¡ ({2})
exception.counterparty.requisite-need-use-confirms=ÐÐ°ÐºÐ°Ð· {0}: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð¤Ð ({1}) Ð´Ð»Ñ Ð®Ð / ÐÐ, Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð½ÑÐµ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð®Ð / ÐÐ
exception.counterparty.requisite-incorrect-type=ÐÐ°ÐºÐ°Ð· {0}: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð¤Ð ({1}) Ð´Ð»Ñ Ð®Ð / ÐÐ, Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð®Ð / ÐÐ
exception.counterparty.requisite-empty=ÐÐ°ÐºÐ°Ð· {0}, ÐºÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½Ñ {1}: Ð½Ðµ ÑÐºÐ°Ð·Ð°Ð½Ñ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð´Ð»Ñ Ð²ÑÐ¿Ð»Ð°ÑÑ
exception.counterparty.requisite-agent-sell-not-set=ÐÐ°ÐºÐ°Ð· {0}: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÑ Ð·Ð°ÐºÐ°Ð·, Ð½Ðµ ÑÐºÐ°Ð·Ð°Ð½Ñ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð´Ð»Ñ Ð°Ð³ÐµÐ½ÑÑÐºÐ¾Ð¹ Ð¿ÑÐ¾Ð´Ð°Ð¶Ð¸ (Ð¿ÑÐ¾Ð´Ð°Ð²ÐµÑ: {1})
exception.counterparty.bank-card-already-linked=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ {0} ({1}): Ð±Ð°Ð½ÐºÐ¾Ð²ÑÐºÐ°Ñ ÐºÐ°ÑÑÐ° {2} ÑÐ¶Ðµ Ð¿ÑÐ¸Ð²ÑÐ·Ð°Ð½Ð°
exception.counterparty.counteragent-not-type-counterparty=ÐÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½Ñ {0} Ð½Ðµ ÑÐ²Ð»ÑÐµÑÑÑ ÑÐ¸Ð¿Ð° CardCounterparty
exception.counterparty.notAllowedToModify=Ð¢ÐµÐºÑÑÐµÐ¼Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð½ÐµÐ´Ð¾ÑÑÑÐ¿Ð½Ð° Ð²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ÑÑÑ Ð¸Ð·Ð¼ÐµÐ½ÐµÐ½Ð¸Ñ ÑÐ¿Ð¾ÑÐ¾Ð±Ð° Ð²ÑÐ¿Ð»Ð°Ñ
exception.counterparty.payout-active=ÐÐ°ÐºÐ°Ð· {0}: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¸Ð·Ð¼ÐµÐ½Ð¸ÑÑ Ð¿Ð°ÑÐ°Ð¼ÐµÑÑÑ Ð²ÑÐ¿Ð»Ð°ÑÑ (ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ), Ð²ÑÐ¿Ð»Ð°ÑÐ° Ð² Ð¿ÑÐ¾ÑÐµÑÑÐµ Ð¸Ð»Ð¸ Ð·Ð°Ð²ÐµÑÑÐµÐ½Ð°
exception.deeplink.not-supported=ÐÐ°Ð½Ð½ÑÐ¹ Ð²Ð¸Ð´ ÑÑÑÐ»ÐºÐ¸ Ð½Ðµ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÐµÑÑÑ
exception.deeplink.already-used=Ð¡ÑÑÐ»ÐºÐ° ÑÐ¶Ðµ Ð±ÑÐ»Ð° Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°Ð½Ð°
exception.deeplink.catalog-item-not-found=Ð¢Ð¾Ð²Ð°Ñ Ð² ÐºÐ°ÑÐ°Ð»Ð¾Ð³Ðµ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
exception.defectimage.not-found=ÐÐµÑ ÑÐ°ÐºÐ¾Ð³Ð¾ Ð¸Ð·Ð¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ñ Ð´ÐµÑÐµÐºÑÐ°
exception.defectimage.extra-identificator=ÐÐµÑÐµÐ´Ð°Ð½Ñ Ð»Ð¸ÑÐ½Ð¸Ðµ Ð¸Ð´ÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¾ÑÑ Ð¸Ð·Ð¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ð¹ Ð´ÐµÑÐµÐºÑÐ¾Ð², ÐºÐ¾ÑÐ¾ÑÑÑ Ð½ÐµÑ Ð² Ð±Ð°Ð·Ðµ
exception.discount.products-missing=Ð ÐºÐ¾ÑÐ·Ð¸Ð½Ðµ Ð¾ÑÑÑÑÑÑÐ²ÑÑÑ ÑÐ¾Ð²Ð°ÑÑ Ð¿Ð¾ ÑÐºÐ°Ð·Ð°Ð½Ð½Ð¾Ð¼Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÑ
exception.discount.order-empty=ÐÐµÐ»ÑÐ·Ñ Ð¿ÑÐ¸Ð¼ÐµÐ½Ð¸ÑÑ ÑÐºÐ¸Ð´ÐºÑ Ðº Ð¿ÑÑÑÐ¾Ð¼Ñ Ð·Ð°ÐºÐ°Ð·Ñ
exception.email.exists=Ð£ÐºÐ°Ð·Ð°Ð½Ð½ÑÐ¹ Ð°Ð´ÑÐµÑ e-mail {0} ÑÐ¶Ðµ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·ÑÐµÑÑÑ (Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ: {1})
exception.email.invalidate-value=ÐÐµÐ´Ð¾Ð¿ÑÑÑÐ¸Ð¼Ð¾Ðµ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ: {0}
exception.email.maximum-length=ÐÐ°ÐºÑÐ¸Ð¼Ð°Ð»ÑÐ½Ð°Ñ Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼Ð°Ñ Ð´Ð»Ð¸Ð½Ð°: {0}
exception.email.invalidate-format=ÐÐ´ÑÐµÑ Ð½Ðµ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÐµÑ ÑÐ¾ÑÐ¼Ð°ÑÑ. ÐÐ¾Ð¿ÑÑÐºÐ°ÑÑÑÑ ÑÐ¾Ð»ÑÐºÐ¾ Ð»Ð°ÑÐ¸Ð½ÑÐºÐ¸Ðµ ÑÐ¸Ð¼Ð²Ð¾Ð»Ñ, ÑÐ¸ÑÑÑ, ÑÐ¾ÑÐºÐ¸, Ð·Ð½Ð°Ðº Ð¿Ð¾Ð´ÑÐµÑÐºÐ¸Ð²Ð°Ð½Ð¸Ñ. ÐÐ±ÑÐ·Ð°ÑÐµÐ»ÑÐ½ÑÐ¹ ÑÐ¸Ð¼Ð²Ð¾Ð» - @
exception.email.too-many-attempts=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ ÑÐ³ÐµÐ½ÐµÑÐ¸ÑÐ¾Ð²Ð°ÑÑ email. ÐÑÐµÐ²ÑÑÐµÐ½Ð¾ ÐºÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð¿Ð¾Ð¿ÑÑÐ¾Ðº.
exception.export.not-found-marker=ÐÐµ Ð½Ð°Ð¹Ð´ÐµÐ½ Ð¼Ð°ÑÐºÐµÑ <offers/> Ð² ÑÐ°Ð±Ð»Ð¾Ð½Ðµ YML-ÑÐ¸Ð´Ð°
exception.fiscal.generate-request-counteragent-empty=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ ÑÐ³ÐµÐ½ÐµÑÐ¸ÑÐ¾Ð²Ð°ÑÑ Ð·Ð°Ð¿ÑÐ¾Ñ Ð½Ð° Ð¿Ð¾Ð»ÑÑÐµÐ½Ð¸Ðµ: Ð·Ð°ÐºÐ°Ð· {0}, Ð°Ð³ÐµÐ½Ñ-Ð¿ÑÐ¾Ð´Ð°Ð²ÐµÑ Ð±ÐµÐ· ÑÐºÐ°Ð·Ð°Ð½Ð½Ð¾Ð³Ð¾ ÐºÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½ÑÐ°
exception.fiscal.generate-request-mark-code-confirmation=ÐÑÐ¸Ð±ÐºÐ° ÑÐ¾ÑÐ¼Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ Ð·Ð°Ð¿ÑÐ¾ÑÐ° Ð½Ð° ÑÐµÐº: Ð·Ð°ÐºÐ°Ð· {0}, ÐºÐ¾Ð´ Ð¼Ð°ÑÐºÐ¸ÑÐ¾Ð²ÐºÐ¸ Ð½Ðµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½
exception.fiscal.unsupported-fiscal-kind=ÐÐµÐ¿Ð¾Ð´Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÐµÐ¼ÑÐ¹ fiscalReceiptKind: {0}
exception.fiscal.unable-generate=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ ÑÐ³ÐµÐ½ÐµÑÐ¸ÑÐ¾Ð²Ð°ÑÑ Ð·Ð°Ð¿ÑÐ¾Ñ Ð½Ð° Ð¿Ð¾Ð»ÑÑÐµÐ½Ð¸Ðµ
exception.fiscal.unsupported-fiscal-type=ÐÐµÐ¿Ð¾Ð´Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÐµÐ¼ÑÐ¹ ÑÐ¸Ð¿ fiscalReceiptType: {0}
exception.fiscal.payment-amount-ne-total=ÐÐµ ÑÐ´Ð°ÐµÑÑÑ ÑÐ³ÐµÐ½ÐµÑÐ¸ÑÐ¾Ð²Ð°ÑÑ Ð·Ð°Ð¿ÑÐ¾Ñ ÑÐµÐºÐ° {0}\: totalPaysAmount ({1}) != positionsAmount({2})
exception.fiscal.unable-generate-invalid-vat=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ ÑÐ³ÐµÐ½ÐµÑÐ¸ÑÐ¾Ð²Ð°ÑÑ Ð·Ð°Ð¿ÑÐ¾Ñ Ð½Ð° Ð¿Ð¾Ð»ÑÑÐµÐ½Ð¸Ðµ Ð¾Ñ Ð°Ð³ÐµÐ½ÑÐ°: ÐºÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½Ñ {0}, Ð½ÐµÐ²ÐµÑÐ½ÑÐ¹ Ð¸Ð½Ð´ÐµÐºÑ ÑÑÐ°Ð²ÐºÐ¸ ÐÐÐ¡ {1}
exception.forbidden.comment-unauthorized=Ð¢Ð¾Ð»ÑÐºÐ¾ Ð°Ð²ÑÐ¾ÑÐ¸Ð·Ð¾Ð²Ð°Ð½Ð½ÑÐ¹ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð¼Ð¾Ð¶ÐµÑ ÑÐ´Ð°Ð»ÑÑÑ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¸
exception.forbidden.comment-delete-only-owner=Ð¢Ð¾Ð»ÑÐºÐ¾ Ð²Ð»Ð°Ð´ÐµÐ»ÐµÑ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ñ Ð¼Ð¾Ð¶ÐµÑ ÑÐ´Ð°Ð»Ð¸ÑÑ ÐµÐ³Ð¾!
exception.forbidden.notification-access-denied=Ð£ Ð²Ð°Ñ Ð½ÐµÑ Ð¿ÑÐ°Ð² Ð½Ð° Ð¿ÑÐ¾ÑÐ¼Ð¾ÑÑ Ð´Ð°Ð½Ð½Ð¾Ð³Ð¾ ÑÐ²ÐµÐ´Ð¾Ð¼Ð»ÐµÐ½Ð¸Ñ
exception.forbidden.access-denied-order-info=Ð£ ÐÐ°Ñ Ð½ÐµÑ Ð¿ÑÐ°Ð² Ð½Ð° Ð¿Ð¾Ð»ÑÑÐµÐ½Ð¸Ðµ Ð´Ð°Ð½Ð½ÑÑ Ð¾ Ð·Ð°ÐºÐ°Ð·Ðµ: {0}
exception.forbidden.access-denied-order-info-in-state=Ð£ ÐÐ°Ñ Ð½ÐµÑ Ð¿ÑÐ°Ð² Ð½Ð° Ð¿Ð¾Ð»ÑÑÐµÐ½Ð¸Ðµ Ð´Ð°Ð½Ð½ÑÑ Ð¾ Ð·Ð°ÐºÐ°Ð·Ðµ Ð² ÑÑÐ°ÑÑÑÐµ {0}
exception.forbidden.not-seller-in-order=ÐÑ Ð½Ðµ ÑÐ²Ð»ÑÐµÑÐµÑÑ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð¼ Ð² Ð·Ð°ÐºÐ°Ð·Ðµ: {0}
exception.forbidden.not-buyer-in-order=ÐÑ Ð½Ðµ ÑÐ²Ð»ÑÐµÑÐµÑÑ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»ÐµÐ¼ Ð² Ð·Ð°ÐºÐ°Ð·Ðµ: {0}
exception.forbidden.invalidate-login-or-password=ÐÐµÐ²ÐµÑÐ½ÑÐ¹ Ð»Ð¾Ð³Ð¸Ð½ Ð¸Ð»Ð¸ Ð¿Ð°ÑÐ¾Ð»Ñ
exception.forbidden.invalidate-phone-number=ÐÐµÐ²ÐµÑÐ½ÑÐ¹ Ð½Ð¾Ð¼ÐµÑ ÑÐµÐ»ÐµÑÐ¾Ð½Ð°
exception.forbidden.invalidate-auth-data=ÐÐµÐ²ÐµÑÐ½ÑÐµ Ð´Ð°Ð½Ð½ÑÐµ Ð°Ð²ÑÐ¾ÑÐ¸Ð·Ð°ÑÐ¸Ð¸
exception.image.incorrect-parameter=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐµ Ð¿Ð°ÑÐ°Ð¼ÐµÑÑÑ Ð¸Ð·Ð¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ñ
exception.image.impossible-save=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ ÑÐ¾ÑÑÐ°Ð½Ð¸ÑÑ Ð¸Ð·Ð¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ðµ
exception.image.extra-image-ids-not-supported=ÐÐµÑÐµÐ´Ð°Ð½Ñ Ð»Ð¸ÑÐ½Ð¸Ðµ Ð¸Ð´ÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¾ÑÑ Ð¸Ð·Ð¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ð¹, ÐºÐ¾ÑÐ¾ÑÑÑ Ð½ÐµÑ Ð² Ð±Ð°Ð·Ðµ
exception.nickname.already-used-simple=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ñ ÑÐ°ÐºÐ¸Ð¼ nickname ÑÐ¶Ðµ Ð·Ð°ÑÐµÐ³Ð¸ÑÑÑÐ¸ÑÐ¾Ð²Ð°Ð½
exception.nickname.already-used=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ñ ÑÐ°ÐºÐ¸Ð¼ nickname {0} ÑÐ¶Ðµ Ð·Ð°ÑÐµÐ³Ð¸ÑÑÑÐ¸ÑÐ¾Ð²Ð°Ð½
exception.nickname.invalidate-value=ÐÐµÐ´Ð¾Ð¿ÑÑÑÐ¸Ð¼Ð¾Ðµ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ: {0}
exception.nickname.maximum-length=ÐÐ°ÐºÑÐ¸Ð¼Ð°Ð»ÑÐ½Ð°Ñ Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼Ð°Ñ Ð´Ð»Ð¸Ð½Ð°: {0}
exception.nickname.minimum-length=ÐÐ¸Ð½Ð¸Ð¼Ð°Ð»ÑÐ½Ð°Ñ Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼Ð°Ñ Ð´Ð»Ð¸Ð½Ð°: {0}
exception.nickname.invalidate-symbol=ÐÐ¼ÐµÑÑÑÑ Ð½ÐµÐ´Ð¾Ð¿ÑÑÑÐ¸Ð¼ÑÐµ ÑÐ¸Ð¼Ð²Ð¾Ð»Ñ. ÐÐ¾Ð¿ÑÑÐºÐ°ÑÑÑÑ ÑÐ¾Ð»ÑÐºÐ¾ Ð»Ð°ÑÐ¸Ð½ÑÐºÐ¸Ðµ Ð±ÑÐºÐ²Ñ, ÑÐ¸ÑÑÑ, ÑÐ¸Ð¼Ð²Ð¾Ð»Ñ Ð¿Ð¾Ð´ÑÐµÑÐºÐ¸Ð²Ð°Ð½Ð¸Ñ Ð¸ ÑÐ¸ÑÐµ
exception.not-found.promocode=Ð¡ÐºÐ¸Ð´ÐºÐ° c ÐºÐ¾Ð´Ð¾Ð¼: {0} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð°
exception.not-found.promocode-simple=Ð¡ÐºÐ¸Ð´ÐºÐ° Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð°
exception.no-access.promocode=ÐÐ¾ÑÑÑÐ¿ Ðº Ð´Ð°Ð½Ð½Ð¾Ð¼Ñ Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´Ñ Ð·Ð°Ð¿ÑÐµÑÐµÐ½
exception.forbidden.promocode.save.user-conflict=Ð¡Ð¾ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´Ð°: Ð½ÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ ÑÐºÐ°Ð·Ð°ÑÑ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð¸ Ð³ÑÑÐ¿Ð¿Ñ Ð¾Ð´Ð½Ð¾Ð²ÑÐµÐ¼ÐµÐ½Ð½Ð¾
exception.forbidden.promocode.save.name=Ð¡Ð¾ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´Ð°: Ð½Ðµ ÑÐºÐ°Ð·Ð°Ð½Ð¾ Ð½Ð°Ð·Ð²Ð°Ð½Ð¸Ðµ
exception.forbidden.promocode.save.many-types=Ð¡Ð¾ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´Ð°: ÑÐ¾Ð»ÑÐºÐ¾ Ð¾Ð´Ð½Ð¾ Ð¸Ð· Ð¿Ð¾Ð»ÐµÐ¹ 'Ð¿ÑÐ¾ÑÐµÐ½Ñ' / 'ÑÑÐ¼Ð¼Ð°' Ð´Ð¾Ð»Ð¶Ð½Ð¾ Ð±ÑÑÑ ÑÐºÐ°Ð·Ð°Ð½Ð¾
exception.forbidden.promocode.save.no-types=Ð¡Ð¾ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´Ð°: Ð½Ð¸ Ð¾Ð´Ð½Ð¾ Ð¸Ð· Ð¿Ð¾Ð»ÐµÐ¹ 'Ð¿ÑÐ¾ÑÐµÐ½Ñ' / 'ÑÑÐ¼Ð¼Ð°' Ð½Ðµ ÑÐºÐ°Ð·Ð°Ð½Ð¾
exception.forbidden.promocode.save.percent=Ð¡Ð¾ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´Ð°: ÑÐºÐ¸Ð´ÐºÐ° Ð² Ð¿ÑÐ¾ÑÐµÐ½ÑÐ°Ñ Ð´Ð¾Ð»Ð¶Ð½Ð° Ð±ÑÑÑ Ð² Ð¸Ð½ÑÐµÑÐ²Ð°Ð»Ðµ 0 .. 100
exception.forbidden.promocode.save.amount=Ð¡Ð¾ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´Ð°: ÑÑÐ¼Ð¼Ð° ÑÐºÐ¸Ð´ÐºÐ¸ Ð´Ð¾Ð»Ð¶Ð½Ð° Ð±ÑÑÑ Ð±Ð¾Ð»ÑÑÐµ Ð½ÑÐ»Ñ
exception.forbidden.promocode.save.order-amount=Ð¡Ð¾ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´Ð°: Ð¼Ð¸Ð½Ð¸Ð¼Ð°Ð»ÑÐ½Ð°Ñ ÑÑÐ¼Ð¼Ð° Ð·Ð°ÐºÐ°Ð·Ð° Ð´Ð¾Ð»Ð¶Ð½Ð° Ð±ÑÑÑ Ð±Ð¾Ð»ÑÑÐµ Ð½ÑÐ»Ñ
exception.forbidden.promocode.save.date=ÐÐ°ÑÐ° Ð¾ÐºÐ¾Ð½ÑÐ°Ð½Ð¸Ñ Ð´ÐµÐ¹ÑÑÐ²Ð¸Ñ Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´Ð° Ð´Ð¾Ð»Ð¶Ð½Ð° Ð±ÑÑÑ Ð±Ð¾Ð»ÑÑÐµ Ð´Ð°ÑÑ Ð½Ð°ÑÐ°Ð»Ð°
exception.forbidden.promocode.save.relation=ÐÑÐ±Ð»Ð¸ÑÑÐµÑÑÑ ÑÑÐ»Ð¾Ð²Ð¸Ðµ ÑÐ¸Ð»ÑÑÑÐ¾Ð²
exception.not-found.position-order=ÐÐ¾Ð·Ð¸ÑÐ¸Ñ Ð·Ð°ÐºÐ°Ð·Ð° Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð°: {0}
exception.not-found.notification=Ð£Ð²ÐµÐ´Ð¾Ð¼Ð»ÐµÐ½Ð¸Ðµ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð¾
exception.not-found.order=ÐÐ°ÐºÐ°Ð· Ñ Ð½Ð¾Ð¼ÐµÑÐ¾Ð¼: {0} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
exception.not-found.user=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ñ clientId: {0} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½!
exception.not-found.user.phone=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ñ Ð½Ð¾Ð¼ÐµÑÐ¾Ð¼ ÑÐµÐ»ÐµÑÐ¾Ð½Ð°: {0} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½!
exception.not-found.current-user=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½!
exception.not-found.invoice=ÐÐ°ÐºÐ»Ð°Ð´Ð½Ð°Ñ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð°: {0}
exception.not-found.product=Ð¢Ð¾Ð²Ð°Ñ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½: {0}
exception.not-found.saleRequestGrid=ÐÐµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð° ÐºÐ¾Ð¼Ð¸ÑÑÐ¸Ð¾Ð½Ð½Ð°Ñ ÑÐµÑÐºÐ° Ð´Ð»Ñ ÑÐ¾Ð²Ð°ÑÐ¾Ð² ÐºÐ¾Ð½ÑÑÐµÑÐ¶Ð° Ð´Ð»Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°!
exception.not-found.user-common-tag=Ð¢ÐµÐ³ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½: {0}
exception.not-found.user-common-tag-group=ÐÑÑÐ¿Ð¿Ð° ÑÑÐ³Ð¾Ð² Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð°: {0}
exception.too-many-found.user.phone=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÐµÐ¹ Ñ ÑÐµÐ»ÐµÑÐ¾Ð½Ð¾Ð¼: {0} Ð±Ð¾Ð»ÐµÐµ ÑÐµÐ¼ Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾!
exception.too-many-found.unverified-user.phone=ÐÐµÐ²ÐµÑÐ¸ÑÐ¸ÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð½ÑÑ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÐµÐ¹ Ñ ÑÐµÐ»ÐµÑÐ¾Ð½Ð¾Ð¼: {0} Ð±Ð¾Ð»ÐµÐµ Ð¾Ð´Ð½Ð¾Ð³Ð¾!
exception.too-many-found.verified-user.phone=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÐµÐ¹ Ñ ÑÐµÐ»ÐµÑÐ¾Ð½Ð¾Ð¼: {0} Ð±Ð¾Ð»ÐµÐµ Ð¾Ð´Ð½Ð¾Ð³Ð¾!
exception.notification.dto=ÐÐµ Ð¿ÐµÑÐµÐ´Ð°Ð½Ð¾ DTO ÑÐ²ÐµÐ´Ð¾Ð¼Ð»ÐµÐ½Ð¸Ñ
exception.notification.class=ÐÐµ Ð¿ÐµÑÐµÐ´Ð°Ð½Ð¾ Ð¸Ð¼Ñ ÐºÐ»Ð°ÑÑÐ°
exception.notification.class-param=ÐÐµ Ð½Ð°Ð¹Ð´ÐµÐ½ ÐºÐ»Ð°ÑÑ {0}
exception.notification.invalidate-packet=ÐÐµÐ²ÐµÑÐ½ÑÐ¹ Ð¿Ð°ÐºÐµÑ
exception.notification.target-id-empty=ÐÐµ Ð·Ð°Ð´Ð°Ð½ ID Ð¿Ð¾Ð»ÑÑÐ°ÑÐµÐ»Ñ
exception.notification.instance-create-access-denied=ÐÑÐ¸Ð±ÐºÐ° Ð´Ð¾ÑÑÑÐ¿Ð° Ð¿ÑÐ¸ ÑÐ¾Ð·Ð´Ð°Ð½Ð¸Ð¸ Ð¸Ð½ÑÑÐ°Ð½ÑÐ° {0} {1}
exception.oauth.apple-jwt=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ ÑÐ¾Ð·Ð´Ð°ÑÑ JWT Ð´Ð»Ñ Apple
exception.oauth.create-url-apple-profile=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ ÑÐ¾Ð·Ð´Ð°ÑÑ ÑÑÑÐ»ÐºÑ Ð´Ð»Ñ Ð·Ð°Ð¿ÑÐ¾ÑÐ° Ð¿ÑÐ¾ÑÐ¸Ð»Ñ Ð² Apple
exception.oauth.collect-request-parameters-apple=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ ÑÐ¾Ð±ÑÐ°ÑÑ Ð¿Ð°ÑÐ°Ð¼ÐµÑÑÑ Ð·Ð°Ð¿ÑÐ¾ÑÐ° Ðº Apple
exception.oauth.parse-apple=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ ÑÐ°ÑÐ¿Ð°ÑÑÐ¸ÑÑ appleTokenResponse Ð¾ÑÐ²ÐµÑ Ð¾Ñ Apple
exception.oauth.waiting-limit-exceeded-apple=ÐÑÐµÐ²ÑÑÐµÐ½ Ð»Ð¸Ð¼Ð¸Ñ Ð¾Ð¶Ð¸Ð´Ð°Ð½Ð¸Ñ ÑÐ¾ÐµÐ´Ð¸Ð½ÐµÐ½Ð¸Ñ Ñ Apple!
exception.oauth.waiting-limit-exceeded-fb=ÐÑÐµÐ²ÑÑÐµÐ½ Ð»Ð¸Ð¼Ð¸Ñ Ð¾Ð¶Ð¸Ð´Ð°Ð½Ð¸Ñ ÑÐ¾ÐµÐ´Ð¸Ð½ÐµÐ½Ð¸Ñ Ñ Facebook!
exception.oauth.waiting-limit-exceeded-vk=ÐÑÐµÐ²ÑÑÐµÐ½ Ð»Ð¸Ð¼Ð¸Ñ Ð¾Ð¶Ð¸Ð´Ð°Ð½Ð¸Ñ ÑÐ¾ÐµÐ´Ð¸Ð½ÐµÐ½Ð¸Ñ Ñ Vk!
exception.oauth.getting-data-error-apple=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¿Ð¾Ð»ÑÑÐ¸ÑÑ Ð´Ð°Ð½Ð½ÑÐµ Ð¾Ñ Apple
exception.oauth.getting-data-error-vk=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¿Ð¾Ð»ÑÑÐ¸ÑÑ Ð´Ð°Ð½Ð½ÑÐµ Ð¾Ñ Vk!
exception.oauth.getting-data-error-fb=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¿Ð¾Ð»ÑÑÐ¸ÑÑ Ð´Ð°Ð½Ð½ÑÐµ Ð¾Ñ Facebook
exception.oauth.unable-log-in-apple=ÐÐµ ÑÐ´Ð°ÐµÑÑÑ Ð°Ð²ÑÐ¾ÑÐ¸Ð·Ð¾Ð²Ð°ÑÑÑÑ Ð² Apple: {0}
exception.oauth.unable-log-in-fb=ÐÐµ ÑÐ´Ð°ÐµÑÑÑ Ð°Ð²ÑÐ¾ÑÐ¸Ð·Ð¾Ð²Ð°ÑÑÑÑ Ð² Facebook: {0}
exception.oauth.unable-log-in-vk=ÐÐµ ÑÐ´Ð°ÐµÑÑÑ Ð°Ð²ÑÐ¾ÑÐ¸Ð·Ð¾Ð²Ð°ÑÑÑÑ Ð² VK: {0}
exception.oauth.token-response-apple=ÐÐµÑ appleTokenResponse, Ð½Ð¾ Ð·Ð°Ð¿ÑÐ¾Ñ Ð±ÑÐ» ÑÑÐ¿ÐµÑÐ½ÑÐ¼!
exception.oauth.unable-get-apple-key=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¿Ð¾Ð»ÑÑÐ¸ÑÑ apple public keys
exception.oauth.invalid-wrapped-apple-id-token=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¿Ð¾Ð»ÑÑÐ¸ÑÑ apple public keys
exception.oauth.token-response-fb=ÐÐµÑ facebookResponse, Ð½Ð¾ Ð·Ð°Ð¿ÑÐ¾Ñ Ð±ÑÐ» ÑÑÐ¿ÐµÑÐ½ÑÐ¼!
exception.oauth.token-response-vk=ÐÐµÑ vkUserInfo, Ð½Ð¾ Ð·Ð°Ð¿ÑÐ¾Ñ Ð±ÑÐ» ÑÑÐ¿ÐµÑÐ½ÑÐ¼!
exception.oauth.parse-id-token-apple=ÐÑÐ¾Ð¸Ð·Ð¾ÑÐ»Ð° Ð¾ÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð¿Ð°ÑÑÐ¸Ð½Ð³Ðµ idToken Ð¾Ñ Apple
exception.oauth.unable-create-url-fb=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ ÑÐ¾Ð·Ð´Ð°ÑÑ ÑÑÑÐ»ÐºÑ Ð´Ð»Ñ Ð·Ð°Ð¿ÑÐ¾ÑÐ° Ð¿ÑÐ¾ÑÐ¸Ð»Ñ Ð² FB
exception.oauth.unable-create-url-vk=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ ÑÐ¾Ð·Ð´Ð°ÑÑ ÑÑÑÐ»ÐºÑ Ð´Ð»Ñ Ð·Ð°Ð¿ÑÐ¾ÑÐ° Ð¸Ð½ÑÐ¾ Ð² VK
exception.oauth.user-not-found-apple=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ñ Apple ID Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
exception.oauth.user-not-found-fb=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ñ Facebook ID Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
exception.oauth.user-not-found-vk=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ñ VK ID Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
exception.oauth.google.id-token-null=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¿Ð¾Ð»ÑÑÐ¸ÑÑ Ð´Ð°Ð½Ð½ÑÐµ Ð¾Ñ Google
exception.oauth.google.verify-error=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¿Ð¾Ð»ÑÑÐ¸ÑÑ Ð´Ð°Ð½Ð½ÑÐµ Ð¾Ñ Google
exception.order-creation.position-seller-not-found=ÐÐ¾Ð·Ð¸ÑÐ¸Ð¸ Ð¿Ð¾ ÑÐºÐ°Ð·Ð°Ð½Ð½Ð¾Ð¼Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÑ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ñ: {0}
exception.order-creation.cant-creating=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¾ÑÐ¾ÑÐ¼Ð»ÐµÐ½
exception.order-creation.cant-creating-reason=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¾ÑÐ¾ÑÐ¼Ð»ÐµÐ½. \nÐÑÐ¸ÑÐ¸Ð½Ð°: {0}. \nÐÐ°ÑÐµÐ³Ð¾ÑÐ¸Ñ: {1}. \nÐ¢Ð¾Ð²Ð°ÑÑ, ÐºÐ¾ÑÐ¾ÑÑÐµ Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾ ÑÐ±ÑÐ°ÑÑ Ð¸Ð· ÐºÐ¾ÑÐ·Ð¸Ð½Ñ: {2}
exception.oskelly.position-not-found=ÐÐµ ÑÑÑÐµÑÑÐ²ÑÐµÑ Ð¿Ð¾Ð·Ð¸ÑÐ¸Ð¸ Ñ id: {0}
exception.oskelly.yr-need-requisite=Ð®ÑÐ¸Ð´Ð¸ÑÐµÑÐºÐ¾Ð¼Ñ Ð»Ð¸ÑÑ Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾ Ð²ÑÐ±ÑÐ°ÑÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð´Ð»Ñ Ð²ÑÐ¿Ð»Ð°ÑÑ ÑÑÐµÐ´ÑÑÐ².
exception.oskelly.position-need-mark-code=ÐÐ»Ñ Ð¿Ð¾Ð·Ð¸ÑÐ¸Ð¹: {0} Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾ Ð´Ð¾Ð±Ð°Ð²Ð¸ÑÑ Ð¼Ð°ÑÐºÐ¸ÑÐ¾Ð²Ð¾ÑÐ½ÑÐµ ÐºÐ¾Ð´Ñ.
exception.oskelly.user-not-set-for-address=ÐÐµ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð´Ð»Ñ ÑÐ¾ÑÑÐ°Ð½ÐµÐ½Ð¸Ñ Ð°Ð´ÑÐµÑÐ¾Ð²
exception.oskelly.user-not-set-contract-details=ÐÐµ ÑÐºÐ°Ð·Ð°Ð½Ñ Ð½Ð¾Ð¼ÐµÑ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ° Ð¸Ð»Ð¸ Ð´Ð°ÑÐ° Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ°
exception.oskelly.not-has-address-save=ÐÐµÑ Ð°Ð´ÑÐµÑÐ¾Ð² Ð´Ð»Ñ ÑÐ¾ÑÑÐ°Ð½ÐµÐ½Ð¸Ñ
exception.oskelly.international.city-is-not-set=ÐÐ¾ÑÐ¾Ð´ Ð´Ð¾Ð»Ð¶ÐµÐ½ Ð±ÑÑÑ Ð²ÑÐ±ÑÐ°Ð½ Ð¸Ð· ÑÐ¿Ð¸ÑÐºÐ°
exception.oskelly.international.country-is-not-set=Ð¡ÑÑÐ°Ð½Ð° Ð´Ð¾Ð»Ð¶Ð½Ð° Ð±ÑÑÑ Ð²ÑÐ±ÑÐ°Ð½ Ð¸Ð· ÑÐ¿Ð¸ÑÐºÐ°
exception.oskelly.international.billing.address.city-is-not-set=ÐÐ¾ÑÐ¾Ð´ Ð´Ð¾Ð»Ð¶ÐµÐ½ Ð±ÑÑÑ Ð²ÑÐ±ÑÐ°Ð½ Ð¸Ð· ÑÐ¿Ð¸ÑÐºÐ° (ÐÐ´ÑÐµÑ Ð´Ð»Ñ Ð²ÑÑÑÐ°Ð²Ð»ÐµÐ½Ð¸Ñ ÑÑÐµÑÐ°)
exception.oskelly.international.billing.address.country-is-not-set=Ð¡ÑÑÐ°Ð½Ð° Ð´Ð¾Ð»Ð¶Ð½Ð° Ð±ÑÑÑ Ð²ÑÐ±ÑÐ°Ð½ Ð¸Ð· ÑÐ¿Ð¸ÑÐºÐ° (ÐÐ´ÑÐµÑ Ð´Ð»Ñ Ð²ÑÑÑÐ°Ð²Ð»ÐµÐ½Ð¸Ñ ÑÑÐµÑÐ°)
exception.oskelly.international.billing.address.not-has-address-save=ÐÐµÑ Ð°Ð´ÑÐµÑÐ¾Ð² Ð´Ð»Ñ ÑÐ¾ÑÑÐ°Ð½ÐµÐ½Ð¸Ñ (ÐÐ´ÑÐµÑ Ð´Ð»Ñ Ð²ÑÑÑÐ°Ð²Ð»ÐµÐ½Ð¸Ñ ÑÑÐµÑÐ°)
exception.oskelly.international.billing.address.zipcode-not-found=ÐÑÑÑÑÑÑÐ²ÑÐµÑ Ð¿Ð¾ÑÑÐ¾Ð²ÑÐ¹ Ð¸Ð½Ð´ÐµÐºÑ (ÐÐ´ÑÐµÑ Ð´Ð»Ñ Ð²ÑÑÑÐ°Ð²Ð»ÐµÐ½Ð¸Ñ ÑÑÐµÑÐ°)
exception.oskelly.international.legal.address.no-country-in-legal-address=ÐÐµ Ð·Ð°Ð¿Ð¾Ð»Ð½ÐµÐ½Ð° ÑÑÑÐ°Ð½Ð° Ð² ÑÑÐ¸Ð´Ð¸ÑÐµÑÐºÐ¾Ð¼ Ð°Ð´ÑÐµÑÐµ
exception.oskelly.international.legal.address.not-found=ÐÐµ ÑÐºÐ°Ð·Ð°Ð½ Ð®ÑÐ¸Ð´Ð¸ÑÐµÑÐºÐ¸Ð¹ Ð°Ð´ÑÐµÑ
exception.oskelly.international.legal.address.city-is-not-set=ÐÐ¾ÑÐ¾Ð´ Ð´Ð¾Ð»Ð¶ÐµÐ½ Ð±ÑÑÑ Ð²ÑÐ±ÑÐ°Ð½ Ð¸Ð· ÑÐ¿Ð¸ÑÐºÐ° (Ð®ÑÐ¸Ð´Ð¸ÑÐµÑÐºÐ¸Ð¹ Ð°Ð´ÑÐµÑ)
exception.oskelly.international.legal.address.country-is-not-set=Ð¡ÑÑÐ°Ð½Ð° Ð´Ð¾Ð»Ð¶Ð½Ð° Ð±ÑÑÑ Ð²ÑÐ±ÑÐ°Ð½ Ð¸Ð· ÑÐ¿Ð¸ÑÐºÐ° (Ð®ÑÐ¸Ð´Ð¸ÑÐµÑÐºÐ¸Ð¹ Ð°Ð´ÑÐµÑ)
exception.oskelly.international.legal.address.not-has-address-save=ÐÐµÑ Ð°Ð´ÑÐµÑÐ¾Ð² Ð´Ð»Ñ ÑÐ¾ÑÑÐ°Ð½ÐµÐ½Ð¸Ñ (Ð®ÑÐ¸Ð´Ð¸ÑÐµÑÐºÐ¸Ð¹ Ð°Ð´ÑÐµÑ)
exception.oskelly.international.legal.address.zipcode-not-found=ÐÑÑÑÑÑÑÐ²ÑÐµÑ Ð¿Ð¾ÑÑÐ¾Ð²ÑÐ¹ Ð¸Ð½Ð´ÐµÐºÑ (Ð®ÑÐ¸Ð´Ð¸ÑÐµÑÐºÐ¸Ð¹ Ð°Ð´ÑÐµÑ)
exception.oskelly.counteragent-requisite-not-have=ÐÐµÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÐ¾Ð² ÐºÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½ÑÐ° Ð´Ð»Ñ ÑÐ¾ÑÑÐ°Ð½ÐµÐ½Ð¸Ñ
exception.oskelly.user-not-set-for-role=ÐÐµ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð´Ð»Ñ ÑÐ¾ÑÑÐ°Ð½ÐµÐ½Ð¸Ñ ÑÐ¾Ð»Ð¸
exception.oskelly.user-not-set-for-sex=ÐÐµ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð´Ð»Ñ ÑÐ¾ÑÑÐ°Ð½ÐµÐ½Ð¸Ñ Ð¿Ð¾Ð»Ð°
exception.oskelly.user-not-set-for-requisite=ÐÐµ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð´Ð»Ñ ÑÐ¾ÑÑÐ°Ð½ÐµÐ½Ð¸Ñ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÐ¾Ð²
exception.oskelly.user-not-set-for-social=ÐÐµ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð´Ð»Ñ ÑÐ¾ÑÑÐ°Ð½ÐµÐ½Ð¸Ñ ÑÐ¾ÑÐ¸Ð°Ð»ÑÐ½ÑÑ Ð°ÐºÐºÐ°ÑÐ½ÑÐ¾Ð²
exception.oskelly.data-not-have-for-role=ÐÐµÑ Ð´Ð°Ð½Ð½ÑÑ Ð´Ð»Ñ ÑÑÑÐ°Ð½Ð¾Ð²ÐºÐ¸ ÑÐ¾Ð»Ð¸
exception.oskelly.data-not-have-for-social=ÐÐµÑ Ð´Ð°Ð½Ð½ÑÑ Ð´Ð»Ñ ÑÑÑÐ°Ð½Ð¾Ð²ÐºÐ¸ ÑÐ¾ÑÐ¸Ð°Ð»ÑÐ½ÑÑ Ð°ÐºÐºÐ°ÑÐ½ÑÐ¾Ð²
exception.oskelly.data-not-have-for-address=ÐÐµÑ Ð°Ð´ÑÐµÑÑÐ¾Ð² Ð´Ð»Ñ ÑÐ¾ÑÑÐ°Ð½ÐµÐ½Ð¸Ñ
exception.oskelly.data-not-have-for-requsite=ÐÐµÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÐ¾Ð² Ð´Ð»Ñ ÑÐ¾ÑÑÐ°Ð½ÐµÐ½Ð¸Ñ
exception.oskelly.incorrect-requsite-condition-active-delete=ÐÐµÐ»ÑÐ·Ñ ÑÐ´ÐµÐ»Ð°ÑÑ ÑÐ´Ð°Ð»ÐµÐ½Ð½ÑÐ¹ ÑÐµÐºÐ²Ð¸Ð·Ð¸Ñ Ð°ÐºÑÐ¸Ð²Ð½ÑÐ¼(ÑÐµÐºÑÑÐ¸Ð¼)
exception.oskelly.incorrect-zipcode=ÐÐ¾ÑÑÐ¾Ð²ÑÐ¹ Ð¸Ð½Ð´ÐµÐºÑ ÑÐºÐ°Ð·Ð°Ð½ Ð½ÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ð¾: {0}
exception.address.incorrect-fias-id-format=ÐÐ´ÑÐµÑ {0}: Ð½ÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐµ Ð´Ð°Ð½Ð½ÑÐµ Ð¤ÐÐÐ¡ ID: {1}
exception.oskelly.zipcode-not-found=ÐÑÑÑÑÑÑÐ²ÑÐµÑ Ð¿Ð¾ÑÑÐ¾Ð²ÑÐ¹ Ð¸Ð½Ð´ÐµÐºÑ
exception.oskelly.address-field-is-null-or-empty=ÐÐ´ÑÐµÑÐ½Ð¾Ðµ Ð¿Ð¾Ð»Ðµ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¿ÑÑÑÑÐ¼
exception.oskelly.address-country-not-supported=Ð¡ÑÑÐ°Ð½Ð° {0} (ID: {1}) Ð½Ðµ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÐµÑÑÑ Ð½Ð¸ Ð¾Ð´Ð½Ð¾Ð¹ ÐºÑÑÑÐµÑÑÐºÐ¾Ð¹ ÑÐ»ÑÐ¶Ð±Ð¾Ð¹
exception.oskelly.address-city-not-supported=ÐÐ¾ÑÐ¾Ð´ {0} (ID: {1}) Ð½Ðµ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÐµÑÑÑ Ð½Ð¸ Ð¾Ð´Ð½Ð¾Ð¹ ÐºÑÑÑÐµÑÑÐºÐ¾Ð¹ ÑÐ»ÑÐ¶Ð±Ð¾Ð¹
exception.oskelly.attribute-color-not-set=ÐÑÑÐ¸Ð±ÑÑ ÑÐ²ÐµÑÐ° Ð½Ðµ Ð·Ð°Ð´Ð°Ð½
exception.oskelly.unavailable-channel-notification=ÐÐµÐ´Ð¾ÑÑÑÐ¿Ð½ÑÐ¹ ÐºÐ°Ð½Ð°Ð» Ð´Ð»Ñ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸ ÑÐ²ÐµÐ´Ð¾Ð¼Ð»ÐµÐ½Ð¸Ð¹: {0}
exception.oskelly.current-order-empty=ÐÐµÑ Ð´ÐµÐ¹ÑÑÐ²ÑÑÑÐµÐ³Ð¾ Ð·Ð°ÐºÐ°Ð·Ð° {0}
exception.oskelly.code-mark-empty=ÐÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾ Ð·Ð°Ð¿Ð¾Ð»Ð½Ð¸ÑÑ ÐºÐ¾Ð´ Ð¼Ð°ÑÐºÐ¸ÑÐ¾Ð²ÐºÐ¸ Ð´Ð»Ñ ÑÐ¾Ð²Ð°ÑÐ°: {0}
exception.oskelly.order-best2pay-error=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ ÑÐµÐ³Ð¸ÑÑÑÐ°ÑÐ¸Ð¸ Ð·Ð°ÐºÐ°Ð·Ð° Ð² Best2pay {0}
exception.oskelly.pay-tcb-error=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð¾Ð¿Ð»Ð°ÑÐµ Ð·Ð°ÐºÐ°Ð·Ð° {0} (Ð¢ÐÐ), Ð¿Ð¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¿Ð¾Ð²ÑÐ¾ÑÐ¸ÑÐµ Ð¿Ð¾Ð¿ÑÑÐºÑ Ð¿Ð¾Ð·Ð¶Ðµ
exception.oskelly.pay-general-error=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð¾Ð¿Ð»Ð°ÑÐµ Ð·Ð°ÐºÐ°Ð·Ð° {0} ({1}), Ð¿Ð¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¿Ð¾Ð²ÑÐ¾ÑÐ¸ÑÐµ Ð¿Ð¾Ð¿ÑÑÐºÑ Ð¿Ð¾Ð·Ð¶Ðµ
exception.oskelly.pay-bonuses-and-promocode=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð¾Ð¿Ð»Ð°ÑÐµ Ð·Ð°ÐºÐ°Ð·Ð° {0}. ÐÐµÐ»ÑÐ·Ñ Ð¾Ð´Ð½Ð¾Ð²ÑÐµÐ¼ÐµÐ½Ð½Ð¾ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ Ð±Ð¾Ð½ÑÑÑ Ð¸ Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´.
exception.oskelly.pay-bonuses-incorrect-sum=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¾Ð²ÐµÑÐºÐ¸ ÑÑÐ¼Ð¼ Ð·Ð°ÐºÐ°Ð·Ð° (ÐºÐ¾Ð´ NOA)
exception.oskelly.pay-bonuses-unexpected=ÐÐµÐ¿ÑÐµÐ´Ð²Ð¸Ð´ÐµÐ½Ð½Ð°Ñ Ð¾ÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ ÑÐ°ÑÐ¿ÑÐµÐ´ÐµÐ»ÐµÐ½Ð¸Ð¸ Ð±Ð¾Ð½ÑÑÐ¾Ð² Ð¿ÑÐ¸ Ð¾Ð¿Ð»Ð°ÑÐµ Ð·Ð°ÐºÐ°Ð·Ð° {0}
exception.oskelly.pay-sum-negative=Ð­ÑÑÐµÐºÑÐ¸Ð²Ð½Ð°Ñ ÑÑÐ¼Ð¼Ð° Ñ ÑÑÐµÑÐ¾Ð¼ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ Ð¿Ð¾ Ð¿Ð¾Ð·Ð¸ÑÐ¸Ð¸ {0} Ð·Ð°ÐºÐ°Ð·Ð° {1} Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¾ÑÑÐ¸ÑÐ°ÑÐµÐ»ÑÐ½Ð¾Ð¹ {2} 
exception.oskelly.tcb-response-error=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð¿Ð¾Ð»ÑÑÐµÐ½Ð¸Ð¸ Ð¾ÑÐ²ÐµÑÐ° Ð¾Ñ TCB Ð±Ð°Ð½ÐºÐ° Ð¿ÑÐ¸ Ð¾Ð¿Ð»Ð°ÑÐµ Ð·Ð°ÐºÐ°Ð·Ð° {0}
exception.oskelly.tcb-partial-refund-error=ÐÐ»Ñ Ð·Ð°ÐºÐ°Ð·Ð° {0} Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½ PARTIAL_REFUND Ðº TCB. ÐÑÐ¸Ð±ÐºÐ°: {1}
exception.oskelly.tcb-refund-error=ÐÐ»Ñ Ð·Ð°ÐºÐ°Ð·Ð° {0} Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½ REFUND Ðº TCB. ÐÑÐ¸Ð±ÐºÐ°: {1}
exception.oskelly.conversion-need-parameter=ÐÐ»Ñ ÐºÐ¾Ð½Ð²ÐµÑÑÐ¸Ð¸ Ð´Ð¾Ð»Ð¶ÐµÐ½ Ð±ÑÑ Ð·Ð°Ð´Ð°Ð½ Ð¾Ð´Ð¸Ð½ Ð¸ ÑÐ¾Ð»ÑÐºÐ¾ Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð´Ð²ÑÑ Ð²Ð¾Ð·Ð¼Ð¾Ð¶Ð½ÑÑ Ð¿Ð°ÑÐ°Ð¼ÐµÑÑÐ¾Ð².
exception.oskelly.date-format-error=ÐÐµÐ²ÐµÑÐ½ÑÐ¹ ÑÐ¾ÑÐ¼Ð°Ñ Ð´Ð°ÑÑ
exception.password.empty=ÐÐµ Ð²Ð²ÐµÐ´ÐµÐ½ ÑÐµÐºÑÑÐ¸Ð¹ Ð¿Ð°ÑÐ¾Ð»Ñ
exception.password.incorrect-password=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ ÑÐµÐºÑÑÐ¸Ð¹ Ð¿Ð°ÑÐ¾Ð»Ñ
exception.password.token-not-found=Ð¢Ð¾ÐºÐµÐ½ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½: {0}
exception.phone.missing=ÐÑÑÑÑÑÑÐ²ÑÐµÑ ÑÐµÐ»ÐµÑÐ¾Ð½Ð½ÑÐ¹ Ð½Ð¾Ð¼ÐµÑ.
exception.phone.incorrect-format=ÐÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½ÑÐ¹ ÑÐ¾ÑÐ¼Ð°Ñ ÑÐµÐ»ÐµÑÐ¾Ð½Ð½Ð¾Ð³Ð¾ Ð½Ð¾Ð¼ÐµÑÐ°.
exception.phone.basket-size-limit=ÐÑÐµÐ²ÑÑÐµÐ½ Ð¼Ð°ÐºÑÐ¸Ð¼Ð°Ð»ÑÐ½ÑÐ¹ ÑÐ°Ð·Ð¼ÐµÑ ÐºÐ¾ÑÐ·Ð¸Ð½Ñ: {0} ÑÐ¾Ð²Ð°ÑÐ¾Ð²
exception.product.unavailable-see=Ð¢Ð¾Ð²Ð°Ñ #{0} Ð½ÐµÐ´Ð¾ÑÑÑÐ¿ÐµÐ½ Ð´Ð»Ñ Ð¿ÑÐ¾ÑÐ¼Ð¾ÑÑÐ° ÑÐµÐºÑÑÐ¸Ð¼ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÐµÐ¼
exception.product.inconsistent-price-params=ÐÐµÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ð½ÑÐµ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ñ ÑÐµÐ½
exception.product.no-authority-for-custom-commission=ÐÐµÐ´Ð¾ÑÑÐ°ÑÐ¾ÑÐ½Ð¾ Ð¿ÑÐ°Ð² Ð´Ð»Ñ ÑÐ¼ÐµÐ½Ñ Ð¿ÑÐ¸Ð·Ð½Ð°ÐºÐ° "ÐºÐ°ÑÑÐ¾Ð¼Ð½Ð°Ñ ÐºÐ¾Ð¼Ð¸ÑÑÐ¸Ñ"
exception.product.by-sale-request.editing-not-allowed-for-seller=Ð ÐµÐ´Ð°ÐºÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ ÑÐ¾Ð²Ð°ÑÐ°, ÑÐ¾Ð·Ð´Ð°Ð½Ð½Ð¾Ð³Ð¾ Ð¿Ð¾ Ð·Ð°ÑÐ²ÐºÐµ ÐºÐ¾Ð½ÑÑÐµÑÐ¶Ð° Ð´Ð»Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð², Ð½ÐµÐ´Ð¾ÑÑÑÐ¿Ð½Ð¾
exception.product.by-sale-request.deletion-not-allowed-for-seller=Ð£Ð´Ð°Ð»ÐµÐ½Ð¸Ðµ ÑÐ¾Ð²Ð°ÑÐ°, ÑÐ¾Ð·Ð´Ð°Ð½Ð½Ð¾Ð³Ð¾ Ð¿Ð¾ Ð·Ð°ÑÐ²ÐºÐµ ÐºÐ¾Ð½ÑÑÐµÑÐ¶Ð° Ð´Ð»Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð², Ð½ÐµÐ´Ð¾ÑÑÑÐ¿Ð½Ð¾
exception.product.by-sale-request.current-price-not-specified=ÐÐµ ÑÐºÐ°Ð·Ð°Ð½Ð° ÑÐµÐ½Ð° Ñ ÐºÐ¾Ð¼Ð¸ÑÑÐ¸ÐµÐ¹ Ð´Ð»Ñ Ð¿ÑÐ¾Ð´ÑÐºÑÐ° {0}
exception.product.by-sale-request.seller-price-not-specified=ÐÐµ ÑÐºÐ°Ð·Ð°Ð½Ð° ÑÐµÐ½Ð° Ð¿Ð¾ÑÐ»Ðµ Ð²ÑÑÐµÑÐ° ÐºÐ¾Ð¼Ð¸ÑÑÐ¸Ð¸ Ð´Ð»Ñ Ð¿ÑÐ¾Ð´ÑÐºÑÐ° {0}
exception.product.exclusive-lot.unavailable=Ð¢Ð¾Ð²Ð°Ñ Ð´Ð¾ÑÑÑÐ¿ÐµÐ½ Ð´Ð»Ñ Ð¿ÑÐ¾ÑÐ¼Ð¾ÑÑÐ° ÑÐ¾Ð»ÑÐºÐ¾ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÑÐ¼ Ñ ÑÑÐ¾Ð²Ð½ÐµÐ¼ Black
exception.sale-request.double-by-bitrix-deal-id=Ð ÑÐ¸ÑÑÐµÐ¼Ðµ ÑÐ¶Ðµ Ð¸Ð¼ÐµÑÑÑÑ Ð·Ð°ÑÐ²ÐºÐ¸ Ñ ÑÐºÐ°Ð·Ð°Ð½Ð½ÑÐ¼ Ð¸Ð´ÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¾ÑÐ¾Ð¼ ÑÐ´ÐµÐ»ÐºÐ¸ Ð² ÐÐ¸ÑÑÐ¸ÐºÑ: {0}
exception.return.order-not-found=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
exception.return.order-access-denied=Ð£ Ð²Ð°Ñ Ð½ÐµÑ Ð¿ÑÐ°Ð² Ð½Ð° ÑÐ¾Ð·Ð´Ð°Ð½Ð¸Ðµ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐ° Ð¿Ð¾ Ð´Ð°Ð½Ð½Ð¾Ð¼Ñ Ð·Ð°ÐºÐ°Ð·Ñ
exception.return.invalidate-status=Ð¡ÑÐ°ÑÑÑ Ð·Ð°ÐºÐ°Ð·Ð° Ð½Ðµ Ð¿Ð¾Ð·Ð²Ð¾Ð»ÑÐµÑ Ð¾ÑÐ¾ÑÐ¼Ð¸ÑÑ Ð²Ð¾Ð·Ð²ÑÐ°Ñ
exception.return.already-created=ÐÐ¾Ð·Ð²ÑÐ°Ñ Ð¿Ð¾ Ð´Ð°Ð½Ð½Ð¾Ð¼Ñ Ð·Ð°ÐºÐ°Ð·Ñ ÑÐ¶Ðµ ÑÐ¾Ð·Ð´Ð°Ð²Ð°Ð»ÑÑ
exception.return.position-empty=ÐÐµ Ð¿ÐµÑÐµÐ´Ð°Ð½Ñ Ð¿Ð¾Ð·Ð¸ÑÐ¸Ð¸ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐ°
exception.return.photo-empty=ÐÐµ Ð¿ÐµÑÐµÐ´Ð°Ð½Ð¾ ÑÐ¾ÑÐ¾ Ð¿Ð°ÑÐ¿Ð¾ÑÑÐ°
exception.return.address-point-empty=ÐÐµ Ð¿ÐµÑÐµÐ´Ð°Ð½Ð° Ð°Ð´ÑÐµÑÐ½Ð°Ñ ÑÐ¾ÑÐºÐ°
exception.return.address-point-not-found=ÐÐµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð° Ð°Ð´ÑÐµÑÐ½Ð°Ñ ÑÐ¾ÑÐºÐ°
exception.return.address-point-incorrect=ÐÐµÑÐµÐ´Ð°Ð½Ð° Ð½ÐµÐºÐ¾ÑÑÐµÐºÑÐ½Ð°Ñ Ð°Ð´ÑÐµÑÐ½Ð°Ñ ÑÐ¾ÑÐºÐ°
exception.return.counteragent-empty=ÐÐµ Ð¿ÐµÑÐµÐ´Ð°Ð½ ÐºÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½Ñ
exception.return.counteragent-not-found=ÐÐµ Ð½Ð°Ð¹Ð´ÐµÐ½ ÐºÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½Ñ
exception.return.counteragent-incorrect=ÐÐµÑÐµÐ´Ð°Ð½ Ð½ÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ ÐºÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½Ñ
exception.return.position-id-order-empty=ÐÐµ Ð¿ÐµÑÐµÐ´Ð°Ð½ id Ð¿Ð¾Ð·Ð¸ÑÐ¸Ð¸ Ð·Ð°ÐºÐ°Ð·Ð°
exception.return.position-id-not-found=ÐÐ¾Ð·Ð¸ÑÐ¸Ñ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð° Ð² Ð·Ð°ÐºÐ°Ð·Ðµ
exception.return.reason-empty=ÐÐµ ÑÐºÐ°Ð·Ð°Ð½Ð° Ð¿ÑÐ¸ÑÐ¸Ð½Ð° Ð²Ð¾Ð·Ð²ÑÐ°ÑÐ°
exception.return.photo-tag-empty=ÐÐµ Ð¿ÐµÑÐµÐ´Ð°Ð½Ð¾ ÑÐ¾ÑÐ¾ Ð±Ð¸ÑÐºÐ¸
exception.return.reason-not-found=ÐÑÐ¸ÑÐ¸Ð½Ð° Ð²Ð¾Ð·Ð²ÑÐ°ÑÐ° Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð°: {0}
exception.return.not-found=ÐÐ¾Ð·Ð²ÑÐ°Ñ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
exception.return.access-denied=ÐÑ Ð½Ðµ Ð¸Ð¼ÐµÐµÑÐµ Ð¿ÑÐ°Ð²Ð° Ð½Ð° Ð¿ÑÐ¾ÑÐ¼Ð¾ÑÑ Ð´Ð°Ð½Ð½Ð¾Ð³Ð¾ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐ°
exception.oauth.registration=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð·Ð°ÑÐµÐ³Ð¸ÑÑÑÐ¸ÑÐ¾Ð²Ð°ÑÑ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ. ÐÐµÑÐµÐ´Ð°Ð¹ÑÐµ ÑÐ»ÐµÐ´ÑÑÑÐ¸Ðµ Ð´Ð°Ð½Ð½ÑÐµ: ['fb_token'] Ð¸Ð»Ð¸ ['vk_token' Ð¸ 'vk_user_id'] Ð¸Ð»Ð¸ ['apple_kid' Ð¸ 'apple_authorization_code']
exception.oauth.auth=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð°Ð²ÑÐ¾ÑÐ¸Ð·Ð¾Ð²Ð°ÑÑ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ. ÐÐµÑÐµÐ´Ð°Ð¹ÑÐµ ÑÐ»ÐµÐ´ÑÑÑÐ¸Ðµ Ð´Ð°Ð½Ð½ÑÐµ: fb_token Ð¸Ð»Ð¸ vk_token Ð¸Ð»Ð¸ (apple_kid Ð¸ apple_authorization_code)
exception.file.limit-max=Ð Ð°Ð·Ð¼ÐµÑ ÑÐ°Ð¹Ð»Ð° Ð¿ÑÐµÐ²ÑÑÐ°ÐµÑ {0} MB
exception.file.not-found=Ð¤Ð°Ð¹Ð» Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
exception.file.access-denied=ÐÑ Ð½Ðµ Ð¸Ð¼ÐµÐµÑÐµ Ð¿ÑÐ°Ð² Ð½Ð° Ð´Ð°Ð½Ð½ÑÐ¹ ÑÐ°Ð¹Ð»
exception.validation.page-error=ÐÐµÐ´Ð¾Ð¿ÑÑÑÐ¸Ð¼ÑÐ¹ ÑÐ°Ð·Ð¼ÐµÑ ÑÑÑÐ°Ð½Ð¸ÑÑ: {0}. ÐÐ¾Ð¿ÑÑÐºÐ°ÑÑÑÑ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ñ Ð¾Ñ {1} Ð´Ð¾ {2}
exception.validation.register-error=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð·Ð°ÑÐµÐ³Ð¸ÑÑÑÐ¸ÑÐ¾Ð²Ð°ÑÑ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ
exception.validation.error=ÐÑÐ¸Ð±ÐºÐ° Ð²Ð°Ð»Ð¸Ð´Ð°ÑÐ¸Ð¸
exception.validation.email-empty=E-mail Ð½Ðµ ÑÐºÐ°Ð·Ð°Ð½
exception.validation.password-empty=ÐÐ°ÑÐ¾Ð»Ñ Ð½Ðµ ÑÐºÐ°Ð·Ð°Ð½
exception.validation.user-not-register=ÐÐµ Ð½Ð°ÑÐ»Ð¸ ÑÐ°ÐºÐ¾Ð¹ e-mail. ÐÑÐ¾Ð²ÐµÑÑÑÐµ, ÑÑÐ¾ Ð²Ñ Ð½Ð°Ð¿Ð¸ÑÐ°Ð»Ð¸ Ð¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ð¾
exception.validation.user-already-register-by-email=ÐÑ ÑÐ¶Ðµ ÑÐµÐ³Ð¸ÑÑÑÐ¸ÑÐ¾Ð²Ð°Ð»Ð¸ÑÑ Ñ ÑÑÐ¾Ð¹ Ð¿Ð¾ÑÑÐ¾Ð¹ Ð´ÑÑÐ³Ð¸Ð¼ ÑÐ¿Ð¾ÑÐ¾Ð±Ð¾Ð¼. ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð°Ð²ÑÐ¾ÑÐ¸Ð·ÑÐ¹ÑÐµÑÑ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°Ð½Ð½ÑÐ¼ ÑÐ°Ð½ÐµÐµ ÑÐ¿Ð¾ÑÐ¾Ð±Ð¾Ð¼
exception.user.authority-required=ÐÐ»Ñ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð¸Ñ Ð´ÐµÐ¹ÑÑÐ²Ð¸Ñ ÐÐ°Ð¼ Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾ Ð¾Ð´Ð½Ð¾ Ð¸Ð· ÑÐ»ÐµÐ´ÑÑÑÐ¸Ñ Ð¿ÑÐ°Ð²: {0}
exception.user.at-least-one-authority-required=ÐÐ»Ñ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð¸Ñ Ð´ÐµÐ¹ÑÑÐ²Ð¸Ñ ÐÐ°Ð¼ Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾ ÑÐ¾ÑÑ Ð±Ñ Ð¾Ð´Ð½Ð¾ Ð½Ð°Ð·Ð½Ð°ÑÐµÐ½Ð½Ð¾Ðµ Ð¿ÑÐ°Ð²Ð¾
exception.user.auth=ÐÐµ Ð°Ð²ÑÐ¾ÑÐ¸Ð·Ð¾Ð²Ð°Ð½
exception.user.auth.unexpectedError=ÐÑÐ¸Ð±ÐºÐ° Ð°Ð²ÑÐ¾ÑÐ¸Ð·Ð°ÑÐ¸Ð¸
exception.user.phone.exist=ÐÐ¾Ð¼ÐµÑ ÑÐµÐ»ÐµÑÐ¾Ð½Ð° ÑÐ¶Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ Ð¸ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½
exception.verification.tooManyRequests=Ð¡Ð»Ð¸ÑÐºÐ¾Ð¼ Ð¼Ð½Ð¾Ð³Ð¾ Ð·Ð°Ð¿ÑÐ¾ÑÐ¾Ð²
exception.verification.generate.failed=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð¾ÑÐ¿ÑÐ°Ð²ÐºÐµ ÐºÐ¾Ð´Ð° Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ñ
exception.verification.generate.success=Ð¡ÐÐ¡ Ð±ÑÐ» Ð¾ÑÐ¿ÑÐ°Ð²Ð»ÐµÐ½
exception.city.cargo-not-found=ÐÐ¾ÑÐ¾Ð´ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½ Ð² ÑÐ¸ÑÑÐµÐ¼Ðµ ÐÐÐ ÐÐ. ÐÐ´ÑÐµÑ: ZIP - {0}, Ð¤ÐÐÐ¡ Ð½.Ð¿. - {1}, Ð¤ÐÐÐ¡ Ð³Ð¾ÑÐ¾Ð´Ð° - {2}, Ð³Ð¾ÑÐ¾Ð´ - {3}.
exception.city.not-supported=ÐÐ°ÑÐµÐ»ÐµÐ½Ð½ÑÐ¹ Ð¿ÑÐ½ÐºÑ/ÑÐµÐ³Ð¸Ð¾Ð½ Ð½Ðµ Ð¾Ð±ÑÐ»ÑÐ¶Ð¸Ð²Ð°ÐµÑÑÑ Ð² DALLI. DALLI Ð¾ÑÑÑÐµÑÑÐ²Ð»ÑÐµÑ Ð´Ð¾ÑÑÐ°Ð²ÐºÑ ÑÐ¾Ð»ÑÐºÐ¾ Ð¿Ð¾ ÐÐ¾ÑÐºÐ²Ðµ, Ð¡Ð°Ð½ÐºÑ-ÐÐµÑÐµÑÐ±ÑÑÐ³Ñ, ÐÐ¾ÑÐºÐ¾Ð²ÑÐºÐ¾Ð¹ Ð¾Ð±Ð»Ð°ÑÑÐ¸ Ð¸ ÐÐµÐ½Ð¸Ð½Ð³ÑÐ°Ð´ÑÐºÐ¾Ð¹ Ð¾Ð±Ð»Ð°ÑÑÐ¸. ÐÐ¾Ð¼ÐµÑ Ð·Ð°ÐºÐ°Ð·Ð°: {0}, ÑÐµÐ³Ð¸Ð¾Ð½: {1}, Ð½Ð°ÑÐµÐ»ÐµÐ½Ð½ÑÐ¹ Ð¿ÑÐ½ÐºÑ: {2}
exception.city.not-supported-dalli=ÐÐ´ÑÐµÑ Ð½Ðµ Ð¾Ð±ÑÐ»ÑÐ¶Ð¸Ð²Ð°ÐµÑÑÑ Dalli. Ð ÐµÐ³Ð¸Ð¾Ð½: {0}, Ð½Ð°ÑÐµÐ»ÐµÐ½Ð½ÑÐ¹ Ð¿ÑÐ½ÐºÑ: {1}
exception.city.not-found-major=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ Ð½Ð°Ð¹ÑÐ¸ Ð³Ð¾ÑÐ¾Ð´ {0} Ð² Ð±Ð°Ð·Ðµ Major Express.
exception.defect.comment-photo-empty=ÐÐµ Ð·Ð°Ð´Ð°Ð½ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹ Ðº Ð¸Ð·Ð¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ñ Ð´ÐµÑÐµÐºÑÐ°
exception.defect.comment-photo-max-length=ÐÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹ Ðº Ð¸Ð·Ð¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ñ Ð´ÐµÑÐµÐºÑÐ° Ð½Ðµ Ð´Ð¾Ð»Ð¶ÐµÐ½ Ð¿ÑÐµÐ²ÑÑÐ°ÑÑ {0} ÑÐ¸Ð¼Ð²Ð¾Ð»Ð¾Ð²
exception.defect.photo-incorrect-count=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½Ð¾Ðµ ÐºÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð¸Ð·Ð¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ð¹ Ð´ÐµÑÐµÐºÑÐ¾Ð²: {0}
exception.defect.photo-incorrect-count-plus=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½Ð¾Ðµ ÐºÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð¸Ð·Ð¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ð¹ Ð´ÐµÑÐµÐºÑÐ¾Ð²: {0}. ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð½Ðµ Ð´Ð¾Ð»Ð¶Ð½Ð¾ Ð¿ÑÐµÐ²ÑÑÐ°ÑÑ ÑÐ°ÐºÑÐ¸ÑÐµÑÐºÐ¾Ðµ ÐºÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð·Ð°Ð³ÑÑÐ¶ÐµÐ½Ð½ÑÑ ÑÐ¾ÑÐ¾Ð³ÑÐ°ÑÐ¸Ð¹ Ð´ÐµÑÐµÐºÑÐ¾Ð²: {1}
exception.image.incorrect-count=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½Ð¾Ðµ ÐºÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð¸Ð·Ð¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ð¹: {0}
exception.image.incorrect-count-plus=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½Ð¾Ðµ ÐºÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð¸Ð·Ð¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ð¹: {0}. ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð½Ðµ Ð´Ð¾Ð»Ð¶Ð½Ð¾ Ð¿ÑÐµÐ²ÑÑÐ°ÑÑ ÑÐ°ÐºÑÐ¸ÑÐµÑÐºÐ¾Ðµ ÐºÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð·Ð°Ð³ÑÑÐ¶ÐµÐ½Ð½ÑÑ ÑÐ¾ÑÐ¾Ð³ÑÐ°ÑÐ¸Ð¹: {1}
exception.IllegalStateException.class-create=ÐÑÐ¸Ð±ÐºÐ° ÑÐ¾Ð·Ð´Ð°Ð½Ð¸Ñ ÑÐºÐ·ÐµÐ¼Ð¿Ð»ÑÑÐ° ÐºÐ»Ð°ÑÑÐ°. ÐÐ»Ð°ÑÑ: {0}
exception.datamatrix.wrong-code=ÐÐ¾Ð´ Ð¼Ð°ÑÐºÐ¸ÑÐ¾Ð²ÐºÐ¸ {0} Ð½Ðµ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÐµÑ ÑÐ¾ÑÐ¼Ð°ÑÑ
exception.datamatrix.unconfirmed=ÐÐ°ÐºÐ°Ð· {0} (Ð¿Ð¾Ð·Ð¸ÑÐ¸Ñ {1}): ÐºÐ¾Ð´ Ð¼Ð°ÑÐºÐ¸ÑÐ¾Ð²ÐºÐ¸ Ð½Ðµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½
exception.datamatrix.wrong-order-state=ÐÐ°ÐºÐ°Ð· {0} (Ð¿Ð¾Ð·Ð¸ÑÐ¸Ñ {1}): Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¸Ð·Ð¼ÐµÐ½Ð¸ÑÑ Ð´Ð°Ð½Ð½ÑÐµ ÐºÐ¾Ð´Ð° Ð¼Ð°ÑÐºÐ¸ÑÐ¾Ð²ÐºÐ¸, Ð½ÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ ÑÑÐ°ÑÑÑ Ð·Ð°ÐºÐ°Ð·Ð° ({2})
exception.country-of-origin.not-found=Ð¡ÑÑÐ°Ð½Ð° Ð¿ÑÐ¾Ð¸ÑÑÐ¾Ð¶Ð´ÐµÐ½Ð¸Ñ ÑÐ¾Ð²Ð°ÑÐ° Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð° Ð² ÑÐ¸ÑÑÐµÐ¼Ðµ. ÐÐ±ÑÐ°ÑÐ¸ÑÐµÑÑ, Ð¿Ð¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð² Ð¿Ð¾Ð´Ð´ÐµÑÐ¶ÐºÑ Ñ Ð´Ð°Ð½Ð½Ð¾Ð¹ Ð¾ÑÐ¸Ð±ÐºÐ¾Ð¹
exception.country-of-origin.null-value=Ð¡ÑÑÐ°Ð½Ð° Ð¿ÑÐ¾Ð¸ÑÑÐ¾Ð¶Ð´ÐµÐ½Ð¸Ñ ÑÐ¾Ð²Ð°ÑÐ° Ð½Ðµ Ð¿ÐµÑÐµÐ´Ð°Ð½Ð°
exception.address-endpoint-aggregation.not-found=ÐÐ´ÑÐµÑ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
exception.order.illegal.state=ÐÐ´ÐµÑ Ð¿ÑÐ¾ÑÐµÑ ÑÐ¿Ð¸ÑÐ°Ð½Ð¸Ñ Ð´ÐµÐ½ÐµÐ¶Ð½ÑÑ ÑÑÐµÐ´ÑÑÐ² Ð¿Ð¾ Ð·Ð°ÐºÐ°Ð·Ñ {0}. ÐÐ¶Ð¸Ð´Ð°Ð¹ÑÐµ Ð¾ÐºÐ¾Ð»Ð¾ 1 Ð¼Ð¸Ð½ÑÑÑ !
exception.order.is.not.boutique=ÐÐ°ÐºÐ°Ð· {0} Ð½Ðµ Ð´Ð»Ñ Ð±ÑÑÐ¸ÐºÐ°. ÐÐ¿ÐµÑÐ°ÑÐ¸Ñ Ð½Ðµ Ð´Ð¾ÑÑÑÐ¿Ð½Ð°.
exception.oskelly.order-pickup-pending=ÐÐ»Ñ Ð·Ð°ÐºÐ°Ð·Ð° {0} Ð¿ÑÐ¾Ð¸ÑÑÐ¾Ð´Ð¸Ñ Ð°Ð²ÑÐ¾Ð¼Ð°ÑÐ¸ÑÐµÑÐºÐ¸Ð¹ Ð²ÑÐ·Ð¾Ð² ÐºÑÑÑÐµÑÑÐºÐ¾Ð¹ ÑÐ»ÑÐ¶Ð±Ñ
exception.oskelly.delivery-service-already-called=ÐÑÑÑÐµÑÑÐºÐ°Ñ ÑÐ»ÑÐ¶Ð±Ð° ÑÐ¶Ðµ Ð±ÑÐ»Ð° Ð²ÑÐ·Ð²Ð°Ð½Ð°
exception.product.published-condition-null=Ð£ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð½Ð¾Ð³Ð¾ ÑÐ¾Ð²Ð°ÑÐ° Ð´Ð¾Ð»Ð¶Ð½Ð¾ Ð±ÑÑÑ Ð¾Ð¿ÑÐµÐ´ÐµÐ»ÐµÐ½Ð¾ ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ðµ
exception.wrong-commission-grid-type-for-user=ÐÐµÐ´Ð¾Ð¿ÑÑÑÐ¸Ð¼ÑÐ¹ ÑÐ¸Ð¿ ÐºÐ¾Ð¼Ð¸ÑÑÐ¸Ð¾Ð½Ð½Ð¾Ð¹ ÑÐµÑÐºÐ¸
exception.wrong-commission-grid-for-user=ÐÐµÐ´Ð¾Ð¿ÑÑÑÐ¸Ð¼Ð°Ñ ÐºÐ¾Ð¼Ð¸ÑÑÐ¸Ð¾Ð½Ð½Ð°Ñ ÑÐµÑÐºÐ°

exception.DefaultAdminCommentService.AdminBadCommentEditException=Ð¢ÐµÐºÑÑ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ñ Ð½Ðµ Ð´Ð¾Ð»Ð¶ÐµÐ½ Ð±ÑÑÑ Ð¿ÑÑÑÑÐ¼!

exception.catalog-menu.failed=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¿Ð¾Ð»ÑÑÐ¸ÑÑ Ð¼ÐµÐ½Ñ ÐºÐ°ÑÐ°Ð»Ð¾Ð³Ð° Ð´Ð»Ñ Ð²ÐµÑÑÐ¸Ð¸ {0}

exception.product-item-deletion.size-of-product-used-in-boutique=Ð Ð°Ð·Ð¼ÐµÑ {0} ÑÐ¾Ð²Ð°ÑÐ° {1} Ð½Ð°ÑÐ¾Ð´Ð¸ÑÑÑ Ð² ÐÑÑÐ¸ÐºÐµ Ð¸ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ ÑÐ´Ð°Ð»ÐµÐ½
exception.product-item-deletion.product-item-used-in-boutique=Ð¢Ð¾Ð²Ð°Ñ {0} Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ ÑÐ´Ð°Ð»ÐµÐ½, Ñ Ð½ÐµÐ³Ð¾ ÐµÑÑÑ Ð°ÐºÑÐ¸Ð²Ð½ÑÐµ Ð·Ð°ÐºÐ°Ð·Ñ ÐÑÑÐ¸ÐºÐ°

exception.sale-request.access-denied=Ð£ Ð²Ð°Ñ Ð½ÐµÑ Ð¿ÑÐ°Ð² Ð´Ð¾ÑÑÑÐ¿Ð° Ðº Ð´Ð°Ð½Ð½Ð¾Ð¹ Ð·Ð°ÑÐ²ÐºÐµ
exception.sale-request.comment.delete-forbidden=Ð£Ð´Ð°Ð»ÐµÐ½Ð¸Ðµ ÑÑÐ¶Ð¸Ñ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸ÐµÐ² Ð·Ð°Ð¿ÑÐµÑÐµÐ½Ð¾
exception.product-bitrix.deal-id-already-used=Bitrix ÑÐ´ÐµÐ»ÐºÐ° Ñ ID:{0} ÑÐ¶Ðµ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·ÑÐµÑÑÑ Ð² ÑÐ¾Ð²Ð°ÑÐµ c ID:{1}
exception.order-position-bitrix.order-position-id-ordeal-id-already-used=ÐÐ¾Ð·Ð¸ÑÐ¸Ñ Ð·Ð°ÐºÐ°Ð·Ð° Ñ ID:{0} ÑÐ¶Ðµ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·ÑÐµÑÑÑ ÐÐÐ Bitrix ÑÐ´ÐµÐ»ÐºÐ° Ñ ID:{1} ÑÐ¶Ðµ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·ÑÐµÑÑÑ
exception.order-position-bitrix.bitrix-deal-id-already-used=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð¿Ð¾Ð¿ÑÑÐºÐµ ÑÐ²ÑÐ·Ð°ÑÑ Bitrix ÑÐ´ÐµÐ»ÐºÑ Ñ Ð·Ð°ÐºÐ°Ð·Ð¾Ð¼. Bitrix ÑÐ´ÐµÐ»ÐºÐ° (ID: {0}) ÑÐ¶Ðµ ÑÐ²ÑÐ·Ð°Ð½Ð° Ñ Ð´ÑÑÐ³Ð¸Ð¼ Ð·Ð°ÐºÐ°Ð·Ð¾Ð¼
exception.order-position-bitrix.order-position-id-is-null=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð¿Ð¾Ð¿ÑÑÐºÐµ ÑÐ²ÑÐ·Ð°ÑÑ Bitrix ÑÐ´ÐµÐ»ÐºÑ Ñ Ð·Ð°ÐºÐ°Ð·Ð¾Ð¼. ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ Ð·Ð°ÐºÐ°Ð·Ðµ Ð½Ðµ Ð¿ÐµÑÐµÐ´Ð°Ð½Ð°.

exception.user-exists-by-email=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ñ ÑÐ°ÐºÐ¸Ð¼ email ÑÐ¶Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ
exception.user-exists-by-nickname=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ñ ÑÐ°ÐºÐ¸Ð¼ nickname ÑÐ¶Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ
su.reddot.domain.service.adminpanel.user.impl.AdminV2UserServiceImpl.NotFoundException.message=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ñ ID={1} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
exception.JwtVerificationException.publicKeyNotFound=Ð¢Ð¾ÐºÐµÐ½ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¿ÑÐ¾Ð²ÐµÑÐµÐ½
exception.JwtVerificationException.unsignedJwt=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¾Ð²ÐµÑÐºÐ¸ Ð¿Ð¾Ð´Ð¿Ð¸ÑÐ¸ ÑÐ¾ÐºÐµÐ½Ð°
exception.JwtVerificationException.badJwt=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¾Ð²ÐµÑÐºÐ¸ ÑÐ¾ÐºÐµÐ½Ð°
exception.JwtVerificationException.expiredJwt=Ð¢Ð¾ÐºÐµÐ½ Ð¿ÑÐ¾ÑÑÐ¾ÑÐµÐ½
exception.UserSyncException.directSync=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¸Ð·Ð¼ÐµÐ½Ð¸ÑÑ ÑÑÑÐ°Ð½Ñ. ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð²ÑÐ¹Ð´Ð¸ÑÐµ Ð¸Ð· Ð°ÐºÐºÐ°ÑÐ½ÑÐ° Ð¸ Ð¿Ð¾Ð²ÑÐ¾ÑÐ¸ÑÐµ Ð¿Ð¾Ð¿ÑÑÐºÑ
exception.UserWithSameEmailExists.directSync=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ñ ÑÐ°ÐºÐ¸Ð¼ email ÑÐ¶Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ. (Id={0}, Uid={1})
exception.UserWithSameNicknameExists.directSync=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ñ ÑÐ°ÐºÐ¸Ð¼ nickname ÑÐ¶Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ. (Id={0}, Uid={1})
exception.UserWithSamePhoneExists.directSync=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ñ ÑÐ°ÐºÐ¸Ð¼ ÑÐµÐ»ÐµÑÐ¾Ð½Ð¾Ð¼ ÑÐ¶Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ. (Id={0}, Uid={1})
exception.UserForSyncIsDeleted.directSync=Ð¦ÐµÐ»ÐµÐ²Ð¾Ð¹ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ ÑÐ´Ð°Ð»ÐµÐ½. (Id={0}, Uid={1})
exception.crossborder.concierge.incompatible=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ CrossBorder Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ ÐÐ¾Ð½ÑÑÐµÑÐ¶ÐµÐ¼ Ð¾Ð´Ð½Ð¾Ð²ÑÐµÐ¼ÐµÐ½Ð½Ð¾
exception.user.orders-in-progress=Ð£ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ ÐµÑÑÑ Ð°ÐºÑÐ¸Ð²Ð½ÑÐµ Ð·Ð°ÐºÐ°Ð·Ñ

exception.text-slides.incorrect-interval=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ Ð¸Ð½ÑÐµÑÐ²Ð°Ð» Ð¿ÐµÑÐµÐ»Ð¸ÑÑÑÐ²Ð°Ð½Ð¸Ñ ÑÐ»Ð°Ð¹Ð´Ð¾Ð²: {0}

exception.osocial.post.text.size=ÐÑ Ð´Ð¾ÑÑÐ¸Ð³Ð»Ð¸ Ð¼Ð°ÐºÑÐ¸Ð¼Ð°Ð»ÑÐ½Ð¾Ð¹ Ð´Ð»Ð¸Ð½Ñ ÑÐµÐºÑÑÐ°
exception.osocial.post.text.lines.size=ÐÑ Ð´Ð¾ÑÑÐ¸Ð³Ð»Ð¸ Ð¼Ð°ÐºÑÐ¸Ð¼Ð°Ð»ÑÐ½Ð¾Ð¹ Ð´Ð»Ð¸Ð½Ñ ÑÐµÐºÑÑÐ°
exception.osocial.post.text.email-must-not-contain=Ð¢ÐµÐºÑÑ Ð¿Ð¾ÑÑÐ° Ð½Ðµ Ð´Ð¾Ð»Ð¶ÐµÐ½ ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑ email!
exception.osocial.post.text.telephone-must-not-contain=Ð¢ÐµÐºÑÑ Ð¿Ð¾ÑÑÐ° Ð½Ðµ Ð´Ð¾Ð»Ð¶ÐµÐ½ ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑ ÑÐµÐ»ÐµÑÐ¾Ð½Ð½ÑÑ Ð½Ð¾Ð¼ÐµÑÐ¾Ð²!
exception.osocial.post.text.unacceptable-words=Ð¢ÐµÐºÑÑ Ð¿Ð¾ÑÑÐ° ÑÐ¾Ð´ÐµÑÐ¶Ð¸Ñ Ð·Ð°Ð¿ÑÐµÑÐµÐ½Ð½ÑÐµ ÑÐ»Ð¾Ð²Ð°!
exception.osocial.post.text.link-must-not-contain=Ð¢ÐµÐºÑÑ Ð¿Ð¾ÑÑÐ° Ð½Ðµ Ð´Ð¾Ð»Ð¶ÐµÐ½ ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑ ÑÑÑÐ»Ð¾Ðº!
exception.osocial.post.link.illegal-host=Ð¡ÑÑÐ»ÐºÐ° Ð½Ðµ Ð´Ð¾Ð»Ð¶Ð½Ð° Ð²ÐµÑÑÐ¸ Ð½Ð° ÑÑÑÐ¾Ð½Ð½Ð¸Ð¹ ÑÐµÑÑÑÑ

exception.osocial.comment.text.size=ÐÑ Ð´Ð¾ÑÑÐ¸Ð³Ð»Ð¸ Ð¼Ð°ÐºÑÐ¸Ð¼Ð°Ð»ÑÐ½Ð¾Ð¹ Ð´Ð»Ð¸Ð½Ñ ÑÐµÐºÑÑÐ°
exception.osocial.comment.text.lines.size=ÐÑ Ð´Ð¾ÑÑÐ¸Ð³Ð»Ð¸ Ð¼Ð°ÐºÑÐ¸Ð¼Ð°Ð»ÑÐ½Ð¾Ð¹ Ð´Ð»Ð¸Ð½Ñ ÑÐµÐºÑÑÐ°
exception.osocial.comment.text.email-must-not-contain=Ð¢ÐµÐºÑÑ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ñ Ð½Ðµ Ð´Ð¾Ð»Ð¶ÐµÐ½ ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑ email!
exception.osocial.comment.text.telephone-must-not-contain=Ð¢ÐµÐºÑÑ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ñ Ð½Ðµ Ð´Ð¾Ð»Ð¶ÐµÐ½ ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑ ÑÐµÐ»ÐµÑÐ¾Ð½Ð½ÑÑ Ð½Ð¾Ð¼ÐµÑÐ¾Ð²!
exception.osocial.comment.text.unacceptable-words=Ð¢ÐµÐºÑÑ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ñ ÑÐ¾Ð´ÐµÑÐ¶Ð¸Ñ Ð·Ð°Ð¿ÑÐµÑÐµÐ½Ð½ÑÐµ ÑÐ»Ð¾Ð²Ð°!
exception.osocial.comment.text.link-must-not-contain=Ð¢ÐµÐºÑÑ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ñ Ð½Ðµ Ð´Ð¾Ð»Ð¶ÐµÐ½ ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑ ÑÑÑÐ»Ð¾Ðº!

exception.osocial.favorite-posts.access-denied=Ð£ Ð²Ð°Ñ Ð½ÐµÑ Ð¿ÑÐ°Ð² Ð´Ð¾ÑÑÑÐ¿Ð° Ðº Ð¸Ð·Ð±ÑÐ°Ð½Ð½Ð¾Ð¼Ñ

exception.admin.user.setLegalEntityForIndividualAccountDetails=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ ÑÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ ÑÐ¸Ð¿ "Ð®ÑÐ¸Ð´Ð¸ÑÐµÑÐºÐ¾Ðµ Ð»Ð¸ÑÐ¾" Ð¸Ð·-Ð·Ð° Ð½Ð°Ð»Ð¸ÑÐ¸Ñ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÐ¾Ð² ÑÐ¸Ð·Ð¸ÑÐµÑÐºÐ¾Ð³Ð¾ Ð»Ð¸ÑÐ°
exception.admin.user.setLegalEntityWithEmptyAccountDetails=ÐÐ»Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ñ ÑÐ¸Ð¿Ð¾Ð¼ "Ð®ÑÐ¸Ð´Ð¸ÑÐµÑÐºÐ¾Ðµ Ð»Ð¸ÑÐ¾" Ð½ÑÐ¶Ð½Ð¾ ÑÐ½Ð°ÑÐ°Ð»Ð° Ð´Ð¾Ð±Ð°Ð²Ð¸ÑÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ ÑÑÐ¸Ð´Ð¸ÑÐµÑÐºÐ¾Ð³Ð¾ Ð»Ð¸ÑÐ°
exception.admin.user.setIndividualForLegalEntityAccountDetails=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ ÑÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ ÑÐ¸Ð¿ "Ð¤Ð¸Ð·Ð¸ÑÐµÑÐºÐ¾Ðµ Ð»Ð¸ÑÐ¾" Ð¸Ð·-Ð·Ð° Ð½Ð°Ð»Ð¸ÑÐ¸Ñ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÐ¾Ð² ÑÑÐ¸Ð´Ð¸ÑÐµÑÐºÐ¾Ð³Ð¾ Ð»Ð¸ÑÐ°
exception.admin.counterparty.createLegalEntityCounterpartyForIndividual=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ Ð´Ð¾Ð±Ð°Ð²Ð¸ÑÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ ÑÑÐ¸Ð´Ð¸ÑÐµÑÐºÐ¾Ð³Ð¾ Ð»Ð¸ÑÐ° Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ñ ÑÐ¸Ð¿Ð¾Ð¼ "Ð¤Ð¸Ð·Ð¸ÑÐµÑÐºÐ¾Ðµ Ð»Ð¸ÑÐ¾"
exception.admin.counterparty.createIndividualCounterpartyForLegalEntity=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ Ð´Ð¾Ð±Ð°Ð²Ð¸ÑÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ ÑÐ¸Ð·Ð¸ÑÐµÑÐºÐ¾Ð³Ð¾ Ð»Ð¸ÑÐ° Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ñ ÑÐ¸Ð¿Ð¾Ð¼ "Ð®ÑÐ¸Ð´Ð¸ÑÐµÑÐºÐ¾Ðµ Ð»Ð¸ÑÐ¾"
exception.no-role.ADMIN_EDIT_PERSONAL_DATA=ÐÐµÐ´Ð¾ÑÑÐ°ÑÐ¾ÑÐ½Ð¾ Ð¿ÑÐ°Ð² Ð½Ð° ÑÐµÐ´Ð°ÐºÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ Ð¿ÐµÑÑÐ¾Ð½Ð°Ð»ÑÐ½ÑÑ Ð´Ð°Ð½Ð½ÑÑ

exception.instagram-feed.instagram.request.error=ÐÑÐ¸Ð±ÐºÐ° Ð·Ð°Ð¿ÑÐ¾ÑÐ° Ðº Instagram

exception.UserLocationService.unknownLocation=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ ÑÐ°ÑÐ¿Ð¾Ð·Ð½Ð°ÑÑ Ð¼ÐµÑÑÐ¾Ð¿Ð¾Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ
exception.UserLocationService.anonymousUser=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ Ð·Ð°Ð´Ð°ÑÑ Ð¸Ð»Ð¸ Ð¿Ð¾Ð»ÑÑÐ¸ÑÑ Ð¼ÐµÑÑÐ¾Ð¿Ð¾Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ Ð´Ð»Ñ Ð½ÐµÐ°Ð²ÑÐ¾ÑÐ¸Ð·Ð¾Ð²Ð°Ð½Ð½Ð¾Ð³Ð¾ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð±ÐµÐ· Ð³Ð¾ÑÑÐµÐ²Ð¾Ð³Ð¾ ÑÐ¾ÐºÐµÐ½Ð°

exception.filter-subscriptions.duplicate-name=ÐÐ¾Ð¸ÑÐº Ñ ÑÐ°ÐºÐ¸Ð¼ Ð½Ð°Ð·Ð²Ð°Ð½Ð¸ÐµÐ¼ ÑÐ¶Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ. ÐÐ²ÐµÐ´Ð¸ÑÐµ Ð´ÑÑÐ³Ð¾Ðµ Ð¸Ð¼Ñ.
exception.filter-subscriptions.empty-name=ÐÐ²ÐµÐ´Ð¸ÑÐµ Ð½Ð°Ð·Ð²Ð°Ð½Ð¸Ðµ Ð´Ð»Ñ ÑÐ¾ÑÑÐ°Ð½ÐµÐ½Ð¸Ñ Ð¿Ð¾Ð¸ÑÐºÐ°

message.refund.operation.default.text=ÐÐ¾Ð·Ð²ÑÐ°Ñ Ð¿Ð¾ Ð·Ð°ÐºÐ°Ð·Ñ {0}

entity.admin-alert.AdminOrderAlertWaybill=ÐÑÐ¾Ð±Ð»ÐµÐ¼Ð° Ñ Ð½Ð°ÐºÐ»Ð°Ð´Ð½Ð¾Ð¹ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸
entity.admin-alert.AdminOrderAlertWaybillCreationError=ÐÐ°ÐºÐ»Ð°Ð´Ð½Ð°Ñ Ð½Ðµ ÑÐ¾Ð·Ð´Ð°Ð½Ð°
entity.admin-alert.AdminOrderAlertWaybillCreationErrorWrongAddress=ÐÑÐ¸Ð±ÐºÐ° Ð°Ð´ÑÐµÑÐ°
entity.admin-alert.AdminOrderAlertWaybillDeliveryFromSellerFailedRaiseComplaint=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð±ÑÐ» Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½ Ð² Ð¾ÑÐ¸Ñ, Ð¿ÑÐµÑÐµÐ½Ð·Ð¸Ñ ÐÐ¡
entity.admin-alert.AdminOrderAlertWaybillDeliveryProblem=ÐÑÐ¾Ð±Ð»ÐµÐ¼Ð° Ñ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¾Ð¹
entity.admin-alert.AdminOrderAlertWaybillDeliveryProblemNoDeliveryAttempt=ÐÐµ Ð±ÑÐ»Ð¾ Ð¿Ð¾Ð¿ÑÑÐºÐ¸ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸
entity.admin-alert.AdminOrderAlertWaybillDeliveryProblemReceiptRenouncement=ÐÑÐºÐ°Ð· Ð¾Ñ Ð¿Ð¾Ð»ÑÑÐµÐ½Ð¸Ñ
entity.admin-alert.AdminOrderAlertWaybillDeliveryProblemRecipientAbsence=ÐÑÑÑÑÑÑÐ²Ð¸Ðµ Ð¿Ð¾Ð»ÑÑÐ°ÑÐµÐ»Ñ
entity.admin-alert.AdminOrderAlertWaybillDeliveryProblemTraceOpen=Ð¢ÑÐµÐ¹Ñ Ð¾ÑÐºÑÑÑ
entity.admin-alert.AdminOrderAlertWaybillDeliveryProblemWrongAddress=ÐÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½ÑÐ¹ Ð°Ð´ÑÐµÑ
entity.admin-alert.AdminOrderAlertWaybillDeliveryToBuyerFailedRaiseComplaint=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð±ÑÐ» Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ, Ð¿ÑÐµÑÐµÐ½Ð·Ð¸Ñ ÐÐ¡
entity.admin-alert.AdminOrderAlertWaybillPostponed=ÐÐµÑÐµÐ½Ð¾Ñ Ð´Ð°ÑÑ (Ð»Ð¾Ð³Ð¸ÑÑÑ)
entity.admin-alert.AdminOrderAlertWaybillPostponedAgreedDeliveryDate=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ð½Ð°Ñ Ð´Ð°ÑÐ° Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸
entity.admin-alert.AdminOrderAlertWaybillPostponedCarrierProblem=ÐÑÐ¾Ð±Ð»ÐµÐ¼Ñ Ð¿ÐµÑÐµÐ²Ð¾Ð·ÑÐ¸ÐºÐ°
entity.admin-alert.AdminOrderAlertWaybillPostponedRedirect=ÐÐµÑÐµÐ°Ð´ÑÐµÑÐ°ÑÐ¸Ñ
entity.admin-alert.AdminOrderAlertAcquirerRefund=ÐÑÐ¸ÐµÐ¼:\nÐ­ÐºÐ²Ð°ÑÐ¸Ð½Ð³ Ð²ÐµÑÐ½ÑÐ» Ð´ÐµÐ½ÑÐ³Ð¸
entity.admin-alert.AdminOrderAlertBankRejectedOperation=ÐÑÐ²Ð¾Ð´:\nÐÐ°Ð½Ðº Ð¾ÑÐºÐ»Ð¾Ð½Ð¸Ð» Ð¾Ð¿ÐµÑÐ°ÑÐ¸Ñ
entity.admin-alert.AdminOrderAlertBankTechnicalProblem=ÐÑÐ²Ð¾Ð´:\nÐ¢ÐµÑÐ½Ð¸ÑÐµÑÐºÐ°Ñ Ð¾ÑÐ¸Ð±ÐºÐ° Ð±Ð°Ð½ÐºÐ°
entity.admin-alert.AdminOrderAlertConfirmationSale=ÐÑÐ¾Ð´Ð°Ð¶Ð° Ð½Ðµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´Ð°ÐµÑÑÑ - Ð±Ð¾Ð»ÐµÐµ 1 Ð´Ð½Ñ ({0})
entity.admin-alert.AdminOrderAlertDeliveryAddress=ÐÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾ ÑÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ Ð°Ð´ÑÐµÑ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸
entity.admin-alert.AdminOrderAlertExpertise=Ð­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ð° Ð´Ð»Ð¸ÑÑÑ - Ð±Ð¾Ð»ÐµÐµ 1 Ð´Ð½Ñ ({0})
entity.admin-alert.AdminOrderAlertFromOfficeToBuyer=ÐÑÑÑÐµÑ Ð²ÐµÐ·ÐµÑ ÑÐ¾Ð²Ð°Ñ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ - Ð±Ð¾Ð»ÐµÐµ 1 Ð´Ð½ÐµÐ¹ ({0})
entity.admin-alert.AdminOrderAlertFromSellerToOffice=ÐÑÑÑÐµÑ Ð²ÐµÐ·ÐµÑ ÑÐ¾Ð²Ð°Ñ Ð² OSKELLY - Ð±Ð¾Ð»ÐµÐµ 1 Ð´Ð½ÐµÐ¹ ({0})
entity.admin-alert.AdminOrderAlertMoneyPaymentNotEnough=ÐÑÐ²Ð¾Ð´:\nÐÐµÐ´Ð¾ÑÑÐ°ÑÐ¾ÑÐ½Ð¾ ÑÑÐµÐ´ÑÑÐ² Ð½Ð° ÑÑÐµÑÑ
entity.admin-alert.AdminOrderAlertMoscowDelivery=ÐÐ°Ð´Ð¾ Ð²ÑÐ±ÑÐ°ÑÑ Ð´Ð¾ÑÑÐ°Ð²Ð»ÑÐµÐ¼ ÑÐ°Ð¼Ð¸ Ð¸Ð»Ð¸ Ð»Ð¾Ð³Ð¸ÑÑÐ¾Ð¼ Ð¿Ð¾ÑÐ»Ðµ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.admin-alert.AdminOrderAlertMoscowLogistOnWayToSeller=ÐÑÑÑÐµÑ OSKELLY - ÐÐ°Ð´Ð¾ Ð¾ÑÐ¼ÐµÑÐ¸ÑÑ ÑÑÐ¾ ÐºÑÑÑÐµÑ Ð²ÑÐµÑÐ°Ð» Ðº Ð¿ÑÐ¾Ð´Ð°Ð²ÑÑ
entity.admin-alert.AdminOrderAlertNeedSendAgentReport=ÐÐ°Ð´Ð¾ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸ÑÑ Ð¾ÑÑÐµÑ Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð¶Ðµ
entity.admin-alert.AdminOrderAlertPartlyExpertise=Ð­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ð° - ÐÐµ Ð¿ÑÐ¾Ð¹Ð´ÐµÐ½Ð¾ - ÐÐµÑÐ½ÑÑÑ ÑÐ¾Ð²Ð°Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÑ
entity.admin-alert.AdminOrderAlertPickupAddress=ÐÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾ ÑÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ Ð°Ð´ÑÐµÑ Ð·Ð°Ð±Ð¾ÑÐ°
entity.admin-alert.AdminOrderAlertPickupFromOffice=ÐÑÑÑÐµÑ Ð½Ðµ Ð·Ð°Ð±ÑÐ°Ð» Ñ OSKELLY - Ð±Ð¾Ð»ÐµÐµ 1 Ð´Ð½Ñ (Ð¿ÑÐ¾ÑÑÐ¾ÑÐµÐ½Ð¾ {0} ÑÐ°ÑÐ¾Ð²)
entity.admin-alert.AdminOrderAlertPickupFromSeller=ÐÑÑÑÐµÑ Ð½Ðµ Ð·Ð°Ð±ÑÐ°Ð» Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ° - Ð±Ð¾Ð»ÐµÐµ 1 Ð´Ð½Ñ ({0})
entity.admin-alert.AdminOrderAlertSendAgentReport=Ð¢Ð¾Ð²Ð°Ñ Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½, Ð¾ÑÑÐµÑ Ð½Ðµ Ð¾ÑÐ¿ÑÐ°Ð²Ð»ÐµÐ½ - Ð±Ð¾Ð»ÐµÐµ 1 Ð´Ð½Ñ ({0})
entity.admin-alert.AdminOrderAlertWaitPaymentMoneyToSeller=ÐÑÐ¾Ð´Ð°Ð²ÐµÑ Ð½Ðµ Ð¿Ð¾Ð»ÑÑÐ¸Ð» \nÐ´ÐµÐ½ÑÐ³Ð¸ - Ð±Ð¾Ð»ÐµÐµ 1 Ð´Ð½Ñ ({0})
entity.admin-alert.AdminOrderAlertWaitPaymentMoneyToSeller.ReportNotFound=ÐÑÑÐµÑ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½!
entity.admin-alert.AdminOrderAlertConfirmationAgentReport=ÐÑÑÐµÑ Ð½Ðµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´Ð°ÐµÑÑÑ - Ð±Ð¾Ð»ÐµÐµ 1 Ð´Ð½Ñ ({0})
entity.admin-alert.AdminOrderAlertConfirmationAgentReport.ReportNotFound=ÐÑÑÐµÑ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½!
entity.admin-alert.AdminOrderAlertDeliveryToBuyerUnconfirmed=ÐÐ¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð½Ðµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸Ð» Ð¿Ð¾Ð»ÑÑÐµÐ½Ð¸Ðµ ({0} Ð´Ð½Ñ Ñ Ð¼Ð¾Ð¼ÐµÐ½ÑÐ° Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸)
entity.admin-alert.AdminOrderAlertDeliveryToBuyerConfirmed=ÐÐ¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸Ð» Ð¿Ð¾Ð»ÑÑÐµÐ½Ð¸Ðµ ({0} Ð´Ð½Ñ Ñ Ð¼Ð¾Ð¼ÐµÐ½ÑÐ° Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸)
entity.admin-alert.AdminProductAlert24hoursState=ÐÑÐ¾ÑÑÐ¾ÑÐµÐ½Ð¾
entity.admin-alert.AdminProductAlert24hoursStateModeration=ÐÐ¾Ð´ÐµÑÐ°ÑÐ¸Ñ Ð¿ÑÐ¾ÑÑÐ¾ÑÐµÐ½Ð° Ð½Ð° {0} Ð´Ð½ÐµÐ¹
entity.admin-alert.AdminProductAlert24hoursStateRetouching=Ð ÐµÑÑÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ Ð¿ÑÐ¾ÑÑÐ¾ÑÐµÐ½Ð¾ Ð½Ð° {0} Ð´Ð½ÐµÐ¹
entity.admin-alert.AdminProductAlert24hoursStateRetouchingDone=Ð¡Ð»Ð¸ÑÐºÐ¾Ð¼ Ð´Ð°Ð²Ð½Ð¾ Ð¾ÑÑÐµÑÑÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð¾, Ð½Ð¾ Ð¿ÑÐ¾ÑÐ»Ð¾ ÑÐ¶Ðµ {0} Ð´Ð½ÐµÐ¹
entity.admin-alert.AdminProductAlert24hoursStateSecondEdition=ÐÐ¾Ð²ÑÐ¾ÑÐ½Ð¾Ðµ ÑÐµÐ´Ð°ÐºÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ Ð¿ÑÐ¾ÑÑÐ¾ÑÐµÐ½Ð¾ Ð½Ð° {0} Ð´Ð½ÐµÐ¹
entity.BargainRecordFromType.enum.fromBuyer=ÐÑ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
entity.BargainRecordFromType.enum.fromSeller=ÐÑ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°
entity.BargainRecordFromType.enum.fromAdmin=ÐÑ Ð°Ð´Ð¼Ð¸Ð½Ð¸ÑÑÑÐ°ÑÐ¾ÑÐ°
entity.BargainRecordFromType.enum.auto=ÐÐ²ÑÐ¾Ð¼Ð°ÑÐ¸ÑÐµÑÐºÐ¸Ð¹
entity.BargainRecordType.enum.hello-from-seller=ÐÑÐ¸Ð²ÐµÑÑÑÐ²Ð¸Ðµ Ð¾Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°
entity.BargainRecordType.enum.offer-from-buyer-or-seller=ÐÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ Ð¾Ñ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð¸Ð»Ð¸ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°
entity.BargainRecordType.enum.accept-offer=ÐÑÐ¸Ð½ÑÑÐ¸Ðµ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð¼ Ð¸Ð»Ð¸ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»ÐµÐ¼
entity.BargainRecordType.enum.declined-offer=ÐÑÐºÐ»Ð¾Ð½ÐµÐ½Ð¸Ðµ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð¼
entity.BargainSort.enum.date-create=ÐÐ°ÑÐ° ÑÐ¾Ð·Ð´Ð°Ð½Ð¸Ñ Ð¿Ð¾ Ð²Ð¾Ð·ÑÐ°ÑÑÐ°Ð½Ð¸Ñ
entity.BargainSort.enum.date-create-desc=ÐÐ°ÑÐ° ÑÐ¾Ð·Ð´Ð°Ð½Ð¸Ñ Ð¿Ð¾ ÑÐ±ÑÐ²Ð°Ð½Ð¸Ñ
entity.BargainSort.enum.date-change=ÐÐ°ÑÐ° Ð¸Ð·Ð¼ÐµÐ½ÐµÐ½Ð¸Ñ Ð¿Ð¾ Ð²Ð¾Ð·ÑÐ°ÑÑÐ°Ð½Ð¸Ñ
entity.BargainSort.enum.date-change-desc=ÐÐ°ÑÐ° Ð¸Ð·Ð¼ÐµÐ½ÐµÐ½Ð¸Ñ Ð¿Ð¾ ÑÐ±ÑÐ²Ð°Ð½Ð¸Ñ
entity.BargainSort.enum.last-price=ÐÐ¾ÑÐ»ÐµÐ´Ð½ÑÑ ÑÐµÐ½Ð° Ð¿Ð¾ Ð²Ð¾Ð·ÑÐ°ÑÑÐ°Ð½Ð¸Ñ
entity.BargainSort.enum.last-price-desc=ÐÐ¾ÑÐ»ÐµÐ´Ð½ÑÑ ÑÐµÐ½Ð° Ð¿Ð¾ ÑÐ±ÑÐ²Ð°Ð½Ð¸Ñ
entity.BargainState.enum.initial=Ð¡Ð¾Ð·Ð´Ð°Ð½Ð¸Ðµ
entity.BargainState.enum.offer=Ð Ð¾Ð¶Ð¸Ð´Ð°Ð½Ð¸Ð¸
entity.BargainState.enum.counter-offer=ÐÑÑÑÐµÑÐ½ÑÐ¹ ÑÐ¾ÑÐ³
entity.BargainState.enum.declined=ÐÑÐºÐ»Ð¾Ð½ÐµÐ½
entity.BargainState.enum.confirmed=ÐÐ¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½
entity.BargainState.enum.sold=Ð¢Ð¾Ð²Ð°Ñ Ð¿ÑÐ¾Ð´Ð°Ð½
entity.BargainState.enum.consumed=Ð¡Ð´ÐµÐ»ÐºÐ° ÑÑÐ¿ÐµÑÐ½Ð°
entity.BargainState.enum.expired=ÐÑÐ¾ÑÑÐ¾ÑÐµÐ½Ð¾
entity.BargainState.enum.canceled=ÐÑÐ¼ÐµÐ½ÐµÐ½
entity.BargainState.enum.undefined=ÐÐµÐ¾Ð¿ÑÐµÐ´ÐµÐ»ÐµÐ½
entity.notification.BargainBuyerConfirmedNotification.subTitle=ÐÐ°Ñ ÑÐ¾ÑÐ³ Ð¾Ð´Ð¾Ð±ÑÐµÐ½!
entity.notification.AnotherCommentNotification.title=ð¬ {0}:
entity.notification.AnotherCommentNotification.baseMessage={0}
entity.notification.BargainBuyerConfirmedNotification.baseMessage=Ð£ Ð²Ð°Ñ ÐµÑÑÑ 24 ÑÐ°ÑÐ°, ÑÑÐ¾Ð±Ñ ÐºÑÐ¿Ð¸ÑÑ ÑÐ¾Ð²Ð°Ñ Ð¿Ð¾ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ð½Ð¾Ð¹ ÑÐµÐ½Ðµ.
entity.notification.BargainBuyerCounterOfferNotification.subTitle=ÐÑÑÑÐµÑÐ½Ð¾Ðµ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ ÑÐµÐ½Ñ
entity.notification.BargainBuyerCounterOfferNotification.baseMessage=Ð£ Ð²Ð°Ñ ÐµÑÑÑ 24 ÑÐ°ÑÐ°, ÑÑÐ¾Ð±Ñ ÐºÑÐ¿Ð¸ÑÑ ÑÐ¾Ð²Ð°Ñ Ð¿Ð¾ ÑÐµÐ½Ðµ, Ð¿ÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð½Ð¾Ð¹ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð¼
entity.notification.BargainBuyerDeclinedNotification.subTitle=Ð¢Ð¾ÑÐ³ Ð¾ÑÐºÐ»Ð¾Ð½ÐµÐ½
entity.notification.BargainBuyerDeclinedNotification.baseMessage=ÐÑÐµÐ´Ð»Ð¾Ð¶Ð¸ÑÐµ Ð½Ð¾Ð²ÑÑ ÑÐµÐ½Ñ. Ð£ Ð²Ð°Ñ ÐµÑÑÑ ÐµÑÐµ {0} Ð¿Ð¾Ð¿ÑÑÐº{1}.
entity.notification.BargainBuyerDeclinedNotification.baseMessage.Alt=ÐÑ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¿ÑÐ¸Ð¾Ð±ÑÐµÑÑÐ¸ ÑÐ¾Ð²Ð°Ñ Ð¿Ð¾ ÑÐµÐ½Ðµ Ð² Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½Ð¸Ð¸.
entity.notification.BargainBuyerDeclinedNotification.baseMessage.Alt2=Ð¢Ð¾ÑÐ³ÑÐ¹ÑÐµÑÑ Ñ Ð´ÑÑÐ³Ð¸Ð¼Ð¸ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°Ð¼Ð¸ Ð¸ Ð¿Ð¾ÐºÑÐ¿Ð°Ð¹ÑÐµ Ð¿Ð¾ ÑÐ°Ð¼ÑÐ¼ Ð²ÑÐ³Ð¾Ð´Ð½ÑÐ¼ ÑÐµÐ½Ð°Ð¼
entity.notification.BargainBuyerExpiredByBuyerNotification.subtitle=ÐÑÐµÐ¼Ñ ÑÐ¾ÑÐ³Ð° Ð¸ÑÑÐµÐºÐ°ÐµÑ
entity.notification.BargainBuyerExpiredByBuyerNotification.baseMessage=Ð£ Ð²Ð°Ñ Ð¾ÑÑÐ°Ð»ÑÑ 1 ÑÐ°Ñ, ÑÑÐ¾Ð±Ñ ÐºÑÐ¿Ð¸ÑÑ ÑÐ¾Ð²Ð°Ñ Ð¿Ð¾ ÑÐµÐ½Ðµ, Ð¿ÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð½Ð¾Ð¹ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð¼
entity.notification.BargainBuyerAlreadyConfirmed24hNotification.title=ÐÐµ Ð·Ð°Ð±ÑÐ´ÑÑÐµ Ð¾ÑÐ¾ÑÐ¼Ð¸ÑÑ Ð·Ð°ÐºÐ°Ð·
entity.notification.BargainBuyerAlreadyConfirmed24hNotification.baseMessage=Ð£ Ð²Ð°Ñ Ð¾ÑÑÐ°Ð»Ð¾ÑÑ 2 ÑÐ°ÑÐ°, ÑÑÐ¾Ð±Ñ ÐºÑÐ¿Ð¸ÑÑ ÑÐ¾Ð²Ð°Ñ Ð¿Ð¾ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ð½Ð¾Ð¹ ÑÐµÐ½Ðµ.
entity.notification.BargainBuyerProductSoldNotification.baseMessage=Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, ÑÐ¾Ð²Ð°Ñ {0}, Ð·Ð° ÐºÐ¾ÑÐ¾ÑÑÐ¹ Ð²Ñ ÑÐ¾ÑÐ³Ð¾Ð²Ð°Ð»Ð¸ÑÑ, Ð±ÑÐ» Ð¿ÑÐ¸Ð¾Ð±ÑÐµÑÐµÐ½ Ð´ÑÑÐ³Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»ÐµÐ¼. ÐÐ¾ÑÐ¾ÑÐ¾Ð¿Ð¸ÑÐµÑÑ Ð² ÑÐ»ÐµÐ´ÑÑÑÐ¸Ð¹ ÑÐ°Ð·!
entity.notification.BargainSellerNewOffer12hNotification.subTitle=Ð¿ÑÐµÐ´Ð»Ð°Ð³Ð°ÐµÑ ÑÐ²Ð¾Ñ ÑÐµÐ½Ñ Ð·Ð° ÑÐ¾Ð²Ð°Ñ:{0}
entity.notification.BargainSellerNewOffer12hNotification.baseMessage=ÐÐµ Ð·Ð°Ð±ÑÐ´ÑÑÐµ ÑÐ¾Ð¾Ð±ÑÐ¸ÑÑ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð¾ ÑÐµÑÐµÐ½Ð¸Ð¸ Ð¿Ð¾ ÑÐ¾ÑÐ³Ñ. ÐÐµ Ð¶Ð´Ð¸ÑÐµ Ð¿Ð¾ÑÐµÐ½ÑÐ¸Ð°Ð»ÑÐ½ÑÑ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»ÐµÐ¹, ÑÐ¾ÑÐ³ÑÐ¹ÑÐµÑÑ Ñ ÑÐµÐ°Ð»ÑÐ½ÑÐ¼Ð¸. Ð£ Ð²Ð°Ñ Ð¾ÑÑÐ°Ð»Ð¾ÑÑ 12 ÑÐ°ÑÐ¾Ð² Ð½Ð° Ð¿ÑÐ¸Ð½ÑÑÐ¸Ðµ ÑÐµÑÐµÐ½Ð¸Ñ. ÐÐ¾ÑÐ»Ðµ Ð¸ÑÑÐµÑÐµÐ½Ð¸Ñ ÑÑÐ¾ÐºÐ°, Ð¿ÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð±ÑÐ´ÐµÑ Ð°Ð²ÑÐ¾Ð¼Ð°ÑÐ¸ÑÐµÑÐºÐ¸ Ð¾ÑÐºÐ»Ð¾Ð½ÐµÐ½Ð¾
entity.notification.BargainSellerNewOffer24hNotification.subTitle=ÐÑÐµÐ¼Ñ ÑÐ¾ÑÐ³Ð° Ð¸ÑÑÐµÐºÐ°ÐµÑ
entity.notification.BargainSellerNewOffer24hNotification.baseMessage=Ð£ Ð²Ð°Ñ Ð¾ÑÑÐ°Ð»Ð¾ÑÑ 2 ÑÐ°ÑÐ°! Ð£ÑÐ¿ÐµÐ¹ÑÐµ Ð¾Ð´Ð¾Ð±ÑÐ¸ÑÑ ÑÐµÐ½Ñ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð¸Ð»Ð¸ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶Ð¸ÑÑ ÑÐ²Ð¾Ñ.
entity.notification.BargainSellerNewOfferNotification.subTitle=ÐÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ ÑÐµÐ½Ñ
entity.notification.NewCommentNotification.title=ð¬ {0}:
entity.notification.NewCommentNotification.baseMessage={0}
entity.notification.NewCommentNotification.remindTitle=ÐÐ´ÑÐ°Ð²ÑÑÐ²ÑÐ¹ÑÐµ, ÑÑÐ¾ Oskelly. ÐÑ Ð·Ð°Ð±ÑÐ»Ð¸ Ð¾ÑÐ²ÐµÑÐ¸ÑÑ Ð½Ð° ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹ Ð²Ð°ÑÐµÐ³Ð¾ Ð¿Ð¾ÑÐµÐ½ÑÐ¸Ð°Ð»ÑÐ½Ð¾Ð³Ð¾ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ. ÐÑÐ¾Ð²ÐµÑÑÑÐµ, Ð¿Ð¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, ÑÐ²ÐµÐ´Ð¾Ð¼Ð»ÐµÐ½Ð¸Ñ Ð² ÐÐ¸ÑÐ½Ð¾Ð¼ ÐÐ°Ð±Ð¸Ð½ÐµÑÐµ.
entity.notification.ReplyCommentNotification.title=ð¬ {0}:
entity.notification.ReplyCommentNotification.baseMessage={0}
entity.notification.NewFollowingNotification.title={0}
entity.notification.NewFollowingNotification.baseMessage=Ð¿Ð¾Ð´Ð¿Ð¸ÑÐ°Ð½{0} Ð½Ð° Ð²Ð°ÑÐ¸ Ð¾Ð±Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ
entity.notification.BargainSellerNewOfferNotification.baseMessage={0}: Ñ Ð²Ð°Ñ Ð³Ð¾ÑÐ¾Ð²Ñ ÐºÑÐ¿Ð¸ÑÑ ÑÐ¾Ð²Ð°Ñ Ð·Ð° {1} ÑÑÐ±. Ð£ Ð²Ð°Ñ ÐµÑÑÑ 24 ÑÐ°ÑÐ°, ÑÑÐ¾Ð±Ñ Ð¾Ð´Ð¾Ð±ÑÐ¸ÑÑ Ð¸Ð»Ð¸ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶Ð¸ÑÑ ÑÐ²Ð¾Ñ ÑÐµÐ½Ñ.
entity.notification.BargainSellerNewOfferNotification.remindTitle=ÐÐ´ÑÐ°Ð²ÑÑÐ²ÑÐ¹ÑÐµ, ÑÑÐ¾ Oskelly. ÐÑ Ð²Ð¸Ð´ÐµÐ»Ð¸? ÐÐ°Ð¼ Ð¿ÑÐ¸ÑÐ»Ð¾ Ð½Ð¾Ð²Ð¾Ðµ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ Ð¿Ð¾ ÑÐµÐ½Ðµ {0}. Ð§ÐµÐ¼ Ð¾Ð¿ÐµÑÐ°ÑÐ¸Ð²Ð½ÐµÐµ Ð²Ñ Ð¾ÑÑÐµÐ°Ð³Ð¸ÑÑÐµÑÐµ Ð½Ð° ÑÐ¾ÑÐ³, ÑÐµÐ¼ Ð±ÑÑÑÑÐµÐµ Ð¿ÑÐ¾Ð¸Ð·Ð¾Ð¹Ð´ÐµÑ Ð¿ÑÐ¾Ð´Ð°Ð¶Ð°.
entity.notification.CatalogNotification.baseMessage=ÐÐ¾Ð²ÑÐµ ÑÐ¾Ð²Ð°ÑÑ Ð² ÐºÐ°ÑÐ°Ð»Ð¾Ð³Ðµ!
entity.notification.LastChance1000Notification.title=ÐÐµ ÑÑÐ¿ÐµÐ»Ð¸ Ð²Ð¾ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑÑÑ ÑÐºÐ¸Ð´ÐºÐ¾Ð¹?
entity.notification.LastChance1000Notification.baseMessage=ÐÐµ Ð±ÐµÐ´Ð°! ÐÐ°ÑÐ¸Ð¼ Ð½Ð¾Ð²ÑÐ¹ Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´ CHANCE5 Ð½Ð° 5% ÑÐºÐ¸Ð´ÐºÐ¸ â¤
entity.notification.NewArrivalsNotification.title=ÐÐ¾Ð²ÑÐµ Ð¿Ð¾ÑÑÑÐ¿Ð»ÐµÐ½Ð¸Ñ ÐºÐ°Ð¶Ð´ÑÐ¹ ÑÐ°Ñ!
entity.notification.NewArrivalsNotification.baseMessage=Ð¢Ð¾ÑÐ¾Ð¿Ð¸ÑÐµÑÑ, ÑÐ°Ð¼ÑÐµ Ð²ÑÐ³Ð¾Ð´Ð½ÑÐµ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ Ð²ÑÐºÑÐ¿Ð°ÑÑÑÑ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ Ð½ÐµÑÐºÐ¾Ð»ÑÐºÐ¸Ñ ÑÐ°ÑÐ¾Ð²!
entity.notification.Promo24HoursLeftNotification.title=Ð£ÑÐ¿ÐµÐ¹ÑÐµ Ð¿Ð¾Ð»ÑÑÐ¸ÑÑ ÑÐºÐ¸Ð´ÐºÑ!
entity.notification.NewProductLikeNotification.baseMessage=ÐÐ¾Ð¿ÑÐ¾Ð±ÑÐ¹ÑÐµ ÑÐ½Ð¸Ð·Ð¸ÑÑ ÑÑÐ¾Ð¸Ð¼Ð¾ÑÑÑ. ÐÑ Ð¾Ð¿Ð¾Ð²ÐµÑÑÐ¸Ð¼ Ð²ÑÐµÑ, Ñ ÐºÐ¾Ð³Ð¾ ÑÐ¾Ð²Ð°Ñ Ð² Ð²Ð¸ÑÐ»Ð¸ÑÑÐµ Ð¸ Ð²Ñ ÑÐ¼Ð¾Ð¶ÐµÑÐµ Ð¿ÑÐ¾Ð´Ð°ÑÑ ÐµÐ³Ð¾ Ð±ÑÑÑÑÐµÐµ.
entity.notification.NewProductLikeNotification.title=ÐÐ°Ñ ÑÐ¾Ð²Ð°Ñ Ð´Ð¾Ð±Ð°Ð²Ð»ÑÑÑ Ð² Ð¸Ð·Ð±ÑÐ°Ð½Ð½Ð¾Ðµ
entity.notification.FirstProductLikeNotification.title=ÐÐ°ÑÐ¸Ð¼ ÑÐ¾Ð²Ð°ÑÐ¾Ð¼ Ð¸Ð½ÑÐµÑÐµÑÑÑÑÑÑ!
entity.notification.FirstProductLikeNotification.baseMessage={0} Ð´Ð¾Ð±Ð°Ð²Ð¸Ð» Ð²Ð°Ñ ÑÐ¾Ð²Ð°Ñ Ð² Ð²Ð¸ÑÐ»Ð¸ÑÑ
entity.notification.LostCartNotification.title.0=ÐÑ ÑÐ¾ÑÑÐ°Ð½Ð¸Ð»Ð¸ Ð²Ð°Ñ Ð²ÑÐ±Ð¾Ñ
entity.notification.LostCartNotification.baseMessage.0=ÐÐ°Ð¿Ð¾Ð¼Ð¸Ð½Ð°ÐµÐ¼ Ð¾ ÑÐ¾Ð²Ð°ÑÐ°Ñ Ð² ÐºÐ¾ÑÐ·Ð¸Ð½Ðµ. Ð£ÑÐ¿ÐµÐ¹ÑÐµ ÑÐ´ÐµÐ»Ð°ÑÑ Ð·Ð°ÐºÐ°Ð·, Ð¿Ð¾ÐºÐ° ÑÐ¾Ð²Ð°Ñ ÐµÑÑÑ Ð² Ð½Ð°Ð»Ð¸ÑÐ¸Ð¸.
entity.notification.LostCartNotificationNewbies.title=ÐÑ ÑÐ¾ÑÑÐ°Ð½Ð¸Ð»Ð¸ Ð²Ð°Ñ Ð²ÑÐ±Ð¾Ñ
entity.notification.LostCartNotificationNewbies.baseMessage=ÐÐ°Ð¿Ð¾Ð¼Ð¸Ð½Ð°ÐµÐ¼ Ð¾ ÑÐ¾Ð²Ð°ÑÐ°Ñ Ð² ÐºÐ¾ÑÐ·Ð¸Ð½Ðµ. Ð£ÑÐ¿ÐµÐ¹ÑÐµ ÑÐ´ÐµÐ»Ð°ÑÑ Ð·Ð°ÐºÐ°Ð·, Ð¿Ð¾ÐºÐ° ÑÐ¾Ð²Ð°Ñ ÐµÑÑÑ Ð² Ð½Ð°Ð»Ð¸ÑÐ¸Ð¸.
entity.notification.Promo24HoursLeftNotification.baseMessage=Ð¡Ð´ÐµÐ»Ð°Ð¹ÑÐµ Ð¿ÐµÑÐ²ÑÐ¹ Ð·Ð°ÐºÐ°Ð· Ð½Ð° ÑÑÐ¼Ð¼Ñ Ð¾Ñ 10 000 ÑÑÐ±Ð»ÐµÐ¹ ÑÐµÐ¹ÑÐ°Ñ Ð¸ Ð¿Ð¾Ð»ÑÑÐ¸ÑÐµ ÑÐºÐ¸Ð´ÐºÑ 5% Ð¿Ð¾ Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´Ñ WELCOME5
entity.notification.YouWillLikeItNotification.title=ÐÐ°Ð¼ ÑÑÐ¾ Ð¿Ð¾Ð½ÑÐ°Ð²Ð¸ÑÑÑ
entity.notification.YouWillLikeItNotification.baseMessage=ÐÑÐµÐ¼Ñ Ð´ÐµÐ»Ð°ÑÑ Ð²ÑÐ³Ð¾Ð´Ð½ÑÐµ Ð¿Ð¾ÐºÑÐ¿ÐºÐ¸! ÐÑ ÑÐ¾Ð±ÑÐ°Ð»Ð¸ Ð´Ð»Ñ Ð²Ð°Ñ Ð»ÑÑÑÐ¸Ðµ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ ÑÑÐ¾Ð¹ Ð½ÐµÐ´ÐµÐ»Ð¸
entity.notification.LostPaymentNotification.title=ÐÑÑÐ°Ð»ÑÑ Ð²ÑÐµÐ³Ð¾ 1ï¸â£ ÑÐ°Ð³ Ð´Ð¾ Ð¿Ð¾ÐºÑÐ¿ÐºÐ¸!
entity.notification.CreatePublicationNotification.title=Ð Ð°Ð·Ð³ÑÑÐ·Ð¸ÑÐµ Ð³Ð°ÑÐ´ÐµÑÐ¾Ð±
entity.notification.CreatePublicationNotification.baseMessage=Ð Ð°Ð·Ð¼ÐµÑÐ°Ð¹ÑÐµ Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½Ð¸Ñ Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð¶Ðµ ÑÐ²Ð¾Ð¸Ñ Ð²ÐµÑÐµÐ¹, ÑÑÐ¾Ð±Ñ Ð·Ð°ÑÐ°Ð±Ð°ÑÑÐ²Ð°ÑÑ Ð½Ð° Ð½Ð¾Ð²ÑÐµ Ð¿Ð¾ÐºÑÐ¿ÐºÐ¸
entity.notification.OrderAgentReportConfirmedNotification.title=ÐÐ¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ñ Ð´Ð°Ð½Ð½ÑÐµ Ð¿Ð¾ Ð·Ð°ÐºÐ°Ð·Ñ â {0}
entity.notification.OrderAgentReportConfirmedNotification.baseMessage=ÐÐ¶Ð¸Ð´Ð°Ð¹ÑÐµ Ð·Ð°ÑÐ¸ÑÐ»ÐµÐ½Ð¸Ñ ÑÑÐµÐ´ÑÑÐ²
entity.notification.OrderConfirmedNotification.title=ÐÐ°ÐºÐ°Ð· â {0} Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½
entity.notification.OrderConfirmedNotification.baseMessage=Ð¡ÐºÐ¾ÑÐ¾ ÐºÑÑÑÐµÑ Ð·Ð°Ð±ÐµÑÑÑ Ð²Ð°ÑÑ Ð¿Ð¾ÐºÑÐ¿ÐºÑ Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ° Ð¸ Ð¿ÑÐ¸Ð²ÐµÐ·ÑÑ Ð² Ð½Ð°Ñ Ð¾ÑÐ¸Ñ Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.notification.OnlineOrderForBoutiqueProductNotification.title=ÐÐ¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÐµ Ð¾Ð½Ð»Ð°Ð¹Ð½-Ð·Ð°ÐºÐ°Ð· {0} Ð½Ð° ÑÐ¾Ð²Ð°Ñ {1} Ð² Ð±ÑÑÐ¸ÐºÐµ
entity.notification.OnlineOrderForBoutiqueProductNotification.baseMessage=ÐÐ½Ð»Ð°Ð¹Ð½-Ð·Ð°ÐºÐ°Ð· Ð½Ð° ÑÐ¾Ð²Ð°Ñ Ð² Ð±ÑÑÐ¸ÐºÐµ
entity.notification.OrderDeliveredToBuyerRequestConfirmationNotification.title=ÐÐ°ÐºÐ°Ð· â {0} Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½
entity.notification.OrderDeliveredToBuyerRequestConfirmationNotification.baseMessage=ÐÐ¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÐµ, ÑÑÐ¾ Ð¾Ð½ Ñ Ð²Ð°Ñ, ÑÑÐ¾Ð±Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÐµÑ Ð·Ð½Ð°Ð», ÑÑÐ¾ ÑÐ´ÐµÐ»ÐºÐ° ÑÐ¾ÑÑÐ¾ÑÐ»Ð°ÑÑ
entity.notification.OrderDeliveredToBuyerNotification.title=ÐÐ°ÐºÐ°Ð· â {0} Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½
entity.notification.OrderDeliveredToBuyerNotification.baseMessage=ÐÐ¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÐµ, ÑÑÐ¾ Ð¾Ð½ Ñ Ð²Ð°Ñ, ÑÑÐ¾Ð±Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÐµÑ Ð·Ð½Ð°Ð», ÑÑÐ¾ ÑÐ´ÐµÐ»ÐºÐ° ÑÐ¾ÑÑÐ¾ÑÐ»Ð°ÑÑ
entity.notification.OrderConfirmedPartlyNotification.title=ÐÐ°ÐºÐ°Ð· â {0} ÑÐ°ÑÑÐ¸ÑÐ½Ð¾ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÑÐ½
entity.notification.OrderDeliveredToBuyerNeedAgentReportNotification.ProSeller.title=ÐÐ¾Ð»ÑÑÐ¸ÑÐµ Ð²ÑÐ¿Ð»Ð°ÑÑ Ð¿Ð¾ Ð·Ð°ÐºÐ°Ð·Ñ â {0}
entity.notification.OrderDeliveredToBuyerNeedAgentReportNotification.Seller.title=ÐÐ°ÐºÐ°Ð· â {0} Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½
entity.notification.OrderConfirmedPartlyNotification.baseMessage.singular=ÐÑÐ¾Ð´Ð°Ð²ÐµÑ Ð¾ÑÐ¼ÐµÐ½Ð¸Ð» ÑÐ°ÑÑÑ Ð·Ð°ÐºÐ°Ð·Ð°. ÐÐ¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð½ÑÐ¹ ÑÐ¾Ð²Ð°Ñ Ð¼Ñ Ð·Ð°Ð±ÐµÑÑÐ¼ Ð² Ð¾ÑÐ¸Ñ Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ.
entity.notification.OrderConfirmedPartlyNotification.baseMessage.plural=ÐÑÐ¾Ð´Ð°Ð²ÐµÑ Ð¾ÑÐ¼ÐµÐ½Ð¸Ð» ÑÐ°ÑÑÑ Ð·Ð°ÐºÐ°Ð·Ð°. ÐÐ¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð½ÑÐµ ÑÐ¾Ð²Ð°ÑÑ Ð¼Ñ Ð·Ð°Ð±ÐµÑÑÐ¼ Ð² Ð¾ÑÐ¸Ñ Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ.
entity.notification.OrderDeliveredToBuyerNeedAgentReportNotification.ProSeller.baseMessage=ÐÐ¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÐµ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð´Ð»Ñ Ð¿Ð¾Ð»ÑÑÐµÐ½Ð¸Ñ Ð²ÑÐ¿Ð»Ð°ÑÑ. ÐÑ Ð¿ÐµÑÐµÐ²ÐµÐ´ÑÐ¼ Ð´ÐµÐ½ÑÐ³Ð¸ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ ÑÑÑÐ¾Ðº Ð¿Ð¾ÑÐ»Ðµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ñ.
entity.notification.OrderDeliveredToBuyerNeedAgentReportNotification.Seller.baseMessage=ÐÐ¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÐµ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð´Ð»Ñ Ð¿Ð¾Ð»ÑÑÐµÐ½Ð¸Ñ Ð²ÑÐ¿Ð»Ð°ÑÑ. ÐÑ Ð¿ÐµÑÐµÐ²ÐµÐ´ÑÐ¼ Ð´ÐµÐ½ÑÐ³Ð¸ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 3 Ð´Ð½ÐµÐ¹ Ð¿Ð¾ÑÐ»Ðµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ñ.
entity.notification.LostPaymentNotification.baseMessage=ÐÑÐ¸ Ð¾ÑÐ¾ÑÐ¼Ð»ÐµÐ½Ð¸Ð¸ Ð·Ð°ÐºÐ°Ð·Ð° ÑÑÐ¾-ÑÐ¾ Ð¿Ð¾ÑÐ»Ð¾ Ð½Ðµ ÑÐ°Ðº, Ð¿Ð¾Ð¿ÑÐ¾Ð±ÑÐ¹ÑÐµ ÐµÑÐµ ÑÐ°Ð· ð
entity.notification.LikedBrandProductPublishedNotification.title=ÐÐ¾Ð²ÑÐµ Ð¿Ð¾ÑÑÑÐ¿Ð»ÐµÐ½Ð¸Ñ Ð¾Ñ Ð²Ð°ÑÐ¸Ñ Ð»ÑÐ±Ð¸Ð¼ÑÑ Ð±ÑÐµÐ½Ð´Ð¾Ð²
entity.notification.CartItemInBoutique.title=Ð¢ÐµÐ¿ÐµÑÑ Ð² Ð±ÑÑÐ¸ÐºÐµ OSKELLY!
entity.notification.CartItemInBoutique.baseMessage=ÐÑÐ¸Ð¼ÐµÑÑÑÐµ ÑÐ¾Ð²Ð°Ñ Ð¸Ð· Ð²Ð°ÑÐµÐ¹ ÐºÐ¾ÑÐ·Ð¸Ð½Ñ Ð² Ð±ÑÑÐ¸ÐºÐµ Ð¿Ð¾ Ð°Ð´ÑÐµÑÑ {0}.
entity.notification.WishlistItemInBoutique.title=Ð¢ÐµÐ¿ÐµÑÑ Ð² Ð±ÑÑÐ¸ÐºÐµ OSKELLY!
entity.notification.WishlistItemInBoutique.baseMessage=ÐÑÐ¸Ð¼ÐµÑÑÑÐµ ÑÐ¾Ð²Ð°Ñ Ð¸Ð· Ð²Ð°ÑÐµÐ³Ð¾ Ð²Ð¸ÑÐ»Ð¸ÑÑÐ° Ð² Ð±ÑÑÐ¸ÐºÐµ Ð¿Ð¾ Ð°Ð´ÑÐµÑÑ {0}.
entity.notification.OrderDeliveredToBuyerWaitingForAgentReportNotification.title=ÐÐ°ÐºÐ°Ð· â {0} Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½
entity.notification.OrderDeliveredToBuyerWaitingForAgentReportNotification.baseMessage=ÐÑ Ð¿Ð¾Ð»ÑÑÐ¸ÑÐµ Ð²ÑÐ¿Ð»Ð°ÑÑ ÑÐµÑÐµÐ· 7 Ð´Ð½ÐµÐ¹
entity.notification.OrderDeliveredToExpertiseNotification.title=ÐÐ°ÐºÐ°Ð· â {0} Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½ Ð² Ð¾ÑÐ¸Ñ Oskelly
entity.notification.OrderDeliveredToExpertiseNotification.baseMessage.singular=Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð¿ÑÐ¾Ð²ÐµÑÐ¸Ð¼ ÑÐ¾Ð²Ð°Ñ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ
entity.notification.OrderDeliveredToExpertiseNotification.baseMessage.plural=Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð¿ÑÐ¾Ð²ÐµÑÐ¸Ð¼ ÑÐ¾Ð²Ð°ÑÑ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ
entity.notification.OrderDeliveringFromOfficeToBuyerNotification.title=ÐÐ°ÐºÐ°Ð· â {0} Ð¿ÐµÑÐµÐ´Ð°Ð½ Ð² ÐºÑÑÑÐµÑÑÐºÑÑ ÑÐ»ÑÐ¶Ð±Ñ
entity.notification.OrderDeliveringFromOfficeToBuyerNotification.baseMessage=ÐÑÑÑÐµÑ ÑÐ²ÑÐ¶ÐµÑÑÑ Ñ Ð²Ð°Ð¼Ð¸ Ð·Ð°ÑÐ°Ð½ÐµÐµ Ð¸ ÑÐ¾Ð³Ð»Ð°ÑÑÐµÑ Ð²ÑÐµÐ¼Ñ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸
entity.notification.OrderDeliveringFromSellerToOfficeNotification.title=ÐÑÑÑÐµÑ Ð·Ð°Ð±ÑÐ°Ð» Ð·Ð°ÐºÐ°Ð· â {0}
entity.notification.OrderDeliveringFromSellerToOfficeNotification.baseMessage=Ð¡ÐºÐ¾ÑÐ¾ Ð¾Ð½ Ð¿ÑÐ¸ÐµÐ´ÐµÑ Ðº Ð½Ð°Ð¼ Ð² Ð¾ÑÐ¸Ñ Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.notification.OrderExpertiseFailedNotification.title=ÐÐ°ÐºÐ°Ð· â{0} Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.notification.OrderExpertiseFailedNotification.baseMessage=ÐÐ°ÑÐ¸ÑÐ»ÐµÐ½Ð½ÑÐµ ÑÑÐµÐ´ÑÑÐ²Ð° Ð±ÑÐ´ÑÑ ÑÐ°Ð·Ð¼Ð¾ÑÐ¾Ð¶ÐµÐ½Ñ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 12 ÑÐ°ÑÐ¾Ð².
entity.notification.OrderExpertiseFakeNotification.title=ÐÐ°ÐºÐ°Ð· â {0} Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.notification.OrderExpertiseFakeNotification.baseMessage=Ð¢Ð¾Ð²Ð°Ñ Ð½Ðµ ÑÐ²Ð»ÑÐµÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»Ð¾Ð¼. ÐÐµÐ½ÑÐ³Ð¸ Ð·Ð° Ð·Ð°ÐºÐ°Ð· Ð¼Ñ Ð²ÐµÑÐ½ÐµÐ¼ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ.
entity.notification.OrderExpertiseImpossibleDetermineAuthenticityNotification.title=ÐÐ°ÐºÐ°Ð· â {0} Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.notification.OrderExpertiseImpossibleDetermineAuthenticityNotification.baseMessage=ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð³Ð»Ð¸ ÑÐ±ÐµÐ´Ð¸ÑÑÑÑ Ð½Ð° 100%, ÑÑÐ¾ ÑÐ¾Ð²Ð°Ñ ÑÐ²Ð»ÑÐµÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»Ð¾Ð¼. ÐÐµÐ½ÑÐ³Ð¸ Ð·Ð° Ð·Ð°ÐºÐ°Ð· Ð¼Ñ Ð²ÐµÑÐ½ÐµÐ¼ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ.
entity.notification.OrderExpertiseReconciliationFailedNotification.title=ÐÐ°ÐºÐ°Ð· â {0} Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.notification.OrderExpertiseReconciliationFailedNotification.baseMessage=ÐÑÐ°Ð½ÑÑ Ð¿Ð¾ ÑÐ¾Ð²Ð°ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ. ÐÐµÐ½ÑÐ³Ð¸ Ð¼Ñ Ð²ÐµÑÐ½ÐµÐ¼ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ.
entity.notification.OrderExpertiseFailedMultipleNotification.title=ÐÐ°ÐºÐ°Ð· â {0} Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.notification.OrderExpertiseFailedMultipleNotification.baseMessage=ÐÐµÑÐ°Ð»Ð¸ â Ð² ÑÑÐ°ÑÑÑÐµ Ð·Ð°ÐºÐ°Ð·Ð°. ÐÐµÐ½ÑÐ³Ð¸ Ð¼Ñ Ð²ÐµÑÐ½ÐµÐ¼ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ.
entity.notification.OrderExpertisePassedNotification.title=ÐÐ°ÐºÐ°Ð· â {0} Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.notification.OrderExpertisePassedNotification.baseMessage.singular=Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÑÐ¾Ð²Ð°Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð²Ð°Ð¼
entity.notification.OrderExpertisePassedNotification.baseMessage.plural=Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÑÐ¾Ð²Ð°ÑÑ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð²Ð°Ð¼
entity.notification.OrderExpertisePassedPartlyNotification.title=Ð§Ð°ÑÑÑ Ð·Ð°ÐºÐ°Ð·Ð° â {0} Ð½Ðµ Ð¿ÑÐ¾ÑÐ»Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.notification.OrderExpertisePassedPartlyNotification.baseMessage.singularPassedSingularRejected={0} {1} Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ. ÐÐµÐ½ÑÐ³Ð¸ Ð·Ð° Ð½ÐµÐ³Ð¾ Ð¼Ñ Ð²ÐµÑÐ½ÐµÐ¼ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ. ÐÑÑÐ³Ð¾Ð¹ ÑÐ¾Ð²Ð°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼.
entity.notification.OrderExpertisePassedPartlyNotification.baseMessage.singularPassedPluralRejected={0} {1} Ð½Ðµ Ð¿ÑÐ¾ÑÐ»Ð¸ Ð¿ÑÐ¾Ð²ÐµÑÐºÑ. ÐÐµÐ½ÑÐ³Ð¸ Ð·Ð° Ð½Ð¸Ñ Ð¼Ñ Ð²ÐµÑÐ½ÐµÐ¼ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ. ÐÑÑÐ³Ð¾Ð¹ ÑÐ¾Ð²Ð°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼.
entity.notification.OrderExpertisePassedPartlyNotification.baseMessage.pluralPassedSingularRejected={0} {1} Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ. ÐÐµÐ½ÑÐ³Ð¸ Ð·Ð° Ð½ÐµÐ³Ð¾ Ð¼Ñ Ð²ÐµÑÐ½ÐµÐ¼ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ. ÐÑÑÐ³Ð¸Ðµ ÑÐ¾Ð²Ð°ÑÑ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼.
entity.notification.OrderExpertisePassedPartlyNotification.baseMessage.pluralPassedPluralRejected={0} {1} Ð½Ðµ Ð¿ÑÐ¾ÑÐ»Ð¸ Ð¿ÑÐ¾Ð²ÐµÑÐºÑ. ÐÐµÐ½ÑÐ³Ð¸ Ð·Ð° Ð½Ð¸Ñ Ð¼Ñ Ð²ÐµÑÐ½ÐµÐ¼ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ. ÐÑÑÐ³Ð¸Ðµ ÑÐ¾Ð²Ð°ÑÑ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼.
entity.notification.OrderExpertisePassedWithDefectNotification.title=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ Ð·Ð°ÐºÐ°Ð·Ñ â {0}
entity.notification.OrderExpertisePassedWithDefectNotification.baseMessage.singular=Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÑÐ¾Ð²Ð°Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼. ÐÐµÑÐ°Ð»Ð¸ â Ð² ÑÑÐ°ÑÑÑÐµ Ð·Ð°ÐºÐ°Ð·Ð°.
entity.notification.OrderExpertisePassedWithDefectNotification.baseMessage.plural=Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÑÐ¾Ð²Ð°ÑÑ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼. ÐÐµÑÐ°Ð»Ð¸ â Ð² ÑÑÐ°ÑÑÑÐµ Ð·Ð°ÐºÐ°Ð·Ð°.
entity.notification.OrderExpertisePassedWithDefectWithoutDiscountNotification.title=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ Ð·Ð°ÐºÐ°Ð·Ñ â {0}
entity.notification.OrderExpertisePassedWithDefectWithoutDiscountNotification.baseMessage.singular=Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÑÐ¾Ð²Ð°Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼
entity.notification.OrderExpertisePassedWithDefectWithoutDiscountNotification.baseMessage.plural=Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÑÐ¾Ð²Ð°ÑÑ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼
entity.notification.OrderExpertisePassedWithDefectMultipleNotification.title=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ Ð·Ð°ÐºÐ°Ð·Ñ â {0}
entity.notification.OrderExpertisePassedWithDefectMultipleNotification.baseMessage=Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÑÐ¾Ð²Ð°ÑÑ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼. ÐÐµÑÐ°Ð»Ð¸ â Ð² ÑÑÐ°ÑÑÑÐµ Ð·Ð°ÐºÐ°Ð·Ð°.
entity.notification.OrderHeldNotification.title=ÐÐ°ÐºÐ°Ð· â {0} Ð¾ÑÐ¾ÑÐ¼Ð»ÐµÐ½
entity.notification.OrderHeldNotification.baseMessage=ÐÑ Ð¾Ð¿Ð¾Ð²ÐµÑÑÐ¸Ð»Ð¸ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ° Ð¾ Ð²Ð°ÑÐµÐ¹ Ð¿Ð¾ÐºÑÐ¿ÐºÐµ, Ð¾Ð¶Ð¸Ð´Ð°Ð¹ÑÐµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ñ Ð·Ð°ÐºÐ°Ð·Ð°
entity.notification.OrderNeedConfirmationNotification.title=Ð£ Ð²Ð°Ñ Ð½Ð¾Ð²ÑÐ¹ Ð·Ð°ÐºÐ°Ð· ð
entity.notification.OrderNeedConfirmationNotification.isBoutiqueOrder.title=ÐÐ¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÐµ Ð¾ÑÐ³ÑÑÐ·ÐºÑ ÑÐ¾Ð²Ð°ÑÐ°.
entity.notification.OrderNeedConfirmationNotification.isOnlineBoutiqueOrder.title=ÐÐ°Ñ ÑÐ¾Ð²Ð°Ñ Ð¿ÑÐ¾Ð´Ð°Ð½ Ð½Ð° Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ðµ!
entity.notification.OrderNeedConfirmationNotification.baseMessage=ÐÐ¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÐµ ÐµÐ³Ð¾ Ð´Ð»Ñ Ð¾ÑÐ¾ÑÐ¼Ð»ÐµÐ½Ð¸Ñ ÑÐ´ÐµÐ»ÐºÐ¸.
entity.notification.OrderNeedConfirmationNotification.isBoutiqueOrder.baseMessage=ÐÑ Ð³Ð¾ÑÐ¾Ð²Ñ Ð·Ð°Ð±ÑÐ°ÑÑ Ð²Ð°Ñ ÑÐ¾Ð²Ð°Ñ Ð² OSKELLY. ÐÐ¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÐµ, Ð¿Ð¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¾ÑÐ³ÑÑÐ·ÐºÑ Ð² ÐÐ¸ÑÐ½Ð¾Ð¼ ÐºÐ°Ð±Ð¸Ð½ÐµÑÐµ
entity.notification.OrderNeedConfirmationNotification.isOnlineBoutiqueOrder.baseMessage=ÐÐ°Ð¼ Ð½Ðµ Ð½ÑÐ¶Ð½Ð¾ Ð½Ð¸ÑÐµÐ³Ð¾ Ð´ÐµÐ»Ð°ÑÑ, Ð¼Ñ ÑÐ°Ð¼Ð¸ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸Ð¼ Ð¿ÑÐ¾Ð´Ð°Ð¶Ñ Ð¸ Ð¾ÑÐ³ÑÑÐ·Ð¸Ð¼ ÑÐ¾Ð²Ð°Ñ, Ð¸ ÑÐ¾Ð¾Ð±ÑÐ¸Ð¼ Ð²Ð°Ð¼, ÐºÐ¾Ð³Ð´Ð° ÑÐ¾Ð²Ð°Ñ Ð±ÑÐ´ÐµÑ Ñ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ.
entity.notification.OrderPickingUpFromOfficeNotification.title=ÐÐ°ÐºÐ°Ð· â {0} Ð³Ð¾ÑÐ¾Ð² Ðº Ð¾ÑÐ¿ÑÐ°Ð²ÐºÐµ
entity.notification.OrderPickingUpFromOfficeNotification.baseMessage=ÐÑÑÐ»ÐµÐ¶Ð¸Ð²Ð°Ð¹ÑÐµ ÑÑÐ°ÑÑÑ Ð² Ð»Ð¸ÑÐ½Ð¾Ð¼ ÐºÐ°Ð±Ð¸Ð½ÐµÑÐµ
entity.notification.OrderPickupDeclinedNotification.title=ÐÐ°ÐºÐ°Ð· â{0} Ð¾ÑÐ¼ÐµÐ½ÑÐ½
entity.notification.OrderPickupDeclinedNotification.baseMessage=ÐÑÐ¾Ð´Ð°Ð²ÐµÑ Ð½Ðµ Ð¿ÐµÑÐµÐ´Ð°Ð» Ð·Ð°ÐºÐ°Ð· ÐºÑÑÑÐµÑÑ. ÐÐµÐ½ÑÐ³Ð¸ Ð¼Ñ Ð²ÐµÑÐ½ÐµÐ¼ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ.
entity.notification.OrderRejectedNotification.title=ÐÑÐ¾Ð´Ð°Ð²ÐµÑ Ð¾ÑÐ¼ÐµÐ½Ð¸Ð» Ð·Ð°ÐºÐ°Ð· â{0}
entity.notification.OrderRejectedNotification.baseMessage=ÐÐµÐ½ÑÐ³Ð¸ Ð¼Ñ Ð²ÐµÑÐ½ÐµÐ¼ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ
entity.notification.SaleCompletedNotification.title=ÐÐ¶Ð¸Ð´Ð°Ð¹ÑÐµ Ð²ÑÐ¿Ð»Ð°ÑÑ Ð¿Ð¾ Ð·Ð°ÐºÐ°Ð·Ñ â {0}
entity.notification.SaleCompletedNotification.baseMessage=ÐÑ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð»Ð¸ Ð´ÐµÐ½ÑÐ³Ð¸ Ð½Ð° Ð²Ð°Ñ ÑÑÑÑ. Ð¡ÑÐ¾ÐºÐ¸ Ð¿Ð¾ÑÑÑÐ¿Ð»ÐµÐ½Ð¸Ñ ÑÑÐµÐ´ÑÑÐ² Ð·Ð°Ð²Ð¸ÑÑÑ Ð¾Ñ Ð²Ð°ÑÐµÐ³Ð¾ Ð±Ð°Ð½ÐºÐ°.
entity.notification.SaleConfirmedNotification.title=ÐÐ°ÐºÐ°Ð· â {0} Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½
entity.notification.SaleConfirmedNotification.baseMessage.singular=Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð·Ð°Ð±ÐµÑÑÐ¼ ÑÐ¾Ð²Ð°Ñ Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ. ÐÐµ Ð·Ð°Ð±ÑÐ´ÑÑÐµ Ð¿ÑÐ¸Ð²ÐµÑÑÐ¸ ÐµÐ³Ð¾ Ð² Ð¿Ð¾ÑÑÐ´Ð¾Ðº Ð¸ Ð¾Ð¶Ð¸Ð´Ð°Ð¹ÑÐµ Ð·Ð²Ð¾Ð½ÐºÐ° ÐºÑÑÑÐµÑÐ°.
entity.notification.SaleConfirmedNotification.baseMessage.plural=Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð·Ð°Ð±ÐµÑÑÐ¼ ÑÐ¾Ð²Ð°ÑÑ Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ. ÐÐµ Ð·Ð°Ð±ÑÐ´ÑÑÐµ Ð¿ÑÐ¸Ð²ÐµÑÑÐ¸ Ð¸Ñ Ð² Ð¿Ð¾ÑÑÐ´Ð¾Ðº Ð¸ Ð¾Ð¶Ð¸Ð´Ð°Ð¹ÑÐµ Ð·Ð²Ð¾Ð½ÐºÐ° ÐºÑÑÑÐµÑÐ°.
entity.notification.SaleConfirmedPartlyNotification.title=ÐÐ°ÐºÐ°Ð· â{0} ÑÐ°ÑÑÐ¸ÑÐ½Ð¾ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½
entity.notification.SaleConfirmedPartlyNotification.baseMessage.singularConfirmedSingularRejected=ÐÑ Ð·Ð°Ð±ÐµÑÑÐ¼ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÑÐ½Ð½ÑÐ¹ ÑÐ¾Ð²Ð°Ñ Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ. ÐÐµ Ð·Ð°Ð±ÑÐ´ÑÑÐµ Ð¿ÑÐ¸Ð²ÐµÑÑÐ¸ ÐµÐ³Ð¾ Ð² Ð¿Ð¾ÑÑÐ´Ð¾Ðº Ð¸ Ð¾Ð¶Ð¸Ð´Ð°Ð¹ÑÐµ Ð·Ð²Ð¾Ð½ÐºÐ° ÐºÑÑÑÐµÑÐ°.\n\nÐÑÐ¼ÐµÐ½ÐµÐ½Ð½ÑÑ Ð²ÐµÑÑ Ð¼Ñ ÑÐ½ÑÐ»Ð¸ Ñ Ð¿ÑÐ¾Ð´Ð°Ð¶Ð¸. ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¿ÑÐ¾Ð²ÐµÑÑÑÐµ Ð´ÑÑÐ³Ð¸Ðµ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð½ÑÐµ ÑÐ¾Ð²Ð°ÑÑ Ð¸ ÑÐºÑÐ¾Ð¹ÑÐµ ÑÐ¾, ÑÑÐ¾ Ð½Ðµ Ð¿ÑÐ¾Ð´Ð°ÑÑÐµ.
entity.notification.SaleConfirmedPartlyNotification.baseMessage.singularConfirmedPluralRejected=ÐÑ Ð·Ð°Ð±ÐµÑÑÐ¼ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÑÐ½Ð½ÑÐ¹ ÑÐ¾Ð²Ð°Ñ Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ. ÐÐµ Ð·Ð°Ð±ÑÐ´ÑÑÐµ Ð¿ÑÐ¸Ð²ÐµÑÑÐ¸ ÐµÐ³Ð¾ Ð² Ð¿Ð¾ÑÑÐ´Ð¾Ðº Ð¸ Ð¾Ð¶Ð¸Ð´Ð°Ð¹ÑÐµ Ð·Ð²Ð¾Ð½ÐºÐ° ÐºÑÑÑÐµÑÐ°.\n\nÐÑÐ¼ÐµÐ½ÐµÐ½Ð½ÑÐµ Ð²ÐµÑÐ¸ Ð¼Ñ ÑÐ½ÑÐ»Ð¸ Ñ Ð¿ÑÐ¾Ð´Ð°Ð¶Ð¸. ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¿ÑÐ¾Ð²ÐµÑÑÑÐµ Ð´ÑÑÐ³Ð¸Ðµ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð½ÑÐµ ÑÐ¾Ð²Ð°ÑÑ Ð¸ ÑÐºÑÐ¾Ð¹ÑÐµ ÑÐ¾, ÑÑÐ¾ Ð½Ðµ Ð¿ÑÐ¾Ð´Ð°ÑÑÐµ.
entity.notification.SaleConfirmedPartlyNotification.baseMessage.pluralConfirmedSingularRejected=ÐÑ Ð·Ð°Ð±ÐµÑÑÐ¼ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÑÐ½Ð½ÑÐµ ÑÐ¾Ð²Ð°ÑÑ Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ. ÐÐµ Ð·Ð°Ð±ÑÐ´ÑÑÐµ Ð¿ÑÐ¸Ð²ÐµÑÑÐ¸ Ð¸Ñ Ð² Ð¿Ð¾ÑÑÐ´Ð¾Ðº Ð¸ Ð¾Ð¶Ð¸Ð´Ð°Ð¹ÑÐµ Ð·Ð²Ð¾Ð½ÐºÐ° ÐºÑÑÑÐµÑÐ°.\n\nÐÑÐ¼ÐµÐ½ÐµÐ½Ð½ÑÑ Ð²ÐµÑÑ Ð¼Ñ ÑÐ½ÑÐ»Ð¸ Ñ Ð¿ÑÐ¾Ð´Ð°Ð¶Ð¸. ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¿ÑÐ¾Ð²ÐµÑÑÑÐµ Ð´ÑÑÐ³Ð¸Ðµ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð½ÑÐµ ÑÐ¾Ð²Ð°ÑÑ Ð¸ ÑÐºÑÐ¾Ð¹ÑÐµ ÑÐ¾, ÑÑÐ¾ Ð½Ðµ Ð¿ÑÐ¾Ð´Ð°ÑÑÐµ.
entity.notification.SaleConfirmedPartlyNotification.baseMessage.pluralConfirmedPluralRejected=ÐÑ Ð·Ð°Ð±ÐµÑÑÐ¼ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÑÐ½Ð½ÑÐµ ÑÐ¾Ð²Ð°ÑÑ Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ. ÐÐµ Ð·Ð°Ð±ÑÐ´ÑÑÐµ Ð¿ÑÐ¸Ð²ÐµÑÑÐ¸ Ð¸Ñ Ð² Ð¿Ð¾ÑÑÐ´Ð¾Ðº Ð¸ Ð¾Ð¶Ð¸Ð´Ð°Ð¹ÑÐµ Ð·Ð²Ð¾Ð½ÐºÐ° ÐºÑÑÑÐµÑÐ°.\n\nÐÑÐ¼ÐµÐ½ÐµÐ½Ð½ÑÐµ Ð²ÐµÑÐ¸ Ð¼Ñ ÑÐ½ÑÐ»Ð¸ Ñ Ð¿ÑÐ¾Ð´Ð°Ð¶Ð¸. ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¿ÑÐ¾Ð²ÐµÑÑÑÐµ Ð´ÑÑÐ³Ð¸Ðµ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð½ÑÐµ ÑÐ¾Ð²Ð°ÑÑ Ð¸ ÑÐºÑÐ¾Ð¹ÑÐµ ÑÐ¾, ÑÑÐ¾ Ð½Ðµ Ð¿ÑÐ¾Ð´Ð°ÑÑÐµ.
entity.notification.SaleDeliveredToExpertiseNotification.title=ÐÐ°ÐºÐ°Ð· â{0} Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½ Ð² Ð¾ÑÐ¸Ñ Oskelly
entity.notification.SaleDeliveredToExpertiseNotification.baseMessage.singular=Ð¡ÐºÐ¾ÑÐ¾ Ð½Ð°ÑÐ½ÑÐ¼ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ. ÐÑÐ¾Ð²ÐµÑÐ¸Ð¼ ÑÐ¾Ð²Ð°Ñ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ.
entity.notification.SaleDeliveredToExpertiseNotification.baseMessage.plural=Ð¡ÐºÐ¾ÑÐ¾ Ð½Ð°ÑÐ½ÑÐ¼ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ. ÐÑÐ¾Ð²ÐµÑÐ¸Ð¼ ÑÐ¾Ð²Ð°ÑÑ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ.
entity.notification.SaleDeliveringFromOfficeToBuyerNotification.title=ÐÐ°ÐºÐ°Ð· â{0} Ð¿ÐµÑÐµÐ´Ð°Ð½ ÐºÑÑÑÐµÑÑ
entity.notification.SaleDeliveringFromOfficeToBuyerNotification.baseMessage=Ð¡ÐºÐ¾ÑÐ¾ Ð¾Ð½ Ð¿ÑÐ¸ÐµÐ´ÐµÑ Ðº Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
entity.notification.SaleDeliveringFromOfficeToBuyerNotification.isBoutiqueOrder.baseMessage=ÐÑÑÑÐµÑ Ð·Ð°Ð±ÑÐ°Ð» Ð·Ð°ÐºÐ°Ð· Ð¸ ÑÐºÐ¾ÑÐ¾ Ð¿ÑÐ¸Ð²ÐµÐ·ÐµÑ Ð² Ð±ÑÑÐ¸Ðº
entity.notification.SaleDeliveringFromSellerToOfficeNotification.title=ÐÑÑÑÐµÑ Ð·Ð°Ð±ÑÐ°Ð» Ð·Ð°ÐºÐ°Ð· â {0}
entity.notification.SaleDeliveringFromSellerToOfficeNotification.baseMessage=Ð¡ÐºÐ¾ÑÐ¾ Ð¾Ð½ Ð¿ÑÐ¸ÐµÐ´ÐµÑ Ðº Ð½Ð°Ð¼ Ð² Ð¾ÑÐ¸Ñ Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.notification.SaleExpertiseFailedNotification.title=ÐÐ°ÐºÐ°Ð· â{0} Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.notification.SaleExpertiseFailedNotification.baseMessage=C Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÑÑÑ Ð½Ð°Ñ Ð¼ÐµÐ½ÐµÐ´Ð¶ÐµÑ Ð¸ ÑÐ¾Ð³Ð»Ð°ÑÑÐµÑ Ð²Ð¾Ð·Ð²ÑÐ°Ñ ÑÐ¾Ð²Ð°ÑÐ°
entity.notification.SaleExpertiseFakeNotification.title=ÐÐ°ÐºÐ°Ð· â {0} Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.notification.SaleExpertiseFakeNotification.baseMessage=Ð¢Ð¾Ð²Ð°Ñ Ð½Ðµ ÑÐ²Ð»ÑÐµÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»Ð¾Ð¼. ÐÐµÑÐ°Ð»Ð¸ â Ð² ÑÑÐ°ÑÑÑÐµ Ð·Ð°ÐºÐ°Ð·Ð°.
entity.notification.SaleExpertiseImpossibleDetermineAuthenticityNotification.title=ÐÐ°ÐºÐ°Ð· â {0} Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.notification.SaleExpertiseImpossibleDetermineAuthenticityNotification.baseMessage=ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð³Ð»Ð¸ ÑÐ±ÐµÐ´Ð¸ÑÑÑÑ Ð½Ð° 100%, ÑÑÐ¾ ÑÐ¾Ð²Ð°Ñ ÑÐ²Ð»ÑÐµÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»Ð¾Ð¼. ÐÐµÑÐ°Ð»Ð¸ â Ð² ÑÑÐ°ÑÑÑÐµ Ð·Ð°ÐºÐ°Ð·Ð°.
entity.notification.SaleExpertiseReconciliationFailedNotification.title=ÐÐ°ÐºÐ°Ð· â {0} Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.notification.SaleExpertiseReconciliationFailedNotification.baseMessage=ÐÑÐ°Ð½ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ. ÐÑ Ð¼Ð¾Ð¶ÐµÑÐµ Ð·Ð°Ð±ÑÐ°ÑÑ ÑÐ¾Ð²Ð°Ñ Ð² Ð½Ð°ÑÐµÐ¼ Ð¾ÑÐ¸ÑÐµ Ð¸Ð»Ð¸ Ð²ÑÐ·Ð²Ð°ÑÑ ÐºÑÑÑÐµÑÐ°. ÐÐµÑÐ°Ð»Ð¸ â Ð² ÑÑÐ°ÑÑÑÐµ Ð·Ð°ÐºÐ°Ð·Ð°.
entity.notification.SaleExpertiseFailedMultipleNotification.title=ÐÐ°ÐºÐ°Ð· â {0} Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.notification.SaleExpertiseFailedMultipleNotification.baseMessage=ÐÐµÑÐ°Ð»Ð¸ â Ð² ÑÑÐ°ÑÑÑÐµ Ð·Ð°ÐºÐ°Ð·Ð°. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ñ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ð¸ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ¸Ð¼ÑÑ Ð¾ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµ.
entity.notification.SaleExpertisePassedNotification.title=ÐÐ°ÐºÐ°Ð· â {0} Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.notification.SaleExpertisePassedNotification.baseMessage.singular=Ð¢Ð¾Ð²Ð°Ñ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¹ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÐµÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ.
entity.notification.SaleExpertisePassedNotification.baseMessage.plural=Ð¢Ð¾Ð²Ð°ÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐµ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÑÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð¸Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ.
entity.notification.SaleExpertisePassedNotification.isBoutiqueOrder.baseMessage=Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð´Ð¾ÑÑÐ°Ð²Ð¸Ð¼ ÑÐ¾Ð²Ð°Ñ Ð² Ð±ÑÑÐ¸Ðº
entity.notification.SaleExpertisePassedWithCleaningNotification.title=Ð¡Ð´ÐµÐ»Ð°Ð»Ð¸ ÑÐ¸Ð¼ÑÐ¸ÑÑÐºÑ Ð¿Ð¾ Ð·Ð°ÐºÐ°Ð·Ñ â {0}
entity.notification.SaleExpertisePassedWithCleaningNotification.baseMessage.singular=ÐÑ Ð²ÑÑÐ»Ð¸ ÑÐ°ÑÑÐ¾Ð´Ñ Ð¸Ð· Ð¾Ð±ÑÐµÐ¹ Ð¿ÑÐ¸Ð±ÑÐ»Ð¸ Ð¸ Ð²Ð·ÑÐ»Ð¸ ÑÑÐ¸ Ð·Ð°Ð±Ð¾ÑÑ Ð½Ð° ÑÐµÐ±Ñ
entity.notification.SaleExpertisePassedWithCleaningNotification.baseMessage.plural=ÐÑ Ð²ÑÑÐ»Ð¸ ÑÐ°ÑÑÐ¾Ð´Ñ Ð¸Ð· Ð¾Ð±ÑÐµÐ¹ Ð¿ÑÐ¸Ð±ÑÐ»Ð¸ Ð¸ Ð²Ð·ÑÐ»Ð¸ ÑÑÐ¸ Ð·Ð°Ð±Ð¾ÑÑ Ð½Ð° ÑÐµÐ±Ñ
entity.notification.SaleExpertisePassedWithDefectNotification.title=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ Ð·Ð°ÐºÐ°Ð·Ñ â {0}
entity.notification.SaleExpertisePassedWithDefectNotification.baseMessage.singular=Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÑÐ¾Ð²Ð°Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ. ÐÐµÑÐ°Ð»Ð¸ â Ð² ÑÑÐ°ÑÑÑÐµ Ð·Ð°ÐºÐ°Ð·Ð°.
entity.notification.SaleExpertisePassedWithDefectNotification.baseMessage.plural=Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÑÐ¾Ð²Ð°ÑÑ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ. ÐÐµÑÐ°Ð»Ð¸ â Ð² ÑÑÐ°ÑÑÑÐµ Ð·Ð°ÐºÐ°Ð·Ð°.
entity.notification.SaleExpertisePassedWithDefectNotification.isBoutiqueOrder.baseMessage=Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð´Ð¾ÑÑÐ°Ð²Ð¸Ð¼ ÑÐ¾Ð²Ð°Ñ Ð² Ð±ÑÑÐ¸Ðº. ÐÐ±ÑÐ°ÑÐ¸ÑÐµ Ð²Ð½Ð¸Ð¼Ð°Ð½Ð¸Ðµ, ÑÑÐ¾ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð¿ÑÐµÐ´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½Ð° ÑÐºÐ¸Ð´ÐºÐ° {0} ÑÑÐ±.
entity.notification.SaleExpertisePassedWithDefectWithoutDiscountNotification.title=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ Ð·Ð°ÐºÐ°Ð·Ñ â {0}
entity.notification.SaleExpertisePassedWithDefectWithoutDiscountNotification.baseMessage.singular=Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÑÐ¾Ð²Ð°Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
entity.notification.SaleExpertisePassedWithDefectWithoutDiscountNotification.baseMessage.plural=Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÑÐ¾Ð²Ð°ÑÑ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
entity.notification.SaleExpertisePassedWithDefectMultipleNotification.title=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ Ð·Ð°ÐºÐ°Ð·Ñ â {0}
entity.notification.SaleExpertisePassedWithDefectMultipleNotification.baseMessage=Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÑÐ¾Ð²Ð°ÑÑ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ. ÐÐµÑÐ°Ð»Ð¸ â Ð² ÑÑÐ°ÑÑÑÐµ Ð·Ð°ÐºÐ°Ð·Ð°.
entity.notification.SalePickingUpFromSellerNotification.title=ÐÑÑÑÐµÑ ÐµÐ´ÐµÑ Ðº Ð²Ð°Ð¼ Ð·Ð° Ð·Ð°ÐºÐ°Ð·Ð¾Ð¼ â {0}
entity.notification.SalePickingUpFromSellerNotification.baseMessage=ÐÐ½ ÑÐ²ÑÐ¶ÐµÑÑÑ Ñ Ð²Ð°Ð¼Ð¸ Ð·Ð° ÑÐ°Ñ Ð´Ð¾ Ð¿ÑÐ¸Ð±ÑÑÐ¸Ñ
entity.notification.SalePickupDeclinedNotification.title=ÐÐ°ÐºÐ°Ð· â {0} Ð¾ÑÐ¼ÐµÐ½ÑÐ½
entity.notification.SalePickupDeclinedNotification.baseMessage.singular=ÐÑ ÑÐ½ÑÐ»Ð¸ ÑÐ¾Ð²Ð°Ñ Ñ Ð¿ÑÐ¾Ð´Ð°Ð¶Ð¸. ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¿ÑÐ¾Ð²ÐµÑÑÑÐµ Ð´ÑÑÐ³Ð¸Ðµ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð½ÑÐµ ÑÐ¾Ð²Ð°ÑÑ Ð¸ ÑÐºÑÐ¾Ð¹ÑÐµ ÑÐ¾, ÑÑÐ¾ Ð½Ðµ Ð¿ÑÐ¾Ð´Ð°ÑÑÐµ.
entity.notification.SalePickupDeclinedNotification.baseMessage.plural=ÐÑ ÑÐ½ÑÐ»Ð¸ ÑÐ¾Ð²Ð°ÑÑ Ñ Ð¿ÑÐ¾Ð´Ð°Ð¶Ð¸. ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¿ÑÐ¾Ð²ÐµÑÑÑÐµ Ð´ÑÑÐ³Ð¸Ðµ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð½ÑÐµ ÑÐ¾Ð²Ð°ÑÑ Ð¸ ÑÐºÑÐ¾Ð¹ÑÐµ ÑÐ¾, ÑÑÐ¾ Ð½Ðµ Ð¿ÑÐ¾Ð´Ð°ÑÑÐµ.
entity.notification.SaleRejectedNotification.title=ÐÑ Ð¾ÑÐ¼ÐµÐ½Ð¸Ð»Ð¸ Ð·Ð°ÐºÐ°Ð· â {0}
entity.notification.SaleRejectedNotification.baseMessage.singular=ÐÑ ÑÐ½ÑÐ»Ð¸ ÑÐ¾Ð²Ð°Ñ Ñ Ð¿ÑÐ¾Ð´Ð°Ð¶Ð¸. ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¿ÑÐ¾Ð²ÐµÑÑÑÐµ Ð´ÑÑÐ³Ð¸Ðµ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð½ÑÐµ ÑÐ¾Ð²Ð°ÑÑ Ð¸ ÑÐºÑÐ¾Ð¹ÑÐµ ÑÐ¾, ÑÑÐ¾ Ð½Ðµ Ð¿ÑÐ¾Ð´Ð°ÑÑÐµ.
entity.notification.SaleRejectedNotification.baseMessage.plural=ÐÑ ÑÐ½ÑÐ»Ð¸ ÑÐ¾Ð²Ð°ÑÑ Ñ Ð¿ÑÐ¾Ð´Ð°Ð¶Ð¸. ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¿ÑÐ¾Ð²ÐµÑÑÑÐµ Ð´ÑÑÐ³Ð¸Ðµ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð½ÑÐµ ÑÐ¾Ð²Ð°ÑÑ Ð¸ ÑÐºÑÐ¾Ð¹ÑÐµ ÑÐ¾, ÑÑÐ¾ Ð½Ðµ Ð¿ÑÐ¾Ð´Ð°ÑÑÐµ.
entity.notification.ModerationFailedNotification.title.0=ÐÐµÐ´Ð¾ÑÑÐ°ÑÐ¾ÑÐ½Ð¾ Ð´Ð°Ð½Ð½ÑÑ Ð¾ ÑÐ¾Ð²Ð°ÑÐµ
entity.notification.ModerationFailedNotification.title.1=ÐÑÐ¾ÑÑÐ±Ð° ÑÐºÐ¾ÑÑÐµÐºÑÐ¸ÑÐ¾Ð²Ð°ÑÑ ÑÐµÐ½Ñ
entity.notification.ModerationFailedNotification.title.2=ÐÑÐ¾ÑÑÐ±Ð° ÑÐºÐ¾ÑÑÐµÐºÑÐ¸ÑÐ¾Ð²Ð°ÑÑ ÑÐ¾ÑÐ¾Ð³ÑÐ°ÑÐ¸Ð¸
entity.notification.ModerationFailedNotification.title.3=ÐÐµÐ´Ð¾ÑÑÐ°ÑÐ¾ÑÐ½Ð¾ Ð´Ð°Ð½Ð½ÑÑ Ð¾ ÑÐ¾Ð²Ð°ÑÐµ
entity.notification.ModerationFailedNotification.title.4=ÐÐµÐ´Ð¾ÑÑÐ°ÑÐ¾ÑÐ½Ð¾ Ð´Ð°Ð½Ð½ÑÑ Ð¾ ÑÐ¾Ð²Ð°ÑÐµ
entity.notification.ModerationFailedNotification.baseMessage.0=Ð§ÑÐ¾Ð±Ñ ÑÐ°Ð·Ð¼ÐµÑÑÐ¸ÑÑ ÑÐ¾Ð²Ð°Ñ Ð² ÐºÐ°ÑÐ°Ð»Ð¾Ð³Ðµ, ÑÐ»ÐµÐ´ÑÐ¹ÑÐµ ÑÐµÐºÐ¾Ð¼ÐµÐ½Ð´Ð°ÑÐ¸ÑÐ¼ Ð¼Ð¾Ð´ÐµÑÐ°ÑÐ¾ÑÐ°
entity.notification.ModerationFailedNotification.baseMessage.1=Ð§ÑÐ¾Ð±Ñ ÑÐ°Ð·Ð¼ÐµÑÑÐ¸ÑÑ ÑÐ¾Ð²Ð°Ñ Ð² ÐºÐ°ÑÐ°Ð»Ð¾Ð³Ðµ, ÑÐºÐ°Ð¶Ð¸ÑÐµ Ð±Ð¾Ð»ÐµÐµ ÑÐ¿ÑÐ°Ð²ÐµÐ´Ð»Ð¸Ð²ÑÑ ÑÐµÐ½Ñ Ð¸Ð»Ð¸ Ð¿ÑÐ¸Ð¼Ð¸ÑÐµ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ Ð¼Ð¾Ð´ÐµÑÐ°ÑÐ¾ÑÐ°
entity.notification.ModerationFailedNotification.baseMessage.2=Ð§ÑÐ¾Ð±Ñ ÑÐ°Ð·Ð¼ÐµÑÑÐ¸ÑÑ ÑÐ¾Ð²Ð°Ñ Ð² ÐºÐ°ÑÐ°Ð»Ð¾Ð³Ðµ, Ð·Ð°Ð³ÑÑÐ·Ð¸ÑÐµ ÑÐ¾ÑÐ¾Ð³ÑÐ°ÑÐ¸Ð¸ ÑÐ¾Ð³Ð»Ð°ÑÐ½Ð¾ Ð½Ð°ÑÐ¸Ð¼ ÑÑÐµÐ±Ð¾Ð²Ð°Ð½Ð¸ÑÐ¼
entity.notification.ModerationFailedNotification.baseMessage.3=Ð§ÑÐ¾Ð±Ñ ÑÐ°Ð·Ð¼ÐµÑÑÐ¸ÑÑ ÑÐ¾Ð²Ð°Ñ Ð² ÐºÐ°ÑÐ°Ð»Ð¾Ð³Ðµ, ÑÐ»ÐµÐ´ÑÐ¹ÑÐµ ÑÐµÐºÐ¾Ð¼ÐµÐ½Ð´Ð°ÑÐ¸ÑÐ¼ Ð¼Ð¾Ð´ÐµÑÐ°ÑÐ¾ÑÐ°
entity.notification.ModerationFailedNotification.baseMessage.4=Ð§ÑÐ¾Ð±Ñ ÑÐ°Ð·Ð¼ÐµÑÑÐ¸ÑÑ ÑÐ¾Ð²Ð°Ñ Ð² ÐºÐ°ÑÐ°Ð»Ð¾Ð³Ðµ, ÑÐ»ÐµÐ´ÑÐ¹ÑÐµ ÑÐµÐºÐ¾Ð¼ÐµÐ½Ð´Ð°ÑÐ¸ÑÐ¼ Ð¼Ð¾Ð´ÐµÑÐ°ÑÐ¾ÑÐ°
entity.notification.ModerationPassedNotification.title=Ð¢Ð¾Ð²Ð°Ñ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½
entity.notification.ModerationPassedNotification.baseMessage=Ð¢ÐµÐ¿ÐµÑÑ ÑÐ¾Ð²Ð°Ñ Ð´Ð¾ÑÑÑÐ¿ÐµÐ½ Ð²ÑÐµÐ¼ Ð¿Ð¾ÑÐµÑÐ¸ÑÐµÐ»ÑÐ¼ Oskelly
entity.notification.ModerationRejectedNotification.title=Ð¢Ð¾Ð²Ð°Ñ Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¼Ð¾Ð´ÐµÑÐ°ÑÐ¸Ñ
entity.notification.ModerationRejectedNotification.baseMessage=ÐÐ·Ð½Ð°ÐºÐ¾Ð¼ÑÑÐµÑÑ Ñ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸ÑÐ¼Ð¸ Ð¼Ð¾Ð´ÐµÑÐ°ÑÐ¾ÑÐ° Ð² Ð»Ð¸ÑÐ½Ð¾Ð¼ ÐºÐ°Ð±Ð¸Ð½ÐµÑÐµ.
entity.notification.CompletePublicationNotification.title=ÐÐ¿ÑÐ±Ð»Ð¸ÐºÑÐ¹ÑÐµ ÑÐ¾Ð²Ð°Ñ
entity.notification.CompletePublicationNotification.baseMessage=ÐÑÑÐ°Ð»Ð¾ÑÑ Ð²ÑÐµÐ³Ð¾ Ð½ÐµÑÐºÐ¾Ð»ÑÐºÐ¾ ÑÐ°Ð³Ð¾Ð², ÑÑÐ¾Ð±Ñ Ð·Ð°Ð²ÐµÑÑÐ¸ÑÑ Ð¿ÑÐ±Ð»Ð¸ÐºÐ°ÑÐ¸Ñ ÑÐ¾Ð²Ð°ÑÐ° Ð½Ð° OSKELLY Ð¸ Ð½Ð°Ð¹ÑÐ¸ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ >
entity.notification.CompletePublicationNotificationRangeDay.title=ÐÐµ Ð¾ÑÐºÐ»Ð°Ð´ÑÐ²Ð°Ð¹ÑÐµ!
entity.notification.CompletePublicationNotificationRangeDay.baseMessage=Ð Ð°Ð·Ð¼ÐµÑÑÐ¸ÑÐµ ÑÐ²Ð¾Ð¹ ÑÐ¾Ð²Ð°Ñ Ð½Ð° OSKELLY Ð² Ð½ÐµÑÐºÐ¾Ð»ÑÐºÐ¾ Ð¿ÑÐ¾ÑÑÑÑ ÑÐ°Ð³Ð¾Ð² >
entity.notification.ProductSentToModerationNotification.title=Ð¢Ð¾Ð²Ð°Ñ Ð·Ð°Ð³ÑÑÐ¶ÐµÐ½ Ð¸ Ð¿ÑÐ¾ÑÐ¾Ð´Ð¸Ñ Ð¼Ð¾Ð´ÐµÑÐ°ÑÐ¸Ñ
entity.notification.ProductSentToModerationNotification.baseMessage=ÐÐ¶Ð¸Ð´Ð°Ð¹ÑÐµ ÑÐµÐ·ÑÐ»ÑÑÐ°ÑÐ¾Ð² Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 48 ÑÐ°ÑÐ¾Ð². ÐÑ Ð¿ÑÐ¾Ð²ÐµÑÑÐµÐ¼ ÑÐ°Ð·Ð¼ÐµÑ, ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ðµ, ÑÑÐ¾Ð¸Ð¼Ð¾ÑÑÑ Ð¸ Ð¿Ð¾Ð´Ð»Ð¸Ð½Ð½Ð¾ÑÑÑ ÑÐ¾Ð²Ð°ÑÐ¾Ð².
entity.notification.ProductSentToModerationNotificationAgain.title=Ð¢Ð¾Ð²Ð°Ñ Ð¾ÑÐ¿ÑÐ°Ð²Ð»ÐµÐ½ Ð½Ð° Ð¿Ð¾Ð²ÑÐ¾ÑÐ½ÑÑ Ð¼Ð¾Ð´ÐµÑÐ°ÑÐ¸Ñ
entity.notification.ProductSentToModerationNotificationAgain.baseMessage=Ð¡Ð¾Ð²ÑÐµÐ¼ ÑÐºÐ¾ÑÐ¾ Ð²Ð°Ñ ÑÐ¾Ð²Ð°Ñ ÑÑÐ°Ð½ÐµÑ Ð´Ð¾ÑÑÑÐ¿ÐµÐ½ Ð²ÑÐµÐ¼ Ð¿Ð¾ÑÐµÑÐ¸ÑÐµÐ»ÑÐ¼ OSKELLY
entity.notification.CartPriceDecreasedNotification.title=â¡ï¸ Ð¦ÐµÐ½Ð° ÑÐ½Ð¸Ð¶ÐµÐ½Ð°!
entity.notification.CartPriceDecreasedNotification.baseMessage=Ð¦ÐµÐ½Ð° Ð½Ð° ÑÐ¾Ð²Ð°Ñ Ð¸Ð· Ð²Ð°ÑÐµÐ¹ ÐºÐ¾ÑÐ·Ð¸Ð½Ñ ÑÐ½Ð¸Ð·Ð¸Ð»Ð°ÑÑ Ð½Ð° {0}Ñ. ÐÐ¾ÑÐ¿ÐµÑÐ¸ÑÐµ, Ð¿Ð¾ÐºÐ° ÑÐ¾Ð²Ð°Ñ Ð² Ð½Ð°Ð»Ð¸ÑÐ¸Ð¸ >>
entity.notification.LikePriceDecreasedNotification.title=â¡ï¸ Ð¦ÐµÐ½Ð° ÑÐ½Ð¸Ð¶ÐµÐ½Ð°!
entity.notification.LikePriceDecreasedNotification.baseMessage=Ð¦ÐµÐ½Ð° Ð½Ð° ÑÐ¾Ð²Ð°Ñ Ð¸Ð· Ð²Ð°ÑÐµÐ³Ð¾ Ð¸Ð·Ð±ÑÐ°Ð½Ð½Ð¾Ð³Ð¾ ÑÐ½Ð¸Ð·Ð¸Ð»Ð°ÑÑ Ð½Ð° {0}Ñ. ÐÐ¾ÑÐ¿ÐµÑÐ¸ÑÐµ, Ð¿Ð¾ÐºÐ° ÑÐ¾Ð²Ð°Ñ Ð² Ð½Ð°Ð»Ð¸ÑÐ¸Ð¸ >>
entity.notification.PriceChangedNotification.baseMessage=ÐÐ·Ð¼ÐµÐ½Ð¸Ð»Ð°ÑÑ ÑÐµÐ½Ð° Ð¾ÑÑÐ»ÐµÐ¶Ð¸Ð²Ð°ÐµÐ¼Ð¾Ð³Ð¾ Ð²Ð°Ð¼Ð¸ ÑÐ¾Ð²Ð°ÑÐ°
entity.notification.SubscriptionPriceDecreasedNotification.baseMessage={0} ÑÐ½Ð¸Ð·Ð¸Ð» ÑÐµÐ½Ñ Ð½Ð° {1}! ÐÐ¾ÑÐ¿ÐµÑÐ¸ÑÐµ, Ð¿Ð¾ÐºÐ° ÑÐ¾Ð²Ð°Ñ Ð² Ð½Ð°Ð»Ð¸ÑÐ¸Ð¸ >>
entity.notification.SubscribeCelebritiesNotification.title=â­ï¸ ÐÐ²ÐµÐ·Ð´Ð½ÑÐµ Ð³Ð°ÑÐ´ÐµÑÐ¾Ð±Ñ
entity.notification.SubscribeCelebritiesNotification.baseMessage=ÐÐ° Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ðµ ÑÐ¾Ð±ÑÐ°Ð½Ñ Ð»Ð¾ÑÑ Ð¸ Ð²Ð¸ÑÐ»Ð¸ÑÑÑ Ð·Ð½Ð°Ð¼ÐµÐ½Ð¸ÑÐ¾ÑÑÐµÐ¹. ÐÐ¾Ð´Ð¿Ð¸ÑÐ¸ÑÐµÑÑ Ð¸ ÑÐ»ÐµÐ´Ð¸ÑÐµ Ð·Ð° Ð½Ð¾Ð²ÑÐ¼Ð¸ Ð¿Ð¾ÑÑÑÐ¿Ð»ÐµÐ½Ð¸ÑÐ¼Ð¸ Ð½Ð° Ð¸Ñ ÑÑÑÐ°Ð½Ð¸ÑÐ°Ñ.
entity.notification.SubscribeInstagramNotification.title=ÐÐ¾Ð»ÑÑÐ°Ð¹ Ð±Ð¾Ð½ÑÑÑ Ð² Instagram
entity.notification.SubscribeInstagramNotification.baseMessage=ÐÐ¾Ð´Ð¿Ð¸ÑÑÐ²Ð°Ð¹ÑÐµÑÑ Ð½Ð° Ð¾ÑÐ¸ÑÐ¸Ð°Ð»ÑÐ½ÑÐ¹ Instagram Ð°ÐºÐºÐ°ÑÐ½Ñ Ð¸ Ð²ÑÑÑÐ¿Ð°Ð¹ÑÐµ Ð² ÐºÐ¾Ð¼ÑÑÐ½Ð¸ÑÐ¸ Ð»ÑÑÑÐ¸Ñ Ð´ÑÑÐ·ÐµÐ¹ OSKELLY. Ð ÑÐµÐ¼ Ð¿ÑÐµÐ¸Ð¼ÑÑÐµÑÑÐ²Ð° Ð»ÑÑÑÐ¸Ñ Ð´ÑÑÐ·ÐµÐ¹?
entity.notification.AddBirthdateAndAvatarNotification.title=ÐÐ±Ð½Ð¾Ð²Ð¸ÑÐµ ÑÐ²Ð¾Ð¹ Ð¿ÑÐ¾ÑÐ¸Ð»Ñ
entity.notification.AddBirthdateAndAvatarNotification.baseMessage=ÐÐ°Ð¿Ð¾Ð»Ð½Ð¸ÑÐµ Ð´Ð°ÑÑ ÑÐ²Ð¾ÐµÐ³Ð¾ ÑÐ¾Ð¶Ð´ÐµÐ½Ð¸Ñ, ÑÑÐ¾Ð±Ñ Ð½Ðµ Ð¿ÑÐ¾Ð¿ÑÑÑÐ¸ÑÑ Ð¿ÐµÑÑÐ¾Ð½Ð°Ð»ÑÐ½ÑÑ ÑÐºÐ¸Ð´ÐºÑ! Ð Ð½Ðµ Ð·Ð°Ð±ÑÐ´ÑÑÐµ Ð·Ð°Ð³ÑÑÐ·Ð¸ÑÑ ÑÐ¾ÑÐ¾ ð
entity.notification.BirthdayPromoCodeNotification.title=Ð¡ Ð´Ð½ÐµÐ¼ ÑÐ¾Ð¶Ð´ÐµÐ½Ð¸Ñ! ð
entity.notification.BirthdayPromoCodeNotification.baseMessage=ÐÐ°ÑÐ¸Ð¼ ÑÐºÐ¸Ð´ÐºÑ {0}% Ð¿Ð¾ Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´Ñ {1} Ð¾Ñ {2} Ñ. Ð {3}% Ð¿Ð¾ Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´Ñ {4} Ð½Ð° concierge Ð¾Ñ {5} Ñ. ÐÐµÐ¹ÑÑÐ²ÑÑÑ {6} {7}.
entity.notification.BirthdayPromoCodeNotification.days=Ð´ÐµÐ½Ñ:Ð´Ð½Ñ:Ð´Ð½ÐµÐ¹
entity.notification.AddBrandLikeNotification.title=ÐÐ°Ð¿Ð¾Ð»Ð½Ð¸ÑÐµ Ð»ÑÐ±Ð¸Ð¼ÑÐµ Ð±ÑÐµÐ½Ð´Ñ
entity.notification.AddBrandLikeNotification.baseMessage=Ð£ÐºÐ°Ð¶Ð¸ÑÐµ ÑÐ²Ð¾Ð¸ Ð»ÑÐ±Ð¸Ð¼ÑÐµ Ð±ÑÐµÐ½Ð´Ñ, ÑÑÐ¾Ð±Ñ Ð¼Ñ Ð¼Ð¾Ð³Ð»Ð¸ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶Ð¸ÑÑ Ð²Ð°Ð¼ Ð¿Ð¾Ð´ÑÐ¾Ð´ÑÑÐ¸Ðµ Ð¼Ð¾Ð´ÐµÐ»Ð¸.
entity.notification.HowItWorksNotification.title=ÐÐ¾Ð±ÑÐ¾ Ð¿Ð¾Ð¶Ð°Ð»Ð¾Ð²Ð°ÑÑ Ð½Ð° OSKELLY!
entity.notification.HowItWorksNotification.baseMessage=ÐÑ Ð½Ð°Ð¿Ð¸ÑÐ°Ð»Ð¸ Ð½ÐµÐ±Ð¾Ð»ÑÑÐ¾Ð¹ Ð³Ð°Ð¹Ð´ Ð¾ ÑÐ°Ð±Ð¾ÑÐµ Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ñ. ÐÐ³Ð¾ Ð¿ÑÐ¾ÑÑÐµÐ½Ð¸Ðµ Ð·Ð°Ð¹Ð¼ÐµÑ Ð½Ðµ Ð±Ð¾Ð»ÐµÐµ 1 Ð¼Ð¸Ð½ÑÑÑ
entity.notification.UsePromocodeNotification.title=Ð¡ÐºÐ¸Ð´ÐºÐ° 5% Ð¶Ð´ÐµÑ Ð²Ð°Ñ!
entity.notification.UsePromocodeNotification.baseMessage=ÐÑÐ¿Ð¾Ð»ÑÐ·ÑÐ¹ÑÐµ Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´ WELCOME5 Ð½Ð° Ð¿ÐµÑÐ²ÑÑ Ð¿Ð¾ÐºÑÐ¿ÐºÑ Ð¾Ñ 10 000 Ñ.
entity.notification.WhyDoYouNeedLikes.title=ÐÐ°ÑÐµÐ¼ Ð½ÑÐ¶Ð½Ñ â¤ï¸
entity.notification.WhyDoYouNeedLikes.baseMessage=ÐÐ°ÑÐµÐ¼ Ð½ÑÐ¶Ð½Ñ Â«ÐÐ°Ð¹ÐºÐ¸Â» Ð½Ð° Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ðµ Ð¸ ÐºÐ°Ðº Ð¸Ñ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ Ð½Ð° Ð¼Ð°ÐºÑÐ¸Ð¼ÑÐ¼.
entity.notification.WhyNeedAuthenticationNotification.title=ÐÑÐ½Ð°Ñ Ð°ÑÑÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¸Ñ ð
entity.notification.WhyNeedAuthenticationNotification.baseMessage=ÐÑÐµ, ÑÑÐ¾ Ð²Ñ ÑÐ¾ÑÐµÐ»Ð¸ Ð·Ð½Ð°ÑÑ Ð¾Ð± ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ðµ Ð¿Ð¾Ð´Ð»Ð¸Ð½Ð½Ð¾ÑÑÐ¸ Ð½Ð° Ð½Ð°ÑÐµÐ¹ Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ðµ.
entity.notification.HowToUseBargainNotification.title=Ð¢Ð¾ÑÐ³ ÑÐ¼ÐµÑÑÐµÐ½!
entity.notification.HowToUseBargainNotification.baseMessage=ÐÐ°Ðº ÑÐ°Ð±Ð¾ÑÐ°ÐµÑ ÑÑÐ½ÐºÑÐ¸Ñ ÑÐ¾ÑÐ³Ð° Ð¸ Ð¿Ð¾ÑÐµÐ¼Ñ Ð½ÑÐ¶Ð½Ð° Ð½Ðµ ÑÐ¾Ð»ÑÐºÐ¾ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»ÑÐ¼, Ð½Ð¾ Ð¸ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°Ð¼.
entity.notification.HowToTakePhotoNotification.title=ÐÐ°Ðº Ð±ÑÑÑÑÐµÐµ Ð¿ÑÐ¾Ð´Ð°Ð²Ð°ÑÑ
entity.notification.HowToTakePhotoNotification.baseMessage=ÐÐ°ÑÑÐµÑ-ÐºÐ»Ð°ÑÑ: ÐºÐ°Ðº Ð¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ð¾ ÑÑÐ¾ÑÐ¾Ð³ÑÐ°ÑÐ¸ÑÐ¾Ð²Ð°ÑÑ ÑÐ¾Ð²Ð°Ñ, ÑÑÐ¾Ð±Ñ Ð¿ÑÐ¸Ð²Ð»ÐµÑÑ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ.
entity.notification.WhatIsBeegzNotification.title=ÐÑÐ¾ÐµÐºÑ BEEGZ ð
entity.notification.WhatIsBeegzNotification.baseMessage=ÐÐ¸Ð¼Ð¸ÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð½ÑÐµ ÑÐ½Ð¸ÐºÐµÑÑ, ÑÐºÑÐºÐ»ÑÐ·Ð¸Ð²Ð½ÑÐµ bearbrick Ð¸ ÐºÐ¾Ð»Ð»ÐµÐºÑÐ¸Ð¾Ð½Ð½ÑÐµ Ð»Ð¾ÑÑ Ð´Ð»Ñ Ð¿Ð¾ÐºÐ»Ð¾Ð½Ð½Ð¸ÐºÐ¾Ð² streetwear.
entity.notification.WhatIsConciergeNotification.title=Concierge ÑÐµÑÐ²Ð¸Ñ ð
entity.notification.WhatIsConciergeNotification.baseMessage=ÐÐ°Ðº Ñ ÐµÐ³Ð¾ Ð¿Ð¾Ð¼Ð¾ÑÑÑ Ð¿Ð¾Ð»ÑÑÐ¸ÑÑ Ð¸Ð·Ð´ÐµÐ»Ð¸Ñ Ð¸Ð· Ð½Ð¾Ð²ÑÑ ÐºÐ¾Ð»Ð»ÐµÐºÑÐ¸Ð¹ Ð»ÑÐºÑÐ¾Ð²ÑÑ Ð±ÑÐµÐ½Ð´Ð¾Ð².
entity.notification.NoActivityNotification.title=ÐÑÐµÐ¼Ñ ÑÐµÑÐ¸ÑÐµÐ»ÑÐ½ÑÑ Ð´ÐµÐ¹ÑÑÐ²Ð¸Ð¹!
entity.notification.NoActivityNotification.baseMessage=ÐÐ¾ÑÐ° ÑÐ´ÐµÐ»Ð°ÑÑ ÑÐ²Ð¾Ð¹ Ð¿ÐµÑÐ²ÑÐ¹ Ð·Ð°ÐºÐ°Ð·! ÐÐ¾Ð»ÐµÐµ 100 ÑÑÑÑÑ ÑÐ¾Ð²Ð°ÑÐ¾Ð² ÑÐ¾ ÑÐºÐ¸Ð´ÐºÐ°Ð¼Ð¸ Ð¾Ñ Ð²ÐµÐ´ÑÑÐ¸Ñ Ð±ÑÐµÐ½Ð´Ð¾Ð²!
entity.notification.SupportMessageNotification.title=Ð¡Ð¾Ð¾Ð±ÑÐµÐ½Ð¸Ðµ Ð¾Ñ ÑÐ»ÑÐ¶Ð±Ñ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶ÐºÐ¸
entity.notification.SupportMessageNotification.baseMessage=Ð¡Ð¾ÑÑÑÐ´Ð½Ð¸Ðº Ð¿Ð¾Ð´Ð´ÐµÑÐ¶ÐºÐ¸ Ð¾ÑÐ²ÐµÑÐ¸Ð» ÐÐ°Ð¼ Ð² ÑÐ°ÑÐµ: Ð¿Ð¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¾ÑÐºÑÐ¾Ð¹ÑÐµ ÑÐ°Ñ Ð¸ Ð¿ÑÐ¾ÑÐ¸ÑÐ°Ð¹ÑÐµ ÐµÐ³Ð¾
entity.notification.ProfileBirthdateNotification.baseMessage=Ð£ÐºÐ°Ð¶Ð¸ÑÐµ Ð´Ð°ÑÑ ÑÐ²Ð¾ÐµÐ³Ð¾ ÑÐ¾Ð¶Ð´ÐµÐ½Ð¸Ñ
entity.notification.TrustedSetNotification.title=ÐÐ¾Ð·Ð´ÑÐ°Ð²Ð»ÑÑ! ÐÐ°Ð¼ Ð¿ÑÐ¸ÑÐ²Ð¾ÐµÐ½ ÑÑÐ°ÑÑÑ Ð´Ð¾Ð²ÐµÑÐ¸Ñ!
entity.notification.RegisterNotification.title=Ð¡ÐºÐ¸Ð´ÐºÐ° Ð½Ð° Ð¿ÐµÑÐ²ÑÐ¹ Ð·Ð°ÐºÐ°Ð·!
entity.notification.RegisterNotification.baseMessage=ÐÐ¾Ð»ÑÑÐ¸ÑÐµ ÑÐºÐ¸Ð´ÐºÑ 5% Ð¿Ð¾ Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´Ñ WELCOME5 Ð½Ð° Ð¿ÐµÑÐ²ÑÑ Ð¿Ð¾ÐºÑÐ¿ÐºÑ Ð¾Ñ 10 000 ÑÑÐ±Ð»ÐµÐ¹.
entity.notification.CanCreateStreamNotification.title=Ð¢ÐµÐ¿ÐµÑÑ ÐÑ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¿ÑÐ¾Ð²Ð¾Ð´Ð¸ÑÑ\nÐ¿ÑÑÐ¼ÑÐµ ÑÑÐ¸ÑÑ
entity.notification.CanCreateStreamNotification.baseMessage=ÐÐ¾Ð·Ð´ÑÐ°Ð²Ð»ÑÐµÐ¼, ÐÑ Ð½Ð°Ð±ÑÐ°Ð»Ð¸ 50\n50 Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð½ÑÑ ÑÐ¾Ð²Ð°ÑÐ¾Ð² Ð¸ 10\nÑÑÐ¿ÐµÑÐ½ÑÑ Ð¿ÑÐ¾Ð´Ð°Ð¶, Ð¸ ÑÐµÐ¿ÐµÑÑ Ð¼Ð¾Ð¶ÐµÑÐµ\nÐ¿ÑÐ¾Ð²Ð¾Ð´Ð¸ÑÑ Ð¿ÑÑÐ¼ÑÐµ ÑÑÐ¸ÑÑ Ð½Ð° Ð½Ð°ÑÐµÐ¹\nÐ¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ðµ
entity.notification.StreamCreateNotification.baseMessage=ÐÑÐ´ÐµÑ Ð¿ÑÐ¾Ð²Ð¾Ð´Ð¸ÑÑ Ð¿ÑÑÐ¼Ð¾Ð¹ ÑÑÐ¸Ñ
entity.notification.StreamFinishNotification.baseMessage=ÐÑÐ¼ÐµÐ½Ð¸Ð» Ð¿ÑÑÐ¼Ð¾Ð¹ ÑÑÐ¸Ñ \nÐ½Ð° ÑÐµÐ¼Ñ:\n{0}\nÐ¾Ð±Ð½Ð¾Ð²Ð¸ÑÐµ Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ ÑÑÐ¾Ð±Ñ Ð½Ðµ Ð¿ÑÐ¾Ð¿ÑÑÐºÐ°ÑÑ Ð±ÑÐ´ÑÑÐ¸Ðµ ÑÑÐ°Ð½ÑÐ»ÑÑÐ¸Ð¸.
entity.notification.StreamStartNotification.baseMessage=Ð£Ð¶Ðµ Ð² ÑÑÐ¸ÑÐµ Ð½Ð° ÑÐµÐ¼Ñ:\n{0}
entity.notification.StreamCreateNotification.messageFormat=ÐÑÐ´ÐµÑ Ð¿ÑÐ¾Ð²Ð¾Ð´Ð¸ÑÑ Ð¿ÑÑÐ¼Ð¾Ð¹ ÑÑÐ¸Ñ \n$dm_date Ð² $hm_time\nÐ½Ð° ÑÐµÐ¼Ñ:\n{0}
entity.notification.StreamFinishNotification.messageFormat=ÐÑÐ¼ÐµÐ½Ð¸Ð» Ð¿ÑÑÐ¼Ð¾Ð¹ ÑÑÐ¸Ñ \n$dm_date Ð² $hm_time\nÐ½Ð° ÑÐµÐ¼Ñ:\n{0}
entity.notification.StreamStartNotification.messageFormat=Ð£Ð¶Ðµ Ð² ÑÑÐ¸ÑÐµ Ð½Ð° ÑÐµÐ¼Ñ:\n{0}
entity.notification.StreamFiveMinutesReminderNotification.title=ÐÑ Ð¼Ð¾Ð¶ÐµÑÐµ Ð²ÑÐ¹ÑÐ¸ Ð² ÑÑÐ¸Ñ!
entity.notification.StreamFiveMinutesReminderNotification.baseMessage=ÐÑ Ð¼Ð¾Ð¶ÐµÑÐµ Ð½Ð°ÑÐ°ÑÑ ÑÑÐ¸Ñ Ð½Ð° ÑÐµÐ¼Ñ:\n{0}.\nÐÐ°ÑÐ¸ Ð·ÑÐ¸ÑÐµÐ»Ð¸ ÑÐ¶Ðµ Ð³Ð¾ÑÐ¾Ð²Ñ
entity.notification.StreamOneHourReminderNotification.title=ÐÐ¾ ÑÑÐ¸ÑÐ° 1 ÑÐ°Ñ!
entity.notification.StreamOneHourReminderNotification.baseMessage=ÐÐ¾ ÑÑÐ¸ÑÐ° Ð½Ð° ÑÐµÐ¼Ñ:\n{0} Ð¾ÑÑÐ°Ð»ÑÑ 1 ÑÐ°Ñ.\nÐÐ¾Ð´Ð³Ð¾ÑÐ¾Ð²ÑÑÐµ Ð²ÑÐµ Ð·Ð°ÑÐ°Ð½ÐµÐµ, ÑÑÐ¾Ð±Ñ Ð²Ð¾Ð²ÑÐµÐ¼Ñ Ð²ÑÐ¹ÑÐ¸ Ð² ÑÑÐ¸Ñ.
entity.notification.BargainBanNotification.baseMessage=ÐÑ Ð½Ð°ÑÑÑÐ¸Ð»Ð¸ Ð¿ÑÐ°Ð²Ð¸Ð»Ð° ÑÐµÑÐ²Ð¸ÑÐ° Ð¸ Ð±Ð¾Ð»ÑÑÐµ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑÑÑ ÑÐµÑÐ²Ð¸ÑÐ¾Ð¼ ÑÐ¾ÑÐ³Ð¾Ð²: \n{0}
entity.notification.CancelBargainBanNotification.title=ÐÐ°Ð¿ÑÐµÑ Ð½Ð° ÑÐ¾ÑÐ³Ð¸ ÑÐ½ÑÑ
entity.notification.CancelBargainBanNotification.baseMessage=ÐÐµ Ð½Ð°ÑÑÑÐ°Ð¹ÑÐµ Ð¿ÑÐ°Ð²Ð¸Ð»Ð° ÑÐµÑÐ²Ð¸ÑÐ° Ð¸ Ð¿Ð¾Ð»ÑÐ·ÑÐ¹ÑÐµÑÑ Ð²ÑÐµÐ¼Ð¸ Ð²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ÑÑÑÐ¼Ð¸ Ð½Ð°ÑÐµÐ¹ Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ñ
entity.notification.CancelCommentBanNotification.title=ÐÐ°Ð¿ÑÐµÑ Ð½Ð° ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¸ ÑÐ½ÑÑ
entity.notification.CancelCommentBanNotification.baseMessage=ÐÐµ Ð½Ð°ÑÑÑÐ°Ð¹ÑÐµ Ð¿ÑÐ°Ð²Ð¸Ð»Ð° ÑÐµÑÐ²Ð¸ÑÐ° Ð¸ Ð¿Ð¾Ð»ÑÐ·ÑÐ¹ÑÐµÑÑ Ð²ÑÐµÐ¼Ð¸ Ð²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ÑÑÑÐ¼Ð¸ Ð½Ð°ÑÐµÐ¹ Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ñ
entity.notification.CancelPublishBanNotification.title=ÐÐ°Ð¿ÑÐµÑ Ð½Ð° Ð¿ÑÐ±Ð»Ð¸ÐºÐ°ÑÐ¸Ñ ÑÐ½ÑÑ
entity.notification.CancelPublishBanNotification.baseMessage=ÐÐµ Ð½Ð°ÑÑÑÐ°Ð¹ÑÐµ Ð¿ÑÐ°Ð²Ð¸Ð»Ð° ÑÐµÑÐ²Ð¸ÑÐ° Ð¸ Ð¿Ð¾Ð»ÑÐ·ÑÐ¹ÑÐµÑÑ Ð²ÑÐµÐ¼Ð¸ Ð²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ÑÑÑÐ¼Ð¸ Ð½Ð°ÑÐµÐ¹ Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ñ
entity.notification.CancelPublishStoriesBanNotification.title=ÐÐ°Ð¿ÑÐµÑ Ð½Ð° Ð¿ÑÐ±Ð»Ð¸ÐºÐ°ÑÐ¸Ñ ÑÑÐ¾ÑÐ¸Ñ ÑÐ½ÑÑ
entity.notification.CancelPublishStoriesBanNotification.baseMessage=ÐÐµ Ð½Ð°ÑÑÑÐ°Ð¹ÑÐµ Ð¿ÑÐ°Ð²Ð¸Ð»Ð° ÑÐµÑÐ²Ð¸ÑÐ° Ð¸ Ð¿Ð¾Ð»ÑÐ·ÑÐ¹ÑÐµÑÑ Ð²ÑÐµÐ¼Ð¸ Ð²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ÑÑÑÐ¼Ð¸ Ð½Ð°ÑÐµÐ¹ Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ñ
entity.notification.CancelStreamBanNotification.title=ÐÐ°Ð¿ÑÐµÑ Ð½Ð° Ð¿ÑÐ¾Ð²ÐµÐ´ÐµÐ½Ð¸Ðµ Ð¿ÑÑÐ¼ÑÑ ÑÑÐ¸ÑÐ¾Ð² ÑÐ½ÑÑ
entity.notification.CancelStreamBanNotification.baseMessage=ÐÐµ Ð½Ð°ÑÑÑÐ°Ð¹ÑÐµ Ð¿ÑÐ°Ð²Ð¸Ð»Ð° ÑÐµÑÐ²Ð¸ÑÐ° Ð¸ Ð¿Ð¾Ð»ÑÐ·ÑÐ¹ÑÐµÑÑ Ð²ÑÐµÐ¼Ð¸ Ð²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ÑÑÑÐ¼Ð¸ Ð½Ð°ÑÐµÐ¹ Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ñ
entity.notification.CancelUserBanNotification.title=ÐÐ°Ñ Ð¿ÑÐ¾ÑÐ¸Ð»Ñ ÑÐ°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½
entity.notification.CancelUserBanNotification.baseMessage=ÐÐµ Ð½Ð°ÑÑÑÐ°Ð¹ÑÐµ Ð¿ÑÐ°Ð²Ð¸Ð»Ð° ÑÐµÑÐ²Ð¸ÑÐ° Ð¸ Ð¿Ð¾Ð»ÑÐ·ÑÐ¹ÑÐµÑÑ Ð²ÑÐµÐ¼Ð¸ Ð²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ÑÑÑÐ¼Ð¸ Ð½Ð°ÑÐµÐ¹ Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ñ
entity.notification.CancelOSocialPostBanNotification.title=ÐÐ°Ð¿ÑÐµÑ Ð½Ð° Ð¿ÑÐ±Ð»Ð¸ÐºÐ°ÑÐ¸Ñ Ð¿Ð¾ÑÑÐ¾Ð² ÑÐ½ÑÑ
entity.notification.CancelOSocialPostBanNotification.baseMessage=ÐÐµ Ð½Ð°ÑÑÑÐ°Ð¹ÑÐµ Ð¿ÑÐ°Ð²Ð¸Ð»Ð° ÑÐµÑÐ²Ð¸ÑÐ° Ð¸ Ð¿Ð¾Ð»ÑÐ·ÑÐ¹ÑÐµÑÑ Ð²ÑÐµÐ¼Ð¸ Ð²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ÑÑÑÐ¼Ð¸ Ð½Ð°ÑÐµÐ¹ Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ñ
entity.notification.CancelOSocialCommentBanNotification.title=ÐÐ°Ð¿ÑÐµÑ Ð½Ð° ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¸ Ð² O!Trends ÑÐ½ÑÑ
entity.notification.CancelOSocialCommentBanNotification.baseMessage=ÐÐµ Ð½Ð°ÑÑÑÐ°Ð¹ÑÐµ Ð¿ÑÐ°Ð²Ð¸Ð»Ð° ÑÐµÑÐ²Ð¸ÑÐ° Ð¸ Ð¿Ð¾Ð»ÑÐ·ÑÐ¹ÑÐµÑÑ Ð²ÑÐµÐ¼Ð¸ Ð²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ÑÑÑÐ¼Ð¸ Ð½Ð°ÑÐµÐ¹ Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ñ
entity.notification.CommentBanNotification.baseMessage=ÐÑ Ð½Ð°ÑÑÑÐ¸Ð»Ð¸ Ð¿ÑÐ°Ð²Ð¸Ð»Ð° ÑÐµÑÐ²Ð¸ÑÐ°, Ð¸ ÑÐµÐ¿ÐµÑÑ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¾ÑÑÐ°Ð²Ð»ÑÑÑ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¸ Ð¿Ð¾Ð´ ÑÐ¾Ð²Ð°ÑÐ°Ð¼Ð¸: \n{0}
entity.notification.PublishBanNotification.baseMessage=ÐÑ Ð½Ð°ÑÑÑÐ¸Ð»Ð¸ Ð¿ÑÐ°Ð²Ð¸Ð»Ð° ÑÐµÑÐ²Ð¸ÑÐ°, Ð¸ ÑÐµÐ¿ÐµÑÑ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¿ÑÐ¾Ð´Ð°Ð²Ð°ÑÑ ÑÐ¾Ð²Ð°ÑÑ: \n{0}
entity.notification.PublishStoriesBanNotification.baseMessage=ÐÑ Ð½Ð°ÑÑÑÐ°ÐµÑÐµ Ð¿ÑÐ°Ð²Ð¸Ð»Ð° ÑÐµÑÐ²Ð¸ÑÐ°, Ð¸ ÑÐµÐ¿ÐµÑÑ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°ÑÑ ÑÑÐ¾ÑÐ¸Ñ: \n{0}
entity.notification.StreamBanNotification.baseMessage=ÐÑ Ð½Ð°ÑÑÑÐ¸Ð»Ð¸ Ð¿ÑÐ°Ð²Ð¸Ð»Ð° ÑÐµÑÐ²Ð¸ÑÐ° Ð¸ Ð±Ð¾Ð»ÑÑÐµ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑÑÑ ÑÐµÑÐ²Ð¸ÑÐ¾Ð¼ Ð¿ÑÑÐ¼ÑÑ ÑÑÐ¸ÑÐ¾Ð²: \n{0}
entity.notification.UserBanNotification.baseMessage=ÐÑ Ð½Ð°ÑÑÑÐ¸Ð»Ð¸ Ð¿ÑÐ°Ð²Ð¸Ð»Ð° ÑÐµÑÐ²Ð¸ÑÐ°, ÐÐ°Ñ Ð¿ÑÐ¾ÑÐ¸Ð»Ñ Ð²ÑÐµÐ¼ÐµÐ½Ð½Ð¾ Ð·Ð°Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½: \n{0}
entity.notification.WarningBanNotification.baseMessage=ÐÑ Ð½Ð°ÑÑÑÐ°ÐµÑÐµ Ð¿ÑÐ°Ð²Ð¸Ð»Ð° ÑÐµÑÐ²Ð¸ÑÐ° Ð¸ ÑÐ¸ÑÐºÑÐµÑÐµ Ð±ÑÑÑ Ð·Ð°Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ð½ÑÐ¼Ð¸:\n{0}
entity.notification.OSocialPostBanNotification.baseMessage=ÐÑ Ð½Ð°ÑÑÑÐ¸Ð»Ð¸ Ð¿ÑÐ°Ð²Ð¸Ð»Ð° ÑÐµÑÐ²Ð¸ÑÐ°, Ð¸ ÑÐµÐ¿ÐµÑÑ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°ÑÑ Ð¿Ð¾ÑÑÑ: \n{0}
entity.notification.OSocialCommentBanNotification.baseMessage=ÐÑ Ð½Ð°ÑÑÑÐ¸Ð»Ð¸ Ð¿ÑÐ°Ð²Ð¸Ð»Ð° ÑÐµÑÐ²Ð¸ÑÐ°, Ð¸ ÑÐµÐ¿ÐµÑÑ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¾ÑÑÐ°Ð²Ð»ÑÑÑ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¸: \n{0}
entity.notification.SaleExpertisePassedPartlyNotification.title=Ð§Ð°ÑÑÑ Ð·Ð°ÐºÐ°Ð·Ð° â{0} Ð½Ðµ Ð¿ÑÐ¾ÑÐ»Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.notification.NewPriceUpdateSubscriptionNotification.title=ÐÐ°ÑÐ¸Ð¼ ÑÐ¾Ð²Ð°ÑÐ¾Ð¼ Ð¸Ð½ÑÐµÑÐµÑÑÑÑÑÑ!
entity.notification.ProductPublishedNotification.title=ÐÐ¾Ð²ÑÐµ Ð¿Ð¾ÑÑÑÐ¿Ð»ÐµÐ½Ð¸Ñ!
entity.notification.ProductPublishedNotification.baseMessage={0} ÑÐ¾Ð»ÑÐºÐ¾ ÑÑÐ¾ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð»{1} ÑÐ¾Ð²Ð°Ñ: {2}
entity.notification.ExclusiveSelectionAvailableProductNotification.title=ÐÐ¾ÑÑÑÐ¿Ð½Ð¾ Ð´Ð»Ñ Ð¿Ð¾ÐºÑÐ¿ÐºÐ¸
entity.notification.ExclusiveSelectionAvailableProductNotification.baseMessage=Ð¢Ð¾ÑÐ¾Ð¿Ð¸ÑÐµÑÑ Ð¾ÑÐ¾ÑÐ¼Ð¸ÑÑ Ð·Ð°ÐºÐ°Ð· Ð½Ð° Ð»Ð¾Ñ Ð¸Ð· Ð²Ð°ÑÐµÐ³Ð¾ Ð¸Ð·Ð±ÑÐ°Ð½Ð½Ð¾Ð³Ð¾: {0}
entity.notification.ExclusiveSelectionSellerProductNotification.title=Ð­ÐºÑÐºÐ»ÑÐ·Ð¸Ð²Ð½Ð°Ñ ÑÐµÐ»ÐµÐºÑÐ¸Ñ
entity.notification.ExclusiveSelectionSellerProductNotification.baseMessage=ÐÐ¾Ð·Ð´ÑÐ°Ð²Ð»ÑÐµÐ¼! ÐÐ°Ñ ÑÐ¾Ð²Ð°Ñ {0} Ð¿Ð¾Ð¿Ð°Ð» Ð² ÑÐ°Ð·Ð´ÐµÐ» Ð­ÐºÑÐºÐ»ÑÐ·Ð¸Ð²Ð½Ð°Ñ ÑÐµÐ»ÐµÐºÑÐ¸Ñ Ð¸ Ð¿Ð¾Ð»ÑÑÐ¸Ñ Ð±Ð¾Ð»ÑÑÐµ Ð²Ð½Ð¸Ð¼Ð°Ð½Ð¸Ñ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»ÐµÐ¹
entity.notification.ProductsPublishedNotification.baseMessage={0} ÑÐ¾Ð»ÑÐºÐ¾ ÑÑÐ¾ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð»{1} {2}
entity.notification.PrivateSellerProductPublishedNotification.baseMessage={0} Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð»{1} ÑÐ¾Ð²Ð°Ñ: {2}
entity.notification.PrivateSellerProductsPublishedNotification.baseMessage={0} Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð»{1} {2} + ÐµÑÐµ {3}... Ð¡Ð¼Ð¾ÑÑÐµÑÑ Ð²ÑÐµ âº
entity.notification.SetLowerPriceForSeveralProductsNotification.title=ð¡ ÐÐ°Ðº Ð¿ÑÐ¾Ð´Ð°ÑÑ Ð±ÑÑÑÑÐµÐµ
entity.notification.SetLowerPriceForSeveralProductsNotification.baseMessage=ÐÐ¾Ð¿ÑÐ¾Ð±ÑÐ¹ÑÐµ ÑÐ½Ð¸Ð·Ð¸ÑÑ ÑÐµÐ½Ñ ÑÐ¾Ð²Ð°ÑÐ¾Ð², ÑÑÐ¾Ð±Ñ Ð¿ÑÐ¸Ð²Ð»ÐµÑÑ Ð±Ð¾Ð»ÑÑÐµ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»ÐµÐ¹.
entity.notification.SetLowerPriceNotification.title=ð¡ ÐÐ°Ðº Ð¿ÑÐ¾Ð´Ð°ÑÑ Ð±ÑÑÑÑÐµÐµ
entity.notification.SetLowerPriceNotification.baseMessage=ÐÐ¾Ð¿ÑÐ¾Ð±ÑÐ¹ÑÐµ ÑÐ½Ð¸Ð·Ð¸ÑÑ ÑÐµÐ½Ñ ÑÐ¾Ð²Ð°ÑÐ°, ÑÑÐ¾Ð±Ñ Ð¿ÑÐ¸Ð²Ð»ÐµÑÑ Ð±Ð¾Ð»ÑÑÐµ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»ÐµÐ¹.
entity.notification.NewPriceUpdateSubscriptionNotification.baseMessage={0} Ð¿Ð¾Ð´Ð¿Ð¸ÑÐ°Ð»{1} Ð½Ð° ÑÐ½Ð¸Ð¶ÐµÐ½Ð¸Ðµ ÑÐµÐ½Ñ Ð²Ð°ÑÐµÐ³Ð¾ ÑÐ¾Ð²Ð°ÑÐ°. ÐÐ¾Ð¿ÑÐ¾Ð±ÑÐ¹ÑÐµ ÑÐ½Ð¸Ð·Ð¸ÑÑ ÑÑÐ¾Ð¸Ð¼Ð¾ÑÑÑ, ÑÑÐ¾Ð±Ñ Ð±ÑÑÑÑÐµÐµ Ð¿ÑÐ¾Ð´Ð°ÑÑ ÑÐ¾Ð²Ð°Ñ.
entity.notification.SaleExpertisePassedPartlyNotification.baseMessage.singularPassedSingularRejected=Ð­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» {0} {1}. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ñ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ð¸ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ¸Ð¼ÑÑ Ð¾ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµ.\n\nÐÑÑÐ³Ð¾Ð¹ ÑÐ¾Ð²Ð°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ.
entity.notification.SaleExpertisePassedPartlyNotification.baseMessage.singularPassedPluralRejected=Ð­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ Ð½Ðµ Ð¿ÑÐ¾ÑÐ»Ð¸ {0} {1}. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ñ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ð¸ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ¸Ð¼ÑÑ Ð¾ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµ.\n\nÐÑÑÐ³Ð¾Ð¹ ÑÐ¾Ð²Ð°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ.
entity.notification.SaleExpertisePassedPartlyNotification.baseMessage.pluralPassedSingularRejected=Ð­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» {0} {1}. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ñ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ð¸ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ¸Ð¼ÑÑ Ð¾ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµ.\n\nÐÑÑÐ³Ð¸Ðµ ÑÐ¾Ð²Ð°ÑÑ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ.
entity.notification.SaleExpertisePassedPartlyNotification.baseMessage.pluralPassedPluralRejected=Ð­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ Ð½Ðµ Ð¿ÑÐ¾ÑÐ»Ð¸ {0} {1}. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ñ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ð¸ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ¸Ð¼ÑÑ Ð¾ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµ.\n\nÐÑÑÐ³Ð¸Ðµ ÑÐ¾Ð²Ð°ÑÑ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ.
entity.notification.SaleExpertisePassedPartlyNotification.isBoutiqueOrder.baseMessage=Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð´Ð¾ÑÑÐ°Ð²Ð¸Ð¼ ÑÐ¾Ð²Ð°Ñ Ð² Ð±ÑÑÐ¸Ðº.\nÐ­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ Ð½Ðµ Ð¿ÑÐ¾ÑÐ»Ð¸ {0} {1}.\nC Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÑÑÑ Ð½Ð°Ñ Ð¼ÐµÐ½ÐµÐ´Ð¶ÐµÑ Ð¸ ÑÐ¾Ð³Ð»Ð°ÑÑÐµÑ Ð²Ð¾Ð·Ð²ÑÐ°Ñ ÑÐ¾Ð²Ð°ÑÐ°, ÐºÐ¾ÑÐ¾ÑÑÐ¹ Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.notification.WishlistItemSold.title=ÐÐ¾Ñ Ð¸Ð· Ð²Ð¸ÑÐ»Ð¸ÑÑÐ° Ð¿ÑÐ¾Ð´Ð°Ð½ ð
entity.notification.WishlistItemSold.baseMessage={0} Ð¸Ð· Ð²Ð°ÑÐµÐ³Ð¾ Ð²Ð¸ÑÐ»Ð¸ÑÑÐ° ÐºÑÐ¿Ð¸Ð»Ð¸. ÐÐ¾ÑÐ¼Ð¾ÑÑÐ¸ÑÐµ Ð¿Ð¾ÑÐ¾Ð¶Ð¸Ðµ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ Ð¸Ð»Ð¸ Ð·Ð°ÐºÐ°Ð¶Ð¸ÑÐµ Ð² ÑÐµÑÐ²Ð¸ÑÐµ Concierge.
entity.notification.HotWishlistItem.title=ÐÐ¾Ñ Ð¸Ð· Ð²Ð¸ÑÐ»Ð¸ÑÑÐ° Ð¿Ð¾Ð¿ÑÐ»ÑÑÐµÐ½ ð¥
entity.notification.HotWishlistItem.baseMessage=ÐÐ¾Ñ Ð¸Ð· Ð²Ð°ÑÐµÐ³Ð¾ Ð²Ð¸ÑÐ»Ð¸ÑÑÐ° ÑÐ°ÑÑÐ¾ Ð´Ð¾Ð±Ð°Ð²Ð»ÑÑÑ Ð² Ð¸Ð·Ð±ÑÐ°Ð½Ð½Ð¾Ðµ. ÐÐµ ÑÐ¿ÑÑÑÐ¸ÑÐµ ÐµÐ³Ð¾!
entity.notification.WishlistItemActiveBargain.title=ÐÐ¾ÑÐ° Ð´ÐµÐ¹ÑÑÐ²Ð¾Ð²Ð°ÑÑ!
entity.notification.WishlistItemActiveBargain.baseMessage=ÐÑÑÐ³Ð¾Ð¹ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶Ð¸Ð» ÑÐµÐ½Ñ Ð½Ð° ÑÐ¾Ð²Ð°Ñ Ð¸Ð· Ð²Ð°ÑÐµÐ³Ð¾ Ð²Ð¸ÑÐ»Ð¸ÑÑÐ°. Ð£ÑÐ¿ÐµÐ¹ÑÐµ ÑÐ´ÐµÐ»Ð°ÑÑ Ð·Ð°ÐºÐ°Ð· ÑÐ°Ð½ÑÑÐµ Ð´ÑÑÐ³Ð¸Ñ!
entity.notification.StoryLikeNotification.title=ÐÐ°ÑÐ¸Ð¼ Ð¾Ð±ÑÐ°Ð·Ð¾Ð¼ Ð¸Ð½ÑÐµÑÐµÑÑÑÑÑÑ
entity.notification.StoryLikeNotification.baseMessage={0} Ð´Ð¾Ð±Ð°Ð²Ð¸Ð»(Ð°) Ð²Ð°Ñ Ð¾Ð±ÑÐ°Ð· Ð² Ð¸Ð·Ð±ÑÐ°Ð½Ð½Ð¾Ðµ
entity.notification.LikedProductStoryNotification.baseMessage=ÐÐ° ÑÐ¾Ð²Ð°Ñ Ð¸Ð· Ð²Ð°ÑÐµÐ³Ð¾ Ð²Ð¸ÑÐ»Ð¸ÑÑÐ° Ð´Ð¾Ð±Ð°Ð²Ð»ÐµÐ½ Ð¾Ð±ÑÐ°Ð·
entity.notification.CartProductStoryNotification.baseMessage=ÐÐ° ÑÐ¾Ð²Ð°Ñ Ð¸Ð· Ð²Ð°ÑÐµÐ¹ ÐºÐ¾ÑÐ·Ð¸Ð½Ñ Ð´Ð¾Ð±Ð°Ð²Ð»ÐµÐ½ Ð¾Ð±ÑÐ°Ð·
entity.notification.ProductNoStoryNotification.title=Ð£ÑÐºÐ¾ÑÑÑÐµ Ð¿ÑÐ¾Ð´Ð°Ð¶Ñ
entity.notification.ProductNoStoryNotification.baseMessage.oneProduct=ÐÐ¾Ð¿ÑÐ¾Ð±ÑÐ¹ÑÐµ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°ÑÑ ÑÑÐ¸Ð»ÑÐ½ÑÐ¹ Ð¾Ð±ÑÐ°Ð·, ÑÑÐ¾Ð±Ñ Ð±Ð¾Ð»ÑÑÐµ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÐµÐ¹ Ð¾Ð±ÑÐ°ÑÐ¸Ð»Ð¸ Ð²Ð½Ð¸Ð¼Ð°Ð½Ð¸Ðµ Ð½Ð° Ð²Ð°Ñ ÑÐ¾Ð²Ð°Ñ
entity.notification.ProductNoStoryNotification.baseMessage.manyProducts=ÐÐ¾Ð¿ÑÐ¾Ð±ÑÐ¹ÑÐµ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°ÑÑ ÑÑÐ¸Ð»ÑÐ½ÑÐµ Ð¾Ð±ÑÐ°Ð·Ñ, ÑÑÐ¾Ð±Ñ Ð±Ð¾Ð»ÑÑÐµ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÐµÐ¹ Ð¾Ð±ÑÐ°ÑÐ¸Ð»Ð¸ Ð²Ð½Ð¸Ð¼Ð°Ð½Ð¸Ðµ Ð½Ð° Ð²Ð°ÑÐ¸ ÑÐ¾Ð²Ð°ÑÑ
entity.notification.NewProductResponseNotification.title=ÐÐ¾Ð²ÑÐ¹ Ð¾ÑÐºÐ»Ð¸Ðº
entity.notification.NewProductResponseNotification.baseMessage=ÐÐ° Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½Ð¸Ðµ {0} Ð¿ÑÐ¸ÑÐµÐ» Ð½Ð¾Ð²ÑÐ¹ Ð¾ÑÐºÐ»Ð¸Ðº
entity.notification.InformAboutProductRequestNotification.title=ÐÑÑ ÑÐ¾Ð²Ð°Ñ
entity.notification.InformAboutProductRequestNotification.baseMessage=ÐÐ¾ÐºÑÐ¿Ð°Ð¹ÑÐµ Ð¸Ð»Ð¸ Ð¿ÑÐ¾Ð´Ð°Ð²Ð°Ð¹ÑÐµ ÑÐµÑÐµÐ· ÑÑÐ½ÐºÑÐ¸Ñ "ÐÑÑ ÑÐ¾Ð²Ð°Ñ"
entity.notification.NewProductRequestCommentForUserHowLikedItNotification.title=ÐÐ¾Ð²ÑÐ¹ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹
entity.notification.NewProductRequestCommentForUserHowLikedItNotification.baseMessage=ÐÐ° Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½Ð¸Ðµ {0} Ð¿ÑÐ¸ÑÐµÐ» Ð½Ð¾Ð²ÑÐ¹ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹
entity.notification.ProductRequestNewCommentNotification.title=ÐÐ¾Ð²ÑÐ¹ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹
entity.notification.ProductRequestNewCommentNotification.baseMessage={0} Ð¿ÑÐ¾ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ¸ÑÐ¾Ð²Ð°Ð»(Ð°) Ð²Ð°ÑÐµ Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½Ð¸Ðµ. ÐÐ¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾, Ð² ÑÑÐ¾Ð¼ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¸ ÑÐ¾Ñ ÑÐ°Ð¼ÑÐ¹ Ð´Ð¾Ð»Ð³Ð¾Ð¶Ð´Ð°Ð½Ð½ÑÐ¹ ÑÐ¾Ð²Ð°Ñ! ÐÐµÑÐµÑÐ¾Ð´Ð¸ÑÐµ Ð¸ Ð¾ÑÐ²ÐµÑÐ°Ð¹ÑÐµ ÑÐºÐ¾ÑÐµÐµ
entity.notification.NeedPublishProductRequest.title=ÐÐµÑ Ð²ÑÐµÐ¼ÐµÐ½Ð¸ Ð¸ÑÐºÐ°ÑÑ?
entity.notification.NeedPublishProductRequest.baseMessage=ÐÐ¾ÐºÑÐ¿Ð°Ð¹ÑÐµ ÑÐµÑÐµÐ· ÑÑÐ½ÐºÑÐ¸Ñ "ÐÑÑ ÑÐ¾Ð²Ð°Ñ", Ð³Ð´Ðµ Ð²Ð°Ð¼ Ð±ÑÐ´ÑÑ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ñ ÑÐ¾Ð²Ð°ÑÑ Ð¾Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÐµÐ¹
entity.notification.RejectedProductRequestNotification.title=ÐÐ±ÑÑÐ²Ð»ÐµÐ½Ð¸Ðµ {0} Ð½Ðµ Ð¿ÑÐ¾ÑÐ»Ð¾ Ð¼Ð¾Ð´ÐµÑÐ°ÑÐ¸Ñ
entity.notification.RejectedProductRequestNotification.baseMessage=ÐÑÑÐµÐ´Ð°ÐºÑÐ¸ÑÑÐ¹ÑÐµ ÐµÐ³Ð¾ Ñ ÑÑÐµÑÐ¾Ð¼ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸ÐµÐ² Ð¼Ð¾Ð´ÐµÑÐ°ÑÐ¾ÑÐ° Ð¸ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÑÐ¹ÑÐµ Ð·Ð°Ð½Ð¾Ð²Ð¾: {0}
entity.notification.NewBuyerProductResponseNotification.title=ÐÐ¾Ð²ÑÐ¹ Ð¾ÑÐºÐ»Ð¸Ðº
entity.notification.NewBuyerProductResponseNotification.baseMessage={0} Ð¿ÑÐµÐ´Ð»Ð¾Ð¶Ð¸Ð»(Ð°) ÑÐ¾Ð²Ð°Ñ Ð¿Ð¾ Ð²Ð°ÑÐµÐ¼Ñ Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½Ð¸Ñ {1}. ÐÐµÑÐµÑÐ¾Ð´Ð¸ÑÐµ Ðº ÑÐ¾Ð²Ð°ÑÑ Ð¸ Ð¿Ð¾ÐºÑÐ¿Ð°Ð¹ÑÐµ ÐµÐ³Ð¾ ÑÐºÐ¾ÑÐµÐµ, ÐµÑÐ»Ð¸ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ Ð²Ð°Ð¼ Ð¿Ð¾Ð´ÑÐ¾Ð´Ð¸Ñ!
entity.notification.NewProductRequestLikeNotification.title=ÐÐ°ÑÐ¸Ð¼ Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½Ð¸ÐµÐ¼ Ð¸Ð½ÑÐµÑÐµÑÑÑÑÑÑ
entity.notification.NewProductRequestLikeNotification.baseMessage={0} Ð´Ð¾Ð±Ð°Ð²Ð¸Ð»(Ð°) Ð²Ð°ÑÐµ Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½Ð¸Ðµ {1} Ð² Ð¸Ð·Ð±ÑÐ°Ð½Ð½Ð¾Ðµ
entity.notification.NoResponseProductRequestNotification.title={0} ÑÐ¾Ð²Ð°ÑÐ¾Ð², ÐºÐ¾ÑÐ¾ÑÑÐµ Ð¼Ð¾Ð³ÑÑ Ð±ÑÑÑ Ð²Ð°Ð¼ Ð¸Ð½ÑÐµÑÐµÑÐ½Ñ 
entity.notification.NoResponseProductRequestNotification.baseMessage=ÐÑ Ð½Ð°ÑÐ»Ð¸ ÑÐ¾Ð²Ð°ÑÑ, ÐºÐ¾ÑÐ¾ÑÑÐµ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÑÑ Ð²Ð°ÑÐµÐ¼Ñ Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½Ð¸Ñ {0}
entity.notification.ReplayProductRequestCommentNotification.title=ÐÐ¾Ð²ÑÐ¹ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹
entity.notification.ReplayProductRequestCommentNotification.baseMessage={0} Ð¾ÑÐ²ÐµÑÐ¸Ð»(Ð°) Ð½Ð° Ð²Ð°Ñ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹ Ð¿Ð¾ Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½Ð¸Ñ {1}. ÐÐµÑÐµÑÐ¾Ð´Ð¸ÑÐµ Ð¸ Ð¾ÑÐ²ÐµÑÐ°Ð¹ÑÐµ ÑÐºÐ¾ÑÐµÐµ
entity.notification.UpdateProductRequestNotification.title=ÐÐ±ÑÑÐ²Ð»ÐµÐ½Ð¸Ðµ {0} Ð±ÑÐ»Ð¾ Ð¾ÑÑÐµÐ´Ð°ÐºÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð¾
entity.notification.UpdateProductRequestNotification.baseMessage={0} Ð¾ÑÑÐµÐ´Ð°ÐºÑÐ¸ÑÐ¾Ð²Ð°Ð»(Ð°) Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½Ð¸Ðµ {1}, Ð½Ð° ÐºÐ¾ÑÐ¾ÑÐ¾Ðµ Ð²Ñ Ð¾ÑÐ¿ÑÐ°Ð²Ð»ÑÐ»Ð¸ Ð¾ÑÐºÐ»Ð¸Ðº
entity.notification.MotivateCreateProductResponseNotification.title=ÐÐ°ÑÐ½Ð¸ Ð·Ð°ÑÐ°Ð±Ð°ÑÑÐ²Ð°ÑÑ Ñ Oskelly
entity.notification.MotivateCreateProductResponseNotification.baseMessage=Ð¡Ð¼Ð¾ÑÑÐ¸ÑÐµ, ÑÑÐ¾ Ð¸ÑÑÑ Ð´ÑÑÐ³Ð¸Ðµ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ð¸ Ð² ÑÐ°Ð·Ð´ÐµÐ»Ðµ "ÐÑÑ ÑÐ¾Ð²Ð°Ñ" Ð¸ Ð¿ÑÐµÐ´Ð»Ð°Ð³Ð°Ð¹ÑÐµ Ð¸Ð¼ ÑÐ²Ð¾Ð¸ ÑÐ¾Ð²Ð°ÑÑ
entity.notification.InformAboutCountOfUserWhoCreateProductRequestNotification.title=Ð¡ÐµÐ¹ÑÐ°Ñ {0} {1} Ð°ÐºÑÐ¸Ð²Ð½Ð¾ Ð¸ÑÑÑ ÑÐ¾Ð²Ð°ÑÑ
entity.notification.InformAboutCountOfUserWhoCreateProductRequestNotification.baseMessage=ÐÑ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶Ð¸ÑÑ ÑÐ²Ð¾Ð¸ ÑÐ¾Ð²Ð°ÑÑ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÑÐ¼, ÐºÐ¾ÑÐ¾ÑÑÐµ Ð·Ð°Ð¼Ð¾ÑÐ¸Ð²Ð¸ÑÐ¾Ð²Ð°Ð½Ñ Ð½Ð° Ð¿Ð¾ÐºÑÐ¿ÐºÑ
entity.notification.SimilarProductRequestsFoundNotification.title={0} {1}, ÐºÐ¾ÑÐ¾ÑÑÐµ Ð¼Ð¾Ð³ÑÑ Ð±ÑÑÑ Ð²Ð°Ð¼ Ð¸Ð½ÑÐµÑÐµÑÐ½Ñ
entity.notification.SimilarProductRequestsFoundNotification.baseMessage=ÐÑ Ð½Ð°ÑÐ»Ð¸ Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½Ð¸Ñ, ÐºÐ¾ÑÐ¾ÑÑÐµ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÑÑ Ð²Ð°ÑÐµÐ¼Ñ ÑÐ¾Ð²Ð°ÑÑ {0}. ÐÑÐµÐ´Ð»Ð¾Ð¶Ð¸ÑÐµ Ð¿Ð¾ÑÐµÐ½ÑÐ¸Ð°Ð»ÑÐ½ÑÐ¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»ÑÐ¼ ÑÐ²Ð¾Ð¹ ÑÐ¾Ð²Ð°Ñ Ð¸ Ð¿ÑÐ¾Ð´Ð°Ð²Ð°Ð¹ÑÐµ Ð±ÑÑÑÑÐµÐµ
entity.notification.SaleRequestCreatedNotification.title=ÐÐ°ÑÐ²ÐºÐ° ÑÐ¾Ð·Ð´Ð°Ð½Ð°
entity.notification.SaleRequestCreatedNotification.baseMessage=ÐÑ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ñ Ð²Ð°Ð¼Ð¸ ÑÐµÑÐµÐ· WhatsApp Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 48 ÑÐ°ÑÐ¾Ð²
entity.notification.SaleRequestInProgressNowNotification.title=ÐÐ°ÑÐ²ÐºÐ° Ð² ÑÐ°Ð±Ð¾ÑÐµ
entity.notification.SaleRequestInProgressNowNotification.baseMessage=ÐÐ°ÑÐ²ÐºÐ° Ð¾Ð±ÑÐ°Ð±Ð°ÑÑÐ²Ð°ÐµÑÑÑ Ð¼ÐµÐ½ÐµÐ´Ð¶ÐµÑÐ¾Ð¼, Ð´Ð»Ñ ÑÑÐ¾ÑÐ½ÐµÐ½Ð¸Ñ Ð´ÐµÑÐ°Ð»ÐµÐ¹ Ð¼Ñ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ñ Ð²Ð°Ð¼Ð¸ ÑÐµÑÐµÐ· WhatsApp
entity.notification.SaleRequestConfirmedNotification.title=ÐÐ°ÑÐ° Ð·Ð°ÑÐ²ÐºÐ° Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð°!
entity.notification.SaleRequestConfirmedNotification.baseMessage=ÐÐ¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÑ Ð·Ð°ÐºÐ°Ð· Ð½Ð° Ð¾ÑÐ³ÑÑÐ·ÐºÑ ÑÐ¾Ð²Ð°ÑÐ¾Ð² Ð² OSKELLY Ð¼Ð¾Ð¶Ð½Ð¾ Ð² ÐÐ¸ÑÐ½Ð¾Ð¼ ÐºÐ°Ð±Ð¸Ð½ÐµÑÐµ
entity.notification.SaleRequestDeclinedNotification.title=ÐÐ°ÑÐ²ÐºÐ° Ð¾ÑÐºÐ»Ð¾Ð½ÐµÐ½Ð°
entity.notification.SaleRequestDeclinedNotification.baseMessage=Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, Ð¼Ñ Ð½Ðµ ÑÐ¼Ð¾Ð¶ÐµÐ¼ Ð¿ÑÐ¸Ð½ÑÑÑ Ð²Ð°ÑÐ¸ ÑÐ¾Ð²Ð°ÑÑ Ðº Ð½Ð°Ð¼ Ð½Ð° ÑÐºÐ»Ð°Ð´
entity.notification.QualityControlStartedSellerNotification.title=ÐÐ°ÐºÐ°Ð· â {0} Ð¿ÑÐ¾ÑÐ¾Ð´Ð¸Ñ ÐºÐ¾Ð½ÑÑÐ¾Ð»Ñ ÐºÐ°ÑÐµÑÑÐ²Ð°
entity.notification.QualityControlStartedSellerNotification.baseMessage.singular=ÐÑ Ð¿ÑÐ¾Ð²ÐµÑÑÐµÐ¼ ÑÐ¾Ð²Ð°Ñ Ð½Ð° ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ
entity.notification.QualityControlStartedSellerNotification.baseMessage.plural=ÐÑ Ð¿ÑÐ¾Ð²ÐµÑÑÐµÐ¼ ÑÐ¾Ð²Ð°ÑÑ Ð½Ð° ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ
entity.notification.QualityControlStartedBuyerNotification.title=ÐÐ°ÐºÐ°Ð· â {0} Ð¿ÑÐ¾ÑÐ¾Ð´Ð¸Ñ ÐºÐ¾Ð½ÑÑÐ¾Ð»Ñ ÐºÐ°ÑÐµÑÑÐ²Ð°
entity.notification.QualityControlStartedBuyerNotification.baseMessage.singular=ÐÑ Ð¿ÑÐ¾Ð²ÐµÑÑÐµÐ¼ ÑÐ¾Ð²Ð°Ñ Ð½Ð° ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ
entity.notification.QualityControlStartedBuyerNotification.baseMessage.plural=ÐÑ Ð¿ÑÐ¾Ð²ÐµÑÑÐµÐ¼ ÑÐ¾Ð²Ð°ÑÑ Ð½Ð° ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ
entity.notification.AuthenticationStartedSellerNotification.title=ÐÐ°ÐºÐ°Ð· â {0} Ð¿ÑÐ¾ÑÐ¾Ð´Ð¸Ñ Ð°ÑÑÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¸Ñ
entity.notification.AuthenticationStartedSellerNotification.baseMessage.singular=ÐÑ Ð´Ð¾Ð»Ð¶Ð½Ñ ÑÐ±ÐµÐ´Ð¸ÑÑÑÑ, ÑÑÐ¾ ÑÐ¾Ð²Ð°Ñ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¹
entity.notification.AuthenticationStartedSellerNotification.baseMessage.plural=ÐÑ Ð´Ð¾Ð»Ð¶Ð½Ñ ÑÐ±ÐµÐ´Ð¸ÑÑÑÑ, ÑÑÐ¾ ÑÐ¾Ð²Ð°ÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐµ
entity.notification.AuthenticationStartedBuyerNotification.title=ÐÐ°ÐºÐ°Ð· â {0} Ð¿ÑÐ¾ÑÐ¾Ð´Ð¸Ñ Ð°ÑÑÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¸Ñ
entity.notification.AuthenticationStartedBuyerNotification.baseMessage.singular=ÐÑÐ¾Ð²ÐµÑÑÐµÐ¼ ÑÐ¾Ð²Ð°Ñ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ
entity.notification.AuthenticationStartedBuyerNotification.baseMessage.plural=ÐÑÐ¾Ð²ÐµÑÑÐµÐ¼ ÑÐ¾Ð²Ð°ÑÑ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ
entity.notification.DefectReconciliationStartedSellerNotification.title=Ð Ð·Ð°ÐºÐ°Ð·Ðµ â {0} Ñ ÑÐ¾Ð²Ð°ÑÐ° ÐµÑÑÑ Ð½ÑÐ°Ð½ÑÑ
entity.notification.DefectReconciliationStartedSellerNotification.baseMessage.singular=Ð¢Ð¾Ð²Ð°Ñ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¹, Ð½Ð¾ Ð¼Ñ Ð½Ð°ÑÐ»Ð¸ Ð½ÑÐ°Ð½ÑÑ, ÐºÐ¾ÑÐ¾ÑÑÑ Ð½Ðµ Ð±ÑÐ»Ð¾ Ð² Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ð¸. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ñ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ð¸ Ð¾Ð±ÑÑÐ´Ð¸Ð¼ Ð´ÐµÑÐ°Ð»Ð¸.
entity.notification.DefectReconciliationStartedSellerNotification.baseMessage.plural=Ð¢Ð¾Ð²Ð°ÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐµ, Ð½Ð¾ Ð¼Ñ Ð½Ð°ÑÐ»Ð¸ Ð½ÑÐ°Ð½ÑÑ, ÐºÐ¾ÑÐ¾ÑÑÑ Ð½Ðµ Ð±ÑÐ»Ð¾ Ð² Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ð¸. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ñ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ð¸ Ð¾Ð±ÑÑÐ´Ð¸Ð¼ Ð´ÐµÑÐ°Ð»Ð¸.
entity.notification.DefectReconciliationStartedBuyerNotification.title=Ð Ð·Ð°ÐºÐ°Ð·Ðµ â {0} Ñ ÑÐ¾Ð²Ð°ÑÐ° ÐµÑÑÑ Ð½ÑÐ°Ð½ÑÑ
entity.notification.DefectReconciliationStartedBuyerNotification.baseMessage.singular=Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ñ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ð¸ Ð¾Ð±ÑÑÐ´Ð¸Ð¼ Ð´ÐµÑÐ°Ð»Ð¸
entity.notification.DefectReconciliationStartedBuyerNotification.baseMessage.plural=Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ñ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ð¸ Ð¾Ð±ÑÑÐ´Ð¸Ð¼ Ð´ÐµÑÐ°Ð»Ð¸
entity.notification.OrderCancelledByBuyerNotification.title=ÐÐ°ÐºÐ°Ð· â {0} Ð¾ÑÐ¼ÐµÐ½ÐµÐ½
entity.notification.OrderCancelledByBuyerNotification.baseMessage=ÐÐ¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð¾ÑÐ¼ÐµÐ½Ð¸Ð» Ð·Ð°ÐºÐ°Ð·. ÐÑ Ð½Ð°Ð¿Ð¸ÑÐµÐ¼ Ð²Ð°Ð¼ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ ÑÑÑÐ¾Ðº Ð¸ ÑÐ°ÑÑÐºÐ°Ð¶ÐµÐ¼, ÑÑÐ¾ Ð´ÐµÐ»Ð°ÑÑ.
entity.notification.CommunityStatusDowngradedNotification.title=Ð¡ÑÐ°ÑÑÑ Ð² O!Community ÑÑÐ°Ð» Ð½Ð¸Ð¶Ðµ
entity.notification.CommunityStatusDowngradedNotification.baseMessage=Ð¢ÐµÐ¿ÐµÑÑ ÑÑÐ¾ {0}. ÐÐ¾ÐºÑÐ¿Ð°Ð¹ÑÐµ Ð¸ Ð¿ÑÐ¾Ð´Ð°Ð²Ð°Ð¹ÑÐµ ÑÐ°ÑÐµ, ÑÑÐ¾Ð±Ñ Ð¿Ð¾Ð»ÑÑÐ°ÑÑ Ð±Ð¾Ð»ÑÑÐµ Ð¿ÑÐ¸Ð²Ð¸Ð»ÐµÐ³Ð¸Ð¹
entity.notification.CommunityStatusUpgradedNotification.title=ÐÐ¾Ð²ÑÐ¹ ÑÑÐ°ÑÑÑ Ð² O!Community
entity.notification.CommunityStatusUpgradedNotification.baseMessage=ÐÑ Ð¿Ð¾Ð»ÑÑÐ¸Ð»Ð¸ ÑÑÐ°ÑÑÑ {0}. Ð¢ÐµÐ¿ÐµÑÑ Ð²Ð°Ð¼ Ð´Ð¾ÑÑÑÐ¿Ð½Ð¾ Ð±Ð¾Ð»ÑÑÐµ Ð¿ÑÐ¸Ð²Ð¸Ð»ÐµÐ³Ð¸Ð¹
entity.notification.CommunityStatusMayBeLostNotification.title=Ð¡Ð¾ÑÑÐ°Ð½Ð¸ÑÐµ Ð²Ð°Ñ ÑÑÐ°ÑÑÑ Ð² O!Community
entity.notification.CommunityStatusMayBeLostNotification.baseMessage=Ð§ÑÐ¾Ð±Ñ Ð½Ðµ Ð¿Ð¾ÑÐµÑÑÑÑ ÑÑÐ°ÑÑÑ {0}, ÑÐ¾Ð²ÐµÑÑÐ¸ÑÐµ{1} Ð´Ð¾ {2}
entity.notification.CommunityStatusLostNotification.title=ÐÑ Ð²ÑÑÐ»Ð¸ Ð¸Ð· Ð¿ÑÐ¾Ð³ÑÐ°Ð¼Ð¼Ñ O!Community
entity.notification.CommunityStatusLostNotification.baseMessage=Ð§ÑÐ¾Ð±Ñ ÑÐ½Ð¾Ð²Ð° Ð¿Ð¾Ð»ÑÑÐ°ÑÑ Ð¿ÑÐ¸Ð²Ð¸Ð»ÐµÐ³Ð¸Ð¸, Ð¿ÑÐ¾Ð´Ð°Ð¹ÑÐµ Ð¸Ð»Ð¸ ÐºÑÐ¿Ð¸ÑÐµ ÑÐ¾ÑÑ Ð±Ñ Ð¾Ð´Ð¸Ð½ ÑÐ¾Ð²Ð°Ñ
entity.notification.CommunityTitleReceivedNotification.title=ÐÐ¾Ð²ÑÐ¹ ÑÐ¸ÑÑÐ» Ð² O!Community
entity.notification.CommunityTitleReceivedNotification.baseMessage=ÐÑ Ð¿Ð¾Ð»ÑÑÐ¸Ð»Ð¸ ÑÐ¸ÑÑÐ» {0}! Ð¡Ð¿Ð°ÑÐ¸Ð±Ð¾, ÑÑÐ¾ Ð²Ñ Ñ Ð½Ð°Ð¼Ð¸!
entity.notification.ProductPriceDiscountNotification.title=Ð¦ÐµÐ½Ð° ÑÐ½Ð¸Ð¶ÐµÐ½Ð° Ð½Ð° {0}%
entity.notification.ProductPriceDiscountNotification.baseMessage=Ð¢Ð¾Ð²Ð°Ñ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½ Ð±Ð¾Ð»ÐµÐµ {0} {1} Ð½Ð°Ð·Ð°Ð´. ÐÐ¾ ÑÑÐ»Ð¾Ð²Ð¸ÑÐ¼ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ° Ð¼Ñ ÑÐ½Ð¸Ð·Ð¸Ð»Ð¸ ÑÐµÐ½Ñ â ÑÑÐ¾ Ð¿Ð¾Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑÑÐµÐµ Ð½Ð°Ð¹ÑÐ¸ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ.
entity.notification.ProductPriceDiscountNotification.chronoUnit.DAYS=Ð´Ð½ÐµÐ¹
entity.notification.ProductPriceDiscountNotification.chronoUnit.WEEKS=Ð½ÐµÐ´ÐµÐ»Ñ
entity.notification.ProductPriceDiscountNotification.chronoUnit.MONTHS=Ð¼ÐµÑÑÑÐµÐ²
entity.notification.ProductPriceDiscountNotification.chronoUnit.HOURS=ÑÐ°ÑÐ¾Ð²
entity.notification.ProductPriceDiscountNotification.chronoUnit.MINUTES=Ð¼Ð¸Ð½ÑÑ
entity.notification.OSocialCommentCreatedNotification.title=ÐÐ¾Ð²ÑÐ¹ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹
entity.notification.OSocialCommentCreatedNotification.withText.baseMessage={0}: â{1}â
entity.notification.OSocialCommentCreatedNotification.withoutText.baseMessage={0} Ð·Ð°Ð³ÑÑÐ·Ð¸Ð»{1} ÑÐ¾ÑÐ¾ Ð² ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹
entity.notification.OSocialPostCommentNotification.title=ÐÐ¾Ð²ÑÐ¹ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹
entity.notification.OSocialPostCommentNotification.withText.baseMessage={0}: â{1}â
entity.notification.OSocialPostCommentNotification.withoutText.baseMessage={0} Ð·Ð°Ð³ÑÑÐ·Ð¸Ð»{1} ÑÐ¾ÑÐ¾ Ð² ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹
entity.notification.OSocialProductUsedAtPostNotification.title={0}
entity.notification.OSocialProductUsedAtPostNotification.baseMessage=Ð¡Ð¼Ð¾ÑÑÐµÑÑ Ð¿ÑÐ±Ð»Ð¸ÐºÐ°ÑÐ¸Ñ Ñ Ð²Ð°ÑÐ¸Ð¼ ÑÐ¾Ð²Ð°ÑÐ¾Ð¼
entity.notification.OSocialUserMentionedAtPostNotification.title=ÐÐ°Ñ Ð¾ÑÐ¼ÐµÑÐ¸Ð»Ð¸
entity.notification.OSocialUserMentionedAtPostNotification.baseMessage={0} Ð¾ÑÐ¼ÐµÑÐ¸Ð»{1} Ð²Ð°Ñ Ð² Ð¿Ð¾ÑÑÐµ
entity.notification.OSocialTitledPostNotification.title={0} Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð»{1} Ð¿Ð¾ÑÑ
entity.notification.OSocialPostWasReviewedAndPublishedNotification.title=ÐÐ¾ÑÑ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½
entity.notification.OSocialPostWasReviewedAndPublishedNotification.baseMessage=ÐÑ Ð¿ÐµÑÐµÐ¿ÑÐ¾Ð²ÐµÑÐ¸Ð»Ð¸ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¸ Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð¸Ð»Ð¸ Ð²Ð°Ñ Ð¿Ð¾ÑÑ. Ð¡Ð¿Ð°ÑÐ¸Ð±Ð¾, ÑÑÐ¾ Ð¾ÑÑÐ°ÑÑÐµÑÑ Ñ Ð½Ð°Ð¼Ð¸!
entity.notification.OSocialPostWasReviewedAndRestoredNotification.title=ÐÐ¾ÑÑ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½
entity.notification.OSocialPostWasReviewedAndRestoredNotification.baseMessage=ÐÑ Ð¿ÐµÑÐµÐ¿ÑÐ¾Ð²ÐµÑÐ¸Ð»Ð¸ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¸ Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð¸Ð»Ð¸ Ð²Ð°Ñ Ð¿Ð¾ÑÑ. Ð¡Ð¿Ð°ÑÐ¸Ð±Ð¾, ÑÑÐ¾ Ð¾ÑÑÐ°ÑÑÐµÑÑ Ñ Ð½Ð°Ð¼Ð¸!
entity.notification.OSocialPostViolatedRulesAndWasHiddenNotification.title=ÐÑÐ±Ð»Ð¸ÐºÐ°ÑÐ¸Ñ ÑÐºÑÑÑÐ°
entity.notification.OSocialPostViolatedRulesAndWasHiddenNotification.baseMessage=ÐÐ°Ñ Ð¿Ð¾ÑÑ Ð½Ð°ÑÑÑÐ°ÐµÑ Ð¿ÑÐ°Ð²Ð¸Ð»Ð° ÑÐ¾Ð¾Ð±ÑÐµÑÑÐ²Ð°. ÐÐ·Ð½Ð°ÐºÐ¾Ð¼ÑÑÐµÑÑ Ñ Ð¿ÑÐ°Ð²Ð¸Ð»Ð°Ð¼Ð¸ Ð¸Ð»Ð¸ ÑÐ²ÑÐ¶Ð¸ÑÐµÑÑ Ñ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶ÐºÐ¾Ð¹
entity.notification.OSocialPostLikeNotification.title={0}
entity.notification.OSocialPostLikeNotification.baseMessage=ÐÑÐ°Ð²Ð¸ÑÑÑ Ð²Ð°Ñ Ð¿Ð¾ÑÑ
entity.notification.OSocialLikedProductUsedAtPostNotification.title={0}
entity.notification.OSocialLikedProductUsedAtPostNotification.baseMessage=Ð¡Ð¼Ð¾ÑÑÐµÑÑ Ð½Ð¾Ð²ÑÑ Ð¿ÑÐ±Ð»Ð¸ÐºÐ°ÑÐ¸Ñ Ñ ÑÐ¾Ð²Ð°ÑÐ¾Ð¼ Ð¸Ð· Ð²Ð°ÑÐµÐ³Ð¾ Ð¸Ð·Ð±ÑÐ°Ð½Ð½Ð¾Ð³Ð¾
entity.notification.BonusesTransferredNotification.title=+{0} ÐÐ°Ð»Ð»Ð¾Ð²!
entity.notification.BonusesTransferredNotification.withOrder.baseMessage=ÐÐ°Ð¼ Ð½Ð°ÑÐ¸ÑÐ»ÐµÐ½Ñ Ð±Ð°Ð»Ð»Ñ Ð·Ð° Ð·Ð°ÐºÐ°Ð· â{1}. Ð£Ð·Ð½Ð°ÑÑ ÑÐ²Ð¾Ð¹ Ð±Ð°Ð»Ð°Ð½Ñ âº
entity.notification.BonusesTransferredNotification.withoutOrder.baseMessage=ÐÐ°Ð¼ Ð½Ð°ÑÐ¸ÑÐ»ÐµÐ½Ñ Ð±Ð°Ð»Ð»Ñ Ð¾Ñ OSKELLY. Ð£Ð·Ð½Ð°ÑÑ ÑÐ²Ð¾Ð¹ Ð±Ð°Ð»Ð°Ð½Ñ âº
entity.notification.BonusesReturnedNotification.title=+{0} ÐÐ°Ð»Ð»Ð¾Ð²!
entity.notification.BonusesReturnedNotification.baseMessage=ÐÐµÑÐ½ÑÐ»Ð¸ Ð±Ð°Ð»Ð»Ñ Ð·Ð° Ð·Ð°ÐºÐ°Ð· â{1}. Ð£Ð·Ð½Ð°ÑÑ ÑÐ²Ð¾Ð¹ Ð±Ð°Ð»Ð°Ð½Ñ âº
entity.notification.BonusesExpiringNotification.title=ÐÐ°Ð»Ð»Ñ ÑÐ³Ð¾ÑÐ°ÑÑ!
entity.notification.BonusesExpiringNotification.baseMessage=â° ÐÑÑÐ°Ð»Ð¾ÑÑ 14 Ð´Ð½ÐµÐ¹, ÑÑÐ¾Ð±Ñ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ {0} Ð±Ð°Ð»Ð»Ð¾Ð². ÐÐµ ÑÐ¿ÑÑÑÐ¸ÑÐµ ÑÐ°Ð½Ñ!
entity.notification.bonuses.default.stage1.title=ÐÐ°Ð»Ð»Ñ ÑÐ³Ð¾ÑÐ°ÑÑ!
entity.notification.bonuses.default.stage1.baseMessage=â° ÐÑÑÐ°Ð»Ð¾ÑÑ 14 Ð´Ð½ÐµÐ¹, ÑÑÐ¾Ð±Ñ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ {0} Ð±Ð°Ð»Ð»Ð¾Ð². ÐÐµ ÑÐ¿ÑÑÑÐ¸ÑÐµ ÑÐ°Ð½Ñ!
entity.notification.bonuses.default.stage2.title=ÐÐ°Ð»Ð»Ñ ÑÐ³Ð¾ÑÐ°ÑÑ!
entity.notification.bonuses.default.stage2.baseMessage=â° ÐÑÑÐ°Ð»Ð¾ÑÑ 3 Ð´Ð½Ñ, ÑÑÐ¾Ð±Ñ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ {0} Ð±Ð°Ð»Ð»Ð¾Ð². ÐÐµ ÑÐ¿ÑÑÑÐ¸ÑÐµ ÑÐ°Ð½Ñ!
entity.notification.bonuses.welcome.initial.title=ÐÐ¾Ð±ÑÐ¾ Ð¿Ð¾Ð¶Ð°Ð»Ð¾Ð²Ð°ÑÑ Ð² ÐºÐ»ÑÐ± ð
entity.notification.bonuses.welcome.initial.baseMessage=ÐÑ Ñ Ð½Ð°Ð¼Ð¸ â Ð¸ ÑÑÐ¾ ÑÐ¶Ðµ Ð¿Ð¾Ð²Ð¾Ð´ Ð´Ð»Ñ Ð¿Ð¾Ð´Ð°ÑÐºÐ°. ÐÐ°Ð»Ð»Ñ Ð½Ð°ÑÐ¸ÑÐ»ÐµÐ½Ñ! ÐÐ¾ÑÐ¾Ð²Ñ Ð²ÑÐ±ÑÐ°ÑÑ ÑÐ²Ð¾Ñ Ð¼Ð¾Ð´Ð½ÑÑ Ð½Ð°ÑÐ¾Ð´ÐºÑ?
entity.notification.bonuses.welcome.stage1.title=ÐÐ°ÑÐ¸ Ð±Ð°Ð»Ð»Ñ ÑÐºÑÑÐ°ÑÑ ð
entity.notification.bonuses.welcome.stage1.baseMessage=ÐÑÑÐ°Ð»Ð¾ÑÑ 14 Ð´Ð½ÐµÐ¹, ÑÑÐ¾Ð±Ñ Ð¿ÑÐ¸Ð¼ÐµÐ½Ð¸ÑÑ Ð¿ÑÐ¸Ð²ÐµÑÑÑÐ²ÐµÐ½Ð½ÑÐµ Ð±Ð°Ð»Ð»Ñ. ÐÐ¾ÑÐ°Ð´ÑÐ¹ÑÐµ ÑÐµÐ±Ñ!
entity.notification.bonuses.welcome.stage2.title=ÐÑÑÐ°Ð»Ð¾ÑÑ 3 Ð´Ð½Ñ â°
entity.notification.bonuses.welcome.stage2.baseMessage=ÐÐ°ÑÐ¸ Ð¿ÑÐ¸Ð²ÐµÑÑÑÐ²ÐµÐ½Ð½ÑÐµ Ð±Ð°Ð»Ð»Ñ ÑÐºÐ¾ÑÐ¾ ÑÐ³Ð¾ÑÑÑ. Ð£ÑÐ¿ÐµÐ¹ÑÐµ Ð¿ÑÐ¸Ð¼ÐµÐ½Ð¸ÑÑ!
entity.notification.bonuses.birthday.stage1.title=ÐÐ°Ð»Ð»Ñ Ð² ÑÐµÑÑÑ Ð²Ð°ÑÐµÐ³Ð¾ Ð´Ð½Ñ! â¨
entity.notification.bonuses.birthday.stage1.baseMessage=Ð ÑÐµÑÑÑ Ð´Ð½Ñ ÑÐ¾Ð¶Ð´ÐµÐ½Ð¸Ñ â {0} Ð±Ð°Ð»Ð»Ð¾Ð² ÑÐ¶Ðµ Ñ Ð²Ð°Ñ! Ð£ÑÐ¿ÐµÐ¹ÑÐµ Ð¿Ð¾ÑÑÐ°ÑÐ¸ÑÑ Ð¸Ñ Ð½Ð° ÑÐ²Ð¾Ð¸ Ð»ÑÐ±Ð¸Ð¼ÑÐµ Ð±ÑÐµÐ½Ð´Ñ.
entity.notification.bonuses.birthday.stage2.title=Ð¡ ÐÐ½ÑÐ¼ Ð Ð¾Ð¶Ð´ÐµÐ½Ð¸Ñ! ð
entity.notification.bonuses.birthday.stage2.baseMessage=ÐÑÑÐ°Ð»Ð¾ÑÑ 22 Ð´Ð½Ñ, ÑÑÐ¾Ð±Ñ Ð²Ð¾ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑÑÑ Ð²Ð°ÑÐ¸Ð¼Ð¸ Ð±Ð¾Ð½ÑÑÐ½ÑÐ¼Ð¸ Ð±Ð°Ð»Ð»Ð°Ð¼Ð¸. ÐÐ¾Ð±Ð°Ð»ÑÐ¹ÑÐµ ÑÐµÐ±Ñ!
entity.notification.bonuses.birthday.stage3.title=ÐÐ°Ð»Ð»Ñ ÑÐºÐ¾ÑÐ¾ Ð¸ÑÑÐµÐ·Ð½ÑÑâ¦
entity.notification.bonuses.birthday.stage3.baseMessage=ÐÑÐµÐ³Ð¾ 3 Ð´Ð½Ñ, ÑÑÐ¾Ð±Ñ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ Ð¿Ð¾Ð´Ð°ÑÐ¾ÑÐ½ÑÐµ Ð±Ð°Ð»Ð»Ñ. ÐÑÑÑÑ Ð¿ÑÐ°Ð·Ð´Ð½Ð¸Ðº Ð´Ð»Ð¸ÑÑÑ Ð´Ð¾Ð»ÑÑÐµ!
entity.notification.LoyaltyStatusUpgradedAutoNotification.title=ÐÐ¾Ð²ÑÐ¹ ÑÑÐ°ÑÑÑ Ð² O!Loyalty
entity.notification.LoyaltyStatusUpgradedAutoNotification.baseMessage=ÐÑ Ð¿Ð¾Ð»ÑÑÐ¸Ð»Ð¸ ÑÑÐ°ÑÑÑ {0}. Ð¢ÐµÐ¿ÐµÑÑ Ð²Ð°Ð¼ Ð´Ð¾ÑÑÑÐ¿Ð½Ð¾ Ð±Ð¾Ð»ÑÑÐµ Ð¿ÑÐ¸Ð²Ð¸Ð»ÐµÐ³Ð¸Ð¹.
entity.notification.LoyaltyStatusDowngradedAutoNotification.title=Ð¡ÑÐ°ÑÑÑ Ð² O!Loyalty ÑÑÐ°Ð» Ð½Ð¸Ð¶Ðµ
entity.notification.LoyaltyStatusDowngradedAutoNotification.baseMessage=Ð¢ÐµÐ¿ÐµÑÑ {0}. Ð¡Ð¾Ð²ÐµÑÑÐ°Ð¹ÑÐµ Ð¿Ð¾ÐºÑÐ¿ÐºÐ¸ ÑÐ°ÑÐµ, ÑÑÐ¾Ð±Ñ Ð¿Ð¾Ð»ÑÑÐ°ÑÑ Ð±Ð¾Ð»ÑÑÐµ Ð¿ÑÐ¸Ð²Ð¸Ð»ÐµÐ³Ð¸Ð¹.
entity.notification.LoyaltyStatusSetByAdminNotification.limited.title=ÐÐ°Ð¼ Ð¿ÑÐ¸ÑÐ²Ð¾ÐµÐ½ ÑÑÐ°ÑÑÑ Ð² O!Loyalty
entity.notification.LoyaltyStatusSetByAdminNotification.limited.baseMessage=ÐÑ Ð¿Ð¾Ð»ÑÑÐ¸Ð»Ð¸ ÑÑÐ°ÑÑÑ {0} Ð´Ð¾ {1}. ÐÐ¾ÑÐ¿ÐµÑÐ¸ÑÐµ ÑÐ·Ð½Ð°ÑÑ, ÐºÐ°ÐºÐ¸Ðµ Ð¿ÑÐ¸Ð²Ð¸Ð»ÐµÐ³Ð¸Ð¸ ÑÐµÐ¿ÐµÑÑ Ð²Ð°Ð¼ Ð´Ð¾ÑÑÑÐ¿Ð½Ñ.
entity.notification.LoyaltyStatusSetByAdminNotification.unlimited.title=ÐÐ°Ð¼ Ð¿ÑÐ¸ÑÐ²Ð¾ÐµÐ½ ÑÑÐ°ÑÑÑ Ð² O!Loyalty
entity.notification.LoyaltyStatusSetByAdminNotification.unlimited.baseMessage=ÐÑ Ð¿Ð¾Ð»ÑÑÐ¸Ð»Ð¸ ÑÑÐ°ÑÑÑ {0}. ÐÐ¾ÑÐ¿ÐµÑÐ¸ÑÐµ ÑÐ·Ð½Ð°ÑÑ, ÐºÐ°ÐºÐ¸Ðµ Ð¿ÑÐ¸Ð²Ð¸Ð»ÐµÐ³Ð¸Ð¸ ÑÐµÐ¿ÐµÑÑ Ð²Ð°Ð¼ Ð´Ð¾ÑÑÑÐ¿Ð½Ñ.

entity.notification.UserDescriptionDeclinedNotification.title=ÐÐ±Ð½Ð¾Ð²Ð¸ÑÐµ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ðµ Ð¿ÑÐ¾ÑÐ¸Ð»Ñ
entity.notification.OrderConfirmNeedPassportDataNotification.title=ÐÑ Ð¾ÑÐ¾ÑÐ¼Ð¸Ð»Ð¸ Ð´Ð¾ÑÑÐ°Ð²ÐºÑ Ð¸Ð·-Ð·Ð° Ð³ÑÐ°Ð½Ð¸ÑÑ Ð¸ ÑÐ¾Ð²Ð°Ñ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð¼. ÐÐ»Ñ Ð¿ÑÐ¾ÑÐ¾Ð¶Ð´ÐµÐ½Ð¸Ñ ÑÐ°Ð¼Ð¾Ð¶Ð½Ð¸ Ð¿ÑÐ¸ Ð²Ð²Ð¾Ð·Ðµ Ð² Ð Ð¤ Ð¿Ð¾ÑÑÐµÐ±ÑÑÑÑÑ Ð²Ð°ÑÐ¸ Ð¿Ð°ÑÐ¿Ð¾ÑÑÐ½ÑÐµ Ð´Ð°Ð½Ð½ÑÐµ.
entity.notification.OrderConfirmNeedPassportDataNotification.baseMessage=ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð·Ð°Ð¿Ð¾Ð»Ð½Ð¸ÑÐµ ÑÐ¾ÑÐ¼Ñ Ð² Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ð¸ Ð² Ð»Ð¸ÑÐ½Ð¾Ð¼ ÐºÐ°Ð±Ð¸Ð½ÐµÑÐµ.
entity.notification.OrderPaidNeedPassportDataNotification.title=ÐÐ°Ñ ÑÐ¾Ð²Ð°Ñ Ð³Ð¾ÑÐ¾Ð²Ð¸ÑÑÑ Ðº Ð¾ÑÐ¿ÑÐ°Ð²ÐºÐµ Ð² Ð½Ð°Ñ Ð¾ÑÐ¸Ñ Ð´Ð»Ñ Ð¿ÑÐ¾ÑÐ¾Ð¶Ð´ÐµÐ½Ð¸Ñ Ð¼Ð½Ð¾Ð³Ð¾ÑÑÑÐ¿ÐµÐ½ÑÐ°ÑÐ¾Ð¹ Ð¿ÑÐ¾Ð²ÐµÑÐºÐ¸. ÐÐ´Ð½Ð°ÐºÐ¾ Ð´Ð»Ñ ÑÐ°Ð¼Ð¾Ð¶ÐµÐ½Ð½Ð¾Ð³Ð¾ Ð¾ÑÐ¾ÑÐ¼Ð»ÐµÐ½Ð¸Ñ Ð¿ÑÐ¸ Ð´Ð¾ÑÑÐ°Ð²ÐºÐµ Ð² Ð Ð¤ Ð½Ð°Ð¼ Ð²ÑÐµ ÐµÑÐµ ÑÑÐµÐ±ÑÑÑÑÑ Ð²Ð°ÑÐ¸ Ð¿Ð°ÑÐ¿Ð¾ÑÑÐ½ÑÐµ Ð´Ð°Ð½Ð½ÑÐµ.
entity.notification.OrderPaidNeedPassportDataNotification.baseMessage=ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð·Ð°Ð¿Ð¾Ð»Ð½Ð¸ÑÐµ ÑÐ¾ÑÐ¼Ñ Ð² Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ð¸ Ð² Ð»Ð¸ÑÐ½Ð¾Ð¼ ÐºÐ°Ð±Ð¸Ð½ÐµÑÐµ.
entity.notification.OrderTransitStockNeedPassportDataNotification.title=ÐÐ°Ñ Ð·Ð°ÐºÐ°Ð· Ð¾ÑÐ¿ÑÐ°Ð²Ð»ÐµÐ½ Ð½Ð° ÑÑÐ°Ð½Ð·Ð¸ÑÐ½ÑÐ¹ ÑÐºÐ»Ð°Ð´ Ð¸ Ð±ÑÐ´ÐµÑ Ð³Ð¾ÑÐ¾Ð²Ð¸ÑÑÑÑ Ðº Ð¾ÑÐ¿ÑÐ°Ð²ÐºÐµ Ð² Ð Ð¾ÑÑÐ¸Ñ. ÐÐ°Ð¿Ð¾Ð¼Ð¸Ð½Ð°ÐµÐ¼, ÑÑÐ¾ Ð±ÐµÐ· Ð¿Ð°ÑÐ¿Ð¾ÑÑÐ½ÑÑ Ð´Ð°Ð½Ð½ÑÑ Ð¾Ð½ Ð½Ðµ ÑÐ¼Ð¾Ð¶ÐµÑ Ð¿ÑÐ¾Ð¹ÑÐ¸ ÑÐ°Ð¼Ð¾Ð¶ÐµÐ½Ð½ÑÐ¹ ÐºÐ¾Ð½ÑÑÐ¾Ð»Ñ.
entity.notification.OrderTransitStockNeedPassportDataNotification.baseMessage=ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð·Ð°Ð¿Ð¾Ð»Ð½Ð¸ÑÐµ ÑÐ¾ÑÐ¼Ñ Ð² Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ð¸ Ð² Ð»Ð¸ÑÐ½Ð¾Ð¼ ÐºÐ°Ð±Ð¸Ð½ÐµÑÐµ.

entity.enum.OrderCreationProblem.minimum-amount-not-reached=ÐÐµ Ð´Ð¾ÑÑÐ¸Ð³Ð½ÑÑÐ° Ð¼Ð¸Ð½Ð¸Ð¼Ð°Ð»ÑÐ½Ð°Ñ ÑÑÐ¼Ð¼Ð°
entity.enum.OrderCreationProblem.CANT_DELIVERY_ITEM_TO_FOREIGN_COUNTRY=ÐÐ°Ð½Ð½Ð°Ñ ÐºÐ°ÑÐµÐ³Ð¾ÑÐ¸Ñ ÑÐ¾Ð²Ð°ÑÐ¾Ð² Ð´Ð¾ÑÑÐ°Ð²Ð»ÑÐµÑÑÑ ÑÐ¾Ð»ÑÐºÐ¾ Ð¿Ð¾ Ð Ð¾ÑÑÐ¸Ð¸. ÐÑÐ»Ð¸ Ð²Ñ Ð² Ð Ð¾ÑÑÐ¸Ð¸, ÑÐ¾ Ð¿ÑÐ¾Ð²ÐµÑÑÑÐµ ÑÑÐ¾ Ð²Ð°Ñ Ð°Ð´ÑÐµÑ ÑÐºÐ°Ð·Ð°Ð½ Ð¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ð¾.
entity.enum.OrderCreationProblem.CANT_CONTAIN_MULTIPLE_ORDER_POSITIONS=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑ Ð½ÐµÑÐºÐ¾Ð»ÑÐºÐ¾ Ð¿Ð¾Ð·Ð¸ÑÐ¸Ð¹.
entity.enum.OrderCreationProblem.CANT_BE_CONCIERGE=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ ÐºÐ¾Ð½ÑÑÐµÑÐ¶Ð½ÑÐ¼.
entity.enum.OrderCreationProblem.CANT_BE_BOUTIQUE=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð±ÑÑÐ¸ÐºÐ¾Ð²ÑÐ¼.
entity.enum.OrderEvent.DEAL_INITIATED.buyerTitle=ÐÐ¶Ð¸Ð´Ð°ÐµÑ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ñ
entity.enum.OrderEvent.CONFIRMED.buyerTitle=ÐÐ°ÐºÐ°Ð· Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½
entity.enum.OrderEvent.CONFIRMED.sellerTitle=ÐÑÐ¾Ð´Ð°Ð¶Ð° Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð°
entity.enum.OrderEvent.CONFIRMED.proSellerTitle=ÐÑÐ¾Ð´Ð°Ð¶Ð° Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð°
entity.enum.OrderEvent.CONFIRMED_PARTLY.buyerTitle=ÐÐ°ÐºÐ°Ð· Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½
entity.enum.OrderEvent.CONFIRMED_PARTLY.sellerTitle=ÐÑÐ¾Ð´Ð°Ð¶Ð° Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð° ÑÐ°ÑÑÐ¸ÑÐ½Ð¾
entity.enum.OrderEvent.CONFIRMED_PARTLY.proSellerTitle=ÐÑÐ¾Ð´Ð°Ð¶Ð° Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð° ÑÐ°ÑÑÐ¸ÑÐ½Ð¾
entity.enum.OrderEvent.SALE_REJECTED.buyerTitle=ÐÐ°ÐºÐ°Ð· Ð¾ÑÐºÐ»Ð¾Ð½ÐµÐ½
entity.enum.OrderEvent.SALE_REJECTED.sellerTitle=ÐÑÐ¾Ð´Ð°Ð¶Ð° Ð¾ÑÐºÐ»Ð¾Ð½ÐµÐ½Ð°
entity.enum.OrderEvent.SALE_REJECTED.proSellerTitle=ÐÑÐ¾Ð´Ð°Ð¶Ð° Ð¾ÑÐºÐ»Ð¾Ð½ÐµÐ½Ð°
entity.enum.OrderEvent.PICKING_UP_FROM_SELLER.buyerTitle=ÐÐ°ÐºÐ°Ð· Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½
entity.enum.OrderEvent.PICKING_UP_FROM_SELLER.sellerTitle=ÐÑÐ¾Ð´Ð°Ð¶Ð° Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð°
entity.enum.OrderEvent.PICKING_UP_FROM_SELLER.proSellerTitle=ÐÑÐ¾Ð´Ð°Ð¶Ð° Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð°
entity.enum.OrderEvent.FROM_SELLER_TO_OFFICE.buyerTitle=ÐÐ°ÐºÐ°Ð· Ð¿ÐµÑÐµÐ´Ð°Ð½ Ð² ÐºÑÑÑÐµÑÑÐºÑÑ ÑÐ»ÑÐ¶Ð±Ñ
entity.enum.OrderEvent.FROM_SELLER_TO_OFFICE.sellerTitle=ÐÐ°ÐºÐ°Ð· Ð¿ÐµÑÐµÐ´Ð°Ð½ Ð² ÐºÑÑÑÐµÑÑÐºÑÑ ÑÐ»ÑÐ¶Ð±Ñ
entity.enum.OrderEvent.FROM_SELLER_TO_OFFICE.proSellerTitle=ÐÐ°ÐºÐ°Ð· Ð¿ÐµÑÐµÐ´Ð°Ð½ Ð² ÐºÑÑÑÐµÑÑÐºÑÑ ÑÐ»ÑÐ¶Ð±Ñ
entity.enum.OrderEvent.PICKUP_DECLINED.buyerTitle=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð±ÑÐ» Ð¾ÑÐ³ÑÑÐ¶ÐµÐ½ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð¼
entity.enum.OrderEvent.PICKUP_DECLINED.sellerTitle=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð±ÑÐ» Ð¾ÑÐ³ÑÑÐ¶ÐµÐ½ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð¼
entity.enum.OrderEvent.PICKUP_DECLINED.proSellerTitle=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð±ÑÐ» Ð¾ÑÐ³ÑÑÐ¶ÐµÐ½ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð¼
entity.enum.OrderEvent.DELIVERED_TO_EXPERTISE.buyerTitle=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¸Ð±ÑÐ» Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.enum.OrderEvent.DELIVERED_TO_EXPERTISE.sellerTitle=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¸Ð±ÑÐ» Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.enum.OrderEvent.DELIVERED_TO_EXPERTISE.proSellerTitle=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¸Ð±ÑÐ» Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.enum.OrderEvent.EXPERTISE_PASSED.buyerTitle=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.enum.OrderEvent.EXPERTISE_PASSED.sellerTitle=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.enum.OrderEvent.EXPERTISE_PASSED.proSellerTitle=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.enum.OrderEvent.EXPERTISE_PASSED_WITH_DEFECT.buyerTitle=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ, ÐµÑÑÑ Ð´ÐµÑÐµÐºÑÑ
entity.enum.OrderEvent.EXPERTISE_PASSED_WITH_DEFECT.sellerTitle=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ, ÐµÑÑÑ Ð´ÐµÑÐµÐºÑÑ
entity.enum.OrderEvent.EXPERTISE_PASSED_WITH_DEFECT.proSellerTitle=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ, ÐµÑÑÑ Ð´ÐµÑÐµÐºÑÑ
entity.enum.OrderEvent.EXPERTISE_PASSED_PARTLY.buyerTitle=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ ÑÐ°ÑÑÐ¸ÑÐ½Ð¾
entity.enum.OrderEvent.EXPERTISE_PASSED_PARTLY.sellerTitle=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ ÑÐ°ÑÑÐ¸ÑÐ½Ð¾
entity.enum.OrderEvent.EXPERTISE_PASSED_PARTLY.proSellerTitle=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ ÑÐ°ÑÑÐ¸ÑÐ½Ð¾
entity.enum.OrderEvent.EXPERTISE_PASSED_WITH_CLEANING.buyerTitle=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.enum.OrderEvent.EXPERTISE_PASSED_WITH_CLEANING.sellerTitle=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ, Ð¿ÑÐ¾Ð²ÐµÐ´ÐµÐ½Ð° ÑÐ¸Ð¼ÑÐ¸ÑÑÐºÐ°
entity.enum.OrderEvent.EXPERTISE_PASSED_WITH_CLEANING.proSellerTitle=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ, Ð¿ÑÐ¾Ð²ÐµÐ´ÐµÐ½Ð° ÑÐ¸Ð¼ÑÐ¸ÑÑÐºÐ°
entity.enum.OrderEvent.EXPERTISE_FAILED.buyerTitle=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.enum.OrderEvent.EXPERTISE_FAILED.sellerTitle=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.enum.OrderEvent.EXPERTISE_FAILED.proSellerTitle=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.enum.OrderEvent.PICKING_UP_FROM_OFFICE.buyerTitle=ÐÐ°ÐºÐ°Ð· Ð¿ÐµÑÐµÐ´Ð°Ð½ ÐºÑÑÑÐµÑÑ
entity.enum.OrderEvent.PICKING_UP_FROM_OFFICE.sellerTitle=ÐÐ°ÐºÐ°Ð· Ð¿ÐµÑÐµÐ´Ð°Ð½ Ð² ÐºÑÑÑÐµÑÑÐºÑÑ ÑÐ»ÑÐ¶Ð±Ñ
entity.enum.OrderEvent.PICKING_UP_FROM_OFFICE.proSellerTitle=ÐÐ°ÐºÐ°Ð· Ð¿ÐµÑÐµÐ´Ð°Ð½ Ð² ÐºÑÑÑÐµÑÑÐºÑÑ ÑÐ»ÑÐ¶Ð±Ñ
entity.enum.OrderEvent.FROM_OFFICE_TO_BUYER.buyerTitle=ÐÐ°ÐºÐ°Ð· Ð¿ÐµÑÐµÐ´Ð°Ð½ Ð² ÐºÑÑÑÐµÑÑÐºÑÑ ÑÐ»ÑÐ¶Ð±Ñ
entity.enum.OrderEvent.FROM_OFFICE_TO_BUYER.sellerTitle=ÐÐ°ÐºÐ°Ð· Ð¿ÐµÑÐµÐ´Ð°Ð½ Ð² ÐºÑÑÑÐµÑÑÐºÑÑ ÑÐ»ÑÐ¶Ð±Ñ
entity.enum.OrderEvent.FROM_OFFICE_TO_BUYER.proSellerTitle=ÐÐ°ÐºÐ°Ð· Ð¿ÐµÑÐµÐ´Ð°Ð½ Ð² ÐºÑÑÑÐµÑÑÐºÑÑ ÑÐ»ÑÐ¶Ð±Ñ
entity.enum.OrderEvent.DELIVERED_TO_BUYER.buyerTitle=ÐÐ°ÐºÐ°Ð· Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½
entity.enum.OrderEvent.DELIVERED_TO_BUYER.sellerTitle=ÐÐ°ÐºÐ°Ð· Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½
entity.enum.OrderEvent.DELIVERED_TO_BUYER.proSellerTitle=ÐÐ°ÐºÐ°Ð· Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½
entity.enum.OrderEvent.DELIVERED_TO_BUYER_EXPECTING_CONFIRM_AGENT_REPORT.buyerTitle=ÐÐ°ÐºÐ°Ð· Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½
entity.enum.OrderEvent.DELIVERED_TO_BUYER_EXPECTING_CONFIRM_AGENT_REPORT.sellerTitle=ÐÐ°ÐºÐ°Ð· Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½
entity.enum.OrderEvent.DELIVERED_TO_BUYER_EXPECTING_CONFIRM_AGENT_REPORT.proSellerTitle=ÐÐ°ÐºÐ°Ð· Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½
entity.enum.OrderEvent.AGENT_REPORT_CONFIRMED.buyerTitle=ÐÐ°ÐºÐ°Ð· Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½
entity.enum.OrderEvent.AGENT_REPORT_CONFIRMED.sellerTitle=ÐÐ°ÐºÐ°Ð· Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½
entity.enum.OrderEvent.AGENT_REPORT_CONFIRMED.proSellerTitle=ÐÐ°ÐºÐ°Ð· Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½
entity.enum.OrderEvent.ORDER_RETURN.buyerTitle=ÐÐ¾Ð·Ð²ÑÐ°Ñ
entity.enum.OrderEvent.ORDER_RETURN.sellerTitle=ÐÐ¾Ð·Ð²ÑÐ°Ñ
entity.enum.OrderEvent.ORDER_RETURN.proSellerTitle=ÐÐ¾Ð·Ð²ÑÐ°Ñ
entity.enum.OrderEvent.ORDER_COMPLETED.buyerTitle=ÐÐ°ÐºÐ°Ð· Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½
entity.enum.OrderEvent.ORDER_COMPLETED.sellerTitle=ÐÐ°ÐºÐ°Ð· Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½
entity.enum.OrderEvent.ORDER_COMPLETED.proSellerTitle=ÐÐ°ÐºÐ°Ð· Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½
entity.enum.OrderPositionMetadata.MONEY_RETURNED=ÐÐµÐ½ÑÐ³Ð¸ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµÐ½Ñ
entity.enum.OrderPositionMetadata.RETURN=ÐÐ¾Ð·Ð²ÑÐ°Ñ
entity.enum.OrderPositionMetadata.product-delivery=Ð¢Ð¾Ð²Ð°Ñ Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½
entity.enum.OrderPositionMetadata.profit=ÐÑÐ¸Ð±ÑÐ»Ñ
entity.enum.OrderPositionMetadata.money-paid=ÐÐµÐ½ÑÐ³Ð¸ Ð²ÑÐ¿Ð»Ð°ÑÐµÐ½Ñ
entity.enum.OrderPositionState.INITIAL.description=ÐÐ°ÑÐ°Ð»ÑÐ½Ð¾Ðµ ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ðµ Ð¿Ð¾Ð·Ð¸ÑÐ¸Ð¸ Ð·Ð°ÐºÐ°Ð·Ð°
entity.enum.OrderPositionState.PURCHASE_REQUEST.description=ÐÐ¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð¾Ð¿Ð»Ð°ÑÐ¸Ð» Ð²ÐµÑÑ Ð² ÑÐ°Ð¼ÐºÐ°Ñ Ð·Ð°ÐºÐ°Ð·Ð°
entity.enum.OrderPositionState.SALE_CONFIRMED.description=Ð¢Ð¾Ð²Ð°Ñ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½
entity.enum.OrderPositionState.SALE_REJECTED.description=Ð¢Ð¾Ð²Ð°Ñ Ð½Ðµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½
entity.enum.OrderPositionState.PICKUP_DECLINED.description=Ð¢Ð¾Ð²Ð°Ñ Ð½Ðµ Ð¾ÑÐ³ÑÑÐ¶ÐµÐ½
entity.enum.OrderPositionState.HQ_WAREHOUSE.description=ÐÐ° ÑÐºÐ»Ð°Ð´Ðµ Oskelly
entity.enum.OrderPositionState.ON_VERIFICATION.description=ÐÐ° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ðµ
entity.enum.OrderPositionState.VERIFICATION_OK.description=Ð­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ð° Ð¿ÑÐ¾Ð¹Ð´ÐµÐ½Ð°
entity.enum.OrderPositionState.VERIFICATION_NEED_CLEANING.description=ÐÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð° ÑÐ¸Ð¼ÑÐ¸ÑÑÐºÐ°
entity.enum.OrderPositionState.VERIFICATION_BAD_STATE.description=ÐÑÑÑ Ð´ÐµÑÐµÐºÑÑ
entity.enum.OrderPositionState.REJECTED_AFTER_VERIFICATION.description=Ð­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ð° Ð½Ðµ Ð¿ÑÐ¾Ð¹Ð´ÐµÐ½Ð°
entity.enum.OrderPositionState.READY_TO_SHIP.description=Ð¡Ð¾Ð·Ð´Ð°Ð½Ð° Ð½Ð°ÐºÐ»Ð°Ð´Ð½Ð°Ñ Ð´Ð»Ñ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
entity.enum.OrderPositionState.CREATE_WAYBILL_TO_BUYER.description=ÐÑÑÑÐµÑ Ð²ÑÐ·Ð²Ð°Ð½, Ð½Ð¾ ÐµÑÐµ Ð½Ðµ Ð¿ÑÐ¸ÐµÑÐ°Ð»
entity.enum.OrderPositionState.SHIPPED_TO_CLIENT.description=ÐÐ¾ÑÑÐ°Ð²ÐºÐ° Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
entity.enum.OrderPositionState.REQUESTED_TO_RETURN.description=ÐÐ¾Ð´Ð°Ð½Ð° Ð·Ð°ÑÐ²ÐºÐ° Ð½Ð° Ð²Ð¾Ð·Ð²ÑÐ°Ñ
entity.enum.OrderPositionState.RETURN_ACCEPTED.description=ÐÑÐ¸Ð½ÑÑ Ð½Ð° Ð²Ð¾Ð·Ð²ÑÐ°Ñ
entity.enum.OrderPositionState.RETURN_DECLINED.description=ÐÑÐºÐ»Ð¾Ð½ÐµÐ½ Ð½Ð° Ð²Ð¾Ð·Ð²ÑÐ°Ñ
entity.enum.OrderPositionState.RETURN_VERIFICATION_OK.description=Ð­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ð° Ð¾Ð´Ð¾Ð±ÑÐµÐ½Ð°
entity.enum.OrderPositionState.RETURN_VERIFICATION_REJECTED.description=Ð­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ð° Ð¾ÑÐºÐ»Ð¾Ð½ÐµÐ½Ð°
entity.enum.OrderRefundReason.UNDEFINED.description=ÐÑÐµ
entity.enum.OrderRefundReason.NOT_CONFIRMED.description=ÐÐµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½
entity.enum.OrderRefundReason.NOT_SHIPPED.description=ÐÐµ Ð¾ÑÐ³ÑÑÐ¶ÐµÐ½
entity.enum.OrderRefundReason.NOT_PASS_EXPERTISE.description=ÐÐµ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
entity.enum.OrderStatus.UNDEFINED.title=ÐÑÐµ
entity.enum.OrderStatus.UNCOMPLETED.title=
entity.enum.OrderStatus.ORDER_CANT_CONFIRM_NO_SELLER_ADDRESS.title=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶ÐµÐ½, Ð½ÐµÑ Ð°Ð´ÑÐµÑÐ°
entity.enum.OrderStatus.ORDER_CONFIRMING.title=ÐÐ°ÐºÐ°Ð· Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´Ð°ÐµÑÑÑ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð¼
entity.enum.OrderStatus.ORDER_REFUND.title=ÐÐ¾Ð·Ð²ÑÐ°Ñ ÑÑÐµÐ´ÑÑÐ²
entity.enum.OrderStatus.ORDER_CONFIRMED.title=ÐÑÐ¾Ð´Ð°Ð¶Ð° Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð° (Ð½ÐµÐ¾Ð¿Ð¾Ð·Ð½Ð°Ð½Ð½ÑÐ¹ Ð°Ð´ÑÐµÑ)
entity.enum.OrderStatus.CONCIERGE_ITEMS_WAITING_CONFIRMATION.title=\uD83D\uDECEï¸ ÐÐ°ÐºÐ°Ð· Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´Ð°ÐµÑÑÑ ÐºÐ¾Ð½ÑÑÐµÑÐ¶ÐµÐ¼
entity.enum.OrderStatus.SELLER_IN_MOSCOW.title=Ð¢Ð¾Ð²Ð°Ñ Ð² ÐÐ¾ÑÐºÐ²Ðµ (Ð²ÑÐ±Ð¾Ñ ÑÐ¿Ð¾ÑÐ¾Ð±Ð° Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸)
entity.enum.OrderStatus.EXPECTING_COURIER_TO_SELLER.title=ÐÐ¶Ð¸Ð´Ð°ÐµÐ¼ ÐºÑÑÑÐµÑÐ° Ð´Ð»Ñ Ð·Ð°Ð±Ð¾ÑÐ° ÑÐ¾Ð²Ð°ÑÐ° Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°
entity.enum.OrderStatus.OURSELVES_PICKING_UP_FROM_SELLER.title=ÐÐ°Ð±Ð¸ÑÐ°ÐµÐ¼ ÑÐ°Ð¼Ð¸ Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°
entity.enum.OrderStatus.OURSELVES_FROM_SELLER_TO_OFFICE.title=Ð¡Ð°Ð¼Ð¸ Ð²ÐµÐ·ÐµÐ¼ ÑÐ¾Ð²Ð°Ñ Ð¾Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ° Ð² Ð¾ÑÐ¸Ñ
entity.enum.OrderStatus.LOGIST_ON_WAY_TO_SELLER.title=ÐÐ¾Ð³Ð¸ÑÑ Ð² Ð¿ÑÑÐ¸ Ðº Ð¿ÑÐ¾Ð´Ð°Ð²ÑÑ
entity.enum.OrderStatus.FROM_SELLER_TO_OFFICE.title=ÐÐ¾Ð³Ð¸ÑÑ Ð²ÐµÐ·ÐµÑ ÑÐ¾Ð²Ð°Ñ Ð¾Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ° Ð² Ð¾ÑÐ¸Ñ
entity.enum.OrderStatus.HAS_CONCIERGE_ITEMS.title=ðï¸ ÐÐ¾Ð½ÑÑÐµÑÐ¶ Ð²ÐµÐ·ÐµÑ ÑÐ¾Ð²Ð°Ñ Ð² Ð¾ÑÐ¸Ñ
entity.enum.OrderStatus.EXPERTISE_START.title=Ð­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ð° Ð½Ð°ÑÐ°Ð»Ð°ÑÑ
entity.enum.OrderStatus.EXPERTISE_COMPLETED.title=Ð­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ð° Ð·Ð°Ð²ÐµÑÑÐµÐ½Ð°
entity.enum.OrderStatus.CHOOSING_DELIVERY_METHOD_O2B.title=ÐÑÐ±Ð¾Ñ ÑÐ¿Ð¾ÑÐ¾Ð±Ð° Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸
entity.enum.OrderStatus.HOLD_COMPLETE_REJECTED.title=ÐÐµÐ½ÑÐ³Ð¸ ÑÐ°ÑÑÐ¾Ð»Ð´Ð¸ÑÐ¾Ð²Ð°Ð»Ð¸ÑÑ
entity.enum.OrderStatus.EXPECTING_COURIER_TO_BUYER.title=ÐÐ¶Ð¸Ð´Ð°ÐµÐ¼ ÐºÑÑÑÐµÑÐ° Ð´Ð»Ñ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
entity.enum.OrderStatus.LOGIST_ON_WAY_TO_BUYER.title=ÐÑÑÑÐµÑ Ð² Ð¿ÑÑÐ¸ Ðº Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
entity.enum.OrderStatus.BUYER_IN_MOSCOW.title=Ð¢Ð¾Ð²Ð°Ñ Ð² ÐÐ¾ÑÐºÐ²Ðµ (Ð²ÑÐ±Ð¾Ñ ÑÐ¿Ð¾ÑÐ¾Ð±Ð° Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸)
entity.enum.OrderStatus.OURSELVES_DELIVERY_TO_BUYER.title=ÐÐ¾ÑÑÐ°Ð²Ð»ÑÐµÐ¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ ÑÐ°Ð¼Ð¸
entity.enum.OrderStatus.OURSELVES_FROM_OFFICE_TO_BUYER.title=Ð Ð¿ÑÑÐ¸ Ðº Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ ÑÐ°Ð¼Ð¸
entity.enum.OrderStatus.ORDER_DELIVERED.title=ÐÐ°ÐºÐ°Ð· Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½
entity.enum.OrderStatus.HAS_DISPUTE.title=ÐÑÑÑ ÑÐ¿Ð¾Ñ
entity.enum.OrderStatus.ORDER_IN_BOUTIQUE.title=Ð¢Ð¾Ð²Ð°Ñ Ð² Ð±ÑÑÐ¸ÐºÐµ
entity.enum.OrderStatus.ORDER_SOLD_IN_BOUTIQUE.title=ÐÑÐ¾Ð´Ð°Ð½Ð¾ Ð² Ð±ÑÑÐ¸ÐºÐµ
entity.enum.OrderStatus.EXPECTING_CONFIRM_AGENT_REPORT.title=ÐÐ¶Ð¸Ð´Ð°ÐµÐ¼ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ðµ Ð¾ÑÑÐµÑÐ° Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð¶Ðµ
entity.enum.OrderStatus.WAIT_PAYMENT_MONEY_TO_SELLER.title=ÐÑÑÐµÑ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½, Ð¾Ð¶Ð¸Ð´Ð°ÐµÑ Ð·Ð°ÑÐ¸ÑÐ»ÐµÐ½Ð¸Ñ
entity.enum.OrderStatus.ORDER_COMPLETED.title=ÐÐ°ÐºÐ°Ð· Ð·Ð°Ð²ÐµÑÑÐµÐ½ (Ð´ÐµÐ½ÑÐ³Ð¸ Ð²ÑÐ¿Ð»Ð°ÑÐµÐ½Ñ)
entity.enum.OrderStatus.ORDER_COMPLETED_RETURN.title=ÐÐ°ÐºÐ°Ð· Ð·Ð°Ð²ÐµÑÑÐµÐ½ (Ð²Ð¾Ð·Ð²ÑÐ°Ñ)
entity.enum.OrderStatus.RETURN_CREATED.title=ÐÐ¾Ð´Ð°Ð½Ð° Ð·Ð°ÑÐ²ÐºÐ° Ð½Ð° Ð²Ð¾Ð·Ð²ÑÐ°Ñ
entity.enum.OrderStatus.RETURN_ON_WAY_TO_OFFICE.title=Ð Ð¿ÑÑÐ¸ Ð² Ð¾ÑÐ¸Ñ
entity.enum.OrderStatus.RETURN_EXPERTISE.title=Ð­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ð°
entity.enum.OrderStatus.RETURN_COMPLETED.title=ÐÐ¾Ð·Ð²ÑÐ°Ñ Ð·Ð°Ð²ÐµÑÑÐµÐ½
entity.enum.OrderStatus.BOUTIQUE_ORDER_ON_WAY_TO_OFFICE.title=ÐÐ°ÐºÐ°Ð·Ñ Ð² Ð¿ÑÑÐ¸ Ð² Ð¾ÑÐ¸Ñ
entity.enum.OrderStatus.BOUTIQUE_ORDER_ON_EXPERTISE.title=ÐÐ°ÐºÐ°Ð·Ñ Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ðµ
entity.enum.OrderStatus.BOUTIQUE_ORDER_ON_WAY_TO_BOUTIQUE.title=ÐÐ°ÐºÐ°Ð·Ñ Ð² Ð¿ÑÑÐ¸ Ð² Ð±ÑÑÐ¸Ðº
entity.enum.OrderStatus.BOUTIQUE_ORDER_IN_BOUTIQUE.title=ÐÐ°ÐºÐ°Ð·Ñ (ÑÐ¾Ð²Ð°ÑÑ) Ð² Ð±ÑÑÐ¸ÐºÐµ
entity.enum.OrderStatus.BOUTIQUE_ORDER_SOLD_IN_BOUTIQUE.title=ÐÑÐ¾Ð´Ð°Ð½Ð¾ Ð² Ð±ÑÑÐ¸ÐºÐµ (Ð²ÑÐ¿Ð»Ð°ÑÑ)
entity.enum.OrderStatus.BOUTIQUE_ORDER_ONLINE_CONFIRM.title=Online Ð·Ð°ÐºÐ°Ð·Ñ: Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ðµ
entity.enum.OrderStatus.BOUTIQUE_ORDER_ONLINE_PICKUP.title=Online Ð·Ð°ÐºÐ°Ð·Ñ: Ð¾ÑÐ³ÑÑÐ·ÐºÐ°
entity.enum.SizeType.RU.description=Ð Ð¾ÑÑÐ¸Ð¹ÑÐºÐ¸Ð¹
entity.enum.SizeType.AGE.description=ÐÐ¾Ð·ÑÐ°ÑÑ
entity.enum.SizeType.RING_RUSSIAN.description=Ð Ð¾ÑÑÐ¸Ð¹ÑÐºÐ¸Ð¹ ÑÐ°Ð·Ð¼ÐµÑ ÐºÐ¾Ð»ÐµÑ (Ð´Ð¸Ð°Ð¼ÐµÑÑ Ð² Ð¼Ð¸Ð»Ð»Ð¸Ð¼ÐµÑÑÐ°Ñ)
entity.enum.SizeType.HEIGHT.description=Ð Ð¾ÑÑ
entity.enum.SizeType.RING_EUROPEAN.description=ÐÐ²ÑÐ¾Ð¿ÐµÐ¹ÑÐºÐ¸Ð¹ ÑÐ°Ð·Ð¼ÐµÑ ÐºÐ¾Ð»ÐµÑ (Ð¾ÐºÑÑÐ¶Ð½Ð¾ÑÑÑ Ð² Ð¼Ð¸Ð»Ð»Ð¸Ð¼ÐµÑÑÐ°Ñ)
entity.enum.SizeType.JEANS.description=Ð Ð°Ð·Ð¼ÐµÑ Ð´Ð¶Ð¸Ð½Ñ
entity.enum.SizeType.INCHES.description=ÐÑÐ¹Ð¼Ñ
entity.enum.SizeType.COLLAR_CENTIMETERS.description=ÐÐ¾ÑÐ¾Ñ Ð² ÑÐ°Ð½ÑÐ¸Ð¼ÐµÑÑÐ°Ñ
entity.enum.SizeType.COLLAR_INCHES.description=ÐÐ¾ÑÐ¾Ñ Ð² Ð´ÑÐ¹Ð¼Ð°Ñ
entity.enum.SizeType.CENTIMETERS.description=Ð¡Ð°Ð½ÑÐ¸Ð¼ÐµÑÑÑ
entity.enum.SizeType.CENTIMETERS.abbreviation=CÐ¼
entity.enum.SizeType.HEIGHT.abbreviation=Ð 
entity.enum.SizeType.AGE.abbreviation=Ð
entity.enum.SizeType.ONE_SIZE.abbreviation=One size
entity.enum.SizeType.ONE_SIZE.description=One size
entity.enum.SizeType.BUST.abbreviation=Ð§Ð°ÑÐºÐ°
entity.enum.SizeType.BUST.description=Ð§Ð°ÑÐºÐ° Ð±ÑÑÑÐ³Ð°Ð»ÑÑÐµÑÐ°
entity.enum.SizeType.JEANS.abbreviation=ÐÐ¶Ð¸Ð½ÑÑ
entity.enum.SizeType.RING_EUROPEAN.abbreviation=EU (Ð´Ð»Ð¸Ð½Ð° Ð¾ÐºÑÑÐ¶Ð½Ð¾ÑÑÐ¸ Ð² Ð¼Ð¼)
entity.enum.SizeType.COLLAR_CENTIMETERS.abbreviation=ÐÐ¾ÑÐ¾Ñ, ÑÐ¼
entity.enum.SizeType.RING_RUSSIAN.abbreviation=RU
entity.enum.SizeType.COLLAR_INCHES.abbreviation=ÐÐ¾ÑÐ¾Ñ, Ð´ÑÐ¹Ð¼Ñ
entity.enum.SizeType.INCHES.abbreviation="
entity.enum.SizeType.RU.abbreviation=RU
entity.enum.SizeType.EU.abbreviation=EU
entity.enum.SizeType.US.abbreviation=US
entity.enum.SizeType.INT.abbreviation=INT
entity.enum.SizeType.UK.abbreviation=UK
entity.enum.SizeType.FR.abbreviation=FR
entity.enum.SizeType.IT.abbreviation=IT
entity.enum.SizeType.DE.abbreviation=DE
entity.enum.SizeType.AU.abbreviation=AU
entity.enum.SizeType.JPN.abbreviation=JPN
entity.enum.SizeType.EU.description=ÐÐ²ÑÐ¾Ð¿ÐµÐ¹ÑÐºÐ¸Ð¹
entity.enum.SizeType.US.description=ÐÐ¼ÐµÑÐ¸ÐºÐ°Ð½ÑÐºÐ¸Ð¹
entity.enum.SizeType.INT.description=ÐÐµÐ¶Ð´ÑÐ½Ð°ÑÐ¾Ð´Ð½ÑÐ¹
entity.enum.SizeType.UK.description=ÐÐ½Ð³Ð»Ð¸Ð¹ÑÐºÐ¸Ð¹
entity.enum.SizeType.FR.description=Ð¤ÑÐ°Ð½ÑÑÐ·ÑÐºÐ¸Ð¹
entity.enum.SizeType.IT.description=ÐÑÐ°Ð»ÑÑÐ½ÑÐºÐ¸Ð¹
entity.enum.SizeType.DE.description=ÐÐµÐ¼ÐµÑÐºÐ¸Ð¹
entity.enum.SizeType.AU.description=ÐÐ²ÑÑÑÐ°Ð»Ð¸Ð¹ÑÐºÐ¸Ð¹
entity.enum.SizeType.JPN.description=Ð¯Ð¿Ð¾Ð½ÑÐºÐ¸Ð¹
entity.enum.Sex.MALE.description=ÐÑÐ¶ÑÐºÐ¾Ð¹
entity.enum.Sex.FEMALE.description=ÐÐµÐ½ÑÐºÐ¸Ð¹
entity.enum.Sex.BOY.description=ÐÐ°Ð»ÑÑÐ¸Ðº
entity.enum.Sex.GIRL.description=ÐÐµÐ²Ð¾ÑÐºÐ°
entity.enum.Sex.ADULT.description=ÐÐ·ÑÐ¾ÑÐ»ÑÐ¹ (unisex)
entity.enum.Sex.CHILD.description=Ð ÐµÐ±ÐµÐ½Ð¾Ðº (unisex)
entity.enum.Role.SIMPLE_USER.role=Ð§Ð°ÑÑÐ½ÑÐ¹ Ð¿ÑÐ¾Ð´Ð°Ð²ÐµÑ
entity.enum.Role.BOUTIQUE.role=ÐÑÑÐ¸Ðº
entity.order.promocode=ÐÑÐ¾Ð¼Ð¾-ÐºÐ¾Ð´
entity.order.certificate=ÐÐ¾Ð´Ð°ÑÐ¾ÑÐ½ÑÐ¹ ÑÐµÑÑÐ¸ÑÐ¸ÐºÐ°Ñ

entity.enum.IdFilterParam.PROBLEMS_WITH_BANK.description=ÐÑÐ¾Ð±Ð»ÐµÐ¼Ñ Ñ Ð±Ð°Ð½ÐºÐ¾Ð¼
entity.enum.IdFilterParam.PROBLEMS_WITH_ADDRESS.description=ÐÑÐ¾Ð±Ð»ÐµÐ¼Ñ Ñ Ð°Ð´ÑÐµÑÐ¾Ð¼

entity.enum.BaseType.WOMAN.baseName=ÐÐµÐ½ÑÐºÐ¾Ðµ
entity.enum.BaseType.MAN.baseName=ÐÑÐ¶ÑÐºÐ¾Ðµ
entity.enum.BaseType.KIDS.baseName=ÐÐµÑÑÐºÐ¾Ðµ

entity.enum.BannerType.BANNER_1=ÐÐ°Ð½ÐµÑ 1
entity.enum.BannerType.BANNER_2=ÐÐ°Ð½ÐµÑ 2
entity.enum.BannerType.BANNER_3=ÐÐ°Ð½ÐµÑ 3
entity.enum.BannerType.BANNER_4=ÐÐ°Ð½ÐµÑ 4
entity.enum.BannerType.BANNER_ACTUAL_1=ÐÐºÑÑÐ°Ð»ÑÐ½Ð¾ ÐÐ°Ð½ÐµÑ 1
entity.enum.BannerType.BANNER_ACTUAL_2=ÐÐºÑÑÐ°Ð»ÑÐ½Ð¾ ÐÐ°Ð½ÐµÑ 2
entity.enum.BannerType.BANNER_ACTUAL_3=ÐÐºÑÑÐ°Ð»ÑÐ½Ð¾ ÐÐ°Ð½ÐµÑ 3

entity.enum.BannerDirectType.BANNER_MONTH=ÐÑÐµÐ½Ð´ Ð¼ÐµÑÑÑÐ°
entity.enum.BannerDirectType.BANNER_BRAND=ÐÑÐµÐ½Ð´Ñ
entity.enum.BannerDirectType.BANNER_CAT1=ÐÐ´ÐµÐ¶Ð´Ð°
entity.enum.BannerDirectType.BANNER_CAT2=ÐÐ±ÑÐ²Ñ
entity.enum.BannerDirectType.BANNER_CAT3=Ð¡ÑÐ¼ÐºÐ¸
entity.enum.BannerDirectType.BANNER_CAT4=ÐÐºÑÐµÑÑÑÐ°ÑÑ

entity.enum.IdFilterParam.WITHOUT_RRP=ÐÐµÐ· ÑÐµÐ½Ñ RRP
entity.enum.IdFilterParam.WITH_RRP=Ð¡ ÑÐµÐ½Ð¾Ð¹ RRP

entity.enum.DateFilterParam.ONE_MONTH.description=ÐÐ¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð¾ 1 Ð¼ÐµÑÑÑ Ð½Ð°Ð·Ð°Ð´
entity.enum.DateFilterParam.TWO_MONTH.description=ÐÐ¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð¾ 2 Ð¼ÐµÑÑÑÐ° Ð½Ð°Ð·Ð°Ð´
entity.enum.DateFilterParam.THREE_MONTH.description=ÐÐ¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð¾ 3 Ð¼ÐµÑÑÑÐ° Ð½Ð°Ð·Ð°Ð´
entity.enum.DateFilterParam.SIX_MONTH.description=ÐÐ¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð¾ 6 Ð¼ÐµÑÑÑÐµÐ² Ð½Ð°Ð·Ð°Ð´
entity.enum.DateFilterParam.NINE_MONTH.description=ÐÐ¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð¾ 9 Ð¼ÐµÑÑÑÐµÐ² Ð½Ð°Ð·Ð°Ð´
entity.enum.DateFilterParam.TWELVE_MONTH.description=ÐÐ¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð¾ 12 Ð¼ÐµÑÑÑÐµÐ² Ð½Ð°Ð·Ð°Ð´
entity.enum.DateFilterParam.EIGHTEEN_MONTH.description=ÐÐ¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð¾ 18 Ð¼ÐµÑÑÑÐµÐ² Ð½Ð°Ð·Ð°Ð´
entity.enum.DateFilterParam.TWENTY_FOUR_MONTH.description=ÐÐ¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð¾ 24 Ð¼ÐµÑÑÑÐ° Ð½Ð°Ð·Ð°Ð´

entity.enum.DocumentType.CERTIFICATE.descriptionPattern=Ð¡ÐµÑÑÐ¸ÑÐ¸ÐºÐ°Ñ Ð½Ð° ÑÐ¾Ð²Ð°Ñ %s
entity.enum.DocumentType.OSKELLY_WAYBILL_FROM_SELLER.descriptionPattern=ÐÐ°ÐºÐ»Ð°Ð´Ð½Ð°Ñ OSKELLY - Ð¾Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°
entity.enum.DocumentType.OSKELLY_WAYBILL_TO_BUYER.descriptionPattern=ÐÐ°ÐºÐ»Ð°Ð´Ð½Ð°Ñ OSKELLY - Ðº Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
entity.enum.DocumentType.TK_WAYBILL_FROM_SELLER.descriptionPattern=ÐÑ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ° Ð² OSKELLY - Ð½Ð°ÐºÐ»Ð°Ð´Ð½Ð°Ñ
entity.enum.DocumentType.TK_WAYBILL_TO_BUYER.descriptionPattern=ÐÑ OSKELLY Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ - Ð½Ð°ÐºÐ»Ð°Ð´Ð½Ð°Ñ
entity.enum.DocumentType.TK_STICKERS_TO_BUYER.descriptionPattern=ÐÑ OSKELLY Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ - ÑÑÐ¸ÐºÐµÑÑ
entity.enum.DocumentType.AGENT_REPORT.descriptionPattern=ÐÑÑÐµÑ Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð¶Ðµ
entity.enum.DocumentType.DOC_INVOICE_TAX_DELIVERY_EN.descriptionPattern=Ð¡ÑÐµÑ-ÑÐ°ÐºÑÑÑÐ° Ð½Ð° Ð´Ð¾ÑÑÐ°Ð²ÐºÑ (en)
entity.enum.DocumentType.DOC_INVOICE_TAX_GOODS_EN.descriptionPattern=Ð¡ÑÐµÑ-ÑÐ°ÐºÑÑÑÐ° Ð½Ð° ÑÐ¾Ð²Ð°ÑÑ (en)
entity.enum.DocumentType.DOC_AGENT_REPORT_EN.descriptionPattern=ÐÑÑÐµÑ Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð¶Ðµ (en)
entity.enum.DocumentType.DOC_AGENT_REPORT_RU.descriptionPattern=ÐÑÑÐµÑ Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð¶Ðµ (ru)
entity.enum.DocumentType.DOC_CROSS_BORDER_AGENT_REPORT.descriptionPattern=ÐÑÑÐµÑ Ð°Ð³ÐµÐ½ÑÐ° Ð¾Ð± Ð¸ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð¸Ð¸ Ð·Ð°ÐºÐ°Ð·Ð°
entity.enum.DocumentType.DOC_ORDER_LABELS.descriptionPattern=Ð­ÑÐ¸ÐºÐµÑÐºÐ¸ ÑÐ¾Ð²Ð°ÑÐ¾Ð²
entity.enum.DocumentType.DOC_ORDER_ARAMEX_S2O_INVOICE.descriptionPattern=ÐÐ°ÐºÐ»Ð°Ð´Ð½Ð°Ñ Aramex (S2O)
entity.enum.DocumentType.DOC_ORDER_ARAMEX_O2B_INVOICE.descriptionPattern=ÐÐ°ÐºÐ»Ð°Ð´Ð½Ð°Ñ Aramex (B2O)
entity.enum.DocumentType.DOC_ORDER_INVOICE.descriptionPattern=ÐÐ½Ð²Ð¾Ð¹Ñ Ð¿Ð¾ Ð·Ð°ÐºÐ°Ð·Ñ
entity.enum.DocumentType.DOC_BOUTIQUE_CONTRACT.descriptionPattern=ÐÐ¾Ð³Ð¾Ð²Ð¾Ñ Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð¼


entity.enum.OrderStepDTO.Type.DISABLED=ÐÐµÐ°ÐºÑÐ¸Ð²Ð½ÑÐ¹
entity.enum.OrderStepDTO.Type.WAITING=ÐÐ¶Ð¸Ð´Ð°Ð½Ð¸Ðµ
entity.enum.OrderStepDTO.Type.COMPLETE=ÐÐ°Ð²ÐµÑÑÐµÐ½Ð¾
entity.enum.OrderStepDTO.Type.FAILED=ÐÑÑÑ Ð¿ÑÐ¾Ð±Ð»ÐµÐ¼Ñ
entity.enum.OrderStepDTO.Type.CONFIRMATION=ÐÐ¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ðµ Ð·Ð°ÐºÐ°Ð·Ð°
entity.enum.OrderStepDTO.Type.EXPERTISE=Ð­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ð°
entity.enum.OrderStepDTO.Type.DELIVERING=ÐÑÑÑÐµÑ Ð²ÑÐµÑÐ°Ð» Ðº Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ

entity.enum.ReturnStepDTO.DISABLED=ÐÐµÐ°ÐºÑÐ¸Ð²Ð½ÑÐ¹
entity.enum.ReturnStepDTO.WAITING=ÐÐ¶Ð¸Ð´Ð°Ð½Ð¸Ðµ
entity.enum.ReturnStepDTO.COMPLETE=ÐÐ°Ð²ÐµÑÑÐµÐ½Ð¾
entity.enum.ReturnStepDTO.FAILED=ÐÑÑÑ Ð¿ÑÐ¾Ð±Ð»ÐµÐ¼Ñ

entity.enum.CounterpartyDTO.CounterpartyType.PHYS=Ð¤Ð¸Ð·Ð¸ÑÐµÑÐºÐ¾Ðµ Ð»Ð¸ÑÐ¾
entity.enum.CounterpartyDTO.CounterpartyType.IP=ÐÐ½Ð´Ð¸Ð²Ð¸Ð´ÑÐ°Ð»ÑÐ½ÑÐ¹ Ð¿ÑÐµÐ´Ð¿ÑÐ¸Ð½Ð¸Ð¼Ð°ÑÐµÐ»Ñ
entity.enum.CounterpartyDTO.CounterpartyType.JUR=Ð®ÑÐ¸Ð´Ð¸ÑÐµÑÐºÐ¾Ðµ Ð»Ð¸ÑÐ¾
entity.enum.CounterpartyDTO.CounterpartyType.CARD=ÐÐ°Ð½ÐºÐ¾Ð²ÑÐºÐ°Ñ ÐºÐ°ÑÑÐ°
entity.enum.CounterpartyDTO.CounterpartyType.BONUS_12_STOREEZ=ÐÐ¾Ð½ÑÑÑ 12Storeez
entity.enum.CounterpartyDTO.CounterpartyType.INTERNATIONAL=Ð¡ÑÐµÑ Ð´Ð»Ñ Ð¼ÐµÐ¶Ð´ÑÐ½Ð°ÑÐ¾Ð´Ð½ÑÑ Ð¿ÐµÑÐµÐ²Ð¾Ð´Ð¾Ð² - Ð¤Ð¸Ð·Ð¸ÑÐµÑÐºÐ¾Ðµ Ð»Ð¸ÑÐ¾
entity.enum.CounterpartyDTO.CounterpartyType.INTERNATIONAL_LEGAL_ENTITY=Ð¡ÑÐµÑ Ð´Ð»Ñ Ð¼ÐµÐ¶Ð´ÑÐ½Ð°ÑÐ¾Ð´Ð½ÑÑ Ð¿ÐµÑÐµÐ²Ð¾Ð´Ð¾Ð² - Ð®ÑÐ¸Ð´Ð¸ÑÐµÑÐºÐ¾Ðµ Ð»Ð¸ÑÐ¾

entity.enum.SaleRejectionReasonType.CHANGE_DECISION.appDisplayName=ÐÐµÑÐµÐ´ÑÐ¼Ð°Ð» Ð¿ÑÐ¾Ð´Ð°Ð²Ð°ÑÑ
entity.enum.SaleRejectionReasonType.SOLD_ON_OTHER_PLATFORM.appDisplayName=ÐÑÐ¾Ð´Ð°Ð» Ð½Ð° Ð´ÑÑÐ³Ð¾Ð¹ Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ðµ
entity.enum.SaleRejectionReasonType.GOING_TO_CHANGE_PRICE.appDisplayName=Ð¥Ð¾ÑÑ Ð¸Ð·Ð¼ÐµÐ½Ð¸ÑÑ ÑÐµÐ½Ñ
entity.enum.SaleRejectionReasonType.PRODUCT_CONDITION_CHANGED.appDisplayName=ÐÐ·Ð¼ÐµÐ½Ð¸Ð»Ð¾ÑÑ ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ðµ ÑÐ¾Ð²Ð°ÑÐ°
entity.enum.SaleRejectionReasonType.OTHER.appDisplayName=ÐÑÑÐ³Ð¾Ðµ

entity.enum.SaleRejectionReasonType.CHANGE_DECISION.adminDisplayName=ÐÑÐ¾Ð´Ð°Ð²ÐµÑ Ð¿ÐµÑÐµÐ´ÑÐ¼Ð°Ð» Ð¿ÑÐ¾Ð´Ð°Ð²Ð°ÑÑ
entity.enum.SaleRejectionReasonType.SOLD_ON_OTHER_PLATFORM.adminDisplayName=ÐÑÐ¾Ð´Ð°Ð» Ð½Ð° Ð´ÑÑÐ³Ð¾Ð¼ ÑÐµÑÑÑÑÐµ
entity.enum.SaleRejectionReasonType.GOING_TO_CHANGE_PRICE.adminDisplayName=ÐÑÐ¾Ð´Ð°Ð²ÐµÑ ÑÐ¾ÑÐµÑ Ð¸Ð·Ð¼ÐµÐ½Ð¸ÑÑ ÑÐµÐ½Ñ
entity.enum.SaleRejectionReasonType.PRODUCT_CONDITION_CHANGED.adminDisplayName=Ð¢Ð¾Ð²Ð°Ñ Ð½Ðµ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÐµÑ ÑÐºÐ°Ð·Ð°Ð½Ð½Ð¾Ð¼Ñ Ð² Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ð¸
entity.enum.SaleRejectionReasonType.OTHER.adminDisplayName=ÐÑÑÐ³Ð¾Ðµ

entity.enum.SaleRejectionReasonType.CAN_NOT_SEND.adminDisplayName=ÐÐµ Ð¼Ð¾Ð³Ñ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸ÑÑ Ð² Ð´Ð°Ð½Ð½ÑÐ¹ Ð¼Ð¾Ð¼ÐµÐ½Ñ
entity.enum.SaleRejectionReasonType.DONT_ANSWER.adminDisplayName=ÐÑÐ¾Ð´Ð°Ð²ÐµÑ Ð½Ðµ Ð¾ÑÐ²ÐµÑÐ°ÐµÑ
entity.enum.ContentBlockType.BANNER=ÐÐ°Ð½ÐµÑÑ
entity.enum.ContentBlockType.COLLECTION=ÐÑÐ½Ð¾Ð²Ð½Ð°Ñ Ð¿Ð¾Ð´Ð±Ð¾ÑÐºÐ°
entity.enum.ContentBlockType.ADDITIONAL_COLLECTION_1=ÐÐ¾Ð¿ Ð¿Ð¾Ð´Ð±Ð¾ÑÐºÐ¸ 1 (ÑÑÐ°ÑÐ¾Ðµ)
entity.enum.ContentBlockType.ADDITIONAL_COLLECTION_2=ÐÐ¾Ð´Ð±Ð¾ÑÐºÐ° Ð³Ð¾ÑÐ¸Ð·Ð¾Ð½ÑÐ°Ð»ÑÐ½Ð°Ñ
entity.enum.ContentBlockType.BLOG=ÐÐ»Ð¾Ð³ OSKELLY
entity.enum.ContentBlockType.TEXT_SLIDES=Ð¢ÐµÐºÑÑÐ¾Ð²ÑÐµ ÑÐ»Ð°Ð¹Ð´Ñ
entity.enum.ContentBlockType.BEST_SELLERS=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ð¸
entity.enum.ContentBlockType.SELECTED_PRODUCT=Ð¢Ð¾Ð²Ð°Ñ
entity.enum.ContentBlockType.STORIES=Ð¡ÑÐ¾ÑÐ¸Ñ
entity.enum.ContentBlockType.STOREEZ=ÐÐµÐ½ÑÐ° Storeez
entity.enum.ContentBlockType.INSTAGRAM_FEED=ÐÐµÐ½ÑÐ° Instagram
entity.enum.ContentBlockType.SHELF=ÐÐ¾Ð»ÐºÐ°
entity.enum.ContentBlockType.FILTERABLE_SHELF=ÐÐ¾Ð»ÐºÐ° c Ð±ÑÐµÐ½Ð´Ð°Ð¼Ð¸
entity.enum.ContentBlockType.VERTICAL_COLLECTION=ÐÐ¾Ð´Ð±Ð¾ÑÐºÐ¸ Ð²ÐµÑÑÐ¸ÐºÐ°Ð»ÑÐ½ÑÐµ
entity.enum.ContentBlockType.HORIZONTAL_COLLECTION=ÐÐ¾Ð´Ð±Ð¾ÑÐºÐ¸ Ð³Ð¾ÑÐ¸Ð·Ð¾Ð½ÑÐ°Ð»ÑÐ½ÑÐµ
entity.enum.ContentBlockType.BUTTON_CONTROL=Ð£Ð¿ÑÐ°Ð²Ð»ÐµÐ½Ð¸Ðµ ÐºÐ½Ð¾Ð¿ÐºÐ°Ð¼Ð¸
entity.enum.ContentBlockType.TEXT_CONTROL=Ð¢ÐµÐºÑÑÑ
entity.enum.ContentBlockType.OSOCIAL_POSTS_COLLECTION=ÐÐ¾Ð»ÐºÐ° Ñ Ð¿Ð¾ÑÑÐ°Ð¼Ð¸
entity.enum.ContentBlockType.SPACER=Ð Ð°Ð·Ð´ÐµÐ»Ð¸ÑÐµÐ»Ñ
entity.enum.ContentBlockType.ORDERS=ÐÐºÑÐ¸Ð²Ð½ÑÐµ Ð·Ð°ÐºÐ°Ð·Ñ
entity.enum.ContentBlockType.PROMO_AUTO_COLLECTION=ÐÐ¾Ð¿Ð¾Ð»Ð½Ð¸ÑÐµÐ»ÑÐ½ÑÐ¹ ÑÐ»Ð°Ð¹Ð´ÐµÑ ÑÐµÐºÐ»Ð°Ð¼Ð½ÑÑ Ð±Ð°Ð½Ð½ÐµÑÐ¾Ð²

#Ð¢Ð¸Ð¿ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°
entity.enum.SellerTypeDTO.ALL=ÐÑÐµ
entity.enum.SellerTypeDTO.PRIVATE_SELLER=Ð§Ð
entity.enum.SellerTypeDTO.BOUTIQUE=ÐÑÑÐ¸ÐºÐ¸

#Ð¡Ð¾ÑÑÐ¾ÑÐ½Ð¸Ðµ Ð¿ÑÐ¾Ð´ÑÐºÑÐ°
entity.enum.ProductState.DRAFT=Ð§ÐµÑÐ½Ð¾Ð²Ð¸Ðº
entity.enum.ProductState.SECOND_EDITION=ÐÐ¾Ð²ÑÐ¾ÑÐ½Ð¾Ðµ ÑÐµÐ´Ð°ÐºÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ
entity.enum.ProductState.NEED_MODERATION=ÐÐ° Ð¼Ð¾Ð´ÐµÑÐ°ÑÐ¸Ð¸
entity.enum.ProductState.NEED_RETOUCH=Ð¢ÑÐµÐ±ÑÐµÑÑÑ ÑÐµÑÑÑÑ
entity.enum.ProductState.RETOUCH_DONE=Ð ÐµÑÑÑÑ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð°
entity.enum.ProductState.REJECTED=ÐÑÐºÐ»Ð¾Ð½ÐµÐ½
entity.enum.ProductState.PUBLISHED=ÐÐ¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½
entity.enum.ProductState.HIDDEN=Ð¡Ð½ÑÑ Ñ Ð¿ÑÐ¾Ð´Ð°Ð¶Ð¸ (Ð¡ÐºÑÑÑ)
entity.enum.ProductState.SOLD=ÐÑÐ¾Ð´Ð°Ð½
entity.enum.ProductState.DELETED=Ð£Ð´Ð°Ð»ÐµÐ½
entity.enum.ProductState.BANED=ÐÐ°Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½

#ÐÐ°Ð½Ð°Ð» Ð¿ÑÐ¾Ð´Ð°Ð¶
entity.enum.SalesChannel.WEBSITE=Ð¡Ð°Ð¹Ñ
entity.enum.SalesChannel.BOUTIQUE_AND_WEBSITE=ÐÑÑÐ¸Ðº Ð¸ ÑÐ°Ð¹Ñ
entity.enum.SalesChannel.STOCK_AND_BOUTIQUE_AND_WEBSITE=Ð¡ÐºÐ»Ð°Ð´, Ð±ÑÑÐ¸Ðº Ð¸ ÑÐ°Ð¹Ñ
entity.enum.SalesChannel.BOUTIQUE=ÐÑÑÐ¸Ðº

entity.enum.OrderPaymentFailCode.PAYMENT_REQUEST_EXPIRED=ÐÑÐµÐ¼Ñ Ð¾Ð¶Ð¸Ð´Ð°Ð½Ð¸Ñ Ð¾Ð¿Ð»Ð°ÑÑ Ð¸ÑÑÐµÐºÐ»Ð¾ Ð»Ð¸Ð±Ð¾ Ð¿Ð»Ð°ÑÐµÐ¶ Ð¾ÑÐ¼ÐµÐ½ÐµÐ½
entity.enum.OrderPaymentFailCode.PAYMENT_REQUEST_REJECTS=ÐÐ°Ð¿ÑÐ¾Ñ Ð½Ð° Ð¾Ð¿Ð»Ð°ÑÑ Ð¾ÑÐºÐ»Ð¾Ð½ÐµÐ½

#ÐÐºÑÐ¸Ð²Ð½Ð¾ÑÑÑ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ
enum.UserActivity.ORDERS=ÐÐ°ÐºÐ°Ð·Ñ
enum.UserActivity.PRODUCTS=Ð¢Ð¾Ð²Ð°ÑÑ
enum.UserActivity.OSKELLY_COMMISSION=ÐÐ¾Ð¼Ð¸ÑÑÐ¸Ñ OSKELLY
enum.UserActivity.SELLS=ÐÑÐ¾Ð´Ð°Ð¶Ð¸
enum.UserActivity.SELLS_DECLINED=ÐÑÐºÐ»Ð¾Ð½ÐµÐ½Ð¾

exception.AdministrationException.current-price-must-not-be-null=Ð¦ÐµÐ½Ð° Ð½Ð° ÑÐ¾Ð²Ð°Ñ Ð´Ð¾Ð»Ð¶Ð½Ð° Ð±ÑÑÑ ÑÐºÐ°Ð·Ð°Ð½Ð°
exception.AdministrationException.seller-price-must-not-be-null=ÐÐµ Ð·Ð°Ð´Ð°Ð½Ð° ÑÐµÐ½Ð° Ð½Ð° ÑÑÐºÐ¸ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÑ
exception.AdministrationException.current-price-must-be-grater-than-seller=Ð¦ÐµÐ½Ð° Ð½Ð° ÑÐ°Ð¹ÑÐµ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¼ÐµÐ½ÑÑÐµ, ÑÐµÐ¼ ÑÐµÐ½Ð° Ð¿ÑÐ¾Ð´Ð°Ð²ÑÑ

exception.MethodiusException.document-generate-fail=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð¿ÐµÑÐ°ÑÐ¸ Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ°

controller.pdf.OskWaybill.Waybill=ÐÐ°ÐºÐ»Ð°Ð´Ð½Ð°Ñ â
controller.pdf.OskWaybill.Sender=ÐÑÐ¿ÑÐ°Ð²Ð¸ÑÐµÐ»Ñ
controller.pdf.OskWaybill.Receiver=ÐÐ¾Ð»ÑÑÐ°ÑÐµÐ»Ñ

controller.pdf.WaybillPeopleInfoBlockTable.Title=ÐÐ°Ð¸Ð¼ÐµÐ½Ð¾Ð²Ð°Ð½Ð¸Ðµ:
controller.pdf.WaybillPeopleInfoBlockTable.FullName=Ð¤ÐÐ:
controller.pdf.WaybillPeopleInfoBlockTable.City=ÐÐ¾ÑÐ¾Ð´:
controller.pdf.WaybillPeopleInfoBlockTable.Address=ÐÐ´ÑÐµÑ:

controller.pdf.WaybillProductsBlockTable.DepartureDescription=ÐÐ¿Ð¸ÑÐ°Ð½Ð¸Ðµ Ð¾ÑÐ¿ÑÐ°Ð²Ð»ÐµÐ½Ð¸Ñ
controller.pdf.WaybillProductsBlockTable.OrderId=ID ÐÐ°ÐºÐ°Ð·Ð°:
controller.pdf.WaybillProductsBlockTable.Item=Ð¢Ð¾Ð²Ð°Ñ

controller.pdf.WaybillConfirmBlockTable.Accepted=ÐÑÐ¸Ð½ÑÑÐ¾
controller.pdf.WaybillConfirmBlockTable.AcceptedAndSigned=ÐÐ¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ðµ Ð¸ Ð¿Ð¾Ð´Ð¿Ð¸ÑÑ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸ÑÐµÐ»Ñ
controller.pdf.WaybillConfirmBlockTable.AgreedWithDeliveryConditions=C Ð£ÑÐ»Ð¾Ð²Ð¸ÑÐ¼Ð¸ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸ Ð¾Ð·Ð½Ð°ÐºÐ¾Ð¼Ð»ÐµÐ½.
controller.pdf.WaybillConfirmBlockTable.Delegate=ÐÑÐµÐ´ÑÑÐ°Ð²Ð¸ÑÐµÐ»Ñ Ð¿Ð¾ Ð´Ð¾Ð²ÐµÑÐµÐ½Ð½Ð¾ÑÑÐ¸
controller.pdf.WaybillConfirmBlockTable.Date=ÐÐ°ÑÐ°:
controller.pdf.WaybillConfirmBlockTable.Sign=ÐÐ¾Ð´Ð¿Ð¸ÑÑ:

controller.pdf.AgentReport.PhysUserContractTextStart=, Ð¸Ð¼ÐµÐ½ÑÐµÐ¼Ð¾Ðµ(ÑÐ¹) Ð² Ð´Ð°Ð»ÑÐ½ÐµÐ¹ÑÐµÐ¼ Â«ÐÑÐ¸Ð½ÑÐ¸Ð¿Ð°Ð»Â», Ñ Ð¾Ð´Ð½Ð¾Ð¹ ÑÑÐ¾ÑÐ¾Ð½Ñ, Ð¸ ÐÐ±ÑÐµÑÑÐ²Ð¾ Ñ Ð¾Ð³ÑÐ°Ð½Ð¸ÑÐµÐ½Ð½Ð¾Ð¹ Ð¾ÑÐ²ÐµÑÑÑÐ²ÐµÐ½Ð½Ð¾ÑÑÑÑ Â«ÐÐ¡ÐÐÐÐ ÐÐ Ð£ÐÐÂ», Ð¸Ð¼ÐµÐ½ÑÐµÐ¼Ð¾Ðµ Ð² Ð´Ð°Ð»ÑÐ½ÐµÐ¹ÑÐµÐ¼ Â«ÐÐ³ÐµÐ½ÑÂ», Ð² Ð»Ð¸ÑÐµ ÐÐµÐ½ÐµÑÐ°Ð»ÑÐ½Ð¾Ð³Ð¾ Ð´Ð¸ÑÐµÐºÑÐ¾ÑÐ° ÐÑÐºÐ°Ð½Ð¾Ð²Ð° Ð.Ð., Ð´ÐµÐ¹ÑÑÐ²ÑÑÑÐµÐ³Ð¾ Ð½Ð° Ð¾ÑÐ½Ð¾Ð²Ð°Ð½Ð¸Ð¸ Ð£ÑÑÐ°Ð²Ð°, Ñ Ð´ÑÑÐ³Ð¾Ð¹ ÑÑÐ¾ÑÐ¾Ð½Ñ, ÑÐ¾Ð²Ð¼ÐµÑÑÐ½Ð¾ Ð¸Ð¼ÐµÐ½ÑÐµÐ¼ÑÐµ Â«Ð¡ÑÐ¾ÑÐ¾Ð½ÑÂ», ÑÐ¾ÑÑÐ°Ð²Ð¸Ð»Ð¸ ÐÑÑÐµÑ Ð¾ Ð½Ð¸Ð¶ÐµÑÐ»ÐµÐ´ÑÑÑÐµÐ¼:
controller.pdf.AgentReport.LegalEntityContractTextStart={0} (ÐÐÐ {1}), Ð¸Ð¼ÐµÐ½ÑÐµÐ¼Ð¾Ðµ(ÑÐ¹) Ð² Ð´Ð°Ð»ÑÐ½ÐµÐ¹ÑÐµÐ¼ Â«ÐÑÐ¸Ð½ÑÐ¸Ð¿Ð°Ð»Â», Ñ Ð¾Ð´Ð½Ð¾Ð¹ ÑÑÐ¾ÑÐ¾Ð½Ñ, Ð¸ ÐÐ±ÑÐµÑÑÐ²Ð¾ Ñ Ð¾Ð³ÑÐ°Ð½Ð¸ÑÐµÐ½Ð½Ð¾Ð¹ Ð¾ÑÐ²ÐµÑÑÑÐ²ÐµÐ½Ð½Ð¾ÑÑÑÑ Â«ÐÐ¡ÐÐÐÐ ÐÐ Ð£ÐÐÂ» (ÐÐÐ:77149663399) , Ð¸Ð¼ÐµÐ½ÑÐµÐ¼Ð¾Ðµ Ð² Ð´Ð°Ð»ÑÐ½ÐµÐ¹ÑÐµÐ¼ Â«ÐÐ³ÐµÐ½ÑÂ», Ð² Ð»Ð¸ÑÐµ ÐÐµÐ½ÐµÑÐ°Ð»ÑÐ½Ð¾Ð³Ð¾ Ð´Ð¸ÑÐµÐºÑÐ¾ÑÐ° ÐÑÐºÐ°Ð½Ð¾Ð²Ð° Ð.Ð., Ð´ÐµÐ¹ÑÑÐ²ÑÑÑÐµÐ³Ð¾ Ð½Ð° Ð¾ÑÐ½Ð¾Ð²Ð°Ð½Ð¸Ð¸ Ð£ÑÑÐ°Ð²Ð°, Ñ Ð´ÑÑÐ³Ð¾Ð¹ ÑÑÐ¾ÑÐ¾Ð½Ñ, ÑÐ¾Ð²Ð¼ÐµÑÑÐ½Ð¾ Ð¸Ð¼ÐµÐ½ÑÐµÐ¼ÑÐµ Â«Ð¡ÑÐ¾ÑÐ¾Ð½ÑÂ», ÑÐ¾ÑÑÐ°Ð²Ð¸Ð»Ð¸ ÐÑÑÐµÑ Ð¾ Ð½Ð¸Ð¶ÐµÑÐ»ÐµÐ´ÑÑÑÐµÐ¼:
controller.pdf.AgentReport.PhysUserContractTextEnd=1.  Ð ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ð¸ Ñ Ð°ÐºÑÐµÐ¿ÑÐ¾Ð¼ ÐÑÐ¸Ð½ÑÐ¸Ð¿Ð°Ð»Ð¾Ð¼ ÐÐ³ÐµÐ½ÑÑÐºÐ¾Ð³Ð¾ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ° (Ð¿ÑÐ±Ð»Ð¸ÑÐ½Ð¾Ð¹ Ð¾ÑÐµÑÑÑ) Ð¾Ñ Â«{0}Â» {1} {2} Ð³Ð¾Ð´Ð° ÐÐ³ÐµÐ½ÑÐ¾Ð¼ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ñ, Ð° ÐÑÐ¸Ð½ÑÐ¸Ð¿Ð°Ð»Ð¾Ð¼ Ð¿ÑÐ¸Ð½ÑÑÑ ÑÐ»ÐµÐ´ÑÑÑÐ¸Ðµ Ð¿Ð¾ÑÑÑÐµÐ½Ð¸Ñ:
controller.pdf.AgentReport.LegalEntityContractTextEnd="1.  Ð ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ð¸ Ñ Ð°ÐºÑÐµÐ¿ÑÐ¾Ð¼ ÐÑÐ¸Ð½ÑÐ¸Ð¿Ð°Ð»Ð¾Ð¼ ÐÐ³ÐµÐ½ÑÑÐºÐ¾Ð³Ð¾ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ° â  {0} Ð¾Ñ Â«{1}Â» {2} {3} Ð³Ð¾Ð´Ð° ÐÐ³ÐµÐ½ÑÐ¾Ð¼ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ñ, Ð° ÐÑÐ¸Ð½ÑÐ¸Ð¿Ð°Ð»Ð¾Ð¼ Ð¿ÑÐ¸Ð½ÑÑÑ ÑÐ»ÐµÐ´ÑÑÑÐ¸Ðµ Ð¿Ð¾ÑÑÑÐµÐ½Ð¸Ñ:"
controller.pdf.AgentReport.Date=ÐÐ°ÑÐ°
controller.pdf.AgentReport.ItemTitle=ÐÐ°Ð¸Ð¼ÐµÐ½Ð¾Ð²Ð°Ð½Ð¸Ðµ ÑÐ¾Ð²Ð°ÑÐ°
controller.pdf.AgentReport.ItemCost=Ð¡ÑÐ¾Ð¸Ð¼Ð¾ÑÑÑ ÑÐ¾Ð²Ð°ÑÐ°, (ÑÑÐ±.)
controller.pdf.AgentReport.FeePercent=Ð Ð°Ð·Ð¼ÐµÑ Ð²Ð¾Ð·Ð½Ð°Ð³ÑÐ°Ð¶Ð´ÐµÐ½Ð¸Ñ, (%)
controller.pdf.AgentReport.FeeAmount=Ð¡ÑÐ¼Ð¼Ð° Ð²Ð¾Ð·Ð½Ð°Ð³ÑÐ°Ð¶Ð´ÐµÐ½Ð¸Ñ, (ÑÑÐ±.)
controller.pdf.AgentReport.Total=ÐÑÐ¾Ð³Ð¾:
controller.pdf.AgentReport.FeeInfo=ÐÐ¾Ð·Ð½Ð°Ð³ÑÐ°Ð¶Ð´ÐµÐ½Ð¸Ðµ ÐÐ³ÐµÐ½ÑÐ° ÑÐ¾ÑÑÐ°Ð²Ð¸Ð»Ð¾ {0} ÑÑÐ±Ð»ÐµÐ¹ ({1}%), ÐÐÐ¡ Ð½Ðµ Ð¾Ð±Ð»Ð°Ð³Ð°ÐµÑÑÑ.
controller.pdf.AgentReport.Expense=2. Ð Ð°ÑÑÐ¾Ð´Ñ, Ð¿ÑÐ¾Ð¸Ð·Ð²ÐµÐ´ÐµÐ½Ð½ÑÐµ ÐÐ³ÐµÐ½ÑÐ¾Ð¼ Ð¸ Ð¿Ð¾Ð´Ð»ÐµÐ¶Ð°ÑÐ¸Ðµ Ð²Ð¾Ð·Ð¼ÐµÑÐµÐ½Ð¸Ñ (Ð²Ð¾Ð·Ð¼ÐµÑÐµÐ½Ð½ÑÐµ) ÐÑÐ¸Ð½ÑÐ¸Ð¿Ð°Ð»Ð¾Ð¼, ÑÐ¾ÑÑÐ°Ð²Ð¸Ð»Ð¸:
controller.pdf.AgentReport.ExpenseTitle=ÐÐ°Ð¸Ð¼ÐµÐ½Ð¾Ð²Ð°Ð½Ð¸Ðµ ÑÐ°ÑÑÐ¾Ð´Ð¾Ð²
controller.pdf.AgentReport.CostOfService=Ð¡ÑÐ¾Ð¸Ð¼Ð¾ÑÑÑ ÑÑÐ»ÑÐ³, (ÑÑÐ±.)
controller.pdf.AgentReport.CompanyProviderTitle=ÐÑÐ³Ð°Ð½Ð¸Ð·Ð°ÑÐ¸Ñ-Ð¿Ð¾ÑÑÐ°Ð²ÑÐ¸Ðº
controller.pdf.AgentReport.DryCleaning=Ð¥Ð¸Ð¼ÑÐ¸ÑÑÐºÐ° Ð¢Ð¾Ð²Ð°ÑÐ°
controller.pdf.AgentReport.ServiceTotalPrice="Ð¡ÑÐ¾Ð¸Ð¼Ð¾ÑÑÑ ÑÑÐ»ÑÐ³, (ÑÑÐ±.)"
controller.pdf.AgentReport.Dealed=.  ÐÑÑÐµÐ¿ÐµÑÐµÑÐ¸ÑÐ»ÐµÐ½Ð½ÑÐµ Ð¿Ð¾ÑÑÑÐµÐ½Ð¸Ñ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ñ Ð² Ð¿Ð¾Ð»Ð½Ð¾Ð¼ Ð¾Ð±ÑÐµÐ¼Ðµ, ÐÑÐ¸Ð½ÑÐ¸Ð¿Ð°Ð» Ð¿ÑÐµÑÐµÐ½Ð·Ð¸Ð¹ Ð¿Ð¾ Ð¾Ð±ÑÐµÐ¼Ñ, ÐºÐ°ÑÐµÑÑÐ²Ñ Ð¸ ÑÑÐ¾ÐºÐ°Ð¼ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð¸Ñ Ð¿Ð¾ÑÑÑÐµÐ½Ð¸Ð¹ Ð½Ðµ Ð¸Ð¼ÐµÐµÑ.
controller.pdf.AgentReport.TotalToBePaid=ÐÑÐ¾Ð³Ð¾ Ðº Ð²ÑÐ¿Ð»Ð°ÑÐµ:   {0} ÑÑÐ±Ð»ÐµÐ¹"

controller.AdminReportsController.Yes=ÐÐ°
controller.AdminReportsController.No=ÐÐµÑ
controller.AdminReportsController.PhysPerson=Ð¤Ð¸Ð·. Ð»Ð¸ÑÐ¾
controller.AdminReportsController.LegalEntity=ÐÑÑÐ¸Ðº

controller.AdminReportsController.Phone=Ð¢ÐµÐ»ÐµÑÐ¾Ð½
controller.AdminReportsController.AmountOfPaidOrders=Ð¡ÑÐ¼Ð¼Ð° Ð¾Ð¿Ð»Ð°ÑÐµÐ½Ð½ÑÑ Ð·Ð°ÐºÐ°Ð·Ð¾Ð²
controller.AdminReportsController.NumberOfPaidOrders=ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð¾Ð¿Ð»Ð°ÑÐµÐ½Ð½ÑÑ Ð·Ð°ÐºÐ°Ð·Ð¾Ð²
controller.AdminReportsController.Date=ÐÐ°ÑÐ°
controller.AdminReportsController.IdOrder=ID Ð·Ð°ÐºÐ°Ð·Ð°
controller.AdminReportsController.IdUser=ID Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ
controller.AdminReportsController.IsSeller=Ð¯Ð²Ð»ÑÐµÑÑÑ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð¼
controller.AdminReportsController.NewUser=ÐÐ¾Ð²ÑÐ¹ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ
controller.AdminReportsController.OrderPrice=Ð¡ÑÐ¾Ð¸Ð¼Ð¾ÑÑÑ Ð·Ð°ÐºÐ°Ð·Ð° (ÑÑÐ±)
controller.AdminReportsController.NumberOfItemsInOrder=ÐÐ¾Ð»-Ð²Ð¾ ÑÐ¾Ð²Ð°ÑÐ¾Ð² Ð² Ð·Ð°ÐºÐ°Ð·Ðµ
controller.AdminReportsController.Accepted=ÐÐ¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½

controller.AdminReportsController.IdItem=ID ÑÐ¾Ð²Ð°ÑÐ°
controller.AdminReportsController.IsBuyer=Ð¯Ð²Ð»ÑÐµÑÑÑ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»ÐµÐ¼
controller.AdminReportsController.SellerType=Ð¢Ð¸Ð¿ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°
controller.AdminReportsController.ItemPrice=Ð¡ÑÐ¾Ð¸Ð¼Ð¾ÑÑÑ ÑÐ¾Ð²Ð°ÑÐ° (ÑÑÐ±)
controller.AdminReportsController.ItemsAvailableForSale=ÐÐ¾Ð»-Ð²Ð¾ ÑÐ¾Ð²Ð°ÑÐ¾Ð² Ð´Ð¾ÑÑÑÐ¿Ð½ÑÑ Ðº Ð¿ÑÐ¾Ð´Ð°Ð¶Ðµ

controller.AdminReportsController.SincePublicationDay=ÐÐ¾Ð»-Ð²Ð¾ Ð´Ð½ÐµÐ¹ Ñ Ð¿ÑÐ±Ð»Ð¸ÐºÐ°ÑÐ¸Ð¸

controller.AccountControllerApiV2.AccessDenied=ÐÐ¾ÑÑÑÐ¿ Ð·Ð°Ð¿ÑÐµÑÐµÐ½

controller.AdminControllerAdvice.Error=ÐÑÐ¸Ð±ÐºÐ°

controller.AdminOrdersController.OrderWrongState=Order Ñ ID {0} Ð¿ÐµÑÐµÐ²Ð¾Ð´Ð¸ÑÑÑ Ð² Ð½ÐµÐ´Ð¾Ð¿ÑÑÑÐ¸Ð¼Ð¾Ðµ ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ðµ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸

controller.DiscountApi.UserNotFound=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½

exception.DefaultAdminCategoryService.ItemNotFound=Ð¢Ð¾Ð²Ð°Ñ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½: {0}

controller.AdminProductsController.ProductCategorySuccessfullySet=ÐÐ°ÑÐµÐ³Ð¾ÑÐ¸Ñ ÑÐ¾Ð²Ð°ÑÐ° ÑÑÐ¿ÐµÑÐ½Ð¾ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð°

controller.AdminPromoCodesController.Limitless=ÐÐµÑÑÑÐ¾ÑÐ½ÑÐ¹
controller.AdminPromoCodesController.From=ÐÑ
controller.AdminPromoCodesController.To=ÐÐ¾
controller.AdminPromoCodesController.PromocodeCreated=ÐÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´ ÑÐ¾Ð·Ð´Ð°Ð½
controller.AdminPromoCodesController.PromocodeUpdated=ÐÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´ Ð¾Ð±Ð½Ð¾Ð²Ð»ÐµÐ½
controller.AdminPromoCodesController.PromocodeDeleted=ÐÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´ ÑÐ´Ð°Ð»ÐµÐ½

controller.AccountAddressControllerApiV2.CountryNotFound=Ð¡ÑÑÐ°Ð½Ð° Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð°

controller.AccountControllerApiV2.Account=ÐÐºÐºÐ°ÑÐ½Ñ
controller.AccountControllerApiV2.UserWithAuthInfo=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ñ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸ÐµÐ¹ Ð¾Ð± Ð°ÑÑÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¸Ð¸
controller.AccountControllerApiV2.Authorization=ÐÐ²ÑÐ¾ÑÐ¸Ð·Ð°ÑÐ¸Ñ
controller.AccountControllerApiV2.RegistrationFormValidation=ÐÑÐ¾Ð²ÐµÑÐºÐ° ÑÐ¾ÑÐ¼Ñ ÑÐµÐ³Ð¸ÑÑÑÐ°ÑÐ¸Ð¸
controller.AccountControllerApiV2.InvalidRegistrationForm=Ð¤Ð¾ÑÐ¼Ð° ÑÐµÐ³Ð¸ÑÑÑÐ°ÑÐ¸Ð¸ Ð·Ð°Ð¿Ð¾Ð»Ð½ÐµÐ½Ð° Ñ Ð¾ÑÐ¸Ð±ÐºÐ°Ð¼Ð¸
controller.AccountControllerApiV2.AccountUpdated=ÐÐºÐºÐ°ÑÐ½Ñ Ð¾Ð±Ð½Ð¾Ð²Ð»ÐµÐ½
controller.AccountControllerApiV2.Counterparty=ÐÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½Ñ
controller.AccountControllerApiV2.BindCardReference=Ð¡ÑÑÐ»ÐºÐ° Ð½Ð° Ð¿ÑÐ¸Ð²ÑÐ·ÐºÑ ÐºÐ°ÑÑÑ
controller.AccountControllerApiV2.DeletedCounterparty=Ð£Ð´Ð°Ð»ÐµÐ½Ð½ÑÐ¹ ÐºÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½Ñ
controller.AccountControllerApiV2.AddressPoint=ÐÐ´ÑÐµÑÐ½Ð°Ñ ÑÐ¾ÑÐºÐ°
controller.AccountControllerApiV2.DeletedAddressPoint=Ð£Ð´Ð°Ð»ÐµÐ½Ð½Ð°Ñ Ð°Ð´ÑÐµÑÐ½Ð°Ñ ÑÐ¾ÑÐºÐ°
controller.AccountControllerApiV2.CheckNicknameFreeResult=Ð ÐµÐ·ÑÐ»ÑÑÐ°Ñ Ð¿ÑÐ¾Ð²ÐµÑÐºÐ¸ Ð´Ð¾ÑÑÑÐ¿Ð½Ð¾ÑÑÐ¸ Ð½Ð¸ÐºÐ½ÐµÐ¹Ð¼Ð°
controller.AccountControllerApiV2.CheckEmailFreeResult=Ð ÐµÐ·ÑÐ»ÑÑÐ°Ñ Ð¿ÑÐ¾Ð²ÐµÑÐºÐ¸ Ð´Ð¾ÑÑÑÐ¿Ð½Ð¾ÑÑÐ¸ e-mail
controller.AccountControllerApiV2.Sex=ÐÐ¾Ð» Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ
controller.AccountControllerApiV2.SetSex=ÐÐ¾Ð» Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½
controller.AccountControllerApiV2.PushNotificationGroups=ÐÑÑÐ¿Ð¿Ñ Ð¿ÑÑ-ÑÐ²ÐµÐ´Ð¾Ð¼Ð»ÐµÐ½Ð¸Ð¹
controller.AccountControllerApiV2.SetPushNotificationGroups=ÐÑÑÐ¿Ð¿Ñ Ð¿ÑÑ-ÑÐ²ÐµÐ´Ð¾Ð¼Ð»ÐµÐ½Ð¸Ð¹ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ñ
controller.AccountControllerApiV2.SubscribtionsIds=Ð¡Ð¿Ð¸ÑÐ¾Ðº ID Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÐµÐ¹ Ð½Ð° ÐºÐ¾ÑÐ¾ÑÑÑ Ñ Ð¿Ð¾Ð´Ð¿Ð¸ÑÐ°Ð½
controller.AccountControllerApiV2.UserRegistered=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð·Ð°ÑÐµÐ³Ð¸ÑÑÑÐ¸ÑÐ¾Ð²Ð°Ð½
controller.AccountControllerApiV2.ResetPassword=ÐÐ¸ÑÑÐ¼Ð¾ Ñ Ð´ÐµÐ¹ÑÑÐ²Ð¸ÑÐ¼Ð¸ Ð´Ð»Ñ ÑÐ±ÑÐ¾ÑÐ° Ð¿Ð°ÑÐ¾Ð»Ñ Ð¾ÑÐ¿ÑÐ°Ð²Ð»ÐµÐ½Ð¾
controller.AccountControllerApiV2.SetPassword=ÐÐ°ÑÐ¾Ð»Ñ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½
controller.AccountControllerApiV2.PasswordChanged=ÐÐ°ÑÐ¾Ð»Ñ Ð¸Ð·Ð¼ÐµÐ½ÐµÐ½
controller.AccountControllerApiV2.AvatarChanged=ÐÐ²Ð°ÑÐ°Ñ Ð¸Ð·Ð¼ÐµÐ½ÐµÐ½
controller.AccountControllerApiV2.FileUploaded=Ð¤Ð°Ð¹Ð» Ð·Ð°Ð³ÑÑÐ¶ÐµÐ½
controller.AccountControllerApiV2.FileInfo=ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ ÑÐ°Ð¹Ð»Ðµ
controller.AccountControllerApiV2.UserFilesPage=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ° Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÑÑÐºÐ¸Ñ ÑÐ°Ð¹Ð»Ð¾Ð²
controller.AccountControllerApiV2.Subscription=ÐÐ¾Ð´Ð¿Ð¸ÑÐºÐ°
controller.AccountControllerApiV2.Subscribed=ÐÐ¾Ð´Ð¿Ð¸ÑÐºÐ° Ð¾ÑÐ¾ÑÐ¼Ð»ÐµÐ½Ð°
controller.AccountControllerApiV2.PreFilledRegisterFormWithOptions=ÐÑÐµÐ´Ð·Ð°Ð¿Ð¾Ð»Ð½ÐµÐ½Ð½Ð°Ñ ÑÐ¾ÑÐ¼Ð° ÑÐµÐ³Ð¸ÑÑÑÐ°ÑÐ¸Ð¸ Ð¸ ÑÐ»Ð°Ð³Ð¸ ÑÐµÐ³Ð¸ÑÑÑÐ°ÑÐ¸Ð¸

controller.AccountCredsControllerApiV2.AccountInfo=Ð ÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð°ÐºÐºÐ°ÑÐ½ÑÐ°

controller.AddressControllerApiV2.Regions=Ð ÐµÐ³Ð¸Ð¾Ð½Ñ
controller.AddressControllerApiV2.Cities=ÐÐ¾ÑÐ¾Ð´Ð°
controller.AddressControllerApiV2.BigCities=ÐÐ¾ÑÐ¾Ð´Ð° Ð¼Ð¸Ð»Ð»Ð¸Ð¾Ð½Ð½Ð¸ÐºÐ¸
controller.AddressControllerApiV2.GetSearchHistory=ÐÑÑÐ¾ÑÐ¸Ñ Ð¿Ð¾Ð¸ÑÐºÐ° Ð¿Ð¾ Ð°Ð´ÑÐµÑÐ°Ð¼
controller.AddressControllerApiV2.AddSearchHistory=Ð ÐµÐ·ÑÐ»ÑÑÐ°Ñ Ð´Ð¾Ð±Ð°Ð²Ð»ÐµÐ½Ð¸Ñ Ð°Ð´ÑÐµÑÐ° Ð² Ð¸ÑÑÐ¾ÑÐ¸Ñ Ð¿Ð¾Ð¸ÑÐºÐ°

controller.CallbackControllerApiV2.AppleUserUpdateInfo=ÐÐ¾Ð»ÑÑÐµÐ½Ð° Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾Ð± Ð¾Ð±Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ apple Ð°ÐºÐºÐ°ÑÐ½ÑÐ° Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ
controller.CallbackControllerApiV2.CardBinderByCounterparty=ÐÐ°ÑÑÐ° Ð¿ÑÐ¸Ð²ÑÐ·Ð°Ð½Ð° ÑÐ¾Ð³Ð»Ð°ÑÐ½Ð¾ counterparty

controller.CartControllerApiV2.Cart=ÐÐ¾ÑÐ·Ð¸Ð½Ð°
controller.CartControllerApiV2.SimpleCart=ÐÐ¾ÑÐ·Ð¸Ð½Ð° Ð² Ð¿ÑÐ¾ÑÑÐ¾Ð¼ Ð²Ð¸Ð´Ðµ
controller.CartControllerApiV2.PositionAdded=ÐÐ¾Ð·Ð¸ÑÐ¸Ñ Ð´Ð¾Ð±Ð°Ð²Ð»ÐµÐ½Ð°
controller.CartControllerApiV2.PositionUpdated=ÐÐ¾Ð·Ð¸ÑÐ¸Ñ Ð¾Ð±Ð½Ð¾Ð²Ð»ÐµÐ½Ð°
controller.CartControllerApiV2.PositionDeletedAndMovedToFavourites=ÐÐ¾Ð·Ð¸ÑÐ¸Ñ ÑÐ´Ð°Ð»ÐµÐ½Ð°, ÑÐ¾Ð²Ð°Ñ Ð´Ð¾Ð±Ð°Ð²Ð»ÐµÐ½ Ð² Ð¸Ð·Ð±ÑÐ°Ð½Ð½Ð¾Ðµ
controller.CartControllerApiV2.DeliveryAddressEndpoint=Ð¢Ð¾ÑÐºÐ° Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸
controller.CartControllerApiV2.SetDeliveryAddressEndpoint=Ð¢Ð¾ÑÐºÐ° Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð°
controller.CartControllerApiV2.BuyerCounterparty=ÐÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½Ñ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
controller.CartControllerApiV2.SetBuyerCounterparty=ÐÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½Ñ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½
controller.CartControllerApiV2.DeliveryComment=ÐÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹ Ðº Ð´Ð¾ÑÑÐ°Ð²ÐºÐµ
controller.CartControllerApiV2.SetDeliveryComment=ÐÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹ Ðº Ð´Ð¾ÑÑÐ°Ð²ÐºÐµ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½
controller.CartControllerApiV2.HoldMoney=ÐÐ¿Ð»Ð°ÑÐ° Ð·Ð°ÐºÐ°Ð·Ð° Ð¸Ð½Ð¸ÑÐ¸Ð¸ÑÐ¾Ð²Ð°Ð½Ð°
controller.CartControllerApiV2.CheckPromocode=Ð ÐµÐ·ÑÐ»ÑÑÐ°Ñ Ð¿ÑÐ¸Ð¼ÐµÐ½ÐµÐ½Ð¸Ñ Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´Ð°

controller.CatalogControllerApiV2.Catalog=ÐÐ°ÑÐ°Ð»Ð¾Ð³
controller.CatalogControllerApiV2.MenuContent=ÐÐµÐ½Ñ ÐºÐ°ÑÐ°Ð»Ð¾Ð³Ð°
controller.CatalogControllerApiV2.ItemsIDs=ID ÑÐ¾Ð²Ð°ÑÐ¾Ð²
controller.CatalogControllerApiV2.AvailableFilters=ÐÐ¾ÑÑÑÐ¿Ð½ÑÐµ ÑÐ¸Ð»ÑÑÑÑ
controller.CatalogControllerApiV2.SizeTree=ÐÐµÑÐµÐ²Ð¾ ÑÐ°Ð·Ð¼ÐµÑÐ¾Ð²
controller.CatalogControllerApiV2.Sizes=Ð Ð°Ð·Ð¼ÐµÑÑ
controller.CatalogControllerApiV2.AttributesTree=ÐÐµÑÐµÐ²Ð¾ Ð°ÑÑÐ¸Ð±ÑÑÐ¾Ð²
controller.CatalogControllerApiV2.AvailableColors=ÐÐ¾ÑÑÑÐ¿Ð½ÑÐµ ÑÐ²ÐµÑÐ°
controller.CatalogControllerApiV2.AvailableMaterials=ÐÐ¾ÑÑÑÐ¿Ð½ÑÐµ Ð¼Ð°ÑÐµÑÐ¸Ð°Ð»Ñ
controller.CatalogControllerApiV2.CategoryTree=ÐÐµÑÐµÐ²Ð¾ ÐºÐ°ÑÐµÐ³Ð¾ÑÐ¸Ð¹
controller.CatalogControllerApiV2.AvailableSubcategories=ÐÐ¾ÑÑÑÐ¿Ð½ÑÐµ Ð¿Ð¾Ð´ÐºÐ°ÑÐµÐ³Ð¾ÑÐ¸Ð¸
controller.CatalogControllerApiV2.LikedProducts=ÐÐ·Ð±ÑÐ°Ð½Ð½ÑÐµ ÑÐ¾Ð²Ð°ÑÑ
controller.CatalogControllerApiV2.LikedProductsPage=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ° Ð¸Ð·Ð±ÑÐ°Ð½Ð½ÑÑ ÑÐ¾Ð²Ð°ÑÐ¾Ð²
controller.CatalogControllerApiV2.UsersLikedProducts=ÐÐ·Ð±ÑÐ°Ð½Ð½ÑÐµ ÑÐ¾Ð²Ð°ÑÑ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ
controller.CatalogControllerApiV2.UsersLikedProductsPage=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ° Ð¸Ð·Ð±ÑÐ°Ð½Ð½ÑÑ ÑÐ¾Ð²Ð°ÑÐ¾Ð² Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ
controller.CatalogControllerApiV2.LastSeenProducts=ÐÐ¾ÑÐ»ÐµÐ´Ð½Ð¸Ðµ Ð¿ÑÐ¾ÑÐ¼Ð¾ÑÑÐµÐ½Ð½ÑÐµ ÑÐ¾Ð²Ð°ÑÑ
controller.CatalogControllerApiV2.LastSeenProductsPage=ÐÑÐ¾ÑÐ¼Ð¾ÑÑÐµÐ½Ð½ÑÐµ ÑÐ¾Ð²Ð°ÑÑ
controller.CatalogControllerApiV2.FollowingProducts=ÐÑÑÐ»ÐµÐ¶Ð¸Ð²Ð°ÐµÐ¼ÑÐµ ÑÐ¾Ð²Ð°ÑÑ
controller.CatalogControllerApiV2.ProductWasLiked=ÐÐ¾Ð±Ð°Ð²Ð»ÐµÐ½ Ð»Ð°Ð¹Ðº Ðº ÑÐ¾Ð²Ð°ÑÑ
controller.CatalogControllerApiV2.ProductCouldntBeLiked=ÐÐµÐ»ÑÐ·Ñ Ð´Ð¾Ð±Ð°Ð²Ð¸ÑÑ Ð»Ð°Ð¹Ðº Ðº ÑÐ¾Ð²Ð°ÑÑ
controller.CatalogControllerApiV2.DislikeProduct=ÐÐ°Ð¹Ðº ÑÐ½ÑÑ Ñ ÑÐ¾Ð²Ð°ÑÐ°
controller.CatalogControllerApiV2.CouldntDislikeProduct=ÐÐµÐ»ÑÐ·Ñ ÑÐ½ÑÑÑ Ð»Ð°Ð¹Ðº Ñ ÑÐ¾Ð²Ð°ÑÐ°
controller.CatalogControllerApiV2.ToggleLikeProduct=ÐÐ°Ð¹Ðº Ð¸Ð½Ð²ÐµÑÑÐ¸ÑÐ¾Ð²Ð°Ð½
controller.CatalogControllerApiV2.TopBrands=TOP Ð±ÑÐµÐ½Ð´Ñ
controller.CatalogControllerApiV2.Brands=ÐÑÐµÐ½Ð´Ñ
controller.CatalogControllerApiV2.Brand=ÐÑÐµÐ½Ð´
controller.CatalogControllerApiV2.LikeBrand=ÐÐ¾Ð±Ð°Ð²Ð»ÐµÐ½ Ð»Ð°Ð¹Ðº Ðº Ð±ÑÐµÐ½Ð´Ñ
controller.CatalogControllerApiV2.CouldntLikeBrand=ÐÐµÐ»ÑÐ·Ñ Ð´Ð¾Ð±Ð°Ð²Ð¸ÑÑ Ð»Ð°Ð¹Ðº Ðº Ð±ÑÐµÐ½Ð´Ñ
controller.CatalogControllerApiV2.DislikeBrand=ÐÐ°Ð¹Ðº ÑÐ½ÑÑ Ñ Ð±ÑÐµÐ½Ð´Ð°
controller.CatalogControllerApiV2.CouldntDislikeBrand=ÐÐµÐ»ÑÐ·Ñ ÑÐ½ÑÑÑ Ð»Ð°Ð¹Ðº Ñ Ð±ÑÐµÐ½Ð´Ð°
controller.CatalogControllerApiV2.LikedBrands=ÐÐ·Ð±ÑÐ°Ð½Ð½ÑÐµ Ð±ÑÐµÐ½Ð´Ñ
controller.CatalogControllerApiV2.UserLikedBrands=ÐÐ·Ð±ÑÐ°Ð½Ð½ÑÐµ Ð±ÑÐµÐ½Ð´Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ

controller.DeeplinkControllerApiV2.ProcessLinkResult=Ð ÐµÐ·ÑÐ»ÑÑÐ°Ñ Ð¾Ð±ÑÐ°Ð±Ð¾ÑÐºÐ¸ ÑÑÑÐ»ÐºÐ¸

controller.NotificationControllerApiV2.Notifications=Ð£Ð²ÐµÐ´Ð¾Ð¼Ð»ÐµÐ½Ð¸Ñ
controller.NotificationControllerApiV2.Notification=Ð£Ð²ÐµÐ´Ð¾Ð¼Ð»ÐµÐ½Ð¸Ðµ
controller.NotificationControllerApiV2.CommentsNotifications=Ð£Ð²ÐµÐ´Ð¾Ð¼Ð»ÐµÐ½Ð¸Ñ Ð¾ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸ÑÑ
controller.NotificationControllerApiV2.NoCommentsNotifications=Ð£Ð²ÐµÐ´Ð¾Ð¼Ð»ÐµÐ½Ð¸Ñ, ÐºÑÐ¾Ð¼Ðµ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸ÐµÐ²
controller.NotificationControllerApiV2.NotificationCounters=Ð¡ÑÐµÑÑÐ¸ÐºÐ¸ ÑÐ²ÐµÐ´Ð¾Ð¼Ð»ÐµÐ½Ð¸Ð¹
controller.NotificationControllerApiV2.SetBirthdate=Ð£ÐºÐ°Ð¶Ð¸ÑÐµ Ð´Ð°ÑÑ Ð²Ð°ÑÐµÐ³Ð¾ ÑÐ¾Ð¶Ð´ÐµÐ½Ð¸Ñ

controller.ProductPublicationControllerApiV2.Drafts=Ð¡Ð¿Ð¸ÑÐ¾Ðº ÑÐµÑÐ½Ð¾Ð²Ð¸ÐºÐ¾Ð²
controller.ProductPublicationControllerApiV2.DraftsPage=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ° ÑÐµÑÐ½Ð¾Ð²Ð¸ÐºÐ¾Ð²
controller.ProductPublicationControllerApiV2.Rejects=Ð¡Ð¿Ð¸ÑÐ¾Ðº Ð¾ÑÐºÐ»Ð¾Ð½ÐµÐ½Ð½ÑÑ ÑÐ¾Ð²Ð°ÑÐ¾Ð²
controller.ProductPublicationControllerApiV2.RejectsPage=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ° Ð¾ÑÐºÐ»Ð¾Ð½ÐµÐ½Ð½ÑÑ ÑÐ¾Ð²Ð°ÑÐ¾Ð²
controller.ProductPublicationControllerApiV2.SecondEditionProducts=Ð¡Ð¿Ð¸ÑÐ¾Ðº ÑÐ¾Ð²Ð°ÑÐ¾Ð², Ð½Ð°Ð¿ÑÐ°Ð²Ð»ÐµÐ½Ð½ÑÑ Ð½Ð° Ð¿Ð¾Ð²ÑÐ¾ÑÐ½Ð¾Ðµ ÑÐµÐ´Ð°ÐºÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ
controller.ProductPublicationControllerApiV2.SecondEditionProductsPage=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ° ÑÐ¾Ð²Ð°ÑÐ¾Ð², Ð½Ð°Ð¿ÑÐ°Ð²Ð»ÐµÐ½Ð½ÑÑ Ð½Ð° Ð¿Ð¾Ð²ÑÐ¾ÑÐ½Ð¾Ðµ ÑÐµÐ´Ð°ÐºÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ
controller.ProductPublicationControllerApiV2.ModeratingProducts=Ð¡Ð¿Ð¸ÑÐ¾Ðº Ð¼Ð¾Ð´ÐµÑÐ¸ÑÑÐµÐ¼ÑÑ ÑÐ¾Ð²Ð°ÑÐ¾Ð²
controller.ProductPublicationControllerApiV2.ModeratingProductsPage=Ð¡ÑÑÐ°Ð½Ð¸ÑÑ Ð¼Ð¾Ð´ÐµÑÐ¸ÑÑÐµÐ¼ÑÑ ÑÐ¾Ð²Ð°ÑÐ¾Ð²
controller.ProductPublicationControllerApiV2.Products=Ð¡Ð¿Ð¸ÑÐ¾Ðº ÑÐ¾Ð²Ð°ÑÐ¾Ð²
controller.ProductPublicationControllerApiV2.ProductsPage=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ° ÑÐ¾Ð²Ð°ÑÐ¾Ð²
controller.ProductPublicationControllerApiV2.ProductsCount=ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ ÑÐ¾Ð²Ð°ÑÐ¾Ð²
controller.ProductPublicationControllerApiV2.ProductDraft=Ð§ÐµÑÐ½Ð¾Ð²Ð¸Ðº
controller.ProductPublicationControllerApiV2.ProductAdded=Ð¢Ð¾Ð²Ð°Ñ Ð´Ð¾Ð±Ð°Ð²Ð»ÐµÐ½
controller.ProductPublicationControllerApiV2.ProductOnModeration=Ð¢Ð¾Ð²Ð°Ñ Ð¾ÑÐ¿ÑÐ°Ð²Ð»ÐµÐ½ Ð½Ð° Ð¼Ð¾Ð´ÐµÑÐ°ÑÐ¸Ñ
controller.ProductPublicationControllerApiV2.ProductEdited=Ð¢Ð¾Ð²Ð°Ñ Ð¾ÑÑÐµÐ´Ð°ÐºÑÐ¸ÑÐ¾Ð²Ð°Ð½
controller.ProductPublicationControllerApiV2.ProductDeleted=Ð¢Ð¾Ð²Ð°Ñ ÑÐ´Ð°Ð»ÐµÐ½
controller.ProductPublicationControllerApiV2.ImageAdded=ÐÐ·Ð¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ðµ Ð´Ð¾Ð±Ð°Ð²Ð»ÐµÐ½Ð¾
controller.ProductPublicationControllerApiV2.ImageDeleted=ÐÐ·Ð¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ðµ ÑÐ´Ð°Ð»ÐµÐ½Ð¾
controller.ProductPublicationControllerApiV2.CommentUpdated=ÐÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹ Ð¾Ð±Ð½Ð¾Ð²Ð»ÐµÐ½
controller.ProductPublicationControllerApiV2.Comission=Ð Ð°Ð·Ð¼ÐµÑ ÐºÐ¾Ð¼Ð¸ÑÑÐ¸Ð¸ ÑÐµÐºÑÑÐµÐ³Ð¾ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ
controller.ProductPublicationControllerApiV2.PriceWithCommission=Ð¦ÐµÐ½Ð° Ñ ÐºÐ¾Ð¼Ð¸ÑÑÐ¸ÐµÐ¹
controller.ProductPublicationControllerApiV2.PriceWithoutCommission=Ð¦ÐµÐ½Ð° Ð¿Ð¾ÑÐ»Ðµ Ð²ÑÑÐµÑÐ° ÐºÐ¾Ð¼Ð¸ÑÑÐ¸Ð¸
controller.ProductPublicationControllerApiV2.Conversion=ÐÑÐµÐ¾Ð±ÑÐ°Ð·Ð¾Ð²Ð°Ð½Ð¸Ðµ ÑÐµÐ½Ñ
controller.ProductPublicationControllerApiV2.Sizes=Ð Ð°Ð·Ð¼ÐµÑÑ ÐºÐ°ÑÐµÐ³Ð¾ÑÐ¸Ð¸
controller.ProductPublicationControllerApiV2.AdditionalSizesForCategory=ÐÐ¾Ð¿Ð¾Ð»Ð½Ð¸ÑÐµÐ»ÑÐ½ÑÐµ ÑÐ°Ð·Ð¼ÐµÑÑ ÐºÐ°ÑÐµÐ³Ð¾ÑÐ¸Ð¸
controller.ProductPublicationControllerApiV2.Categories=ÐÐ°ÑÐµÐ³Ð¾ÑÐ¸Ð¸
controller.ProductPublicationControllerApiV2.CategoryTree=ÐÐµÑÐµÐ²Ð¾ ÐºÐ°ÑÐµÐ³Ð¾ÑÐ¸Ð¹
controller.ProductPublicationControllerApiV2.Brands=ÐÑÐµÐ½Ð´Ñ
controller.ProductPublicationControllerApiV2.ProductConditions=Ð¡Ð¾ÑÑÐ¾ÑÐ½Ð¸Ñ ÑÐ¾Ð²Ð°ÑÐ°
controller.ProductPublicationControllerApiV2.Attributes=ÐÑÑÐ¸Ð±ÑÑÑ ÐºÐ°ÑÐµÐ³Ð¾ÑÐ¸Ð¸
controller.ProductPublicationControllerApiV2.ProductPhotoSamples=ÐÑÐ¸Ð¼ÐµÑÑ ÑÐ¾ÑÐ¾Ð³ÑÐ°ÑÐ¸Ð¹
controller.ProductPublicationControllerApiV2.PublishStepInfo=ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ ÑÐ°Ð³Ðµ Ð¿ÑÐ±Ð»Ð¸ÐºÐ°ÑÐ¸Ð¸

controller.PublicProfileControllerApiV2.Profile=ÐÑÐ¾ÑÐ¸Ð»Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ
controller.PublicProfileControllerApiV2.Followings=Ð¡Ð¿Ð¸ÑÐ¾Ðº Ð¿Ð¾Ð´Ð¿Ð¸ÑÐ¾Ðº
controller.PublicProfileControllerApiV2.FollowingsPage=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ° Ð¿Ð¾Ð´Ð¿Ð¸ÑÐ¾Ðº
controller.PublicProfileControllerApiV2.FollowingsProposalsPage=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ° ÑÐµÐºÐ¾Ð¼ÐµÐ½Ð´Ð°ÑÐ¸Ð¹ Ð´Ð»Ñ Ð¿Ð¾Ð´Ð¿Ð¸ÑÐ¾Ðº
controller.PublicProfileControllerApiV2.Followers=Ð¡Ð¿Ð¸ÑÐ¾Ðº Ð¿Ð¾Ð´Ð¿Ð¸ÑÑÐ¸ÐºÐ¾Ð²
controller.PublicProfileControllerApiV2.FollowersPage=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ° Ð¿Ð¾Ð´Ð¿Ð¸ÑÑÐ¸ÐºÐ¾Ð²
controller.PublicProfileControllerApiV2.ToggleFollowingTurnOn=ÐÐ¾Ð´Ð¿Ð¸ÑÐºÐ° Ð¾ÑÐ¾ÑÐ¼Ð»ÐµÐ½Ð°
controller.PublicProfileControllerApiV2.ToggleFollowingTurnOff=ÐÐ¾Ð´Ð¿Ð¸ÑÐºÐ° Ð¾ÑÐ¼ÐµÐ½ÐµÐ½Ð°
controller.PublicProfileControllerApiV2.Follow=ÐÐ¾Ð´Ð¿Ð¸ÑÐºÐ° Ð¾ÑÐ¾ÑÐ¼Ð»ÐµÐ½Ð°
controller.PublicProfileControllerApiV2.CouldntFollow=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¾ÑÐ¾ÑÐ¼Ð¸ÑÑ Ð¿Ð¾Ð´Ð¿Ð¸ÑÐºÑ
controller.PublicProfileControllerApiV2.Unfollow=ÐÐ¾Ð´Ð¿Ð¸ÑÐºÐ° ÑÐ½ÑÑÐ°
controller.PublicProfileControllerApiV2.CouldntUnfollow=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ ÑÐ½ÑÑÑ Ð¿Ð¾Ð´Ð¿Ð¸ÑÐºÑ

controller.SettingsControllerApiV2.AppSettings=ÐÐ°ÑÑÑÐ¾Ð¹ÐºÐ¸ Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ

controller.SocialControllerApiV2.SocialAccounts=Ð¡Ð¾Ñ. Ð°ÐºÐºÐ°ÑÐ½ÑÑ

controller.AdminBargainsControllerApiV2.Bargains=ÐÐ¾Ð½ÑÑÑÐ¾ÑÐ³Ð¸
controller.AdminBargainsControllerApiV2.DetailedBargain=ÐÐ¾Ð´ÑÐ¾Ð±Ð½Ð°Ñ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ ÐºÐ¾Ð½ÑÑÑÐ¾ÑÐ³Ðµ
controller.AdminBargainsControllerApiV2.RefreshBargain=ÐÐ¾Ð½ÑÑÑÐ¾ÑÐ³ Ð²Ð¾Ð·Ð¾Ð±Ð½Ð¾Ð²Ð»ÐµÐ½

controller.AdminPanelOrdersControllerApiV2.OrderList=Ð¡Ð¿Ð¸ÑÐ¾Ðº Ð·Ð°ÐºÐ°Ð·Ð¾Ð²
controller.AdminPanelOrdersControllerApiV2.CartClean=Ð£Ð´Ð°Ð»ÐµÐ½Ð¸Ðµ Ð¿Ð¾Ð·Ð¸ÑÐ¸Ð¹ Ð¸Ð· ÐºÐ¾ÑÐ·Ð¸Ð½Ñ

controller.BargainsControllerApiV2.Settings=ÐÐ°ÑÑÑÐ¾Ð¹ÐºÐ¸ ÐºÐ¾Ð½ÑÑÑÐ¾ÑÐ³Ð¾Ð²
controller.BargainsControllerApiV2.IncomingBargains=ÐÑÐ¾Ð´ÑÑÐ¸Ðµ ÐºÐ¾Ð½ÑÑÑÐ¾ÑÐ³Ð¸
controller.BargainsControllerApiV2.IncomingActiveBargains=ÐÑÐ¾Ð´ÑÑÐ¸Ðµ Ð°ÐºÑÐ¸Ð²Ð½ÑÐµ ÐºÐ¾Ð½ÑÑÑÐ¾ÑÐ³Ð¸
controller.BargainsControllerApiV2.IncomingFinishedBargains=ÐÑÐ¾Ð´ÑÑÐ¸Ðµ Ð·Ð°Ð²ÐµÑÑÐµÐ½Ð½ÑÐµ ÐºÐ¾Ð½ÑÑÑÐ¾ÑÐ³Ð¸
controller.BargainsControllerApiV2.OutgoingBargains=ÐÑÑÐ¾Ð´ÑÑÐ¸Ðµ ÐºÐ¾Ð½ÑÑÑÐ¾ÑÐ³Ð¸
controller.BargainsControllerApiV2.OutgoingActiveBargains=ÐÑÑÐ¾Ð´ÑÑÐ¸Ðµ Ð°ÐºÑÐ¸Ð²Ð½ÑÐµ ÐºÐ¾Ð½ÑÑÑÐ¾ÑÐ³Ð¸
controller.BargainsControllerApiV2.OutgoingFinishedBargains=ÐÑÑÐ¾Ð´ÑÑÐ¸Ðµ Ð·Ð°Ð²ÐµÑÑÐµÐ½Ð½ÑÐµ ÐºÐ¾Ð½ÑÑÑÐ¾ÑÐ³Ð¸
controller.BargainsControllerApiV2.DetailedBargain=ÐÐ¾Ð´ÑÐ¾Ð±Ð½Ð°Ñ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ ÐºÐ¾Ð½ÑÑÑÐ¾ÑÐ³Ðµ
controller.BargainsControllerApiV2.DetailedBargainByProductAndSize=ÐÐ¾Ð´ÑÐ¾Ð±Ð½Ð°Ñ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ ÐºÐ¾Ð½ÑÑÑÐ¾ÑÐ³Ðµ
controller.BargainsControllerApiV2.DetailedBargainByTemplate=Ð¨Ð°Ð±Ð»Ð¾Ð½ ÐºÐ¾Ð½ÑÑÑÐ¾Ð³Ð¾ÑÐ³Ð°
controller.BargainsControllerApiV2.CreateBargain=ÐÐ¾Ð½ÑÑÑÐ¾ÑÐ³ ÑÐ¾Ð·Ð´Ð°Ð½
controller.BargainsControllerApiV2.AddNewRecord=ÐÐ°Ð¿Ð¸ÑÑ Ð´Ð¾Ð±Ð°Ð²Ð»ÐµÐ½Ð°
controller.BargainsControllerApiV2.BargainBubbles=Ð¡ÑÐµÑÑÐ¸ÐºÐ¸ Ð¿Ð¾ ÐºÐ¾Ð½ÑÑÑÐ¾ÑÐ³Ð°Ð¼

controller.MasterServiceControllerApiV2.NotificationsWillBeCreated=Ð£Ð²ÐµÐ´Ð¾Ð¼Ð»ÐµÐ½Ð¸Ñ Ð±ÑÐ´ÑÑ ÑÐ¾Ð·Ð´Ð°Ð½Ñ
controller.MasterServiceControllerApiV2.OK=OK
controller.MasterServiceControllerApiV2.StatusesWillBeSet=Ð¡ÑÐ°ÑÑÑÑ Ð±ÑÐ´ÑÑ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ñ
controller.MasterServiceControllerApiV2.CheckingWillBeCompleted=ÐÑÐ¾Ð²ÐµÑÐºÐ° Ð±ÑÐ´ÐµÑ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð°
controller.MasterServiceControllerApiV2.NotificationsWillBeClosed=Ð£Ð²ÐµÐ´Ð¾Ð¼Ð»ÐµÐ½Ð¸Ñ Ð±ÑÐ´ÑÑ Ð·Ð°ÐºÑÑÑÑ
controller.MasterServiceControllerApiV2.MarkNotifications=ÐÑÐ¼ÐµÑÐºÐ¸ Ð¿ÑÐ¾ÑÑÐ°Ð²Ð»ÐµÐ½Ñ
controller.MasterServiceControllerApiV2.MarkAsSentNotifications=ÐÑÐ¼ÐµÑÐºÐ¸ Ð±ÑÐ´ÑÑ Ð¿ÑÐ¾ÑÑÐ°Ð²Ð»ÐµÐ½Ñ
controller.MasterServiceControllerApiV2.SaveConversions=ÐÐ¾Ð½Ð²ÐµÑÑÐ¸Ð¸ Ð±ÑÐ´ÑÑ ÑÐ¾ÑÑÐ°Ð½ÐµÐ½Ñ
controller.MasterServiceControllerApiV2.SaveSyncUsersInfo=ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ ÑÐ¸Ð½ÑÑÐ¾Ð½Ð¸Ð·Ð¸ÑÐ¾Ð²Ð°Ð½Ð½ÑÑ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÑÑ Ð±ÑÐ´ÐµÑ ÑÐ¾ÑÑÐ°Ð½ÐµÐ½Ð°
controller.MasterServiceControllerApiV2.StartSyncBuyerChecksForOrder=Ð¡Ð¸Ð½ÑÑÐ¾Ð½Ð¸Ð·Ð°ÑÐ¸Ñ Ð¿ÑÐ¾ÑÑÐ°Ð²Ð»ÐµÐ½Ð¸Ñ ÑÐµÐºÐ¾Ð² Ð´Ð»Ñ Ð·Ð°ÐºÐ°Ð·Ð¾Ð² Ð±ÑÐ´ÐµÑ Ð½Ð°ÑÐ°ÑÐ°
controller.MasterServiceControllerApiV2.SaveLogisticStateDeliveries=ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ ÑÑÐ°ÑÑÑÐ°Ñ Ð½Ð°ÐºÐ»Ð°Ð´Ð½ÑÑ Ð±ÑÐ´ÐµÑ ÑÐ¾ÑÑÐ°Ð½ÐµÐ½Ð°
controller.MasterServiceControllerApiV2.SaveValidatedCities=ÐÑÐ¾Ð²ÐµÑÐµÐ½Ð½ÑÐµ Ð³Ð¾ÑÐ¾Ð´Ð° Ð±ÑÐ´ÑÑ ÑÐ¾ÑÑÐ°Ð½ÐµÐ½Ñ
controller.MasterServiceControllerApiV2.SaveValidatedAddresses=ÐÑÐ¾Ð²ÐµÑÐµÐ½Ð½ÑÐµ Ð°Ð´ÑÐµÑÐ° Ð±ÑÐ´ÑÑ ÑÐ¾ÑÑÐ°Ð½ÐµÐ½Ñ
controller.MasterServiceControllerApiV2.CreateAdminProductAlerts=ÐÑÐ´ÑÑ ÑÐ¾ÑÑÐ°Ð½ÐµÐ½Ñ admin product alerts
controller.MasterServiceControllerApiV2.CreateAdminOrderAlerts=ÐÑÐ´ÑÑ ÑÐ¾ÑÑÐ°Ð½ÐµÐ½Ñ admin order alerts
controller.MasterServiceControllerApiV2.SaveBankBalance=ÐÑÐ´ÐµÑ ÑÐ¾ÑÑÐ°Ð½ÐµÐ½Ð° Ð¸ÑÑÐ¾ÑÐ¸Ñ Ð±Ð°Ð»Ð°Ð½ÑÐ° ÑÑÐµÑÐ° Ð² Ð±Ð°Ð½ÐºÐµ
controller.MasterServiceControllerApiV2.SaveBankMoneyTransfer=Ð¡Ð¾ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð¿ÐµÑÐµÐ²Ð¾Ð´Ð° Ð½Ð° Ð±Ð°Ð½ÐºÐ¾Ð²ÑÐºÐ¸Ð¹ ÑÑÐµÑ Ð±ÑÐ´ÐµÑ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð¾
controller.MasterServiceControllerApiV2.SaveCheckTranferMoneyToSellers=Ð¡Ð¾ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð¿ÑÐ¾Ð²ÐµÑÐºÐ¸ Ð²ÑÐ¿Ð»Ð°Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°Ð¼ Ð±ÑÐ´ÐµÑ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð¾
controller.MasterServiceControllerApiV2.SaveTransferMoneyToSellers=Ð¡Ð¾ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð²ÑÐ¿Ð»Ð°Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°Ð¼ Ð±ÑÐ´ÐµÑ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð¾
controller.MasterServiceControllerApiV2.SavePayoutRequestStatus=Ð¡Ð¾ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ ÑÑÐ°ÑÑÑÐ° Ð±ÑÑÑÑÐ¾Ð¹ Ð·Ð°ÑÐ²ÐºÐ¸ Ð±ÑÐ´ÐµÑ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð¾
controller.MasterServiceControllerApiV2.HandleBankOperations=ÐÐ±ÑÐ°Ð±Ð¾ÑÐ°Ð½Ñ ÑÐµÐ·ÑÐ»ÑÑÐ°ÑÑ Ð±Ð°Ð½ÐºÐ¾Ð²ÑÐºÐ¸Ñ Ð¾Ð¿ÐµÑÐ°ÑÐ¸Ð¹
controller.MasterServiceControllerApiV2.HandleExpiredOrders=ÐÐ°ÐºÐ°Ð·Ñ Ð¾ÑÐ¼ÐµÐ½ÐµÐ½Ñ Ð¸Ð·-Ð·Ð° Ð½ÐµÑÐ²Ð°ÑÐºÐ¸ Ð²ÑÐµÐ¼ÐµÐ½Ð¸ Ð½Ð° ÑÐ¾Ð»Ð´Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ
controller.MasterServiceControllerApiV2.HandleFiscalReceiptsRequests=ÐÐ±ÑÐ°Ð±Ð¾ÑÐ°Ð½Ñ ÑÐµÐ·ÑÐ»ÑÑÐ°ÑÑ Ð¾ÑÐ¿ÑÐ°Ð²ÐºÐ¸ ÑÐ¸ÑÐºÐ°Ð»ÑÐ½ÑÑ ÑÐµÐºÐ¾Ð²
controller.MasterServiceControllerApiV2.SaveUserAttributes=ÐÐ±ÑÐ°Ð±Ð¾ÑÐ°Ð½Ñ Ð°ÑÑÐ¸Ð±ÑÑÑ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÐµÐ¹

controller.OrderControllerApiV2.UnfinishedOrders=ÐÐµÐ·Ð°Ð²ÐµÑÑÐµÐ½Ð½ÑÐµ Ð·Ð°ÐºÐ°Ð·Ñ ÑÐµÐºÑÑÐµÐ³Ð¾ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ
controller.OrderControllerApiV2.FinishedOrdersForBuyer=ÐÐ°Ð²ÐµÑÑÐµÐ½Ð½ÑÐµ Ð·Ð°ÐºÐ°Ð·Ñ ÑÐµÐºÑÑÐµÐ³Ð¾ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
controller.OrderControllerApiV2.FinishedOrdersForSeller=ÐÐ°Ð²ÐµÑÑÐµÐ½Ð½ÑÐµ Ð·Ð°ÐºÐ°Ð·Ñ ÑÐµÐºÑÑÐµÐ³Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°
controller.OrderControllerApiV2.InitHold=ÐÐ¿Ð»Ð°ÑÐ° Ð·Ð°ÐºÐ°Ð·Ð° Ð¸Ð½Ð¸ÑÐ¸Ð¸ÑÐ¾Ð²Ð°Ð½Ð°
controller.OrderControllerApiV2.DeleteOrder=ÐÐ°ÐºÐ°Ð· ÑÐ´Ð°Ð»ÐµÐ½
controller.OrderControllerApiV2.Order=ÐÐ°ÐºÐ°Ð·
controller.OrderControllerApiV2.BuyerOrders=ÐÐ°ÐºÐ°Ð·Ñ ÑÐµÐºÑÑÐµÐ³Ð¾ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
controller.OrderControllerApiV2.BuyerOrdersPage=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ° Ð·Ð°ÐºÐ°Ð·Ð¾Ð² ÑÐµÐºÑÑÐµÐ³Ð¾ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
controller.OrderControllerApiV2.BuyerOrdersInProcess=ÐÐ°ÐºÐ°Ð·Ñ ÑÐµÐºÑÑÐµÐ³Ð¾ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð² Ð¿ÑÐ¾ÑÐµÑÑÐµ
controller.OrderControllerApiV2.BuyerOrdersInProcessPage=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ° Ð·Ð°ÐºÐ°Ð·Ð¾Ð² ÑÐµÐºÑÑÐµÐ³Ð¾ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð² Ð¿ÑÐ¾ÑÐµÑÑÐµ
controller.OrderControllerApiV2.BuyerOrdersFinished=ÐÐ°Ð²ÐµÑÑÐµÐ½Ð½ÑÐµ Ð·Ð°ÐºÐ°Ð·Ñ ÑÐµÐºÑÑÐµÐ³Ð¾ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
controller.OrderControllerApiV2.BuyerOrdersFinishedPage=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ° Ð·Ð°Ð²ÐµÑÑÐµÐ½Ð½ÑÑ Ð·Ð°ÐºÐ°Ð·Ð¾Ð² ÑÐµÐºÑÑÐµÐ³Ð¾ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
controller.OrderControllerApiV2.BuyerOrdersReturns=ÐÐ¾Ð·Ð²ÑÐ°ÑÑ ÑÐµÐºÑÑÐµÐ³Ð¾ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
controller.OrderControllerApiV2.BuyerOrdersReturnsPage=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ° Ð²Ð¾Ð·Ð²ÑÐ°ÑÐ¾Ð² ÑÐµÐºÑÑÐµÐ³Ð¾ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
controller.OrderControllerApiV2.SellerOrders=ÐÑÐ¾Ð´Ð°Ð¶Ð¸ ÑÐµÐºÑÑÐµÐ³Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°
controller.OrderControllerApiV2.SellerOrdersPage=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ° Ð¿ÑÐ¾Ð´Ð°Ð¶ ÑÐµÐºÑÑÐµÐ³Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°
controller.OrderControllerApiV2.SellerOrdersAwaitingConfirmation=ÐÑÐ¾Ð´Ð°Ð¶Ð¸ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°, Ð¾Ð¶Ð¸Ð´Ð°ÑÑÐ¸Ðµ Ð¿Ð¾Ð´Ð²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ñ
controller.OrderControllerApiV2.SellerOrdersAwaitingConfirmationPage=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ° Ð¿ÑÐ¾Ð´Ð°Ð¶ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°, Ð¾Ð¶Ð¸Ð´Ð°ÑÑÐ¸Ñ Ð¿Ð¾Ð´Ð²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ñ
controller.OrderControllerApiV2.SellerOrdersPayments=ÐÑÐ¾Ð´Ð°Ð¶Ð¸ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°, Ð¾Ð¶Ð¸Ð´Ð°ÑÑÐ¸Ðµ Ð²ÑÐ¿Ð»Ð°Ñ
controller.OrderControllerApiV2.SellerOrdersPaymentsPage=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ° Ð¿ÑÐ¾Ð´Ð°Ð¶ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°, Ð¾Ð¶Ð¸Ð´Ð°ÑÑÐ¸Ñ Ð²ÑÐ¿Ð»Ð°Ñ
controller.OrderControllerApiV2.SellerOrdersInProcess=ÐÑÐ¾Ð´Ð°Ð¶Ð¸ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ° Ð² Ð¿ÑÐ¾ÑÐµÑÑÐµ
controller.OrderControllerApiV2.SellerOrdersInProcessPage=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ° Ð¿ÑÐ¾Ð´Ð°Ð¶ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ° Ð² Ð¿ÑÐ¾ÑÐµÑÑÐµ
controller.OrderControllerApiV2.SellerOrdersFinished=ÐÐ°Ð²ÐµÑÑÐµÐ½Ð½ÑÐµ Ð·Ð°ÐºÐ°Ð·Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°
controller.OrderControllerApiV2.SellerOrdersFinishedPage=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ° Ð·Ð°Ð²ÐµÑÑÐµÐ½Ð½ÑÑ Ð·Ð°ÐºÐ°Ð·Ð¾Ð² Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°
controller.OrderControllerApiV2.PickupAddressEndpoint=Ð¢Ð¾ÑÐºÐ° Ð²ÑÐ²Ð¾Ð·Ð° ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð°
controller.OrderControllerApiV2.DeliveryAddressEndpoint=Ð¢Ð¾ÑÐºÐ° Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð°
controller.OrderControllerApiV2.SellerCounterparty=ÐÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ° ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½
controller.OrderControllerApiV2.ConfirmOrderPosition=ÐÐ¾Ð·Ð¸ÑÐ¸Ñ Ð·Ð°ÐºÐ°Ð·Ð° Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð°/Ð¾ÑÐ¼ÐµÐ½ÐµÐ½Ð°
controller.OrderControllerApiV2.ConfirmOrder=ÐÐ°ÐºÐ°Ð· Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½/Ð¾ÑÐ¼ÐµÐ½ÐµÐ½
controller.OrderControllerApiV2.TimeIntervals=ÐÑÐµÐ¼ÐµÐ½Ð½ÑÐµ Ð¸Ð½ÑÐµÑÐ²Ð°Ð»Ñ Ð·Ð°Ð±Ð¾ÑÐ° Ð³ÑÑÐ·Ð°
controller.OrderControllerApiV2.AvailablePickupDates=ÐÐ¾ÑÑÑÐ¿Ð½ÑÐµ Ð´Ð°ÑÑ Ð·Ð°Ð±Ð¾ÑÐ° Ð³ÑÑÐ·Ð°
controller.OrderControllerApiV2.PickupTimeIntervalId=ÐÑÐµÐ¼ÐµÐ½Ð½Ð¾Ð¹ Ð¸Ð½ÑÐµÑÐ²Ð°Ð» Ð·Ð°Ð±Ð¾ÑÐ° ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½
controller.OrderControllerApiV2.PickupComment=ÐÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹ Ðº Ð·Ð°Ð±Ð¾ÑÑ Ð·Ð°ÐºÐ°Ð·Ð° ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½
controller.OrderControllerApiV2.DeliveryComment=ÐÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹ Ðº Ð´Ð¾ÑÑÐ°Ð²ÐºÐµ Ð·Ð°ÐºÐ°Ð·Ð° ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½
controller.OrderControllerApiV2.ReturnReasons=ÐÐ¾Ð·Ð¼Ð¾Ð¶Ð½ÑÐµ Ð¿ÑÐ¸ÑÐ¸Ð½Ñ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐ°
controller.OrderControllerApiV2.InitReturn=ÐÐ¾Ð·Ð²ÑÐ°Ñ Ð¾ÑÐ¾ÑÐ¼Ð»ÐµÐ½
controller.OrderControllerApiV2.ReturnInfo=ÐÐ¾Ð´ÑÐ¾Ð±Ð½Ð°Ñ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµ
controller.OrderControllerApiV2.BuyerReturnsPage=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ° Ð²Ð¾Ð·Ð²ÑÐ°ÑÐ¾Ð² Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
controller.OrderControllerApiV2.SellerReturnsPage=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ° Ð²Ð¾Ð·Ð²ÑÐ°ÑÐ¾Ð² ÑÐ¾Ð´Ð°Ð²ÑÐ°

validator.SelectionValidator.FirstLineNotFound=ÐÐµ ÑÐºÐ°Ð·Ð°Ð½Ð° Ð¿ÐµÑÐ²Ð°Ñ ÑÑÑÐ¾ÐºÐ°
validator.SelectionValidator.SecondLineNotFound=ÐÐµ ÑÐºÐ°Ð·Ð°Ð½Ð° Ð²ÑÐ¾ÑÐ°Ñ ÑÑÑÐ¾ÐºÐ°
validator.SelectionValidator.ThirdLineNotFound=ÐÐµ ÑÐºÐ°Ð·Ð°Ð½Ð° ÑÑÐµÑÑÑ ÑÑÑÐ¾ÐºÐ°
validator.SelectionValidator.FirstLineTooLong=ÐÐµÑÐ²Ð°Ñ ÑÑÑÐ¾ÐºÐ° ÑÐ»Ð¸ÑÐºÐ¾Ð¼ Ð´Ð»Ð¸Ð½Ð½Ð°Ñ
validator.SelectionValidator.SecondLineTooLong=ÐÑÐ¾ÑÐ°Ñ ÑÑÑÐ¾ÐºÐ° ÑÐ»Ð¸ÑÐºÐ¾Ð¼ Ð´Ð»Ð¸Ð½Ð½Ð°Ñ
validator.SelectionValidator.ThirdLineTooLong=Ð¢ÑÐµÑÑÑ ÑÑÑÐ¾ÐºÐ° ÑÐ»Ð¸ÑÐºÐ¾Ð¼ Ð´Ð»Ð¸Ð½Ð½Ð°Ñ
validator.SelectionValidator.UrlNotFound=ÐÐµ ÑÐºÐ°Ð·Ð°Ð½ Url

validator.ProfileImageValidator.ImageWrongSizeWarning=Ð Ð°Ð·Ð¼ÐµÑ ÐºÐ°ÑÑÐ¸Ð½ÐºÐ¸ Ð´Ð¾Ð»Ð¶ÐµÐ½ Ð±ÑÑÑ Ð² Ð¿ÑÐµÐ´ÐµÐ»Ð°Ñ {0}x{1} - {2}x{3}
validator.ProfileImageValidator.ImageWrongSize=ÐÐµÐ´Ð¾Ð¿ÑÑÑÐ¸Ð¼ÑÐ¹ ÑÐ°Ð·Ð¼ÐµÑ Ð¸Ð·Ð¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ñ
validator.ProfileImageValidator.ImageWrongMessage=ÐÐ¾Ð¿ÑÑÐºÐ°ÑÑÑÑ ÑÐ¾Ð»ÑÐºÐ¾ ÐºÐ²Ð°Ð´ÑÐ°ÑÐ½ÑÐµ Ð¸Ð·Ð¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ñ

validator.ImageValidator.EmptyImage=ÐÑÑÑÑÑÑÐ²ÑÐµÑ Ð¸Ð·Ð¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ðµ
validator.ImageValidator.FileSizeTooLarge=Ð¡Ð»Ð¸ÑÐºÐ¾Ð¼ Ð±Ð¾Ð»ÑÑÐ¾Ð¹ ÑÐ°Ð·Ð¼ÐµÑ Ð·Ð°Ð³ÑÑÐ¶Ð°ÐµÐ¼Ð¾Ð³Ð¾ ÑÐ°Ð¹Ð»Ð°
validator.ImageValidator.WrongImageFormat=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ ÑÐ¾ÑÐ¼Ð°Ñ ÑÐ°Ð¹Ð»Ð°

validator.DefaultImageValidator.WrongImageSize=Ð Ð°Ð·Ð¼ÐµÑ ÐºÐ°ÑÑÐ¸Ð½ÐºÐ¸ Ð´Ð¾Ð»Ð¶ÐµÐ½ Ð±ÑÑÑ {0}x{1}

service.DefaultConciergeService.OrderInfo.Notification=Ð£Ð²ÐµÐ´Ð¾Ð¼Ð»ÐµÐ½Ð¸Ðµ Ð¾Ð± Ð¾Ð¿Ð»Ð°ÑÐµÐ½Ð½Ð¾Ð¼ Ð·Ð°ÐºÐ°Ð·Ðµ
service.DefaultConciergeService.OrderInfo.PaymentDate=ÐÐ°ÑÐ° Ð¾Ð¿Ð»Ð°ÑÑ
service.DefaultConciergeService.OrderInfo.OrderId=ÐÐ¾Ð¼ÐµÑ Ð·Ð°ÐºÐ°Ð·Ð°
service.DefaultConciergeService.OrderInfo.ProductList=Ð¡Ð¿Ð¸ÑÐ¾Ðº ÑÐ¾Ð²Ð°ÑÐ¾Ð²
service.DefaultConciergeService.OrderInfo.SizeNotIdentified=Ð Ð°Ð·Ð¼ÐµÑ Ð½Ðµ Ð¾Ð¿ÑÐµÐ´ÐµÐ»ÐµÐ½, ÑÑÐ¾ÑÐ½Ð¸ÑÐµ Ñ Ð¼ÐµÐ½ÐµÐ´Ð¶ÐµÑÐ°
service.DefaultConciergeService.OrderInfo.WithSize=Ñ ÑÐ°Ð·Ð¼ÐµÑÐ¾Ð¼
service.DefaultConciergeService.OrderInfo.Product=Ð¢Ð¾Ð²Ð°Ñ
service.DefaultConciergeService.OrderInfo.ColorNotIdentified=Ð¦Ð²ÐµÑ Ð½Ðµ Ð¾Ð¿ÑÐµÐ´ÐµÐ»ÐµÐ½, ÑÑÐ¾ÑÐ½Ð¸ÑÐµ Ñ Ð¼ÐµÐ½ÐµÐ´Ð¶ÐµÑÐ°
service.DefaultConciergeService.OrderInfo.Color=Ð¦Ð²ÐµÑ
service.DefaultConciergeService.OrderInfo.CurrencyInEuro=Ð¡ÑÐ¼Ð¼Ð° Ð² â¬
service.DefaultConciergeService.OrderInfo.CurrencyInRub=Ð¡ÑÐ¼Ð¼Ð° Ð² â½
service.DefaultConciergeService.OrderInfo.LinkToImage=Ð¡ÑÑÐ»ÐºÐ° Ð½Ð° ÑÐ¾ÑÐ¾

service.DefaultAdminOrdersService.OrderStatusGroupAll=ÐÑÐµ
service.DefaultAdminOrdersService.OrderStatusGroupPayment=ÐÐ¿Ð»Ð°ÑÐ°
service.DefaultAdminOrdersService.OrderStatusGroupFromSeller=ÐÐ°Ð±Ð¸ÑÐ°ÐµÐ¼ Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°
service.DefaultAdminOrdersService.OrderStatusGroupExpertise=Ð­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ð°
service.DefaultAdminOrdersService.OrderStatusGroupToBuyer=ÐÐ¾ÑÑÐ°Ð²ÐºÐ° Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.DefaultAdminOrdersService.OrderStatusGroupPaymentToSeller=ÐÑÐ¿Ð»Ð°ÑÐ° Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°Ð¼
service.DefaultAdminOrdersService.OrderStatusGroupReturn=ÐÐ¾Ð·Ð²ÑÐ°Ñ
service.DefaultAdminOrdersService.OrderStatusGroupBoutiqueOrders=ÐÑÑÐ¸Ðº

service.DefaultAdminOrdersService.OrderStatusGroup.REFUND=ÐÐ¾Ð·Ð²ÑÐ°Ñ ÑÑÐµÐ´ÑÑÐ²
service.DefaultAdminOrdersService.OrderStatusGroup.CREATED=Ð¡Ð¾Ð·Ð´Ð°Ð½Ð¸Ðµ Ð·Ð°ÐºÐ°Ð·Ð°
service.DefaultAdminOrdersService.OrderStatusGroup.COMPLETED=ÐÐ°Ð²ÐµÑÑÐµÐ½Ð½ÑÐµ Ð·Ð°ÐºÐ°Ð·Ñ
service.DefaultAdminOrdersService.OrderStatusGroup.UNCOMPLETED=ÐÐµÐ·Ð°Ð²ÐµÑÑÐµÐ½Ð½ÑÐµ Ð·Ð°ÐºÐ°Ð·Ñ
service.DefaultAdminOrdersService.OrderStatusGroup.OVERDUE_EXPERTISE=5 Ð´Ð½ÐµÐ¹ Ð±ÐµÐ· ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ

service.DefaultAdminOrdersService.OrderStatusUndefined=ÐÑÐµ

service.DefaultAdminOrdersService.DestinationDescription.OFFICE=ÐÑ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ° Ð² OSKELLY
service.DefaultAdminOrdersService.DestinationDescription.BUYER=ÐÑ OSKELLY Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.DefaultAdminOrdersService.DestinationDescription.SELLER=ÐÑ OSKELLY Ð¿ÑÐ¾Ð´Ð°Ð²ÑÑ

#ÐÐ°Ð¿ÑÐ¸Ð¼ÐµÑ, 10 25 2025 Ð² 14:24
service.DefaultAdminOrdersService.InTime='Ð²'

service.DefaultAdminOrdersService.OrderPositionNotFound=ÐÐ¾Ð·Ð¸ÑÐ¸Ñ Ð·Ð°ÐºÐ°Ð·Ð° Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð°: {0}

service.DefaultAdminOrdersService.OrderPositionExpertiseNotCompleted=ÐÐ¾Ð·Ð¸ÑÐ¸Ñ {0} Ð·Ð°ÐºÐ°Ð·Ð° {1} Ð½Ðµ Ð¿ÑÐ¾ÑÐ¾Ð´Ð¸Ð»Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ

service.DefaultAdminOrdersService.OrderListIsEmpty=Ð¡Ð¿Ð¸ÑÐ¾Ðº Ð·Ð°ÐºÐ°Ð·Ð¾Ð² Ð¿ÑÑÑÐ¾Ð¹
service.DefaultAdminOrdersService.OrderNotFound=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½: {0}
service.DefaultAdminOrdersService.WaybillNotFound=ÐÐµ ÑÑÑÐµÑÑÐ²ÑÐµÑ Ð½Ð°ÐºÐ»Ð°Ð´Ð½Ð¾Ð¹ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð´Ð»Ñ Ð·Ð°ÐºÐ°Ð·Ð° {0}
service.DefaultAdminOrdersService.OrderProcessingByDifferentCompanies=ÐÐ°ÐºÐ°Ð·Ñ Ð´Ð¾ÑÑÐ°Ð²Ð»ÑÑÑÑÑ ÑÐ°Ð·Ð½ÑÐ¼Ð¸ ÐºÑÑÑÐµÑÑÐºÐ¸Ð¼Ð¸ ÑÐ»ÑÐ¶Ð±Ð°Ð¼Ð¸: {0}
service.DefaultAdminOrdersService.ActReportNotSupported=ÐÐºÑ Ð¿ÐµÑÐµÐ´Ð°ÑÐ¸ ÐºÑÑÑÐµÑÑÐºÐ¾Ð¹ ÑÐ»ÑÐ¶Ð±Ðµ Ð½Ðµ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÐµÑÑÑ Ð´Ð»Ñ {0}

service.DefaultAdminOrdersService.SellerNameNotFilled=ÐÐÐÐÐÐÐÐ¢Ð Ð¤ÐÐ Ð Ð ÐÐÐÐÐÐÐ¢ÐÐ¥ ÐÐ ÐÐÐÐÐ¦Ð
service.DefaultAdminOrdersService.CbNotAllowDebt=ÐÐ°ÐºÐ°Ð· {0}\: Ð·Ð°ÑÐµÑ Ð´Ð¾Ð»Ð³Ð¾Ð² Ð² Ð·Ð°ÐºÐ°Ð·Ð°Ñ ÐÐ Ð¾ÑÐºÐ»ÑÑÐµÐ½

service.DefaultAdminOrdersService.ReturnablePeriod={0} Ð´Ð½ÐµÐ¹ {1} ÑÐ°ÑÐ° Ð´Ð¾ Ð·Ð°Ð²ÐµÑÑÐµÐ½Ð¸Ñ Ð·Ð°ÐºÐ°Ð·Ð°

service.DefaultAdminOrdersService.DataNotFound=ÐÐµÑ Ð´Ð°Ð½Ð½ÑÑ
service.DefaultAdminOrdersService.HoldPeriod={0} Ð´Ð½Ñ

service.DefaultAdminOrdersService.AgentReportHasProblem=ÐÐµÐ»ÑÐ·Ñ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸ÑÑ Ð¾ÑÑÐµÑ Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð¶Ðµ: ÐµÑÑÑ ÑÐ¿Ð¾Ñ. ÐÐ°ÐºÐ°Ð· - {0}
service.DefaultAdminOrdersService.InvalidStatus=ÐÐµÐ²ÐµÑÐ½ÑÐ¹ ÑÑÐ°ÑÑÑ {0} Ð·Ð°ÐºÐ°Ð·Ð° {1}
service.DefaultAdminOrdersService.InvalidOrderSource=ÐÐµÐ²ÐµÑÐ½ÑÐ¹ Ð¸ÑÑÐ¾ÑÐ½Ð¸Ðº {0} Ð·Ð°ÐºÐ°Ð·Ð° {1}
service.DefaultAdminOrdersService.Shipment2Boutique=ÐÐ° Ð¾ÑÐ³ÑÑÐ·ÐºÑ Ð² Ð±ÑÑÐ¸Ðº

service.DefaultAdminOrdersService.Boutique=Ð±ÑÑÐ¸Ðº

service.DefaultAdminProductService.RetoucherRestricted=Ð ÐµÑÑÑÐµÑ Ð¼Ð¾Ð¶ÐµÑ Ð¿ÑÐ¾ÑÐ¼Ð°ÑÑÐ¸Ð²Ð°ÑÑ ÑÐ¾Ð»ÑÐºÐ¾ ÑÐ¾Ð²Ð°ÑÑ ÑÐ¾ ÑÑÐ°ÑÑÑÐ¾Ð¼ Ð¢ÑÐµÐ±ÑÐµÑÑÑ ÑÐµÑÑÑÑ
service.DefaultAdminProductService.SizeTypeViewComment=ÑÐ¸Ð¿ Ð² ÐºÐ°ÑÐµÐ³Ð¾ÑÐ¸Ð¸
service.DefaultAdminProductService.WebsiteSalesChannelForBoutiqueProductError=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ ÑÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ ÐºÐ°Ð½Ð°Ð» Ð¿ÑÐ¾Ð´Ð°Ð¶ Ð¡Ð°Ð¹Ñ Ð´Ð»Ñ ÑÐ¾Ð²Ð°ÑÐ° {0}, ÑÐ°Ðº ÐºÐ°Ðº ÑÐ¾Ð²Ð°Ñ Ð½Ð°ÑÐ¾Ð´Ð¸ÑÑÑ Ð² ÐÑÑÐ¸ÐºÐµ. ÐÐ°ÐºÐ°Ð·Ñ ÐÑÑÐ¸ÐºÐ° - {1}.
service.DefaultAdminProductService.RemovingSizeUsedInBoutiqueOrderError=ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ ÐµÐ´Ð¸Ð½Ð¸Ñ ÑÐ°Ð·Ð¼ÐµÑÐ° ({0}) Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¼ÐµÐ½ÑÑÐµ {1}, ÑÐ°Ðº ÐºÐ°Ðº ÑÐ¾Ð²Ð°Ñ Ð½Ð°ÑÐ¾Ð´Ð¸ÑÑÑ Ð² ÐÑÑÐ¸ÐºÐµ. ÐÐ°ÐºÐ°Ð·Ñ ÐÑÑÐ¸ÐºÐ° - {2}.

service.DefaultAdminPromoCodeService.PromocodeNotFound=ÐÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´Ð° Ñ ÑÐ°ÐºÐ¸Ð¼ Ð¸Ð´ÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¾ÑÐ¾Ð¼ Ð½Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ Ð¸Ð»Ð¸ ÑÐ´Ð°Ð»ÐµÐ½
service.DefaultAdminPromoCodeService.PromocodeWithSameCodeAlreadyExists=ÐÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´ Ñ ÑÐ°ÐºÐ¸Ð¼ ÐºÐ¾Ð´Ð¾Ð¼ ÑÑÑÐµÑÑÐ²ÑÐµÑ
service.DefaultAdminPromoCodeService.SetDiscountInPercentOrMoney=Ð£ÐºÐ°Ð¶Ð¸ÑÐµ ÑÐºÐ¸Ð´ÐºÑ Ð² Ð¿ÑÐ¾ÑÐµÐ½ÑÐ°Ñ Ð»Ð¸Ð±Ð¾ Ð² ÑÑÐ±Ð»ÑÑ!
service.DefaultAdminPromoCodeService.PromocodeNotExists=ÐÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´Ð° Ñ ÑÐ°ÐºÐ¸Ð¼ Ð¸Ð´ÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¾ÑÐ¾Ð¼ Ð½Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ
service.DefaultAdminPromoCodeService.PromocodeWithSameCodeAlreadyDeleted=ÐÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´ Ñ ÑÐ°ÐºÐ¸Ð¼ Ð¸Ð´ÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¾ÑÐ¾Ð¼ ÑÐ¶Ðµ ÑÐ´Ð°Ð»ÐµÐ½ Ð¸ Ð½Ðµ Ð¿Ð¾Ð´Ð»ÐµÐ¶Ð¸Ñ ÑÐµÐ´Ð°ÐºÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ

service.DefaultProtectedPromoCodeService.SecurePromocodeAlreadyCreated=ÐÐ°ÑÐ¸ÑÐµÐ½Ð½ÑÐ¹ Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´ ÑÐ¶Ðµ ÑÐ¾Ð·Ð´Ð°Ð½ Ð´Ð»Ñ Ð·Ð°ÐºÐ°Ð·Ð° {0}

service.DefaultAdminUserService.ContractInfo=ÐÐ¾Ð¼ÐµÑ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ°: {0}. ÐÐ°ÑÐ°: {1}
service.DefaultAdminUserService.UserNotSet=ÐÐµ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð´Ð»Ñ ÑÐ¾ÑÑÐ°Ð½ÐµÐ½Ð¸Ñ Ð´Ð¾Ð»Ð¶Ð½Ð¾ÑÑÐ¸
service.DefaultAdminUserService.Measure.Million=Ð¼Ð»Ð½
service.DefaultAdminUserService.Measure.Thousand=ÑÑÑ.

service.DefaultAgentReportService.IndividualEntrepreneur=ÐÐ½Ð´Ð¸Ð²Ð¸Ð´ÑÐ°Ð»ÑÐ½ÑÐ¹ Ð¿ÑÐµÐ´Ð¿ÑÐ¸Ð½Ð¸Ð¼Ð°ÑÐµÐ»Ñ {0}
service.DefaultAgentReportService.PaymentSimpleUser=ÐÐ¿Ð»Ð°ÑÐ° Ð¿Ð¾ Ð·Ð°ÐºÐ°Ð·Ñ â {0} Ð¿Ð¾ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÑ-Ð¾ÑÐµÑÑÐµ â {1} Ð¾Ñ {2} Ð½Ð° ÑÐ°Ð¹ÑÐµ: www.oskelly.ru. ÐÐÐ¡ Ð½Ðµ Ð¾Ð±Ð»Ð°Ð³Ð°ÐµÑÑÑ
service.DefaultAgentReportService.PaymentAgent=ÐÐ¿Ð»Ð°ÑÐ° Ð·Ð° ÑÐµÐ°Ð»Ð¸Ð·Ð¾Ð²Ð°Ð½Ð½ÑÐ¹ ÑÐ¾Ð²Ð°Ñ Ð¿Ð¾ Ð·Ð°ÐºÐ°Ð·Ñ â {0} Ð¿Ð¾ Ð°Ð³ÐµÐ½ÑÑÐºÐ¾Ð¼Ñ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÑ â {1} Ð¾Ñ {2} Ð½Ð° ÑÐ°Ð¹ÑÐµ: www.oskelly.ru. ÐÐÐ¡ Ð½Ðµ Ð¾Ð±Ð»Ð°Ð³Ð°ÐµÑÑÑ

service.BasePayoutService.CbNotAllowDebt=ÐÐ°ÐºÐ°Ð· {0}\: Ð·Ð°ÑÐµÑ Ð´Ð¾Ð»Ð³Ð¾Ð² Ð² Ð·Ð°ÐºÐ°Ð·Ð°Ñ ÐÐ Ð½Ðµ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÐµÑÑÑ

service.DefaultCartService.CheckPromoCodeError=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¿ÑÐ¸Ð¼ÐµÐ½Ð¸ÑÑ Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´: Ð·Ð°ÐºÐ°Ð· ÑÑÑÐ°ÑÐµÐ». ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¾Ð±Ð½Ð¾Ð²Ð¸ÑÐµ ÐºÐ¾ÑÐ·Ð¸Ð½Ñ.
service.DefaultCartService.notEnoughAmount=Ð¡ÑÐ¼Ð¼Ð° Ð¿Ð¾ÐºÑÐ¿ÐºÐ¸ Ð´Ð¾Ð»Ð¶Ð½Ð° ÑÐ¾ÑÑÐ°Ð²Ð»ÑÑÑ Ð½Ðµ Ð¼ÐµÐ½ÐµÐµ <b>{0}</b> Ð¾Ñ Ð¾Ð´Ð½Ð¾Ð³Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°

service.BargainConfiguration.SuccessBargains=ÐÐ°Ð»Ð¾Ð³ ÑÑÐ¿ÐµÑÐ½ÑÑ ÑÐ¾ÑÐ³Ð¾Ð²
service.BargainConfiguration.FirstSentence=ÐÑ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶Ð¸ÑÑ Ð½Ðµ Ð¼ÐµÐ½ÐµÐµ 70% Ð¾Ñ ÑÑÐ¾Ð¸Ð¼Ð¾ÑÑÐ¸ ÑÐ¾Ð²Ð°ÑÐ°
service.BargainConfiguration.SecondSentence=ÐÑÐ»Ð¸ Ð¿ÑÐ¾Ð´Ð°Ð²ÐµÑ Ð¾ÑÐºÐ°Ð¶ÐµÑ Ð² ÑÐ¾ÑÐ³Ðµ, Ð½Ðµ ÑÐ°ÑÑÑÑÐ°Ð¸Ð²Ð°Ð¹ÑÐµÑÑ, Ñ Ð²Ð°Ñ ÐµÑÐµ ÐµÑÑÑ 2 Ð¿Ð¾Ð¿ÑÑÐºÐ¸
service.BargainConfiguration.ThirdSentence=Ð£ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ° ÐµÑÑÑ 24 ÑÐ°ÑÐ° Ð½Ð° Ð¿ÑÐ¸Ð½ÑÑÐ¸Ðµ ÑÐµÑÐµÐ½Ð¸Ñ
service.BargainConfiguration.ForthSentence=ÐÐ¾ÑÐ»Ðµ ÑÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ñ ÑÐµÐ½Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð¼, Ñ Ð²Ð°Ñ ÐµÑÑÑ 24 ÑÐ°ÑÐ°, ÑÑÐ¾Ð±Ñ ÐºÑÐ¿Ð¸ÑÑ ÑÐ¾Ð²Ð°Ñ Ð¿Ð¾ Ð½Ð¾Ð²Ð¾Ð¹ ÑÐµÐ½Ðµ

service.BargainConfiguration.Start.SetOwnPrice=ÐÐ»Ñ Ð½Ð°ÑÐ°Ð»Ð° ÑÐ¾ÑÐ³Ð¾Ð² Ð¿ÑÐµÐ´Ð»Ð¾Ð¶Ð¸ÑÐµ ÑÐ²Ð¾Ñ ÑÐµÐ½Ñ
service.BargainConfiguration.Start.LimitPrice=ÐÑ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶Ð¸ÑÑ Ð½Ðµ Ð¼ÐµÐ½ÐµÐµ 70% Ð¾Ñ ÑÑÐ¾Ð¸Ð¼Ð¾ÑÑÐ¸ ÑÐ¾Ð²Ð°ÑÐ°. ÐÑÐ¾Ð´Ð°Ð²ÐµÑ Ð»Ð¸Ð±Ð¾ Ð¿ÑÐ¸Ð¼ÐµÑ, Ð»Ð¸Ð±Ð¾ Ð¾ÑÐºÐ»Ð¾Ð½Ð¸Ñ Ð²Ð°ÑÐµ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ. ÐÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ Ð´ÐµÐ¹ÑÑÐ²Ð¸ÑÐµÐ»ÑÐ½Ð¾ 24 ÑÐ°ÑÐ°
service.BargainConfiguration.Active.ActiveBargainsNotFound=Ð£ Ð²Ð°Ñ Ð½ÐµÑ Ð°ÐºÑÐ¸Ð²Ð½ÑÑ ÑÐ¾ÑÐ³Ð¾Ð²
service.BargainConfiguration.Active.SetOwnPrice=ÐÑÐ»Ð¸ Ð²Ñ ÑÑÐ¸ÑÐ°ÐµÑÐµ, ÑÑÐ¾ ÑÐµÐ½Ð° Ð·Ð°Ð²ÑÑÐµÐ½Ð°, Ð¿ÑÐµÐ´Ð»Ð¾Ð¶Ð¸ÑÐµ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÑ ÑÐ²Ð¾Ñ ÑÐµÐ½Ñ. ÐÐ½, Ð² ÑÐ²Ð¾Ñ Ð¾ÑÐµÑÐµÐ´Ñ, Ð¸Ð¼ÐµÐµÑ Ð¿ÑÐ°Ð²Ð¾ Ð¾ÑÐºÐ°Ð·Ð°ÑÑÑÑ Ð¾Ñ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð½Ð¾Ð¹ ÑÐµÐ½Ñ
service.BargainConfiguration.Completed.CompletedBargainsNotFound=Ð£ Ð²Ð°Ñ Ð½ÐµÑ Ð·Ð°Ð²ÐµÑÑÐµÐ½Ð½ÑÑ ÑÐ¾ÑÐ³Ð¾Ð²
service.BargainConfiguration.Completed.BargainsStory=ÐÐ´ÐµÑÑ Ð±ÑÐ´ÐµÑ Ð¾ÑÐ¾Ð±ÑÐ°Ð¶Ð°ÑÑÑÑ Ð¸ÑÑÐ¾ÑÐ¸Ñ Ð²Ð°ÑÐ¸Ñ ÑÐ¾ÑÐ³Ð¾Ð²
service.BargainConfiguration.Deadline.Deadline=Ð¡ÑÐ¾Ðº Ð´ÐµÐ¹ÑÑÐ²Ð¸Ñ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ Ð¸ÑÑÐµÐº
service.BargainConfiguration.Deadline.BeFaster=Ð ÑÐ»ÐµÐ´ÑÑÑÐ¸Ð¹ ÑÐ°Ð· Ð±ÑÑÑÑÐµÐµ Ð¿ÑÐ¸Ð½Ð¸Ð¼Ð°Ð¹ÑÐµ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°
service.BargainConfiguration.Success.Completed=ÐÐ°Ð¼ ÑÐ´Ð°Ð»Ð¾ÑÑ ÐºÑÐ¿Ð¸ÑÑ Ð¿Ð¾ ÑÐ°Ð¼Ð¾Ð¹ Ð²ÑÐ³Ð¾Ð´Ð½Ð¾Ð¹ ÑÐµÐ½Ðµ!
service.BargainConfiguration.Success.Advice=Ð¢Ð¾ÑÐ³ÑÐ¹ÑÐµÑÑ Ð¸ Ð¿Ð¾Ð»ÑÑÐ°Ð¹ÑÐµ ÑÐ¾ÑÐ¾ÑÐ¸Ðµ ÑÐºÐ¸Ð´ÐºÐ¸ Ð¾Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð²
service.BargainConfiguration.Fail.Completed=Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, Ð¿ÑÐ¾Ð´Ð°Ð²ÐµÑ Ð¾ÑÐºÐ»Ð¾Ð½Ð¸Ð» Ð²Ð°Ñ ÑÐ¾ÑÐ³
service.BargainConfiguration.Fail.Advice=ÐÐµ ÑÐ°ÑÑÑÑÐ°Ð¸Ð²Ð°Ð¹ÑÐµÑÑ, ÑÐ¾ÑÐ³ÑÐ¹ÑÐµÑÑ Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°Ð¼Ð¸ Ð¸ Ð¿Ð¾ÐºÑÐ¿Ð°Ð¹ÑÐµ Ð¿Ð¾ ÑÐ°Ð¼ÑÐ¼ Ð²ÑÐ³Ð¾Ð´Ð½ÑÐ¼ ÑÐµÐ½Ð°Ð¼
service.BargainConfiguration.Response.Failed=Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, Ð¿ÑÐ¾Ð´Ð°Ð²ÐµÑ Ð½Ðµ Ð¾ÑÐ²ÐµÑÐ¸Ð» Ð½Ð° Ð²Ð°Ñ ÑÐ¾ÑÐ³
service.BargainConfiguration.Response.Advice=ÐÐµ ÑÐ°ÑÑÑÑÐ°Ð¸Ð²Ð°Ð¹ÑÐµÑÑ, ÑÐ¾ÑÐ³ÑÐ¹ÑÐµÑÑ Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°Ð¼Ð¸ Ð¸ Ð¿Ð¾ÐºÑÐ¿Ð°Ð¹ÑÐµ Ð¿Ð¾ ÑÐ°Ð¼ÑÐ¼ Ð²ÑÐ³Ð¾Ð´Ð½ÑÐ¼ ÑÐµÐ½Ð°Ð¼
service.BargainConfiguration.BargainsDeadline=Ð£ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ° ÐµÑÑÑ {timeLeft}, ÑÑÐ¾Ð±Ñ Ð²Ð°Ð¼ Ð¾ÑÐ²ÐµÑÐ¸ÑÑ
service.BargainConfiguration.PriceDeadline=Ð¦ÐµÐ½Ð° Ð´ÐµÐ¹ÑÑÐ²Ð¸ÑÐµÐ»ÑÐ½Ð°: {timeLeft}
service.BargainConfiguration.AnotherOne.Fail=Ð¢Ð¾Ð²Ð°Ñ ÑÐ¶Ðµ ÐºÑÐ¿Ð¸Ð» ÐºÑÐ¾-ÑÐ¾ Ð´ÑÑÐ³Ð¾Ð¹
service.BargainConfiguration.AnotherOne.Advice=Ð ÑÐ»ÐµÐ´ÑÑÑÐ¸Ð¹ ÑÐ°Ð· Ð¿Ð¾ÐºÑÐ¿Ð°Ð¹ÑÐµ Ð±ÑÑÑÑÐµÐµ
service.BargainConfiguration.PriceChanged.Notification=Ð¦ÐµÐ½Ð° ÑÐ¾Ð²Ð°ÑÐ° Ð¸Ð·Ð¼ÐµÐ½Ð¸Ð»Ð°ÑÑ
service.BargainConfiguration.PriceChanged.Advice=ÐÑÐ¾Ð´Ð°Ð²ÐµÑ Ð¿Ð¾Ð¼ÐµÐ½ÑÐ» ÑÐµÐ½Ñ ÑÐ¾Ð²Ð°ÑÐ°, Ð¿Ð¾ÑÑÐ¾Ð¼Ñ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ ÑÐ¾ÑÐ³Ð° Ð±ÑÐ»Ð¾ Ð¾ÑÐ¼ÐµÐ½ÐµÐ½Ð¾
service.BargainConfiguration.ApprovePrice.Text=Ð¡Ð¾Ð³Ð»Ð°ÑÑÐ¹ÑÐµ ÑÐµÐ½Ñ
service.BargainConfiguration.ApprovePrice.Advice=ÐÑ Ð¼Ð¾Ð¶ÐµÑÐµ ÑÐ¾Ð³Ð»Ð°ÑÐ¸ÑÑÑÑ, Ð¾ÑÐºÐ»Ð¾Ð½Ð¸ÑÑ, Ð¸Ð»Ð¸ Ð²ÑÐ½ÐµÑÑÐ¸ ÑÐ²Ð¾Ðµ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ Ð½Ð° ÑÐ¾ÑÐ³ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ. ÐÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ Ð´ÐµÐ¹ÑÑÐ²Ð¸ÑÐµÐ»ÑÐ½Ð¾ 24 ÑÐ°ÑÐ°
service.BargainConfiguration.OfferAvailable=ÐÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ Ð´ÐµÐ¹ÑÑÐ²Ð¸ÑÐµÐ»ÑÐ½Ð¾: {timeLeft}
service.BargainConfiguration.BuyerWasLate=Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð½Ðµ ÑÑÐ¿ÐµÐ» ÐºÑÐ¿Ð¸ÑÑ ÑÐ¾Ð²Ð°Ñ Ð¿Ð¾ Ð½Ð¾Ð²Ð¾Ð¹ ÑÐµÐ½Ðµ
service.BargainConfiguration.Deadline.Seller.Failed=Ð¡ÑÐ¾Ðº Ð´ÐµÐ¹ÑÑÐ²Ð¸Ñ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ Ð¸ÑÑÐµÐº
service.BargainConfiguration.Deadline.Seller.Advice=Ð ÑÐ»ÐµÐ´ÑÑÑÐ¸Ð¹ ÑÐ°Ð· Ð±ÑÑÑÑÐµÐµ Ð¿ÑÐ¸Ð½Ð¸Ð¼Ð°Ð¹ÑÐµ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.BargainConfiguration.Success.Seller.Completed=ÐÐ°Ð¼ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¿ÑÐ¾Ð´Ð°ÑÑ Ð¿Ð¾ ÑÐ°Ð¼Ð¾Ð¹ Ð²ÑÐ³Ð¾Ð´Ð½Ð¾Ð¹ ÑÐµÐ½Ðµ!
service.BargainConfiguration.Success.Seller.Advice=Ð¢Ð¾ÑÐ³ÑÐ¹ÑÐµÑÑ Ð¸ Ð¿Ð¾Ð»ÑÑÐ°Ð¹ÑÐµ Ð²ÑÐ³Ð¾Ð´Ð½ÑÐµ Ð·Ð°ÐºÐ°Ð·Ñ Ð¾Ñ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»ÐµÐ¹
service.BargainConfiguration.Seller.CommonAdvice=Ð¡Ð¾Ð³Ð»Ð°ÑÐ°Ð¹ÑÐµÑÑ Ð½Ð° Ð¿ÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»ÐµÐ¹ Ð¸Ð»Ð¸ Ð¿ÑÐµÐ´Ð»Ð°Ð³Ð°Ð¹ÑÐµ ÑÐ²Ð¾Ñ ÑÐµÐ½Ñ. Ð¢Ð°Ðº Ð²Ñ Ð±ÑÑÑÑÐµÐµ Ð¿ÑÐ¾Ð´Ð°Ð´Ð¸ÑÐµ Ð²Ð°Ñ ÑÐ¾Ð²Ð°Ñ
service.BargainConfiguration.Seller.ItemWasSoldBeforeBuyerAnswered=Ð¢Ð¾Ð²Ð°Ñ Ð±ÑÐ» Ð¿ÑÐ¾Ð´Ð°Ð½ Ð´Ð¾ ÑÐ¾Ð³Ð¾, ÐºÐ°Ðº Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð¾ÑÐ²ÐµÑÐ¸Ð» Ð½Ð° ÑÐ¾ÑÐ³
service.BargainConfiguration.Seller.ItemWasSoldBeforeSellerAnswered=Ð¢Ð¾Ð²Ð°Ñ Ð±ÑÐ» Ð¿ÑÐ¾Ð´Ð°Ð½ Ð´Ð¾ ÑÐ¾Ð³Ð¾, ÐºÐ°Ðº Ð²Ñ ÑÐ¼Ð¾Ð³Ð»Ð¸ Ð¾ÑÐ²ÐµÑÐ¸ÑÑ Ð½Ð° ÑÐ¾ÑÐ³
service.BargainConfiguration.Seller.PriceWasChanged=ÐÑ Ð¿Ð¾Ð¼ÐµÐ½ÑÐ»Ð¸ ÑÐµÐ½Ñ ÑÐ¾Ð²Ð°ÑÐ°, Ð¿Ð¾ÑÑÐ¾Ð¼Ñ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ ÑÐ¾ÑÐ³Ð° Ð±ÑÐ»Ð¾ Ð¾ÑÐ¼ÐµÐ½ÐµÐ½Ð¾
service.BargainConfiguration.Seller.BuyerHasDeadline=Ð£ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ ÐµÑÑÑ {timeLeft}, ÑÑÐ¾Ð±Ñ ÐºÑÐ¿Ð¸ÑÑ ÑÐ¾Ð²Ð°Ñ Ð¿Ð¾ Ð½Ð¾Ð²Ð¾Ð¹ ÑÐµÐ½Ðµ

service.BargainConverter.YouReceive=ÐÑ Ð¿Ð¾Ð»ÑÑÐ¸ÑÐµ {0}
service.BargainConverter.Offer.Offer=Ð¯ Ð¿ÑÐµÐ´Ð»Ð°Ð³Ð°Ñ
service.BargainConverter.Offer.Controffer=ÐÐµ ÑÑÐ²ÐµÑÐ¶Ð´Ð°Ñ, Ð¿ÑÐµÐ´Ð»Ð°Ð³Ð°Ñ
service.BargainConverter.Accept.Accepted=ÐÑÐ¿Ð¸Ð»!
service.BargainConverter.Accept.PriceApproved=ÐÐ°ÑÐ° ÑÐµÐ½Ð° Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð°!
service.BargainConverter.Decline=ÐÐ°ÑÐ° ÑÐµÐ½Ð° Ð¾ÑÐºÐ»Ð¾Ð½ÐµÐ½Ð°!

service.BargainConverter.WelcomeMessage=ÐÑÐ¸Ð²ÐµÑ, ÐºÐ°ÐºÑÑ ÑÐµÐ½Ñ ÑÐ¾ÑÐ¸ÑÐµ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶Ð¸ÑÑ?

service.DefaultDiscountService.CertificateNotFound=Ð¡ÐµÑÑÐ¸ÑÐ¸ÐºÐ°Ñ Ñ Ð¸Ð´ÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¾ÑÐ¾Ð¼: {0} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
service.DefaultDiscountService.CertificateCouldntRedeemed=Ð¡ÐµÑÑÐ¸ÑÐ¸ÐºÐ°Ñ Ñ Ð¸Ð´ÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¾ÑÐ¾Ð¼: {0} Ð¾Ð¿Ð»Ð°ÑÐ¸ÑÑ Ð½ÐµÐ»ÑÐ·Ñ

service.DefaultDeeplinkService.Hermes=CÐ£ÐÐÐ HERMES
service.DefaultDeeplinkService.ForHer=ÐÐÐ¯ ÐÐÐ
service.DefaultDeeplinkService.ForHim=ÐÐÐ¯ ÐÐÐÐ
service.DefaultDeeplinkService.Favourites=ÐÐ·Ð±ÑÐ°Ð½Ð½Ð¾Ðµ

service.AmountValidator.NominalNotCorrect=ÐÐµÐ²ÐµÑÐ½ÑÐ¹ Ð½Ð¾Ð¼Ð¸Ð½Ð°Ð» ÑÐµÑÑÐ¸ÑÐ¸ÐºÐ°ÑÐ°

service.RecipientValidator.CertificateRecipientNameIsEmpty=ÐÐ¼Ñ Ð¿Ð¾Ð»ÑÑÐ°ÑÐµÐ»Ñ ÑÐµÑÑÐ¸ÑÐ¸ÐºÐ°ÑÐ° Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¿ÑÑÑÑÐ¼
service.RecipientValidator.DeliveryCompanyNotPicked=ÐÐµ Ð²ÑÐ±ÑÐ°Ð½ Ð¼ÐµÑÐ¾Ð´ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸ ÑÐµÑÑÐ¸ÑÐ¸ÐºÐ°ÑÐ°
service.RecipientValidator.DeliveryMethod=Ð ÐºÐ°ÑÐµÑÑÐ²Ðµ Ð¼ÐµÑÐ¾Ð´Ð° Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸ Ð¼Ð¾Ð¶Ð½Ð¾ Ð²ÑÐ±ÑÐ°ÑÑ Ð»Ð¸Ð±Ð¾ Ð¾ÑÐ¿ÑÐ°Ð²ÐºÑ ÑÐ»ÐµÐºÑÑÐ¾Ð½Ð½Ð¾Ð¹ ÐºÐ¾Ð¿Ð¸Ð¸ ÑÐµÑÑÐ¸ÑÐ¸ÐºÐ°ÑÐ° Ð¿Ð¾ ÑÐ»ÐµÐºÑÑÐ¾Ð½Ð½Ð¾Ð¹ Ð¿Ð¾ÑÑÐµ, Ð»Ð¸Ð±Ð¾ Ð´Ð¾ÑÑÐ°Ð²ÐºÑ ÐºÐ¾Ð¿Ð¸Ð¸ ÐºÑÑÑÐµÑÐ¾Ð¼, Ð½Ð¾ Ð½Ðµ Ð¾Ð±Ðµ Ð¾Ð´Ð½Ð¾Ð²ÑÐµÐ¼ÐµÐ½Ð½Ð¾
service.RecipientValidator.EmailIsEmpty=ÐÐ´ÑÐµÑ ÑÐ»ÐµÐºÑÑÐ¾Ð½Ð½Ð¾Ð¹ Ð¿Ð¾ÑÑÑ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¿ÑÑÑÑÐ¼
service.RecipientValidator.AddressIsEmpty=ÐÐ´ÑÐµÑ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¿ÑÑÑÑÐ¼

service.DefaultNotificationService.UserNotFound=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ñ Ð¸Ð´ÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¾ÑÐ¾Ð¼: {0} Ð½Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ
service.DefaultNotificationService.Duplicate=ÐÑÐ±Ð»Ð¸ÐºÐ°Ñ
service.DefaultNotificationService.NotificationDisabled=Ð£Ð²ÐµÐ´Ð¾Ð¼Ð»ÐµÐ½Ð¸Ðµ Ð²ÑÐºÐ»ÑÑÐµÐ½Ð¾
service.DefaultNotificationService.HasSchedule=Ð£Ð²ÐµÐ´Ð¾Ð¼Ð»ÐµÐ½Ð¸Ðµ Ð¸Ð¼ÐµÐµÑ ÑÐ²Ð¾Ðµ ÑÐ°ÑÐ¿Ð¸ÑÐ°Ð½Ð¸Ðµ
service.DefaultNotificationService.NotificationNotSent=ÐÐµ Ð¿ÐµÑÐµÐ´Ð°Ð½Ð¾ DTO ÑÐ²ÐµÐ´Ð¾Ð¼Ð»ÐµÐ½Ð¸Ñ
service.DefaultNotificationService.ClassNameNotSent=ÐÐµ Ð¿ÐµÑÐµÐ½Ð°Ð½Ð¾ Ð¸Ð¼Ñ ÐºÐ»Ð°ÑÑÐ°
service.DefaultNotificationService.WrongPackage=ÐÐµÐ²ÐµÑÐ½ÑÐ¹ Ð¿Ð°ÐºÐµÑ
service.DefaultNotificationService.UserIdNotSent=ÐÐµ Ð·Ð°Ð´Ð°Ð½ ID Ð¿Ð¾Ð»ÑÑÐ°ÑÐµÐ»Ñ
service.DefaultNotificationService.ClassNotFound=ÐÐµ Ð½Ð°Ð¹Ð´ÐµÐ½ ÐºÐ»Ð°ÑÑ: {0}
service.DefaultNotificationService.AccessErrorWhenCreateInstance=ÐÑÐ¸Ð±ÐºÐ° Ð´Ð¾ÑÑÑÐ¿Ð° Ð¿ÑÐ¸ ÑÐ¾Ð·Ð´Ð°Ð½Ð¸Ð¸ Ð¸Ð½ÑÑÐ°Ð½ÑÐ° {0}: {1}
service.DefaultNotificationService.ErrorWhenCreateInstance=ÐÑÐ¸Ð±ÐºÐ° ÑÐ¾Ð·Ð´Ð°Ð½Ð¸Ñ Ð¸Ð½ÑÑÐ°Ð½ÑÐ° {0}: {1}

service.LikedBrandProductPublishedNotificationRunner.From=Ð¾Ñ
service.LikedBrandProductPublishedNotificationRunner.AndMore=Ð¸ ÐµÑÐµ
service.LikedBrandProductPublishedNotificationRunner.FromBrands=Ð²Ð°ÑÐ¸Ñ Ð»ÑÐ±Ð¸Ð¼ÑÑ Ð±ÑÐµÐ½Ð´Ð¾Ð²
service.LikedBrandProductPublishedNotificationRunner.Online=ÑÐ¶Ðµ Ð¾Ð½Ð»Ð°Ð¹Ð½!

service.DefaultOrderService.Delivery.DeliveryDescription=ÐÐ½Ð»Ð°Ð¹Ð½ Ð¾ÑÑÐ»ÐµÐ¶Ð¸Ð²Ð°Ð½Ð¸Ðµ ÑÐµÑÐµÐ· ÐºÑÑÑÐµÑÑÐºÑÑ ÑÐ»ÑÐ¶Ð±Ñ Ð´Ð¾ÑÑÑÐ¿Ð½Ð¾ Ð¿Ð¾ÑÐ»Ðµ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ. Ð Ð´ÐµÐ½Ñ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸ ÐºÑÑÑÐµÑ Ñ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÑÑÑ Ð¸ ÑÐ¾Ð³Ð»Ð°ÑÑÐµÑ Ð²ÑÐµÐ¼Ñ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸.
service.DefaultOrderService.Delivery.DeliveryTitle=ÐÑÑÑÐµÑÑÐºÐ°Ñ ÑÐ»ÑÐ¶Ð±Ð° Oskelly
service.DefaultOrderService.Delivery.DeliveryInfo=ÐÑ Ð½Ð°ÑÐ¾Ð´Ð¸ÑÐµÑÑ Ð½ÐµÐ´Ð°Ð»ÐµÐºÐ¾ Ð¾Ñ Ð¾ÑÐ¸ÑÐ° OSKELLY, Ð¿Ð¾ÑÑÐ¾Ð¼Ñ Ð¼Ñ ÑÐ°Ð¼Ð¸ Ð´Ð¾ÑÑÐ°Ð²Ð¸Ð¼ ÑÐ¾Ð²Ð°Ñ, Ñ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÑÑÑ ÐºÑÑÑÐµÑ Ð¸ ÑÐ¾Ð³Ð»Ð°ÑÑÐµÑ Ð²ÑÐµÐ¼Ñ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸.
service.DefaultOrderService.Delivery.CourierName=ÐÐ³Ð¾ Ð·Ð¾Ð²ÑÑ
service.DefaultOrderService.Delivery.CourierPhone=Ð½Ð¾Ð¼ÐµÑ Ð¼Ð¾Ð±Ð¸Ð»ÑÐ½Ð¾Ð³Ð¾ ÑÐµÐ»ÐµÑÐ¾Ð½Ð°:
service.DefaultOrderService.Delivery.OnlineTracking=ÐÐ½Ð»Ð°Ð¹Ð½ Ð¾ÑÑÐ»ÐµÐ¶Ð¸Ð²Ð°Ð½Ð¸Ðµ
service.DefaultOrderService.Delivery.ByDeliveryCompany=ÑÐµÑÐµÐ· ÐºÑÑÑÐµÑÑÐºÑÑ ÑÐ»ÑÐ¶Ð±Ñ.
service.DefaultOrderService.Delivery.CourierConnectWithClient=Ð Ð´ÐµÐ½Ñ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸ ÐºÑÑÑÐµÑ Ñ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÑÑÑ Ð¸ ÑÐ¾Ð³Ð»Ð°ÑÑÐµÑ Ð²ÑÐµÐ¼Ñ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸.
service.DefaultOrderService.Delivery.Deadline.Text=ÐÑÐ¸ÐµÐ½ÑÐ¸ÑÐ¾Ð²Ð¾ÑÐ½ÑÐ¹ ÑÑÐ¾Ðº Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸ Ð² OSKELLY:
service.DefaultOrderService.Delivery.Deadline.Period=2-3 ÑÐ°Ð±Ð¾ÑÐ¸Ñ Ð´Ð½Ñ
service.DefaultOrderService.Delivery.DeadlineForBuyer=ÐÑÐ¸ÐµÐ½ÑÐ¸ÑÐ¾Ð²Ð¾ÑÐ½ÑÐ¹ ÑÑÐ¾Ðº Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ:
service.DefaultOrderService.Delivery.ApproveCounterparty=ÐÐ¾ÑÐ»Ðµ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð¼Ñ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸Ð¼ Ð²Ð°ÑÐ¸ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ
service.DefaultOrderService.Delivery.PickupInfo=ÐÑ Ð½Ð°ÑÐ¾Ð´Ð¸ÑÐµÑÑ Ð½ÐµÐ´Ð°Ð»ÐµÐºÐ¾ Ð¾Ñ Ð¾ÑÐ¸ÑÐ° OSKELLY, Ð¿Ð¾ÑÑÐ¾Ð¼Ñ Ð¼Ñ ÑÐ°Ð¼Ð¸ Ð´Ð¾ÑÑÐ°Ð²Ð¸Ð¼ ÑÐ¾Ð²Ð°Ñ, Ñ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÑÑÑ ÐºÑÑÑÐµÑ Ð¸ ÑÐ¾Ð³Ð»Ð°ÑÑÐµÑ Ð²ÑÐµÐ¼Ñ Ð·Ð°Ð±Ð¾ÑÐ°.

service.DefaultOrderService.Validation.NegativeCommissionInOrderPositions=ÐÐ°ÐºÐ°Ð· {0}: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¾ÑÐ¾ÑÐ¼Ð¸ÑÑ Ð·Ð°ÐºÐ°Ð· (OPNC)
service.DefaultOrderService.Validation.NoConciergesInBoutique=ÐÐ°ÐºÐ°Ð· {0}: Ð½ÐµÐ»ÑÐ·Ñ Ð´Ð¾Ð±Ð°Ð²Ð¸ÑÑ ÑÐ¾Ð²Ð°Ñ ÐºÐ¾Ð½ÑÑÐµÑÐ¶Ð° {1} Ð² Ð·Ð°ÐºÐ°Ð· Ð±ÑÑÐ¸ÐºÐ°
service.DefaultOrderService.Payment.OrderNotInDelivery=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ Ð¸Ð½Ð¸ÑÐ¸Ð¸ÑÐ¾Ð²Ð°ÑÑ Ð¾Ð¿Ð»Ð°ÑÑ. ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð¿ÐµÑÐµÐ´Ð°Ð½.
service.DefaultOrderService.Payment.NotYourOrder=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ Ð¸Ð½Ð¸ÑÐ¸Ð¸ÑÐ¾Ð²Ð°ÑÑ Ð¾Ð¿Ð»Ð°ÑÑ ÑÑÐ¶Ð¾Ð³Ð¾ Ð·Ð°ÐºÐ°Ð·Ð°
service.DefaultOrderService.Payment.WrongCard=ÐÐ°ÐºÐ°Ð· â{0}: Ð¾Ð¿Ð»Ð°ÑÐ° Ð¿ÑÐ¸Ð²ÑÐ·Ð°Ð½Ð½Ð¾Ð¹ ÐºÐ°ÑÑÐ¾Ð¹ Ð½Ðµ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÐµÑÑÑ (ÐºÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½Ñ: {1})
service.DefaultOrderService.Payment.WrongCounterparty=ÐÐ°ÐºÐ°Ð· â{0}: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¾Ð¿Ð»Ð°ÑÐ¸ÑÑ Ñ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°Ð½Ð¸ÐµÐ¼ ÑÐºÐ°Ð·Ð°Ð½Ð½ÑÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÐ¾Ð² ({1})
service.DefaultOrderService.Payment.OrderNotFound=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ Ð¸Ð½Ð¸ÑÐ¸Ð¸ÑÐ¾Ð²Ð°ÑÑ Ð¾Ð¿Ð»Ð°ÑÑ. ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½: #{0}
service.DefaultOrderService.Payment.AgentSellerCPsFail=ÐÐ°ÐºÐ°Ð· {0}: ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ° Ð½Ðµ Ð½Ð°ÑÑÑÐ¾ÐµÐ½Ñ
service.DefaultOrderService.Payment.TaxCalculationFail=ÐÐ°ÐºÐ°Ð· {0}: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¾ÑÐ¾ÑÐ¼Ð¸ÑÑ Ð·Ð°ÐºÐ°Ð· (PCNC)
service.DefaultOrderService.Payment.MultipleCurrencies=ÐÐ°ÐºÐ°Ð· {0}\: Ð¿ÑÐ¸ÑÑÑÑÑÐ²ÑÑÑ ÑÐ¾Ð²Ð°ÑÑ Ñ ÑÐ°Ð·Ð»Ð¸ÑÐ½Ð¾Ð¹ Ð²Ð°Ð»ÑÑÐ¾Ð¹ Ð¿ÑÐ±Ð»Ð¸ÐºÐ°ÑÐ¸Ð¸, Ð¿Ð¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, ÑÐ°Ð·Ð±ÐµÐ¹ÑÐµ Ð·Ð°ÐºÐ°Ð· Ð¿Ð¾ Ð¿Ð¾Ð·Ð¸ÑÐ¸ÑÐ¼
service.DefaultOrderService.Payment.CbAllowOneItemOnly=ÐÐ°ÐºÐ°Ð· {0}\: Ð¿ÑÐ¸ Ð´Ð¾ÑÑÐ°Ð²ÐºÐµ Ð¸Ð·-Ð·Ð° ÑÑÐ±ÐµÐ¶Ð° Ð² Ð·Ð°ÐºÐ°Ð·Ðµ Ð´Ð¾Ð¿ÑÑÐºÐ°ÐµÑÑÑ ÑÐ¾Ð»ÑÐºÐ¾ Ð¾Ð´Ð¸Ð½ ÑÐ¾Ð²Ð°Ñ, Ð¿Ð¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¾ÑÐ¾ÑÐ¼Ð¸ÑÐµ Ð¾ÑÐ´ÐµÐ»ÑÐ½ÑÐµ Ð·Ð°ÐºÐ°Ð·Ñ
service.DefaultOrderService.Payment.CbMinAppVersionFail=ÐÐ°ÐºÐ°Ð· {0}\: ÑÑÐ° Ð²ÐµÑÑÐ¸Ñ Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ Ð½Ðµ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÐµÑ Ð·Ð°ÐºÐ°Ð·Ñ Ð¸Ð·-Ð·Ð° ÑÑÐ±ÐµÐ¶Ð°, Ð¿Ð¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¾Ð±Ð½Ð¾Ð²Ð¸ÑÐµÑÑ Ð¸Ð»Ð¸ Ð¾ÑÐ¾ÑÐ¼Ð¸ÑÐµ Ð·Ð°ÐºÐ°Ð· Ð½Ð° ÑÐ°Ð¹ÑÐµ
service.DefaultOrderService.Payment.CbNotAllowPromocode=ÐÐ°ÐºÐ°Ð· {0}\: Ð¿ÑÐ¸ Ð´Ð¾ÑÑÐ°Ð²ÐºÐµ Ð¸Ð·-Ð·Ð° ÑÑÐ±ÐµÐ¶Ð° Ð² Ð·Ð°ÐºÐ°Ð·Ðµ Ð½Ðµ Ð´Ð¾Ð¿ÑÑÐºÐ°ÐµÑÑÑ Ð¿ÑÐ¸Ð¼ÐµÐ½ÐµÐ½Ð¸Ðµ Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´Ð°
service.DefaultOrderService.Payment.CbNotAllowBonuses=ÐÐ°ÐºÐ°Ð· {0}\: Ð¿ÑÐ¸ Ð´Ð¾ÑÑÐ°Ð²ÐºÐµ Ð¸Ð·-Ð·Ð° ÑÑÐ±ÐµÐ¶Ð° Ð² Ð·Ð°ÐºÐ°Ð·Ðµ Ð½Ðµ Ð´Ð¾Ð¿ÑÑÐºÐ°ÐµÑÑÑ Ð¿ÑÐ¸Ð¼ÐµÐ½ÐµÐ½Ð¸Ðµ Ð±Ð¾Ð½ÑÑÐ¾Ð²
service.DefaultOrderService.Payment.CbAllowOnlyEUR=ÐÐ°ÐºÐ°Ð· {0}\: Ð¿ÑÐ¸ Ð´Ð¾ÑÑÐ°Ð²ÐºÐµ Ð¸Ð·-Ð·Ð° ÑÑÐ±ÐµÐ¶Ð° Ð² Ð·Ð°ÐºÐ°Ð·Ðµ Ð½Ðµ Ð´Ð¾Ð¿ÑÑÐºÐ°ÐµÑÑÑ ÑÐµÐ½Ð° Ð² Ð²Ð°Ð»ÑÑÐµ Ð¾ÑÐ»Ð¸ÑÐ½Ð¾Ð¹ Ð¾Ñ EUR
service.DefaultOrderService.Payment.CbNotAllowFixedCommission=ÐÐ°ÐºÐ°Ð· {0}\: Ð¿ÑÐ¸ Ð´Ð¾ÑÑÐ°Ð²ÐºÐµ Ð¸Ð·-Ð·Ð° ÑÑÐ±ÐµÐ¶Ð° Ð² Ð·Ð°ÐºÐ°Ð·Ðµ Ð½Ðµ Ð´Ð¾Ð¿ÑÑÐºÐ°ÐµÑÑÑ ÑÐ¸ÐºÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð½Ð°Ñ ÐºÐ¾Ð¼Ð¸ÑÑÐ¸Ñ
service.DefaultOrderService.Payment.CbNotAllowCustomCommission=ÐÐ°ÐºÐ°Ð· {0}\: Ð¿ÑÐ¸ Ð´Ð¾ÑÑÐ°Ð²ÐºÐµ Ð¸Ð·-Ð·Ð° ÑÑÐ±ÐµÐ¶Ð° Ð² Ð·Ð°ÐºÐ°Ð·Ðµ Ð½Ðµ Ð´Ð¾Ð¿ÑÑÐºÐ°ÐµÑÑÑ Ð¿ÑÐ¾Ð¸Ð·Ð²Ð¾Ð»ÑÐ½Ð°Ñ ÐºÐ¾Ð¼Ð¸ÑÑÐ¸Ñ
service.DefaultOrderService.Payment.CbNotAllowBargain=ÐÐ°ÐºÐ°Ð· {0}\: Ð¿ÑÐ¸ Ð´Ð¾ÑÑÐ°Ð²ÐºÐµ Ð¸Ð·-Ð·Ð° ÑÑÐ±ÐµÐ¶Ð° Ð² Ð·Ð°ÐºÐ°Ð·Ðµ Ð½Ðµ Ð´Ð¾Ð¿ÑÑÐºÐ°ÐµÑÑÑ ÑÐ¾ÑÐ³
service.DefaultOrderService.OrderNotFound=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½: {0}
service.DefaultOrderService.Delete.NotYourOrder=ÐÐµÐ»ÑÐ·Ñ ÑÐ´Ð°Ð»Ð¸ÑÑ ÑÑÐ¶Ð¾Ð¹ Ð·Ð°ÐºÐ°Ð·
service.DefaultOrderService.Delete.ProblemsWithRemoving=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ ÑÐ´Ð°Ð»Ð¸ÑÑ Ð·Ð°ÐºÐ°Ð·: {0}, Ð¿Ð¾Ð²ÑÐ¾ÑÐ¸ÑÐµ Ð¿Ð¾Ð¿ÑÑÐºÑ Ð¿Ð¾Ð·Ð¶Ðµ
service.DefaultOrderService.NotEnoughRights=Ð£ ÐÐ°Ñ Ð½ÐµÑ Ð¿ÑÐ°Ð² Ð½Ð° Ð¿Ð¾Ð»ÑÑÐµÐ½Ð¸Ðµ Ð´Ð°Ð½Ð½ÑÑ Ð¾ Ð·Ð°ÐºÐ°Ð·Ðµ: {0}
service.DefaultOrderService.NotEnoughStatusRights=Ð£ ÐÐ°Ñ Ð½ÐµÑ Ð¿ÑÐ°Ð² Ð½Ð° Ð¿Ð¾Ð»ÑÑÐµÐ½Ð¸Ðµ Ð´Ð°Ð½Ð½ÑÑ Ð¾ Ð·Ð°ÐºÐ°Ð·Ðµ Ð² ÑÑÐ°ÑÑÑÐµ {0}
service.DefaultOrderService.Payment.WrongSchema=Ð¡ÑÐµÐ¼Ð° {0} Ð½Ðµ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÐµÑÑÑ Ð´Ð»Ñ Ð¾Ð±ÑÐ°Ð±Ð¾ÑÐºÐ¸ Ð·Ð°ÐºÐ°Ð·Ð¾Ð²
service.DefaultOrderService.GoodsWereUnpublished=Ð¢Ð¾Ð²Ð°Ñ Ð±ÑÐ» ÑÐ½ÑÑ Ñ Ð¿ÑÐ¾Ð´Ð°Ð¶, ÑÐ°Ðº ÐºÐ°Ðº Ð½Ðµ Ð±ÑÐ» Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½ Ð´Ð»Ñ Ð¿ÑÐ¾Ð´Ð°Ð¶Ð¸. Ð§ÑÐ¾Ð±Ñ Ð¸Ð·Ð±ÐµÐ¶Ð°ÑÑ ÑÐ¸ÑÑÐ°ÑÐ¸Ð¸ Ñ Ð¾ÑÐ¼ÐµÐ½Ð¾Ð¹ Ð·Ð°ÐºÐ°Ð·Ð°, ÑÑÐ°ÑÐ°Ð¹ÑÐµÑÑ Ð²Ð¾Ð²ÑÐµÐ¼Ñ Ð¾Ð±Ð½Ð¾Ð²Ð»ÑÑÑ Ð¾ÑÑÐ°ÑÐºÐ¸
service.DefaultOrderService.SellerDidntApprove=Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, Ð¿ÑÐ¾Ð´Ð°Ð²ÐµÑ Ð½Ðµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸Ð» Ð½Ð°Ð»Ð¸ÑÐ¸Ðµ ÑÐ¾Ð²Ð°ÑÐ°. Ð¡ÑÐµÐ´ÑÑÐ²Ð° Ð½Ð° Ð²Ð°ÑÐµÐ¹ Ð±Ð°Ð½ÐºÐ¾Ð²ÑÐºÐ¾Ð¹ ÐºÐ°ÑÑÐµ ÑÐ°Ð·Ð¼Ð¾ÑÐ¾Ð¶ÐµÐ½Ñ. ÐÑ ÑÐ°Ð±Ð¾ÑÐ°ÐµÐ¼ Ð½Ð°Ð´ ÑÐµÐ¼, ÑÑÐ¾Ð±Ñ ÑÐ°ÐºÐ¸Ðµ ÑÐ¸ÑÑÐ°ÑÐ¸Ð¸ ÑÐ»ÑÑÐ°Ð»Ð¸ÑÑ ÐºÐ°Ðº Ð¼Ð¾Ð¶Ð½Ð¾ ÑÐµÐ¶Ðµ
service.DefaultOrderService.SellerDeclinedOrder=ÐÑÐ¾Ð´Ð°Ð²ÐµÑ Ð½Ðµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸Ð» ÑÐ´ÐµÐ»ÐºÑ. ÐÑ ÑÐ°Ð·Ð¼Ð¾ÑÐ¾Ð·Ð¸Ð¼ Ð´ÐµÐ½ÑÐ³Ð¸ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 24 ÑÐ°ÑÐ¾Ð²
service.DefaultOrderService.HasUnapprovedPositions=ÐÑÑÑ Ð½ÐµÐ¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð½ÑÐµ Ð¿Ð¾Ð·Ð¸ÑÐ¸Ð¸
service.DefaultOrderService.SellerDidntApproveAnything=ÐÑÐ¾Ð´Ð°Ð²ÐµÑ Ð½Ðµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸Ð»
service.DefaultOrderService.UnholdMoneyAfterExpertise=ÐÑ ÑÐ°Ð·Ð¼Ð¾ÑÐ¾Ð·Ð¸Ð¼ Ð´ÐµÐ½ÑÐ³Ð¸ Ð·Ð° ÑÑÐ¾Ñ ÑÐ¾Ð²Ð°Ñ Ð¿Ð¾ÑÐ»Ðµ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ Ð·Ð°ÐºÐ°Ð·Ð°
service.DefaultOrderService.UnapprovedGoods=ÐÐµÐ¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð½ÑÐµ ÑÐ¾Ð²Ð°ÑÑ
service.DefaultOrderService.Goods=ÑÐ¾Ð²Ð°ÑÑ
service.DefaultOrderService.Item=ÑÐ¾Ð²Ð°Ñ
service.DefaultOrderService.TheseGoods=ÑÑÐ¸ ÑÐ¾Ð²Ð°ÑÑ
service.DefaultOrderService.ThisItem=ÑÑÐ¾Ñ ÑÐ¾Ð²Ð°Ñ
service.DefaultOrderService.GoodsWereRemoved=Ð¢Ð¾Ð²Ð°ÑÑ Ð±ÑÐ»Ð¸ ÑÐ½ÑÑÑ
service.DefaultOrderService.ItemWasRemoved=Ð¢Ð¾Ð²Ð°Ñ Ð±ÑÐ» ÑÐ½ÑÑ
service.DefaultOrderService.SellerDeclinedDescription=ÐÑÐ¾Ð´Ð°Ð²ÐµÑ Ð½Ðµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸Ð» {0}. ÐÑ ÑÐ°Ð±Ð¾ÑÐ°ÐµÐ¼ Ð½Ð°Ð´ ÑÐµÐ¼, ÑÑÐ¾Ð±Ñ ÑÐ°ÐºÐ¸Ðµ ÑÐ¸ÑÑÐ°ÑÐ¸Ð¸ ÑÐ»ÑÑÐ°Ð»Ð¸ÑÑ ÐºÐ°Ðº Ð¼Ð¾Ð¶Ð½Ð¾ ÑÐµÐ¶Ðµ. ÐÐ°ÑÐ¸ Ð´ÐµÐ½ÑÐ³Ð¸ Ð·Ð° {1} Ð±ÑÐ´ÑÑ ÑÐ°Ð·Ð¼Ð¾ÑÐ¾Ð¶ÐµÐ½Ñ Ð¿Ð¾ÑÐ»Ðµ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ Ð¾ÑÑÐ°Ð»ÑÐ½Ð¾Ð¹ ÑÐ°ÑÑÐ¸ Ð·Ð°ÐºÐ°Ð·Ð°.
service.DefaultOrderService.These=ÑÑÐ¸
service.DefaultOrderService.This=ÑÑÐ¾Ñ
service.DefaultOrderService.SoonWeTakeToExpertise=Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð·Ð°Ð±ÐµÑÐµÐ¼ ÐµÐ³Ð¾ Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ, Ð¾Ð¶Ð¸Ð´Ð°Ð¹ÑÐµ Ð·Ð²Ð¾Ð½ÐºÐ° ÐºÑÑÑÐµÑÐ°
service.DefaultOrderService.OnlineBoutiqueOrderError.CorrespondingBoutiqueOrderSold=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÑ Ð¾Ð½Ð»Ð°Ð¹Ð½ Ð·Ð°ÐºÐ°Ð· Ð½Ð° ÑÐ¾Ð²Ð°Ñ Ð² ÐÑÑÐ¸ÐºÐµ: ÑÐ²ÑÐ·Ð°Ð½Ð½ÑÐ¹ Ð·Ð°ÐºÐ°Ð· ÐÑÑÐ¸ÐºÐ° {0} ÑÐ¶Ðµ Ð¿ÑÐ¾Ð´Ð°Ð½.
service.DefaultOrderService.OnlineBoutiqueOrderError.BoutiqueHasNotAuthorityName=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÑ Ð¾Ð½Ð»Ð°Ð¹Ð½ Ð·Ð°ÐºÐ°Ð· {0} Ð½Ð° ÑÐ¾Ð²Ð°Ñ Ð² ÐÑÑÐ¸ÐºÐµ: ÐÐµÑ Ð¿ÑÐ°Ð² Ð´Ð»Ñ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ñ. ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸ÑÐµ Ð·Ð°ÐºÐ°Ð· Ð² Ð±ÑÑÐ¸Ðº.
service.DefaultOrderService.OnlineBoutiqueOrderError.BoutiqueHasNotOrderSourceInfo=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÑ Ð¾Ð½Ð»Ð°Ð¹Ð½ Ð·Ð°ÐºÐ°Ð· {0} Ð½Ð° ÑÐ¾Ð²Ð°Ñ Ð² ÐÑÑÐ¸ÐºÐµ: ÐÐµ ÑÐºÐ°Ð·Ð°Ð½ orderSourceInfo.
service.DefaultOrderService.OnlineBoutiqueOrderError.UnableToSaleOnlineFromLocation=ÐÐ°ÐºÐ°Ð· {0}: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÑ Ð¾Ð½Ð»Ð°Ð¹Ð½ Ð·Ð°ÐºÐ°Ð· (Ð² Ð·Ð°ÐºÐ°Ð·Ðµ Ð±ÑÑÐ¸ÐºÐ° {2} ÑÐºÐ°Ð·Ð°Ð½Ð¾ ÑÐ°ÑÐ¿Ð¾Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ [{1}], Ð¿ÑÐ¾Ð´Ð°Ð¶Ð¸ Ð·Ð°Ð¿ÑÐµÑÐµÐ½Ñ)
service.DefaultOrderService.OnlineBoutiqueOrderError.UnableToSaleOnlineWithNullAddr=ÐÐ°ÐºÐ°Ð· {0}: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÑ Ð¾Ð½Ð»Ð°Ð¹Ð½ Ð·Ð°ÐºÐ°Ð· (Ð² Ð·Ð°ÐºÐ°Ð·Ðµ Ð±ÑÑÐ¸ÐºÐ° {2} ÑÐºÐ°Ð·Ð°Ð½Ð¾ ÑÐ°ÑÐ¿Ð¾Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ [{1}], Ð½Ðµ ÑÐºÐ°Ð·Ð°Ð½ Ð°Ð´ÑÐµÑ)

service.DefaultOrderService.Delivery.DeliveryToOskelly=ÐÐ¾ÑÑÐ°Ð²ÐºÐ° Ð² OSKELLY
service.DefaultOrderService.Delivery.WeAreSoSorry=ÐÑ ÑÐ°Ð±Ð¾ÑÐ°ÐµÐ¼ Ð½Ð°Ð´ ÑÐµÐ¼, ÑÑÐ¾Ð±Ñ ÑÐ°ÐºÐ¸Ðµ ÑÐ¸ÑÑÐ°ÑÐ¸Ð¸ ÑÐ»ÑÑÐ°Ð»Ð¸ÑÑ ÐºÐ°Ðº Ð¼Ð¾Ð¶Ð½Ð¾ ÑÐµÐ¶Ðµ
service.DefaultOrderService.Delivery.SellerDidntGiveOrderToCourier=ÐÑÐ¾Ð´Ð°Ð²ÐµÑ Ð½Ðµ Ð¿ÐµÑÐµÐ´Ð°Ð» Ð·Ð°ÐºÐ°Ð· ÐºÑÑÑÐµÑÑ. ÐÑ ÑÐ°Ð·Ð¼Ð¾ÑÐ¾Ð·Ð¸Ð¼ Ð´ÐµÐ½ÑÐ³Ð¸ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 24 ÑÐ°ÑÐ¾Ð²
service.DefaultOrderService.Delivery.ItemWasRemovedBecauseNotShipped=Ð¢Ð¾Ð²Ð°Ñ Ð±ÑÐ» ÑÐ½ÑÑ Ñ Ð¿ÑÐ¾Ð´Ð°Ð¶, ÑÐ°Ðº ÐºÐ°Ðº Ð½Ðµ Ð±ÑÐ» Ð¾ÑÐ³ÑÑÐ¶ÐµÐ½ Ð´Ð»Ñ Ð¿ÑÐ¾Ð´Ð°Ð¶Ð¸. Ð ÑÐ»ÐµÐ´ÑÑÑÐ¸Ð¹ ÑÐ°Ð· Ð²Ñ Ð±ÑÐ´ÐµÑÐµ Ð·Ð°Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ñ
service.DefaultOrderService.Delivery.WaitingForCourier=ÐÐ¶Ð¸Ð´Ð°Ð¹ÑÐµ ÐºÑÑÑÐµÑÐ°
service.DefaultOrderService.Delivery.PickupDateInProcess=ÐÐ°ÑÐ° Ð¾ÑÐ³ÑÑÐ·ÐºÐ¸ Ð¾Ð±ÑÐ°Ð±Ð°ÑÑÐ²Ð°ÐµÑÑÑ

service.DefaultOrderService.Expertise.AdditionalInfo=Ð¡Ð¼Ð¾ÑÑÐµÑÑ Ð¿Ð¾Ð´ÑÐ¾Ð±Ð½Ð¾ÑÑÐ¸
service.DefaultOrderService.Expertise.Failed=Ð­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ð° Ð½Ðµ Ð¿ÑÐ¾Ð¹Ð´ÐµÐ½Ð°
service.DefaultOrderService.Expertise.UnholdMoney=ÐÑ ÑÐ°Ð·Ð¼Ð¾ÑÐ¾Ð·Ð¸Ð¼ Ð²Ð°ÑÐ¸ Ð´ÐµÐ½ÑÐ³Ð¸ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 24 ÑÐ°ÑÐ¾Ð²
service.DefaultOrderService.Expertise.OurManagerConnectWithYou=Ð¡ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÑÑÑ Ð½Ð°Ñ Ð¼ÐµÐ½ÐµÐ´Ð¶ÐµÑ, ÑÑÐ¾Ð±Ñ Ð²ÐµÑÐ½ÑÑÑ Ð²Ð°Ð¼ ÑÐ¾Ð²Ð°Ñ
service.DefaultOrderService.Expertise.PartiallyCompleted=Ð­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ð° Ð¿ÑÐ¾Ð¹Ð´ÐµÐ½Ð° Ð½Ðµ Ð¿Ð¾Ð»Ð½Ð¾ÑÑÑÑ
service.DefaultOrderService.Expertise.MultipleCompleted=Ð¿ÑÐ¾ÑÐ»Ð¸
service.DefaultOrderService.Expertise.SingleCompleted=Ð¿ÑÐ¾ÑÐµÐ»
service.DefaultOrderService.Expertise.ExpertiseNot=Ð­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ Ð½Ðµ
service.DefaultOrderService.Expertise.CompletedByHasDefects=Ð­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ð° Ð¿ÑÐ¾Ð¹Ð´ÐµÐ½Ð°, ÐµÑÑÑ Ð´ÐµÑÐµÐºÑÑ
service.DefaultOrderService.Expertise.YouGotDiscount=ÐÑ Ð¿Ð¾Ð»ÑÑÐ¸Ð»Ð¸ ÑÐºÐ¸Ð´ÐºÑ:
service.DefaultOrderService.Expertise.DefectWasFound=ÐÐ°Ð¹Ð´ÐµÐ½ Ð´ÐµÑÐµÐºÑ:
service.DefaultOrderService.Expertise.DecreasePrices=ÑÐ½Ð¸Ð¶Ð°ÐµÐ¼ ÑÐµÐ½Ñ Ð½Ð°
service.DefaultOrderService.Expertise.DiscountWillUnfreezed=Ð Ð°Ð·Ð¼ÐµÑ ÑÐºÐ¸Ð´ÐºÐ¸ ÑÐ°Ð·Ð¼Ð¾ÑÐ¾Ð·Ð¸ÑÑÑ Ð°Ð²ÑÐ¾Ð¼Ð°ÑÐ¸ÑÐµÑÐºÐ¸ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 24 ÑÐ°ÑÐ¾Ð²
service.DefaultOrderService.Expertise.DryCleaning=ÐÑÐ¾Ð²ÐµÐ´ÐµÐ½Ð° ÑÐ¸Ð¼ÑÐ¸ÑÑÐºÐ°
service.DefaultOrderService.Expertise.ProfitWillBeDecreased=ÐÐ°ÑÐ° Ð¿ÑÐ¸Ð±ÑÐ»Ñ Ð±ÑÐ´ÐµÑ ÑÐ½Ð¸Ð¶ÐµÐ½Ð° Ð½Ð°
service.DefaultOrderService.Expertise.OskellyExpertise=Ð­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ð° OSKELLY

service.DefaultOrderService.Delivery.ToBuyer=ÐÐ¾ÑÑÐ°Ð²ÐºÐ° Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.DefaultOrderService.Delivery.ToBoutique=ÐÐ¾ÑÑÐ°Ð²ÐºÐ° Ð² Ð±ÑÑÐ¸Ðº
service.DefaultOrderService.Delivery.WaitingForCourierSince=ÐÐ¶Ð¸Ð´Ð°Ð¹ÑÐµ ÐºÑÑÑÐµÑÐ° {0}
service.DefaultOrderService.Delivery.PaymentWillPaid=Ð¡ÑÐµÐ´ÑÑÐ²Ð° Ð²ÑÐ¿Ð»Ð°ÑÐµÐ½Ñ. ÐÐ¶Ð¸Ð´Ð°Ð¹ÑÐµ Ð¿Ð¾ÑÑÑÐ¿Ð»ÐµÐ½Ð¸Ñ ÑÑÐµÐ´ÑÑÐ² Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 24 ÑÐ°ÑÐ¾Ð²
service.DefaultOrderService.Delivery.PaymentWillReceived=ÐÐ¿Ð»Ð°ÑÐ° Ð¿Ð¾ÑÑÑÐ¿Ð¸Ñ ÑÐµÑÐµÐ· {0}
service.DefaultOrderService.Delivery.WaitingForPayment=ÐÐ¶Ð¸Ð´Ð°Ð¹ÑÐµ Ð·Ð°ÑÐ¸ÑÐ»ÐµÐ½Ð¸Ñ ÑÑÐµÐ´ÑÑÐ²
service.DefaultOrderService.Delivery.ApproveCounterpartyForPayment=ÐÐ¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÐµ ÑÐ²Ð¾Ð¸ Ð´Ð°Ð½Ð½ÑÐµ Ð´Ð»Ñ Ð¿Ð¾ÑÑÑÐ¿Ð»ÐµÐ½Ð¸Ñ Ð¾Ð¿Ð»Ð°ÑÑ. ÐÐ¿Ð»Ð°ÑÐ° Ð¿Ð¾ÑÑÑÐ¿Ð¸Ñ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 24 ÑÐ°ÑÐ¾Ð² Ð¿Ð¾ÑÐ»Ðµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ñ.
service.DefaultOrderService.Delivery.DeliveredToBoutique=ÐÐ¾ÑÑÐ°Ð²Ð»ÐµÐ½Ð¾ Ð² Ð±ÑÑÐ¸Ðº
service.DefaultOrderService.Delivery.SoldInBoutique=ÐÑÐ¾Ð´Ð°Ð½Ð¾ Ð² Ð±ÑÑÐ¸ÐºÐµ

service.DefaultOrderService.ActiveOrderNotFound=ÐÐµÑ Ð´ÐµÐ¹ÑÑÐ²ÑÑÑÐµÐ³Ð¾ Ð·Ð°ÐºÐ°Ð·Ð° {0}
service.DefaultOrderService.PaymentServiceDoesntSupportPrepayment=ÐÐ°ÐºÐ°Ð· â{0}: ÑÐµÑÐ²Ð¸Ñ Ð¾Ð¿Ð»Ð°ÑÑ {1} Ð½Ðµ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÐµÑ Ð¿ÑÐµÐ´Ð¾Ð¿Ð»Ð°ÑÑ
service.DefaultOrderService.ReceiptPaymentsDoesntSupport=ÐÐ°ÐºÐ°Ð· â{0}: ÑÐµÐºÐ¸ Ð¿ÑÐµÐ´Ð¾Ð¿Ð»Ð°ÑÑ Ð½Ðµ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÑÑÑÑ
service.DefaultOrderService.CouldntGetMoney=ÐÐ°ÐºÐ°Ð· â{0}: Ð¾ÑÐ¸Ð±ÐºÐ° Ð½Ð° Ð¾Ð¿ÐµÑÐ°ÑÐ¸Ð¸ ÑÐ¿Ð¸ÑÐ°Ð½Ð¸Ñ ÐÐ¡
service.DefaultOrderService.NoInStorePickupsInBtqe=ÐÐ°ÐºÐ°Ð· {0}: ÑÐ»Ð°Ð³ ÑÐ°Ð¼Ð¾Ð²ÑÐ²Ð¾Ð· Ð½Ðµ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÐµÑÑÑ Ð² Ð·Ð°ÐºÐ°Ð·Ð°Ñ Ð±ÑÑÐ¸ÐºÐ°
service.DefaultOrderService.PrepaymentAmountLessThanEffectiveAmount=ÐÐ°ÐºÐ°Ð· {0}: ÑÑÐ¼Ð¼Ð° Ð¿ÑÐµÐ´Ð¾Ð¿Ð»Ð°ÑÑ ({1}) Ð¼ÐµÐ½ÑÑÐµ Ð¸ÑÐ¾Ð³Ð¾Ð²Ð¾Ð¹ ÑÑÐ¼Ð¼Ñ Ð·Ð°ÐºÐ°Ð·Ð° ({2})
service.DefaultOrderService.PrepaymentWithNullCaptureAmount=ÐÐ°ÐºÐ°Ð· {0}: ÑÑÐ¼Ð¼Ð° Ð¿ÑÐµÐ´Ð¾Ð¿Ð»Ð°ÑÑ Ð½Ðµ ÑÐºÐ°Ð·Ð°Ð½Ð°  

service.DefaultOrderService.ReceiptResending.ReceiptDidntSent=ÐÐ°ÐºÐ°Ð· â{0}, Ð¿Ð¾Ð²ÑÐ¾ÑÐ½Ð°Ñ Ð¾ÑÐ¿ÑÐ°Ð²ÐºÐ° ÑÐµÐºÐ° Ð½ÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð°: Ð¾ÑÐ¿ÑÐ°Ð²ÐºÐ° ÑÐµÐºÐ° Ð½Ðµ Ð²ÑÐ¿Ð¾Ð»Ð½ÑÐ»Ð°ÑÑ
service.DefaultOrderService.ReceiptResending.ReceiptDataInOrder=ÐÐ°ÐºÐ°Ð· â{0}, Ð¿Ð¾Ð²ÑÐ¾ÑÐ½Ð°Ñ Ð¾ÑÐ¿ÑÐ°Ð²ÐºÐ° ÑÐµÐºÐ° Ð½ÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð°: Ð´Ð°Ð½Ð½ÑÐµ ÑÐµÐºÐ° Ð¿ÑÐ¸ÑÑÑÑÑÐ²ÑÑÑ Ð² Ð·Ð°ÐºÐ°Ð·Ðµ

service.DefaultOrderService.SomePositionsDidntExcludeFromAgentReport=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ Ð¿ÐµÑÐµÐ²ÐµÑÑÐ¸ Ð² Ð²Ð¾Ð·Ð²ÑÐ°Ñ. Ð¡Ð»ÐµÐ´ÑÑÑÐ¸Ðµ Ð¿Ð¾Ð·Ð¸ÑÐ¸Ð¸ Ð² Ð·Ð°ÐºÐ°Ð·Ðµ {0} Ð½Ðµ Ð¸ÑÐºÐ»ÑÑÐµÐ½Ñ Ð¸Ð· Ð¾ÑÑÐµÑÐ° Ð¾ Ð¿ÑÐ¾Ð´Ð°Ð¶Ðµ: {1}
service.DefaultOrderService.ProblemsWithPromocodeRedeem=ÐÐ°ÐºÐ°Ð· â{0}: Ð¾ÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð¿ÑÐ¸Ð¼ÐµÐ½ÐµÐ½Ð¸Ð¸ Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´Ð° {1}

service.DefaultOrderService.CouldntSetCounterparty=ÐÐ°ÐºÐ°Ð· â{0} (Ð¿ÑÐ¾Ð´Ð°Ð²ÐµÑ {1}): Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð·Ð°Ð´Ð°ÑÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ {2} (Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ {3})
service.DefaultOrderService.Profit1=ÐÑÐ¸Ð±ÑÐ»Ñ: {0} {1}
service.DefaultOrderService.Profit2=ÐÑÐ¸Ð±ÑÐ»Ñ: {0} {3} - {1} {3} = {2} {3}
service.DefaultOrderService.FundsPaid=Ð¡ÑÐµÐ´ÑÑÐ²Ð° Ð²ÑÐ¿Ð»Ð°ÑÐµÐ½Ñ
service.DefaultOrderService.ItemDelivered=Ð¢Ð¾Ð²Ð°Ñ Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½

service.NewAdminOrderService.delivery.hint.orders.no.confirmation=ÐÑÐ¾Ð´Ð°Ð²ÐµÑ Ð½Ðµ ÑÐºÐ°Ð·Ð°Ð» Ð´Ð°ÑÑ Ð¾ÑÐ³ÑÑÐ·ÐºÐ¸ (Ð·Ð°ÐºÐ°Ð· Ð½Ðµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½)

service.DeliverySectionTitleService.BuyerTitle=ÐÐ¾ÑÑÐ°Ð²ÐºÐ°
service.DeliverySectionTitleService.SellerNotStartedTitle=ÐÑÑÑÐµÑ Ð¿ÑÐ¸ÐµÐ´ÐµÑ
service.DeliverySectionTitleService.SellerStartedTitle=ÐÑÑÑÐµÑ Ð·Ð°Ð±ÑÐ°Ð» Ð·Ð°ÐºÐ°Ð·
service.DeliverySectionTitleService.SellerFailedTitle=ÐÑÑÑÐµÑ Ð´Ð¾Ð»Ð¶ÐµÐ½ Ð±ÑÐ» Ð¿ÑÐ¸ÐµÑÐ°ÑÑ

service.OrderUITextsService.EstimatedDeliveryDate=ÐÑÐ¸ÐµÐ½ÑÐ¸ÑÐ¾Ð²Ð¾ÑÐ½Ð°Ñ Ð´Ð°ÑÐ° Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸ {0}

service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.title.Buyer=ÐÐ¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ðµ Ð·Ð°ÐºÐ°Ð·Ð°
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.DEAL_INITIATED.description.Buyer=ÐÑ Ð¾Ð¿Ð¾Ð²ÐµÑÑÐ¸Ð»Ð¸ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ° Ð¾ Ð²Ð°ÑÐµÐ¹ Ð¿Ð¾ÐºÑÐ¿ÐºÐµ, Ð¾Ð¶Ð¸Ð´Ð°Ð¹ÑÐµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ñ Ð·Ð°ÐºÐ°Ð·Ð°
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.CONFIRMED.description.Buyer=ÐÑÐ¾Ð´Ð°Ð²ÐµÑ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸Ð» Ð²Ð°Ñ Ð·Ð°ÐºÐ°Ð·
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.CONFIRMED_PARTLY.positionDescription.positive.Buyer=ÐÑÐ¾Ð´Ð°Ð²ÐµÑ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸Ð» ÑÐ¾Ð²Ð°Ñ <b>{0}</b>
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.CONFIRMED_PARTLY.positionDescription.negative.Buyer=<p>ÐÑÐ¾Ð´Ð°Ð²ÐµÑ Ð¾ÑÐ¼ÐµÐ½Ð¸Ð» ÑÐ°ÑÑÑ Ð·Ð°ÐºÐ°Ð·Ð° - <b>{0}</b>.</p>\n<p>ÐÐµÐ½ÑÐ³Ð¸ Ð·Ð° Ð¾ÑÐ¼ÐµÐ½Ð½ÑÐ¹ ÑÐ¾Ð²Ð°Ñ Ð²ÐµÑÐ½ÑÑÑÑ, ÐºÐ¾Ð³Ð´Ð° Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð½ÑÐµ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð¼ Ð¿Ð¾Ð·Ð¸ÑÐ¸Ð¸ Ð¿ÑÐ¾Ð¹Ð´ÑÑ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ</p>
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.SALE_REJECTED.description.Buyer.singular=ÐÑÐ¾Ð´Ð°Ð²ÐµÑ Ð¾ÑÐ¼ÐµÐ½Ð¸Ð» Ð·Ð°ÐºÐ°Ð·. ÐÐµÐ½ÑÐ³Ð¸ Ð·Ð° Ð·Ð°ÐºÐ°Ð· Ð¼Ñ Ð²ÐµÑÐ½ÑÐ»Ð¸ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.SALE_REJECTED.description.Buyer.plural=ÐÑÐ¾Ð´Ð°Ð²ÐµÑ Ð¾ÑÐ¼ÐµÐ½Ð¸Ð» Ð·Ð°ÐºÐ°Ð·. ÐÐµÐ½ÑÐ³Ð¸ Ð·Ð° Ð·Ð°ÐºÐ°Ð· Ð¼Ñ Ð²ÐµÑÐ½ÑÐ»Ð¸ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.comment.Buyer=ÐÑÐ¾Ð´Ð°Ð²ÐµÑ Ð¾ÑÐ²ÐµÑÐ¸Ñ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 24 ÑÐ°ÑÐ¾Ð²

service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.title.Seller=ÐÐ¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ðµ Ð·Ð°ÐºÐ°Ð·Ð°
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.DEAL_INITIATED.description.Seller=
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.CONFIRMED.description.Seller=ÐÑ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸Ð»Ð¸ Ð·Ð°ÐºÐ°Ð·
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.CONFIRMED_PARTLY.positionDescription.positive.Seller=ÐÑ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸Ð»Ð¸ ÑÐ¾Ð²Ð°Ñ <b>{0}</b>
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.CONFIRMED_PARTLY.positionDescription.negative.Seller=ÐÑ Ð¾ÑÐ¼ÐµÐ½Ð¸Ð»Ð¸ ÑÐ¾Ð²Ð°Ñ <b>{0}</b>. ÐÑ ÑÐ½ÑÐ»Ð¸ Ð²ÐµÑÑ Ñ Ð¿ÑÐ¾Ð´Ð°Ð¶Ð¸. ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¿ÑÐ¾Ð²ÐµÑÑÑÐµ Ð´ÑÑÐ³Ð¸Ðµ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð½ÑÐµ ÑÐ¾Ð²Ð°ÑÑ. Ð¡ÐºÑÐ¾Ð¹ÑÐµ ÑÐ¾, ÑÑÐ¾ Ð½Ðµ Ð¿ÑÐ¾Ð´Ð°ÐµÑÐµ, ÑÑÐ¾Ð±Ñ Ð½Ðµ ÑÐ°ÑÑÑÑÐ°Ð¸Ð²Ð°ÑÑ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»ÐµÐ¹
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.SALE_REJECTED.description.Seller.singular=ÐÑ Ð¾ÑÐ¼ÐµÐ½Ð¸Ð»Ð¸ Ð·Ð°ÐºÐ°Ð·. ÐÑ ÑÐ½ÑÐ»Ð¸ Ð²ÐµÑÑ Ñ Ð¿ÑÐ¾Ð´Ð°Ð¶Ð¸. ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¿ÑÐ¾Ð²ÐµÑÑÑÐµ Ð´ÑÑÐ³Ð¸Ðµ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð½ÑÐµ ÑÐ¾Ð²Ð°ÑÑ. Ð¡ÐºÑÐ¾Ð¹ÑÐµ ÑÐ¾, ÑÑÐ¾ Ð½Ðµ Ð¿ÑÐ¾Ð´Ð°ÐµÑÐµ, ÑÑÐ¾Ð±Ñ Ð½Ðµ ÑÐ°ÑÑÑÑÐ°Ð¸Ð²Ð°ÑÑ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»ÐµÐ¹
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.SALE_REJECTED.description.Seller.plural=ÐÑ Ð¾ÑÐ¼ÐµÐ½Ð¸Ð»Ð¸ Ð·Ð°ÐºÐ°Ð·. ÐÑ ÑÐ½ÑÐ»Ð¸ Ð²ÐµÑÐ¸ Ñ Ð¿ÑÐ¾Ð´Ð°Ð¶Ð¸. ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¿ÑÐ¾Ð²ÐµÑÑÑÐµ Ð´ÑÑÐ³Ð¸Ðµ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð½ÑÐµ ÑÐ¾Ð²Ð°ÑÑ. Ð¡ÐºÑÐ¾Ð¹ÑÐµ ÑÐ¾, ÑÑÐ¾ Ð½Ðµ Ð¿ÑÐ¾Ð´Ð°ÐµÑÐµ, ÑÑÐ¾Ð±Ñ Ð½Ðµ ÑÐ°ÑÑÑÑÐ°Ð¸Ð²Ð°ÑÑ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»ÐµÐ¹


service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.title.Seller=ÐÐ¾ÑÑÐ°Ð²ÐºÐ° Ð² OSKELLY
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.disabledDescription.Seller.singular=ÐÑ Ð·Ð°Ð±ÐµÑÐµÐ¼ Ð·Ð°ÐºÐ°Ð· Ð¾Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ° Ð¸ Ð¾ÑÐ²ÐµÐ·ÐµÐ¼ Ð² Ð½Ð°Ñ Ð¾ÑÐ¸Ñ Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.disabledDescription.Seller.plural=ÐÑ Ð·Ð°Ð±ÐµÑÐµÐ¼ Ð·Ð°ÐºÐ°Ð· Ð¾Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ° Ð¸ Ð¾ÑÐ²ÐµÐ·ÐµÐ¼ Ð² Ð½Ð°Ñ Ð¾ÑÐ¸Ñ Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.WAITING_FOR_DELIVERY_FROM_SELLER.description.Seller=ÐÑÑÑÐµÑ Ð¿ÑÐ¸ÐµÐ´ÐµÑ Ðº Ð²Ð°Ð¼ {0}. ÐÐ° ÑÐ°Ñ Ð´Ð¾ Ð²ÑÑÑÐµÑÐ¸ Ð¾Ð½ Ð²Ð°Ð¼ Ð¿Ð¾Ð·Ð²Ð¾Ð½Ð¸Ñ
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.PICKING_UP_FROM_SELLER.description.Seller=ÐÑÑÑÐµÑ Ð¿ÑÐ¸ÐµÐ´ÐµÑ Ðº Ð²Ð°Ð¼ {0}. ÐÐ° ÑÐ°Ñ Ð´Ð¾ Ð²ÑÑÑÐµÑÐ¸ Ð¾Ð½ Ð²Ð°Ð¼ Ð¿Ð¾Ð·Ð²Ð¾Ð½Ð¸Ñ
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.FROM_SELLER_TO_OFFICE.description.Seller=ÐÐ°ÐºÐ°Ð· ÐµÐ´ÐµÑ Ðº Ð½Ð°Ð¼ Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.description.trackingPart.Seller=Ð¡Ð»ÐµÐ´Ð¸ÑÐµ Ð·Ð° Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¾Ð¹ Ð¿Ð¾ ÑÑÐµÐº-Ð½Ð¾Ð¼ÐµÑÑ {0}
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.PICKUP_DECLINED.description.Seller.singular=Ð¡Ð´ÐµÐ»ÐºÐ° Ð¾ÑÐ¼ÐµÐ½ÐµÐ½Ð°, Ð¼Ñ ÑÐ½ÑÐ»Ð¸ Ð²ÐµÑÑ Ñ Ð¿ÑÐ¾Ð´Ð°Ð¶Ð¸. ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¿ÑÐ¾Ð²ÐµÑÑÑÐµ Ð´ÑÑÐ³Ð¸Ðµ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð½ÑÐµ ÑÐ¾Ð²Ð°ÑÑ. Ð¡ÐºÑÐ¾Ð¹ÑÐµ ÑÐ¾, ÑÑÐ¾ Ð½Ðµ Ð¿ÑÐ¾Ð´Ð°ÐµÑÐµ, ÑÑÐ¾Ð±Ñ Ð½Ðµ ÑÐ°ÑÑÑÑÐ°Ð¸Ð²Ð°ÑÑ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»ÐµÐ¹
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.PICKUP_DECLINED.description.Seller.plural=Ð¡Ð´ÐµÐ»ÐºÐ° Ð¾ÑÐ¼ÐµÐ½ÐµÐ½Ð°, Ð¼Ñ ÑÐ½ÑÐ»Ð¸ Ð²ÐµÑÐ¸ Ñ Ð¿ÑÐ¾Ð´Ð°Ð¶Ð¸. ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¿ÑÐ¾Ð²ÐµÑÑÑÐµ Ð´ÑÑÐ³Ð¸Ðµ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð½ÑÐµ ÑÐ¾Ð²Ð°ÑÑ. Ð¡ÐºÑÐ¾Ð¹ÑÐµ ÑÐ¾, ÑÑÐ¾ Ð½Ðµ Ð¿ÑÐ¾Ð´Ð°ÐµÑÐµ, ÑÑÐ¾Ð±Ñ Ð½Ðµ ÑÐ°ÑÑÑÑÐ°Ð¸Ð²Ð°ÑÑ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»ÐµÐ¹
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.FROM_SELLER_DELIVERY_FAIL.description.Seller.singular=<p>Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, Ð¼Ñ Ð½Ðµ Ð¿Ð¾Ð»ÑÑÐ¸Ð»Ð¸ Ð²ÐµÑÑ. ÐÑ ÑÑÐ°ÑÐ°ÐµÐ¼ÑÑ Ð´ÐµÐ»Ð°ÑÑ Ð²ÑÐµ, ÑÑÐ¾Ð±Ñ ÑÐ°ÐºÐ¸Ñ ÑÐ¸ÑÑÐ°ÑÐ¸Ð¹ Ð±ÑÐ»Ð¾ Ð¼ÐµÐ½ÑÑÐµ, Ð½Ð¾ Ð½Ðµ Ð²ÑÐµÐ³Ð´Ð° Ð¼Ð¾Ð¶ÐµÐ¼ Ð¸Ñ Ð¿ÑÐµÐ´Ð²Ð¸Ð´ÐµÑÑ.</p>\n<p>Ð£Ð¶Ðµ Ð¸Ð·Ð²Ð¸Ð½Ð¸Ð»Ð¸ÑÑ Ð¿ÐµÑÐµÐ´ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»ÐµÐ¼, Ð° Ð²Ð°Ð¼ Ð¿ÐµÑÐµÐ²ÐµÐ»Ð¸ Ð´ÐµÐ½ÑÐ³Ð¸, ÐºÐ¾ÑÐ¾ÑÑÐµ Ð²Ñ Ð´Ð¾Ð»Ð¶Ð½Ñ Ð±ÑÐ»Ð¸ Ð¿Ð¾Ð»ÑÑÐ¸ÑÑ</p>
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.FROM_SELLER_DELIVERY_FAIL.description.Seller.plural=<p>Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, Ð¼Ñ Ð½Ðµ Ð¿Ð¾Ð»ÑÑÐ¸Ð»Ð¸ Ð²ÐµÑÑ. ÐÑ ÑÑÐ°ÑÐ°ÐµÐ¼ÑÑ Ð´ÐµÐ»Ð°ÑÑ Ð²ÑÐµ, ÑÑÐ¾Ð±Ñ ÑÐ°ÐºÐ¸Ñ ÑÐ¸ÑÑÐ°ÑÐ¸Ð¹ Ð±ÑÐ»Ð¾ Ð¼ÐµÐ½ÑÑÐµ, Ð½Ð¾ Ð½Ðµ Ð²ÑÐµÐ³Ð´Ð° Ð¼Ð¾Ð¶ÐµÐ¼ Ð¸Ñ Ð¿ÑÐµÐ´Ð²Ð¸Ð´ÐµÑÑ.</p>\n<p>Ð£Ð¶Ðµ Ð¸Ð·Ð²Ð¸Ð½Ð¸Ð»Ð¸ÑÑ Ð¿ÐµÑÐµÐ´ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»ÐµÐ¼, Ð° Ð²Ð°Ð¼ Ð¿ÐµÑÐµÐ²ÐµÐ»Ð¸ Ð´ÐµÐ½ÑÐ³Ð¸, ÐºÐ¾ÑÐ¾ÑÑÐµ Ð²Ñ Ð´Ð¾Ð»Ð¶Ð½Ñ Ð±ÑÐ»Ð¸ Ð¿Ð¾Ð»ÑÑÐ¸ÑÑ</p>
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.DELIVERED_TO_EXPERTISE.description.Seller=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¸ÐµÑÐ°Ð» Ð² Ð¾ÑÐ¸Ñ Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.CANCELLED.description.Seller=ÐÐ°ÐºÐ°Ð· Ð¾ÑÐ¼ÐµÐ½ÐµÐ½

service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.title.Buyer=ÐÐ¾ÑÑÐ°Ð²ÐºÐ° Ð² OSKELLY
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.disabledDescription.Buyer.singular=ÐÑ Ð·Ð°Ð±ÐµÑÐµÐ¼ Ð·Ð°ÐºÐ°Ð· Ð¾Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ° Ð¸ Ð¾ÑÐ²ÐµÐ·ÐµÐ¼ Ð² Ð½Ð°Ñ Ð¾ÑÐ¸Ñ Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.disabledDescription.Buyer.plural=ÐÑ Ð·Ð°Ð±ÐµÑÐµÐ¼ Ð·Ð°ÐºÐ°Ð· Ð¾Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ° Ð¸ Ð¾ÑÐ²ÐµÐ·ÐµÐ¼ Ð² Ð½Ð°Ñ Ð¾ÑÐ¸Ñ Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.WAITING_FOR_DELIVERY_FROM_SELLER.description.Buyer=Ð¡ÐºÐ¾ÑÐ¾ ÐºÑÑÑÐµÑ Ð·Ð°Ð±ÐµÑÐµÑ Ð²Ð°ÑÑ Ð¿Ð¾ÐºÑÐ¿ÐºÑ Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ° Ð¸ Ð¿ÑÐ¸Ð²ÐµÐ·ÐµÑ Ð² Ð½Ð°Ñ Ð¾ÑÐ¸Ñ Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.PICKING_UP_FROM_SELLER.description.Buyer=ÐÑÑÑÐµÑ ÐµÐ´ÐµÑ Ðº Ð¿ÑÐ¾Ð´Ð°Ð²ÑÑ, ÑÑÐ¾Ð±Ñ Ð·Ð°Ð±ÑÐ°ÑÑ Ð·Ð°ÐºÐ°Ð· Ð¸ Ð¾ÑÐ²ÐµÐ·ÑÐ¸ Ð² Ð½Ð°Ñ Ð¾ÑÐ¸Ñ Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.FROM_SELLER_TO_OFFICE.description.Buyer=ÐÑÑÑÐµÑ Ð·Ð°Ð±ÑÐ°Ð» Ð·Ð°ÐºÐ°Ð· Ð¸ Ð²ÐµÐ·ÐµÑ ÐµÐ³Ð¾ Ðº Ð½Ð°Ð¼ Ð² Ð¾ÑÐ¸Ñ Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.PICKUP_DECLINED.description.Buyer.singular=<p>ÐÑÐ¾Ð´Ð°Ð²ÐµÑ Ð¾ÑÐ¼ÐµÐ½Ð¸Ð» Ð´Ð¾ÑÑÐ°Ð²ÐºÑ. ÐÑ ÑÑÐ°ÑÐ°ÐµÐ¼ÑÑ Ð´ÐµÐ»Ð°ÑÑ Ð²ÑÐµ, ÑÑÐ¾Ð±Ñ ÑÐ°ÐºÐ¸Ñ ÑÐ¸ÑÑÐ°ÑÐ¸Ð¹ Ð±ÑÐ»Ð¾ Ð¼ÐµÐ½ÑÑÐµ, Ð½Ð¾ Ð½Ðµ Ð²ÑÐµÐ³Ð´Ð° Ð¼Ð¾Ð¶ÐµÐ¼ Ð¸Ñ Ð¿ÑÐµÐ´Ð²Ð¸Ð´ÐµÑÑ.</p>\n<p>ÐÐµÐ½ÑÐ³Ð¸ Ð·Ð° Ð·Ð°ÐºÐ°Ð· Ð¼Ñ Ð²ÐµÑÐ½ÑÐ»Ð¸ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ</p>
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.PICKUP_DECLINED.description.Buyer.plural=<p>ÐÑÐ¾Ð´Ð°Ð²ÐµÑ Ð¾ÑÐ¼ÐµÐ½Ð¸Ð» Ð´Ð¾ÑÑÐ°Ð²ÐºÑ. ÐÑ ÑÑÐ°ÑÐ°ÐµÐ¼ÑÑ Ð´ÐµÐ»Ð°ÑÑ Ð²ÑÐµ, ÑÑÐ¾Ð±Ñ ÑÐ°ÐºÐ¸Ñ ÑÐ¸ÑÑÐ°ÑÐ¸Ð¹ Ð±ÑÐ»Ð¾ Ð¼ÐµÐ½ÑÑÐµ, Ð½Ð¾ Ð½Ðµ Ð²ÑÐµÐ³Ð´Ð° Ð¼Ð¾Ð¶ÐµÐ¼ Ð¸Ñ Ð¿ÑÐµÐ´Ð²Ð¸Ð´ÐµÑÑ.</p>\n<p>ÐÐµÐ½ÑÐ³Ð¸ Ð·Ð° Ð·Ð°ÐºÐ°Ð· Ð¼Ñ Ð²ÐµÑÐ½ÑÐ»Ð¸ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ</p>
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.FROM_SELLER_DELIVERY_FAIL.description.Buyer.singular=<p>Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, Ð¼Ñ Ð½Ðµ Ð¿Ð¾Ð»ÑÑÐ¸Ð»Ð¸ Ð²ÐµÑÑ. ÐÑ ÑÑÐ°ÑÐ°ÐµÐ¼ÑÑ Ð´ÐµÐ»Ð°ÑÑ Ð²ÑÐµ, ÑÑÐ¾Ð±Ñ ÑÐ°ÐºÐ¸Ñ ÑÐ¸ÑÑÐ°ÑÐ¸Ð¹ Ð±ÑÐ»Ð¾ Ð¼ÐµÐ½ÑÑÐµ, Ð½Ð¾ Ð½Ðµ Ð²ÑÐµÐ³Ð´Ð° Ð¼Ð¾Ð¶ÐµÐ¼ Ð¸Ñ Ð¿ÑÐµÐ´Ð²Ð¸Ð´ÐµÑÑ.</p>\n<p>ÐÐµÐ½ÑÐ³Ð¸ Ð·Ð° Ð·Ð°ÐºÐ°Ð· Ð¼Ñ Ð²ÐµÑÐ½ÑÐ»Ð¸ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ</p>
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.FROM_SELLER_DELIVERY_FAIL.description.Buyer.plural=<p>Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, Ð¼Ñ Ð½Ðµ Ð¿Ð¾Ð»ÑÑÐ¸Ð»Ð¸ Ð²ÐµÑÐ¸. ÐÑ ÑÑÐ°ÑÐ°ÐµÐ¼ÑÑ Ð´ÐµÐ»Ð°ÑÑ Ð²ÑÐµ, ÑÑÐ¾Ð±Ñ ÑÐ°ÐºÐ¸Ñ ÑÐ¸ÑÑÐ°ÑÐ¸Ð¹ Ð±ÑÐ»Ð¾ Ð¼ÐµÐ½ÑÑÐµ, Ð½Ð¾ Ð½Ðµ Ð²ÑÐµÐ³Ð´Ð° Ð¼Ð¾Ð¶ÐµÐ¼ Ð¸Ñ Ð¿ÑÐµÐ´Ð²Ð¸Ð´ÐµÑÑ.</p>\n<p>ÐÐµÐ½ÑÐ³Ð¸ Ð·Ð° Ð·Ð°ÐºÐ°Ð· Ð¼Ñ Ð²ÐµÑÐ½ÑÐ»Ð¸ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ</p>
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.DELIVERED_TO_EXPERTISE.description.Buyer=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¸ÐµÑÐ°Ð» Ð² Ð¾ÑÐ¸Ñ Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.CANCELLED.description.Buyer=ÐÐ°ÐºÐ°Ð· Ð¾ÑÐ¼ÐµÐ½ÐµÐ½
Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, Ð¼Ñ Ð½Ðµ Ð¿Ð¾Ð»ÑÑÐ¸Ð»Ð¸ Ð²ÐµÑÑ. ÐÑ ÑÑÐ°ÑÐ°ÐµÐ¼ÑÑ Ð´ÐµÐ»Ð°ÑÑ Ð²ÑÐµ, ÑÑÐ¾Ð±Ñ ÑÐ°ÐºÐ¸Ñ ÑÐ¸ÑÑÐ°ÑÐ¸Ð¹ Ð±ÑÐ»Ð¾ Ð¼ÐµÐ½ÑÑÐµ, Ð½Ð¾ Ð½Ðµ Ð²ÑÐµÐ³Ð´Ð° Ð¼Ð¾Ð¶ÐµÐ¼ Ð¸Ñ Ð¿ÑÐµÐ´Ð²Ð¸Ð´ÐµÑÑ.

ÐÐµÐ½ÑÐ³Ð¸ Ð·Ð°Â Ð·Ð°ÐºÐ°Ð· Ð¼Ñ Ð²ÐµÑÐ½ÑÐ»Ð¸ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ. 

service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.comment.WillBeDeliveredOnDate=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¸ÐµÐ´ÐµÑ Ð² Ð¾ÑÐ¸Ñ ~ {0}
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.comment.WillBeDeliveredOnDateRange=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¸ÐµÐ´ÐµÑ Ð² Ð¾ÑÐ¸Ñ {0}

service.RejectHistoryServiceImpl.rejectStatusExtension.FAKE=Ð¤ÐµÐ¹Ðº
service.RejectHistoryServiceImpl.rejectStatusExtension.RECONCILIATION_WITH_SELLER_AND_BUYER_NEGATIVE=ÐÐµ Ð¿ÑÐ¾ÑÐ»Ð¾ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ

# Fallback expertise
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.title.Seller=Ð­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ð° OSKELLY
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.disabledDescription.Seller.singular=ÐÑ Ð¿ÑÐ¾Ð²ÐµÑÐ¸Ð¼ Ð²ÐµÑÑ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. ÐÑÐ»Ð¸ Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾, Ð¿Ð¾ÑÐ¸ÑÑÐ¸Ð¼, Ð¾ÑÐ¿Ð°ÑÐ¸Ð¼ Ð¸ Ð¿ÑÐ¸Ð²ÐµÐ´ÐµÐ¼ ÐµÐµ Ð² Ð¿Ð¾ÑÑÐ´Ð¾Ðº
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.disabledDescription.Seller.plural=ÐÑ Ð¿ÑÐ¾Ð²ÐµÑÐ¸Ð¼ Ð²ÐµÑÐ¸ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. ÐÑÐ»Ð¸ Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾, Ð¿Ð¾ÑÐ¸ÑÑÐ¸Ð¼, Ð¾ÑÐ¿Ð°ÑÐ¸Ð¼ Ð¸ Ð¿ÑÐ¸Ð²ÐµÐ´ÐµÐ¼ Ð¸Ñ Ð² Ð¿Ð¾ÑÑÐ´Ð¾Ðº
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.EXPERTISE_PASSED.description.Seller=ÐÐ°ÑÐ¸ ÑÐ¾Ð²Ð°ÑÑ ÑÑÐ¿ÐµÑÐ½Ð¾ Ð¿ÑÐ¾ÑÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.EXPERTISE_FAILED.description.Seller=ÐÐ¸ Ð¾Ð´Ð¸Ð½ Ð¸Ð· ÑÐ¾Ð²Ð°ÑÐ¾Ð² Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.DELIVERED_TO_EXPERTISE.description.Seller=ÐÐ°ÑÐ¸ ÑÐ¾Ð²Ð°ÑÑ Ð¿ÑÐ¾ÑÐ¾Ð´ÑÑ ÐºÐ¾Ð½ÑÑÐ¾Ð»Ñ ÐºÐ°ÑÐµÑÑÐ²Ð°

service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.title.Buyer=Ð­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ð° OSKELLY
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.disabledDescription.Buyer.singular=ÐÑ Ð¿ÑÐ¾Ð²ÐµÑÐ¸Ð¼ Ð²ÐµÑÑ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. ÐÑÐ»Ð¸ Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾, Ð¿Ð¾ÑÐ¸ÑÑÐ¸Ð¼, Ð¾ÑÐ¿Ð°ÑÐ¸Ð¼ Ð¸ Ð¿ÑÐ¸Ð²ÐµÐ´ÐµÐ¼ ÐµÐµ Ð² Ð¿Ð¾ÑÑÐ´Ð¾Ðº
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.disabledDescription.Buyer.plural=ÐÑ Ð¿ÑÐ¾Ð²ÐµÑÐ¸Ð¼ Ð²ÐµÑÐ¸ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. ÐÑÐ»Ð¸ Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾, Ð¿Ð¾ÑÐ¸ÑÑÐ¸Ð¼, Ð¾ÑÐ¿Ð°ÑÐ¸Ð¼ Ð¸ Ð¿ÑÐ¸Ð²ÐµÐ´ÐµÐ¼ Ð¸Ñ Ð² Ð¿Ð¾ÑÑÐ´Ð¾Ðº
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.EXPERTISE_PASSED.description.Buyer=ÐÐ°ÑÐ¸ ÑÐ¾Ð²Ð°ÑÑ ÑÑÐ¿ÐµÑÐ½Ð¾ Ð¿ÑÐ¾ÑÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.EXPERTISE_FAILED.description.Buyer=ÐÐ¸ Ð¾Ð´Ð¸Ð½ Ð¸Ð· ÑÐ¾Ð²Ð°ÑÐ¾Ð² Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.DELIVERED_TO_EXPERTISE.description.Buyer=ÐÐ°ÑÐ¸ ÑÐ¾Ð²Ð°ÑÑ Ð¿ÑÐ¾ÑÐ¾Ð´ÑÑ ÐºÐ¾Ð½ÑÑÐ¾Ð»Ñ ÐºÐ°ÑÐµÑÑÐ²Ð°

# Fallback expertise positions details
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.OrderPositionTrackingState.EXPERTISE_POSITION_PASSED.positionText.Seller=Ð¢Ð¾Ð²Ð°Ñ {0} ÑÑÐ¿ÐµÑÐ½Ð¾ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.OrderPositionTrackingState.EXPERTISE_POSITION_NOT_PASSED.positionText.Seller=Ð¢Ð¾Ð²Ð°Ñ {0} Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ Ð¿Ð¾ Ð¿ÑÐ¸ÑÐ¸Ð½Ðµ:
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.OrderPositionTrackingState.EXPERTISE_POSITION_PASSED_WITH_DEFFECTS.positionText.Seller=Ð¢Ð¾Ð²Ð°Ñ {0} ÑÑÐ¿ÐµÑÐ½Ð¾ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ. ÐÑÐ»Ð¸ Ð´ÐµÑÑÐµÐºÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.OrderPositionTrackingState.EXPERTISE_POSITION_PASSED_WITH_CLEANING.positionText.Seller=Ð¢Ð¾Ð²Ð°Ñ {0} ÑÑÐ¿ÐµÑÐ½Ð¾ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ. Ð¡ÑÐ¾Ð¸Ð¼Ð¾ÑÑÑ ÑÐ¸Ð¼ÑÐ¸ÑÑÐºÐ¸ {1}

service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.OrderPositionTrackingState.EXPERTISE_POSITION_NOT_PASSED.positionText.Buyer=Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ Ð²Ð°Ñ ÑÐ¾Ð²Ð°Ñ {0} Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ Ð¿Ð¾ Ð¿ÑÐ¸ÑÐ¸Ð½Ðµ:
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.OrderPositionTrackingState.EXPERTISE_POSITION_PASSED.positionText.Buyer=ÐÐ°Ñ ÑÐ¾Ð²Ð°Ñ {0} ÑÑÐ¿ÐµÑÐ½Ð¾ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.OrderPositionTrackingState.EXPERTISE_POSITION_PASSED_WITH_DEFFECTS.positionText.Buyer=ÐÐ°Ñ ÑÐ¾Ð²Ð°Ñ {0} ÑÑÐ¿ÐµÑÐ½Ð¾ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ. ÐÑÐ»Ð¸ Ð´ÐµÑÑÐµÐºÑÑ. Ð¡ÐºÐ¸Ð´ÐºÐ° {1}

# Expertise
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.title.Seller=Ð­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ð° OSKELLY
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.disabledDescription.Seller.singular=ÐÑ Ð¿ÑÐ¾Ð²ÐµÑÐ¸Ð¼ Ð²ÐµÑÑ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. ÐÑÐ»Ð¸ Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾, Ð¿Ð¾ÑÐ¸ÑÑÐ¸Ð¼, Ð¾ÑÐ¿Ð°ÑÐ¸Ð¼ Ð¸ Ð¿ÑÐ¸Ð²ÐµÐ´ÐµÐ¼ ÐµÐµ Ð² Ð¿Ð¾ÑÑÐ´Ð¾Ðº
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.disabledDescription.Seller.plural=ÐÑ Ð¿ÑÐ¾Ð²ÐµÑÐ¸Ð¼ Ð²ÐµÑÐ¸ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. ÐÑÐ»Ð¸ Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾, Ð¿Ð¾ÑÐ¸ÑÑÐ¸Ð¼, Ð¾ÑÐ¿Ð°ÑÐ¸Ð¼ Ð¸ Ð¿ÑÐ¸Ð²ÐµÐ´ÐµÐ¼ Ð¸Ñ Ð² Ð¿Ð¾ÑÑÐ´Ð¾Ðº
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.IN_QUEUE.IN_PROGRESS.description.Seller.plural=ÐÐ°ÐºÐ°Ð· Ð² Ð¾ÑÐ¸ÑÐµ OSKELLY. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð½Ð°ÑÐ½ÐµÐ¼ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ. ÐÑÐ¾Ð²ÐµÑÐ¸Ð¼ Ð²ÐµÑÐ¸ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.IN_QUEUE.IN_PROGRESS.description.Seller.singular=ÐÐ°ÐºÐ°Ð· Ð² Ð¾ÑÐ¸ÑÐµ OSKELLY. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð½Ð°ÑÐ½ÐµÐ¼ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ. ÐÑÐ¾Ð²ÐµÑÐ¸Ð¼ Ð²ÐµÑÑ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.UNPACKING.IN_PROGRESS.description.Seller.plural=ÐÐ°ÐºÐ°Ð· Ð² Ð¾ÑÐ¸ÑÐµ OSKELLY. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð½Ð°ÑÐ½ÐµÐ¼ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ. ÐÑÐ¾Ð²ÐµÑÐ¸Ð¼ Ð²ÐµÑÐ¸ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.UNPACKING.IN_PROGRESS.description.Seller.singular=ÐÐ°ÐºÐ°Ð· Ð² Ð¾ÑÐ¸ÑÐµ OSKELLY. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð½Ð°ÑÐ½ÐµÐ¼ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ. ÐÑÐ¾Ð²ÐµÑÐ¸Ð¼ Ð²ÐµÑÑ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.QUALITY_CONTROL.IN_PROGRESS.description.Seller.plural=ÐÑÐ¾Ð²ÐµÑÑÐµÐ¼ Ð²ÐµÑÐ¸ Ð½Ð° ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.QUALITY_CONTROL.IN_PROGRESS.description.Seller.singular=ÐÑÐ¾Ð²ÐµÑÑÐµÐ¼ Ð²ÐµÑÑ Ð½Ð° ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.IN_PROGRESS.description.Seller.plural=ÐÑÐ¾Ð²ÐµÑÑÐµÐ¼ Ð²ÐµÑÐ¸ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.IN_PROGRESS.description.Seller.singular=ÐÑÐ¾Ð²ÐµÑÑÐµÐ¼ Ð²ÐµÑÑ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.SUCCESS.description.Seller.plural=ÐÑ Ð¿ÑÐ¾Ð²ÐµÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ: ÑÐ¾Ð²Ð°ÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐµ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÑÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð¸Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.SUCCESS.description.Seller.singular=ÐÑ Ð¿ÑÐ¾Ð²ÐµÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ: ÑÐ¾Ð²Ð°Ñ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¹ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÐµÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.SUCCESS.boutiqueDescription.Seller.plural=ÐÑ Ð¿ÑÐ¾Ð²ÐµÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ: ÑÐ¾Ð²Ð°ÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐµ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÑÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ Ð¸Ñ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.SUCCESS.boutiqueDescription.Seller.singular=ÐÑ Ð¿ÑÐ¾Ð²ÐµÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ: ÑÐ¾Ð²Ð°Ñ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¹ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÐµÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.NOT_ORIGINAL.description.Seller.plural=
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.NOT_ORIGINAL.description.Seller.singular=
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.description.Seller.plural=<p>ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð³Ð»Ð¸ ÑÐ±ÐµÐ´Ð¸ÑÑÑÑ, ÑÑÐ¾ ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b> ÑÐ²Ð»ÑÑÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¼Ð¸. ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð¶ÐµÐ¼ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸ÑÑ Ð¸Ñ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ, ÑÐ°Ðº ÐºÐ°Ðº Ð½Ðµ Ð³Ð°ÑÐ°Ð½ÑÐ¸ÑÑÐµÐ¼ 100% Ð¿Ð¾Ð´Ð»Ð¸Ð½Ð½Ð¾ÑÑÑ.</p>\n<p>ÐÑ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ñ Ð²Ð°Ð¼Ð¸ Ð² Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ, ÑÑÐ¾Ð±Ñ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ¸ÑÑÑÑ Ð¾ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµ</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.description.Seller.singular=<p>ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð³Ð»Ð¸ ÑÐ±ÐµÐ´Ð¸ÑÑÑÑ, ÑÑÐ¾ ÑÐ¾Ð²Ð°Ñ <b>{0}</b> ÑÐ²Ð»ÑÐµÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¼. ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð¶ÐµÐ¼ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸ÑÑ ÐµÐ³Ð¾ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ, ÑÐ°Ðº ÐºÐ°Ðº Ð½Ðµ Ð³Ð°ÑÐ°Ð½ÑÐ¸ÑÑÐµÐ¼ 100% Ð¿Ð¾Ð´Ð»Ð¸Ð½Ð½Ð¾ÑÑÑ.</p>\n<p>ÐÑ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ñ Ð²Ð°Ð¼Ð¸ Ð² Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ, ÑÑÐ¾Ð±Ñ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ¸ÑÑÑÑ Ð¾ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµ</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.boutiqueDescription.Seller.plural=<p>ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð³Ð»Ð¸ ÑÐ±ÐµÐ´Ð¸ÑÑÑÑ, ÑÑÐ¾ ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b> ÑÐ²Ð»ÑÑÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¼Ð¸. ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð¶ÐµÐ¼ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸ÑÑ Ð¸Ñ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ, ÑÐ°Ðº ÐºÐ°Ðº Ð½Ðµ Ð³Ð°ÑÐ°Ð½ÑÐ¸ÑÑÐµÐ¼ 100% Ð¿Ð¾Ð´Ð»Ð¸Ð½Ð½Ð¾ÑÑÑ.</p>\n<p>ÐÑ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ñ Ð²Ð°Ð¼Ð¸ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 48Ñ, ÑÑÐ¾Ð±Ñ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ¸ÑÑÑÑ Ð¾ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµ ÑÐ¾Ð²Ð°ÑÐ¾Ð²</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.boutiqueDescription.Seller.singular=<p>ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð³Ð»Ð¸ ÑÐ±ÐµÐ´Ð¸ÑÑÑÑ, ÑÑÐ¾ ÑÐ¾Ð²Ð°Ñ <b>{0}</b> ÑÐ²Ð»ÑÐµÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¼. ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð¶ÐµÐ¼ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸ÑÑ ÐµÐ³Ð¾ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ, ÑÐ°Ðº ÐºÐ°Ðº Ð½Ðµ Ð³Ð°ÑÐ°Ð½ÑÐ¸ÑÑÐµÐ¼ 100% Ð¿Ð¾Ð´Ð»Ð¸Ð½Ð½Ð¾ÑÑÑ.</p>\n<p>ÐÑ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ñ Ð²Ð°Ð¼Ð¸ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 48Ñ, ÑÑÐ¾Ð±Ñ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ¸ÑÑÑÑ Ð¾ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµ ÑÐ¾Ð²Ð°ÑÐ°</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.IN_PROGRESS.position.Seller=
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.SUCCESS.position.Seller.plural=Ð¢Ð¾Ð²Ð°ÑÑ <b>{0}</b> ÑÐ²Ð»ÑÑÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»Ð¾Ð¼ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÑÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð¸Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.SUCCESS.position.Seller.singular=Ð¢Ð¾Ð²Ð°Ñ <b>{0}</b> ÑÐ²Ð»ÑÐµÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»Ð¾Ð¼ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÐµÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.SUCCESS.boutiquePosition.Seller.plural=Ð¢Ð¾Ð²Ð°ÑÑ <b>{0}</b> ÑÐ²Ð»ÑÑÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»Ð¾Ð¼ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÑÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ Ð¸Ñ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.SUCCESS.boutiquePosition.Seller.singular=Ð¢Ð¾Ð²Ð°Ñ <b>{0}</b> ÑÐ²Ð»ÑÐµÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»Ð¾Ð¼ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÐµÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.NOT_ORIGINAL.position.Seller=\
  <p>ÐÑ Ð²ÑÑÑÐ½Ð¸Ð»Ð¸, ÑÑÐ¾ ÑÐ¾Ð²Ð°Ñ <b>{0}</b> Ð½Ðµ ÑÐ²Ð»ÑÐµÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»Ð¾Ð¼, Ð¿Ð¾ÑÐ¾Ð¼Ñ ÑÑÐ¾</p>\n\
  <p><red>{1}</red></p>\n\
  <p><a href="https://oskelly.ru/info/pdf/seller-agency-agreement.pdf"><primary>ÐÐ¾ Ð¿ÑÐ°Ð²Ð¸Ð»Ð°Ð¼ Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ñ</primary></a> \
  Ð·Ð°Ð±ÑÐ°ÑÑ Ð²ÐµÑÑ Ð²Ñ ÑÐ¼Ð¾Ð¶ÐµÑÐµ Ð¿Ð¾ÑÐ»Ðµ Ð¾Ð¿Ð»Ð°ÑÑ ÐºÐ¾Ð¼Ð¿ÐµÐ½ÑÐ°ÑÐ¸Ð¸, ÑÐ°Ð²Ð½Ð¾Ð¹ ÐºÐ¾Ð¼Ð¸ÑÑÐ¸Ð¸ ÑÐµÑÐ²Ð¸ÑÐ°. ÐÑ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ñ Ð²Ð°Ð¼Ð¸ Ð² Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ. Ð¢Ð°ÐºÐ¶Ðµ Ð²Ñ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¿Ð¾Ð·Ð²Ð¾Ð½Ð¸ÑÑ Ð´Ð»Ñ ÑÑÐ¾ÑÐ½ÐµÐ½Ð¸Ñ Ð´ÐµÑÐ°Ð»ÐµÐ¹ Ð¿Ð¾ Ð½Ð¾Ð¼ÐµÑÑ <a href="tel:8-************">8 800 707 53 08</a></p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.NOT_ORIGINAL.boutiquePosition.Seller=\
  <p>ÐÑ Ð²ÑÑÑÐ½Ð¸Ð»Ð¸, ÑÑÐ¾ ÑÐ¾Ð²Ð°Ñ <b>{0}</b> Ð½Ðµ ÑÐ²Ð»ÑÐµÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»Ð¾Ð¼, Ð¿Ð¾ÑÐ¾Ð¼Ñ ÑÑÐ¾</p>\n\
  <p><red>{1}</red></p>\n\
  <p>ÐÑ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ñ Ð²Ð°Ð¼Ð¸ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 48Ñ, ÑÑÐ¾Ð±Ñ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ¸ÑÑÑÑ Ð¾ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµ ÑÐ¾Ð²Ð°ÑÐ°</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.position.Seller.plural=<p>ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð³Ð»Ð¸ ÑÐ±ÐµÐ´Ð¸ÑÑÑÑ, ÑÑÐ¾ ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b> ÑÐ²Ð»ÑÑÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¼Ð¸. ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð¶ÐµÐ¼ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸ÑÑ Ð¸Ñ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ, ÑÐ°Ðº ÐºÐ°Ðº Ð½Ðµ Ð³Ð°ÑÐ°Ð½ÑÐ¸ÑÑÐµÐ¼ 100% Ð¿Ð¾Ð´Ð»Ð¸Ð½Ð½Ð¾ÑÑÑ.</p>\n<p>ÐÑ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ñ Ð²Ð°Ð¼Ð¸ Ð² Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ, ÑÑÐ¾Ð±Ñ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ¸ÑÑÑÑ Ð¾ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµ</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.position.Seller.singular=<p>ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð³Ð»Ð¸ ÑÐ±ÐµÐ´Ð¸ÑÑÑÑ, ÑÑÐ¾ ÑÐ¾Ð²Ð°Ñ <b>{0}</b> ÑÐ²Ð»ÑÐµÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¼. ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð¶ÐµÐ¼ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸ÑÑ ÐµÐ³Ð¾ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ, ÑÐ°Ðº ÐºÐ°Ðº Ð½Ðµ Ð³Ð°ÑÐ°Ð½ÑÐ¸ÑÑÐµÐ¼ 100% Ð¿Ð¾Ð´Ð»Ð¸Ð½Ð½Ð¾ÑÑÑ.</p>\n<p>ÐÑ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ñ Ð²Ð°Ð¼Ð¸ Ð² Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ, ÑÑÐ¾Ð±Ñ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ¸ÑÑÑÑ Ð¾ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµ</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.boutiquePosition.Seller.plural=<p>ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð³Ð»Ð¸ ÑÐ±ÐµÐ´Ð¸ÑÑÑÑ, ÑÑÐ¾ ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b> ÑÐ²Ð»ÑÑÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¼Ð¸. ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð¶ÐµÐ¼ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸ÑÑ Ð¸Ñ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ, ÑÐ°Ðº ÐºÐ°Ðº Ð½Ðµ Ð³Ð°ÑÐ°Ð½ÑÐ¸ÑÑÐµÐ¼ 100% Ð¿Ð¾Ð´Ð»Ð¸Ð½Ð½Ð¾ÑÑÑ.</p>\n<p>ÐÑ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ñ Ð²Ð°Ð¼Ð¸ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 48Ñ, ÑÑÐ¾Ð±Ñ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ¸ÑÑÑÑ Ð¾ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµ ÑÐ¾Ð²Ð°ÑÐ¾Ð²</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.boutiquePosition.Seller.singular=<p>ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð³Ð»Ð¸ ÑÐ±ÐµÐ´Ð¸ÑÑÑÑ, ÑÑÐ¾ ÑÐ¾Ð²Ð°Ñ <b>{0}</b> ÑÐ²Ð»ÑÐµÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¼. ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð¶ÐµÐ¼ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸ÑÑ ÐµÐ³Ð¾ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ, ÑÐ°Ðº ÐºÐ°Ðº Ð½Ðµ Ð³Ð°ÑÐ°Ð½ÑÐ¸ÑÑÐµÐ¼ 100% Ð¿Ð¾Ð´Ð»Ð¸Ð½Ð½Ð¾ÑÑÑ.</p>\n<p>ÐÑ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ñ Ð²Ð°Ð¼Ð¸ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 48Ñ, ÑÑÐ¾Ð±Ñ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ¸ÑÑÑÑ Ð¾ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµ ÑÐ¾Ð²Ð°ÑÐ°</p>

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.IN_PROGRESS.description.Seller.plural=ÐÐµÑÐ¸ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐµ, Ð½Ð¾ Ð¼Ñ Ð½Ð°ÑÐ»Ð¸ Ð½ÑÐ°Ð½ÑÑ, ÐºÐ¾ÑÐ¾ÑÑÑ Ð½Ðµ Ð±ÑÐ»Ð¾ Ð² Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ð¸. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð½Ð°Ð¿Ð¸ÑÐµÐ¼ Ð²Ð°Ð¼ Ð¸ Ð¾Ð±ÑÑÐ´Ð¸Ð¼, ÑÑÐ¾ Ð¼Ð¾Ð¶Ð½Ð¾ ÑÐ´ÐµÐ»Ð°ÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.IN_PROGRESS.description.Seller.singular=ÐÐµÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð°Ñ, Ð½Ð¾ Ð¼Ñ Ð½Ð°ÑÐ»Ð¸ Ð½ÑÐ°Ð½ÑÑ, ÐºÐ¾ÑÐ¾ÑÑÑ Ð½Ðµ Ð±ÑÐ»Ð¾ Ð² Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ð¸. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð½Ð°Ð¿Ð¸ÑÐµÐ¼ Ð²Ð°Ð¼ Ð¸ Ð¾Ð±ÑÑÐ´Ð¸Ð¼, ÑÑÐ¾ Ð¼Ð¾Ð¶Ð½Ð¾ ÑÐ´ÐµÐ»Ð°ÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.IN_PROGRESS.boutiqueDescription.Seller.plural=ÐÐ°ÑÐ¸ ÑÐ¾Ð²Ð°ÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐµ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¾Ð½Ð¸ Ð½Ð°ÑÐ¾Ð´ÑÑÑÑ Ð½Ð° Ð¿ÑÐ¾Ð²ÐµÑÐºÐµ Ñ Ð¼ÐµÐ½ÐµÐ´Ð¶ÐµÑÐ° ÐÐ¾Ð½ÑÑÐµÑÐ¶Ð° Ð´Ð»Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð². ÐÑÐ»Ð¸ Ð±ÑÐ´ÐµÑ Ð½ÑÐ¶Ð½Ð¾, Ð¼ÐµÐ½ÐµÐ´Ð¶ÐµÑ Ñ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÑÑÑ Ð´Ð»Ñ ÑÑÐ¾ÑÐ½ÐµÐ½Ð¸Ñ Ð´ÐµÑÐ°Ð»ÐµÐ¹
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.IN_PROGRESS.boutiqueDescription.Seller.singular=ÐÐ°Ñ ÑÐ¾Ð²Ð°Ñ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¹. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¾Ð½ Ð½Ð°ÑÐ¾Ð´Ð¸ÑÑÑ Ð½Ð° Ð¿ÑÐ¾Ð²ÐµÑÐºÐµ Ñ Ð¼ÐµÐ½ÐµÐ´Ð¶ÐµÑÐ° ÐÐ¾Ð½ÑÑÐµÑÐ¶Ð° Ð´Ð»Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð². ÐÑÐ»Ð¸ Ð±ÑÐ´ÐµÑ Ð½ÑÐ¶Ð½Ð¾, Ð¼ÐµÐ½ÐµÐ´Ð¶ÐµÑ Ñ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÑÑÑ Ð´Ð»Ñ ÑÑÐ¾ÑÐ½ÐµÐ½Ð¸Ñ Ð´ÐµÑÐ°Ð»ÐµÐ¹
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.description.Seller.plural=ÐÑ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½Ð¾Ð²ÑÑ ÑÐµÐ½Ñ. Ð¡ ÑÑÐµÑÐ¾Ð¼ ÑÐºÐ¸Ð´Ð¾Ðº ÑÐ¾Ð²Ð°ÑÑ ÑÑÐ¾ÑÑ Ð½Ð° {0} Ð¼ÐµÐ½ÑÑÐµ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð¸Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.description.Seller.singular=ÐÑ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½Ð¾Ð²ÑÑ ÑÐµÐ½Ñ. Ð¡ ÑÑÐµÑÐ¾Ð¼ ÑÐºÐ¸Ð´ÐºÐ¸ ÑÐ¾Ð²Ð°Ñ ÑÑÐ¾Ð¸Ñ Ð½Ð° {0} Ð¼ÐµÐ½ÑÑÐµ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.boutiqueDescription.Seller.plural=ÐÑ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½Ð¾Ð²ÑÑ ÑÐµÐ½Ñ. Ð¡ ÑÑÐµÑÐ¾Ð¼ ÑÐºÐ¸Ð´Ð¾Ðº ÑÐ¾Ð²Ð°ÑÑ ÑÑÐ¾ÑÑ Ð½Ð° {0} Ð¼ÐµÐ½ÑÑÐµ. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ Ð¸Ñ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.boutiqueDescription.Seller.singular=ÐÑ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½Ð¾Ð²ÑÑ ÑÐµÐ½Ñ. Ð¡ ÑÑÐµÑÐ¾Ð¼ ÑÐºÐ¸Ð´ÐºÐ¸ ÑÐ¾Ð²Ð°Ñ ÑÑÐ¾Ð¸Ñ Ð½Ð° {0} Ð¼ÐµÐ½ÑÑÐµ. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.zeroDiscount.description.Seller.plural=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ ÑÐ¾Ð²Ð°ÑÐ°Ð¼ <b>{0}</b>. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð¸Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.zeroDiscount.description.Seller.singular=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b>. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.zeroDiscount.boutiqueDescription.Seller.plural=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ ÑÐ¾Ð²Ð°ÑÐ°Ð¼ <b>{0}</b>. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ Ð¸Ñ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.zeroDiscount.boutiqueDescription.Seller.singular=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b>. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.FAILED.description.Seller.plural=<p>ÐÑÐ°Ð½ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ, Ð·Ð°ÐºÐ°Ð· Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ.</p>\n<p>ÐÑ Ð¼Ð¾Ð¶ÐµÑÐµ ÑÐ°Ð¼Ð¸ Ð·Ð°Ð±ÑÐ°ÑÑ Ð²ÐµÑÑ ÑÐ°Ð¼Ð¸ Ð² Ð½Ð°ÑÐµÐ¼ Ð¾ÑÐ¸ÑÐµ Ð¸Ð»Ð¸ Ð²ÑÐ·Ð²Ð°ÑÑ ÐºÑÑÑÐµÑÐ°. ÐÐ°Ñ Ð°Ð´ÑÐµÑ: <b>ÐÐ°ÑÑÐ°Ð²ÑÐºÐ¾Ðµ ÑÐ¾ÑÑÐµ, Ð´ 9 ÑÑÑ 28.</b> ÐÑ ÑÐ°Ð±Ð¾ÑÐ°ÐµÐ¼ Ð² Ð±ÑÐ´Ð½Ð¸ Ñ 10 Ð´Ð¾ 19. Ð¢ÐµÐ»ÐµÑÐ¾Ð½ <a href="tel:8-************">8 800 707 53 08</a></p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.FAILED.description.Seller.singular=<p>ÐÑÐ°Ð½ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ, Ð·Ð°ÐºÐ°Ð· Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ.</p>\n<p>ÐÑ Ð¼Ð¾Ð¶ÐµÑÐµ ÑÐ°Ð¼Ð¸ Ð·Ð°Ð±ÑÐ°ÑÑ Ð²ÐµÑÑ ÑÐ°Ð¼Ð¸ Ð² Ð½Ð°ÑÐµÐ¼ Ð¾ÑÐ¸ÑÐµ Ð¸Ð»Ð¸ Ð²ÑÐ·Ð²Ð°ÑÑ ÐºÑÑÑÐµÑÐ°. ÐÐ°Ñ Ð°Ð´ÑÐµÑ: <b>ÐÐ°ÑÑÐ°Ð²ÑÐºÐ¾Ðµ ÑÐ¾ÑÑÐµ, Ð´ 9 ÑÑÑ 28.</b> ÐÑ ÑÐ°Ð±Ð¾ÑÐ°ÐµÐ¼ Ð² Ð±ÑÐ´Ð½Ð¸ Ñ 10 Ð´Ð¾ 19. Ð¢ÐµÐ»ÐµÑÐ¾Ð½ <a href="tel:8-************">8 800 707 53 08</a></p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.FAILED.boutiqueDescription.Seller.plural=<p>ÐÑÐ°Ð½ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ, Ð·Ð°ÐºÐ°Ð· Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ.</p>\n<p>ÐÑ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ñ Ð²Ð°Ð¼Ð¸ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 48Ñ, ÑÑÐ¾Ð±Ñ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ¸ÑÑÑÑ Ð¾ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµ ÑÐ¾Ð²Ð°ÑÐ¾Ð²</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.FAILED.boutiqueDescription.Seller.singular=<p>ÐÑÐ°Ð½ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ, Ð·Ð°ÐºÐ°Ð· Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ.</p>\n<p>ÐÑ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ñ Ð²Ð°Ð¼Ð¸ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 48Ñ, ÑÑÐ¾Ð±Ñ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ¸ÑÑÑÑ Ð¾ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµ ÑÐ¾Ð²Ð°ÑÐ°</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.IN_PROGRESS.description.Seller.plural=ÐÐµÑÐ¸ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐµ, Ð½Ð¾ Ð¼Ñ Ð½Ð°ÑÐ»Ð¸ Ð½ÑÐ°Ð½ÑÑ, ÐºÐ¾ÑÐ¾ÑÑÑ Ð½Ðµ Ð±ÑÐ»Ð¾ Ð² Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ð¸. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð½Ð°Ð¿Ð¸ÑÐµÐ¼ Ð²Ð°Ð¼ Ð¸ Ð¾Ð±ÑÑÐ´Ð¸Ð¼, ÑÑÐ¾ Ð¼Ð¾Ð¶Ð½Ð¾ ÑÐ´ÐµÐ»Ð°ÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.IN_PROGRESS.description.Seller.singular=ÐÐµÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð°Ñ, Ð½Ð¾ Ð¼Ñ Ð½Ð°ÑÐ»Ð¸ Ð½ÑÐ°Ð½ÑÑ, ÐºÐ¾ÑÐ¾ÑÑÑ Ð½Ðµ Ð±ÑÐ»Ð¾ Ð² Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ð¸. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð½Ð°Ð¿Ð¸ÑÐµÐ¼ Ð²Ð°Ð¼ Ð¸ Ð¾Ð±ÑÑÐ´Ð¸Ð¼, ÑÑÐ¾ Ð¼Ð¾Ð¶Ð½Ð¾ ÑÐ´ÐµÐ»Ð°ÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.IN_PROGRESS.boutiqueDescription.Seller.plural=ÐÐ°ÑÐ¸ ÑÐ¾Ð²Ð°ÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐµ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¾Ð½Ð¸ Ð½Ð°ÑÐ¾Ð´ÑÑÑÑ Ð½Ð° Ð¿ÑÐ¾Ð²ÐµÑÐºÐµ Ñ Ð¼ÐµÐ½ÐµÐ´Ð¶ÐµÑÐ° ÐÐ¾Ð½ÑÑÐµÑÐ¶Ð° Ð´Ð»Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð². ÐÑÐ»Ð¸ Ð±ÑÐ´ÐµÑ Ð½ÑÐ¶Ð½Ð¾, Ð¼ÐµÐ½ÐµÐ´Ð¶ÐµÑ Ñ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÑÑÑ Ð´Ð»Ñ ÑÑÐ¾ÑÐ½ÐµÐ½Ð¸Ñ Ð´ÐµÑÐ°Ð»ÐµÐ¹
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.IN_PROGRESS.boutiqueDescription.Seller.singular=ÐÐ°Ñ ÑÐ¾Ð²Ð°Ñ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¹. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¾Ð½ Ð½Ð°ÑÐ¾Ð´Ð¸ÑÑÑ Ð½Ð° Ð¿ÑÐ¾Ð²ÐµÑÐºÐµ Ñ Ð¼ÐµÐ½ÐµÐ´Ð¶ÐµÑÐ° ÐÐ¾Ð½ÑÑÐµÑÐ¶Ð° Ð´Ð»Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð². ÐÑÐ»Ð¸ Ð±ÑÐ´ÐµÑ Ð½ÑÐ¶Ð½Ð¾, Ð¼ÐµÐ½ÐµÐ´Ð¶ÐµÑ Ñ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÑÑÑ Ð´Ð»Ñ ÑÑÐ¾ÑÐ½ÐµÐ½Ð¸Ñ Ð´ÐµÑÐ°Ð»ÐµÐ¹
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.description.Seller.plural=ÐÑ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½Ð¾Ð²ÑÑ ÑÐµÐ½Ñ. Ð¡ ÑÑÐµÑÐ¾Ð¼ ÑÐºÐ¸Ð´Ð¾Ðº ÑÐ¾Ð²Ð°ÑÑ ÑÑÐ¾ÑÑ Ð½Ð° {0} Ð¼ÐµÐ½ÑÑÐµ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð¸Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.description.Seller.singular=ÐÑ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½Ð¾Ð²ÑÑ ÑÐµÐ½Ñ. Ð¡ ÑÑÐµÑÐ¾Ð¼ ÑÐºÐ¸Ð´ÐºÐ¸ ÑÐ¾Ð²Ð°Ñ ÑÑÐ¾Ð¸Ñ Ð½Ð° {0} Ð¼ÐµÐ½ÑÑÐµ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.boutiqueDescription.Seller.plural=ÐÑ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½Ð¾Ð²ÑÑ ÑÐµÐ½Ñ. Ð¡ ÑÑÐµÑÐ¾Ð¼ ÑÐºÐ¸Ð´Ð¾Ðº ÑÐ¾Ð²Ð°ÑÑ ÑÑÐ¾ÑÑ Ð½Ð° {0} Ð¼ÐµÐ½ÑÑÐµ. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ Ð¸Ñ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.boutiqueDescription.Seller.singular=ÐÑ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½Ð¾Ð²ÑÑ ÑÐµÐ½Ñ. Ð¡ ÑÑÐµÑÐ¾Ð¼ ÑÐºÐ¸Ð´ÐºÐ¸ ÑÐ¾Ð²Ð°Ñ ÑÑÐ¾Ð¸Ñ Ð½Ð° {0} Ð¼ÐµÐ½ÑÑÐµ. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.zeroDiscount.description.Seller.plural=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ ÑÐ¾Ð²Ð°ÑÐ°Ð¼ <b>{0}</b>. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð¸Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.zeroDiscount.description.Seller.singular=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b>. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.zeroDiscount.boutiqueDescription.Seller.plural=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ ÑÐ¾Ð²Ð°ÑÐ°Ð¼ <b>{0}</b>. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ Ð¸Ñ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.zeroDiscount.boutiqueDescription.Seller.singular=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b>. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.FAILED.description.Seller.plural=<p>ÐÑÐ°Ð½ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ, Ð·Ð°ÐºÐ°Ð· Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ.</p>\n<p>ÐÑ Ð¼Ð¾Ð¶ÐµÑÐµ ÑÐ°Ð¼Ð¸ Ð·Ð°Ð±ÑÐ°ÑÑ Ð²ÐµÑÑ ÑÐ°Ð¼Ð¸ Ð² Ð½Ð°ÑÐµÐ¼ Ð¾ÑÐ¸ÑÐµ Ð¸Ð»Ð¸ Ð²ÑÐ·Ð²Ð°ÑÑ ÐºÑÑÑÐµÑÐ°. ÐÐ°Ñ Ð°Ð´ÑÐµÑ: <b>ÐÐ°ÑÑÐ°Ð²ÑÐºÐ¾Ðµ ÑÐ¾ÑÑÐµ, Ð´ 9 ÑÑÑ 28.</b> ÐÑ ÑÐ°Ð±Ð¾ÑÐ°ÐµÐ¼ Ð² Ð±ÑÐ´Ð½Ð¸ Ñ 10 Ð´Ð¾ 19. Ð¢ÐµÐ»ÐµÑÐ¾Ð½ <a href="tel:8-************">8 800 707 53 08</a></p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.FAILED.description.Seller.singular=<p>ÐÑÐ°Ð½ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ, Ð·Ð°ÐºÐ°Ð· Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ.</p>\n<p>ÐÑ Ð¼Ð¾Ð¶ÐµÑÐµ ÑÐ°Ð¼Ð¸ Ð·Ð°Ð±ÑÐ°ÑÑ Ð²ÐµÑÑ ÑÐ°Ð¼Ð¸ Ð² Ð½Ð°ÑÐµÐ¼ Ð¾ÑÐ¸ÑÐµ Ð¸Ð»Ð¸ Ð²ÑÐ·Ð²Ð°ÑÑ ÐºÑÑÑÐµÑÐ°. ÐÐ°Ñ Ð°Ð´ÑÐµÑ: <b>ÐÐ°ÑÑÐ°Ð²ÑÐºÐ¾Ðµ ÑÐ¾ÑÑÐµ, Ð´ 9 ÑÑÑ 28.</b> ÐÑ ÑÐ°Ð±Ð¾ÑÐ°ÐµÐ¼ Ð² Ð±ÑÐ´Ð½Ð¸ Ñ 10 Ð´Ð¾ 19. Ð¢ÐµÐ»ÐµÑÐ¾Ð½ <a href="tel:8-************">8 800 707 53 08</a></p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.FAILED.boutiqueDescription.Seller.plural=<p>ÐÑÐ°Ð½ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ, Ð·Ð°ÐºÐ°Ð· Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ.</p>\n<p>ÐÑ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ñ Ð²Ð°Ð¼Ð¸ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 48Ñ, ÑÑÐ¾Ð±Ñ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ¸ÑÑÑÑ Ð¾ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµ ÑÐ¾Ð²Ð°ÑÐ¾Ð²</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.FAILED.boutiqueDescription.Seller.singular=<p>ÐÑÐ°Ð½ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ, Ð·Ð°ÐºÐ°Ð· Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ.</p>\n<p>ÐÑ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ñ Ð²Ð°Ð¼Ð¸ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 48Ñ, ÑÑÐ¾Ð±Ñ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ¸ÑÑÑÑ Ð¾ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµ ÑÐ¾Ð²Ð°ÑÐ°</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.IN_PROGRESS.position.Seller.plural=Ð¢Ð¾Ð²Ð°ÑÑ <b>{0}</b> ÑÐ²Ð»ÑÑÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¼Ð¸, Ð½Ð¾ Ð¼Ñ Ð½Ð°ÑÐ»Ð¸ Ð½ÑÐ°Ð½ÑÑ, ÐºÐ¾ÑÐ¾ÑÑÑ Ð½Ðµ Ð±ÑÐ»Ð¾ Ð² Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ð¸. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð½Ð°Ð¿Ð¸ÑÐµÐ¼ Ð²Ð°Ð¼ Ð¸ Ð¾Ð±ÑÑÐ´Ð¸Ð¼, ÑÑÐ¾ Ð¼Ð¾Ð¶Ð½Ð¾ ÑÐ´ÐµÐ»Ð°ÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.IN_PROGRESS.position.Seller.singular=Ð¢Ð¾Ð²Ð°Ñ <b>{0}</b> ÑÐ²Ð»ÑÐµÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»Ð¾Ð¼, Ð½Ð¾ Ð¼Ñ Ð½Ð°ÑÐ»Ð¸ Ð½ÑÐ°Ð½ÑÑ, ÐºÐ¾ÑÐ¾ÑÑÑ Ð½Ðµ Ð±ÑÐ»Ð¾ Ð² Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ð¸. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð½Ð°Ð¿Ð¸ÑÐµÐ¼ Ð²Ð°Ð¼ Ð¸ Ð¾Ð±ÑÑÐ´Ð¸Ð¼, ÑÑÐ¾ Ð¼Ð¾Ð¶Ð½Ð¾ ÑÐ´ÐµÐ»Ð°ÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.IN_PROGRESS.boutiquePosition.Seller.plural=Ð¢Ð¾Ð²Ð°ÑÑ <b>{0}</b> Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐµ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¾Ð½Ð¸ Ð½Ð°ÑÐ¾Ð´ÑÑÑÑ Ð½Ð° Ð¿ÑÐ¾Ð²ÐµÑÐºÐµ Ñ Ð¼ÐµÐ½ÐµÐ´Ð¶ÐµÑÐ° ÐÐ¾Ð½ÑÑÐµÑÐ¶Ð° Ð´Ð»Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð². ÐÑÐ»Ð¸ Ð±ÑÐ´ÐµÑ Ð½ÑÐ¶Ð½Ð¾, Ð¼ÐµÐ½ÐµÐ´Ð¶ÐµÑ Ñ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÑÑÑ Ð´Ð»Ñ ÑÑÐ¾ÑÐ½ÐµÐ½Ð¸Ñ Ð´ÐµÑÐ°Ð»ÐµÐ¹
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.IN_PROGRESS.boutiquePosition.Seller.singular=Ð¢Ð¾Ð²Ð°Ñ <b>{0}</b> Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¹. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¾Ð½ Ð½Ð°ÑÐ¾Ð´Ð¸ÑÑÑ Ð½Ð° Ð¿ÑÐ¾Ð²ÐµÑÐºÐµ Ñ Ð¼ÐµÐ½ÐµÐ´Ð¶ÐµÑÐ° ÐÐ¾Ð½ÑÑÐµÑÐ¶Ð° Ð´Ð»Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð². ÐÑÐ»Ð¸ Ð±ÑÐ´ÐµÑ Ð½ÑÐ¶Ð½Ð¾, Ð¼ÐµÐ½ÐµÐ´Ð¶ÐµÑ Ñ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÑÑÑ Ð´Ð»Ñ ÑÑÐ¾ÑÐ½ÐµÐ½Ð¸Ñ Ð´ÐµÑÐ°Ð»ÐµÐ¹
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.position.Seller.plural=ÐÑ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½Ð¾Ð²ÑÑ ÑÐµÐ½Ñ. Ð¡ ÑÑÐµÑÐ¾Ð¼ ÑÐºÐ¸Ð´Ð¾Ðº ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b> ÑÑÐ¾ÑÑ Ð½Ð° {1} Ð¼ÐµÐ½ÑÑÐµ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð¸Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.position.Seller.singular=ÐÑ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½Ð¾Ð²ÑÑ ÑÐµÐ½Ñ. Ð¡ ÑÑÐµÑÐ¾Ð¼ ÑÐºÐ¸Ð´ÐºÐ¸ ÑÐ¾Ð²Ð°Ñ <b>{0}</b> ÑÑÐ¾Ð¸Ñ Ð½Ð° {1} Ð¼ÐµÐ½ÑÑÐµ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.boutiquePosition.Seller.plural=ÐÑ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½Ð¾Ð²ÑÑ ÑÐµÐ½Ñ. Ð¡ ÑÑÐµÑÐ¾Ð¼ ÑÐºÐ¸Ð´Ð¾Ðº ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b> ÑÑÐ¾ÑÑ Ð½Ð° {1} Ð¼ÐµÐ½ÑÑÐµ. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ Ð¸Ñ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.boutiquePosition.Seller.singular=ÐÑ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½Ð¾Ð²ÑÑ ÑÐµÐ½Ñ. Ð¡ ÑÑÐµÑÐ¾Ð¼ ÑÐºÐ¸Ð´ÐºÐ¸ ÑÐ¾Ð²Ð°Ñ <b>{0}</b> ÑÑÐ¾Ð¸Ñ Ð½Ð° {1} Ð¼ÐµÐ½ÑÑÐµ. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.zeroDiscount.position.Seller.plural=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ ÑÐ¾Ð²Ð°ÑÐ°Ð¼ <b>{0}</b>. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð¸Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.zeroDiscount.position.Seller.singular=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b>. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.zeroDiscount.boutiquePosition.Seller.plural=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ ÑÐ¾Ð²Ð°ÑÐ°Ð¼ <b>{0}</b>. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ Ð¸Ñ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.zeroDiscount.boutiquePosition.Seller.singular=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b>. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.FAILED.position.Seller.plural=<p>ÐÑÐ°Ð½ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ, ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b> Ð½Ðµ Ð¿ÑÐ¾ÑÐ»Ð¸ Ð¿ÑÐ¾Ð²ÐµÑÐºÑ.</p>\n<p>ÐÑ Ð¼Ð¾Ð¶ÐµÑÐµ ÑÐ°Ð¼Ð¸ Ð·Ð°Ð±ÑÐ°ÑÑ Ð²ÐµÑÐ¸ Ð² Ð½Ð°ÑÐµÐ¼ Ð¾ÑÐ¸ÑÐµ Ð¸Ð»Ð¸ Ð²ÑÐ·Ð²Ð°ÑÑ ÐºÑÑÑÐµÑÐ°. ÐÐ°Ñ Ð°Ð´ÑÐµÑ: <b>ÐÐ°ÑÑÐ°Ð²ÑÐºÐ¾Ðµ ÑÐ¾ÑÑÐµ, Ð´ 9 ÑÑÑ 28.</b> ÐÑ ÑÐ°Ð±Ð¾ÑÐ°ÐµÐ¼ Ð² Ð±ÑÐ´Ð½Ð¸ Ñ 10 Ð´Ð¾ 19. Ð¢ÐµÐ»ÐµÑÐ¾Ð½ <a href="tel:8-************">8 800 707 53 08</a></p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.FAILED.position.Seller.singular=<p>ÐÑÐ°Ð½ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ, ÑÐ¾Ð²Ð°Ñ <b>{0}</b> Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ.</p>\n<p>ÐÑ Ð¼Ð¾Ð¶ÐµÑÐµ ÑÐ°Ð¼Ð¸ Ð·Ð°Ð±ÑÐ°ÑÑ Ð²ÐµÑÑ ÑÐ°Ð¼Ð¸ Ð² Ð½Ð°ÑÐµÐ¼ Ð¾ÑÐ¸ÑÐµ Ð¸Ð»Ð¸ Ð²ÑÐ·Ð²Ð°ÑÑ ÐºÑÑÑÐµÑÐ°. ÐÐ°Ñ Ð°Ð´ÑÐµÑ: <b>ÐÐ°ÑÑÐ°Ð²ÑÐºÐ¾Ðµ ÑÐ¾ÑÑÐµ, Ð´ 9 ÑÑÑ 28.</b> ÐÑ ÑÐ°Ð±Ð¾ÑÐ°ÐµÐ¼ Ð² Ð±ÑÐ´Ð½Ð¸ Ñ 10 Ð´Ð¾ 19. Ð¢ÐµÐ»ÐµÑÐ¾Ð½ <a href="tel:8-************">8 800 707 53 08</a></p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.FAILED.boutiquePosition.Seller.plural=<p>ÐÑÐ°Ð½ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ, ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b> Ð½Ðµ Ð¿ÑÐ¾ÑÐ»Ð¸ Ð¿ÑÐ¾Ð²ÐµÑÐºÑ.</p>\n<p>ÐÑ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ñ Ð²Ð°Ð¼Ð¸ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 48Ñ, ÑÑÐ¾Ð±Ñ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ¸ÑÑÑÑ Ð¾ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµ ÑÐ¾Ð²Ð°ÑÐ¾Ð²</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.FAILED.boutiquePosition.Seller.singular=<p>ÐÑÐ°Ð½ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ, ÑÐ¾Ð²Ð°Ñ <b>{0}</b> Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ.</p>\n<p>ÐÑ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ñ Ð²Ð°Ð¼Ð¸ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 48Ñ, ÑÑÐ¾Ð±Ñ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ¸ÑÑÑÑ Ð¾ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµ ÑÐ¾Ð²Ð°ÑÐ°</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.IN_PROGRESS.position.Seller.plural=Ð¢Ð¾Ð²Ð°ÑÑ <b>{0}</b> ÑÐ²Ð»ÑÑÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¼Ð¸, Ð½Ð¾ Ð¼Ñ Ð½Ð°ÑÐ»Ð¸ Ð½ÑÐ°Ð½ÑÑ, ÐºÐ¾ÑÐ¾ÑÑÑ Ð½Ðµ Ð±ÑÐ»Ð¾ Ð² Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ð¸. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð½Ð°Ð¿Ð¸ÑÐµÐ¼ Ð²Ð°Ð¼ Ð¸ Ð¾Ð±ÑÑÐ´Ð¸Ð¼, ÑÑÐ¾ Ð¼Ð¾Ð¶Ð½Ð¾ ÑÐ´ÐµÐ»Ð°ÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.IN_PROGRESS.position.Seller.singular=Ð¢Ð¾Ð²Ð°Ñ <b>{0}</b> ÑÐ²Ð»ÑÐµÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»Ð¾Ð¼, Ð½Ð¾ Ð¼Ñ Ð½Ð°ÑÐ»Ð¸ Ð½ÑÐ°Ð½ÑÑ, ÐºÐ¾ÑÐ¾ÑÑÑ Ð½Ðµ Ð±ÑÐ»Ð¾ Ð² Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ð¸. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð½Ð°Ð¿Ð¸ÑÐµÐ¼ Ð²Ð°Ð¼ Ð¸ Ð¾Ð±ÑÑÐ´Ð¸Ð¼, ÑÑÐ¾ Ð¼Ð¾Ð¶Ð½Ð¾ ÑÐ´ÐµÐ»Ð°ÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.IN_PROGRESS.boutiquePosition.Seller.plural=Ð¢Ð¾Ð²Ð°ÑÑ <b>{0}</b> Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐµ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¾Ð½Ð¸ Ð½Ð°ÑÐ¾Ð´ÑÑÑÑ Ð½Ð° Ð¿ÑÐ¾Ð²ÐµÑÐºÐµ Ñ Ð¼ÐµÐ½ÐµÐ´Ð¶ÐµÑÐ° ÐÐ¾Ð½ÑÑÐµÑÐ¶Ð° Ð´Ð»Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð². ÐÑÐ»Ð¸ Ð±ÑÐ´ÐµÑ Ð½ÑÐ¶Ð½Ð¾, Ð¼ÐµÐ½ÐµÐ´Ð¶ÐµÑ Ñ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÑÑÑ Ð´Ð»Ñ ÑÑÐ¾ÑÐ½ÐµÐ½Ð¸Ñ Ð´ÐµÑÐ°Ð»ÐµÐ¹
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.IN_PROGRESS.boutiquePosition.Seller.singular=Ð¢Ð¾Ð²Ð°Ñ <b>{0}</b> Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¹. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¾Ð½ Ð½Ð°ÑÐ¾Ð´Ð¸ÑÑÑ Ð½Ð° Ð¿ÑÐ¾Ð²ÐµÑÐºÐµ Ñ Ð¼ÐµÐ½ÐµÐ´Ð¶ÐµÑÐ° ÐÐ¾Ð½ÑÑÐµÑÐ¶Ð° Ð´Ð»Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð². ÐÑÐ»Ð¸ Ð±ÑÐ´ÐµÑ Ð½ÑÐ¶Ð½Ð¾, Ð¼ÐµÐ½ÐµÐ´Ð¶ÐµÑ Ñ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÑÑÑ Ð´Ð»Ñ ÑÑÐ¾ÑÐ½ÐµÐ½Ð¸Ñ Ð´ÐµÑÐ°Ð»ÐµÐ¹
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.position.Seller.plural=ÐÑ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½Ð¾Ð²ÑÑ ÑÐµÐ½Ñ. Ð¡ ÑÑÐµÑÐ¾Ð¼ ÑÐºÐ¸Ð´Ð¾Ðº ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b> ÑÑÐ¾ÑÑ Ð½Ð° {1} Ð¼ÐµÐ½ÑÑÐµ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð¸Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.position.Seller.singular=ÐÑ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½Ð¾Ð²ÑÑ ÑÐµÐ½Ñ. Ð¡ ÑÑÐµÑÐ¾Ð¼ ÑÐºÐ¸Ð´ÐºÐ¸ ÑÐ¾Ð²Ð°Ñ <b>{0}</b> ÑÑÐ¾Ð¸Ñ Ð½Ð° {1} Ð¼ÐµÐ½ÑÑÐµ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.boutiquePosition.Seller.plural=ÐÑ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½Ð¾Ð²ÑÑ ÑÐµÐ½Ñ. Ð¡ ÑÑÐµÑÐ¾Ð¼ ÑÐºÐ¸Ð´Ð¾Ðº ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b> ÑÑÐ¾ÑÑ Ð½Ð° {1} Ð¼ÐµÐ½ÑÑÐµ. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ Ð¸Ñ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.boutiquePosition.Seller.singular=ÐÑ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½Ð¾Ð²ÑÑ ÑÐµÐ½Ñ. Ð¡ ÑÑÐµÑÐ¾Ð¼ ÑÐºÐ¸Ð´ÐºÐ¸ ÑÐ¾Ð²Ð°Ñ <b>{0}</b> ÑÑÐ¾Ð¸Ñ Ð½Ð° {1} Ð¼ÐµÐ½ÑÑÐµ. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.zeroDiscount.position.Seller.plural=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ ÑÐ¾Ð²Ð°ÑÐ°Ð¼ <b>{0}</b>. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð¸Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.zeroDiscount.position.Seller.singular=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b>. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.zeroDiscount.boutiquePosition.Seller.plural=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ ÑÐ¾Ð²Ð°ÑÐ°Ð¼ <b>{0}</b>. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ Ð¸Ñ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.zeroDiscount.boutiquePosition.Seller.singular=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b>. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.FAILED.position.Seller.plural=<p>ÐÑÐ°Ð½ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ, ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b> Ð½Ðµ Ð¿ÑÐ¾ÑÐ»Ð¸ Ð¿ÑÐ¾Ð²ÐµÑÐºÑ.</p>\n<p>ÐÑ Ð¼Ð¾Ð¶ÐµÑÐµ ÑÐ°Ð¼Ð¸ Ð·Ð°Ð±ÑÐ°ÑÑ Ð²ÐµÑÐ¸ Ð² Ð½Ð°ÑÐµÐ¼ Ð¾ÑÐ¸ÑÐµ Ð¸Ð»Ð¸ Ð²ÑÐ·Ð²Ð°ÑÑ ÐºÑÑÑÐµÑÐ°. ÐÐ°Ñ Ð°Ð´ÑÐµÑ: <b>ÐÐ°ÑÑÐ°Ð²ÑÐºÐ¾Ðµ ÑÐ¾ÑÑÐµ, Ð´ 9 ÑÑÑ 28.</b> ÐÑ ÑÐ°Ð±Ð¾ÑÐ°ÐµÐ¼ Ð² Ð±ÑÐ´Ð½Ð¸ Ñ 10 Ð´Ð¾ 19. Ð¢ÐµÐ»ÐµÑÐ¾Ð½ <a href="tel:8-************">8 800 707 53 08</a></p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.FAILED.position.Seller.singular=<p>ÐÑÐ°Ð½ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ, ÑÐ¾Ð²Ð°Ñ <b>{0}</b> Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ.</p>\n<p>ÐÑ Ð¼Ð¾Ð¶ÐµÑÐµ ÑÐ°Ð¼Ð¸ Ð·Ð°Ð±ÑÐ°ÑÑ Ð²ÐµÑÑ ÑÐ°Ð¼Ð¸ Ð² Ð½Ð°ÑÐµÐ¼ Ð¾ÑÐ¸ÑÐµ Ð¸Ð»Ð¸ Ð²ÑÐ·Ð²Ð°ÑÑ ÐºÑÑÑÐµÑÐ°. ÐÐ°Ñ Ð°Ð´ÑÐµÑ: <b>ÐÐ°ÑÑÐ°Ð²ÑÐºÐ¾Ðµ ÑÐ¾ÑÑÐµ, Ð´ 9 ÑÑÑ 28.</b> ÐÑ ÑÐ°Ð±Ð¾ÑÐ°ÐµÐ¼ Ð² Ð±ÑÐ´Ð½Ð¸ Ñ 10 Ð´Ð¾ 19. Ð¢ÐµÐ»ÐµÑÐ¾Ð½ <a href="tel:8-************">8 800 707 53 08</a></p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.FAILED.boutiquePosition.Seller.plural=<p>ÐÑÐ°Ð½ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ, ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b> Ð½Ðµ Ð¿ÑÐ¾ÑÐ»Ð¸ Ð¿ÑÐ¾Ð²ÐµÑÐºÑ.</p>\n<p>ÐÑ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ñ Ð²Ð°Ð¼Ð¸ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 48Ñ, ÑÑÐ¾Ð±Ñ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ¸ÑÑÑÑ Ð¾ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµ ÑÐ¾Ð²Ð°ÑÐ¾Ð²</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.FAILED.boutiquePosition.Seller.singular=<p>ÐÑÐ°Ð½ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ, ÑÐ¾Ð²Ð°Ñ <b>{0}</b> Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ.</p>\n<p>ÐÑ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ñ Ð²Ð°Ð¼Ð¸ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 48Ñ, ÑÑÐ¾Ð±Ñ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ¸ÑÑÑÑ Ð¾ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµ ÑÐ¾Ð²Ð°ÑÐ°</p>

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PRESELLING_PREPARATION.IN_PROGRESS.description.Seller.plural=ÐÑ Ð¿ÑÐ¾Ð²ÐµÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ: ÑÐ¾Ð²Ð°ÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐµ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÑÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð¸Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PRESELLING_PREPARATION.IN_PROGRESS.description.Seller.singular=ÐÑ Ð¿ÑÐ¾Ð²ÐµÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ: ÑÐ¾Ð²Ð°Ñ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¹ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÐµÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PRESELLING_PREPARATION.IN_PROGRESS.boutiqueDescription.Seller.plural=ÐÑ Ð¿ÑÐ¾Ð²ÐµÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ: ÑÐ¾Ð²Ð°ÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐµ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÑÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ Ð¸Ñ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PRESELLING_PREPARATION.IN_PROGRESS.boutiqueDescription.Seller.singular=ÐÑ Ð¿ÑÐ¾Ð²ÐµÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ: ÑÐ¾Ð²Ð°Ñ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¹ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÐµÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PRESELLING_PREPARATION.IN_PROGRESS.position.Seller.plural=ÐÑ Ð¿ÑÐ¾Ð²ÐµÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ: ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b> Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐµ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÑÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð¸Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PRESELLING_PREPARATION.IN_PROGRESS.position.Seller.singular=ÐÑ Ð¿ÑÐ¾Ð²ÐµÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ: ÑÐ¾Ð²Ð°Ñ <b>{0}</b> Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¹ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÐµÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PRESELLING_PREPARATION.IN_PROGRESS.boutiquePosition.Seller.plural=ÐÑ Ð¿ÑÐ¾Ð²ÐµÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ: ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b> Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐµ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÑÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ Ð¸Ñ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PRESELLING_PREPARATION.IN_PROGRESS.boutiquePosition.Seller.singular=ÐÑ Ð¿ÑÐ¾Ð²ÐµÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ: ÑÐ¾Ð²Ð°Ñ <b>{0}</b> Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¹ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÐµÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PACKING.IN_PROGRESS.description.Seller.plural=Ð£Ð¿Ð°ÐºÐ¾Ð²ÑÐ²Ð°ÐµÐ¼ Ð²ÐµÑÐ¸, ÑÑÐ¾Ð±Ñ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸ÑÑ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PACKING.IN_PROGRESS.description.Seller.singular=Ð£Ð¿Ð°ÐºÐ¾Ð²ÑÐ²Ð°ÐµÐ¼ Ð²ÐµÑÑ, ÑÑÐ¾Ð±Ñ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸ÑÑ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PACKING.IN_PROGRESS.position.Seller.plural=Ð£Ð¿Ð°ÐºÐ¾Ð²ÑÐ²Ð°ÐµÐ¼ ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b>, ÑÑÐ¾Ð±Ñ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸ÑÑ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PACKING.IN_PROGRESS.position.Seller.singular=Ð£Ð¿Ð°ÐºÐ¾Ð²ÑÐ²Ð°ÐµÐ¼ ÑÐ¾Ð²Ð°Ñ <b>{0}</b>, ÑÑÐ¾Ð±Ñ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸ÑÑ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.SUCCESS.description.Seller=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ Ð¸ Ð³Ð¾ÑÐ¾Ð² Ðº Ð¾ÑÐ¿ÑÐ°Ð²ÐºÐµ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.description.Seller.plural=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.description.Seller.singular=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.SUCCESS.position.Seller.plural=Ð¢Ð¾Ð²Ð°ÑÑ <b>{0}</b> Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐµ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÑÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. ÐÑ Ð¿Ð¾Ð´Ð³Ð¾ÑÐ¾Ð²Ð¸Ð»Ð¸ Ð¸Ñ Ðº Ð¾ÑÐ¿ÑÐ°Ð²ÐºÐµ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.SUCCESS.position.Seller.singular=Ð¢Ð¾Ð²Ð°Ñ <b>{0}</b> Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¹ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÐµÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. ÐÑ Ð¿Ð¾Ð´Ð³Ð¾ÑÐ¾Ð²Ð¸Ð»Ð¸ ÐµÐ³Ð¾ Ðº Ð¾ÑÐ¿ÑÐ°Ð²ÐºÐµ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.position.Seller.plural=Ð¢Ð¾Ð²Ð°ÑÑ <b>{0}</b> Ð½Ðµ Ð¿ÑÐ¾ÑÐ»Ð¸ Ð¿ÑÐ¾Ð²ÐµÑÐºÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.position.Seller.singular=Ð¢Ð¾Ð²Ð°Ñ <b>{0}</b> Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.DESTROYED.description.Seller.singular=<p>Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, Ð¿Ð¾ ÑÐµÑÐ½Ð¸ÑÐµÑÐºÐ¸Ð¼ Ð¿ÑÐ¸ÑÐ¸Ð½Ð°Ð¼ Ð½Ð°Ð¼ Ð¿ÑÐ¸ÑÐ»Ð¾ÑÑ Ð¾ÑÐ¼ÐµÐ½Ð¸ÑÑ Ð·Ð°ÐºÐ°Ð·</p>\n<p>ÐÑ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ñ Ð²Ð°Ð¼Ð¸ Ð² Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ, ÑÑÐ¾Ð±Ñ Ð¾Ð±ÑÑÐ´Ð¸ÑÑ Ð´ÐµÑÐ°Ð»Ð¸ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐ° Ð´ÐµÐ½ÐµÐ¶Ð½ÑÑ ÑÑÐµÐ´ÑÑÐ²</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.DESTROYED.description.Seller.plural=<p>Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, Ð¿Ð¾ ÑÐµÑÐ½Ð¸ÑÐµÑÐºÐ¸Ð¼ Ð¿ÑÐ¸ÑÐ¸Ð½Ð°Ð¼ Ð½Ð°Ð¼ Ð¿ÑÐ¸ÑÐ»Ð¾ÑÑ Ð¾ÑÐ¼ÐµÐ½Ð¸ÑÑ Ð·Ð°ÐºÐ°Ð·</p>\n<p>ÐÑ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ñ Ð²Ð°Ð¼Ð¸ Ð² Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ, ÑÑÐ¾Ð±Ñ Ð¾Ð±ÑÑÐ´Ð¸ÑÑ Ð´ÐµÑÐ°Ð»Ð¸ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐ° Ð´ÐµÐ½ÐµÐ¶Ð½ÑÑ ÑÑÐµÐ´ÑÑÐ²</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.DESTROYED.position.Seller.singular=<p>Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, Ð¿Ð¾ ÑÐµÑÐ½Ð¸ÑÐµÑÐºÐ¸Ð¼ Ð¿ÑÐ¸ÑÐ¸Ð½Ð°Ð¼ Ð½Ð°Ð¼ Ð¿ÑÐ¸ÑÐ»Ð¾ÑÑ Ð¾ÑÐ¼ÐµÐ½Ð¸ÑÑ ÑÐ¾Ð²Ð°Ñ <b>{0}</b></p>\n<p>ÐÑ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ñ Ð²Ð°Ð¼Ð¸, ÑÑÐ¾Ð±Ñ Ð¾Ð±ÑÑÐ´Ð¸ÑÑ Ð´ÐµÑÐ°Ð»Ð¸, ÐºÐ¾Ð³Ð´Ð° Ð´ÑÑÐ³Ð¸Ðµ ÑÐ¾Ð²Ð°ÑÑ Ð¸Ð· Ð·Ð°ÐºÐ°Ð·Ð° Ð±ÑÐ´ÑÑ Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½Ñ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.DESTROYED.position.Seller.plural=<p>Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, Ð¿Ð¾ ÑÐµÑÐ½Ð¸ÑÐµÑÐºÐ¸Ð¼ Ð¿ÑÐ¸ÑÐ¸Ð½Ð°Ð¼ Ð½Ð°Ð¼ Ð¿ÑÐ¸ÑÐ»Ð¾ÑÑ Ð¾ÑÐ¼ÐµÐ½Ð¸ÑÑ ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b></p>\n<p>ÐÑ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ñ Ð²Ð°Ð¼Ð¸, ÑÑÐ¾Ð±Ñ Ð¾Ð±ÑÑÐ´Ð¸ÑÑ Ð´ÐµÑÐ°Ð»Ð¸, ÐºÐ¾Ð³Ð´Ð° Ð´ÑÑÐ³Ð¸Ðµ ÑÐ¾Ð²Ð°ÑÑ Ð¸Ð· Ð·Ð°ÐºÐ°Ð·Ð° Ð±ÑÐ´ÑÑ Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½Ñ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ</p>

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.SUCCESS.boutiqueDescription.Seller=ÐÑ Ð¿ÑÐ¾Ð²ÐµÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ: Ð²ÐµÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð°Ñ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÐµÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. ÐÐ¶Ð¸Ð´Ð°ÐµÑ Ð¿ÐµÑÐµÐ´Ð°ÑÐ¸ Ð½Ð° ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸ Ð² Ð±ÑÑÐ¸Ðº
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.SUCCESS.boutiquePosition.Seller.plural=Ð¢Ð¾Ð²Ð°ÑÑ <b>{0}</b> Ð¿ÑÐ¾ÑÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.SUCCESS.boutiquePosition.Seller.singular=Ð¢Ð¾Ð²Ð°Ñ <b>{0}</b> Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.SUCCESS.boutiqueDescription.Buyer=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.SUCCESS.boutiquePosition.Buyer.plural=Ð¢Ð¾Ð²Ð°ÑÑ <b>{0}</b> Ð¿ÑÐ¾ÑÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.SUCCESS.boutiquePosition.Buyer.singular=Ð¢Ð¾Ð²Ð°Ñ <b>{0}</b> Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.title.Buyer=Ð­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ð° OSKELLY
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.disabledDescription.Buyer.singular=ÐÑ Ð¿ÑÐ¾Ð²ÐµÑÐ¸Ð¼ Ð²ÐµÑÑ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. ÐÑÐ»Ð¸ Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾, Ð¿Ð¾ÑÐ¸ÑÑÐ¸Ð¼, Ð¾ÑÐ¿Ð°ÑÐ¸Ð¼ Ð¸ Ð¿ÑÐ¸Ð²ÐµÐ´ÐµÐ¼ ÐµÐµ Ð² Ð¿Ð¾ÑÑÐ´Ð¾Ðº
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.disabledDescription.Buyer.plural=ÐÑ Ð¿ÑÐ¾Ð²ÐµÑÐ¸Ð¼ Ð²ÐµÑÐ¸ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. ÐÑÐ»Ð¸ Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾, Ð¿Ð¾ÑÐ¸ÑÑÐ¸Ð¼, Ð¾ÑÐ¿Ð°ÑÐ¸Ð¼ Ð¸ Ð¿ÑÐ¸Ð²ÐµÐ´ÐµÐ¼ Ð¸Ñ Ð² Ð¿Ð¾ÑÑÐ´Ð¾Ðº
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.IN_QUEUE.IN_PROGRESS.description.Buyer.plural=ÐÐ°ÐºÐ°Ð· Ð² Ð¾ÑÐ¸ÑÐµ OSKELLY. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð½Ð°ÑÐ½ÐµÐ¼ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ. ÐÑÐ¾Ð²ÐµÑÐ¸Ð¼ Ð²ÐµÑÐ¸ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.IN_QUEUE.IN_PROGRESS.description.Buyer.singular=ÐÐ°ÐºÐ°Ð· Ð² Ð¾ÑÐ¸ÑÐµ OSKELLY. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð½Ð°ÑÐ½ÐµÐ¼ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ. ÐÑÐ¾Ð²ÐµÑÐ¸Ð¼ Ð²ÐµÑÑ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.UNPACKING.IN_PROGRESS.description.Buyer.plural=ÐÐ°ÐºÐ°Ð· Ð² Ð¾ÑÐ¸ÑÐµ OSKELLY. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð½Ð°ÑÐ½ÐµÐ¼ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ. ÐÑÐ¾Ð²ÐµÑÐ¸Ð¼ Ð²ÐµÑÐ¸ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.UNPACKING.IN_PROGRESS.description.Buyer.singular=ÐÐ°ÐºÐ°Ð· Ð² Ð¾ÑÐ¸ÑÐµ OSKELLY. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð½Ð°ÑÐ½ÐµÐ¼ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ. ÐÑÐ¾Ð²ÐµÑÐ¸Ð¼ Ð²ÐµÑÑ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.QUALITY_CONTROL.IN_PROGRESS.description.Buyer.plural=ÐÑÐ¾Ð²ÐµÑÑÐµÐ¼ Ð²ÐµÑÐ¸ Ð½Ð° ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.QUALITY_CONTROL.IN_PROGRESS.description.Buyer.singular=ÐÑÐ¾Ð²ÐµÑÑÐµÐ¼ Ð²ÐµÑÑ Ð½Ð° ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð½Ð¾Ð¼Ñ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.IN_PROGRESS.description.Buyer.plural=ÐÑÐ¾Ð²ÐµÑÑÐµÐ¼ Ð²ÐµÑÐ¸ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.IN_PROGRESS.description.Buyer.singular=ÐÑÐ¾Ð²ÐµÑÑÐµÐ¼ Ð²ÐµÑÑ Ð½Ð° Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.SUCCESS.description.Buyer.plural=ÐÑ Ð¿ÑÐ¾Ð²ÐµÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ: ÑÐ¾Ð²Ð°ÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐµ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÑÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð¸Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.SUCCESS.description.Buyer.singular=ÐÑ Ð¿ÑÐ¾Ð²ÐµÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ: ÑÐ¾Ð²Ð°Ñ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¹ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÐµÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.NOT_ORIGINAL.description.Buyer.plural=<p>Ð¢Ð¾Ð²Ð°ÑÑ <b>{0}</b> Ð½Ðµ ÑÐ²Ð»ÑÑÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»Ð°Ð¼Ð¸.</p>\n<p>ÐÐ¿Ð»Ð°ÑÐµÐ½Ð½Ð°Ñ ÑÑÐ¼Ð¼Ð° Ð·Ð° ÑÐ¾Ð²Ð°ÑÑ Ð±ÑÐ´ÐµÑ ÑÐ°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ð° Ð½Ð° Ð²Ð°ÑÐµÐ¼ Ð±Ð°Ð½ÐºÐ¾Ð²ÑÐºÐ¾Ð¼ ÑÑÐµÑÐµ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ ÑÑÐµÑ Ð´Ð½ÐµÐ¹</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.NOT_ORIGINAL.description.Buyer.singular=<p>Ð¢Ð¾Ð²Ð°Ñ <b>{0}</b> Ð½Ðµ ÑÐ²Ð»ÑÐµÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»Ð¾Ð¼.</p>\n<p>ÐÐ¿Ð»Ð°ÑÐµÐ½Ð½Ð°Ñ ÑÑÐ¼Ð¼Ð° Ð·Ð° ÑÐ¾Ð²Ð°Ñ Ð±ÑÐ´ÐµÑ ÑÐ°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ð° Ð½Ð° Ð²Ð°ÑÐµÐ¼ Ð±Ð°Ð½ÐºÐ¾Ð²ÑÐºÐ¾Ð¼ ÑÑÐµÑÐµ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ ÑÑÐµÑ Ð´Ð½ÐµÐ¹</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.description.Buyer.plural=<p>ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð³Ð»Ð¸ ÑÐ±ÐµÐ´Ð¸ÑÑÑÑ, ÑÑÐ¾ ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b> ÑÐ²Ð»ÑÑÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¼Ð¸. ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð¶ÐµÐ¼ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸ÑÑ Ð¸Ñ Ð²Ð°Ð¼, ÑÐ°Ðº ÐºÐ°Ðº Ð½Ðµ Ð³Ð°ÑÐ°Ð½ÑÐ¸ÑÑÐµÐ¼ 100% Ð¿Ð¾Ð´Ð»Ð¸Ð½Ð½Ð¾ÑÑÑ.</p>\n<p>ÐÐ¿Ð»Ð°ÑÐµÐ½Ð½Ð°Ñ ÑÑÐ¼Ð¼Ð° Ð·Ð° ÑÐ¾Ð²Ð°ÑÑ Ð±ÑÐ´ÐµÑ ÑÐ°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ð° Ð½Ð° Ð²Ð°ÑÐµÐ¼ Ð±Ð°Ð½ÐºÐ¾Ð²ÑÐºÐ¾Ð¼ ÑÑÐµÑÐµ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ ÑÑÐµÑ Ð´Ð½ÐµÐ¹</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.description.Buyer.singular=<p>ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð³Ð»Ð¸ ÑÐ±ÐµÐ´Ð¸ÑÑÑÑ, ÑÑÐ¾ ÑÐ¾Ð²Ð°Ñ <b>{0}</b> ÑÐ²Ð»ÑÐµÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¼. ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð¶ÐµÐ¼ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸ÑÑ ÐµÐ³Ð¾ Ð²Ð°Ð¼, ÑÐ°Ðº ÐºÐ°Ðº Ð½Ðµ Ð³Ð°ÑÐ°Ð½ÑÐ¸ÑÑÐµÐ¼ 100% Ð¿Ð¾Ð´Ð»Ð¸Ð½Ð½Ð¾ÑÑÑ.</p>\n<p>ÐÐ¿Ð»Ð°ÑÐµÐ½Ð½Ð°Ñ ÑÑÐ¼Ð¼Ð° Ð·Ð° ÑÐ¾Ð²Ð°ÑÑ Ð±ÑÐ´ÐµÑ ÑÐ°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ð° Ð½Ð° Ð²Ð°ÑÐµÐ¼ Ð±Ð°Ð½ÐºÐ¾Ð²ÑÐºÐ¾Ð¼ ÑÑÐµÑÐµ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ ÑÑÐµÑ Ð´Ð½ÐµÐ¹</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.IN_PROGRESS.position.Buyer=
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.SUCCESS.position.Buyer.plural=Ð¢Ð¾Ð²Ð°ÑÑ <b>{0}</b> ÑÐ²Ð»ÑÑÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»Ð¾Ð¼ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÑÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð¸Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.SUCCESS.position.Buyer.singular=Ð¢Ð¾Ð²Ð°Ñ <b>{0}</b> ÑÐ²Ð»ÑÐµÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»Ð¾Ð¼ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÐµÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.NOT_ORIGINAL.position.Buyer.plural=<p>Ð¢Ð¾Ð²Ð°ÑÑ <b>{0}</b> Ð½Ðµ ÑÐ²Ð»ÑÑÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»Ð¾Ð¼.</p>\n<p>ÐÐ¿Ð»Ð°ÑÐµÐ½Ð½Ð°Ñ ÑÑÐ¼Ð¼Ð° Ð·Ð° ÑÐ¾Ð²Ð°ÑÑ Ð±ÑÐ´ÐµÑ ÑÐ°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ð° Ð½Ð° Ð²Ð°ÑÐµÐ¼ Ð±Ð°Ð½ÐºÐ¾Ð²ÑÐºÐ¾Ð¼ ÑÑÐµÑÐµ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ ÑÑÐµÑ Ð´Ð½ÐµÐ¹</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.NOT_ORIGINAL.position.Buyer.singular=<p>Ð¢Ð¾Ð²Ð°Ñ <b>{0}</b> Ð½Ðµ ÑÐ²Ð»ÑÐµÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»Ð¾Ð¼.</p>\n<p>ÐÐ¿Ð»Ð°ÑÐµÐ½Ð½Ð°Ñ ÑÑÐ¼Ð¼Ð° Ð·Ð° ÑÐ¾Ð²Ð°Ñ Ð±ÑÐ´ÐµÑ ÑÐ°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ð° Ð½Ð° Ð²Ð°ÑÐµÐ¼ Ð±Ð°Ð½ÐºÐ¾Ð²ÑÐºÐ¾Ð¼ ÑÑÐµÑÐµ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ ÑÑÐµÑ Ð´Ð½ÐµÐ¹</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.position.Buyer.plural=<p>ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð³Ð»Ð¸ ÑÐ±ÐµÐ´Ð¸ÑÑÑÑ, ÑÑÐ¾ ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b> ÑÐ²Ð»ÑÑÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¼Ð¸. ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð¶ÐµÐ¼ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸ÑÑ Ð¸Ñ Ð²Ð°Ð¼, ÑÐ°Ðº ÐºÐ°Ðº Ð½Ðµ Ð³Ð°ÑÐ°Ð½ÑÐ¸ÑÑÐµÐ¼ 100% Ð¿Ð¾Ð´Ð»Ð¸Ð½Ð½Ð¾ÑÑÑ.</p>\n<p>ÐÐ¿Ð»Ð°ÑÐµÐ½Ð½Ð°Ñ ÑÑÐ¼Ð¼Ð° Ð·Ð° ÑÐ¾Ð²Ð°ÑÑ Ð±ÑÐ´ÐµÑ ÑÐ°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ð° Ð½Ð° Ð²Ð°ÑÐµÐ¼ Ð±Ð°Ð½ÐºÐ¾Ð²ÑÐºÐ¾Ð¼ ÑÑÐµÑÐµ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ ÑÑÐµÑ Ð´Ð½ÐµÐ¹</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.position.Buyer.singular=<p>ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð³Ð»Ð¸ ÑÐ±ÐµÐ´Ð¸ÑÑÑÑ, ÑÑÐ¾ ÑÐ¾Ð²Ð°Ñ <b>{0}</b> ÑÐ²Ð»ÑÐµÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¼. ÐÑ Ð½Ðµ ÑÐ¼Ð¾Ð¶ÐµÐ¼ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸ÑÑ ÐµÐ³Ð¾ Ð²Ð°Ð¼, ÑÐ°Ðº ÐºÐ°Ðº Ð½Ðµ Ð³Ð°ÑÐ°Ð½ÑÐ¸ÑÑÐµÐ¼ 100% Ð¿Ð¾Ð´Ð»Ð¸Ð½Ð½Ð¾ÑÑÑ.</p>\n<p>ÐÐ¿Ð»Ð°ÑÐµÐ½Ð½Ð°Ñ ÑÑÐ¼Ð¼Ð° Ð·Ð° ÑÐ¾Ð²Ð°Ñ Ð±ÑÐ´ÐµÑ ÑÐ°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ð° Ð½Ð° Ð²Ð°ÑÐµÐ¼ Ð±Ð°Ð½ÐºÐ¾Ð²ÑÐºÐ¾Ð¼ ÑÑÐµÑÐµ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ ÑÑÐµÑ Ð´Ð½ÐµÐ¹</p>

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.IN_PROGRESS.description.Buyer.plural=ÐÐµÑÐ¸ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐµ, Ð½Ð¾ Ð¼Ñ Ð½Ð°ÑÐ»Ð¸ Ð½ÑÐ°Ð½ÑÑ, ÐºÐ¾ÑÐ¾ÑÑÑ Ð½Ðµ Ð±ÑÐ»Ð¾ Ð² Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ð¸. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ñ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ð¸ Ð¾Ð±ÑÑÐ´Ð¸Ð¼ Ð´ÐµÑÐ°Ð»Ð¸
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.IN_PROGRESS.description.Buyer.singular=ÐÐµÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð°Ñ, Ð½Ð¾ Ð¼Ñ Ð½Ð°ÑÐ»Ð¸ Ð½ÑÐ°Ð½ÑÑ, ÐºÐ¾ÑÐ¾ÑÑÑ Ð½Ðµ Ð±ÑÐ»Ð¾ Ð² Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ð¸. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ñ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ð¸ Ð¾Ð±ÑÑÐ´Ð¸Ð¼ Ð´ÐµÑÐ°Ð»Ð¸
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.description.Buyer.plural=Ð¢Ð¾Ð²Ð°ÑÑ <b>{0}</b> ÑÐµÐ¿ÐµÑÑ ÑÑÐ¾ÑÑ Ð½Ð° {1} Ð¼ÐµÐ½ÑÑÐµ Ñ ÑÑÐµÑÐ¾Ð¼ Ð½ÑÐ°Ð½ÑÐ¾Ð². Ð Ð°Ð·Ð½Ð¸ÑÑ Ð²ÐµÑÐ½ÐµÐ¼ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð¸Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.description.Buyer.singular=Ð¢Ð¾Ð²Ð°Ñ <b>{0}</b> ÑÐµÐ¿ÐµÑÑ ÑÑÐ¾Ð¸Ñ Ð½Ð° {1} Ð¼ÐµÐ½ÑÑÐµ Ñ ÑÑÐµÑÐ¾Ð¼ Ð½ÑÐ°Ð½ÑÐ¾Ð². Ð Ð°Ð·Ð½Ð¸ÑÑ Ð²ÐµÑÐ½ÐµÐ¼ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.zeroDiscount.description.Buyer.plural=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ ÑÐ¾Ð²Ð°ÑÐ°Ð¼ <b>{0}</b>. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð¸Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.zeroDiscount.description.Buyer.singular=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b>. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.FAILED.description.Buyer.plural=ÐÑÐ°Ð½ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ, Ð·Ð°ÐºÐ°Ð· Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ. ÐÐµÐ½ÑÐ³Ð¸ Ð¼Ñ Ð²ÐµÑÐ½ÑÐ»Ð¸ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.FAILED.description.Buyer.singular=ÐÑÐ°Ð½ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ, Ð·Ð°ÐºÐ°Ð· Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ. ÐÐµÐ½ÑÐ³Ð¸ Ð¼Ñ Ð²ÐµÑÐ½ÑÐ»Ð¸ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.IN_PROGRESS.description.Buyer.plural=ÐÐµÑÐ¸ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐµ, Ð½Ð¾ Ð¼Ñ Ð½Ð°ÑÐ»Ð¸ Ð½ÑÐ°Ð½ÑÑ, ÐºÐ¾ÑÐ¾ÑÑÑ Ð½Ðµ Ð±ÑÐ»Ð¾ Ð² Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ð¸. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ñ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ð¸ Ð¾Ð±ÑÑÐ´Ð¸Ð¼ Ð´ÐµÑÐ°Ð»Ð¸
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.IN_PROGRESS.description.Buyer.singular=ÐÐµÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð°Ñ, Ð½Ð¾ Ð¼Ñ Ð½Ð°ÑÐ»Ð¸ Ð½ÑÐ°Ð½ÑÑ, ÐºÐ¾ÑÐ¾ÑÑÑ Ð½Ðµ Ð±ÑÐ»Ð¾ Ð² Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ð¸. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ñ Ð²Ð°Ð¼Ð¸ ÑÐ²ÑÐ¶ÐµÐ¼ÑÑ Ð¸ Ð¾Ð±ÑÑÐ´Ð¸Ð¼ Ð´ÐµÑÐ°Ð»Ð¸
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.description.Buyer.plural=Ð¢Ð¾Ð²Ð°ÑÑ <b>{0}</b> ÑÐµÐ¿ÐµÑÑ ÑÑÐ¾ÑÑ Ð½Ð° {1} Ð¼ÐµÐ½ÑÑÐµ Ñ ÑÑÐµÑÐ¾Ð¼ Ð½ÑÐ°Ð½ÑÐ¾Ð². Ð Ð°Ð·Ð½Ð¸ÑÑ Ð²ÐµÑÐ½ÐµÐ¼ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð¸Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.description.Buyer.singular=Ð¢Ð¾Ð²Ð°Ñ <b>{0}</b> ÑÐµÐ¿ÐµÑÑ ÑÑÐ¾Ð¸Ñ Ð½Ð° {1} Ð¼ÐµÐ½ÑÑÐµ Ñ ÑÑÐµÑÐ¾Ð¼ Ð½ÑÐ°Ð½ÑÐ¾Ð². Ð Ð°Ð·Ð½Ð¸ÑÑ Ð²ÐµÑÐ½ÐµÐ¼ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.zeroDiscount.description.Buyer.plural=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ ÑÐ¾Ð²Ð°ÑÐ°Ð¼ <b>{0}</b>. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð¸Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.zeroDiscount.description.Buyer.singular=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b>. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.FAILED.description.Buyer.plural=ÐÑÐ°Ð½ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ, Ð·Ð°ÐºÐ°Ð· Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ. ÐÐµÐ½ÑÐ³Ð¸ Ð¼Ñ Ð²ÐµÑÐ½ÑÐ»Ð¸ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.FAILED.description.Buyer.singular=ÐÑÐ°Ð½ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ, Ð·Ð°ÐºÐ°Ð· Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ. ÐÐµÐ½ÑÐ³Ð¸ Ð¼Ñ Ð²ÐµÑÐ½ÑÐ»Ð¸ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.IN_PROGRESS.position.Buyer.plural=Ð¢Ð¾Ð²Ð°ÑÑ <b>{0}</b> ÑÐ²Ð»ÑÑÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»Ð°Ð¼Ð¸, Ð½Ð¾ Ð¼Ñ Ð½Ð°ÑÐ»Ð¸ Ð½ÑÐ°Ð½ÑÑ, ÐºÐ¾ÑÐ¾ÑÑÑ Ð½Ðµ Ð±ÑÐ»Ð¾ Ð² Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ð¸. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð½Ð°Ð¿Ð¸ÑÐµÐ¼ Ð²Ð°Ð¼ Ð¸ Ð¾Ð±ÑÑÐ´Ð¸Ð¼, ÑÑÐ¾ Ð¼Ð¾Ð¶Ð½Ð¾ ÑÐ´ÐµÐ»Ð°ÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.IN_PROGRESS.position.Buyer.singular=Ð¢Ð¾Ð²Ð°Ñ <b>{0}</b> ÑÐ²Ð»ÑÐµÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»Ð¾Ð¼, Ð½Ð¾ Ð¼Ñ Ð½Ð°ÑÐ»Ð¸ Ð½ÑÐ°Ð½ÑÑ, ÐºÐ¾ÑÐ¾ÑÑÑ Ð½Ðµ Ð±ÑÐ»Ð¾ Ð² Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ð¸. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð½Ð°Ð¿Ð¸ÑÐµÐ¼ Ð²Ð°Ð¼ Ð¸ Ð¾Ð±ÑÑÐ´Ð¸Ð¼, ÑÑÐ¾ Ð¼Ð¾Ð¶Ð½Ð¾ ÑÐ´ÐµÐ»Ð°ÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.position.Buyer.plural=Ð¢Ð¾Ð²Ð°ÑÑ <b>{0}</b> ÑÐµÐ¿ÐµÑÑ ÑÑÐ¾ÑÑ Ð½Ð° {1} Ð¼ÐµÐ½ÑÑÐµ Ñ ÑÑÐµÑÐ¾Ð¼ Ð½ÑÐ°Ð½ÑÐ¾Ð². Ð Ð°Ð·Ð½Ð¸ÑÑ Ð²ÐµÑÐ½ÐµÐ¼ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð¸Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.position.Buyer.singular=Ð¢Ð¾Ð²Ð°Ñ <b>{0}</b> ÑÐµÐ¿ÐµÑÑ ÑÑÐ¾Ð¸Ñ Ð½Ð° {1} Ð¼ÐµÐ½ÑÑÐµ Ñ ÑÑÐµÑÐ¾Ð¼ Ð½ÑÐ°Ð½ÑÐ¾Ð². Ð Ð°Ð·Ð½Ð¸ÑÑ Ð²ÐµÑÐ½ÐµÐ¼ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.zeroDiscount.position.Buyer.plural=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ ÑÐ¾Ð²Ð°ÑÐ°Ð¼ <b>{0}</b>. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð¸Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.zeroDiscount.position.Buyer.singular=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b>. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.FAILED.position.Buyer.plural=ÐÑÐ°Ð½ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ, ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b> Ð½Ðµ Ð¿ÑÐ¾ÑÐ»Ð¸ Ð¿ÑÐ¾Ð²ÐµÑÐºÑ. ÐÐµÐ½ÑÐ³Ð¸ Ð¼Ñ Ð²ÐµÑÐ½ÑÐ»Ð¸ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.FAILED.position.Buyer.singular=ÐÑÐ°Ð½ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ, ÑÐ¾Ð²Ð°Ñ <b>{0}</b> Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ. ÐÐµÐ½ÑÐ³Ð¸ Ð¼Ñ Ð²ÐµÑÐ½ÑÐ»Ð¸ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.IN_PROGRESS.position.Buyer.plural=Ð¢Ð¾Ð²Ð°ÑÑ <b>{0}</b> ÑÐ²Ð»ÑÑÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»Ð°Ð¼Ð¸, Ð½Ð¾ Ð¼Ñ Ð½Ð°ÑÐ»Ð¸ Ð½ÑÐ°Ð½ÑÑ, ÐºÐ¾ÑÐ¾ÑÑÑ Ð½Ðµ Ð±ÑÐ»Ð¾ Ð² Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ð¸. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð½Ð°Ð¿Ð¸ÑÐµÐ¼ Ð²Ð°Ð¼ Ð¸ Ð¾Ð±ÑÑÐ´Ð¸Ð¼, ÑÑÐ¾ Ð¼Ð¾Ð¶Ð½Ð¾ ÑÐ´ÐµÐ»Ð°ÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.IN_PROGRESS.position.Buyer.singular=Ð¢Ð¾Ð²Ð°Ñ <b>{0}</b> ÑÐ²Ð»ÑÐµÑÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»Ð¾Ð¼, Ð½Ð¾ Ð¼Ñ Ð½Ð°ÑÐ»Ð¸ Ð½ÑÐ°Ð½ÑÑ, ÐºÐ¾ÑÐ¾ÑÑÑ Ð½Ðµ Ð±ÑÐ»Ð¾ Ð² Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ð¸. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð½Ð°Ð¿Ð¸ÑÐµÐ¼ Ð²Ð°Ð¼ Ð¸ Ð¾Ð±ÑÑÐ´Ð¸Ð¼, ÑÑÐ¾ Ð¼Ð¾Ð¶Ð½Ð¾ ÑÐ´ÐµÐ»Ð°ÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.position.Buyer.plural=Ð¢Ð¾Ð²Ð°ÑÑ <b>{0}</b> ÑÐµÐ¿ÐµÑÑ ÑÑÐ¾ÑÑ Ð½Ð° {1} Ð¼ÐµÐ½ÑÑÐµ Ñ ÑÑÐµÑÐ¾Ð¼ Ð½ÑÐ°Ð½ÑÐ¾Ð². Ð Ð°Ð·Ð½Ð¸ÑÑ Ð²ÐµÑÐ½ÐµÐ¼ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð¸Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.position.Buyer.singular=Ð¢Ð¾Ð²Ð°Ñ <b>{0}</b> ÑÐµÐ¿ÐµÑÑ ÑÑÐ¾Ð¸Ñ Ð½Ð° {1} Ð¼ÐµÐ½ÑÑÐµ Ñ ÑÑÐµÑÐ¾Ð¼ Ð½ÑÐ°Ð½ÑÐ¾Ð². Ð Ð°Ð·Ð½Ð¸ÑÑ Ð²ÐµÑÐ½ÐµÐ¼ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.zeroDiscount.position.Buyer.plural=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ ÑÐ¾Ð²Ð°ÑÐ°Ð¼ <b>{0}</b>. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð¸Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.zeroDiscount.position.Buyer.singular=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð»Ð¸ Ð½ÑÐ°Ð½ÑÑ Ð¿Ð¾ ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b>. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ðº Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.FAILED.position.Buyer.plural=ÐÑÐ°Ð½ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ, ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b> Ð½Ðµ Ð¿ÑÐ¾ÑÐ»Ð¸ Ð¿ÑÐ¾Ð²ÐµÑÐºÑ. ÐÐµÐ½ÑÐ³Ð¸ Ð¼Ñ Ð²ÐµÑÐ½ÑÐ»Ð¸ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.FAILED.position.Buyer.singular=ÐÑÐ°Ð½ÑÑ Ð½Ðµ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ, ÑÐ¾Ð²Ð°Ñ <b>{0}</b> Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ. ÐÐµÐ½ÑÐ³Ð¸ Ð¼Ñ Ð²ÐµÑÐ½ÑÐ»Ð¸ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PRESELLING_PREPARATION.IN_PROGRESS.description.Buyer.plural=ÐÑ Ð¿ÑÐ¾Ð²ÐµÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ: ÑÐ¾Ð²Ð°ÑÑ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐµ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÑÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð¸Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PRESELLING_PREPARATION.IN_PROGRESS.description.Buyer.singular=ÐÑ Ð¿ÑÐ¾Ð²ÐµÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ: ÑÐ¾Ð²Ð°Ñ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¹ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÐµÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PRESELLING_PREPARATION.IN_PROGRESS.position.Buyer.plural=ÐÑ Ð¿ÑÐ¾Ð²ÐµÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ: ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b> Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐµ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÑÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ Ð¸Ñ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PRESELLING_PREPARATION.IN_PROGRESS.position.Buyer.singular=ÐÑ Ð¿ÑÐ¾Ð²ÐµÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ: ÑÐ¾Ð²Ð°Ñ <b>{0}</b> Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¹ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÐµÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. Ð¡ÐµÐ¹ÑÐ°Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ´Ð°Ð´Ð¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿ÑÐµÐ´Ð¿ÑÐ¾Ð´Ð°Ð¶Ð½ÑÐ¹ ÑÑÐ¾Ð´, ÑÐ¿Ð°ÐºÑÐµÐ¼ Ð¸ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð¼ Ð²Ð°Ð¼

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PACKING.IN_PROGRESS.description.Buyer.plural=Ð£Ð¿Ð°ÐºÐ¾Ð²ÑÐ²Ð°ÐµÐ¼ Ð²ÐµÑÐ¸, ÑÑÐ¾Ð±Ñ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸ÑÑ Ðº Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PACKING.IN_PROGRESS.description.Buyer.singular=Ð£Ð¿Ð°ÐºÐ¾Ð²ÑÐ²Ð°ÐµÐ¼ Ð²ÐµÑÑ, ÑÑÐ¾Ð±Ñ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸ÑÑ Ðº Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PACKING.IN_PROGRESS.position.Buyer.plural=Ð£Ð¿Ð°ÐºÐ¾Ð²ÑÐ²Ð°ÐµÐ¼ ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b>, ÑÑÐ¾Ð±Ñ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸ÑÑ Ðº Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PACKING.IN_PROGRESS.position.Buyer.singular=Ð£Ð¿Ð°ÐºÐ¾Ð²ÑÐ²Ð°ÐµÐ¼ ÑÐ¾Ð²Ð°Ñ <b>{0}</b>, ÑÑÐ¾Ð±Ñ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸ÑÑ Ðº Ð²Ð°Ð¼

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.SUCCESS.description.Buyer=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ Ð¸ Ð³Ð¾ÑÐ¾Ð² Ðº Ð¾ÑÐ¿ÑÐ°Ð²ÐºÐµ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.description.Buyer.plural=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.description.Buyer.singular=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.SUCCESS.position.Buyer.plural=Ð¢Ð¾Ð²Ð°ÑÑ <b>{0}</b> Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐµ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÑÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. ÐÑ Ð¿Ð¾Ð´Ð³Ð¾ÑÐ¾Ð²Ð¸Ð»Ð¸ Ð¸Ñ Ðº Ð¾ÑÐ¿ÑÐ°Ð²ÐºÐµ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.SUCCESS.position.Buyer.singular=Ð¢Ð¾Ð²Ð°Ñ <b>{0}</b> Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¹ Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÐµÑ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ. ÐÑ Ð¿Ð¾Ð´Ð³Ð¾ÑÐ¾Ð²Ð¸Ð»Ð¸ ÐµÐ³Ð¾ Ðº Ð¾ÑÐ¿ÑÐ°Ð²ÐºÐµ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.position.Buyer.plural=Ð¢Ð¾Ð²Ð°ÑÑ <b>{0}</b> Ð½Ðµ Ð¿ÑÐ¾ÑÐ»Ð¸ Ð¿ÑÐ¾Ð²ÐµÑÐºÑ. ÐÑ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð»Ð¸ Ð²ÐµÑÐ¸ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.position.Buyer.singular=Ð¢Ð¾Ð²Ð°Ñ <b>{0}</b> Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» Ð¿ÑÐ¾Ð²ÐµÑÐºÑ. ÐÑ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð»Ð¸ Ð²ÐµÑÑ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÑ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.DESTROYED.description.Buyer.singular=<p>Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, Ð¿Ð¾ ÑÐµÑÐ½Ð¸ÑÐµÑÐºÐ¸Ð¼ Ð¿ÑÐ¸ÑÐ¸Ð½Ð°Ð¼ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÐ¼ Ð´Ð¾ÑÑÐ°Ð²Ð¸ÑÑ Ð²Ð°Ð¼ Ð·Ð°ÐºÐ°Ð·</p>\n<p>ÐÐ¿Ð»Ð°ÑÐµÐ½Ð½Ð°Ñ ÑÑÐ¼Ð¼Ð° Ð·Ð° ÑÐ¾Ð²Ð°Ñ Ð±ÑÐ´ÐµÑ ÑÐ°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ð° Ð½Ð° Ð²Ð°ÑÐµÐ¼ ÑÑÐµÑÐµ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ ÑÑÐµÑ Ð´Ð½ÐµÐ¹</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.DESTROYED.description.Buyer.plural=<p>Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, Ð¿Ð¾ ÑÐµÑÐ½Ð¸ÑÐµÑÐºÐ¸Ð¼ Ð¿ÑÐ¸ÑÐ¸Ð½Ð°Ð¼ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÐ¼ Ð´Ð¾ÑÑÐ°Ð²Ð¸ÑÑ Ð²Ð°Ð¼ Ð·Ð°ÐºÐ°Ð·</p>\n<p>ÐÐ¿Ð»Ð°ÑÐµÐ½Ð½Ð°Ñ ÑÑÐ¼Ð¼Ð° Ð·Ð° ÑÐ¾Ð²Ð°Ñ Ð±ÑÐ´ÐµÑ ÑÐ°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ð° Ð½Ð° Ð²Ð°ÑÐµÐ¼ ÑÑÐµÑÐµ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ ÑÑÐµÑ Ð´Ð½ÐµÐ¹</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.DESTROYED.position.Buyer.singular=<p>Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, Ð¿Ð¾ ÑÐµÑÐ½Ð¸ÑÐµÑÐºÐ¸Ð¼ Ð¿ÑÐ¸ÑÐ¸Ð½Ð°Ð¼ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÐ¼ Ð´Ð¾ÑÑÐ°Ð²Ð¸ÑÑ Ð²Ð°Ð¼ ÑÐ°ÑÑÑ Ð·Ð°ÐºÐ°Ð·Ð°, Ð³Ð´Ðµ ÐµÑÑÑ ÑÐ¾Ð²Ð°Ñ <b>{0}</b></p>\n<p>ÐÐ¿Ð»Ð°ÑÐµÐ½Ð½Ð°Ñ ÑÑÐ¼Ð¼Ð° Ð·Ð° ÑÐ¾Ð²Ð°Ñ Ð±ÑÐ´ÐµÑ ÑÐ°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ð° Ð½Ð° Ð²Ð°ÑÐµÐ¼ ÑÑÐµÑÐµ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ ÑÑÐµÑ Ð´Ð½ÐµÐ¹</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.DESTROYED.position.Buyer.plural=<p>Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, Ð¿Ð¾ ÑÐµÑÐ½Ð¸ÑÐµÑÐºÐ¸Ð¼ Ð¿ÑÐ¸ÑÐ¸Ð½Ð°Ð¼ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÐ¼ Ð´Ð¾ÑÑÐ°Ð²Ð¸ÑÑ Ð²Ð°Ð¼ ÑÐ°ÑÑÑ Ð·Ð°ÐºÐ°Ð·Ð°, Ð³Ð´Ðµ ÐµÑÑÑ ÑÐ¾Ð²Ð°ÑÑ <b>{0}</b></p>\n<p>ÐÐ¿Ð»Ð°ÑÐµÐ½Ð½Ð°Ñ ÑÑÐ¼Ð¼Ð° Ð·Ð° ÑÐ¾Ð²Ð°Ñ Ð±ÑÐ´ÐµÑ ÑÐ°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ð° Ð½Ð° Ð²Ð°ÑÐµÐ¼ ÑÑÐµÑÐµ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ ÑÑÐµÑ Ð´Ð½ÐµÐ¹</p>

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PREPARATION_FOR_PUBLICATION.IN_PROGRESS.description.Seller.plural=Ð¢Ð¾Ð²Ð°ÑÑ ÑÑÐ¿ÐµÑÐ½Ð¾ Ð¿ÑÐ¾ÑÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÑÐµÐ¼ Ð¸Ñ Ð½Ð° Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ðµ: ÑÑÐ¾ÑÐ¾Ð³ÑÐ°ÑÐ¸ÑÑÐµÐ¼ Ð¸ Ð·Ð°Ð¿Ð¾Ð»Ð½Ð¸Ð¼ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PREPARATION_FOR_PUBLICATION.IN_PROGRESS.description.Seller.singular=Ð¢Ð¾Ð²Ð°Ñ ÑÑÐ¿ÐµÑÐ½Ð¾ Ð¿ÑÐ¾ÑÑÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÑÐµÐ¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ðµ: ÑÑÐ¾ÑÐ¾Ð³ÑÐ°ÑÐ¸ÑÑÐµÐ¼ Ð¸ Ð·Ð°Ð¿Ð¾Ð»Ð½Ð¸Ð¼ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PREPARATION_FOR_PUBLICATION.IN_PROGRESS.position.Seller.plural=Ð¢Ð¾Ð²Ð°ÑÑ <b>{0}</b> ÑÑÐ¿ÐµÑÐ½Ð¾ Ð¿ÑÐ¾ÑÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÑÐµÐ¼ Ð¸Ñ Ð½Ð° Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ðµ: ÑÑÐ¾ÑÐ¾Ð³ÑÐ°ÑÐ¸ÑÑÐµÐ¼ Ð¸ Ð·Ð°Ð¿Ð¾Ð»Ð½Ð¸Ð¼ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PREPARATION_FOR_PUBLICATION.IN_PROGRESS.position.Seller.singular=Ð¢Ð¾Ð²Ð°Ñ <b>{0}</b> ÑÑÐ¿ÐµÑÐ½Ð¾ Ð¿ÑÐ¾ÑÑÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ. Ð¡ÐºÐ¾ÑÐ¾ Ð¼Ñ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÑÐµÐ¼ ÐµÐ³Ð¾ Ð½Ð° Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ðµ: ÑÑÐ¾ÑÐ¾Ð³ÑÐ°ÑÐ¸ÑÑÐµÐ¼ Ð¸ Ð·Ð°Ð¿Ð¾Ð»Ð½Ð¸Ð¼ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.WAIT_DELIVERY_TO_STOCK.IN_PROGRESS.description.Seller.plural=Ð¢Ð¾Ð²Ð°ÑÑ ÑÑÐ¿ÐµÑÐ½Ð¾ Ð¿ÑÐ¾ÑÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ Ð¸ Ð³Ð¾ÑÐ¾Ð²Ñ Ðº Ð¿ÑÐ±Ð»Ð¸ÐºÐ°ÑÐ¸Ð¸. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ Ð¸Ñ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.WAIT_DELIVERY_TO_STOCK.IN_PROGRESS.description.Seller.singular=Ð¢Ð¾Ð²Ð°Ñ ÑÑÐ¿ÐµÑÐ½Ð¾ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ Ð¸ Ð³Ð¾ÑÐ¾Ð² Ðº Ð¿ÑÐ±Ð»Ð¸ÐºÐ°ÑÐ¸Ð¸. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.WAIT_DELIVERY_TO_STOCK.IN_PROGRESS.position.Seller.plural=Ð¢Ð¾Ð²Ð°ÑÑ <b>{0}</b> ÑÑÐ¿ÐµÑÐ½Ð¾ Ð¿ÑÐ¾ÑÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ Ð¸ Ð³Ð¾ÑÐ¾Ð²Ñ Ðº Ð¿ÑÐ±Ð»Ð¸ÐºÐ°ÑÐ¸Ð¸. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ Ð¸Ñ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.WAIT_DELIVERY_TO_STOCK.IN_PROGRESS.position.Seller.singular=Ð¢Ð¾Ð²Ð°Ñ <b>{0}</b> ÑÑÐ¿ÐµÑÐ½Ð¾ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ Ð¸ Ð³Ð¾ÑÐ¾Ð² Ðº Ð¿ÑÐ±Ð»Ð¸ÐºÐ°ÑÐ¸Ð¸. Ð Ð±Ð»Ð¸Ð¶Ð°Ð¹ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ ÐµÐ³Ð¾ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.comment.WillBeCompletedOn.singular=ÐÐ°ÐºÐ¾Ð½ÑÐ¸Ð¼ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ ~ {0}
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.comment.WillBeCompletedOn.plural=ÐÐ°ÐºÐ¾Ð½ÑÐ¸Ð¼ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ {0}
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.comment.WillBeCompletedOn.Delayed=Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, ÑÑÐ¾ÐºÐ¸ ÑÐ²ÐµÐ»Ð¸ÑÐµÐ½Ñ, Ð·Ð°ÐºÐ¾Ð½ÑÐ¸Ð¼ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ Ð¾ÑÐ¸ÐµÐ½ÑÐ¸ÑÐ¾Ð²Ð¾ÑÐ½Ð¾ {0}



service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.title.Seller=ÐÐ¾ÑÑÐ°Ð²ÐºÐ° Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.disabledDescription.Seller.singular=ÐÐ¾ÑÑÐ°Ð²Ð¸Ð¼ Ð·Ð°ÐºÐ°Ð· Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð² ÑÐ´Ð¾Ð±Ð½Ð¾Ðµ Ð´Ð»Ñ Ð½ÐµÐ³Ð¾ Ð²ÑÐµÐ¼Ñ
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.disabledDescription.Seller.plural=ÐÐ¾ÑÑÐ°Ð²Ð¸Ð¼ Ð·Ð°ÐºÐ°Ð· Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð² ÑÐ´Ð¾Ð±Ð½Ð¾Ðµ Ð´Ð»Ñ Ð½ÐµÐ³Ð¾ Ð²ÑÐµÐ¼Ñ
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.PICKING_UP_FROM_OFFICE.description.Seller=ÐÐ´ÐµÐ¼ ÐºÑÑÑÐµÑÐ°, ÑÑÐ¾Ð±Ñ Ð¾ÑÐ´Ð°ÑÑ ÐµÐ¼Ñ Ð·Ð°ÐºÐ°Ð·
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.FROM_OFFICE_TO_BUYER.description.Seller=ÐÑÑÑÐµÑ ÐµÐ´ÐµÑ Ðº Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.TO_BUYER_DELIVERY_FAIL.description.Seller=Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, Ð·Ð°ÐºÐ°Ð· Ð±ÑÐ» Ð¾ÑÐ¼ÐµÐ½ÐµÐ½. ÐÑ Ð½Ð°Ð¿Ð¸ÑÐµÐ¼ Ð²Ð°Ð¼ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ ÑÑÑÐ¾Ðº, ÑÑÐ¾Ð±Ñ Ð¾Ð±ÑÑÐ´Ð¸ÑÑ ÑÐ¸ÑÑÐ°ÑÐ¸Ñ
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.TO_BUYER_DELIVERY_RETURNED.description.Seller=ÐÐ°ÐºÐ°Ð· Ð±ÑÐ» Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµÐ½
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.DELIVERED_TO_BUYER.description.Seller=ÐÐ¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð¿Ð¾Ð»ÑÑÐ¸Ð» Ð·Ð°ÐºÐ°Ð·
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.description.trackingPart.Seller=ÐÑÑÐ»ÐµÐ¶Ð¸Ð²Ð°Ð¹ÑÐµ Ð¿Ð¾ Ð½Ð¾Ð¼ÐµÑÑ {0}

service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.title.Buyer=ÐÐ¾ÑÑÐ°Ð²ÐºÐ° Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.disabledDescription.Buyer.singular=ÐÐ¾ÑÑÐ°Ð²Ð¸Ð¼ Ð·Ð°ÐºÐ°Ð· Ð² Ð½Ð°ÑÐµÐ¹ ÑÐ¸ÑÐ¼ÐµÐ½Ð½Ð¾Ð¹ ÑÐ¿Ð°ÐºÐ¾Ð²ÐºÐµ Ð² ÑÐ´Ð¾Ð±Ð½Ð¾Ðµ Ð´Ð»Ñ Ð²Ð°Ñ Ð²ÑÐµÐ¼Ñ
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.disabledDescription.Buyer.plural=ÐÐ¾ÑÑÐ°Ð²Ð¸Ð¼ Ð·Ð°ÐºÐ°Ð· Ð² Ð½Ð°ÑÐµÐ¹ ÑÐ¸ÑÐ¼ÐµÐ½Ð½Ð¾Ð¹ ÑÐ¿Ð°ÐºÐ¾Ð²ÐºÐµ Ð² ÑÐ´Ð¾Ð±Ð½Ð¾Ðµ Ð´Ð»Ñ Ð²Ð°Ñ Ð²ÑÐµÐ¼Ñ
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.PICKING_UP_FROM_OFFICE.description.Buyer=ÐÐ´ÐµÐ¼ ÐºÑÑÑÐµÑÐ°, ÑÑÐ¾Ð±Ñ Ð¾ÑÐ´Ð°ÑÑ ÐµÐ¼Ñ Ð·Ð°ÐºÐ°Ð·
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.FROM_OFFICE_TO_BUYER.description.Buyer=ÐÑÑÑÐµÑ ÐµÐ´ÐµÑ Ðº Ð²Ð°Ð¼
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.FROM_OFFICE_TO_BUYER.description.extended.Buyer=ÐÑÑÑÐµÑ Ð¿ÑÐ¸ÐµÐ´ÐµÑ Ðº Ð²Ð°Ð¼ {0}. ÐÑÑÑÐµÑÑÐºÐ°Ñ ÑÐ»ÑÐ¶Ð±Ð° ÑÐ²ÑÐ¶ÐµÑÑÑ Ñ Ð²Ð°Ð¼Ð¸, ÑÑÐ¾Ð±Ñ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°ÑÑ Ð²ÑÐµÐ¼Ñ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.FROM_OFFICE_TO_BUYER.description.delayed.Buyer=ÐÐ¾ÑÑÐ°Ð²ÐºÐ° Ð·Ð°Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÐµÑÑÑ. ÐÑÑÑÐµÑÑÐºÐ°Ñ ÑÐ»ÑÐ¶Ð±Ð° ÑÐ²ÑÐ¶ÐµÑÑÑ Ñ Ð²Ð°Ð¼Ð¸, ÑÑÐ¾Ð±Ñ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°ÑÑ Ð²ÑÐµÐ¼Ñ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.TO_BUYER_DELIVERY_FAIL.description.Buyer=<p>ðÐ ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, Ð½Ðµ ÑÐ¼Ð¾Ð¶ÐµÐ¼ Ð¿ÑÐ¸Ð²ÐµÐ·ÑÐ¸ Ð²Ð°Ð¼ Ð·Ð°ÐºÐ°Ð·. ÐÐ¾Ð·Ð½Ð¸ÐºÐ»Ð¸ ÑÐ»Ð¾Ð¶Ð½Ð¾ÑÑÐ¸ Ñ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¾Ð¹. ÐÐ·Ð²Ð¸Ð½Ð¸ÑÐµ, ÑÑÐ¾ Ð¿Ð¾Ð´Ð²ÐµÐ»Ð¸.</p>\n<p>ÐÐµÐ½ÑÐ³Ð¸ Ð·Ð° Ð·Ð°ÐºÐ°Ð· Ð¼Ñ Ð²ÐµÑÐ½ÑÐ»Ð¸ Ð½Ð° Ð²Ð°ÑÑ ÐºÐ°ÑÑÑ</p>
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.TO_BUYER_DELIVERY_RETURNED.description.Buyer=ÐÐ°ÐºÐ°Ð· Ð±ÑÐ» Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµÐ½
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.DELIVERED_TO_BUYER.description.Buyer=ÐÐ°ÐºÐ°Ð· Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.description.trackingPart.Buyer=Ð¡Ð»ÐµÐ´Ð¸ÑÐµ Ð·Ð° Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¾Ð¹ Ð¿Ð¾ ÑÑÐµÐº-Ð½Ð¾Ð¼ÐµÑÑ {0}
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.pickupInStoreDescription.Buyer=ÐÐ°ÐºÐ°Ð· ÑÐ¶Ðµ Ð¶Ð´ÐµÑ Ð²Ð°Ñ. ÐÑ Ð¼Ð¾Ð¶ÐµÑÐµ Ð·Ð°Ð±ÑÐ°ÑÑ ÐµÐ³Ð¾ Ð¿Ð¾ Ð°Ð´ÑÐµÑÑ: Ð³. ÐÐ¾ÑÐºÐ²Ð°, ÐÐ°ÑÑÐ°Ð²ÑÐºÐ¾Ðµ ÑÐ¾ÑÑÐµ, Ð´. 9, ÑÑÑ. 28 ÐÐ¦ Â«ÐÐ°Ð½Ð¸Ð»Ð¾Ð²ÑÐºÐ°Ñ ÐÐ°Ð½ÑÑÐ°ÐºÑÑÑÐ°Â». Ð ÐµÐ¶Ð¸Ð¼ ÑÐ°Ð±Ð¾ÑÑ: Ñ Ð¿Ð¾Ð½ÐµÐ´ÐµÐ»ÑÐ½Ð¸ÐºÐ° Ð¿Ð¾ Ð¿ÑÑÐ½Ð¸ÑÑ Ñ 10 Ð´Ð¾ 19. ÐÐ¾Ð½ÑÐ°ÐºÑÐ½ÑÐ¹ Ð½Ð¾Ð¼ÐµÑ ÑÐµÐ»ÐµÑÐ¾Ð½Ð° <a href="tel:8-************">8 800 707 53 08</a>

service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.comment.Buyer=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¸ÐµÐ´ÐµÑ Ðº Ð²Ð°Ð¼ ~ {0}


service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.title.Seller=Ð¢Ð¾Ð²Ð°Ñ Ð² OSKELLY
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.disabledDescription.Seller.singular=ÐÐ¾ÑÐ»Ðµ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ ÑÐ¾Ð²Ð°Ñ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.disabledDescription.Seller.plural=ÐÐ¾ÑÐ»Ðµ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ Ð¼Ñ Ð¿ÐµÑÐµÐ¼ÐµÑÑÐ¸Ð¼ ÑÐ¾Ð²Ð°ÑÑ Ð½Ð° ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.InProgress.description.Seller=ÐÐ°Ñ ÑÐ¾Ð²Ð°Ñ Ð´Ð¾ÑÑÐ°Ð²Ð»ÑÐµÑÑÑ Ð² Ð±ÑÑÐ¸Ðº {0}
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.InProgressWarehouse.description.Seller=ÐÐ°Ñ ÑÐ¾Ð²Ð°Ñ Ð´Ð¾ÑÑÐ°Ð²Ð»ÑÐµÑÑÑ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.InProgressOther.description.Seller=ÐÐ°Ñ ÑÐ¾Ð²Ð°Ñ Ð´Ð¾ÑÑÐ°Ð²Ð»ÑÐµÑÑÑ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.Completed.description.Seller=ÐÐ°Ñ ÑÐ¾Ð²Ð°Ñ Ð½Ð°ÑÐ¾Ð´Ð¸ÑÑÑ Ð² Ð±ÑÑÐ¸ÐºÐµ Ð¿Ð¾ Ð°Ð´ÑÐµÑÑ {0}
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.CompletedWarehouse.description.Seller=ÐÐ°Ñ ÑÐ¾Ð²Ð°Ñ Ð½Ð°ÑÐ¾Ð´Ð¸ÑÑÑ Ð½Ð° Ð½Ð°ÑÐµÐ¼ ÑÐºÐ»Ð°Ð´Ðµ Ð² Ð¾ÑÐ¸ÑÐµ OSKELLY
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.SoldOnline.description.Seller=Ð¢Ð¾Ð²Ð°Ñ Ð¿ÑÐ¾Ð´Ð°Ð½ Ð¾Ð½Ð»Ð°Ð¹Ð½
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.Failed.description.Seller=ÐÐ°ÐºÐ°Ð· Ð¾ÑÐ¼ÐµÐ½ÐµÐ½

service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.title.Buyer=ÐÐ¾ÑÑÐ°Ð²ÐºÐ° Ð² Ð±ÑÑÐ¸Ðº
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.disabledDescription.Buyer.singular=ÐÑ Ð´Ð¾ÑÑÐ°Ð²Ð¸Ð¼ ÑÐ¾Ð²Ð°Ñ Ð² Ð±ÑÑÐ¸Ðº
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.disabledDescription.Buyer.plural=ÐÑ Ð´Ð¾ÑÑÐ°Ð²Ð¸Ð¼ ÑÐ¾Ð²Ð°ÑÑ Ð² Ð±ÑÑÐ¸Ðº
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.InProgress.description.Buyer=Ð¢Ð¾Ð²Ð°Ñ Ð´Ð¾ÑÑÐ°Ð²Ð»ÑÐµÑÑÑ Ð² Ð±ÑÑÐ¸Ðº
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.InProgressWarehouse.description.Buyer=Ð¢Ð¾Ð²Ð°Ñ Ð´Ð¾ÑÑÐ°Ð²Ð»ÑÐµÑÑÑ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.InProgressOther.description.Buyer=Ð¢Ð¾Ð²Ð°Ñ Ð´Ð¾ÑÑÐ°Ð²Ð»ÑÐµÑÑÑ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´ Ð¸Ð»Ð¸ Ð² Ð¾Ð´Ð¸Ð½ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.Completed.description.Buyer=Ð¢Ð¾Ð²Ð°Ñ Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½ Ð² Ð±ÑÑÐ¸Ðº
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.CompletedWarehouse.description.Buyer=Ð¢Ð¾Ð²Ð°Ñ Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½ Ð½Ð° Ð½Ð°Ñ ÑÐºÐ»Ð°Ð´
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.SoldOnline.description.Buyer=Ð¢Ð¾Ð²Ð°Ñ Ð¿ÑÐ¾Ð´Ð°Ð½ Ð¾Ð½Ð»Ð°Ð¹Ð½
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.Failed.description.Buyer=ÐÐ°ÐºÐ°Ð· Ð¾ÑÐ¼ÐµÐ½ÐµÐ½


service.OrderTrackDTO.OrderStageDTO.BuyerConfirmation.title.Buyer=ÐÐ¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ðµ Ð·Ð°ÐºÐ°Ð·Ð°
service.OrderTrackDTO.OrderStageDTO.BuyerConfirmation.disabledDescription.Buyer.singular=ÐÐ¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÐµ, ÑÑÐ¾ Ð²ÐµÑÑ Ñ Ð²Ð°Ñ, ÑÑÐ¾Ð±Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÐµÑ Ð·Ð½Ð°Ð», ÑÑÐ¾ ÑÐ´ÐµÐ»ÐºÐ° ÑÐ¾ÑÑÐ¾ÑÐ»Ð°ÑÑ
service.OrderTrackDTO.OrderStageDTO.BuyerConfirmation.disabledDescription.Buyer.plural=ÐÐ¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÐµ, ÑÑÐ¾ Ð²ÐµÑÐ¸ Ñ Ð²Ð°Ñ, ÑÑÐ¾Ð±Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÐµÑ Ð·Ð½Ð°Ð», ÑÑÐ¾ ÑÐ´ÐµÐ»ÐºÐ° ÑÐ¾ÑÑÐ¾ÑÐ»Ð°ÑÑ
service.OrderTrackDTO.OrderStageDTO.BuyerConfirmation.Cancelled.description.Buyer=ÐÐ°ÐºÐ°Ð· Ð¾ÑÐ¼ÐµÐ½ÐµÐ½
service.OrderTrackDTO.OrderStageDTO.BuyerConfirmation.NotConfirmed.description.Buyer.singular=ÐÐ¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÐµ, ÑÑÐ¾ Ð²ÐµÑÑ Ñ Ð²Ð°Ñ, ÑÑÐ¾Ð±Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÐµÑ Ð·Ð½Ð°Ð», ÑÑÐ¾ ÑÐ´ÐµÐ»ÐºÐ° ÑÐ¾ÑÑÐ¾ÑÐ»Ð°ÑÑ
service.OrderTrackDTO.OrderStageDTO.BuyerConfirmation.NotConfirmed.description.Buyer.plural=ÐÐ¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÐµ, ÑÑÐ¾ Ð²ÐµÑÐ¸ Ñ Ð²Ð°Ñ, ÑÑÐ¾Ð±Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÐµÑ Ð·Ð½Ð°Ð», ÑÑÐ¾ ÑÐ´ÐµÐ»ÐºÐ° ÑÐ¾ÑÑÐ¾ÑÐ»Ð°ÑÑ
service.OrderTrackDTO.OrderStageDTO.BuyerConfirmation.Confirmed.description.Buyer.singular=ÐÑ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸Ð»Ð¸, ÑÑÐ¾ ÑÐ¾Ð²Ð°Ñ Ñ Ð²Ð°Ñ. Ð¡Ð¿Ð°ÑÐ¸Ð±Ð¾ Ð·Ð° Ð·Ð°ÐºÐ°Ð·!
service.OrderTrackDTO.OrderStageDTO.BuyerConfirmation.Confirmed.description.Buyer.plural=ÐÑ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸Ð»Ð¸, ÑÑÐ¾ ÑÐ¾Ð²Ð°ÑÑ Ñ Ð²Ð°Ñ. Ð¡Ð¿Ð°ÑÐ¸Ð±Ð¾ Ð·Ð° Ð·Ð°ÐºÐ°Ð·!
service.OrderTrackDTO.OrderStageDTO.BuyerConfirmation.NotConfirmed.action.Buyer=ÐÐ°ÐºÐ°Ð· Ñ Ð¼ÐµÐ½Ñ


service.OrderTrackDTO.OrderStageDTO.SoldInBoutique.title.Seller=Ð¢Ð¾Ð²Ð°Ñ Ð¿ÑÐ¾Ð´Ð°Ð½
service.OrderTrackDTO.OrderStageDTO.SoldInBoutique.disabledDescription.Seller=ÐÑ Ð¾Ð¿Ð¾Ð²ÐµÑÑÐ¸Ð¼ Ð²Ð°Ñ, ÐºÐ¾Ð³Ð´Ð° ÑÐ¾Ð²Ð°Ñ Ð±ÑÐ´ÐµÑ Ð¿ÑÐ¾Ð´Ð°Ð½ Ð² Ð¾Ð´Ð½Ð¾Ð¼ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð² Ð¸Ð»Ð¸ Ð½Ð° Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ðµ OSKELLY
service.OrderTrackDTO.OrderStageDTO.SoldInBoutique.description.Seller=ÐÐ°Ñ ÑÐ¾Ð²Ð°Ñ Ð¿ÑÐ¾Ð´Ð°Ð½

service.OrderTrackDTO.OrderStageDTO.SoldInBoutique.title.Buyer=Ð¢Ð¾Ð²Ð°Ñ Ð¿ÑÐ¾Ð´Ð°Ð½
service.OrderTrackDTO.OrderStageDTO.SoldInBoutique.disabledDescription.Buyer=Ð¢Ð¾Ð²Ð°Ñ Ð¿ÑÐ¾Ð´Ð°Ð½ Ð² Ð±ÑÑÐ¸ÐºÐµ
service.OrderTrackDTO.OrderStageDTO.SoldInBoutique.description.Buyer=Ð¢Ð¾Ð²Ð°Ñ Ð±ÑÐ» Ð¿ÑÐ¾Ð´Ð°Ð½

service.OrderTrackDTO.OrderStageDTO.ReturnedToSeller.title.Seller=ÐÐ¾Ð·Ð²ÑÐ°Ñ
service.OrderTrackDTO.OrderStageDTO.ReturnedToSeller.description.Seller=ÐÑ Ð²ÐµÑÐ½ÑÐ»Ð¸ Ð²Ð°Ð¼ ÑÐ¾Ð²Ð°Ñ

service.OrderTrackDTO.OrderStageDTO.ReturnedToSeller.title.Buyer=ÐÐ¾Ð·Ð²ÑÐ°Ñ
service.OrderTrackDTO.OrderStageDTO.ReturnedToSeller.description.Buyer=ÐÑÐ¾Ð´Ð°Ð²ÐµÑ Ð²ÐµÑÐ½ÑÐ» ÑÐ²Ð¾Ð¹ ÑÐ¾Ð²Ð°Ñ


service.OrderTrackDTO.OrderStageDTO.Payout.title.Seller=ÐÑÐ¿Ð»Ð°ÑÐ°
service.OrderTrackDTO.OrderStageDTO.Payout.disabledDescription.Seller.singular=ÐÑ Ð¿Ð¾Ð»ÑÑÐ¸ÑÐµ Ð´ÐµÐ½ÑÐ³Ð¸ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 3Ñ Ð´Ð½ÐµÐ¹, Ð¿Ð¾ÑÐ»Ðµ ÑÐ¾Ð³Ð¾ ÐºÐ°Ðº Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð¿Ð¾Ð»ÑÑÐ¸Ñ Ð·Ð°ÐºÐ°Ð·
service.OrderTrackDTO.OrderStageDTO.Payout.disabledDescription.Seller.plural=ÐÑ Ð¿Ð¾Ð»ÑÑÐ¸ÑÐµ Ð´ÐµÐ½ÑÐ³Ð¸ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 3Ñ Ð´Ð½ÐµÐ¹, Ð¿Ð¾ÑÐ»Ðµ ÑÐ¾Ð³Ð¾ ÐºÐ°Ðº Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð¿Ð¾Ð»ÑÑÐ¸Ñ Ð·Ð°ÐºÐ°Ð·
service.OrderTrackDTO.OrderStageDTO.Payout.disabledBoutiqueDescription.Seller.singular=ÐÑ Ð¿Ð¾Ð»ÑÑÐ¸ÑÐµ Ð´ÐµÐ½ÑÐ³Ð¸ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 3Ñ Ð´Ð½ÐµÐ¹, Ð¿Ð¾ÑÐ»Ðµ ÑÐ¾Ð³Ð¾ ÐºÐ°Ðº Ð·Ð°ÐºÐ°Ð· Ð±ÑÐ´ÐµÑ Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð¸Ð»Ð¸ Ð¿ÑÐ¾Ð´Ð°Ð½ Ð² Ð¾Ð´Ð½Ð¾Ð¼ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.Payout.disabledBoutiqueDescription.Seller.plural=ÐÑ Ð¿Ð¾Ð»ÑÑÐ¸ÑÐµ Ð´ÐµÐ½ÑÐ³Ð¸ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 3Ñ Ð´Ð½ÐµÐ¹, Ð¿Ð¾ÑÐ»Ðµ ÑÐ¾Ð³Ð¾ ÐºÐ°Ðº Ð·Ð°ÐºÐ°Ð· Ð±ÑÐ´ÐµÑ Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð¸Ð»Ð¸ Ð¿ÑÐ¾Ð´Ð°Ð½ Ð² Ð¾Ð´Ð½Ð¾Ð¼ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.Payout.Cancelled.description.Seller=ÐÐ°ÐºÐ°Ð· Ð¾ÑÐ¼ÐµÐ½ÐµÐ½
service.OrderTrackDTO.OrderStageDTO.Payout.NotConfirmed.description.Seller=ÐÑÑÐ°Ð»Ð¾ÑÑ ÑÐ¾Ð»ÑÐºÐ¾ Ð¿Ð¾Ð»ÑÑÐ¸ÑÑ Ð´ÐµÐ½ÑÐ³Ð¸ Ð·Ð° ÑÐ¾Ð²Ð°Ñ. Ð£ÐºÐ°Ð¶Ð¸ÑÐµ, ÐºÑÐ´Ð° Ð¸Ñ Ð¿ÐµÑÐµÐ²ÐµÑÑÐ¸
service.OrderTrackDTO.OrderStageDTO.Payout.InProgress.description.Seller=ÐÑ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð»Ð¸ {0} Ð½Ð° Ð²Ð°Ñ ÑÑÐµÑ. Ð¡ÑÐ¾ÐºÐ¸ Ð·Ð°ÑÐ¸ÑÐ»ÐµÐ½Ð¸Ñ Ð·Ð°Ð²Ð¸ÑÑÑ Ð¾Ñ Ð±Ð°Ð½ÐºÐ°
service.OrderTrackDTO.OrderStageDTO.Payout.Completed.description.Seller=ÐÑ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸Ð»Ð¸ {0} Ð½Ð° Ð²Ð°Ñ ÑÑÐµÑ {1}. ÐÐ¾Ð·Ð´ÑÐ°Ð²Ð»ÑÐµÐ¼ Ñ Ð¿ÑÐ¾Ð´Ð°Ð¶ÐµÐ¹! ð¥³
service.OrderTrackDTO.OrderStageDTO.Payout.NotConfirmed.action.Seller=ÐÑÐ±ÑÐ°ÑÑ ÑÐ¿Ð¾ÑÐ¾Ð± Ð²ÑÐ¿Ð»Ð°ÑÑ

service.PrimaryServiceImpl.RecentlyViewed=ÐÐµÐ´Ð°Ð²Ð½Ð¾ Ð¿ÑÐ¾ÑÐ¼Ð¾ÑÑÐµÐ½Ð½ÑÐµ
service.PrimaryServiceImpl.OurChoice=ÐÑÐ±Ð¾Ñ OSKELLY
service.PrimaryServiceImpl.Blog=ÐÐ»Ð¾Ð³ OSKELLY
service.PrimaryServiceImpl.LinkGroupsBlock=ÐÑÑÐ¿Ð¿Ñ ÑÑÑÐ»Ð¾Ðº
service.PrimaryServiceImpl.Instagram=Instagram Oskelly
service.PrimaryServiceImpl.JoinToCelebrity=ÐÑÐ¸ÑÐ¾ÐµÐ´Ð¸Ð½ÑÐ¹ÑÐµÑÑ Ðº ÑÐµÐ»ÐµÐ±ÑÐ¸ÑÐ¸
service.PrimaryServiceImpl.NewProducts=ÐÐ¾Ð²ÑÐµ Ð¿Ð¾ÑÑÑÐ¿Ð»ÐµÐ½Ð¸Ñ
service.PrimaryServiceImpl.EternalClassic=ÐÐµÑÐ½Ð°Ñ ÐºÐ»Ð°ÑÑÐ¸ÐºÐ°
service.PrimaryServiceImpl.OurSocial=ÐÑ Ð² ÑÐ¾ÑÐ¸Ð°Ð»ÑÐ½ÑÑ ÑÐµÑÑÑ
service.PrimaryServiceImpl.RecommendedProducts=Ð ÐµÐºÐ¾Ð¼ÐµÐ½Ð´Ð¾Ð²Ð°Ð½Ð¾ Ð´Ð»Ñ Ð²Ð°Ñ
service.PrimaryServiceImpl.TopNProducts=ÐÐ¾Ð¿ÑÐ»ÑÑÐ½Ð¾Ðµ

service.DefaultProductService.ForbiddenOperation=ÐÐµÐ´Ð¾Ð¿ÑÑÑÐ¸Ð¼Ð°Ñ Ð¾Ð¿ÐµÑÐ°ÑÐ¸Ñ. Ð¢Ð¸Ð¿ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ: {0}; Ð¢Ð¸Ð¿ Ð¾Ð¿ÐµÑÐ°ÑÐ¸Ð¸: {1}; Ð¢Ð¾Ð²Ð°Ñ: {2}; ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ: {3};
service.DefaultProductService.ForbiddenFilter=ÐÐµÐ´Ð¾Ð¿ÑÑÑÐ¸Ð¼ÑÐ¹ ÑÐ¸Ð»ÑÑÑ. Ð¢Ð¸Ð¿ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ: {0}; Ð¡ÑÐ°ÑÑÑ ÑÐ¾Ð²Ð°ÑÐ°: {1}; ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ: {2};

service.DefaultProductService.ProductCondition=Ð¡Ð¾ÑÑÐ¾ÑÐ½Ð¸Ðµ ÑÐ¾Ð²Ð°ÑÐ°

service.ProductsCheckerRunner.CouldntReceiveDataAboutSlides=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¿Ð¾Ð»ÑÑÐ¸ÑÑ Ð´Ð°Ð½Ð½ÑÐµ Ð¾ ÑÐ»Ð°Ð¹Ð´Ð°Ñ: {0} - {1}

service.DefaultProductPublicationService.UserNotAuthorized=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð½Ðµ Ð°Ð²ÑÐ¾ÑÐ¸Ð·Ð¾Ð²Ð°Ð½
service.DefaultProductPublicationService.ForbidToEditSoldItem=ÐÐµÐ»ÑÐ·Ñ Ð¸Ð·Ð¼ÐµÐ½ÑÑÑ ÑÐ¶Ðµ Ð¿ÑÐ¾Ð´Ð°Ð½Ð½ÑÐ¹ ÑÐ¾Ð²Ð°Ñ
service.DefaultProductPublicationService.ForbidToEditDeletedItem=ÐÐµÐ»ÑÐ·Ñ Ð¸Ð·Ð¼ÐµÐ½ÑÑÑ ÑÐ´Ð°Ð»ÐµÐ½Ð½ÑÐ¹ ÑÐ¾Ð²Ð°Ñ
service.DefaultProductPublicationService.ForbidToEditDeclinedItem=ÐÐµÐ»ÑÐ·Ñ Ð¸Ð·Ð¼ÐµÐ½ÑÑÑ Ð¾ÑÐºÐ»Ð¾Ð½ÐµÐ½Ð½ÑÐ¹ ÑÐ¾Ð²Ð°Ñ

service.DefaultProductPublicationService.BrandNotSpecified=ÐÐµ ÑÐºÐ°Ð·Ð°Ð½ Ð±ÑÐµÐ½Ð´
service.DefaultProductPublicationService.IncorrectId=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ ID
service.DefaultProductPublicationService.CategoryNotSpecified=ÐÐµ ÑÐºÐ°Ð·Ð°Ð½Ð° ÐºÐ°ÑÐµÐ³Ð¾ÑÐ¸Ñ

service.DefaultProductPublicationService.ProductNotFound=ÐÑÐ¾Ð´ÑÐºÑ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
service.DefaultProductPublicationService.ProductItemNotFound=ÐÑÐ¾Ð´ÑÐºÑÐ¾Ð²ÑÐ¹ ÑÐ»ÐµÐ¼ÐµÐ½Ñ {0} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½

service.DefaultProductPublicationService.CanChooseOnlyFinalCategory=ÐÐ¾Ð¶Ð½Ð¾ Ð²ÑÐ±ÑÐ°ÑÑ ÑÐ¾Ð»ÑÐºÐ¾ ÐºÐ¾Ð½ÐµÑÐ½ÑÑ ÐºÐ°ÑÐµÐ³Ð¾ÑÐ¸Ñ
service.DefaultProductPublicationService.CategoryNotFound=ÐÐ°ÑÐµÐ³Ð¾ÑÐ¸Ñ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð°
service.DefaultProductPublicationService.ForbidToChangeCatgoryForPublishedItem=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ Ð¸Ð·Ð¼ÐµÐ½ÑÑÑ ÐºÐ°ÑÐµÐ³Ð¾ÑÐ¸Ñ Ð´Ð»Ñ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½Ð½Ð¾Ð³Ð¾ ÑÐ¾Ð²Ð°ÑÐ°
service.DefaultProductPublicationService.WrongCategoryForPublishing=ÐÑÐ±Ð»Ð¸ÐºÐ°ÑÐ¸Ñ Ð² Ð´Ð°Ð½Ð½Ð¾Ð¹ ÐºÐ°ÑÐµÐ³Ð¾ÑÐ¸Ð¸ Ð½ÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð°
service.DefaultProductPublicationService.BrandNotFound=ÐÑÐµÐ½Ð´ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
service.DefaultProductPublicationService.WrongBrandForPublishing=ÐÑÐµÐ½Ð´ Ð·Ð°ÐºÑÑÑ Ð´Ð»Ñ Ð¿ÑÐ±Ð»Ð¸ÐºÐ°ÑÐ¸Ð¸ ÑÐ¾Ð²Ð°ÑÐ¾Ð²
service.DefaultProductPublicationService.NotPossibleSetAttributeWhileCategoryEmpty=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ ÑÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ Ð°ÑÑÐ¸Ð±ÑÑÑ Ð´Ð»Ñ ÑÐ¾Ð²Ð°ÑÐ° Ñ Ð½ÐµÐ·Ð°Ð´Ð°Ð½Ð½Ð¾Ð¹ ÐºÐ°ÑÐµÐ³Ð¾ÑÐ¸ÐµÐ¹
service.DefaultProductPublicationService.AttributeValueNotFound=ÐÐ½Ð°ÑÐµÐ½Ð¸Ðµ Ð°ÑÑÐ¸Ð±ÑÑÐ° Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð¾
service.DefaultProductPublicationService.AttributeConflictWithCategory=ÐÑÑÐ¸Ð±ÑÑ {0} Ð½ÐµÐ´Ð¾Ð¿ÑÑÑÐ¸Ð¼ Ð´Ð»Ñ ÐºÐ°ÑÐµÐ³Ð¾ÑÐ¸Ð¸ {1}
service.DefaultProductPublicationService.IncorrectAmount=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½Ð¾Ðµ ÐºÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾: {0}
service.DefaultProductPublicationService.IncorrectCustomSize=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐµ Ð¸ÑÑÐ¾Ð´Ð½ÑÐ¹ ÑÐ¸Ð¿ ÑÐ°Ð·Ð¼ÐµÑÐ°: {0} Ð¸ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ: {1}
service.DefaultProductPublicationService.IncorrectValue=ÐÐµÐºÐ¾ÑÐµÐºÑÐ½Ð¾Ðµ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ
service.DefaultProductPublicationService.SizeNotFound=Ð Ð°Ð·Ð¼ÐµÑ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
service.DefaultProductPublicationService.SeasonNotFound=Ð¡ÐµÐ·Ð¾Ð½ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
service.DefaultProductPublicationService.ImpossibleSetAdditionalSizeForUnknownCategory=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ ÑÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ Ð´Ð¾Ð¿. ÑÐ°Ð·Ð¼ÐµÑÑ Ð´Ð»Ñ ÑÐ¾Ð²Ð°ÑÐ° Ñ Ð½ÐµÐ·Ð°Ð´Ð°Ð½Ð½Ð¾Ð¹ ÐºÐ°ÑÐµÐ³Ð¾ÑÐ¸ÐµÐ¹
service.DefaultProductPublicationService.IncorrectAdditionalSizeForCategory=ÐÐµÐ´Ð¾Ð¿ÑÑÑÐ¸Ð¼ÑÐ¹ Ð´Ð¾Ð¿. ÑÐ°Ð·Ð¼ÐµÑ Ð´Ð»Ñ ÐºÐ°ÑÐµÐ³Ð¾ÑÐ¸Ð¸ {0}
service.DefaultProductPublicationService.ProductConditionNotFound=Ð¡Ð¾ÑÑÐ¾ÑÐ½Ð¸Ðµ ÑÐ¾Ð²Ð°ÑÐ° Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð¾
service.DefaultProductPublicationService.IncorrectMinCategoryPrice=ÐÐ¸Ð½Ð¸Ð¼Ð°Ð»ÑÐ½Ð¾Ðµ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ: {0}
service.DefaultProductPublicationService.IncorrectRpr=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½Ð¾Ðµ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ RRP
service.DefaultProductPublicationService.SizesNotFound=ÐÐµÑ Ð½Ð¸ Ð¾Ð´Ð½Ð¾Ð³Ð¾ ÑÐ°Ð·Ð¼ÐµÑÐ°
service.DefaultProductPublicationService.SizeNotSpecifiedForProductItem=ÐÐµ Ð·Ð°Ð´Ð°Ð½ ÑÐ°Ð·Ð¼ÐµÑ Ð´Ð»Ñ ProductItem {0}
service.DefaultProductPublicationService.MandatoryImagesNotSpecified=ÐÐµ Ð·Ð°Ð´Ð°Ð½Ñ Ð¾Ð±ÑÐ·Ð°ÑÐµÐ»ÑÐ½ÑÐµ Ð¸Ð·Ð¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ñ
service.DefaultProductPublicationService.CategoryNotSpecifiedV2=ÐÐµ Ð·Ð°Ð´Ð°Ð½Ð° ÐºÐ°ÑÐµÐ³Ð¾ÑÐ¸Ñ
service.DefaultProductPublicationService.BrandNotSpecifiedV2=ÐÐµ Ð·Ð°Ð´Ð°Ð½ Ð±ÑÐµÐ½Ð´
service.DefaultProductPublicationService.SizeTypeNotSpecified=ÐÐµ Ð·Ð°Ð´Ð°Ð½ ÑÐ¸Ð¿ ÑÐ°Ð·Ð¼ÐµÑÐ°
service.DefaultProductPublicationService.ItemDescriptionNotSpecified=ÐÐµ Ð·Ð°Ð´Ð°Ð½Ð¾ Ð¾Ð¿Ð¸ÑÐ°Ð½Ð¸Ðµ ÑÐ¾Ð²Ð°ÑÐ°
service.DefaultProductPublicationService.ProductConditionNotSpecified=ÐÐµ ÑÐºÐ°Ð·Ð°Ð½Ð¾ ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ðµ ÑÐ¾Ð²Ð°ÑÐ°
service.DefaultProductPublicationService.IncorrectPrice=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½Ð¾Ðµ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ ÑÐµÐ½Ñ
service.DefaultProductPublicationService.IncorrectItemPhotoNumber=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ Ð½Ð¾Ð¼ÐµÑ ÑÐ¾ÑÐ¾Ð³ÑÐ°ÑÐ¸Ð¸ ÑÐ¾Ð²Ð°ÑÐ°: {0}
service.DefaultProductPublicationService.FileTooLarge=Ð Ð°Ð·Ð¼ÐµÑ ÑÐ°Ð¹Ð»Ð° Ð¿ÑÐµÐ²ÑÑÐ°ÐµÑ {0} MB
service.DefaultProductPublicationService.IncorrectDefectPhotoNumber=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ Ð½Ð¾Ð¼ÐµÑ ÑÐ¾ÑÐ¾Ð³ÑÐ°ÑÐ¸Ð¸ Ð´ÐµÑÐµÐºÑÐ°: " {0}
service.DefaultProductPublicationService.ImageNotRelatedToItem=ÐÐ·Ð¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ðµ Ð½Ðµ Ð¸Ð¼ÐµÐµÑ Ð¾ÑÐ½Ð¾ÑÐµÐ½Ð¸Ñ Ðº ÑÐ¾Ð²Ð°ÑÑ {0}
service.DefaultProductPublicationService.ItemIsNotDraft=Ð¢Ð¾Ð²Ð°Ñ Ð½Ðµ ÑÐ²Ð»ÑÐµÑÑÑ ÑÐµÑÐ½Ð¾Ð²Ð¸ÐºÐ¾Ð¼: {0}
service.DefaultProductPublicationService.InvalidCategory=ÐÐµÐ´Ð¾Ð¿ÑÑÑÐ¸Ð¼Ð°Ñ ÐºÐ°ÑÐµÐ³Ð¾ÑÐ¸Ñ

service.DefaultProductPublicationService.Property.Category=ÐÐ°ÑÐµÐ³Ð¾ÑÐ¸Ñ
service.DefaultProductPublicationService.Property.Brand=ÐÑÐµÐ½Ð´
service.DefaultProductPublicationService.Property.Size=Ð Ð°Ð·Ð¼ÐµÑÑ ÑÐ¾Ð²Ð°ÑÐ°
service.DefaultProductPublicationService.Property.MandatoryImage=ÐÐ±ÑÐ·Ð°ÑÐµÐ»ÑÐ½ÑÐµ Ð¸Ð·Ð¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ñ ÑÐ¾Ð²Ð°ÑÐ°
service.DefaultProductPublicationService.Property.ItemDescription=ÐÐ¿Ð¸ÑÐ°Ð½Ð¸Ðµ ÑÐ¾Ð²Ð°ÑÐ°
service.DefaultProductPublicationService.Property.ProductCondition=Ð¡Ð¾ÑÑÐ¾ÑÐ½Ð¸Ðµ ÑÐ¾Ð²Ð°ÑÐ°
service.DefaultProductPublicationService.Property.Price=Ð¦ÐµÐ½Ð°
service.DefaultProductPublicationService.Property.Address=ÐÐ´ÑÐµÑ
service.ProductServiceImpl.sellerConcierge.size.restriction=ÐÐµ ÑÐ´Ð°ÐµÑÑÑ ÑÐ¾ÑÑÐ°Ð½Ð¸ÑÑ Ð´Ð°Ð½Ð½ÑÐµ ÑÐ¾Ð²Ð°ÑÐ°: ÑÐ¾Ð²Ð°Ñ ÐºÐ¾Ð½ÑÑÐµÑÐ¶ Ð´Ð»Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ° Ð´Ð¾Ð»Ð¶ÐµÐ½ ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑ ÑÐ¾Ð»ÑÐºÐ¾ Ð¾Ð´Ð¸Ð½ ÑÐ°Ð·Ð¼ÐµÑ (Ð·Ð°ÐºÐ°Ð·: {0})
service.ProductServiceImpl.sellerConcierge.size.count.restriction=ÐÐµ ÑÐ´Ð°ÐµÑÑÑ ÑÐ¾ÑÑÐ°Ð½Ð¸ÑÑ Ð´Ð°Ð½Ð½ÑÐµ ÑÐ¾Ð²Ð°ÑÐ°: ÑÐ¾Ð²Ð°Ñ ÐºÐ¾Ð½ÑÑÐµÑÐ¶ Ð´Ð»Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ° Ð´Ð¾Ð»Ð¶ÐµÐ½ ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑ ÑÐ¾Ð»ÑÐºÐ¾ Ð¾Ð´Ð¸Ð½ ÑÐ°Ð·Ð¼ÐµÑ Ð² ÐºÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ðµ Ð½Ðµ Ð±Ð¾Ð»ÑÑÐµ Ð¾Ð´Ð½Ð¾Ð¹ ÐµÐ´Ð¸Ð½Ð¸ÑÑ (Ð·Ð°ÐºÐ°Ð·: {0})
service.DefaultProductPublicationService.customCommissionPriceEditDenied=ÐÐµÐ´Ð¾Ð¿ÑÑÑÐ¸Ð¼Ð¾ Ð¾Ð±Ð½Ð¾Ð²Ð»ÑÑÑ ÑÐµÐ½Ñ ÑÐ¾Ð²Ð°ÑÐ° c ÐºÐ°ÑÑÐ¾Ð¼Ð½Ð¾Ð¹ ÐºÐ¾Ð¼Ð¸ÑÑÐ¸ÐµÐ¹

service.DefaultFtpService.ErrorWhenConnectToFolder=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð¿Ð¾Ð´ÐºÐ»ÑÑÐµÐ½Ð¸Ð¸ Ðº Ð´Ð¸ÑÐµÐºÑÐ¾ÑÐ¸Ð¸: {0}

service.PromoGalleryService.ImageNotFound=ÐÑÑÑÑÑÑÐ²ÑÐµÑ Ð¸Ð·Ð¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ðµ ÑÐ»ÐµÐ¼ÐµÐ½ÑÐ° ÑÐ¾ÑÐ¾Ð³Ð°Ð»ÐµÑÐµÐ¸
service.PromoGalleryService.ImageIdNotFound=ÐÑÑÑÑÑÑÐ²ÑÐµÑ Id ÑÐ»ÐµÐ¼ÐµÐ½ÑÐ° ÑÐ¾ÑÐ¾Ð³Ð°Ð»ÐµÑÐµÐ¸
service.PromoGalleryService.WrongImageId=ÐÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ð¾ ÑÐºÐ°Ð·Ð°Ð½ Id ÑÐ»ÐµÐ¼ÐµÐ½ÑÐ° ÑÐ¾ÑÐ¾Ð³Ð°Ð»ÐµÑÐµÐ¸

service.PromoSelectionService.PromoblockIdNotFound=ÐÑÑÑÑÑÑÐ²ÑÐµÑ Id Ð¿ÑÐ¾Ð¼Ð¾Ð±Ð»Ð¾ÐºÐ° Ð¿Ð¾Ð´Ð±Ð¾ÑÐºÐ¸ ÑÐ¾Ð²Ð°ÑÐ¾Ð²
service.PromoSelectionService.WrongPromoblockId=ÐÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ð¾ ÑÐºÐ°Ð·Ð°Ð½ Id Ð¿ÑÐ¾Ð¼Ð¾Ð±Ð»Ð¾ÐºÐ° Ð¿Ð¾Ð´Ð±Ð¾ÑÐºÐ¸ ÑÐ¾Ð²Ð°ÑÐ¾Ð²

service.DefaultPromoCodeService.CouldntSaveProcomode.WrongType=Ð¡Ð¾ÑÑÐ°Ð½ÐµÐ½Ð¸Ðµ Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´Ð°: ÑÐ¸Ð¿ Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´Ð° {0} Ð½Ðµ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÐµÑÑÑ

service.DefaultReturnService.WaitingForAccept=ÐÐ¶Ð¸Ð´Ð°ÐµÑ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ñ

service.DefaultSocialService.SupportAccountOptions.UniqueSets=Ð£Ð½Ð¸ÐºÐ°Ð»ÑÐ½ÑÐµ Ð¿Ð¾Ð´Ð±Ð¾ÑÐºÐ¸ ÑÐ°Ð¼ÑÑ Ð¸Ð½ÑÐµÑÐµÑÐ½ÑÑ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ð¹ Ñ Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ñ
service.DefaultSocialService.SupportAccountOptions.Promocodes=ÐÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´Ñ Ð½Ð° Ð²ÑÑÐ¾Ð´Ð½ÑÐµ Ð¿Ð¾ÐºÑÐ¿ÐºÐ¸ OSKELLY
service.DefaultSocialService.SupportAccountOptions.Presents=ÐÐ¾Ð´Ð°ÑÐºÐ¸ Ð¾Ñ Ð±ÑÐµÐ½Ð´Ð¾Ð²-Ð¿Ð°ÑÑÐ½ÐµÑÐ¾Ð²
service.DefaultSocialService.SupportAccountOptions.ClosedGames=ÐÐ°ÐºÑÑÑÑÐµ ÑÐ¾Ð·ÑÐ³ÑÑÑÐ¸ Ð¸ ÐºÐ¾Ð½ÐºÑÑÑÑ
service.DefaultSocialService.SupportAccountOptions.Education=ÐÐ±ÑÐ°Ð·Ð¾Ð²Ð°ÑÐµÐ»ÑÐ½ÑÐ¹ ÐºÐ¾Ð½ÑÐµÐ½Ñ Ð² Ð¾Ð±Ð»Ð°ÑÑÐ¸ Ð¾ÑÐ¾Ð·Ð½Ð°Ð½Ð½Ð¾Ð³Ð¾ Ð¿Ð¾ÑÑÐµÐ±Ð»ÐµÐ½Ð¸Ñ, ÑÐµÐº-Ð»Ð¸ÑÑÑ, Ð¾Ð¿ÑÐ¾ÑÑ Ð¸ Ð¼Ð½Ð¾Ð³Ð¾Ðµ Ð´ÑÑÐ³Ð¾Ðµ

service.InstagramSocialNetworkClient.InvalidSocialNetwork=ÐÐµÐ´Ð¾Ð¿ÑÑÑÐ¸Ð¼Ð°Ñ ÑÐ¾Ñ. ÑÐµÑÑ: {0}. ÐÐ¶Ð¸Ð´Ð°ÐµÑÑÑ: {1}
service.InstagramSocialNetworkClient.CouldntConnectToAccount=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð²ÑÐ¿Ð¾Ð»Ð½Ð¸ÑÑ Ð·Ð°Ð¿ÑÐ¾Ñ Ðº Ð°ÐºÐºÐ°ÑÐ½ÑÑ {0}: {1}, {2}
service.InstagramSocialNetworkClient.CouldntConnectToAccountShort=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð²ÑÐ¿Ð¾Ð»Ð½Ð¸ÑÑ Ð·Ð°Ð¿ÑÐ¾Ñ Ðº Ð°ÐºÐºÐ°ÑÐ½ÑÑ {0}: {1}
service.InstagramSocialNetworkClient.CouldntAuthByName=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð°Ð²ÑÐ¾ÑÐ¸Ð·Ð¾Ð²Ð°ÑÑÑÑ Ð¾Ñ Ð¸Ð¼ÐµÐ½Ð¸ {0}: {1}
service.InstagramSocialNetworkClient.CouldntReadObjectProperties=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ Ð¿ÑÐ¾ÑÐ¸ÑÐ°ÑÑ ÑÐ²Ð¾Ð¹ÑÑÐ²Ð¾ Ð¾Ð±ÑÐµÐºÑÐ° ({0}): {1}
service.InstagramSocialNetworkClient.CouldntReadPosts=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ Ð¿ÑÐ¾ÑÐ¸ÑÐ°ÑÑ Ð¿Ð¾ÑÑÑ Ð°ÐºÐºÐ°ÑÐ½ÑÐ° ({0}): {1}
service.InstagramSocialNetworkClient.CouldntReadAuthResponse=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ Ð¿ÑÐ¾ÑÐ¸ÑÐ°ÑÑ ÑÐµÑÐ¿Ð¾Ð½Ð· Ð°Ð²ÑÐ¾ÑÐ¸Ð·Ð°ÑÐ¸Ð¸ ({0}): {1}
service.InstagramSocialNetworkClient.CouldntReadObject=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ Ð¿ÑÐ¾ÑÐ¸ÑÐ°ÑÑ Ð¾Ð±ÑÐµÐºÑ ({0}): {1}
service.InstagramSocialNetworkClient.StartIndexNotFound=ÐÐ½Ð´ÐµÐºÑ Ð½Ð°ÑÐ°Ð»Ð° {0} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½: {1}
service.InstagramSocialNetworkClient.FinishIndexNotFound=ÐÐ½Ð´ÐµÐºÑ ÐºÐ¾Ð½ÑÐ° {0} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½: {1}

service.verification.VerificationClient.BadResponse=Ð§ÑÐ¾-ÑÐ¾ Ð¿Ð¾ÑÐ»Ð¾ Ð½Ðµ ÑÐ°Ðº
service.verification.VerificationClient.TooManyRequests=ÐÑÐµÐ²ÑÑÐµÐ½ Ð»Ð¸Ð¼Ð¸Ñ Ð·Ð°Ð¿ÑÐ¾ÑÐ¾Ð²

service.UserServiceImpl.PasswordsDontMatch=ÐÐ°ÑÐ¾Ð»Ð¸ Ð½Ðµ ÑÐ¾Ð²Ð¿Ð°Ð´Ð°ÑÑ
service.UserServiceImpl.UserAlreadyRegistered=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ ÑÐ¶Ðµ Ð·Ð°ÑÐµÐ³Ð¸ÑÑÑÐ¸ÑÐ¾Ð²Ð°Ð½ Ð² ÑÐ¸ÑÑÐµÐ¼Ðµ
service.UserServiceImpl.NicknameAlreadyRegistered=ÐÑÐµÐ²Ð´Ð¾Ð½Ð¸Ð¼ ÑÐ¶Ðµ Ð·Ð°ÑÐµÐ³Ð¸ÑÑÑÐ¸ÑÐ¾Ð²Ð°Ð½ Ð² ÑÐ¸ÑÑÐµÐ¼Ðµ
service.UserServiceImpl.EmailAlreadyRegistered=ÐÐ²ÐµÐ´ÐµÐ½Ð½ÑÐ¹ email ÑÐ¶Ðµ Ð±ÑÐ» Ð·Ð°ÑÐµÐ³Ð¸ÑÑÑÐ¸ÑÐ¾Ð²Ð°Ð½, Ð¿Ð¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð²Ð²ÐµÐ´Ð¸ÑÐµ Ð´ÑÑÐ³Ð¾Ð¹ email Ð¸Ð»Ð¸ Ð²Ð¾ÑÐ¿Ð¾Ð»ÑÐ·ÑÐ¹ÑÐµÑÑ ÑÐ¾ÑÐ¼Ð¾Ð¹ Ð²ÑÐ¾Ð´Ð°

service.UserBanServiceImpl.UserIdIsEmpty=ÐÐ¾Ð»Ðµ userId Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¿ÑÑÑÑÐ¼ Ð¸Ð»Ð¸ Ð¾ÑÑÐ¸ÑÐ°ÑÐµÐ»ÑÐ½ÑÐ¼
service.UserBanServiceImpl.StatusChangedUserIdIsEmpty=ÐÐ¾Ð»Ðµ statusChangedUserId Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¿ÑÑÑÑÐ¼ Ð¸Ð»Ð¸ Ð¾ÑÑÐ¸ÑÐ°ÑÐµÐ»ÑÐ½ÑÐ¼
service.UserBanServiceImpl.BanTypeIsEmpty=ÐÐ¾Ð»Ðµ banType Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¿ÑÑÑÑÐ¼
service.UserBanServiceImpl.EndDateIsBeforeCurrentDate=ÐÐ¾Ð»Ðµ endDate Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð¸Ð¼ÐµÑÑ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ Ð¼ÐµÐ½ÑÑÐµ ÑÐµÐ¼ ÑÐµÐºÑÑÐ°Ñ Ð´Ð°ÑÐ°
service.UserBanServiceImpl.EndDateIsEmpty=ÐÐ¾Ð»Ðµ endDate Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¿ÑÑÑÑÐ¼
service.UserBanServiceImpl.DescriptionIsEmpty=ÐÐ¾Ð»Ðµ description Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¿ÑÑÑÑÐ¼
service.UserBanServiceImpl.UserAlreadyBlocked=ÐÐºÐºÐ°ÑÐ½Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð·Ð°Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½, ÑÐ°Ðº ÐºÐ°Ðº Ñ Ð½ÐµÐ³Ð¾ Ð°ÐºÐºÐ°ÑÐ½Ñ ÑÐ¶Ðµ Ð·Ð°Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½
service.UserBanServiceImpl.BanIdIsEmpty=ÐÐ¾Ð»Ðµ banId Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¿ÑÑÑÑÐ¼ Ð¸Ð»Ð¸ Ð¾ÑÑÐ¸ÑÐ°ÑÐµÐ»ÑÐ½ÑÐ¼

service.BargainBanStrategy.BanTitle=ÐÐ°ÐºÑÑÑ Ð´Ð¾ÑÑÑÐ¿ Ðº ÑÐ¾ÑÐ³Ð°Ð¼
service.CommentBanStrategy.BanTitle=ÐÐ°ÐºÑÑÑ Ð´Ð¾ÑÑÑÐ¿ Ðº ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸ÑÐ¼
service.PublishBanStrategy.BanTitle=ÐÑ Ð±Ð¾Ð»ÑÑÐµ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¿ÑÐ¾Ð´Ð°Ð²Ð°ÑÑ ÑÐ¾Ð²Ð°ÑÑ
service.StoriesPublishBanStrategy.BanTitle=ÐÐ°Ð¿ÑÐµÑ Ð½Ð° Ð¿ÑÐ±Ð»Ð¸ÐºÐ°ÑÐ¸Ñ ÑÑÐ¾ÑÐ¸Ñ
service.StreamBanStrategy.BanTitle=ÐÐ°Ð¿ÑÐµÑ Ð½Ð° Ð¿ÑÐ¾Ð²ÐµÐ´ÐµÐ½Ð¸Ðµ Ð¿ÑÑÐ¼ÑÑ ÑÑÐ¸ÑÐ¾Ð²
service.UserBanStrategy.BanTitle=ÐÑ Ð·Ð°Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ñ
service.WarningStrategy.BanTitle=ÐÑÐµÐ´ÑÐ¿ÑÐµÐ¶Ð´ÐµÐ½Ð¸Ðµ
service.OSocialPostBanStrategy.BanTitle=ÐÐ°Ð¿ÑÐµÑ Ð½Ð° Ð¿ÑÐ±Ð»Ð¸ÐºÐ°ÑÐ¸Ñ Ð¿Ð¾ÑÑÐ¾Ð²
service.OSocialCommentBanStrategy.BanTitle=ÐÐ°Ð¿ÑÐµÑ Ð½Ð° ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¸ Ð² O!Trends
service.CommentShadowBanStrategy.BanTitle=Ð¢ÐµÐ½ÐµÐ²Ð¾Ð¹ Ð·Ð°Ð¿ÑÐµÑ Ðº ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸ÑÐ¼

service.segment.DefaultSegmentService.UserIdOrGuestTokenMustBeFilled=ÐÐ´ÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¾Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð¸Ð»Ð¸ Ð³Ð¾ÑÑÑ Ð´Ð¾Ð»Ð¶ÐµÐ½ Ð±ÑÑÑ Ð·Ð°Ð¿Ð¾Ð»Ð½ÐµÐ½
service.segment.DefaultSegmentService.exception.UserSegmentNotFound=Ð¢Ð°ÐºÐ¾Ð³Ð¾ ÑÐµÐ³Ð¼ÐµÐ½ÑÐ° Ð½Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ
service.segment.DefaultSegmentService.AuthorizedUsers=ÐÐ²ÑÐ¾ÑÐ¸Ð·Ð¾Ð²Ð°Ð½Ð½ÑÐµ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ð¸ Ñ Ð¿Ð¾ÐºÑÐ¿ÐºÐ°Ð¼Ð¸
service.segment.DefaultSegmentService.AllUsers=ÐÑÐµ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ð¸
service.segment.DefaultSegmentService.NewbieUsers=ÐÐ¾Ð²ÑÐµ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ð¸

service.verification.VerificationService.codeVerificationError=ÐÐµÐ²ÐµÑÐ½ÑÐ¹ ÐºÐ¾Ð´ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ñ
service.verification.CloudFlareTurnstileService.Forbidden=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ ÑÐ¾ÐºÐµÐ½ Ð²ÐµÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¸Ð¸ Ð·Ð°Ð¿ÑÐ¾ÑÐ°

validators.UserService.EmailNotSpecified=ÐÐµ ÑÐºÐ°Ð·Ð°Ð½ email
validators.UserService.PhoneNotSpecified=ÐÐµ ÑÐºÐ°Ð·Ð°Ð½ Ð½Ð¾Ð¼ÐµÑ ÑÐµÐ»ÐµÑÐ¾Ð½Ð°
validators.UserService.IncorrectEmail=ÐÐµÐ²ÐµÑÐ½ÑÐ¹ ÑÐ¾ÑÐ¼Ð°Ñ email
validators.UserService.PasswordNotSpecified=ÐÐµ ÑÐºÐ°Ð·Ð°Ð½ Ð¿Ð°ÑÐ¾Ð»Ñ
validators.UserService.TooShortPassword=Ð¡Ð»Ð¸ÑÐºÐ¾Ð¼ ÐºÐ¾ÑÐ¾ÑÐºÐ¸Ð¹ Ð¿Ð°ÑÐ¾Ð»Ñ
validators.UserService.ApprovePasswordNotSpecified=ÐÐµ ÑÐºÐ°Ð·Ð°Ð½ Ð¿Ð°ÑÐ¾Ð»Ñ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ñ
validators.UserService.NicknameNotSpecified=ÐÐµ ÑÐºÐ°Ð·Ð°Ð½ Ð¿ÑÐµÐ²Ð´Ð¾Ð½Ð¸Ð¼

validators.SubscriptionContactService.EmailNotSpecified=ÐÐµ ÑÐºÐ°Ð·Ð°Ð½ email
validators.SubscriptionContactService.TooLargeEmail=Ð¡Ð»Ð¸ÑÐºÐ¾Ð¼ Ð´Ð»Ð¸Ð½Ð½ÑÐ¹ email
validators.SubscriptionContactService.TooLargeName=Ð¡Ð»Ð¸ÑÐºÐ¾Ð¼ Ð´Ð»Ð¸Ð½Ð½Ð¾Ðµ Ð¸Ð¼Ñ

payout.service.username.BONUS_12_STOREEZ=12 Storeez
payout.service.username.b2p+tkb=Ð¢ÐÐ 1 (Best2pay)
payout.service.username.tcb-1.0=Ð¢ÐÐ 2 (Ð¢ÐÐ)
payout.service.username.boutique-1.0=Ð¢ÐÐ 3 (ÐÑÑÐ¸Ðº)
payout.service.username.user-balance-debt=ÐÐ¾Ð»Ð³

service.BoutiqueImportService.InvalidProductState=Ð¢Ð¾Ð²Ð°Ñ {0} Ð½Ð°ÑÐ¾Ð´Ð¸ÑÑÑ Ð² ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ð¸ {1}. Ð¢Ð¾Ð²Ð°Ñ Ð´Ð¾Ð»Ð¶ÐµÐ½ Ð±ÑÑÑ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°Ð½ Ð¿ÐµÑÐµÐ´ ÑÐ¾Ð·Ð´Ð°Ð½Ð¸ÐµÐ¼ Ð·Ð°ÐºÐ°Ð·Ð¾Ð² ÐÑÑÐ¸ÐºÐ°.
service.BoutiqueImportService.SizeNotFound=ÐÐµ ÑÐ´Ð°ÐµÑÑÑ Ð½Ð°Ð¹ÑÐ¸ ÑÐ°Ð·Ð¼ÐµÑ {0} Ð´Ð»Ñ ÑÐ¾Ð²Ð°ÑÐ° {1}.
service.BoutiqueImportService.NotEnoughAmount=ÐÐ°Ð¿ÑÐ¾ÑÐµÐ½Ð½Ð¾Ðµ ÐºÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ ({0}) ÑÐ¾Ð²Ð°ÑÐ° {1} Ð±Ð¾Ð»ÑÑÐµ, ÑÐµÐ¼ Ð¸Ð¼ÐµÑÑÐµÐµÑÑ Ð² Ð½Ð°Ð»Ð¸ÑÐ¸Ð¸ ({2}).
service.BoutiqueImportService.NoAddressForBoutiqueUser=Ð£ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ ÐÑÑÐ¸ÐºÐ° {0} Ð½Ðµ Ð·Ð°Ð´Ð°Ð½Ð¾ Ð½Ð¸ Ð¾Ð´Ð½Ð¾Ð³Ð¾ Ð°Ð´ÑÐµÑÐ°.
service.BoutiqueImportService.MoreThanOneAddressForBoutiqueUser=Ð£ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ ÐÑÑÐ¸ÐºÐ° {0} Ð·Ð°Ð´Ð°Ð½Ð¾ Ð±Ð¾Ð»ÑÑÐµ Ð¾Ð´Ð½Ð¾Ð³Ð¾ Ð°Ð´ÑÐµÑÐ° ({1})
service.BoutiqueImportService.SellerConcierge.OnlyOneSize=ÐÐµ ÑÐ´Ð°ÐµÑÑÑ ÑÐ¾Ð·Ð´Ð°ÑÑ Ð·Ð°ÐºÐ°Ð· Ð¿Ð¾ Ð·Ð°ÑÐ²ÐºÐµ {0}: ÑÐ¾Ð²Ð°Ñ {1}, ÑÐºÐ°Ð·Ð°Ð½Ð¾ Ð±Ð¾Ð»ÐµÐµ Ð¾Ð´Ð½Ð¾Ð³Ð¾ ÑÐ°Ð·Ð¼ÐµÑÐ° Ð² Ð½Ð°Ð»Ð¸ÑÐ¸Ð¸
service.BoutiqueImportService.SellerConcierge.OnlyOneCount=ÐÐµ ÑÐ´Ð°ÐµÑÑÑ ÑÐ¾Ð·Ð´Ð°ÑÑ Ð·Ð°ÐºÐ°Ð· Ð¿Ð¾ Ð·Ð°ÑÐ²ÐºÐµ {0}: ÑÐ¾Ð²Ð°Ñ {1}, ÑÐºÐ°Ð·Ð°Ð½Ð¾ ÐºÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð±Ð¾Ð»ÑÑÐµ Ð¾Ð´Ð½Ð¾Ð³Ð¾ (Ð² ÑÐ°Ð·Ð¼ÐµÑÐµ)

filter.processor.BrandFilterProcessor.filterName=ÐÑÐµÐ½Ð´
filter.processor.BrandFilterProcessor.topSectionName=ÐÐ¾Ð¿ÑÐ»ÑÑÐ½ÑÐµ
filter.processor.BrandFilterProcessor.allSectionName=ÐÑÐµ Ð±ÑÐµÐ½Ð´Ñ
filter.processor.CategoryFilterProcessor.filterName=ÐÐ°ÑÐµÐ³Ð¾ÑÐ¸Ñ
filter.processor.ConditionFilterProcessor.filterName=Ð¡Ð¾ÑÑÐ¾ÑÐ½Ð¸Ðµ ÑÐ¾Ð²Ð°ÑÐ°
filter.processor.ConditionFilterProcessor.withTagConditionName=ÐÐ¾Ð²Ð¾Ðµ Ñ Ð±Ð¸ÑÐºÐ¾Ð¹
filter.processor.ConditionFilterProcessor.withTagConditionDescription=ÐÐ±ÑÐ¾Ð»ÑÑÐ½Ð¾ Ð½Ð¾Ð²Ð°Ñ Ð²ÐµÑÑ, ÐºÐ¾ÑÐ¾ÑÐ°Ñ Ð½Ð¸ ÑÐ°Ð·Ñ Ð½Ðµ Ð±ÑÐ»Ð° Ð² Ð½Ð¾ÑÐºÐµ. ÐÑÐµ ÑÐ¸ÑÐ¼ÐµÐ½Ð½ÑÐµ Ð±Ð¸ÑÐºÐ¸ Ð¸ ÑÐ¿Ð°ÐºÐ¾Ð²ÐºÐ° Ð¿ÑÐ¸Ð»Ð°Ð³Ð°ÑÑÑÑ.
filter.processor.ConditionFilterProcessor.perfectConditionName=ÐÑÐ»Ð¸ÑÐ½Ð¾Ðµ ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ðµ
filter.processor.ConditionFilterProcessor.perfectConditionDescription=ÐÐµÑÑ Ð² Ð¿ÑÐµÐºÑÐ°ÑÐ½Ð¾Ð¼ ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ð¸, Ð±ÐµÐ· ÑÐ²Ð½ÑÑ Ð²Ð½ÐµÑÐ½Ð¸Ñ ÑÐ»ÐµÐ´Ð¾Ð² Ð½Ð¾ÑÐºÐ¸ Ð¸ Ð´ÐµÑÐµÐºÑÐ¾Ð². Ð Ð½ÐµÐ¹ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½ Ð½Ðµ Ð²ÐµÑÑ ÐºÐ¾Ð¼Ð¿Ð»ÐµÐºÑ Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ¾Ð², Ð° ÑÐ°ÐºÐ¶Ðµ Ð¾ÑÑÑÑÑÑÐ²Ð¾Ð²Ð°ÑÑ ÑÐ¸ÑÐ¼ÐµÐ½Ð½Ð°Ñ ÑÐ¿Ð°ÐºÐ¾Ð²ÐºÐ°. ÐÐ¾Ð¿ÑÑÐºÐ°ÑÑÑÑ Ð½ÐµÐ±Ð¾Ð»ÑÑÐ¸Ðµ Ð²Ð½ÑÑÑÐµÐ½Ð½Ð¸Ðµ Ð´ÐµÑÐµÐºÑÑ.
filter.processor.ConditionFilterProcessor.goodConditionName=Ð¥Ð¾ÑÐ¾ÑÐµÐµ ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ðµ
filter.processor.ConditionFilterProcessor.goodConditionDescription=ÐÐµÑÑ Ð±ÑÐ²ÑÐ°Ñ Ð² ÑÐ¿Ð¾ÑÑÐµÐ±Ð»ÐµÐ½Ð¸Ð¸, Ð·Ð° ÐºÐ¾ÑÐ¾ÑÐ¾Ð¹ ÑÐ¾ÑÐ¾ÑÐ¾ ÑÑÐ°Ð¶Ð¸Ð²Ð°Ð»Ð¸. ÐÐ¾Ð·Ð¼Ð¾Ð¶Ð½Ñ Ð½ÐµÐ±Ð¾Ð»ÑÑÐ¸Ðµ Ð´ÐµÑÐµÐºÑÑ Ð² Ð²Ð¸Ð´Ðµ ÑÐºÐ¾Ð»Ð¾Ð², ÑÐ°ÑÐ°Ð¿Ð¸Ð½Ð¾Ðº, Ð´ÑÑÐ¾ÑÐµÐº Ð¸ Ð·Ð°ÑÐµÐ¿Ð¾Ðº.
filter.processor.InStockFilterProcessor.filterName=ÐÐ° ÑÐºÐ»Ð°Ð´Ðµ OSKELLY
filter.processor.InStockFilterProcessor.filterDescription=Ð¢Ð¾Ð²Ð°Ñ Ð¾ÑÐ¿ÑÐ°Ð²Ð¸ÑÑÑ Ðº Ð²Ð°Ð¼ Ð½ÐµÐ·Ð°Ð¼ÐµÐ´Ð»Ð¸ÑÐµÐ»ÑÐ½Ð¾, ÑÐ°Ðº ÐºÐ°Ðº Ð²ÐµÑÐ¸ Ð½Ð° ÑÐºÐ»Ð°Ð´Ðµ OSKELLY ÑÐ¶Ðµ Ð¿ÑÐ¾ÑÐ»Ð¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ Ð¸ Ð½Ðµ ÑÑÐµÐ±ÑÑÑ Ð²ÑÐµÐ¼ÐµÐ½Ð¸ Ð½Ð° Ð¾ÑÐ³ÑÑÐ·ÐºÑ Ð¾Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°
filter.processor.NewResaleFilterProcessor.filterName=ÐÐ¾Ð²Ð¾Ðµ / Ð ÐµÑÐµÐ¹Ð»
filter.processor.NewResaleFilterProcessor.filterDescription=
filter.processor.NewResaleFilterProcessor.allLabel=ÐÑÐµ
filter.processor.NewResaleFilterProcessor.resaleLabel=Ð ÐµÑÐµÐ¹Ð»
filter.processor.NewResaleFilterProcessor.newLabel=ÐÑÑÐ¸ÐºÐ¸
filter.processor.InBoutiqueFilterProcessor.filterName=Ð Ð±ÑÑÐ¸ÐºÐµ OSKELLY
filter.processor.BoutiqueLocationTagFilterProcessor.filterName=ÐÑÑÐ¸ÐºÐ¸ OSKELLY
filter.processor.BoutiqueLocationTagFilterProcessor.incorrectValue=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½Ð¾Ðµ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ ({0}) ÑÐ¸Ð»ÑÑÑÐ° Ð¿Ð¾ Ð±ÑÑÐ¸ÐºÐ°Ð¼!
filter.processor.ModelFilterProcessor.filterName=ÐÐ¾Ð´ÐµÐ»Ñ
filter.processor.NewCollectionFilterProcessor.filterName=ÐÐ¾Ð²Ð°Ñ ÐºÐ¾Ð»Ð»ÐµÐºÑÐ¸Ñ
filter.processor.OskellyChoiceFilterProcessor.filterName=ÐÑÐ±Ð¾Ñ OSKELLY
filter.processor.OskellyChoiceFilterProcessor.filterDescription=ÐÐ°Ð¶Ð´ÑÐ¹ Ð´ÐµÐ½Ñ Ð½Ð°ÑÐ¸ ÑÑÐ¸Ð»Ð¸ÑÑÑ Ð²ÑÐ±Ð¸ÑÐ°ÑÑ Ð»ÑÑÑÐ¸Ðµ ÑÐ¾Ð²Ð°ÑÑ Ð¸Ð· Ð±ÑÑÐ¸ÐºÐ¾Ð² Ð¸ Ð³Ð°ÑÐ´ÐµÑÐ¾Ð±Ð¾Ð² ÑÐ°ÑÑÐ½ÑÑ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð²
filter.processor.PriceFilterProcessor.filterName=Ð¦ÐµÐ½Ð°
filter.processor.SaleFilterProcessor.filterName=SALE
filter.processor.SellerTypeFilterProcessor.filterName=ÐÐ¸Ð´ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°
filter.processor.SellerTypeFilterProcessor.individualValue=Ð§Ð°ÑÑÐ½ÑÐ¹ Ð¿ÑÐ¾Ð´Ð°Ð²ÐµÑ
filter.processor.SellerTypeFilterProcessor.individualDescription=Ð¡ÑÐ°ÑÑÑ Â«Ð§Ð°ÑÑÐ½ÑÐ¹ Ð¿ÑÐ¾Ð´Ð°Ð²ÐµÑÂ» Ð°Ð²ÑÐ¾Ð¼Ð°ÑÐ¸ÑÐµÑÐºÐ¸ Ð¿ÑÐ¸ÑÐ²Ð°Ð¸Ð²Ð°ÐµÑÑÑ ÐºÐ°Ð¶Ð´Ð¾Ð¼Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ, ÐºÐ¾ÑÐ¾ÑÑÐ¹ Ð²ÑÑÑÐ°Ð²Ð¸Ð» Ð½Ð° Ð¿ÑÐ¾Ð´Ð°Ð¶Ñ ÑÐ²Ð¾Ð¸ ÑÐ¾Ð²Ð°ÑÑ, Ð½ÐµÐ·Ð°Ð²Ð¸ÑÐ¸Ð¼Ð¾ Ð¾Ñ Ð¸Ñ ÐºÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð° Ð¸ ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ñ â Ð¾Ñ Â«Ð½Ð¾Ð²ÑÑ Ñ Ð±Ð¸ÑÐºÐ¾Ð¹Â» Ð´Ð¾ Ð»Ð¾ÑÐ¾Ð² Ð² Ð¾ÑÐ»Ð¸ÑÐ½Ð¾Ð¼ Ð¸ ÑÐ¾ÑÐ¾ÑÐµÐ¼ ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ð¸. Ð­ÑÐ¾Ñ ÑÐµÐ³ Ð¿Ð¾Ð¼Ð¾Ð³Ð°ÐµÑ Ð²ÑÐ´ÐµÐ»Ð¸ÑÑ Ð¿ÐµÑÑÐ¾Ð½Ð°Ð»ÑÐ½ÑÐµ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ Ð¸ Ð½Ð°Ð¹ÑÐ¸ ÑÐ½Ð¸ÐºÐ°Ð»ÑÐ½ÑÐµ Ð²ÐµÑÐ¸ Ð½Ð°Ð¿ÑÑÐ¼ÑÑ Ð¾Ñ ÑÐ°ÑÑÐ½ÑÑ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð² Ð¿Ð¾ Ð²ÑÐ³Ð¾Ð´Ð½Ð¾Ð¹ ÑÐµÐ½Ðµ â Ð¸Ð»Ð¸ Ð¿ÑÐµÐ´Ð»Ð¾Ð¶Ð¸ÑÑ ÑÐ²Ð¾Ñ.
filter.processor.SellerTypeFilterProcessor.consignmentShopValue=Ð ÐµÑÐµÐ¹Ð» Ð¼Ð°Ð³Ð°Ð·Ð¸Ð½
filter.processor.SellerTypeFilterProcessor.consignmentShopDescription=Ð¡ÑÐ°ÑÑÑ Â«Ð ÐµÑÐµÐ¸ÌÐ» Ð¼Ð°Ð³Ð°Ð·Ð¸Ð½Â» Ð¿ÑÐ¸ÑÐ²Ð°Ð¸Ð²Ð°ÐµÑÑÑ Ð¿ÑÐ¾ÑÐµÑÑÐ¸Ð¾Ð½Ð°Ð»ÑÐ½ÑÐ¼ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°Ð¼ Ñ Ð±Ð¾Ð»ÑÑÐ¸Ð¼ Ð¸ ÑÑÐ°ÑÐµÐ»ÑÐ½Ð¾ Ð¿Ð¾Ð´Ð¾Ð±ÑÐ°Ð½Ð½ÑÐ¼ Ð°ÑÑÐ¾ÑÑÐ¸Ð¼ÐµÐ½ÑÐ¾Ð¼. Ð Ä¸Ð¾Ð»Ð»ÐµÄ¸ÑÐ¸Ð¸ â Ð¿Ð¾ÑÑÐ¾ÑÐ½Ð½ÑÐµ Ð¿Ð¾Ð·Ð¸ÑÐ¸Ð¸ Ð¼Ð¸ÑÐ¾Ð²ÑÑ Ð±ÑÐµÐ½Ð´Ð¾Ð², Ð°ÑÑÐ¸Ð²Ð½ÑÐµ Ð¸ Ð²Ð¸Ð½ÑÐ°Ð¶Ð½ÑÐµ Ð»Ð¾ÑÑ, ÐºÐ¾ÑÐ¾ÑÑÐµ Ð¼Ð¾Ð³ÑÑ Ð²Ð°ÑÑÐ¸ÑÐ¾Ð²Ð°ÑÑÑÑ Ð¾Ñ Â«Ð½Ð¾Ð²ÑÑ c Ð±Ð¸ÑÐºÐ¾Ð¹Â» Ð´Ð¾ ÑÐ¾Ð²Ð°ÑÐ¾Ð² Ð² Ð¾ÑÐ»Ð¸ÑÐ½Ð¾Ð¼ Ð¸ ÑÐ¾ÑÐ¾ÑÐµÐ¼ ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ð¸.
filter.processor.SellerTypeFilterProcessor.boutiqueValue=ÐÑÑÐ¸Ðº
filter.processor.SellerTypeFilterProcessor.boutiqueDescription=Ð¡ÑÐ°ÑÑÑ Â«ÐÑÑÐ¸ÐºÂ» Ð¿ÑÐ¸ÑÐ²Ð°Ð¸Ð²Ð°ÐµÑÑÑ Ð¼ÑÐ»ÑÑÐ¸Ð±ÑÐµÐ½Ð´Ð¾Ð²ÑÐ¼ Ð±ÑÑÐ¸ÐºÐ°Ð¼. Ð Ä¸Ð¾Ð»Ð»ÐµÄ¸ÑÐ¸Ð¸ â Ð°Ä¸ÑÑÐ°Ð»ÑÐ½ÑÐµ Ð¿Ð¾Ð·Ð¸ÑÐ¸Ð¸ Ð¾Ñ Ð»ÑÐºÑÐ¾Ð²ÑÑ Ð±ÑÐµÐ½Ð´Ð¾Ð², Ð±ÐµÑÑÑÐµÐ»Ð»ÐµÑÑ, Ð¿ÑÐ¾ÑÐ»ÑÐµ ÑÐµÐ·Ð¾Ð½Ñ Ð¸ Ð°ÑÑÐ»ÐµÑ, Ð´Ð¾ÑÑÑÐ¿Ð½ÑÐµ Ð½Ð°Ð¿ÑÑÐ¼ÑÑ Ð¾Ñ Ð¿ÑÐ¾Ð²ÐµÑÐµÐ½Ð½ÑÑ fashion-ÑÐ¸ÑÐµÐ¹Ð»ÐµÑÐ¾Ð². ÐÑÐ¾Ð´Ð°Ð²ÐµÑ Ñ ÑÑÐ¸Ð¼ ÑÑÐ°ÑÑÑÐ¾Ð¼ Ð¿ÑÐµÐ´Ð»Ð°Ð³Ð°ÐµÑ Ð¸ÑÐºÐ»ÑÑÐ¸ÑÐµÐ»ÑÐ½Ð¾ ÑÐ¾Ð²Ð°ÑÑ Ð¸Ð· ÐºÐ°ÑÐµÐ³Ð¾ÑÐ¸Ð¸ Â«Ð½Ð¾Ð²Ð¾Ðµ Ñ Ð±Ð¸ÑÐºÐ¾Ð¹Â» Ð¸ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÐµÑ Ð¿Ð¾ÑÑÐ¾ÑÐ½Ð½Ð¾Ðµ Ð¾Ð±Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ ÑÐ²Ð¾ÐµÐ³Ð¾ Ð°ÑÑÐ¾ÑÑÐ¸Ð¼ÐµÐ½ÑÐ°. OSKELLY ÑÐ°Ð±Ð¾ÑÐ°ÐµÑ ÑÐ¾Ð»ÑÄ¸Ð¾ Ñ Ð±ÑÑÐ¸Ä¸Ð°Ð¼Ð¸ Ñ Ð±ÐµÐ·ÑÐ¿ÑÐµÑÐ½Ð¾Ð¹ ÑÐµÐ¿ÑÑÐ°ÑÐ¸ÐµÐ¹ Ð¿Ð¾ Ð²ÑÐµÐ¼Ñ Ð¼Ð¸ÑÑ.
filter.processor.SellerTypeFilterProcessor.brandValue=ÐÑÐµÐ½Ð´
filter.processor.SellerTypeFilterProcessor.brandDescription=Ð¡ÑÐ°ÑÑÑ Â«ÐÑÐµÐ½Ð´Â» Ð¿ÑÐ¸ÑÐ²Ð°Ð¸Ð²Ð°ÐµÑÑÑ Ð¾ÑÐ¸ÑÐ¸Ð°Ð»ÑÐ½ÑÐ¼ Ð¼Ð¾Ð½Ð¾Ð±ÑÐµÐ½Ð´Ð¾Ð²ÑÐ¼ Ð±ÑÑÐ¸ÐºÐ°Ð¼, Ð¿ÑÐµÐ´ÑÑÐ°Ð²Ð»ÑÑÑÐ¸Ð¼ Ð¸ÑÐºÐ»ÑÑÐ¸ÑÐµÐ»ÑÐ½Ð¾ ÑÐ¾Ð²Ð°ÑÑ ÐºÐ°ÑÐµÐ³Ð¾ÑÐ¸Ð¸ Â«Ð½Ð¾Ð²ÑÐµ Ñ Ð±Ð¸ÑÐºÐ¾Ð¹Â». Ð ÐºÐ¾Ð»Ð»ÐµÐºÑÐ¸Ð¸ â Ð¿Ð¾ÑÑÐ¾ÑÐ½Ð½ÑÐµ Ð¿Ð¾Ð·Ð¸ÑÐ¸Ð¸ Ð¼Ð¸ÑÐ¾Ð²ÑÑ Ð±ÑÐµÐ½Ð´Ð¾Ð², Ð½Ð¾Ð²ÑÐµ ÑÐµÐ·Ð¾Ð½Ñ, ÐºÐ°Ð¿ÑÑÐ»ÑÐ½ÑÐµ Ð»Ð¸Ð½ÐµÐ¹ÐºÐ¸ Ð¸ ÑÐºÑÐºÐ»ÑÐ·Ð¸Ð²Ð½ÑÐµ Ð»Ð¾ÑÑ, Ð¿Ð¾ÑÑÑÐ¿Ð°ÑÑÐ¸Ðµ Ð½Ð°Ð¿ÑÑÐ¼ÑÑ Ð¾Ñ Ð¿ÑÐ¾Ð¸Ð·Ð²Ð¾Ð´Ð¸ÑÐµÐ»Ñ Ð¸Ð»Ð¸ Ð´Ð¸ÑÑÑÐ¸Ð±ÑÑÑÐ¾ÑÐ°, Ð±ÐµÐ· ÑÑÐ°ÑÑÐ¸Ñ Ð¿Ð¾ÑÑÐµÐ´Ð½Ð¸ÐºÐ¾Ð².
filter.processor.SellerTypeFilterProcessor.buyerValue=ÐÐµÑÑÐ¾Ð½Ð°Ð»ÑÐ½ÑÐ¹ ÑÐ¾Ð¿ÐµÑ
filter.processor.SellerTypeFilterProcessor.buyerDescription=Ð¡ÑÐ°ÑÑÑ Â«ÐÐµÑÑÐ¾Ð½Ð°Ð»ÑÐ½ÑÐ¹ ÑÐ¾Ð¿ÐµÑÂ» â Ð½Ð°ÑÑÐ¾ÑÑÐ¸Ð¹ ÑÐºÑÐ¿ÐµÑÑ, ÑÐ¼ÐµÑÑÐ¸Ð¹ Ð½Ð°ÑÐ¾Ð´Ð¸ÑÑ ÑÐ½Ð¸ÐºÐ°Ð»ÑÐ½ÑÐµ Ð²ÐµÑÐ¸ Ð¿Ð¾ Ð¸Ð½Ð´Ð¸Ð²Ð¸Ð´ÑÐ°Ð»ÑÐ½ÑÐ¼ Ð·Ð°Ð¿ÑÐ¾ÑÐ°Ð¼. ÐÐ½ Ð¿ÑÐ¸ÑÐ²Ð°Ð¸Ð²Ð°ÐµÑÑÑ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°Ð¼, ÐºÐ¾ÑÐ¾ÑÑÐµ Ð¿ÑÐµÐ´Ð»Ð°Ð³Ð°ÑÑ ÑÐ¾Ð²Ð°ÑÑ ÑÐ¾Ð»ÑÐºÐ¾ Ð² Ð½Ð¾Ð²Ð¾Ð¼ ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ð¸ Ð¸Ð· Ð¿Ð¾ÑÐ»ÐµÐ´Ð½Ð¸Ñ ÐºÐ¾Ð»Ð»ÐµÐºÑÐ¸Ð¹ Ð¼Ð¸ÑÐ¾Ð²ÑÑ Ð±ÑÐµÐ½Ð´Ð¾Ð², Ð²ÐºÐ»ÑÑÐ°Ñ ÑÐµÐ´ÐºÐ¸Ðµ Ð¸ ÑÐºÑÐºÐ»ÑÐ·Ð¸Ð²Ð½ÑÐµ Ð»Ð¾ÑÑ, ÐºÐ¾ÑÐ¾ÑÑÐµ ÑÐ»Ð¾Ð¶Ð½Ð¾ Ð½Ð°Ð¹ÑÐ¸ Ð³Ð´Ðµ-Ð»Ð¸Ð±Ð¾ ÐµÑÐµ. ÐÐ»Ñ Ð¿Ð¾Ð»ÑÑÐµÐ½Ð¸Ñ ÑÑÐ¾Ð³Ð¾ ÑÑÐ°ÑÑÑÐ° Ð²Ð°Ð¶ÐµÐ½ Ð²ÑÑÐ¾ÐºÐ¸Ð¹ Ð¿ÑÐ¾ÑÐµÐ½Ñ ÑÑÐ¿ÐµÑÐ½Ð¾ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð½ÑÑ Ð·Ð°ÐºÐ°Ð·Ð¾Ð² - 99%, ÑÑÐ¾ Ð³Ð°ÑÐ°Ð½ÑÐ¸ÑÑÐµÑ Ð½Ð°Ð´ÐµÐ¶Ð½Ð¾ÑÑÑ, Ð²Ð½Ð¸Ð¼Ð°Ð½Ð¸Ðµ Ðº Ð´ÐµÑÐ°Ð»ÑÐ¼ Ð¸ Ð±ÐµÐ·ÑÐ¿ÑÐµÑÐ½ÑÐ¹ ÑÐµÑÐ²Ð¸Ñ Ð´Ð»Ñ ÐºÐ°Ð¶Ð´Ð¾Ð³Ð¾ ÐºÐ»Ð¸ÐµÐ½ÑÐ°.
filter.processor.DeliveryDaysFilterProcessor.filterName=Ð¡ÑÐ¾Ðº Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸
filter.processor.DeliveryDaysFilterProcessor.upTo1dayValue=ÐÐ¾ 1 Ð´Ð½Ñ
filter.processor.DeliveryDaysFilterProcessor.upTo3daysValue=ÐÐ¾ 3 Ð´Ð½ÐµÐ¹
filter.processor.DeliveryDaysFilterProcessor.upTo7daysValue=ÐÐ¾ 7 Ð´Ð½ÐµÐ¹
filter.processor.DeliveryDaysFilterProcessor.upTo11daysValue=ÐÐ¾ 11 Ð´Ð½ÐµÐ¹
filter.processor.DeliveryDaysFilterProcessor.upTo20daysValue=ÐÐ¾ 20 Ð´Ð½ÐµÐ¹

filter.processor.SizeFilterProcessor.filterName=Ð Ð°Ð·Ð¼ÐµÑ
filter.processor.SizeTypeFilterProcessor.incorrectValue=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½Ð¾Ðµ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ ({0}) ÑÐ¸Ð»ÑÑÑÐ° Ð¿Ð¾ ÑÐ¸Ð¿Ñ ÑÐ°Ð·Ð¼ÐµÑÐ° ÑÐ¾Ð²Ð°ÑÐ°!
filter.processor.StateFilterProcessor.incorrectValue=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½Ð¾Ðµ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ ({0}) ÑÐ¸Ð»ÑÑÑÐ° Ð¿Ð¾ ÑÑÐ°ÑÑÑÑ ÑÐ¾Ð²Ð°ÑÐ°!
filter.processor.OrderStateFilterProcessor.incorrectValue=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½Ð¾Ðµ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ ({0}) ÑÐ¸Ð»ÑÑÑÐ° Ð¿Ð¾ ÑÑÐ°ÑÑÑÑ Ð·Ð°ÐºÐ°Ð·Ð°!
filter.processor.OrderSourceFilterProcessor.incorrectValue=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½Ð¾Ðµ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ ({0}) ÑÐ¸Ð»ÑÑÑÐ° Ð¿Ð¾ Ð¸ÑÑÐ¾ÑÐ½Ð¸ÐºÑ Ð·Ð°ÐºÐ°Ð·Ð°!
filter.processor.StreetwearFilterProcessor.filterName=BEEGZ
filter.processor.StreetwearFilterProcessor.filterDescription=ÐÐ¸Ð¼Ð¸ÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð½ÑÐµ ÑÐµÐ»Ð¸Ð·Ñ Ð¸ ÐºÐ¾Ð»Ð»Ð°Ð±Ð¾ÑÐ°ÑÐ¸Ð¸ Ð·Ð½Ð°ÐºÐ¾Ð²ÑÑ streetwear-Ð±ÑÐµÐ½Ð´Ð¾Ð² Ð¾Ñ Ð¿ÑÐ¾Ð²ÐµÑÐµÐ½Ð½ÑÑ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð²
filter.processor.LocationTagFilterProcessor.filterName=ÐÐ´Ðµ Ð½Ð°ÑÐ¾Ð´Ð¸ÑÑÑ ÑÐ¾Ð²Ð°Ñ
filter.processor.LocationTagFilterProcessor.incorrectValue=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½Ð¾Ðµ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ ({0}) ÑÐ¸Ð»ÑÑÑÐ° Ð¿Ð¾ ÑÐµÐ³Ñ Ð¼ÐµÑÑÐ¾Ð¿Ð¾Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ ÑÐ¾Ð²Ð°ÑÐ°!
filter.processor.VintageFilterProcessor.filterName=ÐÐ¸Ð½ÑÐ°Ð¶
filter.processor.VintageFilterProcessor.filterDescription=Ð¢Ð¾Ð²Ð°ÑÑ Ð±Ð¾Ð»ÐµÐµ 15 Ð»ÐµÑ
filter.processor.BrandNewFilterProcessor.filterName=ÐÐ¾Ð²Ð¾Ðµ
filter.processor.BrandNewFilterProcessor.filterDescription=
filter.processor.WithTagFilterProcessor.filterName=ÐÐ¾Ð²Ð¾Ðµ Ñ Ð±Ð¸ÑÐºÐ¾Ð¹
filter.processor.WithTagFilterProcessor.filterDescription=ÐÐ±ÑÐ¾Ð»ÑÑÐ½Ð¾ Ð½Ð¾Ð²Ð°Ñ Ð²ÐµÑÑ Ñ Ð¿Ð¾Ð»Ð½ÑÐ¼ ÐºÐ¾Ð¼Ð¿Ð»ÐµÐºÑÐ¾Ð¼ Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ¾Ð² Ð¸ ÑÐ¸ÑÐ¼ÐµÐ½Ð½Ð¾Ð¹ ÑÐ¿Ð°ÐºÐ¾Ð²ÐºÐ¾Ð¹
filter.processor.FilterProcessorProvider.unknownProcessor=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð½Ð°Ð¹ÑÐ¸ Ð¿Ð¾Ð´ÑÐ¾Ð´ÑÑÐ¸Ð¹ Ð¾Ð±ÑÐ°Ð±Ð¾ÑÑÐ¸Ðº Ð´Ð»Ñ ÑÐ¸Ð»ÑÑÑÐ° Ñ ÐºÐ¾Ð´Ð¾Ð¼ {0}
filter.processor.FilterProcessor.incorrectValueType=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½Ð¾Ðµ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ Ð´Ð»Ñ Ð¾Ð±ÑÐ°Ð±Ð¾ÑÑÐ¸ÐºÐ° ÑÐ¸Ð»ÑÑÑÐ°. ÐÐµÑÐµÐ´Ð°Ð½Ð¾ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ ÑÐ¸Ð¿Ð° {0}, Ð¾Ð¶Ð¸Ð´Ð°Ð»Ð¾ÑÑ - {1}
filter.processor.FilterProcessor.incorrectFilterValue=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½Ð¾Ðµ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ Ð´Ð»Ñ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ñ ÑÐ¸Ð»ÑÑÑÐ°. ÐÐµÑÐµÐ´Ð°Ð½Ð¾ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ ÑÐ¸Ð¿Ð° {0}, Ð¾Ð¶Ð¸Ð´Ð°Ð»Ð¾ÑÑ - {1}
filter.processor.ResaleFilterProcessor.filterName=Resale
filter.processor.ResaleFilterProcessor.filterDescription=
filter.processor.OrderSourceInfoFilterProcessor.filterName=ÐÐ´Ðµ Ð½Ð°ÑÐ¾Ð´Ð¸ÑÑÑ ÑÐ¾Ð²Ð°Ñ
filter.processor.ExclusiveSelectionFilterProcessor.filterName=Ð­ÐºÑÐºÐ»ÑÐ·Ð¸Ð²Ð½Ð°Ñ ÑÐµÐ»ÐµÐºÑÐ¸Ñ
filter.processor.ExclusiveLotFilterProcessor.filterName=Ð­ÐºÑÐºÐ»ÑÐ·Ð¸Ð²Ð½ÑÐ¹ Ð»Ð¾Ñ
filter.sorting.ChangeTimeAscSortingProcessor.name=ÐÐ¾ Ð²Ð¾Ð·ÑÐ°ÑÑÐ°Ð½Ð¸Ñ Ð²ÑÐµÐ¼ÐµÐ½Ð¸ Ð¾Ð±Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ ÑÐ¾Ð²Ð°ÑÐ°
filter.sorting.ChangeTimeDescSortingProcessor.name=ÐÐ¾ ÑÐ±ÑÐ²Ð°Ð½Ð¸Ñ Ð²ÑÐµÐ¼ÐµÐ½Ð¸ Ð¾Ð±Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ ÑÐ¾Ð²Ð°ÑÐ°
filter.sorting.DiscountAscSortingProcessor.name=ÐÐ¾ Ð²Ð¾Ð·ÑÐ°ÑÑÐ°Ð½Ð¸Ñ Ð²ÐµÐ»Ð¸ÑÐ¸Ð½Ñ ÑÐºÐ¸Ð´ÐºÐ¸
filter.sorting.DiscountSortingProcessor.name=ÐÐ¾ Ð²ÐµÐ»Ð¸ÑÐ¸Ð½Ðµ ÑÐºÐ¸Ð´ÐºÐ¸
filter.sorting.IdAscSortingProcessor.name=ÐÐ¾ Ð²Ð¾Ð·ÑÐ°ÑÑÐ°Ð½Ð¸Ñ ID
filter.sorting.IdDescSortingProcessor.name=ÐÐ¾ ÑÐ±ÑÐ²Ð°Ð½Ð¸Ñ ID
filter.sorting.NewSortingProcessor.name=Ð¡Ð½Ð°ÑÐ°Ð»Ð° Ð½Ð¾Ð²ÑÐµ
filter.sorting.OldSortingProcessor.name=Ð¡Ð½Ð°ÑÐ°Ð»Ð° ÑÑÐ°ÑÑÐµ
filter.sorting.OskellyChoiceSortingProcessor.name=Ð¡Ð½Ð°ÑÐ°Ð»Ð° ÐÑÐ±Ð¾Ñ OSKELLY
filter.sorting.PriceAscSortingProcessor.name=ÐÐ¾ Ð²Ð¾Ð·ÑÐ°ÑÑÐ°Ð½Ð¸Ñ ÑÐµÐ½Ñ
filter.sorting.PriceDescSortingProcessor.name=ÐÐ¾ ÑÐ±ÑÐ²Ð°Ð½Ð¸Ñ ÑÐµÐ½Ñ
filter.sorting.PromotionTimeDescSortingProcessor.name=Ð¡Ð½Ð°ÑÐ°Ð»Ð° Ð½Ð¾Ð²Ð¸Ð½ÐºÐ¸
filter.sorting.StateTimeAscSortingProcessor.name=ÐÐ¾ Ð²Ð¾Ð·ÑÐ°ÑÑÐ°Ð½Ð¸Ñ Ð²ÑÐµÐ¼ÐµÐ½Ð¸ Ð¾Ð±Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ ÑÑÐ°ÑÑÑÐ°
filter.sorting.StateTimeDescSortingProcessor.name=ÐÐ¾ ÑÐ±ÑÐ²Ð°Ð½Ð¸Ñ Ð²ÑÐµÐ¼ÐµÐ½Ð¸ Ð¾Ð±Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ ÑÑÐ°ÑÑÑÐ°
filter.sorting.SortingProcessorProvider.unknownProcessor=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð½Ð°Ð¹ÑÐ¸ Ð¿Ð¾Ð´ÑÐ¾Ð´ÑÑÐ¸Ð¹ Ð¾Ð±ÑÐ°Ð±Ð¾ÑÑÐ¸Ðº Ð´Ð»Ñ ÑÐ¾ÑÑÐ¸ÑÐ¾Ð²ÐºÐ¸ Ñ ÐºÐ¾Ð´Ð¾Ð¼ {0}
filter.sorting.ChangeTimeDescOrderSortingProcessor.name=ÐÐ¾ ÑÐ±ÑÐ²Ð°Ð½Ð¸Ñ Ð²ÑÐµÐ¼ÐµÐ½Ð¸ Ð¾Ð±Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ Ð·Ð°ÐºÐ°Ð·Ð°
filter.sorting.ChangeTimeAscOrderSortingProcessor.name=ÐÐ¾ Ð²Ð¾Ð·ÑÐ°ÑÑÐ°Ð½Ð¸Ñ Ð²ÑÐµÐ¼ÐµÐ½Ð¸ Ð¾Ð±Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ Ð·Ð°ÐºÐ°Ð·Ð°
filter.sorting.ScoreDescSortingProcessor.name=ÐÐ¾ ÑÐµÐ»ÐµÐ²Ð°Ð½ÑÐ½Ð¾ÑÑÐ¸
filter.sorting.ByProductIdsSortingProcessor.name=ÐÐ¾ ÑÐµÐ»ÐµÐ²Ð°Ð½ÑÐ½Ð¾ÑÑÐ¸
filter.sorting.PersonalizedSortingProcessor.name=ÐÐ¾Ð´Ð¾Ð±ÑÐ°Ð½Ð¾ Ð´Ð»Ñ Ð²Ð°Ñ
filter.controller.availableFilters=ÐÐ¾ÑÑÑÐ¿Ð½ÑÐµ ÑÐ¸Ð»ÑÑÑÑ
filter.controller.availableFilterInfo=ÐÐ¾ÑÑÑÐ¿Ð½ÑÐµ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ñ ÑÐ¸Ð»ÑÑÑÐ°
filter.controller.items=Ð¡Ð¿Ð¸ÑÐ¾Ðº Ð¾Ð±ÑÐµÐºÑÐ¾Ð²
filter.controller.itemsCount=ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð¾Ð±ÑÐµÐºÑÐ¾Ð²

refund.reason.type.message.orderExpertiseTimeExpired=ÐÑÐµÐ¼Ñ Ð¿ÑÐ¾Ð²ÐµÐ´ÐµÐ½Ð¸Ñ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ Ð¸ÑÑÐµÐºÐ»Ð¾
refund.reason.type.message.orderExpertiseFail=Ð­ÐºÑÐ¿ÐµÑÑÐ¸Ð·Ð° Ð¿ÑÐ¾Ð²Ð°Ð»ÐµÐ½Ð°
refund.reason.type.message.fromSellerDeliveryDeclined=ÐÐµ Ð±ÑÐ» Ð¾ÑÐ³ÑÑÐ¶ÐµÐ½
refund.reason.type.message.fromSellerDeliveryFail=ÐÐµ Ð±ÑÐ» Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½ Ð² Ð¾ÑÐ¸Ñ
refund.reason.type.message.adminAction=ÐÐµÐ¹ÑÑÐ²Ð¸Ðµ Ð°Ð´Ð¼Ð¸Ð½Ð¸ÑÑÑÐ°ÑÐ¾ÑÐ°
refund.reason.type.message.fullSaleRejected=ÐÐµ Ð±ÑÐ» Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½
refund.reason.type.message.holdExpired=ÐÐµÑÐ¸Ð¾Ð´ ÑÐ¿Ð¸ÑÐ°Ð½Ð¸Ñ Ð´ÐµÐ½ÐµÐ¶Ð½ÑÑ ÑÑÐµÐ´ÑÑÐ² Ð¸ÑÑÐµÐº
refund.reason.type.message.samePromocode=ÐÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´ ÑÐ¶Ðµ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·ÑÐµÑÑÑ
refund.reason.type.message.promocodeNumberOfAppliesExceeded=ÐÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°Ð½ ÑÐ¾Ð»ÑÐºÐ¾ Ð¾Ð´Ð¸Ð½ ÑÐ°Ð·
refund.reason.type.message.sameGiftCard=ÐÐ¾Ð´Ð°ÑÐ¾ÑÐ½Ð°Ñ ÐºÐ°ÑÑÐ° ÑÐ¶Ðµ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·ÑÐµÑÑÑ
refund.reason.type.message.itemsUnavailable=ÐÑÐµ ÑÐ¾Ð²Ð°ÑÑ Ð² Ð·Ð°ÐºÐ°Ð·Ðµ Ð½Ðµ Ð´Ð¾ÑÑÑÐ¿Ð½Ñ Ð´Ð»Ñ Ð¿Ð¾ÐºÑÐ¿ÐºÐ¸
refund.reason.type.message.excludedFromAgentReport=ÐÑÐµ Ð¿Ð¾Ð·Ð¸ÑÐ¸Ð¸ Ð·Ð°ÐºÐ°Ð·Ð° Ð¸ÑÐºÐ»ÑÑÐµÐ½Ñ Ð¸Ð· Ð¾ÑÑÐµÑÐ° Ð¿ÑÐ¾Ð´Ð°Ð¶
refund.reason.type.message.soldInBoutique=ÐÑÐ¾Ð´Ð°Ð½Ð¾ Ð² Ð±ÑÑÐ¸ÐºÐµ
refund.reason.type.message.returnToSellerFrom1C=ÐÐ¾Ð·Ð²ÑÐ°Ñ ÐÑÐ¾Ð´Ð°Ð²ÑÑ Ð¸Ð· 1Ð¡
refund.reason.type.message.custom=ÐÑÐ¸ÑÐ¸Ð½Ð° Ð½Ðµ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð°
refund.reason.type.message.sold.online=ÐÑÐ¾Ð´Ð°Ð½Ð¾ Ð¾Ð½Ð»Ð°Ð¹Ð½
refund.reason.type.message.inventory=ÐÐ½Ð²ÐµÐ½ÑÐ°ÑÐ¸Ð·Ð°ÑÐ¸Ñ

promocode.sort.PromoCodeSortProvider.unknownSorting=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð½Ð°Ð¹ÑÐ¸ Ð¿Ð¾Ð´ÑÐ¾Ð´ÑÑÐ¸Ð¹ Ð¾Ð±ÑÐ°Ð±Ð¾ÑÑÐ¸Ðº Ð´Ð»Ñ ÑÐ¾ÑÑÐ¸ÑÐ¾Ð²ÐºÐ¸ Ñ ÐºÐ¾Ð´Ð¾Ð¼ {0}
exception.user.deleted=ÐÐºÐºÐ°ÑÐ½Ñ ÑÐ´Ð°Ð»ÐµÐ½
infrastructure.notificationDelivery.mailganer.trigger.order.OrderDeliveredToBuyerMailganerTrigger.subtitle.isPro=ÐÐµÐ½ÑÐ³Ð¸ Ð±ÑÐ´ÑÑ Ð·Ð°ÑÐ¸ÑÐ»ÐµÐ½Ñ Ð½Ð°Â ÑÑÐµÑ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ° ÑÐµÑÐµÐ· 7Â Ð´Ð½ÐµÐ¹
infrastructure.notificationDelivery.mailganer.trigger.order.OrderDeliveredToBuyerMailganerTrigger.subtitle=ÐÐµÐ½ÑÐ³Ð¸ Ð·Ð°ÑÐ¸ÑÐ»ÐµÐ½Ñ Ð½Ð°Â ÑÑÐµÑ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°.
infrastructure.notificationDelivery.mailganer.trigger.sale.OrderDeliveredToBuyerNeedAgentReportMailganerTrigger.header.isPro=ÐÐ°Ñ Ð·Ð°ÐºÐ°Ð· Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½. ÐÑÑÐ°Ð»Ð¾ÑÑ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÑ Ð´Ð°Ð½Ð½ÑÐµ
infrastructure.notificationDelivery.mailganer.trigger.sale.OrderDeliveredToBuyerNeedAgentReportMailganerTrigger.header=ÐÐ°ÐºÐ°Ð· Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½! ÐÐ¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÐµ Ð´Ð°Ð½Ð½ÑÐµ
infrastructure.notificationDelivery.mailganer.trigger.sale.OrderDeliveredToBuyerNeedAgentReportMailganerTrigger.preheader.isPro=ÐÐ¿Ð»Ð°ÑÐ° Ð¿Ð¾ÑÑÑÐ¿Ð¸Ñ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ ÑÑÑÐ¾Ðº Ð¿Ð¾ÑÐ»Ðµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ñ
infrastructure.notificationDelivery.mailganer.trigger.sale.OrderDeliveredToBuyerNeedAgentReportMailganerTrigger.preheader=ÐÐ¿Ð»Ð°ÑÐ° Ð¿Ð¾ÑÑÑÐ¿Ð¸Ñ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ ÑÑÑÐ¾Ðº
infrastructure.notificationDelivery.mailganer.trigger.sale.SaleCompletedMailganerTrigger.header.isPro=ÐÐ¾Ð·Ð´ÑÐ°Ð²Ð»ÑÐµÐ¼, Ð²Ð°Ñ Ð·Ð°ÐºÐ°Ð· Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½
infrastructure.notificationDelivery.mailganer.trigger.sale.SaleCompletedMailganerTrigger.preheader.isPro=ÐÐ°Ð½Ð½ÑÐµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ñ, Ð´ÐµÐ½ÑÐ³Ð¸ Ð²ÑÐ¿Ð»Ð°ÑÐµÐ½Ñ
infrastructure.notificationDelivery.mailganer.trigger.sale.SaleCompletedMailganerTrigger.info.isPro=ÐÐµÐ½ÑÐ³Ð¸ Ð²ÑÐ¿Ð»Ð°ÑÐµÐ½Ñ.
infrastructure.notificationDelivery.mailganer.trigger.sale.SaleCompletedMailganerTrigger.header=ÐÐ°ÐºÐ°Ð· Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½, ÑÑÐµÐ´ÑÑÐ²Ð° Ð²ÑÐ¿Ð»Ð°ÑÐµÐ½Ñ
infrastructure.notificationDelivery.mailganer.trigger.sale.SaleCompletedMailganerTrigger.preheader=ÐÐ¶Ð¸Ð´Ð°Ð¹ÑÐµ Ð¿Ð¾ÑÑÑÐ¿Ð»ÐµÐ½Ð¸Ñ Ð² ÑÐµÑÐµÐ½Ð¸Ð¸ ÑÑÑÐ¾Ðº
infrastructure.mailganer.DeliveryTrackingUrl=https://oskelly.ru/account
infrastructure.mailganer.expertise.free=ÐÐµÑÐ¿Ð»Ð°ÑÐ½Ð¾
infrastructure.mailganer.DeliveryTerm=7 ÑÐ°Ð±Ð¾ÑÐ¸Ñ
infrastructure.security.provider.register.OAuthRegistrationProvider.EmailMissmatchException=ÐÑÐµÐ´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½Ð½ÑÐ¹ email Ð¾ÑÐ»Ð¸ÑÐ°ÐµÑÑÑ Ð¾Ñ email ÑÐµÐºÑÑÐµÐ³Ð¾ Ð°ÐºÐºÐ°ÑÐ½ÑÐ°
infrastructure.security.provider.register.OAuthRegistrationProvider.NicknameMissmatchException=ÐÑÐµÐ´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½Ð½ÑÐ¹ nickname Ð¾ÑÐ»Ð¸ÑÐ°ÐµÑÑÑ Ð¾Ñ nickname ÑÐµÐºÑÑÐµÐ³Ð¾ Ð°ÐºÐºÐ°ÑÐ½ÑÐ°
infrastructure.security.provider.register.OAuthRegistrationProvider.EmailAlreadyExistsException=ÐÑÐµÐ´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½Ð½ÑÐ¹ email Ð·Ð°Ð½ÑÑ Ð´ÑÑÐ³Ð¸Ð¼ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÐµÐ¼. ÐÐ¾Ð¼ÐµÐ½ÑÐ¹ÑÐµ email Ð¸Ð»Ð¸ Ð°Ð²ÑÐ¾ÑÐ¸Ð·ÑÐ¹ÑÐµÑÑ Ð¿Ð¾Ð´ ÑÑÑÐµÑÑÐ²ÑÑÑÐ¸Ð¼ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÐµÐ¼.
infrastructure.security.provider.register.OAuthRegistrationProvider.NicknameAlreadyExistsException=ÐÑÐµÐ´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½Ð½ÑÐ¹ nickname Ð·Ð°Ð½ÑÑ Ð´ÑÑÐ³Ð¸Ð¼ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÐµÐ¼. ÐÐ¾Ð¼ÐµÐ½ÑÐ¹ÑÐµ nickname Ð¸Ð»Ð¸ Ð°Ð²ÑÐ¾ÑÐ¸Ð·ÑÐ¹ÑÐµÑÑ Ð¿Ð¾Ð´ ÑÑÑÐµÑÑÐ²ÑÑÑÐ¸Ð¼ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÐµÐ¼.
infrastructure.security.resetpassword=ÐÐ¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ Ð¿Ð°ÑÐ¾Ð»Ñ
infrastructure.security.setpassword=Ð£ÑÑÐ°Ð½Ð¾Ð²ÐºÐ° Ð¿Ð°ÑÐ¾Ð»Ñ
infrastructure.security.SecurityServiceImpl.Exception.BadLink=Ð¡ÑÑÐ»ÐºÐ° Ð½ÐµÐ´ÐµÐ¹ÑÑÐ²Ð¸ÑÐµÐ»ÑÐ½Ð°
infrastructure.security.SecurityServiceImpl.Exception.LinkExpired=Ð¡ÑÐ¾Ðº Ð´ÐµÐ¹ÑÑÐ²Ð¸Ñ ÑÑÑÐ»ÐºÐ¸ Ð¸ÑÑÐµÐº

service.CommonLogisticService.EmptyAddress=ÐÑÑÑÐ¾Ð¹ Ð°Ð´ÑÐµÑ Ð² Ð·Ð°ÐºÐ°Ð·Ðµ {0}
service.CommonLogisticService.DeliveryServiceAlreadyCalled=ÐÑÑÑÐµÑÑÐºÐ°Ñ ÑÐ»ÑÐ¶Ð±Ð° ÑÐ¶Ðµ Ð±ÑÐ»Ð° Ð²ÑÐ·Ð²Ð°Ð½Ð°
service.CommonLogisticService.AddressIsDeleted=ÐÐ´ÑÐµÑ Ð² Ð·Ð°ÐºÐ°Ð·Ðµ {0} ÑÐ´Ð°Ð»ÐµÐ½

service.CseLogisticService.MissingPhone=Ð Ð°Ð´ÑÐµÑÐµ Ð² Ð·Ð°ÐºÐ°Ð·Ðµ {0} Ð½Ðµ ÑÐºÐ°Ð·Ð°Ð½ ÑÐµÐ»ÐµÑÐ¾Ð½

service.PrimaryServiceImpl.getConciergeBannerBlock.Head=Ð½Ð¾Ð²ÑÐµ ÐºÐ¾Ð»Ð»ÐµÐºÑÐ¸Ð¸
service.PrimaryServiceImpl.getConciergeBannerBlock.Title=Oskelly Concierge
service.PrimaryServiceImpl.getConciergeBannerBlock.Description=ÐÐ¾Ð¸ÑÐº Ð»ÑÐ±Ð¾Ð³Ð¾ ÑÐ¾Ð²Ð°ÑÐ° Ð¿Ð¾ Ð²ÑÐµÐ¼Ñ Ð¼Ð¸ÑÑ
service.PrimaryServiceImpl.getConciergeBannerBlock.ButtonTitle=ÐÑÑÐ°Ð²Ð¸ÑÑ Ð·Ð°ÑÐ²ÐºÑ
infrastructure.security.provider.auth.EmailAuthenticationProvider.BadEmail=ÐÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½ÑÐ¹ email
infrastructure.security.provider.auth.EmailAuthenticationProvider.BadPassword=ÐÐµÐ²ÐµÑÐ½ÑÐ¹ Ð¿Ð°ÑÐ¾Ð»Ñ
infrastructure.security.provider.auth.EmailAuthenticationProvider.BadCredential=ÐÐµÐ²ÐµÑÐ½ÑÐ¹ Ð»Ð¾Ð³Ð¸Ð½ Ð¸Ð»Ð¸ Ð¿Ð°ÑÐ¾Ð»Ñ
infrastructure.security.validation.ValidNewPasswordValidator.RequiredFieldsMissed=ÐÐµ Ð·Ð°Ð´Ð°Ð½Ñ Ð¾Ð±ÑÐ·Ð°ÑÐµÐ»ÑÐ½ÑÐµ Ð¿Ð¾Ð»Ñ
infrastructure.security.validation.ValidNewPasswordValidator.PasswordsNotMatched=ÐÐ°ÑÐ¾Ð»Ð¸ Ð½Ðµ ÑÐ¾Ð²Ð¿Ð°Ð´Ð°ÑÑ
infrastructure.security.validation.ValidNewPasswordValidator.PasswordNotSpecified=ÐÐµ ÑÐºÐ°Ð·Ð°Ð½ Ð¿Ð°ÑÐ¾Ð»Ñ
infrastructure.security.validation.ValidNewPasswordValidator.PasswordConfirmationNotSpecified=ÐÐµ ÑÐºÐ°Ð·Ð°Ð½ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´Ð°ÑÑÐ¸Ð¹ Ð¿Ð°ÑÐ¾Ð»Ñ
infrastructure.security.validation.ValidNewPasswordValidator.ResetTokenNotSpecified=ÐÐµ ÑÐºÐ°Ð·Ð°Ð½ ÑÐ¾ÐºÐµÐ½

infrastructure.security.provider.auth.PhoneNumberAuthenticationProvider.BadJWTToken=ÐÐµ Ð¿ÑÐ°Ð²Ð¸Ð»ÑÐ½ÑÐ¹ JWT ÑÐ¾ÐºÐµÐ½ {0}
infrastructure.security.provider.auth.PhoneNumberAuthenticationProvider.UserNotFound=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ñ ÑÐ°ÐºÐ¸Ð¼ Ð½Ð¾Ð¼ÐµÑÐ¾Ð¼ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½ {0}
infrastructure.security.provider.auth.GoogleIdTokenAuthProvider.BadIdToken=ÐÐµ Ð¿ÑÐ°Ð²Ð¸Ð»ÑÐ½ÑÐ¹ Google ÑÐ¾ÐºÐµÐ½
infrastructure.security.provider.auth.GoogleIdTokenAuthProvider.UserNotFound=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
infrastructure.security.provider.auth.YandexIdTokenAuthProvider.BadIdToken=ÐÐµ Ð¿ÑÐ°Ð²Ð¸Ð»ÑÐ½ÑÐ¹ Yandex ÑÐ¾ÐºÐµÐ½
infrastructure.security.provider.auth.YandexIdTokenAuthProvider.UserNotFound=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
infrastructure.security.provider.auth.AppleIdTokenAuthProvider.BadIdToken=ÐÐµ Ð¿ÑÐ°Ð²Ð¸Ð»ÑÐ½ÑÐ¹ Apple ÑÐ¾ÐºÐµÐ½
infrastructure.security.provider.auth.AppleIdTokenAuthProvider.UserNotFound=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
su.reddot.infrastructure.security.provider.auth.SocialReAuthProvider.UserNotFound=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ñ email {0} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
su.reddot.infrastructure.security.provider.auth.SocialReAuthProvider.BadJWTToken=ÐÑÐ¸Ð±ÐºÐ° Ð¿Ð¾Ð²ÑÐ¾ÑÐ½Ð¾Ð¹ Ð°Ð²ÑÐ¾ÑÐ¸Ð·Ð°ÑÐ¸Ð¸
infrastructure.security.provider.register.v2.PhoneNumberRegistrationProvider.incorrectPhoneNumber=ÐÐµÐ²ÐµÑÐ½ÑÐ¹ Ð½Ð¾Ð¼ÐµÑ ÑÐµÐ»ÐµÑÐ¾Ð½Ð°
infrastructure.security.provider.register.v2.GoogleRegistrationProvider.userWithGoogleIdAlreadyExist=ÐÑ ÑÐ¶Ðµ ÑÐµÐ³Ð¸ÑÑÑÐ¸ÑÐ¾Ð²Ð°Ð»Ð¸ÑÑ Ð´Ð°Ð½Ð½ÑÐ¼ ÑÐ¿Ð¾ÑÐ¾Ð±Ð¾Ð¼, Ð¿Ð¾Ð¿ÑÐ¾Ð±ÑÐ¹ÑÐµ Ð°Ð²ÑÐ¾ÑÐ¸Ð·Ð¾Ð²Ð°ÑÑÑÑ Ð¿Ð¾Ð²ÑÐ¾ÑÐ½Ð¾

infrastructure.security.provider.register.v2.AbstractRegistrationProvider.tokenEmailNotEqualFormEmail=ÐÐµÐ²ÐµÑÐ½ÑÐ¹ email
infrastructure.security.provider.register.v2.AbstractRegistrationProvider.userWithPhoneNumberAlreadyExist=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ñ ÑÐ°ÐºÐ¸Ð¼ Ð½Ð¾Ð¼ÐµÑÐ¾Ð¼ ÑÐµÐ»ÐµÑÐ¾Ð½Ð° ÑÐ¶Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ
infrastructure.security.provider.register.v2.AbstractRegistrationProvider.unexpectedPhoneNumber=ÐÐµÐ²ÐµÑÐ½ÑÐ¹ Ð½Ð¾Ð¼ÐµÑ ÑÐµÐ»ÐµÑÐ¾Ð½Ð°

infrastructure.security.provider.register.v2.YandexRegistrationProvider.userWithYandexIdAlreadyExist=ÐÑ ÑÐ¶Ðµ ÑÐµÐ³Ð¸ÑÑÑÐ¸ÑÐ¾Ð²Ð°Ð»Ð¸ÑÑ Ð´Ð°Ð½Ð½ÑÐ¼ ÑÐ¿Ð¾ÑÐ¾Ð±Ð¾Ð¼, Ð¿Ð¾Ð¿ÑÐ¾Ð±ÑÐ¹ÑÐµ Ð°Ð²ÑÐ¾ÑÐ¸Ð·Ð¾Ð²Ð°ÑÑÑÑ Ð¿Ð¾Ð²ÑÐ¾ÑÐ½Ð¾
su.reddot.infrastructure.security.provider.register.v2.AppleRegistrationProviderV2.userWithAppleRestIdAlreadyExist=ÐÑ ÑÐ¶Ðµ ÑÐµÐ³Ð¸ÑÑÑÐ¸ÑÐ¾Ð²Ð°Ð»Ð¸ÑÑ Ð´Ð°Ð½Ð½ÑÐ¼ ÑÐ¿Ð¾ÑÐ¾Ð±Ð¾Ð¼, Ð¿Ð¾Ð¿ÑÐ¾Ð±ÑÐ¹ÑÐµ Ð°Ð²ÑÐ¾ÑÐ¸Ð·Ð¾Ð²Ð°ÑÑÑÑ Ð¿Ð¾Ð²ÑÐ¾ÑÐ½Ð¾


service.verification.VerificationService.invalidJwtToken=ÐÐµÐ²ÐµÑÐ½ÑÐ¹ ÐºÐ¾Ð´ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ñ

su.reddot.presentation.Utils.ValidationNotPassed=ÐÐ°Ð»Ð¸Ð´Ð°ÑÐ¸Ñ Ð½Ðµ Ð¿ÑÐ¾Ð¹Ð´ÐµÐ½Ð°

infrastructure.util.Utils.Product.One=ÑÐ¾Ð²Ð°Ñ
infrastructure.util.Utils.Product.Many=ÑÐ¾Ð²Ð°ÑÐ¾Ð²
infrastructure.util.Utils.Product.Other=ÑÐ¾Ð²Ð°ÑÐ°
infrastructure.util.Utils.Item.One=ÑÐ¾Ð²Ð°Ñ
infrastructure.util.Utils.Item.Many=ÑÐ¾Ð²Ð°ÑÐ¾Ð²
infrastructure.util.Utils.Item.Other=ÑÐ¾Ð²Ð°ÑÐ°
infrastructure.util.Utils.NewItem.One=Ð½Ð¾Ð²Ð¸Ð½ÐºÐ°
infrastructure.util.Utils.NewItem.Many=Ð½Ð¾Ð²Ð¸Ð½Ð¾Ðº
infrastructure.util.Utils.NewItem.Other=Ð½Ð¾Ð²Ð¸Ð½ÐºÐ¸
infrastructure.util.Utils.Day.One=Ð´ÐµÐ½Ñ
infrastructure.util.Utils.Day.Many=Ð´Ð½ÐµÐ¹
infrastructure.util.Utils.Day.Other=Ð´Ð½Ñ
infrastructure.util.Utils.WorkingDay.One=ÑÐ°Ð±Ð¾ÑÐ¸Ð¹ Ð´ÐµÐ½Ñ
infrastructure.util.Utils.WorkingDay.Many=ÑÐ°Ð±Ð¾ÑÐ¸Ñ Ð´Ð½ÐµÐ¹
infrastructure.util.Utils.WorkingDay.Other=ÑÐ°Ð±Ð¾ÑÐ¸Ñ Ð´Ð½Ñ

infrastructure.util.Utils.ProductRequest.One=Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½Ð¸Ðµ
infrastructure.util.Utils.ProductRequest.Many=Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½Ð¸Ð¹
infrastructure.util.Utils.ProductRequest.Other=Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½Ð¸Ñ

service.BannerContentService.getStoriesBlock.Title=ÐÐ±ÑÐ°Ð·Ñ
service.DefaultPrimaryServiceReactor.getStoriesContent.Title=ÐÐ±ÑÐ°Ð·Ñ
service.BannerContentService.BestSellers=ÐÑÑÑÐ¸Ðµ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÑ

boutique.delivery.seller.warning=ÐÐÐÐÐÐÐÐ: ÐÐ°Ð±Ð¾Ñ Ð·Ð°ÐºÐ°Ð·Ð° Ð¸Ð· Ð±ÑÑÐ¸ÐºÐ° OSKELLY!
boutique.delivery.buyer.warning=ÐÐÐÐÐÐÐÐ: ÐÐ°ÐºÐ°Ð· Ð´Ð¾ÑÑÐ°Ð²Ð»ÑÐµÑÑÑ Ð² Ð±ÑÑÐ¸Ðº OSKELLY!
infrastructure.util.Utils.User.One=Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ
infrastructure.util.Utils.User.Many=Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÐµÐ¹
infrastructure.util.Utils.User.Other=Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ

entity.order.delivery=ÐÐ¾ÑÑÐ°Ð²ÐºÐ°

cart.items.delivery=ÐÐ¾ÑÑÐ°Ð²ÐºÐ°

PositionStateTitle.WAITING_FOR_CONFIRMATION.Seller=ÐÐ¶Ð¸Ð´Ð°ÐµÑ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ñ
PositionStateTitle.CONFIRMED.Seller=ÐÐ¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½
PositionStateTitle.DECLINED.Seller=ÐÑÐ¼ÐµÐ½ÐµÐ½
PositionStateTitle.NOT_HANDED_OVER_BY_SELLER.Seller=ÐÑÐ¼ÐµÐ½ÐµÐ½
PositionStateTitle.NOT_DELIVERED_TO_OFFICE.Seller=ÐÐµ Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½ Ð² Oskelly
PositionStateTitle.WAITING_FOR_EXPERTISE.Seller=Ð Ð¾ÑÐµÑÐµÐ´Ð¸ Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
PositionStateTitle.PASSING_THROUGH_EXPERTISE.Seller=ÐÑÐ¾ÑÐ¾Ð´Ð¸Ñ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
PositionStateTitle.DEFECTS_FOUND.Seller=Ð¢ÑÐµÐ±ÑÐµÑ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ð¸Ñ
PositionStateTitle.DEFECTS_RECONCILED.Seller=Ð¡ÐºÐ¸Ð´ÐºÐ° Ð·Ð° Ð½ÑÐ°Ð½Ñ
PositionStateTitle.DEFECTS_RECONCILED_NO_DISCOUNT.Seller=ÐÑÐ°Ð½ÑÑ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ
PositionStateTitle.DEFECTS_NOT_RECONCILED.Seller=ÐÑÐ¼ÐµÐ½ÐµÐ½
PositionStateTitle.EXPERTISE_SUCCEEDED.Seller=ÐÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
PositionStateTitle.EXPERTISE_FAILED.Seller=ÐÐµ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
PositionStateTitle.DETERMINE_AUTHENTICITY_IMPOSSIBLE.Seller=ÐÑÐ¼ÐµÐ½ÐµÐ½
PositionStateTitle.APPROVED_BY_OSKELLY.Seller=ÐÐ´Ð¾Ð±ÑÐµÐ½ Oskelly
PositionStateTitle.DELIVERED.Seller=ÐÐ¾ÑÑÐ°Ð²Ð»ÐµÐ½
PositionStateTitle.NOT_DELIVERED_TO_BUYER.Seller=ÐÐµ Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½
PositionStateTitle.DESTROYED.Seller=ÐÑÐ¼ÐµÐ½ÐµÐ½

PositionStateTitle.WAITING_FOR_CONFIRMATION.Buyer=ÐÐ¶Ð¸Ð´Ð°ÐµÑ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ñ
PositionStateTitle.CONFIRMED.Buyer=ÐÐ¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½
PositionStateTitle.DECLINED.Buyer=ÐÑÐ¼ÐµÐ½ÐµÐ½
PositionStateTitle.NOT_HANDED_OVER_BY_SELLER.Buyer=ÐÑÐ¼ÐµÐ½ÐµÐ½
PositionStateTitle.NOT_DELIVERED_TO_OFFICE.Buyer=ÐÐµ Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½ Ð² Oskelly
PositionStateTitle.WAITING_FOR_EXPERTISE.Buyer=Ð Ð¾ÑÐµÑÐµÐ´Ð¸ Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
PositionStateTitle.PASSING_THROUGH_EXPERTISE.Buyer=ÐÑÐ¾ÑÐ¾Ð´Ð¸Ñ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
PositionStateTitle.DEFECTS_FOUND.Buyer=Ð¢ÑÐµÐ±ÑÐµÑ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ð¸Ñ
PositionStateTitle.DEFECTS_RECONCILED.Buyer=Ð¡ÐºÐ¸Ð´ÐºÐ° Ð·Ð° Ð½ÑÐ°Ð½Ñ
PositionStateTitle.DEFECTS_RECONCILED_NO_DISCOUNT.Buyer=ÐÑÐ°Ð½ÑÑ ÑÐ¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ñ
PositionStateTitle.DEFECTS_NOT_RECONCILED.Buyer=ÐÑÐ¼ÐµÐ½ÐµÐ½
PositionStateTitle.EXPERTISE_SUCCEEDED.Buyer=ÐÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
PositionStateTitle.EXPERTISE_FAILED.Buyer=ÐÐµ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
PositionStateTitle.DETERMINE_AUTHENTICITY_IMPOSSIBLE.Buyer=ÐÑÐ¼ÐµÐ½ÐµÐ½
PositionStateTitle.APPROVED_BY_OSKELLY.Buyer=ÐÐ´Ð¾Ð±ÑÐµÐ½ Oskelly
PositionStateTitle.DELIVERED.Buyer=ÐÐ¾ÑÑÐ°Ð²Ð»ÐµÐ½
PositionStateTitle.NOT_DELIVERED_TO_BUYER.Buyer=ÐÐµ Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½
PositionStateTitle.DESTROYED.Buyer=ÐÑÐ¼ÐµÐ½ÐµÐ½

CardPaymentOptionProvider.title=ÐÐ¿Ð»Ð°ÑÐ° ÐºÐ°ÑÑÐ¾Ð¹
YandexPayOptionProvider.title=Ð¯Ð½Ð´ÐµÐºÑ ÐÑÐ¹
YandexSplitOptionProvider.title=Ð¯Ð½Ð´ÐµÐºÑ Ð¡Ð¿Ð»Ð¸Ñ
TabbySplitOptionProvider.title=4 Ð±ÐµÑÐ¿ÑÐ¾ÑÐµÐ½ÑÐ½ÑÑ Ð¿Ð»Ð°ÑÐµÐ¶Ð°
AbstractSplitService.part0=Ð¡ÐµÐ³Ð¾Ð´Ð½Ñ
AbstractSplitService.chronoUnit.single.MONTHS=Ð¼ÐµÑÑÑ
AbstractSplitService.chronoUnit.plural.MONTHS=Ð¼ÐµÑÑÑÐµÐ²
AbstractSplitService.chronoUnit.pluralExt.MONTHS=Ð¼ÐµÑÑÑÐ°
AbstractSplitService.chronoUnit.single.WEEKS=Ð½ÐµÐ´ÐµÐ»Ñ
AbstractSplitService.chronoUnit.plural.WEEKS=Ð½ÐµÐ´ÐµÐ»Ñ
AbstractSplitService.chronoUnit.pluralExt.WEEKS=Ð½ÐµÐ´ÐµÐ»Ð¸
#todo Ð»Ð¸ÑÐµÑÐ°ÑÑÑÐ½ÑÐ¹ Ð¿ÐµÑÐµÐ²Ð¾Ð´
TabbySplitOptionProvider.description=Always 0% interest and no fees when you pay on time. Shariah-compliant
SBPOptionProvider.title=Ð¡ÐÐ
NoonApplePayOptionProvider.title=Apple Pay

SaleRequestService.timeout=ÐÑÐµÐ²ÑÑÐµÐ½Ð¾ Ð²ÑÐµÐ¼Ñ Ð¾Ð¶Ð¸Ð´Ð°Ð½Ð¸Ñ ÑÐµÐ·ÑÐ»ÑÑÐ°ÑÐ° Ð¾Ð±ÑÐ°Ð±Ð¾ÑÐºÐ¸ Ð·Ð°Ð¿ÑÐ¾ÑÐ° Ð·Ð°ÑÐ²ÐºÐ¸ Ð½Ð° Ð¿ÑÐ¾Ð´Ð°Ð¶Ñ
SaleRequestService.notFound=ÐÐ°ÑÐ²ÐºÐ° Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð°

CommonLogisticService.timeIntervalFromTo=Ñ {0}:00 Ð´Ð¾ {1}:00

OrderStateTitle.WAITING_FOR_CONFIRMATION.Buyer=ÐÐ¶Ð¸Ð´Ð°ÐµÑ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ñ
OrderStateTitle.CONFIRMED.Buyer=ÐÐ°ÐºÐ°Ð· Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½
OrderStateTitle.CONFIRMED_PARTIALLY.Buyer=ÐÑÐ¾Ð´Ð°Ð¶Ð° Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð° ÑÐ°ÑÑÐ¸ÑÐ½Ð¾
OrderStateTitle.SALE_REJECTED.Buyer=ÐÐ°ÐºÐ°Ð· Ð¾ÑÐºÐ»Ð¾Ð½ÐµÐ½
OrderStateTitle.DELIVERY_TO_OFFICE.Buyer=ÐÐ°ÐºÐ°Ð· Ð¿ÐµÑÐµÐ´Ð°Ð½ Ð² ÐºÑÑÑÐµÑÑÐºÑÑ ÑÐ»ÑÐ¶Ð±Ñ
OrderStateTitle.DELIVERED_TO_OFFICE.Buyer=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¸Ð±ÑÐ» Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
OrderStateTitle.PICKUP_DECLINED.Buyer=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð±ÑÐ» Ð¾ÑÐ³ÑÑÐ¶ÐµÐ½ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð¼
OrderStateTitle.DEFECT_RECONCILIATION.Buyer=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ Ð½ÑÐ°Ð½ÑÐ¾Ð²
OrderStateTitle.EXPERTISE_SUCCEEDED.Buyer=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
OrderStateTitle.EXPERTISE_PARTIALLY_SUCCEEDED.Buyer=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ ÑÐ°ÑÑÐ¸ÑÐ½Ð¾
OrderStateTitle.EXPERTISE_FAILED.Buyer=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
OrderStateTitle.DELIVERY_TO_BUYER.Buyer=ÐÐ°ÐºÐ°Ð· Ð¿ÐµÑÐµÐ´Ð°Ð½ ÐºÑÑÑÐµÑÑ
OrderStateTitle.DELIVERED_TO_BUYER.Buyer=ÐÐ°ÐºÐ°Ð· Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½
OrderStateTitle.RETURNED.Buyer=ÐÐ°ÐºÐ°Ð· Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµÐ½

OrderStateTitle.WAITING_FOR_CONFIRMATION.Seller=ÐÐ¶Ð¸Ð´Ð°ÐµÑ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¸Ñ
OrderStateTitle.CONFIRMED.Seller=ÐÑÐ¾Ð´Ð°Ð¶Ð° Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð°
OrderStateTitle.CONFIRMED_PARTIALLY.Seller=ÐÑÐ¾Ð´Ð°Ð¶Ð° Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð° ÑÐ°ÑÑÐ¸ÑÐ½Ð¾
OrderStateTitle.SALE_REJECTED.Seller=ÐÑÐ¾Ð´Ð°Ð¶Ð° Ð¾ÑÐºÐ»Ð¾Ð½ÐµÐ½Ð°
OrderStateTitle.DELIVERY_TO_OFFICE.Seller=ÐÐ°ÐºÐ°Ð· Ð¿ÐµÑÐµÐ´Ð°Ð½ Ð² ÐºÑÑÑÐµÑÑÐºÑÑ ÑÐ»ÑÐ¶Ð±Ñ
OrderStateTitle.DELIVERED_TO_OFFICE.Seller=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¸Ð±ÑÐ» Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
OrderStateTitle.PICKUP_DECLINED.Seller=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð±ÑÐ» Ð¾ÑÐ³ÑÑÐ¶ÐµÐ½ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð¼
OrderStateTitle.DEFECT_RECONCILIATION.Seller=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ Ð½ÑÐ°Ð½ÑÐ¾Ð²
OrderStateTitle.EXPERTISE_SUCCEEDED.Seller=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
OrderStateTitle.EXPERTISE_PARTIALLY_SUCCEEDED.Seller=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ ÑÐ°ÑÑÐ¸ÑÐ½Ð¾
OrderStateTitle.EXPERTISE_FAILED.Seller=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
OrderStateTitle.DELIVERY_TO_BUYER.Seller=ÐÐ°ÐºÐ°Ð· Ð¿ÐµÑÐµÐ´Ð°Ð½ Ð² ÐºÑÑÑÐµÑÑÐºÑÑ ÑÐ»ÑÐ¶Ð±Ñ
OrderStateTitle.DELIVERED_TO_BUYER.Seller=ÐÐ°ÐºÐ°Ð· Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½
OrderStateTitle.RETURNED.Seller=ÐÐ°ÐºÐ°Ð· Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµÐ½

OrderStateTitle.WAITING_FOR_CONFIRMATION.OnlineBoutique=ÐÐ°ÐºÐ°Ð· Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ´Ð¸Ñ Ð½Ð°Ñ Ð¼ÐµÐ½ÐµÐ´Ð¶ÐµÑ
OrderStateTitle.CONFIRMED.OnlineBoutique=ÐÑÐ¾Ð´Ð°Ð¶Ð° Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð°
OrderStateTitle.CONFIRMED_PARTIALLY.OnlineBoutique=ÐÑÐ¾Ð´Ð°Ð¶Ð° Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð° ÑÐ°ÑÑÐ¸ÑÐ½Ð¾
OrderStateTitle.SALE_REJECTED.OnlineBoutique=ÐÑÐ¾Ð´Ð°Ð¶Ð° Ð¾ÑÐºÐ»Ð¾Ð½ÐµÐ½Ð°
OrderStateTitle.DELIVERY_TO_OFFICE.OnlineBoutique=ÐÐ°ÐºÐ°Ð· Ð¿ÐµÑÐµÐ´Ð°Ð½ Ð² ÐºÑÑÑÐµÑÑÐºÑÑ ÑÐ»ÑÐ¶Ð±Ñ
OrderStateTitle.DELIVERED_TO_OFFICE.OnlineBoutique=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¸Ð±ÑÐ» Ð½Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
OrderStateTitle.PICKUP_DECLINED.OnlineBoutique=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð±ÑÐ» Ð¾ÑÐ³ÑÑÐ¶ÐµÐ½ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ¾Ð¼
OrderStateTitle.DEFECT_RECONCILIATION.OnlineBoutique=Ð¡Ð¾Ð³Ð»Ð°ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ Ð½ÑÐ°Ð½ÑÐ¾Ð²
OrderStateTitle.EXPERTISE_SUCCEEDED.OnlineBoutique=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
OrderStateTitle.EXPERTISE_PARTIALLY_SUCCEEDED.OnlineBoutique=ÐÐ°ÐºÐ°Ð· Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ ÑÐ°ÑÑÐ¸ÑÐ½Ð¾
OrderStateTitle.EXPERTISE_FAILED.OnlineBoutique=ÐÐ°ÐºÐ°Ð· Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
OrderStateTitle.DELIVERY_TO_BUYER.OnlineBoutique=ÐÐ°ÐºÐ°Ð· Ð¿ÐµÑÐµÐ´Ð°Ð½ Ð² ÐºÑÑÑÐµÑÑÐºÑÑ ÑÐ»ÑÐ¶Ð±Ñ
OrderStateTitle.DELIVERED_TO_BUYER.OnlineBoutique=ÐÐ°ÐºÐ°Ð· Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½
OrderStateTitle.RETURNED.OnlineBoutique=ÐÐ°ÐºÐ°Ð· Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµÐ½


su.reddot.presentation.api.v2.adminpanel.users.dto.BadgeDTO.USER_TYPE.INDIVIDUAL=Ð¤Ð¸Ð·Ð¸ÑÐµÑÐºÐ¾Ðµ Ð»Ð¸ÑÐ¾
su.reddot.presentation.api.v2.adminpanel.users.dto.BadgeDTO.USER_TYPE.LEGAL_ENTITY=Ð®ÑÐ¸Ð´Ð¸ÑÐµÑÐºÐ¾Ðµ Ð»Ð¸ÑÐ¾
su.reddot.presentation.api.v2.adminpanel.users.dto.BadgeDTO.SELLER_TYPE.INDIVIDUAL=Ð§Ð°ÑÑÐ½ÑÐ¹ Ð¿ÑÐ¾Ð´Ð°Ð²ÐµÑ
su.reddot.presentation.api.v2.adminpanel.users.dto.BadgeDTO.SELLER_TYPE.CONSIGNMENT_SHOP=Ð ÐµÑÐµÐ¹Ð» Ð¼Ð°Ð³Ð°Ð·Ð¸Ð½
su.reddot.presentation.api.v2.adminpanel.users.dto.BadgeDTO.SELLER_TYPE.BOUTIQUE=ÐÑÑÐ¸Ðº
su.reddot.presentation.api.v2.adminpanel.users.dto.BadgeDTO.SELLER_TYPE.BRAND=ÐÑÐµÐ½Ð´
su.reddot.presentation.api.v2.adminpanel.users.dto.BadgeDTO.SELLER_TYPE.BUYER=ÐÐµÑÑÐ¾Ð½Ð°Ð»ÑÐ½ÑÐ¹ ÑÐ¾Ð¿ÐµÑ
su.reddot.presentation.api.v2.adminpanel.users.dto.BadgeDTO.RETURNS.acceptReturns=ÐÑÐ¸Ð½Ð¸Ð¼Ð°ÐµÑ Ð²Ð¾Ð·Ð²ÑÐ°ÑÑ

EventDateEstimation.calculationContext.afterExpertiseEstimationChange=ÐÐ¾ÑÐ»Ðµ Ð¸Ð·Ð¼ÐµÐ½ÐµÐ½Ð¸Ñ Ð¿ÑÐ¾Ð³Ð½Ð¾Ð·Ð° ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
EventDateEstimation.calculationContext.afterPickupFromSeller=ÐÐ¾ÑÐ»Ðµ Ð·Ð°Ð±Ð¾ÑÐ° Ñ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°
EventDateEstimation.calculationContext.afterDeliveryToOffice=ÐÐ¾ÑÐ»Ðµ Ð´Ð¾ÑÑÐ°Ð²ÐºÐ¸ Ð² Ð¾ÑÐ¸Ñ
EventDateEstimation.calculationContext.afterExpertiseCompletion=ÐÐ¾ÑÐ»Ðµ Ð¾ÐºÐ¾Ð½ÑÐ°Ð½Ð¸Ñ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
EventDateEstimation.calculationContext.afterPickupFromOffice=ÐÐ¾ÑÐ»Ðµ Ð·Ð°Ð±Ð¾ÑÐ° Ð¸Ð· Ð¾ÑÐ¸ÑÐ°
EventDateEstimation.calculationContext.afterExpertiseOverdue=ÐÐ¾ÑÐ»Ðµ Ð·Ð°Ð´ÐµÑÐ¶ÐºÐ¸ ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ
EventDateEstimation.calculationContext.crossBorderAfterHold=ÐÑÐ¾ÑÑÐ±Ð¾ÑÐ´ÐµÑ. ÐÐ¾ÑÐ»Ðµ Ð¾Ð¿Ð»Ð°ÑÑ

su.reddot.presentation.api.v2.adminpanel.users.UpdateUserRequest.SocialAccount.nicknameRequired=ÐÐ¸ÐºÐ½ÐµÐ¼ Ð°ÐºÐºÐ°ÑÐ½ÑÐ° ÑÐ¾ÑÐ¸Ð°Ð»ÑÐ½Ð¾Ð¹ ÑÐµÑÐ¸ Ð´Ð¾Ð»Ð¶ÐµÐ½ Ð±ÑÑÑ Ð·Ð°Ð¿Ð¾Ð»Ð½ÐµÐ½
su.reddot.presentation.api.v2.adminpanel.users.UpdateUserRequest.SocialAccount.urlRequired=URL Ð°ÐºÐºÐ°ÑÐ½ÑÐ° ÑÐ¾ÑÐ¸Ð°Ð»ÑÐ½Ð¾Ð¹ ÑÐµÑÐ¸ Ð´Ð¾Ð»Ð¶Ð½Ð¾ Ð±ÑÑÑ Ð·Ð°Ð¿Ð¾Ð»Ð½ÐµÐ½Ð¾
su.reddot.presentation.api.v2.adminpanel.users.UpdateUserRequest.SocialAccount.subscribersCountRequired=ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð¿Ð¾Ð´Ð¿Ð¸ÑÑÐ¸ÐºÐ¾Ð² Ð°ÐºÐºÐ°ÑÐ½ÑÐ° ÑÐ¾ÑÐ¸Ð°Ð»ÑÐ½Ð¾Ð¹ ÑÐµÑÐ¸ Ð´Ð¾Ð»Ð¶Ð½Ð¾ Ð±ÑÑÑ Ð·Ð°Ð¿Ð¾Ð»Ð½ÐµÐ½Ð¾
su.reddot.presentation.api.v2.adminpanel.users.UpdateUserRequest.SocialAccount.postsCountRequired=ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð¿Ð¾ÑÑÐ¾Ð² Ð°ÐºÐºÐ°ÑÐ½ÑÐ° ÑÐ¾ÑÐ¸Ð°Ð»ÑÐ½Ð¾Ð¹ ÑÐµÑÐ¸ Ð´Ð¾Ð»Ð¶Ð½Ð¾ Ð±ÑÑÑ Ð·Ð°Ð¿Ð¾Ð»Ð½ÐµÐ½Ð¾
su.reddot.presentation.api.v2.adminpanel.users.UpdateUserRequest.SocialAccount.descriptionRequired=ÐÐ¿Ð¸ÑÐ°Ð½Ð¸Ðµ Ð°ÐºÐºÐ°ÑÐ½ÑÐ° ÑÐ¾ÑÐ¸Ð°Ð»ÑÐ½Ð¾Ð¹ ÑÐµÑÐ¸ Ð´Ð¾Ð»Ð¶Ð½Ð¾ Ð±ÑÑÑ Ð·Ð°Ð¿Ð¾Ð»Ð½ÐµÐ½Ð¾
presentation.api.v2.IntegrationAuthControllerApiV2.authCodeOrIdentityTokenRequired=ÐÐ¾Ð»Ð¶ÐµÐ½ Ð±ÑÑÑ ÑÐºÐ°Ð·Ð°Ð½ Ð·Ð°Ð³Ð¾Ð»Ð¾Ð²Ð¾Ðº Apple-Auth-Code Ð¸Ð»Ð¸ Ð¿Ð°ÑÐ°Ð¼ÐµÑÑ identityToken

service.verification.GenerationPayloadDto.phone.validation.not-null=ÐÐ¾Ð¼ÐµÑ ÑÐµÐ»ÐµÑÐ¾Ð½Ð° Ð½Ðµ Ð´Ð¾Ð»Ð¶ÐµÐ½ Ð±ÑÑÑ Ð¿ÑÑÑÑÐ¼
service.verification.GenerationPayloadDto.operation.validation.not-null=ÐÐ´ÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¾Ñ Ð¾Ð¿ÐµÑÐ°ÑÐ¸Ð¸ Ð½Ðµ Ð´Ð¾Ð»Ð¶ÐµÐ½ Ð±ÑÑÑ Ð¿ÑÑÑÑÐ¼

service.verification.VerificationPayloadDto.phone.validation.not-null=ÐÐ¾Ð¼ÐµÑ ÑÐµÐ»ÐµÑÐ¾Ð½Ð° Ð½Ðµ Ð´Ð¾Ð»Ð¶ÐµÐ½ Ð±ÑÑÑ Ð¿ÑÑÑÑÐ¼
service.verification.VerificationPayloadDto.operation.validation.not-null=ÐÐ´ÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¾Ñ Ð¾Ð¿ÐµÑÐ°ÑÐ¸Ð¸ Ð½Ðµ Ð´Ð¾Ð»Ð¶ÐµÐ½ Ð±ÑÑÑ Ð¿ÑÑÑÑÐ¼
service.verification.VerificationPayloadDto.token.validation.not-null=Ð¢Ð¾ÐºÐµÐ½ Ð¿ÑÐ¾Ð²ÐµÑÐºÐ¸ Ð½Ðµ Ð´Ð¾Ð»Ð¶ÐµÐ½ Ð±ÑÑÑ Ð¿ÑÑÑÑÐ¼
service.verification.VerificationPayloadDto.code.validation.not-null=ÐÐ¾Ð´ Ð¸Ð· ÑÐ¼Ñ Ð½Ðµ Ð´Ð¾Ð»Ð¶ÐµÐ½ Ð±ÑÑÑ Ð¿ÑÑÑÑÐ¼
service.verification.VerificationPayloadDto.code.validation.pattern=ÐÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½ÑÐ¹ ÑÐ¾ÑÐ¼Ð°Ñ ÐºÐ¾Ð´Ð°

BonusesService.timeout.overall=ÐÑÐµÐ²ÑÑÐµÐ½Ð¾ Ð¾Ð±ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð¸Ñ Ð¿ÑÐ¸ ÑÐ°Ð±Ð¾ÑÐµ c Ð±Ð¾Ð½ÑÑÐ°Ð¼Ð¸
BonusesService.timeout.request=ÐÑÐµÐ²ÑÑÐµÐ½Ð¾ Ð²ÑÐµÐ¼Ñ Ð¾Ð¶Ð¸Ð´Ð°Ð½Ð¸Ñ Ð¾ÑÐ²ÐµÑÐ° Ð¾Ñ ÑÐµÑÐ²Ð¸ÑÐ° Ð±Ð¾Ð½ÑÑÐ¾Ð²
BonusesService.error=ÐÑÐ¸Ð±ÐºÐ° Ð¾Ð±ÑÐ°ÑÐµÐ½Ð¸Ñ Ðº ÑÐµÑÐ²Ð¸ÑÑ bonuses
BonusesOfflineService.error=ÐÐµÐ¿ÑÐµÐ´Ð²Ð¸Ð´ÐµÐ½Ð½Ð°Ñ Ð¾ÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ ÑÐ°Ð±Ð¾ÑÐµ ÑÐµÑÐ²Ð¸ÑÐ° BonusesOffline
bonuses.transactions.item.title.welcome.transfer=ÐÐ°ÑÐ¸ÑÐ»ÐµÐ½Ð¸Ðµ Ð¿ÑÐ¸Ð²ÐµÑÑÑÐ²ÐµÐ½Ð½ÑÑ Ð±Ð°Ð»Ð»Ð¾Ð²
bonuses.transactions.item.title.birthday.transfer=ÐÐ°ÑÐ¸ÑÐ»ÐµÐ½Ð¸Ðµ Ð±Ð°Ð»Ð»Ð¾Ð² Ð½Ð° Ð´ÐµÐ½Ñ ÑÐ¾Ð¶Ð´ÐµÐ½Ð¸Ñ
bonuses.transactions.item.title.order.transfer=ÐÐ°ÑÐ¸ÑÐ»ÐµÐ½Ð¸Ðµ Ð±Ð°Ð»Ð»Ð¾Ð² Ð·Ð° Ð·Ð°ÐºÐ°Ð·
bonuses.transactions.item.title.order.return=ÐÐ¾Ð·Ð²ÑÐ°Ñ Ð±Ð°Ð»Ð»Ð¾Ð² Ð·Ð° Ð·Ð°ÐºÐ°Ð·
bonuses.transactions.item.title.order.income.other=ÐÐ°ÑÐ¸ÑÐ»ÐµÐ½Ð¸Ðµ Ð±Ð°Ð»Ð»Ð¾Ð²
bonuses.transactions.item.title.order.income.description=ÐÑ OSKELLYð¤
bonuses.transactions.item.title.order.withdraw=Ð¡Ð¿Ð¸ÑÐ°Ð½Ð¸Ðµ Ð±Ð°Ð»Ð»Ð¾Ð² Ð·Ð° Ð·Ð°ÐºÐ°Ð·
bonuses.transactions.item.title.order.expire=Ð¡Ð³Ð¾ÑÐ°Ð½Ð¸Ðµ Ð±Ð°Ð»Ð»Ð¾Ð²
bonuses.transactions.item.title.order.expire.description=ÐÐ°ÐºÐ¾Ð½ÑÐ¸Ð»ÑÑ ÑÑÐ¾Ðº Ð´ÐµÐ¹ÑÑÐ²Ð¸Ñ
bonuses.transactions.item.title.order.outcome.other=Ð¡Ð¿Ð¸ÑÐ°Ð½Ð¸Ðµ Ð±Ð°Ð»Ð»Ð¾Ð²
bonuses.transactions.item.title.order.outcome.description=ÐÐ´Ð¼Ð¸Ð½Ð¸ÑÑÑÐ°ÑÐ¾ÑÐ¾Ð¼ OSKELLY
bonuses.exception.default=ÐÐµÐ¾Ð¶Ð¸Ð´Ð°Ð½Ð½Ð°Ñ Ð¾ÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ ÑÐ°Ð±Ð¾ÑÐµ Ñ Ð±Ð¾Ð½ÑÑÐ½ÑÐ¼ ÑÐµÑÐ²Ð¸ÑÐ¾Ð¼
bonuses.exception.object-not-found.default=ÐÐµ Ð½Ð°Ð¹Ð´ÐµÐ½ Ð¾Ð±ÑÐµÐºÑ: {0}
bonuses.exception.object-not-found.template=ÐÐµ Ð½Ð°Ð¹Ð´ÐµÐ½ ÑÐ°Ð±Ð»Ð¾Ð½ Ð½Ð°ÑÐ¸ÑÐ»ÐµÐ½Ð¸Ñ Ð±Ð¾Ð½ÑÑÐ¾Ð² Ñ ÐºÐ¾Ð´Ð¾Ð¼: {0}
bonuses.exception.object-not-found.transaction.transaction-id=Ð¢ÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ Ñ Ð¸Ð´: {0} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð°
bonuses.exception.object-not-found.transaction.withdraw-account-and-order=Ð¢ÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ ÑÐ¿Ð¸ÑÐ°Ð½Ð¸Ñ Ð¿Ð¾ ÑÑÐµÑÑ: {0} Ð¸ Ð·Ð°ÐºÐ°Ð·Ñ: {1} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð°
bonuses.exception.object-required.order=ÐÐ¾Ð½ÑÑÐ½Ð°Ñ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ Ñ Ð¸Ð´: {0} Ð¸ orderId = null Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð·Ð°ÐºÐ¾Ð¼Ð¸ÑÐµÐ½Ð° ÑÐ¾Ð»ÑÐºÐ¾ Ñ orderId Ð¾ÑÐ»Ð¸ÑÐ½ÑÐ¼ Ð¾Ñ null
bonuses.exception.object-non-unique.template=ÐÐ°Ð¹Ð´ÐµÐ½Ð¾: {0} ÑÐ°Ð±Ð»Ð¾Ð½Ð¾Ð² Ñ ÐºÐ¾Ð´Ð¾Ð¼: {1}
bonuses.exception.object-non-unique.order=ÐÐ¾Ð½ÑÑÐ½Ð°Ñ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ Ð¿Ð¾ ÑÑÐµÑÑ: {0} Ð¸ Ð·Ð°ÐºÐ°Ð·Ñ: {1} ÑÐ¶Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ
bonuses.exception.object-non-unique.transaction=ÐÐµÐ¾Ð¶Ð¸Ð´Ð°Ð½Ð½Ð°Ñ Ð¾ÑÐ¸Ð±ÐºÐ°. ÐÐ°Ð¹Ð´ÐµÐ½Ð¾ {0} Ð±Ð¾Ð½ÑÑÐ½ÑÑ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ð¹ ÑÐ¿Ð¸ÑÐ°Ð½Ð¸Ñ Ð¿Ð¾ ÑÑÐµÑÑ: {1} and Ð·Ð°ÐºÐ°Ð·Ñ: {2}
bonuses.exception.object-non-unique.transaction.transaction-idempotency=Ð£Ð¶Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ Ð±Ð¾Ð½ÑÑÐ½Ð°Ñ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ Ñ ÑÐ°ÐºÐ¸Ð¼ Ð¶Ðµ ÐºÐ»ÑÑÑÐ¼ Ð¸Ð´ÐµÐ¼Ð¿Ð¾ÑÐµÐ½ÑÐ½Ð¾ÑÑÐ¸: {0}
bonuses.exception.object-must-not-be-null.order=ÐÐ¾Ð½ÑÑÐ½Ð°Ñ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ: {0} Ð´Ð¾Ð»Ð¶Ð½Ð° ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑ Ð¿ÑÑÑÐ¾Ð¹ orderId (null)
bonuses.exception.wrong-state.transaction-has-no-children=ÐÐ¾Ð½ÑÑÐ½Ð°Ñ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ: {0} Ð² ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ð¸ CANCELED, Ð½Ð¾ Ð½Ðµ Ð¸Ð¼ÐµÐµÑ Ð´Ð¾ÑÐµÑÐ½Ð¸Ñ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ð¹
bonuses.exception.wrong-state.transaction-children-size-dif=ÐÐ¾Ð½ÑÑÐ½Ð°Ñ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ: {0} Ð² ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ð¸ CANCELED, Ð½Ð¾ ÐºÐ¾Ð»-Ð²Ð¾ Ð´Ð¾ÑÐµÑÐ½Ð¸Ñ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ð¹ {1} Ð½Ðµ ÑÐ°Ð²Ð½Ð¾ ÐºÐ¾Ð»-Ð²Ñ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ð¹ Ð¿ÑÐ¸ Ð²ÑÐ·Ð¾Ð²Ðµ ÑÐ¿Ð»Ð¸ÑÐ° {2}
bonuses.exception.wrong-state.transaction-child-absent=ÐÐ¾Ð½ÑÑÐ½Ð°Ñ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ: {0} Ð² ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ð¸ CANCELED, Ð½Ð¾ Ð´Ð¾ÑÐµÑÐ½ÑÑ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ: {1} Ð¾ÑÑÑÑÑÑÐ²ÑÐµÑ Ð² Ð¿Ð°ÑÐ°Ð¼ÐµÑÑÐ°Ñ Ð²ÑÐ·Ð¾Ð²Ð° ÑÐ¿Ð»Ð¸ÑÐ°
bonuses.exception.wrong-state.transaction-cancel-bonuses-dif=ÐÐ¾Ð½ÑÑÐ½Ð°Ñ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ: {0} Ð² ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ð¸ CANCELED, Ð½Ð¾ Ð´Ð¾ÑÐµÑÐ½ÑÑ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ: {1} ÑÐ¾Ð´ÐµÑÐ¶Ð¸Ñ ÑÐ³Ð¾ÑÐ°ÐµÐ¼ÑÑ Ð±Ð¾Ð½ÑÑÐ¾Ð²: {2}, ÑÑÐ¾ Ð¾ÑÐ»Ð¸ÑÐ°ÐµÑÑÑ Ð¾Ñ Ð¿Ð°ÑÐ°Ð¼ÐµÑÑÐ° Ð²ÑÐ·Ð¾Ð²Ð° ÑÐ¿Ð»Ð¸ÑÐ° {3}
bonuses.exception.wrong-state.transaction-cancel-money-dif=ÐÐ¾Ð½ÑÑÐ½Ð°Ñ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ: {0} Ð² ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ð¸ CANCELED, Ð½Ð¾ Ð´Ð¾ÑÐµÑÐ½ÑÑ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ: {1} ÑÐ¾Ð´ÐµÑÐ¶Ð¸Ñ ÐÐÑÐ³Ð¾ÑÐ°ÐµÐ¼ÑÑ Ð±Ð¾Ð½ÑÑÐ¾Ð²: {2}, ÑÑÐ¾ Ð¾ÑÐ»Ð¸ÑÐ°ÐµÑÑÑ Ð¾Ñ Ð¿Ð°ÑÐ°Ð¼ÐµÑÑÐ° Ð²ÑÐ·Ð¾Ð²Ð° ÑÐ¿Ð»Ð¸ÑÐ° {3}
bonuses.exception.wrong-state.transaction-already-exists=ÐÐ¾Ð½ÑÑÐ½Ð°Ñ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ ÑÐ¿Ð¸ÑÐ°Ð½Ð¸Ñ Ð´Ð»Ñ ÑÑÐµÑÐ°: {0} Ð¸ Ð·Ð°ÐºÐ°Ð·Ð°: {1} ÑÐ¶Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ
bonuses.exception.wrong-state.transaction-wrong-state=ÐÐ¾Ð½ÑÑÐ½Ð°Ñ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ: {0} Ð½Ð°ÑÐ¾Ð´Ð¸ÑÑÑ Ð² ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ð¸: {1}, Ð½Ð¾ Ð´Ð¾Ð»Ð¶Ð½Ð° Ð±ÑÑÑ Ð² ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ð¸ HOLD
bonuses.exception.wrong-amount.bonuses-amount-more-zero=ÐÐ¾Ð»-Ð²Ð¾ ÑÐ³Ð¾ÑÐ°ÐµÐ¼ÑÑ Ð±Ð¾Ð½ÑÑÐ¾Ð² Ð´Ð¾Ð»Ð¶Ð½Ð¾ Ð±ÑÑÑ Ð±Ð¾Ð»ÑÑÐµ Ð¸ ÑÐ°Ð²Ð½Ð¾ 0.
bonuses.exception.wrong-amount.money-amount-more-zero=ÐÐ¾Ð»-Ð²Ð¾ ÐÐÑÐ³Ð¾ÑÐ°ÐµÐ¼ÑÑ Ð±Ð¾Ð½ÑÑÐ¾Ð² Ð´Ð¾Ð»Ð¶Ð½Ð¾ Ð±ÑÑÑ Ð±Ð¾Ð»ÑÑÐµ Ð¸ ÑÐ°Ð²Ð½Ð¾ 0.
bonuses.exception.wrong-amount.bonuses-or-money-not-null=Ð¡Ð³Ð¾ÑÐ°ÐµÐ¼ÑÐµ Ð¸ ÐÐÑÐ³Ð¾ÑÐ°ÐµÐ¼ÑÐµ Ð±Ð¾Ð½ÑÑÑ 0 (null). Ð¥Ð¾ÑÑ Ð±Ñ Ð¾Ð´Ð½Ð¾ Ð¸Ð· ÑÑÐ¸Ñ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ð¹ Ð´Ð¾Ð»Ð¶Ð½Ð¾ Ð±ÑÑÑ Ð¾ÑÐ»Ð¸ÑÐ½Ð¾ Ð¾Ñ 0.
bonuses.exception.wrong-amount.total-amount-more-zero=ÐÐ±ÑÐ°Ñ ÑÑÐ¼Ð¼Ð° Ð´Ð¾Ð»Ð¶Ð½Ð° Ð±ÑÑÑ Ð±Ð¾Ð»ÑÑÐµ Ð¸Ð»Ð¸ ÑÐ°Ð²Ð½Ð° 0.
bonuses.exception.wrong-amount.bonuses-or-money-or-total-not-null=ÐÑÐµ Ð¿ÐµÑÐµÐ´Ð°Ð½Ð½ÑÐµ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ñ Ð±Ð¾Ð½ÑÑÐ¾Ð² 0 (null). Ð¥Ð¾ÑÑ Ð±Ñ Ð¾Ð´Ð½Ð¾ Ð¸Ð· ÑÑÐ¸Ñ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ð¹ Ð´Ð¾Ð»Ð¶Ð½Ð¾ Ð±ÑÑÑ Ð¾ÑÐ»Ð¸ÑÐ½Ð¾ Ð¾Ñ 0.
bonuses.exception.wrong-amount.total-amount-not-null-other-null=ÐÑÐ»Ð¸ Ð¾Ð±ÑÐµÐµ ÑÐ¸ÑÐ»Ð¾ Ð±Ð¾Ð½ÑÑÐ¾Ð² Ð¾ÑÐ»Ð¸ÑÐ½Ð¾ Ð¾Ñ 0, ÑÐ¾ Ð¾ÑÐ´ÐµÐ»ÑÐ½ÑÐµ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ñ ÑÐ³Ð¾ÑÐ°ÐµÐ¼ÑÑ Ð¸ ÐÐÑÐ³Ð¾ÑÐ°ÐµÐ¼ÑÑ Ð±Ð¾Ð½ÑÑÐ¾Ð² Ð´Ð¾Ð»Ð¶Ð½Ñ Ð±ÑÑÑ 0 Ð¸Ð»Ð¸ null (ÑÐ³Ð¾ÑÐ°ÐµÐ¼ÑÑ Ð±Ð¾Ð½ÑÑÐ¾Ð²: {0}, ÐÐÑÐ³Ð¾ÑÐ°ÐµÐ¼ÑÑ Ð±Ð¾Ð½ÑÑÐ¾Ð²: {1})
bonuses.exception.wrong-amount.withdraw-negative-amount-impossible=Ð¡ÑÐ¼Ð¼Ð° ÑÐ¿Ð¸ÑÐ°Ð½Ð¸Ñ Ð½Ðµ Ð´Ð¾Ð»Ð¶Ð½Ð° Ð±ÑÑÑ Ð¼ÐµÐ½ÑÑÐµ Ð¸Ð»Ð¸ ÑÐ°Ð²Ð½Ð° 0: {0}. Ð¡ÑÐµÑ: {1}, Ð·Ð°ÐºÐ°Ð·: {2}
bonuses.exception.wrong-amount.withdraw-not-enough=ÐÐ¾ÑÑÑÐ¿Ð½ÑÑ Ð±Ð¾Ð½ÑÑÐ¾Ð²: {0} Ð¼ÐµÐ½ÑÑÐµ, ÑÐµÐ¼ Ð·Ð°ÑÐ²Ð»ÐµÐ½Ð¾ Ðº ÑÐ¿Ð¸ÑÐ°Ð½Ð¸Ñ: {1}. Ð¡ÑÐµÑ: {2}, Ð·Ð°ÐºÐ°Ð·: {3}
bonuses.exception.wrong-amount.transaction-total-not-null=ÐÐµÐ»ÑÐ·Ñ ÑÐ°Ð·Ð´ÐµÐ»ÑÑÑ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ: {0} ÑÐºÐ°Ð·ÑÐ²Ð°Ñ Ð¾Ð±ÑÐµÐµ ÐºÐ¾Ð»-Ð²Ð¾ Ð±Ð¾Ð½ÑÑÐ¾Ð² (totalAmount): {1}
bonuses.exception.wrong-amount.bonuses-amount-more-zero-or-null=ÐÐ½Ð°ÑÐµÐ½Ð¸Ðµ ÑÐ³Ð¾ÑÐ°ÐµÐ¼ÑÑ Ð±Ð¾Ð½ÑÑÐ¾Ð² Ð´Ð¾Ð»Ð¶Ð½Ð¾ Ð±ÑÑÑ Ð±Ð¾Ð»ÑÑÐµ 0 Ð¸Ð»Ð¸ Ð±ÑÑÑ null
bonuses.exception.wrong-amount.money-amount-more-zero-or-null=ÐÐ½Ð°ÑÐµÐ½Ð¸Ðµ ÐÐÑÐ³Ð¾ÑÐ°ÐµÐ¼ÑÑ Ð±Ð¾Ð½ÑÑÐ¾Ð² Ð´Ð¾Ð»Ð¶Ð½Ð¾ Ð±ÑÑÑ Ð±Ð¾Ð»ÑÑÐµ 0 Ð¸Ð»Ð¸ Ð±ÑÑÑ null
bonuses.exception.wrong-amount.transactions-bonuses-dif=ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ ÑÐ³Ð¾ÑÐ°ÐµÐ¼ÑÑ Ð±Ð¾Ð½ÑÑÐ¾Ð² Ð² ÑÑÑÐµÑÑÐ²ÑÑÑÐµÐ¹ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ð¸: {0} ({1}) Ð¸ Ð² Ð½Ð¾Ð²ÑÑ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸ÑÑ ({2}) Ð´Ð¾Ð»Ð¶Ð½Ð¾ Ð±ÑÑÑ Ð¾Ð´Ð¸Ð½Ð°ÐºÐ¾Ð²ÑÐ¼
bonuses.exception.wrong-amount.transactions-money-dif=ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ ÐÐÑÐ³Ð¾ÑÐ°ÐµÐ¼ÑÑ Ð±Ð¾Ð½ÑÑÐ¾Ð² Ð² ÑÑÑÐµÑÑÐ²ÑÑÑÐµÐ¹ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ð¸: {0} ({1}) Ð¸ Ð² Ð½Ð¾Ð²ÑÑ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸ÑÑ ({2}) Ð´Ð¾Ð»Ð¶Ð½Ð¾ Ð±ÑÑÑ Ð¾Ð´Ð¸Ð½Ð°ÐºÐ¾Ð²ÑÐ¼
bonuses.exception.wrong-amount.transaction-return-bonuses-wrong=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½Ð¾Ðµ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ ÑÐ³Ð¾ÑÐ°ÐµÐ¼ÑÑ Ð±Ð¾Ð½ÑÑÐ¾Ð²: {0} Ðº Ð²Ð¾Ð·Ð²ÑÐ°ÑÑ Ð´Ð»Ñ ÑÑÐµÑÐ°: {1} Ð¸ Ð·Ð°ÐºÐ°Ð·Ð°: {2} Ð¿Ð¾ ÑÑÑÐµÑÑÐ²ÑÑÑÐµÐ¹ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ð¸ ÑÐ¿Ð¸ÑÐ°Ð½Ð¸Ñ: {3} Ñ ÑÑÐµÑÐ¾Ð¼ ÑÐ¶Ðµ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµÐ½Ð½ÑÑ ÑÐ³Ð¾ÑÐ°ÐµÐ¼ÑÑ Ð±Ð¾Ð½ÑÑÐ¾Ð²: {4}
bonuses.exception.wrong-amount.transaction-return-money-wrong=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½Ð¾Ðµ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ ÐÐÑÐ³Ð¾ÑÐ°ÐµÐ¼ÑÑ Ð±Ð¾Ð½ÑÑÐ¾Ð²: {0} Ðº Ð²Ð¾Ð·Ð²ÑÐ°ÑÑ Ð´Ð»Ñ ÑÑÐµÑÐ°: {1} Ð¸ Ð·Ð°ÐºÐ°Ð·Ð°: {2} Ð¿Ð¾ ÑÑÑÐµÑÑÐ²ÑÑÑÐµÐ¹ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ð¸ ÑÐ¿Ð¸ÑÐ°Ð½Ð¸Ñ: {3} Ñ ÑÑÐµÑÐ¾Ð¼ ÑÐ¶Ðµ Ð²Ð¾Ð·Ð²ÑÐ°ÑÐµÐ½Ð½ÑÑ ÐÐÑÐ³Ð¾ÑÐ°ÐµÐ¼ÑÑ Ð±Ð¾Ð½ÑÑÐ¾Ð²: {4}
bonuses.exception.wrong-amount.transaction-return-unknown=ÐÐµÐ¾Ð¶Ð¸Ð´Ð°Ð½Ð½Ð°Ñ Ð¾ÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ ÑÐ°ÑÑÐµÑÐµ Ð±Ð¾Ð½ÑÑÐ¾Ð² Ðº Ð²Ð¾Ð·Ð²ÑÐ°ÑÑ Ð´Ð»Ñ ÑÑÐµÑÐ°: {0} Ð¸ Ð·Ð°ÐºÐ°Ð·Ð°: {1} Ð¸ Ð¾ÑÑÐ°ÑÐºÐ°: {2}
bonuses.exception.wrong-amount.transaction-bonuses-or-money-not-null=Ð¢ÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ Ð´Ð»Ñ ÑÑÐµÑÐ°: {0} Ð¸ Ð·Ð°ÐºÐ°Ð·Ð°: {1} Ð´Ð¾Ð»Ð¶Ð½Ð° ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑ Ð»Ð¸Ð±Ð¾ ÐºÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ ÑÐ³Ð¾ÑÐ°ÐµÐ¼ÑÑ Ð±Ð¾Ð½ÑÑÐ¾Ð², Ð»Ð¸Ð±Ð¾ ÐºÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ ÐÐÑÐ³Ð¾ÑÐ°ÐµÐ¼ÑÑ Ð±Ð¾Ð½ÑÑÐ¾Ð², Ð»Ð¸Ð±Ð¾ Ð¾Ð±Ð° Ð·Ð½Ð°ÑÐµÐ½Ð¸Ñ
LoyaltyCardsService.timeout.overall=ÐÑÐµÐ²ÑÑÐµÐ½Ð¾ Ð¾Ð±ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð¸Ñ Ð¿ÑÐ¸ ÑÐ°Ð±Ð¾ÑÐµ c ÐºÐ°ÑÑÐ°Ð¼Ð¸ Ð»Ð¾ÑÐ»ÑÐ½Ð¾ÑÑÐ¸
LoyaltyCardsService.timeout.request=ÐÑÐµÐ²ÑÑÐµÐ½Ð¾ Ð²ÑÐµÐ¼Ñ Ð¾Ð¶Ð¸Ð´Ð°Ð½Ð¸Ñ Ð¾ÑÐ²ÐµÑÐ° Ð¾Ñ ÑÐµÑÐ²Ð¸ÑÐ° ÐºÐ°ÑÑ Ð»Ð¾ÑÐ»ÑÐ½Ð¾ÑÑÐ¸
LoyaltyCardsService.error=ÐÑÐ¸Ð±ÐºÐ° Ð¾Ð±ÑÐ°ÑÐµÐ½Ð¸Ñ Ðº ÑÐµÑÐ²Ð¸ÑÑ loyalty-cards
loyaltycards.exception.default=ÐÐµÐ¾Ð¶Ð¸Ð´Ð°Ð½Ð½Ð°Ñ Ð¾ÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ ÑÐ°Ð±Ð¾ÑÐµ Ñ ÑÐµÑÐ²Ð¸ÑÐ¾Ð¼ ÐºÐ°ÑÑ Ð»Ð¾ÑÐ»ÑÐ½Ð¾ÑÑÐ¸
loyaltycards.exception.object-not-found.card=ÐÐµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð° ÐºÐ°ÑÑÐ° Ð¾ÑÐ¼Ð¸ Ð´Ð»Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ: {0}
loyaltycards.exception.object-already-bound.card-already-bound=ÐÐ°ÑÑÐ° Ñ Ð¸Ð´ÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¾ÑÐ¾Ð¼ Ð¾ÑÐ¼Ð¸: {0} ÑÐ¶Ðµ Ð¿ÑÐ¸Ð²ÑÐ·Ð°Ð½Ð° Ðº Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ: {1}. ÐÐ°Ð¿ÑÐ¾ÑÐµÐ½Ð½ÑÐ¹ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ: {2}.
loyaltycards.exception.object-already-bound.card-already-exists=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ ÑÐ¾Ð·Ð´Ð°Ð½Ð¸Ð¸ ÐºÐ°ÑÑÑ Ð»Ð¾ÑÐ»ÑÐ½Ð¾ÑÑÐ¸. ÐÐ°ÑÑÐ° ÑÐ¶Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ. ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ: {0}, ÑÐµÐ»ÐµÑÐ¾Ð½: {1}, Ð¸Ð´ÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¾Ñ ÐºÐ°ÑÑÑ: {2}

LoyaltyService.timeout.overall=ÐÑÐµÐ²ÑÑÐµÐ½Ð¾ Ð¾Ð±ÑÐµÐµ Ð²ÑÐµÐ¼Ñ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð¸Ñ Ð¿ÑÐ¸ ÑÐ°Ð±Ð¾ÑÐµ c ÑÐµÑÐ²Ð¸ÑÐ¾Ð¼ Ð»Ð¾ÑÐ»ÑÐ½Ð¾ÑÑÐ¸
LoyaltyService.timeout.request=ÐÑÐµÐ²ÑÑÐµÐ½Ð¾ Ð²ÑÐµÐ¼Ñ Ð¾Ð¶Ð¸Ð´Ð°Ð½Ð¸Ñ Ð¾ÑÐ²ÐµÑÐ° Ð¾Ñ ÑÐµÑÐ²Ð¸ÑÐ° Ð»Ð¾ÑÐ»ÑÐ½Ð¾ÑÑÐ¸
LoyaltyService.error=ÐÑÐ¸Ð±ÐºÐ° Ð¾Ð±ÑÐ°ÑÐµÐ½Ð¸Ñ Ðº ÑÐµÑÐ²Ð¸ÑÑ Ð»Ð¾ÑÐ»ÑÐ½Ð¾ÑÑÐ¸
loyalty.status.WHITE=White
loyalty.status.SILVER=Silver
loyalty.status.BLACK=Black
loyalty.exception.default=ÐÐµÐ¾Ð¶Ð¸Ð´Ð°Ð½Ð½Ð°Ñ Ð¾ÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ ÑÐ°Ð±Ð¾ÑÐµ Ñ ÑÐµÑÐ²Ð¸ÑÐ¾Ð¼ Ð»Ð¾ÑÐ»ÑÐ½Ð¾ÑÑÐ¸
loyalty.exception.object-not-found.default=ÐÐµ Ð½Ð°Ð¹Ð´ÐµÐ½ Ð¾Ð±ÑÐµÐºÑ: {0}
loyalty.history.reason-code.initial=ÐÑÐ¸ÑÐ²Ð¾ÐµÐ½ Ð¿ÑÐ¸ Ð²ÑÑÑÐ¿Ð»ÐµÐ½Ð¸Ð¸ Ð² ÐÑÐ¾Ð³ÑÐ°Ð¼Ð¼Ñ ÐÐ¾ÑÐ»ÑÐ½Ð¾ÑÑÐ¸
loyalty.history.reason-code.recalc=ÐÑÐ¸ÑÐ²Ð¾ÐµÐ½ Ð¿ÑÐ¸ Ð¿ÐµÑÐµÑÑÐµÑÐµ
loyalty.exception.validation-error.status-required=ÐÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾ Ð·Ð°Ð´Ð°ÑÑ ÑÑÐ°ÑÑÑ
loyalty.exception.validation-error.reason-required=ÐÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾ ÑÐºÐ°Ð·Ð°ÑÑ Ð¿ÑÐ¸ÑÐ¸Ð½Ñ Ð½Ð°Ð·Ð½Ð°ÑÐµÐ½Ð¸Ñ ÑÑÐ°ÑÑÑÐ°
loyalty.exception.validation-error.expires-at-must-be-after-now-or-equal=ÐÐ°ÑÐ° Ð¾ÐºÐ¾Ð½ÑÐ°Ð½Ð¸Ñ Ð´ÐµÐ¹ÑÑÐ²Ð¸Ñ ÑÑÐ°ÑÑÑÐ° Ð½Ðµ Ð´Ð¾Ð»Ð¶Ð½Ð° Ð±ÑÑÑ Ð¼ÐµÐ½ÑÑÐµ ÑÐµÐºÑÑÐµÐ¹ Ð´Ð°ÑÑ
loyalty.exception.validation-error.account-banned=ÐÐºÐºÐ°ÑÐ½Ñ Ð·Ð°Ð±Ð°Ð½ÐµÐ½. ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ ÑÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ ÑÑÐ°ÑÑÑ.
loyalty.exception.validation-error.status-lower-auto=Ð£ÑÑÐ°Ð½Ð°Ð²Ð»Ð¸Ð²Ð°ÐµÐ¼ÑÐ¹ ÑÑÐ°ÑÑÑ Ð½Ðµ Ð´Ð¾Ð»Ð¶ÐµÐ½ Ð±ÑÑÑ Ð¼ÐµÐ½ÑÑÐµ ÑÐ°ÑÑÑÑÐ½Ð¾Ð³Ð¾ ÑÑÐ°ÑÑÑÐ°


CommunityService.timeout=ÐÑÐµÐ²ÑÑÐµÐ½Ð¾ Ð²ÑÐµÐ¼Ñ Ð¾Ð¶Ð¸Ð´Ð°Ð½Ð¸Ñ Ð¾ÑÐ²ÐµÑÐ° Ð¾Ñ ÑÐµÑÐ²Ð¸ÑÐ° community
CommunityService.error=ÐÑÐ¸Ð±ÐºÐ° Ð¾Ð±ÑÐ°ÑÐµÐ½Ð¸Ñ Ðº ÑÐµÑÐ²Ð¸ÑÑ community
community.badge-layout.status.not-acquired.title=Ð¡ÑÐ°Ð½ÑÑÐµ ÑÐ°ÑÑÑÑ O!Community
community.badge-layout.status.not-acquired.description=O!Community â ÑÑÐ¾ Ð¼Ð¾Ð´Ð½Ð¾Ðµ ÑÐ¾Ð¾Ð±ÑÐµÑÑÐ²Ð¾ Ñ ÑÐ½Ð¸ÐºÐ°Ð»ÑÐ½ÑÐ¼Ð¸ ÑÑÐ°ÑÑÑÐ°Ð¼Ð¸ Ð¸ ÑÐºÑÐºÐ»ÑÐ·Ð¸Ð²Ð½ÑÐ¼Ð¸ Ð±Ð¾Ð½ÑÑÐ°Ð¼Ð¸. <a>Ð Ð¿ÑÐ¸Ð²Ð¸Ð»ÐµÐ³Ð¸ÑÑ</a>
community.badge-layout.status.actual.title=ÐÑ â ÑÐ°ÑÑÑ O!Community
community.badge-layout.status.actual.max-rank.description=Ð£ Ð²Ð°Ñ ÑÐ°Ð¼ÑÐ¹ Ð²ÑÑÐ¾ÐºÐ¸Ð¹ ÑÑÐ°ÑÑÑ Ð¸ Ð²Ð°Ð¼ Ð´Ð¾ÑÑÑÐ¿Ð½Ñ Ð²ÑÐµ Ð¿ÑÐ¸Ð²Ð¸Ð»ÐµÐ³Ð¸Ð¸. <a>ÐÐ¾Ð¸ Ð¿ÑÐ¸Ð²Ð¸Ð»ÐµÐ³Ð¸Ð¸</a>
community.badge-layout.status.actual.not-max-rank.description=ÐÐ¾ ÑÐ»ÐµÐ´ÑÑÑÐµÐ³Ð¾ ÑÑÐ°ÑÑÑÐ° {0}. <a>ÐÐ¾Ð¸ Ð¿ÑÐ¸Ð²Ð¸Ð»ÐµÐ³Ð¸Ð¸</a>
community.badge-layout.requirements.words.sale=Ð¿Ð¾ÐºÑÐ¿Ð¾Ðº:Ð¿Ð¾ÐºÑÐ¿ÐºÐ°:Ð¿Ð¾ÐºÑÐ¿ÐºÐ¸
community.badge-layout.requirements.words.purchase=Ð¿ÑÐ¾Ð´Ð°Ð¶:Ð¿ÑÐ¾Ð´Ð°Ð¶Ð°:Ð¿ÑÐ¾Ð´Ð°Ð¶Ð¸
community.badge-layout.requirements.words.or=Ð¸Ð»Ð¸
community.badge-layout.requirements.words.and=Ð¸
community.badge-layout.status.may-be-lost.words.purchase=ÐºÑÐ¿Ð¸ÑÐµ
community.badge-layout.status.may-be-lost.words.sale=Ð¿ÑÐ¾Ð´Ð°Ð¹ÑÐµ
community.badge-layout.status.may-be-lost.words.platform=Ð½Ð° Ð¿Ð»Ð°ÑÑÐ¾ÑÐ¼Ðµ
community.badge-layout.status.may-be-lost.words.else=Ð¸Ð»Ð¸
community.badge-layout.status.may-be-lost.words.goods=ÑÐ¾Ð²Ð°Ñ:ÑÐ¾Ð²Ð°ÑÐ°:ÑÐ¾Ð²Ð°ÑÐ¾Ð²
community.badge-layout.status.may-be-lost.words.months=ÑÐ½Ð²Ð°ÑÑ:ÑÐµÐ²ÑÐ°Ð»Ñ:Ð¼Ð°ÑÑÐ°:Ð°Ð¿ÑÐµÐ»Ñ:Ð¼Ð°Ñ:Ð¸ÑÐ½Ñ:Ð¸ÑÐ»Ñ:Ð°Ð²Ð³ÑÑÑÐ°:ÑÐµÐ½ÑÑÐ±ÑÑ:Ð¾ÐºÑÑÐ±ÑÑ:Ð½Ð¾ÑÐ±ÑÑ:Ð´ÐµÐºÐ°Ð±ÑÑ
community.badge-layout.status.may-be-lost.title=ÐÑ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¿Ð¾ÑÐµÑÑÑÑ ÑÑÐ°ÑÑÑ Ð² O!Community
community.badge-layout.status.may-be-lost.description=Ð¡Ð¾ÑÑÐ°Ð½Ð¸ÑÐµ Ð¿ÑÐ¸Ð²Ð¸Ð»ÐµÐ³Ð¸Ð¸ â Ð´Ð¾ {0}. <a>ÐÐ¾Ð´ÑÐ¾Ð±Ð½ÐµÐµ</a>
community.badge-layout.status.lost.title=Oops! ÐÑ Ð±Ð¾Ð»ÑÑÐµ Ð½Ðµ ÑÑÐ°ÑÑÐ½Ð¸Ðº O!Community
community.badge-layout.status.lost.description=Ð ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ, Ð²Ñ Ð½Ðµ Ð²ÑÐ¿Ð¾Ð»Ð½Ð¸Ð»Ð¸ Ð¿ÑÐ°Ð²Ð¸Ð»Ð° Ð¿ÑÐ¾Ð³ÑÐ°Ð¼Ð¼Ñ Ð»Ð¾ÑÐ»ÑÐ½Ð¾ÑÑÐ¸. <a>ÐÐ¾Ð´ÑÐ¾Ð±Ð½ÐµÐµ</a>
community.notification.status.may-be-lost.words.purchases=Ð¿Ð¾ÐºÑÐ¿ÐºÑ:Ð¿Ð¾ÐºÑÐ¿ÐºÐ¸:Ð¿Ð¾ÐºÑÐ¿Ð¾Ðº
community.notification.status.may-be-lost.words.sales=Ð¿ÑÐ¾Ð´Ð°Ð¶Ñ:Ð¿ÑÐ¾Ð´Ð°Ð¶Ð¸:Ð¿ÑÐ¾Ð´Ð°Ð¶
community.history.status.words.purchases=Ð¿Ð¾ÐºÑÐ¿ÐºÐ°:Ð¿Ð¾ÐºÑÐ¿ÐºÐ¸:Ð¿Ð¾ÐºÑÐ¿Ð¾Ðº
community.history.status.words.sales=Ð¿ÑÐ¾Ð´Ð°Ð¶Ð°:Ð¿ÑÐ¾Ð´Ð°Ð¶Ð¸:Ð¿ÑÐ¾Ð´Ð°Ð¶
community.history.username-who-assigned.auto=Ð°Ð²ÑÐ¾Ð¼Ð°ÑÐ¸ÑÐµÑÐºÐ¸
community.history.username-who-assigned.unknown=Ð½ÐµÐ¸Ð·Ð²ÐµÑÑÐ½ÑÐ¹ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ
community.status-get.error=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ Ð¾Ð¿ÑÐµÐ´ÐµÐ»Ð¸ÑÑ ÑÑÐ°ÑÑÑ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ {0}, ÑÐ°Ðº ÐºÐ°Ðº Ñ Ð½ÐµÐ³Ð¾ {1} Ð¿Ð¾Ð¸ÑÐºÐ¾Ð²ÑÑ ÑÑÐ³Ð°
community.status-extra-get.error=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð¾Ð¿ÑÐµÐ´ÐµÐ»ÐµÐ½Ð¸Ð¸ ÑÐ°Ð½Ð³Ð° ÑÑÐ°ÑÑÑÐ° {0}
community.status.add-to-cart-low-status.error=Ð¡ÐµÐ¹ÑÐ°Ñ ÑÐ¾Ð²Ð°Ñ Ð´Ð¾ÑÑÑÐ¿ÐµÐ½ ÑÐ¾Ð»ÑÐºÐ¾ ÑÑÐ°ÑÑÐ½Ð¸ÐºÐ°Ð¼ O!Community ÑÐ¾ ÑÑÐ°ÑÑÑÐ¾Ð¼ {0} Ð¸ Ð²ÑÑÐµ. Ð¡Ð¾ÑÑÐ°Ð½Ð¸ÑÐµ ÐµÐ³Ð¾ Ð² Ð¸Ð·Ð±ÑÐ°Ð½Ð½Ð¾Ðµ, Ð¸ Ð¼Ñ Ð½Ð°Ð¿Ð¸ÑÐµÐ¼, ÐºÐ¾Ð³Ð´Ð° ÐµÐ³Ð¾ Ð¼Ð¾Ð¶Ð½Ð¾ Ð±ÑÐ´ÐµÑ ÐºÑÐ¿Ð¸ÑÑ.
community.promocode.generation.oskelly.text=Ð¡Ð³ÐµÐ½ÐµÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð½ÑÐ¹ Ð½Ð° Ð´ÐµÐ½Ñ ÑÐ¾Ð¶Ð´ÐµÐ½Ð¸Ñ oskelly Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´ Ð´Ð»Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ {0}
community.promocode.generation.concierge.text=Ð¡Ð³ÐµÐ½ÐµÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð½ÑÐ¹ Ð½Ð° Ð´ÐµÐ½Ñ ÑÐ¾Ð¶Ð´ÐµÐ½Ð¸Ñ consierge Ð¿ÑÐ¾Ð¼Ð¾ÐºÐ¾Ð´ Ð´Ð»Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ {0}

search.tabs.names=PRODUCTS_Ð¢Ð¾Ð²Ð°ÑÑ:BRANDS_ÐÑÐµÐ½Ð´Ñ:USERS_ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ð¸:POSTS_ÐÐ¾ÑÑÑ:HASHTAGS_Ð¥ÑÑÑÐµÐ³Ð¸

service.concierge.BitrixOrderProcessor.toManyOrders=ÐÐ±ÑÐ°ÑÐ¸ÑÐµ Ð²Ð½Ð¸Ð¼Ð°Ð½Ð¸Ðµ, ÑÑÐ¾ ÑÐ¾Ð²Ð°Ñ Ð½Ðµ ÑÑÑÑÐ½ÑÐ¹, Ð¿Ð¾ÑÑÐ¾Ð¼Ñ ÑÐ´ÐµÐ»ÐºÐ° Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð¿ÐµÑÐµÐ¹ÑÐ¸ Ð² ÑÑÐ°ÑÑÑ "ÐÐ¿Ð»Ð°ÑÐµÐ½Ð½ÑÐµ Ð·Ð°ÐºÐ°Ð·Ñ" Ð°Ð²ÑÐ¾Ð¼Ð°ÑÐ¸ÑÐµÑÐºÐ¸ Ð¸ Ð² ÑÐ´ÐµÐ»ÐºÐµ Ð½ÐµÑ ÑÐ²ÑÐ·Ð¸ Ñ Ð·Ð°ÐºÐ°Ð·Ð¾Ð¼ Ð¸Ð· Ð°Ð´Ð¼Ð¸Ð½ÐºÐ¸ OSKELLY

CatalogService.timeout=ÐÑÐµÐ²ÑÑÐµÐ½Ð¾ Ð²ÑÐµÐ¼Ñ Ð¾Ð¶Ð¸Ð´Ð°Ð½Ð¸Ñ Ð¾ÑÐ²ÐµÑÐ° Ð¾Ñ ÑÐµÑÐ²Ð¸ÑÐ° catalog
CatalogService.error=ÐÑÐ¸Ð±ÐºÐ° Ð¾Ð±ÑÐ°ÑÐµÐ½Ð¸Ñ Ðº ÑÐµÑÐ²Ð¸ÑÑ catalog

order.duty.VAT_ON_PRICE=Customs duty 5%
order.duty.VAT_ON_PRICE_WITH_VAT=VAT on CIF
order.duty.DELIVERY_COMPANY_FEE=Customs reg. Fee

osocial.media.uploading.exception.unexpected=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð·Ð°Ð³ÑÑÐ·ÐºÐµ Ð¼ÐµÐ´Ð¸Ð°
osocial.media.uploading.exception.max-file-size-exceeded=ÐÑÐµÐ²ÑÑÐµÐ½ Ð¼Ð°ÐºÑÐ¸Ð¼Ð°Ð»ÑÐ½ÑÐ¹ ÑÐ°Ð·Ð¼ÐµÑ ÑÐ°Ð¹Ð»Ð°
osocial.post.creating.exception.user-cannot-be-mentioned-in-post=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ {0} Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¾ÑÐ¼ÐµÑÐµÐ½ Ð² Ð¿Ð¾ÑÑÐµ
osocial.post.creating.exception.product-cannot-be-contained-in-post=ÐÑÐ¾Ð´ÑÐºÑ {0} Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑÑÑ Ð² Ð¿Ð¾ÑÑÐµ
osocial.post.creating.exception.invalid-tagged-user-id-in-post=ÐÑÐ¸Ð±ÐºÐ° ÑÐ¾ÑÐ¼Ð°ÑÐ° ÑÐ¿Ð¾Ð¼ÑÐ½ÑÑÐ¾Ð³Ð¾ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ
osocial.post.creating.exception.user-cannot-be-tagged-in-post=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ {0} Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ ÑÐ¿Ð¾Ð¼ÑÐ½ÑÑ Ð² Ð¿Ð¾ÑÑÐµ
osocial.post.creating.ban.error=ÐÑ Ð·Ð°Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ñ Ð¸ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¾Ð¿ÑÐ±Ð»Ð¸ÐºÐ¾Ð²Ð°ÑÑ Ð¿Ð¾ÑÑ
osocial.post.updating.ban.error=ÐÑ Ð·Ð°Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ñ Ð¸ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ ÑÐµÐ´Ð°ÐºÑÐ¸ÑÐ¾Ð²Ð°ÑÑ Ð¿Ð¾ÑÑ
osocial.comment.creating.ban.error=ÐÑ Ð·Ð°Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ñ Ð¸ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¾ÑÑÐ°Ð²Ð¸ÑÑ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹
osocial.comment.creating.exception.invalid-tagged-user-id=ÐÑÐ¸Ð±ÐºÐ° ÑÐ¾ÑÐ¼Ð°ÑÐ° ÑÐ¿Ð¾Ð¼ÑÐ½ÑÑÐ¾Ð³Ð¾ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ
osocial.comment.creating.exception.user-cannot-be-tagged=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ {0} Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ ÑÐ¿Ð¾Ð¼ÑÐ½ÑÑ Ð² ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¸
osocial.feed-posts-collection.creating.user-cannot-be-contained-in-collection=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ {0} Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑÑÑ Ð² Ð¿Ð¾Ð»ÐºÐµ
osocial.method.timeout=ÐÑÐµÐ²ÑÑÐµÐ½Ð¾ Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼Ð¾Ðµ Ð²ÑÐµÐ¼Ñ ÑÐ°Ð±Ð¾ÑÑ social Ð¼ÐµÑÐ¾Ð´Ð°
osocial.request.timeout=ÐÑÐµÐ²ÑÑÐµÐ½Ð¾ Ð²ÑÐµÐ¼Ñ Ð·Ð°Ð¿ÑÐ¾ÑÐ° Ðº social API
osocial.media.similar-products.exception.unexpected=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð¿Ð¾Ð»ÑÑÐµÐ½Ð¸Ð¸ Ð¿Ð¾ÑÐ¾Ð¶Ð¸Ñ Ð¿ÑÐ¾Ð´ÑÐºÑÐ¾Ð²

osocial.post.rating.explanation.part.boosting=ÐÑÑÑÐ¸Ð½Ð³
osocial.post.rating.explanation.part.publishedAt=ÐÐ¾ÑÑÑÐ¸ÑÐ¸ÐµÐ½Ñ Ð½Ð¾Ð²Ð¸Ð·Ð½Ñ
osocial.post.rating.explanation.part.views=ÐÑÐ¾ÑÐ¼Ð¾ÑÑÑ
osocial.post.rating.explanation.part.likes=ÐÐ°Ð¹ÐºÐ¸
osocial.post.rating.explanation.part.comments=ÐÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¸
osocial.post.rating.explanation.part.engagement=ÐÐ¾ÑÑÑÐ¸ÑÐ¸ÐµÐ½Ñ Ð²Ð¾Ð²Ð»ÐµÑÐµÐ½Ð¸Ñ
osocial.post.rating.explanation.part.author=ÐÐ²ÑÐ¾Ñ
osocial.post.rating.explanation.part.following=ÐÐ¾Ð´Ð¿Ð¸ÑÐºÐ¸

osocial.exception.default=ÐÐ½ÑÑÑÐµÐ½Ð½ÑÑ Ð¾ÑÐ¸Ð±ÐºÐ° ÑÐµÑÐ²ÐµÑÐ°
osocial.exception.object-not-found.default=ÐÐ±ÑÐµÐºÑ {0} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
osocial.exception.object-not-found.post=ÐÐ¾ÑÑ {0} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
osocial.exception.object-not-found.comment=ÐÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹ {0} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
osocial.exception.object-not-found.media=ÐÐµÐ´Ð¸Ð° {0} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð¾
osocial.exception.object-not-found.tag=Ð¢ÐµÐ³ {0} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
osocial.exception.object-not-found.configured_feed_section=Ð¡ÐµÐºÑÐ¸Ñ {0} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð°
osocial.exception.object-not-found.feed_section=Ð¡ÐµÐºÑÐ¸Ñ {0} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð°
osocial.exception.object-not-found.feed_objects_collection=ÐÐ¾Ð»Ð»ÐµÐºÑÐ¸Ñ Ð¾Ð±ÑÐµÐºÑÐ¾Ð² {0} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð°
osocial.exception.object-not-found.reaction_type=Ð¢Ð¸Ð¿ ÑÐµÐ°ÐºÑÐ¸Ð¸ {0} Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
osocial.exception.bad-request=ÐÐµÑÐµÐ´Ð°Ð½Ñ Ð½ÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐµ Ð¿Ð°ÑÐ°Ð¼ÐµÑÑÑ Ð·Ð°Ð¿ÑÐ¾ÑÐ°
osocial.exception.validation-error=ÐÐµÑÐµÐ´Ð°Ð½Ñ Ð½ÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐµ Ð¿Ð°ÑÐ°Ð¼ÐµÑÑÑ Ð·Ð°Ð¿ÑÐ¾ÑÐ°
osocial.exception.state-flow-broken=ÐÐµÑÐµÐ´Ð°Ð½Ñ Ð½ÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐµ Ð¿Ð°ÑÐ°Ð¼ÐµÑÑÑ Ð·Ð°Ð¿ÑÐ¾ÑÐ°

adminPanel.order.documents.documentGroup.generated=Ð¤Ð°Ð¹Ð»Ñ Ð¿Ð¾ Ð·Ð°ÐºÐ°Ð·Ñ
adminPanel.order.documents.documentGroup.invoice=ÐÐ½Ð²Ð¾Ð¹ÑÑ Ð¿Ð¾ Ð·Ð°ÐºÐ°Ð·Ñ
adminPanel.order.documents.documentGroup.btqContract=ÐÐ¾Ð³Ð¾Ð²Ð¾Ñ Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð¼
adminPanel.order.documents.documentGroup.generated.add.buttonName=ÐÐ¾Ð±Ð°Ð²Ð¸ÑÑ ÑÐ°Ð¹Ð»
adminPanel.order.documents.documentGroup.invoice.add.buttonName=ÐÐ¾Ð±Ð°Ð²Ð¸ÑÑ Ð¸Ð½Ð²Ð¾Ð¹Ñ
adminPanel.order.documents.documentGroup.btqContract.add.buttonName=ÐÐ¾Ð±Ð°Ð²Ð¸ÑÑ Ð´Ð¾Ð³Ð¾Ð²Ð¾Ñ Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð¼

adminPanel.order.documents.documentGroup.invoice.alert.invoice-not-found=ÐÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾ Ð´Ð¾Ð±Ð°Ð²Ð¸ÑÑ Ð¸Ð½Ð²Ð¾Ð¹Ñ
