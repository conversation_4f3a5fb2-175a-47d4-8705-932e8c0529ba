openapi: 3.0.1
info:
  title: OpenAPI definition
  version: v0
servers:
  - description: Generated server url
    url: http://localhost:8085
paths:
  /api/v1/requests:
    post:
      operationId: createRequest
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSaleRequestDTO'
        required: true
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResponseBodySaleRequestDTO'
          description: OK
        "400":
          content:
            '*/*':
              schema:
                type: object
          description: Bad Request
        "404":
          content:
            '*/*':
              schema:
                type: object
          description: Not Found
      tags:
        - sale-request-controller-v-1
  /api/v1/requests/items:
    post:
      operationId: filterSaleRequests
      parameters:
        - in: query
          name: page
          required: false
          schema:
            type: integer
            format: int32
        - in: query
          name: rowsPerPage
          required: false
          schema:
            type: integer
            format: int32
        - in: query
          name: sortBy
          required: false
          schema:
            type: string
        - in: query
          name: descending
          required: false
          schema:
            type: boolean
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SaleRequestFilter'
        required: true
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResponseBodyPageDTOSaleRequestDTO'
          description: OK
        "400":
          content:
            '*/*':
              schema:
                type: object
          description: Bad Request
        "404":
          content:
            '*/*':
              schema:
                type: object
          description: Not Found
      tags:
        - sale-request-controller-v-1
  /api/v1/requests/items/count:
    post:
      operationId: getSaleRequestsCount
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SaleRequestFilter'
        required: true
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResponseBodyLong'
          description: OK
        "400":
          content:
            '*/*':
              schema:
                type: object
          description: Bad Request
        "404":
          content:
            '*/*':
              schema:
                type: object
          description: Not Found
      tags:
        - sale-request-controller-v-1
  /api/v1/requests/products/brands/available:
    get:
      operationId: getAvailableProductBrandIds
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResponseBodyListLong'
          description: OK
        "400":
          content:
            '*/*':
              schema:
                type: object
          description: Bad Request
        "404":
          content:
            '*/*':
              schema:
                type: object
          description: Not Found
      tags:
        - sale-request-controller-v-1
  /api/v1/requests/products/categories/available:
    get:
      operationId: getAvailableProductCategoryIds
      parameters:
        - in: query
          name: brandId
          required: true
          schema:
            type: integer
            format: int64
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResponseBodyListLong'
          description: OK
        "400":
          content:
            '*/*':
              schema:
                type: object
          description: Bad Request
        "404":
          content:
            '*/*':
              schema:
                type: object
          description: Not Found
      tags:
        - sale-request-controller-v-1
  /api/v1/requests/products/conditions/available:
    get:
      operationId: getAvailableProductConditionIds
      parameters:
        - in: query
          name: brandId
          required: true
          schema:
            type: integer
            format: int64
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResponseBodyListLong'
          description: OK
        "400":
          content:
            '*/*':
              schema:
                type: object
          description: Bad Request
        "404":
          content:
            '*/*':
              schema:
                type: object
          description: Not Found
      tags:
        - sale-request-controller-v-1
  /api/v1/requests/{saleRequestId}:
    get:
      operationId: getSaleRequestById
      parameters:
        - in: path
          name: saleRequestId
          required: true
          schema:
            type: integer
            format: int64
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResponseBodySaleRequestDTO'
          description: OK
        "400":
          content:
            '*/*':
              schema:
                type: object
          description: Bad Request
        "404":
          content:
            '*/*':
              schema:
                type: object
          description: Not Found
      tags:
        - sale-request-controller-v-1
    patch:
      operationId: patchRequest
      parameters:
        - in: path
          name: saleRequestId
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchSaleRequestDTO'
        required: true
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResponseBodySaleRequestDTO'
          description: OK
        "400":
          content:
            '*/*':
              schema:
                type: object
          description: Bad Request
        "404":
          content:
            '*/*':
              schema:
                type: object
          description: Not Found
      tags:
        - sale-request-controller-v-1
  /api/v1/requests/{saleRequestId}/bitrix-request-errors:
    post:
      operationId: createBitrixRequestError
      parameters:
        - in: path
          name: saleRequestId
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BitrixRequestErrorDTO'
        required: true
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResponseBodyBitrixRequestErrorDTO'
          description: OK
        "400":
          content:
            '*/*':
              schema:
                type: object
          description: Bad Request
        "404":
          content:
            '*/*':
              schema:
                type: object
          description: Not Found
      tags:
        - sale-request-controller-v-1
  /api/v1/requests/{saleRequestId}/comments:
    get:
      operationId: getRequestComments
      parameters:
        - in: path
          name: saleRequestId
          required: true
          schema:
            type: integer
            format: int64
        - in: query
          name: page
          required: false
          schema:
            type: integer
            format: int32
        - in: query
          name: rowsPerPage
          required: false
          schema:
            type: integer
            format: int32
        - in: query
          name: sortBy
          required: false
          schema:
            type: string
        - in: query
          name: descending
          required: false
          schema:
            type: boolean
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResponseBodyPageDTOSaleRequestCommentDTO'
          description: OK
        "400":
          content:
            '*/*':
              schema:
                type: object
          description: Bad Request
        "404":
          content:
            '*/*':
              schema:
                type: object
          description: Not Found
      tags:
        - sale-request-controller-v-1
    post:
      operationId: createRequestComment
      parameters:
        - in: path
          name: saleRequestId
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSaleRequestCommentDTO'
        required: true
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResponseBodySaleRequestCommentDTO'
          description: OK
        "400":
          content:
            '*/*':
              schema:
                type: object
          description: Bad Request
        "404":
          content:
            '*/*':
              schema:
                type: object
          description: Not Found
      tags:
        - sale-request-controller-v-1
  /api/v1/requests/{saleRequestId}/comments/{commentId}:
    delete:
      operationId: deleteRequestComment
      parameters:
        - in: path
          name: saleRequestId
          required: true
          schema:
            type: integer
            format: int64
        - in: path
          name: commentId
          required: true
          schema:
            type: integer
            format: int64
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResponseBodyString'
          description: OK
        "400":
          content:
            '*/*':
              schema:
                type: object
          description: Bad Request
        "404":
          content:
            '*/*':
              schema:
                type: object
          description: Not Found
      tags:
        - sale-request-controller-v-1
    get:
      operationId: getRequestComment
      parameters:
        - in: path
          name: saleRequestId
          required: true
          schema:
            type: integer
            format: int64
        - in: path
          name: commentId
          required: true
          schema:
            type: integer
            format: int64
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResponseBodySaleRequestCommentDTO'
          description: OK
        "400":
          content:
            '*/*':
              schema:
                type: object
          description: Bad Request
        "404":
          content:
            '*/*':
              schema:
                type: object
          description: Not Found
      tags:
        - sale-request-controller-v-1
    patch:
      operationId: patchRequestComment
      parameters:
        - in: path
          name: saleRequestId
          required: true
          schema:
            type: integer
            format: int64
        - in: path
          name: commentId
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchSaleRequestCommentDTO'
        required: true
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResponseBodySaleRequestCommentDTO'
          description: OK
        "400":
          content:
            '*/*':
              schema:
                type: object
          description: Bad Request
        "404":
          content:
            '*/*':
              schema:
                type: object
          description: Not Found
      tags:
        - sale-request-controller-v-1
  /api/v1/requests/{saleRequestId}/history:
    get:
      operationId: getSaleRequestHistory
      parameters:
        - in: path
          name: saleRequestId
          required: true
          schema:
            type: integer
            format: int64
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResponseBodyListSaleRequestHistoryItemDTO'
          description: OK
        "400":
          content:
            '*/*':
              schema:
                type: object
          description: Bad Request
        "404":
          content:
            '*/*':
              schema:
                type: object
          description: Not Found
      tags:
        - sale-request-controller-v-1
  /api/v1/requests/{saleRequestId}/products:
    post:
      operationId: createRequestProduct
      parameters:
        - in: path
          name: saleRequestId
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSaleRequestProductDTO'
        required: true
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResponseBodySaleRequestProductDTO'
          description: OK
        "400":
          content:
            '*/*':
              schema:
                type: object
          description: Bad Request
        "404":
          content:
            '*/*':
              schema:
                type: object
          description: Not Found
      tags:
        - sale-request-controller-v-1
  /api/v1/requests/{saleRequestId}/products/{productId}:
    get:
      operationId: getRequestProduct
      parameters:
        - in: path
          name: saleRequestId
          required: true
          schema:
            type: integer
            format: int64
        - in: path
          name: productId
          required: true
          schema:
            type: integer
            format: int64
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResponseBodySaleRequestProductDTO'
          description: OK
        "400":
          content:
            '*/*':
              schema:
                type: object
          description: Bad Request
        "404":
          content:
            '*/*':
              schema:
                type: object
          description: Not Found
      tags:
        - sale-request-controller-v-1
    patch:
      operationId: patchRequestProduct
      parameters:
        - in: path
          name: saleRequestId
          required: true
          schema:
            type: integer
            format: int64
        - in: path
          name: productId
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchSaleRequestProductDTO'
        required: true
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResponseBodySaleRequestProductDTO'
          description: OK
        "400":
          content:
            '*/*':
              schema:
                type: object
          description: Bad Request
        "404":
          content:
            '*/*':
              schema:
                type: object
          description: Not Found
      tags:
        - sale-request-controller-v-1
  /api/v1/requests/{saleRequestId}/validate-for-order-creation:
    get:
      operationId: validateForOrderCreation
      parameters:
        - in: path
          name: saleRequestId
          required: true
          schema:
            type: integer
            format: int64
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResponseBodySaleRequestDTO'
          description: OK
        "400":
          content:
            '*/*':
              schema:
                type: object
          description: Bad Request
        "404":
          content:
            '*/*':
              schema:
                type: object
          description: Not Found
      tags:
        - sale-request-controller-v-1
  /api/v1/requests/statistic/countGroupedByUser:
    get:
      operationId: getSaleRequestsCountGroupedByUser
      responses:
        '200':
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResponseBodyListRequestCount'
          description: OK
        '400':
          content:
            '*/*':
              schema:
                type: object
          description: Bad Request
        '404':
          content:
            '*/*':
              schema:
                type: object
          description: Not Found
      tags:
        - sale-request-controller-v-1
components:
  schemas:
    ResponseBodyListRequestCount:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/RequestCount'
        errorMessage:
          type: string
          description: Error message
        humanErrorMessage:
          type: string
          description: Human readable error message
    RequestCount:
      required:
        - count
        - userId
      type: object
      properties:
        count:
          type: integer
          format: int32
        userId:
          type: integer
          format: int64
    BitrixRequestErrorDTO:
      type: object
      properties:
        description:
          type: string
          description: Description
        parameters:
          type: string
          description: Parameters
        saleRequestId:
          type: integer
          format: int64
          description: Sale request id
          example: 10
        time:
          type: string
          format: date-time
          description: Time of the request
      required:
        - saleRequestId
        - time
    CreateSaleRequestCommentDTO:
      type: object
      properties:
        text:
          type: string
          description: Text
        userId:
          type: integer
          format: int64
          description: Comment author user id
          example: 10
      required:
        - text
        - userId
    CreateSaleRequestDTO:
      type: object
      properties:
        bitrixDealId:
          type: integer
          format: int64
          description: Bitrix sale request deal id
          example: 10
        contactName:
          type: string
          description: Contact name of the request
          example: Ivan Ivanov
        creatorUserId:
          type: integer
          format: int64
          description: Sale request creator user id
          example: 10
        description:
          type: string
          description: Posted request description
          example: A lot of items
        images:
          type: array
          description: List of images
          items:
            type: string
            description: List of images
        itemsNumber:
          type: integer
          format: int32
          description: Requested number of items
          example: 5
        ownerUserId:
          type: integer
          format: int64
          description: Sale request owner user id
          example: 10
        phone:
          type: string
          description: Contact phone of the request
          example: "+79999123123"
        products:
          type: array
          description: List of products
          items:
            $ref: '#/components/schemas/CreateSaleRequestProductDTO'
        source:
          type: string
          description: Source of the request
          example: CHAT
      required:
        - contactName
        - creatorUserId
        - itemsNumber
        - ownerUserId
        - phone
        - source
    CreateSaleRequestProductDTO:
      type: object
      description: List of products
      properties:
        brandId:
          type: integer
          format: int64
          description: Product brand id
          example: 1000
        categoryId:
          type: integer
          format: int64
          description: Product category id
          example: 3
        conditionId:
          type: integer
          format: int64
          description: Product condition id
          example: 1
        fromPrice:
          type: number
          description: Minimum of product price
          example: 20000
        image:
          type: string
          description: Product image
          example: c3388725-d14c-4a74-8dbc-16a3f66a7d77.jpg
        productId:
          type: integer
          format: int64
          description: Core product id
          example: 1239990
        toPrice:
          type: number
          description: Maximum of product price
          example: 40000
      required:
        - brandId
        - categoryId
        - conditionId
        - image
        - productId
    PageDTOSaleRequestCommentDTO:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/SaleRequestCommentDTO'
        totalAmount:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32
      required:
        - items
        - totalAmount
        - totalPages
    PageDTOSaleRequestDTO:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/SaleRequestDTO'
        totalAmount:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32
      required:
        - items
        - totalAmount
        - totalPages
    PatchSaleRequestCommentDTO:
      type: object
      properties:
        pinned:
          type: boolean
          description: Pinned flag
    PatchSaleRequestDTO:
      type: object
      properties:
        bitrixDealId:
          type: integer
          format: int64
          description: Bitrix deal id
          nullable: true
        bitrixLastSuccessfulRequestTime:
          type: string
          format: date-time
          description: Bitrix last request time
          nullable: true
        contactName:
          type: string
          description: Contact name of the request
          example: Ivan Ivanov
        description:
          type: string
          description: Posted request description
          example: A lot of items
        images:
          type: array
          description: List of images
          items:
            type: string
            description: List of images
            nullable: true
          nullable: true
        itemsNumber:
          type: integer
          format: int32
          description: Requested number of items
          example: 5
        orderId:
          type: integer
          format: int64
          description: Sale request shipment order id
          example: 1000000
        ownerUserId:
          type: integer
          format: int64
          description: Sale request owner user id
          example: 10
        phone:
          type: string
          description: Contact phone of the request
          example: "+79999123123"
        source:
          type: string
          description: Source of the request
          example: CHAT
        state:
          $ref: '#/components/schemas/SaleRequestState'
        updaterUserId:
          type: integer
          format: int64
          description: Sale request updater user id
          example: 10
      required:
        - updaterUserId
    PatchSaleRequestProductDTO:
      type: object
      properties:
        brandId:
          type: integer
          format: int64
          description: Product brand id
          example: 1000
        categoryId:
          type: integer
          format: int64
          description: Product category id
          example: 3
        conditionId:
          type: integer
          format: int64
          description: Product condition id
          example: 1
        declineReason:
          type: string
          description: Product decline reason
          example: Плохое состояние
        fromPrice:
          type: number
          description: Minimum of product price
          example: 20000
        image:
          type: string
          description: Product image
          example: c3388725-d14c-4a74-8dbc-16a3f66a7d77.jpg
        state:
          $ref: '#/components/schemas/ProductState'
        toPrice:
          type: number
          description: Maximum of product price
          example: 40000
    ProductState:
      type: string
      description: Product state
      enum:
        - CREATED
        - CONFIRMED
        - DECLINED
      example: CREATED
    ResponseBodyBitrixRequestErrorDTO:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/BitrixRequestErrorDTO'
        errorMessage:
          type: string
          description: Error message
        humanErrorMessage:
          type: string
          description: Human readable error message
    ResponseBodyListLong:
      type: object
      properties:
        data:
          type: array
          items:
            type: integer
            format: int64
        errorMessage:
          type: string
          description: Error message
        humanErrorMessage:
          type: string
          description: Human readable error message
    ResponseBodyListSaleRequestHistoryItemDTO:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/SaleRequestHistoryItemDTO'
        errorMessage:
          type: string
          description: Error message
        humanErrorMessage:
          type: string
          description: Human readable error message
    ResponseBodyLong:
      type: object
      properties:
        data:
          type: integer
          format: int64
        errorMessage:
          type: string
          description: Error message
        humanErrorMessage:
          type: string
          description: Human readable error message
    ResponseBodyPageDTOSaleRequestCommentDTO:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/PageDTOSaleRequestCommentDTO'
        errorMessage:
          type: string
          description: Error message
        humanErrorMessage:
          type: string
          description: Human readable error message
    ResponseBodyPageDTOSaleRequestDTO:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/PageDTOSaleRequestDTO'
        errorMessage:
          type: string
          description: Error message
        humanErrorMessage:
          type: string
          description: Human readable error message
    ResponseBodySaleRequestCommentDTO:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/SaleRequestCommentDTO'
        errorMessage:
          type: string
          description: Error message
        humanErrorMessage:
          type: string
          description: Human readable error message
    ResponseBodySaleRequestDTO:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/SaleRequestDTO'
        errorMessage:
          type: string
          description: Error message
        humanErrorMessage:
          type: string
          description: Human readable error message
    ResponseBodySaleRequestProductDTO:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/SaleRequestProductDTO'
        errorMessage:
          type: string
          description: Error message
        humanErrorMessage:
          type: string
          description: Human readable error message
    ResponseBodyString:
      type: object
      properties:
        data:
          type: string
        errorMessage:
          type: string
          description: Error message
        humanErrorMessage:
          type: string
          description: Human readable error message
    SaleRequestCommentDTO:
      type: object
      properties:
        createTime:
          type: string
          format: date-time
          description: Creation time
        id:
          type: integer
          format: int64
          description: Id
          example: 10
        pinned:
          type: boolean
          description: Pinned flag
        text:
          type: string
          description: Text
        userId:
          type: integer
          format: int64
          description: Author user id
          example: 10
      required:
        - createTime
        - id
        - pinned
        - text
        - userId
    SaleRequestDTO:
      type: object
      properties:
        bitrixDealId:
          type: integer
          format: int64
          description: Bitrix sale request deal id
          example: 10
        contactName:
          type: string
          description: Contact name of the request
          example: Ivan Ivanov
        createTime:
          type: string
          format: date-time
          description: Sale request creation time
        creatorUserId:
          type: integer
          format: int64
          description: Sale request creator user id
          example: 10
        description:
          type: string
          description: Posted request description
          example: A lot of items
        id:
          type: integer
          format: int64
          description: Sale request id
          example: 10
        images:
          type: array
          description: List of images
          items:
            type: string
            description: List of images
        itemsNumber:
          type: integer
          format: int32
          description: Requested number of items
          example: 5
        orderId:
          type: integer
          format: int64
          description: Sale request shipment order id
          example: 1000000
        ownerUserId:
          type: integer
          format: int64
          description: Sale request owner user id
          example: 10
        phone:
          type: string
          description: Contact phone of the request
          example: "+79999123123"
        products:
          type: array
          description: List of products to sale
          items:
            $ref: '#/components/schemas/SaleRequestProductDTO'
        source:
          type: string
          description: Source of the request
          example: CHAT
        state:
          $ref: '#/components/schemas/SaleRequestState'
        stateInfo:
          $ref: '#/components/schemas/SaleRequestStateDTO'
        updateTime:
          type: string
          format: date-time
          description: Sale request updating time
        updaterUserId:
          type: integer
          format: int64
          description: Sale request updater user id
          example: 10
      required:
        - contactName
        - createTime
        - creatorUserId
        - id
        - images
        - itemsNumber
        - ownerUserId
        - phone
        - products
        - source
        - state
        - stateInfo
        - updateTime
        - updaterUserId
    SaleRequestFilter:
      type: object
      properties:
        bitrixDealIds:
          type: array
          items:
            type: integer
            format: int64
        idContains:
          type: string
        ownerUserIds:
          type: array
          items:
            type: integer
            format: int64
        states:
          type: array
          items:
            $ref: '#/components/schemas/SaleRequestState'
    SaleRequestHistoryItemDTO:
      type: object
      properties:
        enteredByUserId:
          type: integer
          format: int64
          description: "User id, who entered into state, null if auto"
          example: 10
        enteredTime:
          type: string
          format: date-time
          description: Date of enter into state
        id:
          type: integer
          format: int64
          description: History item id
          example: 10
        state:
          $ref: '#/components/schemas/SaleRequestState'
        stateInfo:
          $ref: '#/components/schemas/SaleRequestStateDTO'
      required:
        - enteredTime
        - id
        - state
        - stateInfo
    SaleRequestProductDTO:
      type: object
      description: List of products to sale
      properties:
        brandId:
          type: integer
          format: int64
          description: Product brand id
          example: 1000
        categoryId:
          type: integer
          format: int64
          description: Product category id
          example: 3
        conditionId:
          type: integer
          format: int64
          description: Product condition id
          example: 1
        createTime:
          type: string
          format: date-time
          description: Sale request product creation time
        declineReason:
          type: string
          description: Product decline reason
          example: Плохое состояние
        fromPrice:
          type: number
          description: Minimum of product price
          example: 20000
        id:
          type: integer
          format: int64
          description: Product id
          example: 10
        image:
          type: string
          description: Product image
          example: c3388725-d14c-4a74-8dbc-16a3f66a7d77.jpg
        productId:
          type: integer
          format: int64
          description: Core product id
          example: 1239990
        state:
          $ref: '#/components/schemas/ProductState'
        stateInfo:
          $ref: '#/components/schemas/SaleRequestProductStateDTO'
        toPrice:
          type: number
          description: Maximum of product price
          example: 40000
      required:
        - brandId
        - categoryId
        - conditionId
        - createTime
        - id
        - image
        - productId
        - state
        - stateInfo
    SaleRequestProductStateDTO:
      type: object
      description: Product state info
      properties:
        code:
          type: string
        title:
          type: string
      required:
        - code
        - title
    SaleRequestState:
      type: string
      description: Sale request state
      enum:
        - CREATED
        - IN_PROGRESS
        - CONFIRMED
        - DECLINED
      example: CREATED
    SaleRequestStateDTO:
      type: object
      description: Sale request state info
      properties:
        code:
          type: string
        description:
          type: string
        title:
          type: string
      required:
        - code
        - description
        - title