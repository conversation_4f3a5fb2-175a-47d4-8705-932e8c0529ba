openapi: 3.0.1
info:
  title: OpenAPI definition
  version: v0
servers:
  - description: Generated server url
    url: http://localhost:8081
paths:
  /api/v1/filter-subscriptions:
    delete:
      operationId: delete
      parameters:
        - in: query
          name: userId
          required: true
          schema:
            type: integer
            format: int64
        - in: query
          name: subscriptionId
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: OK
      tags:
        - filter-subscriptions-controller
    post:
      operationId: saveNew
      parameters:
        - in: query
          name: userId
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateFilterSubscriptionRequest'
        required: true
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/BaseApiResponseBodyFilterSubscriptionRestDto'
          description: OK
      tags:
        - filter-subscriptions-controller
    put:
      operationId: update
      parameters:
        - in: query
          name: userId
          required: true
          schema:
            type: integer
            format: int64
        - in: query
          name: subscriptionId
          required: true
          schema:
            type: string
            format: uuid
        - in: query
          name: setName
          required: false
          schema:
            type: string
        - in: query
          name: setNotificationsEnabled
          required: false
          schema:
            type: boolean
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/BaseApiResponseBodyFilterSubscriptionRestDto'
          description: OK
      tags:
        - filter-subscriptions-controller
  /api/v1/filter-subscriptions/all-for-user:
    get:
      operationId: findAllSubscriptionsForUser
      parameters:
        - in: query
          name: userId
          required: true
          schema:
            type: integer
            format: int64
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/BaseApiResponseBodyListFilterSubscriptionRestDto'
          description: OK
      tags:
        - filter-subscriptions-controller
  /api/v1/filter-subscriptions/clear-new-matching-products:
    post:
      operationId: clearNewMatchingProducts
      parameters:
        - in: query
          name: userId
          required: true
          schema:
            type: integer
            format: int64
        - in: query
          name: subscriptionIds
          required: true
          schema:
            type: array
            items:
              type: string
              format: uuid
      responses:
        "200":
          description: OK
      tags:
        - filter-subscriptions-controller
  /api/v1/filter-subscriptions/single-name:
    get:
      operationId: getSubscriptionName
      parameters:
        - in: query
          name: userId
          required: true
          schema:
            type: integer
            format: int64
        - in: query
          name: subscriptionId
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/BaseApiResponseBodyString'
          description: OK
      tags:
        - filter-subscriptions-controller
  /api/v1/filter-subscriptions/support/notification/new-product/send-batch:
    get:
      operationId: startSendingNewProductsBatchEvents
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/BaseApiResponseBodyString'
          description: OK
      tags:
        - filter-subscriptions-support-controller
  /api/v1/filter-subscriptions/support/sync-schema:
    post:
      operationId: syncSchema
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FilterSubscriptionsSchemaSyncCommand'
        required: true
      responses:
        "200":
          description: OK
      tags:
        - filter-subscriptions-support-controller
  /api/v1/filter-subscriptions/support/sync-schema/trigger:
    post:
      operationId: triggerSyncSchema
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/BaseApiResponseBodyString'
          description: OK
      tags:
        - filter-subscriptions-support-controller
components:
  schemas:
    BaseApiResponseBodyFilterSubscriptionRestDto:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/FilterSubscriptionRestDto'
        errorMessage:
          type: string
          description: Error message
    BaseApiResponseBodyListFilterSubscriptionRestDto:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/FilterSubscriptionRestDto'
        errorMessage:
          type: string
          description: Error message
    BaseApiResponseBodyString:
      type: object
      properties:
        data:
          type: string
        errorMessage:
          type: string
          description: Error message
    CreateFilterSubscriptionRequest:
      type: object
      properties:
        catalogRequest:
          type: FilterSubscriptionCatalogRequest
        criteriaSize:
          type: integer
          format: int32
        description:
          type: string
        name:
          type: string
        notificationsEnabled:
          type: boolean
        originalFeRequest:
          type: FilterSubscriptionOriginalFeRequest
      required:
        - catalogRequest
        - criteriaSize
        - name
        - notificationsEnabled
        - originalFeRequest
        - userId
    FilterSubscriptionRestDto:
      type: object
      properties:
        description:
          type: string
        id:
          type: string
          format: uuid
        name:
          type: string
        newMatchingProductsCount:
          type: integer
          format: int32
        notificationsEnabled:
          type: boolean
        originalFeRequest:
          type: FilterSubscriptionOriginalFeRequest
        userId:
          type: integer
          format: int64
      required:
        - id
        - name
        - newMatchingProductsCount
        - notificationsEnabled
        - originalFeRequest
        - userId
    FilterSubscriptionsSchemaSyncCommand:
      type: object
      properties:
        catalogRequestsBySubscriptionId:
          type: object
          additionalProperties:
            type: FilterSubscriptionCatalogRequest
      required:
        - catalogRequestsBySubscriptionId