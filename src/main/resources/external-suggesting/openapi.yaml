openapi: 3.0.1
info:
  title: OpenAPI definition
  version: v0
servers:
  - url: http://localhost:8080
    description: Generated server url
paths:
  /search/history:
    post:
      tags:
        - search-history-controller
      operationId: saveSearchHistory
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SaveSearchHistoryRequestDTO'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: string

  /search/history/byGuestToken:
    get:
      tags:
        - search-history-controller
      operationId: getSearchHistoryByGuestToken
      parameters:
        - name: guestToken
          in: query
          required: true
          schema:
            type: string
        - name: limit
          in: query
          required: false
          schema:
            type: integer
            format: int32
        - name: baseCategory
          in: query
          required: false
          schema:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/HistoryDTO'

  /search/history/byUserId:
    get:
      tags:
        - search-history-controller
      operationId: getSearchHistoryByUserId
      parameters:
        - name: userId
          in: query
          required: true
          schema:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/HistoryDTO'

  /suggest:
    get:
      tags:
        - suggesting-controller
      operationId: getSuggestions
      parameters:
        - name: userQuery
          in: query
          required: true
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  type: string
components:
  schemas:
    HistoryDTO:
      type: object
      properties:
        search_value:
          type: string
        category_id:
          type: integer
          format: int64
    SaveSearchHistoryRequestDTO:
      type: object
      properties:
        userGuestToken:
          type: string
        userQuery:
          type: string
        userQueryHash:
          type: string
        userQuerySource:
          type: string
          enum:
            - SUGGESTION
            - USER_INPUT
        suggestionNumber:
          type: integer
          format: int32
        usedAt:
          type: string
          format: date-time
        userId:
          type: integer
          format: int64
        baseCategory:
          type: integer
          format: int64
