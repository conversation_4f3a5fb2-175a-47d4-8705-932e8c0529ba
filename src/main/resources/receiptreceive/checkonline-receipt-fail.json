{"ClientId": "oskelly.ru", "Date": {"Date": {"Day": 0, "Month": 0, "Year": 0}, "Time": {"Hour": 0, "Minute": 0, "Second": 0}}, "Device": {"Name": "00000003820046280614", "Address": "***********:4444"}, "DeviceRegistrationNumber": "0001377633048987", "DeviceSerialNumber": "00000003820046280614", "DocumentType": 0, "FNSerialNumber": "9252440300117029", "Path": "/fr/api/v2/Complex", "RequestId": "1202184-ae689668-33be-444c-9fb0-2ed6413b3064", "Response": {"Error": 69, "ErrorMessages": ["ERROR: Сумма всех оплат меньше итога чека (ERR_PAY_LESS_TOTAL)"]}, "Responses": [{"Path": "/fr/api/v2/CloseDocument", "Response": {"CorrectionType": 0, "Date": {"Date": {"Day": 14, "Month": 10, "Year": 21}, "Time": {"Hour": 14, "Minute": 19, "Second": 9}}, "DocumentType": 0, "Error": 69, "ErrorMessages": ["ERROR: Сумма всех оплат меньше итога чека (ERR_PAY_LESS_TOTAL)"], "GrandTotal": 1535000, "NonCash": [1385000, 0, 0], "Password": 30, "PaymentAgentModes": 0, "Place": "OSKELLY.RU", "Reason": null, "TaxCalculationMethod": 1, "TaxMode": 0, "TurnNumber": 609}}]}