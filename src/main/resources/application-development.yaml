bank-account:
  payment-version: tcb-1.0 # noonpayments-1.0
  hold-expires-in-seconds: 30
  jobs:
    enabled: false # true
    check-bank-operations:
#      cron: "0 0/1 * * * ?"
    prepare-seller-payouts:
#      cron: "0 0/1 * * * ?"
    check-seller-payouts:
#      cron: "0 0/1 * * * ?"

  tcb:
    #proxyParams: "167.235.243.206;3128;oskelly;fg67DFGE7jwaas1"
  tcb2:
    #proxyParams: "167.235.243.206;3128;oskelly;fg67DFGE7jwaas2"
  tcb3: # Oskelly-Payout
    api-endpoint: https://paytest.online.tkbbank.ru/api/v1
    api-business-endpoint: https://paytest.online.tkbbank.ru/api/interfaces/business
    login: T2873101412ID
    signing-key: zBeOj2mGi0PEMS0uDU68pAbwXt17UqCJ0ESM6vgHBaQKhmNMPj5263K7zS0ZanV2ygOgaowhOyGpUBoMQqtLuJL53TmpErHopWkaHFMAWXpHwvn7DoiU3kHZERmqbhNw0ta6qoekO1XGtdJJFoUrhXueovEZtvyOj1QdvqSpi7gbwbFBhNTXwwpd84T4So1sCiwAwFnqFFhjRsRmYdSPLLrXNNezGXF3fXHnPfeMzDs0oevQoGfPFmQ11HWSAWTR
    account-number: 00000000000000000000
    return-url: http://localhost:8080

payments:
  payments-service:
#    jobcron: "0 0/1 * * * ?"
    useAmqp: true
  boutique:
    enabled: true
  checkout-com:
    enabled: false
  noon-payments:
    enabled: true
    api-url: https://api-test.noonpayments.com/payment/v1
    currencies:
      - "AED"
      - "EUR"
      - "USD"

payouts:
  boutique:
    api-endpoint: https://paytest.online.tkbbank.ru/api/v1
    api-business-endpoint: https://paytest.online.tkbbank.ru/api/interfaces/business
    login: T3657102618ID
    signing-key: hdS2TYO4LYvvAYPdb7djJhXPCbS2Ts1HZaFg24h04rcxwp4cn4h8akLapczRfVkt6XEAtJjf0mpseztogPQXj1GPyF0ZFzVjfTAJTETOVciY4t4ZTmLzewjeqZbXRuKBAfmv7UEL5ccgTSTfKbfuYveSKws2C55ACRxZgK08Q8DctcvHg5nQoqNeaDUMBMi8fLOcu27ajgNY1byHaUVGSpnndtYNYoHe0D5Sxtnn7nFgMXn8enMRZLq0xB7bdvk5
    account-number: PAT100002179ID # login on https://paytest.online.tkbbank.ru/ with password 'TzCpLsPv'

boutique:
  boutique-buyers-ids: 12 # using <EMAIL> as boutique buyer in tests (better switch to new backup with real ID)
  boutique-buyer-id-for-automation: 12 # Used as a buyer in automated Boutique order creation
  office-address-endpoint-id: 1584 # address_endpoint.id used as seller address in Boutique orders

#logging:
  #level:
    #org.hibernate.SQL: DEBUG
    #su.reddot.infrastructure.chat.usedesk: DEBUG
    #org.springframework.security: TRACE
    #org.springframework.web.client: DEBUG

app:
  stories:
    client:
      host: localhost:8083

  cache:
    storage-type: local # тип хранения кэша - local или redis
    life-period-seconds: 300

  maxmind:
    db-file-path: src/main/resources/maxmind/GeoLite2-Country.mmdb

spring:
  data:
    mongodb:
      uri: *****************************************************************************

s3:
  accessKey: YCAJEuV9_xO8_dSVdi5J0Ep_w
  secretKey: YCMHzSzVvaxiJHw7SbLhVQ5kXhUK87x6avjZTvxG
  bucket:
    static: deremushkin-test-bucket
    feed: deremushkin-test-bucket
    images: deremushkin-test-bucket
    logistics: deremushkin-test-bucket

application-feature-flags:
  enable-new-home: true
  enable-android-new-home: true

order-events:
  cron: "-"
