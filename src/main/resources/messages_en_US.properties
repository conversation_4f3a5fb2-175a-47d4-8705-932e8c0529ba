service.DutyService.DutyDescription=Import Customs and Duties
bargain.expired.error=The auction is not overdue and cannot be resumed
service.PrimaryServiceImpl.getProductRequestSmallBanner.Title=Wanted
service.PrimaryServiceImpl.getProductRequestSmallBanner.Description=Haven't found the Wanted item? Create an ad and all our sellers will see it and will offer you options.
service.PrimaryServiceImpl.getProductRequestSmallBanner.ButtonTitle=Create an ad
service.PrimaryServiceImpl.getProductRequestSmallBannerOnPrimary.Title=Wanted
service.PrimaryServiceImpl.getProductRequestSmallBannerOnPrimary.Description=Place a request for the item you are searching for to our community or browse other users desirable items and offer them your lots
service.PrimaryServiceImpl.getProductRequestSmallBannerOnPrimary.ButtonTitle=Create an ad
service.PrimaryServiceImpl.ProductRequestBanner.Title=Wanted
service.PrimaryServiceImpl.ProductRequestBanner.Description=Place a request for the item you are searching for to our community or browse other users desirable items and offer them your lots
service.PrimaryServiceImpl.ProductRequestBanner.ButtonTitle=View Wanted Listings
presentation.api.v2.concierge.ConciergeController.NoName=Name not entered
presentation.api.v2.concierge.ConciergeController.NoPhone=Phone number not entered
infrastructure.security.SecurityServiceImpl.Exception.UserNotFound=User not found. Id: 
infrastructure.security.SecurityServiceImpl.Exception.BadToken=Invalid authorization confirmation token
infrastructure.security.provider.register.OAuthRegistrationProvider.NoEmailException=Please provide an email for registration
bargain.previous.state.error=Failed to identify the previous status of the counter-offer
bargain.is.user.product.error=You cannot make Price offers for your item
bargain.product.size.not.available.error=The size is not available for purchase
bargain.exhausted.action.error=You have exhausted all Price offer attempts for this item/size
bargain.product.not.available.error=Item is not available for Price offers\: {0}
bargain.product.in.boutique.error=The product is in a boutique, so it is not available for auction
bargain.access.error=You do not have permission to perform this action
bargain.add.product.basket.error=To mark the purchase, add the product to the cart and place an order
bargain.price.unacceptable.error=Price setting is invalid for this action
bargain.price.mandatory.error=Price setting is required for this action
bargain.buyer.cannot.use.countertrade.error=The buyer cannot use this counter-offer
bargain.min.price.error=The price cannot be lower than {0}% of the value of the item
bargain.max.price.error=The price cannot exceed the value of the item
bargain.day.error=To make a Price offer, {0} days must have passed since the publication of the item
bargain.seller.disabled.error=Price offer is not supported on this seller's items


stream.can.create.error=You cannot create Stream Sale. To get the permission to start Stream Sale, you need to publish {0} items and make {1} successful sales on the OSKELLY platform
stream.create.ban.error=You are banned and cannot create Stream Sale
stream.update.ban.error=You are banned and cannot update the Stream Sale
stream.update.access.error=You do not have the right to edit this Stream Sale
stream.can.update.error=You cannot update this Stream Sale
stream.delete.ban.error=You are banned and cannot delete Stream Sale
stream.delete.access.error=You do not have the right to delete this Stream Sale
stream.can.delete.error=You are not allowed to delete this Stream Sale
stream.live.ban.error=You are banned and cannot continue Stream Sale
stream.get.deleting.logic.error=This Stream Sale has been deleted
stream.start.ban.error=You are banned and cannot start Stream Sale
stream.start.access.error=You do not have the right to start this Stream Sale
stream.can.start.error=You can't start this Stream Sale
stream.finish.ban.error=You are banned and cannot end the Stream Sale
stream.finish.access.error=You do not have the right to end this Stream Sale
stream.subscribe.error=You have already subscribed to the Stream Sale
stream.unsubscribe.error=You have already unsubscribed Stream Sale
stream.statuses.logic.error=StreamStatuses field cannot be empty
stream.statuses.ban.logic.error=The streamStatuses field cannot contain the BAN type
stream.can.create.title=Congratulations, now you can start Stream sale
stream.can.create.description=You have reached {0} published items and {1} successful sales, and now you can start Stream Sale on our platform
stream.can.not.create.title=You cannot start Stream Sale yet
stream.can.not.create.description=To get the permission to start Stream Sale, you need to publish {0} items and make {1} successful sales on the OSKELLY platform
stream.cover.validation=You should add a cover for the Stream Sale
stream.title.validation=You should add a name for the Stream Sale
stream.description.validation=You should add a description for the Stream Sale
stream.min.product.count.validation=You should add at least 1 item
stream.max.product.count.validation=You can''t add more than {0} items in one Stream Sale
stream.start.time.validation=You cannot set the start date for the Stream Sale earlier than the current date
stream.start.min.time.validation=The time before the start of the postponed Stream Sale should be more than 1 hour
stream.start.max.time.validation=The time before the start of the postponed Stream Sale cannot be more than 30 days
stream.is.your.product.validation=You cannot add item - {0} {1} to Stream Sale as it is not in your list of published items
stream.product.state.validation=You cannot add item - {0} {1} to Stream Sale, as it is not published

exception.product-request.not-found=Product request not found
exception.product-request.bad-request=Not filled mandatory field
exception.product-request.create.category-id-not-found=Not filled mandatory field - Category
exception.product-request.create.not-authorized=Only author can edit product requests
exception.product-request.create.description-id-not-found=Not filled mandatory field - Description
exception.product-request.no-answer-from-product-request-service=Error in product request service
exception.product-request.does-not-belong-to-user=Request does not belong to user
exception.product-request.new-incorrect-state=Wrong state for the product request
exception.bargain.not-found=Counter-offer not found
exception.bargain.not-found-id=Counter-offer not found\: {0}
exception.bargain.small-price=Minimum counter-offer price is\: {0}. You offered\: {1}
exception.bargain.access-denied=You do not have access rights to this counter-offer
exception.bargain.need-update-application=To use Price offers function, update the application to the latest version
exception.bonuses.boutique-user=The user cannot participate in the loyalty program because he is a boutique user
exception.bonuses.loyalty-cards.phone-required=Phone number is required to register loyalty card for user {0}
exception.bonuses.transaction.not-found=Bonuses transaction: {0} for user with barcode: {1} is not found
exception.bonuses.transaction.must-belong-to-user=Bonuses transaction: {0} is not belong to the user: {1} with barcode: {2}
exception.bonuses.transaction.wrong-state=Bonuses transaction: {0} of the user: {1} and barcode: {2} must be in HOLD state
exception.brand.not-found=Brand not found\: {0}.
exception.bubble-filler.not-found=The information enricher for the category was not found
exception.category.not-found=Category not found\: {0}
exception.residues.not-enough=Not enough remaining items. Required\: {0}. Available\: {1}
exception.residues.unavailable=There are unavailable options ({0})
exception.order.promocode-expired=Order {0}: promocode expired ({1}) 
exception.order.promocode-discount-cannot-applied-to-seller=Discount is not available for the goods of this seller
exception.order.promocode-discount-cannot-applied-to-brand=Discount is not available for the goods of this brand
exception.order.promocode-discount-cannot-applied-to-category=Discount is not available for the goods of this category
exception.order.promocode-discount-cannot-applied-to-product=Discount is not available for some of these products
exception.order.promocode-discount-cannot-applied=Discount cannot be applied to this order
exception.order.promocode-discount-cannot-cancel-in-proceed=You cannot cancel a discount for an order that has already been paid or is in the process of payment
exception.order.promocode-already-used=Discount has already been used on another order (\# {0})
exception.order.request-not-found=Unable to set shipment and delivery addresses. Order not found
exception.order.response-not-found=Unable to set delivery information. Order not found
exception.order.response-information-not-full=Unable to set delivery information. Not all required information provided
exception.order.counteragent-order-not-found=It is impossible to identify the buyer's counterparty. Order not found
exception.order.delete-bucket-empty=Unable to delete an item. The Shopping bag is empty.
exception.order.position-not-found=Item {0} not found in order {1}
exception.order.point-not-set=Delivery location is not set
exception.order.point-delivery-incorrect=Delivery location filled incorrectly
exception.order.seller-not-specified=Seller not specified
exception.order.delivery-fail-capture-in-progress=Order {0}\: unable to call delivery, capture is still in progress
exception.order.delivery-o2b-state-fail-no-payment=Order {0}\: unable to change delivery state, payment is not completed
exception.order.delivery-s2o-state-fail-need-payout=Order {0}\: unable to change delivery state, waiting payout 
exception.order.incorrect-status=Order {0}\: illegal order state {1}
exception.order.empty=Empty order
exception.order.not-found=Order not found\: {0}
exception.order.file.not-found=File {0} not found in order {1}
exception.order.file.type.not-found=Order file type not found\: {0}
exception.order.file.size.limit=Order {0} file size limit {1} mb exceeded. Upload size is {2} mb
exception.order.file.name=File with the same name {0} in order {1} already exists
exception.order.file.upload=Fail to upload file {0} to storage in order {1}
exception.order.file.remove=Fail to remove file {0} from storage in order {1}
exception.order.file.obtain=Fail to obtain file {0} from storage in order {1}
exception.order.empty-promocode-error=You can't apply a discount to an empty order
exception.order.incorrect-point-request=Shipment address not specified
exception.order.delete-error=Unable to delete the order\: {0}, please try again later
exception.order.cancel-error-not-all-confirm=Unable to confirm/cancel the order, because not all items in the order are confirmed/cancelled.
exception.order.delete-alien=It is not possible to delete someone's order
exception.order.pay-order-not-found=Unable to initiate payment. Order not found\: \#{0}
exception.order.scheme-not-supported=Scheme {0} is not supported for order processing
exception.order.not-suitable-for-placing-receipt=Order {0} is not suitable for issuing a receipt
exception.order.sync-buyer-check-dto-empty=List buyerCheckDTOs to sync is empty
exception.order.list-order-check-empty=The list of orders for punching checks is empty
exception.order.check-has-error=Order\: {0}, order receipt contains errors, shipping is not possible
exception.order.delivery-point-not-found=Unable to set delivery information. Delivery location not found
exception.order.interval-error=Unavailable time slot ID
exception.order.cancel-error-already-confirm=It is not possible to confirm/cancel an item in a confirmed order
exception.order.cancel-error-already-confirm-order=It is not possible to confirm/cancel a confirmed order
exception.order.position-not-found-simple=Item not found\: {0}
exception.order.error-request-checkonline=Order {0}, an error occured while executing a request to CheckOnline\: {1} ({2})
exception.order.has-not-been-transferred=Unable to initiate payment. Order not shipped.
exception.order.alien=Unable to initiate payment for someone else's order
exception.order.attached-card-not-supported=Order {0}\: payment by linked card is not supported (counterparty\: {1})
exception.order.requisite-error=Order {0}\: unable to pay using the specified bank details ({1})
exception.order.can-not-complete-hold-best2pay=For order {0}, HOLD_COMPLETED to Best2Pay cannot be executed. Error\: {1}
exception.order.can-not-complete-refund-best2pay=A REFUND to Best2Pay cannot be made for an order {0}. Error\: {1}
exception.order.payout-in-progress=Order {0}\: payout is in progress
exception.order.payout-fail=Order {0}\: payout fail
exception.order.payout-list-size-zero=Order {0}\: payout list is null or empty
exception.order.expertise.invalid-state=Order {0}: unable to change expertise state, order state invalid ({1})
exception.order.expertise.unconfirmed=Order {0}: unable to change expertise state, order position {1} unconfirmed
exception.order.paymentOptions.notOrderIdNorSellerId=When receiving payment methods, the order or the seller must be transferred
exception.order.payment.auth.expire=Order {0}: unable to capture payment, auth expire ({1} > {2})
exception.order.no.payments.on.confirm=Order {0}: unable to confirm, no payments info
exception.order.buyer-seller.orders-count.unknown=Unknown error while getting amount of sales|purchases for user {0}
exception.order.item-in-boutique-only=Item {0} is only available in OSKELLY boutique
exception.promocode.number-of-applies-exceeded=The number of uses of the promocode has been exhausted
exception.waybill.forbidden-area=Can''t create order for forbidden area. OrderId\: {0}
exception.waybill.cse.code=CSE error code\: {0}
exception.waybill.cse.history-notfound=History for the document with the number\: {0} not found in the CSE
exception.cse.already-exist=The delivery request for this order has already been registered in the CSE. If the status has not been updated, wait for the status update in the cabinet.
exception.cse.not-found=Order with clientNumber\: {0} not found in CSE
exception.cse.error-code=CSE error code\: {0}
exception.cse.history-error=Error when receiving the order history in the CSE for the document\: {0}
exception.cse.history-error-document=Not found history data for documentType\: {0}, historyNumber\: {1}
exception.cse.waybill-not-found=Waybill with clientNumber\: {0} not found in CSE
exception.streamservice.url-create-error=Failed to generate request link to https\://api.bambuser.com/broadcasts/{0}
exception.streamservice.bambuser-error=Didn''t recieve a valid BambuserResponse {0}
exception.streamservice.bambuser-error-simple=Didn't recieve a valid BambuserResponse
exception.streamservice.connect-error=Failed to connect to https\://api.bambuser.com/broadcasts/{0}
exception.streamservice.user-not-found=User with this primary key does not exist {0}
exception.userban.already-removed=This ban has already been lifted
exception.userban.already-removed-or-deleted=This ban has already been lifted or deleted
exception.userban.rules-violation=You violated the rules of the service and can no longer use the Price offering service
exception.userban.ban=You are banned
exception.address.delete-no-rights=It is not possible to edit/delete/use a location that does not belong to the specified user
exception.address.delete-impossible-used=Location cannot be removed because it is used in orders
exception.address.endpoint-delivery-pickup-not-found=Shipment location not found\: {0}
exception.address.endpoint-pickup-not-belong=Delivery location does not belong to the current user\: {0}
exception.address.endpoint-delivery-not-belong=Shipment location does not belong to this user\: {0}
exception.address.endpoint-not-found=Delivery location not found\: {0}
exception.address.endpoint-pickup-not-found=Shipment location not found\: {0}
exception.address.endpoint-delivery-not-found=Shipment location not found\: {0}
exception.address.endpoint-delivery-not-fully-filled=The export point is not fully filled\: {0}
exception.address.link-not-valid=Link is not valid
exception.agent.report-in-fail-list=Order {0}: unable to process sale report (ARFL)
exception.agent.invalid-state=Agent report {0}\: invalid state {1}
exception.agent.cb.required-seller-link=Agent report in crossborder order {0}\: seller link is required
exception.agent.cb.required-seller-price=Agent report in crossborder order {0}\: seller price is required
exception.agent.cb.required-seller-currency=Agent report in crossborder order {0}\: seller price currency is required
exception.agent.cb.invalid-seller-currency=Agent report in crossborder order {0}\: seller price currency must be same in all positions
exception.agent.repeated-payment-error=It is not possible to make a repeat payment for the agent''s report {0}
exception.agent.report-already-confirmed=The agent''s report for the order {0} has already been confirmed
exception.agent.report-already-in-process=The agent''s report for the order {0} has been already in process of payout. Wait completion of operation!
exception.agent.counteragent-not-installed=The seller''s counterparty is not installed for the order {0}
exception.agent.banking-details-not-set=For the order {0}, the withdrawal details to the account / card are not specified (counterparty\: {1})
exception.agent.banking-details-not-supported=For the order {0}, the details of the withdrawal to the card ({1}) are specified\: not supported at the moment
exception.agent.agent-didnt-receive-money=For order {0} the money has not been transferred yet. Confirmation of the report is available only after the transfer.
exception.agent.counteragent-not-set=The seller''s counterparty is not installed for the order {0}
exception.agent.confirm-someone-report=You cannot confirm someone else's report
exception.agent.access-denied-report=No access to agent report
exception.agent.report-not-exist=Agent report does not exist for order {0}
exception.agent.report-has-disput=Cannot confirm agent report for order {0}: has open dispute
exception.agent.report-not-exist-simple=Agent report does not exist
exception.agent.agent-report-not-exist=Agent report {0} does not exist
exception.agent.illegal-cp-kind=Agent report {0}: invalid counterparty info
exception.agent.agent-report-cb-cp-kind-fail=Order {0}: able to payout only to {1} type (got: {2})
exception.agent.agent-report-cb-ps-kind-fail=Order {0}: paymentsSystem {1} doesnt fits CB orders
exception.agent.counterparty.invalid.type=Counterparty {0} error in order {1}: invalid type
exception.agent.counterparty.empty.company.name=Counterparty {0} error in order {1}: empty company name
exception.agent.counterparty.empty.legal.address=Counterparty {0} error in order {1}: empty legal address
exception.agent.counterparty.empty.director.name=Counterparty {0} error in order {1}: empty director name
exception.agent.counterparty.empty.bank.name=Counterparty {0} error in order {1}: empty bank name
exception.agent.counterparty.empty.billing.address=Counterparty {0} error in order {1}: empty billing address
exception.agent.counterparty.empty.swift.code=Counterparty {0} error in order {1}: empty swift code
exception.agent.counterparty.empty.iban=Counterparty {0} error in order {1}: empty iban
exception.description.unacceptable-words=The text of the description contains forbidden words\!
exception.comment.phone-not-verified=Unable to save comment: profile phone number not verified
exception.comment.telephone-must-not-contain=The text of the comment should not contain phone numbers\!
exception.comment.email-must-not-contain=The text of the comment should not contain e-mail\!
exception.comment.unacceptable-words=The text of the comment contains forbidden words\!
exception.comment.link-must-not-contain=The text of the comment should not contain links\!
exception.comment.configurable-stop-list-word=The text of the comment contains forbidden words\!
exception.comment.user-not-authorized=Only logged in users can leave comments
exception.comment.order-id-not-present=The ID of the product to which the comment relates is not specified
exception.comment.product-request-id-not-present=The product request to which the comment relates is not specified
exception.comment.must-not-empty=The text of the comment cannot be empty\!
exception.comment.image-over-number=Unable to attach more than {0} images\!
exception.comment.product-not-found=Item with id {0} not found\!
exception.comment.parent-comment-not-found=Comment {0} not found\!
exception.comment.already-deleted=The comment you commented on has been deleted by a user. Try leaving a comment below the item.
exception.comment.not-apply-specified-product=Comment {0} does not apply to the specified item\!
exception.comment.not-apply-specified-product-request=Comment {0} does not apply to the specified product request\!
exception.comment.publishing.total-limit-exceeded=Comments limit exceeded
exception.comment.publishing.similar-limit-exceeded=Similar comments limit exceeded
exception.post.configurable-stop-list-word=The text contains forbidden words\!
exception.bank.operation-id-empty=Bank Operation Id is not set for the handleBindCard method
exception.bank.operation-not-card-bind=Operation {0} is not CARD_BIND
exception.bank.operation-not-in-process=Operation {0} is not in progress
exception.bank.tbk-response-fail=TKB did not return a response
exception.bank.card-binding-error=Card linking operation failed
exception.bank.request-for-user-error=No registerCardBeginResponse was received from {0} for the user\: {1}
exception.birthday.can-not-change=You are not allowed to change date of birth
exception.birthday.incorrect-date=Incorrect date of birth\!
exception.card.card-ref-id-empty=Counterparty {0} does not contain cardRefId for unbindCard
exception.card.unbound=The operation of unbinding the card was not performed successfully
exception.card.card-ref-id-unbind=No cardRefId set for unbind card
exception.card.counteragent-card-id-not-found=Counterparty for cardRefId {0} not found
exception.cart.not-possible-create-order-an-order=Unable to create a pre-order for an order with a status other than CREATED
exception.cart.user-not-found=User not found\: {0}
exception.cart.no-available-products-from-seller=There are no available items from the specified seller in the order\: {0}
exception.cart.buyer-not-set=Unable to create a pre-order because the buyer is not identified
exception.cart.empty=Shopping bag is empty. The order cannot be initiated.
exception.chat.add-error=An error occurred while adding a user to HelpCrunch
exception.chat.update-error=An error occurred while updating users in HelpCrunch
exception.chat.delete-error=An error occurred while deleting a user in HelpCrunch
exception.chat.get-info-error=An error occurred while getting user information in HelpCrunch
exception.counterparty.incorrect-format-bik=Incorrect BIK
exception.counterparty.incorrect-format-corr-acc=Incorrect corr account number
exception.counterparty.incorrect-format-swift=SWIFT format is incorrect
exception.counterparty.no-iban-and-swift=Account information should not be empty
exception.counterparty.no-country-type=Country type should not be empty
exception.counterparty.no-code=You need to fill {0} code
exception.counterparty.no-account-type=Account number should not be empty
exception.counterparty.no-bic-and-swift=BIK or SWIFT should not be empty
exception.counterparty.no-country-in-billing-address=Country should not be empty in the billing address
exception.counterparty.no-iban-and-account-number=IBAN or account number should not be empty
exception.counterparty.no-name=You need to fill in the name or company name
exception.counterparty.incorrect-format-corresp-acc=Payment account incorrect format
exception.counterparty.incorrect-format-inn-acc=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ ÑÐ¾ÑÐ¼Ð°Ñ ÐÐÐ ÑÑÐµÑÐ°. ÐÐ¾Ð»Ð¶ÐµÐ½ ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑ {0} ÑÐ¸ÑÑ Ð±ÐµÐ· Ð¿ÑÐ¾Ð±ÐµÐ»Ð¾Ð² Ð¸ Ð´ÑÑÐ³Ð¸Ñ ÑÐ¸Ð¼Ð²Ð¾Ð»Ð¾Ð²
exception.counterparty.access-denied-update-counteragent=You cannot edit a counterparty that does not belong to the current user.
exception.counterparty.incorrect-format-ogrn=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ ÑÐ¾ÑÐ¼Ð°Ñ ÐÐÐ Ð (ÐÐÐ ÐÐÐ). ÐÐ¾Ð»Ð¶ÐµÐ½ ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑ 15 ÑÐ¸ÑÑ Ð±ÐµÐ· Ð¿ÑÐ¾Ð±ÐµÐ»Ð¾Ð² Ð¸ Ð´ÑÑÐ³Ð¸Ñ ÑÐ¸Ð¼Ð²Ð¾Ð»Ð¾Ð²
exception.counterparty.incorrect-format-ogrn-other=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ ÑÐ¾ÑÐ¼Ð°Ñ ÐÐÐ Ð. ÐÐ¾Ð»Ð¶ÐµÐ½ ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑ 13 ÑÐ¸ÑÑ Ð±ÐµÐ· Ð¿ÑÐ¾Ð±ÐµÐ»Ð¾Ð² Ð¸ Ð´ÑÑÐ³Ð¸Ñ ÑÐ¸Ð¼Ð²Ð¾Ð»Ð¾Ð²
exception.counterparty.incorrect-format-kpp=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ ÑÐ¾ÑÐ¼Ð°Ñ ÐÐÐ. ÐÐ¾Ð»Ð¶ÐµÐ½ ÑÐ¾Ð´ÐµÑÐ¶Ð°ÑÑ 9 ÑÐ¸ÑÑ Ð±ÐµÐ· Ð¿ÑÐ¾Ð±ÐµÐ»Ð¾Ð² Ð¸ Ð´ÑÑÐ³Ð¸Ñ ÑÐ¸Ð¼Ð²Ð¾Ð»Ð¾Ð²
exception.counterparty.incorrect-format-iban=IBAN format is incorrect
exception.counterparty.access-denied-delete-counteragent=You cannot delete a counterparty that did not belong to the specified user
exception.counterparty.counterparty-does-not-belong=The counterparty does not belong to the current user\: {0}
exception.counterparty.counteragent-not-found=Counterparty not found\: {0}
exception.counterparty.deleted.true=Counterparty {0} was deleted. Restricted to use
exception.counterparty.field-empty=Empty field
exception.counterparty.should-without-spaces=It is not allowed to start or end Name with spaces
exception.counterparty.must-not-contain-quotation-marks=Name should not contain quotation marks
exception.counterparty.name-should-not-start=It is not allowed to start Name with {0}
exception.counterparty.requisite-can-not-delete-order=Bank details cannot be removed because they are used in orders
exception.counterparty.output-not-available=Order {0}, payment to a bank card is not available\: use the withdrawal of funds according to the details (counterparty\: {1}, {2})
exception.counterparty.output-test-not-available=Order {0}\: payment to the card in test mode, unavailable to the user {1} (choose another withdrawal method or try again later, counterparty\: {2})
exception.counterparty.output-expired-card-empty=Order {0}\: card expiration date {1} not specified (account\: {2})
exception.counterparty.output-expired-card=Order {0}\: card {1} is about to expire or has already expired (account\: {2})
exception.counterparty.requisite-not-change-already-confirm-order=ÐÐ°ÐºÐ°Ð· {0}\: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¸Ð·Ð¼ÐµÐ½Ð¸ÑÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ, Ð·Ð°ÐºÐ°Ð· ÑÐ¶Ðµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½ Ñ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÐ°Ð¼Ð¸ Ð®Ð / ÐÐ ({1})
exception.counterparty.requisite-receipts-exists=ÐÐ°ÐºÐ°Ð· {0}\: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¸Ð·Ð¼ÐµÐ½Ð¸ÑÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ, Ð¾ÑÐ¾ÑÐ¼Ð»ÐµÐ½ ÑÐ¸ÑÐºÐ°Ð»ÑÐ½ÑÐ¹ ÑÐµÐº 
exception.counterparty.requisite-not-used-already-confirm-order=ÐÐ°ÐºÐ°Ð· {0}\: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð®Ð / ÐÐ ({1}), Ð·Ð°ÐºÐ°Ð· ÑÐ¶Ðµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½
exception.counterparty.requisite-not-used-need-admin-confirm=ÐÐ°ÐºÐ°Ð· {0}\: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð®Ð / ÐÐ ({1}), ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð½Ðµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ñ Ð°Ð´Ð¼Ð¸Ð½Ð¸ÑÑÑÐ°ÑÐ¾ÑÐ¾Ð¼
exception.counterparty.requisite-not-used-incorrect-index-nds=ÐÐ°ÐºÐ°Ð· {0}\: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð®Ð / ÐÐ ({1}), Ð½ÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ Ð¸Ð½Ð´ÐµÐºÑ ÐÐÐ¡ ({2})
exception.counterparty.requisite-need-use-confirms=ÐÐ°ÐºÐ°Ð· {0}\: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð¤Ð ({1}) Ð´Ð»Ñ Ð®Ð / ÐÐ, Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÑÐ½Ð½ÑÐµ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð®Ð / ÐÐ
exception.counterparty.requisite-incorrect-type=ÐÐ°ÐºÐ°Ð· {0}\: Ð½Ðµ ÑÐ´Ð°ÐµÑÑÑ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð¤Ð ({1}) Ð´Ð»Ñ Ð®Ð / ÐÐ, Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð®Ð / ÐÐ
exception.counterparty.requisite-empty=Order {0}, counterparty {1}\: payment details are not specified
exception.counterparty.requisite-agent-sell-not-set=Order {0}\: the order cannot be confirmed, the details for the agency sale are not specified (seller\: {1})
exception.counterparty.bank-card-already-linked=User {0} ({1})\: bank card {2} has already been linked
exception.counterparty.counteragent-not-type-counterparty=Counterparty {0} is not of the CardCounterparty type
exception.counterparty.notAllowedToModify=It is not allowed for current user to modify payout methods
exception.counterparty.payout-active=Order {0}: unabe to change payout params (counterparty), payout is in progress or done
exception.deeplink.not-supported=This type of link is not supported
exception.deeplink.already-used=The link has already been used
exception.deeplink.catalog-item-not-found=Catalog item is not found
exception.defectimage.not-found=No such defect image
exception.defectimage.extra-identificator=Extra identifiers of defect images that are not in the database have been transferred
exception.discount.products-missing=There are no items from the specified seller in the Shopping bag
exception.discount.order-empty=You can't apply a discount to an empty order
exception.email.exists=E-mail {0} already used (user: {1})
exception.email.invalidate-value=Invalid value\: {0}
exception.email.maximum-length=Maximum allowed length\: {0}
exception.email.invalidate-format=The email does not match the format. Only Latin characters, numbers, dots, underscores are allowed. Required character - @
exception.email.too-many-attempts=It is not possible to generate email. Max attempts count reached.
exception.export.not-found-marker=The <offers/> marker was not found in the YML feed template
exception.fiscal.generate-request-counteragent-empty=Unable to generate receipt request\: order {0}, agent-seller without the specified counterparty
exception.fiscal.generate-request-mark-code-confirmation=Unable to generate receipt request\: order {0}, unconfirmed marking code
exception.fiscal.unsupported-fiscal-kind=Unsupported fiscalReceiptKind\: {0}
exception.fiscal.unable-generate=Failed to generate request for receivement
exception.fiscal.unsupported-fiscal-type=Unsupported type fiscalReceiptKind\: {0}
exception.fiscal.payment-amount-ne-total=Unable to generate fiscal receipts with type {0}\: totalPaysAmount ({1}) != positionsAmount({2})
exception.fiscal.unable-generate-invalid-vat=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ ÑÐ³ÐµÐ½ÐµÑÐ¸ÑÐ¾Ð²Ð°ÑÑ Ð·Ð°Ð¿ÑÐ¾Ñ Ð½Ð° Ð¿Ð¾Ð»ÑÑÐµÐ½Ð¸Ðµ Ð¾Ñ Ð°Ð³ÐµÐ½ÑÐ°\: ÐºÐ¾Ð½ÑÑÐ°Ð³ÐµÐ½Ñ {0}, Ð½ÐµÐ²ÐµÑÐ½ÑÐ¹ Ð¸Ð½Ð´ÐµÐºÑ ÑÑÐ°Ð²ÐºÐ¸ ÐÐÐ¡ {1}
exception.forbidden.comment-unauthorized=Only logged in user can delete comments
exception.forbidden.comment-delete-only-owner=Only the owner of a comment can delete it\!
exception.forbidden.notification-access-denied=You do not have the right to view this notification
exception.forbidden.access-denied-order-info=You do not have the right to receive order data\: {0}
exception.forbidden.access-denied-order-info-in-state=You do not have the right to to receive order data in the {0} status
exception.forbidden.not-seller-in-order=You are not a seller in the order\: {0}
exception.forbidden.not-buyer-in-order=You are not a buyer in the order\: {0}
exception.forbidden.invalidate-login-or-password=Incorrect login or password
exception.forbidden.invalidate-phone-number=Incorrect phone number
exception.image.incorrect-parameter=Incorrect image settings
exception.forbidden.invalidate-auth-data=Incorrect auth data
exception.image.impossible-save=Unable to save image
exception.image.extra-image-ids-not-supported=Extra image IDs that are not in the database have been transferred
exception.nickname.already-used-simple=User with this nickname already exists
exception.nickname.already-used=User with this nickname {0} already exists
exception.nickname.invalidate-value=Invalid value\: {0}
exception.nickname.maximum-length=Maximum allowed length\: {0}
exception.nickname.minimum-length=Minimum allowed length\: {0}
exception.nickname.invalidate-symbol=Contains invalid characters. Can contain only Latin letters, numbers, underscores, and dashes
exception.not-found.promocode=Discount with promotional code\: {0} not found
exception.not-found.promocode-simple=Discount not found
exception.no-access.promocode=Unable to access this promocode
exception.forbidden.promocode.save.user-conflict=Saving a promo code: unable to specify user and group at the same time
exception.forbidden.promocode.save.name=Saving a promo code: no name specified
exception.forbidden.promocode.save.many-types=Saving a promo code: only one of the fields 'percent' / 'amount' must be specified
exception.forbidden.promocode.save.no-types=Saving promo code: none of the 'percentage'/'amount' fields are specified
exception.forbidden.promocode.save.percent=Saving a promo code: the discount in percentage must be in the range 0 .. 100
exception.forbidden.promocode.save.amount=Saving a promo code: the discount amount must be greater than zero
exception.forbidden.promocode.save.order-amount=Saving a promo code: the minimum order amount must be greater than zero
exception.forbidden.promocode.save.date=The expiration date of the promo code must be greater than the start date
exception.forbidden.promocode.save.relation=Filter condition is duplicated
exception.not-found.position-order=Order item not found\: {0}
exception.not-found.notification=Notification not found
exception.not-found.order=Order with ID\: {0} not found
exception.not-found.user=User with clientId\: {0} not found\!
exception.not-found.user.phone=User with phone\: {0} not found\!
exception.not-found.current-user=User not found\!
exception.not-found.invoice=Consignment note not found\: {0}
exception.not-found.product=Item not found\: {0}
exception.not-found.user-common-tag=User common tag not found: {0}
exception.not-found.user-common-tag-group=User common tag group not found: {0}
exception.too-many-found.user.phone=Too many users with phone: {0} !
exception.too-many-found.unverified-user.phone=Unverified users with phone: {0} more than one!
exception.too-many-found.verified-user.phone=Users with phone: {0} more than one!
exception.notification.dto=Notification DTO not sent
exception.notification.class=Class name not passed
exception.notification.class-param=Class {0} not found
exception.notification.invalidate-packet=Invalid package
exception.notification.target-id-empty=Recipient ID not set
exception.notification.instance-create-access-denied=Access error when creating an instance {0} {1}
exception.oauth.apple-jwt=Unable to set JWT for Apple
exception.oauth.create-url-apple-profile=Failed to create Apple profile request link
exception.oauth.collect-request-parameters-apple=Failed to collect request settings to Apple
exception.oauth.parse-apple=Couldn't parse appleTokenResponse response from Apple
exception.oauth.waiting-limit-exceeded-apple=Apple connection time limit exceeded\!
exception.oauth.waiting-limit-exceeded-fb=Facebook connection time limit exceeded\!
exception.oauth.waiting-limit-exceeded-vk=Vk connection time limit exceeded\!
exception.oauth.getting-data-error-apple=Failed to get data from Apple
exception.oauth.getting-data-error-vk=Failed to get data from Vk\!
exception.oauth.getting-data-error-fb=Failed to get data from Facebook
exception.oauth.unable-log-in-apple=Unable to log in to Apple\: {0}
exception.oauth.unable-log-in-fb=Failed to log in to Facebook\: {0}
exception.oauth.unable-log-in-vk=Unable to log in to VK\: {0}
exception.oauth.token-response-apple=No appleTokenResponse, but the request was successful\!
exception.oauth.unable-get-apple-key=Failed to retrieve apple public keys
exception.oauth.invalid-wrapped-apple-id-token=Failed to retrieve apple public keys
exception.oauth.token-response-fb=No facebookResponse, but the request was successful\!
exception.oauth.token-response-vk=No vkUserInfo, but the request was successful\!
exception.oauth.parse-id-token-apple=An error occurred while parsing Apple's idToken
exception.oauth.unable-create-url-fb=Failed to create FB profile request link
exception.oauth.unable-create-url-vk=Failed to create a link to request info in VK
exception.oauth.user-not-found-apple=User with Apple ID not found
exception.oauth.user-not-found-fb=User with Facebook ID not found
exception.oauth.user-not-found-vk=User with VK ID not found
exception.oauth.google.id-token-null=Can't get data from Google
exception.oauth.google.verify-error=Can't get data from Google
exception.order-creation.position-seller-not-found=Items from the specified seller not found\: {0}
exception.order-creation.cant-creating=The Order cannot be procedeed
exception.order-creation.cant-creating-reason=The Order cannot be procedeed.\nReason\: {0}. \nCategory\: {1}. \nItems you need to remove from Shopping bag\: {2}
exception.oskelly.position-not-found=Item with id\: {0} does not exist
exception.oskelly.yr-need-requisite=Ð®ÑÐ¸Ð´Ð¸ÑÐµÑÐºÐ¾Ð¼Ñ Ð»Ð¸ÑÑ Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾ Ð²ÑÐ±ÑÐ°ÑÑ ÑÐµÐºÐ²Ð¸Ð·Ð¸ÑÑ Ð´Ð»Ñ Ð²ÑÐ¿Ð»Ð°ÑÑ ÑÑÐµÐ´ÑÑÐ².
exception.oskelly.position-need-mark-code=For positions\: {0}, marking codes must be added.
exception.oskelly.user-not-set-for-address=The user to save addresses is not set
exception.oskelly.user-not-set-contract-details=Contract number and contract date are required
exception.oskelly.not-has-address-save=No addresses to save
exception.oskelly.international.city-is-not-set=City must be from the list
exception.oskelly.international.country-is-not-set=Country must be from the list
exception.oskelly.international.billing.address.city-is-not-set=Billing address city must be from the list
exception.oskelly.international.billing.address.country-is-not-set=Billing address country must be from the list
exception.oskelly.international.billing.address.not-has-address-save=No billing addresses to save (address lines)
exception.oskelly.international.billing.address.zipcode-not-found=The postal code is empty in billing address
exception.oskelly.international.legal.address.no-country-in-legal-address=Not set country in legal address
exception.oskelly.international.legal.address.city-is-not-set=Legal address city must be from the list
exception.oskelly.international.legal.address.country-is-not-set=Legal address country must be from the list
exception.oskelly.international.legal.address.not-has-address-save=No legal addresses to save (address lines)
exception.oskelly.international.legal.address.zipcode-not-found=The postal code is empty in legal address
exception.oskelly.counteragent-requisite-not-have=There are no counterparty details to save
exception.oskelly.user-not-set-for-role=The user to save the role is not set
exception.oskelly.user-not-set-for-sex=The user to save gender is not set
exception.oskelly.user-not-set-for-requisite=The user to save bank details is not set
exception.oskelly.user-not-set-for-social=The user to save social accounts is not set
exception.oskelly.data-not-have-for-role=There is no data to install the role
exception.oskelly.data-not-have-for-social=No data to set up social accounts
exception.oskelly.data-not-have-for-address=No addresses to save
exception.oskelly.data-not-have-for-requsite=No bank details to save
exception.oskelly.incorrect-requsite-condition-active-delete=invalid condition, deleted payment detail can be active
exception.address.incorrect-fias-id-format=Address {0}: invalid FIAS id: {1}
exception.oskelly.incorrect-zipcode=The postal code is specified incorrectly\: {0}
exception.oskelly.zipcode-not-found=The postal code is empty
exception.oskelly.address-field-is-null-or-empty=The address field can't be empty
exception.oskelly.address-country-not-supported=Country {0} (ID\: {1}) is not supported by logistics services
exception.oskelly.address-city-not-supported=City {0} (ID\: {1}) is not supported by logistics services
exception.oskelly.attribute-color-not-set=Colour attribute not set
exception.oskelly.unavailable-channel-notification=Unavailable channel for notification delivery\: {0}
exception.oskelly.current-order-empty=No active order {0}
exception.oskelly.code-mark-empty=ÐÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾ Ð·Ð°Ð¿Ð¾Ð»Ð½Ð¸ÑÑ ÐºÐ¾Ð´ Ð¼Ð°ÑÐºÐ¸ÑÐ¾Ð²ÐºÐ¸ Ð´Ð»Ñ ÑÐ¾Ð²Ð°ÑÐ°\: {0}
exception.oskelly.order-best2pay-error=Error occurred while registering an order in Best2pay {0}
exception.oskelly.pay-tcb-error=Order {0}: unable to process payment (TCB), please, try again later
exception.oskelly.pay-general-error=Order {0}: unable to process payment ({1}), please, try again later
exception.oskelly.pay-bonuses-and-promocode=Order {0}: unable to process payment. It is prohibited to use bonuses and promocode simultaneously.
exception.oskelly.pay-bonuses-incorrect-sum=Error checking order sum (code NOA)
exception.oskelly.pay-bonuses-unexpected=Order {0}: unable to process payment. Unexpected bonuses distribution error.
exception.oskelly.pay-sum-negative=Effective sum with expertise on the position {0} of the order {1} can't be negative {2}
exception.oskelly.tcb-response-error=Error when receiving a response from TCB Bank when paying for an order {0}
exception.oskelly.tcb-partial-refund-error=For order {0}, PARTIAL_REFUND to TCB cannot be executed. Error\: {1}
exception.oskelly.tcb-refund-error=For order {0}, REFUND to TCB cannot be performed. Error\: {1}
exception.oskelly.conversion-need-parameter=One and only one of the two possible parameters must be set for the conversion.
exception.oskelly.date-format-error=Invalid data format
exception.password.empty=Current password is empty
exception.password.incorrect-password=Incorrect current password
exception.password.token-not-found=Token not found\: {0}
exception.phone.missing=Missing phone number.
exception.phone.incorrect-format=Invalid phone number format.
exception.phone.basket-size-limit=Maximum Shopping bag size exceeded\: {0} items
exception.product.unavailable-see=Current user cannot view this item \#{0}
exception.product.inconsistent-price-params=Inconsistent price params
exception.product.no-authority-for-custom-commission=No authority for changing "custom commission"
exception.product.by-sale-request.editing-not-allowed-for-seller=Editing of the product for seller not available
exception.product.by-sale-request.deletion-not-allowed-for-seller=Deletion of the product for seller not available
exception.product.by-sale-request.current-price-not-specified=Current price not specified for product {0}
exception.product.by-sale-request.seller-price-not-specified=Seller price not specified for product {0}
exception.product.exclusive-lot.unavailable=The product is only available to users with the Black level
exception.sale-request.double-by-bitrix-deal-id=There are sale requests in the system with bitrix deal id: {0}
exception.return.order-not-found=Order not found
exception.return.order-access-denied=You do not have the right to create a return for this order
exception.return.invalidate-status=Order status does not allow a issue a Return
exception.return.already-created=Return for this order has already been created
exception.return.position-empty=Return items not submitted
exception.return.photo-empty=Passport photo not submitted
exception.return.address-point-empty=Location not submitted
exception.return.address-point-not-found=Location not found
exception.return.address-point-incorrect=Invalid location
exception.return.counteragent-empty=Not transferred to the counterparty
exception.return.counteragent-not-found=Counterparty not found
exception.return.counteragent-incorrect=An incorrect counterparty has been transferred
exception.return.position-id-order-empty=Item Id not submitted
exception.return.position-id-not-found=Item not found in order
exception.return.reason-empty=Reason for return not set
exception.return.photo-tag-empty=Tag photo not submitted
exception.return.reason-not-found=Reason for the return not found\: {0}
exception.return.not-found=Return not found
exception.return.access-denied=You have no rights to view this return
exception.oauth.registration=Failed to register the user. Pass the following data\: ['fb_token'] or ['vk_token' and 'vk_user_id'] or ['apple_kid' and 'apple_authorization_code']
exception.oauth.auth=The user could not be authorized. Pass the following data\: fb_token or vk_token or (apple_id and apple_authorization_code)
exception.file.limit-max=File size exceeds {0} MB
exception.file.not-found=File not found
exception.file.access-denied=You have no rights for this file
exception.validation.page-error=Invalid page size\: {0}. Only values from {1} to {2} are acceptable
exception.validation.register-error=Failed to register the user
exception.validation.error=Validation error
exception.validation.email-empty=Email is not specified
exception.validation.password-empty=Password is not specified
exception.validation.user-not-register=The user is not registered in the system
exception.validation.user-already-register-by-email=You have already registered with this email using a different method. Please log in using the method you used before
exception.user.authority-required=You need one of the authorities {0} to perform this action
exception.user.at-least-one-authority-required=You need at least one authority to perform this action
exception.user.auth=Not logged in
exception.user.auth.unexpectedError=Auth error
exception.city.cargo-not-found=The city is not found in the KARGO system. Address\: ZIP - {0}, settlement FIAS - {1}, city FIAS - {2}, city - {3}.
exception.city.not-supported=The locality/region is not served by DALLI. DALLI delivers only in Moscow, St. Petersburg, the Moscow region and the Leningrad region. Order number\: {0}, region\: {1}, locality\: {2}
exception.city.not-supported-dalli=Address is not served by Falli. Region\: {0}, locality\: {1}
exception.city.not-found-major=Unable to find the city {0} in the Major Express database.
exception.defect.comment-photo-empty=Defect image comment not set
exception.defect.comment-photo-max-length=The comment to the image of the defect must not exceed {0} characters
exception.defect.photo-incorrect-count=Invalid number of images of defects\: {0}
exception.defect.photo-incorrect-count-plus=Invalid number of images with defects\: {0}. The number must not exceed the actual number of photos of defects uploaded\: {1}
exception.image.incorrect-count=Invalid number of images\: {0}
exception.image.incorrect-count-plus=Invalid number of images\: {0}. The number must not exceed the actual number of uploaded photos\: {1}
exception.IllegalStateException.class-create=Error creating an instance of the class. Class\: {0}
exception.datamatrix.wrong-code=HS(Harmonised System)-code {0} does not match the format
exception.datamatrix.unconfirmed=Order {0} (position {1}): marking code unconfirmed
exception.datamatrix.wrong-order-state=Order {0} (position {1}): unable to change marking code info, order state invalid ({2})
exception.country-of-origin.not-found=Country of origin not found. Please, contact support with this problem
exception.country-of-origin.null-value=Country of origin not passed
exception.address-endpoint-aggregation.not-found=Address not found
exception.order.illegal.state=Order {0} has state {1} is not valid for current operation.
exception.order.is.not.boutique=Order {0} is not for boutique. Operation is restricted.
exception.oskelly.order-pickup-pending=Delivery service is being called for the order {0}
exception.oskelly.delivery-service-already-called=Delivery service has already been called
exception.product.published-condition-null=Condition must be set for published product
exception.wrong-commission-grid-type-for-user=Wrong commission grid type for user
exception.wrong-commission-grid-for-user=Wrong commission grid for user

exception.DefaultAdminCommentService.AdminBadCommentEditException=The comment field must not be empty\!

exception.catalog-menu.failed=Failed to get catalog menu for version {0}

exception.product-item-deletion.size-of-product-used-in-boutique=Size {0} of product {1} is in Boutique and cannot be deleted
exception.product-item-deletion.product-item-used-in-boutique=Product {0} has active Boutique orders and cannot be deleted

exception.sale-request.access-denied=You do not have access rights to this sale-request
exception.sale-request.comment.delete-forbidden=Deletion another administrator's comment is forbidden
exception.product-bitrix.deal-id-already-used=Bitrix deal with ID:{0} already used in product with ID:{1}
exception.order-position-bitrix.order-position-id-ordeal-id-already-used=Order position with ID:{0} already used OR Bitrix deal with ID:{1} already used
exception.order-position-bitrix.bitrix-deal-id-already-used=Error when trying to associate a Bitrix deal with an order. Bitrix deal (ID: {0}) is already associated with another order
exception.order-position-bitrix.order-position-id-is-null=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð¿Ð¾Ð¿ÑÑÐºÐµ ÑÐ²ÑÐ·Ð°ÑÑ Bitrix ÑÐ´ÐµÐ»ÐºÑ Ñ Ð·Ð°ÐºÐ°Ð·Ð¾Ð¼. ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ Ð·Ð°ÐºÐ°Ð·Ðµ Ð½Ðµ Ð¿ÐµÑÐµÐ´Ð°Ð½Ð°.
exception.user-exists-by-email=User with this email already exists
exception.user-exists-by-nickname=User with this nickname already exists
exception.user.orders-in-progress=User has orders in progress

su.reddot.domain.service.adminpanel.user.impl.AdminV2UserServiceImpl.NotFoundException.message=User with id={1} is not found
exception.JwtVerificationException.publicKeyNotFound=The token cannot be verified
exception.JwtVerificationException.unsignedJwt=Error on token signature verification
exception.JwtVerificationException.badJwt=Error on token verification
exception.JwtVerificationException.expiredJwt=Expired token
exception.UserSyncException.directSync=Failed to change the country. Please log out and try again
exception.UserWithSameEmailExists.directSync=User with such email already exists. (Id={0}, Uid={1})
exception.UserWithSameNicknameExists.directSync=User with such nickname already exists. (Id={0}, Uid={1})
exception.UserWithSamePhoneExists.directSync=User with such phone already exists. (Id={0}, Uid={1})
exception.UserForSyncIsDeleted.directSync=Target user is deleted. (Id={0}, Uid={1})

exception.text-slides.incorrect-interval=Incorrect slide interval: {0}

exception.osocial.post.text.size=You have reached the maximum text length
exception.osocial.post.text.lines.size=You have reached the maximum text length
exception.osocial.post.text.email-must-not-contain=The text of the post should not contain phone numbers!
exception.osocial.post.text.telephone-must-not-contain=The text of the post should not contain e-mail!
exception.osocial.post.text.unacceptable-words=The text of the post contains forbidden words!
exception.osocial.post.text.link-must-not-contain=The text of the post should not contain links!
exception.osocial.post.link.illegal-host=The link should not lead to a third-party resource

exception.osocial.comment.text.size=You have reached the maximum text length
exception.osocial.comment.text.lines.size=You have reached the maximum text length
exception.osocial.comment.text.email-must-not-contain=The text of the comment should not contain phone numbers!
exception.osocial.comment.text.telephone-must-not-contain=The text of the comment should not contain e-mail!
exception.osocial.comment.text.unacceptable-words=The text of the comment contains forbidden words!
exception.osocial.comment.text.link-must-not-contain=The text of the comment should not contain links!

exception.osocial.favorite-posts.access-denied=You do not have access rights to favorite posts

exception.admin.user.setLegalEntityForIndividualAccountDetails=Cannot set user role to Legal Entity due to existing Individual account details
exception.admin.user.setLegalEntityWithEmptyAccountDetails=Legal Entity user type requires adding account details first
exception.admin.user.setIndividualForLegalEntityAccountDetails=Cannot set user role to Individual due to existing Legal Entity account details
exception.admin.counterparty.createLegalEntityCounterpartyForIndividual=Cannot add Legal Entity account details for Individual user
exception.admin.counterparty.createIndividualCounterpartyForLegalEntity=Cannot add Individual account details for Legal Entity
exception.no-role.ADMIN_EDIT_PERSONAL_DATA=Not authorized to edit personal data

exception.instagram-feed.instagram.request.error=Error while request to Instagram

exception.UserLocationService.unknownLocation=Unknown location
exception.UserLocationService.anonymousUser=Cannot set or retrieve location for unauthorized user without guest token

exception.filter-subscriptions.duplicate-name=Saved search with this name already exists. Select another name
exception.filter-subscriptions.empty-name=Select name for saved search.

message.refund.operation.default.text=Order {0} refund

entity.admin-alert.AdminOrderAlertWaybill=Problems with delivery consignment note
entity.admin-alert.AdminOrderAlertWaybillCreationError=Consignment note not created
entity.admin-alert.AdminOrderAlertWaybillCreationErrorWrongAddress=Address error
entity.admin-alert.AdminOrderAlertWaybillDeliveryFromSellerFailedRaiseComplaint=The order was not delivered to the office, the claim of the COP
entity.admin-alert.AdminOrderAlertWaybillDeliveryProblem=Delivery problem
entity.admin-alert.AdminOrderAlertWaybillDeliveryProblemNoDeliveryAttempt=There was no delivery attempt
entity.admin-alert.AdminOrderAlertWaybillDeliveryProblemReceiptRenouncement=Refusal to receive
entity.admin-alert.AdminOrderAlertWaybillDeliveryProblemRecipientAbsence=Recipient missing
entity.admin-alert.AdminOrderAlertWaybillDeliveryProblemTraceOpen=Trace is open
entity.admin-alert.AdminOrderAlertWaybillDeliveryProblemWrongAddress=Wrong address
entity.admin-alert.AdminOrderAlertWaybillDeliveryToBuyerFailedRaiseComplaint=The order was not delivered to the office, the claim of the COP
entity.admin-alert.AdminOrderAlertWaybillPostponed=Change of the date (logistics)
entity.admin-alert.AdminOrderAlertWaybillPostponedAgreedDeliveryDate=Confirmed delivery date
entity.admin-alert.AdminOrderAlertWaybillPostponedCarrierProblem=Carrier problems
entity.admin-alert.AdminOrderAlertWaybillPostponedRedirect=Redirection
entity.admin-alert.AdminOrderAlertAcquirerRefund=Reception\:\nAcquiring returned the money
entity.admin-alert.AdminOrderAlertBankRejectedOperation=Conclusion\:\nThe bank rejected the transaction
entity.admin-alert.AdminOrderAlertBankTechnicalProblem=Conclusion\:\nBank's technical error
entity.admin-alert.AdminOrderAlertConfirmationSale=Sale not confirmed - more than 1 day ({0})
entity.admin-alert.AdminOrderAlertDeliveryAddress=You need to set the delivery address
entity.admin-alert.AdminOrderAlertExpertise=Ezpertise lasts - more than 1 day ({0})
entity.admin-alert.AdminOrderAlertFromOfficeToBuyer=The courier is delivering the item to the buyer - more than 1 day ({0})
entity.admin-alert.AdminOrderAlertFromSellerToOffice=The courier is delivering the item to OSKELLY - more than 1 day ({0})
entity.admin-alert.AdminOrderAlertMoneyPaymentNotEnough=Conclusion\:\nInsufficient funds in the account
entity.admin-alert.AdminOrderAlertMoscowDelivery=We should choose whether to deliver the item ourselves or by a logistician after the expertise
entity.admin-alert.AdminOrderAlertMoscowLogistOnWayToSeller=OSKELLY courier - It should be noted that the courier went to the seller
entity.admin-alert.AdminOrderAlertNeedSendAgentReport=You need to send an agent report
entity.admin-alert.AdminOrderAlertPartlyExpertise=Examination - Not passed - Return the goods to the seller
entity.admin-alert.AdminOrderAlertPickupAddress=You need to set the shipment address
entity.admin-alert.AdminOrderAlertPickupFromOffice=Courier didn''t pick up from OSKELLY - more than 1 day (outstanding for longer than {0} hours)
entity.admin-alert.AdminOrderAlertPickupFromSeller=The courier did not pick up from the seller - more than 1 day ({0})
entity.admin-alert.AdminOrderAlertSendAgentReport=Item delivered, report not sent - more than 1 day ({0})
entity.admin-alert.AdminOrderAlertWaitPaymentMoneyToSeller=The seller has not received\nmoney - more than 1 day ({0})
entity.admin-alert.AdminOrderAlertWaitPaymentMoneyToSeller.ReportNotFound=The report was not found\!
entity.admin-alert.AdminOrderAlertConfirmationAgentReport=Report not confirmed - more than 1 day ({0})
entity.admin-alert.AdminOrderAlertConfirmationAgentReport.ReportNotFound=The report was not found\!
entity.admin-alert.AdminOrderAlertDeliveryToBuyerUnconfirmed=Buyer did not confirm delivery ({0} days from the delivery)
entity.admin-alert.AdminOrderAlertDeliveryToBuyerConfirmed=Buyer has confirmed delivery ({0} days from the delivery)
entity.admin-alert.AdminProductAlert24hoursState=Expired
entity.admin-alert.AdminProductAlert24hoursStateModeration=Moderation is {0} days overdue
entity.admin-alert.AdminProductAlert24hoursStateRetouching=Retouching is {0} days overdue
entity.admin-alert.AdminProductAlert24hoursStateRetouchingDone=Retouched too long ago, but it''s been {0} days
entity.admin-alert.AdminProductAlert24hoursStateSecondEdition=Repeated editing is {0} days overdue
entity.BargainRecordFromType.enum.fromBuyer=From the Buyer
entity.BargainRecordFromType.enum.fromSeller=From the Seller
entity.BargainRecordFromType.enum.fromAdmin=From admin
entity.BargainRecordFromType.enum.auto=Automatic
entity.BargainRecordType.enum.hello-from-seller=Greetings from the seller
entity.BargainRecordType.enum.offer-from-buyer-or-seller=Offer from buyer or seller
entity.BargainRecordType.enum.accept-offer=Confirmation of the offer by the seller or buyer
entity.BargainRecordType.enum.declined-offer=Rejection of the offer by the seller
entity.BargainSort.enum.date-create=Creation date ascending
entity.BargainSort.enum.date-create-desc=Creation date descending
entity.BargainSort.enum.date-change=Modification date ascending
entity.BargainSort.enum.date-change-desc=Modification date descending
entity.BargainSort.enum.last-price=Last price\: low to high
entity.BargainSort.enum.last-price-desc=Last price\: high to low
entity.BargainState.enum.initial=Creation
entity.BargainState.enum.offer=Awaiting
entity.BargainState.enum.counter-offer=Counter offer
entity.BargainState.enum.declined=Declined
entity.BargainState.enum.confirmed=Confirmed
entity.BargainState.enum.sold=Item sold
entity.BargainState.enum.consumed=Price offer successful
entity.BargainState.enum.expired=Expired
entity.BargainState.enum.canceled=Canceled
entity.BargainState.enum.undefined=Undefined
entity.notification.BargainBuyerConfirmedNotification.subTitle=Your bargain is approved\!
entity.notification.AnotherCommentNotification.title=ð¬ {0}:
entity.notification.AnotherCommentNotification.baseMessage={0}
entity.notification.BargainBuyerConfirmedNotification.baseMessage=You have 24 hours to buy the product at the agreed price.
entity.notification.BargainBuyerCounterOfferNotification.subTitle=Counter offer price
entity.notification.BargainBuyerCounterOfferNotification.baseMessage=You have 24 hours to buy the product at the price offered by the seller
entity.notification.BargainBuyerDeclinedNotification.subTitle=The bargain is declined
entity.notification.BargainBuyerDeclinedNotification.baseMessage=Offer a new price. You have {0} more tries{1}.
entity.notification.BargainBuyerDeclinedNotification.baseMessage.Alt=You can purchase the product at the price shown in the ad.
entity.notification.BargainBuyerDeclinedNotification.baseMessage.Alt2=Haggle with other sellers and buy at the best prices.
entity.notification.BargainBuyerExpiredByBuyerNotification.subtitle=The bidding time is running out. 
entity.notification.BargainBuyerExpiredByBuyerNotification.baseMessage=You have 1 hour left to buy the product at the price offered by the seller.
entity.notification.BargainBuyerAlreadyConfirmed24hNotification.title=Don't forget to place the order
entity.notification.BargainBuyerAlreadyConfirmed24hNotification.baseMessage=You have 2 hours left to buy the item at the agreed price.
entity.notification.BargainBuyerProductSoldNotification.baseMessage=Unfortunately, the item {0} you bargained for has been purchased by another customer. Hurry up next time\!
entity.notification.BargainSellerNewOffer12hNotification.subTitle=offers his price for the product\:{0}
entity.notification.BargainSellerNewOffer12hNotification.baseMessage=Don't forget to let the buyer know about the haggling decision. Don't wait for potential buyers, bargain with real ones. You have 12 hours left to make a decision. After the deadline has passed, the buyer's offer will automatically be rejected. 
entity.notification.BargainSellerNewOffer24hNotification.subTitle=The bidding time is running out. 
entity.notification.BargainSellerNewOffer24hNotification.baseMessage=You have 2 hours left\! Hurry up and approve the buyer's price or offer your own.
entity.notification.BargainSellerNewOfferNotification.subTitle=Price offer
entity.notification.NewCommentNotification.title=ð¬ {0}:
entity.notification.NewCommentNotification.baseMessage={0}
entity.notification.NewCommentNotification.remindTitle=Hello, this is Oskelly. You forgot to reply to your potential buyer's comment. Please check the notifications in your personal account.
entity.notification.ReplyCommentNotification.title=ð¬ {0}:
entity.notification.ReplyCommentNotification.baseMessage={0}
entity.notification.NewFollowingNotification.title={0}
entity.notification.NewFollowingNotification.baseMessage=subscribed to your updates
entity.notification.BargainSellerNewOfferNotification.baseMessage={0}\: your product is ready to be bought for {1} AED. You have 24 hours to approve or offer your price.
entity.notification.BargainSellerNewOfferNotification.remindTitle=Hello, this is Oskelly. Have you seen it? You have a new offer for the price of {0}. The faster you react to the offer, the quicker the sale will happen.
entity.notification.CatalogNotification.baseMessage=New items in the catalogue\!
entity.notification.LastChance1000Notification.title=Did not have enough time to use the discount?
entity.notification.LastChance1000Notification.baseMessage=No worries\! We give you the new promo code CHANCE5 for 5% off â¤
entity.notification.NewArrivalsNotification.title=New arrivals every hour\!
entity.notification.NewArrivalsNotification.baseMessage=Hurry up, the best offers are redeemed within hours\!
entity.notification.Promo24HoursLeftNotification.title=Donât miss your discount\!
entity.notification.NewProductLikeNotification.baseMessage=Try to lower the price. We will notify everyone who has the item on their Wishlist so you can sell it faster.
entity.notification.NewProductLikeNotification.title=Your product is added to Wishlist
entity.notification.FirstProductLikeNotification.title=Someone is interested in your product\!
entity.notification.FirstProductLikeNotification.baseMessage={0} added your item to the Wishlist
entity.notification.LostCartNotification.title.0=We have saved your choice
entity.notification.LostCartNotification.baseMessage.0=Kindly remind you about the items in your shopping basket. Hurry up and place your order while the product is in stock.
entity.notification.LostCartNotificationNewbies.title=We have saved your choice
entity.notification.LostCartNotificationNewbies.baseMessage=Kindly remind you about the items in your shopping basket. Take advantage of a 5% discount with promo code WELCOME5 on your first purchase.
entity.notification.Promo24HoursLeftNotification.baseMessage=Place your first order and get 5% off with WELCOME5 promo code
entity.notification.YouWillLikeItNotification.title=You will like it
entity.notification.YouWillLikeItNotification.baseMessage=It's time for a bargain\! We've put together this week's best deals for you
entity.notification.LostPaymentNotification.title=Only 1ï¸â£ step left to buy\!
entity.notification.CreatePublicationNotification.title=Declutter your wardrobe
entity.notification.CreatePublicationNotification.baseMessage=Publish an item for sale and start making some money on OSKELLY!
entity.notification.OrderAgentReportConfirmedNotification.title=Order No. {0}. Details confirmed.
entity.notification.OrderAgentReportConfirmedNotification.baseMessage=Please wait for the amount to be credited to your account
entity.notification.OrderConfirmedNotification.title=Order No. {0} has been confirmed
entity.notification.OrderConfirmedNotification.baseMessage=The courier service will pick up your purchase from the seller and bring it to the OSKELLY offices for inspection shortly
entity.notification.OnlineOrderForBoutiqueProductNotification.title=Confirm online order {0} for boutique item {1}
entity.notification.OnlineOrderForBoutiqueProductNotification.baseMessage=Online order for a boutique product
entity.notification.OrderDeliveredToBuyerRequestConfirmationNotification.title=Order No. {0} has been delivered
entity.notification.OrderDeliveredToBuyerRequestConfirmationNotification.baseMessage=Confirm that you have received your order so that the seller knows that the delivery was successful
entity.notification.OrderDeliveredToBuyerNotification.title=Order No. {0} has been delivered
entity.notification.OrderDeliveredToBuyerNotification.baseMessage=Confirm that you have received your order so that the seller knows that the delivery was successful
entity.notification.OrderConfirmedPartlyNotification.title=Order No. {0} has been partially confirmed
entity.notification.OrderDeliveredToBuyerNeedAgentReportNotification.ProSeller.title=Order No. {0}. Confirm your details for payment processing
entity.notification.OrderDeliveredToBuyerNeedAgentReportNotification.Seller.title=Order No. {0} has been delivered
entity.notification.OrderConfirmedPartlyNotification.baseMessage.singular=The seller has declined part of the order. The courier service will pick up the confirmed item from the seller and bring it to the OSKELLY offices for inspection shortly.
entity.notification.OrderConfirmedPartlyNotification.baseMessage.plural=The seller has declined part of the order. The courier service will pick up the confirmed items from the seller and bring them to the OSKELLY offices for inspection shortly.
entity.notification.OrderDeliveredToBuyerNeedAgentReportNotification.ProSeller.baseMessage=Confirm your details to receive your payout. You will receive the amount within 24 hours after confirmation.
entity.notification.OrderDeliveredToBuyerNeedAgentReportNotification.Seller.baseMessage=Confirm your details to receive your payout. You will receive the amount within 3 days after confirmation.
entity.notification.LostPaymentNotification.baseMessage=Something went wrong at checkout, try again ð
entity.notification.LikedBrandProductPublishedNotification.title=New arrivals from your favourite brands
entity.notification.CartItemInBoutique.title=Available at OSKELLY boutique!
entity.notification.CartItemInBoutique.baseMessage=Try on a favorite piece from your cart in our boutique at {0}.
entity.notification.WishlistItemInBoutique.title=Available at OSKELLY boutique!
entity.notification.WishlistItemInBoutique.baseMessage=Try on a favorite piece from your wishlist in our boutique at {0}.
entity.notification.OrderDeliveredToBuyerWaitingForAgentReportNotification.title=Order No. {0} has been delivered
entity.notification.OrderDeliveredToBuyerWaitingForAgentReportNotification.baseMessage=The buyer has received the order. Payment will be processed within 7 days.
entity.notification.OrderDeliveredToExpertiseNotification.title=Order No. {0} has been delivered to the OSKELLY offices
entity.notification.OrderDeliveredToExpertiseNotification.baseMessage.singular=We will check the item for originality and compliance with the description shared by the seller shortly
entity.notification.OrderDeliveredToExpertiseNotification.baseMessage.plural=We will check the items for originality and compliance with the description shared by the seller shortly
entity.notification.OrderDeliveringFromOfficeToBuyerNotification.title=Order No. {0} has been handed over to the courier service
entity.notification.OrderDeliveringFromOfficeToBuyerNotification.baseMessage=The courier will contact you in advance to coordinate the delivery time
entity.notification.OrderDeliveringFromSellerToOfficeNotification.title=Order No. {0} is on its way
entity.notification.OrderDeliveringFromSellerToOfficeNotification.baseMessage=The courier service has picked up the order and is bringing it to the OSKELLY offices for inspection
entity.notification.OrderExpertiseFailedNotification.title=Order no. {0} hasn''t passed an examination
entity.notification.OrderExpertiseFailedNotification.baseMessage=Credited funds will be unfrozen within 12 hours.
entity.notification.OrderExpertiseFakeNotification.title=Order No. {0} did not pass inspection
entity.notification.OrderExpertiseFakeNotification.baseMessage=Our authentication team found that the item in your order is not original. We will refund the money for your order to the card.
entity.notification.OrderExpertiseImpossibleDetermineAuthenticityNotification.title=Order No. {0} did not pass inspection
entity.notification.OrderExpertiseImpossibleDetermineAuthenticityNotification.baseMessage=Our authentication team was not able to verify 100% originality for the item in your order. We will refund the money for the order to your card.
entity.notification.OrderExpertiseReconciliationFailedNotification.title=Order No. {0} did not pass inspection
entity.notification.OrderExpertiseReconciliationFailedNotification.baseMessage=The nuances have not been approved. We will refund the money to your card.
entity.notification.OrderExpertiseFailedMultipleNotification.title=Order No. {0} did not pass inspection
entity.notification.OrderExpertiseFailedMultipleNotification.baseMessage=You can see detailed information in your Personal Account. We will refund the money to your card.
entity.notification.OrderExpertisePassedNotification.title=Order No. {0} has successfully passed inspection
entity.notification.OrderExpertisePassedNotification.baseMessage.singular=We will now transfer the item for pre-sale care, pack your order and send it to you
entity.notification.OrderExpertisePassedNotification.baseMessage.plural=We will now transfer the items for pre-sale care, pack your order and send it to you
entity.notification.OrderExpertisePassedPartlyNotification.title=Order No. {0} has partially passed inspection
entity.notification.OrderExpertisePassedPartlyNotification.baseMessage.singularPassedSingularRejected=We will now transfer the item for pre-sale care, pack your order and ship it to you. Please note that {0} {1} in your order has not passed the inspection. We will refund the money for part of the order to your card.
entity.notification.OrderExpertisePassedPartlyNotification.baseMessage.singularPassedPluralRejected=We will now transfer the item for pre-sale care, pack your order and ship it to you. Please note that {0} {1} in your order have not passed the inspection. We will refund the money for part of the order to your card.
entity.notification.OrderExpertisePassedPartlyNotification.baseMessage.pluralPassedSingularRejected=We will now transfer the items for pre-sale care, pack your order and ship it to you. Please note that {0} {1} in your order has not passed the inspection. We will refund the money for part of the order to your card.
entity.notification.OrderExpertisePassedPartlyNotification.baseMessage.pluralPassedPluralRejected=We will now transfer the items for pre-sale care, pack your order and ship it to you. Please note that {0} {1} in your order have not passed the inspection. We will refund the money for part of the order to your card.
entity.notification.OrderExpertisePassedWithDefectNotification.title=Order No. {0} has passed inspection
entity.notification.OrderExpertisePassedWithDefectNotification.baseMessage.singular=The nuances have been approved. We will now transfer your item for pre-sale care, pack your order and send it to you.
entity.notification.OrderExpertisePassedWithDefectNotification.baseMessage.plural=The nuances have been approved. We will now transfer your items for pre-sale care, pack your order and send it to you.
entity.notification.OrderExpertisePassedWithDefectWithoutDiscountNotification.title=Order No. {0} has passed inspection
entity.notification.OrderExpertisePassedWithDefectWithoutDiscountNotification.baseMessage.singular=The nuances have been approved. We will now transfer your item for pre-sale care, pack your order and send it to you.
entity.notification.OrderExpertisePassedWithDefectWithoutDiscountNotification.baseMessage.plural=The nuances have been approved. We will now transfer your items for pre-sale care, pack your order and send it to you.
entity.notification.OrderExpertisePassedWithDefectMultipleNotification.title=Order No. {0} has passed inspection
entity.notification.OrderExpertisePassedWithDefectMultipleNotification.baseMessage=The nuances have been approved. We will now transfer your items for pre-sale care, pack your order and send it to you.
entity.notification.OrderHeldNotification.title=Order No. {0} has been placed
entity.notification.OrderHeldNotification.baseMessage=We have notified the seller of your purchase. Please await order confirmation.
entity.notification.OrderNeedConfirmationNotification.title=You have a new order ð
entity.notification.OrderNeedConfirmationNotification.isBoutiqueOrder.title=Confirm the shipment of the product.
entity.notification.OrderNeedConfirmationNotification.isOnlineBoutiqueOrder.title=Your product was sold on the platform\!
entity.notification.OrderNeedConfirmationNotification.baseMessage=Confirm it to complete the transaction
entity.notification.OrderNeedConfirmationNotification.isBoutiqueOrder.baseMessage=We are ready to pick up your product at the boutique. Please confirm the shipment in your private office.
entity.notification.OrderNeedConfirmationNotification.isOnlineBoutiqueOrder.baseMessage=You don't have to do anything, we will confirm the sale and ship the product ourselves as well as let you know when it is at the buyer's door.
entity.notification.OrderPickingUpFromOfficeNotification.title=Order No. {0} has been handed over to the courier service
entity.notification.OrderPickingUpFromOfficeNotification.baseMessage=The courier service will call you shortly, please wait. You can track the status of your shipment in your personal account.
entity.notification.OrderPickupDeclinedNotification.title=Order No. {0} has been canceled
entity.notification.OrderPickupDeclinedNotification.baseMessage=The seller did not hand the order over to the courier service. We will refund the money for the order to your card.
entity.notification.OrderRejectedNotification.title=Order No. {0} has been declined by the seller
entity.notification.OrderRejectedNotification.baseMessage=We will refund the money for the order to your card
entity.notification.SaleCompletedNotification.title=Order No. {0}. Payment processed.
entity.notification.SaleCompletedNotification.baseMessage=Expect your payout to reach your account
entity.notification.SaleConfirmedNotification.title=Order No. {0} has been confirmed
entity.notification.SaleConfirmedNotification.baseMessage.singular=Our courier service will pick up the item to bring it to the OSKELLY offices for inspection shortly. \nWe remind you that the item must be handed over in proper condition, in accordance with our rules.
entity.notification.SaleConfirmedNotification.baseMessage.plural=Our courier service will pick up the items to bring them to the OSKELLY offices for inspection shortly. \nWe remind you that the items must be handed over in proper condition, in accordance with our rules.
entity.notification.SaleConfirmedPartlyNotification.title=Order No. {0} is partially confirmed
entity.notification.SaleConfirmedPartlyNotification.baseMessage.singularConfirmedSingularRejected=Our courier service will pick up the item to bring it to the OSKELLY offices for inspection shortly. We remind you that the item must be handed over in proper condition, in accordance with our rules.\n\nâï¸Attention: {0} item is not confirmed and has been removed from sale. To avoid disappointments and order cancellation, try to update your listings regularly.
entity.notification.SaleConfirmedPartlyNotification.baseMessage.singularConfirmedPluralRejected=Our courier service will pick up the item to bring it to the OSKELLY offices for inspection shortly. We remind you that the item must be handed over in proper condition, in accordance with our rules.\n\nâï¸Attention: {0} items are not confirmed and have been removed from sale. To avoid disappointments and order cancellation, try to update your listings regularly.
entity.notification.SaleConfirmedPartlyNotification.baseMessage.pluralConfirmedSingularRejected=Our courier service will pick up the items to bring them to the OSKELLY offices for inspection shortly. We remind you that the items must be handed over in proper condition, in accordance with our rules.\n\nâï¸Attention: {0} item is not confirmed and has been removed from sale. To avoid disappointments and order cancellation, try to update your listings regularly.
entity.notification.SaleConfirmedPartlyNotification.baseMessage.pluralConfirmedPluralRejected=Our courier service will pick up the items to bring them to the OSKELLY offices for inspection shortly. We remind you that the items must be handed over in proper condition, in accordance with our rules.\n\nâï¸Attention: {0} items are not confirmed and have been removed from sale. To avoid disappointments and order cancellation, try to update your listings regularly.
entity.notification.SaleDeliveredToExpertiseNotification.title=Order No. {0} has been delivered to the OSKELLY offices
entity.notification.SaleDeliveredToExpertiseNotification.baseMessage.singular=We will begin the inspection process shortly. We will check the item for originality and compliance with the stated description.
entity.notification.SaleDeliveredToExpertiseNotification.baseMessage.plural=We will begin the inspection process shortly. We will check the items for originality and compliance with the stated description.
entity.notification.SaleDeliveringFromOfficeToBuyerNotification.title=Order No. {0} has been handed over to the courier service
entity.notification.SaleDeliveringFromOfficeToBuyerNotification.baseMessage=The courier service has picked up the order and will soon deliver it to the buyer
entity.notification.SaleDeliveringFromOfficeToBuyerNotification.isBoutiqueOrder.baseMessage=The courier has picked up the order and will deliver it to the boutique shortly
entity.notification.SaleDeliveringFromSellerToOfficeNotification.title=Order No. {0} has been sent for inspection to the OSKELLY offices
entity.notification.SaleDeliveringFromSellerToOfficeNotification.baseMessage=We will notify you when the order is submitted for inspection
entity.notification.SaleExpertiseFailedNotification.title=Order No. {0} hasn''t passed an examination 
entity.notification.SaleExpertiseFailedNotification.baseMessage=Our manager will contact you to arrange a return
entity.notification.SaleExpertiseFakeNotification.title=Order No. {0} did not pass inspection
entity.notification.SaleExpertiseFakeNotification.baseMessage=Our authentication team found that the item is not original. The details are in your personal account.
entity.notification.SaleExpertiseImpossibleDetermineAuthenticityNotification.title=Order No. {0} did not pass inspection
entity.notification.SaleExpertiseImpossibleDetermineAuthenticityNotification.baseMessage=Our authentication team was not able to verify 100% originality for the item. We will write to you within 24 hours to arrange a return.
entity.notification.SaleExpertiseReconciliationFailedNotification.title=Order No. {0} did not pass inspection
entity.notification.SaleExpertiseReconciliationFailedNotification.baseMessage=The nuances have not been approved. You can pick up the item yourself from our offices or arrange for a courier service to pick it up for you. The details are in your personal account.
entity.notification.SaleExpertiseFailedMultipleNotification.title=Order No. {0} did not pass inspection
entity.notification.SaleExpertiseFailedMultipleNotification.baseMessage=You can see detailed information in your personal account. We will contact you shortly to arrange the return of goods.
entity.notification.SaleExpertisePassedNotification.title=Order No. {0} has successfully passed inspection
entity.notification.SaleExpertisePassedNotification.baseMessage.singular=We will now transfer the item for pre-sale care, pack it and send it to the buyer
entity.notification.SaleExpertisePassedNotification.baseMessage.plural=We will now transfer the items for pre-sale care, pack them and send them to the buyer
entity.notification.SaleExpertisePassedNotification.isBoutiqueOrder.baseMessage=We will deliver the product to the boutique shortly
entity.notification.SaleExpertisePassedWithCleaningNotification.title=Order No. {0} has passed inspection, and dry cleaning has been carried out
entity.notification.SaleExpertisePassedWithCleaningNotification.baseMessage.singular=We deducted the cost from the item price
entity.notification.SaleExpertisePassedWithCleaningNotification.baseMessage.plural=We deducted the cost from the items price
entity.notification.SaleExpertisePassedWithDefectNotification.title=Order No. {0} has passed inspection
entity.notification.SaleExpertisePassedWithDefectNotification.baseMessage.singular=The nuances have been approved. We will now transfer your item for pre-sale care, pack your order and send it to the buyer.
entity.notification.SaleExpertisePassedWithDefectNotification.baseMessage.plural=The nuances have been approved. We will now transfer your items for pre-sale care, pack your order and send it to the buyer.
entity.notification.SaleExpertisePassedWithDefectNotification.isBoutiqueOrder.baseMessage=We will deliver the product to the boutique shortly. Please note that the buyer has been given a discount of {0} AED.
entity.notification.SaleExpertisePassedWithDefectWithoutDiscountNotification.title=Order No. {0} has passed inspection
entity.notification.SaleExpertisePassedWithDefectWithoutDiscountNotification.baseMessage.singular=The nuances have been approved. We will now transfer your item for pre-sale care, pack your order and send it to the buyer.
entity.notification.SaleExpertisePassedWithDefectWithoutDiscountNotification.baseMessage.plural=The nuances have been approved. We will now transfer your items for pre-sale care, pack your order and send it to the buyer.
entity.notification.SaleExpertisePassedWithDefectMultipleNotification.title=Order No. {0} has passed inspection
entity.notification.SaleExpertisePassedWithDefectMultipleNotification.baseMessage=The nuances have been approved. We will now transfer your items for pre-sale care, pack your order and send it to the buyer.
entity.notification.SalePickingUpFromSellerNotification.title=Order No. {0}\: the courier is on his way to you
entity.notification.SalePickupDeclinedNotification.baseMessage=You have broken the service rules by not submitting the order for examination. If this is an error, please let us <NAME_EMAIL> or by phone\: +971 (4) 608-64-88
entity.notification.SaleRejectedNotification.baseMessage=The product has been withdrawn from sale. \nTo avoid cancellations, please try to update your stock on time
entity.notification.QualityControlStartedSellerNotification.title=Order No. {0} is undergoing quality control
entity.notification.QualityControlStartedSellerNotification.baseMessage.singular=We are currently checking the item for compliance with the stated description
entity.notification.QualityControlStartedSellerNotification.baseMessage.plural=We are currently checking the items for compliance with the stated description
entity.notification.QualityControlStartedBuyerNotification.title=Order No. {0} is undergoing quality control
entity.notification.QualityControlStartedBuyerNotification.baseMessage.singular=We are currently checking the item in your order to confirm compliance with the description shared by the seller
entity.notification.QualityControlStartedBuyerNotification.baseMessage.plural=We are currently checking the items in your order to confirm compliance with the description shared by the seller
entity.notification.AuthenticationStartedSellerNotification.title=Order No. {0} is undergoing authentication
entity.notification.AuthenticationStartedSellerNotification.baseMessage.singular=We are currently verifying the authenticity of the item
entity.notification.AuthenticationStartedSellerNotification.baseMessage.plural=We are currently verifying the authenticity of the items
entity.notification.AuthenticationStartedBuyerNotification.title=Order No. {0} is undergoing authentication
entity.notification.AuthenticationStartedBuyerNotification.baseMessage.singular=We are currently checking the item in your order to confirm that it is 100% original item
entity.notification.AuthenticationStartedBuyerNotification.baseMessage.plural=We are currently checking the items in your order to confirm that they are 100% original items
entity.notification.DefectReconciliationStartedSellerNotification.title=Order No. {0} requires approval
entity.notification.DefectReconciliationStartedSellerNotification.baseMessage.singular=The item is original, but we found nuances that were not in the description. We will contact you shortly and discuss the details.
entity.notification.DefectReconciliationStartedSellerNotification.baseMessage.plural=The items are original, but we found nuances that were not in the description. We will contact you shortly and discuss the details.
entity.notification.DefectReconciliationStartedBuyerNotification.title=Order No. {0} requires approval
entity.notification.DefectReconciliationStartedBuyerNotification.baseMessage.singular=The item is original, but we found nuances that were not in the description shared by the seller. We will contact you shortly and discuss the details.
entity.notification.DefectReconciliationStartedBuyerNotification.baseMessage.plural=The items are original, but we found nuances that were not in the description shared by the seller. We will contact you shortly and discuss the details.
entity.notification.OrderCancelledByBuyerNotification.title=Order No. {0} has been canceled
entity.notification.OrderCancelledByBuyerNotification.baseMessage=Unfortunately, the order was canceled. We will contact you within 24 hours to discuss the situation.
entity.notification.SalePickingUpFromSellerNotification.baseMessage=The courier will contact you in advance
entity.notification.SalePickupDeclinedNotification.title=Order No. {0} has not been shipped
entity.notification.SalePickupDeclinedNotification.baseMessage.singular=The item has been removed from sale. To avoid disappointments and order cancellation, try to update your listings regularly.
entity.notification.SalePickupDeclinedNotification.baseMessage.plural=The items have been removed from sale. To avoid disappointments and order cancellation, try to update your listings regularly.
entity.notification.SaleRejectedNotification.title=Order No. {0} has been declined
entity.notification.SaleRejectedNotification.baseMessage.singular=The item has been removed from sale. To avoid disappointments and order cancellation, try to update your listings regularly.
entity.notification.SaleRejectedNotification.baseMessage.plural=The items have been removed from sale. To avoid disappointments and order cancellation, try to update your listings regularly.
entity.notification.ModerationFailedNotification.title.0=Insufficient product data
entity.notification.ModerationFailedNotification.title.1=Please adjust the price
entity.notification.ModerationFailedNotification.title.2=Please correct Images
entity.notification.ModerationFailedNotification.title.3=Insufficient Item Information
entity.notification.ModerationFailedNotification.title.4=Insufficient Item Information
entity.notification.ModerationFailedNotification.baseMessage.0=To place an item in the catalogue, follow the moderator's guidelines
entity.notification.ModerationFailedNotification.baseMessage.1=To place your item in the catalog, specify a more fair price or accept the moderator's suggestion
entity.notification.ModerationFailedNotification.baseMessage.2=To place your item in the catalog, upload high-resolution images
entity.notification.ModerationFailedNotification.baseMessage.3=To place your item in the catalog, follow the moderator's recommendations
entity.notification.ModerationFailedNotification.baseMessage.4=To place your item in the catalog, follow the moderator's recommendations
entity.notification.ModerationPassedNotification.title=Item Published
entity.notification.ModerationPassedNotification.baseMessage=Your listing is in the catalogue and is now visible to all OSKELLY users
entity.notification.ModerationRejectedNotification.title=Item has not passed OSKELLY authentication: Valentino shoes
entity.notification.ModerationRejectedNotification.baseMessage=Moderator's comment:
entity.notification.CompletePublicationNotification.title=Sell an item
entity.notification.CompletePublicationNotification.baseMessage=There are only a few steps left to complete your listing on OSKELLY and find a buyer >
entity.notification.CompletePublicationNotificationRangeDay.title=Don't put it off!
entity.notification.CompletePublicationNotificationRangeDay.baseMessage=List your item on OSKELLY in a few simple steps >
entity.notification.ProductSentToModerationNotification.title=Product uploaded and undergoing moderation
entity.notification.ProductSentToModerationNotification.baseMessage=Expect results within 48 hours. We check the size, condition, value and authenticity of photographs of your item
entity.notification.ProductSentToModerationNotificationAgain.title=The product has been sent for re-moderation
entity.notification.ProductSentToModerationNotificationAgain.baseMessage=Very soon your product will be available to all visitors to OSKELLY
entity.notification.CartPriceDecreasedNotification.title=â¡ï¸ The price is lowered\!
entity.notification.CartPriceDecreasedNotification.baseMessage=The price of the item in your shopping basket has dropped by {0} AED. Hurry up while the item is in stock >>.
entity.notification.LikePriceDecreasedNotification.title=â¡ï¸ The price is lowered\!
entity.notification.LikePriceDecreasedNotification.baseMessage=The price of the item from your favourites has dropped by {0} AED. Hurry up while the item is in stock >>.
entity.notification.PriceChangedNotification.baseMessage=The price of the item you are tracking has changed
entity.notification.SubscriptionPriceDecreasedNotification.baseMessage={0} has reduced the price by {1}\! Hurry up while the product is in stock >>
entity.notification.SubscribeCelebritiesNotification.title=â­ï¸ Celebrity Wardrobes
entity.notification.SubscribeCelebritiesNotification.baseMessage=The platform contains lots and wishlists of celebrities. Sign up and follow new arrivals on their pages.
entity.notification.SubscribeInstagramNotification.title=Receive bonuses on Instagram
entity.notification.SubscribeInstagramNotification.baseMessage=Follow the official Instagram account and join the community of OSKELLY's best friends. What are the benefits of best friends?
entity.notification.AddBirthdateAndAvatarNotification.title=Update your profile
entity.notification.AddBirthdateAndAvatarNotification.baseMessage=Fill in your date of birth so you don't miss out on your personalized discount\! And don't forget to upload your photo ð
entity.notification.BirthdayPromoCodeNotification.title=Happy birthday! ð
entity.notification.BirthdayPromoCodeNotification.baseMessage=We give you a {0}% discount on the promo code {1} from {2}. And {3}% on the {4} promo code for concierge from {5}. Valid for {6} {7}.
entity.notification.BirthdayPromoCodeNotification.days=day:days:days
entity.notification.AddBrandLikeNotification.title=Fill in your favourite brands
entity.notification.AddBrandLikeNotification.baseMessage=Specify your favourite brands so that we can suggest suitable models for you.
entity.notification.HowItWorksNotification.title=Welcome to OSKELLY!
entity.notification.HowItWorksNotification.baseMessage=We have written a short guide on how the platform works. It won't take you more than 1 minute to read it
entity.notification.UsePromocodeNotification.title=5% discount is waiting for you!
entity.notification.UsePromocodeNotification.baseMessage=Use promo code WELCOME5 on the first purchase of 10 000 p. and more.
entity.notification.WhyDoYouNeedLikes.title=Why do we need â¤ï¸
entity.notification.WhyDoYouNeedLikes.baseMessage=Why we need "Likes" on the platform and how to use them to the maximum.
entity.notification.WhyNeedAuthenticationNotification.title=Face-to-face authentication ð
entity.notification.WhyNeedAuthenticationNotification.baseMessage=Everything you wanted to know about authenticity expertise on our platform.
entity.notification.HowToUseBargainNotification.title=Bargain is appropriate!
entity.notification.HowToUseBargainNotification.baseMessage=How the bargaining feature works and why it's necessary not only for buyers, but also for sellers.
entity.notification.NoActivityNotification.title=Time for decisive action\!
entity.notification.NoActivityNotification.baseMessage=Itâs time to place your first order\! Over 100,000 products with discounts from leading brands\!
entity.notification.SupportMessageNotification.title=Message from customer support
entity.notification.SupportMessageNotification.baseMessage=The support representative replied to you in the chat: please open the chat and read their message
entity.notification.ProfileBirthdateNotification.baseMessage=Enter your date of birth
entity.notification.TrustedSetNotification.title=Congratulations\! You have been awarded trust status\!
entity.notification.RegisterNotification.title=Discount on first order\!
entity.notification.RegisterNotification.baseMessage=Get a 5% discount with promo code WELCOME5 on your first purchase.
entity.notification.CanCreateStreamNotification.title=Now you can go live\! 
entity.notification.CanCreateStreamNotification.baseMessage=Congratulations, you've reached 50\n50 published items and 10successful sales so now you can host live-streams on ourplatform
entity.notification.StreamCreateNotification.baseMessage=Will host a live-stream
entity.notification.StreamFinishNotification.baseMessage=Cancelled the live-stream \n on the theme\:\n{0} please update the app so you don''t miss future broadcasts.
entity.notification.StreamStartNotification.baseMessage=Already is on air on the theme\:\n{0}
entity.notification.StreamCreateNotification.messageFormat=Will hold a live-stream \n$dm_date in $hm_time\n on the theme\:\n{0}
entity.notification.StreamFinishNotification.messageFormat=Cancelled a live-stream \n$dm_date in $hm_timeon the theme\:\n{0}
entity.notification.StreamStartNotification.messageFormat=Already is on air on the theme\:\n{0}
entity.notification.StreamFiveMinutesReminderNotification.title=You can go live\!
entity.notification.StreamFiveMinutesReminderNotification.baseMessage=You can start a live-stream on the theme\:\n{0}.\nYour viewers are ready
entity.notification.StreamOneHourReminderNotification.title=1 hour left until the live-stream\!
entity.notification.StreamOneHourReminderNotification.baseMessage=One hour left to start a live-stream on the theme\:\n{0}.\nPrepare everything in advance so that you can go on air on time.
entity.notification.BargainBanNotification.baseMessage=You have broken the rules of the service and can no longer use the bidding tool\: \n{0}
entity.notification.CancelBargainBanNotification.title=The bidding ban has been lifted
entity.notification.CancelBargainBanNotification.baseMessage=Don't break the service rules and use a full package of advantages on our platform
entity.notification.CancelCommentBanNotification.title=The ban on comments has been lifted
entity.notification.CancelCommentBanNotification.baseMessage=Don't break the service rules and use a full package of advantages on our platform
entity.notification.CancelPublishBanNotification.title=The publication ban has been lifted
entity.notification.CancelPublishBanNotification.baseMessage=Don't break the service rules and use a full package of advantages on our platform
entity.notification.CancelPublishStoriesBanNotification.title=The ban on posting stories has been lifted
entity.notification.CancelPublishStoriesBanNotification.baseMessage=Don't break the service rules and use a full package of advantages on our platform
entity.notification.CancelStreamBanNotification.title=The ban on live broadcasts has been lifted
entity.notification.CancelStreamBanNotification.baseMessage=Don't break the service rules and use a full package of advantages on our platform
entity.notification.CancelUserBanNotification.title=Your profile is unlocked
entity.notification.CancelUserBanNotification.baseMessage=Don't break the service rules and use a full package of advantages on our platform
entity.notification.CancelOSocialPostBanNotification.title=The ban on publishing posts has been lifted
entity.notification.CancelOSocialPostBanNotification.baseMessage=Don't break the service rules and use a full package of advantages on our platform
entity.notification.CancelOSocialCommentBanNotification.title=The ban on comments on O!Trends has been lifted
entity.notification.CancelOSocialCommentBanNotification.baseMessage=Don't break the service rules and use a full package of advantages on our platform
entity.notification.CommentBanNotification.baseMessage=You have broken the rules of the service and now you can''t leave comments under items anymore\: \n{0}
entity.notification.PublishBanNotification.baseMessage=You have broken the rules of the service and now you cannot sell items anymore\: \n{0}
entity.notification.PublishStoriesBanNotification.baseMessage=You have broken the rules of the service and now you can''t post stories anymore\: \n{0}
entity.notification.StreamBanNotification.baseMessage=You have broken the rules of the service and can no longer use the live streaming tool\: \n{0}
entity.notification.UserBanNotification.baseMessage=You have broken the rules of the service, your profile is temporarily blocked\: \n{0}
entity.notification.WarningBanNotification.baseMessage=You are breaking the rules of the service and risk being banned\:\n{0}
entity.notification.OSocialPostBanNotification.baseMessage=You have broken the rules of the service and now you can''t publish posts anymore\: \n{0}
entity.notification.OSocialCommentBanNotification.baseMessage=You have broken the rules of the service and now you can''t leave comments anymore\: \n{0}
entity.notification.SaleExpertisePassedPartlyNotification.title=Order No. {0} has partially passed inspection
entity.notification.NewPriceUpdateSubscriptionNotification.title=Someone is interested in your product\!
entity.notification.ProductPublishedNotification.title=New arrivals\!
entity.notification.ProductPublishedNotification.baseMessage={0} just published{1} an item\: {2}
entity.notification.ProductsPublishedNotification.baseMessage={0} just posted{1} {2}
entity.notification.ExclusiveSelectionAvailableProductNotification.title=Available for purchase
entity.notification.ExclusiveSelectionAvailableProductNotification.baseMessage=Hurry up to place an order for the lot from your favorites: {0}
entity.notification.ExclusiveSelectionSellerProductNotification.title=Exclusive Selection
entity.notification.ExclusiveSelectionSellerProductNotification.baseMessage=Congratulations! Your product {0} has been included in the Exclusive selection section and will receive more attention from customers
entity.notification.PrivateSellerProductPublishedNotification.baseMessage={0} just published{1} an item: {2}
entity.notification.PrivateSellerProductsPublishedNotification.baseMessage={0} just published{1} {2} + {3} more... See all âº
entity.notification.SetLowerPriceForSeveralProductsNotification.title=ð¡ How to sell faster
entity.notification.SetLowerPriceForSeveralProductsNotification.baseMessage=Try to lower the price of the product to attract more customers.
entity.notification.SetLowerPriceNotification.title=ð¡ How to sell faster 
entity.notification.SetLowerPriceNotification.baseMessage=Try to lower the price of the product to attract more customers.
entity.notification.NewPriceUpdateSubscriptionNotification.baseMessage={0} has signed{1} to reduce the price of your goods. Try to lower the price to sell the product faster.
entity.notification.SaleExpertisePassedPartlyNotification.baseMessage.singularPassedSingularRejected=We will now transfer the item for pre-sale care, pack the order and send it to the buyer.\n\n{0} {1} did not pass the inspection. We will contact you shortly to arrange the return of goods that have not passed the inspection.
entity.notification.SaleExpertisePassedPartlyNotification.baseMessage.singularPassedPluralRejected=We will now transfer the item for pre-sale care, pack the order and send it to the buyer.\n\n{0} {1} did not pass the inspection. We will contact you shortly to arrange the return of goods that have not passed the inspection.
entity.notification.SaleExpertisePassedPartlyNotification.baseMessage.pluralPassedSingularRejected=We will now transfer the items for pre-sale care, pack the order and send it to the buyer.\n\n{0} {1} did not pass the inspection. We will contact you shortly to arrange the return of goods that have not passed the inspection.
entity.notification.SaleExpertisePassedPartlyNotification.baseMessage.pluralPassedPluralRejected=We will now transfer the items for pre-sale care, pack the order and send it to the buyer.\n\n{0} {1} did not pass the inspection. We will contact you shortly to arrange the return of goods that have not passed the inspection.
entity.notification.SaleExpertisePassedPartlyNotification.isBoutiqueOrder.baseMessage=We will deliver the product to the boutique shortly.{nThe examination was not passed by the product {0} {Our manager will contact you to arrange the return of the product that have not passed the examination.
entity.notification.WishlistItemSold.title=The wishlist item was sold ð
entity.notification.WishlistItemSold.baseMessage={0} from your Wishlist have been bought. Look for similar offers or order from Concierge.
entity.notification.HotWishlistItem.title=The item from your wishlist is popular ð¥
entity.notification.HotWishlistItem.baseMessage=An item from your Wishlist is often added to favourites. Don't miss it\!
entity.notification.WishlistItemActiveBargain.title=It's time for action\!
entity.notification.WishlistItemActiveBargain.baseMessage=Another user has offered a price for an item from your Wishlist. Hurry up and place your order before anyone else\!
entity.notification.StoryLikeNotification.title=Someone is interested in your story
entity.notification.StoryLikeNotification.baseMessage={0} added your story to the wishlist
entity.notification.LikedProductStoryNotification.baseMessage=A story was added for an item from your Wishlist
entity.notification.CartProductStoryNotification.baseMessage=A story was added for an item from your cart
entity.notification.ProductNoStoryNotification.title=Speed up the sale
entity.notification.ProductNoStoryNotification.baseMessage.oneProduct=Try posting a stylish story so that more users pay attention to your product
entity.notification.ProductNoStoryNotification.baseMessage.manyProducts=Try posting stylish stories to get more users to pay attention to your products
entity.notification.NewProductResponseNotification.title=New Response
entity.notification.NewProductResponseNotification.baseMessage=Someone just responded to a comment on the {0} item on your favourites list
entity.notification.InformAboutProductRequestNotification.title=Looking for An Item
entity.notification.InformAboutProductRequestNotification.baseMessage=Buy or Sell items quick and easy using OSKELLY's 'Looking for An Item' feature
entity.notification.NewProductRequestCommentForUserHowLikedItNotification.title=New Comment
entity.notification.NewProductRequestCommentForUserHowLikedItNotification.baseMessage=Someone just commented on the {0} item on your favourites list
entity.notification.ReplayProductRequestCommentNotification.title=You have a new comment
entity.notification.ReplayProductRequestCommentNotification.baseMessage={0} has responded to your comment on the {1} listing. Head over to OSKELLY to reply and keep the conversation going! 
entity.notification.UpdateProductRequestNotification.title=The listing {0} has been edited
entity.notification.UpdateProductRequestNotification.baseMessage={0} edited the listing {1} to which you responded.
entity.notification.MotivateCreateProductResponseNotification.title=Selling on OSKELLY yet?
entity.notification.MotivateCreateProductResponseNotification.baseMessage=See what other users are looking for in the "Looking for Items" section and offer them your products.
entity.notification.InformAboutCountOfUserWhoCreateProductRequestNotification.title={0} {1} are currently searching for products!
entity.notification.InformAboutCountOfUserWhoCreateProductRequestNotification.baseMessage=Offer your products to users who are ready and motivated to make a purchase.
entity.notification.ProductRequestNewCommentNotification.title=Looking for An Item: New Comment
entity.notification.ProductRequestNewCommentNotification.baseMessage={0} commented on your 'Looking for An Item' listing. Could this be 'the one' you've been looking for? Head over to OSKELLY to find out! Don't forget to respond to comments quickly.
entity.notification.NeedPublishProductRequest.title=No time to search?
entity.notification.NeedPublishProductRequest.baseMessage=Buy through the "Looking for a product" function, where you will be offered products from users
entity.notification.RejectedProductRequestNotification.title=Product request {0} failed moderation
entity.notification.RejectedProductRequestNotification.baseMessage=Edit it with moderator comments and re-publish: {0}
entity.notification.NewBuyerProductResponseNotification.title=New product response
entity.notification.NewBuyerProductResponseNotification.baseMessage={0} has offered {1} an item in your listing. Go to the item and buy it quickly if the offer is right for you!
entity.notification.NewProductRequestLikeNotification.title=Your product request is interested in
entity.notification.NewProductRequestLikeNotification.baseMessage={0} added your product request {1} to Favorites
entity.notification.NoResponseProductRequestNotification.title={0} items that might interest you
entity.notification.NoResponseProductRequestNotification.baseMessage=We found a few items that match your {0} listing. Check them out
entity.notification.SimilarProductRequestsFoundNotification.title={0} product requests that may be of interest to you
entity.notification.SimilarProductRequestsFoundNotification.baseMessage=We found product requests that match your product, {0}. Offer your product to potential buyers and sell faster
entity.notification.SaleRequestCreatedNotification.title=Sale request created
entity.notification.SaleRequestCreatedNotification.baseMessage=We will contact you via WhatsApp within 48 hours
entity.notification.SaleRequestInProgressNowNotification.title=Sale request in progress
entity.notification.SaleRequestInProgressNowNotification.baseMessage=The sale request is being processed by the manager, we will contact you via WhatsApp to clarify the details
entity.notification.SaleRequestConfirmedNotification.title=Sale request confirmed
entity.notification.SaleRequestConfirmedNotification.baseMessage=An order for shipment of goods has been created, confirm the shipment of goods to Oskelly
entity.notification.SaleRequestDeclinedNotification.title=Sale request declined
entity.notification.SaleRequestDeclinedNotification.baseMessage=Unfortunately, we will not be able to take your goods into our warehouse
entity.notification.CommunityStatusDowngradedNotification.title=Status in the O!Community has become lower
entity.notification.CommunityStatusDowngradedNotification.baseMessage=Now it''s {0}. Buy and sell more often to get more benefits
entity.notification.CommunityStatusUpgradedNotification.title=New status in the O!Community
entity.notification.CommunityStatusUpgradedNotification.baseMessage=You have received status {0}. Now you have more privileges available to you
entity.notification.CommunityStatusMayBeLostNotification.title=Save your status in the O!Community
entity.notification.CommunityStatusMayBeLostNotification.baseMessage=To avoid losing your status {0}, complete{1} before {2}
entity.notification.CommunityStatusLostNotification.title=You have left the O!Community program
entity.notification.CommunityStatusLostNotification.baseMessage=To get privileges again, sell or buy at least one product
entity.notification.CommunityTitleReceivedNotification.title=New title in the O!Community
entity.notification.CommunityTitleReceivedNotification.baseMessage=You have got the title {0}! Thank you for being with us!
entity.notification.ProductPriceDiscountNotification.title=Price discount for {0}%
entity.notification.ProductPriceDiscountNotification.baseMessage=Product was published more than {0} {1} ago. According to contract we made price discount â it will help to find buyer quicker.
entity.notification.ProductPriceDiscountNotification.chronoUnit.DAYS=days
entity.notification.ProductPriceDiscountNotification.chronoUnit.WEEKS=weeks
entity.notification.ProductPriceDiscountNotification.chronoUnit.MONTHS=months
entity.notification.ProductPriceDiscountNotification.chronoUnit.HOURS=hours
entity.notification.ProductPriceDiscountNotification.chronoUnit.MINUTES=minutes
entity.notification.OSocialCommentCreatedNotification.title=New Comment
entity.notification.OSocialCommentCreatedNotification.withText.baseMessage={0}: â{1}â
entity.notification.OSocialCommentCreatedNotification.withoutText.baseMessage={0} added a photo in a comment
entity.notification.OSocialPostCommentNotification.title=New Comment
entity.notification.OSocialPostCommentNotification.withText.baseMessage={0}: â{1}â
entity.notification.OSocialPostCommentNotification.withoutText.baseMessage={0} added a photo in a comment
entity.notification.OSocialProductUsedAtPostNotification.title={0}
entity.notification.OSocialProductUsedAtPostNotification.baseMessage=Check out the post featuring your item
entity.notification.OSocialUserMentionedAtPostNotification.title=You were tagged
entity.notification.OSocialUserMentionedAtPostNotification.baseMessage={0} tagged you in a post
entity.notification.OSocialTitledPostNotification.title={0} published post
entity.notification.OSocialPostWasReviewedAndPublishedNotification.title=Post Published
entity.notification.OSocialPostWasReviewedAndPublishedNotification.baseMessage=We've reviewed and restored your post. Thanks for staying with us!
entity.notification.OSocialPostWasReviewedAndRestoredNotification.title=Post Published
entity.notification.OSocialPostWasReviewedAndRestoredNotification.baseMessage=We've reviewed and restored your post. Thanks for staying with us!
entity.notification.OSocialPostViolatedRulesAndWasHiddenNotification.title=Post Hidden
entity.notification.OSocialPostViolatedRulesAndWasHiddenNotification.baseMessage=Your post didnât meet our guidelines. Please review them or reach out to support
entity.notification.OSocialPostLikeNotification.title={0}
entity.notification.OSocialPostLikeNotification.baseMessage=Liked your post
entity.notification.OSocialLikedProductUsedAtPostNotification.title={0}
entity.notification.OSocialLikedProductUsedAtPostNotification.baseMessage=Check out a new post with an item from your favorites
entity.notification.BonusesTransferredNotification.title=+{0} Bonuses!
entity.notification.BonusesTransferredNotification.withOrder.baseMessage=Added up bonuses for purchase no {1}. Wanna spend it?
entity.notification.BonusesTransferredNotification.withoutOrder.baseMessage=Bonuses from OSKELLY have been added to your account. Thank you for staying with us!
entity.notification.BonusesReturnedNotification.title=+{0} Bonuses are with you again!
entity.notification.BonusesReturnedNotification.baseMessage=Bonuses for purchase no {1} have been refunded. Thank you for staying with us!
entity.notification.BonusesExpiringNotification.title={0} Bonuses will burn out soon!
entity.notification.BonusesExpiringNotification.baseMessage=There are two weeks left to spend the Bonuses. Don't miss the chance to use them!
entity.notification.bonuses.default.stage1.title={0} Bonuses will burn out soon!
entity.notification.bonuses.default.stage1.baseMessage=â° There are two weeks left to spend {0} Bonuses. Don't miss the chance to use them!
entity.notification.bonuses.default.stage2.title={0} Bonuses will burn out soon!
entity.notification.bonuses.default.stage2.baseMessage=â° There are 3 days left to spend {0} Bonuses. Don't miss the chance to use them!
entity.notification.bonuses.welcome.title=Bonuses will burn out soon!
entity.notification.bonuses.welcome.baseMessage=Your bonuses {0} will burn out soon. Take advantage of them quickly.
entity.notification.bonuses.welcome.initial.title=Welcome ti the club ð
entity.notification.bonuses.welcome.initial.baseMessage=You're with us, and that's a reason for a gift. Points are awarded! Are you ready to choose your fashion find?
entity.notification.bonuses.welcome.stage1.title=Your scores are getting boring ð
entity.notification.bonuses.welcome.stage1.baseMessage=There are 14 days left to apply the welcome points. Please yourself!
entity.notification.bonuses.welcome.stage2.title=There are 3 days left â°
entity.notification.bonuses.welcome.stage2.baseMessage=Your welcome points will burn up soon. Have time to apply!
entity.notification.bonuses.birthday.stage1.title=Bonuses in honor of your day! â¨
entity.notification.bonuses.birthday.stage1.baseMessage=In honor of the birthday â {0} bonuses are already yours! Have time to spend them on your favorite brands.
entity.notification.bonuses.birthday.stage2.title=Happy Birthday! ð
entity.notification.bonuses.birthday.stage2.baseMessage=There are 22 days left to use your bonuses. Treat yourself!
entity.notification.bonuses.birthday.stage3.title=The bonuses will disappear soonâ¦
entity.notification.bonuses.birthday.stage3.baseMessage=Only 3 days to use the gift bonuses. May the holiday last longer!
entity.notification.LoyaltyStatusUpgradedAutoNotification.title=New status in O!Loyalty
entity.notification.LoyaltyStatusUpgradedAutoNotification.baseMessage=You have received the status {0}. More privileges are now available to you.
entity.notification.LoyaltyStatusDowngradedAutoNotification.title=Status in O!Loyalty has become lower
entity.notification.LoyaltyStatusDowngradedAutoNotification.baseMessage=Now {0}. Make purchases more often to get more privileges.
entity.notification.LoyaltyStatusSetByAdminNotification.limited.title=You have been assigned a status in O!Loyalty
entity.notification.LoyaltyStatusSetByAdminNotification.limited.baseMessage=You have received the status {0} to {1}. Hurry up to find out what privileges are now available to you.
entity.notification.LoyaltyStatusSetByAdminNotification.unlimited.title=You have been assigned a status in O!Loyalty
entity.notification.LoyaltyStatusSetByAdminNotification.unlimited.baseMessage=You have received the status {0}. Hurry up to find out what privileges are now available to you.
entity.notification.UserDescriptionDeclinedNotification.title=Update the profile description
entity.order.delivery=Delivery
cart.items.delivery=Delivery
entity.enum.OrderCreationProblem.minimum-amount-not-reached=The minimum sum has not been reached
entity.enum.OrderCreationProblem.CANT_DELIVERY_ITEM_TO_FOREIGN_COUNTRY=This category of items can be delivered only in Russia. If you are in Russia, please check your address.
entity.enum.OrderCreationProblem.CANT_CONTAIN_MULTIPLE_ORDER_POSITIONS=Order cannot contain multiple order positions.
entity.enum.OrderCreationProblem.CANT_BE_CONCIERGE=Order cannot be concierge.
entity.enum.OrderCreationProblem.CANT_BE_BOUTIQUE=Order cannot be boutique.
entity.enum.OrderEvent.DEAL_INITIATED.buyerTitle=Awaiting the confirmation.
entity.enum.OrderEvent.CONFIRMED.buyerTitle=Order confirmed
entity.enum.OrderEvent.CONFIRMED.sellerTitle=Sale confirmed
entity.enum.OrderEvent.CONFIRMED.proSellerTitle=Sale confirmed
entity.enum.OrderEvent.CONFIRMED_PARTLY.buyerTitle=Order confirmed
entity.enum.OrderEvent.CONFIRMED_PARTLY.sellerTitle=Sale confirmed partially
entity.enum.OrderEvent.CONFIRMED_PARTLY.proSellerTitle=Sale confirmed partially
entity.enum.OrderEvent.SALE_REJECTED.buyerTitle=Order rejected
entity.enum.OrderEvent.SALE_REJECTED.sellerTitle=Sale rejected
entity.enum.OrderEvent.SALE_REJECTED.proSellerTitle=Sale rejected
entity.enum.OrderEvent.PICKING_UP_FROM_SELLER.buyerTitle=Order confirmed
entity.enum.OrderEvent.PICKING_UP_FROM_SELLER.sellerTitle=Sale confirmed
entity.enum.OrderEvent.PICKING_UP_FROM_SELLER.proSellerTitle=Sale confirmed
entity.enum.OrderEvent.FROM_SELLER_TO_OFFICE.buyerTitle=The order has been handed over to the Delivery Service
entity.enum.OrderEvent.FROM_SELLER_TO_OFFICE.sellerTitle=The order has been handed over to the Delivery Service
entity.enum.OrderEvent.FROM_SELLER_TO_OFFICE.proSellerTitle=The order has been handed over to the Delivery Service
entity.enum.OrderEvent.PICKUP_DECLINED.buyerTitle=The order has not been shipped by the seller
entity.enum.OrderEvent.PICKUP_DECLINED.sellerTitle=The order has not been shipped by the seller
entity.enum.OrderEvent.PICKUP_DECLINED.proSellerTitle=The order has not been shipped by the seller
entity.enum.OrderEvent.DELIVERED_TO_EXPERTISE.buyerTitle=The order arrived for the Expertise
entity.enum.OrderEvent.DELIVERED_TO_EXPERTISE.sellerTitle=The order arrived for the Expertise
entity.enum.OrderEvent.DELIVERED_TO_EXPERTISE.proSellerTitle=The order arrived for the Expertise
entity.enum.OrderEvent.EXPERTISE_PASSED.buyerTitle=The order undergone the expertise successfully
entity.enum.OrderEvent.EXPERTISE_PASSED.sellerTitle=The order undergone the expertise successfully
entity.enum.OrderEvent.EXPERTISE_PASSED.proSellerTitle=The order undergone the expertise successfully
entity.enum.OrderEvent.EXPERTISE_PASSED_WITH_DEFECT.buyerTitle=The order undergone the expertise, there are some defects
entity.enum.OrderEvent.EXPERTISE_PASSED_WITH_DEFECT.sellerTitle=The order undergone the expertise, there are some defects
entity.enum.OrderEvent.EXPERTISE_PASSED_WITH_DEFECT.proSellerTitle=The order undergone the expertise, there are some defects
entity.enum.OrderEvent.EXPERTISE_PASSED_PARTLY.buyerTitle=The order undergone the expertise partially
entity.enum.OrderEvent.EXPERTISE_PASSED_PARTLY.sellerTitle=The order undergone the expertise partially
entity.enum.OrderEvent.EXPERTISE_PASSED_PARTLY.proSellerTitle=The order undergone the expertise partially
entity.enum.OrderEvent.EXPERTISE_PASSED_WITH_CLEANING.buyerTitle=The order undergone the expertise successfully
entity.enum.OrderEvent.EXPERTISE_PASSED_WITH_CLEANING.sellerTitle=The order undergone the expertise successfully, the item was dry cleaned
entity.enum.OrderEvent.EXPERTISE_PASSED_WITH_CLEANING.proSellerTitle=The order undergone the expertise successfully, the item was dry cleaned
entity.enum.OrderEvent.EXPERTISE_FAILED.buyerTitle=The order was refused during expertise process
entity.enum.OrderEvent.EXPERTISE_FAILED.sellerTitle=The order was refused during expertise process
entity.enum.OrderEvent.EXPERTISE_FAILED.proSellerTitle=The order was refused during expertise process
entity.enum.OrderEvent.PICKING_UP_FROM_OFFICE.buyerTitle=The item has been handed over to the courier
entity.enum.OrderEvent.PICKING_UP_FROM_OFFICE.sellerTitle=The item has been handed over to the Delivery Service
entity.enum.OrderEvent.PICKING_UP_FROM_OFFICE.proSellerTitle=The item has been handed over to the Delivery Service
entity.enum.OrderEvent.FROM_OFFICE_TO_BUYER.buyerTitle=The item has been handed over to the Delivery Service
entity.enum.OrderEvent.FROM_OFFICE_TO_BUYER.sellerTitle=The item has been handed over to the Delivery Service
entity.enum.OrderEvent.FROM_OFFICE_TO_BUYER.proSellerTitle=The item has been handed over to the Delivery Service
entity.enum.OrderEvent.DELIVERED_TO_BUYER.buyerTitle=Order delivered
entity.enum.OrderEvent.DELIVERED_TO_BUYER.sellerTitle=Order delivered
entity.enum.OrderEvent.DELIVERED_TO_BUYER.proSellerTitle=Order delivered
entity.enum.OrderEvent.DELIVERED_TO_BUYER_EXPECTING_CONFIRM_AGENT_REPORT.buyerTitle=Order delivered
entity.enum.OrderEvent.DELIVERED_TO_BUYER_EXPECTING_CONFIRM_AGENT_REPORT.sellerTitle=Order delivered
entity.enum.OrderEvent.DELIVERED_TO_BUYER_EXPECTING_CONFIRM_AGENT_REPORT.proSellerTitle=Order delivered
entity.enum.OrderEvent.AGENT_REPORT_CONFIRMED.buyerTitle=Order delivered
entity.enum.OrderEvent.AGENT_REPORT_CONFIRMED.sellerTitle=Order delivered
entity.enum.OrderEvent.AGENT_REPORT_CONFIRMED.proSellerTitle=Order delivered
entity.enum.OrderEvent.ORDER_RETURN.buyerTitle=Return
entity.enum.OrderEvent.ORDER_RETURN.sellerTitle=Return
entity.enum.OrderEvent.ORDER_RETURN.proSellerTitle=Return
entity.enum.OrderEvent.ORDER_COMPLETED.buyerTitle=Order delivered
entity.enum.OrderEvent.ORDER_COMPLETED.sellerTitle=Order delivered
entity.enum.OrderEvent.ORDER_COMPLETED.proSellerTitle=Order delivered
entity.enum.OrderPositionMetadata.MONEY_RETURNED=Money returned
entity.enum.OrderPositionMetadata.RETURN=Return
entity.enum.OrderPositionMetadata.product-delivery=Item delivered
entity.enum.OrderPositionMetadata.profit=Profit
entity.enum.OrderPositionMetadata.money-paid=Money paid
entity.enum.OrderPositionState.INITIAL.description=Initial condition of the item in order
entity.enum.OrderPositionState.PURCHASE_REQUEST.description=The buyer paid for the item as part of the order
entity.enum.OrderPositionState.SALE_CONFIRMED.description=Item confirmed
entity.enum.OrderPositionState.SALE_REJECTED.description=Item not confirmed
entity.enum.OrderPositionState.PICKUP_DECLINED.description=Item is not shipped
entity.enum.OrderPositionState.HQ_WAREHOUSE.description=In Oskelly stock
entity.enum.OrderPositionState.ON_VERIFICATION.description=On Expertise
entity.enum.OrderPositionState.VERIFICATION_OK.description=Expertise completed
entity.enum.OrderPositionState.VERIFICATION_NEED_CLEANING.description=Dry cleaning required
entity.enum.OrderPositionState.VERIFICATION_BAD_STATE.description=There are some defects
entity.enum.OrderPositionState.REJECTED_AFTER_VERIFICATION.description=Expertise failed
entity.enum.OrderPositionState.READY_TO_SHIP.description=Created Consignment note for delivery to the buyer
entity.enum.OrderPositionState.CREATE_WAYBILL_TO_BUYER.description=Courier confirmed, but has not arrived yet
entity.enum.OrderPositionState.SHIPPED_TO_CLIENT.description=Delivery to the Buyer
entity.enum.OrderPositionState.REQUESTED_TO_RETURN.description=Return request submitted
entity.enum.OrderPositionState.RETURN_ACCEPTED.description=Accepted for Return
entity.enum.OrderPositionState.RETURN_DECLINED.description=Rejected for Return
entity.enum.OrderPositionState.RETURN_VERIFICATION_OK.description=Expertise is approved
entity.enum.OrderPositionState.RETURN_VERIFICATION_REJECTED.description=Expertise is rejected
entity.enum.OrderRefundReason.UNDEFINED.description=All
entity.enum.OrderRefundReason.NOT_CONFIRMED.description=Not confirmed
entity.enum.OrderRefundReason.NOT_SHIPPED.description=Not shipped
entity.enum.OrderRefundReason.NOT_PASS_EXPERTISE.description=Was refused during expertise process
entity.enum.OrderStatus.UNDEFINED.title=All
entity.enum.OrderStatus.UNCOMPLETED.title=
entity.enum.OrderStatus.ORDER_CANT_CONFIRM_NO_SELLER_ADDRESS.title=Order could not be confirmed, there is no address
entity.enum.OrderStatus.ORDER_CONFIRMING.title=The order is on confirmation by the seller
entity.enum.OrderStatus.ORDER_REFUND.title=Refund
entity.enum.OrderStatus.ORDER_CONFIRMED.title=Sale confirmed (unidentified address)
entity.enum.OrderStatus.CONCIERGE_ITEMS_WAITING_CONFIRMATION.title=ðï¸ The order is on confirmation by the seller
entity.enum.OrderStatus.SELLER_IN_MOSCOW.title=The item is in Moscow (select delivery method)
entity.enum.OrderStatus.EXPECTING_COURIER_TO_SELLER.title=Waiting for a courier to pick up the goods from the seller
entity.enum.OrderStatus.OURSELVES_PICKING_UP_FROM_SELLER.title=Picking up from the seller ourselves
entity.enum.OrderStatus.OURSELVES_FROM_SELLER_TO_OFFICE.title=We carry the goods from the seller to the office
entity.enum.OrderStatus.LOGIST_ON_WAY_TO_SELLER.title=Logistician is on the way to the seller
entity.enum.OrderStatus.FROM_SELLER_TO_OFFICE.title=Logistician carries the item from the seller to the office
entity.enum.OrderStatus.HAS_CONCIERGE_ITEMS.title=ðï¸ The concierge takes the goods to the office
entity.enum.OrderStatus.EXPERTISE_START.title=Expertise process started
entity.enum.OrderStatus.EXPERTISE_COMPLETED.title=Expertise completed
entity.enum.OrderStatus.CHOOSING_DELIVERY_METHOD_O2B.title=Choosing delivery method
entity.enum.OrderStatus.HOLD_COMPLETE_REJECTED.title=Money unhold
entity.enum.OrderStatus.EXPECTING_COURIER_TO_BUYER.title=Waiting for a courier to deliver the item to the buyer
entity.enum.OrderStatus.LOGIST_ON_WAY_TO_BUYER.title=Courier is on the way to the buyer
entity.enum.OrderStatus.BUYER_IN_MOSCOW.title=The item is in Moscow (select delivery method)
entity.enum.OrderStatus.OURSELVES_DELIVERY_TO_BUYER.title=We deliver to the buyer ourselves
entity.enum.OrderStatus.OURSELVES_FROM_OFFICE_TO_BUYER.title=On the way to the buyer
entity.enum.OrderStatus.ORDER_DELIVERED.title=Order delivered
entity.enum.OrderStatus.HAS_DISPUTE.title=There is a dispute
entity.enum.OrderStatus.ORDER_IN_BOUTIQUE.title=Item from the boutique
entity.enum.OrderStatus.ORDER_SOLD_IN_BOUTIQUE.title=Sold in a boutique
entity.enum.OrderStatus.EXPECTING_CONFIRM_AGENT_REPORT.title=Waiting for the confirmation of agent's report
entity.enum.OrderStatus.WAIT_PAYMENT_MONEY_TO_SELLER.title=Report confirmed, pending funds transfer
entity.enum.OrderStatus.ORDER_COMPLETED.title=Order completed (money paid out)
entity.enum.OrderStatus.ORDER_COMPLETED_RETURN.title=Order completed (return)
entity.enum.OrderStatus.RETURN_CREATED.title=Return request submitted
entity.enum.OrderStatus.RETURN_ON_WAY_TO_OFFICE.title=On the way to the office
entity.enum.OrderStatus.RETURN_EXPERTISE.title=Expertise
entity.enum.OrderStatus.RETURN_COMPLETED.title=Return completed
entity.enum.OrderStatus.BOUTIQUE_ORDER_ON_WAY_TO_OFFICE.title=Orders on the way to the office
entity.enum.OrderStatus.BOUTIQUE_ORDER_ON_EXPERTISE.title=The order arrived for the Expertise
entity.enum.OrderStatus.BOUTIQUE_ORDER_ON_WAY_TO_BOUTIQUE.title=Orders on the way to the boutique
entity.enum.OrderStatus.BOUTIQUE_ORDER_IN_BOUTIQUE.title=Orders (goods) in a boutique
entity.enum.OrderStatus.BOUTIQUE_ORDER_SOLD_IN_BOUTIQUE.title=Sold in a boutique (payments)
entity.enum.OrderStatus.BOUTIQUE_ORDER_ONLINE_CONFIRM.title=Online orders\: confirmation
entity.enum.OrderStatus.BOUTIQUE_ORDER_ONLINE_PICKUP.title=Online Orders\: Shipment
entity.enum.SizeType.RU.description=Russian
entity.enum.SizeType.AGE.description=Age
entity.enum.SizeType.RING_RUSSIAN.description=Russian ring size â diameter in millimeters
entity.enum.SizeType.HEIGHT.description=Height
entity.enum.SizeType.RING_EUROPEAN.description=European ring size â circumference in millimeters
entity.enum.SizeType.JEANS.description=Jeans size
entity.enum.SizeType.INCHES.description=Inches
entity.enum.SizeType.COLLAR_CENTIMETERS.description=Collar in centimeters
entity.enum.SizeType.COLLAR_INCHES.description=Collar in inches
entity.enum.SizeType.CENTIMETERS.description=Centimeters
entity.enum.SizeType.CENTIMETERS.abbreviation=Cm
entity.enum.SizeType.HEIGHT.abbreviation=Ht
entity.enum.SizeType.AGE.abbreviation=A
entity.enum.SizeType.ONE_SIZE.abbreviation=One
entity.enum.SizeType.ONE_SIZE.description=One Size
entity.enum.SizeType.BUST.abbreviation=Bust
entity.enum.SizeType.BUST.description=Bust size
entity.enum.SizeType.JEANS.abbreviation=Jeans
entity.enum.SizeType.RING_EUROPEAN.abbreviation=EU
entity.enum.SizeType.COLLAR_CENTIMETERS.abbreviation=Collar Cm
entity.enum.SizeType.RING_RUSSIAN.abbreviation=RU
entity.enum.SizeType.COLLAR_INCHES.abbreviation=Collar In
entity.enum.SizeType.INCHES.abbreviation=In
entity.enum.SizeType.RU.abbreviation=RU
entity.enum.SizeType.EU.abbreviation=EU
entity.enum.SizeType.US.abbreviation=US
entity.enum.SizeType.INT.abbreviation=INT
entity.enum.SizeType.UK.abbreviation=UK
entity.enum.SizeType.FR.abbreviation=FR
entity.enum.SizeType.IT.abbreviation=IT
entity.enum.SizeType.DE.abbreviation=DE
entity.enum.SizeType.AU.abbreviation=AU
entity.enum.SizeType.JPN.abbreviation=JPN
entity.enum.SizeType.EU.description=European
entity.enum.SizeType.US.description=American
entity.enum.SizeType.INT.description=International
entity.enum.SizeType.UK.description=English
entity.enum.SizeType.FR.description=French
entity.enum.SizeType.IT.description=Italian
entity.enum.SizeType.DE.description=German
entity.enum.SizeType.AU.description=Australian
entity.enum.SizeType.JPN.description=Japanese
entity.enum.Sex.MALE.description=Men
entity.enum.Sex.FEMALE.description=Women
entity.enum.Sex.BOY.description=Boy
entity.enum.Sex.GIRL.description=Girl
entity.enum.Sex.ADULT.description=Adult (unisex)
entity.enum.Sex.CHILD.description=Child (unisex)
entity.enum.Role.SIMPLE_USER.role=Individual seller
entity.enum.Role.BOUTIQUE.role=Boutique
entity.order.promocode=Promotional code
entity.order.certificate=Gift Certificate

entity.enum.IdFilterParam.PROBLEMS_WITH_BANK.description=Problems with the bank
entity.enum.IdFilterParam.PROBLEMS_WITH_ADDRESS.description=Problems with the bank

entity.enum.BaseType.WOMAN.baseName=Female
entity.enum.BaseType.MAN.baseName=Male
entity.enum.BaseType.KIDS.baseName=Kids

entity.enum.BannerType.BANNER_1=BanÃ©r 1
entity.enum.BannerType.BANNER_2=BanÃ©r 2
entity.enum.BannerType.BANNER_3=BanÃ©r 3
entity.enum.BannerType.BANNER_4=BanÃ©r 4
entity.enum.BannerType.BANNER_ACTUAL_1=Relevant Banner 1
entity.enum.BannerType.BANNER_ACTUAL_2=Relevant Banner 2
entity.enum.BannerType.BANNER_ACTUAL_3=Relevant Banner 3

entity.enum.BannerDirectType.BANNER_MONTH=Brand of the Month
entity.enum.BannerDirectType.BANNER_BRAND=Brands
entity.enum.BannerDirectType.BANNER_CAT1=Clothing
entity.enum.BannerDirectType.BANNER_CAT2=Shoes
entity.enum.BannerDirectType.BANNER_CAT3=Satchels
entity.enum.BannerDirectType.BANNER_CAT4=Accessories

entity.enum.IdFilterParam.WITHOUT_RRP=Without RRP price
entity.enum.IdFilterParam.WITH_RRP=With RRP price

entity.enum.DateFilterParam.ONE_MONTH.description=Published 1 month ago
entity.enum.DateFilterParam.TWO_MONTH.description=Published 2 months ago
entity.enum.DateFilterParam.THREE_MONTH.description=Published 3 month ago
entity.enum.DateFilterParam.SIX_MONTH.description=Published 6 months ago
entity.enum.DateFilterParam.NINE_MONTH.description=Published 9 months ago
entity.enum.DateFilterParam.TWELVE_MONTH.description=Published 12 months ago
entity.enum.DateFilterParam.EIGHTEEN_MONTH.description=Published 18 months ago
entity.enum.DateFilterParam.TWENTY_FOUR_MONTH.description=Published 24 month ago

entity.enum.DocumentType.CERTIFICATE.descriptionPattern=Product certificate %s
entity.enum.DocumentType.OSKELLY_WAYBILL_FROM_SELLER.descriptionPattern=OSKELLY invoice - from the seller
entity.enum.DocumentType.OSKELLY_WAYBILL_TO_BUYER.descriptionPattern=OSKELLY invoice - to the buyer
entity.enum.DocumentType.TK_WAYBILL_FROM_SELLER.descriptionPattern=From the seller in OSKELLY - invoice
entity.enum.DocumentType.TK_WAYBILL_TO_BUYER.descriptionPattern=From OSKELLY to the buyer - invoice
entity.enum.DocumentType.TK_STICKERS_TO_BUYER.descriptionPattern=From OSKELLY to the buyer - stickers
entity.enum.DocumentType.AGENT_REPORT.descriptionPattern=Sale Report
entity.enum.DocumentType.DOC_INVOICE_TAX_DELIVERY_EN.descriptionPattern=Tax invoice\: delivery (en)
entity.enum.DocumentType.DOC_INVOICE_TAX_GOODS_EN.descriptionPattern=Tax invoice\: goods (en)
entity.enum.DocumentType.DOC_AGENT_REPORT_EN.descriptionPattern=Sale report (en)
entity.enum.DocumentType.DOC_ORDER_LABELS.descriptionPattern=Product labels
entity.enum.DocumentType.DOC_ORDER_ARAMEX_S2O_INVOICE.descriptionPattern=Aramex invoice (S2O)
entity.enum.DocumentType.DOC_ORDER_ARAMEX_O2B_INVOICE.descriptionPattern=Aramex invoice (B2O)
entity.enum.DocumentType.DOC_ORDER_INVOICE.descriptionPattern=Order invoice
entity.enum.DocumentType.DOC_BOUTIQUE_CONTRACT.descriptionPattern=Boutique contract



entity.enum.OrderStepDTO.Type.DISABLED=Inactive
entity.enum.OrderStepDTO.Type.WAITING=Waiting
entity.enum.OrderStepDTO.Type.COMPLETE=Finished
entity.enum.OrderStepDTO.Type.FAILED=Problem Found
entity.enum.OrderStepDTO.Type.CONFIRMATION=Order Confirm
entity.enum.OrderStepDTO.Type.EXPERTISE=Expertise
entity.enum.OrderStepDTO.Type.DELIVERING=Courier is on the way to the buyer

entity.enum.ReturnStepDTO.DISABLED=Inactive
entity.enum.ReturnStepDTO.WAITING=Waiting
entity.enum.ReturnStepDTO.COMPLETE=Finished
entity.enum.ReturnStepDTO.FAILED=Problem Found

entity.enum.CounterpartyDTO.CounterpartyType.PHYS=Individual
entity.enum.CounterpartyDTO.CounterpartyType.IP=Sole trader
entity.enum.CounterpartyDTO.CounterpartyType.JUR=Legal entity
entity.enum.CounterpartyDTO.CounterpartyType.CARD=Credit Card
entity.enum.CounterpartyDTO.CounterpartyType.BONUS_12_STOREEZ=12Storeez Bonuses
entity.enum.CounterpartyDTO.CounterpartyType.INTERNATIONAL=Account for international transfers - Individual
entity.enum.CounterpartyDTO.CounterpartyType.INTERNATIONAL_LEGAL_ENTITY=Account for international transfers - Legal entity

entity.enum.SaleRejectionReasonType.CHANGE_DECISION.appDisplayName=Changed my mind about the sale
entity.enum.SaleRejectionReasonType.SOLD_ON_OTHER_PLATFORM.appDisplayName=Sold on another platform
entity.enum.SaleRejectionReasonType.GOING_TO_CHANGE_PRICE.appDisplayName=Want to change the price
entity.enum.SaleRejectionReasonType.PRODUCT_CONDITION_CHANGED.appDisplayName=Product condition has changed
entity.enum.SaleRejectionReasonType.OTHER.appDisplayName=Other

entity.enum.SaleRejectionReasonType.CHANGE_DECISION.adminDisplayName=Seller changed his mind about the sale
entity.enum.SaleRejectionReasonType.SOLD_ON_OTHER_PLATFORM.adminDisplayName=Seller sold on another platform
entity.enum.SaleRejectionReasonType.GOING_TO_CHANGE_PRICE.adminDisplayName=Seller wants to change the price
entity.enum.SaleRejectionReasonType.PRODUCT_CONDITION_CHANGED.adminDisplayName=The product does not match the ad
entity.enum.SaleRejectionReasonType.OTHER.adminDisplayName=Other

entity.enum.SaleRejectionReasonType.CAN_NOT_SEND.adminDisplayName=Seller cannot send at this time
entity.enum.SaleRejectionReasonType.DONT_ANSWER.adminDisplayName=Cannot contact the seller

entity.enum.ContentBlockType.BANNER=Banner
entity.enum.ContentBlockType.COLLECTION=Collection
entity.enum.ContentBlockType.ADDITIONAL_COLLECTION_1=Additional collection 1
entity.enum.ContentBlockType.ADDITIONAL_COLLECTION_2=Additional collection 2
entity.enum.ContentBlockType.BLOG=The OSKELLY Blog
entity.enum.ContentBlockType.TEXT_SLIDES=Text slides
entity.enum.ContentBlockType.BEST_SELLERS=Best sellers
entity.enum.ContentBlockType.SELECTED_PRODUCT=Selected product
entity.enum.ContentBlockType.STORIES=Stories
entity.enum.ContentBlockType.STOREEZ=Storeez feed
entity.enum.ContentBlockType.INSTAGRAM_FEED=Instagram feed
entity.enum.ContentBlockType.SHELF=Shelf
entity.enum.ContentBlockType.FILTERABLE_SHELF=Shelf with brands
entity.enum.ContentBlockType.VERTICAL_COLLECTION=Vertical collection
entity.enum.ContentBlockType.HORIZONTAL_COLLECTION=Horizontal collection
entity.enum.ContentBlockType.BUTTON_CONTROL=Button control
entity.enum.ContentBlockType.TEXT_CONTROL=Text control
entity.enum.ContentBlockType.OSOCIAL_POSTS_COLLECTION=Shelf with posts
entity.enum.ContentBlockType.SPACER=Spacer
entity.enum.ContentBlockType.ORDERS=Active orders
entity.enum.ContentBlockType.PROMO_AUTO_COLLECTION=Promo auto collection

exception.AdministrationException.item-not-found=Item not found\: {0}
exception.AdministrationException.current-price-must-not-be-null=Current price must present
exception.AdministrationException.seller-price-must-not-be-null=Seller price must present
exception.AdministrationException.current-price-must-be-grater-than-seller=Current price must be grater than seller price


exception.MethodiusException.document-generate-fail=Document generate fail

controller.pdf.OskWaybill.Waybill=Consignment note â
controller.pdf.OskWaybill.Sender=Sender
controller.pdf.OskWaybill.Receiver=Recipient

controller.pdf.WaybillPeopleInfoBlockTable.Title=Name\:
controller.pdf.WaybillPeopleInfoBlockTable.FullName=Full Name\:
controller.pdf.WaybillPeopleInfoBlockTable.City=City\:
controller.pdf.WaybillPeopleInfoBlockTable.Address=Address\:

controller.pdf.WaybillProductsBlockTable.DepartureDescription=Shipment description
controller.pdf.WaybillProductsBlockTable.OrderId=Order ID\:
controller.pdf.WaybillProductsBlockTable.Item=Item

controller.pdf.WaybillConfirmBlockTable.Accepted=Accepted
controller.pdf.WaybillConfirmBlockTable.AcceptedAndSigned=Confirmation and signature of the sender
controller.pdf.WaybillConfirmBlockTable.AgreedWithDeliveryConditions=I agree with the terms of delivery.
controller.pdf.WaybillConfirmBlockTable.Delegate=Attorney-in-fact
controller.pdf.WaybillConfirmBlockTable.Date=Date\:
controller.pdf.WaybillConfirmBlockTable.Sign=Signature\:

controller.pdf.AgentReport.PhysUserContractTextStart=, Ð¸Ð¼ÐµÐ½ÑÐµÐ¼Ð¾Ðµ(ÑÐ¹) Ð² Ð´Ð°Ð»ÑÐ½ÐµÐ¹ÑÐµÐ¼ Â«ÐÑÐ¸Ð½ÑÐ¸Ð¿Ð°Ð»Â», Ñ Ð¾Ð´Ð½Ð¾Ð¹ ÑÑÐ¾ÑÐ¾Ð½Ñ, Ð¸ ÐÐ±ÑÐµÑÑÐ²Ð¾ Ñ Ð¾Ð³ÑÐ°Ð½Ð¸ÑÐµÐ½Ð½Ð¾Ð¹ Ð¾ÑÐ²ÐµÑÑÑÐ²ÐµÐ½Ð½Ð¾ÑÑÑÑ Â«ÐÐ¡ÐÐÐÐ ÐÐ Ð£ÐÐÂ», Ð¸Ð¼ÐµÐ½ÑÐµÐ¼Ð¾Ðµ Ð² Ð´Ð°Ð»ÑÐ½ÐµÐ¹ÑÐµÐ¼ Â«ÐÐ³ÐµÐ½ÑÂ», Ð² Ð»Ð¸ÑÐµ ÐÐµÐ½ÐµÑÐ°Ð»ÑÐ½Ð¾Ð³Ð¾ Ð´Ð¸ÑÐµÐºÑÐ¾ÑÐ° ÐÑÐºÐ°Ð½Ð¾Ð²Ð° Ð.Ð., Ð´ÐµÐ¹ÑÑÐ²ÑÑÑÐµÐ³Ð¾ Ð½Ð° Ð¾ÑÐ½Ð¾Ð²Ð°Ð½Ð¸Ð¸ Ð£ÑÑÐ°Ð²Ð°, Ñ Ð´ÑÑÐ³Ð¾Ð¹ ÑÑÐ¾ÑÐ¾Ð½Ñ, ÑÐ¾Ð²Ð¼ÐµÑÑÐ½Ð¾ Ð¸Ð¼ÐµÐ½ÑÐµÐ¼ÑÐµ Â«Ð¡ÑÐ¾ÑÐ¾Ð½ÑÂ», ÑÐ¾ÑÑÐ°Ð²Ð¸Ð»Ð¸ ÐÑÑÐµÑ Ð¾ Ð½Ð¸Ð¶ÐµÑÐ»ÐµÐ´ÑÑÑÐµÐ¼\:
controller.pdf.AgentReport.LegalEntityContractTextStart={0} (ÐÐÐ {1}), Ð¸Ð¼ÐµÐ½ÑÐµÐ¼Ð¾Ðµ(ÑÐ¹) Ð² Ð´Ð°Ð»ÑÐ½ÐµÐ¹ÑÐµÐ¼ Â«ÐÑÐ¸Ð½ÑÐ¸Ð¿Ð°Ð»Â», Ñ Ð¾Ð´Ð½Ð¾Ð¹ ÑÑÐ¾ÑÐ¾Ð½Ñ, Ð¸ ÐÐ±ÑÐµÑÑÐ²Ð¾ Ñ Ð¾Ð³ÑÐ°Ð½Ð¸ÑÐµÐ½Ð½Ð¾Ð¹ Ð¾ÑÐ²ÐµÑÑÑÐ²ÐµÐ½Ð½Ð¾ÑÑÑÑ Â«ÐÐ¡ÐÐÐÐ ÐÐ Ð£ÐÐÂ» (ÐÐÐ\:77149663399) , Ð¸Ð¼ÐµÐ½ÑÐµÐ¼Ð¾Ðµ Ð² Ð´Ð°Ð»ÑÐ½ÐµÐ¹ÑÐµÐ¼ Â«ÐÐ³ÐµÐ½ÑÂ», Ð² Ð»Ð¸ÑÐµ ÐÐµÐ½ÐµÑÐ°Ð»ÑÐ½Ð¾Ð³Ð¾ Ð´Ð¸ÑÐµÐºÑÐ¾ÑÐ° ÐÑÐºÐ°Ð½Ð¾Ð²Ð° Ð.Ð., Ð´ÐµÐ¹ÑÑÐ²ÑÑÑÐµÐ³Ð¾ Ð½Ð° Ð¾ÑÐ½Ð¾Ð²Ð°Ð½Ð¸Ð¸ Ð£ÑÑÐ°Ð²Ð°, Ñ Ð´ÑÑÐ³Ð¾Ð¹ ÑÑÐ¾ÑÐ¾Ð½Ñ, ÑÐ¾Ð²Ð¼ÐµÑÑÐ½Ð¾ Ð¸Ð¼ÐµÐ½ÑÐµÐ¼ÑÐµ Â«Ð¡ÑÐ¾ÑÐ¾Ð½ÑÂ», ÑÐ¾ÑÑÐ°Ð²Ð¸Ð»Ð¸ ÐÑÑÐµÑ Ð¾ Ð½Ð¸Ð¶ÐµÑÐ»ÐµÐ´ÑÑÑÐµÐ¼\:
controller.pdf.AgentReport.PhysUserContractTextEnd=1.  Ð ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ð¸ Ñ Ð°ÐºÑÐµÐ¿ÑÐ¾Ð¼ ÐÑÐ¸Ð½ÑÐ¸Ð¿Ð°Ð»Ð¾Ð¼ ÐÐ³ÐµÐ½ÑÑÐºÐ¾Ð³Ð¾ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ° (Ð¿ÑÐ±Ð»Ð¸ÑÐ½Ð¾Ð¹ Ð¾ÑÐµÑÑÑ) Ð¾Ñ Â«08Â» Ð¸ÑÐ»Ñ 2020 Ð³Ð¾Ð´Ð° ÐÐ³ÐµÐ½ÑÐ¾Ð¼ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ñ, Ð° ÐÑÐ¸Ð½ÑÐ¸Ð¿Ð°Ð»Ð¾Ð¼ Ð¿ÑÐ¸Ð½ÑÑÑ ÑÐ»ÐµÐ´ÑÑÑÐ¸Ðµ Ð¿Ð¾ÑÑÑÐµÐ½Ð¸Ñ\:
controller.pdf.AgentReport.LegalEntityContractTextEnd="1.  Ð ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ð¸ Ñ Ð°ÐºÑÐµÐ¿ÑÐ¾Ð¼ ÐÑÐ¸Ð½ÑÐ¸Ð¿Ð°Ð»Ð¾Ð¼ ÐÐ³ÐµÐ½ÑÑÐºÐ¾Ð³Ð¾ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÐ° â  {0} Ð¾Ñ Â«{1}Â» {2} {3} Ð³Ð¾Ð´Ð° ÐÐ³ÐµÐ½ÑÐ¾Ð¼ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ñ, Ð° ÐÑÐ¸Ð½ÑÐ¸Ð¿Ð°Ð»Ð¾Ð¼ Ð¿ÑÐ¸Ð½ÑÑÑ ÑÐ»ÐµÐ´ÑÑÑÐ¸Ðµ Ð¿Ð¾ÑÑÑÐµÐ½Ð¸Ñ\:"
controller.pdf.AgentReport.Date=Date
controller.pdf.AgentReport.ItemTitle=Product Name
controller.pdf.AgentReport.ItemCost=Price of the item, ()
controller.pdf.AgentReport.FeePercent=The amount of remuneration, (%)
controller.pdf.AgentReport.FeeAmount=The amount of remuneration, ()
controller.pdf.AgentReport.Total=Total\:
controller.pdf.AgentReport.FeeInfo=ÐÐ¾Ð·Ð½Ð°Ð³ÑÐ°Ð¶Ð´ÐµÐ½Ð¸Ðµ ÐÐ³ÐµÐ½ÑÐ° ÑÐ¾ÑÑÐ°Ð²Ð¸Ð»Ð¾ {0} ÑÑÐ±Ð»ÐµÐ¹ ({1}%), ÐÐÐ¡ Ð½Ðµ Ð¾Ð±Ð»Ð°Ð³Ð°ÐµÑÑÑ.
controller.pdf.AgentReport.Expense=2. Ð Ð°ÑÑÐ¾Ð´Ñ, Ð¿ÑÐ¾Ð¸Ð·Ð²ÐµÐ´ÐµÐ½Ð½ÑÐµ ÐÐ³ÐµÐ½ÑÐ¾Ð¼ Ð¸ Ð¿Ð¾Ð´Ð»ÐµÐ¶Ð°ÑÐ¸Ðµ Ð²Ð¾Ð·Ð¼ÐµÑÐµÐ½Ð¸Ñ (Ð²Ð¾Ð·Ð¼ÐµÑÐµÐ½Ð½ÑÐµ) ÐÑÐ¸Ð½ÑÐ¸Ð¿Ð°Ð»Ð¾Ð¼, ÑÐ¾ÑÑÐ°Ð²Ð¸Ð»Ð¸\:
controller.pdf.AgentReport.ExpenseTitle=Expenditures name
controller.pdf.AgentReport.CostOfService=Cost of services, ()
controller.pdf.AgentReport.CompanyProviderTitle=Provider Organization
controller.pdf.AgentReport.DryCleaning=Dry Cleaning for the item
controller.pdf.AgentReport.ServiceTotalPrice="Cost of services, ()"
controller.pdf.AgentReport.Dealed=.  ÐÑÑÐµÐ¿ÐµÑÐµÑÐ¸ÑÐ»ÐµÐ½Ð½ÑÐµ Ð¿Ð¾ÑÑÑÐµÐ½Ð¸Ñ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ñ Ð² Ð¿Ð¾Ð»Ð½Ð¾Ð¼ Ð¾Ð±ÑÐµÐ¼Ðµ, ÐÑÐ¸Ð½ÑÐ¸Ð¿Ð°Ð» Ð¿ÑÐµÑÐµÐ½Ð·Ð¸Ð¹ Ð¿Ð¾ Ð¾Ð±ÑÐµÐ¼Ñ, ÐºÐ°ÑÐµÑÑÐ²Ñ Ð¸ ÑÑÐ¾ÐºÐ°Ð¼ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð¸Ñ Ð¿Ð¾ÑÑÑÐµÐ½Ð¸Ð¹ Ð½Ðµ Ð¸Ð¼ÐµÐµÑ.
controller.pdf.AgentReport.TotalToBePaid=Total payable price\: {0} "

controller.AdminReportsController.Yes=Yes
controller.AdminReportsController.No=No
controller.AdminReportsController.PhysPerson=Ð¤Ð¸Ð·. Ð»Ð¸ÑÐ¾
controller.AdminReportsController.LegalEntity=Boutique

controller.AdminReportsController.Phone=Phone Number
controller.AdminReportsController.AmountOfPaidOrders=Amount of paid orders
controller.AdminReportsController.NumberOfPaidOrders=Number of paid orders
controller.AdminReportsController.Date=Date
controller.AdminReportsController.IdOrder=Order ID
controller.AdminReportsController.IdUser=User ID
controller.AdminReportsController.IsSeller=Is a Seller
controller.AdminReportsController.NewUser=New User
controller.AdminReportsController.OrderPrice=Price of the order ()
controller.AdminReportsController.NumberOfItemsInOrder=Number of items in the order
controller.AdminReportsController.Accepted=Confirmed

controller.AdminReportsController.IdItem=Item ID
controller.AdminReportsController.IsBuyer=Is a buyer"
controller.AdminReportsController.SellerType=Seller Type
controller.AdminReportsController.ItemPrice=Price of the item ()
controller.AdminReportsController.ItemsAvailableForSale=Number of items available for sale

controller.AdminReportsController.SincePublicationDay=Number of days since publication

controller.AccountControllerApiV2.AccessDenied=Access is denied

controller.AdminControllerAdvice.Error=Error

controller.AdminOrdersController.OrderWrongState=Order with ID {0} has invalid delivery state

controller.DiscountApi.UserNotFound=User not found

exception.DefaultAdminCategoryService.ItemNotFound=Item not found\: {0}

controller.AdminProductsController.ProductCategorySuccessfullySet=Item category successfully set

controller.AdminPromoCodesController.Limitless=Indefinite
controller.AdminPromoCodesController.From=From
controller.AdminPromoCodesController.To=To
controller.AdminPromoCodesController.PromocodeCreated=Promotional code created
controller.AdminPromoCodesController.PromocodeUpdated=Promotional code updated
controller.AdminPromoCodesController.PromocodeDeleted=Promotional code deleted

controller.AccountAddressControllerApiV2.CountryNotFound=Country not found

controller.AccountControllerApiV2.Account=Account
controller.AccountControllerApiV2.UserWithAuthInfo=User with authentication information
controller.AccountControllerApiV2.Authorization=Authorization
controller.AccountControllerApiV2.RegistrationFormValidation=Registration form validation
controller.AccountControllerApiV2.InvalidRegistrationForm=Invalid registration form
controller.AccountControllerApiV2.AccountUpdated=Account updated
controller.AccountControllerApiV2.Counterparty=Counterparty
controller.AccountControllerApiV2.BindCardReference=Link to connect the bank card
controller.AccountControllerApiV2.DeletedCounterparty=Deleted counterparty
controller.AccountControllerApiV2.AddressPoint=Location
controller.AccountControllerApiV2.DeletedAddressPoint=Deleted location
controller.AccountControllerApiV2.CheckNicknameFreeResult=Nickname availability check result
controller.AccountControllerApiV2.CheckEmailFreeResult=E-mail availability check result
controller.AccountControllerApiV2.Sex=User gender
controller.AccountControllerApiV2.SetSex=User gender is set
controller.AccountControllerApiV2.PushNotificationGroups=Push notification groups
controller.AccountControllerApiV2.SetPushNotificationGroups=Push notification groups set
controller.AccountControllerApiV2.SubscribtionsIds=List of user IDs I'm following
controller.AccountControllerApiV2.UserRegistered=User registered
controller.AccountControllerApiV2.ResetPassword=Password reset email sent
controller.AccountControllerApiV2.SetPassword=Password is set
controller.AccountControllerApiV2.PasswordChanged=Password is changed
controller.AccountControllerApiV2.AvatarChanged=Avatar changed
controller.AccountControllerApiV2.FileUploaded=File uploaded
controller.AccountControllerApiV2.FileInfo=File information
controller.AccountControllerApiV2.UserFilesPage=User Files Page
controller.AccountControllerApiV2.Subscription=Subscription
controller.AccountControllerApiV2.Subscribed=Subscribed successfully
controller.AccountControllerApiV2.PreFilledRegisterFormWithOptions=Pre-filled registration form and registration flags

controller.AccountCredsControllerApiV2.AccountInfo=Account bank details

controller.AddressControllerApiV2.Regions=Regions
controller.AddressControllerApiV2.Cities=Cities
controller.AddressControllerApiV2.BigCities=Million-plus cities
controller.AddressControllerApiV2.GetSearchHistory=Address search history
controller.AddressControllerApiV2.AddSearchHistory=The result of adding an address to the search history

controller.CallbackControllerApiV2.AppleUserUpdateInfo=Received information about updating the user's apple account
controller.CallbackControllerApiV2.CardBinderByCounterparty=The card is linked according to counterparty

controller.CartControllerApiV2.Cart=Shopping bag
controller.CartControllerApiV2.SimpleCart=Shopping cart in a simple form
controller.CartControllerApiV2.PositionAdded=Item added
controller.CartControllerApiV2.PositionUpdated=Item edited
controller.CartControllerApiV2.PositionDeletedAndMovedToFavourites=Item removed and moved to favorites
controller.CartControllerApiV2.DeliveryAddressEndpoint=Delivery location
controller.CartControllerApiV2.SetDeliveryAddressEndpoint=Delivery location is set
controller.CartControllerApiV2.BuyerCounterparty=Buyer's counterparty
controller.CartControllerApiV2.SetBuyerCounterparty=Counterparty for buyer is set
controller.CartControllerApiV2.DeliveryComment=Comment for delivery
controller.CartControllerApiV2.SetDeliveryComment=Comment for delivery is set
controller.CartControllerApiV2.HoldMoney=Order payment initiated
controller.CartControllerApiV2.CheckPromocode=The result of applying the promotional code

controller.CatalogControllerApiV2.Catalog=Catalog
controller.CatalogControllerApiV2.MenuContent=Catalog menu
controller.CatalogControllerApiV2.ItemsIDs=Items ID
controller.CatalogControllerApiV2.AvailableFilters=Available filters
controller.CatalogControllerApiV2.SizeTree=Sizes Tree
controller.CatalogControllerApiV2.Sizes=Sizes
controller.CatalogControllerApiV2.AttributesTree=Attribute Tree
controller.CatalogControllerApiV2.AvailableColors=Available colors
controller.CatalogControllerApiV2.AvailableMaterials=Available materials
controller.CatalogControllerApiV2.CategoryTree=Category Tree
controller.CatalogControllerApiV2.AvailableSubcategories=Available subcategories
controller.CatalogControllerApiV2.LikedProducts=Favourite Items
controller.CatalogControllerApiV2.LikedProductsPage=Page of favourite items
controller.CatalogControllerApiV2.UsersLikedProducts=Favourite Items of the user
controller.CatalogControllerApiV2.UsersLikedProductsPage=Page of user's favourite items
controller.CatalogControllerApiV2.LastSeenProducts=Recently viewed items
controller.CatalogControllerApiV2.LastSeenProductsPage=Viewed items
controller.CatalogControllerApiV2.FollowingProducts=Tracked Items
controller.CatalogControllerApiV2.YouAreSubscribedToPriceChanges=You subscribed to price tracking of the item
controller.CatalogControllerApiV2.YouCoudntSubscribeToPriceChanges=Unable to track price changes of the item
controller.CatalogControllerApiV2.YouAreUnsubscribedToPriceChanges=You unsubscribed from price tracking of the item
controller.CatalogControllerApiV2.YouCoudntUnsubscribeToPriceChanges=Unable to unsibscribe from price tracking of the item
controller.CatalogControllerApiV2.ProductWasLiked=Like added to the item
controller.CatalogControllerApiV2.ProductCouldntBeLiked=Unable to add a Like to the product
controller.CatalogControllerApiV2.DislikeProduct=Like deleted from the item
controller.CatalogControllerApiV2.CouldntDislikeProduct=Unable to unlike a product
controller.CatalogControllerApiV2.ToggleLikeProduct=Like inverted
controller.CatalogControllerApiV2.TopBrands=TOP Brands
controller.CatalogControllerApiV2.Brands=Brands
controller.CatalogControllerApiV2.Brand=Brand
controller.CatalogControllerApiV2.LikeBrand=Like added to the Brand
controller.CatalogControllerApiV2.CouldntLikeBrand=Unable to add a Like to the Brand
controller.CatalogControllerApiV2.DislikeBrand=Like deleted from the brand
controller.CatalogControllerApiV2.CouldntDislikeBrand=Unable to unlike a product
controller.CatalogControllerApiV2.LikedBrands=Favourite Brands
controller.CatalogControllerApiV2.UserLikedBrands=Favourite Brands of the user

controller.DeeplinkControllerApiV2.ProcessLinkResult=Link processing result

controller.NotificationControllerApiV2.Notifications=Notifications
controller.NotificationControllerApiV2.Notification=Notifications
controller.NotificationControllerApiV2.CommentsNotifications=Comment notifications
controller.NotificationControllerApiV2.NoCommentsNotifications=Notifications except for comments
controller.NotificationControllerApiV2.NotificationCounters=Counter notifications
controller.NotificationControllerApiV2.SetBirthdate=Enter your birth date

controller.ProductPublicationControllerApiV2.Drafts=List of Drafts
controller.ProductPublicationControllerApiV2.DraftsPage=Draft page
controller.ProductPublicationControllerApiV2.Rejects=List of rejected items
controller.ProductPublicationControllerApiV2.RejectsPage=Page of rejected items
controller.ProductPublicationControllerApiV2.SecondEditionProducts=List of items sent for second editing
controller.ProductPublicationControllerApiV2.SecondEditionProductsPage=Page of items sent for second editing
controller.ProductPublicationControllerApiV2.ModeratingProducts=List of items on moderation
controller.ProductPublicationControllerApiV2.ModeratingProductsPage=Page of items on moderation
controller.ProductPublicationControllerApiV2.Products=List of Items
controller.ProductPublicationControllerApiV2.ProductsPage=Product Page
controller.ProductPublicationControllerApiV2.ProductsCount=Quantity of items
controller.ProductPublicationControllerApiV2.ProductDraft=Draft
controller.ProductPublicationControllerApiV2.ProductAdded=Item added
controller.ProductPublicationControllerApiV2.ProductOnModeration=Your item has been sent for the moderation
controller.ProductPublicationControllerApiV2.ProductEdited=Item edited
controller.ProductPublicationControllerApiV2.ProductDeleted=Item deleted
controller.ProductPublicationControllerApiV2.ImageAdded=Image added
controller.ProductPublicationControllerApiV2.ImageDeleted=Image deleted
controller.ProductPublicationControllerApiV2.CommentUpdated=Comment updated
controller.ProductPublicationControllerApiV2.Comission=Current user's commission
controller.ProductPublicationControllerApiV2.PriceWithCommission=Price with Commission
controller.ProductPublicationControllerApiV2.PriceWithoutCommission=Price after commission
controller.ProductPublicationControllerApiV2.Conversion=Price conversion
controller.ProductPublicationControllerApiV2.Sizes=Category sizes
controller.ProductPublicationControllerApiV2.AdditionalSizesForCategory=Additional category sizes
controller.ProductPublicationControllerApiV2.Categories=Categories
controller.ProductPublicationControllerApiV2.CategoryTree=Category Tree
controller.ProductPublicationControllerApiV2.Brands=Brands
controller.ProductPublicationControllerApiV2.ProductConditions=Condition of the Item
controller.ProductPublicationControllerApiV2.Attributes=Category attributes
controller.ProductPublicationControllerApiV2.ProductPhotoSamples=Photo examples
controller.ProductPublicationControllerApiV2.PublishStepInfo=Publish Step Information

controller.PublicProfileControllerApiV2.Profile=User profile
controller.PublicProfileControllerApiV2.Followings=List of subscriptions
controller.PublicProfileControllerApiV2.FollowingsPage=Page of subscriptions
controller.PublicProfileControllerApiV2.FollowingsProposalsPage=Page of proposed subscriptions
controller.PublicProfileControllerApiV2.Followers=Subscribers List
controller.PublicProfileControllerApiV2.FollowersPage=Page of followers
controller.PublicProfileControllerApiV2.ToggleFollowingTurnOn=Subscribed successfully
controller.PublicProfileControllerApiV2.ToggleFollowingTurnOff=Subscription cancelled
controller.PublicProfileControllerApiV2.Follow=Subscribed successfully
controller.PublicProfileControllerApiV2.CouldntFollow=Failed to subscribe
controller.PublicProfileControllerApiV2.Unfollow=Subscription canceled
controller.PublicProfileControllerApiV2.CouldntUnfollow=Failed to unsubscribe

controller.SettingsControllerApiV2.AppSettings=Application Settings

controller.SocialControllerApiV2.SocialAccounts=Social accounts

controller.AdminBargainsControllerApiV2.Bargains=Counter-offers
controller.AdminBargainsControllerApiV2.DetailedBargain=Detailed information about Counter-offer
controller.AdminBargainsControllerApiV2.RefreshBargain=Counter-offer resumed

controller.AdminPanelOrdersControllerApiV2.OrderList=List of Orders
controller.AdminPanelOrdersControllerApiV2.CartClean=Remove items from cart has been done

controller.BargainsControllerApiV2.Settings=Counter-offer settings
controller.BargainsControllerApiV2.IncomingBargains=Incoming counter-offers
controller.BargainsControllerApiV2.IncomingActiveBargains=Active incoming counter-offers
controller.BargainsControllerApiV2.IncomingFinishedBargains=Completed incoming counter-offers
controller.BargainsControllerApiV2.OutgoingBargains=Outgoing counter-offers
controller.BargainsControllerApiV2.OutgoingActiveBargains=Active outgoing counter-offers
controller.BargainsControllerApiV2.OutgoingFinishedBargains=Completed outgoing counter-offers
controller.BargainsControllerApiV2.DetailedBargain=Detailed information about Counter-offer
controller.BargainsControllerApiV2.DetailedBargainByProductAndSize=Detailed information about Counter-offer
controller.BargainsControllerApiV2.DetailedBargainByTemplate=Counter-offer template
controller.BargainsControllerApiV2.CreateBargain=Counter-offer created
controller.BargainsControllerApiV2.AddNewRecord=Entry added
controller.BargainsControllerApiV2.BargainBubbles=Counter-offers counters

controller.MasterServiceControllerApiV2.NotificationsWillBeCreated=Notifications will be created
controller.MasterServiceControllerApiV2.OK=OK
controller.MasterServiceControllerApiV2.StatusesWillBeSet=Statuses will be set
controller.MasterServiceControllerApiV2.CheckingWillBeCompleted=Verification will be completed
controller.MasterServiceControllerApiV2.NotificationsWillBeClosed=Notifications will be closed
controller.MasterServiceControllerApiV2.MarkNotifications=The marks are affixed
controller.MasterServiceControllerApiV2.MarkAsSentNotifications=The marks will be affixed
controller.MasterServiceControllerApiV2.SaveConversions=Conversions will be saved
controller.MasterServiceControllerApiV2.SaveSyncUsersInfo=Information about synchronized users will be saved
controller.MasterServiceControllerApiV2.StartSyncBuyerChecksForOrder=Synchronization of receipt affixing for orders will be started
controller.MasterServiceControllerApiV2.SaveLogisticStateDeliveries=Consignment note status information will be saved
controller.MasterServiceControllerApiV2.SaveValidatedCities=Verified cities will be saved
controller.MasterServiceControllerApiV2.SaveValidatedAddresses=Verified addresses will be saved
controller.MasterServiceControllerApiV2.CreateAdminProductAlerts=Admin product alerts will be saved
controller.MasterServiceControllerApiV2.CreateAdminOrderAlerts=Admin order alerts will be saved
controller.MasterServiceControllerApiV2.SaveBankBalance=The history of the bank account balance will be saved
controller.MasterServiceControllerApiV2.SaveBankMoneyTransfer=Saving of the transfer to the bank account will be completed
controller.MasterServiceControllerApiV2.SaveCheckTranferMoneyToSellers=Saving payments to sellers check will be completed
controller.MasterServiceControllerApiV2.SaveTransferMoneyToSellers=Saving payments to sellers will be completed
controller.MasterServiceControllerApiV2.SavePayoutRequestStatus=Saving payout request will be completed
controller.MasterServiceControllerApiV2.HandleBankOperations=The results of banking transactions have been processed
controller.MasterServiceControllerApiV2.HandleExpiredOrders=Orders canceled due to lack of time for holding funds
controller.MasterServiceControllerApiV2.HandleFiscalReceiptsRequests=The results of sending fiscal receipts processed
controller.MasterServiceControllerApiV2.SaveUserAttributes=User attributes processed

controller.OrderControllerApiV2.UnfinishedOrders=Current user's incomplete orders
controller.OrderControllerApiV2.FinishedOrdersForBuyer=Current buyer's completed orders
controller.OrderControllerApiV2.FinishedOrdersForSeller=Current seller's completed orders
controller.OrderControllerApiV2.InitHold=Order payment initiated
controller.OrderControllerApiV2.DeleteOrder=Order deleted
controller.OrderControllerApiV2.Order=Order
controller.OrderControllerApiV2.BuyerOrders=Current buyer's orders
controller.OrderControllerApiV2.BuyerOrdersPage=Page of current buyer's orders
controller.OrderControllerApiV2.BuyerOrdersInProcess=Current buyer's orders in process
controller.OrderControllerApiV2.BuyerOrdersInProcessPage=Page of current buyer's orders in process
controller.OrderControllerApiV2.BuyerOrdersFinished=Current buyer's completed orders
controller.OrderControllerApiV2.BuyerOrdersFinishedPage=Page of current buyer's completed orders
controller.OrderControllerApiV2.BuyerOrdersReturns=Current buyer's returns
controller.OrderControllerApiV2.BuyerOrdersReturnsPage=Page of current buyer's returns
controller.OrderControllerApiV2.SellerOrders=Current seller's sales
controller.OrderControllerApiV2.SellerOrdersPage=Page of current seller's sales
controller.OrderControllerApiV2.SellerOrdersAwaitingConfirmation=Seller's sales, waiting for the confirmation
controller.OrderControllerApiV2.SellerOrdersAwaitingConfirmationPage=Page of seller's sales, waiting for the confirmation
controller.OrderControllerApiV2.SellerOrdersPayments=Seller's sales, waiting for the payment
controller.OrderControllerApiV2.SellerOrdersPaymentsPage=Page of seller's sales, waiting for the payment
controller.OrderControllerApiV2.SellerOrdersInProcess=Seller's sales in process
controller.OrderControllerApiV2.SellerOrdersInProcessPage=Page of seller's sales in process
controller.OrderControllerApiV2.SellerOrdersFinished=Seller's completed orders
controller.OrderControllerApiV2.SellerOrdersFinishedPage=Page of seller's completed orders
controller.OrderControllerApiV2.PickupAddressEndpoint=Shipment location is set
controller.OrderControllerApiV2.DeliveryAddressEndpoint=Delivery location is set
controller.OrderControllerApiV2.SellerCounterparty=Counterparty for seller is set
controller.OrderControllerApiV2.ConfirmOrderPosition=Order item confirmed/cancelled
controller.OrderControllerApiV2.ConfirmOrder=Order confirmed/cancelled
controller.OrderControllerApiV2.TimeIntervals=Time frame for shipment
controller.OrderControllerApiV2.AvailablePickupDates=Available dates for shipment
controller.OrderControllerApiV2.PickupTimeIntervalId=Time frame for shipment is set
controller.OrderControllerApiV2.PickupComment=Comment for shipment is set
controller.OrderControllerApiV2.DeliveryComment=Comment for delivery is set
controller.OrderControllerApiV2.ReturnReasons=Possible reasons for the Return
controller.OrderControllerApiV2.InitReturn=Return is issued
controller.OrderControllerApiV2.ReturnInfo=Detailed information about Return
controller.OrderControllerApiV2.BuyerReturnsPage=Buyer Returns Page
controller.OrderControllerApiV2.SellerReturnsPage=Seller Returns Page

validator.SelectionValidator.FirstLineNotFound=Missing first line
validator.SelectionValidator.SecondLineNotFound=Missing second line
validator.SelectionValidator.ThirdLineNotFound=Missing third line
validator.SelectionValidator.FirstLineTooLong=First line is too long
validator.SelectionValidator.SecondLineTooLong=Second line is too long
validator.SelectionValidator.ThirdLineTooLong=Third line is too long
validator.SelectionValidator.UrlNotFound=No Url specified

validator.ProfileImageValidator.ImageWrongSizeWarning=Image size should be between {0}x{1} - {2}x{3}
validator.ProfileImageValidator.ImageWrongSize=Invalid image size
validator.ProfileImageValidator.ImageWrongMessage=Only square images are allowed

validator.ImageValidator.EmptyImage=Missing image
validator.ImageValidator.FileSizeTooLarge=Uploaded file exceeds max size
validator.ImageValidator.WrongImageFormat=Incorrect file format

validator.DefaultImageValidator.WrongImageSize=Image size should be {0}x{1}

service.DefaultConciergeService.OrderInfo.Notification=Notification of a paid order
service.DefaultConciergeService.OrderInfo.PaymentDate=Payment date
service.DefaultConciergeService.OrderInfo.OrderId=Order number
service.DefaultConciergeService.OrderInfo.ProductList=List of products
service.DefaultConciergeService.OrderInfo.SizeNotIdentified=The size is not defined, connect with the manager
service.DefaultConciergeService.OrderInfo.WithSize=with size
service.DefaultConciergeService.OrderInfo.Product=Product
service.DefaultConciergeService.OrderInfo.ColorNotIdentified=The color is not defined, connect with the manager
service.DefaultConciergeService.OrderInfo.Color=Color
service.DefaultConciergeService.OrderInfo.CurrencyInEuro=Price in â¬
service.DefaultConciergeService.OrderInfo.CurrencyInRub=Price in â½
service.DefaultConciergeService.OrderInfo.LinkToImage=Link to the image

service.DefaultAdminOrdersService.OrderStatusGroupAll=All
service.DefaultAdminOrdersService.OrderStatusGroupPayment=Payment
service.DefaultAdminOrdersService.OrderStatusGroupFromSeller=Picking up from the seller
service.DefaultAdminOrdersService.OrderStatusGroupExpertise=Expertise
service.DefaultAdminOrdersService.OrderStatusGroupToBuyer=Delivery to the Buyer
service.DefaultAdminOrdersService.OrderStatusGroupPaymentToSeller=Payment for sellers
service.DefaultAdminOrdersService.OrderStatusGroupReturn=Return
service.DefaultAdminOrdersService.OrderStatusGroupBoutiqueOrders=Boutique

service.DefaultAdminOrdersService.OrderStatusGroup.REFUND=Refund
service.DefaultAdminOrdersService.OrderStatusGroup.CREATED=Order creation
service.DefaultAdminOrdersService.OrderStatusGroup.COMPLETED=Completed orders
service.DefaultAdminOrdersService.OrderStatusGroup.UNCOMPLETED=Incompleted orders
service.DefaultAdminOrdersService.OrderStatusGroup.OVERDUE_EXPERTISE=5 days without Expertise

service.DefaultAdminOrdersService.OrderStatusUndefined=All

service.DefaultAdminOrdersService.DestinationDescription.OFFICE=From seller to OSKELLY
service.DefaultAdminOrdersService.DestinationDescription.BUYER=From OSKELLY to buyer
service.DefaultAdminOrdersService.DestinationDescription.SELLER=From OSKELLY to seller

#ÐÐ°Ð¿ÑÐ¸Ð¼ÐµÑ, 10 25 2025 Ð² 14:24
service.DefaultAdminOrdersService.InTime='at'

service.DefaultAdminOrdersService.OrderPositionNotFound=Order item not found\: {0}

service.DefaultAdminOrdersService.OrderPositionExpertiseNotCompleted=Item {0} of order {1} did not undergone expertise

service.DefaultAdminOrdersService.OrderListIsEmpty=Order list is empty
service.DefaultAdminOrdersService.OrderNotFound=Order not found\: {0}
service.DefaultAdminOrdersService.WaybillNotFound=Consignment note for the buyer does not exist for the order {0}
service.DefaultAdminOrdersService.OrderProcessingByDifferentCompanies=Orders are delivered by different delivery services\: {0}
service.DefaultAdminOrdersService.ActReportNotSupported=The act of transfer to the delivery service is not supported for {0}

service.DefaultAdminOrdersService.SellerNameNotFilled=FILL IN THE FULL NAME IN THE BANK DETAILS OF THE SELLER
service.DefaultAdminOrdersService.CbNotAllowDebt=Order {0}\: for crossborder order debt not allowed

service.DefaultAdminOrdersService.ReturnablePeriod={0} days {1} hours before order completion

service.DefaultAdminOrdersService.DataNotFound=Data not found
service.DefaultAdminOrdersService.HoldPeriod={0} days

service.DefaultAdminOrdersService.AgentReportHasProblem=Unable send agent report\: There is a dispute. Order - {0}
service.DefaultAdminOrdersService.InvalidStatus=ÐÐµÐ²ÐµÑÐ½ÑÐ¹ ÑÑÐ°ÑÑÑ {0} Ð·Ð°ÐºÐ°Ð·Ð° {1}
service.DefaultAdminOrdersService.InvalidOrderSource=ÐÐµÐ²ÐµÑÐ½ÑÐ¹ Ð¸ÑÑÐ¾ÑÐ½Ð¸Ðº {0} Ð·Ð°ÐºÐ°Ð·Ð° {1}
service.DefaultAdminOrdersService.Shipment2Boutique=ÐÐ° Ð¾ÑÐ³ÑÑÐ·ÐºÑ Ð² Ð±ÑÑÐ¸Ðº

service.DefaultAdminOrdersService.Boutique=boutique

service.DefaultAdminProductService.RetoucherRestricted=The retoucher can only view products with the status Retouching required
service.DefaultAdminProductService.SizeTypeViewComment=category topic
service.DefaultAdminProductService.WebsiteSalesChannelForBoutiqueProductError=Cannot set Website sales channel for Boutique product {0}. Boutique orders - {1}.
service.DefaultAdminProductService.RemovingSizeUsedInBoutiqueOrderError=Size count ({0}) cannot be fewer than {1} because the item(s) is in Boutique. Boutique orders - {2}.

service.DefaultAdminPromoCodeService.PromocodeNotFound=Promotional code with this indentifier does not exist or has been deleted
service.DefaultAdminPromoCodeService.PromocodeWithSameCodeAlreadyExists=Promotional code with this code already exists
service.DefaultAdminPromoCodeService.SetDiscountInPercentOrMoney=Specify the discount as a percentage or in \!
service.DefaultAdminPromoCodeService.PromocodeNotExists=Promotional code with this indentifier does not exist
service.DefaultAdminPromoCodeService.PromocodeWithSameCodeAlreadyDeleted=Promotional code with this indentifier has already been deleted and cannot be edited

service.DefaultProtectedPromoCodeService.SecurePromocodeAlreadyCreated=Secure promotional code has been already generated for order {0}

service.DefaultAdminUserService.ContractInfo=Contract number\: {0}. Date\: {1}
service.DefaultAdminUserService.UserNotSet=The user for job tytle is not set
service.DefaultAdminUserService.Measure.Million=million
service.DefaultAdminUserService.Measure.Thousand=thousand.

service.DefaultAgentReportService.IndividualEntrepreneur=Sole trader {0}
service.DefaultAgentReportService.PaymentSimpleUser=ÐÐ¿Ð»Ð°ÑÐ° Ð¿Ð¾ Ð·Ð°ÐºÐ°Ð·Ñ â {0} Ð¿Ð¾ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÑ-Ð¾ÑÐµÑÑÐµ â {1} Ð¾Ñ {2} Ð½Ð° ÑÐ°Ð¹ÑÐµ\: www.oskelly.ru. ÐÐÐ¡ Ð½Ðµ Ð¾Ð±Ð»Ð°Ð³Ð°ÐµÑÑÑ
service.DefaultAgentReportService.PaymentAgent=ÐÐ¿Ð»Ð°ÑÐ° Ð¿Ð¾ Ð·Ð°ÐºÐ°Ð·Ñ â {0} Ð¿Ð¾ Ð°Ð³ÐµÐ½ÑÑÐºÐ¾Ð¼Ñ Ð´Ð¾Ð³Ð¾Ð²Ð¾ÑÑ â {1} Ð¾Ñ {2} Ð½Ð° ÑÐ°Ð¹ÑÐµ\: www.oskelly.ru. ÐÐÐ¡ Ð½Ðµ Ð¾Ð±Ð»Ð°Ð³Ð°ÐµÑÑÑ

service.BasePayoutService.CbNotAllowDebt=Order {0}\: for crossborder order debt not allowed

service.DefaultCartService.CheckPromoCodeError=Cannot apply promo code: the order is outdated. Please, refresh the order.
service.DefaultCartService.notEnoughAmount=The purchase amount must be at least <b>{0}</b> from one seller

service.BargainConfiguration.SuccessBargains=The key to successful Price offers
service.BargainConfiguration.FirstSentence=You can offer at least 70%% of the full price of the item
service.BargainConfiguration.SecondSentence=If the seller refuses to bid, do not worry, you still have 2 attempts
service.BargainConfiguration.ThirdSentence=The seller has 24 hours to make a decision
service.BargainConfiguration.ForthSentence=After the seller approves the price, you have 24 hours to buy the product at the new price

service.BargainConfiguration.Start.SetOwnPrice=To start bidding, offer your price
service.BargainConfiguration.Start.LimitPrice=Your bid can not be less than 70%% of the original price of the item. The seller will either accept or reject your offer. The offer is valid for 24 hours
service.BargainConfiguration.Active.ActiveBargainsNotFound=You have no active addresses
service.BargainConfiguration.Active.SetOwnPrice=If you think the price is too high, offer the seller your price. He, in turn, has the right to refuse the offered price
service.BargainConfiguration.Completed.CompletedBargainsNotFound=You have no completed price offers
service.BargainConfiguration.Completed.BargainsStory=Comments history will be displayed here.
service.BargainConfiguration.Deadline.Deadline=Your invitation has expired
service.BargainConfiguration.Deadline.BeFaster=Next time, accept the seller's offer faster
service.BargainConfiguration.Success.Completed=You managed to buy at the best price\!
service.BargainConfiguration.Success.Advice=Bargain and get good discounts from sellers
service.BargainConfiguration.Fail.Completed=Unfortunately, the seller rejected your bid
service.BargainConfiguration.Fail.Advice=Do not worry, bargain with sellers and buy at the best prices
service.BargainConfiguration.Response.Failed=Unfortunately, the seller rejected your bid
service.BargainConfiguration.Response.Advice=Do not worry, bargain with sellers and buy at the best prices
service.BargainConfiguration.BargainsDeadline=The seller has {timeLeft} to answer you
service.BargainConfiguration.PriceDeadline=The price is valid\: {timeLeft}
service.BargainConfiguration.AnotherOne.Fail=Someone else has already bought the product
service.BargainConfiguration.AnotherOne.Advice=Next time, buy faster
service.BargainConfiguration.PriceChanged.Notification=Room price has changed
service.BargainConfiguration.PriceChanged.Advice=The seller changed the price of the goods, so the bid was canceled
service.BargainConfiguration.ApprovePrice.Text=Agree on a new price
service.BargainConfiguration.ApprovePrice.Advice=You can agree, reject, or submit your offer to the buyer's bidding. The offer is valid for 24 hours
service.BargainConfiguration.OfferAvailable=The price is valid\: {timeLeft}
service.BargainConfiguration.BuyerWasLate=Unfortunately, the buyer did not have time to buy the product at the new price
service.BargainConfiguration.Deadline.Seller.Failed=Your invitation has expired
service.BargainConfiguration.Deadline.Seller.Advice=Next time, accept the seller's offer faster
service.BargainConfiguration.Success.Seller.Completed=You managed to buy at the best price\!
service.BargainConfiguration.Success.Seller.Advice=Bargain and get profitable orders from buyers
service.BargainConfiguration.Seller.CommonAdvice=Agree to the offers of buyers or offer your price. This way you will sell your product faster
service.BargainConfiguration.Seller.ItemWasSoldBeforeBuyerAnswered=The product was sold before the buyer responded to the auction
service.BargainConfiguration.Seller.ItemWasSoldBeforeSellerAnswered=The product was sold before you could respond to the auction
service.BargainConfiguration.Seller.PriceWasChanged=The seller changed the price of the goods, so the bid was canceled
service.BargainConfiguration.Seller.BuyerHasDeadline=The buyer has {timeLeft} to buy the product at the new price

service.BargainConverter.YouReceive=You received {0}
service.BargainConverter.Offer.Offer=I suggest to
service.BargainConverter.Offer.Controffer=I do not claim, I suggest
service.BargainConverter.Accept.Accepted=Purchased
service.BargainConverter.Accept.PriceApproved=Your reservation has been confirmed
service.BargainConverter.Decline=Your price is declined

service.BargainConverter.WelcomeMessage=Hi, what price do you want to offer?

service.DefaultDiscountService.CertificateNotFound=Item with id {0} not found
service.DefaultDiscountService.CertificateCouldntRedeemed=Item with id {0} not found

service.DefaultDeeplinkService.Hermes=HERMES BAGS
service.DefaultDeeplinkService.ForHer=FOR HER
service.DefaultDeeplinkService.ForHim=FOR HIM
service.DefaultDeeplinkService.Favourites=Starred

service.AmountValidator.NominalNotCorrect=Invalid certificate denomination

service.RecipientValidator.CertificateRecipientNameIsEmpty=The name of the certificate recipient cannot be empty
service.RecipientValidator.DeliveryCompanyNotPicked=Certificate delivery method not selected
service.RecipientValidator.DeliveryMethod=As a delivery method, you can choose either to send an electronic copy of the certificate by e-mail, or to deliver a copy by courier, but not both at the same time
service.RecipientValidator.EmailIsEmpty=validators.address.email.blank
service.RecipientValidator.AddressIsEmpty=The delivery address cannot be empty

service.DefaultNotificationService.UserNotFound=User with identifier %s does not exists
service.DefaultNotificationService.Duplicate=Duplication
service.DefaultNotificationService.NotificationDisabled=Notifications muted
service.DefaultNotificationService.HasSchedule=Notification has its own schedule
service.DefaultNotificationService.NotificationNotSent=Notification DTO not sent
service.DefaultNotificationService.ClassNameNotSent=Class name not passed
service.DefaultNotificationService.WrongPackage=Invalid package
service.DefaultNotificationService.UserIdNotSent=Recipient ID not set
service.DefaultNotificationService.ClassNotFound=Class {0} not found
service.DefaultNotificationService.AccessErrorWhenCreateInstance=Access error when creating an instance {0} {1}
service.DefaultNotificationService.ErrorWhenCreateInstance=Access error when creating an instance {0} {1}

service.LikedBrandProductPublishedNotificationRunner.From=from
service.LikedBrandProductPublishedNotificationRunner.AndMore=and more 
service.LikedBrandProductPublishedNotificationRunner.FromBrands=your favorite brands
service.LikedBrandProductPublishedNotificationRunner.Online=already online\!

service.DefaultOrderService.Delivery.DeliveryDescription=Online tracking via courier service is available after examination. On the day of delivery, the courier will contact you and agree on the delivery time.
service.DefaultOrderService.Delivery.DeliveryTitle=Oskelly Courier Service
service.DefaultOrderService.Delivery.DeliveryInfo=You are located near the OSKELLY office, so we will deliver the goods ourselves, a courier will contact you and agree on the pick-up time.
service.DefaultOrderService.Delivery.CourierName=His name is
service.DefaultOrderService.Delivery.CourierPhone=mobile phone number
service.DefaultOrderService.Delivery.OnlineTracking=Online Tracking
service.DefaultOrderService.Delivery.ByDeliveryCompany=through the courier service.
service.DefaultOrderService.Delivery.CourierConnectWithClient=On the day of delivery, the courier will contact you and agree on the delivery time.
service.DefaultOrderService.Delivery.Deadline.Text=Approximate delivery time in OSKELLY\:
service.DefaultOrderService.Delivery.Deadline.Period=2-3 working days
service.DefaultOrderService.Delivery.DeadlineForBuyer=Approximate delivery time in OSKELLY\:
service.DefaultOrderService.Delivery.ApproveCounterparty=After delivery to the buyer, we will confirm your details
service.DefaultOrderService.Delivery.PickupInfo=You are located near the OSKELLY office, so we will deliver the goods ourselves, a courier will contact you and agree on the pick-up time.

service.DefaultOrderService.Validation.NegativeCommissionInOrderPositions=Order {0}: unable to process order (OPNC)
service.DefaultOrderService.Validation.NoConciergesInBoutique=Order {0}: unable to add concierge product {1} to boutique order 
service.DefaultOrderService.Payment.OrderNotInDelivery=Unable to initiate payment. Order not shipped.
service.DefaultOrderService.Payment.NotYourOrder=Unable to initiate payment for someone else's order
service.DefaultOrderService.Payment.WrongCard=Order {0}\: payment by linked card is not supported (counterparty\: {1})
service.DefaultOrderService.Payment.WrongCounterparty=Order {0}\: unable to pay using the specified bank details ({1})
service.DefaultOrderService.Payment.OrderNotFound=Unable to initiate payment. Order not found\: \#{0}
service.DefaultOrderService.Payment.AgentSellerCPsFail=Order {0}: seller config missing
service.DefaultOrderService.Payment.TaxCalculationFail=Order {0}: unable to process order (PCNC)
service.DefaultOrderService.Payment.MultipleCurrencies=Order {0}\: products with multiple currencies found, please, split order by order positions
service.DefaultOrderService.Payment.CbAllowOneItemOnly=Order {0}\: allowing only one item in crossborder order, please, split your orders
service.DefaultOrderService.Payment.CbMinAppVersionFail=Order {0}\: this app version is unable to proceed crossborder orders, please update or use our website
service.DefaultOrderService.Payment.CbNotAllowPromocode=Order {0}\: for crossborder order cannot apply a promo code
service.DefaultOrderService.Payment.CbNotAllowBonuses=Order {0}\: for crossborder order cannot apply a bonuses
service.DefaultOrderService.Payment.CbAllowOnlyEUR=Order {0}\: for crossborder order currency in price must be EUR
service.DefaultOrderService.Payment.CbNotAllowFixedCommission=Order {0}\: for crossborder order commission grid cannot be fixed
service.DefaultOrderService.Payment.CbNotAllowCustomCommission=Order {0}\: for crossborder order commission grid cannot be custom
service.DefaultOrderService.Payment.CbNotAllowBargain=Order {0}\: for crossborder order cannot apply a bargain
service.DefaultOrderService.OrderNotFound=Order not found\: {0}
service.DefaultOrderService.Delete.NotYourOrder=It is not possible to delete someone's order
service.DefaultOrderService.Delete.ProblemsWithRemoving=Unable to delete the order\: {0}, please try again later
service.DefaultOrderService.NotEnoughRights=You do not have the right to receive order data\: {0}
service.DefaultOrderService.NotEnoughStatusRights=You do not have the right to to receive order data in the {0} status
service.DefaultOrderService.Payment.WrongSchema=Scheme {0} is not supported for order processing
service.DefaultOrderService.GoodsWereUnpublished=The item has been withdrawn from sale.To avoid cancellations of orders, please update your available items in time
service.DefaultOrderService.SellerDidntApprove=Unfortunately, the seller did not confirm the availability of the goods. The funds on your bank card are unfrozen. We are working to ensure that such situations happen as rarely as possible
service.DefaultOrderService.SellerDeclinedOrder=The seller did not pass the order to the courier. We will unfreeze the money within 24 hours
service.DefaultOrderService.HasUnapprovedPositions=There are unconfirmed positions
service.DefaultOrderService.SellerDidntApproveAnything=The seller did not confirm
service.DefaultOrderService.UnholdMoneyAfterExpertise=We will unfreeze the money for this product after the examination of the order
service.DefaultOrderService.UnapprovedGoods=Unconfirmed Items
service.DefaultOrderService.Goods=Items
service.DefaultOrderService.Item=item
service.DefaultOrderService.TheseGoods=these products
service.DefaultOrderService.ThisItem=these products
service.DefaultOrderService.GoodsWereRemoved=The goods have been withdrawn
service.DefaultOrderService.ItemWasRemoved=The item has been withdrawn
service.DefaultOrderService.SellerDeclinedDescription=The seller has not confirmed the item. We are working to ensure that situations like this occur as rarely as possible. Your money for this item will be unfrozen after the expertise of the rest items in the order
service.DefaultOrderService.These=these
service.DefaultOrderService.This=this
service.DefaultOrderService.SoonWeTakeToExpertise=In the near future we will take it for examination, expect a courier call
service.DefaultOrderService.OnlineBoutiqueOrderError.CorrespondingBoutiqueOrderSold=Cannot confirm online order for Boutique item: corresponding Boutique order {0} has been already sold.
service.DefaultOrderService.OnlineBoutiqueOrderError.BoutiqueHasNotAuthorityName=Cannot confirm online order {0} for Boutique item: Hasn't authority. Please move order to Boutique.
service.DefaultOrderService.OnlineBoutiqueOrderError.BoutiqueHasNotOrderSourceInfo=Cannot confirm online order {0} for Boutique item: Hasn't orderSourceInfo.
service.DefaultOrderService.OnlineBoutiqueOrderError.UnableToSaleOnlineFromLocation=Order {0}: unable to confirm online order, location [{1}] in boutique order {2} is unable to sale
service.DefaultOrderService.OnlineBoutiqueOrderError.UnableToSaleOnlineWithNullAddr=Order {0}: unable to confirm online order, location [{1}] in boutique order {2} with null addr

service.DefaultOrderService.Delivery.DeliveryToOskelly=Delivery to OSKELLY
service.DefaultOrderService.Delivery.WeAreSoSorry=We are working to ensure that such situations happen as rarely as possible
service.DefaultOrderService.Delivery.SellerDidntGiveOrderToCourier=The seller did not pass the order to the courier. We will unfreeze the money within 24 hours
service.DefaultOrderService.Delivery.ItemWasRemovedBecauseNotShipped=The product was withdrawn from sale because it was not shipped for sale. Next time you will be blocked
service.DefaultOrderService.Delivery.WaitingForCourier=Wait for the courier
service.DefaultOrderService.Delivery.PickupDateInProcess=The shipment date is being processed

service.DefaultOrderService.Expertise.AdditionalInfo=See details
service.DefaultOrderService.Expertise.Failed=Expertise failed
service.DefaultOrderService.Expertise.UnholdMoney=We will unhold your money within 24 hours
service.DefaultOrderService.Expertise.OurManagerConnectWithYou=Our manager will contact you to return the product to you
service.DefaultOrderService.Expertise.PartiallyCompleted=The examination has not been completed completely
service.DefaultOrderService.Expertise.MultipleCompleted=passed
service.DefaultOrderService.Expertise.SingleCompleted=Toimunud
service.DefaultOrderService.Expertise.ExpertiseNot=The examination is not
service.DefaultOrderService.Expertise.CompletedByHasDefects=The examination has been passed, there are defects
service.DefaultOrderService.Expertise.YouGotDiscount=You got a discount\:
service.DefaultOrderService.Expertise.DefectWasFound=A defect has been found\:
service.DefaultOrderService.Expertise.DecreasePrices=we reduce the price for
service.DefaultOrderService.Expertise.DiscountWillUnfreezed=The discount amount will unfreeze automatically within 24 hours
service.DefaultOrderService.Expertise.DryCleaning=Dry cleaning was carried out
service.DefaultOrderService.Expertise.ProfitWillBeDecreased=Your profit will be reduced by
service.DefaultOrderService.Expertise.OskellyExpertise=OSKELLY Expertise

service.DefaultOrderService.Delivery.ToBuyer=Delivery to the buyer
service.DefaultOrderService.Delivery.ToBoutique=Delivery to the boutique
service.DefaultOrderService.Delivery.WaitingForCourierSince=Wait for the courier
service.DefaultOrderService.Delivery.PaymentWillPaid=The funds have been paid. Expect funds to arrive within 24 hours
service.DefaultOrderService.Delivery.PaymentWillReceived=Payment will be received in {0}
service.DefaultOrderService.Delivery.WaitingForPayment=Wait for the paiment
service.DefaultOrderService.Delivery.ApproveCounterpartyForPayment=Confirm your data to receive the payment. You will recieve the payment within 24 hours after confirmation.
service.DefaultOrderService.Delivery.DeliveredToBoutique=ÐÐ¾ÑÑÐ°Ð²Ð»ÐµÐ½Ð¾ Ð² Ð±ÑÑÐ¸Ðº
service.DefaultOrderService.Delivery.SoldInBoutique=ÐÑÐ¾Ð´Ð°Ð½Ð¾ Ð² Ð±ÑÑÐ¸ÐºÐµ

service.DefaultOrderService.ActiveOrderNotFound=No active order {0}
service.DefaultOrderService.PaymentServiceDoesntSupportPrepayment=Order No.{0}\: The payment service {1} does not support prepayment
service.DefaultOrderService.ReceiptPaymentsDoesntSupport=Order No.{0}\: Prepayment checks are not supported
service.DefaultOrderService.CouldntGetMoney=ÐÐ¾Ð¼ÐµÑ Ð·Ð°ÐºÐ°Ð·Ð°{0}\: ÐÑÐ¾Ð²ÐµÑÐºÐ¸ Ð¿ÑÐµÐ´Ð¾Ð¿Ð»Ð°ÑÑ Ð½Ðµ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÑÑÑÑ
service.DefaultOrderService.NoInStorePickupsInBtqe=Order {0}: unable to set inStore pickup in boutique orders 
service.DefaultOrderService.PrepaymentAmountLessThanEffectiveAmount=Order {0}: prepayment amount ({1}) is less than order final amount ({2})
service.DefaultOrderService.PrepaymentWithNullCaptureAmount=Order {0}: prepayment amount is null

service.DefaultOrderService.ReceiptResending.ReceiptDidntSent=Order no.{0}, the receipt cannot be resent\: the receipt was not sent
service.DefaultOrderService.ReceiptResending.ReceiptDataInOrder=Order no.{0}, it is not possible to resend the receipt\: the receipt data is present in the order

service.DefaultOrderService.SomePositionsDidntExcludeFromAgentReport=Cannot be converted to refund. The following items in the order {0} are not excluded from the agent''s report\: {1}
service.DefaultOrderService.ProblemsWithPromocodeRedeem=Order No.{0}\: error when applying promo code {1}

service.DefaultOrderService.CouldntSetCounterparty=Order No.{0} (seller {1})\: unable to set banking details {2} (user {3})
service.DefaultOrderService.Profit1=Profit\: {0} {1}
service.DefaultOrderService.Profit2=Profit\: {0} {3} - {1} {3} \= {2} {3}
service.DefaultOrderService.FundsPaid=The funds have been paid
service.DefaultOrderService.ItemDelivered=Item delivered

service.NewAdminOrderService.delivery.hint.orders.no.confirmation=No shipment date specified by seller (order not confirmed)

service.DeliverySectionTitleService.BuyerTitle=Delivery
service.DeliverySectionTitleService.SellerNotStartedTitle=The courier is on his way
service.DeliverySectionTitleService.SellerStartedTitle=The order has been picked up by the courier service
service.DeliverySectionTitleService.SellerFailedTitle=The courier should have arrived by now

service.OrderUITextsService.EstimatedDeliveryDate=Expected Delivery Date: {0}

service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.title.Buyer=Order Confirmation
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.DEAL_INITIATED.description.Buyer=We have notified the seller about your purchase. Please wait for a order confirmation from the seller.
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.CONFIRMED.description.Buyer=The seller has confirmed your order!
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.CONFIRMED_PARTLY.positionDescription.positive.Buyer=The seller has confirmed the item <b>{0}</b>
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.CONFIRMED_PARTLY.positionDescription.negative.Buyer=The item <b>{0}</b> was rejected by the seller. Funds for the rejected items will be returned to your card after the other items go through expertise.
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.SALE_REJECTED.description.Buyer.singular=The order was rejected by the seller. We have returned the money for your order to your card.
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.SALE_REJECTED.description.Buyer.plural=The order was rejected by the seller. We have returned the money for your order to your card.
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.comment.Buyer=Seller will answer during 24 hours

service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.title.Seller=Order Confirmation
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.DEAL_INITIATED.description.Seller=e
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.CONFIRMED.description.Seller=The order was successfully confirmed
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.CONFIRMED_PARTLY.positionDescription.positive.Seller=The item <b>{0}</b> was successfully confirmed
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.CONFIRMED_PARTLY.positionDescription.negative.Seller=You rejected the item <b>{0}</b>. The item is no longer available for sale. Please update your listings to avoid disappointing potential buyers.
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.SALE_REJECTED.description.Seller.singular=You rejected the order. The item is no longer available for sale. Please update your listings to avoid disappointing potential buyers.
service.OrderTrackDTO.OrderStageDTO.SellerConfirmation.SALE_REJECTED.description.Seller.plural=You rejected the order. The items are no longer available for sale. Please update your listings to avoid disappointing potential buyers.


service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.title.Seller=Delivery to OSKELLY
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.disabledDescription.Seller.singular=After confirmation by the seller, we will pick up the order and bring it to our office
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.disabledDescription.Seller.plural=After confirmation by the seller, we will pick up the order and bring it to our office
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.WAITING_FOR_DELIVERY_FROM_SELLER.description.Seller=The courier will arrive at your address {0}. They will call you an hour before scheduled pick-up.
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.PICKING_UP_FROM_SELLER.description.Seller=The courier will arrive at your address {0}. They will call you an hour before scheduled pick-up.
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.FROM_SELLER_TO_OFFICE.description.Seller=The order is on its way to OSKELLY office for examination
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.description.trackingPart.Seller=Follow the delivery status with tracking number {0}
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.PICKUP_DECLINED.description.Seller.singular=The order has been cancelled, and we have removed the item from sale. Please check other items you have listed. Hide what you're not selling so as not to upset buyers.
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.PICKUP_DECLINED.description.Seller.plural=The order has been cancelled, and we have removed the items from sale. Please check other items you have listed. Hide what you're not selling so as not to upset buyers.
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.FROM_SELLER_DELIVERY_FAIL.description.Seller.singular=<p>Unfortunately, the item hasnât been delivered. We try to do everything to ensure that there are fewer such situations, but we cannot always foresee them.</p>\n<p>We have returned the money for your order to your card.</p>
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.FROM_SELLER_DELIVERY_FAIL.description.Seller.plural=<p>Unfortunately, the item hasnât been delivered. We try to do everything to ensure that there are fewer such situations, but we cannot always foresee them.</p>\n<p>We have returned the money for your order to your card.</p>
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.DELIVERED_TO_EXPERTISE.description.Seller=The order was successfully delivered to OSKELLY office
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.CANCELLED.description.Seller=The order has been cancelled

service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.title.Buyer=Delivery to OSKELLY
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.disabledDescription.Buyer.singular=After confirmation by the seller, we will pick up the order and bring it to our office
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.disabledDescription.Buyer.plural=After confirmation by the seller, we will pick up the order and bring it to our office
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.WAITING_FOR_DELIVERY_FROM_SELLER.description.Buyer=The courier service will pick up your purchase from the seller shortly and bring it to the OSKELLY offices for examination
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.PICKING_UP_FROM_SELLER.description.Buyer=The courier service will pick up the order from the seller shortly and bring it to the OSKELLY offices for examination
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.FROM_SELLER_TO_OFFICE.description.Buyer=The order has been picked up by the courier service and is on its way to the OSKELLY offices for examination
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.PICKUP_DECLINED.description.Buyer.singular=<p>The seller canceled the delivery. We try to do everything to ensure that there are fewer such situations, but we cannot always foresee them.</p>\n<p>We have returned the money for your order to your card.</p>
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.PICKUP_DECLINED.description.Buyer.plural=<p>The seller canceled the delivery. We try to do everything to ensure that there are fewer such situations, but we cannot always foresee them.</p>\n<p>We have returned the money for your order to your card.</p>
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.FROM_SELLER_DELIVERY_FAIL.description.Buyer.singular=<p>The seller canceled the delivery. We try to do everything to ensure that there are fewer such situations, but we cannot always foresee them.</p>\n<p>We have returned the money for your order to your card.</p>
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.FROM_SELLER_DELIVERY_FAIL.description.Buyer.plural=<p>The seller canceled the delivery. We try to do everything to ensure that there are fewer such situations, but we cannot always foresee them.</p>\n<p>We have returned the money for your order to your card.</p>
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.DELIVERED_TO_EXPERTISE.description.Buyer=The order was successfully delivered to the OSKELLY office
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.CANCELLED.description.Buyer=Unfortunately, we did not receive the item. We try to do everything to ensure that there are fewer such situations, but we cannot always foresee them. We have returned the money for your order to your card.

service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.comment.WillBeDeliveredOnDate=Estimated arrival at OSKELLY ~ {0}
service.OrderTrackDTO.OrderStageDTO.DeliveryToOskelly.comment.WillBeDeliveredOnDateRange=Estimated arrival at OSKELLY {0}


# Fallback expertise
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.title.Seller=OSKELLY Examination Check
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.disabledDescription.Seller.singular=Every item in an order undergoes quality control, authenticity examination and, if necessary, we will dry clean, steam and carry out minor repairs
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.disabledDescription.Seller.plural=Every item in an order undergoes quality control, authenticity examination and, if necessary, we will dry clean, steam and carry out minor repairs
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.EXPERTISE_PASSED.description.Seller=The items successfully passed examination
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.EXPERTISE_FAILED.description.Seller=All the items didn't pass examination
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.DELIVERED_TO_EXPERTISE.description.Seller=The items are going through quality control

service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.title.Buyer=OSKELLY Examination Check
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.disabledDescription.Buyer.singular=Every item in an order undergoes quality control, authenticity examination and, if necessary, we will dry clean, steam and carry out minor repairs
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.disabledDescription.Buyer.plural=Every item in an order undergoes quality control, authenticity examination and, if necessary, we will dry clean, steam and carry out minor repairs
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.EXPERTISE_PASSED.description.Buyer=Your items have successfully passed examination
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.EXPERTISE_FAILED.description.Buyer=All the items didn't pass examination
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.DELIVERED_TO_EXPERTISE.description.Buyer=Your items are going through quality control

# Fallback expertise positions details
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.OrderPositionTrackingState.EXPERTISE_POSITION_PASSED.positionText.Seller=The item {0} successfully passed examination
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.OrderPositionTrackingState.EXPERTISE_POSITION_NOT_PASSED.positionText.Seller=The item {0} didn't pass examination because:
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.OrderPositionTrackingState.EXPERTISE_POSITION_PASSED_WITH_DEFFECTS.positionText.Seller=The item {0} successfully passed examination but there were nuances
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.OrderPositionTrackingState.EXPERTISE_POSITION_PASSED_WITH_CLEANING.positionText.Seller=Unfortunately, your item {0} didn't pass examination because:

service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.OrderPositionTrackingState.EXPERTISE_POSITION_NOT_PASSED.positionText.Buyer=eÐ ÑÐ¾Ð¶Ð°Ð»ÐµÐ½Ð¸Ñ Ð²Ð°Ñ ÑÐ¾Ð²Ð°Ñ {0} Ð½Ðµ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ Ð¿Ð¾ Ð¿ÑÐ¸ÑÐ¸Ð½Ðµ:
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.OrderPositionTrackingState.EXPERTISE_POSITION_PASSED.positionText.Buyer=The item {0} successfully passed examination but there were nuances. The discount is {1}
service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise.OrderPositionTrackingState.EXPERTISE_POSITION_PASSED_WITH_DEFFECTS.positionText.Buyer=eÐÐ°Ñ ÑÐ¾Ð²Ð°Ñ {0} ÑÑÐ¿ÐµÑÐ½Ð¾ Ð¿ÑÐ¾ÑÐµÐ» ÑÐºÑÐ¿ÐµÑÑÐ¸Ð·Ñ. ÐÑÐ»Ð¸ Ð´ÐµÑÑÐµÐºÑÑ. Ð¡ÐºÐ¸Ð´ÐºÐ° {1}

# Expertise
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.title.Seller=OSKELLY Examination Check
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.disabledDescription.Seller.singular=Every item in an order undergoes quality control, authenticity examination and, if necessary, we will dry clean, steam and carry out minor repairs
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.disabledDescription.Seller.plural=Every item in an order undergoes quality control, authenticity examination and, if necessary, we will dry clean, steam and carry out minor repairs
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.IN_QUEUE.IN_PROGRESS.description.Seller.plural=The order has arrived at OSKELLY. We will begin the examination soon. We will check the items for originality and compliance with the stated description.
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.IN_QUEUE.IN_PROGRESS.description.Seller.singular=The order has arrived at OSKELLY. We will begin the examination soon. We will check the item for originality and compliance with the stated description.
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.UNPACKING.IN_PROGRESS.description.Seller.plural=The order has arrived at OSKELLY. We will begin the examination soon. We will check the items for originality and compliance with the stated description.
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.UNPACKING.IN_PROGRESS.description.Seller.singular=The order has arrived at OSKELLY. We will begin the examination soon. We will check the item for originality and compliance with the stated description.
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.QUALITY_CONTROL.IN_PROGRESS.description.Seller.plural=We are checking the items for compliance with the stated description
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.QUALITY_CONTROL.IN_PROGRESS.description.Seller.singular=We are checking the item for compliance with the stated description

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.IN_PROGRESS.description.Seller.plural=We are checking the items for originality
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.IN_PROGRESS.description.Seller.singular=We are checking the item for originality
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.SUCCESS.description.Seller.plural=The items are original and as described. We will now transfer them to pre-sale care, pack them and send them to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.SUCCESS.description.Seller.singular=The item is original and as described. We will now transfer it to pre-sale care, pack it and send it to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.SUCCESS.boutiqueDescription.Seller.plural=The items are original and match the description. We will now transfer them to our warehouse
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.SUCCESS.boutiqueDescription.Seller.singular=The item is original and matches the description. We will now transfer it to our warehouse
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.NOT_ORIGINAL.description.Seller.plural=e
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.NOT_ORIGINAL.description.Seller.singular=e
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.description.Seller.plural=<p>We could not be 100% sure that the items <b>{0}</b> are original and we cannot ship them to the buyer.</p>\n<p>We will write to you as soon as possible to arrange a return.</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.description.Seller.singular=<p>We could not be 100% sure that the item <b>{0}</b> is original and we cannot ship it to the buyer.</p>\n<p>We will write to you as soon as possible to arrange a return.</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.boutiqueDescription.Seller.plural=<p>We could not be 100% sure that the items <b>{0}</b> are original and we cannot ship them to the buyer.</p>\n<p>We will write to you as soon as possible to arrange a return.</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.boutiqueDescription.Seller.singular=<p>We could not be 100% sure that the item <b>{0}</b> is original and we cannot ship it to the buyer.</p>\n<p>We will write to you as soon as possible to arrange a return.</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.IN_PROGRESS.position.Seller=e
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.SUCCESS.position.Seller.plural=The items <b>{0}</b> are original and match the description. We will now transfer the items for pre-sale care, pack them and send to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.SUCCESS.position.Seller.singular=The item <b>{0}</b> is original and matches the description. We will now transfer the item for pre-sale care, pack it and send to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.SUCCESS.boutiquePosition.Seller.plural=The items <b>{0}</b> are original and match the description. We will now transfer them to our warehouse
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.SUCCESS.boutiquePosition.Seller.singular=The item <b>{0}</b> is original and matches the description. We will now transfer it to our warehouse
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.NOT_ORIGINAL.position.Seller=<p>We found out that the item <b>{0}</b> is not original because: </p>\n  <p><red>{1}</red></p>\n <p><a href="https://oskelly.ru/info/pdf/seller-agency-agreement.pdf"><primary>According to the rules of the service</primary></a> you need to pay the compensation. After this, you can pick up the item at our office. We will contact you as soon as possible. For more details, call us <a href="tel:+97146086488">+97146086488</a></p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.NOT_ORIGINAL.boutiquePosition.Seller=<p>We found out that the item <b>{0}</b> is not original because: </p>\n  <p><red>{1}</red></p>\n <p><a href="https://oskelly.ru/info/pdf/seller-agency-agreement.pdf"><primary>According to the rules of the service</primary></a> you need to pay the compensation. After this, you can pick up the item at our office. We will contact you as soon as possible. For more details, call us <a href="tel:+97146086488">+97146086488</a></p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.position.Seller.plural=<p>We could not be 100% sure that the items <b>{0}</b> are original and we cannot ship them to the buyer.</p>\n<p>We will write to you as soon as possible to arrange a return.</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.position.Seller.singular=<p>We could not be 100% sure that the item <b>{0}</b> is original and we cannot ship it to the buyer.</p>\n<p>We will write to you as soon as possible to arrange a return.</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.boutiquePosition.Seller.plural=<p>We could not be 100% sure that the items <b>{0}</b> are original and we cannot ship them to the buyer.</p>\n<p>We will write to you as soon as possible to arrange a return.</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.boutiquePosition.Seller.singular=<p>We could not be 100% sure that the item <b>{0}</b> is original and we cannot ship it to the buyer.</p>\n<p>We will write to you as soon as possible to arrange a return.</p>

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.IN_PROGRESS.description.Seller.plural=The items are original, but we found nuances that were not in the stated description. We will contact you shortly and discuss the details
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.IN_PROGRESS.description.Seller.singular=The item is original, but we found nuances that were not in the stated description. We will contact you shortly and discuss the details
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.IN_PROGRESS.boutiqueDescription.Seller.plural=The items are original. The manager is checking and will notify you if we have questions
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.IN_PROGRESS.boutiqueDescription.Seller.singular=The item is original. The manager is checking and will notify you if we have questions
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.description.Seller.plural=You have agreed on a new price. Taking into account the discount, the items cost {0} less. We will now transfer the items to pre-sale care, pack them and send to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.description.Seller.singular=You have agreed on a new price. Taking into account the discount, the item costs {0} less. We will now transfer the item to pre-sale care, pack it and send to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.boutiqueDescription.Seller.plural=You have agreed on a new price. Taking into account the discount, the items cost {0} less. We will now transfer them to our warehouse
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.boutiqueDescription.Seller.singular=You have agreed on a new price. Taking into account the discount, the item costs {0} less. We will now transfer it to our warehouse
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.zeroDiscount.description.Seller.plural=We have agreed on the nuances of the <b>{0}</b>. Now we will transfer the items for pre-sale care, pack them and send them to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.zeroDiscount.description.Seller.singular=We have agreed on the nuances of the <b>{0}</b>. Now we will transfer the item for pre-sale care, pack it and send it to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.zeroDiscount.boutiqueDescription.Seller.plural=The nuances of the <b>{0}</b> have been agreed on. Now we will transfer the items to our warehouse
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.zeroDiscount.boutiqueDescription.Seller.singular=The nuances of the <b>{0}</b> have been agreed on. Now we will transfer the item to our warehouse
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.FAILED.description.Seller.plural=<p>The nuances of haven't been agreed on, the order didn't pass examination.</p>\n<p>You can pick up the item at our office or call a courier. Our address: DUBAI COMMERCITY LLC of PO Box 491, Dubai, United Arab Emirates, BCB2-215. Telephone <a href="tel:+97146086488">+97146086488</a></p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.FAILED.description.Seller.singular=<p>The nuances of haven't been agreed on, the order didn't pass examination.</p>\n<p>You can pick up the item at our office or call a courier. Our address: DUBAI COMMERCITY LLC of PO Box 491, Dubai, United Arab Emirates, BCB2-215. Telephone <a href="tel:+97146086488">+97146086488</a></p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.FAILED.boutiqueDescription.Seller.plural=<p>The nuances of haven't been agreed on, the order didn't pass examination.</p>\n<p>We will write you as soon as possible</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.FAILED.boutiqueDescription.Seller.singular=<p>The nuances of haven't been agreed on, the order didn't pass examination.</p>\n<p>We will write you as soon as possible</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.IN_PROGRESS.description.Seller.plural=The items are original, but we found nuances that were not in the stated description. We will contact you shortly and discuss the details
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.IN_PROGRESS.description.Seller.singular=The item is original, but we found nuances that were not in the stated description. We will contact you shortly and discuss the details
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.IN_PROGRESS.boutiqueDescription.Seller.plural=The items are original. The manager is checking and will notify you if we have questions
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.IN_PROGRESS.boutiqueDescription.Seller.singular=The item is original. The manager is checking and will notify you if we have questions
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.description.Seller.plural=You have agreed on a new price. Taking into account the discount, the items cost {0} less. We will now transfer the items to pre-sale care, pack them and send to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.description.Seller.singular=You have agreed on a new price. Taking into account the discount, the item costs {0} less. We will now transfer the item to pre-sale care, pack it and send to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.boutiqueDescription.Seller.plural=You have agreed on a new price. Taking into account the discount, the items cost {0} less. We will now transfer the items to our warehouse
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.boutiqueDescription.Seller.singular=You have agreed on a new price. Taking into account the discount, the item costs {0} less. We will now transfer the item to our warehouse
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.zeroDiscount.description.Seller.plural=The nuances of the <b>{0}</b> have been agreed on. Now we will transfer the items for pre-sale care, pack them and send them to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.zeroDiscount.description.Seller.singular=The nuances of the <b>{0}</b> have been agreed on. Now we will transfer the item for pre-sale care, pack it and send it to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.zeroDiscount.boutiqueDescription.Seller.plural=The nuances of the <b>{0}</b> have been agreed on. Now we will transfer the items to our warehouse
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.zeroDiscount.boutiqueDescription.Seller.singular=The nuances of the <b>{0}</b> have been agreed on. Now we will transfer the item to our warehouse
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.FAILED.description.Seller.plural=<p>The nuances haven't been agreed on, the order didn't pass examination.</p>\n<p>You can pick up the item at our office or call a courier. Our address: DUBAI COMMERCITY LLC of PO Box 491, Dubai, United Arab Emirates, BCB2-215. Telephone <a href="tel:+97146086488">+97146086488</a></p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.FAILED.description.Seller.singular=<p>The nuances haven't been agreed on, the order didn't pass examination.</p>\n<p>You can pick up the item at our office or call a courier. Our address: DUBAI COMMERCITY LLC of PO Box 491, Dubai, United Arab Emirates, BCB2-215. Telephone <a href="tel:+97146086488">+97146086488</a></p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.FAILED.boutiqueDescription.Seller.plural=<p>The nuances haven't been agreed on, the order didn't pass examination.</p>\n<p>We will write you as soon as possible</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.FAILED.boutiqueDescription.Seller.singular=<p>The nuances haven't been agreed on, the order didn't pass examination.</p>\n<p>We will write you as soon as possible</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.IN_PROGRESS.position.Seller.plural=The items <b>{0}</b> are original, but we found nuances that were not in the description. We will write to you soon and discuss what we can do
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.IN_PROGRESS.position.Seller.singular=The item <b>{0}</b> is original, but we found nuances that were not in the description. We will write to you soon and discuss what we can do
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.IN_PROGRESS.boutiquePosition.Seller.plural=The items <b>{0}</b> are original. The manager is checking and will notify you if we have questions
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.IN_PROGRESS.boutiquePosition.Seller.singular=The item <b>{0}</b> is original. The manager is checking and will notify you if we have questions
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.position.Seller.plural=You have agreed on a new price. Taking into account the discount, the items <b>{0}</b> cost {1} less. We will now transfer the item to pre-sale care, pack it and send to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.position.Seller.singular=You have agreed on a new price. Taking into account the discount, the item <b>{0}</b> costs {1} less. We will now transfer the item to pre-sale care, pack it and send to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.boutiquePosition.Seller.plural=You have agreed on a new price. Taking into account the discount, the items <b>{0}</b> cost {1} less. We will now transfer the item to pre-sale care, pack it and send to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.boutiquePosition.Seller.singular=You have agreed on a new price. Taking into account the discount, the item <b>{0}</b> costs {1} less. We will now transfer the item to pre-sale care, pack it and send to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.zeroDiscount.position.Seller.plural=The nuances of the <b>{0}</b> have been agreed on. Now we will transfer the items for pre-sale care, pack them and send them to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.zeroDiscount.position.Seller.singular=The nuances of the <b>{0}</b> have been agreed on. Now we will transfer the item for pre-sale care, pack it and send it to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.zeroDiscount.boutiquePosition.Seller.plural=The nuances of the <b>{0}</b> have been agreed on. Now we will transfer the items to our warehouse
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.zeroDiscount.boutiquePosition.Seller.singular=The nuances of the <b>{0}</b> have been agreed on. Now we will transfer the item to our warehouse
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.FAILED.position.Seller.plural=<p>The nuances haven''t been agreed on, the <b>{0}</b> didn't pass examination.</p>\n<p>You can pick up the items at our office or call a courier. Our address: DUBAI COMMERCITY LLC of PO Box 491, Dubai, United Arab Emirates, BCB2-215. Telephone <a href="tel:+97146086488">+97146086488</a></p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.FAILED.position.Seller.singular=<p>The nuances haven''t been agreed on, the <b>{0}</b> didn't pass examination.</p>\n<p>You can pick up the item at our office or call a courier. Our address: DUBAI COMMERCITY LLC of PO Box 491, Dubai, United Arab Emirates, BCB2-215. Telephone <a href="tel:+97146086488">+97146086488</a></p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.FAILED.boutiquePosition.Seller.plural=<p>The nuances haven''t been agreed on, the <b>{0}</b> didn't pass examination.</p>\n<p>We will write you as soon as possible</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.FAILED.boutiquePosition.Seller.singular=<p>The nuances haven''t been agreed on, the <b>{0}</b> didn't pass examination.</p>\n<p>We will write you as soon as possible</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.IN_PROGRESS.position.Seller.plural=The items <b>{0}</b> are original, but we found nuances that were not in the description. We will write to you soon and discuss what we can do
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.IN_PROGRESS.position.Seller.singular=The item <b>{0}</b> is original, but we found nuances that were not in the description. We will write to you soon and discuss what we can do
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.IN_PROGRESS.boutiquePosition.Seller.plural=The items <b>{0}</b> are original. The manager is checking and will notify you if we have questions
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.IN_PROGRESS.boutiquePosition.Seller.singular=The item <b>{0}</b> is original. The manager is checking and will notify you if we have questions
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.position.Seller.plural=You have agreed on a new price. Taking into account the discount, the items <b>{0}</b> cost {1} less. We will now transfer the item to pre-sale care, pack it and send to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.position.Seller.singular=You have agreed on a new price. Taking into account the discount, the item <b>{0}</b> costs {1} less. We will now transfer the item to pre-sale care, pack it and send to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.boutiquePosition.Seller.plural=You have agreed on a new price. Taking into account the discount, the items <b>{0}</b> cost {1} less. We will now transfer the item to pre-sale care, pack it and send to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.boutiquePosition.Seller.singular=You have agreed on a new price. Taking into account the discount, the item <b>{0}</b> costs {1} less. We will now transfer the item to pre-sale care, pack it and send to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.zeroDiscount.position.Seller.plural=The nuances of the <b>{0}</b> have been agreed on. Now we will transfer the items for pre-sale care, pack them and send them to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.zeroDiscount.position.Seller.singular=The nuances of the <b>{0}</b> have been agreed on. Now we will transfer the item for pre-sale care, pack it and send it to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.zeroDiscount.boutiquePosition.Seller.plural=The nuances of the <b>{0}</b> have been agreed on. Now we will transfer the items to our warehouse
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.zeroDiscount.boutiquePosition.Seller.singular=The nuances of the <b>{0}</b> have been agreed on. Now we will transfer the item to our warehouse
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.FAILED.position.Seller.plural=<p>The nuances haven''t been agreed on, the <b>{0}</b> didn't pass examination.</p>\n<p>You can pick up the items at our office or call a courier. Our address: DUBAI COMMERCITY LLC of PO Box 491, Dubai, United Arab Emirates, BCB2-215. Telephone <a href="tel:+97146086488">+97146086488</a></p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.FAILED.position.Seller.singular=<p>The nuances haven''t been agreed on, the <b>{0}</b> didn't pass examination.</p>\n<p>You can pick up the item at our office or call a courier. Our address: DUBAI COMMERCITY LLC of PO Box 491, Dubai, United Arab Emirates, BCB2-215. Telephone <a href="tel:+97146086488">+97146086488</a></p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.FAILED.boutiquePosition.Seller.plural=<p>The nuances haven''t been agreed on, the <b>{0}</b> didn't pass examination.</p>\n<p>We will write you as soon as possible</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.FAILED.boutiquePosition.Seller.singular=<p>The nuances haven''t been agreed on, the <b>{0}</b> didn't pass examination.</p>\n<p>We will write you as soon as possible</p>

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PRESELLING_PREPARATION.IN_PROGRESS.description.Seller.plural=The items are original and match the description. We will now transfer the items for pre-sale care, pack them and send to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PRESELLING_PREPARATION.IN_PROGRESS.description.Seller.singular=The item is original and matches the description. We will now transfer the item for pre-sale care, pack it and send to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PRESELLING_PREPARATION.IN_PROGRESS.boutiqueDescription.Seller.plural=The items are original and match the description. We will now transfer them to our warehouse
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PRESELLING_PREPARATION.IN_PROGRESS.boutiqueDescription.Seller.singular=The item is original and matches the description. We will now transfer it to our warehouse
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PRESELLING_PREPARATION.IN_PROGRESS.position.Seller.plural=The items <b>{0}</b> are original and match the description. We will now transfer them to our warehouse
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PRESELLING_PREPARATION.IN_PROGRESS.position.Seller.singular=The item <b>{0}</b> is original and matches the description. We will now transfer it to our warehouse
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PRESELLING_PREPARATION.IN_PROGRESS.boutiquePosition.Seller.plural=The items <b>{0}</b> are original and match the description. We will now transfer them to our warehouse
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PRESELLING_PREPARATION.IN_PROGRESS.boutiquePosition.Seller.singular=The item <b>{0}</b> is original and matches the description. We will now transfer it to our warehouse

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PACKING.IN_PROGRESS.description.Seller.plural=We are packing the items to send them to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PACKING.IN_PROGRESS.description.Seller.singular=We are packing the item to send it to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PACKING.IN_PROGRESS.position.Seller.plural=We are packing the items <b>{0}</b> to send them to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PACKING.IN_PROGRESS.position.Seller.singular=We are packing the item <b>{0}</b> to send it to the buyer

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.SUCCESS.description.Seller=The order has been checked and is ready to be sent to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.description.Seller.plural=The order didn't pass examination
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.description.Seller.singular=The order didn't pass examination
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.SUCCESS.position.Seller.plural=The items <b>{0}</b> are original and match the description. The order is ready to be sent to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.SUCCESS.position.Seller.singular=The item <b>{0}</b> is original and matches the description. The order is ready to be sent to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.position.Seller.plural=The items <b>{0}</b> didn't pass examination
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.position.Seller.singular=The item <b>{0}</b> didn't pass examination
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.DESTROYED.description.Seller.singular=Unfortunately, we had to cancel your order due to technical issues. Our team will contact you shortly to discuss the refund details.
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.DESTROYED.description.Seller.plural=Unfortunately, we had to cancel your order due to technical issues. Our team will contact you shortly to discuss the refund details.
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.DESTROYED.position.Seller.singular=Unfortunately, we had to cancel item <b>{0}</b> due to technical reasons. We will contact you to discuss the details once the other items from the order are delivered to the buyer.
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.DESTROYED.position.Seller.plural=Unfortunately, we had to cancel items <b>{0}</b> due to technical reasons. We will contact you to discuss the details once the other items from the order are delivered to the buyer.

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.SUCCESS.boutiqueDescription.Seller=The item is original and matches the description. We will now transfer it to our warehouse
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.SUCCESS.boutiquePosition.Seller.plural=The items <b>{0}</b> successfully passed examination
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.SUCCESS.boutiquePosition.Seller.singular=The item <b>{0}</b> successfully passed examination
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.SUCCESS.boutiqueDescription.Buyer=The order successfully passed examination
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.SUCCESS.boutiquePosition.Buyer.plural=The items <b>{0}</b> successfully passed examination
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.SUCCESS.boutiquePosition.Buyer.singular=The item <b>{0}</b> successfully passed examination


service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.title.Buyer=OSKELLY Examination Check
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.disabledDescription.Buyer.singular=Every item in an order undergoes quality control, authenticity examination and, if necessary, we will dry clean, steam and carry out minor repairs
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.disabledDescription.Buyer.plural=Every item in an order undergoes quality control, authenticity examination and, if necessary, we will dry clean, steam and carry out minor repairs
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.IN_QUEUE.IN_PROGRESS.description.Buyer.plural=The order has arrived at OSKELLY. We will begin the examination soon. We will check the items for originality and compliance with the stated description.
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.IN_QUEUE.IN_PROGRESS.description.Buyer.singular=The order has arrived at OSKELLY. We will begin the examination soon. We will check the item for originality and compliance with the stated description.
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.UNPACKING.IN_PROGRESS.description.Buyer.plural=The order has arrived at OSKELLY. We will begin the examination soon. We will check the items for originality and compliance with the stated description.
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.UNPACKING.IN_PROGRESS.description.Buyer.singular=The order has arrived at OSKELLY. We will begin the examination soon. We will check the item for originality and compliance with the stated description.
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.QUALITY_CONTROL.IN_PROGRESS.description.Buyer.plural=We are checking the items for compliance with the stated description
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.QUALITY_CONTROL.IN_PROGRESS.description.Buyer.singular=We are checking the item for compliance with the stated description

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.IN_PROGRESS.description.Buyer.plural=We are checking the items for originality
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.IN_PROGRESS.description.Buyer.singular=We are checking the item for originality
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.SUCCESS.description.Buyer.plural=The items are original and match the description. We will now transfer the items for pre-sale care, pack them and send to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.SUCCESS.description.Buyer.singular=The item is original and matches the description. We will now transfer the item for pre-sale care, pack it and send to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.NOT_ORIGINAL.description.Buyer.plural=<p>The <b>{0}</b> products are not authentic.</p>\n<p>We have returned the money for your order to your card.</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.NOT_ORIGINAL.description.Buyer.singular=<p>The <b>{0}</b> product is not authentic.</p>\n<p>We have returned the money for your order to your card.</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.description.Buyer.plural=<p>We couldn''t confirm with 100% certainty that the items <b>{0}</b> are authentic. On policy, we cannot send you an item if we doubt its authenticity.</p>\n<p>The amount for the item has been refunded to your card.</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.description.Buyer.singular=<p>We couldn''t confirm with 100% certainty that the item <b>{0}</b> is authentic. On policy, we cannot send you an item if we doubt its authenticity.</p>\n<p>The amount for the items has been refunded to your card.</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.IN_PROGRESS.position.Buyer=e
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.SUCCESS.position.Buyer.plural=The items <b>{0}</b> are original and match the description. We will now transfer the items for pre-sale care, pack them and send to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.SUCCESS.position.Buyer.singular=The item <b>{0}</b> is original and matches the description. We will now transfer the item for pre-sale care, pack it and send to the buyer
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.NOT_ORIGINAL.position.Buyer.plural=<p>The <b>{0}</b> products are not authentic.</p>\n<p>We have returned the money for your order to your card.</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.NOT_ORIGINAL.position.Buyer.singular=<p>The <b>{0}</b> product is not authentic.</p>\n<p>We have returned the money for your order to your card.</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.position.Buyer.plural=<p>We couldn''t confirm with 100% certainty that the items <b>{0}</b> are authentic. On policy, we cannot send you an item if we doubt its authenticity.</p>\n<p>The amount for the items has been refunded to your card.</p>
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.AUTHENTICITY.FAILED.CANNOT_DETERMINE.position.Buyer.singular=<p>We couldn''t confirm with 100% certainty that the item <b>{0}</b> is authentic. On policy, we cannot send you an item if we doubt its authenticity.</p>\n<p>The amount for the item has been refunded to your card.</p>

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.IN_PROGRESS.description.Buyer.plural=The items are original, but we found nuances that were not in the stated description. We will contact you shortly and discuss the details
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.IN_PROGRESS.description.Buyer.singular=The item is original, but we found nuances that were not in the stated description. We will contact you shortly and discuss the details
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.description.Buyer.plural=The items <b>{0}</b> cost {1} less, taking into account the nuances. The difference will be returned to your card. Now we will transfer them for pre-sale care, pack them and send them to you
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.description.Buyer.singular=The item <b>{0}</b> costs {1} less, taking into account the nuances. The difference will be returned to your card. Now we will transfer it for pre-sale care, pack it and send it to you
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.zeroDiscount.description.Buyer.plural=The nuances of the <b>{0}</b> have been agreed on. Now we will transfer the items for pre-sale care, pack them and send them to you
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.zeroDiscount.description.Buyer.singular=The nuances of the <b>{0}</b> have been agreed on. Now we will transfer the item for pre-sale care, pack it and send it to you
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.FAILED.description.Buyer.plural=The nuances haven''t been agreed on, the <b>{0}</b> didn't pass examination. The amount for the items have been refunded to your card.
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.FAILED.description.Buyer.singular=The nuances haven''t been agreed on, the <b>{0}</b> didn't pass examination. The amount for the item has been refunded to your card.
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.IN_PROGRESS.description.Buyer.plural=The items are original, but we found nuances that were not in the stated description. We will contact you shortly and discuss the details
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.IN_PROGRESS.description.Buyer.singular=The item is original, but we found nuances that were not in the stated description. We will contact you shortly and discuss the details
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.description.Buyer.plural=The items <b>{0}</b> cost {1} less, taking into account the nuances. The difference will be returned to your card. Now we will transfer them for pre-sale care, pack them and send them to you
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.description.Buyer.singular=The item <b>{0}</b> costs {1} less, taking into account the nuances. The difference will be returned to your card. Now we will transfer it for pre-sale care, pack it and send it to you
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.zeroDiscount.description.Buyer.plural=The nuances of the <b>{0}</b> have been agreed on. Now we will transfer the items for pre-sale care, pack them and send them to you
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.zeroDiscount.description.Buyer.singular=The nuances of the <b>{0}</b> have been agreed on. Now we will transfer the item for pre-sale care, pack it and send it to you
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.FAILED.description.Buyer.plural=The nuances haven''t been agreed on, the order didn't pass examination. The amount for the items have been refunded to your card.
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.FAILED.description.Buyer.singular=The nuances haven''t been agreed on, the order didn't pass examination. The amount for the item has been refunded to your card.
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.IN_PROGRESS.position.Buyer.plural=The <b>{0}</b> are original, but we found nuances that were not in the description. We will write to you soon and discuss what we can do
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.IN_PROGRESS.position.Buyer.singular=The <b>{0}</b> is original, but we found nuances that were not in the description. We will write to you soon and discuss what we can do
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.position.Buyer.plural=The items <b>{0}</b> cost {1} less, taking into account the nuances. The difference will be returned to your card. Now we will transfer it for pre-sale care, pack it and send it to you
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.position.Buyer.singular=The item <b>{0}</b> costs {1} less, taking into account the nuances. The difference will be returned to your card. Now we will transfer it for pre-sale care, pack it and send it to you
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.zeroDiscount.position.Buyer.plural=The nuances of the <b>{0}</b> have been agreed on. Now we will transfer the items for pre-sale care, pack them and send them to you
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.SUCCESS.zeroDiscount.position.Buyer.singular=The nuances of the <b>{0}</b> have been agreed on. Now we will transfer the item for pre-sale care, pack it and send it to you
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.FAILED.position.Buyer.plural=The nuances haven''t been agreed on, the <b>{0}</b> didn't pass examination. The amount for the items have been refunded to your card.
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.DEFECT_MATCHING.FAILED.position.Buyer.singular=The nuances haven''t been agreed on, the <b>{0}</b> didn't pass examination. The amount for the item has been refunded to your card.
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.IN_PROGRESS.position.Buyer.plural=The <b>{0}</b> are original, but we found nuances that were not in the description. We will write to you soon and discuss what we can do
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.IN_PROGRESS.position.Buyer.singular=The <b>{0}</b> is original, but we found nuances that were not in the description. We will write to you soon and discuss what we can do
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.position.Buyer.plural=The items <b>{0}</b> cost {1} less, taking into account the nuances. The difference will be returned to your card. Now we will transfer it for pre-sale care, pack it and send it to you
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.position.Buyer.singular=The item <b>{0}</b> costs {1} less, taking into account the nuances. The difference will be returned to your card. Now we will transfer it for pre-sale care, pack it and send it to you
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.zeroDiscount.position.Buyer.plural=The nuances of the <b>{0}</b> have been agreed on. Now we will transfer the items for pre-sale care, pack them and send them to you
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.SUCCESS.zeroDiscount.position.Buyer.singular=The nuances of the <b>{0}</b> have been agreed on. Now we will transfer the item for pre-sale care, pack it and send it to you
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.FAILED.position.Buyer.plural=The nuances haven''t been agreed on, the <b>{0}</b> didn't pass examination. The amount for the items have been refunded to your card.
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.CHANGES_MATCHING.FAILED.position.Buyer.singular=The nuances haven''t been agreed on, the <b>{0}</b> didn't pass examination. The amount for the items have been refunded to your card.

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PRESELLING_PREPARATION.IN_PROGRESS.description.Buyer.plural=The items are original and as described. We will now transfer them to pre-sale care, pack them and send them to you
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PRESELLING_PREPARATION.IN_PROGRESS.description.Buyer.singular=The item is original and as described. We will now transfer it to pre-sale care, pack it and send it to you
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PRESELLING_PREPARATION.IN_PROGRESS.position.Buyer.plural=The items <b>{0}</b> are original and as described. We will now transfer them to pre-sale care, pack them and send them to you
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PRESELLING_PREPARATION.IN_PROGRESS.position.Buyer.singular=The item <b>{0}</b> is original and as described. We will now transfer it to pre-sale care, pack it and send it to you

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PACKING.IN_PROGRESS.description.Buyer.plural=We are packing the items to send them to you
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PACKING.IN_PROGRESS.description.Buyer.singular=We are packing the item to send it to you
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PACKING.IN_PROGRESS.position.Buyer.plural=We are packing the items <b>{0}</b> to send them to you
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PACKING.IN_PROGRESS.position.Buyer.singular=We are packing the item <b>{0}</b> to send it to you

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.SUCCESS.description.Buyer=The order has been checked and is ready to be sent to you
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.description.Buyer.plural=The order didn't pass examination
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.description.Buyer.singular=The order didn't pass examination
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.SUCCESS.position.Buyer.plural=The items <b>{0}</b> are original and match the description. We prepared them for shipment
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.SUCCESS.position.Buyer.singular=The item <b>{0}</b> is original and matches the description. We prepared it for shipment
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.position.Buyer.plural=The items <b>{0}</b> didn't pass examination. We have to send them back to the seller
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.position.Buyer.singular=The item <b>{0}</b> didn't pass examination. We have to send it back to the seller
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.DESTROYED.description.Buyer.singular=Unfortunately, we are unable to deliver your order due to technical reasons. A refund has been initiated and will be credited to your account within three days.
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.DESTROYED.description.Buyer.plural=Unfortunately, we are unable to deliver your order due to technical reasons. A refund has been initiated and will be credited to your account within three days.
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.DESTROYED.position.Buyer.singular=Unfortunately, we are unable to deliver part of your order with the item <b>{0}</b> due to technical reasons. The refund has been processed and will reach your account within three days.
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.FINISHED.FAILED.DESTROYED.position.Buyer.plural=Unfortunately, we are unable to deliver part of your order with the items <b>{0}</b> due to technical reasons. The refund has been processed and will reach your account within three days.

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PREPARATION_FOR_PUBLICATION.IN_PROGRESS.description.Seller.plural=The items successfully passed examination. We will soon list them on the platform: take photos and fill in the description
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PREPARATION_FOR_PUBLICATION.IN_PROGRESS.description.Seller.singular=The item successfully passed examination. We will soon list it on the platform: take photos and fill in the description
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PREPARATION_FOR_PUBLICATION.IN_PROGRESS.position.Seller.plural=The items <b>{0}</b> successfully passed examination. We will soon list them on the platform: take photos and fill in the description
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.PREPARATION_FOR_PUBLICATION.IN_PROGRESS.position.Seller.singular=The item <b>{0}</b> successfully passed examination. We will soon list it on the platform: take photos and fill in the description

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.WAIT_DELIVERY_TO_STOCK.IN_PROGRESS.description.Seller.plural=The items successfully passed examination and ready to be listed. We will now transfer them to our warehouse
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.WAIT_DELIVERY_TO_STOCK.IN_PROGRESS.description.Seller.singular=The item successfully passed examination and ready to be listed. We will now transfer it to our warehouse
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.WAIT_DELIVERY_TO_STOCK.IN_PROGRESS.position.Seller.plural=The items <b>{0}</b> successfully passed examination and ready to be listed. We will now transfer them to our warehouse
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.st.WAIT_DELIVERY_TO_STOCK.IN_PROGRESS.position.Seller.singular=The item <b>{0}</b> successfully passed examination and ready to be listed. We will now transfer it to our warehouse

service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.comment.WillBeCompletedOn.singular=Estimated Completion of Examination ~ {0}
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.comment.WillBeCompletedOn.plural=Estimated Completion of Examination {0}
service.OrderTrackDTO.OrderStageDTO.OskellyExpertise.comment.WillBeCompletedOn.Delayed=New Estimated Completion of Examination: {0}. We're sorry for the delay!


service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.title.Seller=Delivery to Buyer
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.disabledDescription.Seller.singular=We will deliver the order in branded packaging at a time convenient for the buyer
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.disabledDescription.Seller.plural=We will deliver the order in branded packaging at a time convenient for the buyer
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.PICKING_UP_FROM_OFFICE.description.Seller=The courier is on his way to OSKELLY office
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.FROM_OFFICE_TO_BUYER.description.Seller=The courier is on his way to the buyer
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.TO_BUYER_DELIVERY_FAIL.description.Seller=Unfortunately, this order was cancelled. We will write to you within 24 hours to discuss the matter.
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.TO_BUYER_DELIVERY_RETURNED.description.Seller=The order has been returned
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.DELIVERED_TO_BUYER.description.Seller=The buyer has received the order
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.description.trackingPart.Seller=Follow the delivery with tracking number {0}

service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.title.Buyer=Delivery to Buyer
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.disabledDescription.Buyer.singular=We will deliver the item in branded packaging at a time convenient for you
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.disabledDescription.Buyer.plural=We will deliver the items in branded packaging at a time convenient for you
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.PICKING_UP_FROM_OFFICE.description.Buyer=The courier is on his way to OSKELLY office
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.FROM_OFFICE_TO_BUYER.description.Buyer=The courier is on his way to you
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.FROM_OFFICE_TO_BUYER.description.extended.Buyer=Expect your courier delivery {0}. The courier service will call you ahead for accepting the time
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.FROM_OFFICE_TO_BUYER.description.delayed.Buyer=Delivery is delayed. The courier service will call you ahead for accepting the time
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.TO_BUYER_DELIVERY_FAIL.description.Buyer=<p>We're so sorry. We are not able to deliver your order to you due to difficulties with delivery.</p>\n<p>We have refunded the complete order amount to your charged card.</p>
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.TO_BUYER_DELIVERY_RETURNED.description.Buyer=The order has been returned
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.DELIVERED_TO_BUYER.description.Buyer=The order has been delivered
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.description.trackingPart.Buyer=Follow the delivery with tracking number {0}
service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.pickupInStoreDescription.Buyer=The order is ready. You can pick it up at the address DUBAI COMMERCITY LLC of PO Box 491, Dubai, United Arab Emirates, BCB2-215. Telephone <a href="tel:+97146086488">+97146086488</a>

service.OrderTrackDTO.OrderStageDTO.DeliveryToBuyer.comment.Buyer=Estimated Arrival: ~ {0}


service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.title.Seller=The item is in OSKELLY office
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.disabledDescription.Seller.singular=After examination we will transfer the item to our warehouse
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.disabledDescription.Seller.plural=After examination we will transfer the items to our warehouse
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.InProgress.description.Seller=eÐÐ°Ñ ÑÐ¾Ð²Ð°Ñ Ð´Ð¾ÑÑÐ°Ð²Ð»ÑÐµÑÑÑ Ð² Ð±ÑÑÐ¸Ðº {0}
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.InProgressWarehouse.description.Seller=Your item is being transferred to our warehouse
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.InProgressOther.description.Seller=Your item is being transferred to our warehouse
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.Completed.description.Seller=eÐÐ°Ñ ÑÐ¾Ð²Ð°Ñ Ð½Ð°ÑÐ¾Ð´Ð¸ÑÑÑ Ð² Ð±ÑÑÐ¸ÐºÐµ Ð¿Ð¾ Ð°Ð´ÑÐµÑÑ {0}
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.CompletedWarehouse.description.Seller=Your item is being transferred to our warehouse
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.SoldOnline.description.Seller=The item has been sold online
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.Failed.description.Seller=The order has been cancelled

service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.title.Buyer=eÐÐ¾ÑÑÐ°Ð²ÐºÐ° Ð² Ð±ÑÑÐ¸Ðº
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.disabledDescription.Buyer.singular=eÐÑ Ð´Ð¾ÑÑÐ°Ð²Ð¸Ð¼ ÑÐ¾Ð²Ð°Ñ Ð² Ð±ÑÑÐ¸Ðº
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.disabledDescription.Buyer.plural=eÐÑ Ð´Ð¾ÑÑÐ°Ð²Ð¸Ð¼ ÑÐ¾Ð²Ð°ÑÑ Ð² Ð±ÑÑÐ¸Ðº
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.InProgress.description.Buyer=eÐ¢Ð¾Ð²Ð°Ñ Ð´Ð¾ÑÑÐ°Ð²Ð»ÑÐµÑÑÑ Ð² Ð±ÑÑÐ¸Ðº
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.InProgressWarehouse.description.Buyer=Your item is being transferred to our warehouse
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.InProgressOther.description.Buyer=Your item is being transferred to our warehouse
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.Completed.description.Buyer=eÐ¢Ð¾Ð²Ð°Ñ Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½ Ð² Ð±ÑÑÐ¸Ðº
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.CompletedWarehouse.description.Buyer=The item has been transferred to our warehouse
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.SoldOnline.description.Buyer=The item has been sold online
service.OrderTrackDTO.OrderStageDTO.DeliveryToBoutique.Failed.description.Buyer=The order has been cancelled


service.OrderTrackDTO.OrderStageDTO.BuyerConfirmation.title.Buyer=Order Confirmation
service.OrderTrackDTO.OrderStageDTO.BuyerConfirmation.disabledDescription.Buyer.singular=Please, confirm that you received the item for seller to know the deal has been completed
service.OrderTrackDTO.OrderStageDTO.BuyerConfirmation.disabledDescription.Buyer.plural=Please, confirm that you received the items for seller to know the deal has been completed
service.OrderTrackDTO.OrderStageDTO.BuyerConfirmation.Cancelled.description.Buyer=The order has been cancelled
service.OrderTrackDTO.OrderStageDTO.BuyerConfirmation.NotConfirmed.description.Buyer.singular=Please, confirm that you received the item for seller to know the deal has been completed
service.OrderTrackDTO.OrderStageDTO.BuyerConfirmation.NotConfirmed.description.Buyer.plural=Please, confirm that you received the items for seller to know the deal has been completed
service.OrderTrackDTO.OrderStageDTO.BuyerConfirmation.Confirmed.description.Buyer.singular=You have confirmed that you have received the item! Thank you for your order!
service.OrderTrackDTO.OrderStageDTO.BuyerConfirmation.Confirmed.description.Buyer.plural=You have confirmed that you have received the items! Thank you for your order!
service.OrderTrackDTO.OrderStageDTO.BuyerConfirmation.NotConfirmed.action.Buyer=I have received the order


service.OrderTrackDTO.OrderStageDTO.SoldInBoutique.title.Seller=The item has been sold
service.OrderTrackDTO.OrderStageDTO.SoldInBoutique.disabledDescription.Seller=We will notify you when the item is sold
service.OrderTrackDTO.OrderStageDTO.SoldInBoutique.description.Seller=Your item has been sold

service.OrderTrackDTO.OrderStageDTO.SoldInBoutique.title.Buyer=The item has been sold
service.OrderTrackDTO.OrderStageDTO.SoldInBoutique.disabledDescription.Buyer=eÐ¢Ð¾Ð²Ð°Ñ Ð¿ÑÐ¾Ð´Ð°Ð½ Ð² Ð±ÑÑÐ¸ÐºÐµ
service.OrderTrackDTO.OrderStageDTO.SoldInBoutique.description.Buyer=The item has been sold

service.OrderTrackDTO.OrderStageDTO.ReturnedToSeller.title.Seller=The return
service.OrderTrackDTO.OrderStageDTO.ReturnedToSeller.description.Seller=We have returned the item to you

service.OrderTrackDTO.OrderStageDTO.ReturnedToSeller.title.Buyer=The return
service.OrderTrackDTO.OrderStageDTO.ReturnedToSeller.description.Buyer=The seller returned his item


service.OrderTrackDTO.OrderStageDTO.Payout.title.Seller=The payout
service.OrderTrackDTO.OrderStageDTO.Payout.disabledDescription.Seller.singular=You will receive your payout within three 3 days after the buyer receives the order
service.OrderTrackDTO.OrderStageDTO.Payout.disabledDescription.Seller.plural=You will receive your payout within three 3 days after the buyer receives the order
service.OrderTrackDTO.OrderStageDTO.Payout.disabledBoutiqueDescription.Seller.singular=eÐÑ Ð¿Ð¾Ð»ÑÑÐ¸ÑÐµ Ð´ÐµÐ½ÑÐ³Ð¸ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 3Ñ Ð´Ð½ÐµÐ¹, Ð¿Ð¾ÑÐ»Ðµ ÑÐ¾Ð³Ð¾ ÐºÐ°Ðº Ð·Ð°ÐºÐ°Ð· Ð±ÑÐ´ÐµÑ Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð¸Ð»Ð¸ Ð¿ÑÐ¾Ð´Ð°Ð½ Ð² Ð¾Ð´Ð½Ð¾Ð¼ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.Payout.disabledBoutiqueDescription.Seller.plural=eÐÑ Ð¿Ð¾Ð»ÑÑÐ¸ÑÐµ Ð´ÐµÐ½ÑÐ³Ð¸ Ð² ÑÐµÑÐµÐ½Ð¸Ðµ 3Ñ Ð´Ð½ÐµÐ¹, Ð¿Ð¾ÑÐ»Ðµ ÑÐ¾Ð³Ð¾ ÐºÐ°Ðº Ð·Ð°ÐºÐ°Ð· Ð±ÑÐ´ÐµÑ Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½ Ð¿Ð¾ÐºÑÐ¿Ð°ÑÐµÐ»Ñ Ð¸Ð»Ð¸ Ð¿ÑÐ¾Ð´Ð°Ð½ Ð² Ð¾Ð´Ð½Ð¾Ð¼ Ð¸Ð· Ð½Ð°ÑÐ¸Ñ Ð±ÑÑÐ¸ÐºÐ¾Ð²
service.OrderTrackDTO.OrderStageDTO.Payout.Cancelled.description.Seller=The order has been cancelled
service.OrderTrackDTO.OrderStageDTO.Payout.NotConfirmed.description.Seller=All that's left to do now is for you to sit back and let us transfer your payout over to you! How should we get it over to you?
service.OrderTrackDTO.OrderStageDTO.Payout.InProgress.description.Seller=We''ve sent {0} to your account. Payout terms are bank dependent.
service.OrderTrackDTO.OrderStageDTO.Payout.Completed.description.Seller=We sent {0} to your account on {1}. Congratulations on the sale!
service.OrderTrackDTO.OrderStageDTO.Payout.NotConfirmed.action.Seller=Select Payment Method


service.PrimaryServiceImpl.RecentlyViewed=Recently Viewed
service.PrimaryServiceImpl.OurChoice=Oskelly Favourites
service.PrimaryServiceImpl.Blog=The OSKELLY Blog
service.PrimaryServiceImpl.Instagram=Instagram Oskelly
service.PrimaryServiceImpl.JoinToCelebrity=Join celebrities
service.PrimaryServiceImpl.NewProducts=New Arrivals
service.PrimaryServiceImpl.EternalClassic=Timeless Fashion
service.PrimaryServiceImpl.OurSocial=Let's Get Social!
service.PrimaryServiceImpl.RecommendedProducts=Recommended for You
service.PrimaryServiceImpl.TopNProducts=Popular items

service.DefaultProductService.ForbiddenOperation=Invalid operation. User Type\: {0}; Operation Type\: {1}; Product\: {2}; User\: {3};
service.DefaultProductService.ForbiddenFilter=Invalid filter. User Type\: {0}; Product Status\: {1}; User\: {2};

service.DefaultProductService.ProductCondition=Condition of an item

service.ProductsCheckerRunner.CouldntReceiveDataAboutSlides=Could not get data about slides\: {0} - {1}

service.DefaultProductPublicationService.UserNotAuthorized=User unauthorized
service.DefaultProductPublicationService.ForbidToEditSoldItem=You cannot change an item that has already been sold
service.DefaultProductPublicationService.ForbidToEditDeletedItem=You cannot change a deleted item
service.DefaultProductPublicationService.ForbidToEditDeclinedItem=You cannot change a deleted item

service.DefaultProductPublicationService.BrandNotSpecified=Brand fare not defined
service.DefaultProductPublicationService.IncorrectId=Enter a valid CIDR
service.DefaultProductPublicationService.CategoryNotSpecified=No category specified

service.DefaultProductPublicationService.ProductNotFound=Product not found
service.DefaultProductPublicationService.ProductItemNotFound=Product item not found

service.DefaultProductPublicationService.CanChooseOnlyFinalCategory=Only the final category can be selected
service.DefaultProductPublicationService.CategoryNotFound=Category not found
service.DefaultProductPublicationService.ForbidToChangeCatgoryForPublishedItem=It is not possible to change the category for a published product
service.DefaultProductPublicationService.WrongCategoryForPublishing=Publication in this category is not possible
service.DefaultProductPublicationService.BrandNotFound=Brand not found
service.DefaultProductPublicationService.WrongBrandForPublishing=The brand is closed for publishing products
service.DefaultProductPublicationService.NotPossibleSetAttributeWhileCategoryEmpty=It is not possible to set attributes for a product with an unspecified category
service.DefaultProductPublicationService.AttributeValueNotFound=Attribute value not found
service.DefaultProductPublicationService.AttributeConflictWithCategory=Attribute {0} is invalid for category {1}
service.DefaultProductPublicationService.IncorrectAmount=Incorrect quantity
service.DefaultProductPublicationService.IncorrectCustomSize=Incorrect custom size type: {0} and size value: {1}
service.DefaultProductPublicationService.IncorrectValue=Invalid value
service.DefaultProductPublicationService.SizeNotFound=Size not found
service.DefaultProductPublicationService.SeasonNotFound=Season not found
service.DefaultProductPublicationService.ImpossibleSetAdditionalSizeForUnknownCategory=It is not possible to set attributes for a product with an unspecified category
service.DefaultProductPublicationService.IncorrectAdditionalSizeForCategory=Invalid extra size for category {0}
service.DefaultProductPublicationService.ProductConditionNotFound=The condition of the product was not found
service.DefaultProductPublicationService.IncorrectMinCategoryPrice=Minimum value\: {0}
service.DefaultProductPublicationService.IncorrectRpr=Invalid RRP value
service.DefaultProductPublicationService.SizesNotFound=There are no Topics
service.DefaultProductPublicationService.SizeNotSpecifiedForProductItem=No size set for Product Item {0}
service.DefaultProductPublicationService.MandatoryImagesNotSpecified=Required images are not set
service.DefaultProductPublicationService.CategoryNotSpecifiedV2=No category specified
service.DefaultProductPublicationService.BrandNotSpecifiedV2=Brand fare not defined
service.DefaultProductPublicationService.SizeTypeNotSpecified=Size type not specified
service.DefaultProductPublicationService.ItemDescriptionNotSpecified=Product description is not set
service.DefaultProductPublicationService.ProductConditionNotSpecified=The condition of the product is not specified
service.DefaultProductPublicationService.IncorrectPrice=Invalid weight entry
service.DefaultProductPublicationService.IncorrectItemPhotoNumber=Incorrect product photo number\: {0}
service.DefaultProductPublicationService.FileTooLarge=File size exceeds {0} MB
service.DefaultProductPublicationService.IncorrectDefectPhotoNumber=Incorrect photo number of the defect\: " {0}
service.DefaultProductPublicationService.ImageNotRelatedToItem=The image is not related to the product {0}
service.DefaultProductPublicationService.ItemIsNotDraft=The product is not a draft\: {0}
service.DefaultProductPublicationService.InvalidCategory=Invalid category

service.DefaultProductPublicationService.Property.Category=All Categories
service.DefaultProductPublicationService.Property.Brand=Brand
service.DefaultProductPublicationService.Property.Size=Product Dimensions
service.DefaultProductPublicationService.Property.MandatoryImage=Mandatory product images
service.DefaultProductPublicationService.Property.ItemDescription=Product description
service.DefaultProductPublicationService.Property.ProductCondition=Condition of an item
service.DefaultProductPublicationService.Property.Price=Price
service.DefaultProductPublicationService.Property.Address=Address
service.DefaultProductPublicationService.customCommissionPriceEditDenied=Price for product with custom commission can not be changed

service.DefaultFtpService.ErrorWhenConnectToFolder=Error connecting to the directory\: {0}

service.PromoGalleryService.ImageNotFound=The image of the photo gallery element is missing
service.PromoGalleryService.ImageIdNotFound=The image of the photo gallery element is missing
service.PromoGalleryService.WrongImageId=The photo gallery element Id is specified incorrectly

service.PromoSelectionService.PromoblockIdNotFound=The Product selection promo block Id is missing
service.PromoSelectionService.WrongPromoblockId=The Product selection promo block ID is incorrectly specified

service.DefaultPromoCodeService.CouldntSaveProcomode.WrongType=Saving a promo code\: Promo code type {0} is not supported

service.DefaultReturnService.WaitingForAccept=Awaiting the confirmation

service.DefaultSocialService.SupportAccountOptions.UniqueSets=Unique selections of the most interesting offers from the platform
service.DefaultSocialService.SupportAccountOptions.Promocodes=Promo codes for weekend OSKELLY purchases
service.DefaultSocialService.SupportAccountOptions.Presents=Gifts from partner brands
service.DefaultSocialService.SupportAccountOptions.ClosedGames=Closed sweepstakes and contests
service.DefaultSocialService.SupportAccountOptions.Education=Educational content in the field of conscious consumption, checklists, surveys and much more

service.InstagramSocialNetworkClient.InvalidSocialNetwork=Invalid social network\: {0}. Expected\: {1}
service.InstagramSocialNetworkClient.CouldntConnectToAccount=The request to the account could not be completed {0}\: {1}, {2}
service.InstagramSocialNetworkClient.CouldntConnectToAccountShort=Failed to complete the request to the account {0}\: {1}
service.InstagramSocialNetworkClient.CouldntAuthByName=Failed to log in on behalf of {0}\: {1}
service.InstagramSocialNetworkClient.CouldntReadObjectProperties=Unable to read object property ({0})\: {1}
service.InstagramSocialNetworkClient.CouldntReadPosts=Unable to read object property ({0})\: {1}
service.InstagramSocialNetworkClient.CouldntReadAuthResponse=Unable to read object property ({0})\: {1}
service.InstagramSocialNetworkClient.CouldntReadObject=cannot read object %s
service.InstagramSocialNetworkClient.StartIndexNotFound=End index {0} not found\: {1}
service.InstagramSocialNetworkClient.FinishIndexNotFound=End index {0} not found\: {1}

service.verification.VerificationClient.BadResponse=Something went wrong
service.verification.VerificationClient.TooManyRequests=Too many requests

service.UserServiceImpl.PasswordsDontMatch=Passwords do not match
service.UserServiceImpl.UserAlreadyRegistered=The user is already registered in the system
service.UserServiceImpl.NicknameAlreadyRegistered=The user with same nickname is already registered in the system
service.UserServiceImpl.EmailAlreadyRegistered=The entered email has already been registered, please enter another email or use the login form

service.UserBanServiceImpl.UserIdIsEmpty=The userId field cannot be empty or negative
service.UserBanServiceImpl.StatusChangedUserIdIsEmpty=The userId field cannot be empty or negative
service.UserBanServiceImpl.BanTypeIsEmpty=The banType field cannot be empty
service.UserBanServiceImpl.EndDateIsBeforeCurrentDate=The End Date field cannot have a value less than the current date
service.UserBanServiceImpl.EndDateIsEmpty=The banType field cannot be empty
service.UserBanServiceImpl.DescriptionIsEmpty=The banType field cannot be empty
service.UserBanServiceImpl.UserAlreadyBlocked=The user's account cannot be blocked, since his account is already blocked
service.UserBanServiceImpl.BanIdIsEmpty=The userId field cannot be empty or negative

service.BargainBanStrategy.BanTitle=Access to trading is closed
service.CommentBanStrategy.BanTitle=Access to comments is closed
service.PublishBanStrategy.BanTitle=You can no longer sell products
service.StoriesPublishBanStrategy.BanTitle=Ban on posting stories
service.StreamBanStrategy.BanTitle=Ban on live broadcasts
service.UserBanStrategy.BanTitle=You are blocked
service.WarningStrategy.BanTitle=Permissive
service.OSocialPostBanStrategy.BanTitle=Ban on publishing posts
service.OSocialCommentBanStrategy.BanTitle=Ban on comments on O!Trends
service.CommentShadowBanStrategy.BanTitle=Shadow ban on comments

service.segment.DefaultSegmentService.UserIdOrGuestTokenMustBeFilled=The user or guest ID must be filled in
service.segment.DefaultSegmentService.exception.UserSegmentNotFound=Segment not found
service.segment.DefaultSegmentService.AuthorizedUsers=Authorized users with purchases
service.segment.DefaultSegmentService.AllUsers=All users
service.segment.DefaultSegmentService.NewbieUsers=Newbies

service.verification.VerificationService.codeVerificationError=Incorrect OTP
service.verification.CloudFlareTurnstileService.Forbidden=Invalid request verification token

validators.UserService.EmailNotSpecified=E-mail is not defined
validators.UserService.PhoneNotSpecified=Phone number is not defined
validators.UserService.IncorrectEmail=Invalid email format
validators.UserService.PasswordNotSpecified=No password given
validators.UserService.TooShortPassword=Password is too short
validators.UserService.ApprovePasswordNotSpecified=Confirmation password is not specified
validators.UserService.NicknameNotSpecified=No alias specified

validators.SubscriptionContactService.EmailNotSpecified=E-mail is not defined
validators.SubscriptionContactService.TooLargeEmail=Email is too long
validators.SubscriptionContactService.TooLargeName=Account name too long

payout.service.username.BONUS_12_STOREEZ=12 Storeez
payout.service.username.b2p+tkb=TCB 1 (Best2pay)
payout.service.username.tcb-1.0=TCB 2 (TCB)
payout.service.username.boutique-1.0=TCB 3 (boutique)
payout.service.username.user-balance-debt=Debt

service.BoutiqueImportService.InvalidProductState=Product {0} is in {1} state. It must be published before creating Boutique orders.
service.BoutiqueImportService.SizeNotFound=Cannot find size {0} for product {1}.
service.BoutiqueImportService.NotEnoughAmount=Requested amount ({0}) of product {1} is greater than available amount ({2}).
service.BoutiqueImportService.NoAddressForBoutiqueUser=Boutique user {0} has no address endpoints.
service.BoutiqueImportService.NoOrderSourceInfoBoutiqueUser=Order source info boutique id {0} does not have user .
service.BoutiqueImportService.MoreThanOneAddressForBoutiqueUser=Boutique user {0} has more than one address endpoint ({1})
service.BoutiqueImportService.SellerConcierge.OnlyOneSize=Can not create order by request {0}: product {1} has more than 1 size
service.BoutiqueImportService.SellerConcierge.OnlyOneCount=Can not create order by request {0}: product {1} has more than 1 size count

filter.processor.BrandFilterProcessor.filterName=Brands
filter.processor.BrandFilterProcessor.topSectionName=Popular
filter.processor.BrandFilterProcessor.allSectionName=All Brands
filter.processor.CategoryFilterProcessor.filterName=Categories
filter.processor.ConditionFilterProcessor.filterName=Product Condition
filter.processor.ConditionFilterProcessor.withTagConditionName=Never used, with tag
filter.processor.ConditionFilterProcessor.withTagConditionDescription=A completely new item that has never been worn. All branded tags and packaging included
filter.processor.ConditionFilterProcessor.perfectConditionName=Excellent condition
filter.processor.ConditionFilterProcessor.perfectConditionDescription=The item is in excellent condition, without obvious external signs of wear or defects. It may not include the entire set of documents, and may also lack original packaging. Minor internal defects are acceptable.
filter.processor.ConditionFilterProcessor.goodConditionName=Good condition
filter.processor.ConditionFilterProcessor.goodConditionDescription=Used item that has been well cared for. There may be minor defects in the form of chips, scratches, holes and snags.
filter.processor.InStockFilterProcessor.filterName=In Stock
filter.processor.InStockFilterProcessor.filterDescription=The goods will be sent to you immediately, since the items in the OSKELLY warehouse have already been examined and do not require time for shipment from the seller
filter.processor.InBoutiqueFilterProcessor.filterName=In Oskelly boutique
filter.processor.ModelFilterProcessor.filterName=Model
filter.processor.NewCollectionFilterProcessor.filterName=New Season
filter.processor.OskellyChoiceFilterProcessor.filterName=Oskelly's Choice
filter.processor.OskellyChoiceFilterProcessor.filterDescription=Every day our stylists select the best items from boutiques and individual sellers' wardrobes
filter.processor.PriceFilterProcessor.filterName=Price Range
filter.processor.SaleFilterProcessor.filterName=Sale
filter.processor.SellerTypeFilterProcessor.filterName=Seller Type
filter.processor.SellerTypeFilterProcessor.individualValue=Individual Seller
filter.processor.SellerTypeFilterProcessor.individualDescription=The "Individual Seller" status is automatically assigned to any user who lists their items for sale, regardless of quantity or condition â from "never used, with tag" to items in excellent or good condition. This tag highlights individual listings, making it easier to discover unique finds directly from private sellers at great prices â or to make your own offer.
filter.processor.SellerTypeFilterProcessor.consignmentShopValue=Resale Store
filter.processor.SellerTypeFilterProcessor.consignmentShopDescription=The "Resale Store" status is given to professional sellers with a large, carefully curated selection. Their offering features carry-over collections from global luxury brands, alongside archival and vintage pieces â from âbrand new with tagâ to items in excellent or good condition.
filter.processor.SellerTypeFilterProcessor.boutiqueValue=Boutique
filter.processor.SellerTypeFilterProcessor.boutiqueDescription=The âBoutiqueâ status is assigned to multi-brand boutiques. Their collections feature the latest items from luxury brands, bestsellers, past seasons, and outlet pieces, all available directly from trusted fashion retailers. Sellers with this status exclusively offer âbrand new with tagâ items and ensure a continuous refresh of their assortment. OSKELLY partners only with boutiques that have an impeccable reputation worldwide.
filter.processor.SellerTypeFilterProcessor.brandValue=Brand
filter.processor.SellerTypeFilterProcessor.brandDescription=The âBrandâ status is assigned to official single-brand boutiques that exclusively offer âbrand new with tagâ items. Their collections feature carry-over pieces from global brands, new season arrivals, capsule collections, and exclusive items, all sourced directly from the manufacturer or distributor, without intermediaries.
filter.processor.SellerTypeFilterProcessor.buyerValue=Personal Shopper
filter.processor.SellerTypeFilterProcessor.buyerDescription=The âPersonal Shopperâ status is granted to true experts who excel at finding unique items based on individual requests. This status is assigned to sellers who offer only brand-new items from the latest collections of global luxury brands, including rare and exclusive pieces that are hard to find elsewhere. A high percentage of successfully completed orders â 99% â is required to obtain this status, ensuring reliability, attention to detail, and impeccable service for every client.
filter.processor.DeliveryDaysFilterProcessor.filterName=Delivery time
filter.processor.DeliveryDaysFilterProcessor.upTo1dayValue=Up to 1 day
filter.processor.DeliveryDaysFilterProcessor.upTo3daysValue=Up to 3 days
filter.processor.DeliveryDaysFilterProcessor.upTo7daysValue=Up to 7 days
filter.processor.DeliveryDaysFilterProcessor.upTo11daysValue=Up to 11 days
filter.processor.DeliveryDaysFilterProcessor.upTo20daysValue=Up to 20 days

filter.processor.SizeFilterProcessor.filterName=Size
filter.processor.SizeTypeFilterProcessor.incorrectValue=Incorrect size type filter value ({0})\!
filter.processor.StateFilterProcessor.incorrectValue=Incorrect item state filter value ({0})\!
filter.processor.OrderStateFilterProcessor.incorrectValue=Incorrect item state filter value ({0})\!
filter.processor.OrderSourceFilterProcessor.incorrectValue=Incorrect item state filter value ({0})\!
filter.processor.StreetwearFilterProcessor.filterName=BEEGZ
filter.processor.StreetwearFilterProcessor.filterDescription=Limited editions and collaborations of iconic streetwear brands from trusted sellers
filter.processor.LocationTagFilterProcessor.filterName=Product location
filter.processor.LocationTagFilterProcessor.incorrectValue=Incorrect location tag filter value ({0})\!
filter.processor.BoutiqueLocationTagFilterProcessor.filterName=Oskelly boutiques
filter.processor.BoutiqueLocationTagFilterProcessor.incorrectValue=Incorrect boutique location tag filter value ({0})\!
filter.processor.VintageFilterProcessor.filterName=Vintage
filter.processor.VintageFilterProcessor.filterDescription=Item that is at least 15 years old
filter.processor.BrandNewFilterProcessor.filterName=Never used, with tag
filter.processor.BrandNewFilterProcessor.filterDescription=The item is brand new and has not been used
filter.processor.WithTagFilterProcessor.filterName=Never used, with tag
filter.processor.WithTagFilterProcessor.filterDescription=Brand new item with the entire set of documents and original packaging
filter.processor.FilterProcessorProvider.unknownProcessor=Could not find a suitable filter processor with code {0}
filter.processor.FilterProcessor.incorrectValueType=Invalid filter value for the filter processor. Given value type {0}, expected - {1}
filter.processor.FilterProcessor.incorrectFilterValue=Invalid available filter value for the filter processor. Given value type {0}, expected - {1}
filter.processor.ResaleFilterProcessor.filterName=Resale
filter.processor.ResaleFilterProcessor.filterDescription=
filter.processor.ExclusiveSelectionFilterProcessor.filterName=Exclusive Selection
filter.processor.ExclusiveLotFilterProcessor.filterName=Exclusive Lot
filter.processor.NewResaleFilterProcessor.filterName=New / Resale
filter.processor.NewResaleFilterProcessor.filterDescription=
filter.processor.NewResaleFilterProcessor.allLabel=All
filter.processor.NewResaleFilterProcessor.resaleLabel=Resale
filter.processor.NewResaleFilterProcessor.newLabel=Boutiques
filter.sorting.ChangeTimeAscSortingProcessor.name=Updated\: Old to New
filter.sorting.ChangeTimeDescSortingProcessor.name=Updated\: New to Old
filter.sorting.DiscountAscSortingProcessor.name=Lowest Percentage Discount
filter.sorting.DiscountSortingProcessor.name=Highest Percentage Discount
filter.sorting.IdAscSortingProcessor.name=ID\: Old to New
filter.sorting.IdDescSortingProcessor.name=ID\: New to Old
filter.sorting.NewSortingProcessor.name=New Arrivals First
filter.sorting.OldSortingProcessor.name=Added\: Old to New
filter.sorting.OskellyChoiceSortingProcessor.name=Oskelly's Choice first
filter.sorting.PriceAscSortingProcessor.name=Price Low to High
filter.sorting.PriceDescSortingProcessor.name=Price High to Low
filter.sorting.PromotionTimeDescSortingProcessor.name=Added\: New to Old
filter.sorting.StateTimeAscSortingProcessor.name=State Updated\: Old to New
filter.sorting.StateTimeDescSortingProcessor.name=State Updated\: New to Old
filter.sorting.SortingProcessorProvider.unknownProcessor=Could not find a suitable sorting processor with code {0}
filter.sorting.ChangeTimeDescOrderSortingProcessor.name=Updated\: New to Old
filter.sorting.ChangeTimeAscOrderSortingProcessor.name=Updated\: Old to New
filter.sorting.ScoreDescSortingProcessor.name=Relevance
filter.sorting.ByProductIdsSortingProcessor.name=Relevance
filter.sorting.PersonalizedSortingProcessor.name=Personalized
filter.controller.availableFilters=Available Filters
filter.controller.availableFilterInfo=Available Filter Info
filter.controller.items=Items
filter.controller.itemsCount=Items count

refund.reason.type.message.orderExpertiseTimeExpired=Expertise time expired
refund.reason.type.message.orderExpertiseFail=Expertise fail
refund.reason.type.message.fromSellerDeliveryDeclined=From seller delivery declined
refund.reason.type.message.fromSellerDeliveryFail=From seller delivery fail
refund.reason.type.message.adminAction=Admin action
refund.reason.type.message.fullSaleRejected=Full sale rejected
refund.reason.type.message.holdExpired=Hold expired
refund.reason.type.message.samePromocode=Promocode {0} was found
refund.reason.type.message.promocodeNumberOfAppliesExceeded=Promocode can be used only once
refund.reason.type.message.sameGiftCard=Gift card {0} was found
refund.reason.type.message.itemsUnavailable=Items unavailable (sale resolution)
refund.reason.type.message.excludedFromAgentReport=Excluded from agent report
refund.reason.type.message.soldInBoutique=Sold in boutique
refund.reason.type.message.returnToSellerFrom1C=Return to seller from 1C
refund.reason.type.message.custom=Custom
refund.reason.type.message.sold.online=Sold online
refund.reason.type.message.inventory=Inventory

promocode.sort.PromoCodeSortProvider.unknownSorting=Could not find a suitable sorting processor with code {0}
exception.user.deleted=Account has been deleted
exception.user.phone.exist=Phone number already exists and verified
exception.verification.tooManyRequests=Too many requests
exception.verification.generate.failed=Error occured while sending SMS
exception.verification.generate.success=SMS have been sent
infrastructure.notificationDelivery.mailganer.trigger.order.OrderDeliveredToBuyerMailganerTrigger.subtitle.isPro=The money will be creditedÂ to the seller's account in 7Â days
infrastructure.notificationDelivery.mailganer.trigger.order.OrderDeliveredToBuyerMailganerTrigger.subtitle=The money is creditedÂ to the seller's account.
infrastructure.notificationDelivery.mailganer.trigger.sale.OrderDeliveredToBuyerNeedAgentReportMailganerTrigger.header.isPro=Your order has been delivered. It remains to confirm the data
infrastructure.notificationDelivery.mailganer.trigger.sale.OrderDeliveredToBuyerNeedAgentReportMailganerTrigger.header=ÐÐ°ÐºÐ°Ð· Ð´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½! ÐÐ¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÐµ Ð´Ð°Ð½Ð½ÑÐµ
infrastructure.notificationDelivery.mailganer.trigger.sale.OrderDeliveredToBuyerNeedAgentReportMailganerTrigger.preheader.isPro=The order has been delivered! Confirm the data
infrastructure.notificationDelivery.mailganer.trigger.sale.OrderDeliveredToBuyerNeedAgentReportMailganerTrigger.preheader=Payment will be received within 24 hours
infrastructure.notificationDelivery.mailganer.trigger.sale.SaleCompletedMailganerTrigger.header.isPro=Congratulations, your order has been delivered
infrastructure.notificationDelivery.mailganer.trigger.sale.SaleCompletedMailganerTrigger.preheader.isPro=The data is confirmed, the money is paid
infrastructure.notificationDelivery.mailganer.trigger.sale.SaleCompletedMailganerTrigger.info.isPro=The money has been paid.
infrastructure.notificationDelivery.mailganer.trigger.sale.SaleCompletedMailganerTrigger.header=The order has been delivered, the funds have been paid
infrastructure.notificationDelivery.mailganer.trigger.sale.SaleCompletedMailganerTrigger.preheader=Expect receipt within 24 hours
infrastructure.mailganer.DeliveryTrackingUrl=https://oskelly.co/account
infrastructure.mailganer.expertise.free=Free
infrastructure.mailganer.DeliveryTerm=7 working
infrastructure.security.provider.register.OAuthRegistrationProvider.EmailMissmatchException=The provided email is different from the email of the current account
infrastructure.security.provider.register.OAuthRegistrationProvider.NicknameMissmatchException=The provided nickname is different from the nickname of the current account
infrastructure.security.provider.register.OAuthRegistrationProvider.EmailAlreadyExistsException=The provided email is already taken by another user. Please change your email or log in as an existing user.
infrastructure.security.provider.register.OAuthRegistrationProvider.NicknameAlreadyExistsException=The provided nickname is already taken by another user. Please change your nickname or log in as an existing user
infrastructure.security.resetpassword=Reset password
infrastructure.security.setpassword=Set password
infrastructure.security.SecurityServiceImpl.Exception.BadLink=Link is invalid
infrastructure.security.SecurityServiceImpl.Exception.LinkExpired=Link has expired

service.CommonLogisticService.EmptyAddress=Empty address in order {0}
service.CommonLogisticService.DeliveryServiceAlreadyCalled=Delivery service has already been called
service.CommonLogisticService.AddressIsDeleted=Address in order {0} has been deleted

service.CseLogisticService.MissingPhone=Missing address phone in order {0}

service.PrimaryServiceImpl.getConciergeBannerBlock.Head=new collections
service.PrimaryServiceImpl.getConciergeBannerBlock.Title=Oskelly Concierge
service.PrimaryServiceImpl.getConciergeBannerBlock.Description=Any item by your request from all over the world
service.PrimaryServiceImpl.getConciergeBannerBlock.ButtonTitle=Send a request
infrastructure.security.provider.auth.EmailAuthenticationProvider.BadEmail=Incorrect email
infrastructure.security.provider.auth.EmailAuthenticationProvider.BadPassword=Invalid password
infrastructure.security.provider.auth.EmailAuthenticationProvider.BadCredential=Incorrect email or password
infrastructure.security.validation.ValidNewPasswordValidator.RequiredFieldsMissed=Required fields are not specified
infrastructure.security.validation.ValidNewPasswordValidator.PasswordsNotMatched=Passwords do not not match
infrastructure.security.validation.ValidNewPasswordValidator.PasswordNotSpecified=Password is not specified
infrastructure.security.validation.ValidNewPasswordValidator.PasswordConfirmationNotSpecified=Password confirmation is not specified
infrastructure.security.validation.ValidNewPasswordValidator.ResetTokenNotSpecified=Token is not set

infrastructure.security.provider.auth.PhoneNumberAuthenticationProvider.BadJWTToken= Invalid JWT token {0}
infrastructure.security.provider.auth.PhoneNumberAuthenticationProvider.UserNotFound=User not found with number {0}
infrastructure.security.provider.auth.GoogleIdTokenAuthProvider.BadIdToken=Invalid Google token
infrastructure.security.provider.auth.GoogleIdTokenAuthProvider.UserNotFound=User not found
infrastructure.security.provider.auth.YandexIdTokenAuthProvider.BadIdToken=Invalid Yandex token
infrastructure.security.provider.auth.YandexIdTokenAuthProvider.UserNotFound=User not found
infrastructure.security.provider.auth.AppleIdTokenAuthProvider.BadIdToken=Invalid Apple token
infrastructure.security.provider.auth.AppleIdTokenAuthProvider.UserNotFound=User not found
su.reddot.infrastructure.security.provider.auth.SocialReAuthProvider.UserNotFound=User with email {0} not found
infrastructure.security.provider.register.v2.PhoneNumberRegistrationProvider.incorrectPhoneNumber=Incorrect phone number
infrastructure.security.provider.register.v2.GoogleRegistrationProvider.userWithGoogleIdAlreadyExist=You have already registered using this method, try to authorize again

infrastructure.security.provider.register.v2.AbstractRegistrationProvider.tokenEmailNotEqualFormEmail=Incorrect email
infrastructure.security.provider.register.v2.AbstractRegistrationProvider.userWithPhoneNumberAlreadyExist=A user with this phone number already exists
infrastructure.security.provider.register.v2.AbstractRegistrationProvider.unexpectedPhoneNumber=Incorrect phone number

infrastructure.security.provider.register.v2.YandexRegistrationProvider.userWithYandexIdAlreadyExist=You have already registered using this method, try to authorize again
su.reddot.infrastructure.security.provider.register.v2.AppleRegistrationProviderV2.userWithAppleRestIdAlreadyExist=You have already registered using this method, try to authorize again

service.verification.VerificationService.invalidJwtToken=Incorrect OTP code

su.reddot.presentation.Utils.ValidationNotPassed=Validation is not passed

infrastructure.util.Utils.Product.One=product
infrastructure.util.Utils.Product.Many=products
infrastructure.util.Utils.Product.Other=products
infrastructure.util.Utils.Item.One=item
infrastructure.util.Utils.Item.Many=items
infrastructure.util.Utils.Item.Other=items
infrastructure.util.Utils.NewItem.One=new item
infrastructure.util.Utils.NewItem.Many=new items
infrastructure.util.Utils.NewItem.Other=new items
infrastructure.util.Utils.Day.One=day
infrastructure.util.Utils.Day.Many=days
infrastructure.util.Utils.Day.Other=days
infrastructure.util.Utils.WorkingDay.One=working day
infrastructure.util.Utils.WorkingDay.Many=working days
infrastructure.util.Utils.WorkingDay.Other=working days

infrastructure.util.Utils.ProductRequest.One=request
infrastructure.util.Utils.ProductRequest.Many=requests
infrastructure.util.Utils.ProductRequest.Other=requests

service.BannerContentService.getStoriesBlock.Title=Get Inspired
service.DefaultPrimaryServiceReactor.getStoriesContent.Title=Get Inspired
service.BannerContentService.BestSellers=Best sellers

boutique.delivery.seller.warning=Attention: Pick up order from OSKELLY boutique!
boutique.delivery.buyer.warning=Attention: Order delivery to OSKELLY boutique!
infrastructure.util.Utils.User.One=user
infrastructure.util.Utils.User.Many=users
infrastructure.util.Utils.User.Other=users

PositionStateTitle.WAITING_FOR_CONFIRMATION.Seller=Awaiting Confirmation
PositionStateTitle.CONFIRMED.Seller=Confirmed
PositionStateTitle.DECLINED.Seller=Rejected
PositionStateTitle.NOT_HANDED_OVER_BY_SELLER.Seller=Rejected
PositionStateTitle.NOT_DELIVERED_TO_OFFICE.Seller=Not delivered to OSKELLY
PositionStateTitle.WAITING_FOR_EXPERTISE.Seller=In Line for Examination
PositionStateTitle.PASSING_THROUGH_EXPERTISE.Seller=Examination In Progress
PositionStateTitle.DEFECTS_FOUND.Seller=Requires Approval
PositionStateTitle.DEFECTS_RECONCILED.Seller=Discount for a nuance
PositionStateTitle.DEFECTS_RECONCILED_NO_DISCOUNT.Seller=Nuances agreed upon
PositionStateTitle.DEFECTS_NOT_RECONCILED.Seller=Rejected
PositionStateTitle.EXPERTISE_SUCCEEDED.Seller=Passed Examination
PositionStateTitle.EXPERTISE_FAILED.Seller=Failed Examination
PositionStateTitle.DETERMINE_AUTHENTICITY_IMPOSSIBLE.Seller=Rejected
PositionStateTitle.APPROVED_BY_OSKELLY.Seller=OSKELLY Approved
PositionStateTitle.DELIVERED.Seller=Delivered
PositionStateTitle.NOT_DELIVERED_TO_BUYER.Seller=Not delivered
PositionStateTitle.DESTROYED.Seller=Rejected

PositionStateTitle.WAITING_FOR_CONFIRMATION.Buyer=Awaiting Confirmation
PositionStateTitle.CONFIRMED.Buyer=Confirmed
PositionStateTitle.DECLINED.Buyer=Rejected
PositionStateTitle.NOT_HANDED_OVER_BY_SELLER.Buyer=Rejected
PositionStateTitle.NOT_DELIVERED_TO_OFFICE.Buyer=Not delivered to OSKELLY
PositionStateTitle.WAITING_FOR_EXPERTISE.Buyer=In Line for Examination
PositionStateTitle.PASSING_THROUGH_EXPERTISE.Buyer=Examination In Progress
PositionStateTitle.DEFECTS_FOUND.Buyer=Requires Approval
PositionStateTitle.DEFECTS_RECONCILED.Buyer=Discount for a nuance
PositionStateTitle.DEFECTS_RECONCILED_NO_DISCOUNT.Buyer=Nuances agreed upon
PositionStateTitle.DEFECTS_NOT_RECONCILED.Buyer=Rejected
PositionStateTitle.EXPERTISE_SUCCEEDED.Buyer=Passed Examination
PositionStateTitle.EXPERTISE_FAILED.Buyer=Failed Examination
PositionStateTitle.DETERMINE_AUTHENTICITY_IMPOSSIBLE.Buyer=Rejected
PositionStateTitle.APPROVED_BY_OSKELLY.Buyer=OSKELLY Approved
PositionStateTitle.DELIVERED.Buyer=Delivered
PositionStateTitle.NOT_DELIVERED_TO_BUYER.Buyer=Not delivered
PositionStateTitle.DESTROYED.Buyer=Rejected

#Ð¢Ð¸Ð¿ Ð¿ÑÐ¾Ð´Ð°Ð²ÑÐ°
entity.enum.SellerTypeDTO.ALL=All
entity.enum.SellerTypeDTO.PRIVATE_SELLER=Private seller
entity.enum.SellerTypeDTO.BOUTIQUE=Boutique

#Ð¡Ð¾ÑÑÐ¾ÑÐ½Ð¸Ðµ Ð¿ÑÐ¾Ð´ÑÐºÑÐ°
entity.enum.ProductState.DRAFT=Draft
entity.enum.ProductState.SECOND_EDITION=Second edition
entity.enum.ProductState.NEED_MODERATION=Need moderation
entity.enum.ProductState.NEED_RETOUCH=Need retouch
entity.enum.ProductState.RETOUCH_DONE=Retouch done
entity.enum.ProductState.REJECTED=Rejected
entity.enum.ProductState.PUBLISHED=Published
entity.enum.ProductState.HIDDEN=Hidden
entity.enum.ProductState.SOLD=Sold
entity.enum.ProductState.DELETED=Deleted
entity.enum.ProductState.BANED=Banned

#ÐÐ°Ð½Ð°Ð» Ð¿ÑÐ¾Ð´Ð°Ð¶
entity.enum.SalesChannel.WEBSITE=Website
entity.enum.SalesChannel.BOUTIQUE_AND_WEBSITE=Boutique and website
entity.enum.SalesChannel.STOCK_AND_BOUTIQUE_AND_WEBSITE=Stock, boutique and website
entity.enum.SalesChannel.BOUTIQUE=Boutique

entity.enum.OrderPaymentFailCode.PAYMENT_REQUEST_EXPIRED=Payment request expired or canceled
entity.enum.OrderPaymentFailCode.PAYMENT_REQUEST_REJECTS=Payment has been rejected

CardPaymentOptionProvider.title=Pay by card
YandexPayOptionProvider.title=Yandex Pay
YandexSplitOptionProvider.title=Yandex Split
TabbySplitOptionProvider.title=4 interest-free payments
AbstractSplitService.part0=Today
AbstractSplitService.chronoUnit.single.MONTHS=month
AbstractSplitService.chronoUnit.plural.MONTHS=months
AbstractSplitService.chronoUnit.pluralExt.MONTHS=months
AbstractSplitService.chronoUnit.single.WEEKS=week
AbstractSplitService.chronoUnit.plural.WEEKS=weeks
AbstractSplitService.chronoUnit.pluralExt.WEEKS=months
TabbySplitOptionProvider.description=Always 0% interest and no fees when you pay on time. Shariah-compliant
SBPOptionProvider.title=SBP
NoonApplePayOptionProvider.title=Apple Pay

SaleRequestService.timeout=Timed out waiting for a result of sale request processing
SaleRequestService.notFound=Request is not found

CommonLogisticService.timeIntervalFromTo=from {0}:00 to {1}:00

OrderStateTitle.WAITING_FOR_CONFIRMATION.Buyer=Awaiting confirmation
OrderStateTitle.CONFIRMED.Buyer=Confirmed
OrderStateTitle.CONFIRMED_PARTIALLY.Buyer=Partially confirmed
OrderStateTitle.SALE_REJECTED.Buyer=Rejected
OrderStateTitle.DELIVERY_TO_OFFICE.Buyer=Awaiting courier's arrival
OrderStateTitle.DELIVERED_TO_OFFICE.Buyer=Delivered to OSKELLY office
OrderStateTitle.PICKUP_DECLINED.Buyer=Pickup declined
OrderStateTitle.DEFECT_RECONCILIATION.Buyer=Need to approve
OrderStateTitle.EXPERTISE_SUCCEEDED.Buyer=Expertise succeeded
OrderStateTitle.EXPERTISE_PARTIALLY_SUCCEEDED.Buyer=Expertise partially succeeded
OrderStateTitle.EXPERTISE_FAILED.Buyer=Expertise failed
OrderStateTitle.DELIVERY_TO_BUYER.Buyer=Awaiting courier's arrival
OrderStateTitle.DELIVERED_TO_BUYER.Buyer=Delivered
OrderStateTitle.RETURNED.Buyer=Returned

OrderStateTitle.WAITING_FOR_CONFIRMATION.Seller=Awaiting confirmation
OrderStateTitle.CONFIRMED.Seller=Confirmed
OrderStateTitle.CONFIRMED_PARTIALLY.Seller=Partially confirmed
OrderStateTitle.SALE_REJECTED.Seller=Rejected
OrderStateTitle.DELIVERY_TO_OFFICE.Seller=Awaiting courier's arrival
OrderStateTitle.DELIVERED_TO_OFFICE.Seller=Delivered to OSKELLY office
OrderStateTitle.PICKUP_DECLINED.Seller=Pickup declined
OrderStateTitle.DEFECT_RECONCILIATION.Seller=Need to approve
OrderStateTitle.EXPERTISE_SUCCEEDED.Seller=Expertise succeeded
OrderStateTitle.EXPERTISE_PARTIALLY_SUCCEEDED.Seller=Expertise partially succeeded
OrderStateTitle.EXPERTISE_FAILED.Seller=Expertise failed
OrderStateTitle.DELIVERY_TO_BUYER.Seller=Awaiting courier's arrival
OrderStateTitle.DELIVERED_TO_BUYER.Seller=Delivered
OrderStateTitle.RETURNED.Seller=Returned

OrderStateTitle.WAITING_FOR_CONFIRMATION.OnlineBoutique=Awaiting confirmation
OrderStateTitle.CONFIRMED.OnlineBoutique=Confirmed
OrderStateTitle.CONFIRMED_PARTIALLY.OnlineBoutique=Partially confirmed
OrderStateTitle.SALE_REJECTED.OnlineBoutique=Rejected
OrderStateTitle.DELIVERY_TO_OFFICE.OnlineBoutique=Awaiting courier's arrival
OrderStateTitle.DELIVERED_TO_OFFICE.OnlineBoutique=Delivered to OSKELLY office
OrderStateTitle.PICKUP_DECLINED.OnlineBoutique=Pickup declined
OrderStateTitle.DEFECT_RECONCILIATION.OnlineBoutique=Need to approve
OrderStateTitle.EXPERTISE_SUCCEEDED.OnlineBoutique=Expertise succeeded
OrderStateTitle.EXPERTISE_PARTIALLY_SUCCEEDED.OnlineBoutique=Expertise partially succeeded
OrderStateTitle.EXPERTISE_FAILED.OnlineBoutique=Expertise failed
OrderStateTitle.DELIVERY_TO_BUYER.OnlineBoutique=Awaiting courier's arrival
OrderStateTitle.DELIVERED_TO_BUYER.OnlineBoutique=Delivered
OrderStateTitle.RETURNED.OnlineBoutique=Returned
service.ProductServiceImpl.sellerConcierge.size.restriction=Fail to save product data: Seller concierge product must contain only one size (order: {0})
service.ProductServiceImpl.sellerConcierge.size.count.restriction=Fail to save product data: Seller concierge product must contain only one size one quantity (order: {0})

EventDateEstimation.calculationContext.afterExpertiseEstimationChange=After expertise estimation change
EventDateEstimation.calculationContext.afterPickupFromSeller=After pickup from seller
EventDateEstimation.calculationContext.afterDeliveryToOffice=After delivery to office
EventDateEstimation.calculationContext.afterExpertiseCompletion=After expertise completion
EventDateEstimation.calculationContext.afterPickupFromOffice=After pickup from office
EventDateEstimation.calculationContext.afterExpertiseOverdue=After expertise overdue
EventDateEstimation.calculationContext.crossBorderAfterHold=Crossboard. After hold


su.reddot.presentation.api.v2.adminpanel.users.dto.BadgeDTO.USER_TYPE.INDIVIDUAL=Individual
su.reddot.presentation.api.v2.adminpanel.users.dto.BadgeDTO.USER_TYPE.LEGAL_ENTITY=Legal entity
su.reddot.presentation.api.v2.adminpanel.users.dto.BadgeDTO.SELLER_TYPE.INDIVIDUAL=Individual seller
su.reddot.presentation.api.v2.adminpanel.users.dto.BadgeDTO.SELLER_TYPE.CONSIGNMENT_SHOP=Consignment shop
su.reddot.presentation.api.v2.adminpanel.users.dto.BadgeDTO.SELLER_TYPE.BOUTIQUE=Boutique
su.reddot.presentation.api.v2.adminpanel.users.dto.BadgeDTO.SELLER_TYPE.BRAND=Brand
su.reddot.presentation.api.v2.adminpanel.users.dto.BadgeDTO.SELLER_TYPE.BUYER=Personal shopper
su.reddot.presentation.api.v2.adminpanel.users.dto.BadgeDTO.RETURNS.acceptReturns=Accept returns

su.reddot.presentation.api.v2.adminpanel.users.UpdateUserRequest.SocialAccount.nicknameRequired=Social Account type is required
su.reddot.presentation.api.v2.adminpanel.users.UpdateUserRequest.SocialAccount.urlRequired=Social Account url is required
su.reddot.presentation.api.v2.adminpanel.users.UpdateUserRequest.SocialAccount.subscribersCountRequired=Social Account subscribersCount is required
su.reddot.presentation.api.v2.adminpanel.users.UpdateUserRequest.SocialAccount.postsCountRequired=Social Account postsCoun is required
su.reddot.presentation.api.v2.adminpanel.users.UpdateUserRequest.SocialAccount.descriptionRequired=Social Account description is required
presentation.api.v2.IntegrationAuthControllerApiV2.authCodeOrIdentityTokenRequired=The Apple-Auth-Code header or identityToken parameter must be specified

service.verification.GenerationPayloadDto.phone.validation.not-null=Phone number must not be empty
service.verification.GenerationPayloadDto.operation.validation.not-null=Operation identifier must not be empty

service.verification.VerificationPayloadDto.phone.validation.not-null=Phone number must not be empty
service.verification.VerificationPayloadDto.operation.validation.not-null=Operation identifier must not be empty
service.verification.VerificationPayloadDto.token.validation.not-null=Verification token must not be empty
service.verification.VerificationPayloadDto.code.validation.not-null=UTP code must not be empty
service.verification.VerificationPayloadDto.code.validation.pattern=Invalid UTP code format

service.RejectHistoryServiceImpl.rejectStatusExtension.FAKE=Fake
service.RejectHistoryServiceImpl.rejectStatusExtension.RECONCILIATION_WITH_SELLER_AND_BUYER_NEGATIVE=Reconciliation is negative

#ÐÐºÑÐ¸Ð²Ð½Ð¾ÑÑÑ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ
enum.UserActivity.ORDERS=Orders
enum.UserActivity.PRODUCTS=Products
enum.UserActivity.OSKELLY_COMMISSION=OSKELLY profit
enum.UserActivity.SELLS=Sells
enum.UserActivity.SELLS_DECLINED=Declined sells

BonusesService.timeout.overall=Overall timeout while working with the bonuses service
BonusesService.timeout.request=Timed out waiting for a response from the bonuses service
BonusesService.error=Error when receiving a response from the bonuses service
BonusesOfflineService.error=Unexpected error in BonusesOfflineService
bonuses.transactions.item.title.welcome.transfer=Welcome bonuses transfer
bonuses.transactions.item.title.birthday.transfer=Birthday bonuses transfer
bonuses.transactions.item.title.order.transfer=Bonuses transfer for the order
bonuses.transactions.item.title.order.return=Return of bonuses for the order
bonuses.transactions.item.title.order.income.other=Bonuses transfer
bonuses.transactions.item.title.order.income.description=From OSKELLYð¤
bonuses.transactions.item.title.order.withdraw=Bonuses withdraw for the order
bonuses.transactions.item.title.order.expire=Bonuses expiration
bonuses.transactions.item.title.order.expire.description=Bonuses have expired
bonuses.transactions.item.title.order.outcome.other=Bonuses withdraw
bonuses.transactions.item.title.order.outcome.description=By OSKELLY Administrator
bonuses.exception.default=Unexpected error with bonuses service
bonuses.exception.object-not-found.default=Object not found: {0}
bonuses.exception.object-not-found.template=Bonuses template with code: {0} not found
bonuses.exception.object-not-found.transaction.transaction-id=Transaction {0} not found
bonuses.exception.object-not-found.transaction.withdraw-account-and-order=Withdraw transaction with accountId: {0} and orderId: {1} not found
bonuses.exception.object-required.order=Transaction with id: {0} and orderId = null can be committed only with orderId, but provided orderId is null
bonuses.exception.object-non-unique.template=There are {0} templates with code: {1}
bonuses.exception.object-non-unique.order=Transaction with accountId: {0} and orderId: {1} already exists
bonuses.exception.object-non-unique.transaction=Unknown error. There are {0} withdraw transaction for accountId: {1} and orderId: {2}
bonuses.exception.object-non-unique.transaction.transaction-idempotency=Transaction with idempotency key: {0} already exists
bonuses.exception.object-must-not-be-null.order=Transaction with id: {0} must have null as orderId
bonuses.exception.wrong-state.transaction-has-no-children=Transaction with transactionId: {0} is in CANCELED state but doesn't have child transactions
bonuses.exception.wrong-state.transaction-children-size-dif=Transaction with transactionId: {0} is in CANCELED state but childTrns.size {1} is not equal to items.size {2}
bonuses.exception.wrong-state.transaction-child-absent=Transaction with transactionId: {0} is in CANCELED state but child transaction {1} is not in method params
bonuses.exception.wrong-state.transaction-cancel-bonuses-dif=Transaction with transactionId: {0} is in CANCELED state but child transaction {1} bonuses amount {2} is not equal to method param item amount {3}
bonuses.exception.wrong-state.transaction-cancel-money-dif=Transaction with transactionId: {0} is in CANCELED state but child transaction {1} money amount {2} is not equal to method param item amount {3}
bonuses.exception.wrong-state.transaction-already-exists=Withdraw transaction already exists for accountId: {0} and orderId: {1}
bonuses.exception.wrong-state.transaction-wrong-state=Transaction with transactionId: {0} has state: {1}, but must be in HOLD state
bonuses.exception.wrong-amount.bonuses-amount-more-zero=Bonuses amount must be greater or equals 0.
bonuses.exception.wrong-amount.money-amount-more-zero=Money amount must be greater or equals 0.
bonuses.exception.wrong-amount.bonuses-or-money-not-null=Bonuses and money amounts are null. Some of them must be not null (or 0).
bonuses.exception.wrong-amount.total-amount-more-zero=Total amount must be greater or equals 0.
bonuses.exception.wrong-amount.bonuses-or-money-or-total-not-null=All amounts are null (0). Some of them must be not null (0).
bonuses.exception.wrong-amount.total-amount-not-null-other-null=If totalAmount is not null (0), all other amounts must be null or 0 (bonuses: {0}, money: {1})
bonuses.exception.wrong-amount.withdraw-negative-amount-impossible=Negative or zero amount: {0} is not possible for accountId: {1} and orderId: {2}
bonuses.exception.wrong-amount.withdraw-not-enough=There is no enough amount: {0} to withdraw: {1} for accountId: {2} and orderId: {3}
bonuses.exception.wrong-amount.transaction-total-not-null=It's prohibited to split transaction: {0} with amount.totalAmount != null: {1}
bonuses.exception.wrong-amount.bonuses-amount-more-zero-or-null=Bonuses amount must be greater than zero or must be null
bonuses.exception.wrong-amount.money-amount-more-zero-or-null=Money amount must be greater than zero or must be null
bonuses.exception.wrong-amount.transactions-bonuses-dif=Bonuses amounts in existing transaction: {0} ({1}) and in new transactions ({2}) must be equal
bonuses.exception.wrong-amount.transactions-money-dif=Money amounts in existing transaction: {0} ({1}) and in new transactions ({2}) must be equal
bonuses.exception.wrong-amount.transaction-return-bonuses-wrong=Wrong bonusesAmount value: {0} to return for accountId: {1} and orderId: {2} for transaction bonusesAmount: {3} with already returned: {4}
bonuses.exception.wrong-amount.transaction-return-money-wrong=Wrong moneyAmount value: {0} to return for accountId: {1} and orderId: {2} for transaction moneyAmount: {3} with already returned: {4}
bonuses.exception.wrong-amount.transaction-return-unknown=Unknown error while computing bonuses returning for accountId: {0}, orderId: {1} and rest: {2}
bonuses.exception.wrong-amount.transaction-bonuses-or-money-not-null=Transaction for accountId: {0} and orderId: {1} must contains either bonusesAmount, or moneyAmount or both

LoyaltyCardsService.timeout.overall=Overall timeout while working with the loyalty cards service
LoyaltyCardsService.timeout.request=Timed out waiting for the response from the loyalty cards service
LoyaltyCardsService.error=Error when receiving a response from the loyalty-cards service
loyaltycards.exception.default=Unexpected error while working with loyalty cards service
loyaltycards.exception.object-not-found.card=Osmi card for user: {0} not found
loyaltycards.exception.object-already-bound.card-already-bound=Card with osmi id: {0} already bound to the user with id: {1}. Requested user id is {2}.
loyaltycards.exception.object-already-bound.card-already-exists=Unexpected error creating card. Card already exists. User: {0}, phone: {1}, cardId: {2}

LoyaltyService.timeout.overall=Overall timeout while working with the loyalty service
LoyaltyService.timeout.request=Timed out waiting for a response from the loyalty service
LoyaltyService.error=Error when receiving a response from the loyalty service
loyalty.status.WHITE=White
loyalty.status.SILVER=Silver
loyalty.status.BLACK=Black
loyalty.exception.default=Unexpected error with bonuses service
loyalty.exception.object-not-found.default=Object not found: {0}
loyalty.history.reason-code.initial=Assigned upon joining the Loyalty Program
loyalty.history.reason-code.recalc=Assigned during recalculation
loyalty.exception.validation-error.status-required=Status is required
loyalty.exception.validation-error.reason-required=ReasonText is required
loyalty.exception.validation-error.expires-at-must-be-after-now-or-equal=ExpiresAt must be after now or equal
loyalty.exception.validation-error.account-banned=Account has a ban, it's impossible to set status
loyalty.exception.validation-error.status-lower-auto=Status is lower than auto status


CommunityService.timeout=Timed out waiting for a response from the community service
CommunityService.error=Error when receiving a response from the community service
community.badge-layout.status.not-acquired.title=Become a part of the O!Community
community.badge-layout.status.not-acquired.description=The O!Community is a trendy community with unique statuses and exclusive bonuses. <a>About privileges</a>
community.badge-layout.status.actual.title=You are part of the O!Community
community.badge-layout.status.actual.max-rank.description=Congratulations! You have reached the highest possible status. <a>My privileges</a>
community.badge-layout.status.actual.not-max-rank.description=Up to the next status â {0}. <a>My privileges</a>
community.badge-layout.requirements.words.sale=purchases:purchase:purchases
community.badge-layout.requirements.words.purchase=sales:sale:sales
community.badge-layout.requirements.words.or=or
community.badge-layout.requirements.words.and=and
community.badge-layout.status.may-be-lost.words.purchase=purchase
community.badge-layout.status.may-be-lost.words.sale=sale
community.badge-layout.status.may-be-lost.words.platform=on the platform
community.badge-layout.status.may-be-lost.words.else=or
community.badge-layout.status.may-be-lost.words.goods=item:item:item
community.badge-layout.status.may-be-lost.words.months=January:February:March:April:May:June:July:August:September:October:November:December
community.badge-layout.status.may-be-lost.title=You could lose status in O!Community
community.badge-layout.status.may-be-lost.description=Save privileges - until {0}. <a>More details</a>
community.badge-layout.status.lost.title=Oops! You are no longer a member of the O!Community
community.badge-layout.status.lost.description=Unfortunately, you have not fulfilled the rules of the loyalty program. <a>More details</a>
community.notification.status.may-be-lost.words.purchases=purchase:purchases:purchases
community.notification.status.may-be-lost.words.sales=sale:sales:sales
community.history.status.words.purchases=purchase:purchases:purchases
community.history.status.words.sales=sale:sales:sales
community.history.username-who-assigned.auto=automatically
community.history.username-who-assigned.unknown=unknown user
community.status-get.error=Unable to determining  user''s {0} community status, because he has {1} common tags
community.status-extra-get.error=Error determining community status rank of status {0}
community.status.add-to-cart-low-status.error=Now this product is available only for the O!Community members with the status {0} and higher. Save it to favorites, and we will write when it will be available.
community.promocode.generation.oskelly.text=Generated birthday oskelly promocode for user {0}
community.promocode.generation.concierge.text=Generated birthday concierge promocode for user {0}

search.tabs.names=PRODUCTS_Products:BRANDS_Brands:USERS_Users:POSTS_Posts:HASHTAGS_Hashtags

service.concierge.BitrixOrderProcessor.toManyOrders=ÐÐ±ÑÐ°ÑÐ¸ÑÐµ Ð²Ð½Ð¸Ð¼Ð°Ð½Ð¸Ðµ, ÑÑÐ¾ ÑÐ¾Ð²Ð°Ñ Ð½Ðµ ÑÑÑÑÐ½ÑÐ¹, Ð¿Ð¾ÑÑÐ¾Ð¼Ñ ÑÐ´ÐµÐ»ÐºÐ° Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð¿ÐµÑÐµÐ¹ÑÐ¸ Ð² ÑÑÐ°ÑÑÑ "ÐÐ¿Ð»Ð°ÑÐµÐ½Ð½ÑÐµ Ð·Ð°ÐºÐ°Ð·Ñ" Ð°Ð²ÑÐ¾Ð¼Ð°ÑÐ¸ÑÐµÑÐºÐ¸ Ð¸ Ð² ÑÐ´ÐµÐ»ÐºÐµ Ð½ÐµÑ ÑÐ²ÑÐ·Ð¸ Ñ Ð·Ð°ÐºÐ°Ð·Ð¾Ð¼ Ð¸Ð· Ð°Ð´Ð¼Ð¸Ð½ÐºÐ¸ OSKELLY

CatalogService.timeout=Timed out waiting for a response from the catalog service
CatalogService.error=Error when receiving a response from the catalog service

order.duty.VAT_ON_PRICE=Customs duty 5%
order.duty.VAT_ON_PRICE_WITH_VAT=VAT on CIF
order.duty.DELIVERY_COMPANY_FEE=Customs reg. Fee

osocial.media.uploading.exception.unexpected=Error while uploading media
osocial.media.uploading.exception.max-file-size-exceeded=Maximum file size exceeded
osocial.post.creating.exception.user-cannot-be-mentioned-in-post=The user {0} can't be mentioned in the post
osocial.post.creating.exception.product-cannot-be-contained-in-post=The product {0} can't be contained in the post
osocial.post.creating.exception.invalid-tagged-user-id-in-post=Invalid format of tagged user
osocial.post.creating.exception.user-cannot-be-tagged-in-post=The user {0} can't be tagged in the post
osocial.post.creating.ban.error=You are banned and cannot publish post
osocial.post.updating.ban.error=You are banned and cannot update post
osocial.comment.creating.ban.error=You are banned and cannot create comment
osocial.comment.creating.exception.invalid-tagged-user-id=Invalid format of tagged user
osocial.comment.creating.exception.user-cannot-be-tagged=The user {0} can't be tagged in the comment
osocial.feed-posts-collection.creating.user-cannot-be-contained-in-collection=The user {0} can't be contained in the posts collection
osocial.method.timeout=The allowed working time of the social method has been exceeded
osocial.request.timeout=The request time to the social API has been exceeded
osocial.media.similar-products.exception.unexpected=Error while retrieving similar products

osocial.post.rating.explanation.part.boosting=Boosting
osocial.post.rating.explanation.part.publishedAt=Novelty rate
osocial.post.rating.explanation.part.views=Views
osocial.post.rating.explanation.part.likes=Likes
osocial.post.rating.explanation.part.comments=Comments
osocial.post.rating.explanation.part.engagement=Engagement rate
osocial.post.rating.explanation.part.author=Author
osocial.post.rating.explanation.part.following=Followings

osocial.exception.default=Internal server error
osocial.exception.object-not-found.default=Object {0} not found
osocial.exception.object-not-found.post=Post {0} not found
osocial.exception.object-not-found.comment=Comment {0} not found
osocial.exception.object-not-found.media=Media {0} not found
osocial.exception.object-not-found.tag=Tag {0} not found
osocial.exception.object-not-found.configured_feed_section=Section {0} not found
osocial.exception.object-not-found.feed_section=Section {0} not found
osocial.exception.object-not-found.feed_objects_collection=Collection of objects {0} not found
osocial.exception.object-not-found.reaction_type=Type of reaction {0} not found
osocial.exception.bad-request=Incorrect request parameters passed
osocial.exception.validation-error=Incorrect request parameters passed
osocial.exception.state-flow-broken=Incorrect request parameters passed

adminPanel.order.documents.documentGroup.generated=Order files
adminPanel.order.documents.documentGroup.invoice=Order invoices
adminPanel.order.documents.documentGroup.btqContract=Boutique contract
adminPanel.order.documents.documentGroup.generated.add.buttonName=Add order file
adminPanel.order.documents.documentGroup.invoice.add.buttonName=Add invoice
adminPanel.order.documents.documentGroup.btqContract.add.buttonName=add boutique contract

adminPanel.order.documents.documentGroup.invoice.alert.invoice-not-found=Invoice required