bank-account:
  payment-version: tcb-1.0
  enable-agent-sales: true
  enable-advance-receipts: true
  enable-oskelly-payout: false

  jobs:
    enabled: true
    prepare-seller-payouts:
      cron: "0 0/1 * * * ?"
    check-b2p-acquirer-transfers:
      cron: "0 0 0/1 * * ?"
    check-seller-payouts:
      cron: "0 0/1 * * * ?"
    check-bank-operations:
      cron: "0 0/1 * * * ?"
    refund-expiring-orders:
      cron: "-"

  best2pay:
    signing-key: test
    mobile-signing-key: test
    merchant-name: OSKELLY.RU
    return-url: ${MONOLITH_URL:http://localhost:8080}
    api-endpoint: https://test.best2pay.net/webapi
    gateweb-endpoint: https://test.best2pay.net/gateweb
    sector-id: 1304
    mobile-sector-id: 1635
  tcb:
    api-endpoint: https://paytest.online.tkbbank.ru/api/v1
    api-business-endpoint: https://paytest.online.tkbbank.ru/api/interfaces/business
    login: T2721101360ID
    signing-key: V0fmkT6zjLzsgR2aaHMB3iZWxLC0EExvGV3q41ozjO52bHuFcXJY7uLMasYKqg3OoeVvXNeitnspm6UYq5XQ5HxmjrpMcNicUsP6W8SzTzEUbvzdGattsnSPRJkFxBgQdpE3sXcqoy6FdMkX0mfbtYUGJ8qnYmbrtYRW0EUT7df2pmMeywOkwewoE6vsGYBbxQmo3x6wAvdqT0rwxkXBGZ3oCCs2jCmiLdFJ8xkbysUbbfENP74wb8DGV7VBfem7
    account-number: PAT100000763ID
    return-url: ${MONOLITH_URL:http://localhost:8080}
  tcb2:
    api-endpoint: https://paytest.online.tkbbank.ru/api/v1
    api-business-endpoint: https://paytest.online.tkbbank.ru/api/interfaces/business
    login: T2873101412ID
    signing-key: zBeOj2mGi0PEMS0uDU68pAbwXt17UqCJ0ESM6vgHBaQKhmNMPj5263K7zS0ZanV2ygOgaowhOyGpUBoMQqtLuJL53TmpErHopWkaHFMAWXpHwvn7DoiU3kHZERmqbhNw0ta6qoekO1XGtdJJFoUrhXueovEZtvyOj1QdvqSpi7gbwbFBhNTXwwpd84T4So1sCiwAwFnqFFhjRsRmYdSPLLrXNNezGXF3fXHnPfeMzDs0oevQoGfPFmQ11HWSAWTR
    account-number: PAT100000869ID
    return-url: ${MONOLITH_URL:http://localhost:8080}
    hold-timeout-in-seconds: 300
    oskelly-current-account: "40817810238100000000"
    oskelly-legal-name: ООО "ОСКЕЛИ ГРУПП"
    oskelly-account-BIK: "*********"
    oskelly-INN: "**********"
    hold-expires-in: P6dT23h30m # java.time.Duration.parse format
    allow-payout-to-bound-card: true
    allow-payment-from-bound-card: true

payments:
  boutique:
    enabled: true

payouts:
  boutique:
    api-endpoint: https://paytest.online.tkbbank.ru/api/v1
    api-business-endpoint: https://paytest.online.tkbbank.ru/api/interfaces/business
    login: T3657102618ID
    signing-key: hdS2TYO4LYvvAYPdb7djJhXPCbS2Ts1HZaFg24h04rcxwp4cn4h8akLapczRfVkt6XEAtJjf0mpseztogPQXj1GPyF0ZFzVjfTAJTETOVciY4t4ZTmLzewjeqZbXRuKBAfmv7UEL5ccgTSTfKbfuYveSKws2C55ACRxZgK08Q8DctcvHg5nQoqNeaDUMBMi8fLOcu27ajgNY1byHaUVGSpnndtYNYoHe0D5Sxtnn7nFgMXn8enMRZLq0xB7bdvk5
    account-number: PAT100002179ID # login on https://paytest.online.tkbbank.ru/ with password 'TzCpLsPv'

boutique:
  boutique-buyers-ids: 12 # using <EMAIL> as boutique buyer in tests (better switch to new backup with real ID)

#logging:
  #level:
    #org.hibernate.SQL: DEBUG
app:
  stories:
    client:
      host: localhost:8083
  static:
    host: https://static.oskelly.ru

  maxmind:
    db-file-path: /build/maxmind/geo.mmdb

  integration:
    currencyrate:
      update:
        cron: "0 */2 * * * *"

spring:
  data:
    mongodb:
      uri: *****************************************************************************

s3:
  accessKey: YCAJEuV9_xO8_dSVdi5J0Ep_w
  secretKey: YCMHzSzVvaxiJHw7SbLhVQ5kXhUK87x6avjZTvxG
  bucket:
    static: deremushkin-test-bucket
    feed: deremushkin-test-bucket

application-feature-flags:
  enable-new-home: true
  enable-android-new-home: true
