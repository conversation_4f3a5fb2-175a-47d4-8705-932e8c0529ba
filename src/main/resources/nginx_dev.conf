server {
    #listen      443 ssl http2;
    listen	80;
    server_name oskelly.dev ***************;

    charset		utf-8;
    source_charset	utf-8;

    #gzip on;
    #gzip_disable "msie6";
    #gzip_types text/plain text/css application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript application/javascript image/svg+xml;

    #client_max_body_size 16M;

    #Редиректим /admin/static на /admin/static/info
    #rewrite ^/admin/static$ /admin/static/info permanent;

    #rewrite ^(.*)/index\.(html|htm|php) http://oskelly.dev permanent;

    #if ($request_uri ~* "^(.*)/$"){
    #    set $need_rewrite $1;
    #}
    #if ($need_rewrite) {
    #    return 301 http://oskelly.dev$need_rewrite;
    #}

    access_log /home/<USER>/logs/oskelly_nginx_access.log;
    error_log /home/<USER>/logs/oskelly_nginx_error.log;

    location /wordpress/ {
      root /home;
      index index.php index.html;
      try_files $uri $uri/ =404;
      #try_files $uri $uri/ /index.php?$args;
  	}


    location @wp {
        rewrite ^/wblog(.*) /wblog/index.php?q=$1;
    }

    location ^~ /wblog {
        root /var/www/wblog/htdocs/;
        try_files $uri $uri/ @wp;
        index index.php index.html index.htm;
        charset UTF-8;

            location ~ \.php$ {
#                fastcgi_pass unix:/run/php-fpm/php5-fpm.sock;
                fastcgi_pass unix:/run/php/php7.0-fpm.sock;
                fastcgi_param SCRIPT_FILENAME $request_filename;
               fastcgi_intercept_errors on;
                include fastcgi_params;
            }

        access_log /var/log/nginx/wblog.oskelly.ru.access.log;
        error_log /var/log/nginx/wblog.oskelly.ru.error.log;
    }


    location ~ \.php$ {
      root /home/<USER>
      include snippets/fastcgi-php.conf;
      fastcgi_pass unix:/run/php/php7.0-fpm.sock;
    }

    location ~ /\.ht {
      deny all;
    }

    location / {
        proxy_pass http://127.0.0.1:8080;
        #set $frame_options '';
        #if ($http_referer !~ '^https?:\/\/([^\/]+\.)?(oskelly\.local|webvisor\.com)\/'){
        #    set $frame_options 'SAMEORIGIN';
        #}
        #add_header X-Frame-Options $frame_options;
    }

    location ^~ /tilda/ {
	     proxy_pass http://oskelly.ru;
    }

    #location  ^~ /img/ {
    #    alias /home/<USER>/images/;
    #    expires max;
    #    etag on;
    #}

    #location  ^~ /fonts/ {
    #    alias /home/<USER>/static/fonts/;
    #    #expires max;
    #    #etag on;
    #}

    #location ~ \.(css|js|ico|svg|woff|woff2|otf|ttf|eot|jpg|png)$ {
    location ~ \.(css|js)$ {
        root /home/<USER>/static;
    #    #cache
    #    expires 2592000;
    #    etag on;
    }

    location /sitemap.xml {
        root /home/<USER>/static;
    }

    location /export/ {
	     root /home/<USER>
    }

    #location ~ \.(txt|pdf|xlsx|html|xml)$ {
    #    root /home/<USER>/static-files;
    #}

    #ssl_certificate /etc/letsencrypt/live/oskelly.ru/fullchain.pem; # managed by Certbot
    #ssl_certificate_key /etc/letsencrypt/live/oskelly.ru/privkey.pem; # managed by Certbot
    #include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot

}
